<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <parent>
        <artifactId>saas</artifactId>
        <groupId>com.fenbeitong</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>saas-core</artifactId>
    <version>1.0-SNAPSHOT</version>

    <!-- 插件配置 -->
    <build>
        <resources>
            <resource>
                <directory>../conf_saas/${profile.name}</directory>
                <includes>
                    <include>conf/*</include>
                    <include>jdbc/*</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>conf/*</include>
                    <include>liteflow/*</include>
                    <include>database/**</include>
                    <include>jdbc/*</include>
                    <include>mapper/**</include>
                    <include>mybatis-gen/*</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>dubbo/*</include>
                    <include>spring-redis/*</include>
                    <include>spring-mybatis/*</include>
                </includes>
                <!-- 需要对上述文件进行${}变量替换 -->
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>


    <!-- dependencies -->
    <dependencies>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>finhub-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>saas-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-orm</artifactId>
            <version>4.3.6.RELEASE</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>bson</artifactId>
            <version>3.4.2</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>1.2.16</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>3.4.2</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-spring</artifactId>
            <version>1.3.1</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>42.2.1</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>2.9.0</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-redis</artifactId>
            <version>1.8.0.RELEASE</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>5.0.2</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.mangofactory</groupId>
            <artifactId>swagger-springmvc</artifactId>
            <version>1.0.2</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-staticdocs</artifactId>
            <version>2.4.0</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>2.4.0</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>2.4.0</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.45</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.9.7</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
            <version>1.2.3</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>1.2.3</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-access</artifactId>
            <version>1.2.1</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>5.4.0.Final</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>javax.mail</groupId>
            <artifactId>mail</artifactId>
            <version>1.4.7</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--<dependency>-->
            <!--<groupId>org.apache.poi</groupId>-->
            <!--<artifactId>poi-ooxml</artifactId>-->
            <!--<version>3.16</version>-->
            <!--<scope>compile</scope>-->
            <!--<exclusions>-->
                <!--<exclusion>-->
                    <!--<artifactId>slf4j-log4j12</artifactId>-->
                    <!--<groupId>org.slf4j</groupId>-->
                <!--</exclusion>-->
            <!--</exclusions>-->
        <!--</dependency>-->
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.1</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>2.7.8</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka_2.10</artifactId>
            <version>0.8.2.0</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>scala-library</artifactId>
                    <groupId>org.scala-lang</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
            <version>0.9.0.1</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>meta-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
            <version>3.4.12</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.101tec</groupId>
            <artifactId>zkclient</artifactId>
            <version>0.10</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dubbo</artifactId>
            <version>2.6.2</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-framework</artifactId>
            <version>4.0.1</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <version>3.1.0</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>jstl</groupId>
            <artifactId>jstl</artifactId>
            <version>1.2</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>2.9.7</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.5</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>eventbus_2.11</artifactId>
            <version>1.0.157-SNAPSHOT</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.luastar</groupId>
            <artifactId>swift-tools</artifactId>
            <version>1.0.4-SNAPSHOT</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>cn.hutool</groupId>
                    <artifactId>hutool-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>cn.hutool</groupId>
                    <artifactId>hutool-crypto</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>usercenter-api</artifactId>
            <version>5.3.64${current.version}</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>saas-plus-api</artifactId>
            <version>5.4.1.011${current.version}</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.fenbeitong</groupId>
                    <artifactId>finhub-base</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fenbeitong</groupId>
                    <artifactId>fenbei-settlement-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>harmony-city-contrail-api</artifactId>
            <version>2.0.22${current.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>hutool-all</artifactId>
                    <groupId>cn.hutool</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>fenbei-noc-api</artifactId>
            <version>7.7.2.0731${current.version}</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.fenbeitong</groupId>
                    <artifactId>fenbei-settlement-base</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>finhub-base</artifactId>
                    <groupId>com.fenbeitong</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.fenbeitong</groupId>
                    <artifactId>finhub-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>fenbei-pay-api</artifactId>
            <version>5.3.4.112${current.version}</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>finhub-common</artifactId>
                    <groupId>com.fenbeitong</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>finhub-base</artifactId>
                    <groupId>com.fenbeitong</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>finhub-common</artifactId>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>finhub-base</artifactId>
                    <groupId>com.fenbeitong</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-all</artifactId>
            <version>1.3</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-jsonSchema</artifactId>
            <version>2.4.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
            <version>2.6.0</version>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>fenbei-bank-api</artifactId>
            <version>4.9.14${current.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>finhub-kafka</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>finhub-common</artifactId>
                    <groupId>com.fenbeitong</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>fenbei-noc-api-car</artifactId>
            <version>5.4.4${current.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>finhub-common</artifactId>
                    <groupId>com.fenbeitong</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>fenbei-invoice-api</artifactId>
            <version>4.7.7${current.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>finhub-common</artifactId>
                    <groupId>com.fenbeitong</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>order-center-api</artifactId>
            <version>6.9.1${current.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>finhub-common</artifactId>
                    <groupId>com.fenbeitong</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>vendor-common-utils</artifactId>
                    <groupId>com.fenbeitong</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>finhub-base</artifactId>
                    <groupId>com.fenbeitong</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>vendor-common-utils</artifactId>
            <version>1.3.0.0512${current.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>fenbei-didi-river</artifactId>
            <version>1.0.0${current.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong.search.order</groupId>
            <artifactId>fenbei-search-order-api</artifactId>
            <version>1.1.3${current.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>harmony-city-contrail-client</artifactId>
            <version>2.0.22${current.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>finhub-base</artifactId>
                    <groupId>com.fenbeitong</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>budget-service-api</artifactId>
            <version>1.3.1${current.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>dinner-bass-api</artifactId>
        </dependency>
        <!--生成二维码-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.7.9</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.4.0</version>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong.config</groupId>
            <artifactId>fenbei-config-api</artifactId>
            <version>1.0.0${current.version}</version>
        </dependency>
        <!-- 引入神策分析  SDK -->
        <dependency>
            <groupId>com.sensorsdata.analytics.javasdk</groupId>
            <artifactId>SensorsAnalyticsSDK</artifactId>
            <version>3.2.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>jackson-databind</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>finhub-auth</artifactId>
            <version>2.1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>finhub-base</artifactId>
                    <groupId>com.fenbeitong</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>finhub-saturn-api</artifactId>
            <version>1.1.8${current.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>finhub-base</artifactId>
                    <groupId>com.fenbeitong</groupId>
                </exclusion>
                <exclusion>
                    <groupId>javax.el</groupId>
                    <artifactId>javax.el-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>biz-card-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.finhub.framework</groupId>
                    <artifactId>finhub-dubbo</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>vendor-common-utils</artifactId>
                    <groupId>com.fenbeitong</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>finhub-base</artifactId>
                    <groupId>com.fenbeitong</groupId>
                </exclusion>
            </exclusions>
            <version>1.0.9${current.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong.expense.management</groupId>
            <artifactId>expense-management-api</artifactId>
            <version>5.3.5.051${current.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>finhub-base</artifactId>
                    <groupId>com.fenbeitong</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yomahub</groupId>
            <artifactId>liteflow-spring-boot-starter</artifactId>
            <version>2.9.3</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-lang</groupId>
                    <artifactId>commons-lang</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>cn.hutool</groupId>
                    <artifactId>hutool-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.el</groupId>
                    <artifactId>javax.el-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>fenbei-car-api</artifactId>
            <version>5.1.9${current.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>travel-rule-api</artifactId>
            <version>1.6.091.18${current.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fenbei</groupId>
            <artifactId>cost-management-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong.search.worker</groupId>
            <artifactId>fenbei-search-worker-api</artifactId>
            <version>1.1.12${current.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>fenbei-hotel-api</artifactId>
            <version>1.3.1${current.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>mileage-allowance-api</artifactId>
            <version>1.4.0${current.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong.fxpay</groupId>
            <artifactId>fenbei-fx-pay-api</artifactId>
        </dependency>
    </dependencies>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
    </properties>
</project>
