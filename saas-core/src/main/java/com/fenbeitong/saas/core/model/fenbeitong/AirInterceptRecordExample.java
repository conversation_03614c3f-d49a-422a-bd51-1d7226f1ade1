package com.fenbeitong.saas.core.model.fenbeitong;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AirInterceptRecordExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table air_intercept_record
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table air_intercept_record
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table air_intercept_record
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table air_intercept_record
     *
     * @mbg.generated
     */
    public AirInterceptRecordExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table air_intercept_record
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table air_intercept_record
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table air_intercept_record
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table air_intercept_record
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table air_intercept_record
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table air_intercept_record
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table air_intercept_record
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table air_intercept_record
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table air_intercept_record
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table air_intercept_record
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table air_intercept_record
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdIsNull() {
            addCriterion("employee_id is null");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdIsNotNull() {
            addCriterion("employee_id is not null");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdEqualTo(String value) {
            addCriterion("employee_id =", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdNotEqualTo(String value) {
            addCriterion("employee_id <>", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdGreaterThan(String value) {
            addCriterion("employee_id >", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdGreaterThanOrEqualTo(String value) {
            addCriterion("employee_id >=", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdLessThan(String value) {
            addCriterion("employee_id <", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdLessThanOrEqualTo(String value) {
            addCriterion("employee_id <=", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdLike(String value) {
            addCriterion("employee_id like", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdNotLike(String value) {
            addCriterion("employee_id not like", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdIn(List<String> values) {
            addCriterion("employee_id in", values, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdNotIn(List<String> values) {
            addCriterion("employee_id not in", values, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdBetween(String value1, String value2) {
            addCriterion("employee_id between", value1, value2, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdNotBetween(String value1, String value2) {
            addCriterion("employee_id not between", value1, value2, "employeeId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(String value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(String value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(String value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(String value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(String value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(String value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLike(String value) {
            addCriterion("company_id like", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotLike(String value) {
            addCriterion("company_id not like", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<String> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<String> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(String value1, String value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(String value1, String value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andContactNameIsNull() {
            addCriterion("contact_name is null");
            return (Criteria) this;
        }

        public Criteria andContactNameIsNotNull() {
            addCriterion("contact_name is not null");
            return (Criteria) this;
        }

        public Criteria andContactNameEqualTo(String value) {
            addCriterion("contact_name =", value, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameNotEqualTo(String value) {
            addCriterion("contact_name <>", value, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameGreaterThan(String value) {
            addCriterion("contact_name >", value, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameGreaterThanOrEqualTo(String value) {
            addCriterion("contact_name >=", value, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameLessThan(String value) {
            addCriterion("contact_name <", value, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameLessThanOrEqualTo(String value) {
            addCriterion("contact_name <=", value, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameLike(String value) {
            addCriterion("contact_name like", value, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameNotLike(String value) {
            addCriterion("contact_name not like", value, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameIn(List<String> values) {
            addCriterion("contact_name in", values, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameNotIn(List<String> values) {
            addCriterion("contact_name not in", values, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameBetween(String value1, String value2) {
            addCriterion("contact_name between", value1, value2, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameNotBetween(String value1, String value2) {
            addCriterion("contact_name not between", value1, value2, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactPhoneIsNull() {
            addCriterion("contact_phone is null");
            return (Criteria) this;
        }

        public Criteria andContactPhoneIsNotNull() {
            addCriterion("contact_phone is not null");
            return (Criteria) this;
        }

        public Criteria andContactPhoneEqualTo(String value) {
            addCriterion("contact_phone =", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotEqualTo(String value) {
            addCriterion("contact_phone <>", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneGreaterThan(String value) {
            addCriterion("contact_phone >", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("contact_phone >=", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneLessThan(String value) {
            addCriterion("contact_phone <", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneLessThanOrEqualTo(String value) {
            addCriterion("contact_phone <=", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneLike(String value) {
            addCriterion("contact_phone like", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotLike(String value) {
            addCriterion("contact_phone not like", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneIn(List<String> values) {
            addCriterion("contact_phone in", values, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotIn(List<String> values) {
            addCriterion("contact_phone not in", values, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneBetween(String value1, String value2) {
            addCriterion("contact_phone between", value1, value2, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotBetween(String value1, String value2) {
            addCriterion("contact_phone not between", value1, value2, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andChannelIsNull() {
            addCriterion("channel is null");
            return (Criteria) this;
        }

        public Criteria andChannelIsNotNull() {
            addCriterion("channel is not null");
            return (Criteria) this;
        }

        public Criteria andChannelEqualTo(Integer value) {
            addCriterion("channel =", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelNotEqualTo(Integer value) {
            addCriterion("channel <>", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelGreaterThan(Integer value) {
            addCriterion("channel >", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelGreaterThanOrEqualTo(Integer value) {
            addCriterion("channel >=", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelLessThan(Integer value) {
            addCriterion("channel <", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelLessThanOrEqualTo(Integer value) {
            addCriterion("channel <=", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelIn(List<Integer> values) {
            addCriterion("channel in", values, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelNotIn(List<Integer> values) {
            addCriterion("channel not in", values, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelBetween(Integer value1, Integer value2) {
            addCriterion("channel between", value1, value2, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelNotBetween(Integer value1, Integer value2) {
            addCriterion("channel not between", value1, value2, "channel");
            return (Criteria) this;
        }

        public Criteria andTotalPriceIsNull() {
            addCriterion("total_price is null");
            return (Criteria) this;
        }

        public Criteria andTotalPriceIsNotNull() {
            addCriterion("total_price is not null");
            return (Criteria) this;
        }

        public Criteria andTotalPriceEqualTo(BigDecimal value) {
            addCriterion("total_price =", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotEqualTo(BigDecimal value) {
            addCriterion("total_price <>", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceGreaterThan(BigDecimal value) {
            addCriterion("total_price >", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_price >=", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceLessThan(BigDecimal value) {
            addCriterion("total_price <", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_price <=", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceIn(List<BigDecimal> values) {
            addCriterion("total_price in", values, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotIn(List<BigDecimal> values) {
            addCriterion("total_price not in", values, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_price between", value1, value2, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_price not between", value1, value2, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andAirRuleIsNull() {
            addCriterion("air_rule is null");
            return (Criteria) this;
        }

        public Criteria andAirRuleIsNotNull() {
            addCriterion("air_rule is not null");
            return (Criteria) this;
        }

        public Criteria andAirRuleEqualTo(Integer value) {
            addCriterion("air_rule =", value, "airRule");
            return (Criteria) this;
        }

        public Criteria andAirRuleNotEqualTo(Integer value) {
            addCriterion("air_rule <>", value, "airRule");
            return (Criteria) this;
        }

        public Criteria andAirRuleGreaterThan(Integer value) {
            addCriterion("air_rule >", value, "airRule");
            return (Criteria) this;
        }

        public Criteria andAirRuleGreaterThanOrEqualTo(Integer value) {
            addCriterion("air_rule >=", value, "airRule");
            return (Criteria) this;
        }

        public Criteria andAirRuleLessThan(Integer value) {
            addCriterion("air_rule <", value, "airRule");
            return (Criteria) this;
        }

        public Criteria andAirRuleLessThanOrEqualTo(Integer value) {
            addCriterion("air_rule <=", value, "airRule");
            return (Criteria) this;
        }

        public Criteria andAirRuleIn(List<Integer> values) {
            addCriterion("air_rule in", values, "airRule");
            return (Criteria) this;
        }

        public Criteria andAirRuleNotIn(List<Integer> values) {
            addCriterion("air_rule not in", values, "airRule");
            return (Criteria) this;
        }

        public Criteria andAirRuleBetween(Integer value1, Integer value2) {
            addCriterion("air_rule between", value1, value2, "airRule");
            return (Criteria) this;
        }

        public Criteria andAirRuleNotBetween(Integer value1, Integer value2) {
            addCriterion("air_rule not between", value1, value2, "airRule");
            return (Criteria) this;
        }

        public Criteria andAirRuleFlagIsNull() {
            addCriterion("air_rule_flag is null");
            return (Criteria) this;
        }

        public Criteria andAirRuleFlagIsNotNull() {
            addCriterion("air_rule_flag is not null");
            return (Criteria) this;
        }

        public Criteria andAirRuleFlagEqualTo(Boolean value) {
            addCriterion("air_rule_flag =", value, "airRuleFlag");
            return (Criteria) this;
        }

        public Criteria andAirRuleFlagNotEqualTo(Boolean value) {
            addCriterion("air_rule_flag <>", value, "airRuleFlag");
            return (Criteria) this;
        }

        public Criteria andAirRuleFlagGreaterThan(Boolean value) {
            addCriterion("air_rule_flag >", value, "airRuleFlag");
            return (Criteria) this;
        }

        public Criteria andAirRuleFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("air_rule_flag >=", value, "airRuleFlag");
            return (Criteria) this;
        }

        public Criteria andAirRuleFlagLessThan(Boolean value) {
            addCriterion("air_rule_flag <", value, "airRuleFlag");
            return (Criteria) this;
        }

        public Criteria andAirRuleFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("air_rule_flag <=", value, "airRuleFlag");
            return (Criteria) this;
        }

        public Criteria andAirRuleFlagIn(List<Boolean> values) {
            addCriterion("air_rule_flag in", values, "airRuleFlag");
            return (Criteria) this;
        }

        public Criteria andAirRuleFlagNotIn(List<Boolean> values) {
            addCriterion("air_rule_flag not in", values, "airRuleFlag");
            return (Criteria) this;
        }

        public Criteria andAirRuleFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("air_rule_flag between", value1, value2, "airRuleFlag");
            return (Criteria) this;
        }

        public Criteria andAirRuleFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("air_rule_flag not between", value1, value2, "airRuleFlag");
            return (Criteria) this;
        }

        public Criteria andAirVerifyFlagIsNull() {
            addCriterion("air_verify_flag is null");
            return (Criteria) this;
        }

        public Criteria andAirVerifyFlagIsNotNull() {
            addCriterion("air_verify_flag is not null");
            return (Criteria) this;
        }

        public Criteria andAirVerifyFlagEqualTo(Boolean value) {
            addCriterion("air_verify_flag =", value, "airVerifyFlag");
            return (Criteria) this;
        }

        public Criteria andAirVerifyFlagNotEqualTo(Boolean value) {
            addCriterion("air_verify_flag <>", value, "airVerifyFlag");
            return (Criteria) this;
        }

        public Criteria andAirVerifyFlagGreaterThan(Boolean value) {
            addCriterion("air_verify_flag >", value, "airVerifyFlag");
            return (Criteria) this;
        }

        public Criteria andAirVerifyFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("air_verify_flag >=", value, "airVerifyFlag");
            return (Criteria) this;
        }

        public Criteria andAirVerifyFlagLessThan(Boolean value) {
            addCriterion("air_verify_flag <", value, "airVerifyFlag");
            return (Criteria) this;
        }

        public Criteria andAirVerifyFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("air_verify_flag <=", value, "airVerifyFlag");
            return (Criteria) this;
        }

        public Criteria andAirVerifyFlagIn(List<Boolean> values) {
            addCriterion("air_verify_flag in", values, "airVerifyFlag");
            return (Criteria) this;
        }

        public Criteria andAirVerifyFlagNotIn(List<Boolean> values) {
            addCriterion("air_verify_flag not in", values, "airVerifyFlag");
            return (Criteria) this;
        }

        public Criteria andAirVerifyFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("air_verify_flag between", value1, value2, "airVerifyFlag");
            return (Criteria) this;
        }

        public Criteria andAirVerifyFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("air_verify_flag not between", value1, value2, "airVerifyFlag");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagIsNull() {
            addCriterion("exceed_buy_flag is null");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagIsNotNull() {
            addCriterion("exceed_buy_flag is not null");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagEqualTo(Boolean value) {
            addCriterion("exceed_buy_flag =", value, "exceedBuyFlag");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagNotEqualTo(Boolean value) {
            addCriterion("exceed_buy_flag <>", value, "exceedBuyFlag");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagGreaterThan(Boolean value) {
            addCriterion("exceed_buy_flag >", value, "exceedBuyFlag");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("exceed_buy_flag >=", value, "exceedBuyFlag");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagLessThan(Boolean value) {
            addCriterion("exceed_buy_flag <", value, "exceedBuyFlag");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("exceed_buy_flag <=", value, "exceedBuyFlag");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagIn(List<Boolean> values) {
            addCriterion("exceed_buy_flag in", values, "exceedBuyFlag");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagNotIn(List<Boolean> values) {
            addCriterion("exceed_buy_flag not in", values, "exceedBuyFlag");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("exceed_buy_flag between", value1, value2, "exceedBuyFlag");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("exceed_buy_flag not between", value1, value2, "exceedBuyFlag");
            return (Criteria) this;
        }

        public Criteria andAirCabinFlagIsNull() {
            addCriterion("air_cabin_flag is null");
            return (Criteria) this;
        }

        public Criteria andAirCabinFlagIsNotNull() {
            addCriterion("air_cabin_flag is not null");
            return (Criteria) this;
        }

        public Criteria andAirCabinFlagEqualTo(Boolean value) {
            addCriterion("air_cabin_flag =", value, "airCabinFlag");
            return (Criteria) this;
        }

        public Criteria andAirCabinFlagNotEqualTo(Boolean value) {
            addCriterion("air_cabin_flag <>", value, "airCabinFlag");
            return (Criteria) this;
        }

        public Criteria andAirCabinFlagGreaterThan(Boolean value) {
            addCriterion("air_cabin_flag >", value, "airCabinFlag");
            return (Criteria) this;
        }

        public Criteria andAirCabinFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("air_cabin_flag >=", value, "airCabinFlag");
            return (Criteria) this;
        }

        public Criteria andAirCabinFlagLessThan(Boolean value) {
            addCriterion("air_cabin_flag <", value, "airCabinFlag");
            return (Criteria) this;
        }

        public Criteria andAirCabinFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("air_cabin_flag <=", value, "airCabinFlag");
            return (Criteria) this;
        }

        public Criteria andAirCabinFlagIn(List<Boolean> values) {
            addCriterion("air_cabin_flag in", values, "airCabinFlag");
            return (Criteria) this;
        }

        public Criteria andAirCabinFlagNotIn(List<Boolean> values) {
            addCriterion("air_cabin_flag not in", values, "airCabinFlag");
            return (Criteria) this;
        }

        public Criteria andAirCabinFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("air_cabin_flag between", value1, value2, "airCabinFlag");
            return (Criteria) this;
        }

        public Criteria andAirCabinFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("air_cabin_flag not between", value1, value2, "airCabinFlag");
            return (Criteria) this;
        }

        public Criteria andAirCabinTypeIsNull() {
            addCriterion("air_cabin_type is null");
            return (Criteria) this;
        }

        public Criteria andAirCabinTypeIsNotNull() {
            addCriterion("air_cabin_type is not null");
            return (Criteria) this;
        }

        public Criteria andAirCabinTypeEqualTo(String value) {
            addCriterion("air_cabin_type =", value, "airCabinType");
            return (Criteria) this;
        }

        public Criteria andAirCabinTypeNotEqualTo(String value) {
            addCriterion("air_cabin_type <>", value, "airCabinType");
            return (Criteria) this;
        }

        public Criteria andAirCabinTypeGreaterThan(String value) {
            addCriterion("air_cabin_type >", value, "airCabinType");
            return (Criteria) this;
        }

        public Criteria andAirCabinTypeGreaterThanOrEqualTo(String value) {
            addCriterion("air_cabin_type >=", value, "airCabinType");
            return (Criteria) this;
        }

        public Criteria andAirCabinTypeLessThan(String value) {
            addCriterion("air_cabin_type <", value, "airCabinType");
            return (Criteria) this;
        }

        public Criteria andAirCabinTypeLessThanOrEqualTo(String value) {
            addCriterion("air_cabin_type <=", value, "airCabinType");
            return (Criteria) this;
        }

        public Criteria andAirCabinTypeLike(String value) {
            addCriterion("air_cabin_type like", value, "airCabinType");
            return (Criteria) this;
        }

        public Criteria andAirCabinTypeNotLike(String value) {
            addCriterion("air_cabin_type not like", value, "airCabinType");
            return (Criteria) this;
        }

        public Criteria andAirCabinTypeIn(List<String> values) {
            addCriterion("air_cabin_type in", values, "airCabinType");
            return (Criteria) this;
        }

        public Criteria andAirCabinTypeNotIn(List<String> values) {
            addCriterion("air_cabin_type not in", values, "airCabinType");
            return (Criteria) this;
        }

        public Criteria andAirCabinTypeBetween(String value1, String value2) {
            addCriterion("air_cabin_type between", value1, value2, "airCabinType");
            return (Criteria) this;
        }

        public Criteria andAirCabinTypeNotBetween(String value1, String value2) {
            addCriterion("air_cabin_type not between", value1, value2, "airCabinType");
            return (Criteria) this;
        }

        public Criteria andAirPriceFlagIsNull() {
            addCriterion("air_price_flag is null");
            return (Criteria) this;
        }

        public Criteria andAirPriceFlagIsNotNull() {
            addCriterion("air_price_flag is not null");
            return (Criteria) this;
        }

        public Criteria andAirPriceFlagEqualTo(Boolean value) {
            addCriterion("air_price_flag =", value, "airPriceFlag");
            return (Criteria) this;
        }

        public Criteria andAirPriceFlagNotEqualTo(Boolean value) {
            addCriterion("air_price_flag <>", value, "airPriceFlag");
            return (Criteria) this;
        }

        public Criteria andAirPriceFlagGreaterThan(Boolean value) {
            addCriterion("air_price_flag >", value, "airPriceFlag");
            return (Criteria) this;
        }

        public Criteria andAirPriceFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("air_price_flag >=", value, "airPriceFlag");
            return (Criteria) this;
        }

        public Criteria andAirPriceFlagLessThan(Boolean value) {
            addCriterion("air_price_flag <", value, "airPriceFlag");
            return (Criteria) this;
        }

        public Criteria andAirPriceFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("air_price_flag <=", value, "airPriceFlag");
            return (Criteria) this;
        }

        public Criteria andAirPriceFlagIn(List<Boolean> values) {
            addCriterion("air_price_flag in", values, "airPriceFlag");
            return (Criteria) this;
        }

        public Criteria andAirPriceFlagNotIn(List<Boolean> values) {
            addCriterion("air_price_flag not in", values, "airPriceFlag");
            return (Criteria) this;
        }

        public Criteria andAirPriceFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("air_price_flag between", value1, value2, "airPriceFlag");
            return (Criteria) this;
        }

        public Criteria andAirPriceFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("air_price_flag not between", value1, value2, "airPriceFlag");
            return (Criteria) this;
        }

        public Criteria andAirUnitPriceIsNull() {
            addCriterion("air_unit_price is null");
            return (Criteria) this;
        }

        public Criteria andAirUnitPriceIsNotNull() {
            addCriterion("air_unit_price is not null");
            return (Criteria) this;
        }

        public Criteria andAirUnitPriceEqualTo(BigDecimal value) {
            addCriterion("air_unit_price =", value, "airUnitPrice");
            return (Criteria) this;
        }

        public Criteria andAirUnitPriceNotEqualTo(BigDecimal value) {
            addCriterion("air_unit_price <>", value, "airUnitPrice");
            return (Criteria) this;
        }

        public Criteria andAirUnitPriceGreaterThan(BigDecimal value) {
            addCriterion("air_unit_price >", value, "airUnitPrice");
            return (Criteria) this;
        }

        public Criteria andAirUnitPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("air_unit_price >=", value, "airUnitPrice");
            return (Criteria) this;
        }

        public Criteria andAirUnitPriceLessThan(BigDecimal value) {
            addCriterion("air_unit_price <", value, "airUnitPrice");
            return (Criteria) this;
        }

        public Criteria andAirUnitPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("air_unit_price <=", value, "airUnitPrice");
            return (Criteria) this;
        }

        public Criteria andAirUnitPriceIn(List<BigDecimal> values) {
            addCriterion("air_unit_price in", values, "airUnitPrice");
            return (Criteria) this;
        }

        public Criteria andAirUnitPriceNotIn(List<BigDecimal> values) {
            addCriterion("air_unit_price not in", values, "airUnitPrice");
            return (Criteria) this;
        }

        public Criteria andAirUnitPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("air_unit_price between", value1, value2, "airUnitPrice");
            return (Criteria) this;
        }

        public Criteria andAirUnitPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("air_unit_price not between", value1, value2, "airUnitPrice");
            return (Criteria) this;
        }

        public Criteria andFlightNoIsNull() {
            addCriterion("flight_no is null");
            return (Criteria) this;
        }

        public Criteria andFlightNoIsNotNull() {
            addCriterion("flight_no is not null");
            return (Criteria) this;
        }

        public Criteria andFlightNoEqualTo(String value) {
            addCriterion("flight_no =", value, "flightNo");
            return (Criteria) this;
        }

        public Criteria andFlightNoNotEqualTo(String value) {
            addCriterion("flight_no <>", value, "flightNo");
            return (Criteria) this;
        }

        public Criteria andFlightNoGreaterThan(String value) {
            addCriterion("flight_no >", value, "flightNo");
            return (Criteria) this;
        }

        public Criteria andFlightNoGreaterThanOrEqualTo(String value) {
            addCriterion("flight_no >=", value, "flightNo");
            return (Criteria) this;
        }

        public Criteria andFlightNoLessThan(String value) {
            addCriterion("flight_no <", value, "flightNo");
            return (Criteria) this;
        }

        public Criteria andFlightNoLessThanOrEqualTo(String value) {
            addCriterion("flight_no <=", value, "flightNo");
            return (Criteria) this;
        }

        public Criteria andFlightNoLike(String value) {
            addCriterion("flight_no like", value, "flightNo");
            return (Criteria) this;
        }

        public Criteria andFlightNoNotLike(String value) {
            addCriterion("flight_no not like", value, "flightNo");
            return (Criteria) this;
        }

        public Criteria andFlightNoIn(List<String> values) {
            addCriterion("flight_no in", values, "flightNo");
            return (Criteria) this;
        }

        public Criteria andFlightNoNotIn(List<String> values) {
            addCriterion("flight_no not in", values, "flightNo");
            return (Criteria) this;
        }

        public Criteria andFlightNoBetween(String value1, String value2) {
            addCriterion("flight_no between", value1, value2, "flightNo");
            return (Criteria) this;
        }

        public Criteria andFlightNoNotBetween(String value1, String value2) {
            addCriterion("flight_no not between", value1, value2, "flightNo");
            return (Criteria) this;
        }

        public Criteria andPlaneTypeIsNull() {
            addCriterion("plane_type is null");
            return (Criteria) this;
        }

        public Criteria andPlaneTypeIsNotNull() {
            addCriterion("plane_type is not null");
            return (Criteria) this;
        }

        public Criteria andPlaneTypeEqualTo(String value) {
            addCriterion("plane_type =", value, "planeType");
            return (Criteria) this;
        }

        public Criteria andPlaneTypeNotEqualTo(String value) {
            addCriterion("plane_type <>", value, "planeType");
            return (Criteria) this;
        }

        public Criteria andPlaneTypeGreaterThan(String value) {
            addCriterion("plane_type >", value, "planeType");
            return (Criteria) this;
        }

        public Criteria andPlaneTypeGreaterThanOrEqualTo(String value) {
            addCriterion("plane_type >=", value, "planeType");
            return (Criteria) this;
        }

        public Criteria andPlaneTypeLessThan(String value) {
            addCriterion("plane_type <", value, "planeType");
            return (Criteria) this;
        }

        public Criteria andPlaneTypeLessThanOrEqualTo(String value) {
            addCriterion("plane_type <=", value, "planeType");
            return (Criteria) this;
        }

        public Criteria andPlaneTypeLike(String value) {
            addCriterion("plane_type like", value, "planeType");
            return (Criteria) this;
        }

        public Criteria andPlaneTypeNotLike(String value) {
            addCriterion("plane_type not like", value, "planeType");
            return (Criteria) this;
        }

        public Criteria andPlaneTypeIn(List<String> values) {
            addCriterion("plane_type in", values, "planeType");
            return (Criteria) this;
        }

        public Criteria andPlaneTypeNotIn(List<String> values) {
            addCriterion("plane_type not in", values, "planeType");
            return (Criteria) this;
        }

        public Criteria andPlaneTypeBetween(String value1, String value2) {
            addCriterion("plane_type between", value1, value2, "planeType");
            return (Criteria) this;
        }

        public Criteria andPlaneTypeNotBetween(String value1, String value2) {
            addCriterion("plane_type not between", value1, value2, "planeType");
            return (Criteria) this;
        }

        public Criteria andAirlineNameIsNull() {
            addCriterion("airline_name is null");
            return (Criteria) this;
        }

        public Criteria andAirlineNameIsNotNull() {
            addCriterion("airline_name is not null");
            return (Criteria) this;
        }

        public Criteria andAirlineNameEqualTo(String value) {
            addCriterion("airline_name =", value, "airlineName");
            return (Criteria) this;
        }

        public Criteria andAirlineNameNotEqualTo(String value) {
            addCriterion("airline_name <>", value, "airlineName");
            return (Criteria) this;
        }

        public Criteria andAirlineNameGreaterThan(String value) {
            addCriterion("airline_name >", value, "airlineName");
            return (Criteria) this;
        }

        public Criteria andAirlineNameGreaterThanOrEqualTo(String value) {
            addCriterion("airline_name >=", value, "airlineName");
            return (Criteria) this;
        }

        public Criteria andAirlineNameLessThan(String value) {
            addCriterion("airline_name <", value, "airlineName");
            return (Criteria) this;
        }

        public Criteria andAirlineNameLessThanOrEqualTo(String value) {
            addCriterion("airline_name <=", value, "airlineName");
            return (Criteria) this;
        }

        public Criteria andAirlineNameLike(String value) {
            addCriterion("airline_name like", value, "airlineName");
            return (Criteria) this;
        }

        public Criteria andAirlineNameNotLike(String value) {
            addCriterion("airline_name not like", value, "airlineName");
            return (Criteria) this;
        }

        public Criteria andAirlineNameIn(List<String> values) {
            addCriterion("airline_name in", values, "airlineName");
            return (Criteria) this;
        }

        public Criteria andAirlineNameNotIn(List<String> values) {
            addCriterion("airline_name not in", values, "airlineName");
            return (Criteria) this;
        }

        public Criteria andAirlineNameBetween(String value1, String value2) {
            addCriterion("airline_name between", value1, value2, "airlineName");
            return (Criteria) this;
        }

        public Criteria andAirlineNameNotBetween(String value1, String value2) {
            addCriterion("airline_name not between", value1, value2, "airlineName");
            return (Criteria) this;
        }

        public Criteria andServiceClassIsNull() {
            addCriterion("service_class is null");
            return (Criteria) this;
        }

        public Criteria andServiceClassIsNotNull() {
            addCriterion("service_class is not null");
            return (Criteria) this;
        }

        public Criteria andServiceClassEqualTo(Integer value) {
            addCriterion("service_class =", value, "serviceClass");
            return (Criteria) this;
        }

        public Criteria andServiceClassNotEqualTo(Integer value) {
            addCriterion("service_class <>", value, "serviceClass");
            return (Criteria) this;
        }

        public Criteria andServiceClassGreaterThan(Integer value) {
            addCriterion("service_class >", value, "serviceClass");
            return (Criteria) this;
        }

        public Criteria andServiceClassGreaterThanOrEqualTo(Integer value) {
            addCriterion("service_class >=", value, "serviceClass");
            return (Criteria) this;
        }

        public Criteria andServiceClassLessThan(Integer value) {
            addCriterion("service_class <", value, "serviceClass");
            return (Criteria) this;
        }

        public Criteria andServiceClassLessThanOrEqualTo(Integer value) {
            addCriterion("service_class <=", value, "serviceClass");
            return (Criteria) this;
        }

        public Criteria andServiceClassIn(List<Integer> values) {
            addCriterion("service_class in", values, "serviceClass");
            return (Criteria) this;
        }

        public Criteria andServiceClassNotIn(List<Integer> values) {
            addCriterion("service_class not in", values, "serviceClass");
            return (Criteria) this;
        }

        public Criteria andServiceClassBetween(Integer value1, Integer value2) {
            addCriterion("service_class between", value1, value2, "serviceClass");
            return (Criteria) this;
        }

        public Criteria andServiceClassNotBetween(Integer value1, Integer value2) {
            addCriterion("service_class not between", value1, value2, "serviceClass");
            return (Criteria) this;
        }

        public Criteria andSeatMsgIsNull() {
            addCriterion("seat_msg is null");
            return (Criteria) this;
        }

        public Criteria andSeatMsgIsNotNull() {
            addCriterion("seat_msg is not null");
            return (Criteria) this;
        }

        public Criteria andSeatMsgEqualTo(String value) {
            addCriterion("seat_msg =", value, "seatMsg");
            return (Criteria) this;
        }

        public Criteria andSeatMsgNotEqualTo(String value) {
            addCriterion("seat_msg <>", value, "seatMsg");
            return (Criteria) this;
        }

        public Criteria andSeatMsgGreaterThan(String value) {
            addCriterion("seat_msg >", value, "seatMsg");
            return (Criteria) this;
        }

        public Criteria andSeatMsgGreaterThanOrEqualTo(String value) {
            addCriterion("seat_msg >=", value, "seatMsg");
            return (Criteria) this;
        }

        public Criteria andSeatMsgLessThan(String value) {
            addCriterion("seat_msg <", value, "seatMsg");
            return (Criteria) this;
        }

        public Criteria andSeatMsgLessThanOrEqualTo(String value) {
            addCriterion("seat_msg <=", value, "seatMsg");
            return (Criteria) this;
        }

        public Criteria andSeatMsgLike(String value) {
            addCriterion("seat_msg like", value, "seatMsg");
            return (Criteria) this;
        }

        public Criteria andSeatMsgNotLike(String value) {
            addCriterion("seat_msg not like", value, "seatMsg");
            return (Criteria) this;
        }

        public Criteria andSeatMsgIn(List<String> values) {
            addCriterion("seat_msg in", values, "seatMsg");
            return (Criteria) this;
        }

        public Criteria andSeatMsgNotIn(List<String> values) {
            addCriterion("seat_msg not in", values, "seatMsg");
            return (Criteria) this;
        }

        public Criteria andSeatMsgBetween(String value1, String value2) {
            addCriterion("seat_msg between", value1, value2, "seatMsg");
            return (Criteria) this;
        }

        public Criteria andSeatMsgNotBetween(String value1, String value2) {
            addCriterion("seat_msg not between", value1, value2, "seatMsg");
            return (Criteria) this;
        }

        public Criteria andCabinIsNull() {
            addCriterion("cabin is null");
            return (Criteria) this;
        }

        public Criteria andCabinIsNotNull() {
            addCriterion("cabin is not null");
            return (Criteria) this;
        }

        public Criteria andCabinEqualTo(String value) {
            addCriterion("cabin =", value, "cabin");
            return (Criteria) this;
        }

        public Criteria andCabinNotEqualTo(String value) {
            addCriterion("cabin <>", value, "cabin");
            return (Criteria) this;
        }

        public Criteria andCabinGreaterThan(String value) {
            addCriterion("cabin >", value, "cabin");
            return (Criteria) this;
        }

        public Criteria andCabinGreaterThanOrEqualTo(String value) {
            addCriterion("cabin >=", value, "cabin");
            return (Criteria) this;
        }

        public Criteria andCabinLessThan(String value) {
            addCriterion("cabin <", value, "cabin");
            return (Criteria) this;
        }

        public Criteria andCabinLessThanOrEqualTo(String value) {
            addCriterion("cabin <=", value, "cabin");
            return (Criteria) this;
        }

        public Criteria andCabinLike(String value) {
            addCriterion("cabin like", value, "cabin");
            return (Criteria) this;
        }

        public Criteria andCabinNotLike(String value) {
            addCriterion("cabin not like", value, "cabin");
            return (Criteria) this;
        }

        public Criteria andCabinIn(List<String> values) {
            addCriterion("cabin in", values, "cabin");
            return (Criteria) this;
        }

        public Criteria andCabinNotIn(List<String> values) {
            addCriterion("cabin not in", values, "cabin");
            return (Criteria) this;
        }

        public Criteria andCabinBetween(String value1, String value2) {
            addCriterion("cabin between", value1, value2, "cabin");
            return (Criteria) this;
        }

        public Criteria andCabinNotBetween(String value1, String value2) {
            addCriterion("cabin not between", value1, value2, "cabin");
            return (Criteria) this;
        }

        public Criteria andStartingCityIsNull() {
            addCriterion("starting_city is null");
            return (Criteria) this;
        }

        public Criteria andStartingCityIsNotNull() {
            addCriterion("starting_city is not null");
            return (Criteria) this;
        }

        public Criteria andStartingCityEqualTo(String value) {
            addCriterion("starting_city =", value, "startingCity");
            return (Criteria) this;
        }

        public Criteria andStartingCityNotEqualTo(String value) {
            addCriterion("starting_city <>", value, "startingCity");
            return (Criteria) this;
        }

        public Criteria andStartingCityGreaterThan(String value) {
            addCriterion("starting_city >", value, "startingCity");
            return (Criteria) this;
        }

        public Criteria andStartingCityGreaterThanOrEqualTo(String value) {
            addCriterion("starting_city >=", value, "startingCity");
            return (Criteria) this;
        }

        public Criteria andStartingCityLessThan(String value) {
            addCriterion("starting_city <", value, "startingCity");
            return (Criteria) this;
        }

        public Criteria andStartingCityLessThanOrEqualTo(String value) {
            addCriterion("starting_city <=", value, "startingCity");
            return (Criteria) this;
        }

        public Criteria andStartingCityLike(String value) {
            addCriterion("starting_city like", value, "startingCity");
            return (Criteria) this;
        }

        public Criteria andStartingCityNotLike(String value) {
            addCriterion("starting_city not like", value, "startingCity");
            return (Criteria) this;
        }

        public Criteria andStartingCityIn(List<String> values) {
            addCriterion("starting_city in", values, "startingCity");
            return (Criteria) this;
        }

        public Criteria andStartingCityNotIn(List<String> values) {
            addCriterion("starting_city not in", values, "startingCity");
            return (Criteria) this;
        }

        public Criteria andStartingCityBetween(String value1, String value2) {
            addCriterion("starting_city between", value1, value2, "startingCity");
            return (Criteria) this;
        }

        public Criteria andStartingCityNotBetween(String value1, String value2) {
            addCriterion("starting_city not between", value1, value2, "startingCity");
            return (Criteria) this;
        }

        public Criteria andStartingCodeIsNull() {
            addCriterion("starting_code is null");
            return (Criteria) this;
        }

        public Criteria andStartingCodeIsNotNull() {
            addCriterion("starting_code is not null");
            return (Criteria) this;
        }

        public Criteria andStartingCodeEqualTo(String value) {
            addCriterion("starting_code =", value, "startingCode");
            return (Criteria) this;
        }

        public Criteria andStartingCodeNotEqualTo(String value) {
            addCriterion("starting_code <>", value, "startingCode");
            return (Criteria) this;
        }

        public Criteria andStartingCodeGreaterThan(String value) {
            addCriterion("starting_code >", value, "startingCode");
            return (Criteria) this;
        }

        public Criteria andStartingCodeGreaterThanOrEqualTo(String value) {
            addCriterion("starting_code >=", value, "startingCode");
            return (Criteria) this;
        }

        public Criteria andStartingCodeLessThan(String value) {
            addCriterion("starting_code <", value, "startingCode");
            return (Criteria) this;
        }

        public Criteria andStartingCodeLessThanOrEqualTo(String value) {
            addCriterion("starting_code <=", value, "startingCode");
            return (Criteria) this;
        }

        public Criteria andStartingCodeLike(String value) {
            addCriterion("starting_code like", value, "startingCode");
            return (Criteria) this;
        }

        public Criteria andStartingCodeNotLike(String value) {
            addCriterion("starting_code not like", value, "startingCode");
            return (Criteria) this;
        }

        public Criteria andStartingCodeIn(List<String> values) {
            addCriterion("starting_code in", values, "startingCode");
            return (Criteria) this;
        }

        public Criteria andStartingCodeNotIn(List<String> values) {
            addCriterion("starting_code not in", values, "startingCode");
            return (Criteria) this;
        }

        public Criteria andStartingCodeBetween(String value1, String value2) {
            addCriterion("starting_code between", value1, value2, "startingCode");
            return (Criteria) this;
        }

        public Criteria andStartingCodeNotBetween(String value1, String value2) {
            addCriterion("starting_code not between", value1, value2, "startingCode");
            return (Criteria) this;
        }

        public Criteria andDestinationCityIsNull() {
            addCriterion("destination_city is null");
            return (Criteria) this;
        }

        public Criteria andDestinationCityIsNotNull() {
            addCriterion("destination_city is not null");
            return (Criteria) this;
        }

        public Criteria andDestinationCityEqualTo(String value) {
            addCriterion("destination_city =", value, "destinationCity");
            return (Criteria) this;
        }

        public Criteria andDestinationCityNotEqualTo(String value) {
            addCriterion("destination_city <>", value, "destinationCity");
            return (Criteria) this;
        }

        public Criteria andDestinationCityGreaterThan(String value) {
            addCriterion("destination_city >", value, "destinationCity");
            return (Criteria) this;
        }

        public Criteria andDestinationCityGreaterThanOrEqualTo(String value) {
            addCriterion("destination_city >=", value, "destinationCity");
            return (Criteria) this;
        }

        public Criteria andDestinationCityLessThan(String value) {
            addCriterion("destination_city <", value, "destinationCity");
            return (Criteria) this;
        }

        public Criteria andDestinationCityLessThanOrEqualTo(String value) {
            addCriterion("destination_city <=", value, "destinationCity");
            return (Criteria) this;
        }

        public Criteria andDestinationCityLike(String value) {
            addCriterion("destination_city like", value, "destinationCity");
            return (Criteria) this;
        }

        public Criteria andDestinationCityNotLike(String value) {
            addCriterion("destination_city not like", value, "destinationCity");
            return (Criteria) this;
        }

        public Criteria andDestinationCityIn(List<String> values) {
            addCriterion("destination_city in", values, "destinationCity");
            return (Criteria) this;
        }

        public Criteria andDestinationCityNotIn(List<String> values) {
            addCriterion("destination_city not in", values, "destinationCity");
            return (Criteria) this;
        }

        public Criteria andDestinationCityBetween(String value1, String value2) {
            addCriterion("destination_city between", value1, value2, "destinationCity");
            return (Criteria) this;
        }

        public Criteria andDestinationCityNotBetween(String value1, String value2) {
            addCriterion("destination_city not between", value1, value2, "destinationCity");
            return (Criteria) this;
        }

        public Criteria andDestinationCodeIsNull() {
            addCriterion("destination_code is null");
            return (Criteria) this;
        }

        public Criteria andDestinationCodeIsNotNull() {
            addCriterion("destination_code is not null");
            return (Criteria) this;
        }

        public Criteria andDestinationCodeEqualTo(String value) {
            addCriterion("destination_code =", value, "destinationCode");
            return (Criteria) this;
        }

        public Criteria andDestinationCodeNotEqualTo(String value) {
            addCriterion("destination_code <>", value, "destinationCode");
            return (Criteria) this;
        }

        public Criteria andDestinationCodeGreaterThan(String value) {
            addCriterion("destination_code >", value, "destinationCode");
            return (Criteria) this;
        }

        public Criteria andDestinationCodeGreaterThanOrEqualTo(String value) {
            addCriterion("destination_code >=", value, "destinationCode");
            return (Criteria) this;
        }

        public Criteria andDestinationCodeLessThan(String value) {
            addCriterion("destination_code <", value, "destinationCode");
            return (Criteria) this;
        }

        public Criteria andDestinationCodeLessThanOrEqualTo(String value) {
            addCriterion("destination_code <=", value, "destinationCode");
            return (Criteria) this;
        }

        public Criteria andDestinationCodeLike(String value) {
            addCriterion("destination_code like", value, "destinationCode");
            return (Criteria) this;
        }

        public Criteria andDestinationCodeNotLike(String value) {
            addCriterion("destination_code not like", value, "destinationCode");
            return (Criteria) this;
        }

        public Criteria andDestinationCodeIn(List<String> values) {
            addCriterion("destination_code in", values, "destinationCode");
            return (Criteria) this;
        }

        public Criteria andDestinationCodeNotIn(List<String> values) {
            addCriterion("destination_code not in", values, "destinationCode");
            return (Criteria) this;
        }

        public Criteria andDestinationCodeBetween(String value1, String value2) {
            addCriterion("destination_code between", value1, value2, "destinationCode");
            return (Criteria) this;
        }

        public Criteria andDestinationCodeNotBetween(String value1, String value2) {
            addCriterion("destination_code not between", value1, value2, "destinationCode");
            return (Criteria) this;
        }

        public Criteria andStartingAirportIsNull() {
            addCriterion("starting_airport is null");
            return (Criteria) this;
        }

        public Criteria andStartingAirportIsNotNull() {
            addCriterion("starting_airport is not null");
            return (Criteria) this;
        }

        public Criteria andStartingAirportEqualTo(String value) {
            addCriterion("starting_airport =", value, "startingAirport");
            return (Criteria) this;
        }

        public Criteria andStartingAirportNotEqualTo(String value) {
            addCriterion("starting_airport <>", value, "startingAirport");
            return (Criteria) this;
        }

        public Criteria andStartingAirportGreaterThan(String value) {
            addCriterion("starting_airport >", value, "startingAirport");
            return (Criteria) this;
        }

        public Criteria andStartingAirportGreaterThanOrEqualTo(String value) {
            addCriterion("starting_airport >=", value, "startingAirport");
            return (Criteria) this;
        }

        public Criteria andStartingAirportLessThan(String value) {
            addCriterion("starting_airport <", value, "startingAirport");
            return (Criteria) this;
        }

        public Criteria andStartingAirportLessThanOrEqualTo(String value) {
            addCriterion("starting_airport <=", value, "startingAirport");
            return (Criteria) this;
        }

        public Criteria andStartingAirportLike(String value) {
            addCriterion("starting_airport like", value, "startingAirport");
            return (Criteria) this;
        }

        public Criteria andStartingAirportNotLike(String value) {
            addCriterion("starting_airport not like", value, "startingAirport");
            return (Criteria) this;
        }

        public Criteria andStartingAirportIn(List<String> values) {
            addCriterion("starting_airport in", values, "startingAirport");
            return (Criteria) this;
        }

        public Criteria andStartingAirportNotIn(List<String> values) {
            addCriterion("starting_airport not in", values, "startingAirport");
            return (Criteria) this;
        }

        public Criteria andStartingAirportBetween(String value1, String value2) {
            addCriterion("starting_airport between", value1, value2, "startingAirport");
            return (Criteria) this;
        }

        public Criteria andStartingAirportNotBetween(String value1, String value2) {
            addCriterion("starting_airport not between", value1, value2, "startingAirport");
            return (Criteria) this;
        }

        public Criteria andDestinationAirportIsNull() {
            addCriterion("destination_airport is null");
            return (Criteria) this;
        }

        public Criteria andDestinationAirportIsNotNull() {
            addCriterion("destination_airport is not null");
            return (Criteria) this;
        }

        public Criteria andDestinationAirportEqualTo(String value) {
            addCriterion("destination_airport =", value, "destinationAirport");
            return (Criteria) this;
        }

        public Criteria andDestinationAirportNotEqualTo(String value) {
            addCriterion("destination_airport <>", value, "destinationAirport");
            return (Criteria) this;
        }

        public Criteria andDestinationAirportGreaterThan(String value) {
            addCriterion("destination_airport >", value, "destinationAirport");
            return (Criteria) this;
        }

        public Criteria andDestinationAirportGreaterThanOrEqualTo(String value) {
            addCriterion("destination_airport >=", value, "destinationAirport");
            return (Criteria) this;
        }

        public Criteria andDestinationAirportLessThan(String value) {
            addCriterion("destination_airport <", value, "destinationAirport");
            return (Criteria) this;
        }

        public Criteria andDestinationAirportLessThanOrEqualTo(String value) {
            addCriterion("destination_airport <=", value, "destinationAirport");
            return (Criteria) this;
        }

        public Criteria andDestinationAirportLike(String value) {
            addCriterion("destination_airport like", value, "destinationAirport");
            return (Criteria) this;
        }

        public Criteria andDestinationAirportNotLike(String value) {
            addCriterion("destination_airport not like", value, "destinationAirport");
            return (Criteria) this;
        }

        public Criteria andDestinationAirportIn(List<String> values) {
            addCriterion("destination_airport in", values, "destinationAirport");
            return (Criteria) this;
        }

        public Criteria andDestinationAirportNotIn(List<String> values) {
            addCriterion("destination_airport not in", values, "destinationAirport");
            return (Criteria) this;
        }

        public Criteria andDestinationAirportBetween(String value1, String value2) {
            addCriterion("destination_airport between", value1, value2, "destinationAirport");
            return (Criteria) this;
        }

        public Criteria andDestinationAirportNotBetween(String value1, String value2) {
            addCriterion("destination_airport not between", value1, value2, "destinationAirport");
            return (Criteria) this;
        }

        public Criteria andStartingTerminalIsNull() {
            addCriterion("starting_terminal is null");
            return (Criteria) this;
        }

        public Criteria andStartingTerminalIsNotNull() {
            addCriterion("starting_terminal is not null");
            return (Criteria) this;
        }

        public Criteria andStartingTerminalEqualTo(String value) {
            addCriterion("starting_terminal =", value, "startingTerminal");
            return (Criteria) this;
        }

        public Criteria andStartingTerminalNotEqualTo(String value) {
            addCriterion("starting_terminal <>", value, "startingTerminal");
            return (Criteria) this;
        }

        public Criteria andStartingTerminalGreaterThan(String value) {
            addCriterion("starting_terminal >", value, "startingTerminal");
            return (Criteria) this;
        }

        public Criteria andStartingTerminalGreaterThanOrEqualTo(String value) {
            addCriterion("starting_terminal >=", value, "startingTerminal");
            return (Criteria) this;
        }

        public Criteria andStartingTerminalLessThan(String value) {
            addCriterion("starting_terminal <", value, "startingTerminal");
            return (Criteria) this;
        }

        public Criteria andStartingTerminalLessThanOrEqualTo(String value) {
            addCriterion("starting_terminal <=", value, "startingTerminal");
            return (Criteria) this;
        }

        public Criteria andStartingTerminalLike(String value) {
            addCriterion("starting_terminal like", value, "startingTerminal");
            return (Criteria) this;
        }

        public Criteria andStartingTerminalNotLike(String value) {
            addCriterion("starting_terminal not like", value, "startingTerminal");
            return (Criteria) this;
        }

        public Criteria andStartingTerminalIn(List<String> values) {
            addCriterion("starting_terminal in", values, "startingTerminal");
            return (Criteria) this;
        }

        public Criteria andStartingTerminalNotIn(List<String> values) {
            addCriterion("starting_terminal not in", values, "startingTerminal");
            return (Criteria) this;
        }

        public Criteria andStartingTerminalBetween(String value1, String value2) {
            addCriterion("starting_terminal between", value1, value2, "startingTerminal");
            return (Criteria) this;
        }

        public Criteria andStartingTerminalNotBetween(String value1, String value2) {
            addCriterion("starting_terminal not between", value1, value2, "startingTerminal");
            return (Criteria) this;
        }

        public Criteria andDestinationTerminalIsNull() {
            addCriterion("destination_terminal is null");
            return (Criteria) this;
        }

        public Criteria andDestinationTerminalIsNotNull() {
            addCriterion("destination_terminal is not null");
            return (Criteria) this;
        }

        public Criteria andDestinationTerminalEqualTo(String value) {
            addCriterion("destination_terminal =", value, "destinationTerminal");
            return (Criteria) this;
        }

        public Criteria andDestinationTerminalNotEqualTo(String value) {
            addCriterion("destination_terminal <>", value, "destinationTerminal");
            return (Criteria) this;
        }

        public Criteria andDestinationTerminalGreaterThan(String value) {
            addCriterion("destination_terminal >", value, "destinationTerminal");
            return (Criteria) this;
        }

        public Criteria andDestinationTerminalGreaterThanOrEqualTo(String value) {
            addCriterion("destination_terminal >=", value, "destinationTerminal");
            return (Criteria) this;
        }

        public Criteria andDestinationTerminalLessThan(String value) {
            addCriterion("destination_terminal <", value, "destinationTerminal");
            return (Criteria) this;
        }

        public Criteria andDestinationTerminalLessThanOrEqualTo(String value) {
            addCriterion("destination_terminal <=", value, "destinationTerminal");
            return (Criteria) this;
        }

        public Criteria andDestinationTerminalLike(String value) {
            addCriterion("destination_terminal like", value, "destinationTerminal");
            return (Criteria) this;
        }

        public Criteria andDestinationTerminalNotLike(String value) {
            addCriterion("destination_terminal not like", value, "destinationTerminal");
            return (Criteria) this;
        }

        public Criteria andDestinationTerminalIn(List<String> values) {
            addCriterion("destination_terminal in", values, "destinationTerminal");
            return (Criteria) this;
        }

        public Criteria andDestinationTerminalNotIn(List<String> values) {
            addCriterion("destination_terminal not in", values, "destinationTerminal");
            return (Criteria) this;
        }

        public Criteria andDestinationTerminalBetween(String value1, String value2) {
            addCriterion("destination_terminal between", value1, value2, "destinationTerminal");
            return (Criteria) this;
        }

        public Criteria andDestinationTerminalNotBetween(String value1, String value2) {
            addCriterion("destination_terminal not between", value1, value2, "destinationTerminal");
            return (Criteria) this;
        }

        public Criteria andIsMiddleStopIsNull() {
            addCriterion("is_middle_stop is null");
            return (Criteria) this;
        }

        public Criteria andIsMiddleStopIsNotNull() {
            addCriterion("is_middle_stop is not null");
            return (Criteria) this;
        }

        public Criteria andIsMiddleStopEqualTo(Boolean value) {
            addCriterion("is_middle_stop =", value, "isMiddleStop");
            return (Criteria) this;
        }

        public Criteria andIsMiddleStopNotEqualTo(Boolean value) {
            addCriterion("is_middle_stop <>", value, "isMiddleStop");
            return (Criteria) this;
        }

        public Criteria andIsMiddleStopGreaterThan(Boolean value) {
            addCriterion("is_middle_stop >", value, "isMiddleStop");
            return (Criteria) this;
        }

        public Criteria andIsMiddleStopGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_middle_stop >=", value, "isMiddleStop");
            return (Criteria) this;
        }

        public Criteria andIsMiddleStopLessThan(Boolean value) {
            addCriterion("is_middle_stop <", value, "isMiddleStop");
            return (Criteria) this;
        }

        public Criteria andIsMiddleStopLessThanOrEqualTo(Boolean value) {
            addCriterion("is_middle_stop <=", value, "isMiddleStop");
            return (Criteria) this;
        }

        public Criteria andIsMiddleStopIn(List<Boolean> values) {
            addCriterion("is_middle_stop in", values, "isMiddleStop");
            return (Criteria) this;
        }

        public Criteria andIsMiddleStopNotIn(List<Boolean> values) {
            addCriterion("is_middle_stop not in", values, "isMiddleStop");
            return (Criteria) this;
        }

        public Criteria andIsMiddleStopBetween(Boolean value1, Boolean value2) {
            addCriterion("is_middle_stop between", value1, value2, "isMiddleStop");
            return (Criteria) this;
        }

        public Criteria andIsMiddleStopNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_middle_stop not between", value1, value2, "isMiddleStop");
            return (Criteria) this;
        }

        public Criteria andDepartureTimestampIsNull() {
            addCriterion("departure_timestamp is null");
            return (Criteria) this;
        }

        public Criteria andDepartureTimestampIsNotNull() {
            addCriterion("departure_timestamp is not null");
            return (Criteria) this;
        }

        public Criteria andDepartureTimestampEqualTo(Date value) {
            addCriterion("departure_timestamp =", value, "departureTimestamp");
            return (Criteria) this;
        }

        public Criteria andDepartureTimestampNotEqualTo(Date value) {
            addCriterion("departure_timestamp <>", value, "departureTimestamp");
            return (Criteria) this;
        }

        public Criteria andDepartureTimestampGreaterThan(Date value) {
            addCriterion("departure_timestamp >", value, "departureTimestamp");
            return (Criteria) this;
        }

        public Criteria andDepartureTimestampGreaterThanOrEqualTo(Date value) {
            addCriterion("departure_timestamp >=", value, "departureTimestamp");
            return (Criteria) this;
        }

        public Criteria andDepartureTimestampLessThan(Date value) {
            addCriterion("departure_timestamp <", value, "departureTimestamp");
            return (Criteria) this;
        }

        public Criteria andDepartureTimestampLessThanOrEqualTo(Date value) {
            addCriterion("departure_timestamp <=", value, "departureTimestamp");
            return (Criteria) this;
        }

        public Criteria andDepartureTimestampIn(List<Date> values) {
            addCriterion("departure_timestamp in", values, "departureTimestamp");
            return (Criteria) this;
        }

        public Criteria andDepartureTimestampNotIn(List<Date> values) {
            addCriterion("departure_timestamp not in", values, "departureTimestamp");
            return (Criteria) this;
        }

        public Criteria andDepartureTimestampBetween(Date value1, Date value2) {
            addCriterion("departure_timestamp between", value1, value2, "departureTimestamp");
            return (Criteria) this;
        }

        public Criteria andDepartureTimestampNotBetween(Date value1, Date value2) {
            addCriterion("departure_timestamp not between", value1, value2, "departureTimestamp");
            return (Criteria) this;
        }

        public Criteria andArrivedTimestampIsNull() {
            addCriterion("arrived_timestamp is null");
            return (Criteria) this;
        }

        public Criteria andArrivedTimestampIsNotNull() {
            addCriterion("arrived_timestamp is not null");
            return (Criteria) this;
        }

        public Criteria andArrivedTimestampEqualTo(Date value) {
            addCriterion("arrived_timestamp =", value, "arrivedTimestamp");
            return (Criteria) this;
        }

        public Criteria andArrivedTimestampNotEqualTo(Date value) {
            addCriterion("arrived_timestamp <>", value, "arrivedTimestamp");
            return (Criteria) this;
        }

        public Criteria andArrivedTimestampGreaterThan(Date value) {
            addCriterion("arrived_timestamp >", value, "arrivedTimestamp");
            return (Criteria) this;
        }

        public Criteria andArrivedTimestampGreaterThanOrEqualTo(Date value) {
            addCriterion("arrived_timestamp >=", value, "arrivedTimestamp");
            return (Criteria) this;
        }

        public Criteria andArrivedTimestampLessThan(Date value) {
            addCriterion("arrived_timestamp <", value, "arrivedTimestamp");
            return (Criteria) this;
        }

        public Criteria andArrivedTimestampLessThanOrEqualTo(Date value) {
            addCriterion("arrived_timestamp <=", value, "arrivedTimestamp");
            return (Criteria) this;
        }

        public Criteria andArrivedTimestampIn(List<Date> values) {
            addCriterion("arrived_timestamp in", values, "arrivedTimestamp");
            return (Criteria) this;
        }

        public Criteria andArrivedTimestampNotIn(List<Date> values) {
            addCriterion("arrived_timestamp not in", values, "arrivedTimestamp");
            return (Criteria) this;
        }

        public Criteria andArrivedTimestampBetween(Date value1, Date value2) {
            addCriterion("arrived_timestamp between", value1, value2, "arrivedTimestamp");
            return (Criteria) this;
        }

        public Criteria andArrivedTimestampNotBetween(Date value1, Date value2) {
            addCriterion("arrived_timestamp not between", value1, value2, "arrivedTimestamp");
            return (Criteria) this;
        }

        public Criteria andParPriceIsNull() {
            addCriterion("par_price is null");
            return (Criteria) this;
        }

        public Criteria andParPriceIsNotNull() {
            addCriterion("par_price is not null");
            return (Criteria) this;
        }

        public Criteria andParPriceEqualTo(BigDecimal value) {
            addCriterion("par_price =", value, "parPrice");
            return (Criteria) this;
        }

        public Criteria andParPriceNotEqualTo(BigDecimal value) {
            addCriterion("par_price <>", value, "parPrice");
            return (Criteria) this;
        }

        public Criteria andParPriceGreaterThan(BigDecimal value) {
            addCriterion("par_price >", value, "parPrice");
            return (Criteria) this;
        }

        public Criteria andParPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("par_price >=", value, "parPrice");
            return (Criteria) this;
        }

        public Criteria andParPriceLessThan(BigDecimal value) {
            addCriterion("par_price <", value, "parPrice");
            return (Criteria) this;
        }

        public Criteria andParPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("par_price <=", value, "parPrice");
            return (Criteria) this;
        }

        public Criteria andParPriceIn(List<BigDecimal> values) {
            addCriterion("par_price in", values, "parPrice");
            return (Criteria) this;
        }

        public Criteria andParPriceNotIn(List<BigDecimal> values) {
            addCriterion("par_price not in", values, "parPrice");
            return (Criteria) this;
        }

        public Criteria andParPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("par_price between", value1, value2, "parPrice");
            return (Criteria) this;
        }

        public Criteria andParPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("par_price not between", value1, value2, "parPrice");
            return (Criteria) this;
        }

        public Criteria andFuelTaxIsNull() {
            addCriterion("fuel_tax is null");
            return (Criteria) this;
        }

        public Criteria andFuelTaxIsNotNull() {
            addCriterion("fuel_tax is not null");
            return (Criteria) this;
        }

        public Criteria andFuelTaxEqualTo(BigDecimal value) {
            addCriterion("fuel_tax =", value, "fuelTax");
            return (Criteria) this;
        }

        public Criteria andFuelTaxNotEqualTo(BigDecimal value) {
            addCriterion("fuel_tax <>", value, "fuelTax");
            return (Criteria) this;
        }

        public Criteria andFuelTaxGreaterThan(BigDecimal value) {
            addCriterion("fuel_tax >", value, "fuelTax");
            return (Criteria) this;
        }

        public Criteria andFuelTaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("fuel_tax >=", value, "fuelTax");
            return (Criteria) this;
        }

        public Criteria andFuelTaxLessThan(BigDecimal value) {
            addCriterion("fuel_tax <", value, "fuelTax");
            return (Criteria) this;
        }

        public Criteria andFuelTaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("fuel_tax <=", value, "fuelTax");
            return (Criteria) this;
        }

        public Criteria andFuelTaxIn(List<BigDecimal> values) {
            addCriterion("fuel_tax in", values, "fuelTax");
            return (Criteria) this;
        }

        public Criteria andFuelTaxNotIn(List<BigDecimal> values) {
            addCriterion("fuel_tax not in", values, "fuelTax");
            return (Criteria) this;
        }

        public Criteria andFuelTaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("fuel_tax between", value1, value2, "fuelTax");
            return (Criteria) this;
        }

        public Criteria andFuelTaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("fuel_tax not between", value1, value2, "fuelTax");
            return (Criteria) this;
        }

        public Criteria andAirportTaxIsNull() {
            addCriterion("airport_tax is null");
            return (Criteria) this;
        }

        public Criteria andAirportTaxIsNotNull() {
            addCriterion("airport_tax is not null");
            return (Criteria) this;
        }

        public Criteria andAirportTaxEqualTo(BigDecimal value) {
            addCriterion("airport_tax =", value, "airportTax");
            return (Criteria) this;
        }

        public Criteria andAirportTaxNotEqualTo(BigDecimal value) {
            addCriterion("airport_tax <>", value, "airportTax");
            return (Criteria) this;
        }

        public Criteria andAirportTaxGreaterThan(BigDecimal value) {
            addCriterion("airport_tax >", value, "airportTax");
            return (Criteria) this;
        }

        public Criteria andAirportTaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("airport_tax >=", value, "airportTax");
            return (Criteria) this;
        }

        public Criteria andAirportTaxLessThan(BigDecimal value) {
            addCriterion("airport_tax <", value, "airportTax");
            return (Criteria) this;
        }

        public Criteria andAirportTaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("airport_tax <=", value, "airportTax");
            return (Criteria) this;
        }

        public Criteria andAirportTaxIn(List<BigDecimal> values) {
            addCriterion("airport_tax in", values, "airportTax");
            return (Criteria) this;
        }

        public Criteria andAirportTaxNotIn(List<BigDecimal> values) {
            addCriterion("airport_tax not in", values, "airportTax");
            return (Criteria) this;
        }

        public Criteria andAirportTaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("airport_tax between", value1, value2, "airportTax");
            return (Criteria) this;
        }

        public Criteria andAirportTaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("airport_tax not between", value1, value2, "airportTax");
            return (Criteria) this;
        }

        public Criteria andSalePriceIsNull() {
            addCriterion("sale_price is null");
            return (Criteria) this;
        }

        public Criteria andSalePriceIsNotNull() {
            addCriterion("sale_price is not null");
            return (Criteria) this;
        }

        public Criteria andSalePriceEqualTo(BigDecimal value) {
            addCriterion("sale_price =", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceNotEqualTo(BigDecimal value) {
            addCriterion("sale_price <>", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceGreaterThan(BigDecimal value) {
            addCriterion("sale_price >", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("sale_price >=", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceLessThan(BigDecimal value) {
            addCriterion("sale_price <", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("sale_price <=", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceIn(List<BigDecimal> values) {
            addCriterion("sale_price in", values, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceNotIn(List<BigDecimal> values) {
            addCriterion("sale_price not in", values, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sale_price between", value1, value2, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sale_price not between", value1, value2, "salePrice");
            return (Criteria) this;
        }

        public Criteria andPassengerInfoListIsNull() {
            addCriterion("passenger_info_list is null");
            return (Criteria) this;
        }

        public Criteria andPassengerInfoListIsNotNull() {
            addCriterion("passenger_info_list is not null");
            return (Criteria) this;
        }

        public Criteria andPassengerInfoListEqualTo(String value) {
            addCriterion("passenger_info_list =", value, "passengerInfoList");
            return (Criteria) this;
        }

        public Criteria andPassengerInfoListNotEqualTo(String value) {
            addCriterion("passenger_info_list <>", value, "passengerInfoList");
            return (Criteria) this;
        }

        public Criteria andPassengerInfoListGreaterThan(String value) {
            addCriterion("passenger_info_list >", value, "passengerInfoList");
            return (Criteria) this;
        }

        public Criteria andPassengerInfoListGreaterThanOrEqualTo(String value) {
            addCriterion("passenger_info_list >=", value, "passengerInfoList");
            return (Criteria) this;
        }

        public Criteria andPassengerInfoListLessThan(String value) {
            addCriterion("passenger_info_list <", value, "passengerInfoList");
            return (Criteria) this;
        }

        public Criteria andPassengerInfoListLessThanOrEqualTo(String value) {
            addCriterion("passenger_info_list <=", value, "passengerInfoList");
            return (Criteria) this;
        }

        public Criteria andPassengerInfoListLike(String value) {
            addCriterion("passenger_info_list like", value, "passengerInfoList");
            return (Criteria) this;
        }

        public Criteria andPassengerInfoListNotLike(String value) {
            addCriterion("passenger_info_list not like", value, "passengerInfoList");
            return (Criteria) this;
        }

        public Criteria andPassengerInfoListIn(List<String> values) {
            addCriterion("passenger_info_list in", values, "passengerInfoList");
            return (Criteria) this;
        }

        public Criteria andPassengerInfoListNotIn(List<String> values) {
            addCriterion("passenger_info_list not in", values, "passengerInfoList");
            return (Criteria) this;
        }

        public Criteria andPassengerInfoListBetween(String value1, String value2) {
            addCriterion("passenger_info_list between", value1, value2, "passengerInfoList");
            return (Criteria) this;
        }

        public Criteria andPassengerInfoListNotBetween(String value1, String value2) {
            addCriterion("passenger_info_list not between", value1, value2, "passengerInfoList");
            return (Criteria) this;
        }

        public Criteria andErrMsgIsNull() {
            addCriterion("err_msg is null");
            return (Criteria) this;
        }

        public Criteria andErrMsgIsNotNull() {
            addCriterion("err_msg is not null");
            return (Criteria) this;
        }

        public Criteria andErrMsgEqualTo(String value) {
            addCriterion("err_msg =", value, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgNotEqualTo(String value) {
            addCriterion("err_msg <>", value, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgGreaterThan(String value) {
            addCriterion("err_msg >", value, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgGreaterThanOrEqualTo(String value) {
            addCriterion("err_msg >=", value, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgLessThan(String value) {
            addCriterion("err_msg <", value, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgLessThanOrEqualTo(String value) {
            addCriterion("err_msg <=", value, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgLike(String value) {
            addCriterion("err_msg like", value, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgNotLike(String value) {
            addCriterion("err_msg not like", value, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgIn(List<String> values) {
            addCriterion("err_msg in", values, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgNotIn(List<String> values) {
            addCriterion("err_msg not in", values, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgBetween(String value1, String value2) {
            addCriterion("err_msg between", value1, value2, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgNotBetween(String value1, String value2) {
            addCriterion("err_msg not between", value1, value2, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrCodeIsNull() {
            addCriterion("err_code is null");
            return (Criteria) this;
        }

        public Criteria andErrCodeIsNotNull() {
            addCriterion("err_code is not null");
            return (Criteria) this;
        }

        public Criteria andErrCodeEqualTo(Integer value) {
            addCriterion("err_code =", value, "errCode");
            return (Criteria) this;
        }

        public Criteria andErrCodeNotEqualTo(Integer value) {
            addCriterion("err_code <>", value, "errCode");
            return (Criteria) this;
        }

        public Criteria andErrCodeGreaterThan(Integer value) {
            addCriterion("err_code >", value, "errCode");
            return (Criteria) this;
        }

        public Criteria andErrCodeGreaterThanOrEqualTo(Integer value) {
            addCriterion("err_code >=", value, "errCode");
            return (Criteria) this;
        }

        public Criteria andErrCodeLessThan(Integer value) {
            addCriterion("err_code <", value, "errCode");
            return (Criteria) this;
        }

        public Criteria andErrCodeLessThanOrEqualTo(Integer value) {
            addCriterion("err_code <=", value, "errCode");
            return (Criteria) this;
        }

        public Criteria andErrCodeIn(List<Integer> values) {
            addCriterion("err_code in", values, "errCode");
            return (Criteria) this;
        }

        public Criteria andErrCodeNotIn(List<Integer> values) {
            addCriterion("err_code not in", values, "errCode");
            return (Criteria) this;
        }

        public Criteria andErrCodeBetween(Integer value1, Integer value2) {
            addCriterion("err_code between", value1, value2, "errCode");
            return (Criteria) this;
        }

        public Criteria andErrCodeNotBetween(Integer value1, Integer value2) {
            addCriterion("err_code not between", value1, value2, "errCode");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdIsNull() {
            addCriterion("cost_center_id is null");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdIsNotNull() {
            addCriterion("cost_center_id is not null");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdEqualTo(String value) {
            addCriterion("cost_center_id =", value, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdNotEqualTo(String value) {
            addCriterion("cost_center_id <>", value, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdGreaterThan(String value) {
            addCriterion("cost_center_id >", value, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdGreaterThanOrEqualTo(String value) {
            addCriterion("cost_center_id >=", value, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdLessThan(String value) {
            addCriterion("cost_center_id <", value, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdLessThanOrEqualTo(String value) {
            addCriterion("cost_center_id <=", value, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdLike(String value) {
            addCriterion("cost_center_id like", value, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdNotLike(String value) {
            addCriterion("cost_center_id not like", value, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdIn(List<String> values) {
            addCriterion("cost_center_id in", values, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdNotIn(List<String> values) {
            addCriterion("cost_center_id not in", values, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdBetween(String value1, String value2) {
            addCriterion("cost_center_id between", value1, value2, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdNotBetween(String value1, String value2) {
            addCriterion("cost_center_id not between", value1, value2, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeIsNull() {
            addCriterion("cost_center_type is null");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeIsNotNull() {
            addCriterion("cost_center_type is not null");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeEqualTo(Integer value) {
            addCriterion("cost_center_type =", value, "costCenterType");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeNotEqualTo(Integer value) {
            addCriterion("cost_center_type <>", value, "costCenterType");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeGreaterThan(Integer value) {
            addCriterion("cost_center_type >", value, "costCenterType");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("cost_center_type >=", value, "costCenterType");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeLessThan(Integer value) {
            addCriterion("cost_center_type <", value, "costCenterType");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeLessThanOrEqualTo(Integer value) {
            addCriterion("cost_center_type <=", value, "costCenterType");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeIn(List<Integer> values) {
            addCriterion("cost_center_type in", values, "costCenterType");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeNotIn(List<Integer> values) {
            addCriterion("cost_center_type not in", values, "costCenterType");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeBetween(Integer value1, Integer value2) {
            addCriterion("cost_center_type between", value1, value2, "costCenterType");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("cost_center_type not between", value1, value2, "costCenterType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeIsNull() {
            addCriterion("exceed_buy_type is null");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeIsNotNull() {
            addCriterion("exceed_buy_type is not null");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeEqualTo(Integer value) {
            addCriterion("exceed_buy_type =", value, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeNotEqualTo(Integer value) {
            addCriterion("exceed_buy_type <>", value, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeGreaterThan(Integer value) {
            addCriterion("exceed_buy_type >", value, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("exceed_buy_type >=", value, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeLessThan(Integer value) {
            addCriterion("exceed_buy_type <", value, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeLessThanOrEqualTo(Integer value) {
            addCriterion("exceed_buy_type <=", value, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeIn(List<Integer> values) {
            addCriterion("exceed_buy_type in", values, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeNotIn(List<Integer> values) {
            addCriterion("exceed_buy_type not in", values, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeBetween(Integer value1, Integer value2) {
            addCriterion("exceed_buy_type between", value1, value2, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("exceed_buy_type not between", value1, value2, "exceedBuyType");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table air_intercept_record
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table air_intercept_record
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}