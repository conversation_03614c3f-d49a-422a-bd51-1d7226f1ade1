package com.fenbeitong.saas.core.contract.reason;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/11
 */
public class BusinessOrderWriteOffConfig {

    /**
     * biz_types : [7,11,15]
     * train_ticket_collect : 1
     * start_date : 2020-05-01
     */

    private List<Integer> biz_types;
    private Integer train_ticket_collect;
    private String start_date;
    private String individual_card_cost_info;//虚拟卡费用文案说明

    public List<Integer> getBiz_types() {
        return biz_types;
    }

    public void setBiz_types(List<Integer> biz_types) {
        this.biz_types = biz_types;
    }

    public Integer getTrain_ticket_collect() {
        return train_ticket_collect;
    }

    public void setTrain_ticket_collect(Integer train_ticket_collect) {
        this.train_ticket_collect = train_ticket_collect;
    }

    public String getStart_date() {
        return start_date;
    }

    public void setStart_date(String start_date) {
        this.start_date = start_date;
    }

    public String getIndividual_card_cost_info() {
        return individual_card_cost_info;
    }

    public void setIndividual_card_cost_info(String individual_card_cost_info) {
        this.individual_card_cost_info = individual_card_cost_info;
    }
}
