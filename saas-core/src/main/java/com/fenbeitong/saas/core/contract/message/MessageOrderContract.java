package com.fenbeitong.saas.core.contract.message;

import com.fenbeitong.saas.core.contract.message.inner.OrderInfo;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/4/17.
 */
public class MessageOrderContract {

    private Integer total_count;

    private Integer count;

    private List<Order> message_list;

    public Integer getTotal_count() {
        return total_count;
    }

    public void setTotal_count(Integer total_count) {
        this.total_count = total_count;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public List<Order> getMessage_list() {
        return message_list;
    }

    public void setMessage_list(List<Order> message_list) {
        this.message_list = message_list;
    }

    public static class Order extends OrderInfo{

        private String id;

        private String title;

        private String content;

        private Integer message_type;

        private String link;

        private String biz_order;

        public String getBiz_order() {
            return biz_order;
        }

        public void setBiz_order(String biz_order) {
            this.biz_order = biz_order;
        }
        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public Integer getMessage_type() {
            return message_type;
        }

        public void setMessage_type(Integer message_type) {
            this.message_type = message_type;
        }

        public String getLink() {
            return link;
        }

        public void setLink(String link) {
            this.link = link;
        }
    }
}
