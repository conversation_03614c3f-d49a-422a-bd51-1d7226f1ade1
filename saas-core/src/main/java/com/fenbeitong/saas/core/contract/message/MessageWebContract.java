package com.fenbeitong.saas.core.contract.message;

import com.fenbeitong.saas.core.model.saas.MessageSetupReceiver;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/9/17 9:10 下午
 */
@Data
public class MessageWebContract {

    /**
     * 消息类型
     */
    private Integer messageType;

    /**
     * 消息子类
     */
    private Integer messageSubType;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 消息发送人
     */
    private List<String> receiverIdList;

    /**
     * 消息标题
     */
    private String title;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 业务类型
     */
    private Integer bizeType;

    /**
     * 订单id
     */
    private String bizOrder;


}
