package com.fenbeitong.saas.core.contract.applyflow;

import java.util.List;

/**
 * Created by zhaohaichao on 2019/11/28.
 */
public class ApplyFlowSaveContract {
    private String companyId;
    private String userId;
    private List<ApplyFlowContract> applyFlowList;

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public List<ApplyFlowContract> getApplyFlowList() {
        return applyFlowList;
    }

    public void setApplyFlowList(List<ApplyFlowContract> applyFlowList) {
        this.applyFlowList = applyFlowList;
    }
}
