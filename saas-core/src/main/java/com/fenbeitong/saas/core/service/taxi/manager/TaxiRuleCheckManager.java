package com.fenbeitong.saas.core.service.taxi.manager;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.harmony.city.contrail.common.RpcCommonResult;
import com.fenbeitong.harmony.city.contrail.dto.airport.AirportDTO;
import com.fenbeitong.harmony.city.contrail.dto.trainstation.TrainStationDTO;
import com.fenbeitong.harmony.city.contrail.param.ContrailParam;
import com.fenbeitong.harmony.city.contrail.service.airport.IAirportContrailSearchService;
import com.fenbeitong.harmony.city.contrail.service.trainstation.ITrainStationContrailService;
import com.fenbeitong.noc.api.car.resp.CarOrderConsumeInfo;
import com.fenbeitong.saas.api.model.dto.reason.ReasonConfig;
import com.fenbeitong.saas.api.model.dto.rule.EmployeeTaxiRuleInfo;
import com.fenbeitong.saas.api.model.dto.rule.RuleTimeRange;
import com.fenbeitong.saas.api.model.enums.TaxiCategory;
import com.fenbeitong.saas.core.common.base.SaasException;
import com.fenbeitong.saas.core.common.constant.CommonSwitchConstant;
import com.fenbeitong.saas.core.common.constant.GlobalResponseCode;
import com.fenbeitong.saas.core.common.constant.RedisKeyConstant;
import com.fenbeitong.saas.core.common.constant.SaasMessageConstant;
import com.fenbeitong.saas.core.common.constant.TemporaryResponseCode;
import com.fenbeitong.saas.core.contract.applyflow.KvContract;
import com.fenbeitong.saas.api.model.dto.city.HarmonyCityInfo;
import com.fenbeitong.saas.core.contract.city.LocationCityContract;
import com.fenbeitong.saas.core.contract.order.apply.CustomFormContext;
import com.fenbeitong.saas.core.contract.order.check.*;
import com.fenbeitong.saas.core.contract.rule.PriceLimitPayTips;
import com.fenbeitong.saas.core.contract.rule.TaxiLimitPayTipContract;
import com.fenbeitong.saas.core.contract.rule.TaxiRuleListContract;
import com.fenbeitong.saas.core.contract.rule.TaxiRulePath;
import com.fenbeitong.saas.core.dao.fenbeitong.TaxiLocationMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.TaxiTimeRangeMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.LocationCollectionMapper;
import com.fenbeitong.saas.core.dao.saasplus.CustomFormApplyConfigMapper;
import com.fenbeitong.saas.core.model.enums.apply.ApplyType;
import com.fenbeitong.saas.core.model.enums.apply.TotalEstimatedLimitType;
import com.fenbeitong.saas.core.model.enums.message.BizType;
import com.fenbeitong.saas.core.model.enums.reason.ReasonType;
import com.fenbeitong.saas.core.model.enums.rule.*;
import com.fenbeitong.saas.core.model.enums.rule.taxi.AllowSameCityLimitType;
import com.fenbeitong.saas.core.model.fenbeitong.LocationCollection;
import com.fenbeitong.saas.core.model.fenbeitong.LocationCollectionExample;
import com.fenbeitong.saas.core.model.fenbeitong.TaxiLocation;
import com.fenbeitong.saas.core.model.fenbeitong.TaxiLocationExample;
import com.fenbeitong.saas.core.model.fenbeitong.TaxiTimeRange;
import com.fenbeitong.saas.core.model.fenbeitong.TaxiTimeRangeExample;
import com.fenbeitong.saas.core.model.saas.MessageSetup;
import com.fenbeitong.saas.core.model.saasplus.CustomFormApplyConfig;
import com.fenbeitong.saas.core.model.saasplus.CustomFormApplyConfigExample;
import com.fenbeitong.saas.core.service.ICityService;
import com.fenbeitong.saas.core.service.ICustomReasonService;
import com.fenbeitong.saas.core.service.IMessageSetupService;
import com.fenbeitong.saas.core.service.taxi.context.TaxiFloatingConfig;
import com.fenbeitong.saas.core.service.taxi.enums.TaxiTypeEnum;
import com.fenbeitong.saas.core.service.taxi.helper.TaxiApplyHelper;
import com.fenbeitong.saas.core.service.taxi.helper.TaxiRuleTimeHelper;
import com.fenbeitong.saas.core.service.taxi.inner.IDingtalkAttendanceService;
import com.fenbeitong.saas.core.service.taxi.rules.PriceRuleCheckContext;
import com.fenbeitong.saas.core.service.uc.CompanyCalendarService;
import com.fenbeitong.saas.core.utils.cache.RedisService;
import com.fenbeitong.saas.core.utils.city.CityUtils;
import com.fenbeitong.saas.core.utils.tools.BigDecimalTool;
import com.fenbeitong.saas.core.utils.tools.LocationUtils;
import com.fenbeitong.saas.core.utils.tools.StringTool;
import com.fenbeitong.saasplus.api.model.dto.custform.CustomFormTotalEstimatedOptionDTO;
import com.fenbeitong.saasplus.api.model.dto.rule.TaxiApproveRuleGroupContract;
import com.fenbeitong.saasplus.api.model.dto.rule.taxi.CompanyAreaLevelGroupDetailDTO;
import com.fenbeitong.saasplus.api.model.dto.rule.taxi.SpecifyCityDTO;
import com.fenbeitong.saasplus.api.model.dto.rule.taxi.TaxiAllowCrossCityDTO;
import com.fenbeitong.saasplus.api.model.dto.rule.taxi.TaxiAllowCrossCityReqDTO;
import com.fenbeitong.saasplus.api.model.dto.rule.taxi.TaxiAllowCrossCityResDTO;
import com.fenbeitong.saasplus.api.model.dto.rule.taxi.TaxiCommonRuleDTO;
import com.fenbeitong.saasplus.api.model.enums.rule.CityRestrictionType;
import com.fenbeitong.saasplus.api.model.enums.rule.DayPriceLimitTypeEnum;
import com.fenbeitong.saasplus.api.model.enums.rule.SpecifyCityType;
import com.fenbeitong.saasplus.api.model.enums.rule.TaxiApproveCityLimitTypeEnum;
import com.fenbeitong.saasplus.api.model.enums.rule.TaxiApproveEmployeeLimitTypeEnum;
import com.fenbeitong.saasplus.api.model.enums.rule.TaxiExceedConfigEnum;
import com.fenbeitong.saasplus.api.model.po.fenbeitong.rulenew.TimesLimitTypeEnum;
import com.fenbeitong.saasplus.api.model.po.fenbeitong.taxi.TaxiExceedConfigPo;
import com.fenbeitong.saasplus.api.model.enums.rule.TaxiType;
import com.fenbeitong.saasplus.api.service.custform.ICustomFormService;
import com.fenbeitong.saasplus.api.service.rule.IRuleService;
import com.fenbeitong.saasplus.api.service.taxi.ITaxiRuleCityService;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.fenbeitong.usercenter.api.service.employee.IBaseEmployeeExtService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.DateUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.StrUtils;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *     用车叫车前管控业务处理
 * </p>
 */
@Slf4j
@Component
public class TaxiRuleCheckManager {

    private Logger logger = LoggerFactory.getLogger(TaxiRuleCheckManager.class);
    @Autowired
    private RedisService redisService;
    @Resource
    private ITrainStationContrailService iTrainStationContrailService;
    @Resource
    private IAirportContrailSearchService iAirportContrailSearchService;
    @Autowired
    private TaxiTimeRangeMapper taxiTimeRangeMapper;
    @Autowired
    private CompanyCalendarService companyCalendarService;
    @Resource
    TaxiLocationMapper taxiLocationMapper;
    @Autowired
    private TaxiUserUsedDataManager taxiUserUsedDataManager;
    @Autowired
    private ICustomReasonService customReasonService;
    @Resource
    ICustomFormService iCustomFormService;

    @Resource
    FlowExecutor flowExecutor;

    @Autowired
    private IBaseEmployeeExtService iBaseEmployeeExtService;

    @Autowired
    private ICityService iCityService;

    @Autowired
    private IRuleService iRuleService;

    @Autowired
    private IDingtalkAttendanceService iDingtalkAttendanceService;

    @Autowired
    private ITaxiRuleCityService iTaxiRuleCityService;

    @Autowired
    private IMessageSetupService iMessageSetupService;

    @Autowired
    private LocationCollectionMapper locationCollectionMapper;

    @Autowired
    private CustomFormApplyConfigMapper customFormApplyConfigMapper;

    /**
     * 用车获取可用车型校验其它除金额和车型外的规则信息
     *
     * @param taxiOrderCheckRule
     * @param taxiAvailableReqContract
     * @return
     */
    public List<TempOrderRuleCheckResult> checkTaxiNotPriceExceedType(TaxiOrderCheckRule taxiOrderCheckRule,
                                                                      TaxiAvailableReqContract taxiAvailableReqContract) {
        List<TempOrderRuleCheckResult> listMap = new ArrayList<>();
        Integer useRule = taxiOrderCheckRule.getUseRule();
        Integer ruleId = null;
        Integer taxiApplyRuleId = -1;
        Boolean limitPath = false;
        List<TaxiRulePath> taxiRulePathList = null;
        List<TaxiRuleListContract.Path> pathList = null;
        Boolean limitCity = false;
        List<String> cityIds = null;
        Integer allowSameCityType = AllowSameCityLimitType.NOT_LIMIT.getCode();
        Boolean limitTime = false;
        List<RuleTimeRange> taxiTimeRangesAll = null;
        Boolean limitDate = false;
        String startTime = null;
        String endTime = null;
        String startDateTime = null;
        String endDateTime = null;

        //用车次数
        Boolean timesLimitFlag = false;
        Integer dayTimesLimit = 0;
        Integer periodHour = 0;
        Integer periodMinute = 0;

        // 用车城市限制
        TaxiCommonRuleDTO taxiCommonRuleDTO = new TaxiCommonRuleDTO();
        Integer limitPathType = null;
        Double applyLocationLimitRadius = 0.00;
        EmployeeTaxiRuleInfo.ApplyLimitLocation applyStartAddress = null;
        EmployeeTaxiRuleInfo.ApplyLimitLocation applyArrivalAddress = null;
        Integer checkStartAddress = null;
        Integer checkArrivalAddress = null;

        if (useRule == 1) {//普通用车
            TaxiRuleListContract taxiRuleListContract = taxiOrderCheckRule.getTaxiRuleListContract();
            ruleId = taxiRuleListContract.getId();
            // 用车位置限制
            limitPath = taxiRuleListContract.getLimitPath();
            limitPathType = taxiRuleListContract.getLimitPath() != null && taxiRuleListContract.getLimitPath() ? 1 : 0;
            taxiRulePathList = taxiRuleListContract.getPathLocationInfos();
            pathList = taxiRuleListContract.getPath();
            // 同城限制
            allowSameCityType = taxiRuleListContract.getAllowSameCityType();
            // 时段限制
            limitTime = taxiRuleListContract.getLimitTime();
            taxiTimeRangesAll = taxiRuleListContract.getTimeRange();

            timesLimitFlag = taxiRuleListContract.getTimesLimitFlag();
            dayTimesLimit = taxiRuleListContract.getDayTimesLimit();

            periodHour = StringUtils.isBlank(taxiRuleListContract.getPeriodHour())? periodHour : Integer.valueOf(taxiRuleListContract.getPeriodHour());
            periodMinute = StringUtils.isBlank(taxiRuleListContract.getPeriodMinute())? periodMinute : Integer.valueOf(taxiRuleListContract.getPeriodMinute());

            // 用车城市限制
            BeanUtils.copyProperties(taxiRuleListContract, taxiCommonRuleDTO);

        } else if (useRule == 2) {//申请用车
            TaxiApplyRule taxiApplyRule = taxiOrderCheckRule.getTaxiApplyRule();
            ruleId = -1; //申请用车设置默认值，防止下面查询的时候空指针
            if (Objects.nonNull(taxiApplyRule) && Objects.nonNull(taxiApplyRule.getId())) {
                taxiApplyRuleId = taxiApplyRule.getId();
            }
            // 上车城市限制
            limitCity = !ObjUtils.isNotEmpty(taxiApplyRule.getCityLimit()) || taxiApplyRule.getCityLimit() != 0;
            cityIds = taxiApplyRule.getStartCityIds();
            // 同城限制
            allowSameCityType = taxiApplyRule.getAllowSameCityType();
            timesLimitFlag = taxiApplyRule.getTimesLimitFlag() == 1;
            Integer timesLimitType = taxiApplyRule.getTimesLimitType();
            if(Objects.equals(TimesLimitTypeEnum.day_count_limit.getCode(),timesLimitType)) {
                dayTimesLimit = taxiApplyRule.getTimesLimit();
            }
            // 日期限制
            limitDate = true;
            startTime = taxiApplyRule.getStartTime();
            endTime = taxiApplyRule.getEndTime();
            startDateTime = taxiApplyRule.getStartDateTime();
            endDateTime = taxiApplyRule.getEndDateTime();
            // 时段限制
            limitTime = Optional.ofNullable(taxiOrderCheckRule.getTaxiApplyRule().getLimitTime()).orElse(false);
            taxiTimeRangesAll = taxiApplyRule.getTimeRange();
            // 用车位置限制
            limitPath = Optional.ofNullable(taxiApplyRule.getLimitPath()).orElse(false);
            limitPathType = taxiApplyRule.getLimitPathType();
            if (limitPathType==null){
                limitPathType = taxiApplyRule.getLimitPath() != null && taxiApplyRule.getLimitPath() ? 1 : 0;
            }
            if (taxiApplyRule.getLimitPathType() != null && Objects.equals(2, taxiApplyRule.getLimitPathType())) {
                applyLocationLimitRadius = taxiApplyRule.getApplyLocationLimitRadius();
                applyStartAddress = JSONObject.parseObject(JSONObject.toJSONString(taxiApplyRule.getStartTaxiAddress()), EmployeeTaxiRuleInfo.ApplyLimitLocation.class);
                applyArrivalAddress = JSONObject.parseObject(JSONObject.toJSONString(taxiApplyRule.getArrivalTaxiAddress()), EmployeeTaxiRuleInfo.ApplyLimitLocation.class);
            }
            checkStartAddress = taxiApplyRule.getCheckStartAddress();
            checkArrivalAddress = taxiApplyRule.getCheckArrivalAddress();
            List<com.fenbeitong.saasplus.api.model.dto.rule.TaxiRulePath> taxiRulePaths =
                    taxiApplyRule.getPathLocationInfos();
            if(CollectionUtils.isNotEmpty(taxiRulePaths)) {
                List<TaxiRulePath> taxiRulePathsTemp = new ArrayList<>();
                taxiRulePaths.forEach(taxiRulePath -> {
                    TaxiRulePath taxiRulePathTemp = new TaxiRulePath();
                    taxiRulePathTemp.setArrivalLocationId(taxiRulePath.getArrivalLocationId());
                    taxiRulePathTemp.setLimitPathType(taxiRulePath.getLimitPathType());
                    taxiRulePathTemp.setLimitArrival(taxiRulePath.getLimitArrival());
                    taxiRulePathTemp.setLimitArrivalType(taxiRulePath.getLimitArrivalType());
                    taxiRulePathTemp.setLimitDeparture(taxiRulePath.getLimitDeparture());
                    taxiRulePathTemp.setDepartureLocationId(taxiRulePath.getDepartureLocationId());
                    taxiRulePathTemp.setLimitDepartureType(taxiRulePath.getLimitDepartureType());
                    taxiRulePathsTemp.add(taxiRulePathTemp);
                });
                taxiRulePathList = taxiRulePathsTemp;
                pathList = getPathList(taxiRulePaths, taxiOrderCheckRule.getCompanyId());
            }

            // 用车城市限制
            BeanUtils.copyProperties(taxiApplyRule, taxiCommonRuleDTO);
        } else {
            logger.error("用车规则校验类型错误, useRule:{}", useRule);
        }
        EmployeeTaxiRuleInfo ruleInfo = new EmployeeTaxiRuleInfo();
        ruleInfo.setLimitPath(limitPath);
        ruleInfo.setLimitPathType(limitPathType);
        ruleInfo.setApplyLocationLimitRadius(applyLocationLimitRadius);
        ruleInfo.setApplyStartAddress(applyStartAddress);
        ruleInfo.setApplyArrivalAddress(applyArrivalAddress);
        ruleInfo.setCheckStartAddress(checkStartAddress);
        ruleInfo.setCheckArrivalAddress(checkArrivalAddress);
        ruleInfo.setTaxiRulePathList(getPathRule(taxiRulePathList, pathList));
        ruleInfo.setLimitCity(limitCity);
        ruleInfo.setLimitCityIds(cityIds);
        ruleInfo.setAllowSameCityType(allowSameCityType);
        TaxiModifyDestinationCheckReqContract.Location dep = new TaxiModifyDestinationCheckReqContract.Location();
        dep.setCity_code(taxiAvailableReqContract.getStart_city_id());
        dep.setCity_name("");
        dep.setLat(taxiAvailableReqContract.getDeparture_lat());
        dep.setLng(taxiAvailableReqContract.getDeparture_lng());
        TaxiModifyDestinationCheckReqContract.Location arr = new TaxiModifyDestinationCheckReqContract.Location();
        arr.setCity_code(taxiAvailableReqContract.getArrival_city_id());
        arr.setCity_name("");
        arr.setLat(taxiAvailableReqContract.getArrival_lat());
        arr.setLng(taxiAvailableReqContract.getArrival_lng());

        //校验用车地点
        listMap.addAll(checkTaxiLocationRuleExceedType(ruleInfo, dep, arr, useRule, ruleId, taxiApplyRuleId,
            taxiAvailableReqContract.getCompany_id(), taxiAvailableReqContract.getHome_location()));

        //校验用车时段不符合规则
        if (limitTime!=null && limitTime && CollectionUtils.isNotEmpty(taxiTimeRangesAll)) {
            // 时间范围校验结果
            boolean isTimeRangeExceed = false;

            // 是否按照节假日的新类型时间范围校验
            List<Integer> newTimeRangeType = Lists.newArrayList(10, 11, 12);
            boolean isNewTimeRangeType = false;
            for (RuleTimeRange range : taxiTimeRangesAll) {
                if (newTimeRangeType.contains(range.getDayType())) {
                    isNewTimeRangeType = true;
                    break;
                }
            }

            // 根据企业日历校验时间范围
            if (isNewTimeRangeType) {
                String departureTime = taxiAvailableReqContract.getDeparture_time();
                Date departureDate = DateUtils.parse(departureTime);
                String companyId = taxiAvailableReqContract.getCompany_id();
                Integer type = companyCalendarService.getCompanyCalendarDayType(companyId, departureTime);
                logger.info("校验时间范围,当前日期类型:{}", type);
                boolean result = TaxiRuleTimeHelper.checkHolidayNotOverNightTimeRange(taxiTimeRangesAll, departureTime, type);
                logger.info("校验时间范围,当天结果:{}", JsonUtils.toJson(result));

                // 往前一天
                Date preDepartureDate = com.fenbeitong.finhub.common.utils.DateUtils.addDay(departureDate, -1);
                String preDepartureTime = DateUtils.format(preDepartureDate);
                Integer preDayType = companyCalendarService.getCompanyCalendarDayType(companyId, preDepartureTime);
                boolean preDayResult = TaxiRuleTimeHelper.checkHolidayOverNightTimeRange(taxiTimeRangesAll, preDepartureTime, departureTime, preDayType);
                logger.info("校验时间范围,前一天结果:{}", preDayResult);
                // 如果当天和往前一天均超规 时间范围项超规
                if (!result && !preDayResult) {
                    isTimeRangeExceed = true;
                }
            }
            if (!isNewTimeRangeType) {
                Integer dayType = TaxiRuleTimeHelper.getDayType(taxiAvailableReqContract.getDeparture_time());
                TaxiTimeRangeExample taxiTimeRangeExample = new TaxiTimeRangeExample();
                List<Integer> dayTypeList = new ArrayList<>();
                dayTypeList.add(dayType);
                if (dayType >= DayTempType.FIRSTDAY.getCode() && dayType <= DayTempType.FIVEDAY.getCode()) {
                    //周一时，默认加入周日的时间规则
                    if (dayType == DayTempType.FIRSTDAY.getCode()) {
                        dayTypeList.add(DayTempType.SEVENDAY.getCode());
                        dayTypeList.add(9);
                    } else {
                        dayTypeList.add(dayType - 1);
                    }
                    dayTypeList.add(8);
                    taxiTimeRangeExample.createCriteria().andRuleIdEqualTo(ruleId).andDayTypeIn(dayTypeList);
                } else {
                    if (dayType == DayTempType.SIXDAY.getCode()) {
                        dayTypeList.add(dayType - 1);
                        dayTypeList.add(8);
                    } else {
                        dayTypeList.add(dayType - 1);
                    }
                    dayTypeList.add(9);
                    taxiTimeRangeExample.createCriteria().andRuleIdEqualTo(ruleId).andDayTypeIn(dayTypeList);
                }
                List<TaxiTimeRange> taxiTimeRanges = new ArrayList<>();
                if(useRule == 1){
                    taxiTimeRanges = taxiTimeRangeMapper.selectByExample(taxiTimeRangeExample);
                    logger.info("时间段校验，taxiTimeRanges｜{}｜useRule = 1|{}", JSONUtil.toJsonStr(taxiTimeRanges), useRule);
                } else if(useRule == 2 && CollectionUtils.isNotEmpty(taxiTimeRangesAll)){
                    for(RuleTimeRange ruleTimeRange : taxiTimeRangesAll){
                        if(dayTypeList.contains(ruleTimeRange.getDayType())){
                            TaxiTimeRange taxiTimeRange = new TaxiTimeRange();
                            Date endDate = DateUtil.parse(ruleTimeRange.getEndTime(),"HH:mm");
                            taxiTimeRange.setEndTime(endDate);
                            Date beginDate = DateUtil.parse(ruleTimeRange.getBeginTime(),"HH:mm");
                            taxiTimeRange.setBeginTime(beginDate);
                            taxiTimeRange.setDayType(ruleTimeRange.getDayType());
                            taxiTimeRange.setIsOvernight(ruleTimeRange.getIsOvernight());
                            taxiTimeRange.setRuleId(ruleTimeRange.getRuleId());
                            taxiTimeRange.setId(ruleTimeRange.getId());
                            taxiTimeRanges.add(taxiTimeRange);
                        }
                    }
                    logger.info("时间段校验，taxiTimeRanges｜{}｜useRule = 2|{}", JSONUtil.toJsonStr(taxiTimeRanges), useRule);
                }

                if (CollectionUtils.isEmpty(taxiTimeRanges) || (CollectionUtils.isNotEmpty(taxiTimeRanges) && !TaxiRuleTimeHelper.checkoutTimeAuth(dayType, taxiTimeRanges, taxiAvailableReqContract.getDeparture_time()))) {
                    isTimeRangeExceed = true;
                } else if (useRule == 1) {
                    // 用车规则钉钉考勤
                    TempOrderRuleCheckResult tempOrderRuleCheckResult =
                            iDingtalkAttendanceService.checkDingtalkResult(taxiTimeRanges,
                                    taxiAvailableReqContract.getCompany_id(), taxiAvailableReqContract.getEmployee_id(),
                                    taxiAvailableReqContract.getDeparture_time());
                    if (null != tempOrderRuleCheckResult) {
                        logger.info("考勤结果：{}", JsonUtils.toJson(tempOrderRuleCheckResult));
                        listMap.add(tempOrderRuleCheckResult);
                    }
                }
            }
            // 新旧范围校验任一存在超规情况 日期范围校验超规 添加超规项
            if (isTimeRangeExceed) {
                TempOrderRuleCheckResult timeRangeResult = new TempOrderRuleCheckResult();
                timeRangeResult.setResCode(TemporaryResponseCode.OrderTaxiTimeRangeNotAuth);
                listMap.add(timeRangeResult);
            }
        }
        //校验用车日期规则(审批用车)
        if (limitDate) {

            if (ObjUtils.isNotEmpty(startTime) && ObjUtils.isNotEmpty(endTime)) {
                Date departureTime = DateUtils.parse(taxiAvailableReqContract.getDeparture_time());
                Date startTimeDate = DateUtils.parse(startTime, DateUtils.FORMAT_DATE_WITH_BAR);
                Date endTimeDate = DateUtils.parse(endTime + " 23:59:59");
                if (ObjUtils.isNotEmpty(startDateTime) && ObjUtils.isNotEmpty(endDateTime)) {
                    startTimeDate = DateUtils.parse(startDateTime, DateUtils.FORMAT_TIME_WITH_BAR);;
                    endTimeDate = DateUtils.parse(endDateTime, DateUtils.FORMAT_TIME_WITH_BAR);;
                }
                if (departureTime.compareTo(startTimeDate) < 0 || departureTime.compareTo(endTimeDate) > 0) {
                    TempOrderRuleCheckResult checkResult = new TempOrderRuleCheckResult();
                    checkResult.setResCode(TemporaryResponseCode.OrderTaxiApplyTimeNotAuth);
                    listMap.add(checkResult);
                }
            }
        }

        // 校验限制次数 如果是按日管控
        if(timesLimitFlag && dayTimesLimit>0){
            List<CarOrderConsumeInfo> list = taxiUserUsedDataManager.getUserCustomizeDailyCarOrdersByScene(taxiAvailableReqContract.getEmployee_id(), taxiAvailableReqContract.getCompany_id(), taxiAvailableReqContract.getDeparture_time(), periodHour, periodMinute, taxiAvailableReqContract.getScene_code());
            logger.info("次数限制.list：{}", JsonUtils.toJson(list));
            long dayUsed = list.stream().filter(CarOrderConsumeInfo::getUseApply).count();
            if (dayUsed >= dayTimesLimit) {
                TempOrderRuleCheckResult checkResult = new TempOrderRuleCheckResult();
                checkResult.setResCode(TemporaryResponseCode.OrderTaxiUseTimesLimit4Rule.getCode(), StrUtils.formatString(TemporaryResponseCode.OrderTaxiUseTimesLimit4Rule.getMsg(), dayTimesLimit));
                listMap.add(checkResult);
            }
        }
        // 校验 用车城市限制
        boolean isExceed = checkSpecifyCity(taxiAvailableReqContract.getCompany_id(),
            taxiAvailableReqContract.getEmployee_id(), taxiAvailableReqContract.getStart_city_id(), taxiCommonRuleDTO,
            useRule, ruleId, taxiApplyRuleId);
        if (isExceed) {
            TempOrderRuleCheckResult checkResult = new TempOrderRuleCheckResult();
            checkResult.setResCode(TemporaryResponseCode.OrderTaxiRuleNotConformToAllowSameCity);
            listMap.add(checkResult);
        }
        return listMap;
    }

    /**
     * 校验 用车城市限制
     * @param commonRuleDTO
     * @param useRule
     */
    public boolean checkSpecifyCity(String companyId, String employeeId, String startCityId,
        TaxiCommonRuleDTO commonRuleDTO, Integer useRule, Integer taxiRuleId, Integer taxiApplyRuleId) {
        logger.info("校验 用车城市限制");
        if (Objects.isNull(commonRuleDTO)) {
            logger.info("commonRuleDTO为空");
            return false;
        }
        if (Objects.isNull(commonRuleDTO.getCityRestrictionType()) || CityRestrictionType.NOT_LIMIT.equalsTo(commonRuleDTO.getCityRestrictionType())) {
            logger.info("用车城市限制不校验此项");
            return false;
        }

        // 常驻城市校验
        boolean isResidentCityContainsStartCity = false; // 是否常驻城市包括 用车订单的Start_city_id
        boolean hasUserResidentCityInfo = false;
        if (Objects.equals(CommonSwitchConstant.OPEN, commonRuleDTO.getResidentCitySwitch())) {
            EmployeeContract employeeInfo = iBaseEmployeeExtService.queryEmployeeInfo(employeeId, companyId);
            log.info("[员工]:{}, 员工编号:{}, employeeInfo:{}", employeeId, companyId, JsonUtils.toJson(employeeInfo));
            if (ObjUtils.isNotEmpty(employeeInfo) && ObjUtils.isNotEmpty(employeeInfo.getLocationCodeList())) {
                hasUserResidentCityInfo = true;
                List<String> baseLocationList = employeeInfo.getLocationCodeList();
                // 按照城市管控base地时 如果员工base地为区县需要向上查询，如果员工base地为市直接匹配
                // 此时如果酒店城市是区县 需要向上查询到市
                List<String> employeeBaseLocationList = Lists.newArrayList();
                baseLocationList.forEach(city -> iCityService.getParentCityIds(city, employeeBaseLocationList));
                log.info("[员工base地校验] employeeId:{}, employeeBaseLocationList:{}",
                        employeeId, JsonUtils.toJson(employeeBaseLocationList));
                HarmonyCityInfo cityInfo = iCityService.getCityInfo(startCityId, 2);
                String preparedCityId = ObjUtils.isEmpty(cityInfo) ? startCityId : cityInfo.getId();
                log.info("[员工base地校验] employeeId:{}, cityId:{}, preparedCityId:{}", employeeId, startCityId, preparedCityId);
                employeeBaseLocationList.addAll(employeeInfo.getLocationCodeList()); // 添加employeeInfo常驻地信息
                if (employeeBaseLocationList.contains(preparedCityId)) {
                    isResidentCityContainsStartCity = true;
                }
            }
        }

        // 实际校验的参数
        Integer ruleIdCheckSpecifyCity = Objects.equals(useRule, TaxiType.Taxi.getSubtype()) ? taxiRuleId : taxiApplyRuleId;
        if (Objects.isNull(ruleIdCheckSpecifyCity) || Objects.equals(-1, ruleIdCheckSpecifyCity)) {
            logger.info("ruleIdCheckSpecifyCity={}", ruleIdCheckSpecifyCity);
            return false;
        }

        // 指定城市组别
        boolean isSpecifyCityGroupContainsStartCity = false;
        logger.info("rpc请求参数：companyId={}, userId={}, ruleIdCheckSpecifyCity={}, useRule={},SpecifyCityType={} ",
                companyId, employeeId, ruleIdCheckSpecifyCity, useRule, commonRuleDTO.getSpecifyCityType());
        List<SpecifyCityDTO> specifyCityList = iRuleService.getTaxiSpecifyCityListBy(companyId,
                employeeId, ruleIdCheckSpecifyCity, useRule, commonRuleDTO.getSpecifyCityType());
        logger.info("rpc返回参数specifyCityList={}", JsonUtils.toJson(specifyCityList));
        Set<String> cityCodes = new HashSet<>();
        if (SpecifyCityType.CITY_GROUP.equalsTo(commonRuleDTO.getSpecifyCityType()) && CollectionUtils.isNotEmpty(specifyCityList)) {
            isSpecifyCityGroupContainsStartCity = checkCityGroupContainsStartCity(startCityId, specifyCityList, cityCodes);
        }

        // 指定城市
        boolean isSpecifyCityContainsStartCity = false;
        if (SpecifyCityType.CITY.equalsTo(commonRuleDTO.getSpecifyCityType())) {
            isSpecifyCityContainsStartCity = checkCityContainsStartCity(startCityId, specifyCityList, cityCodes);
        }

        logger.info("isResidentCityContainsStartCity={}, isSpecifyCityGroupContainsStartCity={}, isSpecifyCityContainsStartCity={}",
                isResidentCityContainsStartCity, isSpecifyCityGroupContainsStartCity, isSpecifyCityContainsStartCity);
        boolean isExceed = false;
        if (CityRestrictionType.ALLOW.equalsTo(commonRuleDTO.getCityRestrictionType())) {
            List<Boolean> checkExceedList = new ArrayList<>();
            // 当配置允许部分城市用车
            // 常驻城市或者指定城市有任意一项符合规则（或的关系），则通过校验，否则弹窗提示
            log.info("当配置允许部分城市用车：常驻城市或者指定城市有任意一项符合规则（或的关系），则通过校验，否则弹窗提示");
            if (Objects.equals(CommonSwitchConstant.OPEN, commonRuleDTO.getResidentCitySwitch())) {
                checkExceedList.add(!isResidentCityContainsStartCity && hasUserResidentCityInfo);
            }
            if (SpecifyCityType.CITY_GROUP.equalsTo(commonRuleDTO.getSpecifyCityType())) {
                checkExceedList.add(!isSpecifyCityGroupContainsStartCity);
            }
            if (SpecifyCityType.CITY.equalsTo(commonRuleDTO.getSpecifyCityType())) {
                checkExceedList.add(!isSpecifyCityContainsStartCity);
            }
            if (CollectionUtils.isNotEmpty(checkExceedList)) {
                isExceed = checkExceedList.stream().noneMatch(a -> Objects.equals(a, false));
            }
        } else {
            // 当配置禁止部分城市用车
            // 常驻城市和者指定城市有任意一项符合规则（与的关系），则不通过校验弹窗提示，否则通过校验
            log.info("当配置禁止部分城市用车：常驻城市和者指定城市有任意一项符合规则（与的关系），则不通过校验弹窗提示，否则通过校验");
            if (Objects.equals(CommonSwitchConstant.OPEN, commonRuleDTO.getResidentCitySwitch())) {
                isExceed = isExceed || (isResidentCityContainsStartCity && hasUserResidentCityInfo);
            }
            if (SpecifyCityType.CITY_GROUP.equalsTo(commonRuleDTO.getSpecifyCityType())) {
                isExceed = isExceed || isSpecifyCityGroupContainsStartCity;
            }
            if (SpecifyCityType.CITY.equalsTo(commonRuleDTO.getSpecifyCityType())) {
                isExceed = isExceed || isSpecifyCityContainsStartCity;
            }
        }

        return isExceed;
    }

    private boolean checkCityContainsStartCity(String startCityId, List<SpecifyCityDTO> specifyCityList, Set<String> cityCodes) {
        List<String> cityCodeListConfig = specifyCityList.stream()
                .filter(specifyCityDTO -> SpecifyCityType.CITY.equalsTo(specifyCityDTO.getSpecifyCityType()))
                .map(SpecifyCityDTO::getCityCode).collect(Collectors.toList());
        cityCodes.addAll(cityCodeListConfig);
        return cityCodes.contains(startCityId);
    }

    private boolean checkCityGroupContainsStartCity(String startCityId, List<SpecifyCityDTO> specifyCityList, Set<String> cityCodes) {
        specifyCityList.stream()
                .filter(specifyCityDTO -> SpecifyCityType.CITY_GROUP.equalsTo(specifyCityDTO.getSpecifyCityType())
                        && CollectionUtils.isNotEmpty(specifyCityDTO.getGroupDetailDTOList()))
                .forEach(specifyCityDTO -> {
                    List<String> cityCodesOfThisGroup = specifyCityDTO.getGroupDetailDTOList().stream()
                            .map(CompanyAreaLevelGroupDetailDTO::getAreaCode).collect(Collectors.toList());
                    cityCodes.addAll(cityCodesOfThisGroup);
                });
        return cityCodes.contains(startCityId);
    }


//    /**
//     * 用于下单校验，此方法和上面的方法大量重复代码需要抽取出公共部分
//     * 用车校验其它除金额和车型外的规则信息
//     *
//     * @param taxiOrderCheckRule
//     * @param taxiOrderCheckReqContract
//     * @param phone
//     * @return
//     */
//    public List<TempOrderRuleCheckResult> checkTaxiNotPriceExceedTypeFinal(EmployeeTaxiRuleInfo employeeTaxiRuleInfo,
//                                                                            TaxiOrderCheckRule taxiOrderCheckRule,
//                                                                            TaxiOrderCheckReqContract taxiOrderCheckReqContract, String phone) {
//
//        List<TempOrderRuleCheckResult> listMap = new ArrayList<>();
//        Integer useRule = taxiOrderCheckRule.getUseRule();
//        Integer ruleId = null;
//        Integer taxiApplyRuleId = -1;
//        Boolean limitPath = false;
//        List<TaxiRulePath> taxiRulePathList = null;
//        List<TaxiRuleListContract.Path> pathList = null;
//        Boolean limitCity = false;
//        List<String> cityIds = null;
//        Integer allowSameCityType = AllowSameCityLimitType.NOT_LIMIT.getCode();
//        Boolean limitTime = false;
//        List<RuleTimeRange> taxiTimeRangesAll = null;
//        Boolean limitDate = false;
//        String startTime = null;
//        String endTime = null;
//        Boolean allowCalledForother = false;
//        // 用车城市限制
//        TaxiCommonRuleDTO taxiCommonRuleDTO = new TaxiCommonRuleDTO();
//        if (useRule == 1) {
//            TaxiRuleListContract taxiRuleListContract = taxiOrderCheckRule.getTaxiRuleListContract();
//            ruleId = taxiRuleListContract.getId();
//            // 用车位置限制
//            limitPath = taxiRuleListContract.getLimitPath();
//            taxiRulePathList = taxiRuleListContract.getPathLocationInfos();
//            pathList = taxiRuleListContract.getPath();
//            // 同城限制
//            allowSameCityType = taxiRuleListContract.getAllowSameCityType();
//            // 时段限制
//            limitTime = taxiRuleListContract.getLimitTime();
//            taxiTimeRangesAll = taxiRuleListContract.getTimeRange();
//            // 为他人叫车
//            allowCalledForother = taxiRuleListContract.getAllowCalledForOther();
//            //添加时间管控区间 为后续支付提供周期支持
//            employeeTaxiRuleInfo.setPeriodHour(taxiRuleListContract.getPeriodHour());
//            employeeTaxiRuleInfo.setPeriodMinute(taxiRuleListContract.getPeriodMinute());
//
//            // 用车城市限制
//            BeanUtils.copyProperties(taxiRuleListContract, taxiCommonRuleDTO);
//
//        } else if (useRule == 2) {
//            TaxiApplyRule taxiApplyRule = taxiOrderCheckRule.getTaxiApplyRule();
//            ruleId = -1; //申请用车设置默认值，防止下面查询的时候空指针
//            if (Objects.nonNull(taxiApplyRule) && Objects.nonNull(taxiApplyRule.getId())) {
//                taxiApplyRuleId = taxiApplyRule.getId();
//            }
//            // 上车城市限制
//            if (ObjUtils.isNotEmpty(taxiApplyRule.getCityLimit()) && taxiApplyRule.getCityLimit() == 0) {
//                limitCity = false;
//            } else {
//                limitCity = true;
//            }
//            cityIds = taxiApplyRule.getStartCityIds();
//            // 同城限制
//            allowSameCityType = taxiApplyRule.getAllowSameCityType();
//            // 日期限制
//            limitDate = true;
//            startTime = taxiApplyRule.getStartTime();
//            endTime = taxiApplyRule.getEndTime();
//            // 为他人叫车
//            allowCalledForother = taxiApplyRule.getAllowCalledForother();
//            //时段限制
//            limitTime = Optional.ofNullable(taxiOrderCheckRule.getTaxiApplyRule().getLimitTime()).orElse(false);
//            taxiTimeRangesAll = taxiOrderCheckRule.getTaxiApplyRule().getTimeRange();
//            // 用车位置限制
//            limitPath = Optional.ofNullable(taxiApplyRule.getLimitPath()).orElse(false);
//            List<com.fenbeitong.saasplus.api.model.dto.rule.TaxiRulePath> taxiRulePaths =
//                    taxiApplyRule.getPathLocationInfos();
//            List<TaxiRulePath> taxiRulePathsTemp = new ArrayList<>();
//            if(CollectionUtils.isNotEmpty(taxiRulePaths)) {
//                taxiRulePaths.forEach(taxiRulePath -> {
//                    TaxiRulePath taxiRulePathTemp = new TaxiRulePath();
//                    taxiRulePathTemp.setArrivalLocationId(taxiRulePath.getArrivalLocationId());
//                    taxiRulePathTemp.setLimitPathType(taxiRulePath.getLimitPathType());
//                    taxiRulePathTemp.setLimitArrival(taxiRulePath.getLimitArrival());
//                    taxiRulePathTemp.setLimitArrivalType(taxiRulePath.getLimitArrivalType());
//                    taxiRulePathTemp.setLimitDeparture(taxiRulePath.getLimitDeparture());
//                    taxiRulePathTemp.setDepartureLocationId(taxiRulePath.getDepartureLocationId());
//                    taxiRulePathTemp.setLimitDepartureType(taxiRulePath.getLimitDepartureType());
//                    taxiRulePathsTemp.add(taxiRulePathTemp);
//                });
//                taxiRulePathList = taxiRulePathsTemp;
//                pathList = getPathList(taxiRulePaths, taxiOrderCheckRule.getCompanyId());
//            }
//
//            // 用车城市限制
//            BeanUtils.copyProperties(taxiApplyRule, taxiCommonRuleDTO);
//        } else {
//            logger.error("用车规则校验类型错误, useRule:{}", useRule);
//        }
//        //记录快照
//        employeeTaxiRuleInfo.setLimitPath(limitPath);
//        employeeTaxiRuleInfo.setTaxiRulePathList(getPathRule(taxiRulePathList, pathList));
//        employeeTaxiRuleInfo.setLimitCity(limitCity);
//        employeeTaxiRuleInfo.setLimitCityIds(cityIds);
//        employeeTaxiRuleInfo.setAllowSameCityType(allowSameCityType);
//        TaxiModifyDestinationCheckReqContract.Location dep = new TaxiModifyDestinationCheckReqContract.Location();
//        dep.setCity_code(taxiOrderCheckReqContract.getStart_city_id());
//        dep.setCity_name("");
//        dep.setLat(taxiOrderCheckReqContract.getDeparture_lat());
//        dep.setLng(taxiOrderCheckReqContract.getDeparture_lng());
//        TaxiModifyDestinationCheckReqContract.Location arr = new TaxiModifyDestinationCheckReqContract.Location();
//        arr.setCity_code(taxiOrderCheckReqContract.getArrival_city_id());
//        arr.setCity_name("");
//        arr.setLat(taxiOrderCheckReqContract.getArrival_lat());
//        arr.setLng(taxiOrderCheckReqContract.getArrival_lng());
//
//        listMap.addAll(checkTaxiLocationRuleExceedType(employeeTaxiRuleInfo, dep, arr, useRule, ruleId, taxiApplyRuleId));
//
//        logger.info("checkTaxiNotPriceExceedTypeFinal.limitTime:{}", JsonUtils.toJson(limitTime));
//        //校验用车时段不符合规则
//        if (limitTime && CollectionUtils.isNotEmpty(taxiTimeRangesAll)) {
//            // 时间范围校验结果
//            boolean isTimeRangeExceed = false;
//
//            // 是否按照节假日的新类型时间范围校验
//            List<Integer> newTimeRangeType = Lists.newArrayList(10, 11, 12);
//            boolean isNewTimeRangeType = false;
//            for (RuleTimeRange range : taxiTimeRangesAll) {
//                if (newTimeRangeType.contains(range.getDayType())) {
//                    isNewTimeRangeType = true;
//                    break;
//                }
//            }
//
//            // 根据企业日历校验时间范围
//            if (isNewTimeRangeType) {
//                String departureTime = taxiOrderCheckReqContract.getDeparture_time();
//                Date departureDate = DateUtils.parse(departureTime);
//                String companyId = taxiOrderCheckReqContract.getCompany_id();
//                Integer type = companyCalendarService.getCompanyCalendarDayType(companyId, departureTime);
//                logger.info("校验时间范围,当前日期类型:{}", type);
//                boolean result = TaxiRuleTimeHelper.checkHolidayNotOverNightTimeRange(taxiTimeRangesAll, departureTime, type);
//                logger.info("校验时间范围,当天结果:{}", JsonUtils.toJson(result));
//                // 往前一天
//                Date preDepartureDate = com.fenbeitong.finhub.common.utils.DateUtils.addDay(departureDate, -1);
//                String preDepartureTime = DateUtils.format(preDepartureDate);
//                Integer preDayType = companyCalendarService.getCompanyCalendarDayType(companyId, preDepartureTime);
//                boolean preDayResult = TaxiRuleTimeHelper.checkHolidayOverNightTimeRange(taxiTimeRangesAll, preDepartureTime, departureTime, preDayType);
//                logger.info("校验时间范围,前一天结果:{}", JsonUtils.toJson(preDayResult));
//                // 如果当天和往前一天均超规 时间范围项超规
//                if (!result && !preDayResult) {
//                    isTimeRangeExceed = true;
//                }
//            }
//            // 旧的时间范围校验
//            if (!isNewTimeRangeType) {
//                Integer dayType = TaxiRuleTimeHelper.getDayType(taxiOrderCheckReqContract.getDeparture_time());
//                TaxiTimeRangeExample taxiTimeRangeExample = new TaxiTimeRangeExample();
//                List<Integer> dayTypeList = new ArrayList<>();
//                dayTypeList.add(dayType);
//                if (dayType >= DayTempType.FIRSTDAY.getCode() && dayType <= DayTempType.FIVEDAY.getCode()) {
//                    //周一时，默认加入周日的时间规则
//                    if (dayType == DayTempType.FIRSTDAY.getCode()) {
//                        dayTypeList.add(DayTempType.SEVENDAY.getCode());
//                        dayTypeList.add(9);
//                    } else {
//                        dayTypeList.add(dayType - 1);
//                    }
//                    dayTypeList.add(8);
//                    taxiTimeRangeExample.createCriteria().andRuleIdEqualTo(ruleId).andDayTypeIn(dayTypeList);
//                } else {
//                    if (dayType == DayTempType.SIXDAY.getCode()) {
//                        dayTypeList.add(dayType - 1);
//                        dayTypeList.add(8);
//                    } else {
//                        dayTypeList.add(dayType - 1);
//                    }
//                    dayTypeList.add(9);
//                    taxiTimeRangeExample.createCriteria().andRuleIdEqualTo(ruleId).andDayTypeIn(dayTypeList);
//                }
//                List<TaxiTimeRange> taxiTimeRanges = new ArrayList<>();
//                if(useRule == 1){
//                    taxiTimeRanges = taxiTimeRangeMapper.selectByExample(taxiTimeRangeExample);
//                    logger.info("时间段校验，taxiTimeRanges｜{}｜useRule = 1|{}", JSONUtil.toJsonStr(taxiTimeRanges), useRule);
//                } else if(useRule == 2 && CollectionUtils.isNotEmpty(taxiTimeRangesAll)){
//                    for(RuleTimeRange ruleTimeRange : taxiTimeRangesAll){
//                        if(dayTypeList.contains(ruleTimeRange.getDayType())){
//                            TaxiTimeRange taxiTimeRange = new TaxiTimeRange();
//                            Date endDate = DateUtil.parse(ruleTimeRange.getEndTime(),"HH:mm");
//                            taxiTimeRange.setEndTime(endDate);
//                            Date beginDate = DateUtil.parse(ruleTimeRange.getBeginTime(),"HH:mm");
//                            taxiTimeRange.setBeginTime(beginDate);
//                            taxiTimeRange.setDayType(ruleTimeRange.getDayType());
//                            taxiTimeRange.setIsOvernight(ruleTimeRange.getIsOvernight());
//                            taxiTimeRange.setRuleId(ruleTimeRange.getRuleId());
//                            taxiTimeRange.setId(ruleTimeRange.getId());
//                            taxiTimeRanges.add(taxiTimeRange);
//                        }
//                        logger.info("时间段校验，taxiTimeRanges｜{}｜useRule = 2|{}", JSONUtil.toJsonStr(taxiTimeRanges),
//                                useRule);
//                    }
//                }
//                logger.info("checkTaxiNotPriceExceedTypeFinal.taxiTimeRanges:{}", JsonUtils.toJson(taxiTimeRanges));
//                if (CollectionUtils.isEmpty(taxiTimeRanges) || (CollectionUtils.isNotEmpty(taxiTimeRanges) && !TaxiRuleTimeHelper.checkoutTimeAuth(dayType, taxiTimeRanges, taxiOrderCheckReqContract.getDeparture_time()))) {
//                    isTimeRangeExceed = true;
//                } else if (useRule == 1) {
//                    // 用车规则钉钉考勤
//                    TempOrderRuleCheckResult tempOrderRuleCheckResult =
//                            iDingtalkAttendanceService.checkDingtalkResult(taxiTimeRanges,
//                                    taxiOrderCheckReqContract.getCompany_id(), taxiOrderCheckReqContract.getEmployee_id(),
//                                    taxiOrderCheckReqContract.getDeparture_time());
//                    if (null != tempOrderRuleCheckResult) {
//                        logger.info("考勤结果：{}", JsonUtils.toJson(tempOrderRuleCheckResult));
//                        listMap.add(tempOrderRuleCheckResult);
//                    }
//                }
//            }
//            // 新旧范围校验任一存在超规情况 日期范围校验超规 添加超规项
//            if (isTimeRangeExceed) {
//                TempOrderRuleCheckResult timeRangeResult = new TempOrderRuleCheckResult();
//                timeRangeResult.setResCode(TemporaryResponseCode.OrderTaxiTimeRangeNotAuth);
//                listMap.add(timeRangeResult);
//            }
//        }
//        //校验用车日期规则(审批用车)
//        if (limitDate) {
//            if (ObjUtils.isNotEmpty(startTime) && ObjUtils.isNotEmpty(endTime)) {
//                Date departureTime = DateUtils.parse(taxiOrderCheckReqContract.getDeparture_time());
//                Date startTimeDate = DateUtils.parse(startTime, DateUtils.FORMAT_DATE_WITH_BAR);
//                Date endTimeDate = DateUtils.parse(endTime + " 23:59:59");
//                if (departureTime.compareTo(startTimeDate) < 0 || departureTime.compareTo(endTimeDate) > 0) {
//                    TempOrderRuleCheckResult checkResult = new TempOrderRuleCheckResult();
//                    checkResult.setResCode(TemporaryResponseCode.OrderTaxiTimeRangeNotAuth);
//                    listMap.add(checkResult);
//                }
//            }
//        }
//        //校验是否可以为他人叫车
//        if(!allowCalledForother && CollectionUtils.isNotEmpty(taxiOrderCheckReqContract.getPassengers_phone())){
//            for (String phoneTemp : taxiOrderCheckReqContract.getPassengers_phone()) {
//                if (!phoneTemp.equals(phone)) {
//                    TempOrderRuleCheckResult checkResult = new TempOrderRuleCheckResult();
//                    checkResult.setResCode(TemporaryResponseCode.OrderTaxiCannotCallForOther);
//                    listMap.add(checkResult);
//                    break;
//                }
//            }
//        }
//
//        // 校验 用车城市限制
//        boolean isExceed = checkSpecifyCity(taxiOrderCheckReqContract.getCompany_id(),
//            taxiOrderCheckReqContract.getEmployee_id(), taxiOrderCheckReqContract.getStart_city_id(),
//            taxiCommonRuleDTO, useRule, ruleId, taxiApplyRuleId);
//
//        if (isExceed) {
//            TempOrderRuleCheckResult checkResult = new TempOrderRuleCheckResult();
//            checkResult.setResCode(TemporaryResponseCode.OrderTaxiRuleNotConformToAllowSameCity);
//            listMap.add(checkResult);
//        }
//        return listMap;
//    }



    /**
     * 用车地点规则校验
     * 1.地点限制检查  （自定义地点，机场，火车站）
     * 2.审批单出发城市限制
     * 3.校验起点和终点必须为同一城市限制
     */
    public List<TempOrderRuleCheckResult> checkTaxiLocationRuleExceedType(EmployeeTaxiRuleInfo employeeTaxiRuleInfo,
        TaxiModifyDestinationCheckReqContract.Location dep, TaxiModifyDestinationCheckReqContract.Location arr,
        Integer useRule, Integer taxiRuleId, Integer taxiApplyRuleId, String companyId,
        TaxiAvailableReqContract.HomeLocation homeLocation) {
        List<TempOrderRuleCheckResult> res = new ArrayList<>();
        List<String> depCityCodes = Lists.newArrayList();       //出发城市补偿集合
        List<String> arrCityCodes = Lists.newArrayList();       //目的城市补偿集合
        //检查地点限制
//        boolean limitPath = employeeTaxiRuleInfo.getLimitPath();
        Integer limitPathType = employeeTaxiRuleInfo.getLimitPathType();
        if (Objects.equals(2, limitPathType)) {
            // 特殊处理申请单地点限制的匹配，员工填写类型，只有申请用车有
            // 检查申请单出发地点
            boolean depAccess = checkApplyLimitLocation(dep, employeeTaxiRuleInfo.getCheckStartAddress(),
                employeeTaxiRuleInfo.getApplyStartAddress(), employeeTaxiRuleInfo.getApplyLocationLimitRadius());
            boolean arrAccess = checkApplyLimitLocation(arr,
                employeeTaxiRuleInfo.getCheckArrivalAddress(), employeeTaxiRuleInfo.getApplyArrivalAddress(),
                employeeTaxiRuleInfo.getApplyLocationLimitRadius());
            if (!depAccess || !arrAccess) {
                TempOrderRuleCheckResult checkResult = new TempOrderRuleCheckResult();
                checkResult.setResCode(TemporaryResponseCode.OrderTaxiApplyLocationNoAuth);
                if (employeeTaxiRuleInfo.getIsOpenExceedConfig()) {
                    checkResult.setExceedConfig(employeeTaxiRuleInfo.getPathLocationConfig());
                }
                res.add(checkResult);
            }
        } else {
            List<EmployeeTaxiRuleInfo.PathRule> rulePathList = employeeTaxiRuleInfo.getTaxiRulePathList();
            if (Objects.equals(1, limitPathType) && ObjUtils.isNotEmpty(rulePathList)) {
                boolean hasHomeAddressCheck = rulePathList.stream().anyMatch(taxiRulePath -> {
                            if (Objects.equals(taxiRulePath.getLimitDepartureType(), FixedPathLocationEnum.HOME_ADDRESS.getCode())
                                    || Objects.equals(taxiRulePath.getLimitArrivalType(), FixedPathLocationEnum.HOME_ADDRESS.getCode())) {
                                return true;
                            }

                            if (CollectionUtils.isNotEmpty(taxiRulePath.getArrLocationList())) {
                                if (taxiRulePath.getArrLocationList().stream().anyMatch(limitLocation ->
                                        Objects.equals(limitLocation.getLocationId(), FixedPathLocationEnum.HOME_ADDRESS.getCode()))) {
                                    return true;
                                }
                            }
                            if (CollectionUtils.isNotEmpty(taxiRulePath.getDepLocationList())) {
                                if (taxiRulePath.getDepLocationList().stream().anyMatch(limitLocation ->
                                        Objects.equals(limitLocation.getLocationId(), FixedPathLocationEnum.HOME_ADDRESS.getCode()))) {
                                    return true;
                                }
                            }
                            return false;
                        }
                );
                double homeRadius = 0d;
                if (hasHomeAddressCheck) {
                    MessageSetup messageSetup = iMessageSetupService.queryCompanyMessageSetupWithDefault(companyId,
                            SaasMessageConstant.TAXI_GO_HOME_SETTING);
                    homeRadius = Double.parseDouble(messageSetup.getStrVal1());
                }
                List<TaxiLocation> workLocationList = new ArrayList<>();
                boolean hasWorkAddressCheck = rulePathList.stream().anyMatch(taxiRulePath -> {
                            if (Objects.equals(taxiRulePath.getLimitDepartureType(), FixedPathLocationEnum.WORK_LOCATION.getCode())
                                    || Objects.equals(taxiRulePath.getLimitArrivalType(), FixedPathLocationEnum.WORK_LOCATION.getCode())) {
                                return true;
                            }

                            if (CollectionUtils.isNotEmpty(taxiRulePath.getArrLocationList())) {
                                if (taxiRulePath.getArrLocationList().stream().anyMatch(limitLocation ->
                                        Objects.equals(limitLocation.getLocationId(), FixedPathLocationEnum.WORK_LOCATION.getCode()))) {
                                    return true;
                                }
                            }
                            if (CollectionUtils.isNotEmpty(taxiRulePath.getDepLocationList())) {
                                if (taxiRulePath.getDepLocationList().stream().anyMatch(limitLocation ->
                                        Objects.equals(limitLocation.getLocationId(), FixedPathLocationEnum.WORK_LOCATION.getCode()))) {
                                    return true;
                                }
                            }
                            return false;
                        }
                );
                if (hasWorkAddressCheck) {
                    LocationCollectionExample example = new LocationCollectionExample();
                    example.createCriteria().andCompanyIdEqualTo(companyId).andLocationTypeEqualTo(1);
                    List<LocationCollection> locationCollectionList = locationCollectionMapper.selectByExample(example);
                    if (CollectionUtils.isNotEmpty(locationCollectionList)) {
                        List<Integer> taxiLocationIdList = locationCollectionList.stream()
                                .map(LocationCollection::getLocationId).collect(Collectors.toList());
                        TaxiLocationExample taxiLocationExample = new TaxiLocationExample();
                        taxiLocationExample.createCriteria().andIdIn(taxiLocationIdList);
                        workLocationList = taxiLocationMapper.selectByExample(taxiLocationExample);
                    }
                    log.info("办公地点集合:{}", JsonUtils.toJson(workLocationList));
                }

                boolean isAccess = false;    //任一条件满足则通过
                for (EmployeeTaxiRuleInfo.PathRule taxiRulePath : rulePathList) {
                    if (isAccess) {
                        log.info("用车地点限制 {} ,已通过，不检查", taxiRulePath);
                        break;
                    }
                    try {
                        boolean depSuccess = true;
                        if (taxiRulePath.getLimitDeparture()) {  //检查上车点
                            log.info("检查上车点 {} 是否合规", dep);
                            if (ObjUtils.isEmpty(taxiRulePath.getDepLocationList())) {
                                depSuccess = taxiPathCheck(taxiRulePath.getLimitDepartureType(), dep, depCityCodes,
                                        taxiRulePath.getDepLocation(), homeRadius, homeLocation, workLocationList);
                            } else {
                                depSuccess = taxiPathListCheck(dep, depCityCodes, taxiRulePath.getDepLocationList(),
                                        homeRadius, homeLocation, workLocationList);
                            }
                        }
                        boolean arrSuccess = true;
                        if (taxiRulePath.getLimitArrival()) {    //检查下车点
                            log.info("检查下车点 {} 是否合规", arr);
                            if (ObjUtils.isEmpty(taxiRulePath.getArrLocationList())) {
                                arrSuccess = taxiPathCheck(taxiRulePath.getLimitArrivalType(), arr, arrCityCodes,
                                        taxiRulePath.getArrLocation(), homeRadius, homeLocation, workLocationList);
                            } else {
                                arrSuccess = taxiPathListCheck(arr, arrCityCodes, taxiRulePath.getArrLocationList(),
                                        homeRadius, homeLocation, workLocationList);
                            }
                        }
                        isAccess = depSuccess && arrSuccess;
                    } finally {
                        log.info("用车地点限制 {} ,检查通过-{}", JsonUtils.toJson(taxiRulePath), isAccess);
                    }
                }
                if (!isAccess) {
                    TempOrderRuleCheckResult checkResult = new TempOrderRuleCheckResult();
                    checkResult.setResCode(TemporaryResponseCode.OrderTaxiLocationNoAuth);
                    // 新规则启用 设置超规措施
                    if (employeeTaxiRuleInfo.getIsOpenExceedConfig()) {
                        checkResult.setExceedConfig(employeeTaxiRuleInfo.getPathLocationConfig());
                    }
                    res.add(checkResult);
                }
            }
        }
        //校验出发城市限制(审批用车)
        boolean limitCity = employeeTaxiRuleInfo.getLimitCity();
        List<String> limitCityIds = employeeTaxiRuleInfo.getLimitCityIds();
        if (limitCity && ObjUtils.isNotEmpty(limitCityIds)) {
            String startCityId = dep.getCity_code();
            List<String> addCityIds = Lists.newArrayList();
            for (String cityId : limitCityIds) {
                HarmonyCityInfo harmonyCityInfo = iCityService.getCityInfo(cityId, 2);
                if (null != harmonyCityInfo) {
                    if (!limitCityIds.contains(harmonyCityInfo.getId())) {
                        addCityIds.add(harmonyCityInfo.getId());
                    }
                }
            }
            limitCityIds.addAll(addCityIds);
            Boolean isAccess = limitCityIds.contains(startCityId);
            if (!isAccess) {    //不包含出发城市，尝试补偿
                depCityCodes = getCityCodesByLocation(dep.getLat(), dep.getLng(), depCityCodes);
                for (String cityCode : depCityCodes) {
                    if (isAccess) {
                        continue;
                    }
                    isAccess = limitCityIds.contains(cityCode);
                }
            }
            if (!isAccess) {
                TempOrderRuleCheckResult checkResult = new TempOrderRuleCheckResult();
                checkResult.setResCode(TemporaryResponseCode.OrderTaxiCityNotApply);
                // 新规则启用 设置超规措施
                if (employeeTaxiRuleInfo.getIsOpenExceedConfig()) {
                    checkResult.setExceedConfig(employeeTaxiRuleInfo.getLimitCityConfig());
                }
                res.add(checkResult);
            }
        }
        //校验起点和终点必须为同一城市
        if (Objects.nonNull(employeeTaxiRuleInfo.getAllowSameCityType())) {
            logger.info("走重构");
            checkAllowSameCityAfterRefactor(employeeTaxiRuleInfo, dep, arr, res, depCityCodes, arrCityCodes, useRule,
                taxiRuleId, taxiApplyRuleId);
        } else {
            logger.info("不走重构");
            Boolean allowSameCity = employeeTaxiRuleInfo.getAllowSameCity();
            if (allowSameCity) {
                Boolean isAccess = dep.getCity_code().equals(arr.getCity_code());
                if (!isAccess) {     //城市兼容处理
                    depCityCodes = getCityCodesByLocation(dep.getLat(), dep.getLng(), depCityCodes);
                    arrCityCodes = getCityCodesByLocation(arr.getLat(), arr.getLng(), arrCityCodes);
                    Set<String> setDep = Sets.newHashSet(depCityCodes);
                    setDep.add(dep.getCity_code());
                    setDep.removeAll(Lists.newArrayList(null, ""));
                    Set<String> setArr = Sets.newHashSet(arrCityCodes);
                    setArr.add(arr.getCity_code());
                    setArr.removeAll(Lists.newArrayList(null, ""));
                    Set<String> setAll = Sets.newHashSet();
                    setAll.addAll(setDep);
                    setAll.addAll(setArr);
                    isAccess = setAll.size() > 0 && setAll.size() < (setDep.size() + setArr.size());
                }
                if (!isAccess) {
                    TempOrderRuleCheckResult checkResult = new TempOrderRuleCheckResult();
                    checkResult.setResCode(TemporaryResponseCode.OrderTaxiLocationNotAllow);
                    // 新规则启用 设置超规措施
                    if (employeeTaxiRuleInfo.getIsOpenExceedConfig()) {
                        checkResult.setExceedConfig(employeeTaxiRuleInfo.getAllowSameCityConfig());
                    }
                    res.add(checkResult);
                }
            }
        }


        return res;
    }

    private void checkAllowSameCityAfterRefactor(EmployeeTaxiRuleInfo employeeTaxiRuleInfo,
        TaxiModifyDestinationCheckReqContract.Location dep, TaxiModifyDestinationCheckReqContract.Location arr,
        List<TempOrderRuleCheckResult> res, List<String> depCityCodes, List<String> arrCityCodes,
        Integer useRule, Integer taxiRuleId, Integer taxiApplyRuleId) {
        Integer allowSameCityType = employeeTaxiRuleInfo.getAllowSameCityType();
        if (AllowSameCityLimitType.MUST_BE_AT_SAME_CITY.equalsTo(allowSameCityType)) {
            boolean isAccess = dep.getCity_code().equals(arr.getCity_code());
            if (!isAccess) {     //城市兼容处理
                depCityCodes = getCityCodesByLocation(dep.getLat(), dep.getLng(), depCityCodes);
                arrCityCodes = getCityCodesByLocation(arr.getLat(), arr.getLng(), arrCityCodes);
                Set<String> setDep = Sets.newHashSet(depCityCodes);
                setDep.add(dep.getCity_code());
                setDep.removeAll(Lists.newArrayList(null, ""));
                Set<String> setArr = Sets.newHashSet(arrCityCodes);
                setArr.add(arr.getCity_code());
                setArr.removeAll(Lists.newArrayList(null, ""));
                Set<String> setAll = Sets.newHashSet();
                setAll.addAll(setDep);
                setAll.addAll(setArr);
                isAccess = setAll.size() > 0 && setAll.size() < (setDep.size() + setArr.size());
            }
            // 部分城市允许跨城校验
            if (!isAccess) {
                TaxiAllowCrossCityReqDTO req = new TaxiAllowCrossCityReqDTO();
                req.setTaxiRuleType(useRule);
                if (Objects.equals(useRule, 1)) {
                    req.setRuleId(taxiRuleId);
                } else {
                    req.setRuleId(taxiApplyRuleId);
                }
                TaxiAllowCrossCityResDTO allowCrossCityConfig = iTaxiRuleCityService.getAllowCrossCityConfig(req);
                if (Objects.equals(allowCrossCityConfig.getAllowCrossCityConfig(), 1)) {
                    List<TaxiAllowCrossCityDTO> allowCrossCityList = allowCrossCityConfig.getAllowCrossCityList();
                    if (CollectionUtils.isNotEmpty(allowCrossCityList)) {
                        for (TaxiAllowCrossCityDTO taxiAllowCrossCityDTO : allowCrossCityList) {
                            String[] split = taxiAllowCrossCityDTO.getCityIds().split(",");
                            if ((Objects.equals(dep.getCity_code(), split[0]) && Objects.equals(arr.getCity_code(), split[1]))
                            ||(Objects.equals(arr.getCity_code(), split[0]) && Objects.equals(dep.getCity_code(), split[1]))) {
                                isAccess = true;
                                log.info("部分城市允许跨城校验通过");
                                break;
                            }
                        }
                    }
                }
            }

            if (!isAccess) {
                TempOrderRuleCheckResult checkResult = new TempOrderRuleCheckResult();
                checkResult.setResCode(TemporaryResponseCode.OrderTaxiLocationNotAllow);
                // 新规则启用 设置超规措施
                if (employeeTaxiRuleInfo.getIsOpenExceedConfig()) {
                    checkResult.setExceedConfig(employeeTaxiRuleInfo.getAllowSameCityConfig());
                }
                res.add(checkResult);
            }
        } else if (AllowSameCityLimitType.CANNOT_BE_AT_SAME_CITY.equalsTo(allowSameCityType)) {
            // 出发和到达城市code不一样
            boolean isAccess = false;
            depCityCodes = getCityCodesByLocation(dep.getLat(), dep.getLng(), depCityCodes);
            arrCityCodes = getCityCodesByLocation(arr.getLat(), arr.getLng(), arrCityCodes);
            Set<String> setDep = Sets.newHashSet(depCityCodes);
            setDep.add(dep.getCity_code());
            setDep.removeAll(Lists.newArrayList(null, ""));
            Set<String> setArr = Sets.newHashSet(arrCityCodes);
            setArr.add(arr.getCity_code());
            setArr.removeAll(Lists.newArrayList(null, ""));
            Set<String> setAll = Sets.newHashSet();
            setAll.addAll(setDep);
            setAll.addAll(setArr);
            // 如果是不同城市，则 出发setDep的城市code集合长度 和 到达setArr的城市code集合长度 = setAll
            isAccess = setAll.size() == (setDep.size() + setArr.size());

            if (!isAccess) {
                TempOrderRuleCheckResult checkResult = new TempOrderRuleCheckResult();
                checkResult.setResCode(TemporaryResponseCode.OrderTaxiLocationCannotBeAtSameCity);
                // 新规则启用 设置超规措施
                if (employeeTaxiRuleInfo.getIsOpenExceedConfig()) {
                    checkResult.setExceedConfig(employeeTaxiRuleInfo.getAllowSameCityConfig());
                }
                res.add(checkResult);
            }
        }
    }


    /**
     * 校验金额规则信息
     * @param taxiOrderCheckRule
     * @param useCouponAfterPrice
     * @param dailyConsumption
     * @param ruleDayAmountSumbit 申请用车使用用车规则额度标志，true表示用户同意合并（用户选择"知道了"），false表示首次校验（额度不够会提示用户合并额度）
     * @param startCityId
     * @param arrivalCityId
     * @param taxiTypePriceInfo
     * @return
     */
    public List<TempOrderRuleCheckResult> checkTaxiPriceExceedTypeTemp(TaxiOrderCheckRule taxiOrderCheckRule,
                                                                        BigDecimal useCouponAfterPrice, BigDecimal dailyConsumption,
                                                                        Boolean ruleDayAmountSumbit,
                                                                        String startCityId, String arrivalCityId,
                                                                       TaxiTypePriceInfoV2 taxiTypePriceInfo,
                                                                       TaxiModifyDestinationCheckResContract contract,
                                                                       EmployeeTaxiRuleInfo employeeTaxiRuleInfoContract,
                                                                       CustomFormContext customFormContext) {
        // 自定义申请单总预估费校验
        CustomFormTotalEstimatedOptionDTO totalEstimatedOption = customFormContext.getTotalEstimatedOption();
        if (customFormContext.getTotalEstimatedOpen()) {
            if (Objects.equals(TotalEstimatedLimitType.DAILY_LIMIT.getCode(), totalEstimatedOption.getTotalEstimatedLimitType())) {
                totalEstimatedOption.setTotalEstimatedLimitAmount(totalEstimatedOption.getTotalEstimatedLimitAmount());
            } else {
                totalEstimatedOption.setTotalEstimatedLimitAmount(totalEstimatedOption.getApplyBudgeInYuan());
            }
            return checkTaxiPriceExceedSumPredict(taxiOrderCheckRule, useCouponAfterPrice, taxiTypePriceInfo, customFormContext, employeeTaxiRuleInfoContract);
        } else {
            //未开启总预估费校验
            return checkTaxiPriceExceedType(taxiOrderCheckRule, useCouponAfterPrice, dailyConsumption, ruleDayAmountSumbit, startCityId, arrivalCityId, taxiTypePriceInfo, contract, employeeTaxiRuleInfoContract);
        }
    }

    /**
     * 自定义申请单总预估费校验
     */
    public List<TempOrderRuleCheckResult> checkTaxiPriceExceedSumPredict(TaxiOrderCheckRule taxiOrderCheckRule,
                                                                         BigDecimal useCouponAfterPrice,
                                                                         TaxiTypePriceInfoV2 taxiTypePriceInfo,
                                                                         CustomFormContext customFormContext,
                                                                         EmployeeTaxiRuleInfo employeeTaxiRuleInfoContract) {
        logger.info("ITaxiCheckServiceImpl checkTaxiPriceExceedSumPredict param, " +
                        "taxiOrderCheckRule|{}|taxiTypePriceInfo|{}|employeeTaxiRuleInfoContract|{}|customFormContext|{}",
                JSONUtil.toJsonStr(taxiOrderCheckRule),
                JSONUtil.toJsonStr(taxiTypePriceInfo), JSONUtil.toJsonStr(employeeTaxiRuleInfoContract), JSONUtil.toJsonStr(customFormContext));
        List<TempOrderRuleCheckResult> listMap = new ArrayList<>();
        //优惠券金额
        BigDecimal couponAmount = BigDecimal.ZERO;
        //默认抵扣超规个人支付金额
        Boolean isAllowDeductExceed = false;
        //优惠券是否可用
        Boolean couponAvailable = true;

        CustomFormTotalEstimatedOptionDTO totalEstimatedOption = customFormContext.getTotalEstimatedOption();
        //总预估费限制类型：1-单日上限，2-单日日均，3-员工填写
        Integer estimatedLimitType = totalEstimatedOption.getTotalEstimatedLimitType();
        //总预估费限制金额
        BigDecimal estimatedLimitAmount = totalEstimatedOption.getTotalEstimatedLimitAmount();
        log.info("上浮前的总预估费金额:{}",estimatedLimitAmount);
        if(ObjUtils.isNotEmpty(customFormContext.getApplyOrder().getFormId())){
            CustomFormApplyConfigExample example = new CustomFormApplyConfigExample();
            example.createCriteria()
                    .andFormIdEqualTo(customFormContext.getApplyOrder().getFormId());
            List<CustomFormApplyConfig> customFormApplyConfigList = customFormApplyConfigMapper.selectByExample(example);
            log.info("获取表单预估费相关配置customFormApplyConfigList={}", JsonUtils.toJson(customFormApplyConfigList));
            CustomFormApplyConfig customFormApplyConfig = customFormApplyConfigList.get(0);

            if(ObjectUtil.isNotEmpty(customFormApplyConfig.getFloatVal())){
                estimatedLimitAmount = estimatedLimitAmount.multiply(new BigDecimal(customFormApplyConfig.getFloatVal())).divide(new BigDecimal(100));
                log.info("上浮后的总预估费金额:{}",estimatedLimitAmount);
            }
        }

        //申请单已使用金额
        BigDecimal applyUsedPrice = customFormContext.getApplyUsedAmount();
        TaxiApplyRule taxiApplyRule = taxiOrderCheckRule.getTaxiApplyRule();
        Integer code = -1;
        String msg = "";
        BigDecimal availableAmount = estimatedLimitAmount.subtract(applyUsedPrice);

        boolean isOpenExceedConfig = false;
        Integer totalEstimatedCostConfig = null;
        if (Objects.equals(taxiApplyRule.getOpenExceedConfig(), true)) {
            TaxiExceedConfigPo exceedConfigInfo = taxiApplyRule.getExceedConfigInfo();
            totalEstimatedCostConfig = exceedConfigInfo.getTotalEstimatedCostConfig();
            isOpenExceedConfig = true;
        }

        switch (estimatedLimitType) {
            case 1:
                //单日上限
                code = TemporaryResponseCode.OrderTaxiDayTotalPriceLimit.getCode();
                msg = StrUtils.formatString(TemporaryResponseCode.OrderTaxiDayTotalPriceLimit.getMsg(), availableAmount.setScale(2
                        , BigDecimal.ROUND_HALF_UP));
                if(availableAmount.compareTo(useCouponAfterPrice) < 0){
                    BigDecimal limit = estimatedLimitAmount.subtract(applyUsedPrice);
                    //优惠券金额不抵扣超规金额 且 优惠券金额比差标大的话 无法使用优惠券
                    if(couponAmount.compareTo(estimatedLimitAmount) > 0 && !isAllowDeductExceed){
                        couponAvailable = false;
                    }
                    TempOrderRuleCheckResult checkResult = new TempOrderRuleCheckResult();
                    checkResult.setResCode(code, msg);
                    if (isOpenExceedConfig) {
                        checkResult.setExceedConfig(totalEstimatedCostConfig);
                    }
                    listMap.add(checkResult);
                }
                break;
            case 2:
                //单日日均
                code = TemporaryResponseCode.OrderTaxiAllDayAvgPriceLimit.getCode();
                msg = StrUtils.formatString(TemporaryResponseCode.OrderTaxiAllDayAvgPriceLimit.getMsg(), availableAmount.setScale(2
                        , BigDecimal.ROUND_HALF_UP));
//                Date endTime = DateUtils.parse(taxiApplyRule.getEndTime(), DateUtils.FORMAT_DATE_WITH_BAR);
//                Date startTime = DateUtils.parse(taxiApplyRule.getStartTime(), DateUtils.FORMAT_DATE_WITH_BAR);
//                long days = DateUtil.between(startTime, endTime, DateUnit.DAY) + 1;
                //日均限额的总额度
                //剩余额度
                BigDecimal avgDayPrice = estimatedLimitAmount.subtract(applyUsedPrice);
                if (avgDayPrice.compareTo(useCouponAfterPrice) < 0 ) {
                    TempOrderRuleCheckResult checkResult = new TempOrderRuleCheckResult();
                    checkResult.setResCode(code, msg);
                    if (isOpenExceedConfig) {
                        checkResult.setExceedConfig(totalEstimatedCostConfig);
                    }
                    listMap.add(checkResult);
                }
                break;
            case 3:
                //员工填写
                code = TemporaryResponseCode.OrderTaxiApplyPriceLimit.getCode();
                msg = StrUtils.formatString(TemporaryResponseCode.OrderTaxiApplyPriceLimit.getMsg(), availableAmount.setScale(2
                        , BigDecimal.ROUND_HALF_UP));
                if(availableAmount.compareTo(useCouponAfterPrice) < 0){
                    BigDecimal limit = estimatedLimitAmount.subtract(applyUsedPrice);
                    //优惠券金额不抵扣超规金额 且 优惠券金额比差标大的话 无法使用优惠券
                    if(couponAmount.compareTo(estimatedLimitAmount) > 0 && !isAllowDeductExceed){
                        couponAvailable = false;
                    }
                    TempOrderRuleCheckResult checkResult = new TempOrderRuleCheckResult();
                    checkResult.setResCode(code, msg);
                    if (isOpenExceedConfig) {
                        checkResult.setExceedConfig(totalEstimatedCostConfig);
                    }
                    listMap.add(checkResult);
                }
                break;
            default:
                logger.warn("estimatedLimitType is error, estimatedLimitType|{}", estimatedLimitType);
                break;
        }
        if(employeeTaxiRuleInfoContract != null){
            employeeTaxiRuleInfoContract.setPriceLimit(estimatedLimitAmount);
            employeeTaxiRuleInfoContract.setDayPriceLimit(estimatedLimitAmount);
            employeeTaxiRuleInfoContract.setApplyPriceLimit(estimatedLimitAmount);
            employeeTaxiRuleInfoContract.setApplyPriceUsed(applyUsedPrice);
        }
        //优惠券处理
        if(taxiTypePriceInfo!= null){
            if(CollectionUtils.isNotEmpty(taxiTypePriceInfo.getCoupon_list())){
                //设置当前优惠券可使用状态
                taxiTypePriceInfo.getCoupon_list().get(0).setAvailable(couponAvailable);
            }
        }
        return listMap;
    }

    /**
     * 未开启总预估费校验
     */
    public List<TempOrderRuleCheckResult> checkTaxiPriceExceedType(TaxiOrderCheckRule taxiOrderCheckRule, BigDecimal useCouponAfterPrice,
                                                                   BigDecimal dailyConsumption, Boolean ruleDayAmountSumbit,
                                                                   String startCityId, String arrivalCityId,
                                                                   TaxiTypePriceInfoV2 taxiTypePriceInfo,
                                                                   TaxiModifyDestinationCheckResContract contract,
                                                                   EmployeeTaxiRuleInfo employeeTaxiRuleInfoContract) {
        logger.info("费用校验, useCouponAfterPrice: {}, dailyConsumption: {}, startCityId: {}, arrivalCityId: {},taxiTypePriceInfo:{}, ruleDayAmountSumbit:{}, 规则:{}",
                useCouponAfterPrice, dailyConsumption, startCityId, arrivalCityId,JsonUtils.toJson(taxiTypePriceInfo),
                ruleDayAmountSumbit, JsonUtils.toJson(taxiOrderCheckRule));

        // 1、准备规则校验上下文参数
        PriceRuleCheckContext ctx = preparePriceRuleCheckContext(taxiOrderCheckRule, useCouponAfterPrice, dailyConsumption,
                ruleDayAmountSumbit, startCityId, arrivalCityId, taxiTypePriceInfo, contract, employeeTaxiRuleInfoContract);

        // 2、使用规则引擎执行规则
        LiteflowResponse response = flowExecutor.execute2Resp("taxiPriceCheckChain",null, ctx);

        if (!response.isSuccess()){
            Exception e = response.getCause();
            log.error("checkTaxiPriceExceedType费用类规则校验失败",e);
            throw new SaasException(TemporaryResponseCode.InnerError.getCode(), TemporaryResponseCode.InnerError.getMsg());
        }

        // 3、结果处理
        afterPriceRuleCheckProcess(ctx,contract,taxiTypePriceInfo);

        return ctx.getListMap();

    }

    private void afterPriceRuleCheckProcess(PriceRuleCheckContext ctx, TaxiModifyDestinationCheckResContract contract, TaxiTypePriceInfoV2 taxiTypePriceInfo) {
        if (contract != null) {
            contract.setAvailableBalance(ctx.getAvailableBalance().max(BigDecimal.ZERO));
            contract.setRuleAvailableBalance(ctx.getRuleAvailableBalance());
        }

        //优惠券处理
        if(taxiTypePriceInfo!= null){
            if(CollectionUtils.isNotEmpty(taxiTypePriceInfo.getCoupon_list())){
                //设置当前优惠券可使用状态
                taxiTypePriceInfo.getCoupon_list().get(0).setAvailable(ctx.getCouponAvailable());
            }
        }
        logger.info("taxiTypePriceInfo:{}",JsonUtils.toJson(taxiTypePriceInfo));
    }

    private PriceRuleCheckContext preparePriceRuleCheckContext(TaxiOrderCheckRule taxiOrderCheckRule, BigDecimal useCouponAfterPrice, BigDecimal dailyConsumption, Boolean ruleDayAmountSumbit, String startCityId, String arrivalCityId, TaxiTypePriceInfoV2 taxiTypePriceInfo, TaxiModifyDestinationCheckResContract contract, EmployeeTaxiRuleInfo employeeTaxiRuleInfoContract) {
        PriceRuleCheckContext ctx = new PriceRuleCheckContext();

        Integer useRule = taxiOrderCheckRule.getUseRule();
        ctx.setTaxiOrderCheckRule(taxiOrderCheckRule);
        ctx.setUseRule(useRule);
        ruleDayAmountSumbit = ObjUtils.toBoolean(ruleDayAmountSumbit, false);
        ctx.setRuleDayAmountSumbit(ruleDayAmountSumbit);
        ctx.setUseCouponAfterPrice(useCouponAfterPrice);

        if (useRule == 1) {
            //用车场景
            TaxiRuleListContract taxiRuleListContract = taxiOrderCheckRule.getTaxiRuleListContract();
            // 单次限额
            ctx.setPriceLimitFlag(taxiRuleListContract.getPriceLimitFlag());
            ctx.setPriceLimit(taxiRuleListContract.getPriceLimit());
            // 单日限额
            ctx.setDayPriceLimit(taxiRuleListContract.getDayPriceLimit());
            ctx.setDayPriceLimitFlag(ctx.getDayPriceLimit().compareTo(BigDecimal.valueOf(0)) == 1);

            // 按城市分组级别限制
            if (Objects.equals(taxiRuleListContract.getCityLimitType(), TaxiApproveCityLimitTypeEnum.CITY_LEVEL.getType())) {

                List<TaxiApproveRuleGroupContract> cityPriceList = taxiRuleListContract.getCityPriceList();
                String priceLimitCity = startCityId;
                if (!Objects.equals(startCityId, arrivalCityId) && taxiRuleListContract.getPriceAcrossCityFlag() == 1) {
                    priceLimitCity = arrivalCityId;
                }

                if (CollectionUtils.isEmpty(cityPriceList)) {
                    logger.info("申请用车快照规则错误，获取城市信息失败，cityPriceList: {}", JsonUtils.toJson(cityPriceList));
                    throw new SaasException(GlobalResponseCode.InnerError);
                }

                boolean containsLimitCity = false;
                for (TaxiApproveRuleGroupContract group : cityPriceList) {
                    List<String> cityIdList;
                    // 兼容用户分组城市配置为空的情况
                    if (ObjUtils.isEmpty(group.getCityInfoList())) {
                        cityIdList = new ArrayList<>();
                    }
                    // 非空
                    else {
                        cityIdList = group.getCityInfoList()
                                .stream()
                                .map(TaxiApproveRuleGroupContract.CityInfo::getCityId)
                                .collect(Collectors.toList());
                    }
                    logger.info("校验用车城市所在分组, 当前分组信息 group:{}, priceLimit:{}, dayPriceLimit:{}, groupType:{}",
                            group.getGroupId(), group.getPriceLimit(), group.getDayPriceLimit(), group.getGroupType());
                    logger.info("校验用车城市所在分组, 当前分组城市信息 cityIdList:{}, priceLimitCity:{}",
                            JsonUtils.toJson(cityIdList), priceLimitCity);
                    if (cityIdList.contains(priceLimitCity)) {
                        containsLimitCity = true;
                        ctx.setCityPriceLimit(group.getPriceLimit());
                        ctx.setCityPriceLimitFlag(ctx.getCityPriceLimit().compareTo(BigDecimal.ZERO) > 0);
                        ctx.setCityDayPriceLimit(group.getDayPriceLimit());
                        ctx.setCityDayPriceLimitFlag(ctx.getCityDayPriceLimit().compareTo(BigDecimal.ZERO) > 0);
                        break;
                    }
                }
                // 如果规则里不存在该城市，走其他规则限制
                if (!containsLimitCity) {
                    ctx.setCityPriceLimit(ObjUtils.toBigDecimal(taxiRuleListContract.getExtraPerCountLimitPrice(), new BigDecimal("-1")));
                    ctx.setCityPriceLimitFlag(ctx.getCityPriceLimit().compareTo(BigDecimal.ZERO) > 0);
                    // 审批单日限额
                    ctx.setCityDayPriceLimit(ObjUtils.toBigDecimal(taxiRuleListContract.getExtraPerDayLimitPrice(), new BigDecimal("-1")));
                    ctx.setCityDayPriceLimitFlag(ctx.getCityDayPriceLimit().compareTo(BigDecimal.valueOf(0)) > 0);
                }
            }
            if (employeeTaxiRuleInfoContract != null) {
                employeeTaxiRuleInfoContract.setPriceLimit(ctx.getPriceLimit());
                employeeTaxiRuleInfoContract.setPriceLimitFlag(ctx.getPriceLimitFlag());
                employeeTaxiRuleInfoContract.setDayPriceLimit(ctx.getDayPriceLimit());
                employeeTaxiRuleInfoContract.setDayPriceLimitFlag(ctx.getDayPriceLimitFlag());
                employeeTaxiRuleInfoContract.setAvgDayPriceLimit(ctx.getAvgDayPriceLimit());
                employeeTaxiRuleInfoContract.setAvgDayPriceLimitFlag(ctx.getAvgDayPriceLimitFlag());
                employeeTaxiRuleInfoContract.setCityPriceLimit(ctx.getCityPriceLimit());
                employeeTaxiRuleInfoContract.setCityPriceLimitFlag(ctx.getCityPriceLimitFlag());
                employeeTaxiRuleInfoContract.setCityDayPriceLimit(ctx.getCityDayPriceLimit());
                employeeTaxiRuleInfoContract.setCityDayPriceLimitFlag(ctx.getCityDayPriceLimitFlag());
                employeeTaxiRuleInfoContract.setPerDayPriceLimit(ctx.getPerDayLimitPrice());
                employeeTaxiRuleInfoContract.setPerDayPriceLimitFlag(ctx.getPerDayLimitPriceFlag());
                employeeTaxiRuleInfoContract.setPriceAcrossCityFlag(taxiRuleListContract.getPriceAcrossCityFlag());
            }

        } else if (useRule == 2) {
            //申请用车场景
            TaxiApplyRule taxiApplyRule = taxiOrderCheckRule.getTaxiApplyRule();
            ctx.setRuleDayPriceLimit(ObjUtils.toBigDecimal(taxiApplyRule.getRuleDayPriceLimit(), BigDecimal.ZERO));
            ctx.setRuleDayPriceLimitFlag(ctx.getRuleDayPriceLimit().compareTo(BigDecimal.ZERO) > 0);
            dailyConsumption = ObjUtils.toBigDecimal(taxiApplyRule.getRuleDayPriceUsed(), BigDecimal.ZERO);
            //是否合并用车额度（1表示合并）
            ctx.setApplyUseRuleDayPriceLimit(ObjUtils.toInteger(taxiApplyRule.getUseRuleDayPriceLimit(), 0));

            Integer taxiApproveRuleFlag = taxiApplyRule.getTaxiApproveRuleFlag();

            log.info("preparePriceRuleCheckContext.taxiApproveRuleFlag={}",taxiApproveRuleFlag);
            if(taxiApproveRuleFlag == null || taxiApproveRuleFlag == 0){
                //处理V1 版本申请用车规则信息，费用配置为不限制、限制、有员工填写
                initV1ApplyContext(startCityId, arrivalCityId, ctx, taxiApplyRule,employeeTaxiRuleInfoContract);
            }else{
                //处理V2 版本申请用车规则信息，https://wiki.fenbeijinfu.com/pages/viewpage.action?pageId=60014420
                //此版本费用管控变成了平铺的模式，减少了费用限制类型字段
                initV2ApplyContext(startCityId, arrivalCityId, ctx, taxiApplyRule,employeeTaxiRuleInfoContract);
            }
            if (employeeTaxiRuleInfoContract != null) {
                employeeTaxiRuleInfoContract.setPriceLimit(ctx.getPriceLimit());
                employeeTaxiRuleInfoContract.setPriceLimitFlag(ctx.getPriceLimitFlag());
                employeeTaxiRuleInfoContract.setDayPriceLimit(ctx.getApplyDayPriceLimit());
                employeeTaxiRuleInfoContract.setDayPriceLimitFlag(ctx.getApplyDayPriceLimitFlag());
                employeeTaxiRuleInfoContract.setAvgDayPriceLimit(ctx.getAvgDayPriceLimit());
                employeeTaxiRuleInfoContract.setAvgDayPriceLimitFlag(ctx.getAvgDayPriceLimitFlag());
                employeeTaxiRuleInfoContract.setCityPriceLimit(ctx.getCityPriceLimit());
                employeeTaxiRuleInfoContract.setCityPriceLimitFlag(ctx.getCityPriceLimitFlag());
                employeeTaxiRuleInfoContract.setCityDayPriceLimit(ctx.getCityDayPriceLimit());
                employeeTaxiRuleInfoContract.setCityDayPriceLimitFlag(ctx.getCityDayPriceLimitFlag());
                employeeTaxiRuleInfoContract.setPerDayPriceLimit(ctx.getPerDayLimitPrice());
                employeeTaxiRuleInfoContract.setPerDayPriceLimitFlag(ctx.getPerDayLimitPriceFlag());
            }
        } else {
            logger.error("用车规则校验类型错误, useRule:{}", useRule);
        }

        ctx.setDailyConsumption(dailyConsumption);

        log.info("价格规则检查上下文，preparePriceRuleCheckContext ctx={}", JSON.toJSONString(ctx));
        return ctx;
    }

    private void initV2ApplyContext(String startCityId, String arrivalCityId, PriceRuleCheckContext ctx, TaxiApplyRule taxiApplyRule, EmployeeTaxiRuleInfo employeeTaxiRuleInfoContract) {
        boolean isSameCity = startCityId.equals(arrivalCityId);
        // 单次限额
        ctx.setPriceLimit(taxiApplyRule.getPriceLimit());
        ctx.setPriceLimitFlag(taxiApplyRule.getPriceLimit()!=null && taxiApplyRule.getPriceLimit().compareTo(BigDecimal.ZERO) > 0);
        if(DayPriceLimitTypeEnum.AVR_DAY_LIMIT.getType().equals(taxiApplyRule.getDayPriceLimitType())) {
            // 日均限额
            ctx.setAvgDayPriceLimit(taxiApplyRule.getDayPriceLimit());
            ctx.setAvgDayPriceLimitFlag(taxiApplyRule.getDayPriceLimit()!=null && taxiApplyRule.getDayPriceLimit().compareTo(BigDecimal.valueOf(0)) > 0);
            Date endTime = DateUtils.parse(taxiApplyRule.getEndTime(), DateUtils.FORMAT_DATE_WITH_BAR);
            Date startTime = DateUtils.parse(taxiApplyRule.getStartTime(), DateUtils.FORMAT_DATE_WITH_BAR);
            long days = DateUtil.between(startTime, endTime, DateUnit.DAY) + 1;
            //日均限额的总额度
            ctx.setAvgTotalPriceLimit(taxiApplyRule.getDayPriceLimit().multiply(BigDecimal.valueOf(days)));
            //已使用的额度
            ctx.setApplyPriceUsed(taxiApplyRule.getApplyPriceUsed());
        } else {
            // 单日限额
            ctx.setApplyDayPriceLimit(taxiApplyRule.getDayPriceLimit());
            ctx.setApplyDayPriceLimitFlag(taxiApplyRule.getDayPriceLimit()!=null && taxiApplyRule.getDayPriceLimit().compareTo(BigDecimal.valueOf(0)) > 0);
            ctx.setApplyDayPriceUsed(taxiApplyRule.getApplyDayPriceUsed());
        }

        // 2:员工填写
        // 审批单限额
        if(TaxiApproveEmployeeLimitTypeEnum.EMPLOYEE_WRITE.getType() == taxiApplyRule.getEmployeeLimitType()) {
            ctx.setApplyPriceLimitFlag(true);
            ctx.setApplyPriceLimit(taxiApplyRule.getApplyPriceLimit());
            ctx.setApplyPriceUsed(taxiApplyRule.getApplyPriceUsed());
        }

        // 3:按城市分组级别限制
        // 4:按照指定城市限制
        if(TaxiApplyHelper.isCityPriceLimit(taxiApplyRule)) {
            String priceLimitCity;
            if (!isSameCity && taxiApplyRule.getPriceAcrossCityFlag() == 1) {
                priceLimitCity = arrivalCityId;
            } else {
                priceLimitCity = startCityId;
            }
            List<TaxiApproveRuleGroupContract> groupList = taxiApplyRule.getRuleGroupContractList();
            if (CollectionUtils.isEmpty(groupList)) {
                logger.info("申请用车快照规则错误，获取城市信息失败，groupList: {}", JsonUtils.toJson(groupList));
                throw new SaasException(GlobalResponseCode.InnerError);
            }
            boolean containsLimitCity = false;
            for (TaxiApproveRuleGroupContract group : groupList) {
                List<String> cityIdList = group.getCityInfoList()
                        .stream()
                        .map(TaxiApproveRuleGroupContract.CityInfo::getCityId)
                        .collect(Collectors.toList());
                logger.info("校验用车城市所在分组, 当前分组信息 group: {},priceLimit={},dayPriceLimit={},groupType={}", group.getGroupId(),group.getPriceLimit(),group.getDayPriceLimit(),group.getGroupType());
                logger.info("校验用车城市所在分组, 当前分组城市信息 cityIdList: {} priceLimitCity: {}", JsonUtils.toJson(cityIdList),priceLimitCity);
                if (cityIdList.contains(priceLimitCity)) {
                    containsLimitCity = true;
                    ctx.setCityPriceLimit(group.getPriceLimit());
                    ctx.setCityPriceLimitFlag(ctx.getCityPriceLimit()!=null && ctx.getCityPriceLimit().compareTo(BigDecimal.ZERO) > 0);
                    ctx.setCityDayPriceLimit(group.getDayPriceLimit());
                    ctx.setCityDayPriceLimitFlag(ctx.getCityDayPriceLimit()!=null && ctx.getCityDayPriceLimit().compareTo(BigDecimal.ZERO) > 0);
                    break;
                }
            }
            // 如果规则里不存在该城市，走其他规则限制
            if (!containsLimitCity) {
                ctx.setCityPriceLimit(taxiApplyRule.getExtraPerCountLimitPrice());
                ctx.setCityPriceLimitFlag(taxiApplyRule.getExtraPerCountLimitPrice()!=null && taxiApplyRule.getExtraPerCountLimitPrice().compareTo(BigDecimal.ZERO) > 0);
                // 审批单日限额
                ctx.setCityDayPriceLimit(taxiApplyRule.getExtraPerDayLimitPrice());
                ctx.setCityDayPriceLimitFlag(taxiApplyRule.getExtraPerDayLimitPrice()!=null && taxiApplyRule.getExtraPerDayLimitPrice().compareTo(BigDecimal.valueOf(0)) > 0);
            }
            ctx.setCityApplyPriceUsed(taxiApplyRule.getCityApplyPriceUsed());
            ctx.setCityApplyDayPriceUsed(taxiApplyRule.getCityApplyDayPriceUsed());
        }

        //申请用车单日限额，用于限制该场景下多个申请单汇总额度
        if(taxiApplyRule.getPerDayLimitPrice()!=null && taxiApplyRule.getPerDayLimitPrice().compareTo(BigDecimal.ZERO) > 0) {
            ctx.setPerDayLimitPriceFlag(true);
            ctx.setPerDayLimitPrice(taxiApplyRule.getPerDayLimitPrice());
            ctx.setPerDayLimitPriceUsed(taxiApplyRule.getPerDayPriceUsed());
        }

    }

    private void initV1ApplyContext(String startCityId, String arrivalCityId, PriceRuleCheckContext ctx, TaxiApplyRule taxiApplyRule, EmployeeTaxiRuleInfo employeeTaxiRuleInfoContract) {
        TaxiApprovePriceLimitType limitType = TaxiApprovePriceLimitType.valueOf(taxiApplyRule.getPriceLimitFlag());
        boolean isSameCity = startCityId.equals(arrivalCityId);
        if (TaxiApprovePriceLimitType.LIMIT_ALL == limitType) {
            // 1:限制
            // 单次限额
            ctx.setPriceLimit(taxiApplyRule.getPriceLimit());
            ctx.setPriceLimitFlag(ctx.getPriceLimit().compareTo(BigDecimal.ZERO) > 0);
            if(DayPriceLimitTypeEnum.AVR_DAY_LIMIT.getType().equals(taxiApplyRule.getDayPriceLimitType())) {
                // 日均限额
                ctx.setAvgDayPriceLimitFlag(true);
                ctx.setAvgDayPriceLimit(taxiApplyRule.getDayPriceLimit());
                Date endTime = DateUtils.parse(taxiApplyRule.getEndTime(), DateUtils.FORMAT_DATE_WITH_BAR);
                Date startTime = DateUtils.parse(taxiApplyRule.getStartTime(), DateUtils.FORMAT_DATE_WITH_BAR);
                long days = DateUtil.between(startTime, endTime, DateUnit.DAY) + 1;
                //日均限额的总额度
                ctx.setAvgTotalPriceLimit(taxiApplyRule.getDayPriceLimit().multiply(BigDecimal.valueOf(days)));
                //已使用的额度
                ctx.setApplyPriceUsed(taxiApplyRule.getApplyPriceUsed());
            } else {
                // 单日限额
                ctx.setApplyDayPriceLimit(taxiApplyRule.getDayPriceLimit());
                ctx.setApplyDayPriceLimitFlag(ctx.getApplyDayPriceLimit().compareTo(BigDecimal.valueOf(0)) > 0);
                ctx.setApplyDayPriceUsed(taxiApplyRule.getApplyDayPriceUsed());
            }
        } else if (TaxiApprovePriceLimitType.EMPLOYEE_FILL == limitType) {
            // 2:员工填写
            // 审批单限额
            ctx.setApplyPriceLimitFlag(true);
            ctx.setApplyPriceLimit(taxiApplyRule.getApplyPriceLimit());
            ctx.setApplyPriceUsed(taxiApplyRule.getApplyPriceUsed());
        } else if (TaxiApprovePriceLimitType.LIMIT_CITY_GROUP == limitType
                || TaxiApprovePriceLimitType.LIMIT_CITY == limitType) {
            // 3:按城市分组级别限制
            // 4:按照指定城市限制
            String priceLimitCity;
            if (!isSameCity && taxiApplyRule.getPriceAcrossCityFlag() == 1) {
                priceLimitCity = arrivalCityId;
            } else {
                priceLimitCity = startCityId;
            }
            List<TaxiApproveRuleGroupContract> groupList = taxiApplyRule.getRuleGroupContractList();
            if (CollectionUtils.isEmpty(groupList)) {
                logger.info("申请用车快照规则错误，获取城市信息失败，groupList: {}", JsonUtils.toJson(groupList));
                throw new SaasException(GlobalResponseCode.InnerError);
            }
            boolean containsLimitCity = false;
            for (TaxiApproveRuleGroupContract group : groupList) {
                List<String> cityIdList = group.getCityInfoList()
                        .stream()
                        .map(TaxiApproveRuleGroupContract.CityInfo::getCityId)
                        .collect(Collectors.toList());
                logger.info("校验用车城市所在分组, 当前分组信息 group: {},priceLimit={},dayPriceLimit={},groupType={}", group.getGroupId(),group.getPriceLimit(),group.getDayPriceLimit(),group.getGroupType());
                logger.info("校验用车城市所在分组, 当前分组城市信息 cityIdList: {},priceLimitCity: {}", JsonUtils.toJson(cityIdList), priceLimitCity);
                if (cityIdList.contains(priceLimitCity)) {
                    containsLimitCity = true;
                    ctx.setCityPriceLimit(group.getPriceLimit());
                    ctx.setCityPriceLimitFlag(ctx.getCityPriceLimit().compareTo(BigDecimal.ZERO) > 0);
                    ctx.setCityDayPriceLimit(group.getDayPriceLimit());
                    ctx.setCityDayPriceLimitFlag(ctx.getCityDayPriceLimit().compareTo(BigDecimal.ZERO) > 0);
                    break;
                }
            }
            // 如果规则里不存在该城市，走其他规则限制
            if (!containsLimitCity) {
                ctx.setCityPriceLimit(taxiApplyRule.getPriceLimit());
                ctx.setCityPriceLimitFlag(ctx.getCityPriceLimit().compareTo(BigDecimal.ZERO) > 0);
                // 审批单日限额
                ctx.setCityDayPriceLimit(taxiApplyRule.getDayPriceLimit());
                ctx.setCityDayPriceLimitFlag(ctx.getCityDayPriceLimit().compareTo(BigDecimal.valueOf(0)) > 0);
            }
            ctx.setCityApplyDayPriceUsed(taxiApplyRule.getCityApplyDayPriceUsed());
        }
    }

    /**
     * 用车校验车型
     *
     * @param taxiOrderCheckRule
     * @param taxiTypeCode
     * @return
     */
    public List<TempOrderRuleCheckResult> checkTaxiAllowedTaxiType(TaxiOrderCheckRule taxiOrderCheckRule,
                                                                   BigDecimal useCouponAfterPrice, String taxiTypeCode) {
        logger.info("checkTaxiAllowedTaxiType.taxiTypeCode：{}", JsonUtils.toJson(taxiTypeCode));

        List<TempOrderRuleCheckResult> listMap = new ArrayList<>();
        Integer useRule = taxiOrderCheckRule.getUseRule();
        Boolean limitTaxiType = false;
        String allowedTaxiType = null;
        boolean isOpenExceedConfig = false;
        Integer allowedTaxiTypeConfig = null;
        if (useRule == 1) {
            TaxiRuleListContract taxiRuleListContract = taxiOrderCheckRule.getTaxiRuleListContract();
            // 车型限制
            limitTaxiType = taxiRuleListContract.getLimitTaxiType();
            List<KvContract> allowedTaxiTypeList = taxiRuleListContract.getAllowedTaxiType();
            List<String> keyList = Lists.newArrayList();
            if (ObjUtils.isNotEmpty(allowedTaxiTypeList)) {
                for (KvContract kvContract : allowedTaxiTypeList) {
                    keyList.add(String.valueOf(kvContract.getKey()));
                }
            }
            allowedTaxiType = String.join(",", keyList);
            if (Objects.equals(taxiRuleListContract.getIsOpenExceedConfig(), true)) {
                TaxiExceedConfigPo exceedConfigInfo = taxiRuleListContract.getExceedConfigInfo();
                allowedTaxiTypeConfig = exceedConfigInfo.getAllowedTaxiTypeConfig();
                isOpenExceedConfig = true;
            }
        } else if (useRule == 2) {
            TaxiApplyRule taxiApplyRule = taxiOrderCheckRule.getTaxiApplyRule();
            // 车型限制
            limitTaxiType = ObjUtils.isNotBlank(taxiApplyRule.getAllowedTaxiType());
            allowedTaxiType = taxiApplyRule.getAllowedTaxiType();
            if (Objects.equals(taxiApplyRule.getOpenExceedConfig(), true)) {
                TaxiExceedConfigPo exceedConfigInfo = taxiApplyRule.getExceedConfigInfo();
                allowedTaxiTypeConfig = exceedConfigInfo.getAllowedTaxiTypeConfig();
                isOpenExceedConfig = true;
            }
        } else {
            logger.error("用车规则校验类型错误, useRule:{}", useRule);
        }

        TaxiFloatingConfig taxiFloatingConfig = taxiOrderCheckRule.getTaxiFloatingConfig();
        // 浮动车型价格校验
        if (taxiFloatingConfig != null && taxiFloatingConfig.isFloatingConfig()) {
            BigDecimal floatedPrice = taxiFloatingConfig.getFloatedPrice();
            if (useCouponAfterPrice.compareTo(floatedPrice) > 0) {
                TempOrderRuleCheckResult checkResult = new TempOrderRuleCheckResult();
                checkResult.setResCode(TemporaryResponseCode.OrderTaxiFloatingCheckFail.getCode(),
                        StrUtils.formatString(
                                TemporaryResponseCode.OrderTaxiFloatingCheckFail.getMsg(),
                                TaxiTypeEnum.getTaxiTypeName(taxiFloatingConfig.getTaxiTypeBased()),
                                taxiFloatingConfig.getFloatingRatio()
                        )
                );
                if (isOpenExceedConfig) {
                    checkResult.setExceedConfig(allowedTaxiTypeConfig);
                }
                listMap.add(checkResult);
            }
        } else {
            //校验用车类型
            if (limitTaxiType && allowedTaxiType != null && !StringTool.isContains(taxiTypeCode, allowedTaxiType)) {
                TempOrderRuleCheckResult checkResult = new TempOrderRuleCheckResult();
                checkResult.setResCode(TemporaryResponseCode.OrderTaxiType.getCode(), StrUtils.formatString(TemporaryResponseCode.OrderTaxiType.getMsg(), TaxiModelsType.getNamesByIds(allowedTaxiType)));
                if (isOpenExceedConfig) {
                    checkResult.setExceedConfig(allowedTaxiTypeConfig);
                }
                listMap.add(checkResult);
            }
        }
        return listMap;
    }

    /**
     * 强制提交提示（超标但需要理由）
     * @return
     */
    public OneKeyTaxiOrderRuleCheckResult checkExceedAuth(String companyId, Boolean exceedSubmit, String exceedReason, String exceedReasonComment) {
        //默认初始化
        OneKeyTaxiOrderRuleCheckResult result = new OneKeyTaxiOrderRuleCheckResult();
        result.setResCode(TemporaryResponseCode.Success);
        exceedSubmit = ObjUtils.ifNull(exceedSubmit, Boolean.FALSE);
        if (exceedSubmit) {
            // 超标后强制提交
            if (StringUtils.isEmpty(exceedReason)
                    || exceedReason.length() > 50) {
                result.setResCode(TemporaryResponseCode.OrderCheckExceedReasonInvalid);
                return result;
            }
            if (StringUtils.isNotEmpty(exceedReasonComment)
                    && exceedReasonComment.length() > 200) {
                result.setResCode(TemporaryResponseCode.OrderCheckExceedReasonCommentInvalid);
                return result;
            }
            ReasonConfig reasonConfig = customReasonService.queryReasonConfigWithoutItems(companyId, ReasonType.EXCEED_ORDER_TAXI);
            if (reasonConfig.getReason() == SaasMessageConstant.IS_CHECKED_TRUE) {
                if (StringUtils.isBlank(exceedReason)) {
                    result.setResCode(TemporaryResponseCode.OrderCheckExceedReasonIsNull);
                    return result;
                }
                if (reasonConfig.getReason_desc() == SaasMessageConstant.IS_CHECKED_TRUE) {
                    if (StringUtils.isBlank(exceedReasonComment)) {
                        result.setResCode(TemporaryResponseCode.OrderCheckExceedReasonCommentIsNull);
                        return result;
                    }
                }
            }
        } else {
            // 超标，提示用户
            result.setResCode(TemporaryResponseCode.OrderCheckExceedNeedReason);
            return result;
        }
        return result;
    }

    public List<EmployeeTaxiRuleInfo.PathRule> getPathRule(List<TaxiRulePath> pathLocationInfos, List<TaxiRuleListContract.Path> pathList) {
        log.info("pathLocationInfos={}", JsonUtils.toJson(pathLocationInfos));
        log.info("pathList={}", JsonUtils.toJson(pathList));
        List<EmployeeTaxiRuleInfo.PathRule> res = Lists.newArrayList();
        if (ObjUtils.isEmpty(pathLocationInfos)) {
            log.info("getPathRule pathLocationInfos isEmpty");
            return res;
        } else if (ObjUtils.isEmpty(pathList)) {
            log.info("getPathRule pathList isEmpty");
            return res;
        } else if (pathLocationInfos.size() != pathList.size()) {
            log.info("getPathRule pathLocationInfos pathList 数量不等");
            return res;
        }
        Set<String> depIdSet = Sets.newHashSet();
        List<EmployeeTaxiRuleInfo.LimitLocation> depList = Lists.newArrayList();  //自选地点集合
        Set<String> arrIdSet = Sets.newHashSet();
        List<EmployeeTaxiRuleInfo.LimitLocation> arrList = Lists.newArrayList();  //自选地点集合
        for (int i = 0, len = pathList.size(); i < len; i++) {
            TaxiRulePath t = pathLocationInfos.get(i);
            TaxiRuleListContract.Path p = pathList.get(i);
            if (t.getLimitPathType() != null && t.getLimitPathType() == 3) {     //任意点集合间通行
                distinctAdd(depIdSet, depList, p.getDeparture());
                distinctAdd(arrIdSet, arrList, p.getArrival());
                continue;
            }
            EmployeeTaxiRuleInfo.PathRule rule = new EmployeeTaxiRuleInfo.PathRule();
            //dep
            if (t.getLimitDeparture() != null && t.getLimitDeparture()) {
                rule.setLimitDeparture(true);
                if (t.getLimitDepartureType() == 1) {       //自选
                    rule.setLimitDepartureType(1);
                    TaxiRuleListContract.Departure location = p.getDeparture();
                    if (location == null) {
                        log.info("配置异常，不记录快照信息 {}", t);
                        continue;
                    }
                    EmployeeTaxiRuleInfo.LimitLocation depLocation = new EmployeeTaxiRuleInfo.LimitLocation();
                    depLocation.setLocationId(location.getId());
                    depLocation.setName(location.getName());
                    depLocation.setLocationType(location.getLocation_type());
                    depLocation.setCityCode(location.getCity_code());
                    depLocation.setRadius(location.getRadius());
                    depLocation.setLat(location.getLat());
                    depLocation.setLng(location.getLng());
                    rule.setDepLocation(depLocation);
                } else if (t.getLimitDepartureType() == 2) {       //场站
                    rule.setLimitDepartureType(t.getDepartureLocationId());
                }
            }
            //arr
            if (t.getLimitArrival() != null && t.getLimitArrival()) {
                rule.setLimitArrival(true);
                if (t.getLimitArrivalType() == 1) {       //自选
                    rule.setLimitArrivalType(1);
                    TaxiRuleListContract.Departure location = p.getArrival();
                    if (location == null) {
                        log.info("配置异常，不记录快照信息 {}", t);
                        continue;
                    }
                    EmployeeTaxiRuleInfo.LimitLocation arrLocation = new EmployeeTaxiRuleInfo.LimitLocation();
                    arrLocation.setLocationId(location.getId());
                    arrLocation.setName(location.getName());
                    arrLocation.setLocationType(location.getLocation_type());
                    arrLocation.setCityCode(location.getCity_code());
                    arrLocation.setRadius(location.getRadius());
                    arrLocation.setLat(location.getLat());
                    arrLocation.setLng(location.getLng());
                    rule.setArrLocation(arrLocation);
                } else if (t.getLimitArrivalType() == 2) {       //场站
                    rule.setLimitArrivalType(t.getArrivalLocationId());
                }
            }
            if (rule.getLimitArrival() || rule.getLimitDeparture()) {
                res.add(rule);
            }
        }
        if (!ObjUtils.isEmpty(depList) && !ObjUtils.isEmpty(arrList)) {     //含任意点集合间用车规则
            EmployeeTaxiRuleInfo.PathRule rule = new EmployeeTaxiRuleInfo.PathRule();
            rule.setLimitDeparture(true);
            rule.setDepLocationList(depList);
            rule.setLimitArrival(true);
            rule.setArrLocationList(arrList);
            res.add(rule);
        }
        return res;
    }

    public List<TaxiRuleListContract.Path> getPathList(List<com.fenbeitong.saasplus.api.model.dto.rule.TaxiRulePath> taxiRulePaths, String companyId){
        List<TaxiRuleListContract.Path> pathList = new ArrayList<>();
        Set<Integer> locationIdSet = Sets.newHashSet();
        for (com.fenbeitong.saasplus.api.model.dto.rule.TaxiRulePath taxiPathLocation : taxiRulePaths) {
            if (taxiPathLocation.getLimitDeparture() && taxiPathLocation.getLimitDepartureType() == PathLocationTypeEnum.NORMAL.getCode()) {
                locationIdSet.add(taxiPathLocation.getDepartureLocationId());
            }
            if (taxiPathLocation.getLimitArrival() && taxiPathLocation.getLimitArrivalType() == PathLocationTypeEnum.NORMAL.getCode()) {
                locationIdSet.add(taxiPathLocation.getArrivalLocationId());
            }
        }
        locationIdSet = locationIdSet.stream().filter(x -> x != null && x > 0).collect(Collectors.toSet());
        Map<String, TaxiLocation> locationMap = getLocationMap(locationIdSet);
        for (com.fenbeitong.saasplus.api.model.dto.rule.TaxiRulePath taxiRulePath : taxiRulePaths) {
            // 用车到达位置限制
            TaxiRuleListContract.Departure arrival = new TaxiRuleListContract.Departure();
            arrival.setId(-1);
            arrival.setName("任意位置");
            arrival.setCompanyId(companyId);
            arrival.setComment_name("");
            if (taxiRulePath.getLimitArrival()) {
                Integer limitArrivalType = taxiRulePath.getLimitArrivalType();
                if (limitArrivalType == PathLocationTypeEnum.NORMAL.getCode()) {
                    arrival.setPath_type(PathLocationTypeEnum.NORMAL.getCode());
                    Integer arrivalId = taxiRulePath.getArrivalLocationId();
                    TaxiLocation taxiLocation = locationMap.get(arrivalId + "");
                    //taxiLocationMapper.selectByPrimaryKey(arrivalId);
                    if (taxiLocation == null) {
                        continue;
                    }
                    arrival.setId(taxiLocation.getId());
                    arrival.setName(taxiLocation.getName());
                    arrival.setCompanyId(taxiLocation.getCompanyId());
                    arrival.setLat(taxiLocation.getLat());
                    arrival.setLng(taxiLocation.getLng());
                    arrival.setRadius(taxiLocation.getRadius());
                    arrival.setLocation_type(taxiLocation.getLocationType());
                    arrival.setCity_code(taxiLocation.getCityCode());
                    arrival.setCity_name(taxiLocation.getCityName());
                    if (StringUtils.isBlank(taxiLocation.getCommentName())) {
                        arrival.setComment_name("用车位置");
                    } else {
                        arrival.setComment_name(taxiLocation.getCommentName());
                    }
                }
                if (limitArrivalType == PathLocationTypeEnum.FIXED.getCode()) {
                    arrival.setPath_type(PathLocationTypeEnum.FIXED.getCode());
                    if (taxiRulePath.getArrivalLocationId() == FixedPathLocationEnum.AIR_PORT.getCode()) {
                        arrival.setId(FixedPathLocationEnum.AIR_PORT.getCode());
                        arrival.setComment_name(FixedPathLocationEnum.AIR_PORT.getValue());
                        arrival.setName(FixedPathLocationEnum.AIR_PORT.getDesc());
                    }
                    if (taxiRulePath.getArrivalLocationId() == FixedPathLocationEnum.TRAIN_STATION.getCode()) {
                        arrival.setId(FixedPathLocationEnum.TRAIN_STATION.getCode());
                        arrival.setComment_name(FixedPathLocationEnum.TRAIN_STATION.getValue());
                        arrival.setName(FixedPathLocationEnum.TRAIN_STATION.getDesc());
                    }
                    if (taxiRulePath.getArrivalLocationId() == FixedPathLocationEnum.HOME_ADDRESS.getCode()) {
                        arrival.setId(FixedPathLocationEnum.HOME_ADDRESS.getCode());
                        arrival.setComment_name(FixedPathLocationEnum.HOME_ADDRESS.getValue());
                        arrival.setName(FixedPathLocationEnum.HOME_ADDRESS.getDesc());
                    }
                    if (taxiRulePath.getArrivalLocationId() == FixedPathLocationEnum.WORK_LOCATION.getCode()) {
                        arrival.setId(FixedPathLocationEnum.WORK_LOCATION.getCode());
                        arrival.setComment_name(FixedPathLocationEnum.WORK_LOCATION.getValue());
                        arrival.setName(FixedPathLocationEnum.WORK_LOCATION.getDesc());
                    }
                }
            }
            TaxiRuleListContract.Departure departure = new TaxiRuleListContract.Departure();
            departure.setId(-1);
            departure.setName("任意位置");
            departure.setCompanyId(companyId);
            departure.setComment_name("");
            if (taxiRulePath.getLimitDeparture()) {
                Integer limitDepartureType = taxiRulePath.getLimitDepartureType();
                if (limitDepartureType == PathLocationTypeEnum.NORMAL.getCode()) {
                    departure.setPath_type(PathLocationTypeEnum.NORMAL.getCode());
                    Integer departureId = taxiRulePath.getDepartureLocationId();
                    TaxiLocation taxiLocation = locationMap.get(departureId + "");
                    //taxiLocationMapper.selectByPrimaryKey(departureId);
                    if (taxiLocation == null) {
                        continue;
                    }
                    departure.setId(taxiLocation.getId());
                    departure.setName(taxiLocation.getName());
                    departure.setCompanyId(taxiLocation.getCompanyId());
                    departure.setLat(taxiLocation.getLat());
                    departure.setLng(taxiLocation.getLng());
                    departure.setRadius(taxiLocation.getRadius());
                    departure.setLocation_type(taxiLocation.getLocationType());
                    departure.setCity_code(taxiLocation.getCityCode());
                    departure.setCity_name(taxiLocation.getCityName());
                    if (StringUtils.isBlank(taxiLocation.getCommentName())) {
                        departure.setComment_name("用车位置");
                    } else {
                        departure.setComment_name(taxiLocation.getCommentName());
                    }
                }
                if (limitDepartureType == PathLocationTypeEnum.FIXED.getCode()) {
                    departure.setPath_type(PathLocationTypeEnum.FIXED.getCode());
                    if (taxiRulePath.getDepartureLocationId() == FixedPathLocationEnum.AIR_PORT.getCode()) {
                        departure.setId(FixedPathLocationEnum.AIR_PORT.getCode());
                        departure.setComment_name(FixedPathLocationEnum.AIR_PORT.getValue());
                        departure.setName(FixedPathLocationEnum.AIR_PORT.getDesc());
                    }
                    if (taxiRulePath.getDepartureLocationId() == FixedPathLocationEnum.TRAIN_STATION.getCode()) {
                        departure.setId(FixedPathLocationEnum.TRAIN_STATION.getCode());
                        departure.setComment_name(FixedPathLocationEnum.TRAIN_STATION.getValue());
                        departure.setName(FixedPathLocationEnum.TRAIN_STATION.getDesc());
                    }
                    if (taxiRulePath.getDepartureLocationId() == FixedPathLocationEnum.HOME_ADDRESS.getCode()) {
                        departure.setId(FixedPathLocationEnum.HOME_ADDRESS.getCode());
                        departure.setComment_name(FixedPathLocationEnum.HOME_ADDRESS.getValue());
                        departure.setName(FixedPathLocationEnum.HOME_ADDRESS.getDesc());
                    }
                    if (taxiRulePath.getDepartureLocationId() == FixedPathLocationEnum.WORK_LOCATION.getCode()) {
                        departure.setId(FixedPathLocationEnum.WORK_LOCATION.getCode());
                        departure.setComment_name(FixedPathLocationEnum.WORK_LOCATION.getValue());
                        departure.setName(FixedPathLocationEnum.WORK_LOCATION.getDesc());
                    }
                }
            }
            TaxiRuleListContract.Path path = new TaxiRuleListContract.Path();
            path.setDeparture(departure);
            path.setArrival(arrival);
            pathList.add(path);
        }
        return pathList;
    }

    private Map<String, TaxiLocation> getLocationMap(Collection<Integer> locationIdSet) {
        Map<String, TaxiLocation> map = Maps.newHashMap();
        if (ObjUtils.isEmpty(locationIdSet)) {
            return map;
        }
        List<List<Integer>> lists = Lists.partition(Lists.newArrayList(locationIdSet), 200);
        for (List<Integer> list : lists) {
            List<TaxiLocation> res = taxiLocationMapper.listByPrimaryKey(list);
            if (res != null) {
                for (TaxiLocation location : res) {
                    if (location == null) {
                        continue;
                    }
                    map.put(location.getId() + "", location);
                }
            }
        }
        return map;
    }

    /**
     * set中不存在id时add
     */
    private void distinctAdd(Set<String> idSet, List<EmployeeTaxiRuleInfo.LimitLocation> list, TaxiRuleListContract.Departure location) {
        if (location == null) {
            log.warn("distinctAdd location null");
            return;
        } else if (location.getId() < 1) {
            log.warn("distinctAdd id error {}", location);
            return;
        }
        String id = location.getId() + "";
        if (idSet.contains(id)) {
            return;
        }
        idSet.add(id);
        EmployeeTaxiRuleInfo.LimitLocation limitLocation = new EmployeeTaxiRuleInfo.LimitLocation();
        limitLocation.setLocationId(location.getId());
        limitLocation.setName(location.getName());
        limitLocation.setLat(location.getLat());
        limitLocation.setLng(location.getLng());
        if (location.getLocation_type() == null || location.getLocation_type().equals(1)) {         //定点，距离最大10km
            limitLocation.setRadius(location.getRadius());
        } else if (location.getLocation_type().equals(2)) {
            limitLocation.setCityCode(location.getCity_code());
        } else {
            log.warn("distinctAdd 不支持的点类型 {}", location);
            return;
        }
        list.add(limitLocation);
    }

    /**
     * 任意点集合间校验(含任意点附近固定距离和固定城市)
     */
    private boolean taxiPathListCheck(TaxiModifyDestinationCheckReqContract.Location location, List<String> cityCodes,
        List<EmployeeTaxiRuleInfo.LimitLocation> locations, double homeRadius,
        TaxiAvailableReqContract.HomeLocation homeLocation, List<TaxiLocation> workLocationList) {
        log.info("判断城市之前：locations={}, cityCodes={}", JsonUtils.toJson(locations), JsonUtils.toJson(cityCodes));
        log.info("判断城市之前： location={}", JsonUtils.toJson(location));
        //判断城市
        List<String> limitCityList = locations.stream().filter(x -> !ObjUtils.isEmpty(x.getCityCode())).map(x -> x.getCityCode()).collect(Collectors.toList());
        log.info("任意点集合间往返,城市限制集合 {}", limitCityList);
        if (limitCityList.size() > 0) {
            if (limitCityList.contains(location.getCity_code())) {
                return true;
            }
            //尝试补偿城市
            getCityCodesByLocation(location.getLat(), location.getLng(), cityCodes);
            for (String cityCode : cityCodes) {
                if (limitCityList.contains(cityCode)) {
                    return true;
                }
            }
        }
        //判断经纬度
        Double lat = location.getLat();         //0.1 度约11km
        Double maxLat = lat + 0.1d;
        Double minLat = lat - 0.1d;
        Double lng = location.getLng();         //0.14 度约11km
        Double maxLng = lng + 0.14d;
        Double minLng = lng - 0.14d;

        //固定地点
        List<EmployeeTaxiRuleInfo.LimitLocation> gdlist = locations.stream()
                .filter(x->x.getLocationId().equals(3) || x.getLocationId().equals(2)
                    || x.getLocationId().equals(4) || x.getLocationId().equals(5))
                .collect(Collectors.toList());
        if(ObjUtils.isNotEmpty(gdlist)){
            for (EmployeeTaxiRuleInfo.LimitLocation limitLocation : gdlist) {
                if (limitLocation.getLocationId().equals(FixedPathLocationEnum.AIR_PORT.getCode())) {     //机场
                    int radius = 3500;
                    ContrailParam contrailParam = new ContrailParam();
                    contrailParam.setLongitude(location.getLng().toString());
                    contrailParam.setLatitude(location.getLat().toString());
                    contrailParam.setDistance(radius);
                    log.info("查询机场信息：contrailParam = {} ,radius = {}", JsonUtils.toJson(contrailParam), radius);
                    RpcCommonResult<List<AirportDTO>> listRpcCommonResult = iAirportContrailSearchService.queryByLocationAndDistance(contrailParam);
                    log.info("查询机场信息返回结果：listRpcCommonResult = {}", JsonUtils.toJson(listRpcCommonResult));
                    if (CollectionUtils.isNotEmpty(listRpcCommonResult.getData())) {   //范围内无机场
                        return true;
                    }
                } else if (limitLocation.getLocationId().equals(FixedPathLocationEnum.TRAIN_STATION.getCode())) {       //火车站
                    int radius = 1500;
                    ContrailParam contrailParam = new ContrailParam();
                    contrailParam.setLongitude(location.getLng().toString());
                    contrailParam.setLatitude(location.getLat().toString());
                    contrailParam.setDistance(radius);
                    log.info("查询火车站信息：contrailParam = {} ,radius = {}", JsonUtils.toJson(contrailParam), radius);
                    RpcCommonResult<List<TrainStationDTO>> listRpcCommonResult = iTrainStationContrailService.queryByLocationAndDistance(contrailParam);
                    log.info("查询火车站信息返回结果：listRpcCommonResult = {}", JsonUtils.toJson(listRpcCommonResult));
                    if (CollectionUtils.isNotEmpty(listRpcCommonResult.getData())) {   //范围内无火车站
                        return true;
                    }
                } else if (limitLocation.getLocationId().equals(FixedPathLocationEnum.HOME_ADDRESS.getCode())) {
                    if (homeLocation != null && homeLocation.getLat() != null && homeLocation.getLng() != null) {
                        double des = LocationUtils.getDistance(homeLocation.getLat(), homeLocation.getLng(),
                            location.getLat(), location.getLng());
                        log.info("家庭地址，计算距离：{},{}, radius={},des={}", location, homeLocation, homeRadius, des);
                        if (des <= homeRadius) {     //符合距离
                            return true;
                        }
                    }
                } else if (limitLocation.getLocationId().equals(FixedPathLocationEnum.WORK_LOCATION.getCode())) {
                    if (CollectionUtils.isNotEmpty(workLocationList)) {
                        //自选地点
                        List<TaxiLocation> list = workLocationList.stream()
                            .filter(x -> x.getRadius() != null && x.getLat() > minLat
                                && x.getLat() < maxLat && x.getLng() > minLng && x.getLng() < maxLng)
                            .collect(Collectors.toList());
                        for (TaxiLocation taxiLocation : list) {
                            Double radius = taxiLocation.getRadius();
                            Double des = LocationUtils.getDistance(taxiLocation.getLat(), taxiLocation.getLng(), location.getLat(), location.getLng());
                            log.info("办公地点，计算距离：{},{},radius={},des={}", location, limitLocation, radius, des);
                            if (des <= radius) {     //符合距离
                                return true;
                            }
                        }
                    }
                }
            }
        }

        //自选地点
        List<EmployeeTaxiRuleInfo.LimitLocation> list = locations.stream()
                .filter(x -> x.getRadius() != null && x.getLat() > minLat && x.getLat() < maxLat && x.getLng() > minLng && x.getLng() < maxLng)
                .collect(Collectors.toList());
        log.info("任意点集合间往返，坐标 {} 附近11km点集合：{}", location, JsonUtils.toJson(list));
        for (EmployeeTaxiRuleInfo.LimitLocation limitLocation : list) {
            Double radius = limitLocation.getRadius();
            Double des = LocationUtils.getDistance(limitLocation.getLat(), limitLocation.getLng(), location.getLat(), location.getLng());
            log.info("任意点集合间往返，计算距离：{},{},radius={},des={}", location, limitLocation, radius, des);
            if (des <= radius) {     //符合距离
                return true;
            }
        }
        return false;
    }

    /**
     * 检查申请单填写地点距离
     *
     * @param location
     * @param checkAddress
     * @param applyLimitLocation
     * @param applyLocationLimitRadius
     * @return
     */
    private boolean checkApplyLimitLocation(TaxiModifyDestinationCheckReqContract.Location location,
        Integer checkAddress, EmployeeTaxiRuleInfo.ApplyLimitLocation applyLimitLocation,
        Double applyLocationLimitRadius) {
        log.info("[ checkApplyLimitLocation ] location={},applyLimitLocation={},applyLocationLimitRadius={}", JsonUtils.toJson(location), JsonUtils.toJson(applyLimitLocation), applyLocationLimitRadius);
        if (Objects.equals(checkAddress, 0)) {
            // 申请单填写地点选填，此时校验通过
            return true;
        }
        if (applyLimitLocation == null || location == null || applyLocationLimitRadius == null) {
            log.info("申请单填写地点有误，无法计算距离");
            return false;
        }
        double des = LocationUtils.getDistance(applyLimitLocation.getLat(), applyLimitLocation.getLng(),
                location.getLat(), location.getLng());
        log.info("申请单填写地点，计算距离：des={}", des);
        //符合距离
        return des <= applyLocationLimitRadius;
    }

    /**
     * 地点限制检查  （自定义地点，城市，机场，火车站）
     * 单独校验出发地/目的地
     */
    private boolean taxiPathCheck(Integer type, TaxiModifyDestinationCheckReqContract.Location location,
        List<String> cityCodes, EmployeeTaxiRuleInfo.LimitLocation limitLocation, double homeRadius,
        TaxiAvailableReqContract.HomeLocation homeLocation, List<TaxiLocation> workLocationList) {
        log.info("地点限制检查type={}, location={}, cityCodes={},limitLocation={}", type, JsonUtils.toJson(location),
            JsonUtils.toJson(cityCodes), JsonUtils.toJson(limitLocation));
        if (type == null) {
            return false;
        }
        if (type.equals(PathLocationTypeEnum.NORMAL.getCode())) {      //自选位置
            if (limitLocation == null) {
                log.info("规则信息错误，无法校验 type-1 location-null");
                return false;
            }
            if (limitLocation.getLocationType() == TaxiLocationType.Location.getCode()) {       //固定点距离
                Double radius = limitLocation.getRadius();
                Double des = LocationUtils.getDistance(limitLocation.getLat(), limitLocation.getLng(), location.getLat(), location.getLng());
                log.info("计算距离：{},{},radius={},des={}", location, limitLocation, radius, des);
                if (des > radius) {     //超出距离
                    return false;
                }
            } else {               //固定城市
                String limitCityCode = limitLocation.getCityCode();
                String cityCode = location.getCity_code();
                if (!limitCityCode.equals(cityCode)) {        //城市不一致，尝试补偿
                    getCityCodesByLocation(location.getLat(), location.getLng(), cityCodes);
                    if (cityCodes == null || !cityCodes.contains(limitCityCode)) {    //补偿失败，不符合规则
                        return false;
                    }
                }
            }
        } else if (type.equals(FixedPathLocationEnum.AIR_PORT.getCode())) {     //机场
            int radius = 3500;
            ContrailParam contrailParam = new ContrailParam();
            contrailParam.setLongitude(location.getLng().toString());
            contrailParam.setLatitude(location.getLat().toString());
            contrailParam.setDistance(radius);
            log.info("查询机场信息：contrailParam = {} ,radius = {}", JsonUtils.toJson(contrailParam), radius);
            RpcCommonResult<List<AirportDTO>> listRpcCommonResult = iAirportContrailSearchService.queryByLocationAndDistance(contrailParam);
            log.info("查询机场信息返回结果：listRpcCommonResult = {}", JsonUtils.toJson(listRpcCommonResult));
            if (CollectionUtils.isEmpty(listRpcCommonResult.getData())) {   //范围内无机场
                return false;
            }
        } else if (type.equals(FixedPathLocationEnum.TRAIN_STATION.getCode())) {       //火车站
            int radius = 1500;
            ContrailParam contrailParam = new ContrailParam();
            contrailParam.setLongitude(location.getLng().toString());
            contrailParam.setLatitude(location.getLat().toString());
            contrailParam.setDistance(radius);
            log.info("查询火车站信息：contrailParam = {} ,radius = {}", JsonUtils.toJson(contrailParam), radius);
            RpcCommonResult<List<TrainStationDTO>> listRpcCommonResult = iTrainStationContrailService.queryByLocationAndDistance(contrailParam);
            log.info("查询火车站信息返回结果：listRpcCommonResult = {}", JsonUtils.toJson(listRpcCommonResult));
            if (CollectionUtils.isEmpty(listRpcCommonResult.getData())) {   //范围内无火车站
                return false;
            }
        } else if (type.equals(FixedPathLocationEnum.HOME_ADDRESS.getCode())) {
            if (homeLocation != null && homeLocation.getLat() != null && homeLocation.getLng() != null) {
                double des = LocationUtils.getDistance(homeLocation.getLat(), homeLocation.getLng(),
                    location.getLat(), location.getLng());
                log.info("家庭地址，计算距离：{},{}, radius={},des={}", location, homeLocation, homeRadius, des);
                //符合距离
                return des <= homeRadius;
            } else {
                return false;
            }
        } else if (type.equals(FixedPathLocationEnum.WORK_LOCATION.getCode())) {
            if (CollectionUtils.isNotEmpty(workLocationList)) {
                //判断经纬度
                Double lat = location.getLat();         //0.1 度约11km
                Double maxLat = lat + 0.1d;
                Double minLat = lat - 0.1d;
                Double lng = location.getLng();         //0.14 度约11km
                Double maxLng = lng + 0.14d;
                Double minLng = lng - 0.14d;
                //自选地点
                List<TaxiLocation> list = workLocationList.stream()
                    .filter(x -> x.getRadius() != null && x.getLat() > minLat
                        && x.getLat() < maxLat && x.getLng() > minLng && x.getLng() < maxLng)
                    .collect(Collectors.toList());
                for (TaxiLocation taxiLocation : list) {
                    Double radius = taxiLocation.getRadius();
                    Double des = LocationUtils.getDistance(taxiLocation.getLat(), taxiLocation.getLng(), location.getLat(), location.getLng());
                    log.info("办公地点，计算距离：{},{},radius={},des={}", location, limitLocation, radius, des);
                    if (des <= radius) {     //符合距离
                        return true;
                    }
                }
                return false;
            } else {
                return false;
            }
        }
        return true;
    }

    /**
     * 通过坐标获取城市编码（如机场附近）
     */
    private List<String> getCityCodesByLocation(Double lat, Double lng, List<String> defList) {
        if (!ObjUtils.isEmpty(defList)) {
            return defList;
        }
        if (defList == null) {
            defList = Lists.newArrayList();
        }
        List<String> codes = getCityCodesByLocation(lat, lng);
        if (!ObjUtils.isEmpty(codes)) {
            defList.addAll(codes);
        }
        return defList;
    }

    /**
     * 通过坐标获取城市编码（如机场附近）
     *
     * @param departure_lat
     * @param departure_lng
     * @return
     */
    private List<String> getCityCodesByLocation(Double departure_lat, Double departure_lng) {
        List<LocationCityContract> cityContractList = null;
        String cacheResult = (String) redisService.getValueOperations().get(RedisKeyConstant.LOCATION_CITY_LIST_REDIS_KEY);
        if (ObjUtils.isEmpty(cacheResult)) {
            cityContractList = CityUtils.getLocationCity();
            if (ObjUtils.isEmpty(cityContractList)) {
                return null;
            }
        } else {
            cityContractList = com.fenbeitong.common.utils.json.JsonUtils.parseJsonList(cacheResult, LocationCityContract.class);
        }
        for (LocationCityContract cityContract : cityContractList) {
            Double des = LocationUtils.getDistance(cityContract.getLatitude(), cityContract.getLongitude(), departure_lat, departure_lng);
            log.info("计算距离：" + JsonUtils.toJson(cityContract) + ",des=" + des);
            if (des <= cityContract.getRadius()) {
                log.info("返回城市：" + cityContract.getCityCodes());
                return Arrays.asList(cityContract.getCityCodes().split(","));
            }
        }
        return null;
    }


    public TaxiLimitPayTipContract priceLimitPayTips(EmployeeTaxiRuleInfo employeeTaxiRuleInfoContract,
        Boolean advancePayment, TaxiOrderCheckRule taxiOrderCheckRule, boolean personalBudgetPayTipsFlag) {
        logger.info("个人支付提示，规则：{}", JsonUtils.toJson(employeeTaxiRuleInfoContract));

        TaxiLimitPayTipContract taxiLimitPayTipContract = new TaxiLimitPayTipContract();
        taxiLimitPayTipContract.setTip_flag(false);
        taxiLimitPayTipContract.setTip(new TaxiLimitPayTipContract.Tip());
        // 无规则限制
        if (employeeTaxiRuleInfoContract == null || !employeeTaxiRuleInfoContract.getTaxiRuleFlag()) {
            return getPersonalBudgetPayTips(personalBudgetPayTipsFlag, taxiLimitPayTipContract);
        }

        if (Objects.equals(employeeTaxiRuleInfoContract.getIsOpenExceedConfig(), false)) {
            // 未开启个人支付
            if (!employeeTaxiRuleInfoContract.getPersonalPay()) {
                return getPersonalBudgetPayTips(personalBudgetPayTipsFlag, taxiLimitPayTipContract);
            }
        }

        //新版本用车费用限制类型为null，不能根据这个类型做提示
        return getTaxiLimitPayTipContractV2(employeeTaxiRuleInfoContract, advancePayment, taxiOrderCheckRule,
            personalBudgetPayTipsFlag, taxiLimitPayTipContract);
    }

    private TaxiLimitPayTipContract getPersonalBudgetPayTips(boolean personalBudgetPayTipsFlag,
        TaxiLimitPayTipContract taxiLimitPayTipContract) {
        if (personalBudgetPayTipsFlag) {
            TaxiLimitPayTipContract.Tip tip = new TaxiLimitPayTipContract.Tip();
            String content = taxiLimitPayTipContract.getTip().getContent();
            if (StringUtils.isEmpty(content)) {
                tip.setContent("根据企业规则，如果本次下单导致预算超额，超额部分需要您进行个人支付。");
            } else {
                tip.setContent(content + "\n" + "根据企业规则，如果本次下单导致预算超额，超额部分需要您进行个人支付。");
            }

            taxiLimitPayTipContract.setTip(tip);
            taxiLimitPayTipContract.setTip_flag(true);
        }
        return taxiLimitPayTipContract;
    }

    /**
     * https://wiki.fenbeijinfu.com/pages/viewpage.action?pageId=60014420
     * 提示超规个人付的规则新增逻辑：
     *
     * 1、单次限额+单日上限+申请单金额
     * 您的单次用车上限为30元，您的单日用车上限为1000元。申请单申请金额300元。超出部分将需要您进行个人支付
     *
     * 2、单次限额+单日日均
     * 您的单次用车上限为30元，您的单日日均金额为100元，当前可用金额200元。超出部分将需要您进行个人支付
     *
     * 3、单次限额+单日日均+申请单金额
     * a.不显示申请单单日日均金额，因申请单金额小于等于日均金额
     * b.您的单次用车上限为30元，申请单申请金额300元。超出部分将需要您进行个人支付
     *
     * 4、分城市单次限额+分城市单日限额+申请单金额
     * 当前城市您的单次用车上限为30元，您的单日用车上限为1000元。申请单申请金额300元。超出部分将需要您进行个人支付
     *
     * 5、单次限额+单日上限+分城市单次限额+分城市单日限额+申请单金额
     * a.不显示单次和单日上限，因为分城市金额会小于等于单次和单日配置
     * b.当前城市您的单次用车上限为30元，您的单日用车上限为1000元。申请单申请金额300元。超出部分将需要您进行个人支付
     * @return
     */
    private TaxiLimitPayTipContract getTaxiLimitPayTipContractV2(EmployeeTaxiRuleInfo employeeTaxiRuleInfoContract,
        Boolean advancePayment, TaxiOrderCheckRule taxiOrderCheckRule, boolean personalBudgetPayTipsFlag,
        TaxiLimitPayTipContract taxiLimitPayTipContract) {

        BigDecimal priceLimit = employeeTaxiRuleInfoContract.getPriceLimit();
        BigDecimal dayPriceLimit = employeeTaxiRuleInfoContract.getDayPriceLimit();
        BigDecimal applyPriceLimit = employeeTaxiRuleInfoContract.getApplyPriceLimit();
        StringBuilder msg = new StringBuilder();
        Boolean tipFlag = Boolean.FALSE;
        String title = "超额个人支付提示";

        log.info("getTaxiLimitPayTipContractV2 ");

        Boolean isOpenExceedConfig = employeeTaxiRuleInfoContract.getIsOpenExceedConfig();
        TaxiExceedConfigPo exceedConfigInfo = null;
        if (Objects.equals(isOpenExceedConfig, true)) {
            exceedConfigInfo = JsonUtils.toObj(employeeTaxiRuleInfoContract.getExceedConfigInfoString(), TaxiExceedConfigPo.class);
        }

        //查询总预估费用配置
        CustomFormTotalEstimatedOptionDTO estimatedOptionDTO = null;
        if(StringUtils.isNotBlank(taxiOrderCheckRule.getApplyId()) && StringUtils.isNotBlank(taxiOrderCheckRule.getFormId())){
            estimatedOptionDTO = iCustomFormService.queryTotalEstimatedOptionSnapshot(taxiOrderCheckRule.getApplyId(),
                    taxiOrderCheckRule.getFormId());
        }
        if(ObjectUtil.isNotNull(estimatedOptionDTO) && ObjectUtil.isNotNull(estimatedOptionDTO.getCheckTotalEstimatedFlag())
                && estimatedOptionDTO.getCheckTotalEstimatedFlag() == 1
                && ApplyType.CustomFromBeforehand.getValue() == taxiOrderCheckRule.getApplyType()
                && estimatedOptionDTO.getTotalEstimatedCheckSceneWithCompatibilityMultiTrip().stream().anyMatch(a->Objects.equals(a, BizType.Taxi.getCode()))){
            if(estimatedOptionDTO.getTotalEstimatedLimitType() == 3){
                estimatedOptionDTO.setTotalEstimatedLimitAmount(estimatedOptionDTO.getApplyBudgeInYuan());
            }

            if (isOpenExceedConfig && exceedConfigInfo != null) {
                Integer totalEstimatedCostConfig = exceedConfigInfo.getTotalEstimatedCostConfig();
                if (com.fenbeitong.common.utils.basis.Objects.equals(totalEstimatedCostConfig, TaxiExceedConfigEnum.EXCEED_PERSONAL_PAY.getCode())) {
                    tipFlag = Boolean.TRUE;
                    BigDecimal applyLeft = BigDecimalTool.getNotNegative(applyPriceLimit.subtract(employeeTaxiRuleInfoContract.getApplyPriceUsed()));
                    msg.append(String.format("您的申请单总额度上限为%s元，申请单可用额度为%s元，",
                        dayPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString(),
                        applyLeft.setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
                }
            } else {
                tipFlag = Boolean.TRUE;
                BigDecimal applyLeft = BigDecimalTool.getNotNegative(applyPriceLimit.subtract(employeeTaxiRuleInfoContract.getApplyPriceUsed()));
                msg.append(String.format("您的申请单总额度上限为%s元，申请单可用额度为%s元，",
                    dayPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString(),
                    applyLeft.setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
            }
        }else {
            BigDecimal cityPriceLimit = employeeTaxiRuleInfoContract.getCityPriceLimit();
            if (employeeTaxiRuleInfoContract.getCityPriceLimitFlag() && cityPriceLimit != null && cityPriceLimit.compareTo(BigDecimal.ZERO) >= 0) {
                if (Objects.equals(isOpenExceedConfig, true) && exceedConfigInfo != null) {
                    Integer applyCityGroupConfig = exceedConfigInfo.getApplyCityGroupConfig();
                    if (Objects.equals(applyCityGroupConfig, TaxiExceedConfigEnum.EXCEED_PERSONAL_PAY.getCode())) {
                        tipFlag = Boolean.TRUE;
                        msg.append(String.format("您在当前城市单次用车上限为%s元，", cityPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
                    } else {
                        // 城市不提示 看单次要不要提示
                        if (Objects.equals(isOpenExceedConfig, true) && exceedConfigInfo != null) {
                            Integer priceLimitConfig = exceedConfigInfo.getPriceLimitConfig();
                            if (Objects.equals(priceLimitConfig, TaxiExceedConfigEnum.EXCEED_PERSONAL_PAY.getCode())
                                    && priceLimit.compareTo(BigDecimal.ZERO) >= 0) {
                                tipFlag = Boolean.TRUE;
                                msg.append(String.format("您的单次用车上限为%s元，", priceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
                            }
                        }
                    }
                } else {
                    tipFlag = Boolean.TRUE;
                    msg.append(String.format("您在当前城市单次用车上限为%s元，", cityPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
                }
            }else if (employeeTaxiRuleInfoContract.getPriceLimitFlag() && priceLimit != null && priceLimit.compareTo(BigDecimal.ZERO) >= 0) {
                if (Objects.equals(isOpenExceedConfig, true) && exceedConfigInfo != null) {
                    Integer priceLimitConfig = exceedConfigInfo.getPriceLimitConfig();
                    if (Objects.equals(priceLimitConfig, TaxiExceedConfigEnum.EXCEED_PERSONAL_PAY.getCode())) {
                        tipFlag = Boolean.TRUE;
                        msg.append(String.format("您的单次用车上限为%s元，", priceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
                    }
                } else {
                    tipFlag = Boolean.TRUE;
                    msg.append(String.format("您的单次用车上限为%s元，", priceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
                }
            }

            BigDecimal cityDayPriceLimit = employeeTaxiRuleInfoContract.getCityDayPriceLimit();
            if (employeeTaxiRuleInfoContract.getCityDayPriceLimitFlag() && cityDayPriceLimit != null && cityDayPriceLimit.compareTo(BigDecimal.ZERO) >= 0) {
                if (Objects.equals(isOpenExceedConfig, true) && exceedConfigInfo != null) {
                    Integer applyCityGroupConfig = exceedConfigInfo.getApplyCityGroupConfig();
                    if (Objects.equals(applyCityGroupConfig, TaxiExceedConfigEnum.EXCEED_PERSONAL_PAY.getCode())) {
                        tipFlag = Boolean.TRUE;
                        msg.append(String.format("您在当前城市单日用车上限为%s元，", cityDayPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
                    }
                } else {
                    tipFlag = Boolean.TRUE;
                    msg.append(String.format("您在当前城市单日用车上限为%s元，", cityDayPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
                }
            }

            BigDecimal perDayPriceLimit = employeeTaxiRuleInfoContract.getPerDayPriceLimit();
            if (employeeTaxiRuleInfoContract.getPerDayPriceLimitFlag() && perDayPriceLimit != null && perDayPriceLimit.compareTo(BigDecimal.ZERO) >= 0) {
                if (Objects.equals(isOpenExceedConfig, true) && exceedConfigInfo != null) {
                    Integer dayPriceLimitConfig = exceedConfigInfo.getDayPriceLimitConfig();
                    if (Objects.equals(dayPriceLimitConfig, TaxiExceedConfigEnum.EXCEED_PERSONAL_PAY.getCode())) {
                        tipFlag = Boolean.TRUE;
                        msg.append(String.format("您的单日用车上限为%s元，", perDayPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
                    }
                } else {
                    tipFlag = Boolean.TRUE;
                    msg.append(String.format("您的单日用车上限为%s元，", perDayPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
                }
            }

            if (DayPriceLimitTypeEnum.AVR_DAY_LIMIT.getType().equals(employeeTaxiRuleInfoContract.getDayPriceLimitType())) {
                if (Objects.equals(isOpenExceedConfig, true) && exceedConfigInfo != null) {
                    Integer applyDayPriceLimitConfig = exceedConfigInfo.getApplyDayPriceLimitConfig();
                    if (Objects.equals(applyDayPriceLimitConfig, TaxiExceedConfigEnum.EXCEED_PERSONAL_PAY.getCode())) {
                        // 日均限额
                        tipFlag = Boolean.TRUE;
                        Date endTime = DateUtils.parse(employeeTaxiRuleInfoContract.getEndTime(), DateUtils.FORMAT_DATE_WITH_BAR);
                        Date startTime = DateUtils.parse(employeeTaxiRuleInfoContract.getStartTime(), DateUtils.FORMAT_DATE_WITH_BAR);
                        long days = DateUtil.between(startTime, endTime, DateUnit.DAY) + 1;
                        //日均限额的总额度
                        BigDecimal avgDayPriceLimit = employeeTaxiRuleInfoContract.getAvgDayPriceLimit();
                        BigDecimal avgTotalDayPriceLimit = avgDayPriceLimit.multiply(BigDecimal.valueOf(days));
                        BigDecimal applyLeft = BigDecimalTool.getNotNegative(avgTotalDayPriceLimit.subtract(employeeTaxiRuleInfoContract.getApplyPriceUsed()));
                        msg.append(String.format("您的单日日均用车总额度上限为%s元，申请单可用额度为%s元，",
                            avgDayPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString(),
                            applyLeft.setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
                    }
                } else {
                    // 日均限额
                    tipFlag = Boolean.TRUE;
                    Date endTime = DateUtils.parse(employeeTaxiRuleInfoContract.getEndTime(), DateUtils.FORMAT_DATE_WITH_BAR);
                    Date startTime = DateUtils.parse(employeeTaxiRuleInfoContract.getStartTime(), DateUtils.FORMAT_DATE_WITH_BAR);
                    long days = DateUtil.between(startTime, endTime, DateUnit.DAY) + 1;
                    //日均限额的总额度
                    BigDecimal avgDayPriceLimit = employeeTaxiRuleInfoContract.getAvgDayPriceLimit();
                    BigDecimal avgTotalDayPriceLimit = avgDayPriceLimit.multiply(BigDecimal.valueOf(days));
                    BigDecimal applyLeft = BigDecimalTool.getNotNegative(avgTotalDayPriceLimit.subtract(employeeTaxiRuleInfoContract.getApplyPriceUsed()));
                    msg.append(String.format("您的单日日均用车总额度上限为%s元，申请单可用额度为%s元，",
                        avgDayPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString(),
                        applyLeft.setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
                }
            } else if (dayPriceLimit.compareTo(BigDecimal.ZERO) > 0) {
                if(!employeeTaxiRuleInfoContract.getCityDayPriceLimitFlag()) {
                    if (Objects.equals(isOpenExceedConfig, true) && exceedConfigInfo != null) {
                        if (Objects.equals(exceedConfigInfo.getTaxiRuleType(), 1)) { // 用车
                            Integer dayPriceLimitConfig = exceedConfigInfo.getDayPriceLimitConfig();
                            if (Objects.equals(dayPriceLimitConfig, TaxiExceedConfigEnum.EXCEED_PERSONAL_PAY.getCode())) {
                                tipFlag = Boolean.TRUE;
                                msg.append(String.format("您的单日用车上限为%s元，", dayPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
                            }
                        } else if (Objects.equals(exceedConfigInfo.getTaxiRuleType(), 2)) { // 申请用车
                            Integer applyDayPriceLimitConfig = exceedConfigInfo.getApplyDayPriceLimitConfig();
                            if (Objects.equals(applyDayPriceLimitConfig, TaxiExceedConfigEnum.EXCEED_PERSONAL_PAY.getCode())) {
                                tipFlag = Boolean.TRUE;
                                msg.append(String.format("您的单日用车上限为%s元，", dayPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
                            }
                        }

                    } else {
                        tipFlag = Boolean.TRUE;
                        msg.append(String.format("您的单日用车上限为%s元，", dayPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
                    }
                } else {
                    // 如果城市开启了，并且超规措施不是个人支付则判断申请单单日用车是否个人支付
                    if (Objects.equals(isOpenExceedConfig, true) && exceedConfigInfo != null) {
                        Integer applyCityGroupConfig = exceedConfigInfo.getApplyCityGroupConfig();
                        if (!Objects.equals(applyCityGroupConfig, TaxiExceedConfigEnum.EXCEED_PERSONAL_PAY.getCode())) {
                            // 用车
                            if (Objects.equals(TaxiCategory.Taxi.getType(), employeeTaxiRuleInfoContract.getCategory())) {
                                Integer priceLimitConfig = exceedConfigInfo.getPriceLimitConfig();
                                if (Objects.equals(priceLimitConfig, TaxiExceedConfigEnum.EXCEED_PERSONAL_PAY.getCode())) {
                                    tipFlag = Boolean.TRUE;
                                    msg.append(String.format("您的单日用车上限为%s元，", dayPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
                                }
                            } else {
                                Integer applyDayPriceLimitConfig = exceedConfigInfo.getApplyDayPriceLimitConfig();
                                if (Objects.equals(applyDayPriceLimitConfig, TaxiExceedConfigEnum.EXCEED_PERSONAL_PAY.getCode())) {
                                    tipFlag = Boolean.TRUE;
                                    msg.append(String.format("您的单日用车上限为%s元，", dayPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
                                }
                            }
                        }
                    }
                }
            }

            if (applyPriceLimit != null && applyPriceLimit.compareTo(BigDecimal.ZERO) > 0) {
                if (Objects.equals(isOpenExceedConfig, true) && exceedConfigInfo != null) {
                    Integer applyTotalPriceConfig = exceedConfigInfo.getApplyTotalPriceConfig();
                    if (Objects.equals(applyTotalPriceConfig, TaxiExceedConfigEnum.EXCEED_PERSONAL_PAY.getCode())) {
                        tipFlag = Boolean.TRUE;
                        msg.append(String.format("申请单申请金额上限为%s元，", applyPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
                    }
                } else {
                    tipFlag = Boolean.TRUE;
                    msg.append(String.format("申请单申请金额上限为%s元，", applyPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
                }
            }

        }
        if(tipFlag) {
            if (advancePayment) {
                msg.append("超出部分需您个人自费无法报销，是否继续提交？");
            } else {
                msg.append("超出部分将需要您进行个人支付。");
            }
            TaxiLimitPayTipContract.Tip tip = new TaxiLimitPayTipContract.Tip();
            tip.setTitle(title);
            tip.setContent(msg.toString());
            taxiLimitPayTipContract.setTip(tip);
            taxiLimitPayTipContract.setTip_flag(tipFlag);
        }
        if (personalBudgetPayTipsFlag) {
            taxiLimitPayTipContract = getPersonalBudgetPayTips(personalBudgetPayTipsFlag, taxiLimitPayTipContract);
        }
        return taxiLimitPayTipContract;
    }

    public PriceLimitPayTips budgetPersonPayTips(EmployeeTaxiRuleInfo employeeTaxiRuleInfo,
                                                 String budgetPersonPayLimitPriceMsg, Boolean advancePayment) {
        StringBuffer items = new StringBuffer();
        int index = 1;
        items.append(index++).append(".").append(budgetPersonPayLimitPriceMsg).append("；");
        BigDecimal priceLimit = employeeTaxiRuleInfo.getPriceLimit();
        BigDecimal dayPriceLimit = employeeTaxiRuleInfo.getDayPriceLimit();
        BigDecimal applyPriceLimit = employeeTaxiRuleInfo.getApplyPriceLimit();

        //员工填写
        if (employeeTaxiRuleInfo.getPriceLimitType() == 2 && applyPriceLimit != null && applyPriceLimit.compareTo(BigDecimal.ZERO) > 0) {
            items.append("\n").append(index++).append(".申请用车总额上限").append(applyPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()).append("元；");
        } else {
            if (employeeTaxiRuleInfo.getPriceLimitFlag() && priceLimit != null && priceLimit.compareTo(BigDecimal.ZERO) >= 0) {
                items.append("\n").append(index++).append(".单次用车上限").append(priceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()).append("元；");
            }
            if (DayPriceLimitTypeEnum.AVR_DAY_LIMIT.getType().equals(employeeTaxiRuleInfo.getDayPriceLimitType())) {
                // 日均限额
                Date endTime = DateUtils.parse(employeeTaxiRuleInfo.getEndTime(), DateUtils.FORMAT_DATE_WITH_BAR);
                Date startTime = DateUtils.parse(employeeTaxiRuleInfo.getStartTime(), DateUtils.FORMAT_DATE_WITH_BAR);
                long days = DateUtil.between(startTime, endTime, DateUnit.DAY) + 1;
                //日均限额的总额度
                applyPriceLimit = employeeTaxiRuleInfo.getDayPriceLimit().multiply(BigDecimal.valueOf(days));
                BigDecimal applyLeft = BigDecimalTool.getNotNegative(applyPriceLimit.subtract(employeeTaxiRuleInfo.getApplyPriceUsed()));
                items.append("\n")
                        .append(index++).append(".单日日均用车上限")
                        .append(dayPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()).append("元，")
                        .append("申请单可用额度")
                        .append(applyLeft.setScale(2, BigDecimal.ROUND_HALF_UP).toString()).append("元；");
            } else if(DayPriceLimitTypeEnum.PER_DAY_LIMIT.getType().equals(employeeTaxiRuleInfo.getDayPriceLimitType())) {
                items.append("\n").append(index++).append(".单日用车上限").append(dayPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()).append("元；");
            } else {
                if (dayPriceLimit != null && dayPriceLimit.compareTo(BigDecimal.ZERO) > 0) {
                    items.append("\n").append(index++).append(".单日用车上限").append(dayPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()).append("元；");
                }
            }
        }
        TemporaryResponseCode tip;
        String title;
        if (advancePayment) {
            tip = TemporaryResponseCode.OrderTaxiBudgetAndRulePriceLimitTip2;
            title = "超额部分需个人自费";
        } else {
            tip = TemporaryResponseCode.OrderTaxiBudgetAndRulePriceLimitTip;
            title = "超额部分需个人支付";
        }

        return PriceLimitPayTips.builder().code(tip.getCode()).title(title)
                .msg(StrUtils.formatString(tip.getMsg(), items)).build();
    }
}
