package com.fenbeitong.saas.core.model.fenbeitong;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class HotelRuleExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table hotel_rule
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table hotel_rule
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table hotel_rule
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_rule
     *
     * @mbg.generated
     */
    public HotelRuleExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_rule
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_rule
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_rule
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_rule
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_rule
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_rule
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_rule
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_rule
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_rule
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_rule
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table hotel_rule
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(String value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(String value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(String value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(String value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(String value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(String value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLike(String value) {
            addCriterion("company_id like", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotLike(String value) {
            addCriterion("company_id not like", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<String> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<String> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(String value1, String value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(String value1, String value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andHotelPriceFlagIsNull() {
            addCriterion("hotel_price_flag is null");
            return (Criteria) this;
        }

        public Criteria andHotelPriceFlagIsNotNull() {
            addCriterion("hotel_price_flag is not null");
            return (Criteria) this;
        }

        public Criteria andHotelPriceFlagEqualTo(Boolean value) {
            addCriterion("hotel_price_flag =", value, "hotelPriceFlag");
            return (Criteria) this;
        }

        public Criteria andHotelPriceFlagNotEqualTo(Boolean value) {
            addCriterion("hotel_price_flag <>", value, "hotelPriceFlag");
            return (Criteria) this;
        }

        public Criteria andHotelPriceFlagGreaterThan(Boolean value) {
            addCriterion("hotel_price_flag >", value, "hotelPriceFlag");
            return (Criteria) this;
        }

        public Criteria andHotelPriceFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("hotel_price_flag >=", value, "hotelPriceFlag");
            return (Criteria) this;
        }

        public Criteria andHotelPriceFlagLessThan(Boolean value) {
            addCriterion("hotel_price_flag <", value, "hotelPriceFlag");
            return (Criteria) this;
        }

        public Criteria andHotelPriceFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("hotel_price_flag <=", value, "hotelPriceFlag");
            return (Criteria) this;
        }

        public Criteria andHotelPriceFlagIn(List<Boolean> values) {
            addCriterion("hotel_price_flag in", values, "hotelPriceFlag");
            return (Criteria) this;
        }

        public Criteria andHotelPriceFlagNotIn(List<Boolean> values) {
            addCriterion("hotel_price_flag not in", values, "hotelPriceFlag");
            return (Criteria) this;
        }

        public Criteria andHotelPriceFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("hotel_price_flag between", value1, value2, "hotelPriceFlag");
            return (Criteria) this;
        }

        public Criteria andHotelPriceFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("hotel_price_flag not between", value1, value2, "hotelPriceFlag");
            return (Criteria) this;
        }

        public Criteria andHotelUnitPriceIsNull() {
            addCriterion("hotel_unit_price is null");
            return (Criteria) this;
        }

        public Criteria andHotelUnitPriceIsNotNull() {
            addCriterion("hotel_unit_price is not null");
            return (Criteria) this;
        }

        public Criteria andHotelUnitPriceEqualTo(BigDecimal value) {
            addCriterion("hotel_unit_price =", value, "hotelUnitPrice");
            return (Criteria) this;
        }

        public Criteria andHotelUnitPriceNotEqualTo(BigDecimal value) {
            addCriterion("hotel_unit_price <>", value, "hotelUnitPrice");
            return (Criteria) this;
        }

        public Criteria andHotelUnitPriceGreaterThan(BigDecimal value) {
            addCriterion("hotel_unit_price >", value, "hotelUnitPrice");
            return (Criteria) this;
        }

        public Criteria andHotelUnitPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("hotel_unit_price >=", value, "hotelUnitPrice");
            return (Criteria) this;
        }

        public Criteria andHotelUnitPriceLessThan(BigDecimal value) {
            addCriterion("hotel_unit_price <", value, "hotelUnitPrice");
            return (Criteria) this;
        }

        public Criteria andHotelUnitPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("hotel_unit_price <=", value, "hotelUnitPrice");
            return (Criteria) this;
        }

        public Criteria andHotelUnitPriceIn(List<BigDecimal> values) {
            addCriterion("hotel_unit_price in", values, "hotelUnitPrice");
            return (Criteria) this;
        }

        public Criteria andHotelUnitPriceNotIn(List<BigDecimal> values) {
            addCriterion("hotel_unit_price not in", values, "hotelUnitPrice");
            return (Criteria) this;
        }

        public Criteria andHotelUnitPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("hotel_unit_price between", value1, value2, "hotelUnitPrice");
            return (Criteria) this;
        }

        public Criteria andHotelUnitPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("hotel_unit_price not between", value1, value2, "hotelUnitPrice");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andFirstTierFlagIsNull() {
            addCriterion("first_tier_flag is null");
            return (Criteria) this;
        }

        public Criteria andFirstTierFlagIsNotNull() {
            addCriterion("first_tier_flag is not null");
            return (Criteria) this;
        }

        public Criteria andFirstTierFlagEqualTo(Boolean value) {
            addCriterion("first_tier_flag =", value, "firstTierFlag");
            return (Criteria) this;
        }

        public Criteria andFirstTierFlagNotEqualTo(Boolean value) {
            addCriterion("first_tier_flag <>", value, "firstTierFlag");
            return (Criteria) this;
        }

        public Criteria andFirstTierFlagGreaterThan(Boolean value) {
            addCriterion("first_tier_flag >", value, "firstTierFlag");
            return (Criteria) this;
        }

        public Criteria andFirstTierFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("first_tier_flag >=", value, "firstTierFlag");
            return (Criteria) this;
        }

        public Criteria andFirstTierFlagLessThan(Boolean value) {
            addCriterion("first_tier_flag <", value, "firstTierFlag");
            return (Criteria) this;
        }

        public Criteria andFirstTierFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("first_tier_flag <=", value, "firstTierFlag");
            return (Criteria) this;
        }

        public Criteria andFirstTierFlagIn(List<Boolean> values) {
            addCriterion("first_tier_flag in", values, "firstTierFlag");
            return (Criteria) this;
        }

        public Criteria andFirstTierFlagNotIn(List<Boolean> values) {
            addCriterion("first_tier_flag not in", values, "firstTierFlag");
            return (Criteria) this;
        }

        public Criteria andFirstTierFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("first_tier_flag between", value1, value2, "firstTierFlag");
            return (Criteria) this;
        }

        public Criteria andFirstTierFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("first_tier_flag not between", value1, value2, "firstTierFlag");
            return (Criteria) this;
        }

        public Criteria andFirstTierPriceIsNull() {
            addCriterion("first_tier_price is null");
            return (Criteria) this;
        }

        public Criteria andFirstTierPriceIsNotNull() {
            addCriterion("first_tier_price is not null");
            return (Criteria) this;
        }

        public Criteria andFirstTierPriceEqualTo(BigDecimal value) {
            addCriterion("first_tier_price =", value, "firstTierPrice");
            return (Criteria) this;
        }

        public Criteria andFirstTierPriceNotEqualTo(BigDecimal value) {
            addCriterion("first_tier_price <>", value, "firstTierPrice");
            return (Criteria) this;
        }

        public Criteria andFirstTierPriceGreaterThan(BigDecimal value) {
            addCriterion("first_tier_price >", value, "firstTierPrice");
            return (Criteria) this;
        }

        public Criteria andFirstTierPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("first_tier_price >=", value, "firstTierPrice");
            return (Criteria) this;
        }

        public Criteria andFirstTierPriceLessThan(BigDecimal value) {
            addCriterion("first_tier_price <", value, "firstTierPrice");
            return (Criteria) this;
        }

        public Criteria andFirstTierPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("first_tier_price <=", value, "firstTierPrice");
            return (Criteria) this;
        }

        public Criteria andFirstTierPriceIn(List<BigDecimal> values) {
            addCriterion("first_tier_price in", values, "firstTierPrice");
            return (Criteria) this;
        }

        public Criteria andFirstTierPriceNotIn(List<BigDecimal> values) {
            addCriterion("first_tier_price not in", values, "firstTierPrice");
            return (Criteria) this;
        }

        public Criteria andFirstTierPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("first_tier_price between", value1, value2, "firstTierPrice");
            return (Criteria) this;
        }

        public Criteria andFirstTierPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("first_tier_price not between", value1, value2, "firstTierPrice");
            return (Criteria) this;
        }

        public Criteria andSecondTierFlagIsNull() {
            addCriterion("second_tier_flag is null");
            return (Criteria) this;
        }

        public Criteria andSecondTierFlagIsNotNull() {
            addCriterion("second_tier_flag is not null");
            return (Criteria) this;
        }

        public Criteria andSecondTierFlagEqualTo(Boolean value) {
            addCriterion("second_tier_flag =", value, "secondTierFlag");
            return (Criteria) this;
        }

        public Criteria andSecondTierFlagNotEqualTo(Boolean value) {
            addCriterion("second_tier_flag <>", value, "secondTierFlag");
            return (Criteria) this;
        }

        public Criteria andSecondTierFlagGreaterThan(Boolean value) {
            addCriterion("second_tier_flag >", value, "secondTierFlag");
            return (Criteria) this;
        }

        public Criteria andSecondTierFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("second_tier_flag >=", value, "secondTierFlag");
            return (Criteria) this;
        }

        public Criteria andSecondTierFlagLessThan(Boolean value) {
            addCriterion("second_tier_flag <", value, "secondTierFlag");
            return (Criteria) this;
        }

        public Criteria andSecondTierFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("second_tier_flag <=", value, "secondTierFlag");
            return (Criteria) this;
        }

        public Criteria andSecondTierFlagIn(List<Boolean> values) {
            addCriterion("second_tier_flag in", values, "secondTierFlag");
            return (Criteria) this;
        }

        public Criteria andSecondTierFlagNotIn(List<Boolean> values) {
            addCriterion("second_tier_flag not in", values, "secondTierFlag");
            return (Criteria) this;
        }

        public Criteria andSecondTierFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("second_tier_flag between", value1, value2, "secondTierFlag");
            return (Criteria) this;
        }

        public Criteria andSecondTierFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("second_tier_flag not between", value1, value2, "secondTierFlag");
            return (Criteria) this;
        }

        public Criteria andSecondTierPriceIsNull() {
            addCriterion("second_tier_price is null");
            return (Criteria) this;
        }

        public Criteria andSecondTierPriceIsNotNull() {
            addCriterion("second_tier_price is not null");
            return (Criteria) this;
        }

        public Criteria andSecondTierPriceEqualTo(BigDecimal value) {
            addCriterion("second_tier_price =", value, "secondTierPrice");
            return (Criteria) this;
        }

        public Criteria andSecondTierPriceNotEqualTo(BigDecimal value) {
            addCriterion("second_tier_price <>", value, "secondTierPrice");
            return (Criteria) this;
        }

        public Criteria andSecondTierPriceGreaterThan(BigDecimal value) {
            addCriterion("second_tier_price >", value, "secondTierPrice");
            return (Criteria) this;
        }

        public Criteria andSecondTierPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("second_tier_price >=", value, "secondTierPrice");
            return (Criteria) this;
        }

        public Criteria andSecondTierPriceLessThan(BigDecimal value) {
            addCriterion("second_tier_price <", value, "secondTierPrice");
            return (Criteria) this;
        }

        public Criteria andSecondTierPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("second_tier_price <=", value, "secondTierPrice");
            return (Criteria) this;
        }

        public Criteria andSecondTierPriceIn(List<BigDecimal> values) {
            addCriterion("second_tier_price in", values, "secondTierPrice");
            return (Criteria) this;
        }

        public Criteria andSecondTierPriceNotIn(List<BigDecimal> values) {
            addCriterion("second_tier_price not in", values, "secondTierPrice");
            return (Criteria) this;
        }

        public Criteria andSecondTierPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("second_tier_price between", value1, value2, "secondTierPrice");
            return (Criteria) this;
        }

        public Criteria andSecondTierPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("second_tier_price not between", value1, value2, "secondTierPrice");
            return (Criteria) this;
        }

        public Criteria andOtherTierFlagIsNull() {
            addCriterion("other_tier_flag is null");
            return (Criteria) this;
        }

        public Criteria andOtherTierFlagIsNotNull() {
            addCriterion("other_tier_flag is not null");
            return (Criteria) this;
        }

        public Criteria andOtherTierFlagEqualTo(Boolean value) {
            addCriterion("other_tier_flag =", value, "otherTierFlag");
            return (Criteria) this;
        }

        public Criteria andOtherTierFlagNotEqualTo(Boolean value) {
            addCriterion("other_tier_flag <>", value, "otherTierFlag");
            return (Criteria) this;
        }

        public Criteria andOtherTierFlagGreaterThan(Boolean value) {
            addCriterion("other_tier_flag >", value, "otherTierFlag");
            return (Criteria) this;
        }

        public Criteria andOtherTierFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("other_tier_flag >=", value, "otherTierFlag");
            return (Criteria) this;
        }

        public Criteria andOtherTierFlagLessThan(Boolean value) {
            addCriterion("other_tier_flag <", value, "otherTierFlag");
            return (Criteria) this;
        }

        public Criteria andOtherTierFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("other_tier_flag <=", value, "otherTierFlag");
            return (Criteria) this;
        }

        public Criteria andOtherTierFlagIn(List<Boolean> values) {
            addCriterion("other_tier_flag in", values, "otherTierFlag");
            return (Criteria) this;
        }

        public Criteria andOtherTierFlagNotIn(List<Boolean> values) {
            addCriterion("other_tier_flag not in", values, "otherTierFlag");
            return (Criteria) this;
        }

        public Criteria andOtherTierFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("other_tier_flag between", value1, value2, "otherTierFlag");
            return (Criteria) this;
        }

        public Criteria andOtherTierFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("other_tier_flag not between", value1, value2, "otherTierFlag");
            return (Criteria) this;
        }

        public Criteria andOtherTierPriceIsNull() {
            addCriterion("other_tier_price is null");
            return (Criteria) this;
        }

        public Criteria andOtherTierPriceIsNotNull() {
            addCriterion("other_tier_price is not null");
            return (Criteria) this;
        }

        public Criteria andOtherTierPriceEqualTo(BigDecimal value) {
            addCriterion("other_tier_price =", value, "otherTierPrice");
            return (Criteria) this;
        }

        public Criteria andOtherTierPriceNotEqualTo(BigDecimal value) {
            addCriterion("other_tier_price <>", value, "otherTierPrice");
            return (Criteria) this;
        }

        public Criteria andOtherTierPriceGreaterThan(BigDecimal value) {
            addCriterion("other_tier_price >", value, "otherTierPrice");
            return (Criteria) this;
        }

        public Criteria andOtherTierPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("other_tier_price >=", value, "otherTierPrice");
            return (Criteria) this;
        }

        public Criteria andOtherTierPriceLessThan(BigDecimal value) {
            addCriterion("other_tier_price <", value, "otherTierPrice");
            return (Criteria) this;
        }

        public Criteria andOtherTierPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("other_tier_price <=", value, "otherTierPrice");
            return (Criteria) this;
        }

        public Criteria andOtherTierPriceIn(List<BigDecimal> values) {
            addCriterion("other_tier_price in", values, "otherTierPrice");
            return (Criteria) this;
        }

        public Criteria andOtherTierPriceNotIn(List<BigDecimal> values) {
            addCriterion("other_tier_price not in", values, "otherTierPrice");
            return (Criteria) this;
        }

        public Criteria andOtherTierPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("other_tier_price between", value1, value2, "otherTierPrice");
            return (Criteria) this;
        }

        public Criteria andOtherTierPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("other_tier_price not between", value1, value2, "otherTierPrice");
            return (Criteria) this;
        }

        public Criteria andLevelIsNull() {
            addCriterion("level is null");
            return (Criteria) this;
        }

        public Criteria andLevelIsNotNull() {
            addCriterion("level is not null");
            return (Criteria) this;
        }

        public Criteria andLevelEqualTo(String value) {
            addCriterion("level =", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelNotEqualTo(String value) {
            addCriterion("level <>", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelGreaterThan(String value) {
            addCriterion("level >", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelGreaterThanOrEqualTo(String value) {
            addCriterion("level >=", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelLessThan(String value) {
            addCriterion("level <", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelLessThanOrEqualTo(String value) {
            addCriterion("level <=", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelLike(String value) {
            addCriterion("level like", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelNotLike(String value) {
            addCriterion("level not like", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelIn(List<String> values) {
            addCriterion("level in", values, "level");
            return (Criteria) this;
        }

        public Criteria andLevelNotIn(List<String> values) {
            addCriterion("level not in", values, "level");
            return (Criteria) this;
        }

        public Criteria andLevelBetween(String value1, String value2) {
            addCriterion("level between", value1, value2, "level");
            return (Criteria) this;
        }

        public Criteria andLevelNotBetween(String value1, String value2) {
            addCriterion("level not between", value1, value2, "level");
            return (Criteria) this;
        }

        public Criteria andLevelFlagIsNull() {
            addCriterion("level_flag is null");
            return (Criteria) this;
        }

        public Criteria andLevelFlagIsNotNull() {
            addCriterion("level_flag is not null");
            return (Criteria) this;
        }

        public Criteria andLevelFlagEqualTo(Boolean value) {
            addCriterion("level_flag =", value, "levelFlag");
            return (Criteria) this;
        }

        public Criteria andLevelFlagNotEqualTo(Boolean value) {
            addCriterion("level_flag <>", value, "levelFlag");
            return (Criteria) this;
        }

        public Criteria andLevelFlagGreaterThan(Boolean value) {
            addCriterion("level_flag >", value, "levelFlag");
            return (Criteria) this;
        }

        public Criteria andLevelFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("level_flag >=", value, "levelFlag");
            return (Criteria) this;
        }

        public Criteria andLevelFlagLessThan(Boolean value) {
            addCriterion("level_flag <", value, "levelFlag");
            return (Criteria) this;
        }

        public Criteria andLevelFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("level_flag <=", value, "levelFlag");
            return (Criteria) this;
        }

        public Criteria andLevelFlagIn(List<Boolean> values) {
            addCriterion("level_flag in", values, "levelFlag");
            return (Criteria) this;
        }

        public Criteria andLevelFlagNotIn(List<Boolean> values) {
            addCriterion("level_flag not in", values, "levelFlag");
            return (Criteria) this;
        }

        public Criteria andLevelFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("level_flag between", value1, value2, "levelFlag");
            return (Criteria) this;
        }

        public Criteria andLevelFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("level_flag not between", value1, value2, "levelFlag");
            return (Criteria) this;
        }

        public Criteria andPrivDayMinIsNull() {
            addCriterion("priv_day_min is null");
            return (Criteria) this;
        }

        public Criteria andPrivDayMinIsNotNull() {
            addCriterion("priv_day_min is not null");
            return (Criteria) this;
        }

        public Criteria andPrivDayMinEqualTo(Integer value) {
            addCriterion("priv_day_min =", value, "privDayMin");
            return (Criteria) this;
        }

        public Criteria andPrivDayMinNotEqualTo(Integer value) {
            addCriterion("priv_day_min <>", value, "privDayMin");
            return (Criteria) this;
        }

        public Criteria andPrivDayMinGreaterThan(Integer value) {
            addCriterion("priv_day_min >", value, "privDayMin");
            return (Criteria) this;
        }

        public Criteria andPrivDayMinGreaterThanOrEqualTo(Integer value) {
            addCriterion("priv_day_min >=", value, "privDayMin");
            return (Criteria) this;
        }

        public Criteria andPrivDayMinLessThan(Integer value) {
            addCriterion("priv_day_min <", value, "privDayMin");
            return (Criteria) this;
        }

        public Criteria andPrivDayMinLessThanOrEqualTo(Integer value) {
            addCriterion("priv_day_min <=", value, "privDayMin");
            return (Criteria) this;
        }

        public Criteria andPrivDayMinIn(List<Integer> values) {
            addCriterion("priv_day_min in", values, "privDayMin");
            return (Criteria) this;
        }

        public Criteria andPrivDayMinNotIn(List<Integer> values) {
            addCriterion("priv_day_min not in", values, "privDayMin");
            return (Criteria) this;
        }

        public Criteria andPrivDayMinBetween(Integer value1, Integer value2) {
            addCriterion("priv_day_min between", value1, value2, "privDayMin");
            return (Criteria) this;
        }

        public Criteria andPrivDayMinNotBetween(Integer value1, Integer value2) {
            addCriterion("priv_day_min not between", value1, value2, "privDayMin");
            return (Criteria) this;
        }

        public Criteria andPrivDayMaxIsNull() {
            addCriterion("priv_day_max is null");
            return (Criteria) this;
        }

        public Criteria andPrivDayMaxIsNotNull() {
            addCriterion("priv_day_max is not null");
            return (Criteria) this;
        }

        public Criteria andPrivDayMaxEqualTo(Integer value) {
            addCriterion("priv_day_max =", value, "privDayMax");
            return (Criteria) this;
        }

        public Criteria andPrivDayMaxNotEqualTo(Integer value) {
            addCriterion("priv_day_max <>", value, "privDayMax");
            return (Criteria) this;
        }

        public Criteria andPrivDayMaxGreaterThan(Integer value) {
            addCriterion("priv_day_max >", value, "privDayMax");
            return (Criteria) this;
        }

        public Criteria andPrivDayMaxGreaterThanOrEqualTo(Integer value) {
            addCriterion("priv_day_max >=", value, "privDayMax");
            return (Criteria) this;
        }

        public Criteria andPrivDayMaxLessThan(Integer value) {
            addCriterion("priv_day_max <", value, "privDayMax");
            return (Criteria) this;
        }

        public Criteria andPrivDayMaxLessThanOrEqualTo(Integer value) {
            addCriterion("priv_day_max <=", value, "privDayMax");
            return (Criteria) this;
        }

        public Criteria andPrivDayMaxIn(List<Integer> values) {
            addCriterion("priv_day_max in", values, "privDayMax");
            return (Criteria) this;
        }

        public Criteria andPrivDayMaxNotIn(List<Integer> values) {
            addCriterion("priv_day_max not in", values, "privDayMax");
            return (Criteria) this;
        }

        public Criteria andPrivDayMaxBetween(Integer value1, Integer value2) {
            addCriterion("priv_day_max between", value1, value2, "privDayMax");
            return (Criteria) this;
        }

        public Criteria andPrivDayMaxNotBetween(Integer value1, Integer value2) {
            addCriterion("priv_day_max not between", value1, value2, "privDayMax");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Integer value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Integer value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Integer value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Integer value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Integer value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Integer> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Integer> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Integer value1, Integer value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andGlobalPriceIsNull() {
            addCriterion("global_price is null");
            return (Criteria) this;
        }

        public Criteria andGlobalPriceIsNotNull() {
            addCriterion("global_price is not null");
            return (Criteria) this;
        }

        public Criteria andGlobalPriceEqualTo(BigDecimal value) {
            addCriterion("global_price =", value, "globalPrice");
            return (Criteria) this;
        }

        public Criteria andGlobalPriceNotEqualTo(BigDecimal value) {
            addCriterion("global_price <>", value, "globalPrice");
            return (Criteria) this;
        }

        public Criteria andGlobalPriceGreaterThan(BigDecimal value) {
            addCriterion("global_price >", value, "globalPrice");
            return (Criteria) this;
        }

        public Criteria andGlobalPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("global_price >=", value, "globalPrice");
            return (Criteria) this;
        }

        public Criteria andGlobalPriceLessThan(BigDecimal value) {
            addCriterion("global_price <", value, "globalPrice");
            return (Criteria) this;
        }

        public Criteria andGlobalPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("global_price <=", value, "globalPrice");
            return (Criteria) this;
        }

        public Criteria andGlobalPriceIn(List<BigDecimal> values) {
            addCriterion("global_price in", values, "globalPrice");
            return (Criteria) this;
        }

        public Criteria andGlobalPriceNotIn(List<BigDecimal> values) {
            addCriterion("global_price not in", values, "globalPrice");
            return (Criteria) this;
        }

        public Criteria andGlobalPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("global_price between", value1, value2, "globalPrice");
            return (Criteria) this;
        }

        public Criteria andGlobalPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("global_price not between", value1, value2, "globalPrice");
            return (Criteria) this;
        }

        public Criteria andRuleLimitIsNull() {
            addCriterion("rule_limit is null");
            return (Criteria) this;
        }

        public Criteria andRuleLimitIsNotNull() {
            addCriterion("rule_limit is not null");
            return (Criteria) this;
        }

        public Criteria andRuleLimitEqualTo(Boolean value) {
            addCriterion("rule_limit =", value, "ruleLimit");
            return (Criteria) this;
        }

        public Criteria andRuleLimitNotEqualTo(Boolean value) {
            addCriterion("rule_limit <>", value, "ruleLimit");
            return (Criteria) this;
        }

        public Criteria andRuleLimitGreaterThan(Boolean value) {
            addCriterion("rule_limit >", value, "ruleLimit");
            return (Criteria) this;
        }

        public Criteria andRuleLimitGreaterThanOrEqualTo(Boolean value) {
            addCriterion("rule_limit >=", value, "ruleLimit");
            return (Criteria) this;
        }

        public Criteria andRuleLimitLessThan(Boolean value) {
            addCriterion("rule_limit <", value, "ruleLimit");
            return (Criteria) this;
        }

        public Criteria andRuleLimitLessThanOrEqualTo(Boolean value) {
            addCriterion("rule_limit <=", value, "ruleLimit");
            return (Criteria) this;
        }

        public Criteria andRuleLimitIn(List<Boolean> values) {
            addCriterion("rule_limit in", values, "ruleLimit");
            return (Criteria) this;
        }

        public Criteria andRuleLimitNotIn(List<Boolean> values) {
            addCriterion("rule_limit not in", values, "ruleLimit");
            return (Criteria) this;
        }

        public Criteria andRuleLimitBetween(Boolean value1, Boolean value2) {
            addCriterion("rule_limit between", value1, value2, "ruleLimit");
            return (Criteria) this;
        }

        public Criteria andRuleLimitNotBetween(Boolean value1, Boolean value2) {
            addCriterion("rule_limit not between", value1, value2, "ruleLimit");
            return (Criteria) this;
        }

        public Criteria andBrandLimitIsNull() {
            addCriterion("brand_limit is null");
            return (Criteria) this;
        }

        public Criteria andBrandLimitIsNotNull() {
            addCriterion("brand_limit is not null");
            return (Criteria) this;
        }

        public Criteria andBrandLimitEqualTo(Integer value) {
            addCriterion("brand_limit =", value, "brandLimit");
            return (Criteria) this;
        }

        public Criteria andBrandLimitNotEqualTo(Integer value) {
            addCriterion("brand_limit <>", value, "brandLimit");
            return (Criteria) this;
        }

        public Criteria andBrandLimitGreaterThan(Integer value) {
            addCriterion("brand_limit >", value, "brandLimit");
            return (Criteria) this;
        }

        public Criteria andBrandLimitGreaterThanOrEqualTo(Integer value) {
            addCriterion("brand_limit >=", value, "brandLimit");
            return (Criteria) this;
        }

        public Criteria andBrandLimitLessThan(Integer value) {
            addCriterion("brand_limit <", value, "brandLimit");
            return (Criteria) this;
        }

        public Criteria andBrandLimitLessThanOrEqualTo(Integer value) {
            addCriterion("brand_limit <=", value, "brandLimit");
            return (Criteria) this;
        }

        public Criteria andBrandLimitIn(List<Integer> values) {
            addCriterion("brand_limit in", values, "brandLimit");
            return (Criteria) this;
        }

        public Criteria andBrandLimitNotIn(List<Integer> values) {
            addCriterion("brand_limit not in", values, "brandLimit");
            return (Criteria) this;
        }

        public Criteria andBrandLimitBetween(Integer value1, Integer value2) {
            addCriterion("brand_limit between", value1, value2, "brandLimit");
            return (Criteria) this;
        }

        public Criteria andBrandLimitNotBetween(Integer value1, Integer value2) {
            addCriterion("brand_limit not between", value1, value2, "brandLimit");
            return (Criteria) this;
        }

        public Criteria andBrandLimitTypeIsNull() {
            addCriterion("brand_limit_type is null");
            return (Criteria) this;
        }

        public Criteria andBrandLimitTypeIsNotNull() {
            addCriterion("brand_limit_type is not null");
            return (Criteria) this;
        }

        public Criteria andBrandLimitTypeEqualTo(Integer value) {
            addCriterion("brand_limit_type =", value, "brandLimitType");
            return (Criteria) this;
        }

        public Criteria andBrandLimitTypeNotEqualTo(Integer value) {
            addCriterion("brand_limit_type <>", value, "brandLimitType");
            return (Criteria) this;
        }

        public Criteria andBrandLimitTypeGreaterThan(Integer value) {
            addCriterion("brand_limit_type >", value, "brandLimitType");
            return (Criteria) this;
        }

        public Criteria andBrandLimitTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("brand_limit_type >=", value, "brandLimitType");
            return (Criteria) this;
        }

        public Criteria andBrandLimitTypeLessThan(Integer value) {
            addCriterion("brand_limit_type <", value, "brandLimitType");
            return (Criteria) this;
        }

        public Criteria andBrandLimitTypeLessThanOrEqualTo(Integer value) {
            addCriterion("brand_limit_type <=", value, "brandLimitType");
            return (Criteria) this;
        }

        public Criteria andBrandLimitTypeIn(List<Integer> values) {
            addCriterion("brand_limit_type in", values, "brandLimitType");
            return (Criteria) this;
        }

        public Criteria andBrandLimitTypeNotIn(List<Integer> values) {
            addCriterion("brand_limit_type not in", values, "brandLimitType");
            return (Criteria) this;
        }

        public Criteria andBrandLimitTypeBetween(Integer value1, Integer value2) {
            addCriterion("brand_limit_type between", value1, value2, "brandLimitType");
            return (Criteria) this;
        }

        public Criteria andBrandLimitTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("brand_limit_type not between", value1, value2, "brandLimitType");
            return (Criteria) this;
        }

        public Criteria andIsDingIsNull() {
            addCriterion("is_ding is null");
            return (Criteria) this;
        }

        public Criteria andIsDingIsNotNull() {
            addCriterion("is_ding is not null");
            return (Criteria) this;
        }

        public Criteria andIsDingEqualTo(Integer value) {
            addCriterion("is_ding =", value, "isDing");
            return (Criteria) this;
        }

        public Criteria andIsDingNotEqualTo(Integer value) {
            addCriterion("is_ding <>", value, "isDing");
            return (Criteria) this;
        }

        public Criteria andIsDingGreaterThan(Integer value) {
            addCriterion("is_ding >", value, "isDing");
            return (Criteria) this;
        }

        public Criteria andIsDingGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_ding >=", value, "isDing");
            return (Criteria) this;
        }

        public Criteria andIsDingLessThan(Integer value) {
            addCriterion("is_ding <", value, "isDing");
            return (Criteria) this;
        }

        public Criteria andIsDingLessThanOrEqualTo(Integer value) {
            addCriterion("is_ding <=", value, "isDing");
            return (Criteria) this;
        }

        public Criteria andIsDingIn(List<Integer> values) {
            addCriterion("is_ding in", values, "isDing");
            return (Criteria) this;
        }

        public Criteria andIsDingNotIn(List<Integer> values) {
            addCriterion("is_ding not in", values, "isDing");
            return (Criteria) this;
        }

        public Criteria andIsDingBetween(Integer value1, Integer value2) {
            addCriterion("is_ding between", value1, value2, "isDing");
            return (Criteria) this;
        }

        public Criteria andIsDingNotBetween(Integer value1, Integer value2) {
            addCriterion("is_ding not between", value1, value2, "isDing");
            return (Criteria) this;
        }

        public Criteria andMaxReverseDayLimitFlagIsNull() {
            addCriterion("max_reverse_day_limit_flag is null");
            return (Criteria) this;
        }

        public Criteria andMaxReverseDayLimitFlagIsNotNull() {
            addCriterion("max_reverse_day_limit_flag is not null");
            return (Criteria) this;
        }

        public Criteria andMaxReverseDayLimitFlagEqualTo(Boolean value) {
            addCriterion("max_reverse_day_limit_flag =", value, "maxReverseDayLimitFlag");
            return (Criteria) this;
        }

        public Criteria andMaxReverseDayLimitFlagNotEqualTo(Boolean value) {
            addCriterion("max_reverse_day_limit_flag <>", value, "maxReverseDayLimitFlag");
            return (Criteria) this;
        }

        public Criteria andMaxReverseDayLimitFlagGreaterThan(Boolean value) {
            addCriterion("max_reverse_day_limit_flag >", value, "maxReverseDayLimitFlag");
            return (Criteria) this;
        }

        public Criteria andMaxReverseDayLimitFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("max_reverse_day_limit_flag >=", value, "maxReverseDayLimitFlag");
            return (Criteria) this;
        }

        public Criteria andMaxReverseDayLimitFlagLessThan(Boolean value) {
            addCriterion("max_reverse_day_limit_flag <", value, "maxReverseDayLimitFlag");
            return (Criteria) this;
        }

        public Criteria andMaxReverseDayLimitFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("max_reverse_day_limit_flag <=", value, "maxReverseDayLimitFlag");
            return (Criteria) this;
        }

        public Criteria andMaxReverseDayLimitFlagIn(List<Boolean> values) {
            addCriterion("max_reverse_day_limit_flag in", values, "maxReverseDayLimitFlag");
            return (Criteria) this;
        }

        public Criteria andMaxReverseDayLimitFlagNotIn(List<Boolean> values) {
            addCriterion("max_reverse_day_limit_flag not in", values, "maxReverseDayLimitFlag");
            return (Criteria) this;
        }

        public Criteria andMaxReverseDayLimitFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("max_reverse_day_limit_flag between", value1, value2, "maxReverseDayLimitFlag");
            return (Criteria) this;
        }

        public Criteria andMaxReverseDayLimitFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("max_reverse_day_limit_flag not between", value1, value2, "maxReverseDayLimitFlag");
            return (Criteria) this;
        }

        public Criteria andMaxReverseDayLimitIsNull() {
            addCriterion("max_reverse_day_limit is null");
            return (Criteria) this;
        }

        public Criteria andMaxReverseDayLimitIsNotNull() {
            addCriterion("max_reverse_day_limit is not null");
            return (Criteria) this;
        }

        public Criteria andMaxReverseDayLimitEqualTo(Integer value) {
            addCriterion("max_reverse_day_limit =", value, "maxReverseDayLimit");
            return (Criteria) this;
        }

        public Criteria andMaxReverseDayLimitNotEqualTo(Integer value) {
            addCriterion("max_reverse_day_limit <>", value, "maxReverseDayLimit");
            return (Criteria) this;
        }

        public Criteria andMaxReverseDayLimitGreaterThan(Integer value) {
            addCriterion("max_reverse_day_limit >", value, "maxReverseDayLimit");
            return (Criteria) this;
        }

        public Criteria andMaxReverseDayLimitGreaterThanOrEqualTo(Integer value) {
            addCriterion("max_reverse_day_limit >=", value, "maxReverseDayLimit");
            return (Criteria) this;
        }

        public Criteria andMaxReverseDayLimitLessThan(Integer value) {
            addCriterion("max_reverse_day_limit <", value, "maxReverseDayLimit");
            return (Criteria) this;
        }

        public Criteria andMaxReverseDayLimitLessThanOrEqualTo(Integer value) {
            addCriterion("max_reverse_day_limit <=", value, "maxReverseDayLimit");
            return (Criteria) this;
        }

        public Criteria andMaxReverseDayLimitIn(List<Integer> values) {
            addCriterion("max_reverse_day_limit in", values, "maxReverseDayLimit");
            return (Criteria) this;
        }

        public Criteria andMaxReverseDayLimitNotIn(List<Integer> values) {
            addCriterion("max_reverse_day_limit not in", values, "maxReverseDayLimit");
            return (Criteria) this;
        }

        public Criteria andMaxReverseDayLimitBetween(Integer value1, Integer value2) {
            addCriterion("max_reverse_day_limit between", value1, value2, "maxReverseDayLimit");
            return (Criteria) this;
        }

        public Criteria andMaxReverseDayLimitNotBetween(Integer value1, Integer value2) {
            addCriterion("max_reverse_day_limit not between", value1, value2, "maxReverseDayLimit");
            return (Criteria) this;
        }

        public Criteria andEmployeeBaseLocationControlIsNull() {
            addCriterion("employee_base_location_control is null");
            return (Criteria) this;
        }

        public Criteria andEmployeeBaseLocationControlIsNotNull() {
            addCriterion("employee_base_location_control is not null");
            return (Criteria) this;
        }

        public Criteria andEmployeeBaseLocationControlEqualTo(Integer value) {
            addCriterion("employee_base_location_control =", value, "employeeBaseLocationControl");
            return (Criteria) this;
        }

        public Criteria andEmployeeBaseLocationControlNotEqualTo(Integer value) {
            addCriterion("employee_base_location_control <>", value, "employeeBaseLocationControl");
            return (Criteria) this;
        }

        public Criteria andEmployeeBaseLocationControlGreaterThan(Integer value) {
            addCriterion("employee_base_location_control >", value, "employeeBaseLocationControl");
            return (Criteria) this;
        }

        public Criteria andEmployeeBaseLocationControlGreaterThanOrEqualTo(Integer value) {
            addCriterion("employee_base_location_control >=", value, "employeeBaseLocationControl");
            return (Criteria) this;
        }

        public Criteria andEmployeeBaseLocationControlLessThan(Integer value) {
            addCriterion("employee_base_location_control <", value, "employeeBaseLocationControl");
            return (Criteria) this;
        }

        public Criteria andEmployeeBaseLocationControlLessThanOrEqualTo(Integer value) {
            addCriterion("employee_base_location_control <=", value, "employeeBaseLocationControl");
            return (Criteria) this;
        }

        public Criteria andEmployeeBaseLocationControlIn(List<Integer> values) {
            addCriterion("employee_base_location_control in", values, "employeeBaseLocationControl");
            return (Criteria) this;
        }

        public Criteria andEmployeeBaseLocationControlNotIn(List<Integer> values) {
            addCriterion("employee_base_location_control not in", values, "employeeBaseLocationControl");
            return (Criteria) this;
        }

        public Criteria andEmployeeBaseLocationControlBetween(Integer value1, Integer value2) {
            addCriterion("employee_base_location_control between", value1, value2, "employeeBaseLocationControl");
            return (Criteria) this;
        }

        public Criteria andEmployeeBaseLocationControlNotBetween(Integer value1, Integer value2) {
            addCriterion("employee_base_location_control not between", value1, value2, "employeeBaseLocationControl");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table hotel_rule
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table hotel_rule
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}