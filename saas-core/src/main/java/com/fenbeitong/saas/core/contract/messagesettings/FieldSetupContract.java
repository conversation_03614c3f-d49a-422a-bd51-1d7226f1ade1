package com.fenbeitong.saas.core.contract.messagesettings;


/**
 * Created by ma<PERSON><PERSON><PERSON> on 18/04/23.
 * 字段配置
 */
public class FieldSetupContract {

    /**
     * 事由字段 酒店
     */
    private Integer order_reason_hotel;
    /**
     * 事由字段 用餐
     */
    private Integer order_reason_dinner;
    /**
     * 事由字段 火车票
     */
    private Integer order_reason_train;
    /**
     * 事由字段 采购
     */
    private Integer order_reason_mall;
    /**
     * 事由字段 用车
     */
    private Integer order_reason_car;
    /**
     * 事由字段 国内机票
     */
    private Integer order_reason_air;
    /**
     * 事由字段 国际机票
     */
    private Integer order_reason_inter_air;
    /**
     * 事由字段 外卖
     */
    private Integer order_reason_takeaway;
    /**
     * 事由字段 闪送
     */
    private Integer order_reason_express;
    /**
     * 事由字段 顺丰
     */
    private Integer order_reason_sf_express;

    /**
     * 事由字段 补充内容
     */
    private Integer order_reason_additions;

    /**
     * 自定义字段1：启用场景 酒店
     */
    private Integer order_custom_attr1_hotel;
    /**
     * 自定义字段1：启用场景 用餐
     */
    private Integer order_custom_attr1_dinner;
    /**
     * 自定义字段1：启用场景 火车票
     */
    private Integer order_custom_attr1_train;
    /**
     * 自定义字段1：启用场景 采购
     */
    private Integer order_custom_attr1_mall;
    /**
     * 自定义字段1：启用场景 用车
     */
    private Integer order_custom_attr1_car;
    /**
     * 自定义字段1：启用场景 国内机票
     */
    private Integer order_custom_attr1_air;
    /**
     * 自定义字段1：启用场景 国际机票
     */
    private Integer order_custom_attr1_inter_air;
    /**
     * 自定义字段1：启用场景 外卖
     */
    private Integer order_custom_attr1_takeaway;
    /**
     * 自定义字段1：启用场景 闪送
     */
    private Integer order_custom_attr1_express;
    /**
     * 自定义字段1：启用场景 顺丰
     */
    private Integer order_custom_attr1_sf_express;
    /**
     * 自定义字段1：字段标题
     */
    private String order_custom_attr1_title;
    /**
     * 自定义字段1：字段类型 0文本-提示语 1多选框-备选项
     */
    private Integer order_custom_attr1_type;
    /**
     * 自定义字段1：提示语
     */
    private String order_custom_attr1_prompt;

    /**
     * 自定义字段1：备选项
     */
    private String[] order_custom_attr1_options;

    private Integer order_custom_attr2_hotel;
    private Integer order_custom_attr2_dinner;
    private Integer order_custom_attr2_train;
    private Integer order_custom_attr2_mall;
    private Integer order_custom_attr2_car;
    private Integer order_custom_attr2_air;
    private Integer order_custom_attr2_inter_air;
    private Integer order_custom_attr2_takeaway;
    private Integer order_custom_attr2_express;
    private Integer order_custom_attr2_sf_express;
    private String order_custom_attr2_title;
    private Integer order_custom_attr2_type;
    private String order_custom_attr2_prompt;
    private String[] order_custom_attr2_options;

    private Integer order_custom_attr3_hotel;
    private Integer order_custom_attr3_dinner;
    private Integer order_custom_attr3_train;
    private Integer order_custom_attr3_mall;
    private Integer order_custom_attr3_car;
    private Integer order_custom_attr3_air;
    private Integer order_custom_attr3_inter_air;
    private Integer order_custom_attr3_takeaway;
    private Integer order_custom_attr3_express;
    private Integer order_custom_attr3_sf_express;
    private String order_custom_attr3_title;
    private Integer order_custom_attr3_type;
    private String order_custom_attr3_prompt;
    private String[] order_custom_attr3_options;

    private Integer order_reason_exceed_desc;

    public Integer getOrder_reason_hotel() {
        return order_reason_hotel;
    }

    public void setOrder_reason_hotel(Integer order_reason_hotel) {
        this.order_reason_hotel = order_reason_hotel;
    }

    public Integer getOrder_reason_dinner() {
        return order_reason_dinner;
    }

    public void setOrder_reason_dinner(Integer order_reason_dinner) {
        this.order_reason_dinner = order_reason_dinner;
    }

    public Integer getOrder_reason_train() {
        return order_reason_train;
    }

    public void setOrder_reason_train(Integer order_reason_train) {
        this.order_reason_train = order_reason_train;
    }

    public Integer getOrder_reason_mall() {
        return order_reason_mall;
    }

    public void setOrder_reason_mall(Integer order_reason_mall) {
        this.order_reason_mall = order_reason_mall;
    }

    public Integer getOrder_reason_car() {
        return order_reason_car;
    }

    public void setOrder_reason_car(Integer order_reason_car) {
        this.order_reason_car = order_reason_car;
    }

    public Integer getOrder_custom_attr1_hotel() { return order_custom_attr1_hotel; }

    public void setOrder_custom_attr1_hotel(Integer order_custom_attr1_hotel) { this.order_custom_attr1_hotel = order_custom_attr1_hotel; }

    public Integer getOrder_custom_attr1_dinner() { return order_custom_attr1_dinner; }

    public void setOrder_custom_attr1_dinner(Integer order_custom_attr1_dinner) { this.order_custom_attr1_dinner = order_custom_attr1_dinner; }

    public Integer getOrder_custom_attr1_train() { return order_custom_attr1_train; }

    public void setOrder_custom_attr1_train(Integer order_custom_attr1_train) { this.order_custom_attr1_train = order_custom_attr1_train; }

    public Integer getOrder_custom_attr1_mall() { return order_custom_attr1_mall; }

    public void setOrder_custom_attr1_mall(Integer order_custom_attr1_mall) { this.order_custom_attr1_mall = order_custom_attr1_mall; }

    public Integer getOrder_custom_attr1_car() { return order_custom_attr1_car; }

    public void setOrder_custom_attr1_car(Integer order_custom_attr1_car) { this.order_custom_attr1_car = order_custom_attr1_car; }

    public Integer getOrder_custom_attr2_hotel() { return order_custom_attr2_hotel; }

    public void setOrder_custom_attr2_hotel(Integer order_custom_attr2_hotel) { this.order_custom_attr2_hotel = order_custom_attr2_hotel; }

    public Integer getOrder_custom_attr2_dinner() { return order_custom_attr2_dinner; }

    public void setOrder_custom_attr2_dinner(Integer order_custom_attr2_dinner) { this.order_custom_attr2_dinner = order_custom_attr2_dinner; }

    public Integer getOrder_custom_attr2_train() { return order_custom_attr2_train; }

    public void setOrder_custom_attr2_train(Integer order_custom_attr2_train) { this.order_custom_attr2_train = order_custom_attr2_train; }

    public Integer getOrder_custom_attr2_mall() { return order_custom_attr2_mall; }

    public void setOrder_custom_attr2_mall(Integer order_custom_attr2_mall) { this.order_custom_attr2_mall = order_custom_attr2_mall; }

    public Integer getOrder_custom_attr2_car() { return order_custom_attr2_car; }

    public void setOrder_custom_attr2_car(Integer order_custom_attr2_car) { this.order_custom_attr2_car = order_custom_attr2_car; }

    public Integer getOrder_custom_attr3_hotel() { return order_custom_attr3_hotel; }

    public void setOrder_custom_attr3_hotel(Integer order_custom_attr3_hotel) { this.order_custom_attr3_hotel = order_custom_attr3_hotel; }

    public Integer getOrder_custom_attr3_dinner() { return order_custom_attr3_dinner; }

    public void setOrder_custom_attr3_dinner(Integer order_custom_attr3_dinner) { this.order_custom_attr3_dinner = order_custom_attr3_dinner; }

    public Integer getOrder_custom_attr3_train() { return order_custom_attr3_train; }

    public void setOrder_custom_attr3_train(Integer order_custom_attr3_train) { this.order_custom_attr3_train = order_custom_attr3_train; }

    public Integer getOrder_custom_attr3_mall() { return order_custom_attr3_mall; }

    public void setOrder_custom_attr3_mall(Integer order_custom_attr3_mall) { this.order_custom_attr3_mall = order_custom_attr3_mall; }

    public Integer getOrder_custom_attr3_car() { return order_custom_attr3_car; }

    public void setOrder_custom_attr3_car(Integer order_custom_attr3_car) { this.order_custom_attr3_car = order_custom_attr3_car; }

    public String getOrder_custom_attr1_title() { return order_custom_attr1_title; }

    public void setOrder_custom_attr1_title(String order_custom_attr1_title) { this.order_custom_attr1_title = order_custom_attr1_title; }

    public String getOrder_custom_attr1_prompt() { return order_custom_attr1_prompt; }

    public void setOrder_custom_attr1_prompt(String order_custom_attr1_prompt) { this.order_custom_attr1_prompt = order_custom_attr1_prompt; }

    public String getOrder_custom_attr2_title() { return order_custom_attr2_title; }

    public void setOrder_custom_attr2_title(String order_custom_attr2_title) { this.order_custom_attr2_title = order_custom_attr2_title; }

    public String getOrder_custom_attr2_prompt() { return order_custom_attr2_prompt; }

    public void setOrder_custom_attr2_prompt(String order_custom_attr2_prompt) { this.order_custom_attr2_prompt = order_custom_attr2_prompt; }

    public String getOrder_custom_attr3_title() { return order_custom_attr3_title; }

    public void setOrder_custom_attr3_title(String order_custom_attr3_title) { this.order_custom_attr3_title = order_custom_attr3_title; }

    public String getOrder_custom_attr3_prompt() { return order_custom_attr3_prompt; }

    public void setOrder_custom_attr3_prompt(String order_custom_attr3_prompt) { this.order_custom_attr3_prompt = order_custom_attr3_prompt; }

    public Integer getOrder_custom_attr1_type() { return order_custom_attr1_type; }

    public void setOrder_custom_attr1_type(Integer order_custom_attr1_type) { this.order_custom_attr1_type = order_custom_attr1_type; }

    public Integer getOrder_custom_attr2_type() { return order_custom_attr2_type; }

    public void setOrder_custom_attr2_type(Integer order_custom_attr2_type) { this.order_custom_attr2_type = order_custom_attr2_type; }

    public Integer getOrder_custom_attr3_type() { return order_custom_attr3_type; }

    public void setOrder_custom_attr3_type(Integer order_custom_attr3_type) { this.order_custom_attr3_type = order_custom_attr3_type; }

    public String[] getOrder_custom_attr1_options() { return order_custom_attr1_options; }

    public void setOrder_custom_attr1_options(String[] order_custom_attr1_options) { this.order_custom_attr1_options = order_custom_attr1_options; }

    public String[] getOrder_custom_attr2_options() { return order_custom_attr2_options; }

    public void setOrder_custom_attr2_options(String[] order_custom_attr2_options) { this.order_custom_attr2_options = order_custom_attr2_options; }

    public String[] getOrder_custom_attr3_options() { return order_custom_attr3_options; }

    public void setOrder_custom_attr3_options(String[] order_custom_attr3_options) { this.order_custom_attr3_options = order_custom_attr3_options; }

    public Integer getOrder_reason_additions() { return order_reason_additions; }

    public void setOrder_reason_additions(Integer order_reason_additions) { this.order_reason_additions = order_reason_additions; }

    public Integer getOrder_reason_air() {
        return order_reason_air;
    }

    public void setOrder_reason_air(Integer order_reason_air) {
        this.order_reason_air = order_reason_air;
    }

    public Integer getOrder_reason_inter_air() {
        return order_reason_inter_air;
    }

    public void setOrder_reason_inter_air(Integer order_reason_inter_air) {
        this.order_reason_inter_air = order_reason_inter_air;
    }

    public Integer getOrder_custom_attr1_air() {
        return order_custom_attr1_air;
    }

    public void setOrder_custom_attr1_air(Integer order_custom_attr1_air) {
        this.order_custom_attr1_air = order_custom_attr1_air;
    }

    public Integer getOrder_custom_attr1_inter_air() {
        return order_custom_attr1_inter_air;
    }

    public void setOrder_custom_attr1_inter_air(Integer order_custom_attr1_inter_air) {
        this.order_custom_attr1_inter_air = order_custom_attr1_inter_air;
    }

    public Integer getOrder_custom_attr2_air() {
        return order_custom_attr2_air;
    }

    public void setOrder_custom_attr2_air(Integer order_custom_attr2_air) {
        this.order_custom_attr2_air = order_custom_attr2_air;
    }

    public Integer getOrder_custom_attr2_inter_air() {
        return order_custom_attr2_inter_air;
    }

    public void setOrder_custom_attr2_inter_air(Integer order_custom_attr2_inter_air) {
        this.order_custom_attr2_inter_air = order_custom_attr2_inter_air;
    }

    public Integer getOrder_custom_attr3_air() {
        return order_custom_attr3_air;
    }

    public void setOrder_custom_attr3_air(Integer order_custom_attr3_air) {
        this.order_custom_attr3_air = order_custom_attr3_air;
    }

    public Integer getOrder_custom_attr3_inter_air() {
        return order_custom_attr3_inter_air;
    }

    public void setOrder_custom_attr3_inter_air(Integer order_custom_attr3_inter_air) {
        this.order_custom_attr3_inter_air = order_custom_attr3_inter_air;
    }

    public Integer getOrder_reason_takeaway() {
        return order_reason_takeaway;
    }

    public void setOrder_reason_takeaway(Integer order_reason_takeaway) {
        this.order_reason_takeaway = order_reason_takeaway;
    }

    public Integer getOrder_custom_attr1_takeaway() {
        return order_custom_attr1_takeaway;
    }

    public void setOrder_custom_attr1_takeaway(Integer order_custom_attr1_takeaway) {
        this.order_custom_attr1_takeaway = order_custom_attr1_takeaway;
    }

    public Integer getOrder_custom_attr2_takeaway() {
        return order_custom_attr2_takeaway;
    }

    public void setOrder_custom_attr2_takeaway(Integer order_custom_attr2_takeaway) {
        this.order_custom_attr2_takeaway = order_custom_attr2_takeaway;
    }

    public Integer getOrder_custom_attr3_takeaway() {
        return order_custom_attr3_takeaway;
    }

    public void setOrder_custom_attr3_takeaway(Integer order_custom_attr3_takeaway) {
        this.order_custom_attr3_takeaway = order_custom_attr3_takeaway;
    }

    public Integer getOrder_reason_express() {
        return order_reason_express;
    }

    public void setOrder_reason_express(Integer order_reason_express) {
        this.order_reason_express = order_reason_express;
    }

    public Integer getOrder_custom_attr1_express() {
        return order_custom_attr1_express;
    }

    public void setOrder_custom_attr1_express(Integer order_custom_attr1_express) {
        this.order_custom_attr1_express = order_custom_attr1_express;
    }

    public Integer getOrder_custom_attr2_express() {
        return order_custom_attr2_express;
    }

    public void setOrder_custom_attr2_express(Integer order_custom_attr2_express) {
        this.order_custom_attr2_express = order_custom_attr2_express;
    }

    public Integer getOrder_custom_attr3_express() {
        return order_custom_attr3_express;
    }

    public void setOrder_custom_attr3_express(Integer order_custom_attr3_express) {
        this.order_custom_attr3_express = order_custom_attr3_express;
    }

    public Integer getOrder_reason_exceed_desc() {
        return order_reason_exceed_desc;
    }

    public void setOrder_reason_exceed_desc(Integer order_reason_exceed_desc) {
        this.order_reason_exceed_desc = order_reason_exceed_desc;
    }

    public Integer getOrder_reason_sf_express() {
        return order_reason_sf_express;
    }

    public void setOrder_reason_sf_express(Integer order_reason_sf_express) {
        this.order_reason_sf_express = order_reason_sf_express;
    }

    public Integer getOrder_custom_attr1_sf_express() {
        return order_custom_attr1_sf_express;
    }

    public void setOrder_custom_attr1_sf_express(Integer order_custom_attr1_sf_express) {
        this.order_custom_attr1_sf_express = order_custom_attr1_sf_express;
    }

    public Integer getOrder_custom_attr2_sf_express() {
        return order_custom_attr2_sf_express;
    }

    public void setOrder_custom_attr2_sf_express(Integer order_custom_attr2_sf_express) {
        this.order_custom_attr2_sf_express = order_custom_attr2_sf_express;
    }

    public Integer getOrder_custom_attr3_sf_express() {
        return order_custom_attr3_sf_express;
    }

    public void setOrder_custom_attr3_sf_express(Integer order_custom_attr3_sf_express) {
        this.order_custom_attr3_sf_express = order_custom_attr3_sf_express;
    }
}