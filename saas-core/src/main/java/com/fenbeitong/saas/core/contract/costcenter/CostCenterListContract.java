package com.fenbeitong.saas.core.contract.costcenter;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * Created by xuzn on 18/3/23.
 */
public class CostCenterListContract {
    private String id;
    private String code;
    private String name;
    private String member_name;
    @JsonIgnore
    private Integer state_type;
    private StateTypeBean state;
    private String begin_date;
    private String end_date;
    @JsonIgnore
    private Integer usable_range_type;
    private UsableRangeBean usable_range;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMember_name() {
        return member_name;
    }

    public void setMember_name(String member_name) {
        this.member_name = member_name;
    }

    public Integer getState_type() {
        return state_type;
    }

    public void setState_type(Integer state_type) {
        this.state_type = state_type;
    }

    public StateTypeBean getState() {
        return state;
    }

    public void setState(StateTypeBean state) {
        this.state = state;
    }

    public String getBegin_date() {
        return begin_date;
    }

    public void setBegin_date(String begin_date) {
        this.begin_date = begin_date;
    }

    public String getEnd_date() {
        return end_date;
    }

    public void setEnd_date(String end_date) {
        this.end_date = end_date;
    }

    public Integer getUsable_range_type() {
        return usable_range_type;
    }

    public void setUsable_range_type(Integer usable_range_type) {
        this.usable_range_type = usable_range_type;
    }

    public UsableRangeBean getUsable_range() {
        return usable_range;
    }

    public void setUsable_range(UsableRangeBean usable_range) {
        this.usable_range = usable_range;
    }

    public static class StateTypeBean{
        private Integer key;
        private String value;

        public Integer getKey() {
            return key;
        }

        public void setKey(Integer key) {
            this.key = key;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }

    public static class UsableRangeBean{
        private Integer key;
        private String value;

        public Integer getKey() {
            return key;
        }

        public void setKey(Integer key) {
            this.key = key;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }
}
