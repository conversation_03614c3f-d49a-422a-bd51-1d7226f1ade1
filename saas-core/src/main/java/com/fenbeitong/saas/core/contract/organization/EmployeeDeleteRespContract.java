package com.fenbeitong.saas.core.contract.organization;

import com.alibaba.fastjson.JSONObject;
import kafka.utils.Json;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/5/20.
 */
public class EmployeeDeleteRespContract {


    /**
     * result : {"delete_status":1}
     * sync_data : {}
     */

    private JSONObject result;
    private JSONObject sync_data;

    public JSONObject getResult() {
        return result;
    }

    public void setResult(JSONObject result) {
        this.result = result;
    }

    public JSONObject getSync_data() {
        return sync_data;
    }

    public void setSync_data(JSONObject sync_data) {
        this.sync_data = sync_data;
    }

    public EmployeeDeleteRespContract(JSONObject sync_data) {
        JSONObject result = new JSONObject();
        result.put("delete_status",1);
        this.result = result;
        this.sync_data = sync_data;
    }

    public EmployeeDeleteRespContract(JSONObject result, JSONObject sync_data) {
        this.result = result;
        this.sync_data = sync_data;
    }
}
