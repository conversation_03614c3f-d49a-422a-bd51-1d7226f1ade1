package com.fenbeitong.saas.core.contract.message;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/4/14.
 */
public class MessageGeneralContract {

    private long total_unread_count;

    private boolean total_unread_state;

    private Notify spend_notify;

    private Notify order_notify;

    private Notify approval_notify;

    private Notify booking_notify;

    private Notify system_notify;

    public long getTotal_unread_count() {
        return total_unread_count;
    }

    public void setTotal_unread_count(long total_unread_count) {
        this.total_unread_count = total_unread_count;
    }

    public boolean isTotal_unread_state() {
        return total_unread_state;
    }

    public void setTotal_unread_state(boolean total_unread_state) {
        this.total_unread_state = total_unread_state;
    }

    public Notify getSpend_notify() {
        return spend_notify;
    }

    public void setSpend_notify(Notify spend_notify) {
        this.spend_notify = spend_notify;
    }

    public Notify getOrder_notify() {
        return order_notify;
    }

    public void setOrder_notify(Notify order_notify) {
        this.order_notify = order_notify;
    }

    public Notify getApproval_notify() {
        return approval_notify;
    }

    public void setApproval_notify(Notify approval_notify) {
        this.approval_notify = approval_notify;
    }

    public Notify getBooking_notify() {
        return booking_notify;
    }

    public void setBooking_notify(Notify booking_notify) {
        this.booking_notify = booking_notify;
    }

    public Notify getSystem_notify() {
        return system_notify;
    }

    public void setSystem_notify(Notify system_notify) {
        this.system_notify = system_notify;
    }

    public static class Notify{

        private long unread_count;

        private boolean unread_state;

        private String msg_title;

        private String msg_desc;

        public long getUnread_count() {
            return unread_count;
        }

        public void setUnread_count(long unread_count) {
            this.unread_count = unread_count;
        }

        public boolean isUnread_state() {
            return unread_state;
        }

        public void setUnread_state(boolean unread_state) {
            this.unread_state = unread_state;
        }

        public String getMsg_title() {
            return msg_title;
        }

        public void setMsg_title(String msg_title) {
            this.msg_title = msg_title;
        }

        public String getMsg_desc() {
            return msg_desc;
        }

        public void setMsg_desc(String msg_desc) {
            this.msg_desc = msg_desc;
        }
    }
}
