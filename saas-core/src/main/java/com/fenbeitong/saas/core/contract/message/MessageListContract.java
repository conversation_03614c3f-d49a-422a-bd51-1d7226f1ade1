package com.fenbeitong.saas.core.contract.message;

/**
 * Created by xuzn on 17/9/21.
 */
public class MessageListContract extends MessageVoContrcat{

    private Integer msg_type;//消息类型

//    private String comment;//消息内容

    private String create_time;//创建时间

    private String order_id;//订单id

//    private String title;//标题

    private String apply_time;//申请时间

    private Integer apply_type;//申请类型

    private String apply_id;//申请id

    private Boolean myself;//是否是本人申请

    private Integer view_type;//审批类型

    private Integer apply_status;//审批状态

    private String generate_time;//系统消息时间

    private Boolean has_image;//是否有图片

    private Boolean has_link;//是否有链接

    private String image_url;//图片地址

    private String redirect_url;//跳转地址

    private String consume_time;//消费时间

    private Integer order_type;//订单类型

    private String creator_msg;//下单人

    private String passenger_msg;//接受人名单

    private String price_msg;//消费金额

    private Integer setting_type;


    public Integer getMsg_type() {
        return msg_type;
    }

    public void setMsg_type(Integer msg_type) {
        this.msg_type = msg_type;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public String getOrder_id() {
        return order_id;
    }

    public void setOrder_id(String order_id) {
        this.order_id = order_id;
    }

    public String getApply_time() {
        return apply_time;
    }

    public void setApply_time(String apply_time) {
        this.apply_time = apply_time;
    }

    public Integer getApply_type() {
        return apply_type;
    }

    public void setApply_type(Integer apply_type) {
        this.apply_type = apply_type;
    }

    public String getApply_id() {
        return apply_id;
    }

    public void setApply_id(String apply_id) {
        this.apply_id = apply_id;
    }

    public Boolean getMyself() {
        return myself;
    }

    public void setMyself(Boolean myself) {
        this.myself = myself;
    }

    public Integer getView_type() {
        return view_type;
    }

    public void setView_type(Integer view_type) {
        this.view_type = view_type;
    }

    public Integer getApply_status() {
        return apply_status;
    }

    public void setApply_status(Integer apply_status) {
        this.apply_status = apply_status;
    }

    public String getGenerate_time() {
        return generate_time;
    }

    public void setGenerate_time(String generate_time) {
        this.generate_time = generate_time;
    }

    public Boolean getHas_image() {
        return has_image;
    }

    public void setHas_image(Boolean has_image) {
        this.has_image = has_image;
    }

    public Boolean getHas_link() {
        return has_link;
    }

    public void setHas_link(Boolean has_link) {
        this.has_link = has_link;
    }

    public String getImage_url() {
        return image_url;
    }

    public void setImage_url(String image_url) {
        this.image_url = image_url;
    }

    public String getRedirect_url() {
        return redirect_url;
    }

    public void setRedirect_url(String redirect_url) {
        this.redirect_url = redirect_url;
    }

    public String getConsume_time() {
        return consume_time;
    }

    public void setConsume_time(String consume_time) {
        this.consume_time = consume_time;
    }

    public Integer getOrder_type() {
        return order_type;
    }

    public void setOrder_type(Integer order_type) {
        this.order_type = order_type;
    }

    public String getCreator_msg() {
        return creator_msg;
    }

    public void setCreator_msg(String creator_msg) {
        this.creator_msg = creator_msg;
    }

    public String getPassenger_msg() {
        return passenger_msg;
    }

    public void setPassenger_msg(String passenger_msg) {
        this.passenger_msg = passenger_msg;
    }

    public String getPrice_msg() {
        return price_msg;
    }

    public void setPrice_msg(String price_msg) {
        this.price_msg = price_msg;
    }

    public Integer getSetting_type() {
        return setting_type;
    }

    public void setSetting_type(Integer setting_type) {
        this.setting_type = setting_type;
    }

}
