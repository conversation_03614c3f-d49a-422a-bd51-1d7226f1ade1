package com.fenbeitong.saas.core.contract.applyflow;

import com.fenbeitong.saas.core.model.enums.CompanyApplyType;

/**
 * Created by <PERSON><PERSON>bin on 2017/4/25.
 * 应用审批流，返回数据
 */
public class ApplyFlowApplicateResponseContract {
    private CompanyApplyType companyApplyType;
    /**
     * 当前审批人ID
     */
    private String approver;

    private Integer currentLogId;

    private Integer nextLogId;

    public String getApprover() {
        return approver;
    }

    public void setApprover(String approver) {
        this.approver = approver;
    }

    public CompanyApplyType getCompanyApplyType() {
        return companyApplyType;
    }

    public void setCompanyApplyType(CompanyApplyType companyApplyType) {
        this.companyApplyType = companyApplyType;
    }

    public Integer getCurrentLogId() {
        return currentLogId;
    }

    public void setCurrentLogId(Integer currentLogId) {
        this.currentLogId = currentLogId;
    }

    public Integer getNextLogId() {
        return nextLogId;
    }

    public void setNextLogId(Integer nextLogId) {
        this.nextLogId = nextLogId;
    }
}
