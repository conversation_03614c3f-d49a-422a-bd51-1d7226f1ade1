package com.fenbeitong.saas.core.model.fenbeitong;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class HotelInterceptRecordExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table hotel_intercept_record
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table hotel_intercept_record
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table hotel_intercept_record
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_intercept_record
     *
     * @mbg.generated
     */
    public HotelInterceptRecordExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_intercept_record
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_intercept_record
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_intercept_record
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_intercept_record
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_intercept_record
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_intercept_record
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_intercept_record
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_intercept_record
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_intercept_record
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_intercept_record
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table hotel_intercept_record
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdIsNull() {
            addCriterion("employee_id is null");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdIsNotNull() {
            addCriterion("employee_id is not null");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdEqualTo(String value) {
            addCriterion("employee_id =", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdNotEqualTo(String value) {
            addCriterion("employee_id <>", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdGreaterThan(String value) {
            addCriterion("employee_id >", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdGreaterThanOrEqualTo(String value) {
            addCriterion("employee_id >=", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdLessThan(String value) {
            addCriterion("employee_id <", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdLessThanOrEqualTo(String value) {
            addCriterion("employee_id <=", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdLike(String value) {
            addCriterion("employee_id like", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdNotLike(String value) {
            addCriterion("employee_id not like", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdIn(List<String> values) {
            addCriterion("employee_id in", values, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdNotIn(List<String> values) {
            addCriterion("employee_id not in", values, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdBetween(String value1, String value2) {
            addCriterion("employee_id between", value1, value2, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdNotBetween(String value1, String value2) {
            addCriterion("employee_id not between", value1, value2, "employeeId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(String value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(String value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(String value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(String value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(String value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(String value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLike(String value) {
            addCriterion("company_id like", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotLike(String value) {
            addCriterion("company_id not like", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<String> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<String> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(String value1, String value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(String value1, String value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andContactNameIsNull() {
            addCriterion("contact_name is null");
            return (Criteria) this;
        }

        public Criteria andContactNameIsNotNull() {
            addCriterion("contact_name is not null");
            return (Criteria) this;
        }

        public Criteria andContactNameEqualTo(String value) {
            addCriterion("contact_name =", value, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameNotEqualTo(String value) {
            addCriterion("contact_name <>", value, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameGreaterThan(String value) {
            addCriterion("contact_name >", value, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameGreaterThanOrEqualTo(String value) {
            addCriterion("contact_name >=", value, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameLessThan(String value) {
            addCriterion("contact_name <", value, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameLessThanOrEqualTo(String value) {
            addCriterion("contact_name <=", value, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameLike(String value) {
            addCriterion("contact_name like", value, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameNotLike(String value) {
            addCriterion("contact_name not like", value, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameIn(List<String> values) {
            addCriterion("contact_name in", values, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameNotIn(List<String> values) {
            addCriterion("contact_name not in", values, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameBetween(String value1, String value2) {
            addCriterion("contact_name between", value1, value2, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameNotBetween(String value1, String value2) {
            addCriterion("contact_name not between", value1, value2, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactPhoneIsNull() {
            addCriterion("contact_phone is null");
            return (Criteria) this;
        }

        public Criteria andContactPhoneIsNotNull() {
            addCriterion("contact_phone is not null");
            return (Criteria) this;
        }

        public Criteria andContactPhoneEqualTo(String value) {
            addCriterion("contact_phone =", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotEqualTo(String value) {
            addCriterion("contact_phone <>", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneGreaterThan(String value) {
            addCriterion("contact_phone >", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("contact_phone >=", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneLessThan(String value) {
            addCriterion("contact_phone <", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneLessThanOrEqualTo(String value) {
            addCriterion("contact_phone <=", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneLike(String value) {
            addCriterion("contact_phone like", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotLike(String value) {
            addCriterion("contact_phone not like", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneIn(List<String> values) {
            addCriterion("contact_phone in", values, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotIn(List<String> values) {
            addCriterion("contact_phone not in", values, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneBetween(String value1, String value2) {
            addCriterion("contact_phone between", value1, value2, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotBetween(String value1, String value2) {
            addCriterion("contact_phone not between", value1, value2, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andChannelIsNull() {
            addCriterion("channel is null");
            return (Criteria) this;
        }

        public Criteria andChannelIsNotNull() {
            addCriterion("channel is not null");
            return (Criteria) this;
        }

        public Criteria andChannelEqualTo(Integer value) {
            addCriterion("channel =", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelNotEqualTo(Integer value) {
            addCriterion("channel <>", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelGreaterThan(Integer value) {
            addCriterion("channel >", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelGreaterThanOrEqualTo(Integer value) {
            addCriterion("channel >=", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelLessThan(Integer value) {
            addCriterion("channel <", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelLessThanOrEqualTo(Integer value) {
            addCriterion("channel <=", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelIn(List<Integer> values) {
            addCriterion("channel in", values, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelNotIn(List<Integer> values) {
            addCriterion("channel not in", values, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelBetween(Integer value1, Integer value2) {
            addCriterion("channel between", value1, value2, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelNotBetween(Integer value1, Integer value2) {
            addCriterion("channel not between", value1, value2, "channel");
            return (Criteria) this;
        }

        public Criteria andTotalPriceIsNull() {
            addCriterion("total_price is null");
            return (Criteria) this;
        }

        public Criteria andTotalPriceIsNotNull() {
            addCriterion("total_price is not null");
            return (Criteria) this;
        }

        public Criteria andTotalPriceEqualTo(BigDecimal value) {
            addCriterion("total_price =", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotEqualTo(BigDecimal value) {
            addCriterion("total_price <>", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceGreaterThan(BigDecimal value) {
            addCriterion("total_price >", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_price >=", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceLessThan(BigDecimal value) {
            addCriterion("total_price <", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_price <=", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceIn(List<BigDecimal> values) {
            addCriterion("total_price in", values, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotIn(List<BigDecimal> values) {
            addCriterion("total_price not in", values, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_price between", value1, value2, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_price not between", value1, value2, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andHotelRuleIsNull() {
            addCriterion("hotel_rule is null");
            return (Criteria) this;
        }

        public Criteria andHotelRuleIsNotNull() {
            addCriterion("hotel_rule is not null");
            return (Criteria) this;
        }

        public Criteria andHotelRuleEqualTo(Integer value) {
            addCriterion("hotel_rule =", value, "hotelRule");
            return (Criteria) this;
        }

        public Criteria andHotelRuleNotEqualTo(Integer value) {
            addCriterion("hotel_rule <>", value, "hotelRule");
            return (Criteria) this;
        }

        public Criteria andHotelRuleGreaterThan(Integer value) {
            addCriterion("hotel_rule >", value, "hotelRule");
            return (Criteria) this;
        }

        public Criteria andHotelRuleGreaterThanOrEqualTo(Integer value) {
            addCriterion("hotel_rule >=", value, "hotelRule");
            return (Criteria) this;
        }

        public Criteria andHotelRuleLessThan(Integer value) {
            addCriterion("hotel_rule <", value, "hotelRule");
            return (Criteria) this;
        }

        public Criteria andHotelRuleLessThanOrEqualTo(Integer value) {
            addCriterion("hotel_rule <=", value, "hotelRule");
            return (Criteria) this;
        }

        public Criteria andHotelRuleIn(List<Integer> values) {
            addCriterion("hotel_rule in", values, "hotelRule");
            return (Criteria) this;
        }

        public Criteria andHotelRuleNotIn(List<Integer> values) {
            addCriterion("hotel_rule not in", values, "hotelRule");
            return (Criteria) this;
        }

        public Criteria andHotelRuleBetween(Integer value1, Integer value2) {
            addCriterion("hotel_rule between", value1, value2, "hotelRule");
            return (Criteria) this;
        }

        public Criteria andHotelRuleNotBetween(Integer value1, Integer value2) {
            addCriterion("hotel_rule not between", value1, value2, "hotelRule");
            return (Criteria) this;
        }

        public Criteria andHotelRuleFlagIsNull() {
            addCriterion("hotel_rule_flag is null");
            return (Criteria) this;
        }

        public Criteria andHotelRuleFlagIsNotNull() {
            addCriterion("hotel_rule_flag is not null");
            return (Criteria) this;
        }

        public Criteria andHotelRuleFlagEqualTo(Boolean value) {
            addCriterion("hotel_rule_flag =", value, "hotelRuleFlag");
            return (Criteria) this;
        }

        public Criteria andHotelRuleFlagNotEqualTo(Boolean value) {
            addCriterion("hotel_rule_flag <>", value, "hotelRuleFlag");
            return (Criteria) this;
        }

        public Criteria andHotelRuleFlagGreaterThan(Boolean value) {
            addCriterion("hotel_rule_flag >", value, "hotelRuleFlag");
            return (Criteria) this;
        }

        public Criteria andHotelRuleFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("hotel_rule_flag >=", value, "hotelRuleFlag");
            return (Criteria) this;
        }

        public Criteria andHotelRuleFlagLessThan(Boolean value) {
            addCriterion("hotel_rule_flag <", value, "hotelRuleFlag");
            return (Criteria) this;
        }

        public Criteria andHotelRuleFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("hotel_rule_flag <=", value, "hotelRuleFlag");
            return (Criteria) this;
        }

        public Criteria andHotelRuleFlagIn(List<Boolean> values) {
            addCriterion("hotel_rule_flag in", values, "hotelRuleFlag");
            return (Criteria) this;
        }

        public Criteria andHotelRuleFlagNotIn(List<Boolean> values) {
            addCriterion("hotel_rule_flag not in", values, "hotelRuleFlag");
            return (Criteria) this;
        }

        public Criteria andHotelRuleFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("hotel_rule_flag between", value1, value2, "hotelRuleFlag");
            return (Criteria) this;
        }

        public Criteria andHotelRuleFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("hotel_rule_flag not between", value1, value2, "hotelRuleFlag");
            return (Criteria) this;
        }

        public Criteria andHotelVerifyFlagIsNull() {
            addCriterion("hotel_verify_flag is null");
            return (Criteria) this;
        }

        public Criteria andHotelVerifyFlagIsNotNull() {
            addCriterion("hotel_verify_flag is not null");
            return (Criteria) this;
        }

        public Criteria andHotelVerifyFlagEqualTo(Boolean value) {
            addCriterion("hotel_verify_flag =", value, "hotelVerifyFlag");
            return (Criteria) this;
        }

        public Criteria andHotelVerifyFlagNotEqualTo(Boolean value) {
            addCriterion("hotel_verify_flag <>", value, "hotelVerifyFlag");
            return (Criteria) this;
        }

        public Criteria andHotelVerifyFlagGreaterThan(Boolean value) {
            addCriterion("hotel_verify_flag >", value, "hotelVerifyFlag");
            return (Criteria) this;
        }

        public Criteria andHotelVerifyFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("hotel_verify_flag >=", value, "hotelVerifyFlag");
            return (Criteria) this;
        }

        public Criteria andHotelVerifyFlagLessThan(Boolean value) {
            addCriterion("hotel_verify_flag <", value, "hotelVerifyFlag");
            return (Criteria) this;
        }

        public Criteria andHotelVerifyFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("hotel_verify_flag <=", value, "hotelVerifyFlag");
            return (Criteria) this;
        }

        public Criteria andHotelVerifyFlagIn(List<Boolean> values) {
            addCriterion("hotel_verify_flag in", values, "hotelVerifyFlag");
            return (Criteria) this;
        }

        public Criteria andHotelVerifyFlagNotIn(List<Boolean> values) {
            addCriterion("hotel_verify_flag not in", values, "hotelVerifyFlag");
            return (Criteria) this;
        }

        public Criteria andHotelVerifyFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("hotel_verify_flag between", value1, value2, "hotelVerifyFlag");
            return (Criteria) this;
        }

        public Criteria andHotelVerifyFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("hotel_verify_flag not between", value1, value2, "hotelVerifyFlag");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagIsNull() {
            addCriterion("exceed_buy_flag is null");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagIsNotNull() {
            addCriterion("exceed_buy_flag is not null");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagEqualTo(Boolean value) {
            addCriterion("exceed_buy_flag =", value, "exceedBuyFlag");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagNotEqualTo(Boolean value) {
            addCriterion("exceed_buy_flag <>", value, "exceedBuyFlag");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagGreaterThan(Boolean value) {
            addCriterion("exceed_buy_flag >", value, "exceedBuyFlag");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("exceed_buy_flag >=", value, "exceedBuyFlag");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagLessThan(Boolean value) {
            addCriterion("exceed_buy_flag <", value, "exceedBuyFlag");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("exceed_buy_flag <=", value, "exceedBuyFlag");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagIn(List<Boolean> values) {
            addCriterion("exceed_buy_flag in", values, "exceedBuyFlag");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagNotIn(List<Boolean> values) {
            addCriterion("exceed_buy_flag not in", values, "exceedBuyFlag");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("exceed_buy_flag between", value1, value2, "exceedBuyFlag");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("exceed_buy_flag not between", value1, value2, "exceedBuyFlag");
            return (Criteria) this;
        }

        public Criteria andFirstTierFlagIsNull() {
            addCriterion("first_tier_flag is null");
            return (Criteria) this;
        }

        public Criteria andFirstTierFlagIsNotNull() {
            addCriterion("first_tier_flag is not null");
            return (Criteria) this;
        }

        public Criteria andFirstTierFlagEqualTo(Boolean value) {
            addCriterion("first_tier_flag =", value, "firstTierFlag");
            return (Criteria) this;
        }

        public Criteria andFirstTierFlagNotEqualTo(Boolean value) {
            addCriterion("first_tier_flag <>", value, "firstTierFlag");
            return (Criteria) this;
        }

        public Criteria andFirstTierFlagGreaterThan(Boolean value) {
            addCriterion("first_tier_flag >", value, "firstTierFlag");
            return (Criteria) this;
        }

        public Criteria andFirstTierFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("first_tier_flag >=", value, "firstTierFlag");
            return (Criteria) this;
        }

        public Criteria andFirstTierFlagLessThan(Boolean value) {
            addCriterion("first_tier_flag <", value, "firstTierFlag");
            return (Criteria) this;
        }

        public Criteria andFirstTierFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("first_tier_flag <=", value, "firstTierFlag");
            return (Criteria) this;
        }

        public Criteria andFirstTierFlagIn(List<Boolean> values) {
            addCriterion("first_tier_flag in", values, "firstTierFlag");
            return (Criteria) this;
        }

        public Criteria andFirstTierFlagNotIn(List<Boolean> values) {
            addCriterion("first_tier_flag not in", values, "firstTierFlag");
            return (Criteria) this;
        }

        public Criteria andFirstTierFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("first_tier_flag between", value1, value2, "firstTierFlag");
            return (Criteria) this;
        }

        public Criteria andFirstTierFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("first_tier_flag not between", value1, value2, "firstTierFlag");
            return (Criteria) this;
        }

        public Criteria andFirstTierPriceIsNull() {
            addCriterion("first_tier_price is null");
            return (Criteria) this;
        }

        public Criteria andFirstTierPriceIsNotNull() {
            addCriterion("first_tier_price is not null");
            return (Criteria) this;
        }

        public Criteria andFirstTierPriceEqualTo(BigDecimal value) {
            addCriterion("first_tier_price =", value, "firstTierPrice");
            return (Criteria) this;
        }

        public Criteria andFirstTierPriceNotEqualTo(BigDecimal value) {
            addCriterion("first_tier_price <>", value, "firstTierPrice");
            return (Criteria) this;
        }

        public Criteria andFirstTierPriceGreaterThan(BigDecimal value) {
            addCriterion("first_tier_price >", value, "firstTierPrice");
            return (Criteria) this;
        }

        public Criteria andFirstTierPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("first_tier_price >=", value, "firstTierPrice");
            return (Criteria) this;
        }

        public Criteria andFirstTierPriceLessThan(BigDecimal value) {
            addCriterion("first_tier_price <", value, "firstTierPrice");
            return (Criteria) this;
        }

        public Criteria andFirstTierPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("first_tier_price <=", value, "firstTierPrice");
            return (Criteria) this;
        }

        public Criteria andFirstTierPriceIn(List<BigDecimal> values) {
            addCriterion("first_tier_price in", values, "firstTierPrice");
            return (Criteria) this;
        }

        public Criteria andFirstTierPriceNotIn(List<BigDecimal> values) {
            addCriterion("first_tier_price not in", values, "firstTierPrice");
            return (Criteria) this;
        }

        public Criteria andFirstTierPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("first_tier_price between", value1, value2, "firstTierPrice");
            return (Criteria) this;
        }

        public Criteria andFirstTierPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("first_tier_price not between", value1, value2, "firstTierPrice");
            return (Criteria) this;
        }

        public Criteria andSecondTierFlagIsNull() {
            addCriterion("second_tier_flag is null");
            return (Criteria) this;
        }

        public Criteria andSecondTierFlagIsNotNull() {
            addCriterion("second_tier_flag is not null");
            return (Criteria) this;
        }

        public Criteria andSecondTierFlagEqualTo(Boolean value) {
            addCriterion("second_tier_flag =", value, "secondTierFlag");
            return (Criteria) this;
        }

        public Criteria andSecondTierFlagNotEqualTo(Boolean value) {
            addCriterion("second_tier_flag <>", value, "secondTierFlag");
            return (Criteria) this;
        }

        public Criteria andSecondTierFlagGreaterThan(Boolean value) {
            addCriterion("second_tier_flag >", value, "secondTierFlag");
            return (Criteria) this;
        }

        public Criteria andSecondTierFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("second_tier_flag >=", value, "secondTierFlag");
            return (Criteria) this;
        }

        public Criteria andSecondTierFlagLessThan(Boolean value) {
            addCriterion("second_tier_flag <", value, "secondTierFlag");
            return (Criteria) this;
        }

        public Criteria andSecondTierFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("second_tier_flag <=", value, "secondTierFlag");
            return (Criteria) this;
        }

        public Criteria andSecondTierFlagIn(List<Boolean> values) {
            addCriterion("second_tier_flag in", values, "secondTierFlag");
            return (Criteria) this;
        }

        public Criteria andSecondTierFlagNotIn(List<Boolean> values) {
            addCriterion("second_tier_flag not in", values, "secondTierFlag");
            return (Criteria) this;
        }

        public Criteria andSecondTierFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("second_tier_flag between", value1, value2, "secondTierFlag");
            return (Criteria) this;
        }

        public Criteria andSecondTierFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("second_tier_flag not between", value1, value2, "secondTierFlag");
            return (Criteria) this;
        }

        public Criteria andSecondTierPriceIsNull() {
            addCriterion("second_tier_price is null");
            return (Criteria) this;
        }

        public Criteria andSecondTierPriceIsNotNull() {
            addCriterion("second_tier_price is not null");
            return (Criteria) this;
        }

        public Criteria andSecondTierPriceEqualTo(BigDecimal value) {
            addCriterion("second_tier_price =", value, "secondTierPrice");
            return (Criteria) this;
        }

        public Criteria andSecondTierPriceNotEqualTo(BigDecimal value) {
            addCriterion("second_tier_price <>", value, "secondTierPrice");
            return (Criteria) this;
        }

        public Criteria andSecondTierPriceGreaterThan(BigDecimal value) {
            addCriterion("second_tier_price >", value, "secondTierPrice");
            return (Criteria) this;
        }

        public Criteria andSecondTierPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("second_tier_price >=", value, "secondTierPrice");
            return (Criteria) this;
        }

        public Criteria andSecondTierPriceLessThan(BigDecimal value) {
            addCriterion("second_tier_price <", value, "secondTierPrice");
            return (Criteria) this;
        }

        public Criteria andSecondTierPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("second_tier_price <=", value, "secondTierPrice");
            return (Criteria) this;
        }

        public Criteria andSecondTierPriceIn(List<BigDecimal> values) {
            addCriterion("second_tier_price in", values, "secondTierPrice");
            return (Criteria) this;
        }

        public Criteria andSecondTierPriceNotIn(List<BigDecimal> values) {
            addCriterion("second_tier_price not in", values, "secondTierPrice");
            return (Criteria) this;
        }

        public Criteria andSecondTierPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("second_tier_price between", value1, value2, "secondTierPrice");
            return (Criteria) this;
        }

        public Criteria andSecondTierPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("second_tier_price not between", value1, value2, "secondTierPrice");
            return (Criteria) this;
        }

        public Criteria andOtherTierFlagIsNull() {
            addCriterion("other_tier_flag is null");
            return (Criteria) this;
        }

        public Criteria andOtherTierFlagIsNotNull() {
            addCriterion("other_tier_flag is not null");
            return (Criteria) this;
        }

        public Criteria andOtherTierFlagEqualTo(Boolean value) {
            addCriterion("other_tier_flag =", value, "otherTierFlag");
            return (Criteria) this;
        }

        public Criteria andOtherTierFlagNotEqualTo(Boolean value) {
            addCriterion("other_tier_flag <>", value, "otherTierFlag");
            return (Criteria) this;
        }

        public Criteria andOtherTierFlagGreaterThan(Boolean value) {
            addCriterion("other_tier_flag >", value, "otherTierFlag");
            return (Criteria) this;
        }

        public Criteria andOtherTierFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("other_tier_flag >=", value, "otherTierFlag");
            return (Criteria) this;
        }

        public Criteria andOtherTierFlagLessThan(Boolean value) {
            addCriterion("other_tier_flag <", value, "otherTierFlag");
            return (Criteria) this;
        }

        public Criteria andOtherTierFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("other_tier_flag <=", value, "otherTierFlag");
            return (Criteria) this;
        }

        public Criteria andOtherTierFlagIn(List<Boolean> values) {
            addCriterion("other_tier_flag in", values, "otherTierFlag");
            return (Criteria) this;
        }

        public Criteria andOtherTierFlagNotIn(List<Boolean> values) {
            addCriterion("other_tier_flag not in", values, "otherTierFlag");
            return (Criteria) this;
        }

        public Criteria andOtherTierFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("other_tier_flag between", value1, value2, "otherTierFlag");
            return (Criteria) this;
        }

        public Criteria andOtherTierFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("other_tier_flag not between", value1, value2, "otherTierFlag");
            return (Criteria) this;
        }

        public Criteria andOtherTierPriceIsNull() {
            addCriterion("other_tier_price is null");
            return (Criteria) this;
        }

        public Criteria andOtherTierPriceIsNotNull() {
            addCriterion("other_tier_price is not null");
            return (Criteria) this;
        }

        public Criteria andOtherTierPriceEqualTo(BigDecimal value) {
            addCriterion("other_tier_price =", value, "otherTierPrice");
            return (Criteria) this;
        }

        public Criteria andOtherTierPriceNotEqualTo(BigDecimal value) {
            addCriterion("other_tier_price <>", value, "otherTierPrice");
            return (Criteria) this;
        }

        public Criteria andOtherTierPriceGreaterThan(BigDecimal value) {
            addCriterion("other_tier_price >", value, "otherTierPrice");
            return (Criteria) this;
        }

        public Criteria andOtherTierPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("other_tier_price >=", value, "otherTierPrice");
            return (Criteria) this;
        }

        public Criteria andOtherTierPriceLessThan(BigDecimal value) {
            addCriterion("other_tier_price <", value, "otherTierPrice");
            return (Criteria) this;
        }

        public Criteria andOtherTierPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("other_tier_price <=", value, "otherTierPrice");
            return (Criteria) this;
        }

        public Criteria andOtherTierPriceIn(List<BigDecimal> values) {
            addCriterion("other_tier_price in", values, "otherTierPrice");
            return (Criteria) this;
        }

        public Criteria andOtherTierPriceNotIn(List<BigDecimal> values) {
            addCriterion("other_tier_price not in", values, "otherTierPrice");
            return (Criteria) this;
        }

        public Criteria andOtherTierPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("other_tier_price between", value1, value2, "otherTierPrice");
            return (Criteria) this;
        }

        public Criteria andOtherTierPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("other_tier_price not between", value1, value2, "otherTierPrice");
            return (Criteria) this;
        }

        public Criteria andHotelAddressIsNull() {
            addCriterion("hotel_address is null");
            return (Criteria) this;
        }

        public Criteria andHotelAddressIsNotNull() {
            addCriterion("hotel_address is not null");
            return (Criteria) this;
        }

        public Criteria andHotelAddressEqualTo(String value) {
            addCriterion("hotel_address =", value, "hotelAddress");
            return (Criteria) this;
        }

        public Criteria andHotelAddressNotEqualTo(String value) {
            addCriterion("hotel_address <>", value, "hotelAddress");
            return (Criteria) this;
        }

        public Criteria andHotelAddressGreaterThan(String value) {
            addCriterion("hotel_address >", value, "hotelAddress");
            return (Criteria) this;
        }

        public Criteria andHotelAddressGreaterThanOrEqualTo(String value) {
            addCriterion("hotel_address >=", value, "hotelAddress");
            return (Criteria) this;
        }

        public Criteria andHotelAddressLessThan(String value) {
            addCriterion("hotel_address <", value, "hotelAddress");
            return (Criteria) this;
        }

        public Criteria andHotelAddressLessThanOrEqualTo(String value) {
            addCriterion("hotel_address <=", value, "hotelAddress");
            return (Criteria) this;
        }

        public Criteria andHotelAddressLike(String value) {
            addCriterion("hotel_address like", value, "hotelAddress");
            return (Criteria) this;
        }

        public Criteria andHotelAddressNotLike(String value) {
            addCriterion("hotel_address not like", value, "hotelAddress");
            return (Criteria) this;
        }

        public Criteria andHotelAddressIn(List<String> values) {
            addCriterion("hotel_address in", values, "hotelAddress");
            return (Criteria) this;
        }

        public Criteria andHotelAddressNotIn(List<String> values) {
            addCriterion("hotel_address not in", values, "hotelAddress");
            return (Criteria) this;
        }

        public Criteria andHotelAddressBetween(String value1, String value2) {
            addCriterion("hotel_address between", value1, value2, "hotelAddress");
            return (Criteria) this;
        }

        public Criteria andHotelAddressNotBetween(String value1, String value2) {
            addCriterion("hotel_address not between", value1, value2, "hotelAddress");
            return (Criteria) this;
        }

        public Criteria andHotelCodeIsNull() {
            addCriterion("hotel_code is null");
            return (Criteria) this;
        }

        public Criteria andHotelCodeIsNotNull() {
            addCriterion("hotel_code is not null");
            return (Criteria) this;
        }

        public Criteria andHotelCodeEqualTo(String value) {
            addCriterion("hotel_code =", value, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeNotEqualTo(String value) {
            addCriterion("hotel_code <>", value, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeGreaterThan(String value) {
            addCriterion("hotel_code >", value, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeGreaterThanOrEqualTo(String value) {
            addCriterion("hotel_code >=", value, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeLessThan(String value) {
            addCriterion("hotel_code <", value, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeLessThanOrEqualTo(String value) {
            addCriterion("hotel_code <=", value, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeLike(String value) {
            addCriterion("hotel_code like", value, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeNotLike(String value) {
            addCriterion("hotel_code not like", value, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeIn(List<String> values) {
            addCriterion("hotel_code in", values, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeNotIn(List<String> values) {
            addCriterion("hotel_code not in", values, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeBetween(String value1, String value2) {
            addCriterion("hotel_code between", value1, value2, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelCodeNotBetween(String value1, String value2) {
            addCriterion("hotel_code not between", value1, value2, "hotelCode");
            return (Criteria) this;
        }

        public Criteria andHotelLngIsNull() {
            addCriterion("hotel_lng is null");
            return (Criteria) this;
        }

        public Criteria andHotelLngIsNotNull() {
            addCriterion("hotel_lng is not null");
            return (Criteria) this;
        }

        public Criteria andHotelLngEqualTo(String value) {
            addCriterion("hotel_lng =", value, "hotelLng");
            return (Criteria) this;
        }

        public Criteria andHotelLngNotEqualTo(String value) {
            addCriterion("hotel_lng <>", value, "hotelLng");
            return (Criteria) this;
        }

        public Criteria andHotelLngGreaterThan(String value) {
            addCriterion("hotel_lng >", value, "hotelLng");
            return (Criteria) this;
        }

        public Criteria andHotelLngGreaterThanOrEqualTo(String value) {
            addCriterion("hotel_lng >=", value, "hotelLng");
            return (Criteria) this;
        }

        public Criteria andHotelLngLessThan(String value) {
            addCriterion("hotel_lng <", value, "hotelLng");
            return (Criteria) this;
        }

        public Criteria andHotelLngLessThanOrEqualTo(String value) {
            addCriterion("hotel_lng <=", value, "hotelLng");
            return (Criteria) this;
        }

        public Criteria andHotelLngLike(String value) {
            addCriterion("hotel_lng like", value, "hotelLng");
            return (Criteria) this;
        }

        public Criteria andHotelLngNotLike(String value) {
            addCriterion("hotel_lng not like", value, "hotelLng");
            return (Criteria) this;
        }

        public Criteria andHotelLngIn(List<String> values) {
            addCriterion("hotel_lng in", values, "hotelLng");
            return (Criteria) this;
        }

        public Criteria andHotelLngNotIn(List<String> values) {
            addCriterion("hotel_lng not in", values, "hotelLng");
            return (Criteria) this;
        }

        public Criteria andHotelLngBetween(String value1, String value2) {
            addCriterion("hotel_lng between", value1, value2, "hotelLng");
            return (Criteria) this;
        }

        public Criteria andHotelLngNotBetween(String value1, String value2) {
            addCriterion("hotel_lng not between", value1, value2, "hotelLng");
            return (Criteria) this;
        }

        public Criteria andHotelLatIsNull() {
            addCriterion("hotel_lat is null");
            return (Criteria) this;
        }

        public Criteria andHotelLatIsNotNull() {
            addCriterion("hotel_lat is not null");
            return (Criteria) this;
        }

        public Criteria andHotelLatEqualTo(String value) {
            addCriterion("hotel_lat =", value, "hotelLat");
            return (Criteria) this;
        }

        public Criteria andHotelLatNotEqualTo(String value) {
            addCriterion("hotel_lat <>", value, "hotelLat");
            return (Criteria) this;
        }

        public Criteria andHotelLatGreaterThan(String value) {
            addCriterion("hotel_lat >", value, "hotelLat");
            return (Criteria) this;
        }

        public Criteria andHotelLatGreaterThanOrEqualTo(String value) {
            addCriterion("hotel_lat >=", value, "hotelLat");
            return (Criteria) this;
        }

        public Criteria andHotelLatLessThan(String value) {
            addCriterion("hotel_lat <", value, "hotelLat");
            return (Criteria) this;
        }

        public Criteria andHotelLatLessThanOrEqualTo(String value) {
            addCriterion("hotel_lat <=", value, "hotelLat");
            return (Criteria) this;
        }

        public Criteria andHotelLatLike(String value) {
            addCriterion("hotel_lat like", value, "hotelLat");
            return (Criteria) this;
        }

        public Criteria andHotelLatNotLike(String value) {
            addCriterion("hotel_lat not like", value, "hotelLat");
            return (Criteria) this;
        }

        public Criteria andHotelLatIn(List<String> values) {
            addCriterion("hotel_lat in", values, "hotelLat");
            return (Criteria) this;
        }

        public Criteria andHotelLatNotIn(List<String> values) {
            addCriterion("hotel_lat not in", values, "hotelLat");
            return (Criteria) this;
        }

        public Criteria andHotelLatBetween(String value1, String value2) {
            addCriterion("hotel_lat between", value1, value2, "hotelLat");
            return (Criteria) this;
        }

        public Criteria andHotelLatNotBetween(String value1, String value2) {
            addCriterion("hotel_lat not between", value1, value2, "hotelLat");
            return (Criteria) this;
        }

        public Criteria andHotelNameIsNull() {
            addCriterion("hotel_name is null");
            return (Criteria) this;
        }

        public Criteria andHotelNameIsNotNull() {
            addCriterion("hotel_name is not null");
            return (Criteria) this;
        }

        public Criteria andHotelNameEqualTo(String value) {
            addCriterion("hotel_name =", value, "hotelName");
            return (Criteria) this;
        }

        public Criteria andHotelNameNotEqualTo(String value) {
            addCriterion("hotel_name <>", value, "hotelName");
            return (Criteria) this;
        }

        public Criteria andHotelNameGreaterThan(String value) {
            addCriterion("hotel_name >", value, "hotelName");
            return (Criteria) this;
        }

        public Criteria andHotelNameGreaterThanOrEqualTo(String value) {
            addCriterion("hotel_name >=", value, "hotelName");
            return (Criteria) this;
        }

        public Criteria andHotelNameLessThan(String value) {
            addCriterion("hotel_name <", value, "hotelName");
            return (Criteria) this;
        }

        public Criteria andHotelNameLessThanOrEqualTo(String value) {
            addCriterion("hotel_name <=", value, "hotelName");
            return (Criteria) this;
        }

        public Criteria andHotelNameLike(String value) {
            addCriterion("hotel_name like", value, "hotelName");
            return (Criteria) this;
        }

        public Criteria andHotelNameNotLike(String value) {
            addCriterion("hotel_name not like", value, "hotelName");
            return (Criteria) this;
        }

        public Criteria andHotelNameIn(List<String> values) {
            addCriterion("hotel_name in", values, "hotelName");
            return (Criteria) this;
        }

        public Criteria andHotelNameNotIn(List<String> values) {
            addCriterion("hotel_name not in", values, "hotelName");
            return (Criteria) this;
        }

        public Criteria andHotelNameBetween(String value1, String value2) {
            addCriterion("hotel_name between", value1, value2, "hotelName");
            return (Criteria) this;
        }

        public Criteria andHotelNameNotBetween(String value1, String value2) {
            addCriterion("hotel_name not between", value1, value2, "hotelName");
            return (Criteria) this;
        }

        public Criteria andHotelPhoneIsNull() {
            addCriterion("hotel_phone is null");
            return (Criteria) this;
        }

        public Criteria andHotelPhoneIsNotNull() {
            addCriterion("hotel_phone is not null");
            return (Criteria) this;
        }

        public Criteria andHotelPhoneEqualTo(String value) {
            addCriterion("hotel_phone =", value, "hotelPhone");
            return (Criteria) this;
        }

        public Criteria andHotelPhoneNotEqualTo(String value) {
            addCriterion("hotel_phone <>", value, "hotelPhone");
            return (Criteria) this;
        }

        public Criteria andHotelPhoneGreaterThan(String value) {
            addCriterion("hotel_phone >", value, "hotelPhone");
            return (Criteria) this;
        }

        public Criteria andHotelPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("hotel_phone >=", value, "hotelPhone");
            return (Criteria) this;
        }

        public Criteria andHotelPhoneLessThan(String value) {
            addCriterion("hotel_phone <", value, "hotelPhone");
            return (Criteria) this;
        }

        public Criteria andHotelPhoneLessThanOrEqualTo(String value) {
            addCriterion("hotel_phone <=", value, "hotelPhone");
            return (Criteria) this;
        }

        public Criteria andHotelPhoneLike(String value) {
            addCriterion("hotel_phone like", value, "hotelPhone");
            return (Criteria) this;
        }

        public Criteria andHotelPhoneNotLike(String value) {
            addCriterion("hotel_phone not like", value, "hotelPhone");
            return (Criteria) this;
        }

        public Criteria andHotelPhoneIn(List<String> values) {
            addCriterion("hotel_phone in", values, "hotelPhone");
            return (Criteria) this;
        }

        public Criteria andHotelPhoneNotIn(List<String> values) {
            addCriterion("hotel_phone not in", values, "hotelPhone");
            return (Criteria) this;
        }

        public Criteria andHotelPhoneBetween(String value1, String value2) {
            addCriterion("hotel_phone between", value1, value2, "hotelPhone");
            return (Criteria) this;
        }

        public Criteria andHotelPhoneNotBetween(String value1, String value2) {
            addCriterion("hotel_phone not between", value1, value2, "hotelPhone");
            return (Criteria) this;
        }

        public Criteria andCityNameIsNull() {
            addCriterion("city_name is null");
            return (Criteria) this;
        }

        public Criteria andCityNameIsNotNull() {
            addCriterion("city_name is not null");
            return (Criteria) this;
        }

        public Criteria andCityNameEqualTo(String value) {
            addCriterion("city_name =", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotEqualTo(String value) {
            addCriterion("city_name <>", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThan(String value) {
            addCriterion("city_name >", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("city_name >=", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameLessThan(String value) {
            addCriterion("city_name <", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameLessThanOrEqualTo(String value) {
            addCriterion("city_name <=", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameLike(String value) {
            addCriterion("city_name like", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotLike(String value) {
            addCriterion("city_name not like", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameIn(List<String> values) {
            addCriterion("city_name in", values, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotIn(List<String> values) {
            addCriterion("city_name not in", values, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameBetween(String value1, String value2) {
            addCriterion("city_name between", value1, value2, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotBetween(String value1, String value2) {
            addCriterion("city_name not between", value1, value2, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNull() {
            addCriterion("city_code is null");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNotNull() {
            addCriterion("city_code is not null");
            return (Criteria) this;
        }

        public Criteria andCityCodeEqualTo(String value) {
            addCriterion("city_code =", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotEqualTo(String value) {
            addCriterion("city_code <>", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThan(String value) {
            addCriterion("city_code >", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("city_code >=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThan(String value) {
            addCriterion("city_code <", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThanOrEqualTo(String value) {
            addCriterion("city_code <=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLike(String value) {
            addCriterion("city_code like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotLike(String value) {
            addCriterion("city_code not like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIn(List<String> values) {
            addCriterion("city_code in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotIn(List<String> values) {
            addCriterion("city_code not in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeBetween(String value1, String value2) {
            addCriterion("city_code between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotBetween(String value1, String value2) {
            addCriterion("city_code not between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andFbCityNameIsNull() {
            addCriterion("fb_city_name is null");
            return (Criteria) this;
        }

        public Criteria andFbCityNameIsNotNull() {
            addCriterion("fb_city_name is not null");
            return (Criteria) this;
        }

        public Criteria andFbCityNameEqualTo(String value) {
            addCriterion("fb_city_name =", value, "fbCityName");
            return (Criteria) this;
        }

        public Criteria andFbCityNameNotEqualTo(String value) {
            addCriterion("fb_city_name <>", value, "fbCityName");
            return (Criteria) this;
        }

        public Criteria andFbCityNameGreaterThan(String value) {
            addCriterion("fb_city_name >", value, "fbCityName");
            return (Criteria) this;
        }

        public Criteria andFbCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("fb_city_name >=", value, "fbCityName");
            return (Criteria) this;
        }

        public Criteria andFbCityNameLessThan(String value) {
            addCriterion("fb_city_name <", value, "fbCityName");
            return (Criteria) this;
        }

        public Criteria andFbCityNameLessThanOrEqualTo(String value) {
            addCriterion("fb_city_name <=", value, "fbCityName");
            return (Criteria) this;
        }

        public Criteria andFbCityNameLike(String value) {
            addCriterion("fb_city_name like", value, "fbCityName");
            return (Criteria) this;
        }

        public Criteria andFbCityNameNotLike(String value) {
            addCriterion("fb_city_name not like", value, "fbCityName");
            return (Criteria) this;
        }

        public Criteria andFbCityNameIn(List<String> values) {
            addCriterion("fb_city_name in", values, "fbCityName");
            return (Criteria) this;
        }

        public Criteria andFbCityNameNotIn(List<String> values) {
            addCriterion("fb_city_name not in", values, "fbCityName");
            return (Criteria) this;
        }

        public Criteria andFbCityNameBetween(String value1, String value2) {
            addCriterion("fb_city_name between", value1, value2, "fbCityName");
            return (Criteria) this;
        }

        public Criteria andFbCityNameNotBetween(String value1, String value2) {
            addCriterion("fb_city_name not between", value1, value2, "fbCityName");
            return (Criteria) this;
        }

        public Criteria andFbCityCodeIsNull() {
            addCriterion("fb_city_code is null");
            return (Criteria) this;
        }

        public Criteria andFbCityCodeIsNotNull() {
            addCriterion("fb_city_code is not null");
            return (Criteria) this;
        }

        public Criteria andFbCityCodeEqualTo(String value) {
            addCriterion("fb_city_code =", value, "fbCityCode");
            return (Criteria) this;
        }

        public Criteria andFbCityCodeNotEqualTo(String value) {
            addCriterion("fb_city_code <>", value, "fbCityCode");
            return (Criteria) this;
        }

        public Criteria andFbCityCodeGreaterThan(String value) {
            addCriterion("fb_city_code >", value, "fbCityCode");
            return (Criteria) this;
        }

        public Criteria andFbCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("fb_city_code >=", value, "fbCityCode");
            return (Criteria) this;
        }

        public Criteria andFbCityCodeLessThan(String value) {
            addCriterion("fb_city_code <", value, "fbCityCode");
            return (Criteria) this;
        }

        public Criteria andFbCityCodeLessThanOrEqualTo(String value) {
            addCriterion("fb_city_code <=", value, "fbCityCode");
            return (Criteria) this;
        }

        public Criteria andFbCityCodeLike(String value) {
            addCriterion("fb_city_code like", value, "fbCityCode");
            return (Criteria) this;
        }

        public Criteria andFbCityCodeNotLike(String value) {
            addCriterion("fb_city_code not like", value, "fbCityCode");
            return (Criteria) this;
        }

        public Criteria andFbCityCodeIn(List<String> values) {
            addCriterion("fb_city_code in", values, "fbCityCode");
            return (Criteria) this;
        }

        public Criteria andFbCityCodeNotIn(List<String> values) {
            addCriterion("fb_city_code not in", values, "fbCityCode");
            return (Criteria) this;
        }

        public Criteria andFbCityCodeBetween(String value1, String value2) {
            addCriterion("fb_city_code between", value1, value2, "fbCityCode");
            return (Criteria) this;
        }

        public Criteria andFbCityCodeNotBetween(String value1, String value2) {
            addCriterion("fb_city_code not between", value1, value2, "fbCityCode");
            return (Criteria) this;
        }

        public Criteria andBedTypeIsNull() {
            addCriterion("bed_type is null");
            return (Criteria) this;
        }

        public Criteria andBedTypeIsNotNull() {
            addCriterion("bed_type is not null");
            return (Criteria) this;
        }

        public Criteria andBedTypeEqualTo(String value) {
            addCriterion("bed_type =", value, "bedType");
            return (Criteria) this;
        }

        public Criteria andBedTypeNotEqualTo(String value) {
            addCriterion("bed_type <>", value, "bedType");
            return (Criteria) this;
        }

        public Criteria andBedTypeGreaterThan(String value) {
            addCriterion("bed_type >", value, "bedType");
            return (Criteria) this;
        }

        public Criteria andBedTypeGreaterThanOrEqualTo(String value) {
            addCriterion("bed_type >=", value, "bedType");
            return (Criteria) this;
        }

        public Criteria andBedTypeLessThan(String value) {
            addCriterion("bed_type <", value, "bedType");
            return (Criteria) this;
        }

        public Criteria andBedTypeLessThanOrEqualTo(String value) {
            addCriterion("bed_type <=", value, "bedType");
            return (Criteria) this;
        }

        public Criteria andBedTypeLike(String value) {
            addCriterion("bed_type like", value, "bedType");
            return (Criteria) this;
        }

        public Criteria andBedTypeNotLike(String value) {
            addCriterion("bed_type not like", value, "bedType");
            return (Criteria) this;
        }

        public Criteria andBedTypeIn(List<String> values) {
            addCriterion("bed_type in", values, "bedType");
            return (Criteria) this;
        }

        public Criteria andBedTypeNotIn(List<String> values) {
            addCriterion("bed_type not in", values, "bedType");
            return (Criteria) this;
        }

        public Criteria andBedTypeBetween(String value1, String value2) {
            addCriterion("bed_type between", value1, value2, "bedType");
            return (Criteria) this;
        }

        public Criteria andBedTypeNotBetween(String value1, String value2) {
            addCriterion("bed_type not between", value1, value2, "bedType");
            return (Criteria) this;
        }

        public Criteria andRoomCodeIsNull() {
            addCriterion("room_code is null");
            return (Criteria) this;
        }

        public Criteria andRoomCodeIsNotNull() {
            addCriterion("room_code is not null");
            return (Criteria) this;
        }

        public Criteria andRoomCodeEqualTo(String value) {
            addCriterion("room_code =", value, "roomCode");
            return (Criteria) this;
        }

        public Criteria andRoomCodeNotEqualTo(String value) {
            addCriterion("room_code <>", value, "roomCode");
            return (Criteria) this;
        }

        public Criteria andRoomCodeGreaterThan(String value) {
            addCriterion("room_code >", value, "roomCode");
            return (Criteria) this;
        }

        public Criteria andRoomCodeGreaterThanOrEqualTo(String value) {
            addCriterion("room_code >=", value, "roomCode");
            return (Criteria) this;
        }

        public Criteria andRoomCodeLessThan(String value) {
            addCriterion("room_code <", value, "roomCode");
            return (Criteria) this;
        }

        public Criteria andRoomCodeLessThanOrEqualTo(String value) {
            addCriterion("room_code <=", value, "roomCode");
            return (Criteria) this;
        }

        public Criteria andRoomCodeLike(String value) {
            addCriterion("room_code like", value, "roomCode");
            return (Criteria) this;
        }

        public Criteria andRoomCodeNotLike(String value) {
            addCriterion("room_code not like", value, "roomCode");
            return (Criteria) this;
        }

        public Criteria andRoomCodeIn(List<String> values) {
            addCriterion("room_code in", values, "roomCode");
            return (Criteria) this;
        }

        public Criteria andRoomCodeNotIn(List<String> values) {
            addCriterion("room_code not in", values, "roomCode");
            return (Criteria) this;
        }

        public Criteria andRoomCodeBetween(String value1, String value2) {
            addCriterion("room_code between", value1, value2, "roomCode");
            return (Criteria) this;
        }

        public Criteria andRoomCodeNotBetween(String value1, String value2) {
            addCriterion("room_code not between", value1, value2, "roomCode");
            return (Criteria) this;
        }

        public Criteria andRoomNameIsNull() {
            addCriterion("room_name is null");
            return (Criteria) this;
        }

        public Criteria andRoomNameIsNotNull() {
            addCriterion("room_name is not null");
            return (Criteria) this;
        }

        public Criteria andRoomNameEqualTo(String value) {
            addCriterion("room_name =", value, "roomName");
            return (Criteria) this;
        }

        public Criteria andRoomNameNotEqualTo(String value) {
            addCriterion("room_name <>", value, "roomName");
            return (Criteria) this;
        }

        public Criteria andRoomNameGreaterThan(String value) {
            addCriterion("room_name >", value, "roomName");
            return (Criteria) this;
        }

        public Criteria andRoomNameGreaterThanOrEqualTo(String value) {
            addCriterion("room_name >=", value, "roomName");
            return (Criteria) this;
        }

        public Criteria andRoomNameLessThan(String value) {
            addCriterion("room_name <", value, "roomName");
            return (Criteria) this;
        }

        public Criteria andRoomNameLessThanOrEqualTo(String value) {
            addCriterion("room_name <=", value, "roomName");
            return (Criteria) this;
        }

        public Criteria andRoomNameLike(String value) {
            addCriterion("room_name like", value, "roomName");
            return (Criteria) this;
        }

        public Criteria andRoomNameNotLike(String value) {
            addCriterion("room_name not like", value, "roomName");
            return (Criteria) this;
        }

        public Criteria andRoomNameIn(List<String> values) {
            addCriterion("room_name in", values, "roomName");
            return (Criteria) this;
        }

        public Criteria andRoomNameNotIn(List<String> values) {
            addCriterion("room_name not in", values, "roomName");
            return (Criteria) this;
        }

        public Criteria andRoomNameBetween(String value1, String value2) {
            addCriterion("room_name between", value1, value2, "roomName");
            return (Criteria) this;
        }

        public Criteria andRoomNameNotBetween(String value1, String value2) {
            addCriterion("room_name not between", value1, value2, "roomName");
            return (Criteria) this;
        }

        public Criteria andPlanInfoIsNull() {
            addCriterion("plan_info is null");
            return (Criteria) this;
        }

        public Criteria andPlanInfoIsNotNull() {
            addCriterion("plan_info is not null");
            return (Criteria) this;
        }

        public Criteria andPlanInfoEqualTo(String value) {
            addCriterion("plan_info =", value, "planInfo");
            return (Criteria) this;
        }

        public Criteria andPlanInfoNotEqualTo(String value) {
            addCriterion("plan_info <>", value, "planInfo");
            return (Criteria) this;
        }

        public Criteria andPlanInfoGreaterThan(String value) {
            addCriterion("plan_info >", value, "planInfo");
            return (Criteria) this;
        }

        public Criteria andPlanInfoGreaterThanOrEqualTo(String value) {
            addCriterion("plan_info >=", value, "planInfo");
            return (Criteria) this;
        }

        public Criteria andPlanInfoLessThan(String value) {
            addCriterion("plan_info <", value, "planInfo");
            return (Criteria) this;
        }

        public Criteria andPlanInfoLessThanOrEqualTo(String value) {
            addCriterion("plan_info <=", value, "planInfo");
            return (Criteria) this;
        }

        public Criteria andPlanInfoLike(String value) {
            addCriterion("plan_info like", value, "planInfo");
            return (Criteria) this;
        }

        public Criteria andPlanInfoNotLike(String value) {
            addCriterion("plan_info not like", value, "planInfo");
            return (Criteria) this;
        }

        public Criteria andPlanInfoIn(List<String> values) {
            addCriterion("plan_info in", values, "planInfo");
            return (Criteria) this;
        }

        public Criteria andPlanInfoNotIn(List<String> values) {
            addCriterion("plan_info not in", values, "planInfo");
            return (Criteria) this;
        }

        public Criteria andPlanInfoBetween(String value1, String value2) {
            addCriterion("plan_info between", value1, value2, "planInfo");
            return (Criteria) this;
        }

        public Criteria andPlanInfoNotBetween(String value1, String value2) {
            addCriterion("plan_info not between", value1, value2, "planInfo");
            return (Criteria) this;
        }

        public Criteria andCustomerInfoIsNull() {
            addCriterion("customer_info is null");
            return (Criteria) this;
        }

        public Criteria andCustomerInfoIsNotNull() {
            addCriterion("customer_info is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerInfoEqualTo(String value) {
            addCriterion("customer_info =", value, "customerInfo");
            return (Criteria) this;
        }

        public Criteria andCustomerInfoNotEqualTo(String value) {
            addCriterion("customer_info <>", value, "customerInfo");
            return (Criteria) this;
        }

        public Criteria andCustomerInfoGreaterThan(String value) {
            addCriterion("customer_info >", value, "customerInfo");
            return (Criteria) this;
        }

        public Criteria andCustomerInfoGreaterThanOrEqualTo(String value) {
            addCriterion("customer_info >=", value, "customerInfo");
            return (Criteria) this;
        }

        public Criteria andCustomerInfoLessThan(String value) {
            addCriterion("customer_info <", value, "customerInfo");
            return (Criteria) this;
        }

        public Criteria andCustomerInfoLessThanOrEqualTo(String value) {
            addCriterion("customer_info <=", value, "customerInfo");
            return (Criteria) this;
        }

        public Criteria andCustomerInfoLike(String value) {
            addCriterion("customer_info like", value, "customerInfo");
            return (Criteria) this;
        }

        public Criteria andCustomerInfoNotLike(String value) {
            addCriterion("customer_info not like", value, "customerInfo");
            return (Criteria) this;
        }

        public Criteria andCustomerInfoIn(List<String> values) {
            addCriterion("customer_info in", values, "customerInfo");
            return (Criteria) this;
        }

        public Criteria andCustomerInfoNotIn(List<String> values) {
            addCriterion("customer_info not in", values, "customerInfo");
            return (Criteria) this;
        }

        public Criteria andCustomerInfoBetween(String value1, String value2) {
            addCriterion("customer_info between", value1, value2, "customerInfo");
            return (Criteria) this;
        }

        public Criteria andCustomerInfoNotBetween(String value1, String value2) {
            addCriterion("customer_info not between", value1, value2, "customerInfo");
            return (Criteria) this;
        }

        public Criteria andCheckInDateIsNull() {
            addCriterion("check_in_date is null");
            return (Criteria) this;
        }

        public Criteria andCheckInDateIsNotNull() {
            addCriterion("check_in_date is not null");
            return (Criteria) this;
        }

        public Criteria andCheckInDateEqualTo(Date value) {
            addCriterion("check_in_date =", value, "checkInDate");
            return (Criteria) this;
        }

        public Criteria andCheckInDateNotEqualTo(Date value) {
            addCriterion("check_in_date <>", value, "checkInDate");
            return (Criteria) this;
        }

        public Criteria andCheckInDateGreaterThan(Date value) {
            addCriterion("check_in_date >", value, "checkInDate");
            return (Criteria) this;
        }

        public Criteria andCheckInDateGreaterThanOrEqualTo(Date value) {
            addCriterion("check_in_date >=", value, "checkInDate");
            return (Criteria) this;
        }

        public Criteria andCheckInDateLessThan(Date value) {
            addCriterion("check_in_date <", value, "checkInDate");
            return (Criteria) this;
        }

        public Criteria andCheckInDateLessThanOrEqualTo(Date value) {
            addCriterion("check_in_date <=", value, "checkInDate");
            return (Criteria) this;
        }

        public Criteria andCheckInDateIn(List<Date> values) {
            addCriterion("check_in_date in", values, "checkInDate");
            return (Criteria) this;
        }

        public Criteria andCheckInDateNotIn(List<Date> values) {
            addCriterion("check_in_date not in", values, "checkInDate");
            return (Criteria) this;
        }

        public Criteria andCheckInDateBetween(Date value1, Date value2) {
            addCriterion("check_in_date between", value1, value2, "checkInDate");
            return (Criteria) this;
        }

        public Criteria andCheckInDateNotBetween(Date value1, Date value2) {
            addCriterion("check_in_date not between", value1, value2, "checkInDate");
            return (Criteria) this;
        }

        public Criteria andCheckOutDateIsNull() {
            addCriterion("check_out_date is null");
            return (Criteria) this;
        }

        public Criteria andCheckOutDateIsNotNull() {
            addCriterion("check_out_date is not null");
            return (Criteria) this;
        }

        public Criteria andCheckOutDateEqualTo(Date value) {
            addCriterion("check_out_date =", value, "checkOutDate");
            return (Criteria) this;
        }

        public Criteria andCheckOutDateNotEqualTo(Date value) {
            addCriterion("check_out_date <>", value, "checkOutDate");
            return (Criteria) this;
        }

        public Criteria andCheckOutDateGreaterThan(Date value) {
            addCriterion("check_out_date >", value, "checkOutDate");
            return (Criteria) this;
        }

        public Criteria andCheckOutDateGreaterThanOrEqualTo(Date value) {
            addCriterion("check_out_date >=", value, "checkOutDate");
            return (Criteria) this;
        }

        public Criteria andCheckOutDateLessThan(Date value) {
            addCriterion("check_out_date <", value, "checkOutDate");
            return (Criteria) this;
        }

        public Criteria andCheckOutDateLessThanOrEqualTo(Date value) {
            addCriterion("check_out_date <=", value, "checkOutDate");
            return (Criteria) this;
        }

        public Criteria andCheckOutDateIn(List<Date> values) {
            addCriterion("check_out_date in", values, "checkOutDate");
            return (Criteria) this;
        }

        public Criteria andCheckOutDateNotIn(List<Date> values) {
            addCriterion("check_out_date not in", values, "checkOutDate");
            return (Criteria) this;
        }

        public Criteria andCheckOutDateBetween(Date value1, Date value2) {
            addCriterion("check_out_date between", value1, value2, "checkOutDate");
            return (Criteria) this;
        }

        public Criteria andCheckOutDateNotBetween(Date value1, Date value2) {
            addCriterion("check_out_date not between", value1, value2, "checkOutDate");
            return (Criteria) this;
        }

        public Criteria andErrMsgIsNull() {
            addCriterion("err_msg is null");
            return (Criteria) this;
        }

        public Criteria andErrMsgIsNotNull() {
            addCriterion("err_msg is not null");
            return (Criteria) this;
        }

        public Criteria andErrMsgEqualTo(String value) {
            addCriterion("err_msg =", value, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgNotEqualTo(String value) {
            addCriterion("err_msg <>", value, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgGreaterThan(String value) {
            addCriterion("err_msg >", value, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgGreaterThanOrEqualTo(String value) {
            addCriterion("err_msg >=", value, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgLessThan(String value) {
            addCriterion("err_msg <", value, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgLessThanOrEqualTo(String value) {
            addCriterion("err_msg <=", value, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgLike(String value) {
            addCriterion("err_msg like", value, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgNotLike(String value) {
            addCriterion("err_msg not like", value, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgIn(List<String> values) {
            addCriterion("err_msg in", values, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgNotIn(List<String> values) {
            addCriterion("err_msg not in", values, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgBetween(String value1, String value2) {
            addCriterion("err_msg between", value1, value2, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgNotBetween(String value1, String value2) {
            addCriterion("err_msg not between", value1, value2, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrCodeIsNull() {
            addCriterion("err_code is null");
            return (Criteria) this;
        }

        public Criteria andErrCodeIsNotNull() {
            addCriterion("err_code is not null");
            return (Criteria) this;
        }

        public Criteria andErrCodeEqualTo(Integer value) {
            addCriterion("err_code =", value, "errCode");
            return (Criteria) this;
        }

        public Criteria andErrCodeNotEqualTo(Integer value) {
            addCriterion("err_code <>", value, "errCode");
            return (Criteria) this;
        }

        public Criteria andErrCodeGreaterThan(Integer value) {
            addCriterion("err_code >", value, "errCode");
            return (Criteria) this;
        }

        public Criteria andErrCodeGreaterThanOrEqualTo(Integer value) {
            addCriterion("err_code >=", value, "errCode");
            return (Criteria) this;
        }

        public Criteria andErrCodeLessThan(Integer value) {
            addCriterion("err_code <", value, "errCode");
            return (Criteria) this;
        }

        public Criteria andErrCodeLessThanOrEqualTo(Integer value) {
            addCriterion("err_code <=", value, "errCode");
            return (Criteria) this;
        }

        public Criteria andErrCodeIn(List<Integer> values) {
            addCriterion("err_code in", values, "errCode");
            return (Criteria) this;
        }

        public Criteria andErrCodeNotIn(List<Integer> values) {
            addCriterion("err_code not in", values, "errCode");
            return (Criteria) this;
        }

        public Criteria andErrCodeBetween(Integer value1, Integer value2) {
            addCriterion("err_code between", value1, value2, "errCode");
            return (Criteria) this;
        }

        public Criteria andErrCodeNotBetween(Integer value1, Integer value2) {
            addCriterion("err_code not between", value1, value2, "errCode");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdIsNull() {
            addCriterion("cost_center_id is null");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdIsNotNull() {
            addCriterion("cost_center_id is not null");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdEqualTo(String value) {
            addCriterion("cost_center_id =", value, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdNotEqualTo(String value) {
            addCriterion("cost_center_id <>", value, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdGreaterThan(String value) {
            addCriterion("cost_center_id >", value, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdGreaterThanOrEqualTo(String value) {
            addCriterion("cost_center_id >=", value, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdLessThan(String value) {
            addCriterion("cost_center_id <", value, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdLessThanOrEqualTo(String value) {
            addCriterion("cost_center_id <=", value, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdLike(String value) {
            addCriterion("cost_center_id like", value, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdNotLike(String value) {
            addCriterion("cost_center_id not like", value, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdIn(List<String> values) {
            addCriterion("cost_center_id in", values, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdNotIn(List<String> values) {
            addCriterion("cost_center_id not in", values, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdBetween(String value1, String value2) {
            addCriterion("cost_center_id between", value1, value2, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdNotBetween(String value1, String value2) {
            addCriterion("cost_center_id not between", value1, value2, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeIsNull() {
            addCriterion("cost_center_type is null");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeIsNotNull() {
            addCriterion("cost_center_type is not null");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeEqualTo(Integer value) {
            addCriterion("cost_center_type =", value, "costCenterType");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeNotEqualTo(Integer value) {
            addCriterion("cost_center_type <>", value, "costCenterType");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeGreaterThan(Integer value) {
            addCriterion("cost_center_type >", value, "costCenterType");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("cost_center_type >=", value, "costCenterType");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeLessThan(Integer value) {
            addCriterion("cost_center_type <", value, "costCenterType");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeLessThanOrEqualTo(Integer value) {
            addCriterion("cost_center_type <=", value, "costCenterType");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeIn(List<Integer> values) {
            addCriterion("cost_center_type in", values, "costCenterType");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeNotIn(List<Integer> values) {
            addCriterion("cost_center_type not in", values, "costCenterType");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeBetween(Integer value1, Integer value2) {
            addCriterion("cost_center_type between", value1, value2, "costCenterType");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("cost_center_type not between", value1, value2, "costCenterType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeIsNull() {
            addCriterion("exceed_buy_type is null");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeIsNotNull() {
            addCriterion("exceed_buy_type is not null");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeEqualTo(Integer value) {
            addCriterion("exceed_buy_type =", value, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeNotEqualTo(Integer value) {
            addCriterion("exceed_buy_type <>", value, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeGreaterThan(Integer value) {
            addCriterion("exceed_buy_type >", value, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("exceed_buy_type >=", value, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeLessThan(Integer value) {
            addCriterion("exceed_buy_type <", value, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeLessThanOrEqualTo(Integer value) {
            addCriterion("exceed_buy_type <=", value, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeIn(List<Integer> values) {
            addCriterion("exceed_buy_type in", values, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeNotIn(List<Integer> values) {
            addCriterion("exceed_buy_type not in", values, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeBetween(Integer value1, Integer value2) {
            addCriterion("exceed_buy_type between", value1, value2, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("exceed_buy_type not between", value1, value2, "exceedBuyType");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table hotel_intercept_record
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table hotel_intercept_record
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}