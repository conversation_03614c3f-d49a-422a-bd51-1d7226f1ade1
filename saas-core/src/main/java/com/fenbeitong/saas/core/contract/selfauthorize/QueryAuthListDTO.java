package com.fenbeitong.saas.core.contract.selfauthorize;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

@Data
@Builder
public class QueryAuthListDTO {

    @Tolerate
    public QueryAuthListDTO(){}

    private Integer viewType;

    private String companyId;

    private String employeeId;

    private Integer authPermissionType;

    private Integer authState;

    private Integer authSceneCode;

    private Integer sortType;

    private boolean paginationRequired;

    private Integer pageSize;

    private Integer pageCount;

    ResponsePaginationRequired responsePaginationRequired;
}
