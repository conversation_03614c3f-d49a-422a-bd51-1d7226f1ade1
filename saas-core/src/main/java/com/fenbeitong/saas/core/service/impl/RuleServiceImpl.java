package com.fenbeitong.saas.core.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.common.utils.basis.Objects;
import com.fenbeitong.finhub.common.constant.CategoryTypeEnum;
import com.fenbeitong.finhub.common.constant.LogOperateActionEnum;
import com.fenbeitong.finhub.common.constant.LogOperateObjectEnum;
import com.fenbeitong.finhub.common.constant.LogOperatePageEnum;
import com.fenbeitong.finhub.common.constant.UsePersonalBudgetTypeEnum;
import com.fenbeitong.harmony.city.contrail.dto.city.FullPathAreaDTO;
import com.fenbeitong.saas.api.model.dto.rule.EmployeeTaxiRuleInfo;
import com.fenbeitong.saas.api.model.dto.rule.RuleTimeRange;
import com.fenbeitong.saas.api.model.dto.template.rule.ControlOrderRuleDto;
import com.fenbeitong.saas.core.common.base.SaasException;
import com.fenbeitong.saas.core.common.constant.CategorySubType;
import com.fenbeitong.saas.core.common.constant.GlobalResponseCode;
import com.fenbeitong.saas.core.common.constant.TaxiRuleConstant;
import com.fenbeitong.saas.core.common.constant.TemporaryResponseCode;
import com.fenbeitong.saas.core.common.function.GenCategory;
import com.fenbeitong.saas.core.common.function.GenMallCategory;
import com.fenbeitong.saas.core.common.function.GenNewCategory;
import com.fenbeitong.saas.core.contract.applyflow.KvContract;
import com.fenbeitong.saas.core.contract.customrole.EmployeeListContract;
import com.fenbeitong.saas.core.contract.mall.SkuInfoContract;
import com.fenbeitong.saas.core.contract.order.check.CompanyCouponInfo;
import com.fenbeitong.saas.core.contract.order.check.CouponSort;
import com.fenbeitong.saas.core.contract.order.check.TaxiOrderCheckRule;
import com.fenbeitong.saas.core.contract.organization.AllEmployeeOfCompanyContract;
import com.fenbeitong.saas.core.contract.organization.SetRulesReqContract;
import com.fenbeitong.saas.core.contract.organization.inner.AirPolicyBean;
import com.fenbeitong.saas.core.contract.organization.inner.BizTripPolicyBean;
import com.fenbeitong.saas.core.contract.organization.inner.CarPolicyBean;
import com.fenbeitong.saas.core.contract.organization.inner.DinnerPolicyBean;
import com.fenbeitong.saas.core.contract.organization.inner.HotelPolicyBean;
import com.fenbeitong.saas.core.contract.organization.inner.IntlAirPolicyBean;
import com.fenbeitong.saas.core.contract.organization.inner.MallPolicyBean;
import com.fenbeitong.saas.core.contract.organization.inner.TrainPolicyBean;
import com.fenbeitong.saas.core.contract.rule.AuthorizationEmployeeContract;
import com.fenbeitong.saas.core.contract.rule.AuthorizationEmployeeV2Contract;
import com.fenbeitong.saas.core.contract.rule.HotelGroupPriceVO;
import com.fenbeitong.saas.core.contract.rule.HotelRuleContract;
import com.fenbeitong.saas.core.contract.rule.MallCategoryContract;
import com.fenbeitong.saas.core.contract.rule.MallCategoryNewContract;
import com.fenbeitong.saas.core.contract.rule.MallMatchEmployeeContract;
import com.fenbeitong.saas.core.contract.rule.MallRuleContract;
import com.fenbeitong.saas.core.contract.rule.MallRuleDeleteContract;
import com.fenbeitong.saas.core.contract.rule.MallRuleDetailContract;
import com.fenbeitong.saas.core.contract.rule.MallRuleDetailNewContract;
import com.fenbeitong.saas.core.contract.rule.MallRuleDetailV3Contract;
import com.fenbeitong.saas.core.contract.rule.MyRuleDisployContract;
import com.fenbeitong.saas.core.contract.rule.PriceLimitPayTips;
import com.fenbeitong.saas.core.contract.rule.RuleDeleteContract;
import com.fenbeitong.saas.core.contract.rule.RuleResponseContract;
import com.fenbeitong.saas.core.contract.rule.SecondCategoryContract;
import com.fenbeitong.saas.core.contract.rule.TaxiLimitPayTipContract;
import com.fenbeitong.saas.core.contract.rule.TaxiLocationContract;
import com.fenbeitong.saas.core.contract.rule.TaxiLocationDetailContract;
import com.fenbeitong.saas.core.contract.rule.TaxiRuleContract;
import com.fenbeitong.saas.core.contract.rule.TaxiRuleFullContract;
import com.fenbeitong.saas.core.contract.rule.TaxiRuleListContract;
import com.fenbeitong.saas.core.contract.rule.TaxiRulePath;
import com.fenbeitong.saas.core.contract.user.EmployeeInfoContract;
import com.fenbeitong.saas.core.contract.user.UserAuthWithVerify;
import com.fenbeitong.saas.core.contract.user.UserBizAuthWithVerify;
import com.fenbeitong.saas.core.dao.fenbeitong.AirRuleMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.AreaMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.BizTripRuleMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.BusRuleMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.CompanyAreaLevelGroupMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.CompanyAreaLevelMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.DinnerRuleMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.DinnerTimeRangeMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.HotelRuleGroupDetailMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.HotelRuleMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.IntlAirRuleMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.LocationCollectionMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.MallRuleMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.MallRuleSkuMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.OrgUnitEmployeeSimpleMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.OrgUnitMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.RuleMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.TakeawayTimeRangeExtMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.TaxiLocationMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.TaxiPathLocationMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.TaxiRuleMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.TaxiRuleMapperExt;
import com.fenbeitong.saas.core.dao.fenbeitong.TaxiTimeRangeExtMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.TaxiTimeRangeMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.TimeRangeExtMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.TrainRuleMapper;
import com.fenbeitong.saas.core.model.enums.apply.ApplyType;
import com.fenbeitong.saas.core.model.enums.message.BizType;
import com.fenbeitong.saas.core.model.enums.rule.AirCabinType;
import com.fenbeitong.saas.core.model.enums.rule.AirPortCityEnum;
import com.fenbeitong.saas.core.model.enums.rule.AirRuleType;
import com.fenbeitong.saas.core.model.enums.rule.AllowedTaxiType;
import com.fenbeitong.saas.core.model.enums.rule.AuthorizationType;
import com.fenbeitong.saas.core.model.enums.rule.DayTempType;
import com.fenbeitong.saas.core.model.enums.rule.DinnerRuleType;
import com.fenbeitong.saas.core.model.enums.rule.ExceedAllowType;
import com.fenbeitong.saas.core.model.enums.rule.FilterStopoverFlightFlag;
import com.fenbeitong.saas.core.model.enums.rule.FixedPathLocationEnum;
import com.fenbeitong.saas.core.model.enums.rule.HotelLevel;
import com.fenbeitong.saas.core.model.enums.rule.HotelPriceLimitType;
import com.fenbeitong.saas.core.model.enums.rule.HotelPriceSetType;
import com.fenbeitong.saas.core.model.enums.rule.HotelRuleType;
import com.fenbeitong.saas.core.model.enums.rule.MallRuleType;
import com.fenbeitong.saas.core.model.enums.rule.NeedApplyType;
import com.fenbeitong.saas.core.model.enums.rule.PathLocationTypeEnum;
import com.fenbeitong.saas.core.model.enums.rule.RuleType;
import com.fenbeitong.saas.core.model.enums.rule.TakeawayRuleType;
import com.fenbeitong.saas.core.model.enums.rule.TaxiApprovePriceLimitType;
import com.fenbeitong.saas.core.model.enums.rule.TaxiLocationType;
import com.fenbeitong.saas.core.model.enums.rule.TaxiModelsType;
import com.fenbeitong.saas.core.model.enums.rule.TaxiRuleType;
import com.fenbeitong.saas.core.model.enums.rule.TrainRuleType;
import com.fenbeitong.saas.core.model.enums.rule.TrainSeatType;
import com.fenbeitong.saas.core.model.enums.rule.TravelType;
import com.fenbeitong.saas.core.model.enums.rule.taxi.AllowSameCityLimitType;
import com.fenbeitong.saas.core.model.fenbeitong.AirRule;
import com.fenbeitong.saas.core.model.fenbeitong.AirRuleExample;
import com.fenbeitong.saas.core.model.fenbeitong.Area;
import com.fenbeitong.saas.core.model.fenbeitong.AreaExample;
import com.fenbeitong.saas.core.model.fenbeitong.AreaPath;
import com.fenbeitong.saas.core.model.fenbeitong.BizTripRule;
import com.fenbeitong.saas.core.model.fenbeitong.BizTripRuleExample;
import com.fenbeitong.saas.core.model.fenbeitong.BusRule;
import com.fenbeitong.saas.core.model.fenbeitong.BusinessRule;
import com.fenbeitong.saas.core.model.fenbeitong.CompanyAreaLevel;
import com.fenbeitong.saas.core.model.fenbeitong.CompanyAreaLevelExample;
import com.fenbeitong.saas.core.model.fenbeitong.CompanyAreaLevelGroup;
import com.fenbeitong.saas.core.model.fenbeitong.CompanyAreaLevelGroupExample;
import com.fenbeitong.saas.core.model.fenbeitong.DinnerRule;
import com.fenbeitong.saas.core.model.fenbeitong.DinnerRuleExample;
import com.fenbeitong.saas.core.model.fenbeitong.DinnerTimeRangeExample;
import com.fenbeitong.saas.core.model.fenbeitong.HotelRule;
import com.fenbeitong.saas.core.model.fenbeitong.HotelRuleDetailDTO;
import com.fenbeitong.saas.core.model.fenbeitong.HotelRuleExample;
import com.fenbeitong.saas.core.model.fenbeitong.HotelRuleGroupDetail;
import com.fenbeitong.saas.core.model.fenbeitong.HotelRuleGroupDetailExample;
import com.fenbeitong.saas.core.model.fenbeitong.IntlAirRule;
import com.fenbeitong.saas.core.model.fenbeitong.LocationCollectionExample;
import com.fenbeitong.saas.core.model.fenbeitong.MallRule;
import com.fenbeitong.saas.core.model.fenbeitong.MallRuleExample;
import com.fenbeitong.saas.core.model.fenbeitong.MallRuleSku;
import com.fenbeitong.saas.core.model.fenbeitong.MallRuleSkuExample;
import com.fenbeitong.saas.core.model.fenbeitong.OrgUnit;
import com.fenbeitong.saas.core.model.fenbeitong.OrgUnitEmployeeSimple;
import com.fenbeitong.saas.core.model.fenbeitong.OrgUnitEmployeeSimpleExample;
import com.fenbeitong.saas.core.model.fenbeitong.OrgUnitExample;
import com.fenbeitong.saas.core.model.fenbeitong.TaxiLocation;
import com.fenbeitong.saas.core.model.fenbeitong.TaxiLocationExample;
import com.fenbeitong.saas.core.model.fenbeitong.TaxiPathLocation;
import com.fenbeitong.saas.core.model.fenbeitong.TaxiPathLocationExample;
import com.fenbeitong.saas.core.model.fenbeitong.TaxiRule;
import com.fenbeitong.saas.core.model.fenbeitong.TaxiRuleExample;
import com.fenbeitong.saas.core.model.fenbeitong.TaxiTimeRange;
import com.fenbeitong.saas.core.model.fenbeitong.TaxiTimeRangeExample;
import com.fenbeitong.saas.core.model.fenbeitong.TimeRange;
import com.fenbeitong.saas.core.model.fenbeitong.TrainRule;
import com.fenbeitong.saas.core.model.fenbeitong.TrainRuleExample;
import com.fenbeitong.saas.core.model.saas.ApplyOrder;
import com.fenbeitong.saas.core.model.saas.OperateLogRule;
import com.fenbeitong.saas.core.service.IAirRuleService;
import com.fenbeitong.saas.core.service.IBaseOrganizationService;
import com.fenbeitong.saas.core.service.ICityService;
import com.fenbeitong.saas.core.service.IOperateLogRuleService;
import com.fenbeitong.saas.core.service.IOrderCheckService;
import com.fenbeitong.saas.core.service.IOrganizationService;
import com.fenbeitong.saas.core.service.IRuleService;
import com.fenbeitong.saas.core.service.ITaxiRuleService;
import com.fenbeitong.saas.core.service.ITrainRuleService;
import com.fenbeitong.saas.core.service.IVendorHubService;
import com.fenbeitong.saas.core.service.kafka.CompanyLogKafkaProducerService;
import com.fenbeitong.saas.core.service.rule.multi.ConsumeTemplateRuleService;
import com.fenbeitong.saas.core.service.taxi.inner.TaxiRedisControlService;
import com.fenbeitong.saas.core.utils.constant.MallConstant;
import com.fenbeitong.saas.core.utils.tools.BigDecimalTool;
import com.fenbeitong.saas.core.utils.tools.CompanyLogMsgUtil;
import com.fenbeitong.saas.core.utils.tools.DateTimeTool;
import com.fenbeitong.saas.core.utils.tools.HostPropertyConfigTool;
import com.fenbeitong.saas.core.utils.tools.HttpException;
import com.fenbeitong.saas.core.utils.tools.HttpTool;
import com.fenbeitong.saas.core.utils.tools.MySortList;
import com.fenbeitong.saas.core.utils.tools.VersionTool;
import com.fenbeitong.saas.core.utils.transfer.RuleTransferUtil;
import com.fenbeitong.saasplus.api.model.dto.custform.CustomFormTotalEstimatedOptionDTO;
import com.fenbeitong.saasplus.api.model.dto.hotel.CompanyAreaLevelGroupDTO;
import com.fenbeitong.saasplus.api.model.dto.hotel.CompanyAreaLevelGroupDetailDTO;
import com.fenbeitong.saasplus.api.model.dto.rule.TakeawayAuthContract;
import com.fenbeitong.saasplus.api.model.dto.rule.TakeawayContract;
import com.fenbeitong.saasplus.api.model.dto.rule.TaxiApproveRuleContract;
import com.fenbeitong.saasplus.api.model.enums.rule.DayPriceLimitTypeEnum;
import com.fenbeitong.saasplus.api.model.po.fenbeitong.TakeawayRule;
import com.fenbeitong.saasplus.api.model.vo.rule.TaxiExceedConfigQueryRes;
import com.fenbeitong.saasplus.api.service.custform.ICustomFormService;
import com.fenbeitong.saasplus.api.service.hotel.IHotelGroupAreaService;
import com.fenbeitong.saasplus.api.service.messageSetup.IMessageSetupService;
import com.fenbeitong.saasplus.api.service.rule.IBaseTakeawayRuleExtService;
import com.fenbeitong.saasplus.api.service.rule.IBaseTakeawayRuleService;
import com.fenbeitong.saasplus.api.service.rule.IHotelGroupRuleService;
import com.fenbeitong.saasplus.api.service.rule.IRuleV2Service;
import com.fenbeitong.saasplus.api.service.rule.ITaxiApproveRuleService;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyRuleDTO;
import com.fenbeitong.usercenter.api.model.dto.company.SceneAuthDto;
import com.fenbeitong.usercenter.api.model.dto.company.ShansongAuthDto;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.fenbeitong.usercenter.api.model.dto.employee.OrgUnitEmployeeSimpleDTO;
import com.fenbeitong.usercenter.api.model.dto.rule.EmployeeAirRuleContract;
import com.fenbeitong.usercenter.api.model.dto.rule.EmployeeBusRuleDto;
import com.fenbeitong.usercenter.api.model.dto.rule.EmployeeHotelRuleContract;
import com.fenbeitong.usercenter.api.model.dto.rule.EmployeeMallRuleDto;
import com.fenbeitong.usercenter.api.model.dto.rule.EmployeeMeishiRuleDto;
import com.fenbeitong.usercenter.api.model.dto.rule.EmployeeTaxiRuleContract;
import com.fenbeitong.usercenter.api.model.dto.rule.EmployeeTaxiRuleDto;
import com.fenbeitong.usercenter.api.model.dto.rule.EmployeeTaxiRuleUpdateApplyDTO;
import com.fenbeitong.usercenter.api.model.dto.rule.EmployeeTrainRuleContract;
import com.fenbeitong.usercenter.api.model.dto.rule.RuleIdDto;
import com.fenbeitong.usercenter.api.model.enums.privilege.CodeTypeEnums;
import com.fenbeitong.usercenter.api.model.enums.privilege.CustomCodeType;
import com.fenbeitong.usercenter.api.model.po.company.Company;
import com.fenbeitong.usercenter.api.model.po.company.CompanyRuleExample;
import com.fenbeitong.usercenter.api.model.po.rule.*;
import com.fenbeitong.usercenter.api.service.company.ICompanyRuleService;
import com.fenbeitong.usercenter.api.service.company.ICompanyService;
import com.fenbeitong.usercenter.api.service.employee.IBaseEmployeeExtService;
import com.fenbeitong.usercenter.api.service.orgunit.IOrgUnitService;
import com.fenbeitong.usercenter.api.service.privilege.IPrivilegeService;
import com.fenbeitong.usercenter.api.service.rule.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.DateUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.StrUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toCollection;
import static java.util.stream.Collectors.toMap;

/**
 * Created by chenshang on 2017/5/20.
 */
@Slf4j
@Service
public class RuleServiceImpl implements IRuleService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RuleServiceImpl.class);


    @Resource
    OrgUnitMapper orgUnitMapper;
    @Resource
    OrgUnitEmployeeSimpleMapper orgUnitEmployeeSimpleMapper;
    @Autowired
    IOrganizationService organizationService;
    @Autowired
    RuleMapper ruleMapper;
    @Autowired
    TaxiRuleMapper taxiRuleMapper;
    @Autowired
    TaxiLocationMapper taxiLocationMapper;
    @Autowired
    TaxiTimeRangeMapper taxiTimeRangeMapper;
    @Resource
    AirRuleMapper airRuleMapper;
    @Resource
    HotelRuleMapper hotelRuleMapper;
    @Resource
    TrainRuleMapper trainRuleMapper;
    @Autowired
    BizTripRuleMapper bizTripRuleMapper;
    @Autowired
    MallRuleMapper mallRuleMapper;
    @Autowired
    DinnerRuleMapper dinnerRuleMapper;
    @Autowired
    IVendorHubService vendorHubService;
    @Autowired
    TaxiRuleMapperExt taxiRuleMapperExt;
    @Autowired
    ICompanyRuleService iCompanyRuleService;
    @Autowired
    CompanyAreaLevelMapper companyAreaLevelMapper;
    @Autowired
    AreaMapper areaMapper;
    @Autowired
    DinnerTimeRangeMapper dinnerTimeRangeMapper;
    @Autowired(required = true)
    private IAirRuleService airRuleService;
    @Autowired
    private ITrainRuleService trainRuleService;
    @Autowired
    MallRuleSkuMapper mallRuleSkuMapper;
    @Autowired
    TaxiTimeRangeExtMapper taxiTimeRangeExtMapper;
    @Autowired
    private TimeRangeExtMapper timeRangeExtMapper;
    @Autowired
    private IntlAirRuleMapper intlAirRuleMapper;
    @Autowired
    private IBaseEmployeeDinnerRuleExtService iBaseEmployeeDinnerRuleExtService;
    @Autowired
    private IBaseEmployeeDinnerRuleService iBaseEmployeeDinnerRuleService;
    @Autowired
    private IBaseEmployeeAirRuleService iBaseEmployeeAirRuleService;
    @Autowired
    private IBaseEmployeeAirRuleExtService iBaseEmployeeAirRuleExtService;
    @Autowired
    private IBaseEmployeeIntlAirRuleService iBaseEmployeeIntlAirRuleService;
    @Autowired
    private IBaseEmployeeIntlAirRuleExtService iBaseEmployeeIntlAirRuleExtService;
    @Autowired
    private IBaseEmployeeHotelRuleService iBaseEmployeeHotelRuleService;
    @Autowired
    private IBaseEmployeeHotelRuleExtService iBaseEmployeeHotelRuleExtService;
    @Autowired
    private IBaseEmployeeTrainRuleService iBaseEmployeeTrainRuleService;
    @Autowired
    private IBaseEmployeeTrainRuleExtService iBaseEmployeeTrainRuleExtService;
    @Autowired
    private IBaseEmployeeMallRuleExtService iBaseEmployeeMallRuleExtService;
    @Autowired
    private IBaseEmployeeMallRuleService iBaseEmployeeMallRuleService;
    @Autowired
    private IBaseEmployeeTaxiRuleService iBaseEmployeeTaxiRuleService;
    @Autowired
    private IBaseEmployeeTaxiRuleExtService iBaseEmployeeTaxiRuleExtService;
    @Autowired
    private IBaseOrganizationService iBaseOrganizationService;
    @Autowired
    private ITaxiRuleService iTaxiRuleService;
    @Autowired
    private IBaseEmployeeTakeawayRuleService iBaseEmployeeTakeawayRuleService;
    @Autowired
    private IBaseTakeawayRuleService iBaseTakeawayRuleService;
    @Autowired
    private IBaseTakeawayRuleExtService iBaseTakeawayRuleExtService;
    @Autowired
    private TakeawayTimeRangeExtMapper takeawayTimeRangeExtMapper;
    @Autowired
    private ITaxiApproveRuleService iTaxiApproveRuleService;
    @Autowired
    private ICompanyService iCompanyService;
    @Autowired
    private ICommonEmployeeRuleService iCommonEmployeeRuleService;

    @Autowired
    private IOperateLogRuleService operateLogRuleService;

    @Autowired
    private TaxiPathLocationMapper taxiPathLocationMapper;

    @Autowired
    private CompanyLogKafkaProducerService companyLogKafkaProducerService;

    @Autowired
    private IPrivilegeService iPrivilegeService;

    @Autowired
    private IBaseEmployeeMeishiRuleExtService iBaseEmployeeMeishiRuleExtService;

    @Autowired
    private BusRuleMapper busRuleMapper;

    @Autowired
    private IBaseEmployeeBusRuleService iBaseEmployeeBusRuleService;

    @Autowired
    private IOrgUnitService orgUnitService;

    @Autowired
    private IHotelGroupRuleService iHotelGroupRuleService;

    @Autowired
    private IHotelGroupAreaService iHotelGroupAreaService;

    @Autowired
    private HotelRuleGroupDetailMapper hotelRuleGroupDetailMapper;

    @Autowired
    private CompanyAreaLevelGroupMapper companyAreaLevelGroupMapper;

    @Autowired
    private IOrderCheckService iOrderCheckService;
    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyOrderMapper applyOrderMapper;

    @Autowired
    private IBaseEmployeeExtService iBaseEmployeeExtService;

    @Autowired
    private ICityService iCityService;
    @Autowired
    private ConsumeTemplateRuleService consumeTemplateRuleService;

    @Resource
    ICustomFormService iCustomFormService;

    @Resource
    private TaxiRedisControlService taxiRedisControlService;

    @Autowired
    private IMessageSetupService iMessageSetupRpcService;

    @Autowired
    private IRuleV2Service iRuleV2Service;

    @Autowired
    private IBaseEmployeeRuleService iBaseEmployeeRuleService;

    @Autowired
    private LocationCollectionMapper locationCollectionMapper;

    @Override
    @Transactional(value = "fenbeitong")
    public int addRule(BusinessRule rule) {
        //添加采购规则
        if (rule instanceof MallRule) {
            return mallRuleMapper.insert((MallRule) rule);
        }
        return -1;
    }

    @Override
    @Transactional(value = "fenbeitong")
    public int addRule(BusinessRule rule, MallRuleContract param) {
        //添加采购规则
        if (rule instanceof MallRule) {
            //1:品类 2:sku
            if (MallConstant.SKU_TYPE == param.getType()) {
                List<SkuInfoContract> skuList = param.getSkuList();
                if (!CollectionUtils.isEmpty(skuList)) {
                    Date datetime = new Date();
                    for (SkuInfoContract skuInfoContract : skuList) {
                        MallRuleSku record = new MallRuleSku();
                        record.setRuleId(((MallRule) rule).getId());
                        record.setCreateTime(datetime);
                        record.setSkuId(skuInfoContract.getSku_id());
                        record.setShortDescription(skuInfoContract.getShort_description());
                        record.setSkuCategory(skuInfoContract.getSku_category());
                        insertMallRuleSku(record);
                    }
                }
            }
            //添加规则日志
            addOperateLogRule(((MallRule) rule).getCompanyId(), ((MallRule) rule).getId(), "", JSON.toJSONString(rule), 1, CategoryTypeEnum.Mall.getCode());
            return mallRuleMapper.insert((MallRule) rule);
        }
        return -1;
    }

    /**
     * 添加规则信息
     *
     * @param record
     * @return
     */
    @Transactional(value = "fenbeitong")
    public int insertMallRuleSku(MallRuleSku record) {
        return mallRuleSkuMapper.insert(record);
    }

    @Override
    @Transactional(value = "fenbeitong")
    public int updateRule(BusinessRule rule) {
        //更新采购规则
        if (rule instanceof MallRule) {
            return mallRuleMapper.updateByPrimaryKeySelective((MallRule) rule);
        }
        return -1;
    }

    @Override
    @Transactional(value = "fenbeitong")
    public int updateRule(BusinessRule rule, MallRuleContract param, String companyId, String ruleId) {
        MallRuleExample mallRuleExampleInfo = new MallRuleExample();
        mallRuleExampleInfo.createCriteria().andCompanyIdEqualTo(companyId).andNameEqualTo(param.getName()).andIdNotEqualTo(ruleId);
        List<MallRule> mallRules = mallRuleMapper.selectByExample(mallRuleExampleInfo);
        if (CollectionUtils.isNotEmpty(mallRules)) {
            throw new SaasException(GlobalResponseCode.RuleNameRepetition);
        }
        //更新采购规则
        if (rule instanceof MallRule) {
            if (MallConstant.CATEGORY_TYPE == param.getType()) {
                MallRuleSkuExample mallRuleSkuExample = new MallRuleSkuExample();
                mallRuleSkuExample.createCriteria().andRuleIdEqualTo(((MallRule) rule).getId());
                //mallRuleSkuMapper.deleteByExample(mallRuleSkuExample);
            } else if (MallConstant.SKU_TYPE == param.getType()) {
                List<SkuInfoContract> skuList = param.getSkuList();
                MallRuleSkuExample mallRuleSkuExample = new MallRuleSkuExample();
                mallRuleSkuExample.createCriteria().andRuleIdEqualTo(((MallRule) rule).getId());
                mallRuleSkuMapper.deleteByExample(mallRuleSkuExample);
                if (!CollectionUtils.isEmpty(skuList)) {
                    Date datetimes = new Date();
                    for (SkuInfoContract skuInfoContract : skuList) {
                        MallRuleSku record = new MallRuleSku();
                        record.setRuleId(((MallRule) rule).getId());
                        record.setCreateTime(datetimes);
                        record.setSkuId(skuInfoContract.getSku_id());
                        record.setShortDescription(skuInfoContract.getShort_description());
                        record.setSkuCategory(skuInfoContract.getSku_category());
                        insertMallRuleSku(record);
                    }
                }
            }
            //添加规则日志
            addOperateLogRule(companyId, ((MallRule) rule).getId(), "", JSON.toJSONString(rule), 2, CategoryTypeEnum.Mall.getCode());
            return mallRuleMapper.updateByPrimaryKeySelective((MallRule) rule);
        }
        return -1;
    }

    @Override
    @Transactional(value = "fenbeitong")
    public void matchMallRule(MallMatchEmployeeContract mallMatchEmployee, String ruleId, String companyId, String userId) throws SaasException {
        validate(mallMatchEmployee, companyId);
//        EmployeeMallRuleExample employeeMallRuleExample = new EmployeeMallRuleExample();
//        employeeMallRuleExample.createCriteria().andManualMallRuleIdEqualTo(ruleId).andCompanyIdEqualTo(companyId);
//        EmployeeMallRule rule = new EmployeeMallRule(null, MallRuleType.NotAllowed.getCode(), false, null, "", false, false, companyId);
//        iBaseEmployeeMallRuleService.updateByExampleSelective(rule, employeeMallRuleExample);
//        if (CollectionUtils.isEmpty(mallMatchEmployee.getEmployee_ids())) {
//            return;
//        }
        MallRuleExample example = new MallRuleExample();
        example.createCriteria().andCompanyIdEqualTo(companyId).andIdEqualTo(ruleId);
        List<MallRule> mallRules = mallRuleMapper.selectByExample(example);
        if (ObjUtils.isEmpty(mallRules)) {
            throw new SaasException(GlobalResponseCode.RuleIdNotExistInCompany);
        }

        iBaseEmployeeMallRuleExtService.updateEmployeeApplyRuleId(companyId, userId, mallMatchEmployee.getEmployee_ids(), CategorySubType.Mall.getSubtype(), ruleId);
        //添加规则日志
        addOperateLogRule(companyId, ruleId, "", JSON.toJSONString(mallMatchEmployee), 4, CategoryTypeEnum.Mall.getCode());
//        employeeMallRuleExample = new EmployeeMallRuleExample();
//        employeeMallRuleExample.createCriteria().andEmployeeIdIn(mallMatchEmployee.getEmployee_ids()).andCompanyIdEqualTo(companyId);
//        rule.setMallRule(MallRuleType.Allowed.getCode());
//        rule.setManualMallRuleId(ruleId);
//        rule.setMallRuleFlag(true);
//        iBaseEmployeeMallRuleService.updateByExampleSelective(rule, employeeMallRuleExample);
    }

    @Override
    @Transactional(value = "fenbeitong")
    public void deleteMallRule(MallRuleDeleteContract deleteContract, String company, String userId) throws SaasException {
        validate(deleteContract, company);
        MallRuleExample mallRuleExample = new MallRuleExample();
        mallRuleExample.createCriteria().andCompanyIdEqualTo(company).andIdIn(deleteContract.getRule_ids());
        mallRuleMapper.deleteByExample(mallRuleExample);
    }

    @Override
    @Transactional(value = "fenbeitong")
    public void deleteMallRuleV2(RuleDeleteContract deleteContract, String company, String userId) throws SaasException {
        //查询规则ID是否存在
        MallRuleExample mallRuleExample = new MallRuleExample();
        mallRuleExample.createCriteria().andCompanyIdEqualTo(company)
                .andIdIn(deleteContract.getRule_ids());
        if (deleteContract.getRule_ids().size() != mallRuleMapper.countByExample(mallRuleExample)) {
            throw new SaasException(GlobalResponseCode.RuleIdNotExistInCompany);
        }
        for (String ruleId : deleteContract.getRule_ids()) {
            iBaseEmployeeMallRuleExtService.updateManualRuleIdByRuleId(company, userId, ruleId);
        }
        // 删除之前先查询，为推送消息获取数据
        MallRule mallRule = mallRuleMapper.selectByPrimaryKey(deleteContract.getRule_ids().get(0));
        //删除对应的sku信息集合
        MallRuleSkuExample mallRuleSkuExample = new MallRuleSkuExample();
        mallRuleSkuExample.createCriteria().andRuleIdIn(deleteContract.getRule_ids());
        mallRuleSkuMapper.deleteByExample(mallRuleSkuExample);
        //删除规则
        MallRuleExample mallRuleDeleteExample = new MallRuleExample();
        mallRuleDeleteExample.createCriteria().andCompanyIdEqualTo(company).andIdIn(deleteContract.getRule_ids());
        mallRuleMapper.deleteByExample(mallRuleDeleteExample);
        //添加规则日志
        addOperateLogRule(company, JSON.toJSONString(deleteContract.getRule_ids()), userId, JSON.toJSONString(deleteContract), 3, CategoryTypeEnum.Mall.getCode());
        if (deleteContract.getRule_ids().size() == 1) {
            companyLogKafkaProducerService.sendCompanyLogMsg(CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Mall_Rule, LogOperateActionEnum.DELETE,
                    LogOperateObjectEnum.MALL_RULE, mallRule.getName()));
        } else {
            companyLogKafkaProducerService.sendCompanyLogMsg(CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Mall_Rule, LogOperateActionEnum.BATCH_DELETE,
                    LogOperateObjectEnum.MALL_RULE, ""));
        }
    }


    /**
     * 采购规则列表
     */
    @Override
    public List<MallRuleDetailContract> listMallRule(String companyId, String ruleId, String ruleName, GenCategory genCategory, String token) throws SaasException, IOException, HttpException, ParseException {
        List<MallRule> mallRules = getMallRules(companyId, ruleId, ruleName);// 采购规则基本数据
        LOGGER.info("getMallRules参数companyId" + companyId + "ruleId:" + ruleId + "mallRules:" + JsonUtils.toJson(mallRules));
        List<MallCategoryContract> mallCategoryContracts = vendorHubService.getCategories(token);//最新的采购类目列表
        final int nowCategoriesCount = mallCategoryContracts.size();
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date defaultDate = dateFormat.parse("1992-09-03 00:00:00");
        mallRules.sort(((o1, o2) -> -Optional.ofNullable(o1.getModifyTime()).orElse(defaultDate).compareTo(Optional.ofNullable(o2.getModifyTime()).orElse(defaultDate))));//排序
        List<MallRuleDetailContract> mallRuleDetailContracts = mallRules.stream().map(mallRule -> {
            String mallRuleId = mallRule.getId();//单条采购规则 id
            String selectedCategoryIds = mallRule.getLimitCategoryIds();//已选的采购类目id
            List<Long> selectedIds = !org.springframework.util.StringUtils.isEmpty(selectedCategoryIds) ? Arrays.asList(selectedCategoryIds.split(",")).stream().map(Long::parseLong).collect(Collectors.toList()) : new ArrayList<>();
            List<MallCategoryContract> categories = genCategory.gen(mallCategoryContracts, selectedIds);
            List<EmployeeInfoContract> employees = genEmployeeInfoContracts(companyId, mallRuleId, 1, Integer.MAX_VALUE).getEmployees();
            LOGGER.info("listMallRule方法处理值：" + "categories:" + JsonUtils.toJson(categories) + "mallRules:" + JsonUtils.toJson(mallRules) + "ruleId:" + mallRuleId + "employees:" + JsonUtils.toJson(employees));
            return new MallRuleDetailContract(
                    mallRuleId,
                    mallRule.getName(),
                    mallRule.getLimitPriceFlag(),
                    mallRule.getLimitPriceHighest(),
                    categories,
                    employees,
                    nowCategoriesCount
            );
        }).collect(Collectors.toList());
        LOGGER.info("listMallRule返回值:" + JsonUtils.toJson(mallRuleDetailContracts));
        return mallRuleDetailContracts;
    }

    @Override
    public List<AuthorizationEmployeeContract> listAuthEmployee(String companyId,
                                                                AuthorizationType authorizationEnum,
                                                                List<TravelType> travelEnums,
                                                                NeedApplyType needApplyEnum,
                                                                List<ExceedAllowType> exceedAllowEnum) throws SaasException {
        List<AuthorizationEmployeeContract> results = new ArrayList<>();

        List<OrgUnitEmployeeSimple> simpleList = getSimple(companyId); //小表查公司下属员工信息
        List<OrgUnit> orgUnitList = getDepartment(companyId);
        List<String> employeeIdList = simpleList.stream().map(OrgUnitEmployeeSimple::getEmployeeId).collect(Collectors.toList());
        List<EmployeeAirRule> airRuleList = getEmployeeAirRule(employeeIdList, companyId); //这些员工中应用了机票规则的
        List<EmployeeHotelRule> hotelRuleList = getEmployeeHotelRule(employeeIdList, companyId);//这些员工中应用了酒店规则的
        List<EmployeeTrainRule> trainRuleList = getEmployeeTrainRule(employeeIdList, companyId);//这些员工中应用了火车规则的
        List<EmployeeMallRule> mallRuleList = getEmployeeMallRule(employeeIdList, companyId);//这些员工中应用了采购规则的
        List<EmployeeTaxiRule> taxiRuleList = getCompanyEmployee(employeeIdList, companyId);//这些员工中应用了用车规则的
        List<EmployeeDinnerRule> dinnerRuleList = getEmployeeDinnerRule(employeeIdList, companyId);//这些员工中应用了用餐规则的

        Map<String, String> employeeIdNameMap = getEmployee(employeeIdList, companyId).stream().collect(toMap(EmployeeContract::getId, EmployeeContract::getName));
        Map<String, String> departIdName = orgUnitList.stream().collect(toMap(OrgUnit::getId, OrgUnit::getName, (a, b) -> a + "," + b));
        Map<String, String> employeeIdDepartmentNameMap = simpleList.stream().collect(toMap(OrgUnitEmployeeSimple::getEmployeeId, simple -> departIdName.getOrDefault(simple.getOrgUnitId(), ""), (a, b) -> a + "," + b));

        Map<String, Optional<Boolean>> employeeAirRuleMap = airRuleList.stream().collect(toMap(EmployeeAirRule::getEmployee_id, rule -> getRuleFlag(rule.getAir_rule(), BizType.AirPlane)));
        Map<String, Optional<Boolean>> employeeHotelRuleMap = hotelRuleList.stream().collect(toMap(EmployeeHotelRule::getEmployee_id, rule -> getRuleFlag(rule.getHotel_rule(), BizType.Hotel)));
        Map<String, Optional<Boolean>> employeeTrainRuleMap = trainRuleList.stream().collect(toMap(EmployeeTrainRule::getEmployee_id, rule -> getRuleFlag(rule.getTrain_rule(), BizType.Train)));
        Map<String, Optional<Boolean>> employeeMallRuleMap = mallRuleList.stream().collect(toMap(EmployeeMallRule::getEmployeeId, rule -> getRuleFlag(rule.getMallRule(), BizType.Mall)));
        Map<String, Optional<Boolean>> employeeTaxiRuleMap = taxiRuleList.stream().collect(toMap(EmployeeTaxiRule::getEmployeeId, rule -> getRuleFlag(rule.getTaxiRule(), BizType.Taxi)));
        Map<String, Optional<Boolean>> employeeDinnerRuleMap = dinnerRuleList.stream().collect(toMap(EmployeeDinnerRule::getEmployee_id, rule -> getRuleFlag(rule.getDinner_rule(), BizType.Dinner)));

        Map<String, Optional<EmployeeAirRule>> employeeAirApplyMap = airRuleList.stream().collect(toMap(EmployeeAirRule::getEmployee_id, Optional::ofNullable));
        Map<String, Optional<EmployeeHotelRule>> employeeHotelApplyMap = hotelRuleList.stream().collect(toMap(EmployeeHotelRule::getEmployee_id, Optional::ofNullable));
        Map<String, Optional<EmployeeTrainRule>> employeeTrainApplyMap = trainRuleList.stream().collect(toMap(EmployeeTrainRule::getEmployee_id, Optional::ofNullable));
        Map<String, Optional<EmployeeTaxiRule>> employeeTaxiApplyMap = taxiRuleList.stream().collect(toMap(EmployeeTaxiRule::getEmployeeId, Optional::ofNullable));
        Map<String, Optional<EmployeeMallRule>> employeeMallApplyMap = mallRuleList.stream().collect(toMap(EmployeeMallRule::getEmployeeId, Optional::ofNullable));
        Map<String, Optional<EmployeeDinnerRule>> employeeDinnerApplyMap = dinnerRuleList.stream().collect(toMap(EmployeeDinnerRule::getEmployee_id, Optional::ofNullable));

        Boolean applyFlag = needApplyEnum.verifyFlag();
        //Boolean exceedFlag = exceedAllowEnum.verifyFlag();
        List<Integer> exceedIntegerFlag = new ArrayList<>();
        for (ExceedAllowType exceedInteger : exceedAllowEnum) {
            Integer exceedIntegerFlagCode = exceedInteger.getCode();
            exceedIntegerFlag.add(exceedIntegerFlagCode);
        }
        Boolean exceedFlag = false;
        switch (authorizationEnum) {

            case Dinner:
                exceedFlag = exceedAllowEnum.get(0).verifyFlag();
                addDinnerAuthorizationEmployee(results, dinnerRuleList, employeeIdNameMap, employeeIdDepartmentNameMap, employeeAirRuleMap, employeeHotelRuleMap, employeeTrainRuleMap, employeeMallRuleMap, employeeTaxiRuleMap, employeeDinnerRuleMap, employeeAirApplyMap, employeeHotelApplyMap, employeeTrainApplyMap, employeeTaxiApplyMap, employeeMallApplyMap, employeeDinnerApplyMap, exceedFlag);
                break;
            case Mall:
                exceedFlag = exceedAllowEnum.get(0).verifyFlag();
                addMallAuthorizationEmployee(results, mallRuleList, employeeIdNameMap, employeeIdDepartmentNameMap, employeeAirRuleMap, employeeHotelRuleMap, employeeTrainRuleMap, employeeMallRuleMap, employeeTaxiRuleMap, employeeDinnerRuleMap, employeeAirApplyMap, employeeHotelApplyMap, employeeTrainApplyMap, employeeTaxiApplyMap, employeeMallApplyMap, employeeDinnerApplyMap, exceedFlag);
                break;
            case Taxi:
                exceedFlag = exceedAllowEnum.get(0).verifyFlag();
                addTaxiAuthorizationEmployee(results, taxiRuleList, employeeIdNameMap, employeeIdDepartmentNameMap, employeeAirRuleMap, employeeHotelRuleMap, employeeTrainRuleMap, employeeMallRuleMap, employeeTaxiRuleMap, employeeDinnerRuleMap, employeeAirApplyMap, employeeHotelApplyMap, employeeTrainApplyMap, employeeTaxiApplyMap, employeeMallApplyMap, employeeDinnerApplyMap, exceedFlag);
                break;
            case ChaiLv:
                addAirAuthorizationEmployee(results, airRuleList, employeeIdNameMap, employeeIdDepartmentNameMap, employeeAirRuleMap, employeeHotelRuleMap, employeeTrainRuleMap, employeeMallRuleMap, employeeTaxiRuleMap, employeeDinnerRuleMap, employeeAirApplyMap, employeeHotelApplyMap, employeeTrainApplyMap, employeeTaxiApplyMap, employeeMallApplyMap, applyFlag, employeeDinnerApplyMap, exceedIntegerFlag);
                addTrainAuthorizationEmployee(results, trainRuleList, employeeIdNameMap, employeeIdDepartmentNameMap, employeeAirRuleMap, employeeHotelRuleMap, employeeTrainRuleMap, employeeMallRuleMap, employeeTaxiRuleMap, employeeDinnerRuleMap, employeeAirApplyMap, employeeHotelApplyMap, employeeTrainApplyMap, employeeTaxiApplyMap, employeeMallApplyMap, applyFlag, employeeDinnerApplyMap, exceedIntegerFlag);
                addHotelAuthorizationEmployee(results, hotelRuleList, employeeIdNameMap, employeeIdDepartmentNameMap, employeeAirRuleMap, employeeHotelRuleMap, employeeTrainRuleMap, employeeMallRuleMap, employeeTaxiRuleMap, employeeDinnerRuleMap, employeeAirApplyMap, employeeHotelApplyMap, employeeTrainApplyMap, employeeTaxiApplyMap, employeeMallApplyMap, applyFlag, employeeDinnerApplyMap, exceedIntegerFlag);
                if (travelEnums != null) {
                    travelEnums.stream().forEach(travelEnum -> {
                                switch (travelEnum) {
                                    case Air:

                                        List<AuthorizationEmployeeContract> tmpAir = results.stream().filter(result ->
                                                result.getAir_flag() != null && result.getAir_flag() == true).collect(Collectors.toList());
                                        LOGGER.info("and 过滤出拥有机票权限的员工:" + tmpAir.size());
                                        results.clear();
                                        results.addAll(tmpAir);
                                        break;
                                    case Train:
                                        List<AuthorizationEmployeeContract> tmpTrain = results.stream().filter(result ->
                                                result.getTrain_flag() != null && result.getTrain_flag() == true).collect(Collectors.toList());
                                        LOGGER.info("and 过滤出拥有火车权限的员工:" + tmpTrain.size());
                                        results.clear();
                                        results.addAll(tmpTrain);
                                        break;
                                    case Hotel:
                                        List<AuthorizationEmployeeContract> tmpHotel = results.stream().filter(result ->
                                                result.getHotel_flag() != null && result.getHotel_flag() == true).collect(Collectors.toList());
                                        LOGGER.info("and 过滤出拥有酒店限的员工:" + tmpHotel.size());
                                        results.clear();
                                        results.addAll(tmpHotel);
                                        break;
                                }
                            }
                    );
                }
                break;
        }
        LOGGER.info("人数:" + results.size());
        List<AuthorizationEmployeeContract> collect = results.stream().collect(
                collectingAndThen(toCollection(() -> new TreeSet<>(Comparator.comparing(AuthorizationEmployeeContract::getId))), ArrayList::new)
        );
        LOGGER.info("去重人数:" + collect.size());
        return collect;
    }

    @Override
    public List<AuthorizationEmployeeV2Contract> listAuthEmployeeV2(String companyId, AuthorizationType authorizationEnum,
                                                                    NeedApplyType needApplyEnum,
                                                                    List<ExceedAllowType> exceedAllowEnum) throws SaasException {
        List<AuthorizationEmployeeV2Contract> results = new ArrayList<>();

        List<OrgUnitEmployeeSimple> simpleList = getSimple(companyId); //小表查公司下属员工信息
        List<OrgUnit> orgUnitList = getDepartment(companyId);
        List<String> employeeIdList = simpleList.stream().map(OrgUnitEmployeeSimple::getEmployeeId).collect(Collectors.toList());
        List<EmployeeAirRuleContract> airRuleList = iBaseEmployeeAirRuleExtService.getEmployeeAirRuleResult(employeeIdList, companyId); //这些员工中应用了机票规则的
        List<EmployeeHotelRuleContract> hotelRuleList = iBaseEmployeeHotelRuleExtService.getEmployeeHotelRuleResult(employeeIdList, companyId);//这些员工中应用了酒店规则的
        List<EmployeeTrainRuleContract> trainRuleList = iBaseEmployeeTrainRuleExtService.getEmployeeTrainRuleResult(employeeIdList, companyId);//这些员工中应用了火车规则的
        List<EmployeeMallRule> mallRuleList = getEmployeeMallRule(employeeIdList, companyId);//这些员工中应用了采购规则的
        List<EmployeeTaxiRuleContract> taxiRuleList = iBaseEmployeeTaxiRuleExtService.getEmployeeTaxiRuleResult(employeeIdList, companyId);//这些员工中应用了用车规则的
        List<EmployeeDinnerRule> dinnerRuleList = getEmployeeDinnerRule(employeeIdList, companyId);//这些员工中应用了用餐规则的
        List<EmployeeAirRuleContract> internationalAirRuleList = iBaseEmployeeIntlAirRuleExtService.getEmployeeIntlAirRuleResult(employeeIdList, companyId); //这些员工中应用了机票规则的

        Map<String, String> employeeIdNameMap = getEmployee(employeeIdList, companyId).stream().collect(toMap(EmployeeContract::getId, EmployeeContract::getName));
        Map<String, String> departIdName = orgUnitList.stream().collect(toMap(OrgUnit::getId, OrgUnit::getName, (a, b) -> a + "," + b));
        Map<String, String> employeeIdDepartmentNameMap = simpleList.stream().collect(toMap(OrgUnitEmployeeSimple::getEmployeeId, simple -> departIdName.getOrDefault(simple.getOrgUnitId(), ""), (a, b) -> a + "," + b));
        Map<String, Optional<Boolean>> employeeAirRuleMap = airRuleList.stream().collect(toMap(EmployeeAirRuleContract::getEmployee_id, rule -> getRuleFlag(rule.getAir_rule(), BizType.AirPlane)));
        Map<String, Optional<Boolean>> employeeHotelRuleMap = hotelRuleList.stream().collect(toMap(EmployeeHotelRuleContract::getEmployee_id, rule -> getRuleFlag(rule.getHotel_rule(), BizType.Hotel)));
        Map<String, Optional<Boolean>> employeeTrainRuleMap = trainRuleList.stream().collect(toMap(EmployeeTrainRuleContract::getEmployee_id, rule -> getRuleFlag(rule.getTrain_rule(), BizType.Train)));
        Map<String, Optional<Boolean>> employeeMallRuleMap = mallRuleList.stream().collect(toMap(EmployeeMallRule::getEmployeeId, rule -> getRuleFlag(rule.getMallRule(), BizType.Mall)));
        Map<String, Optional<Boolean>> employeeTaxiRuleMap = taxiRuleList.stream().collect(toMap(EmployeeTaxiRuleContract::getEmployee_id, rule -> getRuleFlag(rule.getTaxi_rule(), BizType.Taxi)));
        Map<String, Optional<Boolean>> employeeDinnerRuleMap = dinnerRuleList.stream().collect(toMap(EmployeeDinnerRule::getEmployee_id, rule -> getRuleFlag(rule.getDinner_rule(), BizType.Dinner)));
        Map<String, Optional<Boolean>> employeeIntlAirRuleMap = internationalAirRuleList.stream().collect(toMap(EmployeeAirRuleContract::getEmployee_id, rule -> getRuleFlag(rule.getInternational_air_rule(), BizType.InternationalAirPlane)));

        Map<String, Optional<EmployeeAirRuleContract>> employeeAirApplyMap = airRuleList.stream().collect(toMap(EmployeeAirRuleContract::getEmployee_id, Optional::ofNullable));
        Map<String, Optional<EmployeeHotelRuleContract>> employeeHotelApplyMap = hotelRuleList.stream().collect(toMap(EmployeeHotelRuleContract::getEmployee_id, Optional::ofNullable));
        Map<String, Optional<EmployeeTrainRuleContract>> employeeTrainApplyMap = trainRuleList.stream().collect(toMap(EmployeeTrainRuleContract::getEmployee_id, Optional::ofNullable));
        Map<String, Optional<EmployeeTaxiRuleContract>> employeeTaxiApplyMap = taxiRuleList.stream().collect(toMap(EmployeeTaxiRuleContract::getEmployee_id, Optional::ofNullable));
        Map<String, Optional<EmployeeMallRule>> employeeMallApplyMap = mallRuleList.stream().collect(toMap(EmployeeMallRule::getEmployeeId, Optional::ofNullable));
        Map<String, Optional<EmployeeDinnerRule>> employeeDinnerApplyMap = dinnerRuleList.stream().collect(toMap(EmployeeDinnerRule::getEmployee_id, Optional::ofNullable));
        Map<String, Optional<EmployeeAirRuleContract>> employeeIntlAirApplyMap = internationalAirRuleList.stream().collect(toMap(EmployeeAirRuleContract::getEmployee_id, Optional::ofNullable));

        Boolean applyFlag = needApplyEnum.verifyFlag();
        //Boolean exceedFlag = exceedAllowEnum.verifyFlag();
        List<Integer> exceedIntegerFlag = new ArrayList<>();
        for (ExceedAllowType exceedInteger : exceedAllowEnum) {
            Integer exceedIntegerFlagCode = exceedInteger.getCode();
            exceedIntegerFlag.add(exceedIntegerFlagCode);
        }
        Boolean exceedFlag = false;

        switch (authorizationEnum) {

            case Dinner:
                addDinnerAuthorizationEmployeeV2(results, dinnerRuleList, employeeIdNameMap, employeeIdDepartmentNameMap, employeeAirRuleMap, employeeHotelRuleMap, employeeTrainRuleMap, employeeMallRuleMap, employeeTaxiRuleMap, employeeDinnerRuleMap, employeeIntlAirRuleMap, employeeAirApplyMap, employeeHotelApplyMap, employeeTrainApplyMap, employeeTaxiApplyMap, employeeMallApplyMap, employeeDinnerApplyMap, employeeIntlAirApplyMap, exceedIntegerFlag);
                break;
            case Mall:
                exceedFlag = exceedAllowEnum.get(0).verifyFlag();
                addMallAuthorizationEmployeeV2(results, mallRuleList, employeeIdNameMap, employeeIdDepartmentNameMap, employeeAirRuleMap, employeeHotelRuleMap, employeeTrainRuleMap, employeeMallRuleMap, employeeTaxiRuleMap, employeeDinnerRuleMap, employeeIntlAirRuleMap, employeeAirApplyMap, employeeHotelApplyMap, employeeTrainApplyMap, employeeTaxiApplyMap, employeeMallApplyMap, employeeDinnerApplyMap, employeeIntlAirApplyMap, exceedFlag);
                break;
            case Taxi:
                addTaxiAuthorizationEmployeeV2(results, taxiRuleList, employeeIdNameMap, employeeIdDepartmentNameMap, employeeAirRuleMap, employeeHotelRuleMap, employeeTrainRuleMap, employeeMallRuleMap, employeeTaxiRuleMap, employeeDinnerRuleMap, employeeIntlAirRuleMap, employeeAirApplyMap, employeeHotelApplyMap, employeeTrainApplyMap, employeeTaxiApplyMap, employeeMallApplyMap, employeeDinnerApplyMap, employeeIntlAirApplyMap, exceedIntegerFlag);
                break;
            case Air:
                addAirAuthorizationEmployeeV2(results, airRuleList, employeeIdNameMap, employeeIdDepartmentNameMap, employeeAirRuleMap, employeeHotelRuleMap, employeeTrainRuleMap, employeeMallRuleMap, employeeTaxiRuleMap, employeeDinnerRuleMap, employeeIntlAirRuleMap, employeeAirApplyMap, employeeHotelApplyMap, employeeTrainApplyMap, employeeTaxiApplyMap, employeeMallApplyMap, applyFlag, employeeDinnerApplyMap, employeeIntlAirApplyMap, exceedIntegerFlag);
                break;
            case Train:
                addTrainAuthorizationEmployeeV2(results, trainRuleList, employeeIdNameMap, employeeIdDepartmentNameMap, employeeAirRuleMap, employeeHotelRuleMap, employeeTrainRuleMap, employeeMallRuleMap, employeeTaxiRuleMap, employeeDinnerRuleMap, employeeIntlAirRuleMap, employeeAirApplyMap, employeeHotelApplyMap, employeeTrainApplyMap, employeeTaxiApplyMap, employeeMallApplyMap, applyFlag, employeeDinnerApplyMap, employeeIntlAirApplyMap, exceedIntegerFlag);
                break;
            case Hotel:
                addHotelAuthorizationEmployeeV2(results, hotelRuleList, employeeIdNameMap, employeeIdDepartmentNameMap, employeeAirRuleMap, employeeHotelRuleMap, employeeTrainRuleMap, employeeMallRuleMap, employeeTaxiRuleMap, employeeDinnerRuleMap, employeeIntlAirRuleMap, employeeAirApplyMap, employeeHotelApplyMap, employeeTrainApplyMap, employeeTaxiApplyMap, employeeMallApplyMap, applyFlag, employeeDinnerApplyMap, employeeIntlAirApplyMap, exceedIntegerFlag);
                break;
            case InternationalAirPlane:
                addIntlAirAuthorizationEmployeeV2(results, internationalAirRuleList, employeeIdNameMap, employeeIdDepartmentNameMap, employeeAirRuleMap, employeeHotelRuleMap, employeeTrainRuleMap, employeeMallRuleMap, employeeTaxiRuleMap, employeeDinnerRuleMap, employeeIntlAirRuleMap, employeeAirApplyMap, employeeHotelApplyMap, employeeTrainApplyMap, employeeTaxiApplyMap, employeeMallApplyMap, applyFlag, employeeDinnerApplyMap, employeeIntlAirApplyMap, exceedIntegerFlag);
                break;
        }
        LOGGER.info("人数:" + results.size());
        List<AuthorizationEmployeeV2Contract> collect = results.stream().collect(
                collectingAndThen(toCollection(() -> new TreeSet<>(Comparator.comparing(AuthorizationEmployeeV2Contract::getId))), ArrayList::new)
        );
        LOGGER.info("去重人数:" + collect.size());
        return collect;
    }

    public static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> map = new ConcurrentHashMap<>();
        return t -> map.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    /**
     * 添加符合用车规则的员工到返回结果 results 中
     */
    private void addTaxiAuthorizationEmployee(List<AuthorizationEmployeeContract> results, List<EmployeeTaxiRule> taxiRuleList, Map<String, String> employeeIdNameMap, Map<String, String> employeeIdDepartmentNameMap, Map<String, Optional<Boolean>> employeeAirRuleMap, Map<String, Optional<Boolean>> employeeHotelRuleMap, Map<String, Optional<Boolean>> employeeTrainRuleMap, Map<String, Optional<Boolean>> employeeMallRuleMap, Map<String, Optional<Boolean>> employeeTaxiRuleMap, Map<String, Optional<Boolean>> employeeDinnerRuleMap, Map<String, Optional<EmployeeAirRule>> employeeAirApplyMap, Map<String, Optional<EmployeeHotelRule>> employeeHotelApplyMap, Map<String, Optional<EmployeeTrainRule>> employeeTrainApplyMap, Map<String, Optional<EmployeeTaxiRule>> employeeTaxiApplyMap, Map<String, Optional<EmployeeMallRule>> employeeMallApplyMap, Map<String, Optional<EmployeeDinnerRule>> employeeDinnerApplyMap, Boolean exceedFlag) {
        taxiRuleList.stream()
                .filter(rule -> TaxiRuleType.getAllowedType().contains(Optional.ofNullable(rule.getTaxiRule()).orElse(0)))
                .filter(rule -> {

                    if (exceedFlag == null) {
                        return true;
                    } else {
                        return rule.getExceedBuyFlag().equals(exceedFlag);
                    }
                })
                .forEach(rule -> addAuthorizationEmployeeContract(
                        results,
                        rule.getEmployeeId(),
                        employeeIdNameMap,
                        employeeIdDepartmentNameMap,
                        employeeAirRuleMap,
                        employeeHotelRuleMap,
                        employeeTrainRuleMap,
                        employeeMallRuleMap,
                        employeeTaxiRuleMap,
                        employeeDinnerRuleMap,
                        employeeAirApplyMap,
                        employeeHotelApplyMap,
                        employeeTrainApplyMap,
                        employeeTaxiApplyMap,
                        employeeMallApplyMap,
                        employeeDinnerApplyMap)
                );
    }

    /**
     * v2
     * 添加符合用车规则的员工到返回结果 results 中
     */
    private void addTaxiAuthorizationEmployeeV2(List<AuthorizationEmployeeV2Contract> results, List<EmployeeTaxiRuleContract> taxiRuleList, Map<String, String> employeeIdNameMap,
                                                Map<String, String> employeeIdDepartmentNameMap, Map<String, Optional<Boolean>> employeeAirRuleMap, Map<String,
            Optional<Boolean>> employeeHotelRuleMap, Map<String, Optional<Boolean>> employeeTrainRuleMap, Map<String, Optional<Boolean>> employeeMallRuleMap,
                                                Map<String, Optional<Boolean>> employeeTaxiRuleMap, Map<String, Optional<Boolean>> employeeDinnerRuleMap, Map<String, Optional<Boolean>> employeeIntlAirRuleMap,
                                                Map<String, Optional<EmployeeAirRuleContract>> employeeAirApplyMap, Map<String, Optional<EmployeeHotelRuleContract>> employeeHotelApplyMap,
                                                Map<String, Optional<EmployeeTrainRuleContract>> employeeTrainApplyMap, Map<String, Optional<EmployeeTaxiRuleContract>> employeeTaxiApplyMap,
                                                Map<String, Optional<EmployeeMallRule>> employeeMallApplyMap, Map<String, Optional<EmployeeDinnerRule>> employeeDinnerApplyMap, Map<String, Optional<EmployeeAirRuleContract>> employeeIntlAirApplyMap,
                                                List<Integer> exceedCarFlag) {
        taxiRuleList.stream()
                .filter(rule -> TaxiRuleType.getAllowedType().contains(Optional.ofNullable(rule.getTaxi_rule()).orElse(0)))
                .filter(rule -> {

                    if (exceedCarFlag == null || (exceedCarFlag.size() == 1 && exceedCarFlag.get(0) == 0)) {
                        return true;
                    } else {
                        if (exceedCarFlag.size() >= 2) {
                            return rule.getTaxi_exceed_buy_type() == exceedCarFlag.get(0) || rule.getTaxi_exceed_buy_type() == exceedCarFlag.get(1);
                        }
                        return rule.getTaxi_exceed_buy_type() == exceedCarFlag.get(0);
                    }
                })
                .forEach(rule -> addAuthorizationEmployeeContractV2(
                        results,
                        rule.getEmployee_id(),
                        employeeIdNameMap,
                        employeeIdDepartmentNameMap,
                        employeeAirRuleMap,
                        employeeHotelRuleMap,
                        employeeTrainRuleMap,
                        employeeMallRuleMap,
                        employeeTaxiRuleMap,
                        employeeDinnerRuleMap,
                        employeeIntlAirRuleMap,
                        employeeAirApplyMap,
                        employeeHotelApplyMap,
                        employeeTrainApplyMap,
                        employeeTaxiApplyMap,
                        employeeMallApplyMap,
                        employeeDinnerApplyMap,
                        employeeIntlAirApplyMap)
                );
    }

    /**
     * 添加符合用餐规则的员工到返回结果 results 中
     */
    private void addDinnerAuthorizationEmployee(List<AuthorizationEmployeeContract> results, List<EmployeeDinnerRule> dinnerRuleList, Map<String, String> employeeIdNameMap, Map<String, String> employeeIdDepartmentNameMap, Map<String, Optional<Boolean>> employeeAirRuleMap, Map<String, Optional<Boolean>> employeeHotelRuleMap, Map<String, Optional<Boolean>> employeeTrainRuleMap, Map<String, Optional<Boolean>> employeeMallRuleMap, Map<String, Optional<Boolean>> employeeTaxiRuleMap, Map<String, Optional<Boolean>> employeeDinnerRuleMap, Map<String, Optional<EmployeeAirRule>> employeeAirApplyMap, Map<String, Optional<EmployeeHotelRule>> employeeHotelApplyMap, Map<String, Optional<EmployeeTrainRule>> employeeTrainApplyMap, Map<String, Optional<EmployeeTaxiRule>> employeeTaxiApplyMap, Map<String, Optional<EmployeeMallRule>> employeeMallApplyMap, Map<String, Optional<EmployeeDinnerRule>> employeeDinnerApplyMap, Boolean exceedFlag) {
        dinnerRuleList.stream()
                .filter(rule -> DinnerRuleType.getAllowedType().contains(Optional.ofNullable(rule.getDinner_rule()).orElse(0)))
                .filter(rule -> {
                    if (exceedFlag == null) {
                        return true;
                    } else {
                        return rule.getExceed_buy_flag().equals(exceedFlag ? 1 : 2);
                    }
                })
                .forEach(rule -> addAuthorizationEmployeeContract(
                        results,
                        rule.getEmployee_id(),
                        employeeIdNameMap,
                        employeeIdDepartmentNameMap,
                        employeeAirRuleMap,
                        employeeHotelRuleMap,
                        employeeTrainRuleMap,
                        employeeMallRuleMap,
                        employeeTaxiRuleMap,
                        employeeDinnerRuleMap,
                        employeeAirApplyMap,
                        employeeHotelApplyMap,
                        employeeTrainApplyMap,
                        employeeTaxiApplyMap,
                        employeeMallApplyMap,
                        employeeDinnerApplyMap)
                );
    }

    /**
     * v2
     * 添加符合用餐规则的员工到返回结果 results 中
     */
    private void addDinnerAuthorizationEmployeeV2(List<AuthorizationEmployeeV2Contract> results,
                                                  List<EmployeeDinnerRule> dinnerRuleList, Map<String, String> employeeIdNameMap,
                                                  Map<String, String> employeeIdDepartmentNameMap, Map<String, Optional<Boolean>> employeeAirRuleMap,
                                                  Map<String, Optional<Boolean>> employeeHotelRuleMap, Map<String, Optional<Boolean>> employeeTrainRuleMap,
                                                  Map<String, Optional<Boolean>> employeeMallRuleMap, Map<String, Optional<Boolean>> employeeTaxiRuleMap,
                                                  Map<String, Optional<Boolean>> employeeDinnerRuleMap, Map<String, Optional<Boolean>> employeeIntlAirRuleMap,
                                                  Map<String, Optional<EmployeeAirRuleContract>> employeeAirApplyMap, Map<String, Optional<EmployeeHotelRuleContract>> employeeHotelApplyMap, Map<String, Optional<EmployeeTrainRuleContract>> employeeTrainApplyMap,
                                                  Map<String, Optional<EmployeeTaxiRuleContract>> employeeTaxiApplyMap, Map<String, Optional<EmployeeMallRule>> employeeMallApplyMap, Map<String, Optional<EmployeeDinnerRule>> employeeDinnerApplyMap, Map<String, Optional<EmployeeAirRuleContract>> employeeIntlAirApplyMap,
                                                  List<Integer> exceedDinnerFalg) {
        dinnerRuleList.stream()
                .filter(rule -> DinnerRuleType.getAllowedType().contains(Optional.ofNullable(rule.getDinner_rule()).orElse(0)))
                .filter(rule -> {

                    if (exceedDinnerFalg == null || (exceedDinnerFalg.size() == 1 && exceedDinnerFalg.get(0) == 0)) {
                        return true;
                    } else {
                        if (exceedDinnerFalg.size() >= 2) {
                            return rule.getExceed_buy_flag() == exceedDinnerFalg.get(0) || rule.getExceed_buy_flag() == exceedDinnerFalg.get(1);
                        }
                        return rule.getExceed_buy_flag() == exceedDinnerFalg.get(0);
                    }
                })
                .forEach(rule -> addAuthorizationEmployeeContractV2(
                        results,
                        rule.getEmployee_id(),
                        employeeIdNameMap,
                        employeeIdDepartmentNameMap,
                        employeeAirRuleMap,
                        employeeHotelRuleMap,
                        employeeTrainRuleMap,
                        employeeMallRuleMap,
                        employeeTaxiRuleMap,
                        employeeDinnerRuleMap,
                        employeeIntlAirRuleMap,
                        employeeAirApplyMap,
                        employeeHotelApplyMap,
                        employeeTrainApplyMap,
                        employeeTaxiApplyMap,
                        employeeMallApplyMap,
                        employeeDinnerApplyMap,
                        employeeIntlAirApplyMap)
                );
    }

    /**
     * 添加符合采购规则的员工到返回结果 results 中
     */
    private void addMallAuthorizationEmployee(List<AuthorizationEmployeeContract> results, List<EmployeeMallRule> mallRuleList, Map<String, String> employeeIdNameMap, Map<String, String> employeeIdDepartmentNameMap, Map<String, Optional<Boolean>> employeeAirRuleMap, Map<String, Optional<Boolean>> employeeHotelRuleMap, Map<String, Optional<Boolean>> employeeTrainRuleMap, Map<String, Optional<Boolean>> employeeMallRuleMap, Map<String, Optional<Boolean>> employeeTaxiRuleMap, Map<String, Optional<Boolean>> employeeDinnerRuleMap, Map<String, Optional<EmployeeAirRule>> employeeAirApplyMap, Map<String, Optional<EmployeeHotelRule>> employeeHotelApplyMap, Map<String, Optional<EmployeeTrainRule>> employeeTrainApplyMap, Map<String, Optional<EmployeeTaxiRule>> employeeTaxiApplyMap, Map<String, Optional<EmployeeMallRule>> employeeMallApplyMap, Map<String, Optional<EmployeeDinnerRule>> employeeDinnerApplyMap, Boolean exceedFlag) {
        mallRuleList.stream()
                .filter(rule -> MallRuleType.getAllowedType().contains(Optional.ofNullable(rule.getMallRule()).orElse(0)))
                .filter(rule -> {
                    if (exceedFlag == null) {
                        return true;
                    } else {
                        return rule.getExceedBuyFlag().equals(exceedFlag);
                    }
                })
                .forEach(rule -> addAuthorizationEmployeeContract(
                        results,
                        rule.getEmployeeId(),
                        employeeIdNameMap,
                        employeeIdDepartmentNameMap,
                        employeeAirRuleMap,
                        employeeHotelRuleMap,
                        employeeTrainRuleMap,
                        employeeMallRuleMap,
                        employeeTaxiRuleMap,
                        employeeDinnerRuleMap,
                        employeeAirApplyMap,
                        employeeHotelApplyMap,
                        employeeTrainApplyMap,
                        employeeTaxiApplyMap,
                        employeeMallApplyMap,
                        employeeDinnerApplyMap)
                );
    }

    /**
     * V2
     * 添加符合采购规则的员工到返回结果 results 中
     */
    private void addMallAuthorizationEmployeeV2(List<AuthorizationEmployeeV2Contract> results, List<EmployeeMallRule> mallRuleList, Map<String, String> employeeIdNameMap,
                                                Map<String, String> employeeIdDepartmentNameMap, Map<String, Optional<Boolean>> employeeAirRuleMap, Map<String, Optional<Boolean>> employeeHotelRuleMap,
                                                Map<String, Optional<Boolean>> employeeTrainRuleMap, Map<String, Optional<Boolean>> employeeMallRuleMap,
                                                Map<String, Optional<Boolean>> employeeTaxiRuleMap, Map<String, Optional<Boolean>> employeeDinnerRuleMap, Map<String, Optional<Boolean>> employeeIntlAirRuleMap,
                                                Map<String, Optional<EmployeeAirRuleContract>> employeeAirApplyMap, Map<String, Optional<EmployeeHotelRuleContract>> employeeHotelApplyMap,
                                                Map<String, Optional<EmployeeTrainRuleContract>> employeeTrainApplyMap,
                                                Map<String, Optional<EmployeeTaxiRuleContract>> employeeTaxiApplyMap, Map<String, Optional<EmployeeMallRule>> employeeMallApplyMap,
                                                Map<String, Optional<EmployeeDinnerRule>> employeeDinnerApplyMap, Map<String, Optional<EmployeeAirRuleContract>> employeeIntlAirApplyMap, Boolean exceedFlag) {
        mallRuleList.stream()
                .filter(rule -> MallRuleType.getAllowedType().contains(Optional.ofNullable(rule.getMallRule()).orElse(0)))
                .filter(rule -> {
                    if (exceedFlag == null) {
                        return true;
                    } else {
                        return rule.getExceedBuyFlag().equals(exceedFlag);
                    }
                })
                .forEach(rule -> addAuthorizationEmployeeContractV2(
                        results,
                        rule.getEmployeeId(),
                        employeeIdNameMap,
                        employeeIdDepartmentNameMap,
                        employeeAirRuleMap,
                        employeeHotelRuleMap,
                        employeeTrainRuleMap,
                        employeeMallRuleMap,
                        employeeTaxiRuleMap,
                        employeeDinnerRuleMap,
                        employeeIntlAirRuleMap,
                        employeeAirApplyMap,
                        employeeHotelApplyMap,
                        employeeTrainApplyMap,
                        employeeTaxiApplyMap,
                        employeeMallApplyMap,
                        employeeDinnerApplyMap,
                        employeeIntlAirApplyMap)
                );
    }

    /**
     * 添加符合酒店规则的员工到返回结果 results 中
     */
    private void addHotelAuthorizationEmployee(List<AuthorizationEmployeeContract> results, List<EmployeeHotelRule> hotelRuleList, Map<String, String> employeeIdNameMap, Map<String, String> employeeIdDepartmentNameMap, Map<String, Optional<Boolean>> employeeAirRuleMap, Map<String, Optional<Boolean>> employeeHotelRuleMap, Map<String, Optional<Boolean>> employeeTrainRuleMap, Map<String, Optional<Boolean>> employeeMallRuleMap, Map<String, Optional<Boolean>> employeeTaxiRuleMap, Map<String, Optional<Boolean>> employeeDinnerRuleMap, Map<String, Optional<EmployeeAirRule>> employeeAirApplyMap, Map<String, Optional<EmployeeHotelRule>> employeeHotelApplyMap, Map<String, Optional<EmployeeTrainRule>> employeeTrainApplyMap, Map<String, Optional<EmployeeTaxiRule>> employeeTaxiApplyMap, Map<String, Optional<EmployeeMallRule>> employeeMallApplyMap, Boolean applyFlag, Map<String, Optional<EmployeeDinnerRule>> employeeDinnerApplyMap, List<Integer> exceedIntegerFlag) {
        hotelRuleList.stream()
                .filter(rule -> HotelRuleType.getAllowedType().contains(Optional.ofNullable(rule.getHotel_rule()).orElse(0)))
                .filter(rule -> {
                    if (exceedIntegerFlag == null || (exceedIntegerFlag.size() == 1 && exceedIntegerFlag.get(0) == 0)) {
                        return true;
                    } else {
                        if (exceedIntegerFlag.size() >= 2) {
                            return rule.getExceed_buy_type() == exceedIntegerFlag.get(0) || rule.getExceed_buy_type() == exceedIntegerFlag.get(1);
                        }
                        return rule.getExceed_buy_type() == exceedIntegerFlag.get(0);
                    }
                })
                .filter(rule -> {
                    if (applyFlag == null) {
                        return true;
                    } else {
                        return rule.getHotel_verify_flag().equals(applyFlag);
                    }
                })
                .forEach(rule -> addAuthorizationEmployeeContract(
                        results,
                        rule.getEmployee_id(),
                        employeeIdNameMap,
                        employeeIdDepartmentNameMap,
                        employeeAirRuleMap,
                        employeeHotelRuleMap,
                        employeeTrainRuleMap,
                        employeeMallRuleMap,
                        employeeTaxiRuleMap,
                        employeeDinnerRuleMap,
                        employeeAirApplyMap,
                        employeeHotelApplyMap,
                        employeeTrainApplyMap,
                        employeeTaxiApplyMap,
                        employeeMallApplyMap,
                        employeeDinnerApplyMap)
                );
    }

    /**
     * 添加符合酒店规则的员工到返回结果 results 中
     */
    private void addHotelAuthorizationEmployeeV2(List<AuthorizationEmployeeV2Contract> results, List<EmployeeHotelRuleContract> hotelRuleList,
                                                 Map<String, String> employeeIdNameMap, Map<String, String> employeeIdDepartmentNameMap,
                                                 Map<String, Optional<Boolean>> employeeAirRuleMap, Map<String, Optional<Boolean>> employeeHotelRuleMap,
                                                 Map<String, Optional<Boolean>> employeeTrainRuleMap, Map<String, Optional<Boolean>> employeeMallRuleMap,
                                                 Map<String, Optional<Boolean>> employeeTaxiRuleMap, Map<String, Optional<Boolean>> employeeDinnerRuleMap, Map<String, Optional<Boolean>> employeeIntlAirRuleMap,
                                                 Map<String, Optional<EmployeeAirRuleContract>> employeeAirApplyMap, Map<String, Optional<EmployeeHotelRuleContract>> employeeHotelApplyMap,
                                                 Map<String, Optional<EmployeeTrainRuleContract>> employeeTrainApplyMap, Map<String, Optional<EmployeeTaxiRuleContract>> employeeTaxiApplyMap,
                                                 Map<String, Optional<EmployeeMallRule>> employeeMallApplyMap, Boolean applyFlag, Map<String, Optional<EmployeeDinnerRule>> employeeDinnerApplyMap, Map<String, Optional<EmployeeAirRuleContract>> employeeIntlAirApplyMap,
                                                 List<Integer> exceedIntegerFlag) {
        hotelRuleList.stream()
                .filter(rule -> HotelRuleType.getAllowedType().contains(Optional.ofNullable(rule.getHotel_rule()).orElse(0)))
                .filter(rule -> {
                    if (exceedIntegerFlag == null || (exceedIntegerFlag.size() == 1 && exceedIntegerFlag.get(0) == 0)) {
                        return true;
                    } else {
                        if (exceedIntegerFlag.size() >= 2) {
                            return rule.getHotel_exceed_buy_type() == exceedIntegerFlag.get(0) || rule.getHotel_exceed_buy_type() == exceedIntegerFlag.get(1);
                        }
                        return rule.getHotel_exceed_buy_type() == exceedIntegerFlag.get(0);
                    }
                })
                .filter(rule -> {
                    if (applyFlag == null) {
                        return true;
                    } else {
                        return rule.getHotel_verify_flag().equals(applyFlag);
                    }
                })
                .forEach(rule -> addAuthorizationEmployeeContractV2(
                        results,
                        rule.getEmployee_id(),
                        employeeIdNameMap,
                        employeeIdDepartmentNameMap,
                        employeeAirRuleMap,
                        employeeHotelRuleMap,
                        employeeTrainRuleMap,
                        employeeMallRuleMap,
                        employeeTaxiRuleMap,
                        employeeDinnerRuleMap,
                        employeeIntlAirRuleMap,
                        employeeAirApplyMap,
                        employeeHotelApplyMap,
                        employeeTrainApplyMap,
                        employeeTaxiApplyMap,
                        employeeMallApplyMap,
                        employeeDinnerApplyMap,
                        employeeAirApplyMap)
                );
    }

    /**
     * 添加符合火车规则的员工到返回结果 results 中
     */
    private void addTrainAuthorizationEmployee(List<AuthorizationEmployeeContract> results, List<EmployeeTrainRule> trainRuleList, Map<String, String> employeeIdNameMap, Map<String, String> employeeIdDepartmentNameMap, Map<String, Optional<Boolean>> employeeAirRuleMap, Map<String, Optional<Boolean>> employeeHotelRuleMap, Map<String, Optional<Boolean>> employeeTrainRuleMap, Map<String, Optional<Boolean>> employeeMallRuleMap, Map<String, Optional<Boolean>> employeeTaxiRuleMap, Map<String, Optional<Boolean>> employeeDinnerRuleMap, Map<String, Optional<EmployeeAirRule>> employeeAirApplyMap, Map<String, Optional<EmployeeHotelRule>> employeeHotelApplyMap, Map<String, Optional<EmployeeTrainRule>> employeeTrainApplyMap, Map<String, Optional<EmployeeTaxiRule>> employeeTaxiApplyMap, Map<String, Optional<EmployeeMallRule>> employeeMallApplyMap, Boolean applyFlag, Map<String, Optional<EmployeeDinnerRule>> employeeDinnerApplyMap, List<Integer> exceedIntegerFlag) {
        trainRuleList.stream()
                .filter(rule -> TrainRuleType.getAllowedType().contains(Optional.ofNullable(rule.getTrain_rule()).orElse(0)))
                .filter(rule -> {
                    if (exceedIntegerFlag == null || (exceedIntegerFlag.size() == 1 && exceedIntegerFlag.get(0) == 0)) {
                        return true;
                    } else {
                        if (exceedIntegerFlag.size() >= 2) {
                            return rule.getExceed_buy_type() == exceedIntegerFlag.get(0) || rule.getExceed_buy_type() == exceedIntegerFlag.get(1);
                        }
                        return rule.getExceed_buy_type() == exceedIntegerFlag.get(0);
                    }
                })
                .filter(rule -> {
                    if (applyFlag == null) {
                        return true;
                    } else {
                        return rule.getTrain_verify_flag().equals(applyFlag);
                    }
                })
                .forEach(rule -> addAuthorizationEmployeeContract(
                        results,
                        rule.getEmployee_id(),
                        employeeIdNameMap,
                        employeeIdDepartmentNameMap,
                        employeeAirRuleMap,
                        employeeHotelRuleMap,
                        employeeTrainRuleMap,
                        employeeMallRuleMap,
                        employeeTaxiRuleMap,
                        employeeDinnerRuleMap,
                        employeeAirApplyMap,
                        employeeHotelApplyMap,
                        employeeTrainApplyMap,
                        employeeTaxiApplyMap,
                        employeeMallApplyMap,
                        employeeDinnerApplyMap)
                );
    }

    /**
     * v2
     * 添加符合火车规则的员工到返回结果 results 中
     */
    private void addTrainAuthorizationEmployeeV2(List<AuthorizationEmployeeV2Contract> results, List<EmployeeTrainRuleContract> trainRuleList,
                                                 Map<String, String> employeeIdNameMap, Map<String, String> employeeIdDepartmentNameMap,
                                                 Map<String, Optional<Boolean>> employeeAirRuleMap, Map<String, Optional<Boolean>> employeeHotelRuleMap,
                                                 Map<String, Optional<Boolean>> employeeTrainRuleMap, Map<String, Optional<Boolean>> employeeMallRuleMap,
                                                 Map<String, Optional<Boolean>> employeeTaxiRuleMap, Map<String, Optional<Boolean>> employeeDinnerRuleMap, Map<String, Optional<Boolean>> employeeIntlAirRuleMap,
                                                 Map<String, Optional<EmployeeAirRuleContract>> employeeAirApplyMap, Map<String, Optional<EmployeeHotelRuleContract>> employeeHotelApplyMap,
                                                 Map<String, Optional<EmployeeTrainRuleContract>> employeeTrainApplyMap, Map<String, Optional<EmployeeTaxiRuleContract>> employeeTaxiApplyMap,
                                                 Map<String, Optional<EmployeeMallRule>> employeeMallApplyMap, Boolean applyFlag, Map<String, Optional<EmployeeDinnerRule>> employeeDinnerApplyMap, Map<String, Optional<EmployeeAirRuleContract>> employeeIntlAirApplyMap,
                                                 List<Integer> exceedTrainFlag) {
        trainRuleList.stream()
                .filter(rule -> TrainRuleType.getAllowedType().contains(Optional.ofNullable(rule.getTrain_rule()).orElse(0)))
                .filter(rule -> {
                    if (exceedTrainFlag == null || (exceedTrainFlag.size() == 1 && exceedTrainFlag.get(0) == 0)) {
                        return true;
                    } else {
                        if (exceedTrainFlag.size() >= 2) {
                            return rule.getTrain_exceed_buy_type() == exceedTrainFlag.get(0) || rule.getTrain_exceed_buy_type() == exceedTrainFlag.get(1);
                        }
                        return rule.getTrain_exceed_buy_type() == exceedTrainFlag.get(0);
                    }
                })
                .filter(rule -> {
                    if (applyFlag == null) {
                        return true;
                    } else {
                        return rule.getTrain_verify_flag().equals(applyFlag);
                    }
                })
                .forEach(rule -> addAuthorizationEmployeeContractV2(
                        results,
                        rule.getEmployee_id(),
                        employeeIdNameMap,
                        employeeIdDepartmentNameMap,
                        employeeAirRuleMap,
                        employeeHotelRuleMap,
                        employeeTrainRuleMap,
                        employeeMallRuleMap,
                        employeeTaxiRuleMap,
                        employeeDinnerRuleMap,
                        employeeIntlAirRuleMap,
                        employeeAirApplyMap,
                        employeeHotelApplyMap,
                        employeeTrainApplyMap,
                        employeeTaxiApplyMap,
                        employeeMallApplyMap,
                        employeeDinnerApplyMap,
                        employeeIntlAirApplyMap)
                );
    }

    /**
     * 添加符合机票规则的员工到返回结果 results 中
     */
    private void addAirAuthorizationEmployee(List<AuthorizationEmployeeContract> results, List<EmployeeAirRule> airRuleList, Map<String, String> employeeIdNameMap, Map<String, String> employeeIdDepartmentNameMap, Map<String, Optional<Boolean>> employeeAirRuleMap, Map<String, Optional<Boolean>> employeeHotelRuleMap, Map<String, Optional<Boolean>> employeeTrainRuleMap, Map<String, Optional<Boolean>> employeeMallRuleMap, Map<String, Optional<Boolean>> employeeTaxiRuleMap, Map<String, Optional<Boolean>> employeeDinnerRuleMap, Map<String, Optional<EmployeeAirRule>> employeeAirApplyMap, Map<String, Optional<EmployeeHotelRule>> employeeHotelApplyMap, Map<String, Optional<EmployeeTrainRule>> employeeTrainApplyMap, Map<String, Optional<EmployeeTaxiRule>> employeeTaxiApplyMap, Map<String, Optional<EmployeeMallRule>> employeeMallApplyMap, Boolean applyFlag, Map<String, Optional<EmployeeDinnerRule>> employeeDinnerApplyMap, List<Integer> exceedIntegerFlag) {
        airRuleList.stream()
                .filter(rule -> AirRuleType.getAllowedType().contains(Optional.ofNullable(rule.getAir_rule()).orElse(0)))
                .filter(rule -> {
                    if (exceedIntegerFlag == null || (exceedIntegerFlag.size() == 1 && exceedIntegerFlag.get(0) == 0)) {
                        return true;
                    } else {
                        if (exceedIntegerFlag.size() >= 2) {
                            return rule.getExceed_buy_type() == exceedIntegerFlag.get(0) || rule.getExceed_buy_type() == exceedIntegerFlag.get(1);
                        }
                        return rule.getExceed_buy_type() == exceedIntegerFlag.get(0);
                    }
                })
                .filter(rule -> {
                    if (applyFlag == null) {
                        return true;
                    } else {
                        return rule.getAir_verify_flag().equals(applyFlag);
                    }
                })
                .forEach(rule -> addAuthorizationEmployeeContract(
                        results,
                        rule.getEmployee_id(),
                        employeeIdNameMap,
                        employeeIdDepartmentNameMap,
                        employeeAirRuleMap,
                        employeeHotelRuleMap,
                        employeeTrainRuleMap,
                        employeeMallRuleMap,
                        employeeTaxiRuleMap,
                        employeeDinnerRuleMap,
                        employeeAirApplyMap,
                        employeeHotelApplyMap,
                        employeeTrainApplyMap,
                        employeeTaxiApplyMap,
                        employeeMallApplyMap,
                        employeeDinnerApplyMap)
                );
    }

    /**
     * v2
     * 添加符合机票规则的员工到返回结果 results 中
     */
    private void addAirAuthorizationEmployeeV2(List<AuthorizationEmployeeV2Contract> results, List<EmployeeAirRuleContract> airRuleList,
                                               Map<String, String> employeeIdNameMap, Map<String, String> employeeIdDepartmentNameMap,
                                               Map<String, Optional<Boolean>> employeeAirRuleMap, Map<String, Optional<Boolean>> employeeHotelRuleMap,
                                               Map<String, Optional<Boolean>> employeeTrainRuleMap, Map<String, Optional<Boolean>> employeeMallRuleMap,
                                               Map<String, Optional<Boolean>> employeeTaxiRuleMap, Map<String, Optional<Boolean>> employeeDinnerRuleMap, Map<String, Optional<Boolean>> employeeIntlAirRuleMap,
                                               Map<String, Optional<EmployeeAirRuleContract>> employeeAirApplyMap,
                                               Map<String, Optional<EmployeeHotelRuleContract>> employeeHotelApplyMap,
                                               Map<String, Optional<EmployeeTrainRuleContract>> employeeTrainApplyMap,
                                               Map<String, Optional<EmployeeTaxiRuleContract>> employeeTaxiApplyMap,
                                               Map<String, Optional<EmployeeMallRule>> employeeMallApplyMap, Boolean applyFlag,
                                               Map<String, Optional<EmployeeDinnerRule>> employeeDinnerApplyMap, Map<String, Optional<EmployeeAirRuleContract>> employeeIntlAirApplyMap, List<Integer> exceedAirFlag) {
        airRuleList.stream()
                .filter(rule -> AirRuleType.getAllowedType().contains(Optional.ofNullable(rule.getAir_rule()).orElse(0)))
                .filter(rule -> {
                    if (exceedAirFlag == null || (exceedAirFlag.size() == 1 && exceedAirFlag.get(0) == 0)) {
                        return true;
                    } else {
                        if (exceedAirFlag.size() >= 2) {
                            return rule.getAir_exceed_buy_type() == exceedAirFlag.get(0) || rule.getAir_exceed_buy_type() == exceedAirFlag.get(1);
                        }
                        return rule.getAir_exceed_buy_type() == exceedAirFlag.get(0);
                    }
                })
                .filter(rule -> {
                    if (applyFlag == null) {
                        return true;
                    } else {
                        return rule.getAir_verify_flag().equals(applyFlag);
                    }
                })
                .forEach(rule -> addAuthorizationEmployeeContractV2(
                        results,
                        rule.getEmployee_id(),
                        employeeIdNameMap,
                        employeeIdDepartmentNameMap,
                        employeeAirRuleMap,
                        employeeHotelRuleMap,
                        employeeTrainRuleMap,
                        employeeMallRuleMap,
                        employeeTaxiRuleMap,
                        employeeDinnerRuleMap,
                        employeeIntlAirRuleMap,
                        employeeAirApplyMap,
                        employeeHotelApplyMap,
                        employeeTrainApplyMap,
                        employeeTaxiApplyMap,
                        employeeMallApplyMap,
                        employeeDinnerApplyMap,
                        employeeIntlAirApplyMap)
                );
    }

    @Override
    public EmployeeListContract matchEmployeeWithRuleId(String companyId, String mallRuleId, Integer pageIndex, Integer pageSize) {
        return genEmployeeInfoContracts(companyId, mallRuleId, pageIndex, pageSize);
    }

    /**
     * 获取采购规则关联的员工信息
     */
    private EmployeeListContract genEmployeeInfoContracts(String companyId, String mallRuleId, Integer pageIndex, Integer pageSize) {
        EmployeeListContract employeeListContract = new EmployeeListContract();
        employeeListContract.setPageIndex(pageIndex);
        employeeListContract.setPageSize(pageSize);
        EmployeeMallRuleDto employeeMallRuleDto = iBaseEmployeeMallRuleExtService.selectEmployeeMallRuleList(companyId, mallRuleId, pageIndex, pageSize);
        employeeListContract.setTotalCount(employeeMallRuleDto.getCount());
        List<EmployeeMallRule> employeeMallRuleList = employeeMallRuleDto.getEmployeeMallRuleList();
        List<EmployeeInfoContract> employees = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(employeeMallRuleList)) {
            List<String> employeeIds = employeeMallRuleList.stream().map(
                    employeeMallRule -> employeeMallRule.getEmployeeId()
            ).distinct().collect(Collectors.toList());
            List<EmployeeContract> employeeList = iBaseOrganizationService.getEmployee(employeeIds, companyId);
            employees = employeeList.stream().map(employee ->
                    new EmployeeInfoContract(
                            employee.getId(),
                            employee.getName(),
                            employee.getPhone_num(),
                            employee.getThird_employee_id(),
                            Lists.newArrayList(new AllEmployeeOfCompanyContract.EmployeeListBean.OrgUnitListBean(employee.getOrg_id(), employee.getOrg_name(), employee.getThird_org_id()))
                    )
            ).collect(Collectors.toList());//从公司的所有员工中获取应用改规则的员工信息
        }
        employeeListContract.setEmployees(employees);
        return employeeListContract;
    }

    /**
     * 根据公司 id 获取采购规则列表
     */
    private List<MallRule> getMallRules(String companyId, String ruleId, String ruleName) {
        MallRuleExample mallRuleExample = new MallRuleExample();
        MallRuleExample.Criteria mallRuleExampleCriteria = mallRuleExample.createCriteria();
        mallRuleExampleCriteria.andCompanyIdEqualTo(companyId.trim());
        if (!StringUtils.isEmpty(ruleId)) {
            mallRuleExampleCriteria.andIdEqualTo(ruleId);
        }
        if (!StringUtils.isEmpty(ruleName)) {
            mallRuleExampleCriteria.andNameLike("%" + ruleName + "%");
        }
        return mallRuleMapper.selectByExample(mallRuleExample);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, value = "fenbeitong")
    public void setRules(SetRulesReqContract setRulesReq, String companyId) throws SaasException {
        validate(setRulesReq, companyId);
        for (String employeeId : setRulesReq.getEmployee_ids()) {
            setRule(employeeId, companyId, null, setRulesReq.getBiz_trip_policy(), setRulesReq.getCar_policy(), setRulesReq.getMall_policy(), setRulesReq.getDinner_policy());
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, value = "fenbeitong")
    public void setRulesV2(SetRulesReqContract setRulesReq, String companyId, String clientVersion, String clientType) throws SaasException {
        validateV2(setRulesReq, companyId);
        checkVersion(clientVersion, setRulesReq);
        this.checkRuleParameter(setRulesReq);
        for (String employeeId : setRulesReq.getEmployee_ids()) {
            setRuleV2(employeeId, companyId, null, setRulesReq.getTrain_policy(), setRulesReq.getAir_policy(), setRulesReq.getHotel_policy(),
                    setRulesReq.getCar_policy(), setRulesReq.getMall_policy(), setRulesReq.getDinner_policy(), clientVersion, clientType, setRulesReq.getIntl_air_policy());
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, value = "fenbeitong")
    public void modifyRules(SetRulesReqContract setRulesReq, String companyId) throws SaasException {
        if (CollectionUtils.isEmpty(setRulesReq.getEmployee_ids())) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull, "用户列表为空");
        }

        if (setRulesReq.getBiz_trip_policy() == null && setRulesReq.getCar_policy() == null && setRulesReq.getMall_policy() == null && setRulesReq.getDinner_policy() == null) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull, "规则为空");
        }

        OrgUnitEmployeeSimpleExample example = new OrgUnitEmployeeSimpleExample();
        example.createCriteria().andCompanyIdEqualTo(companyId).andEmployeeIdIn(setRulesReq.getEmployee_ids());
        if (setRulesReq.getEmployee_ids().size() != orgUnitEmployeeSimpleMapper.countByExample(example)) {
            throw new SaasException(GlobalResponseCode.UserNotInCompany);
        }

        if (setRulesReq.getDinner_policy() != null && setRulesReq.getDinner_policy().getDinner_priv_flag()) {
            CompanyRuleExample companyRuleExample = new CompanyRuleExample();
            companyRuleExample.createCriteria().andCompanyIdEqualTo(companyId).andDinnerRuleEqualTo(0);
            List<CompanyRuleDTO> companyEmployees = iCompanyRuleService.selectByExample(companyRuleExample);
            if (!CollectionUtils.isEmpty(companyEmployees)) {
                throw new SaasException(GlobalResponseCode.DinnerJurisdictionError, "您的企业尚未开启用餐业务，如有需要请联系分贝通客服");
            }
        }

        setRulesReq.getEmployee_ids().forEach(employeeId ->
                modifyRule(employeeId,
                        companyId,
                        null,
                        setRulesReq.getBiz_trip_policy(),
                        setRulesReq.getCar_policy(),
                        setRulesReq.getMall_policy(),
                        setRulesReq.getDinner_policy())
        );
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, value = "fenbeitong")
    public void modifyRulesV2(SetRulesReqContract setRulesReq, String companyId, String clientVersion, String clientType) throws SaasException {

        if (CollectionUtils.isEmpty(setRulesReq.getEmployee_ids())) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull, "用户列表为空");
        }

        if (setRulesReq.getHotel_policy() == null && setRulesReq.getTrain_policy() == null
                && setRulesReq.getAir_policy() == null && setRulesReq.getCar_policy() == null && setRulesReq.getMall_policy() == null && setRulesReq.getDinner_policy() == null && setRulesReq.getIntl_air_policy() == null) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull, "规则为空");
        }
        this.checkVersion(clientVersion, setRulesReq);            //兼容老版本两个权限开关问题
        this.checkRuleParameter(setRulesReq);                // 校验权限数据 脏数据问题

        OrgUnitEmployeeSimpleExample example = new OrgUnitEmployeeSimpleExample();
        example.createCriteria().andCompanyIdEqualTo(companyId).andEmployeeIdIn(setRulesReq.getEmployee_ids());
        if (setRulesReq.getEmployee_ids().size() != orgUnitEmployeeSimpleMapper.countByExample(example)) {
            throw new SaasException(GlobalResponseCode.UserNotInCompany);
        }

        if (setRulesReq.getDinner_policy() != null && setRulesReq.getDinner_policy().getDinner_priv_flag()) {
            CompanyRuleExample companyRuleExample = new CompanyRuleExample();
            companyRuleExample.createCriteria().andCompanyIdEqualTo(companyId).andDinnerRuleEqualTo(0);
            List<CompanyRuleDTO> companyEmployees = iCompanyRuleService.selectByExample(companyRuleExample);
            if (!CollectionUtils.isEmpty(companyEmployees)) {
                throw new SaasException(GlobalResponseCode.DinnerJurisdictionError, "您的企业尚未开启用餐业务，如有需要请联系分贝通客服");
            }
        }

        setRulesReq.getEmployee_ids().forEach(employeeId ->
                modifyRuleV2(employeeId,
                        companyId,
                        null,
                        setRulesReq.getTrain_policy(),
                        setRulesReq.getAir_policy(),
                        setRulesReq.getHotel_policy(),
                        setRulesReq.getCar_policy(),
                        setRulesReq.getMall_policy(),
                        setRulesReq.getDinner_policy(),
                        setRulesReq.getIntl_air_policy()
                        , clientVersion, clientType
                )
        );
    }

    private void checkVersion(String clientVersion, SetRulesReqContract employeeOperate) {
        String currentVersion = "2.0.4";
        AirPolicyBean airPolicyBean = employeeOperate.getAir_policy();
        IntlAirPolicyBean intlAirPolicyBean = employeeOperate.getIntl_air_policy();
        HotelPolicyBean hotelPolicyBean = employeeOperate.getHotel_policy();
        TrainPolicyBean trainPolicyBean = employeeOperate.getTrain_policy();
        LOGGER.info("当前版本号是 clientVersion={}, 权限规则入参是:getAir_policy={} ,getIntl_air_policy={},getHotel_policy={} , getTrain_policy={}", clientVersion, JsonUtils.toJson(airPolicyBean), JsonUtils.toJson(intlAirPolicyBean), JsonUtils.toJson(hotelPolicyBean), JsonUtils.toJson(trainPolicyBean));
        if (StringUtils.isNotEmpty(clientVersion)) {  //兼容老版本 如果只传入允许字段 则为其他的字段均取允许本人字段
            LOGGER.info("APP端...");
            if (VersionTool.compare(clientVersion, currentVersion) < 0) {
                LOGGER.info("旧版本调用...");
                if (airPolicyBean != null && airPolicyBean.getAir_priv_flag() != null && airPolicyBean.getAir_other_flag() == null) {
                    employeeOperate.getAir_policy().setAir_other_flag(employeeOperate.getAir_policy().getAir_priv_flag());
                }
                if (intlAirPolicyBean != null && intlAirPolicyBean.getAir_priv_flag() != null && intlAirPolicyBean.getAir_other_flag() == null) {
                    employeeOperate.getIntl_air_policy().setAir_other_flag(employeeOperate.getIntl_air_policy().getAir_priv_flag());
                }
                if (hotelPolicyBean != null && hotelPolicyBean.getHotel_priv_flag() != null && hotelPolicyBean.getHotel_other_flag() == null) {
                    employeeOperate.getHotel_policy().setHotel_other_flag(employeeOperate.getHotel_policy().getHotel_priv_flag());
                }
                if (trainPolicyBean != null && trainPolicyBean.getTrain_priv_flag() != null && trainPolicyBean.getTrain_other_flag() == null) {
                    employeeOperate.getTrain_policy().setTrain_other_flag(employeeOperate.getTrain_policy().getTrain_priv_flag());
                }
            }

        } else {  // WEB端兼容老版本
            LOGGER.info("WEB端...");
            if (airPolicyBean != null && airPolicyBean.getAir_priv_flag() != null && airPolicyBean.getAir_other_flag() == null) {
                employeeOperate.getAir_policy().setAir_other_flag(employeeOperate.getAir_policy().getAir_priv_flag());
            }
            if (intlAirPolicyBean != null && intlAirPolicyBean.getAir_priv_flag() != null && intlAirPolicyBean.getAir_other_flag() == null) {
                employeeOperate.getIntl_air_policy().setAir_other_flag(employeeOperate.getIntl_air_policy().getAir_priv_flag());
            }
            if (hotelPolicyBean != null && hotelPolicyBean.getHotel_priv_flag() != null && hotelPolicyBean.getHotel_other_flag() == null) {
                employeeOperate.getHotel_policy().setHotel_other_flag(employeeOperate.getHotel_policy().getHotel_priv_flag());
            }
            if (trainPolicyBean != null && trainPolicyBean.getTrain_priv_flag() != null && trainPolicyBean.getTrain_other_flag() == null) {
                employeeOperate.getTrain_policy().setTrain_other_flag(employeeOperate.getTrain_policy().getTrain_priv_flag());
            }

        }
    }


    /**
     * 校验用户权限数据问题
     *
     * @param employeeOperate
     */
    private void checkRuleParameter(SetRulesReqContract employeeOperate) {
        AirPolicyBean airPolicyBean = employeeOperate.getAir_policy();
        IntlAirPolicyBean intlAirPolicyBean = employeeOperate.getIntl_air_policy();
        HotelPolicyBean hotelPolicyBean = employeeOperate.getHotel_policy();
        TrainPolicyBean trainPolicyBean = employeeOperate.getTrain_policy();
        LOGGER.info("第三方校验权限参数  权限规则入参是:airPolicyBean={} ,intlAirPolicyBean={},hotelPolicyBean={} , trainPolicyBean={}", JsonUtils.toJson(airPolicyBean), JsonUtils.toJson(intlAirPolicyBean), JsonUtils.toJson(hotelPolicyBean), JsonUtils.toJson(trainPolicyBean));

        if (airPolicyBean != null) {

            if (airPolicyBean.getAir_priv_flag() != null && !airPolicyBean.getAir_priv_flag()) {  //如果本人没有权限         则无权 --给其他员工/非企业员工预定
                airPolicyBean.setAir_other_flag(false);
                airPolicyBean.setUnemployee_air(false);
            }

            if (airPolicyBean.getAir_other_flag() != null && !airPolicyBean.getAir_other_flag()) { //如果无权给其他员工预定   则无权  --给非企业员工预定
                airPolicyBean.setUnemployee_air(false);
            }
        }

        if (intlAirPolicyBean != null) {

            if (intlAirPolicyBean.getAir_priv_flag() != null && !intlAirPolicyBean.getAir_priv_flag()) {
                intlAirPolicyBean.setAir_other_flag(false);
                intlAirPolicyBean.setUnemployee_air(false);
            }
            if (intlAirPolicyBean.getAir_other_flag() != null && !intlAirPolicyBean.getAir_other_flag()) {
                intlAirPolicyBean.setUnemployee_air(false);
            }

        }
        if (hotelPolicyBean != null) {

            if (hotelPolicyBean.getHotel_priv_flag() != null && !hotelPolicyBean.getHotel_priv_flag()) {
                hotelPolicyBean.setHotel_other_flag(false);
                hotelPolicyBean.setUnemployee_hotel(false);
            }
            if (hotelPolicyBean.getHotel_other_flag() != null && !hotelPolicyBean.getHotel_other_flag()) {
                hotelPolicyBean.setUnemployee_hotel(false);
            }
        }

        if (trainPolicyBean != null) {

            if (trainPolicyBean.getTrain_priv_flag() != null && !trainPolicyBean.getTrain_priv_flag()) {
                trainPolicyBean.setTrain_other_flag(false);
                trainPolicyBean.setUnemployee_train(false);
            }
            if (trainPolicyBean.getTrain_other_flag() != null && !trainPolicyBean.getTrain_other_flag()) {
                trainPolicyBean.setUnemployee_train(false);
            }
        }
        LOGGER.info("第三方校验权限参数结果:  airPolicyBean={} ,intlAirPolicyBean={},hotelPolicyBean={} , trainPolicyBean={}", JsonUtils.toJson(airPolicyBean), JsonUtils.toJson(intlAirPolicyBean), JsonUtils.toJson(hotelPolicyBean), JsonUtils.toJson(trainPolicyBean));
    }

    private void validate(SetRulesReqContract setRulesReq, String companyId) throws SaasException {
        if (CollectionUtils.isEmpty(setRulesReq.getEmployee_ids())) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull, "用户列表为空");
        }
        if (setRulesReq != null && setRulesReq.getBiz_trip_policy() != null) {
            if (setRulesReq.getBiz_trip_policy().isRule_limit_flag()) {
                if (StringUtils.isEmpty(setRulesReq.getBiz_trip_policy().getRule_id())) {
                    throw new SaasException(GlobalResponseCode.ParameterIsNull);
                }
            }
        }
        if (setRulesReq != null && setRulesReq.getCar_policy() != null) {
            if (setRulesReq.getCar_policy().isRule_limit_flag()) {
                if (setRulesReq.getCar_policy().getRule_id() == null) {
                    throw new SaasException(GlobalResponseCode.ParameterIsNull);
                }
            }
        }
        //TODO 上线之后打开
        if (setRulesReq != null && setRulesReq.getMall_policy() != null) {
            if (setRulesReq.getMall_policy().isRule_limit_flag()) {
                if (StringUtils.isEmpty(setRulesReq.getMall_policy().getRule_id())) {
                    throw new SaasException(GlobalResponseCode.ParameterIsNull);
                }
            }
        }
        OrgUnitEmployeeSimpleExample example = new OrgUnitEmployeeSimpleExample();
        example.createCriteria().andCompanyIdEqualTo(companyId).andEmployeeIdIn(setRulesReq.getEmployee_ids());
        if (setRulesReq.getEmployee_ids().size() != orgUnitEmployeeSimpleMapper.countByExample(example)) {
            throw new SaasException(GlobalResponseCode.UserNotInCompany);
        }
        if (setRulesReq != null && setRulesReq.getDinner_policy() != null) {
            if (setRulesReq.getDinner_policy().isRule_limit_flag()) {
                if (StringUtils.isEmpty(setRulesReq.getDinner_policy().getRule_id())) {
                    throw new SaasException(GlobalResponseCode.ParameterIsNull);
                }
            }
        }
    }

    private void validateV2(SetRulesReqContract setRulesReq, String companyId) throws SaasException {
        if (CollectionUtils.isEmpty(setRulesReq.getEmployee_ids())) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull, "用户列表为空");
        }
        if (setRulesReq != null && setRulesReq.getTrain_policy() != null) {   //火车
            if (setRulesReq.getTrain_policy().getRule_limit_flag()) {
                if (StringUtils.isEmpty(setRulesReq.getTrain_policy().getRule_id())) {
                    throw new SaasException(GlobalResponseCode.ParameterIsNull);
                }
            }
        }
        if (setRulesReq != null && setRulesReq.getHotel_policy() != null) {  //酒店
            if (setRulesReq.getHotel_policy().getRule_limit_flag()) {
                if (StringUtils.isEmpty(setRulesReq.getHotel_policy().getRule_id())) {
                    throw new SaasException(GlobalResponseCode.ParameterIsNull);
                }
            }
        }
        if (setRulesReq != null && setRulesReq.getIntl_air_policy() != null) {  //国际机票
            if (setRulesReq.getIntl_air_policy().getRule_limit_flag()) {
                if (StringUtils.isEmpty(setRulesReq.getIntl_air_policy().getRule_id())) {
                    throw new SaasException(GlobalResponseCode.ParameterIsNull);
                }
            }
        }

        if (setRulesReq != null && setRulesReq.getAir_policy() != null) {  //国内机票
            if (setRulesReq.getAir_policy().getRule_limit_flag()) {
                if (StringUtils.isEmpty(setRulesReq.getAir_policy().getRule_id())) {
                    throw new SaasException(GlobalResponseCode.ParameterIsNull);
                }
            }
        }
        if (setRulesReq != null && setRulesReq.getCar_policy() != null) {  //用车
            if (setRulesReq.getCar_policy().isRule_limit_flag()) {
                if (setRulesReq.getCar_policy().getRule_id() == null) {
                    throw new SaasException(GlobalResponseCode.ParameterIsNull);
                }
            }
        }
        //TODO 上线之后打开
        if (setRulesReq != null && setRulesReq.getMall_policy() != null) {  //
            if (setRulesReq.getMall_policy().isRule_limit_flag()) {
                if (StringUtils.isEmpty(setRulesReq.getMall_policy().getRule_id())) {
                    throw new SaasException(GlobalResponseCode.ParameterIsNull);
                }
            }
        }
        OrgUnitEmployeeSimpleExample example = new OrgUnitEmployeeSimpleExample();
        example.createCriteria().andCompanyIdEqualTo(companyId).andEmployeeIdIn(setRulesReq.getEmployee_ids());
        if (setRulesReq.getEmployee_ids().size() != orgUnitEmployeeSimpleMapper.countByExample(example)) {
            throw new SaasException(GlobalResponseCode.UserNotInCompany);
        }
        if (setRulesReq != null && setRulesReq.getDinner_policy() != null) {
            if (setRulesReq.getDinner_policy().isRule_limit_flag()) {
                if (StringUtils.isEmpty(setRulesReq.getDinner_policy().getRule_id())) {
                    throw new SaasException(GlobalResponseCode.ParameterIsNull);
                }
            }
        }
    }

    private void validate(MallMatchEmployeeContract mallMatchEmployeeContract, String companyId) throws SaasException {
        if (null == mallMatchEmployeeContract) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        List<String> employeeIds = mallMatchEmployeeContract.getEmployee_ids();
        if (CollectionUtils.isEmpty(employeeIds)) {
            return;
        }
        if (StringUtils.isEmpty(companyId)) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }

        List<OrgUnitEmployeeSimpleDTO> result = Lists.newArrayList();
        try {
            List<List<String>> employeeIdsLet500List = Lists.partition(employeeIds, 500);
            for (List<String> employeeIdsLet500 : employeeIdsLet500List) {
                LOGGER.error("employeeIds.size():{}， employeeIdsLet500.size()", employeeIds.size(), employeeIdsLet500.size());
                // 每次最多500个id
                result.addAll(orgUnitService.listOrgUnitEmployeeSimpleDTO(companyId, employeeIdsLet500));
            }
//        OrgUnitEmployeeSimpleExample ouese = new OrgUnitEmployeeSimpleExample();
//        ouese.createCriteria().andCompanyIdEqualTo(companyId).andEmployeeIdIn(mallMatchEmployeeContract.getEmployee_ids());
        } catch (Exception e) {
            LOGGER.error("Failed to invoke listOrgUnitEmployeeSimpleDTO(), companyId:{}, employeeIds:{}", companyId,
                    employeeIds);
            throw new SaasException(GlobalResponseCode.UserNotInCompany);
        }

        if (mallMatchEmployeeContract.getEmployee_ids().size() != result.size()) {
            throw new SaasException(GlobalResponseCode.UserNotInCompany);
        }
    }

    private void validate(MallRuleDeleteContract deleteContract, String company) throws SaasException {
        if (null == deleteContract) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        if (CollectionUtils.isEmpty(deleteContract.getRule_ids())) {
            throw new SaasException(GlobalResponseCode.RuleNameNotNull);
        }
        List<EmployeeMallRule> mallRuleList = iBaseEmployeeMallRuleService.queryEmployeeMallRuleByRuleIds(company, deleteContract.getRule_ids());
        if (CollectionUtils.isNotEmpty(mallRuleList)) {
            LOGGER.info("以下规则已经被应用到员工或部门,不能删除");
            mallRuleList.stream().map(EmployeeMallRule::getManualMallRuleId).collect(Collectors.toSet()).forEach(LOGGER::info);
            throw new SaasException(GlobalResponseCode.ExistReferenceErr);
        }

        MallRuleExample mallRuleExample = new MallRuleExample();
        mallRuleExample.createCriteria().andCompanyIdEqualTo(company)
                .andIdIn(deleteContract.getRule_ids());
        if (deleteContract.getRule_ids().size() != mallRuleMapper.countByExample(mallRuleExample)) {
            throw new SaasException(GlobalResponseCode.RuleIdNotExistInCompany);
        }
    }

    @Transactional(value = "fenbeitong")
    @Override
    public void setRule(String userId, String companyId, String orgUnitId, BizTripPolicyBean bizTripPolicyBean, CarPolicyBean carPolicyBean, MallPolicyBean mallPolicyBean, DinnerPolicyBean dinnerPolicyBean) throws SaasException {
        EmployeeAirRule airRule = RuleTransferUtil.getEmployeeAirRule(userId, bizTripPolicyBean, companyId);
        EmployeeHotelRule hotelRule = RuleTransferUtil.getEmployeeHotelRule(userId, bizTripPolicyBean, companyId);
        EmployeeTrainRule trainRule = RuleTransferUtil.getEmployeeTrainRule(userId, bizTripPolicyBean, companyId);
        EmployeeMallRule mallRule = RuleTransferUtil.getEmployeeMallRule(userId, mallPolicyBean, companyId);
        EmployeeTaxiRule taxiRule = RuleTransferUtil.getEmployeeTaxiRule(userId, companyId, carPolicyBean);
        EmployeeDinnerRule dinnerRule = RuleTransferUtil.getEmployeeDinerRule(userId, dinnerPolicyBean, companyId);
        try {
            if (airRule.getAir_rule_flag() != null) {
                iBaseEmployeeAirRuleService.updateByPrimaryKeySelective(airRule);
            }
            if (hotelRule.getHotel_rule_flag() != null) {
                iBaseEmployeeHotelRuleService.updateByPrimaryKeySelective(hotelRule);
            }
            if (trainRule.getTrain_rule_flag() != null) {
                iBaseEmployeeTrainRuleService.updateByPrimaryKeySelective(trainRule);
            }
            if (mallPolicyBean != null && mallRule.getMallRuleFlag() != null) {
                iBaseEmployeeMallRuleService.updateByPrimaryKeySelective(mallRule);
            }
            if (carPolicyBean != null && taxiRule.getTaxiRuleFlag() != null) {
                iBaseEmployeeTaxiRuleService.updateByPrimaryKeySelective(taxiRule);
            }

            if (dinnerPolicyBean != null && dinnerRule.getDinner_rule_flag() != null) {
                iBaseEmployeeDinnerRuleService.updateByPrimaryKeySelective(dinnerRule);
            }
        } catch (Exception e) {
            LOGGER.error("修改规则接口异常:" + JsonUtils.toJson(airRule) + JsonUtils.toJson(hotelRule) + JsonUtils.toJson(trainRule) + JsonUtils.toJson(mallRule) + JsonUtils.toJson(taxiRule) + e.getMessage());
            throw new SaasException(GlobalResponseCode.SAVERULEERROR);
        }
    }

    @Transactional(value = "fenbeitong")
    @Override
    public void setRuleV2(String userId, String companyId, String orgUnitId, TrainPolicyBean trainPolicyBean, AirPolicyBean airPolicyBean, HotelPolicyBean hotelPolicyBean,
                          CarPolicyBean carPolicyBean, MallPolicyBean mallPolicyBean, DinnerPolicyBean dinnerPolicyBean, String clientVersion, String clientType, IntlAirPolicyBean intlAirPolicy) throws SaasException {
        EmployeeAirRule airRule = RuleTransferUtil.getEmployeeAirRuleV2(userId, airPolicyBean, companyId);
        EmployeeHotelRule hotelRule = RuleTransferUtil.getEmployeeHotelRuleV2(userId, hotelPolicyBean, companyId);
        EmployeeTrainRule trainRule = RuleTransferUtil.getEmployeeTrainRuleV2(userId, trainPolicyBean, companyId);
        EmployeeMallRule mallRule = RuleTransferUtil.getEmployeeMallRule(userId, mallPolicyBean, companyId);
        EmployeeTaxiRule taxiRule = RuleTransferUtil.getEmployeeTaxiRule(userId, companyId, carPolicyBean);
        EmployeeDinnerRule dinnerRule = RuleTransferUtil.getEmployeeDinerRuleV2(userId, dinnerPolicyBean, clientVersion, clientType, companyId);
        EmployeeIntlAirRule intlAirRule = RuleTransferUtil.getEmployeeIntlAirRuleV2(userId, intlAirPolicy, companyId);
        try {
            if (airRule.getAir_rule_flag() != null) {
                iBaseEmployeeAirRuleService.updateByPrimaryKeySelective(airRule);
            }
            if (hotelRule.getHotel_rule_flag() != null) {
                iBaseEmployeeHotelRuleService.updateByPrimaryKeySelective(hotelRule);
            }
            if (trainRule.getTrain_rule_flag() != null) {
                iBaseEmployeeTrainRuleService.updateByPrimaryKeySelective(trainRule);
            }
            if (mallPolicyBean != null && mallRule.getMallRuleFlag() != null) {
                iBaseEmployeeMallRuleService.updateByPrimaryKeySelective(mallRule);
            }
            if (carPolicyBean != null && taxiRule.getTaxiRuleFlag() != null) {
                iBaseEmployeeTaxiRuleService.updateByPrimaryKeySelective(taxiRule);
            }
            if (dinnerPolicyBean != null && dinnerRule.getDinner_rule_flag() != null) {
                iBaseEmployeeDinnerRuleService.updateByPrimaryKeySelective(dinnerRule);
            }
            if (intlAirRule.getAir_rule_flag() != null) {
                iBaseEmployeeIntlAirRuleService.updateByPrimaryKeySelective(intlAirRule);
            }
        } catch (Exception e) {
            LOGGER.error("修改规则接口异常:" + JsonUtils.toJson(airRule) + JsonUtils.toJson(hotelRule) + JsonUtils.toJson(trainRule) + JsonUtils.toJson(mallRule) + JsonUtils.toJson(taxiRule) + e.getMessage());
            throw new SaasException(GlobalResponseCode.SAVERULEERROR);
        }
    }

    @Transactional(value = "fenbeitong")
    @Override
    public void modifyRule(String userId, String companyId, String orgUnitId, BizTripPolicyBean bizTripPolicyBean, CarPolicyBean carPolicyBean, MallPolicyBean mallPolicyBean, DinnerPolicyBean dinnerPolicyBean) {
        EmployeeAirRule airRule = RuleTransferUtil.getEmployeeAirRule(userId, bizTripPolicyBean, companyId); // 从差旅规则中剥离出机票权限
        EmployeeHotelRule hotelRule = RuleTransferUtil.getEmployeeHotelRule(userId, bizTripPolicyBean, companyId);// 从差旅规则中剥离出酒店权限
        EmployeeTrainRule trainRule = RuleTransferUtil.getEmployeeTrainRule(userId, bizTripPolicyBean, companyId);// 从差旅规则中剥离出火车权限
        EmployeeMallRule mallRule = RuleTransferUtil.getEmployeeMallRule(userId, mallPolicyBean, companyId); //构建采购规则
        EmployeeTaxiRule taxiRule = RuleTransferUtil.getEmployeeTaxiRule(userId, companyId, carPolicyBean);//构建用车规则
        EmployeeDinnerRule dinnerRule = RuleTransferUtil.getEmployeeDinerRule(userId, dinnerPolicyBean, companyId);//构建用餐规则

        if (bizTripPolicyBean != null && airRule.getAir_rule_flag() != null) {
            iBaseEmployeeAirRuleService.updateByPrimaryKeySelective(airRule);//设置机票权限
        }
        if (bizTripPolicyBean != null && hotelRule.getHotel_rule_flag() != null) {
            iBaseEmployeeHotelRuleService.updateByPrimaryKeySelective(hotelRule);//设置酒店权限
        }

        if (bizTripPolicyBean != null && trainRule.getTrain_rule_flag() != null) {
            iBaseEmployeeTrainRuleService.updateByPrimaryKeySelective(trainRule);//设置火车权限
        }

        if (mallPolicyBean != null && mallRule.getMallRuleFlag() != null) {
            iBaseEmployeeMallRuleService.updateByPrimaryKeySelective(mallRule);//设置采购权限
        }
        if (carPolicyBean != null && taxiRule.getTaxiRuleFlag() != null) {
            iBaseEmployeeTaxiRuleService.updateByPrimaryKeySelective(taxiRule);
        } //设置用车权限

        if (dinnerPolicyBean != null && dinnerRule.getDinner_rule_flag() != null) {
            iBaseEmployeeDinnerRuleService.updateByPrimaryKeySelective(dinnerRule);//设置用餐权限
        }

    }

    @Transactional(value = "fenbeitong")
    @Override
    public void modifyRuleV2(String userId, String companyId, String orgUnitId, TrainPolicyBean trainPolicyBean, AirPolicyBean airPolicyBean, HotelPolicyBean hotelPolicyBean,
                             CarPolicyBean carPolicyBean, MallPolicyBean mallPolicyBean, DinnerPolicyBean dinnerPolicyBean, IntlAirPolicyBean intlAirPolicyBean, String clientVersion, String clientType) {
        EmployeeAirRule airRule = RuleTransferUtil.getEmployeeAirRuleV2(userId, airPolicyBean, companyId); // 从差旅规则中剥离出机票权限
        EmployeeHotelRule hotelRule = RuleTransferUtil.getEmployeeHotelRuleV2(userId, hotelPolicyBean, companyId);// 从差旅规则中剥离出酒店权限
        EmployeeTrainRule trainRule = RuleTransferUtil.getEmployeeTrainRuleV2(userId, trainPolicyBean, companyId);// 从差旅规则中剥离出火车权限
        EmployeeMallRule mallRule = RuleTransferUtil.getEmployeeMallRule(userId, mallPolicyBean, companyId); //构建采购规则
        EmployeeTaxiRule taxiRule = RuleTransferUtil.getEmployeeTaxiRuleV2(userId, companyId, carPolicyBean, clientVersion, clientType);//构建用车规则
        EmployeeDinnerRule dinnerRule = RuleTransferUtil.getEmployeeDinerRuleV2(userId, dinnerPolicyBean, clientVersion, clientType, companyId);//构建用餐规则
        EmployeeIntlAirRule intlAirRule = RuleTransferUtil.getEmployeeIntlAirRuleV2(userId, intlAirPolicyBean, companyId);//构建国际机票规则

        if (airPolicyBean != null && airRule.getAir_rule_flag() != null) {
            iBaseEmployeeAirRuleService.updateByPrimaryKeySelective(airRule);//设置机票权限
        }
        if (hotelPolicyBean != null && hotelRule.getHotel_rule_flag() != null) {
            iBaseEmployeeHotelRuleService.updateByPrimaryKeySelective(hotelRule);//设置酒店权限
        }

        if (trainPolicyBean != null && trainRule.getTrain_rule_flag() != null) {
            iBaseEmployeeTrainRuleService.updateByPrimaryKeySelective(trainRule);//设置火车权限
        }

        if (mallPolicyBean != null && mallRule.getMallRuleFlag() != null) {
            iBaseEmployeeMallRuleService.updateByPrimaryKeySelective(mallRule);//设置采购权限
        }

        if (carPolicyBean != null && taxiRule.getTaxiRuleFlag() != null) {
            iBaseEmployeeTaxiRuleService.updateByPrimaryKeySelective(taxiRule);//设置用车权限
        } //设置用车权限

        if (dinnerPolicyBean != null && dinnerRule.getDinner_rule_flag() != null) {
            iBaseEmployeeDinnerRuleService.updateByPrimaryKeySelective(dinnerRule);//设置用餐权限
        }

        if (intlAirPolicyBean != null && intlAirRule.getAir_rule_flag() != null) {
            iBaseEmployeeIntlAirRuleService.updateByPrimaryKeySelective(intlAirRule);//设置国际机票权限
        }

    }

    @Transactional(value = "fenbeitong")
    @Override
    public void putRule(String userId, String companyId, String orgUnitId, BizTripPolicyBean bizTripPolicyBean, CarPolicyBean carPolicyBean, MallPolicyBean mallPolicyBean) throws SaasException {
        EmployeeAirRule airRule = RuleTransferUtil.getEmployeeAirRule(userId, bizTripPolicyBean, companyId);
        EmployeeHotelRule hotelRule = RuleTransferUtil.getEmployeeHotelRule(userId, bizTripPolicyBean, companyId);
        EmployeeTrainRule trainRule = RuleTransferUtil.getEmployeeTrainRule(userId, bizTripPolicyBean, companyId);
        EmployeeMallRule mallRule = RuleTransferUtil.getEmployeeMallRule(userId, mallPolicyBean, companyId);
        EmployeeTaxiRule taxiRule = RuleTransferUtil.getEmployeeTaxiRule(userId, companyId, carPolicyBean);
        try {
            iBaseEmployeeAirRuleService.insertSelective(airRule);
            iBaseEmployeeHotelRuleService.insertSelective(hotelRule);
            iBaseEmployeeTrainRuleService.insertSelective(trainRule);
            iBaseEmployeeMallRuleService.insertSelective(mallRule);
            iBaseEmployeeTaxiRuleService.insertSelective(taxiRule);
        } catch (Exception e) {
            LOGGER.error("保存规则接口异常:" + JsonUtils.toJson(airRule) + JsonUtils.toJson(hotelRule) + JsonUtils.toJson(trainRule) + JsonUtils.toJson(mallRule) + JsonUtils.toJson(taxiRule) + e.getMessage());
            throw new SaasException(GlobalResponseCode.SAVERULEERROR);
        }
    }

    @Transactional(value = "fenbeitong")
    @Override
    public void putRuleV2(String userId, String companyId, String orgUnitId, TrainPolicyBean trainPolicyBean, AirPolicyBean airPolicyBean, HotelPolicyBean hotelPolicyBean,
                          CarPolicyBean carPolicyBean, MallPolicyBean mallPolicyBean) throws SaasException {
        EmployeeAirRule airRule = RuleTransferUtil.getEmployeeAirRuleV2(userId, airPolicyBean, companyId);
        EmployeeHotelRule hotelRule = RuleTransferUtil.getEmployeeHotelRuleV2(userId, hotelPolicyBean, companyId);
        EmployeeTrainRule trainRule = RuleTransferUtil.getEmployeeTrainRuleV2(userId, trainPolicyBean, companyId);
        EmployeeMallRule mallRule = RuleTransferUtil.getEmployeeMallRule(userId, mallPolicyBean, companyId);
        EmployeeTaxiRule taxiRule = RuleTransferUtil.getEmployeeTaxiRule(userId, companyId, carPolicyBean);
        try {
            iBaseEmployeeAirRuleService.insertSelective(airRule);
            iBaseEmployeeHotelRuleService.insertSelective(hotelRule);
            iBaseEmployeeTrainRuleService.insertSelective(trainRule);
            iBaseEmployeeMallRuleService.insertSelective(mallRule);
            iBaseEmployeeTaxiRuleService.insertSelective(taxiRule);
        } catch (Exception e) {
            LOGGER.error("保存规则接口异常:" + JsonUtils.toJson(airRule) + JsonUtils.toJson(hotelRule) + JsonUtils.toJson(trainRule) + JsonUtils.toJson(mallRule) + JsonUtils.toJson(taxiRule) + e.getMessage());
            throw new SaasException(GlobalResponseCode.SAVERULEERROR);
        }
    }

    private List<OrgUnit> getDepartment(String company) throws SaasException {
        OrgUnitExample orgUnitExample = new OrgUnitExample();
        orgUnitExample.createCriteria().andCompanyIdEqualTo(company);
        return orgUnitMapper.selectByExample(orgUnitExample);
    }

    private List<OrgUnitEmployeeSimple> getSimple(String company) throws SaasException {
        OrgUnitEmployeeSimpleExample simpleExample = new OrgUnitEmployeeSimpleExample();
        simpleExample.createCriteria().andCompanyIdEqualTo(company);
        return orgUnitEmployeeSimpleMapper.selectByExample(simpleExample);
    }

    private List<EmployeeContract> getEmployee(List<String> employeeIds, String companyId) throws SaasException {
        return iBaseOrganizationService.getEmployee(employeeIds, companyId);
    }

    private List<EmployeeAirRule> getEmployeeAirRule(List<String> employeeIds, String companyId) throws SaasException {
        return iBaseEmployeeAirRuleService.queryEmployeeAirRule(companyId, employeeIds);
    }

    private List<EmployeeHotelRule> getEmployeeHotelRule(List<String> employeeIds, String companyId) throws SaasException {
        return iBaseEmployeeHotelRuleService.queryEmployeeHotelRuleList(companyId, employeeIds);
    }

    private List<EmployeeTrainRule> getEmployeeTrainRule(List<String> employeeIds, String companyId) throws SaasException {
        return iBaseEmployeeTrainRuleService.queryEmployeeTrainRuleList(companyId, employeeIds);
    }

    private List<EmployeeMallRule> getEmployeeMallRule(List<String> employeeIds, String companyId) throws SaasException {
        return iBaseEmployeeMallRuleService.queryEmployeeMallRuleList(companyId, employeeIds);
    }

    private List<EmployeeTaxiRule> getCompanyEmployee(List<String> employeeIds, String companyId) throws SaasException {
        return iBaseEmployeeTaxiRuleService.queryEmployeeTaxiRuleList(companyId, employeeIds);
    }

    private List<EmployeeDinnerRule> getEmployeeDinnerRule(List<String> employeeIds, String companyId) throws SaasException {
        EmployeeDinnerRuleExample example = new EmployeeDinnerRuleExample();
        example.createCriteria().andEmployee_idIn(employeeIds).andCompany_idEqualTo(companyId);
        return iBaseEmployeeDinnerRuleService.selectByExample(example);
    }

    private Optional<Boolean> getRuleFlag(Integer rule, BizType bizType) {
        if (rule == null) {
            return Optional.empty();
        }
        switch (bizType) {
            case Mall:
                return Optional.ofNullable(MallRuleType.getEnum(rule)).map(MallRuleType::allow);
            case Hotel:
                return Optional.ofNullable(HotelRuleType.getEnum(rule)).map(HotelRuleType::allow);
            case Taxi:
                return Optional.ofNullable(TaxiRuleType.getEnum(rule)).map(TaxiRuleType::allow);
            case Train:
                return Optional.ofNullable(TrainRuleType.getEnum(rule)).map(TrainRuleType::allow);
            case AirPlane:
                return Optional.ofNullable(AirRuleType.getEnum(rule)).map(AirRuleType::allow);
            case InternationalAirPlane:
                return Optional.ofNullable(AirRuleType.getEnum(rule)).map(AirRuleType::allow);
            default:
                return Optional.empty();
        }
    }

    private void addAuthorizationEmployeeContract(List<AuthorizationEmployeeContract> results, String employeeId,
                                                  Map<String, String> employeeIdNameMap,
                                                  Map<String, String> employeeIdDepartmentNameMap,
                                                  Map<String, Optional<Boolean>> employeeAirRuleMap,
                                                  Map<String, Optional<Boolean>> employeeHotelRuleMap,
                                                  Map<String, Optional<Boolean>> employeeTrainRuleMap,
                                                  Map<String, Optional<Boolean>> employeeMallRuleMap,
                                                  Map<String, Optional<Boolean>> employeeTaxiRuleMap,
                                                  Map<String, Optional<Boolean>> employeeDinnerRuleMap,
                                                  Map<String, Optional<EmployeeAirRule>> employeeAirApplyMap,
                                                  Map<String, Optional<EmployeeHotelRule>> employeeHotelApplyMap,
                                                  Map<String, Optional<EmployeeTrainRule>> employeeTrainApplyMap,
                                                  Map<String, Optional<EmployeeTaxiRule>> employeeTaxiApplyMap,
                                                  Map<String, Optional<EmployeeMallRule>> employeeMallApplyMap,
                                                  Map<String, Optional<EmployeeDinnerRule>> employeeDinnerApplyMap) {


        if (employeeDinnerApplyMap != null && employeeDinnerApplyMap.size() > 0) {
            System.out.print("employeeId==" + employeeId);
            System.out.print("json=" + JsonUtils.toJson(employeeAirApplyMap.get(employeeId)));
            results.add(new AuthorizationEmployeeContract(
                    employeeId,
                    employeeIdNameMap.getOrDefault(employeeId, ""),
                    employeeIdDepartmentNameMap.getOrDefault(employeeId, ""),
                    employeeMallRuleMap.getOrDefault(employeeId, Optional.empty()).orElse(null),//采购权限
                    employeeAirRuleMap.getOrDefault(employeeId, Optional.empty()).orElse(null),//机票权限
                    employeeHotelRuleMap.getOrDefault(employeeId, Optional.empty()).orElse(null),//酒店权限
                    employeeTrainRuleMap.getOrDefault(employeeId, Optional.empty()).orElse(null),//火车权限
                    employeeTaxiRuleMap.getOrDefault(employeeId, Optional.empty()).orElse(null),//用车权限
                    employeeDinnerRuleMap.getOrDefault(employeeId, Optional.empty()).orElse(null),//用餐权限

                    employeeTaxiApplyMap.get(employeeId).map(EmployeeTaxiRule::getExceedBuyFlag).orElse(null),//用车超标
                    employeeMallApplyMap.get(employeeId).map(EmployeeMallRule::getExceedBuyFlag).orElse(null),//采购超标
                    employeeAirApplyMap.get(employeeId).map(EmployeeAirRule::getExceed_buy_flag).orElse(null),//机票超标


                    employeeDinnerApplyMap.get(employeeId).map(EmployeeDinnerRule::getExceed_buy_flag).orElse(null),//用餐超标

                    employeeAirApplyMap.get(employeeId).map(EmployeeAirRule::getAir_verify_flag).orElse(null),// 机票审批
                    employeeHotelApplyMap.get(employeeId).map(EmployeeHotelRule::getHotel_verify_flag).orElse(null),//酒店审批
                    employeeTrainApplyMap.get(employeeId).map(EmployeeTrainRule::getTrain_verify_flag).orElse(null)//火车审批
            ));
        } else {
            results.add(new AuthorizationEmployeeContract(
                    employeeId,
                    employeeIdNameMap.getOrDefault(employeeId, ""),
                    employeeIdDepartmentNameMap.getOrDefault(employeeId, ""),
                    employeeMallRuleMap.getOrDefault(employeeId, Optional.empty()).orElse(null),//采购权限
                    employeeAirRuleMap.getOrDefault(employeeId, Optional.empty()).orElse(null),//机票权限
                    employeeHotelRuleMap.getOrDefault(employeeId, Optional.empty()).orElse(null),//酒店权限
                    employeeTrainRuleMap.getOrDefault(employeeId, Optional.empty()).orElse(null),//火车权限
                    employeeTaxiRuleMap.getOrDefault(employeeId, Optional.empty()).orElse(null),//用车权限
                    employeeDinnerRuleMap.getOrDefault(employeeId, Optional.empty()).orElse(null),//用餐权限

                    employeeTaxiApplyMap.get(employeeId).map(EmployeeTaxiRule::getExceedBuyFlag).orElse(null),//用车超标
                    employeeMallApplyMap.get(employeeId).map(EmployeeMallRule::getExceedBuyFlag).orElse(null),//采购超标
                    employeeAirApplyMap.get(employeeId).map(EmployeeAirRule::getExceed_buy_flag).orElse(null),//机票超标
                    null,//用餐超标

                    employeeAirApplyMap.get(employeeId).map(EmployeeAirRule::getAir_verify_flag).orElse(null),// 机票审批
                    employeeHotelApplyMap.get(employeeId).map(EmployeeHotelRule::getHotel_verify_flag).orElse(null),//酒店审批
                    employeeTrainApplyMap.get(employeeId).map(EmployeeTrainRule::getTrain_verify_flag).orElse(null)//火车审批
            ));
        }
    }

    private void addAuthorizationEmployeeContractV2(List<AuthorizationEmployeeV2Contract> results, String employeeId,
                                                    Map<String, String> employeeIdNameMap,
                                                    Map<String, String> employeeIdDepartmentNameMap,
                                                    Map<String, Optional<Boolean>> employeeAirRuleMap,
                                                    Map<String, Optional<Boolean>> employeeHotelRuleMap,
                                                    Map<String, Optional<Boolean>> employeeTrainRuleMap,
                                                    Map<String, Optional<Boolean>> employeeMallRuleMap,
                                                    Map<String, Optional<Boolean>> employeeTaxiRuleMap,
                                                    Map<String, Optional<Boolean>> employeeDinnerRuleMap,
                                                    Map<String, Optional<Boolean>> employeeIntlAirRuleMap,
                                                    Map<String, Optional<EmployeeAirRuleContract>> employeeAirApplyMap,
                                                    Map<String, Optional<EmployeeHotelRuleContract>> employeeHotelApplyMap,
                                                    Map<String, Optional<EmployeeTrainRuleContract>> employeeTrainApplyMap,
                                                    Map<String, Optional<EmployeeTaxiRuleContract>> employeeTaxiApplyMap,
                                                    Map<String, Optional<EmployeeMallRule>> employeeMallApplyMap,
                                                    Map<String, Optional<EmployeeDinnerRule>> employeeDinnerApplyMap,
                                                    Map<String, Optional<EmployeeAirRuleContract>> employeeIntlAirApplyMap) {


        if (employeeDinnerApplyMap != null && employeeDinnerApplyMap.size() > 0) {
            System.out.print("employeeId==" + employeeId);
            System.out.print("json=" + JsonUtils.toJson(employeeAirApplyMap.get(employeeId)));
            results.add(new AuthorizationEmployeeV2Contract(
                    employeeId,
                    employeeIdNameMap.getOrDefault(employeeId, ""),
                    employeeIdDepartmentNameMap.getOrDefault(employeeId, ""),
                    employeeMallRuleMap.getOrDefault(employeeId, Optional.empty()).orElse(null),//采购权限
                    employeeAirRuleMap.getOrDefault(employeeId, Optional.empty()).orElse(null),//机票权限
                    employeeHotelRuleMap.getOrDefault(employeeId, Optional.empty()).orElse(null),//酒店权限
                    employeeTrainRuleMap.getOrDefault(employeeId, Optional.empty()).orElse(null),//火车权限
                    employeeTaxiRuleMap.getOrDefault(employeeId, Optional.empty()).orElse(null),//用车权限
                    employeeDinnerRuleMap.getOrDefault(employeeId, Optional.empty()).orElse(null),//用餐权限
                    employeeIntlAirRuleMap.getOrDefault(employeeId, Optional.empty()).orElse(null),//国际机票权限

                    employeeTaxiApplyMap.get(employeeId).map(EmployeeTaxiRuleContract::getTaxi_exceed_buy_type).orElse(null),//用车超标
                    employeeMallApplyMap.get(employeeId).map(EmployeeMallRule::getExceedBuyFlag).orElse(null),//采购超标
                    employeeAirApplyMap.get(employeeId).map(EmployeeAirRuleContract::getAir_exceed_buy_type).orElse(null),//机票超标
                    employeeIntlAirApplyMap.get(employeeId).map(EmployeeAirRuleContract::getAir_exceed_buy_type).orElse(null),//国际机票超标
                    employeeDinnerApplyMap.get(employeeId).map(EmployeeDinnerRule::getExceed_buy_flag).orElse(null),//用餐超标

                    employeeAirApplyMap.get(employeeId).map(EmployeeAirRuleContract::getAir_verify_flag).orElse(null),// 机票审批
                    employeeHotelApplyMap.get(employeeId).map(EmployeeHotelRuleContract::getHotel_verify_flag).orElse(null),//酒店审批
                    employeeTrainApplyMap.get(employeeId).map(EmployeeTrainRuleContract::getTrain_verify_flag).orElse(null),//火车审批
                    employeeTrainApplyMap.get(employeeId).map(EmployeeTrainRuleContract::getTrain_exceed_buy_type).orElse(null),
                    employeeHotelApplyMap.get(employeeId).map(EmployeeHotelRuleContract::getHotel_exceed_buy_type).orElse(null),
                    employeeIntlAirApplyMap.get(employeeId).map(EmployeeAirRuleContract::getAir_verify_flag).orElse(null)// 机票审批

            ));
        } else {
            results.add(new AuthorizationEmployeeV2Contract(
                    employeeId,
                    employeeIdNameMap.getOrDefault(employeeId, ""),
                    employeeIdDepartmentNameMap.getOrDefault(employeeId, ""),
                    employeeMallRuleMap.getOrDefault(employeeId, Optional.empty()).orElse(null),//采购权限
                    employeeAirRuleMap.getOrDefault(employeeId, Optional.empty()).orElse(null),//机票权限
                    employeeHotelRuleMap.getOrDefault(employeeId, Optional.empty()).orElse(null),//酒店权限
                    employeeTrainRuleMap.getOrDefault(employeeId, Optional.empty()).orElse(null),//火车权限
                    employeeTaxiRuleMap.getOrDefault(employeeId, Optional.empty()).orElse(null),//用车权限
                    employeeDinnerRuleMap.getOrDefault(employeeId, Optional.empty()).orElse(null),//用餐权限
                    employeeIntlAirRuleMap.getOrDefault(employeeId, Optional.empty()).orElse(null),//国际机票权限


                    employeeTaxiApplyMap.get(employeeId).map(EmployeeTaxiRuleContract::getTaxi_exceed_buy_type).orElse(null),//用车超标
                    employeeMallApplyMap.get(employeeId).map(EmployeeMallRule::getExceedBuyFlag).orElse(null),//采购超标
                    employeeAirApplyMap.get(employeeId).map(EmployeeAirRuleContract::getAir_exceed_buy_type).orElse(null),//机票超标
                    employeeIntlAirApplyMap.get(employeeId).map(EmployeeAirRuleContract::getAir_exceed_buy_type).orElse(null),//国际机票超标
                    null,//用餐超标

                    employeeAirApplyMap.get(employeeId).map(EmployeeAirRuleContract::getAir_verify_flag).orElse(null),// 机票审批
                    employeeHotelApplyMap.get(employeeId).map(EmployeeHotelRuleContract::getHotel_verify_flag).orElse(null),//酒店审批
                    employeeTrainApplyMap.get(employeeId).map(EmployeeTrainRuleContract::getTrain_verify_flag).orElse(null),//火车审批
                    employeeTrainApplyMap.get(employeeId).map(EmployeeTrainRuleContract::getTrain_exceed_buy_type).orElse(null),
                    employeeHotelApplyMap.get(employeeId).map(EmployeeHotelRuleContract::getHotel_exceed_buy_type).orElse(null),
                    employeeIntlAirApplyMap.get(employeeId).map(EmployeeAirRuleContract::getAir_verify_flag).orElse(null)// 机票审批
            ));
        }

    }

    /**
     * 删除用车规则
     *
     * @param deleteContract
     * @param companyId
     * @throws SaasException
     */
    @Override
    @Transactional(value = "fenbeitong")
    public void deleteCarRule(RuleDeleteContract deleteContract, String companyId, String userId) throws SaasException {
        List<Integer> ruleIds = Lists.newArrayList();
        List<String> ruleList = deleteContract.getRule_ids();
        for (String str : ruleList) {
            ruleIds.add(Integer.parseInt(str));
        }
        //查询规则ID是否存在
        TaxiRuleExample taxiRuleExample = new TaxiRuleExample();
        taxiRuleExample.createCriteria().andCompanyIdEqualTo(companyId)
                .andIdIn(ruleIds);
        List<TaxiRule> taxiRuleList = taxiRuleMapper.selectByExample(taxiRuleExample);
        if (CollectionUtils.isEmpty(taxiRuleList) || deleteContract.getRule_ids().size() != taxiRuleMapper.countByExample(taxiRuleExample)) {
            throw new SaasException(GlobalResponseCode.RuleIdNotExistInCompany);
        }
        EmployeeTaxiRuleUpdateApplyDTO employeeTaxiRuleUpdateApplyDTO = new EmployeeTaxiRuleUpdateApplyDTO();
        employeeTaxiRuleUpdateApplyDTO.setCompanyId(companyId);
        employeeTaxiRuleUpdateApplyDTO.setOperatorId(userId);
        employeeTaxiRuleUpdateApplyDTO.setRuleType(Integer.parseInt(String.valueOf(taxiRuleList.get(0).getType())));
        employeeTaxiRuleUpdateApplyDTO.setRuleIdList(ruleIds);
        employeeTaxiRuleUpdateApplyDTO.setDeleteAll(false);
        iBaseEmployeeTaxiRuleExtService.updateManualRuleIdByRuleId(employeeTaxiRuleUpdateApplyDTO);
        // 删除之前先查询，为了推送操作日志消息拿数据
        TaxiRule taxiRule = taxiRuleMapper.selectByPrimaryKey(Integer.valueOf(ruleList.get(0)));
        TaxiRuleExample taxiRuleDeleteExample = new TaxiRuleExample();
        taxiRuleDeleteExample.createCriteria().andCompanyIdEqualTo(companyId).andIdIn(ruleIds).andTypeEqualTo(taxiRuleList.get(0).getType());
        taxiRuleMapper.deleteByExample(taxiRuleDeleteExample);
        //添加规则日志
        addOperateLogRule(companyId, JSON.toJSONString(deleteContract.getRule_ids()), userId, JSON.toJSONString(deleteContract), 3, CategoryTypeEnum.Taxi.getCode());
        if (ruleList.size() == 1) {
            companyLogKafkaProducerService.sendCompanyLogMsg(CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Taxi_Rule, LogOperateActionEnum.DELETE,
                    LogOperateObjectEnum.TAXI_RULE, taxiRule.getName()));
        } else {
            companyLogKafkaProducerService.sendCompanyLogMsg(CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Taxi_Rule, LogOperateActionEnum.BATCH_DELETE,
                    LogOperateObjectEnum.TAXI_RULE, ""));
        }
    }

    /**
     * 删除差旅规则
     *
     * @param deleteContract
     * @param companyId
     * @throws SaasException
     */
    @Override
    @Transactional(value = "fenbeitong")
    public void deleteBizTripRule(RuleDeleteContract deleteContract, String companyId, String userId) throws SaasException {
        //查询规则ID是否存在
        BizTripRuleExample bizTripRuleExample = new BizTripRuleExample();
        bizTripRuleExample.createCriteria().andCompanyIdEqualTo(companyId)
                .andIdIn(deleteContract.getRule_ids());
        if (deleteContract.getRule_ids().size() != bizTripRuleMapper.countByExample(bizTripRuleExample)) {
            throw new SaasException(GlobalResponseCode.RuleIdNotExistInCompany);
        }
        //根据差旅规则id查询机、酒、火规则id
        BizTripRuleExample bizTripRuleSelectExample = new BizTripRuleExample();
        bizTripRuleSelectExample.createCriteria().andCompanyIdEqualTo(companyId).andIdIn(deleteContract.getRule_ids());
        List<BizTripRule> bizTripRuleList = bizTripRuleMapper.selectByExample(bizTripRuleSelectExample);
        for (BizTripRule bizTripRule : bizTripRuleList) {
            String airRuleId = bizTripRule.getAirRuleId();
            String hotelRuleId = bizTripRule.getHotelRuleId();
            String trainRuleId = bizTripRule.getTrainRuleId();
            if (!StringUtils.isEmpty(airRuleId)) {
                iBaseEmployeeAirRuleExtService.updateManualRuleIdByRuleId(companyId, userId, airRuleId);
                AirRuleExample airRuleExample = new AirRuleExample();
                airRuleExample.createCriteria().andCompanyIdEqualTo(companyId).andIdEqualTo(airRuleId);
                airRuleMapper.deleteByExample(airRuleExample);
            }
            if (!StringUtils.isEmpty(hotelRuleId)) {
                iBaseEmployeeHotelRuleExtService.updateManualRuleIdByRuleId(companyId, userId, hotelRuleId);
                HotelRuleExample hotelRuleExample = new HotelRuleExample();
                hotelRuleExample.createCriteria().andCompanyIdEqualTo(companyId).andIdEqualTo(hotelRuleId);
                hotelRuleMapper.deleteByExample(hotelRuleExample);
            }
            if (!StringUtils.isEmpty(trainRuleId)) {
                iBaseEmployeeTrainRuleExtService.updateManualRuleIdByRuleId(companyId, userId, trainRuleId);
                TrainRuleExample trainRuleExample = new TrainRuleExample();
                trainRuleExample.createCriteria().andCompanyIdEqualTo(companyId).andIdEqualTo(trainRuleId);
                trainRuleMapper.deleteByExample(trainRuleExample);
            }
        }
        BizTripRuleExample bizTripRuleDeleteExample = new BizTripRuleExample();
        bizTripRuleDeleteExample.createCriteria().andCompanyIdEqualTo(companyId).andIdIn(deleteContract.getRule_ids());
        bizTripRuleMapper.deleteByExample(bizTripRuleDeleteExample);
    }

    @Override
    @Transactional(value = "fenbeitong")
    public List<MyRuleDisployContract> queryMyRule(String userId, String companyId, String token, String clientVersion) throws SaasException, ParseException, IOException, HttpException {
        //处理酒店展示问题
        String oldVersion = "1.9.96";
        List<MyRuleDisployContract> myRuleDisployContracts = Lists.newArrayList();
        // 查询是否个人垫付模式
        Map<String, Boolean> funcMap = iPrivilegeService.queryFunctionMoudle(CodeTypeEnums.CustomAuth.getKey(), companyId, userId, CustomCodeType.M_ADVANCE_PAYMENT.getKey());
        log.info("查询是否个人垫付模式, funcMap={}", JsonUtils.toJson(funcMap));
        Boolean advancePayment = funcMap.get(CustomCodeType.M_ADVANCE_PAYMENT.getKey());
        String personalPayName = advancePayment ? "个人自费" : "个人支付";
        // 机票权限规则
        EmployeeAirRule employeeAirRule = iBaseEmployeeAirRuleService.queryEmployeeAirRuleByPrimaryKey(userId, companyId);
        log.info("查询机票权限规则, employeeAirRule={}", JsonUtils.toJson(employeeAirRule));
        List<MyRuleDisployContract.NameDescItem> airNameDescItemList = Lists.newArrayList();
        MyRuleDisployContract myRuleAir = new MyRuleDisployContract();
        myRuleAir.setCategory_type(7);
        myRuleAir.setCategory_name("国内机票");
        myRuleAir.setAuth_flag(0);
        myRuleAir.setApprove_flag(0);
        myRuleAir.setView_flag(false);
        myRuleAir.setOrder_approve_flag(0);
        myRuleAir.setRefund_approve_flag(0);
        myRuleAir.setChange_approve_flag(0);
        if (employeeAirRule != null) {
            myRuleAir.setOrder_approve_flag(employeeAirRule.getAir_order_verify_flag() == true ? 1 : 0);
            if (employeeAirRule.getAir_rule() != null) {
                myRuleAir.setAuth_flag(employeeAirRule.getAir_rule() == 4 ? 1 : 0);
            }
            myRuleAir.setApprove_flag(employeeAirRule.getAir_verify_flag() == true ? 1 : 0);
            myRuleAir.setRefund_approve_flag(employeeAirRule.getRefund_ticket_type());
            myRuleAir.setChange_approve_flag(employeeAirRule.getChanges_ticket_type());
            String manualAirRuleId = employeeAirRule.getManual_air_rule_id();
            Boolean airRuleFlag = employeeAirRule.getAir_rule_flag();
            if (airRuleFlag && !StringUtils.isEmpty(manualAirRuleId)) {
                AirRule airRule = airRuleMapper.selectByPrimaryKey(manualAirRuleId);
                log.info("查询机票权限规则, airRule={}", JsonUtils.toJson(airRule));
                if (airRule != null) {
                    //可订舱位
                    Boolean airCabinFlag = airRule.getAirCabinFlag();
                    String airCabinType = airRule.getAirCabinType();
                    if (airCabinFlag) {
                        if (airCabinType != null) {
                            String[] cabinTypeList = airCabinType.split(",");
                            List<String> cabinNameList = Arrays.stream(cabinTypeList).map(key -> AirCabinType.getName(ObjUtils.toInteger(key, 0))).collect(Collectors.toList());
                            airNameDescItemList.add(new MyRuleDisployContract.NameDescItem("可订舱位", StringUtils.join(cabinNameList, ",")));
                        }
                    } else {
                        airNameDescItemList.add(new MyRuleDisployContract.NameDescItem("可订舱位", "无限制"));
                    }
                    Boolean airPriceFlag = airRule.getAirPriceFlag();
                    Boolean airDiscountFlag = airRule.getAirDiscountFlag();
                    if (!airPriceFlag && !airDiscountFlag) {
                        airNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单张票价", "无限制"));
                    } else if (airPriceFlag) {
                        airNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单张票价",
                                "需低于¥" + BigDecimalTool.formatMoney(airRule.getAirUnitPrice())));
                    } else {
                        if (org.springframework.util.StringUtils.isEmpty(airRule.getAirDiscount())) {
                            airNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单张票价", "无限制"));
                        } else {
                            airNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单张票价", "折扣需低于" + ObjUtils.toString(airRule.getAirDiscount().multiply(new BigDecimal(10)).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue()) + "折"));
                        }
                    }
                    //最低价限制
                    Integer lowPriceFlag = airRule.getLowPriceFlag();
                    Integer lowPriceTime = airRule.getLowPriceTime();
                    if (lowPriceFlag == 0) {
                        airNameDescItemList.add(new MyRuleDisployContract.NameDescItem("最低价限制", "无限制"));
                    } else {
                        String StopoverFlightMsg = FilterStopoverFlightFlag.ALL.equalsTo(airRule.getFilterStopoverFlightFlag())
                                ? "" : FilterStopoverFlightFlag.valueOf(airRule.getFilterStopoverFlightFlag()).getDesc();
                        String descFormat = StrUtils.formatString(GlobalResponseCode.OrderCheckAirMinPriceNoAuth.getMsg(),
                                airRule.getLowPriceTime(),
                                AirPortCityEnum.getDescribe(airRule.getAirPortCityFlag()),
                                StopoverFlightMsg);
                        airNameDescItemList.add(new MyRuleDisployContract.NameDescItem("最低价限制", descFormat));
                    }
                    //提前预定天数
                    airNameDescItemList.add(new MyRuleDisployContract.NameDescItem("提前预订", this.getPreDayStr(airRule.getPrivDayMin(), airRule.getPrivDayMax())));
                    //起飞时段
                    airNameDescItemList.add(new MyRuleDisployContract.NameDescItem("起飞时段", DayTypeChange(airRule.getId(), 2)));
                    // 行程耗时
                    if (airRule.getTimeConsumeFlag()) {
                        airNameDescItemList.add(new MyRuleDisployContract.NameDescItem("行程耗时",
                                "火车(高铁/动车)最短耗时超过" + airRule.getTimeConsumeValue() + "小时"));
                    } else {
                        airNameDescItemList.add(new MyRuleDisployContract.NameDescItem("行程耗时", "不限制"));
                    }
                    if (airRule.getDistanceLimitFlag()) {
                        airNameDescItemList.add(new MyRuleDisployContract.NameDescItem("行程距离",
                                "城市直线距离大于" + airRule.getDistanceLimitValue() + "公里"));
                    } else {
                        airNameDescItemList.add(new MyRuleDisployContract.NameDescItem("行程距离", "不限制"));
                    }
                }
            }
        }
        myRuleAir.setDetail_rules(airNameDescItemList);
        myRuleDisployContracts.add(myRuleAir);

        //国际机票
        EmployeeIntlAirRule employeeIntlAirRule = iBaseEmployeeIntlAirRuleService.queryEmployeeIntlAirRuleByPrimaryKey(userId, companyId);
        log.info("国际机票, employeeIntlAirRule={}", JsonUtils.toJson(employeeIntlAirRule));
        List<MyRuleDisployContract.NameDescItem> intlAirNameDescItemList = Lists.newArrayList();
        MyRuleDisployContract myRuleIntlAir = new MyRuleDisployContract();
        myRuleIntlAir.setCategory_type(40);
        myRuleIntlAir.setCategory_name("国际机票");
        myRuleIntlAir.setAuth_flag(0);
        myRuleIntlAir.setApprove_flag(0);
        myRuleIntlAir.setView_flag(false);
        myRuleIntlAir.setOrder_approve_flag(0);
        myRuleIntlAir.setRefund_approve_flag(0);
        myRuleIntlAir.setChange_approve_flag(0);
        if (employeeIntlAirRule != null) {
            myRuleIntlAir.setOrder_approve_flag(employeeIntlAirRule.getIntl_air_order_verify_flag() == true ? 1 : 0);
            if (employeeIntlAirRule.getAir_rule() != null) {
                myRuleIntlAir.setAuth_flag(employeeIntlAirRule.getAir_rule() == 4 ? 1 : 0);
            }
            myRuleIntlAir.setApprove_flag(employeeIntlAirRule.getAir_verify_flag() == true ? 1 : 0);
            String manualIntlAirRuleId = employeeIntlAirRule.getManual_air_rule_id();
            Boolean intlAirRuleFlag = employeeIntlAirRule.getAir_rule_flag();
            if (intlAirRuleFlag && !StringUtils.isEmpty(manualIntlAirRuleId)) {
                IntlAirRule airRule = intlAirRuleMapper.selectByPrimaryKey(manualIntlAirRuleId);
                log.info("国际机票, intlAirRuleMapper={}", JsonUtils.toJson(intlAirRuleMapper));
                if (airRule != null) {
                    //可订舱位
                    Boolean airCabinFlag = airRule.getAirCabinFlag();
                    String airCabinType = airRule.getAirCabinType();
                    if (airCabinFlag) {
                        if (airCabinType != null) {
                            String[] cabinTypeList = airCabinType.split(",");
                            List<String> cabinNameList = Arrays.stream(cabinTypeList).map(key -> AirCabinType.getName(ObjUtils.toInteger(key, 0))).collect(Collectors.toList());
                            intlAirNameDescItemList.add(new MyRuleDisployContract.NameDescItem("可订舱位", StringUtils.join(cabinNameList, ",")));
                        }
                    } else {
                        if (airCabinType != null) {
                            intlAirNameDescItemList.add(new MyRuleDisployContract.NameDescItem("可订舱位", "请联系管理员设置"));
                        }
                    }
                    Boolean airPriceFlag = ObjUtils.ifNull(airRule.getAirPriceFlag(), false);
                    if (!airPriceFlag) {
                        intlAirNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单张票价", "无限制"));
                    } else {
                        intlAirNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单张票价", "需低于¥" + BigDecimalTool.formatMoney(airRule.getAirUnitPrice())));
                    }
                    //提前预定天数
                    intlAirNameDescItemList.add(new MyRuleDisployContract.NameDescItem("提前预订", this.getPreDayStr(airRule.getPrivDayMin(), airRule.getPrivDayMax())));
                }
            }
        }
        myRuleIntlAir.setDetail_rules(intlAirNameDescItemList);
        myRuleDisployContracts.add(myRuleIntlAir);

        //酒店
        EmployeeHotelRule employeeHotelRule = iBaseEmployeeHotelRuleService.queryEmployeeHotelRuleByPrimaryKey(userId, companyId);
        log.info("酒店, employeeHotelRule={}", JsonUtils.toJson(employeeHotelRule));
        List<MyRuleDisployContract.NameDescItem> hotelNameDescItemList = Lists.newArrayList();
        MyRuleDisployContract myRuleHotel = new MyRuleDisployContract();
        myRuleHotel.setCategory_type(11);
        myRuleHotel.setCategory_name("国内酒店");
        myRuleHotel.setAuth_flag(0);
        myRuleHotel.setApprove_flag(0);
        myRuleHotel.setView_flag(true);
        myRuleHotel.setOrder_approve_flag(0);
        myRuleHotel.setRefund_approve_flag(0);
        myRuleHotel.setChange_approve_flag(0);
        if (employeeHotelRule != null) {
            myRuleHotel.setOrder_approve_flag(employeeHotelRule.getHotel_order_verify_flag() == true ? 1 : 0);
            if (employeeHotelRule.getHotel_rule() != null) {
                myRuleHotel.setAuth_flag(employeeHotelRule.getHotel_rule() == 2 ? 1 : 0);
            }
            if (myRuleHotel.getAuth_flag() == 0) {
                myRuleHotel.setView_flag(false);
            }
            myRuleHotel.setApprove_flag(employeeHotelRule.getHotel_verify_flag() == true ? 1 : 0);
            myRuleHotel.setRefund_approve_flag(employeeHotelRule.getRefund_ticket_type());
            String manualHotelRuleId = employeeHotelRule.getManual_hotel_rule_id();
            Boolean hotelRuleFlag = employeeHotelRule.getHotel_rule_flag();
            if (hotelRuleFlag && StringUtils.isNotBlank(manualHotelRuleId)) {
                HotelRule hotelRule = hotelRuleMapper.selectByPrimaryKey(manualHotelRuleId);
                log.info("酒店, hotelRule={}", JsonUtils.toJson(hotelRule));
                if (hotelRule != null) {
                    Integer type = hotelRule.getType();
                    myRuleHotel.setType(type);
                    Boolean ruleLimit = hotelRule.getRuleLimit();
                    myRuleHotel.setRule_limit(ruleLimit);
                    //高级模式
                    if (HotelPriceSetType.SENIORPATTERN.getCode() == type.intValue()) {
                        myRuleHotel.setView_flag(false);
                        if (!hotelRule.getRuleLimit()) {
                            hotelNameDescItemList.add(new MyRuleDisployContract.NameDescItem("房费限制", "无限制"));
                        }
                        //老版本高级模式展示
                        if (VersionTool.compare(clientVersion, oldVersion) < 0) {
                            hotelNameDescItemList.add(new MyRuleDisployContract.NameDescItem("房费限制", "请升级至最新版本查看"));
                        }
                    } else {
                        //查看价格限制类型 1:均价限制 2:最高价限制
                        CompanyRuleDTO companyRule = iCompanyRuleService.queryByCompanyId(companyId);
                        log.info("酒店, companyRule={}", JsonUtils.toJson(companyRule));
                        Integer limitType = companyRule.getHotelRulePriceType();
                        Boolean firstTierFlag = hotelRule.getFirstTierFlag();
                        Boolean secondTierFlag = hotelRule.getSecondTierFlag();
                        Boolean otherTierFlag = hotelRule.getOtherTierFlag();
                        if (!firstTierFlag && !secondTierFlag && !otherTierFlag) {
                            myRuleHotel.setView_flag(false);
                            hotelNameDescItemList.add(new MyRuleDisployContract.NameDescItem("房费限制", "无限制"));
                        } else {
                            StringBuffer sb = new StringBuffer();
                            if (firstTierFlag) {
                                if (HotelPriceLimitType.HIGHESTLIMIT.getCode() == limitType.intValue()) {
                                    sb.append("一线城市需低于¥" + hotelRule.getFirstTierPrice()).append("\n");
                                } else {
                                    sb.append("一线城市平均每晚房费需低于" + hotelRule.getFirstTierPrice()).append("元\n");
                                }
                            } else {
                                sb.append("一线城市无限制").append("\n");
                            }
                            if (secondTierFlag) {
                                if (HotelPriceLimitType.HIGHESTLIMIT.getCode() == limitType.intValue()) {
                                    sb.append("二线城市需低于¥" + hotelRule.getSecondTierPrice()).append("\n");
                                } else {
                                    sb.append("二线城市平均每晚房费需低于" + hotelRule.getSecondTierPrice()).append("元\n");
                                }

                            } else {
                                sb.append("二线城市无限制").append("\n");
                            }
                            if (otherTierFlag) {
                                if (HotelPriceLimitType.HIGHESTLIMIT.getCode() == limitType.intValue()) {
                                    sb.append("其他城市需低于¥" + hotelRule.getOtherTierPrice()).append("\n");
                                } else {
                                    sb.append("其他城市平均每晚房费需低于" + hotelRule.getOtherTierPrice()).append("元\n");
                                }
                            } else {
                                sb.append("其他城市无限制").append("\n");
                            }
                            hotelNameDescItemList.add(new MyRuleDisployContract.NameDescItem("房费限制", sb.toString()));
                        }
                    }
                    //酒店-个人支付
                    hotelNameDescItemList.add(new MyRuleDisployContract.NameDescItem(personalPayName, employeeHotelRule.getPersonal_pay() ? "开启" : "关闭"));
                    Boolean levelFlag = hotelRule.getLevelFlag();
                    if (levelFlag) {
                        String level = hotelRule.getLevel();
                        String[] levelList = level.split(",");
                        List<String> cabinNameList = Arrays.stream(levelList).map(key -> HotelLevel.getName(ObjUtils.toInteger(key, 0))).collect(Collectors.toList());
                        hotelNameDescItemList.add(new MyRuleDisployContract.NameDescItem("酒店类型", StringUtils.join(cabinNameList, ",")));
                    } else {
                        hotelNameDescItemList.add(new MyRuleDisployContract.NameDescItem("酒店类型", "无限制"));
                    }
                    //提前预定天数
                    hotelNameDescItemList.add(new MyRuleDisployContract.NameDescItem("提前预订", this.getPreDayStr(hotelRule.getPrivDayMin(), hotelRule.getPrivDayMax())));
                }
            } else {
                myRuleHotel.setView_flag(false);
            }
        } else {
            myRuleHotel.setView_flag(false);
        }
        //老版本处理兼容
        if (VersionTool.compare(clientVersion, oldVersion) < 0) {
            myRuleHotel.setType(null);
            myRuleHotel.setRule_limit(null);
        }
        myRuleHotel.setDetail_rules(hotelNameDescItemList);
        myRuleDisployContracts.add(myRuleHotel);

        EmployeeTrainRule employeeTrainRule = iBaseEmployeeTrainRuleService.queryEmployeeTrainRuleByPrimaryKey(userId, companyId);
        log.info("员工火车规则, employeeTrainRule={}", JsonUtils.toJson(employeeTrainRule));
        List<MyRuleDisployContract.NameDescItem> trainNameDescItemList = Lists.newArrayList();
        MyRuleDisployContract myRuleTrain = new MyRuleDisployContract();
        myRuleTrain.setCategory_type(15);
        myRuleTrain.setCategory_name("火车");
        myRuleTrain.setAuth_flag(0);
        myRuleTrain.setApprove_flag(0);
        myRuleTrain.setView_flag(false);
        myRuleTrain.setOrder_approve_flag(0);
        myRuleTrain.setRefund_approve_flag(0);
        myRuleTrain.setChange_approve_flag(0);
        if (employeeTrainRule != null) {
            myRuleTrain.setOrder_approve_flag(employeeTrainRule.getTrain_order_verify_flag() == true ? 1 : 0);
            if (employeeTrainRule.getTrain_rule() != null) {
                myRuleTrain.setAuth_flag(employeeTrainRule.getTrain_rule() == 4 ? 1 : 0);
            }
            myRuleTrain.setApprove_flag(employeeTrainRule.getTrain_verify_flag() == true ? 1 : 0);
            myRuleTrain.setRefund_approve_flag(employeeTrainRule.getRefund_ticket_type());
            myRuleTrain.setChange_approve_flag(employeeTrainRule.getChanges_ticket_type());
            String manualTrainRuleId = employeeTrainRule.getManual_train_rule_id();
            Boolean trainRuleFlag = employeeTrainRule.getTrain_rule_flag();
            if (trainRuleFlag && StringUtils.isNotBlank(manualTrainRuleId)) {
                TrainRule trainRule = trainRuleMapper.selectByPrimaryKey(manualTrainRuleId);
                log.info("火车规则, trainRule={}", JsonUtils.toJson(trainRule));
                if (trainRule != null) {
                    Boolean trainSeatFlag = trainRule.getTrainSeatFlag();
                    String commonTrainSeatType = trainRule.getCommonTrainSeatType();
                    String highspeedTrainSeatType = trainRule.getHighspeedTrainSeatType();
                    if (trainSeatFlag) {
                        String[] commonTrainSeatTypeList = StringUtils.isEmpty(commonTrainSeatType) ? new String[0] : commonTrainSeatType.split(",");
                        String[] highspeedTrainSeatTypeList = StringUtils.isEmpty(highspeedTrainSeatType) ? new String[0] : highspeedTrainSeatType.split(",");
                        if ((commonTrainSeatTypeList.length == 0 && highspeedTrainSeatTypeList.length == 0) ||
                                (commonTrainSeatTypeList.length + highspeedTrainSeatTypeList.length == TrainSeatType.values().length)) {
                            trainNameDescItemList.add(new MyRuleDisployContract.NameDescItem("可订席别", "无限制"));
                        } else {
                            List<String> allList = new ArrayList<>();
                            //普通
                            if (commonTrainSeatTypeList.length != 0) {
                                List<String> cabinNameList = Arrays.stream(commonTrainSeatTypeList).map(key -> TrainSeatType.getName(ObjUtils.toInteger(key, 0))).collect(Collectors.toList());
                                allList.addAll(cabinNameList);
                            }
                            //高铁
                            if (highspeedTrainSeatTypeList.length != 0) {
                                List<String> cabinNameList = Arrays.stream(highspeedTrainSeatTypeList).map(key -> TrainSeatType.getName(ObjUtils.toInteger(key, 0))).collect(Collectors.toList());
                                allList.addAll(cabinNameList);
                            }
                            trainNameDescItemList.add(new MyRuleDisployContract.NameDescItem("可订席别", StringUtils.join(allList, ",")));
                        }
                    } else {
                        trainNameDescItemList.add(new MyRuleDisployContract.NameDescItem("可订席别", "无限制"));
                    }
                    //单张票价
                    if (!org.springframework.util.StringUtils.isEmpty(trainRule.getPriceLimit())) {
                        trainNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单张票价", "需低于¥" + BigDecimalTool.formatMoney(trainRule.getPriceLimit())));
                    } else {
                        trainNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单张票价", "无限制"));
                    }
                    //提前预定天数
                    trainNameDescItemList.add(new MyRuleDisployContract.NameDescItem("提前预订", this.getPreDayStr(trainRule.getPrivDayMin(), trainRule.getPrivDayMax())));
                    //车次限制
                    if (Objects.equals(trainRule.getDayLimit(), true)) {
                        trainNameDescItemList.add(new MyRuleDisployContract.NameDescItem("车次限制", "必选夕发朝至车次"));
                    } else {
                        trainNameDescItemList.add(new MyRuleDisployContract.NameDescItem("车次限制", "无限制"));
                    }
                    //抢票席别
                    if (trainRule.getIsSameSeat()) {
                        trainNameDescItemList.add(new MyRuleDisployContract.NameDescItem("抢票席别", "与普通预订时一致"));
                    } else {
                        String grapCommonTrainSeatType = trainRule.getGrapCommonSeatType();
                        String grapHighspeedTrainSeatType = trainRule.getGrapHighspeedSeatType();
                        String[] gcommonTrainSeatTypeList = StringUtils.isEmpty(grapCommonTrainSeatType) ? new String[0] : grapCommonTrainSeatType.split(",");
                        String[] ghighspeedTrainSeatTypeList = StringUtils.isEmpty(grapHighspeedTrainSeatType) ? new String[0] : grapHighspeedTrainSeatType.split(",");

                        if ((gcommonTrainSeatTypeList.length + ghighspeedTrainSeatTypeList.length == TrainSeatType.values().length) ||
                                (gcommonTrainSeatTypeList.length == 0 && ghighspeedTrainSeatTypeList.length == 0)) {
                            trainNameDescItemList.add(new MyRuleDisployContract.NameDescItem("抢票席别", "无限制"));
                        } else {
                            List<String> allList = new ArrayList<>();
                            //普通
                            if (gcommonTrainSeatTypeList.length != 0) {
                                List<String> cabinNameList = Arrays.stream(gcommonTrainSeatTypeList).map(key -> TrainSeatType.getName(ObjUtils.toInteger(key, 0))).collect(Collectors.toList());
                                allList.addAll(cabinNameList);
                            }
                            //高铁
                            if (ghighspeedTrainSeatTypeList.length != 0) {
                                List<String> cabinNameList = Arrays.stream(ghighspeedTrainSeatTypeList).map(key -> TrainSeatType.getName(ObjUtils.toInteger(key, 0))).collect(Collectors.toList());
                                allList.addAll(cabinNameList);
                            }
                            trainNameDescItemList.add(new MyRuleDisployContract.NameDescItem("抢票席别", StringUtils.join(allList, ",")));
                        }
                    }
                }
            }
        }
        myRuleTrain.setDetail_rules(trainNameDescItemList);
        myRuleDisployContracts.add(myRuleTrain);
        //用车
        com.fenbeitong.usercenter.api.model.dto.rule.CarPolicyBean employeeTaxiRule = iBaseEmployeeTaxiRuleExtService.getCarPolicyBean(userId, companyId);
        log.info("员工用车规则, employeeTaxiRule={}", JsonUtils.toJson(employeeTaxiRule));
        //用车规则
        List<MyRuleDisployContract.NameDescItem> taxiNameDescItemList = Lists.newArrayList();
        //审批用车规则
        List<MyRuleDisployContract.NameDescItem> approveTaxiNameDescItemList = Lists.newLinkedList();
        //所有用车规则
        List<Map> ruleList = Lists.newLinkedList();
        MyRuleDisployContract myRuleTaxi = new MyRuleDisployContract();
        myRuleTaxi.setCategory_type(3);
        myRuleTaxi.setCategory_name("用车");
        myRuleTaxi.setAuth_flag(0);
        myRuleTaxi.setApprove_flag(0);
        myRuleTaxi.setView_flag(false);
        myRuleTaxi.setRefund_approve_flag(0);
        myRuleTaxi.setChange_approve_flag(0);
        if (employeeTaxiRule != null) {
            if (employeeTaxiRule.getCar_priv_flag() != null && employeeTaxiRule.getCar_priv_flag()) {
                myRuleTaxi.setAuth_flag(1);
            } else {
                myRuleTaxi.setAuth_flag(0);
            }
            myRuleTaxi.setApprove_flag(0);
            Boolean taxiRuleFlag = employeeTaxiRule.getRule_limit_flag();
            if (taxiRuleFlag != null && taxiRuleFlag) {
                String taxiRuleId = null;
                String taxiApproveRuleId = null;
                List<RuleIdDto> ruleInfos = employeeTaxiRule.getRule_infos();
                if (ObjUtils.isNotEmpty(ruleInfos)) {
                    for (RuleIdDto ruleIdDto : ruleInfos) {
                        if (ruleIdDto.getType().getKey() == 1) {
                            if (ObjUtils.isNotEmpty(ruleIdDto.getRule_info())) {
                                taxiRuleId = ruleIdDto.getRule_info().get(0).getRule_id();
                            }
                        }
                        if (ruleIdDto.getType().getKey() == 2) {
                            if (ObjUtils.isNotEmpty(ruleIdDto.getRule_info())) {
                                taxiApproveRuleId = ruleIdDto.getRule_info().get(0).getRule_id();
                            }
                        }
                    }
                }
                //用车
                TaxiRule taxiRule = taxiRuleMapper.selectByPrimaryKey(ObjUtils.toInteger(taxiRuleId, 0));
                log.info("用车规则, taxiRule={}", JsonUtils.toJson(taxiRule));
                if (taxiRule != null) {
                    Boolean priceLimitFlag = taxiRule.getPriceLimitFlag();
                    if (priceLimitFlag) {
                        taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单笔限额", "需低于¥" + taxiRule.getPriceLimit().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue() + "元"));
                    } else {
                        taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单笔限额", "无限制"));
                    }
                    if (taxiRule.getDayPriceLimit().compareTo(BigDecimal.valueOf(0)) != 1) {
                        taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单日限额", "无限制"));
                    } else {
                        taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单日限额", "需低于¥" + taxiRule.getDayPriceLimit() + "元"));
                    }
                    //用车-个人支付
                    taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem(personalPayName, employeeTaxiRule.getPersonal_pay() ? "开启" : "关闭"));
                    //出租车调度费
                    BigDecimal taxiSchedulingFee = taxiRule.getTaxiSchedulingFee();
                    taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("调度费", taxiSchedulingFee.equals(new BigDecimal("-1.00")) ? "禁止使用" : "上限" + taxiSchedulingFee.intValue() + "元"));
                    Boolean limitTime = taxiRule.getLimitTime();
                    if (limitTime) {
                        List<TaxiTimeRange> taxiTimeRangeList = taxiTimeRangeMapper.queryRuleRangeTime(taxiRule.getId());
                        log.info("用车时段, taxiTimeRangeList={}", JsonUtils.toJson(taxiTimeRangeList));
                        List<Integer> dataTypes = Lists.newArrayList();
                        for (TaxiTimeRange taxiTimeRange : taxiTimeRangeList) {
                            dataTypes.add(taxiTimeRange.getDayType());
                        }

                        taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("用车时段", DayTypeChange(taxiRule.getId().toString(), 1)));
                    } else {
                        taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("用车时段", "无限制"));
                    }

                    Boolean limitPath = taxiRule.getLimitPath();
                    if (limitPath) {
                        TaxiPathLocationExample taxiPathLocationExample = new TaxiPathLocationExample();
                        taxiPathLocationExample.createCriteria().andCompanyIdEqualTo(companyId).andTaxiRuleIdEqualTo(taxiRule.getId());
                        List<TaxiPathLocation> taxiPathLocationList = taxiPathLocationMapper.selectByExample(taxiPathLocationExample);
                        log.info("用车上车位置, taxiPathLocationList={}", JsonUtils.toJson(taxiPathLocationList));
                        List<String> pathStrList = Lists.newArrayList();
                        for (TaxiPathLocation taxiPathLocation : taxiPathLocationList) {
                            StringBuffer sb = new StringBuffer();
                            //用车上车位置限制
                            if (taxiPathLocation.getLimitDeparture()) {
                                Integer departureId = taxiPathLocation.getDepartureLocationId();
                                TaxiLocation taxiLocation = taxiLocationMapper.selectByPrimaryKey(departureId);
                                log.info("用车上车位置, taxiPathLocationList={}", JsonUtils.toJson(taxiLocation));
                                // 修复线上npe
                                if (ObjectUtil.isNull(taxiLocation)) {
                                    continue;
                                }
                                String commentName = taxiLocation.getCommentName();
                                if (StringUtils.isBlank(commentName)) {
                                    commentName = "用车位置";
                                }
                                if (taxiLocation.getLocationType() == TaxiLocationType.Location.getCode()) {
                                    sb.append(commentName + "," + taxiLocation.getName() + "," + taxiLocation.getRadius() / 1000 + "km内");
                                } else {
                                    sb.append(commentName + "," + taxiLocation.getCityName() + " 本市范围可用");

                                }
                            } else {
                                sb.append("任意位置");
                            }
                            sb.append("-");
                            //用车到达位置限制
                            if (taxiPathLocation.getLimitArrival()) {
                                Integer arrivalId = taxiPathLocation.getArrivalLocationId();
                                TaxiLocation taxiLocation = taxiLocationMapper.selectByPrimaryKey(arrivalId);
                                log.info("用车上车位置, taxiLocation={}", JsonUtils.toJson(taxiLocation));
                                // 修复线上npe
                                if (ObjectUtil.isNull(taxiLocation)) {
                                    continue;
                                }
                                String commentName = taxiLocation.getCommentName();
                                if (StringUtils.isBlank(commentName)) {
                                    commentName = "用车位置";
                                }
                                if (taxiLocation.getLocationType() == TaxiLocationType.Location.getCode()) {
                                    sb.append(commentName + "," + taxiLocation.getName() + "," + taxiLocation.getRadius() / 1000 + "km内");
                                } else {
                                    sb.append(commentName + "," + taxiLocation.getCityName() + " 本市范围可用");
                                }
                            } else {
                                sb.append("任意位置");
                            }
                            pathStrList.add(sb.toString());
                        }
                        // 处理脏数据，没有拼到的时候默认返回无限制
                        if (pathStrList.size() == 0) {
                            pathStrList.add("无限制");
                        }
                        taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("用车位置", String.join("\n\n", pathStrList)));
                    } else {
                        taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("用车位置", "无限制"));
                    }
                    Boolean limitTaxiType = taxiRule.getLimitTaxiType();
                    String allowedTaxiType = taxiRule.getAllowedTaxiType();
                    if (limitTaxiType) {
                        String[] allowedTaxiTypeList = allowedTaxiType.split(",");
                        List<String> cabinNameList = Arrays.stream(allowedTaxiTypeList).map(key -> AllowedTaxiType.getName(ObjUtils.toInteger(key, 0))).collect(Collectors.toList());
                        taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("乘坐车型", StringUtils.join(cabinNameList, ",")));
                    } else {
                        taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("乘坐车型", "无限制"));
                    }
                    //同城限制
                    if (Objects.nonNull(taxiRule.getAllowedTaxiType())) {
                        log.info("走重构后代码");
                        AllowSameCityLimitType allowSameCityType = AllowSameCityLimitType.valueOf(taxiRule.getAllowSameCityType());
                        approveTaxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem(TaxiRuleConstant.SAME_CITY_LIMIT, allowSameCityType.getDesc()));
                    } else {
                        log.info("走老代码");
                        if (taxiRule.getAllowSameCity()) {
                            approveTaxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("同城限制", "起点和终点必须在同一城市"));
                        } else {
                            approveTaxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("同城限制", "无限制"));
                        }
                    }
                    //叫车限制
                    if (taxiRule.getAllowCalledForother()) {
                        taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("叫车限制", "允许为他人叫车"));
                    } else {
                        taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("叫车限制", "禁止为他人叫车"));
                    }
                    //提前上车限制
                    taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("提前上车限制", taxiRule.getLimitAdvance() == null || !taxiRule.getLimitAdvance() ? "允许" : "不允许"));
                    //预算限制
                    if (ObjUtils.toInteger(taxiRule.getUsePersonalBudget(), UsePersonalBudgetTypeEnum.USE.getCode()) != UsePersonalBudgetTypeEnum.NOT_USE.getCode()) {
                        taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("预算限制", "占用个人预算"));
                    } else {
                        taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("预算限制", "不占用个人预算"));
                    }
                    //时间周期
                    String periodTime = (StringUtils.isBlank(taxiRule.getPeriodHour()) ? "00" : taxiRule.getPeriodHour()) + ":" + (StringUtils.isBlank(taxiRule.getPeriodMinute()) ? "00" : taxiRule.getPeriodMinute());
                    taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("时间周期设置", "以" + periodTime + "至次日" + periodTime + "为一天，管控单日用车金额和单日用车次数"));
                    Map taxiRuleMap = Maps.newHashMap();
                    taxiRuleMap.put("category_name", CategorySubType.Taxi.getDesc());
                    taxiRuleMap.put("detail_rules", taxiNameDescItemList);
                    ruleList.add(taxiRuleMap);
                }
                //审批用车
                TaxiApproveRuleContract approveRuleContract = iTaxiApproveRuleService.queryRuleById(ObjUtils.toInteger(taxiApproveRuleId, 0), companyId);
                log.info("审批用车, approveRuleContract={}", JsonUtils.toJson(approveRuleContract));
                if (approveRuleContract != null) {
                    if (ObjUtils.isNotEmpty(approveRuleContract.getAllowedTaxiTypeList())) {
                        List<String> cabinNameList = approveRuleContract.getAllowedTaxiTypeList().stream().map(key -> key.getValue()).collect(Collectors.toList());
                        approveTaxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("乘坐车型", StringUtils.join(cabinNameList, ",")));
                    }

                    if(Objects.equals(approveRuleContract.getTaxiApproveRuleFlag(),1)){

                    }else{
                        //费用限制 0不限制，1限制，2员工填写
                        if (approveRuleContract.getPriceLimitFlag() == 0) {
                            approveTaxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("用车费用", "无限制"));
                        } else if (approveRuleContract.getPriceLimitFlag() == 1) {
                            if (approveRuleContract.getPriceLimit() == null || approveRuleContract.getPriceLimit().compareTo(new BigDecimal(-1)) == 0) {
                                approveTaxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单笔限额", "无限制"));
                            } else {
                                approveTaxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单笔限额", "需低于¥" + approveRuleContract.getPriceLimit().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue() + "元"));
                            }
                            if (approveRuleContract.getDayPriceLimit() == null || approveRuleContract.getDayPriceLimit().compareTo(new BigDecimal(-1)) == 0) {
                                approveTaxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单日限额", "无限制"));
                            } else {
                                approveTaxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单日限额", "需低于¥" + approveRuleContract.getDayPriceLimit().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue() + "元"));
                            }
                        } else {
                            approveTaxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("用车费用", "申请单填写"));
                        }
                    }

                    approveTaxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("调度费", approveRuleContract.getTaxiSchedulingFee() == -1 ? "禁止使用" : "上限" + approveRuleContract.getTaxiSchedulingFee() + "元"));
                    approveTaxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("用车位置", "申请单填写"));
                    //同城限制
                    if (Objects.nonNull(approveRuleContract.getAllowSameCityType())) {
                        log.info("走重构后代码");
                        AllowSameCityLimitType allowSameCityType = AllowSameCityLimitType.valueOf(approveRuleContract.getAllowSameCityType());
                        approveTaxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem(TaxiRuleConstant.SAME_CITY_LIMIT, allowSameCityType.getDesc()));
                    } else {
                        log.info("走老代码");
                        if (approveRuleContract.getAllowSameCity()) {
                            approveTaxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("同城限制", "起点和终点必须在同一城市"));
                        } else {
                            approveTaxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("同城限制", "无限制"));
                        }
                    }

                    //叫车限制
                    if (approveRuleContract.getAllowCalledForother()) {
                        approveTaxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("叫车限制", "允许为他人叫车"));
                    } else {
                        approveTaxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("叫车限制", "禁止为他人叫车"));
                    }
                    //时段
                    approveTaxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("用车时段", "申请单填写"));
                    //次数选限制 0不限制，1限制，2员工填写
                    if (approveRuleContract.getTimesLimitFlag() == 0) {
                        approveTaxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("用车次数", "无限制"));
                    } else if (approveRuleContract.getTimesLimitFlag() == 1) {
                        approveTaxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("用车次数", "限" + approveRuleContract.getTimesLimit() + "次"));
                    } else {
                        approveTaxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("用车次数", "申请单填写"));
                    }
                    if (approveRuleContract.getUseRuleDayPriceLimit() == 1) {
                        approveTaxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("额度合并", "允许与用车剩余额度合并"));
                    }
                    //预算限制
                    if (ObjUtils.toInteger(approveRuleContract.getUsePersonalBudget(), UsePersonalBudgetTypeEnum.USE.getCode()) != UsePersonalBudgetTypeEnum.NOT_USE.getCode()) {
                        taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("预算限制", "占用个人预算"));
                    } else {
                        taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("预算限制", "不占用个人预算"));
                    }
                    Map taxiApproveRuleMap = Maps.newHashMap();
                    taxiApproveRuleMap.put("category_name", CategorySubType.TaxiApply.getDesc());
                    taxiApproveRuleMap.put("detail_rules", approveTaxiNameDescItemList);
                    ruleList.add(taxiApproveRuleMap);
                }

            }
        }
        myRuleTaxi.setRule_list(ruleList);
        myRuleTaxi.setDetail_rules(taxiNameDescItemList);
        myRuleDisployContracts.add(myRuleTaxi);
        EmployeeDinnerRule employeeDinnerRule = iBaseEmployeeDinnerRuleService.selectByPrimaryKey(userId, companyId);
        log.info("员工用餐规则, employeeDinnerRule={}", JsonUtils.toJson(employeeDinnerRule));
        List<MyRuleDisployContract.NameDescItem> dinnerNameDescItemList = Lists.newArrayList();
        MyRuleDisployContract myRuleDinner = new MyRuleDisployContract();
        myRuleDinner.setCategory_type(60);
        myRuleDinner.setCategory_name("用餐");
        myRuleDinner.setAuth_flag(0);
        myRuleDinner.setApprove_flag(0);
        myRuleDinner.setView_flag(false);
        myRuleDinner.setRefund_approve_flag(0);
        myRuleDinner.setChange_approve_flag(0);
        if (employeeDinnerRule != null) {
            if (employeeDinnerRule.getDinner_rule() != null) {
                myRuleDinner.setAuth_flag(employeeDinnerRule.getDinner_rule() == 2 ? 1 : 0);
                Integer exceedBuyFlag = employeeDinnerRule.getExceed_buy_flag();
//                if (exceedBuyFlag == 2) {
//                    myRuleDinner.setExceed_explain("超出餐标部分将由企业支付（下单时需填写理由）");
//                } else {
//                    myRuleDinner.setExceed_explain("超出餐标部分将由个人支付");
//                }
            }
            String manualDinnerRuleId = employeeDinnerRule.getManual_dinner_rule_id();
            Boolean dinnerRuleFlag = employeeDinnerRule.getDinner_rule_flag();
            if (dinnerRuleFlag && StringUtils.isNotBlank(manualDinnerRuleId)) {
                DinnerRule dinnerRule = dinnerRuleMapper.selectByPrimaryKey(manualDinnerRuleId);
                log.info("用餐规则, dinnerRule={}", JsonUtils.toJson(dinnerRule));
                if (dinnerRule != null) {
                    BigDecimal dinnerFrequencyLimitPrice = dinnerRule.getDinnerFrequencyLimitPrice();
                    BigDecimal dinnerEverydayLimitPrice = dinnerRule.getDinnerEverydayLimitPrice();
                    if (dinnerFrequencyLimitPrice.compareTo(BigDecimal.valueOf(-1)) == 0) {
                        dinnerNameDescItemList.add(new MyRuleDisployContract.NameDescItem("每餐限额", "无限制"));
                    } else {
                        dinnerNameDescItemList.add(new MyRuleDisployContract.NameDescItem("每餐限额", "¥" + dinnerFrequencyLimitPrice + "元"));
                    }
                    if (dinnerEverydayLimitPrice.compareTo(BigDecimal.valueOf(-1)) == 0) {
                        dinnerNameDescItemList.add(new MyRuleDisployContract.NameDescItem("每日限额", "无限制"));
                    } else {
                        dinnerNameDescItemList.add(new MyRuleDisployContract.NameDescItem("每日限额", "¥" + dinnerEverydayLimitPrice + "元"));
                    }
                    //用餐时段
                    dinnerNameDescItemList.add(new MyRuleDisployContract.NameDescItem("用餐时段", DayTypeChange(dinnerRule.getId(), 3)));

                }
            }

        }
        myRuleDinner.setDetail_rules(dinnerNameDescItemList);
        myRuleDisployContracts.add(myRuleDinner);

        EmployeeMallRule employeeMallRule = iBaseEmployeeMallRuleService.queryEmployeeMallRuleByPrimaryKey(userId, companyId);
        log.info("员工采购规则, employeeMallRule={}", JsonUtils.toJson(employeeMallRule));
        List<MyRuleDisployContract.NameDescItem> mailNameDescItemList = Lists.newArrayList();
        MyRuleDisployContract myRuleMall = new MyRuleDisployContract();
        myRuleMall.setCategory_type(20);
        myRuleMall.setCategory_name("采购");
        myRuleMall.setAuth_flag(0);
        myRuleMall.setApprove_flag(0);
        myRuleMall.setView_flag(false);
        myRuleMall.setRefund_approve_flag(0);
        myRuleMall.setChange_approve_flag(0);
        if (employeeMallRule != null) {
            if (employeeMallRule.getMallRule() != null) {
                myRuleMall.setAuth_flag(employeeMallRule.getMallRule() == DinnerRuleType.Allowed.getCode() ? 1 : 0);
            }
            if (employeeMallRule.getMallVerifyFlag()) {
                myRuleMall.setApprove_flag(1);
            }
            String manualMallRuleId = employeeMallRule.getManualMallRuleId();
            Boolean mallRuleFlag = employeeMallRule.getMallRuleFlag();
            if (mallRuleFlag && StringUtils.isNotBlank(manualMallRuleId)) {
                MallRule mallRule = mallRuleMapper.selectByPrimaryKey(manualMallRuleId);
                log.info("采购规则, mallRule={}", JsonUtils.toJson(mallRule));
                if (mallRule != null) {
                    myRuleMall.setType(mallRule.getLimitType());
                    String limitCategoryIds = mallRule.getLimitCategoryIds();
                    String[] limitCategoryIdsList = StringUtils.isEmpty(limitCategoryIds) ? new String[0] : limitCategoryIds.split(",");
                    List<String> typeNameList = Lists.newArrayList();
                    if (mallRule.getLimitType() == null || mallRule.getLimitType().intValue() == 3) {
                        mailNameDescItemList.add(new MyRuleDisployContract.NameDescItem("采购品类", "无限制"));
                        //品类限制
                    } else if (mallRule.getLimitType() == null || mallRule.getLimitType().intValue() == 1) {
                        List<SecondCategoryContract> categories = getSecondCategoryList(token);
                        for (String str : limitCategoryIdsList) {
                            for (SecondCategoryContract mallCategoryContract : categories) {
                                if (mallCategoryContract.getCategoryCode() == ObjUtils.toLong(str, 0L).longValue()) {
                                    typeNameList.add(mallCategoryContract.getDescribe());
                                    break;
                                }
                            }
                        }
                        mailNameDescItemList.add(new MyRuleDisployContract.NameDescItem("采购品类", StringUtils.join(typeNameList, ",")));
                    }
                    //sku限制
                    else {
                        MallRuleSkuExample mallRuleSkuExamplet = new MallRuleSkuExample();
                        mallRuleSkuExamplet.createCriteria().andRuleIdEqualTo(mallRule.getId());
                        List<MallRuleSku> mallRuleSkus = mallRuleSkuMapper.selectByExample(mallRuleSkuExamplet);
                        log.info("采购sku规则, mallRuleSkus={}", JsonUtils.toJson(mallRuleSkus));
                        if (CollectionUtils.isNotEmpty(mallRuleSkus)) {
                            myRuleMall.setView_flag(true);
                            mailNameDescItemList.add(new MyRuleDisployContract.NameDescItem("采购品类", "可采购" + mallRuleSkus.size() + "项商品SKU"));
                        } else {
                            myRuleMall.setView_flag(false);
                            mailNameDescItemList.add(new MyRuleDisployContract.NameDescItem("采购品类", "无限制"));
                        }

                    }

                    Boolean limitPriceFlag = mallRule.getLimitPriceFlag();
                    if (limitPriceFlag) {
                        mailNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单笔限额", "需低于¥" + mallRule.getLimitPriceHighest().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue() + "元"));
                    } else {
                        mailNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单笔限额", "无限制"));
                    }
                }
            }
        }
        myRuleMall.setDetail_rules(mailNameDescItemList);
        myRuleDisployContracts.add(myRuleMall);

        EmployeeTakeawayRule employeeTakeawayRule = iBaseEmployeeTakeawayRuleService.selectByPrimaryKey(userId, companyId);
        log.info("员工外卖规则, employeeTakeawayRule={}", JsonUtils.toJson(employeeTakeawayRule));
        List<MyRuleDisployContract.NameDescItem> takeawayNameDescItemList = Lists.newArrayList();
        MyRuleDisployContract myRuleTakeaway = new MyRuleDisployContract();
        myRuleTakeaway.setCategory_type(50);
        myRuleTakeaway.setCategory_name("外卖");
        myRuleTakeaway.setAuth_flag(0);
        myRuleTakeaway.setApprove_flag(0);
        myRuleTakeaway.setView_flag(false);
        myRuleTakeaway.setRefund_approve_flag(0);
        myRuleTakeaway.setChange_approve_flag(0);
        if (employeeTakeawayRule != null) {
            myRuleTakeaway.setAuth_flag(employeeTakeawayRule.getTakeawayRule() == 4 ? 1 : 0);
            myRuleTakeaway.setApprove_flag(0);
            Boolean takeawayRuleFlag = employeeTakeawayRule.getTakeawayRuleFlag();
            if (takeawayRuleFlag != null && takeawayRuleFlag) {
                TakeawayRule takeawayRule = iBaseTakeawayRuleService.selectByPrimaryKey(employeeTakeawayRule.getManualTakeawayRuleId());
                log.info("外卖规则, takeawayRule={}", JsonUtils.toJson(takeawayRule));
                if (takeawayRule != null) {
                    Boolean limitLocation = takeawayRule.getLimitLocation();
                    if (limitLocation) {
                        List<TakeawayContract.Location> locationList = iBaseTakeawayRuleExtService.queryLocationList(takeawayRule.getId(), companyId);
                        log.info("外卖地址, locationList={}", JsonUtils.toJson(locationList));
                        if (CollectionUtils.isNotEmpty(locationList)) {
                            StringBuffer sb = new StringBuffer();
                            for (int i = 0; i < locationList.size(); i++) {
                                TakeawayContract.Location locationInfo = locationList.get(i);
                                if (i == locationList.size() - 1) {
                                    sb.append(locationInfo.getAddress());
                                } else {
                                    sb.append(locationInfo.getAddress()).append("\n").append("\n");
                                }
                            }
                            takeawayNameDescItemList.add(new MyRuleDisployContract.NameDescItem("送餐地址", sb.toString()));
                        }
                    } else {
                        takeawayNameDescItemList.add(new MyRuleDisployContract.NameDescItem("送餐地址", "无限制"));
                    }
                    Boolean limitTime = takeawayRule.getLimitTime();
                    if (limitTime) {
                        takeawayNameDescItemList.add(new MyRuleDisployContract.NameDescItem("下单时段", DayTypeTakeawayChange(takeawayRule.getId())));
                    } else {
                        takeawayNameDescItemList.add(new MyRuleDisployContract.NameDescItem("下单时段", "无限制"));
                    }
                }
            }
        }
        myRuleTakeaway.setDetail_rules(takeawayNameDescItemList);
        myRuleDisployContracts.add(myRuleTakeaway);

        // 闪送
        MyRuleDisployContract myRuleExpress = new MyRuleDisployContract();
        myRuleExpress.setCategory_type(CategoryTypeEnum.EXPRESS_CITY.getCode());
        myRuleExpress.setCategory_name(CategoryTypeEnum.EXPRESS_CITY.getName());
        myRuleExpress.setAuth_flag(0);
        SceneAuthDto sceneAuthDto = iCompanyService.querySecenAuth(companyId, userId);
        log.info("闪送规则, sceneAuthDto={}", JsonUtils.toJson(sceneAuthDto));
        if (ObjUtils.isNotEmpty(sceneAuthDto)) {
            ShansongAuthDto shansongAuthDto = sceneAuthDto.getShansongAuth();
            if (ObjUtils.isNotEmpty(shansongAuthDto)) {
                // 员工闪送权限
                if (shansongAuthDto.getPersonalShansong() == 1) {
                    myRuleExpress.setAuth_flag(1);
                }
            }
        }
        myRuleDisployContracts.add(myRuleExpress);
        return myRuleDisployContracts;
    }

    private List<SecondCategoryContract> getSecondCategoryList(String token) throws IOException, HttpException {
        List<MallCategoryNewContract> mallCategoryContracts = vendorHubService.getNewCategories(token);//最新的采购类目列表
        return vendorHubService.secondlist(mallCategoryContracts);
    }

    /**
     * 获取外卖多时间段数据集合信息
     *
     * @param ruleId
     * @return
     */
    private String DayTypeTakeawayChange(Integer ruleId) {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        List<TimeRange> timeRanges = null;
        timeRanges = takeawayTimeRangeExtMapper.queryEmployeeTakeawayRuleRangeTime(Integer.valueOf(ruleId));
        List<TimeRange> timeRangeList = new ArrayList<>();
        List<TimeRange> timeRangeSingleList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(timeRanges)) {
            for (TimeRange timeRange : timeRanges) {
                if (8 == timeRange.getDayType().intValue()) {
                    timeRangeList.add(new TimeRange(DayTempType.FIRSTDAY.getCode(), timeRange.getBeginTime(), timeRange.getEndTime(), timeRange.getIsOvernight(), timeRange.getFrequencyLimitPrice(), timeRange.getAccumulativeLimitPrice(), timeRange.getOrderLimitNum()));
                    timeRangeList.add(new TimeRange(DayTempType.SECONDDAY.getCode(), timeRange.getBeginTime(), timeRange.getEndTime(), timeRange.getIsOvernight(), timeRange.getFrequencyLimitPrice(), timeRange.getAccumulativeLimitPrice(), timeRange.getOrderLimitNum()));
                    timeRangeList.add(new TimeRange(DayTempType.THREEDAY.getCode(), timeRange.getBeginTime(), timeRange.getEndTime(), timeRange.getIsOvernight(), timeRange.getFrequencyLimitPrice(), timeRange.getAccumulativeLimitPrice(), timeRange.getOrderLimitNum()));
                    timeRangeList.add(new TimeRange(DayTempType.FOURDAY.getCode(), timeRange.getBeginTime(), timeRange.getEndTime(), timeRange.getIsOvernight(), timeRange.getFrequencyLimitPrice(), timeRange.getAccumulativeLimitPrice(), timeRange.getOrderLimitNum()));
                    timeRangeList.add(new TimeRange(DayTempType.FIVEDAY.getCode(), timeRange.getBeginTime(), timeRange.getEndTime(), timeRange.getIsOvernight(), timeRange.getFrequencyLimitPrice(), timeRange.getAccumulativeLimitPrice(), timeRange.getOrderLimitNum()));
                } else if (9 == timeRange.getDayType().intValue()) {
                    timeRangeList.add(new TimeRange(DayTempType.SIXDAY.getCode(), timeRange.getBeginTime(), timeRange.getEndTime(), timeRange.getIsOvernight(), timeRange.getFrequencyLimitPrice(), timeRange.getAccumulativeLimitPrice(), timeRange.getOrderLimitNum()));
                    timeRangeList.add(new TimeRange(DayTempType.SEVENDAY.getCode(), timeRange.getBeginTime(), timeRange.getEndTime(), timeRange.getIsOvernight(), timeRange.getFrequencyLimitPrice(), timeRange.getAccumulativeLimitPrice(), timeRange.getOrderLimitNum()));
                } else {
                    timeRangeList.add(timeRange);
                }
            }
            for (TimeRange timeRange : timeRangeList) {
                if (!timeRangeSingleList.contains(timeRange)) {
                    timeRangeSingleList.add(timeRange);
                }
            }
        }
        Map<Integer, List<String>> map = new HashMap<>();
        //分组按照日
        Map<Integer, List<TimeRange>> mapList = timeRangeSingleList.stream().collect(groupingBy(tr -> tr.getDayType()));
        for (Integer dayType : mapList.keySet()) {
            List<TimeRange> timeRanges1 = mapList.get(dayType);
            MySortList<TimeRange> msList = new MySortList<TimeRange>();
            msList.sortByMethod(timeRanges1, "getBeginTime", false);
            for (TimeRange timeRange : timeRanges1) {
                if (map.containsKey(dayType)) {
                    List<String> stringList = map.get(dayType);
                    if (timeRange.getIsOvernight()) {
                        String timeStr = sdf.format(timeRange.getBeginTime()) + "-次日" + sdf.format(timeRange.getEndTime()) + "\n";
                        String orderLimitNum = "点餐次数：" + (timeRange.getOrderLimitNum() == -1 ? "不限制" : timeRange.getOrderLimitNum().toString()) + "\n";
                        String frequencyLimitPrice = "单次金额：" + (timeRange.getFrequencyLimitPrice().compareTo(BigDecimal.valueOf(-1)) == 0 ? "不限制" : "¥" + timeRange.getFrequencyLimitPrice().toString()) + "\n";
                        String accumulativeLimitPrice = "累计金额：" + (timeRange.getAccumulativeLimitPrice().compareTo(BigDecimal.valueOf(-1)) == 0 ? "不限制" : "¥" + timeRange.getAccumulativeLimitPrice().toString());
                        stringList.add(timeStr + orderLimitNum + frequencyLimitPrice + accumulativeLimitPrice);
                        map.put(dayType, stringList);
                    } else {
                        String timeStr = sdf.format(timeRange.getBeginTime()) + "-" + sdf.format(timeRange.getEndTime()) + "\n";
                        String orderLimitNum = "点餐次数：" + (timeRange.getOrderLimitNum() == -1 ? "不限制" : timeRange.getOrderLimitNum().toString()) + "\n";
                        String frequencyLimitPrice = "单次金额：" + (timeRange.getFrequencyLimitPrice().compareTo(BigDecimal.valueOf(-1)) == 0 ? "不限制" : "¥" + timeRange.getFrequencyLimitPrice().toString()) + "\n";
                        String accumulativeLimitPrice = "累计金额：" + (timeRange.getAccumulativeLimitPrice().compareTo(BigDecimal.valueOf(-1)) == 0 ? "不限制" : "¥" + timeRange.getAccumulativeLimitPrice().toString());
                        stringList.add(timeStr + orderLimitNum + frequencyLimitPrice + accumulativeLimitPrice);
                        map.put(dayType, stringList);
                    }
                    map.put(dayType, stringList);
                } else {
                    List<String> stringList = new ArrayList<>();
                    if (timeRange.getIsOvernight()) {
                        String timeStr = sdf.format(timeRange.getBeginTime()) + "-次日" + sdf.format(timeRange.getEndTime()) + "\n";
                        String orderLimitNum = "点餐次数：" + (timeRange.getOrderLimitNum() == -1 ? "不限制" : timeRange.getOrderLimitNum().toString()) + "\n";
                        String frequencyLimitPrice = "单次金额：" + (timeRange.getFrequencyLimitPrice().compareTo(BigDecimal.valueOf(-1)) == 0 ? "不限制" : "¥" + timeRange.getFrequencyLimitPrice().toString()) + "\n";
                        String accumulativeLimitPrice = "累计金额：" + (timeRange.getAccumulativeLimitPrice().compareTo(BigDecimal.valueOf(-1)) == 0 ? "不限制" : "¥" + timeRange.getAccumulativeLimitPrice().toString());
                        stringList.add(timeStr + orderLimitNum + frequencyLimitPrice + accumulativeLimitPrice);
                        map.put(dayType, stringList);
                    } else {
                        String timeStr = sdf.format(timeRange.getBeginTime()) + "-" + sdf.format(timeRange.getEndTime()) + "\n";
                        String orderLimitNum = "点餐次数：" + (timeRange.getOrderLimitNum() == -1 ? "不限制" : timeRange.getOrderLimitNum().toString()) + "\n";
                        String frequencyLimitPrice = "单次金额：" + (timeRange.getFrequencyLimitPrice().compareTo(BigDecimal.valueOf(-1)) == 0 ? "不限制" : "¥" + timeRange.getFrequencyLimitPrice().toString()) + "\n";
                        String accumulativeLimitPrice = "累计金额：" + (timeRange.getAccumulativeLimitPrice().compareTo(BigDecimal.valueOf(-1)) == 0 ? "不限制" : "¥" + timeRange.getAccumulativeLimitPrice().toString());
                        stringList.add(timeStr + orderLimitNum + frequencyLimitPrice + accumulativeLimitPrice);
                        map.put(dayType, stringList);
                    }
                }
            }
        }

        if (CollectionUtils.isNotEmpty(timeRangeSingleList)) {
            StringBuffer dayStr = new StringBuffer();
            for (DayTempType enu : DayTempType.values()) {
                if (map.containsKey(enu.getCode())) {
                    List<String> timeList = map.get(enu.getCode());
                    for (int i = 0; i < timeList.size(); i++) {
                        StringBuffer timeTemp = new StringBuffer();
                        timeTemp.append(timeList.get(i));
                        if (enu.getCode() != 1) {
                            dayStr.append("\n" + "\n" + enu.getName()).append(timeTemp);
                        } else {
                            if (i != 0) {
                                dayStr.append("\n" + "\n" + enu.getName()).append(timeTemp);
                            } else {
                                dayStr.append(enu.getName()).append(timeTemp);
                            }
                        }
                    }
                } else {
                    if (enu.getCode() != 1) {
                        dayStr.append("\n" + "\n" + enu.getName()).append("不可预订");
                    } else {
                        dayStr.append(enu.getName()).append("不可预订");
                    }
                }
            }
            return dayStr.toString();
        } else {
            return "无限制";
        }
    }

    /**
     * 获取多时间段数据集合信息
     *
     * @param ruleId
     * @param type
     * @return
     */
    @Override
    public String DayTypeChange(String ruleId, Integer type) {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        List<TimeRange> timeRanges = null;
        if (type == 1 || type == 4) {
            timeRanges = timeRangeExtMapper.queryEmployeeTaxiRuleRangeTime(Integer.valueOf(ruleId), type);
        } else {
            timeRanges = timeRangeExtMapper.queryEmployeeRuleRangeTime(ruleId, type);
        }
        List<TimeRange> timeRangeList = new ArrayList<>();
        List<TimeRange> timeRangeSingleList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(timeRanges)) {
            for (TimeRange timeRange : timeRanges) {
                if (8 == timeRange.getDayType().intValue()) {
                    timeRangeList.add(new TimeRange(DayTempType.FIRSTDAY.getCode(), timeRange.getBeginTime(), timeRange.getEndTime(), timeRange.getIsOvernight()));
                    timeRangeList.add(new TimeRange(DayTempType.SECONDDAY.getCode(), timeRange.getBeginTime(), timeRange.getEndTime(), timeRange.getIsOvernight()));
                    timeRangeList.add(new TimeRange(DayTempType.THREEDAY.getCode(), timeRange.getBeginTime(), timeRange.getEndTime(), timeRange.getIsOvernight()));
                    timeRangeList.add(new TimeRange(DayTempType.FOURDAY.getCode(), timeRange.getBeginTime(), timeRange.getEndTime(), timeRange.getIsOvernight()));
                    timeRangeList.add(new TimeRange(DayTempType.FIVEDAY.getCode(), timeRange.getBeginTime(), timeRange.getEndTime(), timeRange.getIsOvernight()));
                } else if (9 == timeRange.getDayType().intValue()) {
                    timeRangeList.add(new TimeRange(DayTempType.SIXDAY.getCode(), timeRange.getBeginTime(), timeRange.getEndTime(), timeRange.getIsOvernight()));
                    timeRangeList.add(new TimeRange(DayTempType.SEVENDAY.getCode(), timeRange.getBeginTime(), timeRange.getEndTime(), timeRange.getIsOvernight()));
                } else {
                    timeRangeList.add(timeRange);
                }
            }
            for (TimeRange timeRange : timeRangeList) {
                if (!timeRangeSingleList.contains(timeRange)) {
                    timeRangeSingleList.add(timeRange);
                }
            }
        }
        Map<Integer, List<String>> map = new HashMap<>();
        //分组按照日
        Map<Integer, List<TimeRange>> mapList = timeRangeSingleList.stream().collect(groupingBy(tr -> tr.getDayType()));
        for (Integer dayType : mapList.keySet()) {
            List<TimeRange> timeRanges1 = mapList.get(dayType);
            MySortList<TimeRange> msList = new MySortList<TimeRange>();
            msList.sortByMethod(timeRanges1, "getBeginTime", false);
            for (TimeRange timeRange : timeRanges1) {
                if (map.containsKey(dayType)) {
                    List<String> stringList = map.get(dayType);
                    if (timeRange.getIsOvernight()) {
                        stringList.add(sdf.format(timeRange.getBeginTime()) + "-次日" + sdf.format(timeRange.getEndTime()));
                    } else {
                        stringList.add(sdf.format(timeRange.getBeginTime()) + "-" + sdf.format(timeRange.getEndTime()));
                    }
                    map.put(dayType, stringList);
                } else {
                    List<String> stringList = new ArrayList<>();
                    if (timeRange.getIsOvernight()) {
                        stringList.add(sdf.format(timeRange.getBeginTime()) + "-次日" + sdf.format(timeRange.getEndTime()));
                    } else {
                        stringList.add(sdf.format(timeRange.getBeginTime()) + "-" + sdf.format(timeRange.getEndTime()));
                    }
                    map.put(dayType, stringList);
                }
            }

        }

        if (CollectionUtils.isNotEmpty(timeRangeSingleList)) {
            StringBuffer dayStr = new StringBuffer();
            for (DayTempType enu : DayTempType.values()) {
                if (map.containsKey(enu.getCode())) {
                    List<String> timeList = map.get(enu.getCode());
                    StringBuffer timeTemp = new StringBuffer();
                    for (int i = 0; i < timeList.size(); i++) {
                        if (i == timeList.size() - 1) {
                            timeTemp.append(timeList.get(i));
                        } else {
                            timeTemp.append(timeList.get(i)).append(",");
                        }
                    }
                    dayStr.append(enu.getName()).append(timeTemp).append("\n");
                } else {
                   // dayStr.append(enu.getName()).append("不可预订").append("\n");
                }
            }
            return dayStr.toString();
        } else {
            return "无限制";
        }
    }


    /**
     * 获取提前预定天数
     *
     * @param privDayMin
     * @param privDayMax
     * @return
     */

    private String getPreDayStr(Integer privDayMin, Integer privDayMax) {
        String privDayMinStr = "";
        String privDayMaxStr = "";
        if (!org.springframework.util.StringUtils.isEmpty(privDayMin)) {
            privDayMinStr = "至少提前" + privDayMin + "天";
        }
        if (!org.springframework.util.StringUtils.isEmpty(privDayMax)) {
            privDayMaxStr = "至多提前" + privDayMax + "天";
        }
        if (!org.springframework.util.StringUtils.isEmpty(privDayMinStr) && !org.springframework.util.StringUtils.isEmpty(privDayMax)) {
            return privDayMinStr + "," + privDayMaxStr;
        } else if (!org.springframework.util.StringUtils.isEmpty(privDayMinStr) && org.springframework.util.StringUtils.isEmpty(privDayMax)) {
            return privDayMinStr;
        } else if (org.springframework.util.StringUtils.isEmpty(privDayMinStr) && !org.springframework.util.StringUtils.isEmpty(privDayMax)) {
            return privDayMaxStr;
        }
        return "不限制";
    }

    @Override
    @Transactional(value = "fenbeitong")
    public void deleteRule(RuleDeleteContract deleteContract, String companyId, String userId, String clientVersion) throws SaasException {
        Integer rule_type = deleteContract.getRule_type();
        if (null == deleteContract) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        if (CollectionUtils.isEmpty(deleteContract.getRule_ids())) {
            throw new SaasException(GlobalResponseCode.RuleNameNotNull);
        }
        if (rule_type == null || RuleType.valueOf(rule_type) == RuleType.Unknown) {
            throw new SaasException(GlobalResponseCode.RuleTypeISERROR);
        }
        //1:差旅规则 2:用车 3:采购 4:用餐 5:机票 6:酒店 7:火车
        if (rule_type == 1) {
            throw new SaasException(GlobalResponseCode.APPLYFLOWDETAILERROR);
            //deleteBizTripRule(deleteContract, companyId);
        } else if (rule_type == 2) {
            deleteCarRule(deleteContract, companyId, userId);
        } else if (rule_type == 3) {
            deleteMallRuleV2(deleteContract, companyId, userId);
        } else if (rule_type == 4) {
            deleteDinnerRule(deleteContract, companyId, userId);
        } else if (rule_type == 5) {
            airRuleService.deleteAirRule(deleteContract, companyId, userId, clientVersion);
        } else if (rule_type == 6) {
            deleteHotelRule(deleteContract, companyId, userId);
        } else if (rule_type == 7) {
            trainRuleService.deleteTrainRule(deleteContract, companyId, userId);
        }
    }

    private List<MallCategoryContract> genMallCategoryContractsDetail(List<MallCategoryContract> mallCategoryContracts, List<Long> selectedIds) {
        mallCategoryContracts.forEach(mallCategoryContract -> {
            if (selectedIds.contains(mallCategoryContract.getCategory_code())) {
                mallCategoryContract.setSelected(true);//如果该规则包含在已选的规则中则标记为已选中
            }
        });
        return mallCategoryContracts;
    }


    //获取未被限制的人数
    @Override
    public Integer info(Integer type, String companyId) throws SaasException {
        return ruleMapper.getCountByType(type, companyId);
    }

    //获取未限制人数
    @Override
    public List<RuleResponseContract> searchList(Integer type, Integer pageIndexT, Integer pageSizeT, Integer pageTag, String companyId) throws SaasException {

        Integer pageSize = pageSizeT;
        Integer pageIndex = pageIndexT;
        Integer start = 0;
        Integer end = 0;
        if (org.springframework.util.StringUtils.isEmpty(pageSize) || pageSize <= 0) {
            pageSize = 20;
        }
        if (org.springframework.util.StringUtils.isEmpty(pageIndex) || pageIndex <= 0) {
            pageIndex = 1;
        }
        start = pageSize * (pageIndex - 1);
        return ruleMapper.searchList(pageSize, start, type, companyId, pageTag);
    }

    @Transactional(value = "fenbeitong")
    public void deleteDinnerRule(RuleDeleteContract deleteContract, String companyId, String userId) throws SaasException {
        //查询规则ID是否存在
        DinnerRuleExample dinnerRuleExample = new DinnerRuleExample();
        dinnerRuleExample.createCriteria().andCompanyIdEqualTo(companyId)
                .andIdIn(deleteContract.getRule_ids());
        if (deleteContract.getRule_ids().size() != dinnerRuleMapper.countByExample(dinnerRuleExample)) {
            throw new SaasException(GlobalResponseCode.RuleIdNotExistInCompany);
        }
        for (String ruleId : deleteContract.getRule_ids()) {
            iBaseEmployeeDinnerRuleExtService.updateManualRuleIdByRuleId(companyId, userId, ruleId);
            DinnerTimeRangeExample dinnerTimeRangeExample = new DinnerTimeRangeExample();
            dinnerTimeRangeExample.createCriteria().andRuleIdEqualTo(ruleId);
            dinnerTimeRangeMapper.deleteByExample(dinnerTimeRangeExample);
        }
        // 删除之前先查询，为了推送操作日志消息拿数据
        DinnerRule dinnerRule = dinnerRuleMapper.selectByPrimaryKey(deleteContract.getRule_ids().get(0));
        DinnerRuleExample dinnerRuleDeleteExample = new DinnerRuleExample();
        dinnerRuleDeleteExample.createCriteria().andCompanyIdEqualTo(companyId).andIdIn(deleteContract.getRule_ids());
        dinnerRuleMapper.deleteByExample(dinnerRuleDeleteExample);
        //添加规则日志
        addOperateLogRule(companyId, JSON.toJSONString(deleteContract.getRule_ids()), userId, JSON.toJSONString(deleteContract), 3, CategoryTypeEnum.Dinner.getCode());
        if (deleteContract.getRule_ids().size() == 1) {
            companyLogKafkaProducerService.sendCompanyLogMsg(CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Dinner_Rule, LogOperateActionEnum.DELETE,
                    LogOperateObjectEnum.DINNER_RULE, dinnerRule.getName()));
        } else {
            companyLogKafkaProducerService.sendCompanyLogMsg(CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Dinner_Rule, LogOperateActionEnum.BATCH_DELETE,
                    LogOperateObjectEnum.DINNER_RULE, ""));
        }
    }


    /**
     * 新增用车规则
     *
     * @param taxiRuleContract
     * @param companyId
     * @return
     * @throws SaasException
     */
    @Override
    public Integer addRule(TaxiRuleContract taxiRuleContract, String companyId) throws SaasException {
        TaxiRule taxiRule = taxiRuleContract.toModel();
        taxiRule.setLimitLevel(1);
        taxiRule.setCompanyId(companyId);
        List<Integer> departureLocationIds = taxiRuleContract.getDepartureLocationId();
        List<Integer> arrivalLocationIds = taxiRuleContract.getArrivalLocationId();
        List<Integer> departureLocationIdList = Lists.newArrayList();
        List<Integer> arrivalLocationIdList = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(departureLocationIds)) {
            departureLocationIdList = departureLocationIds.stream().filter(departureId -> taxiLocationMapper.selectByPrimaryKey(departureId) != null).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(departureLocationIdList)) {
                taxiRule.setLimitDeparture(false);
            }
        }
        if (!CollectionUtils.isEmpty(arrivalLocationIds)) {
            arrivalLocationIdList = arrivalLocationIds.stream().filter(arrivalId -> taxiLocationMapper.selectByPrimaryKey(arrivalId) != null).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(arrivalLocationIdList)) {
                taxiRule.setLimitArrival(false);
            }
        }
        taxiRule.setDepartureLocationId(StringUtils.join(departureLocationIdList, ","));
        taxiRule.setArrivalLocationId(StringUtils.join(arrivalLocationIdList, ","));
        taxiRule.setAllowedTaxiType(StringUtils.join(taxiRuleContract.getAllowedTaxiType(), ","));
        taxiRule.setModifyTime(new Date());
        taxiRuleMapper.insert(taxiRule);
        Integer id = taxiRule.getId();
        List<TaxiRuleContract.TimeRange> timeRangeList = taxiRuleContract.getTimeRange();
        if (!CollectionUtils.isEmpty(timeRangeList)) {
            for (TaxiRuleContract.TimeRange timeRange : timeRangeList) {
                TaxiTimeRange taxiTimeRange = new TaxiTimeRange();
                taxiTimeRange.setRuleId(id);
                taxiTimeRange.setDayType(timeRange.getDayType());
                taxiTimeRange.setIsOvernight(timeRange.getIsOvernight());
                taxiTimeRange.setBeginTime(DateUtils.parse(timeRange.getBeginTime(), "HH:mm"));
                taxiTimeRange.setEndTime(DateUtils.parse(timeRange.getEndTime(), "HH:mm"));
                taxiTimeRangeMapper.insert(taxiTimeRange);
            }
        }
        return id;
    }

    /**
     * 新增用车规则v3
     *
     * @param taxiRuleContract
     * @param companyId
     * @return
     * @throws SaasException
     */
    @Override
    @Transactional(value = "fenbeitong")
    public Integer addRulev3(TaxiRuleContract taxiRuleContract, String companyId) throws SaasException {
        //限制规则名称不能重复
        TaxiRuleExample taxiRuleExample = new TaxiRuleExample();
        String ruleName = taxiRuleContract.getName();
        if (StringUtils.isEmpty(ruleName)) {
            throw new SaasException(GlobalResponseCode.RuleNameNotNull);
        }
        taxiRuleExample.createCriteria().andCompanyIdEqualTo(companyId).andNameEqualTo(ruleName);
        List<TaxiRule> taxiRuleList = taxiRuleMapper.selectByExample(taxiRuleExample);
        if (CollectionUtils.isNotEmpty(taxiRuleList)) {
            throw new SaasException(GlobalResponseCode.RuleNameRepetition);
        }
        TaxiRule taxiRule = taxiRuleContract.toModel();
        taxiRule.setLimitLevel(1);
        taxiRule.setCompanyId(companyId);
        // 位置限制
        List<TaxiRulePath> pathLocationInfos = taxiRuleContract.getPathLocationInfos();
        List<TaxiPathLocation> taxiPathLocationList = Lists.newArrayList();
        if (ObjUtils.isNotEmpty(pathLocationInfos)) {
            if (ObjUtils.toBoolean(taxiRule.getLimitPath(), false)) {
                // 过滤有效路线限制
                pathLocationInfos = pathLocationInfos.stream().filter(pathLocationInfo -> checkPathLocation(pathLocationInfo)).collect(Collectors.toList());
                if (ObjUtils.isNotEmpty(pathLocationInfos)) {
                    for (TaxiRulePath pathLocationInfo : pathLocationInfos) {
                        TaxiPathLocation taxiPathLocation = new TaxiPathLocation();
                        taxiPathLocation.setCompanyId(companyId);
                        taxiPathLocation.setLimitDeparture(pathLocationInfo.getLimitDeparture());
                        taxiPathLocation.setDepartureLocationId(pathLocationInfo.getDepartureLocationId());
                        taxiPathLocation.setLimitArrival(pathLocationInfo.getLimitArrival());
                        taxiPathLocation.setArrivalLocationId(pathLocationInfo.getArrivalLocationId());
                        taxiPathLocationList.add(taxiPathLocation);
                    }
                } else {
                    taxiRule.setLimitPath(false);
                }
            }
        } else {
            if (taxiRule.getLimitPath() == null) {
                // 兼容老版本
                if (taxiRuleContract.getLimitDeparture() && ObjUtils.isNotEmpty(taxiRuleContract.getDepartureLocationId())) {
                    taxiRule.setLimitPath(true);
                    List<Integer> departureLocationIds = taxiRuleContract.getDepartureLocationId();
                    List<Integer> departureLocationIdList = Lists.newArrayList();
                    if (ObjUtils.isNotEmpty(departureLocationIds)) {
                        departureLocationIdList = departureLocationIds.stream().filter(departureId -> taxiLocationMapper.selectByPrimaryKey(departureId) != null).collect(Collectors.toList());
                        if (ObjUtils.isEmpty(departureLocationIdList)) {
                            taxiRule.setLimitPath(false);
                        }
                    }
                    //为空，不限制
                    if (CollectionUtils.isEmpty(departureLocationIds)) {
                        taxiRule.setLimitPath(false);
                    }
                    if (taxiRule.getLimitPath()) {
                        for (Integer departureLocationId : departureLocationIdList) {
                            TaxiPathLocation taxiPathLocation = new TaxiPathLocation();
                            taxiPathLocation.setCompanyId(companyId);
                            taxiPathLocation.setLimitDeparture(true);
                            taxiPathLocation.setDepartureLocationId(departureLocationId);
                            taxiPathLocationList.add(taxiPathLocation);
                        }
                    }
                } else {
                    taxiRule.setLimitPath(false);
                }
            } else {
                taxiRule.setLimitPath(false);
            }
        }
        if (CollectionUtils.isEmpty(taxiRuleContract.getAllowedTaxiType())) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
        log.info("[新增用车规则v3],AllowedTaxiType={}", StringUtils.join(taxiRuleContract.getAllowedTaxiType(), ","));
        taxiRule.setAllowedTaxiType(StringUtils.join(taxiRuleContract.getAllowedTaxiType(), ","));
        taxiRule.setLimitTaxiType(taxiRuleContract.getAllowedTaxiType().size() != AllowedTaxiType.values().length);
        taxiRule.setModifyTime(new Date());
        taxiRuleMapper.insertSelective(taxiRule);
        Integer id = taxiRule.getId();
        // 保存位置限制
        if (ObjUtils.isNotEmpty(taxiPathLocationList)) {
            for (TaxiPathLocation taxiPathLocation : taxiPathLocationList) {
                taxiPathLocation.setTaxiRuleId(id);
                taxiPathLocationMapper.insertSelective(taxiPathLocation);
            }

        }
        //多个时间批次保存
        List<List<TaxiRuleContract.TimeRange>> timeRangeList = taxiRuleContract.getTimeRangeList();
        if (!CollectionUtils.isEmpty(timeRangeList)) {
            for (List<TaxiRuleContract.TimeRange> timeRange : timeRangeList) {
                if (!CollectionUtils.isEmpty(timeRange)) {
                    Long batchId = System.currentTimeMillis();
                    for (TaxiRuleContract.TimeRange timeR : timeRange) {
                        TaxiTimeRange taxiTimeRange = new TaxiTimeRange();
                        taxiTimeRange.setRuleId(id);
                        taxiTimeRange.setDayType(timeR.getDayType());
                        //结束时间必须大于开始时间
                        if (!timeR.getIsOvernight() && DateUtils.parse(timeR.getBeginTime(), "HH:mm").after(DateUtils.parse(timeR.getEndTime(), "HH:mm"))) {
                            throw new SaasException(GlobalResponseCode.RuleTimeError);
                        }
                        taxiTimeRange.setIsOvernight(timeR.getIsOvernight());
                        taxiTimeRange.setBeginTime(DateUtils.parse(timeR.getBeginTime(), "HH:mm"));
                        taxiTimeRange.setEndTime(DateUtils.parse(timeR.getEndTime(), "HH:mm"));
                        taxiTimeRange.setBatchId(batchId);
                        taxiTimeRangeMapper.insert(taxiTimeRange);
                    }
                }

            }
        }
        //添加规则日志
        addOperateLogRule(companyId, id.toString(), "", JSON.toJSONString(taxiRuleContract), 1, CategoryTypeEnum.Taxi.getCode());
        return id;
    }

    private boolean checkPathLocation(TaxiRulePath taxiRulePath) {
        Boolean limitDeparture = ObjUtils.toBoolean(taxiRulePath.getLimitDeparture(), false);
        Boolean limitArrival = ObjUtils.toBoolean(taxiRulePath.getLimitArrival(), false);
        if (!limitDeparture && !limitArrival) {
            return false;
        }
        if (limitDeparture) {
            Integer departureLocationId = taxiRulePath.getDepartureLocationId();
            if (ObjUtils.isEmpty(departureLocationId) || departureLocationId == -1) {
                taxiRulePath.setLimitDeparture(false);
            } else {
                if (taxiLocationMapper.selectByPrimaryKey(departureLocationId) == null) {
                    return false;
                }
            }
        }
        if (limitArrival) {
            Integer arrivalLocationId = taxiRulePath.getArrivalLocationId();
            if (ObjUtils.isEmpty(arrivalLocationId) || arrivalLocationId == -1) {
                taxiRulePath.setLimitArrival(false);
            } else {
                if (taxiLocationMapper.selectByPrimaryKey(arrivalLocationId) == null) {
                    return false;
                }
            }
        }
        return ObjUtils.toBoolean(taxiRulePath.getLimitDeparture(), false) || ObjUtils.toBoolean(taxiRulePath.getLimitArrival(), false);
    }

    /**
     * 修改用车规则
     *
     * @param taxiRuleContract
     * @param companyId
     * @throws SaasException
     */
    @Override
    public void updateRule(TaxiRuleContract taxiRuleContract, String companyId) throws SaasException {
        Integer ruleId = taxiRuleContract.getId();
        TaxiRule taxiRule = taxiRuleContract.toModel();
        taxiRule.setLimitLevel(1);
        taxiRule.setCompanyId(companyId);
        List<Integer> departureLocationIds = taxiRuleContract.getDepartureLocationId();
        List<Integer> arrivalLocationIds = taxiRuleContract.getArrivalLocationId();
        List<Integer> departureLocationIdList = Lists.newArrayList();
        List<Integer> arrivalLocationIdList = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(departureLocationIds)) {
            departureLocationIdList = departureLocationIds.stream().filter(departureId -> taxiLocationMapper.selectByPrimaryKey(departureId) != null).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(departureLocationIdList)) {
                taxiRule.setLimitDeparture(false);
            }
        }
        if (!CollectionUtils.isEmpty(arrivalLocationIds)) {
            arrivalLocationIdList = arrivalLocationIds.stream().filter(arrivalId -> taxiLocationMapper.selectByPrimaryKey(arrivalId) != null).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(arrivalLocationIdList)) {
                taxiRule.setLimitArrival(false);
            }
        }
        taxiRule.setDepartureLocationId(StringUtils.join(departureLocationIdList, ","));
        taxiRule.setArrivalLocationId(StringUtils.join(arrivalLocationIdList, ","));
        taxiRule.setAllowedTaxiType(StringUtils.join(taxiRuleContract.getAllowedTaxiType(), ","));
        taxiRule.setModifyTime(new Date());
        TaxiRuleExample taxiRuleExample = new TaxiRuleExample();
        taxiRuleExample.createCriteria().andIdEqualTo(ruleId).andCompanyIdEqualTo(companyId);
        taxiRuleMapper.updateByExampleSelective(taxiRule, taxiRuleExample);
        TaxiTimeRangeExample taxiTimeRange = new TaxiTimeRangeExample();
        taxiTimeRange.createCriteria().andRuleIdEqualTo(ruleId);
        taxiTimeRangeMapper.deleteByExample(taxiTimeRange);
        List<TaxiRuleContract.TimeRange> timeRangeList = taxiRuleContract.getTimeRange();
        if (!CollectionUtils.isEmpty(timeRangeList)) {
            for (TaxiRuleContract.TimeRange timeRange : timeRangeList) {
                TaxiTimeRange taxiTimeInfoRange = new TaxiTimeRange();
                taxiTimeInfoRange.setRuleId(ruleId);
                taxiTimeInfoRange.setDayType(timeRange.getDayType());
                taxiTimeInfoRange.setIsOvernight(timeRange.getIsOvernight());
                taxiTimeInfoRange.setBeginTime(DateUtils.parse(timeRange.getBeginTime(), "HH:mm"));
                taxiTimeInfoRange.setEndTime(DateUtils.parse(timeRange.getEndTime(), "HH:mm"));
                taxiTimeRangeMapper.insert(taxiTimeInfoRange);
            }
        }

    }

    /**
     * 修改用车规则v3
     *
     * @param taxiRuleContract
     * @param companyId
     * @throws SaasException
     */
    @Override
    @Transactional(value = "fenbeitong")
    public void updateRulev3(TaxiRuleContract taxiRuleContract, String companyId) throws SaasException {
        Integer ruleId = taxiRuleContract.getId();
        //限制规则名称不能重复
        TaxiRuleExample taxiRuleExamples = new TaxiRuleExample();
        String ruleName = taxiRuleContract.getName();
        if (StringUtils.isEmpty(ruleName)) {
            throw new SaasException(GlobalResponseCode.RuleNameNotNull);
        }
        taxiRuleExamples.createCriteria().andCompanyIdEqualTo(companyId).andNameEqualTo(ruleName).andIdNotEqualTo(ruleId);
        List<TaxiRule> taxiRuleList = taxiRuleMapper.selectByExample(taxiRuleExamples);
        if (CollectionUtils.isNotEmpty(taxiRuleList)) {
            throw new SaasException(GlobalResponseCode.RuleNameRepetition);
        }
        TaxiRule taxiRule = taxiRuleContract.toModel();
        taxiRule.setLimitLevel(1);
        taxiRule.setCompanyId(companyId);
        // 位置限制
        List<TaxiRulePath> pathLocationInfos = taxiRuleContract.getPathLocationInfos();
        List<TaxiPathLocation> taxiPathLocationList = Lists.newArrayList();
        if (ObjUtils.isNotEmpty(pathLocationInfos)) {
            if (ObjUtils.toBoolean(taxiRule.getLimitPath(), false)) {
                // 过滤有效路线限制
                pathLocationInfos = pathLocationInfos.stream().filter(pathLocationInfo -> checkPathLocation(pathLocationInfo)).collect(Collectors.toList());
                if (ObjUtils.isNotEmpty(pathLocationInfos)) {
                    for (TaxiRulePath pathLocationInfo : pathLocationInfos) {
                        TaxiPathLocation taxiPathLocation = new TaxiPathLocation();
                        taxiPathLocation.setCompanyId(companyId);
                        taxiPathLocation.setLimitDeparture(pathLocationInfo.getLimitDeparture());
                        taxiPathLocation.setDepartureLocationId(pathLocationInfo.getDepartureLocationId());
                        taxiPathLocation.setLimitArrival(pathLocationInfo.getLimitArrival());
                        taxiPathLocation.setArrivalLocationId(pathLocationInfo.getArrivalLocationId());
                        taxiPathLocationList.add(taxiPathLocation);
                    }
                } else {
                    taxiRule.setLimitPath(false);
                }
            }
        } else {
            if (taxiRule.getLimitPath() == null) {
                // 兼容老版本
                if (taxiRuleContract.getLimitDeparture() && ObjUtils.isNotEmpty(taxiRuleContract.getDepartureLocationId())) {
                    taxiRule.setLimitPath(true);
                    List<Integer> departureLocationIds = taxiRuleContract.getDepartureLocationId();
                    List<Integer> departureLocationIdList = Lists.newArrayList();
                    if (ObjUtils.isNotEmpty(departureLocationIds)) {
                        departureLocationIdList = departureLocationIds.stream().filter(departureId -> taxiLocationMapper.selectByPrimaryKey(departureId) != null).collect(Collectors.toList());
                        if (ObjUtils.isEmpty(departureLocationIdList)) {
                            taxiRule.setLimitPath(false);
                        }
                    }
                    //为空，不限制
                    if (CollectionUtils.isEmpty(departureLocationIds)) {
                        taxiRule.setLimitPath(false);
                    }
                    if (taxiRule.getLimitPath()) {
                        for (Integer departureLocationId : departureLocationIdList) {
                            TaxiPathLocation taxiPathLocation = new TaxiPathLocation();
                            taxiPathLocation.setCompanyId(companyId);
                            taxiPathLocation.setLimitDeparture(true);
                            taxiPathLocation.setDepartureLocationId(departureLocationId);
                            taxiPathLocationList.add(taxiPathLocation);
                        }
                    }
                } else {
                    taxiRule.setLimitPath(false);
                }
            } else {
                taxiRule.setLimitPath(false);
            }
        }
        if (CollectionUtils.isEmpty(taxiRuleContract.getAllowedTaxiType())) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
        LOGGER.info("[修改用车规则v3],AllowedTaxiType={}", StringUtils.join(taxiRuleContract.getAllowedTaxiType(), ","));
        taxiRule.setAllowedTaxiType(StringUtils.join(taxiRuleContract.getAllowedTaxiType(), ","));
        taxiRule.setLimitTaxiType(taxiRuleContract.getAllowedTaxiType().size() != AllowedTaxiType.values().length);
        taxiRule.setModifyTime(new Date());
        TaxiRuleExample taxiRuleExample = new TaxiRuleExample();
        taxiRuleExample.createCriteria().andIdEqualTo(ruleId).andCompanyIdEqualTo(companyId);
        taxiRuleMapper.updateByExampleSelective(taxiRule, taxiRuleExample);
        // 保存位置限制
        TaxiPathLocationExample taxiPathLocationExample = new TaxiPathLocationExample();
        taxiPathLocationExample.createCriteria().andTaxiRuleIdEqualTo(ruleId);
        taxiPathLocationMapper.deleteByExample(taxiPathLocationExample);
        if (ObjUtils.isNotEmpty(taxiPathLocationList)) {
            for (TaxiPathLocation taxiPathLocation : taxiPathLocationList) {
                taxiPathLocation.setTaxiRuleId(ruleId);
                taxiPathLocationMapper.insertSelective(taxiPathLocation);
            }

        }
        TaxiTimeRangeExample taxiTimeRange = new TaxiTimeRangeExample();
        taxiTimeRange.createCriteria().andRuleIdEqualTo(ruleId);
        taxiTimeRangeMapper.deleteByExample(taxiTimeRange);
        //多个时间批次保存
        List<List<TaxiRuleContract.TimeRange>> timeRangeList = taxiRuleContract.getTimeRangeList();
        if (!CollectionUtils.isEmpty(timeRangeList)) {
            for (List<TaxiRuleContract.TimeRange> timeRange : timeRangeList) {
                if (!CollectionUtils.isEmpty(timeRange)) {
                    Long batchId = System.currentTimeMillis();
                    for (TaxiRuleContract.TimeRange timeR : timeRange) {
                        TaxiTimeRange taxiTimeInfoRange = new TaxiTimeRange();
                        taxiTimeInfoRange.setRuleId(ruleId);
                        taxiTimeInfoRange.setDayType(timeR.getDayType());
                        //结束时间必须大于开始时间
                        if (!timeR.getIsOvernight() && DateUtils.parse(timeR.getBeginTime(), "HH:mm").after(DateUtils.parse(timeR.getEndTime(), "HH:mm"))) {
                            throw new SaasException(GlobalResponseCode.RuleTimeError);
                        }
                        taxiTimeInfoRange.setIsOvernight(timeR.getIsOvernight());
                        taxiTimeInfoRange.setBeginTime(DateUtils.parse(timeR.getBeginTime(), "HH:mm"));
                        taxiTimeInfoRange.setEndTime(DateUtils.parse(timeR.getEndTime(), "HH:mm"));
                        taxiTimeInfoRange.setBatchId(batchId);
                        taxiTimeRangeMapper.insert(taxiTimeInfoRange);
                    }
                }

            }
        }
        //添加规则日志
        addOperateLogRule(companyId, taxiRuleContract.getId().toString(), "", JSON.toJSONString(taxiRuleContract), 2, CategoryTypeEnum.Taxi.getCode());
    }

    /**
     * 查询用车规则详情（web专用）v3
     *
     * @param companyId
     * @param id
     * @return
     * @throws SaasException
     */
    @Override
    public TaxiRuleListContract queryTaxiRuleDetailv3(String companyId, Integer id) throws SaasException {
        if (StringUtils.isBlank(companyId) || id == null) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }

        // 是否新版规则规则
        TaxiExceedConfigQueryRes taxiExceedConfigQueryRes =
            iMessageSetupRpcService.queryTaxiExceedConfigMessage(companyId);
        log.info("用车使用规则版本信息:{}", JsonUtils.toJson(taxiExceedConfigQueryRes));

        TaxiRuleListContract taxiRuleListContract;
        if (Objects.equals(taxiExceedConfigQueryRes.getIsOpenExceedConfig(), true)) {
            com.fenbeitong.saasplus.api.model.dto.rule.taxi.TaxiRuleListContract taxiRuleV2 =
                iRuleV2Service.getTaxiRuleV2By(companyId, id);
            String json = JsonUtils.toJson(taxiRuleV2);
            log.info("新版用车规则:{}", json);
            taxiRuleListContract = JsonUtils.toObj(json, TaxiRuleListContract.class);
            taxiRuleListContract.setIsOpenExceedConfig(true);
        } else {
            //调用接口查询 补全城市限制信息
            com.fenbeitong.saasplus.api.model.dto.rule.taxi.TaxiRuleListContract taxiRuleV2 =
                iRuleV2Service.getTaxiRuleV2By(companyId, id);
            String json = JsonUtils.toJson(taxiRuleV2);
            log.info("未升级的用车规则:{}", json);
            taxiRuleListContract = JsonUtils.toObj(json, TaxiRuleListContract.class);
        }
        //用车时间限制
        if (taxiRuleListContract.getLimitTime()) {
            List<List<RuleTimeRange>> timeRangesList = Lists.newArrayList();
            List<RuleTimeRange> timeRangeList = Lists.newArrayList();
            List<RuleTimeRange> allTimeRangeList = Lists.newArrayList();
            List<TaxiTimeRange> taxiTimeRanges = taxiTimeRangeExtMapper.queryRuleRangeTimeGroup(taxiRuleListContract.getId());
            log.info("queryTaxiRuleDetailv3.taxiTimeRanges：{}", JsonUtils.toJson(taxiTimeRanges));
            Long batchId = -1L;
            if (!CollectionUtils.isEmpty(taxiTimeRanges)) {
                for (TaxiTimeRange taxiTimeRange : taxiTimeRanges) {
                    //切换batchId并且初始化list
                    if (batchId != -1L && batchId.longValue() != taxiTimeRange.getBatchId().longValue()) {
                        timeRangesList.add(timeRangeList);
                        timeRangeList = Lists.newArrayList();
                    }
                    batchId = taxiTimeRange.getBatchId();
                    RuleTimeRange timeRange = new RuleTimeRange();
                    timeRange.setId(taxiTimeRange.getId());
                    timeRange.setRuleId(taxiTimeRange.getRuleId());
                    timeRange.setDayType(taxiTimeRange.getDayType());
                    timeRange.setIsOvernight(taxiTimeRange.getIsOvernight());
                    timeRange.setBeginTime(DateUtils.format(taxiTimeRange.getBeginTime(), "HH:mm"));
                    timeRange.setEndTime(DateUtils.format(taxiTimeRange.getEndTime(), "HH:mm"));
                    timeRangeList.add(timeRange);
                    allTimeRangeList.add(timeRange);
                }
                taxiRuleListContract.setTimeRange(allTimeRangeList);
                if (!CollectionUtils.isEmpty(timeRangeList)) {
                    timeRangesList.add(timeRangeList);
                }
                taxiRuleListContract.setTimeRangeList(timeRangesList);
            }
        }
        // 用车路线限制
        if (taxiRuleListContract.getLimitPath()) {
            appendLimitPathInfo(companyId, taxiRuleListContract.getId(), taxiRuleListContract);
        }
        Integer companyEmployeeCount = iBaseEmployeeTaxiRuleExtService.queryEmployeeTaxiRuleCount(companyId,
            CategorySubType.Taxi.getSubtype(), id);
        log.info("queryTaxiRuleDetailv3.companyEmployeeCount：{}", JsonUtils.toJson(companyEmployeeCount));
        taxiRuleListContract.setEmployeeCount(ObjUtils.toInteger(companyEmployeeCount, 0));
        log.info("queryTaxiRuleDetailv3.taxiRuleListContract:{}", JsonUtils.toJson(taxiRuleListContract));
        return taxiRuleListContract;
    }

    /**
     * 查询用车规则列表
     *
     * @param companyId
     * @return
     * @throws SaasException
     */
    @Override
    public List<TaxiRuleListContract> queryTaxiRuleList(String companyId, String ruleName) throws SaasException {
        List<TaxiRuleListContract> taxiRuleListContractList = Lists.newArrayList();
        TaxiRuleExample taxiRuleExample = new TaxiRuleExample();
        if (StringUtils.isNotBlank(ruleName)) {
            taxiRuleExample.createCriteria().andCompanyIdEqualTo(companyId).andNameLike("%" + ruleName + "%");
        } else {
            taxiRuleExample.createCriteria().andCompanyIdEqualTo(companyId);
        }
        taxiRuleExample.setOrderByClause("modify_time desc");
        List<TaxiRule> taxiRuleList = taxiRuleMapper.selectByExample(taxiRuleExample);
        if (CollectionUtils.isEmpty(taxiRuleList)) {
            return Lists.newArrayList();
        }
        for (TaxiRule taxiRule : taxiRuleList) {
            TaxiRuleListContract taxiRuleListContract = TaxiRuleListContract.fromModel(taxiRule);
            //车型
            if (taxiRule.getLimitTaxiType() && StringUtils.isNotEmpty(taxiRule.getAllowedTaxiType())) {
                List<KvContract> allowedTaxiTypes = Lists.newArrayList();
                String[] allowTaxiTypeList = taxiRule.getAllowedTaxiType().split(",");
                for (String taxiType : allowTaxiTypeList) {
                    allowedTaxiTypes.add(new KvContract(ObjUtils.toInteger(taxiType), AllowedTaxiType.getName(ObjUtils.toInteger(taxiType))));
                }
                taxiRuleListContract.setAllowedTaxiType(allowedTaxiTypes);
            }
            //用车时间限制
            if (taxiRule.getLimitTime()) {
                List<RuleTimeRange> timeRangeList = Lists.newArrayList();
                TaxiTimeRangeExample taxiTimeRangeExample = new TaxiTimeRangeExample();
                taxiTimeRangeExample.createCriteria().andRuleIdEqualTo(taxiRule.getId());
                List<TaxiTimeRange> taxiTimeRanges = taxiTimeRangeExtMapper.queryRuleRangeTimeList(taxiRule.getId());
                for (TaxiTimeRange taxiTimeRange : taxiTimeRanges) {
                    RuleTimeRange timeRange = new RuleTimeRange();
                    timeRange.setId(taxiTimeRange.getId());
                    timeRange.setRuleId(taxiTimeRange.getRuleId());
                    timeRange.setDayType(taxiTimeRange.getDayType());
                    timeRange.setIsOvernight(taxiTimeRange.getIsOvernight());
                    timeRange.setBeginTime(DateUtils.format(taxiTimeRange.getBeginTime(), "HH:mm"));
                    timeRange.setEndTime(DateUtils.format(taxiTimeRange.getEndTime(), "HH:mm"));
                    timeRangeList.add(timeRange);
                }
                taxiRuleListContract.setTimeRange(timeRangeList);
            }
            //用车上车位置限制
            if (taxiRule.getLimitDeparture()) {
                String[] departureLocationList = taxiRule.getDepartureLocationId().split(",");
                List<TaxiRuleListContract.Departure> departureStartList = Lists.newArrayList();
                for (String departureId : departureLocationList) {
                    TaxiLocation taxiLocation = taxiLocationMapper.selectByPrimaryKey(ObjUtils.toInteger(departureId));
                    if (taxiLocation != null) {
                        TaxiRuleListContract.Departure departure = new TaxiRuleListContract.Departure();
                        departure.setId(taxiLocation.getId());
                        departure.setName(taxiLocation.getName());
                        departure.setCompanyId(taxiLocation.getCompanyId());
                        departure.setLat(taxiLocation.getLat());
                        departure.setLng(taxiLocation.getLng());
                        departure.setRadius(taxiLocation.getRadius());
                        if (StringUtils.isBlank(taxiLocation.getCommentName())) {
                            departure.setComment_name("用车位置");
                        } else {
                            departure.setComment_name(taxiLocation.getCommentName());
                        }
                        departureStartList.add(departure);
                    }

                }
                taxiRuleListContract.setDeparture(departureStartList);
            }
            //用车到达位置限制
            if (taxiRule.getLimitArrival()) {
                String[] arrivalLocationList = taxiRule.getArrivalLocationId().split(",");
                List<TaxiRuleListContract.Departure> departureArrivalList = Lists.newArrayList();
                for (String departureId : arrivalLocationList) {
                    TaxiLocation taxiLocation = taxiLocationMapper.selectByPrimaryKey(ObjUtils.toInteger(departureId));
                    if (taxiLocation != null) {
                        TaxiRuleListContract.Departure departure = new TaxiRuleListContract.Departure();
                        departure.setId(taxiLocation.getId());
                        departure.setName(taxiLocation.getName());
                        departure.setCompanyId(taxiLocation.getCompanyId());
                        departure.setLat(taxiLocation.getLat());
                        departure.setLng(taxiLocation.getLng());
                        departure.setRadius(taxiLocation.getRadius());
                        if (StringUtils.isBlank(taxiLocation.getCommentName())) {
                            departure.setComment_name("用车位置");
                        } else {
                            departure.setComment_name(taxiLocation.getCommentName());
                        }
                        departureArrivalList.add(departure);
                    }

                }
                taxiRuleListContract.setArrival(departureArrivalList);
            }
            Integer companyEmployeeCount = iBaseEmployeeTaxiRuleExtService.queryEmployeeTaxiRuleCount(companyId, CategorySubType.Taxi.getSubtype(), taxiRule.getId());
            taxiRuleListContract.setEmployeeCount(companyEmployeeCount);
            taxiRuleListContractList.add(taxiRuleListContract);
        }
        return taxiRuleListContractList;
    }


    /**
     * 查询用车规则列表v3
     *
     * @param companyId
     * @param ruleName
     * @param simpleQuery
     * @return
     * @throws SaasException
     */
    @Override
    public List<TaxiRuleListContract> queryTaxiRuleListv3(String companyId, String ruleName, Boolean simpleQuery) throws SaasException {
        List<TaxiRuleListContract> taxiRuleListContractList = Lists.newArrayList();
        TaxiRuleExample taxiRuleExample = new TaxiRuleExample();
        if (StringUtils.isNotBlank(ruleName)) {
            taxiRuleExample.createCriteria().andCompanyIdEqualTo(companyId).andNameLike("%" + ruleName + "%");
        } else {
            taxiRuleExample.createCriteria().andCompanyIdEqualTo(companyId);
        }
        taxiRuleExample.setOrderByClause("modify_time desc");
        List<TaxiRule> taxiRuleList = taxiRuleMapper.selectByExample(taxiRuleExample);
        if (CollectionUtils.isEmpty(taxiRuleList)) {
            return Lists.newArrayList();
        }
        if (simpleQuery) {
            for (TaxiRule taxiRule : taxiRuleList) {
                TaxiRuleListContract taxiRuleListContract = new TaxiRuleListContract();
                taxiRuleListContract.setId(taxiRule.getId());
                taxiRuleListContract.setName(taxiRule.getName());
                taxiRuleListContractList.add(taxiRuleListContract);
            }
            return taxiRuleListContractList;
        }
        List<String> ruleIds = Lists.newArrayList();
        List<Integer> limitTimeRuleIds = Lists.newArrayList();
        List<Integer> locationIds = Lists.newArrayList();
        for (TaxiRule taxiRule : taxiRuleList) {
            Integer ruleId = taxiRule.getId();
            ruleIds.add(ruleId.toString());
            if (taxiRule.getLimitTime()) {
                limitTimeRuleIds.add(ruleId);
            }
            TaxiPathLocationExample taxiPathLocationExample = new TaxiPathLocationExample();
            taxiPathLocationExample.createCriteria().andCompanyIdEqualTo(companyId).andTaxiRuleIdEqualTo(taxiRule.getId());
            List<TaxiPathLocation> taxiPathLocationList = taxiPathLocationMapper.selectByExample(taxiPathLocationExample);
            for (TaxiPathLocation taxiPathLocation : taxiPathLocationList) {
                if (taxiPathLocation.getLimitDeparture()) {
                    Integer departureId = taxiPathLocation.getDepartureLocationId();
                    if (!locationIds.contains(departureId)) {
                        locationIds.add(departureId);
                    }
                }
                if (taxiPathLocation.getLimitArrival()) {
                    Integer arrivalId = taxiPathLocation.getArrivalLocationId();
                    if (!locationIds.contains(arrivalId)) {
                        locationIds.add(arrivalId);
                    }
                }
            }
        }
        // 应用人数
        Map<String, Integer> countMap = iCommonEmployeeRuleService.queryRuleConfigEmployeeCount(companyId, CategorySubType.Taxi.getType(), CategorySubType.Taxi.getSubtype(), ruleIds);
        // 批量查询时段
        List<TaxiTimeRange> allTaxiTimeRanges = Lists.newArrayList();
        if (ObjUtils.isNotEmpty(limitTimeRuleIds)) {
            allTaxiTimeRanges = taxiTimeRangeExtMapper.queryRuleRangeTimeGroupByIds(limitTimeRuleIds);
        }
        Map<Integer, List<TaxiTimeRange>> taxiTimeRangeMap = Maps.newHashMap();
        for (TaxiTimeRange taxiTimeRange : allTaxiTimeRanges) {
            Integer ruleId = taxiTimeRange.getRuleId();
            List<TaxiTimeRange> taxiTimeRanges;
            if (taxiTimeRangeMap.containsKey(ruleId)) {
                taxiTimeRanges = taxiTimeRangeMap.get(ruleId);
            } else {
                taxiTimeRanges = Lists.newArrayList();
                taxiTimeRangeMap.put(ruleId, taxiTimeRanges);
            }
            taxiTimeRanges.add(taxiTimeRange);
        }
        // 批量查询地址
        List<TaxiLocation> allTaxiLocationList = Lists.newArrayList();
        if (ObjUtils.isNotEmpty(locationIds)) {
            TaxiLocationExample example = new TaxiLocationExample();
            example.createCriteria().andIdIn(locationIds);
            allTaxiLocationList = taxiLocationMapper.selectByExample(example);
        }
        Map<Integer, TaxiLocation> taxiLocationMap = Maps.newHashMap();
        for (TaxiLocation taxiLocation : allTaxiLocationList) {
            taxiLocationMap.put(taxiLocation.getId(), taxiLocation);
        }
        for (TaxiRule taxiRule : taxiRuleList) {
            TaxiRuleListContract taxiRuleListContract = TaxiRuleListContract.fromModel(taxiRule);
            //车型
            if (taxiRule.getLimitTaxiType() && StringUtils.isNotEmpty(taxiRule.getAllowedTaxiType())) {
                List<KvContract> allowedTaxiTypes = Lists.newArrayList();
                String[] allowTaxiTypeList = taxiRule.getAllowedTaxiType().split(",");
                for (String taxiType : allowTaxiTypeList) {
                    allowedTaxiTypes.add(new KvContract(ObjUtils.toInteger(taxiType), AllowedTaxiType.getName(ObjUtils.toInteger(taxiType))));
                }
                taxiRuleListContract.setAllowedTaxiType(allowedTaxiTypes);
            }
            //用车时间限制
            if (taxiRule.getLimitTime()) {
                List<List<RuleTimeRange>> timeRangesList = Lists.newArrayList();
                List<RuleTimeRange> timeRangeList = Lists.newArrayList();
                List<TaxiTimeRange> taxiTimeRanges = taxiTimeRangeMap.get(taxiRule.getId());
                Long batchId = -1L;
                if (!CollectionUtils.isEmpty(taxiTimeRanges)) {
                    for (TaxiTimeRange taxiTimeRange : taxiTimeRanges) {
                        //切换batchId并且初始化list
                        if (batchId != -1L && batchId.longValue() != taxiTimeRange.getBatchId().longValue()) {
                            timeRangesList.add(timeRangeList);
                            timeRangeList = Lists.newArrayList();
                        }
                        batchId = taxiTimeRange.getBatchId();
                        RuleTimeRange timeRange = new RuleTimeRange();
                        timeRange.setId(taxiTimeRange.getId());
                        timeRange.setRuleId(taxiTimeRange.getRuleId());
                        timeRange.setDayType(taxiTimeRange.getDayType());
                        timeRange.setIsOvernight(taxiTimeRange.getIsOvernight());
                        timeRange.setBeginTime(DateUtils.format(taxiTimeRange.getBeginTime(), "HH:mm"));
                        timeRange.setEndTime(DateUtils.format(taxiTimeRange.getEndTime(), "HH:mm"));
                        timeRangeList.add(timeRange);
                    }
                    if (!CollectionUtils.isEmpty(timeRangeList)) {
                        timeRangesList.add(timeRangeList);
                    }
                    taxiRuleListContract.setTimeRangeList(timeRangesList);
                }

            }
            //用车路线限制
            if (taxiRule.getLimitPath()) {
                appendLimitPathInfo(companyId, taxiRule.getId(), taxiRuleListContract);
            }
            Integer companyEmployeeCount = ObjUtils.toInteger(countMap.get(taxiRule.getId().toString()), 0);
            taxiRuleListContract.setEmployeeCount(companyEmployeeCount);
            taxiRuleListContractList.add(taxiRuleListContract);
        }
        return taxiRuleListContractList;
    }


    /**
     * 新增用车地理位置
     *
     * @param taxiLocationContract
     * @param companyId
     * @return
     * @throws SaasException
     */
    @Override
    public Integer addTaxiLocation(TaxiLocationContract taxiLocationContract, String companyId) throws SaasException {
        validParam(taxiLocationContract);
        TaxiLocation taxiLocation = taxiLocationContract.toModel();
        taxiLocation.setModifyTime(new Date());
        taxiLocation.setCompanyId(companyId);
        taxiLocationMapper.insert(taxiLocation);
        return taxiLocation.getId();
    }

    /**
     * 校验规则
     *
     * @param taxiLocationContract
     */
    private void validParam(TaxiLocationContract taxiLocationContract) {
        if (taxiLocationContract == null) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        if (StringUtils.isBlank(taxiLocationContract.getName())) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        if (taxiLocationContract.getLocation_type() == null) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        if (taxiLocationContract.getLocation_type() == null) {
            taxiLocationContract.setLocation_type(TaxiLocationType.Location.getCode());
        }
        if (TaxiLocationType.getEnum(taxiLocationContract.getLocation_type()) == null) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        if (taxiLocationContract.getLocation_type() == TaxiLocationType.City.getCode() && taxiLocationContract.getCity_code() == null) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        } else {
            if (taxiLocationContract.getLat() == null || taxiLocationContract.getLng() == null || taxiLocationContract.getRadius() == null) {
                throw new SaasException(GlobalResponseCode.ParameterIsNull);
            }
        }

    }

    /**
     * 删除用车地理位置
     *
     * @param taxiLocationContract
     * @param companyId
     * @throws SaasException
     */
    @Override
    @Transactional(value = "fenbeitong", rollbackFor = Exception.class)
    public void deleteTaxiLocation(TaxiLocationContract taxiLocationContract, String companyId) throws SaasException {
        if (taxiLocationContract == null || CollectionUtils.isEmpty(taxiLocationContract.getTaxiLocationIds()) || StringUtils.isBlank(companyId)) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        List<Integer> taxiLocationIdList = taxiLocationContract.getTaxiLocationIds();
        for (Integer locationId : taxiLocationIdList) {
            List<TaxiRule> taxiRuleList = taxiRuleMapperExt.queryTaxiRuleByLocationId(companyId, locationId);
            for (TaxiRule taxiRule : taxiRuleList) {
                String departureLocationId = taxiRule.getDepartureLocationId();
                String[] departureArray = departureLocationId.split(",");
                if (departureArray.length == 1) {
                    //只限制一个地理位置情况下删除地理位置，规则改为不限制。
                    taxiRuleMapperExt.updateTaxiRuleById(companyId, taxiRule.getId(), false, null);
                } else {
                    List departureLocationIds = Arrays.asList(departureArray).stream().filter(departureId -> !ObjUtils.toInteger(departureId).equals(locationId)).collect(Collectors.toList());
                    taxiRuleMapperExt.updateTaxiRuleById(companyId, taxiRule.getId(), true, StringUtils.join(departureLocationIds, ","));
                }
            }
        }

        TaxiLocationExample taxiLocationExample = new TaxiLocationExample();
        taxiLocationExample.createCriteria().andCompanyIdEqualTo(companyId).andIdIn(taxiLocationContract.getTaxiLocationIds());
        taxiLocationMapper.deleteByExample(taxiLocationExample);

        LocationCollectionExample locationCollectionExample = new LocationCollectionExample();
        locationCollectionExample.createCriteria().andCompanyIdEqualTo(companyId).andLocationIdIn(taxiLocationIdList);
        locationCollectionMapper.deleteByExample(locationCollectionExample);
    }

    /**
     * 用车地理位置详情
     *
     * @param companyId
     * @param id
     * @return
     * @throws SaasException
     */
    @Override
    public TaxiLocationDetailContract queryTaxiLocationDetail(String companyId, Integer id) throws SaasException {
        if (StringUtils.isBlank(companyId) || id == null) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        TaxiLocationExample taxiLocationExample = new TaxiLocationExample();
        taxiLocationExample.createCriteria().andCompanyIdEqualTo(companyId).andIdEqualTo(id);
        List<TaxiLocation> taxiLocationList = taxiLocationMapper.selectByExample(taxiLocationExample);
        if (CollectionUtils.isEmpty(taxiLocationList)) {
            return null;
        }
        TaxiLocation taxiLocation = taxiLocationList.get(0);
        TaxiLocationDetailContract taxiLocationDetailContract = new TaxiLocationDetailContract();
        TaxiLocationContract taxiLocationInfo = TaxiLocationContract.fromModel(taxiLocation);
        if (taxiLocationInfo != null && StringUtils.isBlank(taxiLocationInfo.getComment_name())) {
            taxiLocationInfo.setComment_name("用车位置");
        }
        taxiLocationInfo.setId(taxiLocation.getId());
        taxiLocationInfo.setCompanyId(taxiLocation.getCompanyId());
        taxiLocationInfo.setModifyTime(DateTimeTool.fromDateToString(taxiLocation.getModifyTime()));
        Company company = iCompanyService.queryCompanyById(taxiLocation.getCompanyId());
        if (company != null && StringUtils.isNotBlank(company.getName())) {
            taxiLocationDetailContract.setCompanyName(company.getName());
        }
        taxiLocationDetailContract.setUsed(false);
        TaxiRuleExample taxiRuleExample = new TaxiRuleExample();
        taxiRuleExample.createCriteria().andCompanyIdEqualTo(companyId).andDepartureLocationIdLike(taxiLocation.getId().toString());
        List<TaxiRule> taxiRules = taxiRuleMapper.selectByExample(taxiRuleExample);
        for (TaxiRule taxiRule : taxiRules) {
            String departureLocationId = taxiRule.getDepartureLocationId();
            if (StringUtils.isNotBlank(departureLocationId)) {
            }
            String[] departureLocations = departureLocationId.split(",");
            for (String departureLocation : departureLocations) {
                if (departureLocation.equals(taxiLocation.getId().toString())) {
                    taxiLocationDetailContract.setUsed(true);
                }
            }
        }
        taxiLocationDetailContract.setLocation(taxiLocationInfo);
        return taxiLocationDetailContract;
    }

    /**
     * 用车地理位置列表
     *
     * @param companyId
     * @return
     * @throws SaasException
     */
    @Override
    public List<TaxiLocationDetailContract> queryTaxiLocationList(String companyId, String remark, String address) throws SaasException {
        List<TaxiLocationDetailContract> taxiLocationDetailContractList = Lists.newArrayList();
        TaxiLocationExample taxiLocationExample = new TaxiLocationExample();
        TaxiLocationExample.Criteria criteria = taxiLocationExample.createCriteria();
        if(StringUtils.isNotEmpty(remark)){
            criteria.andCommentNameLike("%" + remark + "%");
        }
        if(StringUtils.isNotEmpty(address)){
            criteria.andNameLike("%" + address + "%");
        }
        criteria.andCompanyIdEqualTo(companyId);
        taxiLocationExample.setOrderByClause("modify_time desc");

        List<TaxiLocation> taxiLocationList = taxiLocationMapper.selectByExample(taxiLocationExample);
        log.info("taxiLocationList:{}",JsonUtils.toJson(taxiLocationList));
        if (CollectionUtils.isEmpty(taxiLocationList)) {
            return Lists.newArrayList();
        }
        for (TaxiLocation taxiLocation : taxiLocationList) {
            TaxiLocationDetailContract taxiLocationDetailContract = new TaxiLocationDetailContract();
            TaxiLocationContract taxiLocationInfo = TaxiLocationContract.fromModel(taxiLocation);
            if (taxiLocationInfo != null && StringUtils.isBlank(taxiLocationInfo.getComment_name())) {
                //taxiLocationInfo.setComment_name("用车位置");
            }
            taxiLocationInfo.setId(taxiLocation.getId());
            taxiLocationInfo.setCompanyId(taxiLocation.getCompanyId());
            taxiLocationInfo.setModifyTime(DateTimeTool.fromDateToString(taxiLocation.getModifyTime()));
            Company company = iCompanyService.queryCompanyById(taxiLocation.getCompanyId());
            if (company != null && StringUtils.isNotBlank(company.getName())) {
                taxiLocationDetailContract.setCompanyName(company.getName());
            }
            taxiLocationDetailContract.setUsed(false);
            TaxiRuleExample taxiRuleExample = new TaxiRuleExample();
            taxiRuleExample.createCriteria().andCompanyIdEqualTo(companyId).andDepartureLocationIdLike(taxiLocation.getId().toString());
            List<TaxiRule> taxiRules = taxiRuleMapper.selectByExample(taxiRuleExample);
            for (TaxiRule taxiRule : taxiRules) {
                String departureLocationId = taxiRule.getDepartureLocationId();
                if (StringUtils.isNotBlank(departureLocationId)) {
                }
                String[] departureLocations = departureLocationId.split(",");
                for (String departureLocation : departureLocations) {
                    if (departureLocation.equals(taxiLocation.getId().toString())) {
                        taxiLocationDetailContract.setUsed(true);
                    }
                }
            }
            taxiLocationDetailContract.setLocation(taxiLocationInfo);
            taxiLocationDetailContractList.add(taxiLocationDetailContract);
        }
        return taxiLocationDetailContractList;
    }

    /**
     * 修改用车地理位置
     *
     * @param taxiLocationContract
     * @param companyId
     * @throws SaasException
     */
    @Override
    public void updateTaxiLocation(TaxiLocationContract taxiLocationContract, String companyId) throws SaasException {
        if (taxiLocationContract != null && taxiLocationContract.getId() == null) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        validParam(taxiLocationContract);
        TaxiLocation taxiLocation = taxiLocationContract.toModel();
        if (taxiLocation != null && StringUtils.isBlank(taxiLocation.getCommentName())) {
            taxiLocation.setCommentName("用车位置");
        }
        taxiLocation.setModifyTime(new Date());
        TaxiLocationExample taxiLocationExample = new TaxiLocationExample();
        taxiLocationExample.createCriteria().andCompanyIdEqualTo(companyId).andIdEqualTo(taxiLocationContract.getId());
        taxiLocationMapper.updateByExampleSelective(taxiLocation, taxiLocationExample);
    }

    @Override
    public List catorgeryIdsList() {

        List<String> list = mallRuleMapper.catorgeryIdsList();
        List list1 = new ArrayList();
        return list;
    }

    /**
     * 采购规则列表 新接口
     */
    @Override
    public List<MallRuleDetailNewContract> listNewMallRule(String companyId, String ruleId, String ruleName, GenNewCategory genCategory, String token) throws SaasException, IOException, HttpException, ParseException {
        List<MallRule> mallRules = getMallRules(companyId, ruleId, ruleName);// 采购规则基本数据
        List<MallCategoryNewContract> mallCategoryContracts = vendorHubService.getNewCategories(token);//最新的采购类目列表

        List<SecondCategoryContract> secondlist = vendorHubService.secondlist(mallCategoryContracts);

        final int nowCategoriesCount = mallCategoryContracts.size();
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date defaultDate = dateFormat.parse("1992-09-03 00:00:00");
        mallRules.sort(((o1, o2) -> -Optional.ofNullable(o1.getModifyTime()).orElse(defaultDate).compareTo(Optional.ofNullable(o2.getModifyTime()).orElse(defaultDate))));//排序
        List<MallRuleDetailNewContract> mallRuleDetailContracts = mallRules.stream().map(mallRule -> {
            String mallRuleId = mallRule.getId();//单条采购规则 id
            String selectedCategoryIds = mallRule.getLimitCategoryIds();//已选的采购类目id
            //将选中的id转换成list
            List<Long> selectedIds = !org.springframework.util.StringUtils.isEmpty(selectedCategoryIds) ? Arrays.asList(selectedCategoryIds.split(",")).stream().map(Long::parseLong).collect(Collectors.toList()) : new ArrayList<>();

            List<SecondCategoryContract> categories = genCategory.genNew(secondlist, selectedIds);

            List<EmployeeInfoContract> employees = genEmployeeInfoContracts(companyId, mallRuleId, 1, Integer.MAX_VALUE).getEmployees();
            return new MallRuleDetailNewContract(
                    mallRuleId,
                    mallRule.getName(),
                    mallRule.getLimitPriceFlag(),
                    mallRule.getLimitPriceHighest(),
                    categories,
                    employees,
                    nowCategoriesCount
            );
        }).collect(Collectors.toList());
        return mallRuleDetailContracts;
    }

    /**
     * 采购规则列表 新接口v2
     */
    @Override
    public List<MallRuleDetailNewContract> listNewMallRuleV2(String companyId, String ruleId, String ruleName, GenMallCategory genCategory, String token) throws SaasException, IOException, HttpException, ParseException {
        // 采购规则基本数据
        List<MallRule> mallRules = getMallRules(companyId, ruleId, ruleName);
        //最新的采购类目列表
        List<MallCategoryNewContract> mallCategoryContracts = vendorHubService.getNewCategories(token);

        int categoriesCount = 0;
        int tempCount = 0;
        for (MallCategoryNewContract category : mallCategoryContracts) {
            tempCount = category.getList().size();
            categoriesCount = categoriesCount + tempCount;
        }
        //排序
        Date defaultDate = DateUtils.parse("1992-09-03 00:00:00");
        mallRules.sort(((o1, o2) -> -Optional.ofNullable(o1.getModifyTime()).orElse(defaultDate).compareTo(Optional.ofNullable(o2.getModifyTime()).orElse(defaultDate))));
        final int nowCategoriesCount = categoriesCount;
        List<MallRuleDetailNewContract> mallRuleDetailContracts = mallRules.stream().map(mallRule -> {
            // 单条采购规则 id
            String mallRuleId = mallRule.getId();
            // 已选的采购类目id
            String selectedCategoryIds = mallRule.getLimitCategoryIds();
            List<Long> selectCategoryList = !org.springframework.util.StringUtils.isEmpty(selectedCategoryIds) ? Arrays.asList(selectedCategoryIds.split(",")).stream().distinct().map(Long::parseLong).collect(Collectors.toList()) : new ArrayList<>();

            //匹配之后得到已选中的类目对象
            List<MallCategoryNewContract> categories = genCategory.genMallNew(mallCategoryContracts, selectCategoryList);
            if (CollectionUtils.isEmpty(categories)) {
                categories = new ArrayList<>();
                MallCategoryNewContract mallCategoryNewContract = new MallCategoryNewContract();
                mallCategoryNewContract.setCategory_code(0L);
                mallCategoryNewContract.setDescribe("SKU限制");
                mallCategoryNewContract.setSelected(true);
                List<SecondCategoryContract> list = new ArrayList<>();
                SecondCategoryContract secondCategoryContract = new SecondCategoryContract();
                secondCategoryContract.setCategoryCode(0L);
                secondCategoryContract.setDescribe("SKU限制");
                secondCategoryContract.setSelected(false);
                list.add(secondCategoryContract);
                mallCategoryNewContract.setList(list);
                categories.add(mallCategoryNewContract);
            }
            List<EmployeeInfoContract> employees = genEmployeeInfoContracts(companyId, mallRuleId, 1, Integer.MAX_VALUE).getEmployees();
            {
                return new MallRuleDetailNewContract(
                        mallRuleId,
                        mallRule.getName(),
                        mallRule.getLimitPriceFlag(),
                        mallRule.getLimitPriceHighest(),
                        employees,
                        nowCategoriesCount,
                        categories
                );
            }
        }).collect(Collectors.toList());
        return mallRuleDetailContracts;
    }


    /**
     * 采购规则列表 新接口v3
     */
    @Override
    public List<MallRuleDetailV3Contract> listNewMallRuleV3(String companyId, String ruleId, String ruleName, GenMallCategory genCategory, String token, Boolean simpleQuery) throws SaasException, IOException, HttpException, ParseException {
        // 采购规则基本数据
        List<MallRule> mallRules = getMallRules(companyId, ruleId, ruleName);
        if (ObjUtils.isEmpty(mallRules)) {
            return Lists.newArrayList();
        }
        List<MallRuleDetailV3Contract> simpleMallRuleList = Lists.newArrayList();
        if (simpleQuery) {
            for (MallRule mallRule : mallRules) {
                MallRuleDetailV3Contract simpleMallRule = new MallRuleDetailV3Contract(mallRule.getId(), mallRule.getName());
                simpleMallRuleList.add(simpleMallRule);
            }
            return simpleMallRuleList;
        }
        //最新的采购类目列表
        List<MallCategoryNewContract> mallCategoryContracts = vendorHubService.getNewCategories(token);

        int categoriesCount = 0;
        int tempCount = 0;
        for (MallCategoryNewContract category : mallCategoryContracts) {
            tempCount = category.getList().size();
            categoriesCount = categoriesCount + tempCount;
        }
        //排序
        Date defaultDate = DateUtils.parse("1992-09-03 00:00:00");
        mallRules.sort(((o1, o2) -> -Optional.ofNullable(o1.getModifyTime()).orElse(defaultDate).compareTo(Optional.ofNullable(o2.getModifyTime()).orElse(defaultDate))));
        final int nowCategoriesCount = categoriesCount;
        List<String> ruleIds = mallRules.stream().map(mallRule -> mallRule.getId()).collect(Collectors.toList());
        Map<String, Integer> countMap = iCommonEmployeeRuleService.queryRuleConfigEmployeeCount(companyId, CategorySubType.Mall.getType(), CategorySubType.Mall.getSubtype(), ruleIds);
        MallRuleSkuExample mallRuleSkuExample = new MallRuleSkuExample();
        mallRuleSkuExample.createCriteria().andRuleIdIn(ruleIds);
        List<MallRuleSku> allMallRuleSkuList = mallRuleSkuMapper.selectByExample(mallRuleSkuExample);
        Map<String, List<MallRuleSku>> mallRuleSkuMap = Maps.newHashMap();
        for (MallRuleSku mallRuleSku : allMallRuleSkuList) {
            String mallRuleId = mallRuleSku.getRuleId();
            List<MallRuleSku> mallRuleSkuList;
            if (mallRuleSkuMap.containsKey(mallRuleId)) {
                mallRuleSkuList = mallRuleSkuMap.get(mallRuleId);
            } else {
                mallRuleSkuList = Lists.newArrayList();
                mallRuleSkuMap.put(mallRuleId, mallRuleSkuList);
            }
            mallRuleSkuList.add(mallRuleSku);
        }
        List<MallRuleDetailV3Contract> mallRuleDetailContracts = mallRules.stream().map(mallRule -> {
            // 单条采购规则 id
            String mallRuleId = mallRule.getId();
            // 已选的采购类目id
            String selectedCategoryIds = mallRule.getLimitCategoryIds();
            List<Long> selectCategoryList = !org.springframework.util.StringUtils.isEmpty(selectedCategoryIds) ? Arrays.asList(selectedCategoryIds.split(",")).stream().distinct().map(Long::parseLong).collect(Collectors.toList()) : new ArrayList<>();

            //匹配之后得到已选中的类目对象
            List<MallCategoryNewContract> categories = genCategory.genMallNew(mallCategoryContracts, selectCategoryList);
            List<SkuInfoContract> skuList = new ArrayList<>();
            //if (mallRule.getLimitType() != null && mallRule.getLimitType() == MallConstant.SKU_TYPE) {
            List<MallRuleSku> mallRuleSkuList = mallRuleSkuMap.get(mallRuleId);
            if (!CollectionUtils.isEmpty(mallRuleSkuList)) {
                for (MallRuleSku mallRuleSku : mallRuleSkuList) {
                    SkuInfoContract skuInfoContract = new SkuInfoContract();
                    skuInfoContract.setSku_id(mallRuleSku.getSkuId());
                    skuInfoContract.setShort_description(mallRuleSku.getShortDescription());
                    skuInfoContract.setSku_category(mallRuleSku.getSkuCategory());
                    skuList.add(skuInfoContract);
                }
            }
            //}
//            List<EmployeeInfoContract> employees = genSimpleEmployeeInfoContracts(companyId, mallRuleId);
            {
                return new MallRuleDetailV3Contract(
                        mallRuleId,
                        mallRule.getName(),
                        mallRule.getLimitPriceFlag(),
                        mallRule.getLimitPriceHighest(),
                        Lists.newArrayListWithCapacity(ObjUtils.toInteger(countMap.get(mallRuleId), 0)),
                        nowCategoriesCount,
                        mallRule.getLimitType(),
                        skuList,
                        categories,
                        ObjUtils.toInteger(countMap.get(mallRuleId), 0)
                );
            }
        }).collect(Collectors.toList());
        return mallRuleDetailContracts;
    }


    /**
     * 转换数据
     *
     * @param list
     * @return
     */
    @Override
    public Map<String, String> convertId(List<MallCategoryNewContract> list) {
        Map<String, String> resultMap = new HashMap<>();
        for (MallCategoryNewContract mcn : list) {
            if (mcn != null) {
                // 获取二级
                List<SecondCategoryContract> cateList = mcn.getList();
                StringBuilder sb = new StringBuilder();
                // 遍历,然后获取二级的code
                for (SecondCategoryContract scc : cateList) {
                    String tmpStr = scc.getCategoryCode() + "";
                    // 将二级的所有code拼接成字符串
                    sb.append(tmpStr + ",");
                }
                String strR = sb.toString();
                strR = strR.substring(0, strR.lastIndexOf(","));

                // 一级code对应所有的属于它的二级code
                resultMap.put(mcn.getCategory_code() + "", strR);
            }

        }
        return resultMap;
    }

    // 返回父类的code
    public String getParentCode(Map<String, String> resultMap, String selectedIds) {
        selectedIds = "," + selectedIds + ",";
        StringBuilder sb = new StringBuilder();
        String strR = null;
        for (Map.Entry<String, String> entry : resultMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            String[] valueArr = value.split(",");
            for (String v : valueArr) {

                v = "," + v + ",";
                if (selectedIds.contains(v)) {
                    String tmpStr = key;
                    sb.append(tmpStr + ",");
                }
            }

        }
        strR = sb.toString();
        if (strR.contains(",")) {
            strR = strR.substring(0, strR.lastIndexOf(","));
            return strR.trim();
        } else {
            return null;
        }
    }

    /**
     * 获取用车规则的应用id
     *
     * @param companyId
     * @param ruleId
     * @return
     */
    @Override
    public EmployeeListContract matchTaxiRuleEmployeeWithRuleId(String companyId, Integer ruleId, Integer pageIndex, Integer pageSize) {
        return genTaxiRuleEmployeeInfoContracts(companyId, ruleId, pageIndex, pageSize);
    }

    /**
     * 用车规则全量更新应用人呢
     *
     * @param companyId
     * @param taxiRuleFullContract
     * @throws SaasException
     */
    @Override
    public void fullUpdateTaxiEmployee(String companyId, TaxiRuleFullContract taxiRuleFullContract, String userId) throws SaasException {
        if (taxiRuleFullContract == null || taxiRuleFullContract.getTaxiRuleId() == null || StringUtils.isBlank(companyId)) {
            throw new SaasException(GlobalResponseCode.ParametersIsNull);
        }
        List<String> fullEmployeeIds = taxiRuleFullContract.getFullEmployeeIds();
        //查询规则ID是否存在
        TaxiRuleExample taxiRuleExample = new TaxiRuleExample();
        taxiRuleExample.createCriteria().andCompanyIdEqualTo(companyId).andIdEqualTo(taxiRuleFullContract.getTaxiRuleId());
        if (taxiRuleMapper.countByExample(taxiRuleExample) < 1) {
            throw new SaasException(GlobalResponseCode.RuleIdNotExistInCompany);
        }

        iBaseEmployeeTaxiRuleExtService.updateEmployeeApplyRuleId(companyId, userId, fullEmployeeIds, CategorySubType.Taxi.getSubtype(), taxiRuleFullContract.getTaxiRuleId());
        //已废除
//            EmployeeTaxiRuleExample employeeTaxiRuleExample = new EmployeeTaxiRuleExample();
//            employeeTaxiRuleExample.createCriteria().andEmployeeIdIn(fullEmployeeIds).andCompanyIdEqualTo(companyId);
//            EmployeeTaxiRule employeeTaxiRule = new EmployeeTaxiRule();
//            employeeTaxiRule.setManualTaxiRuleId(taxiRuleFullContract.getTaxiRuleId());
//            employeeTaxiRule.setTaxiRuleFlag(true);
//            iBaseEmployeeTaxiRuleService.updateByExampleSelective(employeeTaxiRule, employeeTaxiRuleExample);

        //添加规则日志
        addOperateLogRule(companyId, taxiRuleFullContract.getTaxiRuleId().toString(), "", JSON.toJSONString(taxiRuleFullContract), 4, CategoryTypeEnum.Taxi.getCode());
    }

    /**
     * 获取用车规则关联的员工信息
     */
    private EmployeeListContract genTaxiRuleEmployeeInfoContracts(String companyId, Integer taxiRuleId, Integer pageIndex, Integer pageSize) {
        EmployeeListContract employeeListContract = new EmployeeListContract();
        employeeListContract.setPageIndex(pageIndex);
        employeeListContract.setPageSize(pageSize);
        EmployeeTaxiRuleDto employeeTaxiRuleDto = iBaseEmployeeTaxiRuleExtService.selectEmployeeTaxiRuleList(companyId, CategorySubType.Taxi.getSubtype(), taxiRuleId, pageIndex, pageSize);
        employeeListContract.setTotalCount(employeeTaxiRuleDto.getCount());
        List<EmployeeInfoContract> employees = Lists.newArrayList();
        List<EmployeeTaxiRule> employeeTaxiRuleList = employeeTaxiRuleDto.getEmployeeTaxiRuleList();
        if (CollectionUtils.isNotEmpty(employeeTaxiRuleList)) {
            List<String> employeeIds = employeeTaxiRuleList.stream().map(
                    employeeTaxiRule -> employeeTaxiRule.getEmployeeId()
            ).distinct().collect(Collectors.toList());
            List<EmployeeContract> employeeList = iBaseOrganizationService.getEmployee(employeeIds, companyId);
            employees = employeeList.stream().map(employee ->
                    new EmployeeInfoContract(
                            employee.getId(),
                            employee.getName(),
                            employee.getPhone_num(),
                            employee.getThird_employee_id(),
                            Lists.newArrayList(new AllEmployeeOfCompanyContract.EmployeeListBean.OrgUnitListBean(employee.getOrg_id(), employee.getOrg_name(), employee.getThird_org_id()))
                    )
            ).collect(Collectors.toList());//从公司的所有员工中获取应用改规则的员工信息
        }
        employeeListContract.setEmployees(employees);
        return employeeListContract;
    }

    /**
     * 获取酒店规则信息
     *
     * @param companyId
     * @param areaId
     * @return
     */
    @Override
    public HotelRuleContract ruleHotelInfo(String companyId, String areaId, String employeeId) {
        LOGGER.info("获取酒店规则信息，入参：companyId:{},areaId:{}，employeeId:{}", companyId, areaId, employeeId);
        HotelRuleContract data = new HotelRuleContract();
        //处理酒店规则信息
        EmployeeHotelRule employeeHotelRule = iBaseEmployeeHotelRuleService.queryEmployeeHotelRuleByPrimaryKey(employeeId, companyId);
        LOGGER.info("ruleHotelInfo.queryEmployeeHotelRuleByPrimaryKey, 获取用户酒店规则信息, employeeHotelRule={}",
                JsonUtils.toJson(employeeHotelRule));
        if (employeeHotelRule == null) {
            LOGGER.info("获取用户酒店规则信息为空");
            return new HotelRuleContract("", false, "", false, BigDecimal.ZERO);
        }
        Boolean hotelRuleFlag = employeeHotelRule.getHotel_rule_flag();
        String manual_hotel_rule_id = employeeHotelRule.getManual_hotel_rule_id();
        // 开启了多规则时，查询默认员工级别限制
        if (ObjUtils.toBoolean(employeeHotelRule.getMulti_rule_switch(), Boolean.FALSE)) {
            ControlOrderRuleDto consumeRule = consumeTemplateRuleService.getConsumeRule("", employeeHotelRule.getTemplate_id(), companyId,
                    null, com.fenbeitong.saas.core.model.enums.apply.BizType.Hotel, ConsumeTemplateRuleService.MULTI_RULE_VERSION);
            hotelRuleFlag = consumeRule.getIsEnable();
            if (hotelRuleFlag) {
                manual_hotel_rule_id = consumeRule.getRuleId();
            }
        }
        //限制规则信息
        if (!hotelRuleFlag) {
            LOGGER.info("获取用户酒店规则信息,不限制酒店规则");
            return new HotelRuleContract("", false, "", false, BigDecimal.ZERO);
        }

        //获取当前规则各配置的规则信息
        HotelRule hotelRule = JsonUtils.toObj(JsonUtils.toJson(iHotelGroupRuleService.queryHotelGroupRule(manual_hotel_rule_id)), HotelRule.class);
        LOGGER.info("获取规则信息列表：{}", JsonUtils.toJson(hotelRule));
        if (ObjUtils.isEmpty(hotelRule)) {
            LOGGER.info("获取酒店规则信息为空");
            return new HotelRuleContract("", false, "", false, BigDecimal.ZERO);
        }
        if (HotelPriceSetType.SENIORPATTERN == HotelPriceSetType.getEnum(hotelRule.getType())) {
            LOGGER.info("高级模式规则，暂时返回差标为空，待后续优化");
            return new HotelRuleContract("", false, "", false, null);
        }
        //订单返回差标消息
        StringBuffer ruleMsg = new StringBuffer();
        //存储规则的酒店星级
        String level = hotelRule.getLevel();
        String[] levelArray = null;
        //此处存在规则应用问题
        if (StringUtils.isNotEmpty(level)) {
            levelArray = level.split(",");
        }
        if (!hotelRule.getLevelFlag() || (hotelRule.getLevelFlag() && levelArray.length == 4)) {
            data.setLevelFlag(false);
        }
        if ((hotelRule.getLevelFlag() && levelArray.length < 4)) {
            StringBuffer levelBuffer = new StringBuffer();
            StringBuffer levelStrBuffer = new StringBuffer();
            data.setLevelFlag(true);
            //处理星级
            for (String levelTemp : levelArray) {
                if (!"4".equals(levelTemp)) {
                    levelBuffer.append(HotelLevel.getName(Integer.valueOf(levelTemp))).append("、");
                    levelStrBuffer.append(levelTemp).append(",");
                }
            }
            data.setLevel(levelStrBuffer.deleteCharAt(levelStrBuffer.length() - 1).toString());
            ruleMsg.append(levelBuffer.deleteCharAt(levelBuffer.length() - 1));
        }
        //处理城市级别对应价格
        //获取当前城市所在配置id
        //1.根据公司获取企业相关配置信息  调用刘斌方法获取
        List<CompanyAreaLevelGroupDTO> list = JsonUtils.toObj(JsonUtils.toJson(iHotelGroupAreaService.queryCompanyAreaGroup(companyId)), new com.fasterxml.jackson.core.type.TypeReference<List<CompanyAreaLevelGroupDTO>>() {
        });
        LOGGER.info("根据公司获取企业相关配置信息：{}", JsonUtils.toJson(list));
        List<CompanyAreaLevelGroupDTO> companyGroupList = new ArrayList<>();
        for (CompanyAreaLevelGroupDTO companyGroup : list) {
            if (CollectionUtils.isNotEmpty(hotelRule.getHotelRuleDetailList())) {
                for (HotelRuleDetailDTO item : hotelRule.getHotelRuleDetailList()) {
                    if (companyGroup.getId().equals(item.getCompanyAreaLevelGroupId())) {
                        companyGroupList.add(companyGroup);
                    }
                }
            }
        }

        Map<String, String> groupMap = getHotelAearDetail(companyGroupList, companyId, areaId);
        String areagroupid = "";
        String cityName = "";
        String otherCityName = "";
        if (!groupMap.isEmpty()) {
            areagroupid = groupMap.get("groupId");
            areaId = groupMap.get("cityId");
            cityName = groupMap.get("cityName");
            otherCityName = groupMap.get("otherCityName");
        }

        BigDecimal price = BigDecimal.ZERO;
        //判断所在城市分组如果是空的话  应该走其他城市配置
        if (StringUtils.isNotEmpty(areagroupid)) {
            LOGGER.info("获取到的企业配置分组id:{}", areagroupid);
            if (CollectionUtils.isNotEmpty(hotelRule.getHotelRuleDetailList())) {
                for (HotelRuleDetailDTO item : hotelRule.getHotelRuleDetailList()) {
                    if (item.getCompanyAreaLevelGroupId().equals(areagroupid) && item.getGroupTierFlag()) {
                        //设置企业城市分组名称
                        // data.setCityName(companyGroupList.stream().filter(b -> b.getId().equals(areagroupid)).collect(Collectors.toList()).get(0).getGroupTitle());
                        data.setCityName(cityName);
                        data.setHotelPriceFlag(item.getGroupTierFlag());
                        data.setPrice(item.getGroupTierPrice());
                        price = item.getGroupTierPrice();
                        break;
                    } else {
                        data.setHotelPriceFlag(false);
                    }
                }
            }
        } else if (hotelRule.getOtherTierFlag()) {
            data.setCityName(otherCityName);
            data.setHotelPriceFlag(true);
            data.setPrice(hotelRule.getOtherTierPrice());
            price = hotelRule.getOtherTierPrice();
        } else {
            data.setHotelPriceFlag(false);
        }
        if (data.getHotelPriceFlag()) {
            if (ruleMsg.length() > 0) {
                ruleMsg.append("\n").append("单价").append(price).append("元以下");
            } else {
                ruleMsg.append("单价").append(price).append("元以下");
            }
        }
        data.setRuleMsg(ruleMsg.toString());
        return data;
    }

    /**
     * 国际
     *
     * @param companyId
     * @param areaId
     * @param employeeId
     * @return
     */
    @Override
    public HotelRuleContract ruleIntlHotelInfo(String companyId, String areaId, String employeeId) {
        LOGGER.info("获取海外酒店规则信息，入参：companyId:{},areaId:{}，employeeId:{}", companyId, areaId, employeeId);
        HotelRuleContract data = new HotelRuleContract();
        //处理酒店规则信息
        List<EmployeeBaseRule> employeeBaseRules = iBaseEmployeeRuleService.queryRuleListByCompanyIdAndEmployeeIds(companyId, Lists.newArrayList(employeeId), CategoryTypeEnum.IntelHotel.getCode(), null);
        LOGGER.info("iBaseEmployeeRuleService.queryRuleListByCompanyIdAndEmployeeIds, 获取用户酒店规则信息, employeeBaseRules={}",
                JsonUtils.toJson(employeeBaseRules));
        if (CollectionUtils.isEmpty(employeeBaseRules)) {
            LOGGER.info("获取用户酒店规则信息为空");
            return new HotelRuleContract("", false, "", false, BigDecimal.ZERO);
        }
        EmployeeBaseRule employeeBaseRule = employeeBaseRules.get(0);
        //限制规则信息
        if (!employeeBaseRule.getRuleFlag()) {
            LOGGER.info("获取用户酒店规则信息,不限制酒店规则");
            return new HotelRuleContract("", false, "", false, BigDecimal.ZERO);
        }

        String manual_hotel_rule_id = employeeBaseRule.getRuleId();

        //获取当前规则各配置的规则信息
        HotelRule hotelRule = JsonUtils.toObj(JsonUtils.toJson(iHotelGroupRuleService.queryIntlHotelGroupRule(manual_hotel_rule_id)), HotelRule.class);
        LOGGER.info("获取规则信息列表：{}", JsonUtils.toJson(hotelRule));
        if (ObjUtils.isEmpty(hotelRule)) {
            LOGGER.info("获取酒店规则信息为空");
            return new HotelRuleContract("", false, "", false, BigDecimal.ZERO);
        }
        //订单返回差标消息
        StringBuffer ruleMsg = new StringBuffer();
        //存储规则的酒店星级
        String level = hotelRule.getLevel();
        String[] levelArray = null;
        //此处存在规则应用问题
        if (StringUtils.isNotEmpty(level)) {
            levelArray = level.split(",");
        }
        if (!hotelRule.getLevelFlag() || (hotelRule.getLevelFlag() && levelArray.length == 4)) {
            data.setLevelFlag(false);
        }
        if ((hotelRule.getLevelFlag() && levelArray.length < 4)) {
            StringBuffer levelBuffer = new StringBuffer();
            StringBuffer levelStrBuffer = new StringBuffer();
            data.setLevelFlag(true);
            //处理星级
            for (String levelTemp : levelArray) {
                if (!"4".equals(levelTemp)) {
                    levelBuffer.append(HotelLevel.getName(Integer.valueOf(levelTemp))).append("、");
                    levelStrBuffer.append(levelTemp).append(",");
                }
            }
            data.setLevel(levelStrBuffer.deleteCharAt(levelStrBuffer.length() - 1).toString());
            ruleMsg.append(levelBuffer.deleteCharAt(levelBuffer.length() - 1));
        }
        //处理城市级别对应价格
        //获取当前城市所在配置id
        //1.根据公司获取企业相关配置信息  调用刘斌方法获取
        List<CompanyAreaLevelGroupDTO> list = JsonUtils.toObj(JsonUtils.toJson(iHotelGroupAreaService.queryIntlCompanyAreaGroup(companyId)), new com.fasterxml.jackson.core.type.TypeReference<List<CompanyAreaLevelGroupDTO>>() {
        });
        LOGGER.info("根据公司获取企业相关配置信息：{}", JsonUtils.toJson(list));
        List<CompanyAreaLevelGroupDTO> companyGroupList = new ArrayList<>();
        for (CompanyAreaLevelGroupDTO companyGroup : list) {
            if (CollectionUtils.isNotEmpty(hotelRule.getHotelRuleDetailList())) {
                for (HotelRuleDetailDTO item : hotelRule.getHotelRuleDetailList()) {
                    if (companyGroup.getId().equals(item.getCompanyAreaLevelGroupId())) {
                        companyGroupList.add(companyGroup);
                    }
                }
            }
        }

        Map<String, String> groupMap = getIntlHotelAreaDetail(companyGroupList, companyId, areaId);
        String areagroupid = "";
        String cityName = "";
        String otherCityName = "";
        if (!groupMap.isEmpty()) {
            areagroupid = groupMap.get("groupId");
            areaId = groupMap.get("cityId");
            cityName = groupMap.get("cityName");
            otherCityName = groupMap.get("otherCityName");
        }

        BigDecimal price = BigDecimal.ZERO;
        //判断所在城市分组如果是空的话  应该走其他城市配置
        if (StringUtils.isNotEmpty(areagroupid)) {
            LOGGER.info("获取到的企业配置分组id:{}", areagroupid);
            if (CollectionUtils.isNotEmpty(hotelRule.getHotelRuleDetailList())) {
                for (HotelRuleDetailDTO item : hotelRule.getHotelRuleDetailList()) {
                    if (item.getCompanyAreaLevelGroupId().equals(areagroupid) && item.getGroupTierFlag()) {
                        //设置企业城市分组名称
                        // data.setCityName(companyGroupList.stream().filter(b -> b.getId().equals(areagroupid)).collect(Collectors.toList()).get(0).getGroupTitle());
                        data.setCityName(cityName);
                        data.setHotelPriceFlag(item.getGroupTierFlag());
                        data.setPrice(item.getGroupTierPrice());
                        price = item.getGroupTierPrice();
                        break;
                    } else {
                        data.setHotelPriceFlag(false);
                    }
                }
            }
        } else if (hotelRule.getOtherTierFlag()) {
            data.setCityName(otherCityName);
            data.setHotelPriceFlag(true);
            data.setPrice(hotelRule.getOtherTierPrice());
            price = hotelRule.getOtherTierPrice();
        } else {
            data.setHotelPriceFlag(false);
        }
        if (data.getHotelPriceFlag()) {
            if (ruleMsg.length() > 0) {
                ruleMsg.append("\n").append("单价").append(price).append("元以下");
            } else {
                ruleMsg.append("单价").append(price).append("元以下");
            }
        }
        data.setRuleMsg(ruleMsg.toString());
        return data;

    }

    //    /**
//     * 获取酒店规则信息  因改造管控至区县所以原方法注释
//     *
//     * @param companyId
//     * @param areaId
//     * @return
//     */
//    @Override
//    public HotelRuleContract ruleHotelInfo(String companyId, String areaId, String employeeId) {
//        LOGGER.info("获取酒店规则信息，入参：companyId:{},areaId:{}，employeeId:{}", companyId, areaId, employeeId);
//        HotelRuleContract data = new HotelRuleContract();
//        Integer cityLevel = getCityLevelByAreaId(companyId, areaId);
//        //处理酒店规则信息
//        EmployeeHotelRule employeeHotelRule = iBaseEmployeeHotelRuleService.queryEmployeeHotelRuleByPrimaryKey(employeeId, companyId);
//        if (employeeHotelRule == null) {
//            LOGGER.info("获取用户酒店规则信息为空");
//            return new HotelRuleContract("", false, "", false, BigDecimal.ZERO);
//        } else {
//            //限制规则信息
//            if (employeeHotelRule.getHotel_rule_flag()) {
//                String manual_hotel_rule_id = employeeHotelRule.getManual_hotel_rule_id();
//                HotelRuleExample exampleRuleExample = new HotelRuleExample();
//                exampleRuleExample.createCriteria().andIdEqualTo(manual_hotel_rule_id).andCompanyIdEqualTo(companyId);
//                List<HotelRule> hotelRuleList = hotelRuleMapper.selectByExample(exampleRuleExample);
//                //没有查询到规则信息
//                if (CollectionUtils.isEmpty(hotelRuleList)) {
//                    LOGGER.info("获取酒店规则信息为空");
//                    return new HotelRuleContract("", false, "", false, BigDecimal.ZERO);
//                } else {
//                    StringBuffer ruleMsg = new StringBuffer();
//                    HotelRule hotelRule = hotelRuleList.get(0);
//                    //获取酒店价格和酒店星级
//                    String level = hotelRule.getLevel();
//                    String levelArray[] = null;
//                    //此处存在规则应用问题
//                    if (StringUtils.isNotEmpty(level)) {
//                        levelArray = level.split(",");
//                    }
//                    if (!hotelRule.getLevelFlag() || (hotelRule.getLevelFlag() && levelArray.length == 4)) {
//                        data.setLevelFlag(false);
//                    }
//                    if ((hotelRule.getLevelFlag() && levelArray.length < 4)) {
//                        StringBuffer levelBuffer = new StringBuffer();
//                        StringBuffer levelStrBuffer = new StringBuffer();
//                        data.setLevelFlag(true);
//                        //处理星级
//                        for (String levelTemp : levelArray) {
//                            if (!"4".equals(levelTemp)) {
//                                levelBuffer.append(HotelLevel.getName(Integer.valueOf(levelTemp))).append("、");
//                                levelStrBuffer.append(levelTemp).append(",");
//                            }
//                        }
//                        data.setLevel(levelStrBuffer.deleteCharAt(levelStrBuffer.length() - 1).toString());
//                        ruleMsg.append(levelBuffer.deleteCharAt(levelBuffer.length() - 1).toString());
//                    }
//                    //处理城市级别对应价格
//                    BigDecimal price = BigDecimal.ZERO;
//                    LOGGER.info("获取到的城市级别:{}", cityLevel);
//                    if (CityRuleType.FirstTierCity.getCode() == cityLevel && hotelRule.getFirstTierFlag()) {
//                        data.setHotelPriceFlag(true);
//                        data.setPrice(hotelRule.getFirstTierPrice());
//                        price = hotelRule.getFirstTierPrice();
//                    } else if (CityRuleType.SecondTierCity.getCode() == cityLevel && hotelRule.getSecondTierFlag()) {
//                        data.setHotelPriceFlag(true);
//                        data.setPrice(hotelRule.getSecondTierPrice());
//                        price = hotelRule.getSecondTierPrice();
//                    } else if (CityRuleType.OtherTierCity.getCode() == cityLevel && hotelRule.getOtherTierFlag()) {
//                        data.setHotelPriceFlag(true);
//                        data.setPrice(hotelRule.getOtherTierPrice());
//                        price = hotelRule.getOtherTierPrice();
//                    } else {
//                        data.setHotelPriceFlag(false);
//                    }
//                    if (data.getHotelPriceFlag()) {
//                        if (ruleMsg.length() > 0) {
//                            ruleMsg.append("\n").append("单价").append(price).append("元以下");
//                        } else {
//                            ruleMsg.append("单价").append(price).append("元以下");
//                        }
//                    }
//                    data.setRuleMsg(ruleMsg.toString());
//                }
//            } else {
//                LOGGER.info("获取用户酒店规则信息,不限制酒店规则");
//                return new HotelRuleContract("", false, "", false, BigDecimal.ZERO);
//            }
//        }
//        return data;
//    }

    /**
     * 获取公司的规则信息以及所有管辖城市信息 by  ghq   20211211
     *
     * @param companyId
     * @param areaId
     * @return
     */
    @Override
    public Map<String, String> getHotelAearDetail(List<CompanyAreaLevelGroupDTO> companyGroupList, String companyId, String areaId) {
        LOGGER.info("getHotelAearDetail获取的信息，入参：companyId:{},areaId:{}", companyId, areaId);
        String areaProvince = "";
        String areaCity = "";
        String areaid = "";

        Map<String, String> map = new HashMap<>();
        List<CompanyAreaLevelGroupDetailDTO> companyGroupDetailList = new ArrayList<>();
        try {
            if (CollectionUtils.isNotEmpty(companyGroupList)) {
                companyGroupList.forEach(a -> {
                    if (CollectionUtils.isNotEmpty(a.getCompanyAreaLevelGroupDetailList())) {
                        companyGroupDetailList.addAll(0, a.getCompanyAreaLevelGroupDetailList());
                    }
                });
            }

            String otherCityName = "";

            //先根据areaId获取完整的城市层级链路
            //获取酒店城市信息（双飞提供城市数据接口）
            log.info("获取酒店城市信息:" + HostPropertyConfigTool.HOST_HARMONY + "/city/areas/" + areaId + "/full_path");
            String data = HttpTool.get(HostPropertyConfigTool.HOST_HARMONY + "/city/areas/" + areaId + "/full_path");
            log.info("获取酒店城市信息:" + data);
            AreaPath<JSONArray> areaPath = JSONObject.parseObject(data, AreaPath.class);
            //获取当前传入城市真实路径 并根据路径筛选出最小维度为区县，区县不存在则找城市，城市不存在则找省 获取到省市区三级
            if (ObjUtils.isNotEmpty(areaPath)) {
                if (ObjUtils.isNotEmpty(areaPath.getData())) {
                    JSONArray areas = areaPath.getData();
                    Integer arealenght = areas.size();
                    if (arealenght >= 3) {
                        areaid = areas.get(arealenght - 3).toString();
                        areaCity = areas.get(arealenght - 2).toString();
                        areaProvince = areas.get(arealenght - 1).toString();
                    } else if (arealenght >= 2) {
                        areaCity = areas.get(arealenght - 2).toString();
                        areaProvince = areas.get(arealenght - 1).toString();
                    } else {
                        areaProvince = areas.get(arealenght - 1).toString();
                    }
                }
            }

            log.info("开始获取城市名称");
            String areaCityName = getCityName(areaCity);
            String areaName = getCityName(areaid);
            log.info("开始获取城市名称结束");


            if (CollectionUtils.isNotEmpty(companyGroupDetailList)) {
                if (StringUtils.isNotEmpty(areaid)) {
                    map = getGroupId(companyGroupDetailList, areaid);
                    if (map.isEmpty()) {
                        map = getGroupId(companyGroupDetailList, areaCity);
                        if (map.isEmpty()) {
                            map = getGroupId(companyGroupDetailList, areaProvince);
                        } else {
                            map.put("cityName", areaCityName);
                        }
                    } else {
                        map.put("cityName", areaCityName + areaName);
                    }
                } else if (StringUtils.isNotEmpty(areaCity)) {
                    map = getGroupId(companyGroupDetailList, areaCity);
                    if (map.isEmpty()) {
                        map = getGroupId(companyGroupDetailList, areaProvince);
                    } else {
                        map.put("cityName", areaCityName);
                    }
                } else if (StringUtils.isNotEmpty(areaProvince)) {
                    map = getGroupId(companyGroupDetailList, areaProvince);
                    map.put("cityName", "");
                }
            }
            if (StringUtils.isEmpty(areaName)) {
                map.put("otherCityName", areaCityName);
            } else {
                map.put("otherCityName", areaCityName + areaName);
            }

        } catch (Exception e) {
            log.error(String.format("url:%s,调用城市信息接口异常：", HostPropertyConfigTool.HOST_HARMONY + "/city/areas/" + areaId + "/full_path" + e.getMessage()));
        }

        LOGGER.info("最终获取企业配置级别:{},入参：companyId:{},areaId:{}", map, companyId, areaId);
        //根据完整链路判断当前应为哪个配置项进行管控
        return map;
    }


    @Override
    public Map<String, String> getIntlHotelAreaDetail(List<CompanyAreaLevelGroupDTO> companyGroupList, String companyId, String areaId) {
        LOGGER.info("getIntlHotelAreaDetail获取的信息，入参：companyId:{},areaId:{}", companyId, areaId);
        Map<String, String> map = new HashMap<>();
        List<CompanyAreaLevelGroupDetailDTO> companyGroupDetailList = new ArrayList<>();
        try {
            if (CollectionUtils.isNotEmpty(companyGroupList)) {
                companyGroupList.forEach(a -> {
                    if (CollectionUtils.isNotEmpty(a.getCompanyAreaLevelGroupDetailList())) {
                        companyGroupDetailList.addAll(0, a.getCompanyAreaLevelGroupDetailList());
                    }
                });
            }
            if (StringUtils.isBlank(areaId)) {
                return map;
            }
            FullPathAreaDTO areaDTO = iCityService.getIntlCityFullPath(areaId);
            LOGGER.info("海外酒店全路径:{}",JsonUtils.toJson(areaDTO));
            if (areaDTO == null) {
                return map;
            }

            if (CollectionUtils.isNotEmpty(companyGroupDetailList)) {
                Map<String, CompanyAreaLevelGroupDetailDTO> matchedAreaGroupMap = Maps.newHashMap();
                for (CompanyAreaLevelGroupDetailDTO c : companyGroupDetailList) {
                    // 国家匹配的分组
                    if (Objects.equals(c.getAreaCode(), areaDTO.getFbNationId())) {
                        matchedAreaGroupMap.put("nation", c);
                    }
                    // 城市匹配的分组
                    if(Objects.equals(c.getAreaCode(), areaDTO.getFbCityId())) {
                        matchedAreaGroupMap.put("city", c);
                    }
                    // 区县匹配的分组
                    if (Objects.equals(c.getAreaCode(), areaDTO.getFbDistrictId())) {
                        matchedAreaGroupMap.put("district", c);
                    }
                }
                // 按照优先级获取   区县 -> 城市 -> 国家
                CompanyAreaLevelGroupDetailDTO matchedAreaGroup = matchedAreaGroupMap.getOrDefault("district",
                        matchedAreaGroupMap.getOrDefault("city", matchedAreaGroupMap.get("nation")));
                if (ObjUtils.isNotEmpty(matchedAreaGroup)) {
                    map.put("groupId", matchedAreaGroup.getAreaGroupId());
                    map.put("cityId", matchedAreaGroup.getAreaCode());
                    map.put("cityName", matchedAreaGroup.getAreaName());
                }
            }
            return map;
        } catch (Exception e) {
            log.error("getIntlHotelAreaDetail获取的信息异常：{}", e.getMessage());
        }
        return map;
    }
    private String getCityName(String areaId) {
        String cityName = "";
        try {
            String data = HttpTool.get(HostPropertyConfigTool.HOST_HARMONY + "/city/areas/" + areaId);
            log.info("getCityName获取城市信息:" + data);
            AreaPath<Map<String, String>> areaPath = com.fenbeitong.common.utils.json.JsonUtils.parseJson(data, AreaPath.class);
            if (!Objects.isNull(areaPath) && !Objects.isNull(areaPath.getData())) {
                Map<String, String> map = new HashMap<>();
                map.putAll(areaPath.getData());
                cityName = map.get("name");
            }
        } catch (IOException e) {
            e.printStackTrace();
        } catch (HttpException e) {
            e.printStackTrace();
        }
        return cityName;
    }

    private Map<String, String> getGroupId(List<CompanyAreaLevelGroupDetailDTO> companyGroupDetailList, String id) {
        Map<String, String> map = new HashMap<>();
        for (CompanyAreaLevelGroupDetailDTO c : companyGroupDetailList) {
            if (c.getAreaCode().equals(id)) {
                map.put("groupId", c.getAreaGroupId());
                map.put("cityId", c.getAreaCode());
                break;
            }
        }
        return map;
    }

    /**
     * 获取城市等级
     *
     * @param companyId
     * @param areaId
     * @return
     */
    @Override
    public Integer getCityLevelByAreaId(String companyId, String areaId) {
        LOGGER.info("获取的城市级别信息，入参：companyId:{},areaId:{}", companyId, areaId);
        Integer cityLevel = -1;
        //获取当前城市级别
        CompanyAreaLevelExample companyAreaLevelExample = new CompanyAreaLevelExample();
        companyAreaLevelExample.createCriteria().andCompanyIdEqualTo(companyId);
        //获取企业当前配置的一二级城市管控
        List<CompanyAreaLevel> companyAreaLevelList = companyAreaLevelMapper.selectByExample(companyAreaLevelExample);
        //获取酒店城市编码
        Area area = areaMapper.selectByPrimaryKey(areaId);
        //如果企业没有进行配置
        if (CollectionUtils.isEmpty(companyAreaLevelList)) {
            //获取通用城市设置 根据城市等级判定城市级别 1 2 进行设置 否则为其它
            if (area != null && (Integer.valueOf(1).equals(area.getLevel()) || Integer.valueOf(2).equals(area.getLevel()))) {
                cityLevel = area.getLevel();
            }
        }
        //企业进行配置
        else {
            //筛选所匹配的城市所在等级集合
            List<CompanyAreaLevel> areaLevelList = companyAreaLevelList.stream().filter(areaInfo -> areaInfo.getAreaId().equals(areaId)).collect(Collectors.toList());
            //找到城市说明在一 二级城市中
            if (CollectionUtils.isNotEmpty(areaLevelList)) {
                LOGGER.info("设置的找到公司匹配的城市级别信息，入参：companyId:{},areaId:{}", companyId, areaId);
                //设置城市等级为公司配置等级
                cityLevel = areaLevelList.get(0).getLev();
            } else {
                //当前公司设置在其他城市中 判断城市数据不为空 并且在1级 或者 2级之内
                if (area != null && (Integer.valueOf(1).equals(area.getLevel()) || Integer.valueOf(2).equals(area.getLevel()))) {
                    //获取公司配置城市级别与城市级别一直的其他城市
                    List<CompanyAreaLevel> notAreaList = companyAreaLevelList.stream().filter(areaInfo -> areaInfo.getLev().equals(area.getLevel())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(notAreaList)) {
                        //城市级别设置为 当前城市级别
                        cityLevel = area.getLevel();
                    }
                }
            }
        }
        LOGGER.info("最终城市级别:{},入参：companyId:{},areaId:{}", cityLevel, companyId, areaId);
        return cityLevel;
    }

    @Transactional(value = "fenbeitong")
    public void deleteHotelRule(RuleDeleteContract deleteContract, String companyId, String userId) throws SaasException {
        //查询规则ID是否存在
        HotelRuleExample hotelRuleExample = new HotelRuleExample();
        hotelRuleExample.createCriteria().andCompanyIdEqualTo(companyId)
                .andIdIn(deleteContract.getRule_ids());
        if (deleteContract.getRule_ids().size() != hotelRuleMapper.countByExample(hotelRuleExample)) {
            throw new SaasException(GlobalResponseCode.RuleIdNotExistInCompany);
        }
        // 删除之前先查询，为了推送操作日志消息拿数据
        HotelRule hotelRule = hotelRuleMapper.selectByPrimaryKey(deleteContract.getRule_ids().get(0));
        HotelRuleGroupDetailExample detailExample = new HotelRuleGroupDetailExample();
        for (String ruleId : deleteContract.getRule_ids()) {
            iBaseEmployeeHotelRuleExtService.updateManualRuleIdByRuleId(companyId, userId, ruleId);
            HotelRuleExample hotelRuleDeleteExample = new HotelRuleExample();
            hotelRuleDeleteExample.createCriteria().andCompanyIdEqualTo(companyId).andIdEqualTo(ruleId);
            hotelRuleMapper.deleteByExample(hotelRuleDeleteExample);
            // 删除城市分组关联的信息
            detailExample.clear();
            detailExample.createCriteria().andCompanyIdEqualTo(companyId).andHotelRuleIdEqualTo(ruleId);
            hotelRuleGroupDetailMapper.deleteByExample(detailExample);
        }
        //添加规则日志
        addOperateLogRule(companyId, JSON.toJSONString(deleteContract.getRule_ids()), userId, JSON.toJSONString(deleteContract), 3, CategoryTypeEnum.Hotel.getCode());
        if (deleteContract.getRule_ids().size() == 1) {
            companyLogKafkaProducerService.sendCompanyLogMsg(CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Hotel_Rule, LogOperateActionEnum.DELETE,
                    LogOperateObjectEnum.HOTEL_RULE, hotelRule.getName()));
        } else {
            companyLogKafkaProducerService.sendCompanyLogMsg(CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Hotel_Rule, LogOperateActionEnum.BATCH_DELETE,
                    LogOperateObjectEnum.HOTEL_RULE, ""));
        }
    }

    private void addOperateLogRule(String companyId, String ruleId, String userId, String operateContent, int operateType, int businessType) {
        OperateLogRule operateLogRule = new OperateLogRule();
        operateLogRule.setCompanyId(companyId);
        operateLogRule.setBusinessType(businessType);
        operateLogRule.setRuleId(ruleId);
        operateLogRule.setOperateType(operateType);
        operateLogRule.setCreateUser(userId);
        operateLogRule.setOperateContent(operateContent);
        operateLogRuleService.addOperateLogRule(operateLogRule);
    }


    @Override
    public List<SkuInfoContract> getSkuListByCompanyIdAndEmployeeId(String companyId, String employeeId) {
        List<SkuInfoContract> skuInfoContractList = new ArrayList<>();
        EmployeeMallRule employeeMallRule = iBaseEmployeeMallRuleService.queryEmployeeMallRuleByPrimaryKey(employeeId, companyId);
        if (employeeMallRule == null) {
            throw new SaasException(GlobalResponseCode.UserIdInvalid);
        }
        MallRuleSkuExample mallRuleSkuExample = new MallRuleSkuExample();
        mallRuleSkuExample.createCriteria().andRuleIdEqualTo(employeeMallRule.getManualMallRuleId());
        List<MallRuleSku> mallRuleSkus = mallRuleSkuMapper.selectByExample(mallRuleSkuExample);
        if (CollectionUtils.isNotEmpty(mallRuleSkus)) {
            for (MallRuleSku mallRuleSku : mallRuleSkus) {
                SkuInfoContract skuInfoContract = new SkuInfoContract();
                skuInfoContract.setSku_id(mallRuleSku.getSkuId());
                skuInfoContract.setShort_description(mallRuleSku.getShortDescription());
                skuInfoContract.setSku_category(mallRuleSku.getSkuCategory());
                skuInfoContractList.add(skuInfoContract);
            }
        }
        return skuInfoContractList;
    }

    /**
     * v2
     * 添加符合国际机票规则的员工到返回结果 results 中
     */
    private void addIntlAirAuthorizationEmployeeV2(List<AuthorizationEmployeeV2Contract> results, List<EmployeeAirRuleContract> intlAirRuleList,
                                                   Map<String, String> employeeIdNameMap, Map<String, String> employeeIdDepartmentNameMap,
                                                   Map<String, Optional<Boolean>> employeeAirRuleMap, Map<String, Optional<Boolean>> employeeHotelRuleMap,
                                                   Map<String, Optional<Boolean>> employeeTrainRuleMap, Map<String, Optional<Boolean>> employeeMallRuleMap,
                                                   Map<String, Optional<Boolean>> employeeTaxiRuleMap, Map<String, Optional<Boolean>> employeeDinnerRuleMap, Map<String, Optional<Boolean>> employeeIntlAirRuleMap,
                                                   Map<String, Optional<EmployeeAirRuleContract>> employeeAirApplyMap,
                                                   Map<String, Optional<EmployeeHotelRuleContract>> employeeHotelApplyMap,
                                                   Map<String, Optional<EmployeeTrainRuleContract>> employeeTrainApplyMap,
                                                   Map<String, Optional<EmployeeTaxiRuleContract>> employeeTaxiApplyMap,
                                                   Map<String, Optional<EmployeeMallRule>> employeeMallApplyMap, Boolean applyFlag,
                                                   Map<String, Optional<EmployeeDinnerRule>> employeeDinnerApplyMap, Map<String, Optional<EmployeeAirRuleContract>> employeeIntlAirApplyMap, List<Integer> exceedAirFlag) {
        intlAirRuleList.stream()
                .filter(rule -> AirRuleType.getAllowedType().contains(Optional.ofNullable(rule.getAir_rule()).orElse(0)))
                /*.filter(rule -> {
                    if (exceedAirFlag == null || (exceedAirFlag.size() == 1 && exceedAirFlag.get(0) == 0)) {
                        return true;
                    } else {
                        if (exceedAirFlag.size() >= 2) {
                            return rule.getAir_exceed_buy_type() == exceedAirFlag.get(0) || rule.getAir_exceed_buy_type() == exceedAirFlag.get(1);
                        }
                        return rule.getAir_exceed_buy_type() == exceedAirFlag.get(0);
                    }
                })
                .filter(rule -> {
                    if (applyFlag == null) {
                        return true;
                    } else {
                        return rule.getAir_verify_flag().equals(applyFlag);
                    }
                })*/
                .forEach(rule -> addAuthorizationEmployeeContractV2(
                        results,
                        rule.getEmployee_id(),
                        employeeIdNameMap,
                        employeeIdDepartmentNameMap,
                        employeeAirRuleMap,
                        employeeHotelRuleMap,
                        employeeTrainRuleMap,
                        employeeMallRuleMap,
                        employeeTaxiRuleMap,
                        employeeDinnerRuleMap,
                        employeeIntlAirRuleMap,
                        employeeAirApplyMap,
                        employeeHotelApplyMap,
                        employeeTrainApplyMap,
                        employeeTaxiApplyMap,
                        employeeMallApplyMap,
                        employeeDinnerApplyMap,
                        employeeIntlAirApplyMap)
                );
    }


    /**
     * 采购规则列表 新接口v3(stereo)
     */
    @Override
    public List<MallRuleDetailV3Contract> getMallRuleV3Detail(String companyId, String ruleId, String ruleName, GenMallCategory genCategory, String token) throws SaasException, IOException, HttpException, ParseException {
        // 采购规则基本数据
        MallRuleExample mallRuleExample = new MallRuleExample();
        MallRuleExample.Criteria mallRuleExampleCriteria = mallRuleExample.createCriteria();
        mallRuleExampleCriteria.andIdEqualTo(ruleId);
        List<MallRule> mallRules = mallRuleMapper.selectByExample(mallRuleExample);
        //最新的采购类目列表
        List<MallCategoryNewContract> mallCategoryContracts = vendorHubService.getNewCategories(token);
        int categoriesCount = 0;
        int tempCount = 0;
        for (MallCategoryNewContract category : mallCategoryContracts) {
            tempCount = category.getList().size();
            categoriesCount = categoriesCount + tempCount;
        }
        //排序
        Date defaultDate = DateUtils.parse("1992-09-03 00:00:00");
        mallRules.sort(((o1, o2) -> -Optional.ofNullable(o1.getModifyTime()).orElse(defaultDate).compareTo(Optional.ofNullable(o2.getModifyTime()).orElse(defaultDate))));
        final int nowCategoriesCount = categoriesCount;
        List<MallRuleDetailV3Contract> mallRuleDetailContracts = mallRules.stream().map(mallRule -> {
            // 单条采购规则 id
            String mallRuleId = mallRule.getId();
            // 已选的采购类目id
            String selectedCategoryIds = mallRule.getLimitCategoryIds();
            List<Long> selectCategoryList = !org.springframework.util.StringUtils.isEmpty(selectedCategoryIds) ? Arrays.asList(selectedCategoryIds.split(",")).stream().distinct().map(Long::parseLong).collect(Collectors.toList()) : new ArrayList<>();

            //匹配之后得到已选中的类目对象
            List<MallCategoryNewContract> categories = genCategory.genMallNew(mallCategoryContracts, selectCategoryList);
            MallRuleSkuExample mallRuleSkuExample = new MallRuleSkuExample();
            mallRuleSkuExample.createCriteria().andRuleIdEqualTo(mallRuleId);
            List<SkuInfoContract> skuList = new ArrayList<>();
            if (mallRule.getLimitType() != null && mallRule.getLimitType().equals(MallConstant.SKU_TYPE)) {
                List<MallRuleSku> mallRuleSkuList = mallRuleSkuMapper.selectByExample(mallRuleSkuExample);
                if (!CollectionUtils.isEmpty(mallRuleSkuList)) {
                    for (MallRuleSku mallRuleSku : mallRuleSkuList) {
                        SkuInfoContract skuInfoContract = new SkuInfoContract();
                        skuInfoContract.setSku_id(mallRuleSku.getSkuId());
                        skuInfoContract.setShort_description(mallRuleSku.getShortDescription());
                        skuInfoContract.setSku_category(mallRuleSku.getSkuCategory());
                        skuList.add(skuInfoContract);
                    }
                }
            }
            {
                return new MallRuleDetailV3Contract(
                        mallRuleId,
                        mallRule.getName(),
                        mallRule.getLimitPriceFlag(),
                        mallRule.getLimitPriceHighest(),
                        Lists.newArrayList(),
                        nowCategoriesCount,
                        mallRule.getLimitType(),
                        skuList,
                        categories,
                        0
                );
            }
        }).collect(Collectors.toList());
        return mallRuleDetailContracts;
    }

    /**
     * 查询某个用户的规则
     *
     * @param userId
     * @param type
     * @return
     */
    @Override
    public MyRuleDisployContract queryRuleInfo(String userId, Integer type, String companyId) {
        if (StringUtils.isBlank(userId) || BizType.getEnum(type) == BizType.Nothing || StringUtils.isBlank(companyId)) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
        //国内机票
        if (type == BizType.AirPlane.getCode()) {
            EmployeeAirRule employeeAirRule = iBaseEmployeeAirRuleService.queryEmployeeAirRuleByPrimaryKey(userId, companyId);
            List<MyRuleDisployContract.NameDescItem> airNameDescItemList = Lists.newArrayList();
            MyRuleDisployContract myRuleAir = new MyRuleDisployContract();
            if (employeeAirRule != null) {
                if (employeeAirRule.getAir_rule() != null) {
                    myRuleAir.setAuth_flag(employeeAirRule.getAir_rule() == 4 ? 1 : 0);
                }
                myRuleAir.setApprove_flag(employeeAirRule.getAir_verify_flag() == true ? 1 : 0);
                String manualAirRuleId = employeeAirRule.getManual_air_rule_id();
                Boolean airRuleFlag = employeeAirRule.getAir_rule_flag();
                if (airRuleFlag && !StringUtils.isEmpty(manualAirRuleId)) {
                    AirRule airRule = airRuleMapper.selectByPrimaryKey(manualAirRuleId);
                    if (airRule != null) {
                        //可订舱位
                        Boolean airCabinFlag = airRule.getAirCabinFlag();
                        String airCabinType = airRule.getAirCabinType();
                        if (airCabinFlag) {
                            if (airCabinType != null) {
                                String[] cabinTypeList = airCabinType.split(",");
                                List<String> cabinNameList = Arrays.stream(cabinTypeList).map(key -> AirCabinType.getName(ObjUtils.toInteger(key, 0))).collect(Collectors.toList());
                                airNameDescItemList.add(new MyRuleDisployContract.NameDescItem("可订舱位", StringUtils.join(cabinNameList, ",")));
                            }
                        } else {
                            if (airCabinType != null) {
                                airNameDescItemList.add(new MyRuleDisployContract.NameDescItem("可订舱位", "请联系管理员设置"));
                            }
                        }
                        Boolean airPriceFlag = airRule.getAirPriceFlag();
                        Boolean airDiscountFlag = airRule.getAirDiscountFlag();
                        if (!airPriceFlag && !airDiscountFlag) {
                            airNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单张票价", "无限制"));
                        } else if (airPriceFlag) {
                            airNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单张票价",
                                    "需低于¥" + BigDecimalTool.formatMoney(airRule.getAirUnitPrice())));
                        } else {
                            if (org.springframework.util.StringUtils.isEmpty(airRule.getAirDiscount())) {
                                airNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单张票价", "无限制"));
                            } else {
                                airNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单张票价", "折扣需低于" + ObjUtils.toString(airRule.getAirDiscount().multiply(new BigDecimal(10)).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue()) + "折"));
                            }
                        }
                        //最低价限制
                        Integer lowPriceFlag = airRule.getLowPriceFlag();
                        Integer lowPriceTime = airRule.getLowPriceTime();
                        if (lowPriceFlag == 0) {
                            airNameDescItemList.add(new MyRuleDisployContract.NameDescItem("最低价限制", "无限制"));
                        } else {
                            String StopoverFlightMsg = FilterStopoverFlightFlag.ALL.equalsTo(airRule.getFilterStopoverFlightFlag())
                                    ? "" : FilterStopoverFlightFlag.valueOf(airRule.getFilterStopoverFlightFlag()).getDesc();
                            String descFormat = StrUtils.formatString(GlobalResponseCode.OrderCheckAirMinPriceNoAuth.getMsg(),
                                    airRule.getLowPriceTime(),
                                    AirPortCityEnum.getDescribe(airRule.getAirPortCityFlag()),
                                    StopoverFlightMsg);
                            airNameDescItemList.add(new MyRuleDisployContract.NameDescItem("最低价限制", descFormat));
                        }
                        //提前预定天数
                        airNameDescItemList.add(new MyRuleDisployContract.NameDescItem("提前预订", this.getPreDayStr(airRule.getPrivDayMin(), airRule.getPrivDayMax())));
                        //起飞时段
                        airNameDescItemList.add(new MyRuleDisployContract.NameDescItem("起飞时段", DayTypeChange(airRule.getId(), 2)));
                        // 行程耗时
                        if (airRule.getTimeConsumeFlag()) {
                            StringBuilder builder = new StringBuilder();
                            builder.append("火车(高铁/动车)最短耗时超过").append(airRule.getTimeConsumeValue()).append("小时")
                                    .append("，或飞机含税价比火车(高铁/动车)低");
                            if (ObjUtils.toInteger(airRule.getTimeLowerPrice(), 0) > 0) {
                                builder.append(airRule.getTimeLowerPrice()).append("元");
                            }
                            airNameDescItemList.add(new MyRuleDisployContract.NameDescItem("行程耗时", builder.toString()));
                        } else {
                            airNameDescItemList.add(new MyRuleDisployContract.NameDescItem("行程耗时", "不限制"));
                        }
                        if (airRule.getDistanceLimitFlag()) {
                            StringBuilder builder = new StringBuilder();
                            builder.append("城市直线距离大于").append(airRule.getDistanceLimitValue()).append("公里")
                                    .append("，或飞机含税价比火车(高铁/动车)低");
                            if (ObjUtils.toInteger(airRule.getDistanceLowerPrice(), 0) > 0) {
                                builder.append(airRule.getDistanceLowerPrice()).append("元");
                            }
                            airNameDescItemList.add(new MyRuleDisployContract.NameDescItem("行程距离", builder.toString()));
                        } else {
                            airNameDescItemList.add(new MyRuleDisployContract.NameDescItem("行程距离", "不限制"));
                        }

                    }
                }
            }
            myRuleAir.setDetail_rules(airNameDescItemList);
            return myRuleAir;
        } else if (type == BizType.InternationalAirPlane.getCode()) {
            EmployeeIntlAirRule employeeIntlAirRule = iBaseEmployeeIntlAirRuleService.queryEmployeeIntlAirRuleByPrimaryKey(userId, companyId);
            List<MyRuleDisployContract.NameDescItem> intlAirNameDescItemList = Lists.newArrayList();
            MyRuleDisployContract myRuleIntlAir = new MyRuleDisployContract();
            myRuleIntlAir.setOrder_approve_flag(employeeIntlAirRule.getIntl_air_order_verify_flag() == true ? 1 : 0);
            if (employeeIntlAirRule != null) {
                if (employeeIntlAirRule.getAir_rule() != null) {
                    myRuleIntlAir.setAuth_flag(employeeIntlAirRule.getAir_rule() == 4 ? 1 : 0);
                }
                myRuleIntlAir.setApprove_flag(employeeIntlAirRule.getAir_verify_flag() == true ? 1 : 0);
                String manualIntlAirRuleId = employeeIntlAirRule.getManual_air_rule_id();
                Boolean intlAirRuleFlag = employeeIntlAirRule.getAir_rule_flag();
                if (intlAirRuleFlag && !StringUtils.isEmpty(manualIntlAirRuleId)) {
                    IntlAirRule airRule = intlAirRuleMapper.selectByPrimaryKey(manualIntlAirRuleId);
                    if (airRule != null) {
                        //可订舱位
                        Boolean airCabinFlag = airRule.getAirCabinFlag();
                        String airCabinType = airRule.getAirCabinType();
                        if (airCabinFlag) {
                            if (airCabinType != null) {
                                String[] cabinTypeList = airCabinType.split(",");
                                List<String> cabinNameList = Arrays.stream(cabinTypeList).map(key -> AirCabinType.getName(ObjUtils.toInteger(key, 0))).collect(Collectors.toList());
                                intlAirNameDescItemList.add(new MyRuleDisployContract.NameDescItem("可订舱位", StringUtils.join(cabinNameList, ",")));
                            }
                        } else {
                            if (airCabinType != null) {
                                intlAirNameDescItemList.add(new MyRuleDisployContract.NameDescItem("可订舱位", "请联系管理员设置"));
                            }
                        }
                        Boolean airPriceFlag = ObjUtils.ifNull(airRule.getAirPriceFlag(), false);
                        if (!airPriceFlag) {
                            intlAirNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单张票价", "无限制"));
                        } else {
                            intlAirNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单张票价",
                                    "需低于¥" + BigDecimalTool.formatMoney(airRule.getAirUnitPrice())));
                        }
                        //提前预定天数
                        intlAirNameDescItemList.add(new MyRuleDisployContract.NameDescItem("提前预订", this.getPreDayStr(airRule.getPrivDayMin(), airRule.getPrivDayMax())));
                    }
                }
            }
            myRuleIntlAir.setDetail_rules(intlAirNameDescItemList);
            return myRuleIntlAir;
        } else if (type == BizType.Hotel.getCode()) {
            EmployeeHotelRule employeeHotelRule = iBaseEmployeeHotelRuleService.queryEmployeeHotelRuleByPrimaryKey(userId, companyId);
            List<MyRuleDisployContract.NameDescItem> hotelNameDescItemList = Lists.newArrayList();
            MyRuleDisployContract myRuleHotel = new MyRuleDisployContract();
            if (employeeHotelRule != null) {
                if (employeeHotelRule.getHotel_rule() != null) {
                    myRuleHotel.setAuth_flag(employeeHotelRule.getHotel_rule() == 2 ? 1 : 0);
                }
                if (myRuleHotel.getAuth_flag() == 0) {
                    myRuleHotel.setView_flag(false);
                }
                myRuleHotel.setApprove_flag(employeeHotelRule.getHotel_verify_flag() == true ? 1 : 0);
                String manualHotelRuleId = employeeHotelRule.getManual_hotel_rule_id();
                Boolean hotelRuleFlag = employeeHotelRule.getHotel_rule_flag();
                if (hotelRuleFlag && StringUtils.isNotBlank(manualHotelRuleId)) {
                    HotelRule hotelRule = hotelRuleMapper.selectByPrimaryKey(manualHotelRuleId);
                    if (hotelRule != null) {
                        Integer hotelType = hotelRule.getType();
                        myRuleHotel.setType(hotelType);
                        Boolean ruleLimit = hotelRule.getRuleLimit();
                        myRuleHotel.setRule_limit(ruleLimit);
                        //高级模式
                        if (HotelPriceSetType.SENIORPATTERN.getCode() == hotelType.intValue()) {
                            if (!hotelRule.getRuleLimit()) {
                                myRuleHotel.setView_flag(false);
                                hotelNameDescItemList.add(new MyRuleDisployContract.NameDescItem("房费限制", "无限制"));
                            }
                        } else {
                            hotelNameDescItemList.add(new MyRuleDisployContract.NameDescItem("房费限制", getGroupPriceRule(companyId, hotelRule, myRuleHotel)));
                        }
                        Boolean levelFlag = hotelRule.getLevelFlag();
                        if (levelFlag) {
                            String level = hotelRule.getLevel();
                            String[] levelList = level.split(",");
                            List<String> cabinNameList = Arrays.stream(levelList).map(key -> HotelLevel.getName(ObjUtils.toInteger(key, 0))).collect(Collectors.toList());
                            hotelNameDescItemList.add(new MyRuleDisployContract.NameDescItem("酒店类型", StringUtils.join(cabinNameList, ",")));
                        } else {
                            hotelNameDescItemList.add(new MyRuleDisployContract.NameDescItem("酒店类型", "无限制"));
                        }
                        //提前预定天数
                        hotelNameDescItemList.add(new MyRuleDisployContract.NameDescItem("提前预订", this.getPreDayStr(hotelRule.getPrivDayMin(), hotelRule.getPrivDayMax())));
                        // base地限制
                        Integer employeeBaseLocationControl = ObjUtils.toInteger(hotelRule.getEmployeeBaseLocationControl(), 0);
                        StringBuilder builder = new StringBuilder();
                        if (employeeBaseLocationControl == 0) {
                            builder.append("不限制");
                        } else if (employeeBaseLocationControl == 2) {
                            builder.append("按城市限制");
                        } else if (employeeBaseLocationControl == 3) {
                            builder.append("按区/县限制");
                        }
                        EmployeeContract employeeInfo = iBaseEmployeeExtService.queryEmployeeInfo(userId, companyId);
                        log.info("调用uc查询员工信息, employeeInfo:{}", JsonUtils.toJson(employeeInfo));
                        if (ObjUtils.isNotEmpty(employeeInfo) && ObjUtils.isNotEmpty(employeeInfo.getLocationCodeList())) {
                            List<String> cityNameList = Lists.newArrayList();
                            employeeInfo.getLocationCodeList()
                                    .forEach(cityId -> cityNameList.add(iCityService.getHierarchicalCityName(cityId, 2, "-")));
                            builder.append("，常驻地信息：").append(String.join("、", cityNameList));
                        }
                        hotelNameDescItemList.add(new MyRuleDisployContract.NameDescItem("常驻地限制", builder.toString()));


                    }
                } else {
                    myRuleHotel.setView_flag(false);
                }
            }
            myRuleHotel.setDetail_rules(hotelNameDescItemList);
            return myRuleHotel;
        } else if (type == BizType.Train.getCode()) {
            EmployeeTrainRule employeeTrainRule = iBaseEmployeeTrainRuleService.queryEmployeeTrainRuleByPrimaryKey(userId, companyId);
            List<MyRuleDisployContract.NameDescItem> trainNameDescItemList = Lists.newArrayList();
            MyRuleDisployContract myRuleTrain = new MyRuleDisployContract();
            if (employeeTrainRule != null) {
                if (employeeTrainRule.getTrain_rule() != null) {
                    myRuleTrain.setAuth_flag(employeeTrainRule.getTrain_rule() == 4 ? 1 : 0);
                }
                myRuleTrain.setApprove_flag(employeeTrainRule.getTrain_verify_flag() == true ? 1 : 0);
                String manualTrainRuleId = employeeTrainRule.getManual_train_rule_id();
                Boolean trainRuleFlag = employeeTrainRule.getTrain_rule_flag();
                if (trainRuleFlag && StringUtils.isNotBlank(manualTrainRuleId)) {
                    TrainRule trainRule = trainRuleMapper.selectByPrimaryKey(manualTrainRuleId);
                    if (trainRule != null) {
                        Boolean trainSeatFlag = trainRule.getTrainSeatFlag();
                        String commonTrainSeatType = trainRule.getCommonTrainSeatType();
                        String highspeedTrainSeatType = trainRule.getHighspeedTrainSeatType();
                        if (Objects.equals(trainSeatFlag, true)) {
                            String[] commonTrainSeatTypeList = StringUtils.isEmpty(commonTrainSeatType) ? new String[0] : commonTrainSeatType.split(",");
                            String[] highspeedTrainSeatTypeList = StringUtils.isEmpty(highspeedTrainSeatType) ? new String[0] : highspeedTrainSeatType.split(",");
                            if ((commonTrainSeatTypeList.length == 0 && highspeedTrainSeatTypeList.length == 0) ||
                                    (commonTrainSeatTypeList.length + highspeedTrainSeatTypeList.length == TrainSeatType.values().length)) {
                                trainNameDescItemList.add(new MyRuleDisployContract.NameDescItem("可订席别", "无限制"));
                            } else {
                                List<String> allList = new ArrayList<>();
                                //普通
                                if (commonTrainSeatTypeList.length != 0) {
                                    List<String> cabinNameList = Arrays.stream(commonTrainSeatTypeList).map(key -> TrainSeatType.getName(ObjUtils.toInteger(key, 0))).collect(Collectors.toList());
                                    allList.addAll(cabinNameList);
                                }
                                //高铁
                                if (highspeedTrainSeatTypeList.length != 0) {
                                    List<String> cabinNameList = Arrays.stream(highspeedTrainSeatTypeList).map(key -> TrainSeatType.getName(ObjUtils.toInteger(key, 0))).collect(Collectors.toList());
                                    allList.addAll(cabinNameList);
                                }
                                trainNameDescItemList.add(new MyRuleDisployContract.NameDescItem("可订席别", StringUtils.join(allList, ",")));
                            }
                        } else {
                            trainNameDescItemList.add(new MyRuleDisployContract.NameDescItem("可订席别", "无限制"));
                        }
                        //单张票价
                        if (!org.springframework.util.StringUtils.isEmpty(trainRule.getPriceLimit())) {
                            trainNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单张票价",
                                    "需低于¥" + BigDecimalTool.formatMoney(trainRule.getPriceLimit())));
                        } else {
                            trainNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单张票价", "无限制"));
                        }
                        //提前预定天数
                        trainNameDescItemList.add(new MyRuleDisployContract.NameDescItem("提前预订", this.getPreDayStr(trainRule.getPrivDayMin(), trainRule.getPrivDayMax())));
                        //车次限制
                        if (Objects.equals(trainRule.getDayLimit(), true)) {
                            trainNameDescItemList.add(new MyRuleDisployContract.NameDescItem("车次限制", "必选夕发朝至车次"));
                        } else {
                            trainNameDescItemList.add(new MyRuleDisployContract.NameDescItem("车次限制", "无限制"));
                        }
                        //抢票席别
                        if (Objects.equals(trainRule.getIsSameSeat(), true)) {
                            trainNameDescItemList.add(new MyRuleDisployContract.NameDescItem("抢票席别", "与普通预订时一致"));
                        } else {
                            String grapCommonTrainSeatType = trainRule.getGrapCommonSeatType();
                            String grapHighspeedTrainSeatType = trainRule.getGrapHighspeedSeatType();
                            String[] gcommonTrainSeatTypeList = StringUtils.isEmpty(grapCommonTrainSeatType) ? new String[0] : grapCommonTrainSeatType.split(",");
                            String[] ghighspeedTrainSeatTypeList = StringUtils.isEmpty(grapHighspeedTrainSeatType) ? new String[0] : grapHighspeedTrainSeatType.split(",");

                            if ((gcommonTrainSeatTypeList.length + ghighspeedTrainSeatTypeList.length == TrainSeatType.values().length) ||
                                    (gcommonTrainSeatTypeList.length == 0 && ghighspeedTrainSeatTypeList.length == 0)) {
                                trainNameDescItemList.add(new MyRuleDisployContract.NameDescItem("抢票席别", "无限制"));
                            } else {
                                List<String> allList = new ArrayList<>();
                                //普通
                                if (gcommonTrainSeatTypeList.length != 0) {
                                    List<String> cabinNameList = Arrays.stream(gcommonTrainSeatTypeList).map(key -> TrainSeatType.getName(ObjUtils.toInteger(key, 0))).collect(Collectors.toList());
                                    allList.addAll(cabinNameList);
                                }
                                //高铁
                                if (ghighspeedTrainSeatTypeList.length != 0) {
                                    List<String> cabinNameList = Arrays.stream(ghighspeedTrainSeatTypeList).map(key -> TrainSeatType.getName(ObjUtils.toInteger(key, 0))).collect(Collectors.toList());
                                    allList.addAll(cabinNameList);
                                }
                                trainNameDescItemList.add(new MyRuleDisployContract.NameDescItem("抢票席别", StringUtils.join(allList, ",")));
                            }
                        }
                    }
                }
            }
            myRuleTrain.setDetail_rules(trainNameDescItemList);
            return myRuleTrain;
        } else if (type == BizType.BUS.getCode()) {
            EmployeeBusRuleDto employeeBusRuleDto = iBaseEmployeeBusRuleService.queryEmployeeBusRuleByPrimaryKey(userId, companyId);
            List<MyRuleDisployContract.NameDescItem> busNameDescItemList = Lists.newArrayList();
            MyRuleDisployContract myRuleBus = new MyRuleDisployContract();
            myRuleBus.setCategory_type(135);
            myRuleBus.setCategory_name("汽车");
            myRuleBus.setAuth_flag(0);
            myRuleBus.setApprove_flag(0);
            myRuleBus.setView_flag(false);
            myRuleBus.setOrder_approve_flag(0);
            myRuleBus.setRefund_approve_flag(0);
            myRuleBus.setChange_approve_flag(0);
            if (ObjectUtil.isNotNull(employeeBusRuleDto)) {
                myRuleBus.setOrder_approve_flag(employeeBusRuleDto.getBus_order_verify_flag() ? 1 : 0);
                if (employeeBusRuleDto.getBus_rule() != null) {
                    myRuleBus.setAuth_flag(employeeBusRuleDto.getBus_rule() == 4 ? 1 : 0);
                }
                myRuleBus.setApprove_flag(employeeBusRuleDto.getBus_verify_flag() ? 1 : 0);
                Integer manualBusRuleId = employeeBusRuleDto.getBus_rule_id();
                Boolean busRuleFlag = employeeBusRuleDto.getBus_rule_flag();
                if (busRuleFlag && ObjUtils.isNotBlank(manualBusRuleId)) {
                    BusRule busRule = busRuleMapper.selectByPrimaryKey(manualBusRuleId);
                    if (busRule != null) {
                        Boolean priceLimitFlag = busRule.getPriceLimitFlag();
                        if (priceLimitFlag) {
                            busNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单张票价", "需低于¥" + BigDecimalTool.formatMoney(busRule.getPriceLimit())));
                        } else {
                            busNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单张票价", "无限制"));
                        }
                        busNameDescItemList.add(new MyRuleDisployContract.NameDescItem("提前预订", getPreDayStr(busRule.getPrevDayMin(), busRule.getPrevDayMax())));
                    }
                }
            }
            myRuleBus.setDetail_rules(busNameDescItemList);
            return myRuleBus;
        } else if (type == BizType.Dinner.getCode()) {
            EmployeeDinnerRule employeeDinnerRule = iBaseEmployeeDinnerRuleService.selectByPrimaryKey(userId, companyId);
            List<MyRuleDisployContract.NameDescItem> dinnerNameDescItemList = Lists.newArrayList();
            MyRuleDisployContract myRuleDinner = new MyRuleDisployContract();
            if (employeeDinnerRule != null) {
                if (employeeDinnerRule.getDinner_rule() != null) {
                    myRuleDinner.setAuth_flag(employeeDinnerRule.getDinner_rule() == 2 ? 1 : 0);
                    Integer exceedBuyFlag = employeeDinnerRule.getExceed_buy_flag();
                }
                String manualDinnerRuleId = employeeDinnerRule.getManual_dinner_rule_id();
                Boolean dinnerRuleFlag = employeeDinnerRule.getDinner_rule_flag();
                if (dinnerRuleFlag && StringUtils.isNotBlank(manualDinnerRuleId)) {
                    DinnerRule dinnerRule = dinnerRuleMapper.selectByPrimaryKey(manualDinnerRuleId);
                    if (dinnerRule != null) {
                        BigDecimal dinnerFrequencyLimitPrice = dinnerRule.getDinnerFrequencyLimitPrice();
                        BigDecimal dinnerEverydayLimitPrice = dinnerRule.getDinnerEverydayLimitPrice();
                        if (dinnerFrequencyLimitPrice.compareTo(BigDecimal.valueOf(-1)) == 0) {
                            dinnerNameDescItemList.add(new MyRuleDisployContract.NameDescItem("每餐限额", "无限制"));
                        } else {
                            dinnerNameDescItemList.add(new MyRuleDisployContract.NameDescItem("每餐限额", "¥" + dinnerFrequencyLimitPrice + "元"));
                        }
                        if (dinnerEverydayLimitPrice.compareTo(BigDecimal.valueOf(-1)) == 0) {
                            dinnerNameDescItemList.add(new MyRuleDisployContract.NameDescItem("每日限额", "无限制"));
                        } else {
                            dinnerNameDescItemList.add(new MyRuleDisployContract.NameDescItem("每日限额", "¥" + dinnerEverydayLimitPrice + "元"));
                        }
                        //用餐时段
                        dinnerNameDescItemList.add(new MyRuleDisployContract.NameDescItem("用餐时段", DayTypeChange(dinnerRule.getId(), 3)));

                    }
                }

            }
            myRuleDinner.setDetail_rules(dinnerNameDescItemList);
            return myRuleDinner;
        } else if (type == BizType.Mall.getCode()) {
            EmployeeMallRule employeeMallRule = iBaseEmployeeMallRuleService.queryEmployeeMallRuleByPrimaryKey(userId, companyId);
            List<MyRuleDisployContract.NameDescItem> mailNameDescItemList = Lists.newArrayList();
            MyRuleDisployContract myRuleMall = new MyRuleDisployContract();
            if (employeeMallRule != null) {
                if (employeeMallRule.getMallRule() != null) {
                    myRuleMall.setAuth_flag(employeeMallRule.getMallRule() == DinnerRuleType.Allowed.getCode() ? 1 : 0);
                }
                if (employeeMallRule.getMallVerifyFlag()) {
                    myRuleMall.setApprove_flag(1);
                }
                String manualMallRuleId = employeeMallRule.getManualMallRuleId();
                Boolean mallRuleFlag = employeeMallRule.getMallRuleFlag();
                if (mallRuleFlag && StringUtils.isNotBlank(manualMallRuleId)) {
                    MallRule mallRule = mallRuleMapper.selectByPrimaryKey(manualMallRuleId);
                    if (mallRule != null) {
                        myRuleMall.setType(mallRule.getLimitType());
                        String limitCategoryIds = mallRule.getLimitCategoryIds();
                        String[] limitCategoryIdsList = StringUtils.isEmpty(limitCategoryIds) ? new String[0] : limitCategoryIds.split(",");
                        List<SecondCategoryContract> categories = Lists.newArrayList();
                        try {
                            categories = getSecondCategoryList(null);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        List<String> typeNameList = Lists.newArrayList();
                        if (mallRule.getLimitType() == null || mallRule.getLimitType().intValue() == 3) {
                            mailNameDescItemList.add(new MyRuleDisployContract.NameDescItem("采购品类", "无限制"));
                            //品类限制
                        } else if (mallRule.getLimitType() == null || mallRule.getLimitType().intValue() == 1) {
                            for (String str : limitCategoryIdsList) {
                                for (SecondCategoryContract mallCategoryContract : categories) {
                                    if (mallCategoryContract.getCategoryCode() == ObjUtils.toLong(str, 0L).longValue()) {
                                        typeNameList.add(mallCategoryContract.getDescribe());
                                        break;
                                    }
                                }
                            }
                            mailNameDescItemList.add(new MyRuleDisployContract.NameDescItem("采购品类", StringUtils.join(typeNameList, ",")));
                        }
                        //sku限制
                        else {
                            MallRuleSkuExample mallRuleSkuExamplet = new MallRuleSkuExample();
                            mallRuleSkuExamplet.createCriteria().andRuleIdEqualTo(mallRule.getId());
                            List<MallRuleSku> mallRuleSkus = mallRuleSkuMapper.selectByExample(mallRuleSkuExamplet);
                            if (CollectionUtils.isNotEmpty(mallRuleSkus)) {
                                myRuleMall.setView_flag(true);
                                mailNameDescItemList.add(new MyRuleDisployContract.NameDescItem("采购品类", "可采购" + mallRuleSkus.size() + "项商品SKU"));
                            } else {
                                myRuleMall.setView_flag(false);
                                mailNameDescItemList.add(new MyRuleDisployContract.NameDescItem("采购品类", "无限制"));
                            }

                        }

                        Boolean limitPriceFlag = mallRule.getLimitPriceFlag();
                        if (limitPriceFlag) {
                            mailNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单笔限额", "需低于¥" + mallRule.getLimitPriceHighest().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue() + "元"));
                        } else {
                            mailNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单笔限额", "无限制"));
                        }
                    }
                }
            }
            myRuleMall.setDetail_rules(mailNameDescItemList);
            return myRuleMall;
        } else if (type == BizType.Taxi.getCode()) {
            EmployeeTaxiRule employeeTaxiRule = iBaseEmployeeTaxiRuleService.queryEmployeeTaxiRuleByPrimaryKey(userId, companyId);
            List<MyRuleDisployContract.NameDescItem> taxiNameDescItemList = Lists.newArrayList();
            MyRuleDisployContract myRuleTaxi = new MyRuleDisployContract();
            if (employeeTaxiRule != null) {
                if (employeeTaxiRule.getTaxiRule() != null) {
                    if (employeeTaxiRule.getTaxiRule() == 2 || employeeTaxiRule.getTaxiRule() == 3) {
                        myRuleTaxi.setAuth_flag(1);
                    } else {
                        myRuleTaxi.setAuth_flag(0);
                    }
                }
                myRuleTaxi.setApprove_flag(0);
                Boolean taxiRuleFlag = employeeTaxiRule.getTaxiRuleFlag();
                if (taxiRuleFlag != null && taxiRuleFlag && employeeTaxiRule != null) {
                    TaxiRule taxiRule = taxiRuleMapper.selectByPrimaryKey(employeeTaxiRule.getManualTaxiRuleId());
                    if (taxiRule != null) {
                        Boolean priceLimitFlag = taxiRule.getPriceLimitFlag();
                        if (priceLimitFlag) {
                            taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单笔限额", "需低于¥" + taxiRule.getPriceLimit().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue() + "元"));
                        } else {
                            taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单笔限额", "无限制"));
                        }
                        if (taxiRule.getDayPriceLimit().compareTo(BigDecimal.valueOf(0)) != 1) {
                            taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单日限额", "无限制"));
                        } else {
                            taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("单日限额", "需低于¥" + taxiRule.getDayPriceLimit() + "元"));
                        }
                        Boolean limitTime = taxiRule.getLimitTime();
                        if (limitTime) {
                            List<TaxiTimeRange> taxiTimeRangeList = taxiTimeRangeMapper.queryRuleRangeTime(taxiRule.getId());
                            List<Integer> dataTypes = Lists.newArrayList();
                            for (TaxiTimeRange taxiTimeRange : taxiTimeRangeList) {
                                dataTypes.add(taxiTimeRange.getDayType());
                            }

                            taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("用车时段", DayTypeChange(taxiRule.getId().toString(), 1)));
                        } else {
                            taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("用车时段", "无限制"));
                        }

                        Boolean limitDeparture = taxiRule.getLimitDeparture();
                        if (limitDeparture) {
                            String departureLocationId = taxiRule.getDepartureLocationId();
                            if (StringUtils.isNotBlank(departureLocationId)) {
                                String[] departureLocations = departureLocationId.split(",");
                                StringBuffer sb = new StringBuffer();
                                for (int i = 0; i < departureLocations.length; i++) {
                                    TaxiLocation taxiLocation = taxiLocationMapper.selectByPrimaryKey(ObjUtils.toInteger(departureLocations[i]));
                                    String commentName = taxiLocation.getCommentName();
                                    if (StringUtils.isBlank(commentName)) {
                                        commentName = "用车位置";
                                    }
                                    if (taxiLocation.getLocationType() == TaxiLocationType.Location.getCode()) {
                                        if (i == departureLocations.length - 1) {
                                            sb.append(commentName + "," + taxiLocation.getName() + "," + taxiLocation.getRadius() / 1000 + "km内");
                                        } else {
                                            sb.append(commentName + "," + taxiLocation.getName() + "," + taxiLocation.getRadius() / 1000 + "km内").append("\n").append("\n");
                                        }
                                    } else {
                                        if (i == departureLocations.length - 1) {
                                            sb.append(commentName + "," + taxiLocation.getCityName() + " 本市范围可用");
                                        } else {
                                            sb.append(commentName + "," + taxiLocation.getCityName() + " 本市范围可用").append("\n").append("\n");
                                        }
                                    }
                                }
                                taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("上车位置", sb.toString()));
                            }
                        } else {
                            taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("上车位置", "无限制"));
                        }
                        Boolean limitTaxiType = taxiRule.getLimitTaxiType();
                        String allowedTaxiType = taxiRule.getAllowedTaxiType();
                        if (limitTaxiType) {
                            String[] allowedTaxiTypeList = allowedTaxiType.split(",");
                            List<String> cabinNameList = Arrays.stream(allowedTaxiTypeList).map(key -> AllowedTaxiType.getName(ObjUtils.toInteger(key, 0))).collect(Collectors.toList());
                            taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("乘坐车型", StringUtils.join(cabinNameList, ",")));
                        } else {
                            taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("乘坐车型", "无限制"));
                        }
                        //同城限制
                        if (Objects.nonNull(taxiRule.getAllowSameCityType())) {
                            log.info("走重构后代码");
                            AllowSameCityLimitType allowSameCityType = AllowSameCityLimitType.valueOf(taxiRule.getAllowSameCityType());
                            taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem(TaxiRuleConstant.SAME_CITY_LIMIT, allowSameCityType.getDesc()));
                        } else {
                            log.info("走老代码");
                            if (taxiRule.getAllowSameCity()) {
                                taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("同城限制", "起点和终点必须在同一城市"));
                            } else {
                                taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("同城限制", "无限制"));
                            }
                        }
                        if (taxiRule.getAllowSameCity()) {
                            taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("同城限制", "起点和终点必须在同一城市"));
                        } else {
                            taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("同城限制", "无限制"));
                        }
                        //叫车限制
                        if (taxiRule.getAllowCalledForother()) {
                            taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("叫车限制", "允许为他人叫车"));
                        } else {
                            taxiNameDescItemList.add(new MyRuleDisployContract.NameDescItem("叫车限制", "禁止为他人叫车"));
                        }
                    }
                }
            }
            myRuleTaxi.setDetail_rules(taxiNameDescItemList);
            return myRuleTaxi;
        }
        return null;
    }

    private String getGroupPriceRule(String companyId, HotelRule hotelRule, MyRuleDisployContract myRuleHotel) {
        // 查看价格限制类型 1:均价限制 2:最高价限制
        CompanyRuleDTO companyRule = iCompanyRuleService.queryByCompanyId(companyId);
        Integer limitType = companyRule.getHotelRulePriceType();
        Boolean otherTierFlag = hotelRule.getOtherTierFlag();

        CompanyAreaLevelGroupExample groupExample = new CompanyAreaLevelGroupExample();
        groupExample.setOrderByClause("create_time");
        groupExample.createCriteria().andCompanyIdEqualTo(companyId);
        List<CompanyAreaLevelGroup> groupList = companyAreaLevelGroupMapper.selectByExample(groupExample);
        HotelRuleGroupDetailExample detailExample = new HotelRuleGroupDetailExample();
        detailExample.createCriteria().andCompanyIdEqualTo(companyId).andHotelRuleIdEqualTo(hotelRule.getId());
        List<HotelRuleGroupDetail> detailList = hotelRuleGroupDetailMapper.selectByExample(detailExample);

        // 整合全量分组和已配置分组限价
        List<HotelGroupPriceVO> groupPriceList = Lists.newArrayList();
        boolean hasLimit = false;
        for (CompanyAreaLevelGroup group : groupList) {
            boolean isMatched = false;
            for (HotelRuleGroupDetail detail : detailList) {
                if (group.getId().equals(detail.getCompanyAreaLevelGroupId())) {
                    isMatched = true;
                    HotelGroupPriceVO groupDetail = new HotelGroupPriceVO();
                    groupDetail.setGroupTitle(group.getGroupTitle());
                    BigDecimal price = detail.getGroupTierPrice();
                    groupDetail.setPrice(price);
                    if (price != null && price.compareTo(BigDecimal.ZERO) > 0) {
                        groupDetail.setFlag(true);
                        hasLimit = true;
                    } else {
                        groupDetail.setFlag(false);
                    }
                    groupPriceList.add(groupDetail);
                    break;
                }
            }
            if (!isMatched) {
                HotelGroupPriceVO groupDetail = new HotelGroupPriceVO();
                groupDetail.setGroupTitle(group.getGroupTitle());
                groupDetail.setFlag(false);
                groupPriceList.add(groupDetail);
            }
        }

        if (!hasLimit && !otherTierFlag) {
            myRuleHotel.setView_flag(false);
            return "无限制";
        }

        StringBuilder builder = new StringBuilder();
        boolean isHighestLimit = HotelPriceLimitType.HIGHESTLIMIT.getCode() == limitType;
        groupPriceList.forEach(g -> {
            if (g.getFlag()) {
                if (isHighestLimit) {
                    builder.append(g.getGroupTitle()).append("需低于¥").append(g.getPrice()).append("\n");
                } else {
                    builder.append(g.getGroupTitle()).append("平均每晚房费需低于").append(g.getPrice()).append("元\n");
                }
            } else {
                builder.append(g.getGroupTitle()).append("无限制\n");
            }
        });

        if (otherTierFlag) {
            if (isHighestLimit) {
                builder.append("其他城市需低于¥").append(hotelRule.getOtherTierPrice()).append("\n");
            } else {
                builder.append("其他城市平均每晚房费需低于").append(hotelRule.getOtherTierPrice()).append("元\n");
            }
        } else {
            builder.append("其他城市无限制").append("\n");
        }

        return builder.toString();
    }

    /**
     * 清洗用车类型数据
     */
    @Override
    public void updateTaxiTypeData() {
        TaxiRuleExample taxiRuleExample = new TaxiRuleExample();
        taxiRuleExample.createCriteria().andIdNotEqualTo(-1);
        List<TaxiRule> taxiRuleList = taxiRuleMapper.selectByExample(taxiRuleExample);
        if (CollectionUtils.isNotEmpty(taxiRuleList)) {
            LOGGER.info("[清洗用车类型数据]条数：{}", taxiRuleList.size());
            for (TaxiRule taxiRule : taxiRuleList) {
                LOGGER.info("清洗用车类型数据,清洗前数据：{}", JsonUtils.toJson(taxiRule));
                String allowedTaxtType = taxiRule.getAllowedTaxiType();
                //默认限制一个车型
                if (StringUtils.isEmpty(allowedTaxtType)) {
                    taxiRule.setLimitTaxiType(true);
                    taxiRule.setAllowedTaxiType("600");
                } else {
                    String[] allowedTaxtTypeStrs = allowedTaxtType.split(",");
                    List<String> allowedTypes = new ArrayList<>();
                    if (allowedTaxtTypeStrs.length > 0) {
                        for (int i = 0; i < allowedTaxtTypeStrs.length; i++) {
                            if (TaxiModelsType.containsKey(Integer.valueOf(allowedTaxtTypeStrs[i]))) {
                                allowedTypes.add(allowedTaxtTypeStrs[i]);
                            }
                        }
                        //处理最终数据
                        if (TaxiModelsType.values().length == allowedTypes.size()) {
                            taxiRule.setLimitTaxiType(false);
                            taxiRule.setAllowedTaxiType(StringUtils.join(allowedTypes, ","));
                        } else {
                            if (allowedTypes.size() == 0) {
                                allowedTypes.add("600");
                            }
                            taxiRule.setLimitTaxiType(true);
                            taxiRule.setAllowedTaxiType(StringUtils.join(allowedTypes, ","));
                        }
                    }
                }
                LOGGER.info("清洗用车类型数据,清洗后数据：{}", JsonUtils.toJson(taxiRule));
                TaxiRuleExample taxiRuleExample1 = new TaxiRuleExample();
                taxiRuleExample1.createCriteria().andIdEqualTo(taxiRule.getId());
                int count = taxiRuleMapper.updateByExampleSelective(taxiRule, taxiRuleExample1);
                LOGGER.info("清洗用车类型数据,清洗数据结束，受影响行数：{}", count);
            }
        }
    }

    /**
     * 查询用户的规则权限
     *
     * @param userId
     * @return
     */
    @Override
    public List<UserBizAuthWithVerify> queryUserRuleAuth(String companyId, String userId) {
        List<UserBizAuthWithVerify> userBizAuthWithVerifyList = Lists.newArrayList();
        //国内机票
        EmployeeAirRule employeeAirRule = iBaseEmployeeAirRuleService.queryEmployeeAirRuleByPrimaryKey(userId, companyId);
        if (employeeAirRule != null) {
            UserBizAuthWithVerify airInfo = new UserBizAuthWithVerify();
            airInfo.setBiz(BizType.AirPlane.getCode());
            UserAuthWithVerify airAuthWithVerify = new UserAuthWithVerify();
            airAuthWithVerify.setHas_auth(employeeAirRule.getAir_rule() == 4);
            airAuthWithVerify.setNeed_verify(employeeAirRule.getAir_verify_flag());
            airInfo.setAuth(airAuthWithVerify);
            userBizAuthWithVerifyList.add(airInfo);
        }
        //国际机票
        EmployeeIntlAirRule intlAirRule = iBaseEmployeeIntlAirRuleService.queryEmployeeIntlAirRuleByPrimaryKey(userId, companyId);
        if (intlAirRule != null) {
            UserBizAuthWithVerify intlAirInfo = new UserBizAuthWithVerify();
            intlAirInfo.setBiz(BizType.InternationalAirPlane.getCode());
            UserAuthWithVerify intlAirAuthWithVerify = new UserAuthWithVerify();
            intlAirAuthWithVerify.setHas_auth(intlAirRule.getAir_rule() == 4);
            intlAirAuthWithVerify.setNeed_verify(intlAirRule.getAir_verify_flag());
            intlAirInfo.setAuth(intlAirAuthWithVerify);
            userBizAuthWithVerifyList.add(intlAirInfo);
        }
        //酒店
        EmployeeHotelRule hotelRule = iBaseEmployeeHotelRuleService.queryEmployeeHotelRuleByPrimaryKey(userId, companyId);
        if (hotelRule != null) {
            UserBizAuthWithVerify hotelInfo = new UserBizAuthWithVerify();
            hotelInfo.setBiz(BizType.Hotel.getCode());
            UserAuthWithVerify hotelAuthWithVerify = new UserAuthWithVerify();
            hotelAuthWithVerify.setHas_auth(hotelRule.getHotel_rule() == 2);
            hotelAuthWithVerify.setNeed_verify(hotelRule.getHotel_verify_flag());
            hotelInfo.setAuth(hotelAuthWithVerify);
            userBizAuthWithVerifyList.add(hotelInfo);
        }
        //火车
        EmployeeTrainRule trainRule = iBaseEmployeeTrainRuleService.queryEmployeeTrainRuleByPrimaryKey(userId, companyId);
        if (trainRule != null) {
            UserBizAuthWithVerify trainInfo = new UserBizAuthWithVerify();
            trainInfo.setBiz(BizType.Train.getCode());
            UserAuthWithVerify trainAuthWithVerify = new UserAuthWithVerify();
            trainAuthWithVerify.setHas_auth(trainRule.getTrain_rule() == 4);
            trainAuthWithVerify.setNeed_verify(trainRule.getTrain_verify_flag());
            trainInfo.setAuth(trainAuthWithVerify);
            userBizAuthWithVerifyList.add(trainInfo);
        }
        //采购
        EmployeeMallRule mallRule = iBaseEmployeeMallRuleService.queryEmployeeMallRuleByPrimaryKey(userId, companyId);
        if (mallRule != null) {
            UserBizAuthWithVerify mallInfo = new UserBizAuthWithVerify();
            mallInfo.setBiz(BizType.Mall.getCode());
            UserAuthWithVerify mallAuthWithVerify = new UserAuthWithVerify();
            mallAuthWithVerify.setHas_auth(mallRule.getMallRule() == 2);
            mallAuthWithVerify.setNeed_verify(mallRule.getMallVerifyFlag());
            mallInfo.setAuth(mallAuthWithVerify);
            userBizAuthWithVerifyList.add(mallInfo);
        }
        //用车
        EmployeeTaxiRule carRule = iBaseEmployeeTaxiRuleService.queryEmployeeTaxiRuleByPrimaryKey(userId, companyId);
        if (carRule != null) {
            UserBizAuthWithVerify carInfo = new UserBizAuthWithVerify();
            carInfo.setBiz(BizType.Taxi.getCode());
            UserAuthWithVerify carAuthWithVerify = new UserAuthWithVerify();
            carAuthWithVerify.setHas_auth(carRule.getTaxiRule() == 2);
            carAuthWithVerify.setHas_auth(carRule.getTaxiRule() == 2 || carRule.getTaxiRule() == 3);
            carAuthWithVerify.setNeed_verify(false);
            carInfo.setAuth(carAuthWithVerify);
            userBizAuthWithVerifyList.add(carInfo);
        }
        //用餐
        EmployeeMeishiRuleDto employeeMeishiRuleDto = iBaseEmployeeMeishiRuleExtService.queryEmployeeRule(companyId, userId);
        if (employeeMeishiRuleDto != null) {
            UserBizAuthWithVerify dinnerInfo = new UserBizAuthWithVerify();
            dinnerInfo.setBiz(BizType.Dinner.getCode());
            UserAuthWithVerify dinnerAuthWithVerify = new UserAuthWithVerify();
            dinnerAuthWithVerify.setHas_auth(employeeMeishiRuleDto.getMeishiRule() == 2);
            dinnerAuthWithVerify.setNeed_verify(false);
            dinnerInfo.setAuth(dinnerAuthWithVerify);
            userBizAuthWithVerifyList.add(dinnerInfo);
        }
        //外卖
        TakeawayAuthContract takeawayRule = iBaseTakeawayRuleExtService.myTakeawayRule(userId, companyId);
        if (takeawayRule != null) {
            UserBizAuthWithVerify takeawayInfo = new UserBizAuthWithVerify();
            takeawayInfo.setBiz(BizType.Takeaway.getCode());
            UserAuthWithVerify takeawayAuthWithVerify = new UserAuthWithVerify();
            takeawayAuthWithVerify.setHas_auth(takeawayRule.getTakeaway_rule() == TakeawayRuleType.Allowed.getCode());
            takeawayAuthWithVerify.setNeed_verify(false);
            takeawayInfo.setAuth(takeawayAuthWithVerify);
            userBizAuthWithVerifyList.add(takeawayInfo);
        }
        return userBizAuthWithVerifyList;
    }

    /**
     * 用车限额个人支付提示
     *
     * @param companyId
     * @param userId
     * @param applyId
     * @return
     */
    @Override
    public TaxiLimitPayTipContract priceLimitPayTips(String companyId, String userId, String applyId) {
        // 查询是否个人垫付模式
        Map<String, Boolean> funcMap = iPrivilegeService.queryFunctionMoudle(CodeTypeEnums.CustomAuth.getKey(), companyId, userId, CustomCodeType.M_ADVANCE_PAYMENT.getKey());
        Boolean advancePayment = funcMap.get(CustomCodeType.M_ADVANCE_PAYMENT.getKey());
        return priceLimitPayTips(companyId, userId, applyId, advancePayment, 1);
    }

    /**
     * 用车限额个人支付提示
     *
     * @param companyId
     * @param userId
     * @param applyId
     * @param advancePayment
     * @return
     */
    @Override
    public TaxiLimitPayTipContract priceLimitPayTips(String companyId, String userId, String applyId, Boolean advancePayment, Integer sceneCode) {
        TaxiLimitPayTipContract taxiLimitPayTipContract = new TaxiLimitPayTipContract();
        taxiLimitPayTipContract.setTip_flag(false);
        EmployeeTaxiRuleInfo employeeTaxiRuleInfoContract = iTaxiRuleService.getEmployeeTaxiRule(userId, companyId, applyId, null, sceneCode);
        ApplyOrder applyOrder = applyOrderMapper.selectByPrimaryKey(applyId);
        TaxiOrderCheckRule taxiOrderCheckRule = new TaxiOrderCheckRule();
        if(ObjectUtil.isNotNull(applyOrder)){
            taxiOrderCheckRule.setApplyId(applyId);
            taxiOrderCheckRule.setFormId(applyOrder.getFormId());
            taxiOrderCheckRule.setApplyType(applyOrder.getType());
        }
        return this.priceLimitPayTips(employeeTaxiRuleInfoContract, advancePayment, taxiOrderCheckRule);
    }

    @Override
    public TaxiLimitPayTipContract priceLimitPayTips(EmployeeTaxiRuleInfo employeeTaxiRuleInfoContract,
                                                     Boolean advancePayment, TaxiOrderCheckRule taxiOrderCheckRule) {
        log.info("个人支付提示，规则：{}", JsonUtils.toJson(employeeTaxiRuleInfoContract));
        StringBuilder msg = new StringBuilder();
        Boolean tipFlag = Boolean.FALSE;
        String title = "超额个人支付提示";
        BigDecimal priceLimit = employeeTaxiRuleInfoContract.getPriceLimit();
        BigDecimal dayPriceLimit = employeeTaxiRuleInfoContract.getDayPriceLimit();
        BigDecimal applyPriceLimit = employeeTaxiRuleInfoContract.getApplyPriceLimit();
        TaxiLimitPayTipContract taxiLimitPayTipContract = new TaxiLimitPayTipContract();
        taxiLimitPayTipContract.setTip_flag(false);
        // 无规则限制
        if (employeeTaxiRuleInfoContract == null || !employeeTaxiRuleInfoContract.getTaxiRuleFlag()) {
            return taxiLimitPayTipContract;
        }
        // 未开启个人支付
        if (!employeeTaxiRuleInfoContract.getPersonalPay()) {
            return taxiLimitPayTipContract;
        }
        // 用车费用不限制
        Integer priceLimitType = employeeTaxiRuleInfoContract.getPriceLimitType();
        TaxiApprovePriceLimitType limitType = TaxiApprovePriceLimitType.valueOf(priceLimitType);
        if (TaxiApprovePriceLimitType.UNKNOWN == limitType) {
            return taxiLimitPayTipContract;
        }
        //查询总预估费用配置
        CustomFormTotalEstimatedOptionDTO estimatedOptionDTO = null;
        if(StringUtils.isNotBlank(taxiOrderCheckRule.getApplyId()) && StringUtils.isNotBlank(taxiOrderCheckRule.getFormId())){
            estimatedOptionDTO = iCustomFormService.queryTotalEstimatedOptionSnapshot(taxiOrderCheckRule.getApplyId(),
                    taxiOrderCheckRule.getFormId());
        }
        if(ObjectUtil.isNotNull(estimatedOptionDTO) && ObjectUtil.isNotNull(estimatedOptionDTO.getCheckTotalEstimatedFlag())
                && estimatedOptionDTO.getCheckTotalEstimatedFlag() == 1
                && ApplyType.CustomFromBeforehand.getValue() == taxiOrderCheckRule.getApplyType()){
            if(estimatedOptionDTO.getTotalEstimatedLimitType() == 3){
                estimatedOptionDTO.setTotalEstimatedLimitAmount(estimatedOptionDTO.getApplyBudgeInYuan());
            }
            tipFlag = Boolean.TRUE;
            BigDecimal applyLeft = BigDecimalTool.getNotNegative(applyPriceLimit.subtract(employeeTaxiRuleInfoContract.getApplyPriceUsed()));
            msg.append(String.format("您的申请单总额度上限为%s元，申请单可用额度为%s元，",
                    dayPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString(),
                    applyLeft.setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
        }else {

            if (employeeTaxiRuleInfoContract.getPriceLimitFlag() && priceLimit != null && priceLimit.compareTo(BigDecimal.ZERO) >= 0) {
                tipFlag = Boolean.TRUE;
                msg.append(String.format("您的单次用车上限为%s元，", priceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
            }

            if (TaxiApprovePriceLimitType.LIMIT_ALL == limitType){
                if (DayPriceLimitTypeEnum.AVR_DAY_LIMIT.getType().equals(employeeTaxiRuleInfoContract.getDayPriceLimitType())) {
                    // 日均限额
                    tipFlag = Boolean.TRUE;
                    Date endTime = DateUtils.parse(employeeTaxiRuleInfoContract.getEndTime(), DateUtils.FORMAT_DATE_WITH_BAR);
                    Date startTime = DateUtils.parse(employeeTaxiRuleInfoContract.getStartTime(), DateUtils.FORMAT_DATE_WITH_BAR);
                    long days = DateUtil.between(startTime, endTime, DateUnit.DAY) + 1;
                    //日均限额的总额度
                    applyPriceLimit = dayPriceLimit.multiply(BigDecimal.valueOf(days));
                    BigDecimal applyLeft = BigDecimalTool.getNotNegative(applyPriceLimit.subtract(employeeTaxiRuleInfoContract.getApplyPriceUsed()));
                    msg.append(String.format("您的单日日均用车总额度上限为%s元，申请单可用额度为%s元，",
                            dayPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString(),
                            applyLeft.setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
                } else if (dayPriceLimit.compareTo(BigDecimal.ZERO) >= 0) {
                    tipFlag = Boolean.TRUE;
                    msg.append(String.format("您的单日用车上限为%s元，", dayPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
                }
            } else if(TaxiApprovePriceLimitType.EMPLOYEE_FILL == limitType) {
                if (applyPriceLimit != null && applyPriceLimit.compareTo(BigDecimal.ZERO) > 0) {
                    tipFlag = Boolean.TRUE;
                    msg.append(String.format("您的申请用车总额上限为%s元，", applyPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
                }
            } else if (TaxiApprovePriceLimitType.LIMIT_CITY == limitType
                    || TaxiApprovePriceLimitType.LIMIT_CITY_GROUP == limitType) {
                if (applyPriceLimit != null && applyPriceLimit.compareTo(BigDecimal.ZERO) > 0) {
                    tipFlag = Boolean.TRUE;
                    msg.append(String.format("您的总额上限为%s元，", priceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
                }
            }
        }
        if(tipFlag) {
            if (advancePayment) {
                msg.append("超出部分需您个人自费无法报销，是否继续提交？");
            } else {
                msg.append("超出部分将需要您进行个人支付。");
            }
            TaxiLimitPayTipContract.Tip tip = new TaxiLimitPayTipContract.Tip();
            tip.setTitle(title);
            tip.setContent(msg.toString());
            taxiLimitPayTipContract.setTip(tip);
            taxiLimitPayTipContract.setTip_flag(tipFlag);
        }

        return taxiLimitPayTipContract;
    }

    @Override
    public PriceLimitPayTips budgetPersonPayTips(EmployeeTaxiRuleInfo employeeTaxiRuleInfo,
                                               String budgetPersonPayLimitPriceMsg, Boolean advancePayment) {
        StringBuffer items = new StringBuffer();
        int index = 1;
        items.append(index++).append(".").append(budgetPersonPayLimitPriceMsg).append("；");
        BigDecimal priceLimit = employeeTaxiRuleInfo.getPriceLimit();
        BigDecimal dayPriceLimit = employeeTaxiRuleInfo.getDayPriceLimit();
        BigDecimal applyPriceLimit = employeeTaxiRuleInfo.getApplyPriceLimit();

        if (employeeTaxiRuleInfo.getPriceLimitType() == 2 && applyPriceLimit != null && applyPriceLimit.compareTo(BigDecimal.ZERO) > 0) {
            items.append("\n").append(index++).append(".申请用车总额上限").append(applyPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()).append("元；");
        } else {
            if (employeeTaxiRuleInfo.getPriceLimitFlag() && priceLimit != null && priceLimit.compareTo(BigDecimal.ZERO) >= 0) {
                items.append("\n").append(index++).append(".单次用车上限").append(priceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()).append("元；");
            }
            if (DayPriceLimitTypeEnum.AVR_DAY_LIMIT.getType().equals(employeeTaxiRuleInfo.getDayPriceLimitType())) {
                // 日均限额
                Date endTime = DateUtils.parse(employeeTaxiRuleInfo.getEndTime(), DateUtils.FORMAT_DATE_WITH_BAR);
                Date startTime = DateUtils.parse(employeeTaxiRuleInfo.getStartTime(), DateUtils.FORMAT_DATE_WITH_BAR);
                long days = DateUtil.between(startTime, endTime, DateUnit.DAY) + 1;
                //日均限额的总额度
                applyPriceLimit = employeeTaxiRuleInfo.getDayPriceLimit().multiply(BigDecimal.valueOf(days));
                BigDecimal applyLeft = BigDecimalTool.getNotNegative(applyPriceLimit.subtract(employeeTaxiRuleInfo.getApplyPriceUsed()));
                items.append("\n")
                        .append(index++).append(".单日日均用车上限")
                        .append(dayPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()).append("元，")
                        .append("申请单可用额度")
                        .append(applyLeft.setScale(2, BigDecimal.ROUND_HALF_UP).toString()).append("元；");
            } else if(DayPriceLimitTypeEnum.PER_DAY_LIMIT.getType().equals(employeeTaxiRuleInfo.getDayPriceLimitType())) {
                items.append("\n").append(index++).append(".单日用车上限").append(dayPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()).append("元；");
            } else {
                if (dayPriceLimit != null && dayPriceLimit.compareTo(BigDecimal.ZERO) > 0) {
                    items.append("\n").append(index++).append(".单日用车上限").append(dayPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()).append("元；");
                }
            }
        }
        TemporaryResponseCode tip;
        String title;
        if (advancePayment) {
            tip = TemporaryResponseCode.OrderTaxiBudgetAndRulePriceLimitTip2;
            title = "超额部分需个人自费";
        } else {
            tip = TemporaryResponseCode.OrderTaxiBudgetAndRulePriceLimitTip;
            title = "超额部分需个人支付";
        }

        return PriceLimitPayTips.builder().code(tip.getCode()).title(title)
                .msg(StrUtils.formatString(tip.getMsg(), items)).build();
    }

    /**
     * 获取地区上级ID
     *
     * @param areaId 地区ID
     * @return
     */
    @Override
    public String getAreaParentId(String areaId) {
        AreaExample areaExample = new AreaExample();
        areaExample.createCriteria().andIdEqualTo(areaId);
        Area area = areaMapper.selectByPrimaryKey(areaId);
        if (ObjectUtil.isNotNull(area)) {
            return area.getParentId();
        }
        return null;
    }

    private void appendLimitPathInfo(String companyId, Integer id, TaxiRuleListContract taxiRuleListContract) {
        log.info("用车校验,appendLimitPathInfo start");
        long start = System.currentTimeMillis();
        TaxiPathLocationExample taxiPathLocationExample = new TaxiPathLocationExample();
        taxiPathLocationExample.createCriteria().andCompanyIdEqualTo(companyId).andTaxiRuleIdEqualTo(id);
        List<TaxiPathLocation> taxiPathLocationList = taxiPathLocationMapper.selectByExample(taxiPathLocationExample);
        List<TaxiRulePath> pathLocationInfos = Lists.newArrayList();
        List<TaxiRuleListContract.Path> pathList = Lists.newArrayList();
        List<TaxiRuleListContract.Departure> departureStartList = Lists.newArrayList();
        List<TaxiRuleListContract.Departure> departureArrivalList = Lists.newArrayList();
        Set<Integer> locationIdSet = Sets.newHashSet();
        for (TaxiPathLocation taxiPathLocation : taxiPathLocationList) {
            if (taxiPathLocation.getLimitDeparture() && taxiPathLocation.getLimitDepartureType() == PathLocationTypeEnum.NORMAL.getCode()) {
                locationIdSet.add(taxiPathLocation.getDepartureLocationId());
            }
            if (taxiPathLocation.getLimitArrival() && taxiPathLocation.getLimitArrivalType() == PathLocationTypeEnum.NORMAL.getCode()) {
                locationIdSet.add(taxiPathLocation.getArrivalLocationId());
            }
        }
        locationIdSet = locationIdSet.stream().filter(x -> x != null && x > 0).collect(Collectors.toSet());
        Map<String, TaxiLocation> locationMap = getLocationMap(locationIdSet);
        for (TaxiPathLocation taxiPathLocation : taxiPathLocationList) {
            // 用车上车位置限制
            TaxiRuleListContract.Departure departure = new TaxiRuleListContract.Departure();
            departure.setId(-1);
            departure.setName("任意位置");
            departure.setCompanyId(companyId);
            departure.setComment_name("");
            if (taxiPathLocation.getLimitDeparture()) {
                Integer limitDepartureType = taxiPathLocation.getLimitDepartureType();
                if (limitDepartureType == PathLocationTypeEnum.NORMAL.getCode()) {
                    departure.setPath_type(PathLocationTypeEnum.NORMAL.getCode());
                    Integer departureId = taxiPathLocation.getDepartureLocationId();
                    TaxiLocation taxiLocation = locationMap.get(departureId + "");
                    //taxiLocationMapper.selectByPrimaryKey(departureId);
                    if (taxiLocation == null) {
                        continue;
                    }
                    departure.setId(taxiLocation.getId());
                    departure.setName(taxiLocation.getName());
                    departure.setCompanyId(taxiLocation.getCompanyId());
                    departure.setLat(taxiLocation.getLat());
                    departure.setLng(taxiLocation.getLng());
                    departure.setRadius(taxiLocation.getRadius());
                    departure.setLocation_type(taxiLocation.getLocationType());
                    departure.setCity_code(taxiLocation.getCityCode());
                    departure.setCity_name(taxiLocation.getCityName());
                    if (StringUtils.isBlank(taxiLocation.getCommentName())) {
                        departure.setComment_name("用车位置");
                    } else {
                        departure.setComment_name(taxiLocation.getCommentName());
                    }
                }
                if (limitDepartureType == PathLocationTypeEnum.FIXED.getCode()) {
                    departure.setPath_type(PathLocationTypeEnum.FIXED.getCode());
                    if (taxiPathLocation.getDepartureLocationId() == FixedPathLocationEnum.AIR_PORT.getCode()) {
                        departure.setId(FixedPathLocationEnum.AIR_PORT.getCode());
                        departure.setComment_name(FixedPathLocationEnum.AIR_PORT.getValue());
                        departure.setName(FixedPathLocationEnum.AIR_PORT.getDesc());
                    }
                    if (taxiPathLocation.getDepartureLocationId() == FixedPathLocationEnum.TRAIN_STATION.getCode()) {
                        departure.setId(FixedPathLocationEnum.TRAIN_STATION.getCode());
                        departure.setComment_name(FixedPathLocationEnum.TRAIN_STATION.getValue());
                        departure.setName(FixedPathLocationEnum.TRAIN_STATION.getDesc());
                    }
                    if (taxiPathLocation.getDepartureLocationId() == FixedPathLocationEnum.HOME_ADDRESS.getCode()) {
                        departure.setId(FixedPathLocationEnum.HOME_ADDRESS.getCode());
                        departure.setComment_name(FixedPathLocationEnum.HOME_ADDRESS.getValue());
                        departure.setName(FixedPathLocationEnum.HOME_ADDRESS.getDesc());
                    }
                    if (taxiPathLocation.getDepartureLocationId() == FixedPathLocationEnum.WORK_LOCATION.getCode()) {
                        departure.setId(FixedPathLocationEnum.WORK_LOCATION.getCode());
                        departure.setComment_name(FixedPathLocationEnum.WORK_LOCATION.getValue());
                        departure.setName(FixedPathLocationEnum.WORK_LOCATION.getDesc());
                    }
                }
            }
            // 用车到达位置限制
            TaxiRuleListContract.Departure arrival = new TaxiRuleListContract.Departure();
            arrival.setId(-1);
            arrival.setName("任意位置");
            arrival.setCompanyId(companyId);
            arrival.setComment_name("");
            if (taxiPathLocation.getLimitArrival()) {
                Integer limitArrivalType = taxiPathLocation.getLimitArrivalType();
                if (limitArrivalType == PathLocationTypeEnum.NORMAL.getCode()) {
                    arrival.setPath_type(PathLocationTypeEnum.NORMAL.getCode());
                    Integer arrivalId = taxiPathLocation.getArrivalLocationId();
                    TaxiLocation taxiLocation = locationMap.get(arrivalId + "");
                    //taxiLocationMapper.selectByPrimaryKey(arrivalId);
                    if (taxiLocation == null) {
                        continue;
                    }
                    arrival.setId(taxiLocation.getId());
                    arrival.setName(taxiLocation.getName());
                    arrival.setCompanyId(taxiLocation.getCompanyId());
                    arrival.setLat(taxiLocation.getLat());
                    arrival.setLng(taxiLocation.getLng());
                    arrival.setRadius(taxiLocation.getRadius());
                    arrival.setLocation_type(taxiLocation.getLocationType());
                    arrival.setCity_code(taxiLocation.getCityCode());
                    arrival.setCity_name(taxiLocation.getCityName());
                    if (StringUtils.isBlank(taxiLocation.getCommentName())) {
                        arrival.setComment_name("用车位置");
                    } else {
                        arrival.setComment_name(taxiLocation.getCommentName());
                    }
                }
                if (limitArrivalType == PathLocationTypeEnum.FIXED.getCode()) {
                    arrival.setPath_type(PathLocationTypeEnum.FIXED.getCode());
                    if (taxiPathLocation.getArrivalLocationId() == FixedPathLocationEnum.AIR_PORT.getCode()) {
                        arrival.setId(FixedPathLocationEnum.AIR_PORT.getCode());
                        arrival.setComment_name(FixedPathLocationEnum.AIR_PORT.getValue());
                        arrival.setName(FixedPathLocationEnum.AIR_PORT.getDesc());
                    }
                    if (taxiPathLocation.getArrivalLocationId() == FixedPathLocationEnum.TRAIN_STATION.getCode()) {
                        arrival.setId(FixedPathLocationEnum.TRAIN_STATION.getCode());
                        arrival.setComment_name(FixedPathLocationEnum.TRAIN_STATION.getValue());
                        arrival.setName(FixedPathLocationEnum.TRAIN_STATION.getDesc());
                    }
                    if (taxiPathLocation.getArrivalLocationId() == FixedPathLocationEnum.HOME_ADDRESS.getCode()) {
                        arrival.setId(FixedPathLocationEnum.HOME_ADDRESS.getCode());
                        arrival.setComment_name(FixedPathLocationEnum.HOME_ADDRESS.getValue());
                        arrival.setName(FixedPathLocationEnum.HOME_ADDRESS.getDesc());
                    }
                    if (taxiPathLocation.getArrivalLocationId() == FixedPathLocationEnum.WORK_LOCATION.getCode()) {
                        arrival.setId(FixedPathLocationEnum.WORK_LOCATION.getCode());
                        arrival.setComment_name(FixedPathLocationEnum.WORK_LOCATION.getValue());
                        arrival.setName(FixedPathLocationEnum.WORK_LOCATION.getDesc());
                    }
                }
            }
            departureStartList.add(departure);
            departureArrivalList.add(arrival);
            TaxiRuleListContract.Path path = new TaxiRuleListContract.Path();
            path.setDeparture(departure);
            path.setArrival(arrival);
            pathList.add(path);
            TaxiRulePath taxiRulePath = TaxiRulePath.fromModel(taxiPathLocation);
            pathLocationInfos.add(taxiRulePath);
        }
        taxiRuleListContract.setPath(pathList);
        taxiRuleListContract.setPathLocationInfos(pathLocationInfos);
        // 兼容老版本字段
        taxiRuleListContract.setLimitDeparture(true);
        taxiRuleListContract.setDeparture(departureStartList);
        taxiRuleListContract.setArrival(departureArrivalList);
        log.info("用车校验,appendLimitPathInfo end cost:{}ms", System.currentTimeMillis() - start);
    }

    @Override
    /**
     * 获取优惠券排序规则
     * 用车场景：
     * 优惠券类型：优先选公司券。
     * 面额：多张公司券，优先选面额大的。
     * 即将到期的：多张公司券，面额相同，优先选即将到期的。
     * 酒店场景：
     * 面额：先选择面额大的
     * 即将到期：然后同一面额里，选择即将到期的
     * 优惠券类型：同一面额，同到期时间里，优先选公司券。
     * 机票场景：
     * 面额：先选择面额大的
     * 即将到期：然后同一面额里，选择即将到期的
     * 优惠券类型：同一面额，同到期时间里，优先选公司券。
     */
    public CompanyCouponInfo getCouponSort(String companyId,Integer bizType){
        //查询优惠券配置 0.未开启 1.开启
        Integer couponExceedPriceSetting = iOrderCheckService.queryCouponExceedPriceSetting(companyId);
        log.info("[获取优惠卷是否开启]：{}", JsonUtils.toJson(couponExceedPriceSetting));
        CompanyCouponInfo companyCouponInfo = new CompanyCouponInfo();
        companyCouponInfo.setCouponExceedPriceSetting(couponExceedPriceSetting);
        List<CouponSort> list = new ArrayList<>();
        //用车的排序规则
        if(BizType.Taxi.getCode() == bizType){
            CouponSort couponSort = new CouponSort();
            couponSort.setSerialNumber(0);
            couponSort.setType(0);
            couponSort.setDesc(1);
            list.add(couponSort);
            couponSort = new CouponSort();
            couponSort.setSerialNumber(1);
            couponSort.setType(1);
            couponSort.setDesc(0);
            list.add(couponSort);
            couponSort = new CouponSort();
            couponSort.setSerialNumber(2);
            couponSort.setType(2);
            couponSort.setDesc(1);
            list.add(couponSort);
        }
        else{
            CouponSort couponSort = new CouponSort();
            couponSort.setSerialNumber(0);
            couponSort.setType(1);
            couponSort.setDesc(0);
            list.add(couponSort);
            couponSort = new CouponSort();
            couponSort.setSerialNumber(1);
            couponSort.setType(2);
            couponSort.setDesc(1);
            list.add(couponSort);
            couponSort = new CouponSort();
            couponSort.setSerialNumber(2);
            couponSort.setType(0);
            couponSort.setDesc(1);
            list.add(couponSort);
        }
        companyCouponInfo.setCouponSortList(list);
        return companyCouponInfo;
    }


    @Override
    public Map<String, TaxiLocation> getLocationMap(Collection<Integer> locationIdSet) {
        Map<String, TaxiLocation> map = Maps.newHashMap();
        if (ObjUtils.isEmpty(locationIdSet)) {
            return map;
        }
        List<List<Integer>> lists = Lists.partition(Lists.newArrayList(locationIdSet), 200);
        for (List<Integer> list : lists) {
            List<TaxiLocation> res = taxiLocationMapper.listByPrimaryKey(list);
            if (res != null) {
                for (TaxiLocation location : res) {
                    if (location == null) {
                        continue;
                    }
                    map.put(location.getId() + "", location);
                }
            }
        }
        return map;
    }




}
