package com.fenbeitong.saas.core.contract.cost;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.expense.management.api.costAttribution.dto.CostAttributionInfoReqDTO;
import com.fenbeitong.finhub.common.constant.CostInfoTypeEnum;
import com.fenbeitong.finhub.common.constant.OrderChannelEnum;
import com.fenbeitong.finhub.common.saas.entity.TicketCostInfo;
import com.fenbeitong.saas.core.contract.order.apply.OrderApplyCommonV2Contract;
import com.fenbeitong.saas.core.contract.order.check.AirOrderCheckReqV2Contract;
import com.fenbeitong.saas.core.contract.order.check.CostInfoContract;
import com.fenbeitong.saas.core.contract.order.check.CostInfoTicketContract;
import com.fenbeitong.saas.core.contract.order.check.TravelOnBusiOrderCheckReqV2Contract;
import com.fenbeitong.saas.core.model.enums.message.BizType;
import com.fenbeitong.saas.core.utils.log.LogUtil;
import com.fenbeitong.saasplus.api.model.dto.util.CostInfoUtil;
import com.fenbeitong.saasplus.api.model.enums.cost.AttributionCallerEnum;
import com.google.common.collect.Lists;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/31
 */
@Slf4j
@Getter
@Setter
public class CostCheckVO {

    /**
     * 企业id
     */
    private String companyId;

    /**
     * 分摊类型 默认订单分摊
     * {@link CostInfoTypeEnum}
     */
    private Integer costInfoType = CostInfoTypeEnum.ORDER.getKey();

    /**
     * 订单费用归属（V2版本
     */
    private CostInfoContract costInfo;

    /**
     * 订单费用归属（V3版本
     */
    private String costInfoString;

    /**
     * 票分摊费用归属信息（V2版本
     */
    private List<CostInfoTicketContract> costInfoTicketList;

    /**
     * 票分摊费用归属信息（v3版本
     */
    private String costInfoTicketString;

    /**
     * 人员信息
     */
    private List<String> userIdList;

    /**
     * 下单场景
     * {@link com.fenbeitong.saas.core.model.enums.apply.BizType}
     */
    private Integer category;

    /**
     * 订单渠道 默认APP端
     * {@link OrderChannelEnum}
     */
    private Integer channelKey = OrderChannelEnum.App.getKey();

    /**
     * 是否强制使用v2版本校验
     * <p>
     * 机票场景存在客户端传参指定使用校验的版本，前端不传时默认false，走查询到的开关配置逻辑
     * <p/>
     */
    private Boolean isCostV2Check = Boolean.FALSE;

    /**
     * 审批单id
     */
    private String applyId;

    /**
     * 是否三方审批  默认否
     */
    private Boolean isThird = Boolean.FALSE;


    /**
     * 是否校验三方审批
     */
    private Boolean isCheckThird = false;

    /**
     * 用车/用餐/外卖 子场景码值
     */
    private Integer sceneCode;

    public static CostCheckVO from(AirOrderCheckReqV2Contract source) {
        CostCheckVO target = new CostCheckVO();
        target.setCostInfoType(source.getCost_info_type());
        target.setCostInfo(source.getCost_info());
        target.setCostInfoString(source.getCostInfoString());
        target.setCostInfoTicketList(source.getCost_info_ticket_list());
        target.setCostInfoTicketString(source.getCostInfoTicketListString());
        target.setUserIdList(source.getUser_id_list());
        target.setCategory(source.getCategory());
        target.setCompanyId(source.getTravel_on_busi_common_req_contract().getCompany_id());
        target.setChannelKey(source.getTravel_on_busi_common_req_contract().getChannel());
        target.setApplyId(source.getTravel_on_busi_common_req_contract().getApply_id());
        return target;
    }

    public static CostCheckVO from(TravelOnBusiOrderCheckReqV2Contract source) {
        CostCheckVO target = new CostCheckVO();
        target.setCostInfoType(source.getCost_info_type());
        target.setCostInfo(source.getCost_info());
        target.setCostInfoString(source.getCostInfoString());
        target.setCostInfoTicketList(source.getCost_info_ticket_list());
        target.setCostInfoTicketString(source.getCostInfoTicketListString());
        target.setUserIdList(source.getUser_id_list());
        target.setCategory(source.getCategory());
        target.setCompanyId(source.getTravel_on_busi_common_req_contract().getCompany_id());
        if (ObjUtils.isNotEmpty(source.getTravel_on_busi_common_req_contract().getChannel())) {
            target.setChannelKey(source.getTravel_on_busi_common_req_contract().getChannel());
        }
        if (ObjUtils.isNotEmpty(source.getIs_cost_v2())) {
            target.setIsCostV2Check(source.getIs_cost_v2());
        }
        target.setApplyId(source.getTravel_on_busi_common_req_contract().getApply_id());
        return target;
    }

    public static CostCheckVO from(OrderApplyCommonV2Contract source) {
        CostCheckVO target = new CostCheckVO();
        target.setCostInfoType(source.getCost_info_type());
        target.setCostInfo(source.getCost_info());
        target.setCostInfoString(source.getCostInfoString());
        target.setCostInfoTicketList(source.getCost_info_ticket_list());
        target.setCostInfoTicketString(source.getCostInfoTicketListString());
        target.setUserIdList(source.getUser_id_list());
        target.setCategory(source.getCategory()== BizType.IntlHotel.getCode() ? BizType.Hotel.getCode(): source.getCategory());
        target.setCompanyId(source.getCompany_id());
        return target;
    }

    public List<CostAttributionInfoReqDTO> toV2CostInfo() {
        List<CostAttributionInfoReqDTO> targetList = Lists.newArrayList();
        CostAttributionInfoReqDTO target = JSON.parseObject(JSON.toJSONString(getCostInfo()), CostAttributionInfoReqDTO.class);
        target.setUserIdList(getUserIdList());
        target.setCategory(getCategory());
        target.setCompanyId(getCompanyId());
        target.setCostInfoType(getCostInfoType());
        target.setRequestId(LogUtil.getRequestId());
        target.setChannelKey(getChannelKey());
        target.setIsThird(getIsThird());
        target.setSubCategoryId(ObjUtils.toString(getSceneCode()));
        targetList.add(target);
        log.info("[费用归属校验] 按订单分摊V2, 费用归属:{}", JsonUtils.toJson(targetList));
        return targetList;
    }

    public List<CostAttributionInfoReqDTO> toV2CostTicket() {
        List<CostAttributionInfoReqDTO> targetList = Lists.newArrayList();
        for (CostInfoTicketContract costInfoTicketContract : getCostInfoTicketList()) {
            CostAttributionInfoReqDTO target = JSON.parseObject(JSON.toJSONString(costInfoTicketContract), CostAttributionInfoReqDTO.class);
            target.setUserIdList(getUserIdList());
            target.setCategory(getCategory());
            target.setCompanyId(getCompanyId());
            target.setCostInfoType(getCostInfoType());
            target.setRequestId(LogUtil.getRequestId());
            target.setChannelKey(getChannelKey());
            target.setIsThird(getIsThird());
            target.setSubCategoryId(ObjUtils.toString(getSceneCode()));
            targetList.add(target);
        }
        log.info("[费用归属校验] 按票分摊V2, 费用归属:{}", JsonUtils.toJson(targetList));
        return targetList;
    }

    public List<CostAttributionInfoReqDTO> toV3CostInfo() {
        List<CostAttributionInfoReqDTO> targetList = Lists.newArrayList();
        CostAttributionInfoReqDTO target = JSON.parseObject(getCostInfoString(), CostAttributionInfoReqDTO.class);
        // 费用归属允许非必填
        if (ObjUtils.isEmpty(target)) {
            target = new CostAttributionInfoReqDTO();
        }
        target.setUserIdList(getUserIdList());
        target.setCategory(getCategory());
        target.setCompanyId(getCompanyId());
        target.setCostInfoType(getCostInfoType());
        target.setRequestId(LogUtil.getRequestId());
        target.setChannelKey(getChannelKey());
        target.setCallerType(AttributionCallerEnum.SCENARIO.getCode());
        target.setIsThird(getIsThird());
        target.setSubCategoryId(ObjUtils.toString(getSceneCode()));
        targetList.add(target);
        log.info("[费用归属校验] 按订单分摊V3, 费用归属:{}", JsonUtils.toJson(targetList));
        return targetList;
    }

    public List<CostAttributionInfoReqDTO> toV3CostTicket() {
        List<CostAttributionInfoReqDTO> targetList = Lists.newArrayList();
        List<TicketCostInfo> ticketCostInfoList = CostInfoUtil.getTicketCostInfoList(getCostInfoTicketString());
        for (TicketCostInfo ticketCostInfo : ticketCostInfoList) {
            CostAttributionInfoReqDTO target = JSON.parseObject(JSON.toJSONString(ticketCostInfo), CostAttributionInfoReqDTO.class);
            target.setUserIdList(getUserIdList());
            target.setCategory(getCategory());
            target.setCompanyId(getCompanyId());
            target.setCostInfoType(getCostInfoType());
            target.setRequestId(LogUtil.getRequestId());
            target.setChannelKey(getChannelKey());
            target.setIsThird(getIsThird());
            target.setSubCategoryId(ObjUtils.toString(getSceneCode()));
            targetList.add(target);
        }
        log.info("[费用归属校验] 按票分摊V3, 费用归属:{}", JsonUtils.toJson(targetList));
        return targetList;
    }

}
