package com.fenbeitong.saas.core.contract.message;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
/**
 * Created by xuzn on 17/9/22.
 */
public class MessageVoContrcat {

    private String id;

    @JsonIgnore
    private Integer msgType;

    @JsonIgnore
    private Integer bizType;


    private String title;


    private String comment;

    @JsonIgnore
    private String bizOrder;


    private String info;

    @JsonIgnore
    private String link;


    private String sender;


    private Integer senderType;


    private String receiver;

    @JsonIgnore
    private Boolean read;


    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")// 存储日期时使用
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")// 查询日期时使用
    private Date createTime;

    @JsonIgnore
    private Date updateTime;

    private String companyId;


    public String getId() {
        return id;
    }


    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }


    public Integer getMsgType() {
        return msgType;
    }


    public void setMsgType(Integer msgType) {
        this.msgType = msgType;
    }


    public Integer getBizType() {
        return bizType;
    }


    public void setBizType(Integer bizType) {
        this.bizType = bizType;
    }


    public String getTitle() {
        return title;
    }


    public void setTitle(String title) {
        this.title = title == null ? null : title.trim();
    }


    public String getComment() {
        return comment;
    }


    public void setComment(String comment) {
        this.comment = comment == null ? null : comment.trim();
    }


    public String getBizOrder() {
        return bizOrder;
    }


    public void setBizOrder(String bizOrder) {
        this.bizOrder = bizOrder == null ? null : bizOrder.trim();
    }


    public String getInfo() {
        return info;
    }


    public void setInfo(String info) {
        this.info = info == null ? null : info.trim();
    }


    public String getLink() {
        return link;
    }


    public void setLink(String link) {
        this.link = link == null ? null : link.trim();
    }


    public String getSender() {
        return sender;
    }


    public void setSender(String sender) {
        this.sender = sender == null ? null : sender.trim();
    }


    public Integer getSenderType() {
        return senderType;
    }


    public void setSenderType(Integer senderType) {
        this.senderType = senderType;
    }


    public String getReceiver() {
        return receiver;
    }


    public void setReceiver(String receiver) {
        this.receiver = receiver == null ? null : receiver.trim();
    }


    public Boolean getRead() {
        return read;
    }


    public void setRead(Boolean read) {
        this.read = read;
    }


    public Date getCreateTime() {
        return createTime;
    }


    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }


    public Date getUpdateTime() {
        return updateTime;
    }


    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }
}
