package com.fenbeitong.saas.core.model.fenbeitong;

import java.math.BigDecimal;
import java.util.Date;

/**
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table taxi_time_range
 *
 * @mbg.generated do_not_delete_during_merge
 */
public class TimeRange {

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column taxi_time_range.day_type
     *
     * @mbg.generated
     */
    private Integer dayType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column taxi_time_range.begin_time
     *
     * @mbg.generated
     */
    private Date beginTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column taxi_time_range.end_time
     *
     * @mbg.generated
     */
    private Date endTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column taxi_time_range.is_overnight
     *
     * @mbg.generated
     */
    private Boolean isOvernight;

    /**
     * Database Column Remarks:
     *   单次金额限制
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column takeaway_time_range.frequency_limit_price
     *
     * @mbg.generated
     */
    private BigDecimal frequencyLimitPrice;

    /**
     * Database Column Remarks:
     *   累计金额限制
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column takeaway_time_range.accumulative_limit_price
     *
     * @mbg.generated
     */
    private BigDecimal accumulativeLimitPrice;

    /**
     * Database Column Remarks:
     *   点餐次数限制
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column takeaway_time_range.order_limit_num
     *
     * @mbg.generated
     */
    private Integer orderLimitNum;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column taxi_time_range.day_type
     *
     * @return the value of taxi_time_range.day_type
     * @mbg.generated
     */
    public Integer getDayType() {
        return dayType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column taxi_time_range.day_type
     *
     * @param dayType the value for taxi_time_range.day_type
     * @mbg.generated
     */
    public void setDayType(Integer dayType) {
        this.dayType = dayType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column taxi_time_range.begin_time
     *
     * @return the value of taxi_time_range.begin_time
     * @mbg.generated
     */
    public Date getBeginTime() {
        return beginTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column taxi_time_range.begin_time
     *
     * @param beginTime the value for taxi_time_range.begin_time
     * @mbg.generated
     */
    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column taxi_time_range.end_time
     *
     * @return the value of taxi_time_range.end_time
     * @mbg.generated
     */
    public Date getEndTime() {
        return endTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column taxi_time_range.end_time
     *
     * @param endTime the value for taxi_time_range.end_time
     * @mbg.generated
     */
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column taxi_time_range.is_overnight
     *
     * @return the value of taxi_time_range.is_overnight
     * @mbg.generated
     */
    public Boolean getIsOvernight() {
        return isOvernight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column taxi_time_range.is_overnight
     *
     * @param isOvernight the value for taxi_time_range.is_overnight
     * @mbg.generated
     */
    public void setIsOvernight(Boolean isOvernight) {
        this.isOvernight = isOvernight;
    }

    public BigDecimal getFrequencyLimitPrice() {
        return frequencyLimitPrice;
    }

    public void setFrequencyLimitPrice(BigDecimal frequencyLimitPrice) {
        this.frequencyLimitPrice = frequencyLimitPrice;
    }

    public BigDecimal getAccumulativeLimitPrice() {
        return accumulativeLimitPrice;
    }

    public void setAccumulativeLimitPrice(BigDecimal accumulativeLimitPrice) {
        this.accumulativeLimitPrice = accumulativeLimitPrice;
    }

    public Integer getOrderLimitNum() {
        return orderLimitNum;
    }

    public void setOrderLimitNum(Integer orderLimitNum) {
        this.orderLimitNum = orderLimitNum;
    }

    public TimeRange() {
    }

    public TimeRange(Integer dayType, Date beginTime, Date endTime, Boolean isOvernight) {
        this.dayType = dayType;
        this.beginTime = beginTime;
        this.endTime = endTime;
        this.isOvernight = isOvernight;
    }

    public TimeRange(Integer dayType, Date beginTime, Date endTime, Boolean isOvernight, BigDecimal frequencyLimitPrice, BigDecimal accumulativeLimitPrice, Integer orderLimitNum) {
        this.dayType = dayType;
        this.beginTime = beginTime;
        this.endTime = endTime;
        this.isOvernight = isOvernight;
        this.frequencyLimitPrice = frequencyLimitPrice;
        this.accumulativeLimitPrice = accumulativeLimitPrice;
        this.orderLimitNum = orderLimitNum;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        TimeRange timeRange = (TimeRange) o;
        if (timeRange.getDayType().intValue() == this.getDayType().intValue() && timeRange.getIsOvernight().equals(this.getIsOvernight()) &&
                timeRange.getBeginTime().equals(this.getBeginTime()) && timeRange.getEndTime().equals(this.getEndTime())) {
            return true;
        }
        return false;
    }


}