package com.fenbeitong.saas.core.contract.messagesettings.inner;

import com.fenbeitong.saas.core.contract.user.EmployeeInfoContract;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by zhangkai on 2017/6/2.
 */
public class ConsumeInfo {

    private int consume_info_notice;
    private List<EmployeeInfoContract> receiver_list = new ArrayList<>();

    public ConsumeInfo() {
    }

    public ConsumeInfo(int consume_info_notice) {
        this.consume_info_notice = consume_info_notice;
    }

    public ConsumeInfo(int consume_info_notice, List<EmployeeInfoContract> receiver_list) {
        this.consume_info_notice = consume_info_notice;
        this.receiver_list = receiver_list;
    }

    public int getConsume_info_notice() {
        return consume_info_notice;
    }

    public void setConsume_info_notice(int consume_info_notice) {
        this.consume_info_notice = consume_info_notice;
    }

    public List<EmployeeInfoContract> getReceiver_list() {
        return receiver_list;
    }

    public void setReceiver_list(List<EmployeeInfoContract> receiver_list) {
        this.receiver_list = receiver_list;
    }


}
