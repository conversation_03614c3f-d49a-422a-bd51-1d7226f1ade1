package com.fenbeitong.saas.core.contract.applyflow;

import com.fenbeitong.saas.core.model.saas.CompanyApplySettingUse;

import java.util.List;

public class CompanyApplySettingUseContract {

    //审批流设置类型
    private Integer company_setting_type;

    //应用类型
    private Integer setting_type;

    //审批流设置id
    private String company_apply_setting_id;

    private List<String> item_list;

    //员工/部门/成本中心id
    private String item_id;

    public List<String> getItem_list() {
        return item_list;
    }

    public void setItem_list(List<String> item_list) {
        this.item_list = item_list;
    }

    public CompanyApplySettingUse toModel() {
        CompanyApplySettingUse companyApplySettingUse = new CompanyApplySettingUse();
        companyApplySettingUse.setItemId(getItem_id());
        companyApplySettingUse.setSettingType(getSetting_type());
        return companyApplySettingUse;
    }

    public static CompanyApplySettingUseContract fromModel(CompanyApplySettingUse companyApplySettingUse) {
        CompanyApplySettingUseContract companyApplySettingUseContract = new CompanyApplySettingUseContract();
        companyApplySettingUseContract.setItem_id(companyApplySettingUse.getItemId());
        companyApplySettingUseContract.setSetting_type(companyApplySettingUse.getSettingType());
        return companyApplySettingUseContract;
    }

    public Integer getSetting_type() {
        return setting_type;
    }

    public void setSetting_type(Integer setting_type) {
        this.setting_type = setting_type;
    }

    public String getCompany_apply_setting_id() {
        return company_apply_setting_id;
    }

    public void setCompany_apply_setting_id(String company_apply_setting_id) {
        this.company_apply_setting_id = company_apply_setting_id;
    }

    public String getItem_id() {
        return item_id;
    }

    public void setItem_id(String item_id) {
        this.item_id = item_id;
    }

    public Integer getCompany_setting_type() {
        return company_setting_type;
    }

    public void setCompany_setting_type(Integer company_setting_type) {
        this.company_setting_type = company_setting_type;
    }
}