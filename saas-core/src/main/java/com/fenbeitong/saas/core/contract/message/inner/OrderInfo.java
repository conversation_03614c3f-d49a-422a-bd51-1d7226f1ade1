package com.fenbeitong.saas.core.contract.message.inner;

import com.alibaba.fastjson.JSONObject;

/**
 * 订单通知附加信息
 */
public class OrderInfo{

    /**
     * 7 机票, 11 酒店, 15 火车
     */
    private Integer order_type;

    /**
     * 格式固定为yyyy-MM-dd HH:mm:ss
     */
    private String create_time;

    /**
     * 订单状态文本
     */
    private String order_status_msg;

    private String order_msg;

    /**
     * 顾客信息(名字)
     */
    private String customer_msg;

    /**
     * 跳转订单号
     */
    private String redirect_order_id;

    public Integer getOrder_type() {
        return order_type;
    }

    public void setOrder_type(Integer order_type) {
        this.order_type = order_type;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public String getOrder_status_msg() {
        return order_status_msg;
    }

    public void setOrder_status_msg(String order_status_msg) {
        this.order_status_msg = order_status_msg;
    }

    public String getOrder_msg() {
        return order_msg;
    }

    public void setOrder_msg(String order_msg) {
        this.order_msg = order_msg;
    }

    public String getCustomer_msg() {
        return customer_msg;
    }

    public void setCustomer_msg(String customer_msg) {
        this.customer_msg = customer_msg;
    }

    public String getRedirect_order_id() {
        return redirect_order_id;
    }

    public void setRedirect_order_id(String redirect_order_id) {
        this.redirect_order_id = redirect_order_id;
    }

    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }
}


