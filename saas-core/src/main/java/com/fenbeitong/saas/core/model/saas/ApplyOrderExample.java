package com.fenbeitong.saas.core.model.saas;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ApplyOrderExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table apply_order
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table apply_order
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table apply_order
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table apply_order
     *
     * @mbg.generated
     */
    public ApplyOrderExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table apply_order
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table apply_order
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table apply_order
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table apply_order
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table apply_order
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table apply_order
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table apply_order
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table apply_order
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table apply_order
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table apply_order
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table apply_order
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBillNoIsNull() {
            addCriterion("bill_no is null");
            return (Criteria) this;
        }

        public Criteria andBillNoIsNotNull() {
            addCriterion("bill_no is not null");
            return (Criteria) this;
        }

        public Criteria andBillNoEqualTo(String value) {
            addCriterion("bill_no =", value, "billNo");
            return (Criteria) this;
        }

        public Criteria andBillNoNotEqualTo(String value) {
            addCriterion("bill_no <>", value, "billNo");
            return (Criteria) this;
        }

        public Criteria andBillNoGreaterThan(String value) {
            addCriterion("bill_no >", value, "billNo");
            return (Criteria) this;
        }

        public Criteria andBillNoGreaterThanOrEqualTo(String value) {
            addCriterion("bill_no >=", value, "billNo");
            return (Criteria) this;
        }

        public Criteria andBillNoLessThan(String value) {
            addCriterion("bill_no <", value, "billNo");
            return (Criteria) this;
        }

        public Criteria andBillNoLessThanOrEqualTo(String value) {
            addCriterion("bill_no <=", value, "billNo");
            return (Criteria) this;
        }

        public Criteria andBillNoLike(String value) {
            addCriterion("bill_no like", value, "billNo");
            return (Criteria) this;
        }

        public Criteria andBillNoNotLike(String value) {
            addCriterion("bill_no not like", value, "billNo");
            return (Criteria) this;
        }

        public Criteria andBillNoIn(List<String> values) {
            addCriterion("bill_no in", values, "billNo");
            return (Criteria) this;
        }

        public Criteria andBillNoNotIn(List<String> values) {
            addCriterion("bill_no not in", values, "billNo");
            return (Criteria) this;
        }

        public Criteria andBillNoBetween(String value1, String value2) {
            addCriterion("bill_no between", value1, value2, "billNo");
            return (Criteria) this;
        }

        public Criteria andBillNoNotBetween(String value1, String value2) {
            addCriterion("bill_no not between", value1, value2, "billNo");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Integer value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Integer value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Integer value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Integer value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Integer value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Integer> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Integer> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Integer value1, Integer value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdIsNull() {
            addCriterion("employee_id is null");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdIsNotNull() {
            addCriterion("employee_id is not null");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdEqualTo(String value) {
            addCriterion("employee_id =", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdNotEqualTo(String value) {
            addCriterion("employee_id <>", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdGreaterThan(String value) {
            addCriterion("employee_id >", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdGreaterThanOrEqualTo(String value) {
            addCriterion("employee_id >=", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdLessThan(String value) {
            addCriterion("employee_id <", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdLessThanOrEqualTo(String value) {
            addCriterion("employee_id <=", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdLike(String value) {
            addCriterion("employee_id like", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdNotLike(String value) {
            addCriterion("employee_id not like", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdIn(List<String> values) {
            addCriterion("employee_id in", values, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdNotIn(List<String> values) {
            addCriterion("employee_id not in", values, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdBetween(String value1, String value2) {
            addCriterion("employee_id between", value1, value2, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdNotBetween(String value1, String value2) {
            addCriterion("employee_id not between", value1, value2, "employeeId");
            return (Criteria) this;
        }

        public Criteria andBudgetIsNull() {
            addCriterion("budget is null");
            return (Criteria) this;
        }

        public Criteria andBudgetIsNotNull() {
            addCriterion("budget is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetEqualTo(Integer value) {
            addCriterion("budget =", value, "budget");
            return (Criteria) this;
        }

        public Criteria andBudgetNotEqualTo(Integer value) {
            addCriterion("budget <>", value, "budget");
            return (Criteria) this;
        }

        public Criteria andBudgetGreaterThan(Integer value) {
            addCriterion("budget >", value, "budget");
            return (Criteria) this;
        }

        public Criteria andBudgetGreaterThanOrEqualTo(Integer value) {
            addCriterion("budget >=", value, "budget");
            return (Criteria) this;
        }

        public Criteria andBudgetLessThan(Integer value) {
            addCriterion("budget <", value, "budget");
            return (Criteria) this;
        }

        public Criteria andBudgetLessThanOrEqualTo(Integer value) {
            addCriterion("budget <=", value, "budget");
            return (Criteria) this;
        }

        public Criteria andBudgetIn(List<Integer> values) {
            addCriterion("budget in", values, "budget");
            return (Criteria) this;
        }

        public Criteria andBudgetNotIn(List<Integer> values) {
            addCriterion("budget not in", values, "budget");
            return (Criteria) this;
        }

        public Criteria andBudgetBetween(Integer value1, Integer value2) {
            addCriterion("budget between", value1, value2, "budget");
            return (Criteria) this;
        }

        public Criteria andBudgetNotBetween(Integer value1, Integer value2) {
            addCriterion("budget not between", value1, value2, "budget");
            return (Criteria) this;
        }

        public Criteria andStateIsNull() {
            addCriterion("state is null");
            return (Criteria) this;
        }

        public Criteria andStateIsNotNull() {
            addCriterion("state is not null");
            return (Criteria) this;
        }

        public Criteria andStateEqualTo(Integer value) {
            addCriterion("state =", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotEqualTo(Integer value) {
            addCriterion("state <>", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateGreaterThan(Integer value) {
            addCriterion("state >", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateGreaterThanOrEqualTo(Integer value) {
            addCriterion("state >=", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateLessThan(Integer value) {
            addCriterion("state <", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateLessThanOrEqualTo(Integer value) {
            addCriterion("state <=", value, "state");
            return (Criteria) this;
        }

        public Criteria andStateIn(List<Integer> values) {
            addCriterion("state in", values, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotIn(List<Integer> values) {
            addCriterion("state not in", values, "state");
            return (Criteria) this;
        }

        public Criteria andStateBetween(Integer value1, Integer value2) {
            addCriterion("state between", value1, value2, "state");
            return (Criteria) this;
        }

        public Criteria andStateNotBetween(Integer value1, Integer value2) {
            addCriterion("state not between", value1, value2, "state");
            return (Criteria) this;
        }

        public Criteria andCheckReasonIsNull() {
            addCriterion("check_reason is null");
            return (Criteria) this;
        }

        public Criteria andCheckReasonIsNotNull() {
            addCriterion("check_reason is not null");
            return (Criteria) this;
        }

        public Criteria andCheckReasonEqualTo(String value) {
            addCriterion("check_reason =", value, "checkReason");
            return (Criteria) this;
        }

        public Criteria andCheckReasonNotEqualTo(String value) {
            addCriterion("check_reason <>", value, "checkReason");
            return (Criteria) this;
        }

        public Criteria andCheckReasonGreaterThan(String value) {
            addCriterion("check_reason >", value, "checkReason");
            return (Criteria) this;
        }

        public Criteria andCheckReasonGreaterThanOrEqualTo(String value) {
            addCriterion("check_reason >=", value, "checkReason");
            return (Criteria) this;
        }

        public Criteria andCheckReasonLessThan(String value) {
            addCriterion("check_reason <", value, "checkReason");
            return (Criteria) this;
        }

        public Criteria andCheckReasonLessThanOrEqualTo(String value) {
            addCriterion("check_reason <=", value, "checkReason");
            return (Criteria) this;
        }

        public Criteria andCheckReasonLike(String value) {
            addCriterion("check_reason like", value, "checkReason");
            return (Criteria) this;
        }

        public Criteria andCheckReasonNotLike(String value) {
            addCriterion("check_reason not like", value, "checkReason");
            return (Criteria) this;
        }

        public Criteria andCheckReasonIn(List<String> values) {
            addCriterion("check_reason in", values, "checkReason");
            return (Criteria) this;
        }

        public Criteria andCheckReasonNotIn(List<String> values) {
            addCriterion("check_reason not in", values, "checkReason");
            return (Criteria) this;
        }

        public Criteria andCheckReasonBetween(String value1, String value2) {
            addCriterion("check_reason between", value1, value2, "checkReason");
            return (Criteria) this;
        }

        public Criteria andCheckReasonNotBetween(String value1, String value2) {
            addCriterion("check_reason not between", value1, value2, "checkReason");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andReimburseTimeIsNull() {
            addCriterion("reimburse_time is null");
            return (Criteria) this;
        }

        public Criteria andReimburseTimeIsNotNull() {
            addCriterion("reimburse_time is not null");
            return (Criteria) this;
        }

        public Criteria andReimburseTimeEqualTo(Date value) {
            addCriterion("reimburse_time =", value, "reimburseTime");
            return (Criteria) this;
        }

        public Criteria andReimburseTimeNotEqualTo(Date value) {
            addCriterion("reimburse_time <>", value, "reimburseTime");
            return (Criteria) this;
        }

        public Criteria andReimburseTimeGreaterThan(Date value) {
            addCriterion("reimburse_time >", value, "reimburseTime");
            return (Criteria) this;
        }

        public Criteria andReimburseTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("reimburse_time >=", value, "reimburseTime");
            return (Criteria) this;
        }

        public Criteria andReimburseTimeLessThan(Date value) {
            addCriterion("reimburse_time <", value, "reimburseTime");
            return (Criteria) this;
        }

        public Criteria andReimburseTimeLessThanOrEqualTo(Date value) {
            addCriterion("reimburse_time <=", value, "reimburseTime");
            return (Criteria) this;
        }

        public Criteria andReimburseTimeIn(List<Date> values) {
            addCriterion("reimburse_time in", values, "reimburseTime");
            return (Criteria) this;
        }

        public Criteria andReimburseTimeNotIn(List<Date> values) {
            addCriterion("reimburse_time not in", values, "reimburseTime");
            return (Criteria) this;
        }

        public Criteria andReimburseTimeBetween(Date value1, Date value2) {
            addCriterion("reimburse_time between", value1, value2, "reimburseTime");
            return (Criteria) this;
        }

        public Criteria andReimburseTimeNotBetween(Date value1, Date value2) {
            addCriterion("reimburse_time not between", value1, value2, "reimburseTime");
            return (Criteria) this;
        }

        public Criteria andReimburserIdIsNull() {
            addCriterion("reimburser_id is null");
            return (Criteria) this;
        }

        public Criteria andReimburserIdIsNotNull() {
            addCriterion("reimburser_id is not null");
            return (Criteria) this;
        }

        public Criteria andReimburserIdEqualTo(String value) {
            addCriterion("reimburser_id =", value, "reimburserId");
            return (Criteria) this;
        }

        public Criteria andReimburserIdNotEqualTo(String value) {
            addCriterion("reimburser_id <>", value, "reimburserId");
            return (Criteria) this;
        }

        public Criteria andReimburserIdGreaterThan(String value) {
            addCriterion("reimburser_id >", value, "reimburserId");
            return (Criteria) this;
        }

        public Criteria andReimburserIdGreaterThanOrEqualTo(String value) {
            addCriterion("reimburser_id >=", value, "reimburserId");
            return (Criteria) this;
        }

        public Criteria andReimburserIdLessThan(String value) {
            addCriterion("reimburser_id <", value, "reimburserId");
            return (Criteria) this;
        }

        public Criteria andReimburserIdLessThanOrEqualTo(String value) {
            addCriterion("reimburser_id <=", value, "reimburserId");
            return (Criteria) this;
        }

        public Criteria andReimburserIdLike(String value) {
            addCriterion("reimburser_id like", value, "reimburserId");
            return (Criteria) this;
        }

        public Criteria andReimburserIdNotLike(String value) {
            addCriterion("reimburser_id not like", value, "reimburserId");
            return (Criteria) this;
        }

        public Criteria andReimburserIdIn(List<String> values) {
            addCriterion("reimburser_id in", values, "reimburserId");
            return (Criteria) this;
        }

        public Criteria andReimburserIdNotIn(List<String> values) {
            addCriterion("reimburser_id not in", values, "reimburserId");
            return (Criteria) this;
        }

        public Criteria andReimburserIdBetween(String value1, String value2) {
            addCriterion("reimburser_id between", value1, value2, "reimburserId");
            return (Criteria) this;
        }

        public Criteria andReimburserIdNotBetween(String value1, String value2) {
            addCriterion("reimburser_id not between", value1, value2, "reimburserId");
            return (Criteria) this;
        }

        public Criteria andReimburserNameIsNull() {
            addCriterion("reimburser_name is null");
            return (Criteria) this;
        }

        public Criteria andReimburserNameIsNotNull() {
            addCriterion("reimburser_name is not null");
            return (Criteria) this;
        }

        public Criteria andReimburserNameEqualTo(String value) {
            addCriterion("reimburser_name =", value, "reimburserName");
            return (Criteria) this;
        }

        public Criteria andReimburserNameNotEqualTo(String value) {
            addCriterion("reimburser_name <>", value, "reimburserName");
            return (Criteria) this;
        }

        public Criteria andReimburserNameGreaterThan(String value) {
            addCriterion("reimburser_name >", value, "reimburserName");
            return (Criteria) this;
        }

        public Criteria andReimburserNameGreaterThanOrEqualTo(String value) {
            addCriterion("reimburser_name >=", value, "reimburserName");
            return (Criteria) this;
        }

        public Criteria andReimburserNameLessThan(String value) {
            addCriterion("reimburser_name <", value, "reimburserName");
            return (Criteria) this;
        }

        public Criteria andReimburserNameLessThanOrEqualTo(String value) {
            addCriterion("reimburser_name <=", value, "reimburserName");
            return (Criteria) this;
        }

        public Criteria andReimburserNameLike(String value) {
            addCriterion("reimburser_name like", value, "reimburserName");
            return (Criteria) this;
        }

        public Criteria andReimburserNameNotLike(String value) {
            addCriterion("reimburser_name not like", value, "reimburserName");
            return (Criteria) this;
        }

        public Criteria andReimburserNameIn(List<String> values) {
            addCriterion("reimburser_name in", values, "reimburserName");
            return (Criteria) this;
        }

        public Criteria andReimburserNameNotIn(List<String> values) {
            addCriterion("reimburser_name not in", values, "reimburserName");
            return (Criteria) this;
        }

        public Criteria andReimburserNameBetween(String value1, String value2) {
            addCriterion("reimburser_name between", value1, value2, "reimburserName");
            return (Criteria) this;
        }

        public Criteria andReimburserNameNotBetween(String value1, String value2) {
            addCriterion("reimburser_name not between", value1, value2, "reimburserName");
            return (Criteria) this;
        }

        public Criteria andReimburserSnapContentIsNull() {
            addCriterion("reimburser_snap_content is null");
            return (Criteria) this;
        }

        public Criteria andReimburserSnapContentIsNotNull() {
            addCriterion("reimburser_snap_content is not null");
            return (Criteria) this;
        }

        public Criteria andReimburserSnapContentEqualTo(String value) {
            addCriterion("reimburser_snap_content =", value, "reimburserSnapContent");
            return (Criteria) this;
        }

        public Criteria andReimburserSnapContentNotEqualTo(String value) {
            addCriterion("reimburser_snap_content <>", value, "reimburserSnapContent");
            return (Criteria) this;
        }

        public Criteria andReimburserSnapContentGreaterThan(String value) {
            addCriterion("reimburser_snap_content >", value, "reimburserSnapContent");
            return (Criteria) this;
        }

        public Criteria andReimburserSnapContentGreaterThanOrEqualTo(String value) {
            addCriterion("reimburser_snap_content >=", value, "reimburserSnapContent");
            return (Criteria) this;
        }

        public Criteria andReimburserSnapContentLessThan(String value) {
            addCriterion("reimburser_snap_content <", value, "reimburserSnapContent");
            return (Criteria) this;
        }

        public Criteria andReimburserSnapContentLessThanOrEqualTo(String value) {
            addCriterion("reimburser_snap_content <=", value, "reimburserSnapContent");
            return (Criteria) this;
        }

        public Criteria andReimburserSnapContentLike(String value) {
            addCriterion("reimburser_snap_content like", value, "reimburserSnapContent");
            return (Criteria) this;
        }

        public Criteria andReimburserSnapContentNotLike(String value) {
            addCriterion("reimburser_snap_content not like", value, "reimburserSnapContent");
            return (Criteria) this;
        }

        public Criteria andReimburserSnapContentIn(List<String> values) {
            addCriterion("reimburser_snap_content in", values, "reimburserSnapContent");
            return (Criteria) this;
        }

        public Criteria andReimburserSnapContentNotIn(List<String> values) {
            addCriterion("reimburser_snap_content not in", values, "reimburserSnapContent");
            return (Criteria) this;
        }

        public Criteria andReimburserSnapContentBetween(String value1, String value2) {
            addCriterion("reimburser_snap_content between", value1, value2, "reimburserSnapContent");
            return (Criteria) this;
        }

        public Criteria andReimburserSnapContentNotBetween(String value1, String value2) {
            addCriterion("reimburser_snap_content not between", value1, value2, "reimburserSnapContent");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(String value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(String value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(String value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(String value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(String value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(String value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLike(String value) {
            addCriterion("company_id like", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotLike(String value) {
            addCriterion("company_id not like", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<String> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<String> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(String value1, String value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(String value1, String value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andMailOrderIdIsNull() {
            addCriterion("mail_order_id is null");
            return (Criteria) this;
        }

        public Criteria andMailOrderIdIsNotNull() {
            addCriterion("mail_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andMailOrderIdEqualTo(String value) {
            addCriterion("mail_order_id =", value, "mailOrderId");
            return (Criteria) this;
        }

        public Criteria andMailOrderIdNotEqualTo(String value) {
            addCriterion("mail_order_id <>", value, "mailOrderId");
            return (Criteria) this;
        }

        public Criteria andMailOrderIdGreaterThan(String value) {
            addCriterion("mail_order_id >", value, "mailOrderId");
            return (Criteria) this;
        }

        public Criteria andMailOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("mail_order_id >=", value, "mailOrderId");
            return (Criteria) this;
        }

        public Criteria andMailOrderIdLessThan(String value) {
            addCriterion("mail_order_id <", value, "mailOrderId");
            return (Criteria) this;
        }

        public Criteria andMailOrderIdLessThanOrEqualTo(String value) {
            addCriterion("mail_order_id <=", value, "mailOrderId");
            return (Criteria) this;
        }

        public Criteria andMailOrderIdLike(String value) {
            addCriterion("mail_order_id like", value, "mailOrderId");
            return (Criteria) this;
        }

        public Criteria andMailOrderIdNotLike(String value) {
            addCriterion("mail_order_id not like", value, "mailOrderId");
            return (Criteria) this;
        }

        public Criteria andMailOrderIdIn(List<String> values) {
            addCriterion("mail_order_id in", values, "mailOrderId");
            return (Criteria) this;
        }

        public Criteria andMailOrderIdNotIn(List<String> values) {
            addCriterion("mail_order_id not in", values, "mailOrderId");
            return (Criteria) this;
        }

        public Criteria andMailOrderIdBetween(String value1, String value2) {
            addCriterion("mail_order_id between", value1, value2, "mailOrderId");
            return (Criteria) this;
        }

        public Criteria andMailOrderIdNotBetween(String value1, String value2) {
            addCriterion("mail_order_id not between", value1, value2, "mailOrderId");
            return (Criteria) this;
        }

        public Criteria andTravelPriceDetailIsNull() {
            addCriterion("travel_price_detail is null");
            return (Criteria) this;
        }

        public Criteria andTravelPriceDetailIsNotNull() {
            addCriterion("travel_price_detail is not null");
            return (Criteria) this;
        }

        public Criteria andTravelPriceDetailEqualTo(String value) {
            addCriterion("travel_price_detail =", value, "travelPriceDetail");
            return (Criteria) this;
        }

        public Criteria andTravelPriceDetailNotEqualTo(String value) {
            addCriterion("travel_price_detail <>", value, "travelPriceDetail");
            return (Criteria) this;
        }

        public Criteria andTravelPriceDetailGreaterThan(String value) {
            addCriterion("travel_price_detail >", value, "travelPriceDetail");
            return (Criteria) this;
        }

        public Criteria andTravelPriceDetailGreaterThanOrEqualTo(String value) {
            addCriterion("travel_price_detail >=", value, "travelPriceDetail");
            return (Criteria) this;
        }

        public Criteria andTravelPriceDetailLessThan(String value) {
            addCriterion("travel_price_detail <", value, "travelPriceDetail");
            return (Criteria) this;
        }

        public Criteria andTravelPriceDetailLessThanOrEqualTo(String value) {
            addCriterion("travel_price_detail <=", value, "travelPriceDetail");
            return (Criteria) this;
        }

        public Criteria andTravelPriceDetailLike(String value) {
            addCriterion("travel_price_detail like", value, "travelPriceDetail");
            return (Criteria) this;
        }

        public Criteria andTravelPriceDetailNotLike(String value) {
            addCriterion("travel_price_detail not like", value, "travelPriceDetail");
            return (Criteria) this;
        }

        public Criteria andTravelPriceDetailIn(List<String> values) {
            addCriterion("travel_price_detail in", values, "travelPriceDetail");
            return (Criteria) this;
        }

        public Criteria andTravelPriceDetailNotIn(List<String> values) {
            addCriterion("travel_price_detail not in", values, "travelPriceDetail");
            return (Criteria) this;
        }

        public Criteria andTravelPriceDetailBetween(String value1, String value2) {
            addCriterion("travel_price_detail between", value1, value2, "travelPriceDetail");
            return (Criteria) this;
        }

        public Criteria andTravelPriceDetailNotBetween(String value1, String value2) {
            addCriterion("travel_price_detail not between", value1, value2, "travelPriceDetail");
            return (Criteria) this;
        }

        public Criteria andApplyReasonIsNull() {
            addCriterion("apply_reason is null");
            return (Criteria) this;
        }

        public Criteria andApplyReasonIsNotNull() {
            addCriterion("apply_reason is not null");
            return (Criteria) this;
        }

        public Criteria andApplyReasonEqualTo(String value) {
            addCriterion("apply_reason =", value, "applyReason");
            return (Criteria) this;
        }

        public Criteria andApplyReasonNotEqualTo(String value) {
            addCriterion("apply_reason <>", value, "applyReason");
            return (Criteria) this;
        }

        public Criteria andApplyReasonGreaterThan(String value) {
            addCriterion("apply_reason >", value, "applyReason");
            return (Criteria) this;
        }

        public Criteria andApplyReasonGreaterThanOrEqualTo(String value) {
            addCriterion("apply_reason >=", value, "applyReason");
            return (Criteria) this;
        }

        public Criteria andApplyReasonLessThan(String value) {
            addCriterion("apply_reason <", value, "applyReason");
            return (Criteria) this;
        }

        public Criteria andApplyReasonLessThanOrEqualTo(String value) {
            addCriterion("apply_reason <=", value, "applyReason");
            return (Criteria) this;
        }

        public Criteria andApplyReasonLike(String value) {
            addCriterion("apply_reason like", value, "applyReason");
            return (Criteria) this;
        }

        public Criteria andApplyReasonNotLike(String value) {
            addCriterion("apply_reason not like", value, "applyReason");
            return (Criteria) this;
        }

        public Criteria andApplyReasonIn(List<String> values) {
            addCriterion("apply_reason in", values, "applyReason");
            return (Criteria) this;
        }

        public Criteria andApplyReasonNotIn(List<String> values) {
            addCriterion("apply_reason not in", values, "applyReason");
            return (Criteria) this;
        }

        public Criteria andApplyReasonBetween(String value1, String value2) {
            addCriterion("apply_reason between", value1, value2, "applyReason");
            return (Criteria) this;
        }

        public Criteria andApplyReasonNotBetween(String value1, String value2) {
            addCriterion("apply_reason not between", value1, value2, "applyReason");
            return (Criteria) this;
        }

        public Criteria andApplyReasonDescIsNull() {
            addCriterion("apply_reason_desc is null");
            return (Criteria) this;
        }

        public Criteria andApplyReasonDescIsNotNull() {
            addCriterion("apply_reason_desc is not null");
            return (Criteria) this;
        }

        public Criteria andApplyReasonDescEqualTo(String value) {
            addCriterion("apply_reason_desc =", value, "applyReasonDesc");
            return (Criteria) this;
        }

        public Criteria andApplyReasonDescNotEqualTo(String value) {
            addCriterion("apply_reason_desc <>", value, "applyReasonDesc");
            return (Criteria) this;
        }

        public Criteria andApplyReasonDescGreaterThan(String value) {
            addCriterion("apply_reason_desc >", value, "applyReasonDesc");
            return (Criteria) this;
        }

        public Criteria andApplyReasonDescGreaterThanOrEqualTo(String value) {
            addCriterion("apply_reason_desc >=", value, "applyReasonDesc");
            return (Criteria) this;
        }

        public Criteria andApplyReasonDescLessThan(String value) {
            addCriterion("apply_reason_desc <", value, "applyReasonDesc");
            return (Criteria) this;
        }

        public Criteria andApplyReasonDescLessThanOrEqualTo(String value) {
            addCriterion("apply_reason_desc <=", value, "applyReasonDesc");
            return (Criteria) this;
        }

        public Criteria andApplyReasonDescLike(String value) {
            addCriterion("apply_reason_desc like", value, "applyReasonDesc");
            return (Criteria) this;
        }

        public Criteria andApplyReasonDescNotLike(String value) {
            addCriterion("apply_reason_desc not like", value, "applyReasonDesc");
            return (Criteria) this;
        }

        public Criteria andApplyReasonDescIn(List<String> values) {
            addCriterion("apply_reason_desc in", values, "applyReasonDesc");
            return (Criteria) this;
        }

        public Criteria andApplyReasonDescNotIn(List<String> values) {
            addCriterion("apply_reason_desc not in", values, "applyReasonDesc");
            return (Criteria) this;
        }

        public Criteria andApplyReasonDescBetween(String value1, String value2) {
            addCriterion("apply_reason_desc between", value1, value2, "applyReasonDesc");
            return (Criteria) this;
        }

        public Criteria andApplyReasonDescNotBetween(String value1, String value2) {
            addCriterion("apply_reason_desc not between", value1, value2, "applyReasonDesc");
            return (Criteria) this;
        }

        public Criteria andCityRangeIsNull() {
            addCriterion("city_range is null");
            return (Criteria) this;
        }

        public Criteria andCityRangeIsNotNull() {
            addCriterion("city_range is not null");
            return (Criteria) this;
        }

        public Criteria andCityRangeEqualTo(String value) {
            addCriterion("city_range =", value, "cityRange");
            return (Criteria) this;
        }

        public Criteria andCityRangeNotEqualTo(String value) {
            addCriterion("city_range <>", value, "cityRange");
            return (Criteria) this;
        }

        public Criteria andCityRangeGreaterThan(String value) {
            addCriterion("city_range >", value, "cityRange");
            return (Criteria) this;
        }

        public Criteria andCityRangeGreaterThanOrEqualTo(String value) {
            addCriterion("city_range >=", value, "cityRange");
            return (Criteria) this;
        }

        public Criteria andCityRangeLessThan(String value) {
            addCriterion("city_range <", value, "cityRange");
            return (Criteria) this;
        }

        public Criteria andCityRangeLessThanOrEqualTo(String value) {
            addCriterion("city_range <=", value, "cityRange");
            return (Criteria) this;
        }

        public Criteria andCityRangeLike(String value) {
            addCriterion("city_range like", value, "cityRange");
            return (Criteria) this;
        }

        public Criteria andCityRangeNotLike(String value) {
            addCriterion("city_range not like", value, "cityRange");
            return (Criteria) this;
        }

        public Criteria andCityRangeIn(List<String> values) {
            addCriterion("city_range in", values, "cityRange");
            return (Criteria) this;
        }

        public Criteria andCityRangeNotIn(List<String> values) {
            addCriterion("city_range not in", values, "cityRange");
            return (Criteria) this;
        }

        public Criteria andCityRangeBetween(String value1, String value2) {
            addCriterion("city_range between", value1, value2, "cityRange");
            return (Criteria) this;
        }

        public Criteria andCityRangeNotBetween(String value1, String value2) {
            addCriterion("city_range not between", value1, value2, "cityRange");
            return (Criteria) this;
        }

        public Criteria andTimeRangeIsNull() {
            addCriterion("time_range is null");
            return (Criteria) this;
        }

        public Criteria andTimeRangeIsNotNull() {
            addCriterion("time_range is not null");
            return (Criteria) this;
        }

        public Criteria andTimeRangeEqualTo(String value) {
            addCriterion("time_range =", value, "timeRange");
            return (Criteria) this;
        }

        public Criteria andTimeRangeNotEqualTo(String value) {
            addCriterion("time_range <>", value, "timeRange");
            return (Criteria) this;
        }

        public Criteria andTimeRangeGreaterThan(String value) {
            addCriterion("time_range >", value, "timeRange");
            return (Criteria) this;
        }

        public Criteria andTimeRangeGreaterThanOrEqualTo(String value) {
            addCriterion("time_range >=", value, "timeRange");
            return (Criteria) this;
        }

        public Criteria andTimeRangeLessThan(String value) {
            addCriterion("time_range <", value, "timeRange");
            return (Criteria) this;
        }

        public Criteria andTimeRangeLessThanOrEqualTo(String value) {
            addCriterion("time_range <=", value, "timeRange");
            return (Criteria) this;
        }

        public Criteria andTimeRangeLike(String value) {
            addCriterion("time_range like", value, "timeRange");
            return (Criteria) this;
        }

        public Criteria andTimeRangeNotLike(String value) {
            addCriterion("time_range not like", value, "timeRange");
            return (Criteria) this;
        }

        public Criteria andTimeRangeIn(List<String> values) {
            addCriterion("time_range in", values, "timeRange");
            return (Criteria) this;
        }

        public Criteria andTimeRangeNotIn(List<String> values) {
            addCriterion("time_range not in", values, "timeRange");
            return (Criteria) this;
        }

        public Criteria andTimeRangeBetween(String value1, String value2) {
            addCriterion("time_range between", value1, value2, "timeRange");
            return (Criteria) this;
        }

        public Criteria andTimeRangeNotBetween(String value1, String value2) {
            addCriterion("time_range not between", value1, value2, "timeRange");
            return (Criteria) this;
        }

        public Criteria andCurrentLogIdIsNull() {
            addCriterion("current_log_id is null");
            return (Criteria) this;
        }

        public Criteria andCurrentLogIdIsNotNull() {
            addCriterion("current_log_id is not null");
            return (Criteria) this;
        }

        public Criteria andCurrentLogIdEqualTo(Long value) {
            addCriterion("current_log_id =", value, "currentLogId");
            return (Criteria) this;
        }

        public Criteria andCurrentLogIdNotEqualTo(Long value) {
            addCriterion("current_log_id <>", value, "currentLogId");
            return (Criteria) this;
        }

        public Criteria andCurrentLogIdGreaterThan(Long value) {
            addCriterion("current_log_id >", value, "currentLogId");
            return (Criteria) this;
        }

        public Criteria andCurrentLogIdGreaterThanOrEqualTo(Long value) {
            addCriterion("current_log_id >=", value, "currentLogId");
            return (Criteria) this;
        }

        public Criteria andCurrentLogIdLessThan(Long value) {
            addCriterion("current_log_id <", value, "currentLogId");
            return (Criteria) this;
        }

        public Criteria andCurrentLogIdLessThanOrEqualTo(Long value) {
            addCriterion("current_log_id <=", value, "currentLogId");
            return (Criteria) this;
        }

        public Criteria andCurrentLogIdIn(List<Long> values) {
            addCriterion("current_log_id in", values, "currentLogId");
            return (Criteria) this;
        }

        public Criteria andCurrentLogIdNotIn(List<Long> values) {
            addCriterion("current_log_id not in", values, "currentLogId");
            return (Criteria) this;
        }

        public Criteria andCurrentLogIdBetween(Long value1, Long value2) {
            addCriterion("current_log_id between", value1, value2, "currentLogId");
            return (Criteria) this;
        }

        public Criteria andCurrentLogIdNotBetween(Long value1, Long value2) {
            addCriterion("current_log_id not between", value1, value2, "currentLogId");
            return (Criteria) this;
        }

        public Criteria andApproverIdIsNull() {
            addCriterion("approver_id is null");
            return (Criteria) this;
        }

        public Criteria andApproverIdIsNotNull() {
            addCriterion("approver_id is not null");
            return (Criteria) this;
        }

        public Criteria andApproverIdEqualTo(String value) {
            addCriterion("approver_id =", value, "approverId");
            return (Criteria) this;
        }

        public Criteria andApproverIdNotEqualTo(String value) {
            addCriterion("approver_id <>", value, "approverId");
            return (Criteria) this;
        }

        public Criteria andApproverIdGreaterThan(String value) {
            addCriterion("approver_id >", value, "approverId");
            return (Criteria) this;
        }

        public Criteria andApproverIdGreaterThanOrEqualTo(String value) {
            addCriterion("approver_id >=", value, "approverId");
            return (Criteria) this;
        }

        public Criteria andApproverIdLessThan(String value) {
            addCriterion("approver_id <", value, "approverId");
            return (Criteria) this;
        }

        public Criteria andApproverIdLessThanOrEqualTo(String value) {
            addCriterion("approver_id <=", value, "approverId");
            return (Criteria) this;
        }

        public Criteria andApproverIdLike(String value) {
            addCriterion("approver_id like", value, "approverId");
            return (Criteria) this;
        }

        public Criteria andApproverIdNotLike(String value) {
            addCriterion("approver_id not like", value, "approverId");
            return (Criteria) this;
        }

        public Criteria andApproverIdIn(List<String> values) {
            addCriterion("approver_id in", values, "approverId");
            return (Criteria) this;
        }

        public Criteria andApproverIdNotIn(List<String> values) {
            addCriterion("approver_id not in", values, "approverId");
            return (Criteria) this;
        }

        public Criteria andApproverIdBetween(String value1, String value2) {
            addCriterion("approver_id between", value1, value2, "approverId");
            return (Criteria) this;
        }

        public Criteria andApproverIdNotBetween(String value1, String value2) {
            addCriterion("approver_id not between", value1, value2, "approverId");
            return (Criteria) this;
        }

        public Criteria andFlowTypeIsNull() {
            addCriterion("flow_type is null");
            return (Criteria) this;
        }

        public Criteria andFlowTypeIsNotNull() {
            addCriterion("flow_type is not null");
            return (Criteria) this;
        }

        public Criteria andFlowTypeEqualTo(Integer value) {
            addCriterion("flow_type =", value, "flowType");
            return (Criteria) this;
        }

        public Criteria andFlowTypeNotEqualTo(Integer value) {
            addCriterion("flow_type <>", value, "flowType");
            return (Criteria) this;
        }

        public Criteria andFlowTypeGreaterThan(Integer value) {
            addCriterion("flow_type >", value, "flowType");
            return (Criteria) this;
        }

        public Criteria andFlowTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("flow_type >=", value, "flowType");
            return (Criteria) this;
        }

        public Criteria andFlowTypeLessThan(Integer value) {
            addCriterion("flow_type <", value, "flowType");
            return (Criteria) this;
        }

        public Criteria andFlowTypeLessThanOrEqualTo(Integer value) {
            addCriterion("flow_type <=", value, "flowType");
            return (Criteria) this;
        }

        public Criteria andFlowTypeIn(List<Integer> values) {
            addCriterion("flow_type in", values, "flowType");
            return (Criteria) this;
        }

        public Criteria andFlowTypeNotIn(List<Integer> values) {
            addCriterion("flow_type not in", values, "flowType");
            return (Criteria) this;
        }

        public Criteria andFlowTypeBetween(Integer value1, Integer value2) {
            addCriterion("flow_type between", value1, value2, "flowType");
            return (Criteria) this;
        }

        public Criteria andFlowTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("flow_type not between", value1, value2, "flowType");
            return (Criteria) this;
        }

        public Criteria andFlowCcTypeIsNull() {
            addCriterion("flow_cc_type is null");
            return (Criteria) this;
        }

        public Criteria andFlowCcTypeIsNotNull() {
            addCriterion("flow_cc_type is not null");
            return (Criteria) this;
        }

        public Criteria andFlowCcTypeEqualTo(Integer value) {
            addCriterion("flow_cc_type =", value, "flowCcType");
            return (Criteria) this;
        }

        public Criteria andFlowCcTypeNotEqualTo(Integer value) {
            addCriterion("flow_cc_type <>", value, "flowCcType");
            return (Criteria) this;
        }

        public Criteria andFlowCcTypeGreaterThan(Integer value) {
            addCriterion("flow_cc_type >", value, "flowCcType");
            return (Criteria) this;
        }

        public Criteria andFlowCcTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("flow_cc_type >=", value, "flowCcType");
            return (Criteria) this;
        }

        public Criteria andFlowCcTypeLessThan(Integer value) {
            addCriterion("flow_cc_type <", value, "flowCcType");
            return (Criteria) this;
        }

        public Criteria andFlowCcTypeLessThanOrEqualTo(Integer value) {
            addCriterion("flow_cc_type <=", value, "flowCcType");
            return (Criteria) this;
        }

        public Criteria andFlowCcTypeIn(List<Integer> values) {
            addCriterion("flow_cc_type in", values, "flowCcType");
            return (Criteria) this;
        }

        public Criteria andFlowCcTypeNotIn(List<Integer> values) {
            addCriterion("flow_cc_type not in", values, "flowCcType");
            return (Criteria) this;
        }

        public Criteria andFlowCcTypeBetween(Integer value1, Integer value2) {
            addCriterion("flow_cc_type between", value1, value2, "flowCcType");
            return (Criteria) this;
        }

        public Criteria andFlowCcTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("flow_cc_type not between", value1, value2, "flowCcType");
            return (Criteria) this;
        }

        public Criteria andPastStatusIsNull() {
            addCriterion("past_status is null");
            return (Criteria) this;
        }

        public Criteria andPastStatusIsNotNull() {
            addCriterion("past_status is not null");
            return (Criteria) this;
        }

        public Criteria andPastStatusEqualTo(Boolean value) {
            addCriterion("past_status =", value, "pastStatus");
            return (Criteria) this;
        }

        public Criteria andPastStatusNotEqualTo(Boolean value) {
            addCriterion("past_status <>", value, "pastStatus");
            return (Criteria) this;
        }

        public Criteria andPastStatusGreaterThan(Boolean value) {
            addCriterion("past_status >", value, "pastStatus");
            return (Criteria) this;
        }

        public Criteria andPastStatusGreaterThanOrEqualTo(Boolean value) {
            addCriterion("past_status >=", value, "pastStatus");
            return (Criteria) this;
        }

        public Criteria andPastStatusLessThan(Boolean value) {
            addCriterion("past_status <", value, "pastStatus");
            return (Criteria) this;
        }

        public Criteria andPastStatusLessThanOrEqualTo(Boolean value) {
            addCriterion("past_status <=", value, "pastStatus");
            return (Criteria) this;
        }

        public Criteria andPastStatusIn(List<Boolean> values) {
            addCriterion("past_status in", values, "pastStatus");
            return (Criteria) this;
        }

        public Criteria andPastStatusNotIn(List<Boolean> values) {
            addCriterion("past_status not in", values, "pastStatus");
            return (Criteria) this;
        }

        public Criteria andPastStatusBetween(Boolean value1, Boolean value2) {
            addCriterion("past_status between", value1, value2, "pastStatus");
            return (Criteria) this;
        }

        public Criteria andPastStatusNotBetween(Boolean value1, Boolean value2) {
            addCriterion("past_status not between", value1, value2, "pastStatus");
            return (Criteria) this;
        }

        public Criteria andPastDayIsNull() {
            addCriterion("past_day is null");
            return (Criteria) this;
        }

        public Criteria andPastDayIsNotNull() {
            addCriterion("past_day is not null");
            return (Criteria) this;
        }

        public Criteria andPastDayEqualTo(Long value) {
            addCriterion("past_day =", value, "pastDay");
            return (Criteria) this;
        }

        public Criteria andPastDayNotEqualTo(Long value) {
            addCriterion("past_day <>", value, "pastDay");
            return (Criteria) this;
        }

        public Criteria andPastDayGreaterThan(Long value) {
            addCriterion("past_day >", value, "pastDay");
            return (Criteria) this;
        }

        public Criteria andPastDayGreaterThanOrEqualTo(Long value) {
            addCriterion("past_day >=", value, "pastDay");
            return (Criteria) this;
        }

        public Criteria andPastDayLessThan(Long value) {
            addCriterion("past_day <", value, "pastDay");
            return (Criteria) this;
        }

        public Criteria andPastDayLessThanOrEqualTo(Long value) {
            addCriterion("past_day <=", value, "pastDay");
            return (Criteria) this;
        }

        public Criteria andPastDayIn(List<Long> values) {
            addCriterion("past_day in", values, "pastDay");
            return (Criteria) this;
        }

        public Criteria andPastDayNotIn(List<Long> values) {
            addCriterion("past_day not in", values, "pastDay");
            return (Criteria) this;
        }

        public Criteria andPastDayBetween(Long value1, Long value2) {
            addCriterion("past_day between", value1, value2, "pastDay");
            return (Criteria) this;
        }

        public Criteria andPastDayNotBetween(Long value1, Long value2) {
            addCriterion("past_day not between", value1, value2, "pastDay");
            return (Criteria) this;
        }

        public Criteria andThirdIdIsNull() {
            addCriterion("third_id is null");
            return (Criteria) this;
        }

        public Criteria andThirdIdIsNotNull() {
            addCriterion("third_id is not null");
            return (Criteria) this;
        }

        public Criteria andThirdIdEqualTo(String value) {
            addCriterion("third_id =", value, "thirdId");
            return (Criteria) this;
        }

        public Criteria andThirdIdNotEqualTo(String value) {
            addCriterion("third_id <>", value, "thirdId");
            return (Criteria) this;
        }

        public Criteria andThirdIdGreaterThan(String value) {
            addCriterion("third_id >", value, "thirdId");
            return (Criteria) this;
        }

        public Criteria andThirdIdGreaterThanOrEqualTo(String value) {
            addCriterion("third_id >=", value, "thirdId");
            return (Criteria) this;
        }

        public Criteria andThirdIdLessThan(String value) {
            addCriterion("third_id <", value, "thirdId");
            return (Criteria) this;
        }

        public Criteria andThirdIdLessThanOrEqualTo(String value) {
            addCriterion("third_id <=", value, "thirdId");
            return (Criteria) this;
        }

        public Criteria andThirdIdLike(String value) {
            addCriterion("third_id like", value, "thirdId");
            return (Criteria) this;
        }

        public Criteria andThirdIdNotLike(String value) {
            addCriterion("third_id not like", value, "thirdId");
            return (Criteria) this;
        }

        public Criteria andThirdIdIn(List<String> values) {
            addCriterion("third_id in", values, "thirdId");
            return (Criteria) this;
        }

        public Criteria andThirdIdNotIn(List<String> values) {
            addCriterion("third_id not in", values, "thirdId");
            return (Criteria) this;
        }

        public Criteria andThirdIdBetween(String value1, String value2) {
            addCriterion("third_id between", value1, value2, "thirdId");
            return (Criteria) this;
        }

        public Criteria andThirdIdNotBetween(String value1, String value2) {
            addCriterion("third_id not between", value1, value2, "thirdId");
            return (Criteria) this;
        }

        public Criteria andThirdRemarkIsNull() {
            addCriterion("third_remark is null");
            return (Criteria) this;
        }

        public Criteria andThirdRemarkIsNotNull() {
            addCriterion("third_remark is not null");
            return (Criteria) this;
        }

        public Criteria andThirdRemarkEqualTo(String value) {
            addCriterion("third_remark =", value, "thirdRemark");
            return (Criteria) this;
        }

        public Criteria andThirdRemarkNotEqualTo(String value) {
            addCriterion("third_remark <>", value, "thirdRemark");
            return (Criteria) this;
        }

        public Criteria andThirdRemarkGreaterThan(String value) {
            addCriterion("third_remark >", value, "thirdRemark");
            return (Criteria) this;
        }

        public Criteria andThirdRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("third_remark >=", value, "thirdRemark");
            return (Criteria) this;
        }

        public Criteria andThirdRemarkLessThan(String value) {
            addCriterion("third_remark <", value, "thirdRemark");
            return (Criteria) this;
        }

        public Criteria andThirdRemarkLessThanOrEqualTo(String value) {
            addCriterion("third_remark <=", value, "thirdRemark");
            return (Criteria) this;
        }

        public Criteria andThirdRemarkLike(String value) {
            addCriterion("third_remark like", value, "thirdRemark");
            return (Criteria) this;
        }

        public Criteria andThirdRemarkNotLike(String value) {
            addCriterion("third_remark not like", value, "thirdRemark");
            return (Criteria) this;
        }

        public Criteria andThirdRemarkIn(List<String> values) {
            addCriterion("third_remark in", values, "thirdRemark");
            return (Criteria) this;
        }

        public Criteria andThirdRemarkNotIn(List<String> values) {
            addCriterion("third_remark not in", values, "thirdRemark");
            return (Criteria) this;
        }

        public Criteria andThirdRemarkBetween(String value1, String value2) {
            addCriterion("third_remark between", value1, value2, "thirdRemark");
            return (Criteria) this;
        }

        public Criteria andThirdRemarkNotBetween(String value1, String value2) {
            addCriterion("third_remark not between", value1, value2, "thirdRemark");
            return (Criteria) this;
        }

        public Criteria andExceedBuyDescIsNull() {
            addCriterion("exceed_buy_desc is null");
            return (Criteria) this;
        }

        public Criteria andExceedBuyDescIsNotNull() {
            addCriterion("exceed_buy_desc is not null");
            return (Criteria) this;
        }

        public Criteria andExceedBuyDescEqualTo(String value) {
            addCriterion("exceed_buy_desc =", value, "exceedBuyDesc");
            return (Criteria) this;
        }

        public Criteria andExceedBuyDescNotEqualTo(String value) {
            addCriterion("exceed_buy_desc <>", value, "exceedBuyDesc");
            return (Criteria) this;
        }

        public Criteria andExceedBuyDescGreaterThan(String value) {
            addCriterion("exceed_buy_desc >", value, "exceedBuyDesc");
            return (Criteria) this;
        }

        public Criteria andExceedBuyDescGreaterThanOrEqualTo(String value) {
            addCriterion("exceed_buy_desc >=", value, "exceedBuyDesc");
            return (Criteria) this;
        }

        public Criteria andExceedBuyDescLessThan(String value) {
            addCriterion("exceed_buy_desc <", value, "exceedBuyDesc");
            return (Criteria) this;
        }

        public Criteria andExceedBuyDescLessThanOrEqualTo(String value) {
            addCriterion("exceed_buy_desc <=", value, "exceedBuyDesc");
            return (Criteria) this;
        }

        public Criteria andExceedBuyDescLike(String value) {
            addCriterion("exceed_buy_desc like", value, "exceedBuyDesc");
            return (Criteria) this;
        }

        public Criteria andExceedBuyDescNotLike(String value) {
            addCriterion("exceed_buy_desc not like", value, "exceedBuyDesc");
            return (Criteria) this;
        }

        public Criteria andExceedBuyDescIn(List<String> values) {
            addCriterion("exceed_buy_desc in", values, "exceedBuyDesc");
            return (Criteria) this;
        }

        public Criteria andExceedBuyDescNotIn(List<String> values) {
            addCriterion("exceed_buy_desc not in", values, "exceedBuyDesc");
            return (Criteria) this;
        }

        public Criteria andExceedBuyDescBetween(String value1, String value2) {
            addCriterion("exceed_buy_desc between", value1, value2, "exceedBuyDesc");
            return (Criteria) this;
        }

        public Criteria andExceedBuyDescNotBetween(String value1, String value2) {
            addCriterion("exceed_buy_desc not between", value1, value2, "exceedBuyDesc");
            return (Criteria) this;
        }

        public Criteria andRepulseDescIsNull() {
            addCriterion("repulse_desc is null");
            return (Criteria) this;
        }

        public Criteria andRepulseDescIsNotNull() {
            addCriterion("repulse_desc is not null");
            return (Criteria) this;
        }

        public Criteria andRepulseDescEqualTo(String value) {
            addCriterion("repulse_desc =", value, "repulseDesc");
            return (Criteria) this;
        }

        public Criteria andRepulseDescNotEqualTo(String value) {
            addCriterion("repulse_desc <>", value, "repulseDesc");
            return (Criteria) this;
        }

        public Criteria andRepulseDescGreaterThan(String value) {
            addCriterion("repulse_desc >", value, "repulseDesc");
            return (Criteria) this;
        }

        public Criteria andRepulseDescGreaterThanOrEqualTo(String value) {
            addCriterion("repulse_desc >=", value, "repulseDesc");
            return (Criteria) this;
        }

        public Criteria andRepulseDescLessThan(String value) {
            addCriterion("repulse_desc <", value, "repulseDesc");
            return (Criteria) this;
        }

        public Criteria andRepulseDescLessThanOrEqualTo(String value) {
            addCriterion("repulse_desc <=", value, "repulseDesc");
            return (Criteria) this;
        }

        public Criteria andRepulseDescLike(String value) {
            addCriterion("repulse_desc like", value, "repulseDesc");
            return (Criteria) this;
        }

        public Criteria andRepulseDescNotLike(String value) {
            addCriterion("repulse_desc not like", value, "repulseDesc");
            return (Criteria) this;
        }

        public Criteria andRepulseDescIn(List<String> values) {
            addCriterion("repulse_desc in", values, "repulseDesc");
            return (Criteria) this;
        }

        public Criteria andRepulseDescNotIn(List<String> values) {
            addCriterion("repulse_desc not in", values, "repulseDesc");
            return (Criteria) this;
        }

        public Criteria andRepulseDescBetween(String value1, String value2) {
            addCriterion("repulse_desc between", value1, value2, "repulseDesc");
            return (Criteria) this;
        }

        public Criteria andRepulseDescNotBetween(String value1, String value2) {
            addCriterion("repulse_desc not between", value1, value2, "repulseDesc");
            return (Criteria) this;
        }

        public Criteria andApplyOrderTypeIsNull() {
            addCriterion("apply_order_type is null");
            return (Criteria) this;
        }

        public Criteria andApplyOrderTypeIsNotNull() {
            addCriterion("apply_order_type is not null");
            return (Criteria) this;
        }

        public Criteria andApplyOrderTypeEqualTo(Integer value) {
            addCriterion("apply_order_type =", value, "applyOrderType");
            return (Criteria) this;
        }

        public Criteria andApplyOrderTypeNotEqualTo(Integer value) {
            addCriterion("apply_order_type <>", value, "applyOrderType");
            return (Criteria) this;
        }

        public Criteria andApplyOrderTypeGreaterThan(Integer value) {
            addCriterion("apply_order_type >", value, "applyOrderType");
            return (Criteria) this;
        }

        public Criteria andApplyOrderTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("apply_order_type >=", value, "applyOrderType");
            return (Criteria) this;
        }

        public Criteria andApplyOrderTypeLessThan(Integer value) {
            addCriterion("apply_order_type <", value, "applyOrderType");
            return (Criteria) this;
        }

        public Criteria andApplyOrderTypeLessThanOrEqualTo(Integer value) {
            addCriterion("apply_order_type <=", value, "applyOrderType");
            return (Criteria) this;
        }

        public Criteria andApplyOrderTypeIn(List<Integer> values) {
            addCriterion("apply_order_type in", values, "applyOrderType");
            return (Criteria) this;
        }

        public Criteria andApplyOrderTypeNotIn(List<Integer> values) {
            addCriterion("apply_order_type not in", values, "applyOrderType");
            return (Criteria) this;
        }

        public Criteria andApplyOrderTypeBetween(Integer value1, Integer value2) {
            addCriterion("apply_order_type between", value1, value2, "applyOrderType");
            return (Criteria) this;
        }

        public Criteria andApplyOrderTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("apply_order_type not between", value1, value2, "applyOrderType");
            return (Criteria) this;
        }

        public Criteria andApplicantNameIsNull() {
            addCriterion("applicant_name is null");
            return (Criteria) this;
        }

        public Criteria andApplicantNameIsNotNull() {
            addCriterion("applicant_name is not null");
            return (Criteria) this;
        }

        public Criteria andApplicantNameEqualTo(String value) {
            addCriterion("applicant_name =", value, "applicantName");
            return (Criteria) this;
        }

        public Criteria andApplicantNameNotEqualTo(String value) {
            addCriterion("applicant_name <>", value, "applicantName");
            return (Criteria) this;
        }

        public Criteria andApplicantNameGreaterThan(String value) {
            addCriterion("applicant_name >", value, "applicantName");
            return (Criteria) this;
        }

        public Criteria andApplicantNameGreaterThanOrEqualTo(String value) {
            addCriterion("applicant_name >=", value, "applicantName");
            return (Criteria) this;
        }

        public Criteria andApplicantNameLessThan(String value) {
            addCriterion("applicant_name <", value, "applicantName");
            return (Criteria) this;
        }

        public Criteria andApplicantNameLessThanOrEqualTo(String value) {
            addCriterion("applicant_name <=", value, "applicantName");
            return (Criteria) this;
        }

        public Criteria andApplicantNameLike(String value) {
            addCriterion("applicant_name like", value, "applicantName");
            return (Criteria) this;
        }

        public Criteria andApplicantNameNotLike(String value) {
            addCriterion("applicant_name not like", value, "applicantName");
            return (Criteria) this;
        }

        public Criteria andApplicantNameIn(List<String> values) {
            addCriterion("applicant_name in", values, "applicantName");
            return (Criteria) this;
        }

        public Criteria andApplicantNameNotIn(List<String> values) {
            addCriterion("applicant_name not in", values, "applicantName");
            return (Criteria) this;
        }

        public Criteria andApplicantNameBetween(String value1, String value2) {
            addCriterion("applicant_name between", value1, value2, "applicantName");
            return (Criteria) this;
        }

        public Criteria andApplicantNameNotBetween(String value1, String value2) {
            addCriterion("applicant_name not between", value1, value2, "applicantName");
            return (Criteria) this;
        }

        public Criteria andExceedBuyDescContentIsNull() {
            addCriterion("exceed_buy_desc_content is null");
            return (Criteria) this;
        }

        public Criteria andExceedBuyDescContentIsNotNull() {
            addCriterion("exceed_buy_desc_content is not null");
            return (Criteria) this;
        }

        public Criteria andExceedBuyDescContentEqualTo(String value) {
            addCriterion("exceed_buy_desc_content =", value, "exceedBuyDescContent");
            return (Criteria) this;
        }

        public Criteria andExceedBuyDescContentNotEqualTo(String value) {
            addCriterion("exceed_buy_desc_content <>", value, "exceedBuyDescContent");
            return (Criteria) this;
        }

        public Criteria andExceedBuyDescContentGreaterThan(String value) {
            addCriterion("exceed_buy_desc_content >", value, "exceedBuyDescContent");
            return (Criteria) this;
        }

        public Criteria andExceedBuyDescContentGreaterThanOrEqualTo(String value) {
            addCriterion("exceed_buy_desc_content >=", value, "exceedBuyDescContent");
            return (Criteria) this;
        }

        public Criteria andExceedBuyDescContentLessThan(String value) {
            addCriterion("exceed_buy_desc_content <", value, "exceedBuyDescContent");
            return (Criteria) this;
        }

        public Criteria andExceedBuyDescContentLessThanOrEqualTo(String value) {
            addCriterion("exceed_buy_desc_content <=", value, "exceedBuyDescContent");
            return (Criteria) this;
        }

        public Criteria andExceedBuyDescContentLike(String value) {
            addCriterion("exceed_buy_desc_content like", value, "exceedBuyDescContent");
            return (Criteria) this;
        }

        public Criteria andExceedBuyDescContentNotLike(String value) {
            addCriterion("exceed_buy_desc_content not like", value, "exceedBuyDescContent");
            return (Criteria) this;
        }

        public Criteria andExceedBuyDescContentIn(List<String> values) {
            addCriterion("exceed_buy_desc_content in", values, "exceedBuyDescContent");
            return (Criteria) this;
        }

        public Criteria andExceedBuyDescContentNotIn(List<String> values) {
            addCriterion("exceed_buy_desc_content not in", values, "exceedBuyDescContent");
            return (Criteria) this;
        }

        public Criteria andExceedBuyDescContentBetween(String value1, String value2) {
            addCriterion("exceed_buy_desc_content between", value1, value2, "exceedBuyDescContent");
            return (Criteria) this;
        }

        public Criteria andExceedBuyDescContentNotBetween(String value1, String value2) {
            addCriterion("exceed_buy_desc_content not between", value1, value2, "exceedBuyDescContent");
            return (Criteria) this;
        }

        public Criteria andCostAttributionIdIsNull() {
            addCriterion("cost_attribution_id is null");
            return (Criteria) this;
        }

        public Criteria andCostAttributionIdIsNotNull() {
            addCriterion("cost_attribution_id is not null");
            return (Criteria) this;
        }

        public Criteria andCostAttributionIdEqualTo(String value) {
            addCriterion("cost_attribution_id =", value, "costAttributionId");
            return (Criteria) this;
        }

        public Criteria andCostAttributionIdNotEqualTo(String value) {
            addCriterion("cost_attribution_id <>", value, "costAttributionId");
            return (Criteria) this;
        }

        public Criteria andCostAttributionIdGreaterThan(String value) {
            addCriterion("cost_attribution_id >", value, "costAttributionId");
            return (Criteria) this;
        }

        public Criteria andCostAttributionIdGreaterThanOrEqualTo(String value) {
            addCriterion("cost_attribution_id >=", value, "costAttributionId");
            return (Criteria) this;
        }

        public Criteria andCostAttributionIdLessThan(String value) {
            addCriterion("cost_attribution_id <", value, "costAttributionId");
            return (Criteria) this;
        }

        public Criteria andCostAttributionIdLessThanOrEqualTo(String value) {
            addCriterion("cost_attribution_id <=", value, "costAttributionId");
            return (Criteria) this;
        }

        public Criteria andCostAttributionIdLike(String value) {
            addCriterion("cost_attribution_id like", value, "costAttributionId");
            return (Criteria) this;
        }

        public Criteria andCostAttributionIdNotLike(String value) {
            addCriterion("cost_attribution_id not like", value, "costAttributionId");
            return (Criteria) this;
        }

        public Criteria andCostAttributionIdIn(List<String> values) {
            addCriterion("cost_attribution_id in", values, "costAttributionId");
            return (Criteria) this;
        }

        public Criteria andCostAttributionIdNotIn(List<String> values) {
            addCriterion("cost_attribution_id not in", values, "costAttributionId");
            return (Criteria) this;
        }

        public Criteria andCostAttributionIdBetween(String value1, String value2) {
            addCriterion("cost_attribution_id between", value1, value2, "costAttributionId");
            return (Criteria) this;
        }

        public Criteria andCostAttributionIdNotBetween(String value1, String value2) {
            addCriterion("cost_attribution_id not between", value1, value2, "costAttributionId");
            return (Criteria) this;
        }

        public Criteria andCostAttributionNameIsNull() {
            addCriterion("cost_attribution_name is null");
            return (Criteria) this;
        }

        public Criteria andCostAttributionNameIsNotNull() {
            addCriterion("cost_attribution_name is not null");
            return (Criteria) this;
        }

        public Criteria andCostAttributionNameEqualTo(String value) {
            addCriterion("cost_attribution_name =", value, "costAttributionName");
            return (Criteria) this;
        }

        public Criteria andCostAttributionNameNotEqualTo(String value) {
            addCriterion("cost_attribution_name <>", value, "costAttributionName");
            return (Criteria) this;
        }

        public Criteria andCostAttributionNameGreaterThan(String value) {
            addCriterion("cost_attribution_name >", value, "costAttributionName");
            return (Criteria) this;
        }

        public Criteria andCostAttributionNameGreaterThanOrEqualTo(String value) {
            addCriterion("cost_attribution_name >=", value, "costAttributionName");
            return (Criteria) this;
        }

        public Criteria andCostAttributionNameLessThan(String value) {
            addCriterion("cost_attribution_name <", value, "costAttributionName");
            return (Criteria) this;
        }

        public Criteria andCostAttributionNameLessThanOrEqualTo(String value) {
            addCriterion("cost_attribution_name <=", value, "costAttributionName");
            return (Criteria) this;
        }

        public Criteria andCostAttributionNameLike(String value) {
            addCriterion("cost_attribution_name like", value, "costAttributionName");
            return (Criteria) this;
        }

        public Criteria andCostAttributionNameNotLike(String value) {
            addCriterion("cost_attribution_name not like", value, "costAttributionName");
            return (Criteria) this;
        }

        public Criteria andCostAttributionNameIn(List<String> values) {
            addCriterion("cost_attribution_name in", values, "costAttributionName");
            return (Criteria) this;
        }

        public Criteria andCostAttributionNameNotIn(List<String> values) {
            addCriterion("cost_attribution_name not in", values, "costAttributionName");
            return (Criteria) this;
        }

        public Criteria andCostAttributionNameBetween(String value1, String value2) {
            addCriterion("cost_attribution_name between", value1, value2, "costAttributionName");
            return (Criteria) this;
        }

        public Criteria andCostAttributionNameNotBetween(String value1, String value2) {
            addCriterion("cost_attribution_name not between", value1, value2, "costAttributionName");
            return (Criteria) this;
        }

        public Criteria andCostAttributionCategoryIsNull() {
            addCriterion("cost_attribution_category is null");
            return (Criteria) this;
        }

        public Criteria andCostAttributionCategoryIsNotNull() {
            addCriterion("cost_attribution_category is not null");
            return (Criteria) this;
        }

        public Criteria andCostAttributionCategoryEqualTo(Integer value) {
            addCriterion("cost_attribution_category =", value, "costAttributionCategory");
            return (Criteria) this;
        }

        public Criteria andCostAttributionCategoryNotEqualTo(Integer value) {
            addCriterion("cost_attribution_category <>", value, "costAttributionCategory");
            return (Criteria) this;
        }

        public Criteria andCostAttributionCategoryGreaterThan(Integer value) {
            addCriterion("cost_attribution_category >", value, "costAttributionCategory");
            return (Criteria) this;
        }

        public Criteria andCostAttributionCategoryGreaterThanOrEqualTo(Integer value) {
            addCriterion("cost_attribution_category >=", value, "costAttributionCategory");
            return (Criteria) this;
        }

        public Criteria andCostAttributionCategoryLessThan(Integer value) {
            addCriterion("cost_attribution_category <", value, "costAttributionCategory");
            return (Criteria) this;
        }

        public Criteria andCostAttributionCategoryLessThanOrEqualTo(Integer value) {
            addCriterion("cost_attribution_category <=", value, "costAttributionCategory");
            return (Criteria) this;
        }

        public Criteria andCostAttributionCategoryIn(List<Integer> values) {
            addCriterion("cost_attribution_category in", values, "costAttributionCategory");
            return (Criteria) this;
        }

        public Criteria andCostAttributionCategoryNotIn(List<Integer> values) {
            addCriterion("cost_attribution_category not in", values, "costAttributionCategory");
            return (Criteria) this;
        }

        public Criteria andCostAttributionCategoryBetween(Integer value1, Integer value2) {
            addCriterion("cost_attribution_category between", value1, value2, "costAttributionCategory");
            return (Criteria) this;
        }

        public Criteria andCostAttributionCategoryNotBetween(Integer value1, Integer value2) {
            addCriterion("cost_attribution_category not between", value1, value2, "costAttributionCategory");
            return (Criteria) this;
        }

        public Criteria andRealPriceIsNull() {
            addCriterion("real_price is null");
            return (Criteria) this;
        }

        public Criteria andRealPriceIsNotNull() {
            addCriterion("real_price is not null");
            return (Criteria) this;
        }

        public Criteria andRealPriceEqualTo(BigDecimal value) {
            addCriterion("real_price =", value, "realPrice");
            return (Criteria) this;
        }

        public Criteria andRealPriceNotEqualTo(BigDecimal value) {
            addCriterion("real_price <>", value, "realPrice");
            return (Criteria) this;
        }

        public Criteria andRealPriceGreaterThan(BigDecimal value) {
            addCriterion("real_price >", value, "realPrice");
            return (Criteria) this;
        }

        public Criteria andRealPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("real_price >=", value, "realPrice");
            return (Criteria) this;
        }

        public Criteria andRealPriceLessThan(BigDecimal value) {
            addCriterion("real_price <", value, "realPrice");
            return (Criteria) this;
        }

        public Criteria andRealPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("real_price <=", value, "realPrice");
            return (Criteria) this;
        }

        public Criteria andRealPriceIn(List<BigDecimal> values) {
            addCriterion("real_price in", values, "realPrice");
            return (Criteria) this;
        }

        public Criteria andRealPriceNotIn(List<BigDecimal> values) {
            addCriterion("real_price not in", values, "realPrice");
            return (Criteria) this;
        }

        public Criteria andRealPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("real_price between", value1, value2, "realPrice");
            return (Criteria) this;
        }

        public Criteria andRealPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("real_price not between", value1, value2, "realPrice");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeIsNull() {
            addCriterion("exceed_buy_type is null");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeIsNotNull() {
            addCriterion("exceed_buy_type is not null");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeEqualTo(Integer value) {
            addCriterion("exceed_buy_type =", value, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeNotEqualTo(Integer value) {
            addCriterion("exceed_buy_type <>", value, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeGreaterThan(Integer value) {
            addCriterion("exceed_buy_type >", value, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("exceed_buy_type >=", value, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeLessThan(Integer value) {
            addCriterion("exceed_buy_type <", value, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeLessThanOrEqualTo(Integer value) {
            addCriterion("exceed_buy_type <=", value, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeIn(List<Integer> values) {
            addCriterion("exceed_buy_type in", values, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeNotIn(List<Integer> values) {
            addCriterion("exceed_buy_type not in", values, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeBetween(Integer value1, Integer value2) {
            addCriterion("exceed_buy_type between", value1, value2, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("exceed_buy_type not between", value1, value2, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andOvertimeIsNull() {
            addCriterion("overtime is null");
            return (Criteria) this;
        }

        public Criteria andOvertimeIsNotNull() {
            addCriterion("overtime is not null");
            return (Criteria) this;
        }

        public Criteria andOvertimeEqualTo(Date value) {
            addCriterion("overtime =", value, "overtime");
            return (Criteria) this;
        }

        public Criteria andOvertimeNotEqualTo(Date value) {
            addCriterion("overtime <>", value, "overtime");
            return (Criteria) this;
        }

        public Criteria andOvertimeGreaterThan(Date value) {
            addCriterion("overtime >", value, "overtime");
            return (Criteria) this;
        }

        public Criteria andOvertimeGreaterThanOrEqualTo(Date value) {
            addCriterion("overtime >=", value, "overtime");
            return (Criteria) this;
        }

        public Criteria andOvertimeLessThan(Date value) {
            addCriterion("overtime <", value, "overtime");
            return (Criteria) this;
        }

        public Criteria andOvertimeLessThanOrEqualTo(Date value) {
            addCriterion("overtime <=", value, "overtime");
            return (Criteria) this;
        }

        public Criteria andOvertimeIn(List<Date> values) {
            addCriterion("overtime in", values, "overtime");
            return (Criteria) this;
        }

        public Criteria andOvertimeNotIn(List<Date> values) {
            addCriterion("overtime not in", values, "overtime");
            return (Criteria) this;
        }

        public Criteria andOvertimeBetween(Date value1, Date value2) {
            addCriterion("overtime between", value1, value2, "overtime");
            return (Criteria) this;
        }

        public Criteria andOvertimeNotBetween(Date value1, Date value2) {
            addCriterion("overtime not between", value1, value2, "overtime");
            return (Criteria) this;
        }

        public Criteria andTravelDayIsNull() {
            addCriterion("travel_day is null");
            return (Criteria) this;
        }

        public Criteria andTravelDayIsNotNull() {
            addCriterion("travel_day is not null");
            return (Criteria) this;
        }

        public Criteria andTravelDayEqualTo(BigDecimal value) {
            addCriterion("travel_day =", value, "travelDay");
            return (Criteria) this;
        }

        public Criteria andTravelDayNotEqualTo(BigDecimal value) {
            addCriterion("travel_day <>", value, "travelDay");
            return (Criteria) this;
        }

        public Criteria andTravelDayGreaterThan(BigDecimal value) {
            addCriterion("travel_day >", value, "travelDay");
            return (Criteria) this;
        }

        public Criteria andTravelDayGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("travel_day >=", value, "travelDay");
            return (Criteria) this;
        }

        public Criteria andTravelDayLessThan(BigDecimal value) {
            addCriterion("travel_day <", value, "travelDay");
            return (Criteria) this;
        }

        public Criteria andTravelDayLessThanOrEqualTo(BigDecimal value) {
            addCriterion("travel_day <=", value, "travelDay");
            return (Criteria) this;
        }

        public Criteria andTravelDayIn(List<BigDecimal> values) {
            addCriterion("travel_day in", values, "travelDay");
            return (Criteria) this;
        }

        public Criteria andTravelDayNotIn(List<BigDecimal> values) {
            addCriterion("travel_day not in", values, "travelDay");
            return (Criteria) this;
        }

        public Criteria andTravelDayBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("travel_day between", value1, value2, "travelDay");
            return (Criteria) this;
        }

        public Criteria andTravelDayNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("travel_day not between", value1, value2, "travelDay");
            return (Criteria) this;
        }

        public Criteria andRootApplyOrderIdIsNull() {
            addCriterion("root_apply_order_id is null");
            return (Criteria) this;
        }

        public Criteria andRootApplyOrderIdIsNotNull() {
            addCriterion("root_apply_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andRootApplyOrderIdEqualTo(String value) {
            addCriterion("root_apply_order_id =", value, "rootApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andRootApplyOrderIdNotEqualTo(String value) {
            addCriterion("root_apply_order_id <>", value, "rootApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andRootApplyOrderIdGreaterThan(String value) {
            addCriterion("root_apply_order_id >", value, "rootApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andRootApplyOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("root_apply_order_id >=", value, "rootApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andRootApplyOrderIdLessThan(String value) {
            addCriterion("root_apply_order_id <", value, "rootApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andRootApplyOrderIdLessThanOrEqualTo(String value) {
            addCriterion("root_apply_order_id <=", value, "rootApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andRootApplyOrderIdLike(String value) {
            addCriterion("root_apply_order_id like", value, "rootApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andRootApplyOrderIdNotLike(String value) {
            addCriterion("root_apply_order_id not like", value, "rootApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andRootApplyOrderIdIn(List<String> values) {
            addCriterion("root_apply_order_id in", values, "rootApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andRootApplyOrderIdNotIn(List<String> values) {
            addCriterion("root_apply_order_id not in", values, "rootApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andRootApplyOrderIdBetween(String value1, String value2) {
            addCriterion("root_apply_order_id between", value1, value2, "rootApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andRootApplyOrderIdNotBetween(String value1, String value2) {
            addCriterion("root_apply_order_id not between", value1, value2, "rootApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andParentApplyOrderIdIsNull() {
            addCriterion("parent_apply_order_id is null");
            return (Criteria) this;
        }

        public Criteria andParentApplyOrderIdIsNotNull() {
            addCriterion("parent_apply_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andParentApplyOrderIdEqualTo(String value) {
            addCriterion("parent_apply_order_id =", value, "parentApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andParentApplyOrderIdNotEqualTo(String value) {
            addCriterion("parent_apply_order_id <>", value, "parentApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andParentApplyOrderIdGreaterThan(String value) {
            addCriterion("parent_apply_order_id >", value, "parentApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andParentApplyOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("parent_apply_order_id >=", value, "parentApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andParentApplyOrderIdLessThan(String value) {
            addCriterion("parent_apply_order_id <", value, "parentApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andParentApplyOrderIdLessThanOrEqualTo(String value) {
            addCriterion("parent_apply_order_id <=", value, "parentApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andParentApplyOrderIdLike(String value) {
            addCriterion("parent_apply_order_id like", value, "parentApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andParentApplyOrderIdNotLike(String value) {
            addCriterion("parent_apply_order_id not like", value, "parentApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andParentApplyOrderIdIn(List<String> values) {
            addCriterion("parent_apply_order_id in", values, "parentApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andParentApplyOrderIdNotIn(List<String> values) {
            addCriterion("parent_apply_order_id not in", values, "parentApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andParentApplyOrderIdBetween(String value1, String value2) {
            addCriterion("parent_apply_order_id between", value1, value2, "parentApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andParentApplyOrderIdNotBetween(String value1, String value2) {
            addCriterion("parent_apply_order_id not between", value1, value2, "parentApplyOrderId");
            return (Criteria) this;
        }

        public Criteria andIsChangeApplyIsNull() {
            addCriterion("is_change_apply is null");
            return (Criteria) this;
        }

        public Criteria andIsChangeApplyIsNotNull() {
            addCriterion("is_change_apply is not null");
            return (Criteria) this;
        }

        public Criteria andIsChangeApplyEqualTo(Boolean value) {
            addCriterion("is_change_apply =", value, "isChangeApply");
            return (Criteria) this;
        }

        public Criteria andIsChangeApplyNotEqualTo(Boolean value) {
            addCriterion("is_change_apply <>", value, "isChangeApply");
            return (Criteria) this;
        }

        public Criteria andIsChangeApplyGreaterThan(Boolean value) {
            addCriterion("is_change_apply >", value, "isChangeApply");
            return (Criteria) this;
        }

        public Criteria andIsChangeApplyGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_change_apply >=", value, "isChangeApply");
            return (Criteria) this;
        }

        public Criteria andIsChangeApplyLessThan(Boolean value) {
            addCriterion("is_change_apply <", value, "isChangeApply");
            return (Criteria) this;
        }

        public Criteria andIsChangeApplyLessThanOrEqualTo(Boolean value) {
            addCriterion("is_change_apply <=", value, "isChangeApply");
            return (Criteria) this;
        }

        public Criteria andIsChangeApplyIn(List<Boolean> values) {
            addCriterion("is_change_apply in", values, "isChangeApply");
            return (Criteria) this;
        }

        public Criteria andIsChangeApplyNotIn(List<Boolean> values) {
            addCriterion("is_change_apply not in", values, "isChangeApply");
            return (Criteria) this;
        }

        public Criteria andIsChangeApplyBetween(Boolean value1, Boolean value2) {
            addCriterion("is_change_apply between", value1, value2, "isChangeApply");
            return (Criteria) this;
        }

        public Criteria andIsChangeApplyNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_change_apply not between", value1, value2, "isChangeApply");
            return (Criteria) this;
        }

        public Criteria andChangeReasonIsNull() {
            addCriterion("change_reason is null");
            return (Criteria) this;
        }

        public Criteria andChangeReasonIsNotNull() {
            addCriterion("change_reason is not null");
            return (Criteria) this;
        }

        public Criteria andChangeReasonEqualTo(String value) {
            addCriterion("change_reason =", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonNotEqualTo(String value) {
            addCriterion("change_reason <>", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonGreaterThan(String value) {
            addCriterion("change_reason >", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonGreaterThanOrEqualTo(String value) {
            addCriterion("change_reason >=", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonLessThan(String value) {
            addCriterion("change_reason <", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonLessThanOrEqualTo(String value) {
            addCriterion("change_reason <=", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonLike(String value) {
            addCriterion("change_reason like", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonNotLike(String value) {
            addCriterion("change_reason not like", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonIn(List<String> values) {
            addCriterion("change_reason in", values, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonNotIn(List<String> values) {
            addCriterion("change_reason not in", values, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonBetween(String value1, String value2) {
            addCriterion("change_reason between", value1, value2, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonNotBetween(String value1, String value2) {
            addCriterion("change_reason not between", value1, value2, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescIsNull() {
            addCriterion("change_reason_desc is null");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescIsNotNull() {
            addCriterion("change_reason_desc is not null");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescEqualTo(String value) {
            addCriterion("change_reason_desc =", value, "changeReasonDesc");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescNotEqualTo(String value) {
            addCriterion("change_reason_desc <>", value, "changeReasonDesc");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescGreaterThan(String value) {
            addCriterion("change_reason_desc >", value, "changeReasonDesc");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescGreaterThanOrEqualTo(String value) {
            addCriterion("change_reason_desc >=", value, "changeReasonDesc");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescLessThan(String value) {
            addCriterion("change_reason_desc <", value, "changeReasonDesc");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescLessThanOrEqualTo(String value) {
            addCriterion("change_reason_desc <=", value, "changeReasonDesc");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescLike(String value) {
            addCriterion("change_reason_desc like", value, "changeReasonDesc");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescNotLike(String value) {
            addCriterion("change_reason_desc not like", value, "changeReasonDesc");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescIn(List<String> values) {
            addCriterion("change_reason_desc in", values, "changeReasonDesc");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescNotIn(List<String> values) {
            addCriterion("change_reason_desc not in", values, "changeReasonDesc");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescBetween(String value1, String value2) {
            addCriterion("change_reason_desc between", value1, value2, "changeReasonDesc");
            return (Criteria) this;
        }

        public Criteria andChangeReasonDescNotBetween(String value1, String value2) {
            addCriterion("change_reason_desc not between", value1, value2, "changeReasonDesc");
            return (Criteria) this;
        }

        public Criteria andCancelReasonIsNull() {
            addCriterion("cancel_reason is null");
            return (Criteria) this;
        }

        public Criteria andCancelReasonIsNotNull() {
            addCriterion("cancel_reason is not null");
            return (Criteria) this;
        }

        public Criteria andCancelReasonEqualTo(String value) {
            addCriterion("cancel_reason =", value, "cancelReason");
            return (Criteria) this;
        }

        public Criteria andCancelReasonNotEqualTo(String value) {
            addCriterion("cancel_reason <>", value, "cancelReason");
            return (Criteria) this;
        }

        public Criteria andCancelReasonGreaterThan(String value) {
            addCriterion("cancel_reason >", value, "cancelReason");
            return (Criteria) this;
        }

        public Criteria andCancelReasonGreaterThanOrEqualTo(String value) {
            addCriterion("cancel_reason >=", value, "cancelReason");
            return (Criteria) this;
        }

        public Criteria andCancelReasonLessThan(String value) {
            addCriterion("cancel_reason <", value, "cancelReason");
            return (Criteria) this;
        }

        public Criteria andCancelReasonLessThanOrEqualTo(String value) {
            addCriterion("cancel_reason <=", value, "cancelReason");
            return (Criteria) this;
        }

        public Criteria andCancelReasonLike(String value) {
            addCriterion("cancel_reason like", value, "cancelReason");
            return (Criteria) this;
        }

        public Criteria andCancelReasonNotLike(String value) {
            addCriterion("cancel_reason not like", value, "cancelReason");
            return (Criteria) this;
        }

        public Criteria andCancelReasonIn(List<String> values) {
            addCriterion("cancel_reason in", values, "cancelReason");
            return (Criteria) this;
        }

        public Criteria andCancelReasonNotIn(List<String> values) {
            addCriterion("cancel_reason not in", values, "cancelReason");
            return (Criteria) this;
        }

        public Criteria andCancelReasonBetween(String value1, String value2) {
            addCriterion("cancel_reason between", value1, value2, "cancelReason");
            return (Criteria) this;
        }

        public Criteria andCancelReasonNotBetween(String value1, String value2) {
            addCriterion("cancel_reason not between", value1, value2, "cancelReason");
            return (Criteria) this;
        }

        public Criteria andCancelReasonDescIsNull() {
            addCriterion("cancel_reason_desc is null");
            return (Criteria) this;
        }

        public Criteria andCancelReasonDescIsNotNull() {
            addCriterion("cancel_reason_desc is not null");
            return (Criteria) this;
        }

        public Criteria andCancelReasonDescEqualTo(String value) {
            addCriterion("cancel_reason_desc =", value, "cancelReasonDesc");
            return (Criteria) this;
        }

        public Criteria andCancelReasonDescNotEqualTo(String value) {
            addCriterion("cancel_reason_desc <>", value, "cancelReasonDesc");
            return (Criteria) this;
        }

        public Criteria andCancelReasonDescGreaterThan(String value) {
            addCriterion("cancel_reason_desc >", value, "cancelReasonDesc");
            return (Criteria) this;
        }

        public Criteria andCancelReasonDescGreaterThanOrEqualTo(String value) {
            addCriterion("cancel_reason_desc >=", value, "cancelReasonDesc");
            return (Criteria) this;
        }

        public Criteria andCancelReasonDescLessThan(String value) {
            addCriterion("cancel_reason_desc <", value, "cancelReasonDesc");
            return (Criteria) this;
        }

        public Criteria andCancelReasonDescLessThanOrEqualTo(String value) {
            addCriterion("cancel_reason_desc <=", value, "cancelReasonDesc");
            return (Criteria) this;
        }

        public Criteria andCancelReasonDescLike(String value) {
            addCriterion("cancel_reason_desc like", value, "cancelReasonDesc");
            return (Criteria) this;
        }

        public Criteria andCancelReasonDescNotLike(String value) {
            addCriterion("cancel_reason_desc not like", value, "cancelReasonDesc");
            return (Criteria) this;
        }

        public Criteria andCancelReasonDescIn(List<String> values) {
            addCriterion("cancel_reason_desc in", values, "cancelReasonDesc");
            return (Criteria) this;
        }

        public Criteria andCancelReasonDescNotIn(List<String> values) {
            addCriterion("cancel_reason_desc not in", values, "cancelReasonDesc");
            return (Criteria) this;
        }

        public Criteria andCancelReasonDescBetween(String value1, String value2) {
            addCriterion("cancel_reason_desc between", value1, value2, "cancelReasonDesc");
            return (Criteria) this;
        }

        public Criteria andCancelReasonDescNotBetween(String value1, String value2) {
            addCriterion("cancel_reason_desc not between", value1, value2, "cancelReasonDesc");
            return (Criteria) this;
        }

        public Criteria andSubTypeIsNull() {
            addCriterion("sub_type is null");
            return (Criteria) this;
        }

        public Criteria andSubTypeIsNotNull() {
            addCriterion("sub_type is not null");
            return (Criteria) this;
        }

        public Criteria andSubTypeEqualTo(Integer value) {
            addCriterion("sub_type =", value, "subType");
            return (Criteria) this;
        }

        public Criteria andSubTypeNotEqualTo(Integer value) {
            addCriterion("sub_type <>", value, "subType");
            return (Criteria) this;
        }

        public Criteria andSubTypeGreaterThan(Integer value) {
            addCriterion("sub_type >", value, "subType");
            return (Criteria) this;
        }

        public Criteria andSubTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("sub_type >=", value, "subType");
            return (Criteria) this;
        }

        public Criteria andSubTypeLessThan(Integer value) {
            addCriterion("sub_type <", value, "subType");
            return (Criteria) this;
        }

        public Criteria andSubTypeLessThanOrEqualTo(Integer value) {
            addCriterion("sub_type <=", value, "subType");
            return (Criteria) this;
        }

        public Criteria andSubTypeIn(List<Integer> values) {
            addCriterion("sub_type in", values, "subType");
            return (Criteria) this;
        }

        public Criteria andSubTypeNotIn(List<Integer> values) {
            addCriterion("sub_type not in", values, "subType");
            return (Criteria) this;
        }

        public Criteria andSubTypeBetween(Integer value1, Integer value2) {
            addCriterion("sub_type between", value1, value2, "subType");
            return (Criteria) this;
        }

        public Criteria andSubTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("sub_type not between", value1, value2, "subType");
            return (Criteria) this;
        }

        public Criteria andCustomFieldsIsNull() {
            addCriterion("custom_fields is null");
            return (Criteria) this;
        }

        public Criteria andCustomFieldsIsNotNull() {
            addCriterion("custom_fields is not null");
            return (Criteria) this;
        }

        public Criteria andCustomFieldsEqualTo(String value) {
            addCriterion("custom_fields =", value, "customFields");
            return (Criteria) this;
        }

        public Criteria andCustomFieldsNotEqualTo(String value) {
            addCriterion("custom_fields <>", value, "customFields");
            return (Criteria) this;
        }

        public Criteria andCustomFieldsGreaterThan(String value) {
            addCriterion("custom_fields >", value, "customFields");
            return (Criteria) this;
        }

        public Criteria andCustomFieldsGreaterThanOrEqualTo(String value) {
            addCriterion("custom_fields >=", value, "customFields");
            return (Criteria) this;
        }

        public Criteria andCustomFieldsLessThan(String value) {
            addCriterion("custom_fields <", value, "customFields");
            return (Criteria) this;
        }

        public Criteria andCustomFieldsLessThanOrEqualTo(String value) {
            addCriterion("custom_fields <=", value, "customFields");
            return (Criteria) this;
        }

        public Criteria andCustomFieldsLike(String value) {
            addCriterion("custom_fields like", value, "customFields");
            return (Criteria) this;
        }

        public Criteria andCustomFieldsNotLike(String value) {
            addCriterion("custom_fields not like", value, "customFields");
            return (Criteria) this;
        }

        public Criteria andCustomFieldsIn(List<String> values) {
            addCriterion("custom_fields in", values, "customFields");
            return (Criteria) this;
        }

        public Criteria andCustomFieldsNotIn(List<String> values) {
            addCriterion("custom_fields not in", values, "customFields");
            return (Criteria) this;
        }

        public Criteria andCustomFieldsBetween(String value1, String value2) {
            addCriterion("custom_fields between", value1, value2, "customFields");
            return (Criteria) this;
        }

        public Criteria andCustomFieldsNotBetween(String value1, String value2) {
            addCriterion("custom_fields not between", value1, value2, "customFields");
            return (Criteria) this;
        }

        public Criteria andApplyReasonIdIsNull() {
            addCriterion("apply_reason_id is null");
            return (Criteria) this;
        }

        public Criteria andApplyReasonIdIsNotNull() {
            addCriterion("apply_reason_id is not null");
            return (Criteria) this;
        }

        public Criteria andApplyReasonIdEqualTo(Integer value) {
            addCriterion("apply_reason_id =", value, "applyReasonId");
            return (Criteria) this;
        }

        public Criteria andApplyReasonIdNotEqualTo(Integer value) {
            addCriterion("apply_reason_id <>", value, "applyReasonId");
            return (Criteria) this;
        }

        public Criteria andApplyReasonIdGreaterThan(Integer value) {
            addCriterion("apply_reason_id >", value, "applyReasonId");
            return (Criteria) this;
        }

        public Criteria andApplyReasonIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("apply_reason_id >=", value, "applyReasonId");
            return (Criteria) this;
        }

        public Criteria andApplyReasonIdLessThan(Integer value) {
            addCriterion("apply_reason_id <", value, "applyReasonId");
            return (Criteria) this;
        }

        public Criteria andApplyReasonIdLessThanOrEqualTo(Integer value) {
            addCriterion("apply_reason_id <=", value, "applyReasonId");
            return (Criteria) this;
        }

        public Criteria andApplyReasonIdIn(List<Integer> values) {
            addCriterion("apply_reason_id in", values, "applyReasonId");
            return (Criteria) this;
        }

        public Criteria andApplyReasonIdNotIn(List<Integer> values) {
            addCriterion("apply_reason_id not in", values, "applyReasonId");
            return (Criteria) this;
        }

        public Criteria andApplyReasonIdBetween(Integer value1, Integer value2) {
            addCriterion("apply_reason_id between", value1, value2, "applyReasonId");
            return (Criteria) this;
        }

        public Criteria andApplyReasonIdNotBetween(Integer value1, Integer value2) {
            addCriterion("apply_reason_id not between", value1, value2, "applyReasonId");
            return (Criteria) this;
        }

        public Criteria andChangeReasonIdIsNull() {
            addCriterion("change_reason_id is null");
            return (Criteria) this;
        }

        public Criteria andChangeReasonIdIsNotNull() {
            addCriterion("change_reason_id is not null");
            return (Criteria) this;
        }

        public Criteria andChangeReasonIdEqualTo(Integer value) {
            addCriterion("change_reason_id =", value, "changeReasonId");
            return (Criteria) this;
        }

        public Criteria andChangeReasonIdNotEqualTo(Integer value) {
            addCriterion("change_reason_id <>", value, "changeReasonId");
            return (Criteria) this;
        }

        public Criteria andChangeReasonIdGreaterThan(Integer value) {
            addCriterion("change_reason_id >", value, "changeReasonId");
            return (Criteria) this;
        }

        public Criteria andChangeReasonIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("change_reason_id >=", value, "changeReasonId");
            return (Criteria) this;
        }

        public Criteria andChangeReasonIdLessThan(Integer value) {
            addCriterion("change_reason_id <", value, "changeReasonId");
            return (Criteria) this;
        }

        public Criteria andChangeReasonIdLessThanOrEqualTo(Integer value) {
            addCriterion("change_reason_id <=", value, "changeReasonId");
            return (Criteria) this;
        }

        public Criteria andChangeReasonIdIn(List<Integer> values) {
            addCriterion("change_reason_id in", values, "changeReasonId");
            return (Criteria) this;
        }

        public Criteria andChangeReasonIdNotIn(List<Integer> values) {
            addCriterion("change_reason_id not in", values, "changeReasonId");
            return (Criteria) this;
        }

        public Criteria andChangeReasonIdBetween(Integer value1, Integer value2) {
            addCriterion("change_reason_id between", value1, value2, "changeReasonId");
            return (Criteria) this;
        }

        public Criteria andChangeReasonIdNotBetween(Integer value1, Integer value2) {
            addCriterion("change_reason_id not between", value1, value2, "changeReasonId");
            return (Criteria) this;
        }

        public Criteria andCancelReasonIdIsNull() {
            addCriterion("cancel_reason_id is null");
            return (Criteria) this;
        }

        public Criteria andCancelReasonIdIsNotNull() {
            addCriterion("cancel_reason_id is not null");
            return (Criteria) this;
        }

        public Criteria andCancelReasonIdEqualTo(Integer value) {
            addCriterion("cancel_reason_id =", value, "cancelReasonId");
            return (Criteria) this;
        }

        public Criteria andCancelReasonIdNotEqualTo(Integer value) {
            addCriterion("cancel_reason_id <>", value, "cancelReasonId");
            return (Criteria) this;
        }

        public Criteria andCancelReasonIdGreaterThan(Integer value) {
            addCriterion("cancel_reason_id >", value, "cancelReasonId");
            return (Criteria) this;
        }

        public Criteria andCancelReasonIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("cancel_reason_id >=", value, "cancelReasonId");
            return (Criteria) this;
        }

        public Criteria andCancelReasonIdLessThan(Integer value) {
            addCriterion("cancel_reason_id <", value, "cancelReasonId");
            return (Criteria) this;
        }

        public Criteria andCancelReasonIdLessThanOrEqualTo(Integer value) {
            addCriterion("cancel_reason_id <=", value, "cancelReasonId");
            return (Criteria) this;
        }

        public Criteria andCancelReasonIdIn(List<Integer> values) {
            addCriterion("cancel_reason_id in", values, "cancelReasonId");
            return (Criteria) this;
        }

        public Criteria andCancelReasonIdNotIn(List<Integer> values) {
            addCriterion("cancel_reason_id not in", values, "cancelReasonId");
            return (Criteria) this;
        }

        public Criteria andCancelReasonIdBetween(Integer value1, Integer value2) {
            addCriterion("cancel_reason_id between", value1, value2, "cancelReasonId");
            return (Criteria) this;
        }

        public Criteria andCancelReasonIdNotBetween(Integer value1, Integer value2) {
            addCriterion("cancel_reason_id not between", value1, value2, "cancelReasonId");
            return (Criteria) this;
        }

        public Criteria andDownloadLinkIsNull() {
            addCriterion("download_link is null");
            return (Criteria) this;
        }

        public Criteria andDownloadLinkIsNotNull() {
            addCriterion("download_link is not null");
            return (Criteria) this;
        }

        public Criteria andDownloadLinkEqualTo(String value) {
            addCriterion("download_link =", value, "downloadLink");
            return (Criteria) this;
        }

        public Criteria andDownloadLinkNotEqualTo(String value) {
            addCriterion("download_link <>", value, "downloadLink");
            return (Criteria) this;
        }

        public Criteria andDownloadLinkGreaterThan(String value) {
            addCriterion("download_link >", value, "downloadLink");
            return (Criteria) this;
        }

        public Criteria andDownloadLinkGreaterThanOrEqualTo(String value) {
            addCriterion("download_link >=", value, "downloadLink");
            return (Criteria) this;
        }

        public Criteria andDownloadLinkLessThan(String value) {
            addCriterion("download_link <", value, "downloadLink");
            return (Criteria) this;
        }

        public Criteria andDownloadLinkLessThanOrEqualTo(String value) {
            addCriterion("download_link <=", value, "downloadLink");
            return (Criteria) this;
        }

        public Criteria andDownloadLinkLike(String value) {
            addCriterion("download_link like", value, "downloadLink");
            return (Criteria) this;
        }

        public Criteria andDownloadLinkNotLike(String value) {
            addCriterion("download_link not like", value, "downloadLink");
            return (Criteria) this;
        }

        public Criteria andDownloadLinkIn(List<String> values) {
            addCriterion("download_link in", values, "downloadLink");
            return (Criteria) this;
        }

        public Criteria andDownloadLinkNotIn(List<String> values) {
            addCriterion("download_link not in", values, "downloadLink");
            return (Criteria) this;
        }

        public Criteria andDownloadLinkBetween(String value1, String value2) {
            addCriterion("download_link between", value1, value2, "downloadLink");
            return (Criteria) this;
        }

        public Criteria andDownloadLinkNotBetween(String value1, String value2) {
            addCriterion("download_link not between", value1, value2, "downloadLink");
            return (Criteria) this;
        }

        public Criteria andVoucherStatusIsNull() {
            addCriterion("voucher_status is null");
            return (Criteria) this;
        }

        public Criteria andVoucherStatusIsNotNull() {
            addCriterion("voucher_status is not null");
            return (Criteria) this;
        }

        public Criteria andVoucherStatusEqualTo(Integer value) {
            addCriterion("voucher_status =", value, "voucherStatus");
            return (Criteria) this;
        }

        public Criteria andVoucherStatusNotEqualTo(Integer value) {
            addCriterion("voucher_status <>", value, "voucherStatus");
            return (Criteria) this;
        }

        public Criteria andVoucherStatusGreaterThan(Integer value) {
            addCriterion("voucher_status >", value, "voucherStatus");
            return (Criteria) this;
        }

        public Criteria andVoucherStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("voucher_status >=", value, "voucherStatus");
            return (Criteria) this;
        }

        public Criteria andVoucherStatusLessThan(Integer value) {
            addCriterion("voucher_status <", value, "voucherStatus");
            return (Criteria) this;
        }

        public Criteria andVoucherStatusLessThanOrEqualTo(Integer value) {
            addCriterion("voucher_status <=", value, "voucherStatus");
            return (Criteria) this;
        }

        public Criteria andVoucherStatusIn(List<Integer> values) {
            addCriterion("voucher_status in", values, "voucherStatus");
            return (Criteria) this;
        }

        public Criteria andVoucherStatusNotIn(List<Integer> values) {
            addCriterion("voucher_status not in", values, "voucherStatus");
            return (Criteria) this;
        }

        public Criteria andVoucherStatusBetween(Integer value1, Integer value2) {
            addCriterion("voucher_status between", value1, value2, "voucherStatus");
            return (Criteria) this;
        }

        public Criteria andVoucherStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("voucher_status not between", value1, value2, "voucherStatus");
            return (Criteria) this;
        }

        public Criteria andVoucherCodeIsNull() {
            addCriterion("voucher_code is null");
            return (Criteria) this;
        }

        public Criteria andVoucherCodeIsNotNull() {
            addCriterion("voucher_code is not null");
            return (Criteria) this;
        }

        public Criteria andVoucherCodeEqualTo(String value) {
            addCriterion("voucher_code =", value, "voucherCode");
            return (Criteria) this;
        }

        public Criteria andVoucherCodeNotEqualTo(String value) {
            addCriterion("voucher_code <>", value, "voucherCode");
            return (Criteria) this;
        }

        public Criteria andVoucherCodeGreaterThan(String value) {
            addCriterion("voucher_code >", value, "voucherCode");
            return (Criteria) this;
        }

        public Criteria andVoucherCodeGreaterThanOrEqualTo(String value) {
            addCriterion("voucher_code >=", value, "voucherCode");
            return (Criteria) this;
        }

        public Criteria andVoucherCodeLessThan(String value) {
            addCriterion("voucher_code <", value, "voucherCode");
            return (Criteria) this;
        }

        public Criteria andVoucherCodeLessThanOrEqualTo(String value) {
            addCriterion("voucher_code <=", value, "voucherCode");
            return (Criteria) this;
        }

        public Criteria andVoucherCodeLike(String value) {
            addCriterion("voucher_code like", value, "voucherCode");
            return (Criteria) this;
        }

        public Criteria andVoucherCodeNotLike(String value) {
            addCriterion("voucher_code not like", value, "voucherCode");
            return (Criteria) this;
        }

        public Criteria andVoucherCodeIn(List<String> values) {
            addCriterion("voucher_code in", values, "voucherCode");
            return (Criteria) this;
        }

        public Criteria andVoucherCodeNotIn(List<String> values) {
            addCriterion("voucher_code not in", values, "voucherCode");
            return (Criteria) this;
        }

        public Criteria andVoucherCodeBetween(String value1, String value2) {
            addCriterion("voucher_code between", value1, value2, "voucherCode");
            return (Criteria) this;
        }

        public Criteria andVoucherCodeNotBetween(String value1, String value2) {
            addCriterion("voucher_code not between", value1, value2, "voucherCode");
            return (Criteria) this;
        }

        public Criteria andHastenStatuIsNull() {
            addCriterion("hasten_statu is null");
            return (Criteria) this;
        }

        public Criteria andHastenStatuIsNotNull() {
            addCriterion("hasten_statu is not null");
            return (Criteria) this;
        }

        public Criteria andHastenStatuEqualTo(Integer value) {
            addCriterion("hasten_statu =", value, "hastenStatu");
            return (Criteria) this;
        }

        public Criteria andHastenStatuNotEqualTo(Integer value) {
            addCriterion("hasten_statu <>", value, "hastenStatu");
            return (Criteria) this;
        }

        public Criteria andHastenStatuGreaterThan(Integer value) {
            addCriterion("hasten_statu >", value, "hastenStatu");
            return (Criteria) this;
        }

        public Criteria andHastenStatuGreaterThanOrEqualTo(Integer value) {
            addCriterion("hasten_statu >=", value, "hastenStatu");
            return (Criteria) this;
        }

        public Criteria andHastenStatuLessThan(Integer value) {
            addCriterion("hasten_statu <", value, "hastenStatu");
            return (Criteria) this;
        }

        public Criteria andHastenStatuLessThanOrEqualTo(Integer value) {
            addCriterion("hasten_statu <=", value, "hastenStatu");
            return (Criteria) this;
        }

        public Criteria andHastenStatuIn(List<Integer> values) {
            addCriterion("hasten_statu in", values, "hastenStatu");
            return (Criteria) this;
        }

        public Criteria andHastenStatuNotIn(List<Integer> values) {
            addCriterion("hasten_statu not in", values, "hastenStatu");
            return (Criteria) this;
        }

        public Criteria andHastenStatuBetween(Integer value1, Integer value2) {
            addCriterion("hasten_statu between", value1, value2, "hastenStatu");
            return (Criteria) this;
        }

        public Criteria andHastenStatuNotBetween(Integer value1, Integer value2) {
            addCriterion("hasten_statu not between", value1, value2, "hastenStatu");
            return (Criteria) this;
        }

        public Criteria andHastenCreateTimeIsNull() {
            addCriterion("hasten_create_time is null");
            return (Criteria) this;
        }

        public Criteria andHastenCreateTimeIsNotNull() {
            addCriterion("hasten_create_time is not null");
            return (Criteria) this;
        }

        public Criteria andHastenCreateTimeEqualTo(Date value) {
            addCriterion("hasten_create_time =", value, "hastenCreateTime");
            return (Criteria) this;
        }

        public Criteria andHastenCreateTimeNotEqualTo(Date value) {
            addCriterion("hasten_create_time <>", value, "hastenCreateTime");
            return (Criteria) this;
        }

        public Criteria andHastenCreateTimeGreaterThan(Date value) {
            addCriterion("hasten_create_time >", value, "hastenCreateTime");
            return (Criteria) this;
        }

        public Criteria andHastenCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("hasten_create_time >=", value, "hastenCreateTime");
            return (Criteria) this;
        }

        public Criteria andHastenCreateTimeLessThan(Date value) {
            addCriterion("hasten_create_time <", value, "hastenCreateTime");
            return (Criteria) this;
        }

        public Criteria andHastenCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("hasten_create_time <=", value, "hastenCreateTime");
            return (Criteria) this;
        }

        public Criteria andHastenCreateTimeIn(List<Date> values) {
            addCriterion("hasten_create_time in", values, "hastenCreateTime");
            return (Criteria) this;
        }

        public Criteria andHastenCreateTimeNotIn(List<Date> values) {
            addCriterion("hasten_create_time not in", values, "hastenCreateTime");
            return (Criteria) this;
        }

        public Criteria andHastenCreateTimeBetween(Date value1, Date value2) {
            addCriterion("hasten_create_time between", value1, value2, "hastenCreateTime");
            return (Criteria) this;
        }

        public Criteria andHastenCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("hasten_create_time not between", value1, value2, "hastenCreateTime");
            return (Criteria) this;
        }

        public Criteria andReturnTicketIsNull() {
            addCriterion("return_ticket is null");
            return (Criteria) this;
        }

        public Criteria andReturnTicketIsNotNull() {
            addCriterion("return_ticket is not null");
            return (Criteria) this;
        }

        public Criteria andReturnTicketEqualTo(Integer value) {
            addCriterion("return_ticket =", value, "returnTicket");
            return (Criteria) this;
        }

        public Criteria andReturnTicketNotEqualTo(Integer value) {
            addCriterion("return_ticket <>", value, "returnTicket");
            return (Criteria) this;
        }

        public Criteria andReturnTicketGreaterThan(Integer value) {
            addCriterion("return_ticket >", value, "returnTicket");
            return (Criteria) this;
        }

        public Criteria andReturnTicketGreaterThanOrEqualTo(Integer value) {
            addCriterion("return_ticket >=", value, "returnTicket");
            return (Criteria) this;
        }

        public Criteria andReturnTicketLessThan(Integer value) {
            addCriterion("return_ticket <", value, "returnTicket");
            return (Criteria) this;
        }

        public Criteria andReturnTicketLessThanOrEqualTo(Integer value) {
            addCriterion("return_ticket <=", value, "returnTicket");
            return (Criteria) this;
        }

        public Criteria andReturnTicketIn(List<Integer> values) {
            addCriterion("return_ticket in", values, "returnTicket");
            return (Criteria) this;
        }

        public Criteria andReturnTicketNotIn(List<Integer> values) {
            addCriterion("return_ticket not in", values, "returnTicket");
            return (Criteria) this;
        }

        public Criteria andReturnTicketBetween(Integer value1, Integer value2) {
            addCriterion("return_ticket between", value1, value2, "returnTicket");
            return (Criteria) this;
        }

        public Criteria andReturnTicketNotBetween(Integer value1, Integer value2) {
            addCriterion("return_ticket not between", value1, value2, "returnTicket");
            return (Criteria) this;
        }

        public Criteria andReturnDownloadIsNull() {
            addCriterion("return_download is null");
            return (Criteria) this;
        }

        public Criteria andReturnDownloadIsNotNull() {
            addCriterion("return_download is not null");
            return (Criteria) this;
        }

        public Criteria andReturnDownloadEqualTo(Integer value) {
            addCriterion("return_download =", value, "returnDownload");
            return (Criteria) this;
        }

        public Criteria andReturnDownloadNotEqualTo(Integer value) {
            addCriterion("return_download <>", value, "returnDownload");
            return (Criteria) this;
        }

        public Criteria andReturnDownloadGreaterThan(Integer value) {
            addCriterion("return_download >", value, "returnDownload");
            return (Criteria) this;
        }

        public Criteria andReturnDownloadGreaterThanOrEqualTo(Integer value) {
            addCriterion("return_download >=", value, "returnDownload");
            return (Criteria) this;
        }

        public Criteria andReturnDownloadLessThan(Integer value) {
            addCriterion("return_download <", value, "returnDownload");
            return (Criteria) this;
        }

        public Criteria andReturnDownloadLessThanOrEqualTo(Integer value) {
            addCriterion("return_download <=", value, "returnDownload");
            return (Criteria) this;
        }

        public Criteria andReturnDownloadIn(List<Integer> values) {
            addCriterion("return_download in", values, "returnDownload");
            return (Criteria) this;
        }

        public Criteria andReturnDownloadNotIn(List<Integer> values) {
            addCriterion("return_download not in", values, "returnDownload");
            return (Criteria) this;
        }

        public Criteria andReturnDownloadBetween(Integer value1, Integer value2) {
            addCriterion("return_download between", value1, value2, "returnDownload");
            return (Criteria) this;
        }

        public Criteria andReturnDownloadNotBetween(Integer value1, Integer value2) {
            addCriterion("return_download not between", value1, value2, "returnDownload");
            return (Criteria) this;
        }

        public Criteria andTitleIsNull() {
            addCriterion("title is null");
            return (Criteria) this;
        }

        public Criteria andTitleIsNotNull() {
            addCriterion("title is not null");
            return (Criteria) this;
        }

        public Criteria andTitleEqualTo(String value) {
            addCriterion("title =", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotEqualTo(String value) {
            addCriterion("title <>", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThan(String value) {
            addCriterion("title >", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThanOrEqualTo(String value) {
            addCriterion("title >=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThan(String value) {
            addCriterion("title <", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThanOrEqualTo(String value) {
            addCriterion("title <=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLike(String value) {
            addCriterion("title like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotLike(String value) {
            addCriterion("title not like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleIn(List<String> values) {
            addCriterion("title in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotIn(List<String> values) {
            addCriterion("title not in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleBetween(String value1, String value2) {
            addCriterion("title between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotBetween(String value1, String value2) {
            addCriterion("title not between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusIsNull() {
            addCriterion("payment_status is null");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusIsNotNull() {
            addCriterion("payment_status is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusEqualTo(Integer value) {
            addCriterion("payment_status =", value, "paymentStatus");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusNotEqualTo(Integer value) {
            addCriterion("payment_status <>", value, "paymentStatus");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusGreaterThan(Integer value) {
            addCriterion("payment_status >", value, "paymentStatus");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("payment_status >=", value, "paymentStatus");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusLessThan(Integer value) {
            addCriterion("payment_status <", value, "paymentStatus");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusLessThanOrEqualTo(Integer value) {
            addCriterion("payment_status <=", value, "paymentStatus");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusIn(List<Integer> values) {
            addCriterion("payment_status in", values, "paymentStatus");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusNotIn(List<Integer> values) {
            addCriterion("payment_status not in", values, "paymentStatus");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusBetween(Integer value1, Integer value2) {
            addCriterion("payment_status between", value1, value2, "paymentStatus");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("payment_status not between", value1, value2, "paymentStatus");
            return (Criteria) this;
        }

        public Criteria andPaymentTimeIsNull() {
            addCriterion("payment_time is null");
            return (Criteria) this;
        }

        public Criteria andPaymentTimeIsNotNull() {
            addCriterion("payment_time is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentTimeEqualTo(Date value) {
            addCriterion("payment_time =", value, "paymentTime");
            return (Criteria) this;
        }

        public Criteria andPaymentTimeNotEqualTo(Date value) {
            addCriterion("payment_time <>", value, "paymentTime");
            return (Criteria) this;
        }

        public Criteria andPaymentTimeGreaterThan(Date value) {
            addCriterion("payment_time >", value, "paymentTime");
            return (Criteria) this;
        }

        public Criteria andPaymentTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("payment_time >=", value, "paymentTime");
            return (Criteria) this;
        }

        public Criteria andPaymentTimeLessThan(Date value) {
            addCriterion("payment_time <", value, "paymentTime");
            return (Criteria) this;
        }

        public Criteria andPaymentTimeLessThanOrEqualTo(Date value) {
            addCriterion("payment_time <=", value, "paymentTime");
            return (Criteria) this;
        }

        public Criteria andPaymentTimeIn(List<Date> values) {
            addCriterion("payment_time in", values, "paymentTime");
            return (Criteria) this;
        }

        public Criteria andPaymentTimeNotIn(List<Date> values) {
            addCriterion("payment_time not in", values, "paymentTime");
            return (Criteria) this;
        }

        public Criteria andPaymentTimeBetween(Date value1, Date value2) {
            addCriterion("payment_time between", value1, value2, "paymentTime");
            return (Criteria) this;
        }

        public Criteria andPaymentTimeNotBetween(Date value1, Date value2) {
            addCriterion("payment_time not between", value1, value2, "paymentTime");
            return (Criteria) this;
        }

        public Criteria andSnapContentIsNull() {
            addCriterion("snap_content is null");
            return (Criteria) this;
        }

        public Criteria andSnapContentIsNotNull() {
            addCriterion("snap_content is not null");
            return (Criteria) this;
        }

        public Criteria andSnapContentEqualTo(String value) {
            addCriterion("snap_content =", value, "snapContent");
            return (Criteria) this;
        }

        public Criteria andSnapContentNotEqualTo(String value) {
            addCriterion("snap_content <>", value, "snapContent");
            return (Criteria) this;
        }

        public Criteria andSnapContentGreaterThan(String value) {
            addCriterion("snap_content >", value, "snapContent");
            return (Criteria) this;
        }

        public Criteria andSnapContentGreaterThanOrEqualTo(String value) {
            addCriterion("snap_content >=", value, "snapContent");
            return (Criteria) this;
        }

        public Criteria andSnapContentLessThan(String value) {
            addCriterion("snap_content <", value, "snapContent");
            return (Criteria) this;
        }

        public Criteria andSnapContentLessThanOrEqualTo(String value) {
            addCriterion("snap_content <=", value, "snapContent");
            return (Criteria) this;
        }

        public Criteria andSnapContentLike(String value) {
            addCriterion("snap_content like", value, "snapContent");
            return (Criteria) this;
        }

        public Criteria andSnapContentNotLike(String value) {
            addCriterion("snap_content not like", value, "snapContent");
            return (Criteria) this;
        }

        public Criteria andSnapContentIn(List<String> values) {
            addCriterion("snap_content in", values, "snapContent");
            return (Criteria) this;
        }

        public Criteria andSnapContentNotIn(List<String> values) {
            addCriterion("snap_content not in", values, "snapContent");
            return (Criteria) this;
        }

        public Criteria andSnapContentBetween(String value1, String value2) {
            addCriterion("snap_content between", value1, value2, "snapContent");
            return (Criteria) this;
        }

        public Criteria andSnapContentNotBetween(String value1, String value2) {
            addCriterion("snap_content not between", value1, value2, "snapContent");
            return (Criteria) this;
        }

        public Criteria andReimburseOrderIdIsNull() {
            addCriterion("reimburse_order_id is null");
            return (Criteria) this;
        }

        public Criteria andReimburseOrderIdIsNotNull() {
            addCriterion("reimburse_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andReimburseOrderIdEqualTo(String value) {
            addCriterion("reimburse_order_id =", value, "reimburseOrderId");
            return (Criteria) this;
        }

        public Criteria andReimburseOrderIdNotEqualTo(String value) {
            addCriterion("reimburse_order_id <>", value, "reimburseOrderId");
            return (Criteria) this;
        }

        public Criteria andReimburseOrderIdGreaterThan(String value) {
            addCriterion("reimburse_order_id >", value, "reimburseOrderId");
            return (Criteria) this;
        }

        public Criteria andReimburseOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("reimburse_order_id >=", value, "reimburseOrderId");
            return (Criteria) this;
        }

        public Criteria andReimburseOrderIdLessThan(String value) {
            addCriterion("reimburse_order_id <", value, "reimburseOrderId");
            return (Criteria) this;
        }

        public Criteria andReimburseOrderIdLessThanOrEqualTo(String value) {
            addCriterion("reimburse_order_id <=", value, "reimburseOrderId");
            return (Criteria) this;
        }

        public Criteria andReimburseOrderIdLike(String value) {
            addCriterion("reimburse_order_id like", value, "reimburseOrderId");
            return (Criteria) this;
        }

        public Criteria andReimburseOrderIdNotLike(String value) {
            addCriterion("reimburse_order_id not like", value, "reimburseOrderId");
            return (Criteria) this;
        }

        public Criteria andReimburseOrderIdIn(List<String> values) {
            addCriterion("reimburse_order_id in", values, "reimburseOrderId");
            return (Criteria) this;
        }

        public Criteria andReimburseOrderIdNotIn(List<String> values) {
            addCriterion("reimburse_order_id not in", values, "reimburseOrderId");
            return (Criteria) this;
        }

        public Criteria andReimburseOrderIdBetween(String value1, String value2) {
            addCriterion("reimburse_order_id between", value1, value2, "reimburseOrderId");
            return (Criteria) this;
        }

        public Criteria andReimburseOrderIdNotBetween(String value1, String value2) {
            addCriterion("reimburse_order_id not between", value1, value2, "reimburseOrderId");
            return (Criteria) this;
        }

        public Criteria andPayApplyStatusIsNull() {
            addCriterion("pay_apply_status is null");
            return (Criteria) this;
        }

        public Criteria andPayApplyStatusIsNotNull() {
            addCriterion("pay_apply_status is not null");
            return (Criteria) this;
        }

        public Criteria andPayApplyStatusEqualTo(Integer value) {
            addCriterion("pay_apply_status =", value, "payApplyStatus");
            return (Criteria) this;
        }

        public Criteria andPayApplyStatusNotEqualTo(Integer value) {
            addCriterion("pay_apply_status <>", value, "payApplyStatus");
            return (Criteria) this;
        }

        public Criteria andPayApplyStatusGreaterThan(Integer value) {
            addCriterion("pay_apply_status >", value, "payApplyStatus");
            return (Criteria) this;
        }

        public Criteria andPayApplyStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("pay_apply_status >=", value, "payApplyStatus");
            return (Criteria) this;
        }

        public Criteria andPayApplyStatusLessThan(Integer value) {
            addCriterion("pay_apply_status <", value, "payApplyStatus");
            return (Criteria) this;
        }

        public Criteria andPayApplyStatusLessThanOrEqualTo(Integer value) {
            addCriterion("pay_apply_status <=", value, "payApplyStatus");
            return (Criteria) this;
        }

        public Criteria andPayApplyStatusIn(List<Integer> values) {
            addCriterion("pay_apply_status in", values, "payApplyStatus");
            return (Criteria) this;
        }

        public Criteria andPayApplyStatusNotIn(List<Integer> values) {
            addCriterion("pay_apply_status not in", values, "payApplyStatus");
            return (Criteria) this;
        }

        public Criteria andPayApplyStatusBetween(Integer value1, Integer value2) {
            addCriterion("pay_apply_status between", value1, value2, "payApplyStatus");
            return (Criteria) this;
        }

        public Criteria andPayApplyStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("pay_apply_status not between", value1, value2, "payApplyStatus");
            return (Criteria) this;
        }

        public Criteria andTaskInfoIsNull() {
            addCriterion("task_info is null");
            return (Criteria) this;
        }

        public Criteria andTaskInfoIsNotNull() {
            addCriterion("task_info is not null");
            return (Criteria) this;
        }

        public Criteria andTaskInfoEqualTo(String value) {
            addCriterion("task_info =", value, "taskInfo");
            return (Criteria) this;
        }

        public Criteria andTaskInfoNotEqualTo(String value) {
            addCriterion("task_info <>", value, "taskInfo");
            return (Criteria) this;
        }

        public Criteria andTaskInfoGreaterThan(String value) {
            addCriterion("task_info >", value, "taskInfo");
            return (Criteria) this;
        }

        public Criteria andTaskInfoGreaterThanOrEqualTo(String value) {
            addCriterion("task_info >=", value, "taskInfo");
            return (Criteria) this;
        }

        public Criteria andTaskInfoLessThan(String value) {
            addCriterion("task_info <", value, "taskInfo");
            return (Criteria) this;
        }

        public Criteria andTaskInfoLessThanOrEqualTo(String value) {
            addCriterion("task_info <=", value, "taskInfo");
            return (Criteria) this;
        }

        public Criteria andTaskInfoLike(String value) {
            addCriterion("task_info like", value, "taskInfo");
            return (Criteria) this;
        }

        public Criteria andTaskInfoNotLike(String value) {
            addCriterion("task_info not like", value, "taskInfo");
            return (Criteria) this;
        }

        public Criteria andTaskInfoIn(List<String> values) {
            addCriterion("task_info in", values, "taskInfo");
            return (Criteria) this;
        }

        public Criteria andTaskInfoNotIn(List<String> values) {
            addCriterion("task_info not in", values, "taskInfo");
            return (Criteria) this;
        }

        public Criteria andTaskInfoBetween(String value1, String value2) {
            addCriterion("task_info between", value1, value2, "taskInfo");
            return (Criteria) this;
        }

        public Criteria andTaskInfoNotBetween(String value1, String value2) {
            addCriterion("task_info not between", value1, value2, "taskInfo");
            return (Criteria) this;
        }

        public Criteria andSendStatusIsNull() {
            addCriterion("send_status is null");
            return (Criteria) this;
        }

        public Criteria andSendStatusIsNotNull() {
            addCriterion("send_status is not null");
            return (Criteria) this;
        }

        public Criteria andSendStatusEqualTo(Integer value) {
            addCriterion("send_status =", value, "sendStatus");
            return (Criteria) this;
        }

        public Criteria andSendStatusNotEqualTo(Integer value) {
            addCriterion("send_status <>", value, "sendStatus");
            return (Criteria) this;
        }

        public Criteria andSendStatusGreaterThan(Integer value) {
            addCriterion("send_status >", value, "sendStatus");
            return (Criteria) this;
        }

        public Criteria andSendStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("send_status >=", value, "sendStatus");
            return (Criteria) this;
        }

        public Criteria andSendStatusLessThan(Integer value) {
            addCriterion("send_status <", value, "sendStatus");
            return (Criteria) this;
        }

        public Criteria andSendStatusLessThanOrEqualTo(Integer value) {
            addCriterion("send_status <=", value, "sendStatus");
            return (Criteria) this;
        }

        public Criteria andSendStatusIn(List<Integer> values) {
            addCriterion("send_status in", values, "sendStatus");
            return (Criteria) this;
        }

        public Criteria andSendStatusNotIn(List<Integer> values) {
            addCriterion("send_status not in", values, "sendStatus");
            return (Criteria) this;
        }

        public Criteria andSendStatusBetween(Integer value1, Integer value2) {
            addCriterion("send_status between", value1, value2, "sendStatus");
            return (Criteria) this;
        }

        public Criteria andSendStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("send_status not between", value1, value2, "sendStatus");
            return (Criteria) this;
        }

        public Criteria andDeleteStatusIsNull() {
            addCriterion("delete_status is null");
            return (Criteria) this;
        }

        public Criteria andDeleteStatusIsNotNull() {
            addCriterion("delete_status is not null");
            return (Criteria) this;
        }

        public Criteria andDeleteStatusEqualTo(Integer value) {
            addCriterion("delete_status =", value, "deleteStatus");
            return (Criteria) this;
        }

        public Criteria andDeleteStatusNotEqualTo(Integer value) {
            addCriterion("delete_status <>", value, "deleteStatus");
            return (Criteria) this;
        }

        public Criteria andDeleteStatusGreaterThan(Integer value) {
            addCriterion("delete_status >", value, "deleteStatus");
            return (Criteria) this;
        }

        public Criteria andDeleteStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("delete_status >=", value, "deleteStatus");
            return (Criteria) this;
        }

        public Criteria andDeleteStatusLessThan(Integer value) {
            addCriterion("delete_status <", value, "deleteStatus");
            return (Criteria) this;
        }

        public Criteria andDeleteStatusLessThanOrEqualTo(Integer value) {
            addCriterion("delete_status <=", value, "deleteStatus");
            return (Criteria) this;
        }

        public Criteria andDeleteStatusIn(List<Integer> values) {
            addCriterion("delete_status in", values, "deleteStatus");
            return (Criteria) this;
        }

        public Criteria andDeleteStatusNotIn(List<Integer> values) {
            addCriterion("delete_status not in", values, "deleteStatus");
            return (Criteria) this;
        }

        public Criteria andDeleteStatusBetween(Integer value1, Integer value2) {
            addCriterion("delete_status between", value1, value2, "deleteStatus");
            return (Criteria) this;
        }

        public Criteria andDeleteStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("delete_status not between", value1, value2, "deleteStatus");
            return (Criteria) this;
        }

        public Criteria andFlowProcessTypeIsNull() {
            addCriterion("flow_process_type is null");
            return (Criteria) this;
        }

        public Criteria andFlowProcessTypeIsNotNull() {
            addCriterion("flow_process_type is not null");
            return (Criteria) this;
        }

        public Criteria andFlowProcessTypeEqualTo(Integer value) {
            addCriterion("flow_process_type =", value, "flowProcessType");
            return (Criteria) this;
        }

        public Criteria andFlowProcessTypeNotEqualTo(Integer value) {
            addCriterion("flow_process_type <>", value, "flowProcessType");
            return (Criteria) this;
        }

        public Criteria andFlowProcessTypeGreaterThan(Integer value) {
            addCriterion("flow_process_type >", value, "flowProcessType");
            return (Criteria) this;
        }

        public Criteria andFlowProcessTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("flow_process_type >=", value, "flowProcessType");
            return (Criteria) this;
        }

        public Criteria andFlowProcessTypeLessThan(Integer value) {
            addCriterion("flow_process_type <", value, "flowProcessType");
            return (Criteria) this;
        }

        public Criteria andFlowProcessTypeLessThanOrEqualTo(Integer value) {
            addCriterion("flow_process_type <=", value, "flowProcessType");
            return (Criteria) this;
        }

        public Criteria andFlowProcessTypeIn(List<Integer> values) {
            addCriterion("flow_process_type in", values, "flowProcessType");
            return (Criteria) this;
        }

        public Criteria andFlowProcessTypeNotIn(List<Integer> values) {
            addCriterion("flow_process_type not in", values, "flowProcessType");
            return (Criteria) this;
        }

        public Criteria andFlowProcessTypeBetween(Integer value1, Integer value2) {
            addCriterion("flow_process_type between", value1, value2, "flowProcessType");
            return (Criteria) this;
        }

        public Criteria andFlowProcessTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("flow_process_type not between", value1, value2, "flowProcessType");
            return (Criteria) this;
        }

        public Criteria andFlowProcessIdIsNull() {
            addCriterion("flow_process_id is null");
            return (Criteria) this;
        }

        public Criteria andFlowProcessIdIsNotNull() {
            addCriterion("flow_process_id is not null");
            return (Criteria) this;
        }

        public Criteria andFlowProcessIdEqualTo(String value) {
            addCriterion("flow_process_id =", value, "flowProcessId");
            return (Criteria) this;
        }

        public Criteria andFlowProcessIdNotEqualTo(String value) {
            addCriterion("flow_process_id <>", value, "flowProcessId");
            return (Criteria) this;
        }

        public Criteria andFlowProcessIdGreaterThan(String value) {
            addCriterion("flow_process_id >", value, "flowProcessId");
            return (Criteria) this;
        }

        public Criteria andFlowProcessIdGreaterThanOrEqualTo(String value) {
            addCriterion("flow_process_id >=", value, "flowProcessId");
            return (Criteria) this;
        }

        public Criteria andFlowProcessIdLessThan(String value) {
            addCriterion("flow_process_id <", value, "flowProcessId");
            return (Criteria) this;
        }

        public Criteria andFlowProcessIdLessThanOrEqualTo(String value) {
            addCriterion("flow_process_id <=", value, "flowProcessId");
            return (Criteria) this;
        }

        public Criteria andFlowProcessIdLike(String value) {
            addCriterion("flow_process_id like", value, "flowProcessId");
            return (Criteria) this;
        }

        public Criteria andFlowProcessIdNotLike(String value) {
            addCriterion("flow_process_id not like", value, "flowProcessId");
            return (Criteria) this;
        }

        public Criteria andFlowProcessIdIn(List<String> values) {
            addCriterion("flow_process_id in", values, "flowProcessId");
            return (Criteria) this;
        }

        public Criteria andFlowProcessIdNotIn(List<String> values) {
            addCriterion("flow_process_id not in", values, "flowProcessId");
            return (Criteria) this;
        }

        public Criteria andFlowProcessIdBetween(String value1, String value2) {
            addCriterion("flow_process_id between", value1, value2, "flowProcessId");
            return (Criteria) this;
        }

        public Criteria andFlowProcessIdNotBetween(String value1, String value2) {
            addCriterion("flow_process_id not between", value1, value2, "flowProcessId");
            return (Criteria) this;
        }

        public Criteria andClientTypeIsNull() {
            addCriterion("client_type is null");
            return (Criteria) this;
        }

        public Criteria andClientTypeIsNotNull() {
            addCriterion("client_type is not null");
            return (Criteria) this;
        }

        public Criteria andClientTypeEqualTo(String value) {
            addCriterion("client_type =", value, "clientType");
            return (Criteria) this;
        }

        public Criteria andClientTypeNotEqualTo(String value) {
            addCriterion("client_type <>", value, "clientType");
            return (Criteria) this;
        }

        public Criteria andClientTypeGreaterThan(String value) {
            addCriterion("client_type >", value, "clientType");
            return (Criteria) this;
        }

        public Criteria andClientTypeGreaterThanOrEqualTo(String value) {
            addCriterion("client_type >=", value, "clientType");
            return (Criteria) this;
        }

        public Criteria andClientTypeLessThan(String value) {
            addCriterion("client_type <", value, "clientType");
            return (Criteria) this;
        }

        public Criteria andClientTypeLessThanOrEqualTo(String value) {
            addCriterion("client_type <=", value, "clientType");
            return (Criteria) this;
        }

        public Criteria andClientTypeLike(String value) {
            addCriterion("client_type like", value, "clientType");
            return (Criteria) this;
        }

        public Criteria andClientTypeNotLike(String value) {
            addCriterion("client_type not like", value, "clientType");
            return (Criteria) this;
        }

        public Criteria andClientTypeIn(List<String> values) {
            addCriterion("client_type in", values, "clientType");
            return (Criteria) this;
        }

        public Criteria andClientTypeNotIn(List<String> values) {
            addCriterion("client_type not in", values, "clientType");
            return (Criteria) this;
        }

        public Criteria andClientTypeBetween(String value1, String value2) {
            addCriterion("client_type between", value1, value2, "clientType");
            return (Criteria) this;
        }

        public Criteria andClientTypeNotBetween(String value1, String value2) {
            addCriterion("client_type not between", value1, value2, "clientType");
            return (Criteria) this;
        }

        public Criteria andClientVersionIsNull() {
            addCriterion("client_version is null");
            return (Criteria) this;
        }

        public Criteria andClientVersionIsNotNull() {
            addCriterion("client_version is not null");
            return (Criteria) this;
        }

        public Criteria andClientVersionEqualTo(String value) {
            addCriterion("client_version =", value, "clientVersion");
            return (Criteria) this;
        }

        public Criteria andClientVersionNotEqualTo(String value) {
            addCriterion("client_version <>", value, "clientVersion");
            return (Criteria) this;
        }

        public Criteria andClientVersionGreaterThan(String value) {
            addCriterion("client_version >", value, "clientVersion");
            return (Criteria) this;
        }

        public Criteria andClientVersionGreaterThanOrEqualTo(String value) {
            addCriterion("client_version >=", value, "clientVersion");
            return (Criteria) this;
        }

        public Criteria andClientVersionLessThan(String value) {
            addCriterion("client_version <", value, "clientVersion");
            return (Criteria) this;
        }

        public Criteria andClientVersionLessThanOrEqualTo(String value) {
            addCriterion("client_version <=", value, "clientVersion");
            return (Criteria) this;
        }

        public Criteria andClientVersionLike(String value) {
            addCriterion("client_version like", value, "clientVersion");
            return (Criteria) this;
        }

        public Criteria andClientVersionNotLike(String value) {
            addCriterion("client_version not like", value, "clientVersion");
            return (Criteria) this;
        }

        public Criteria andClientVersionIn(List<String> values) {
            addCriterion("client_version in", values, "clientVersion");
            return (Criteria) this;
        }

        public Criteria andClientVersionNotIn(List<String> values) {
            addCriterion("client_version not in", values, "clientVersion");
            return (Criteria) this;
        }

        public Criteria andClientVersionBetween(String value1, String value2) {
            addCriterion("client_version between", value1, value2, "clientVersion");
            return (Criteria) this;
        }

        public Criteria andClientVersionNotBetween(String value1, String value2) {
            addCriterion("client_version not between", value1, value2, "clientVersion");
            return (Criteria) this;
        }

        public Criteria andCostAttributionCodeIsNull() {
            addCriterion("cost_attribution_code is null");
            return (Criteria) this;
        }

        public Criteria andCostAttributionCodeIsNotNull() {
            addCriterion("cost_attribution_code is not null");
            return (Criteria) this;
        }

        public Criteria andCostAttributionCodeEqualTo(String value) {
            addCriterion("cost_attribution_code =", value, "costAttributionCode");
            return (Criteria) this;
        }

        public Criteria andCostAttributionCodeNotEqualTo(String value) {
            addCriterion("cost_attribution_code <>", value, "costAttributionCode");
            return (Criteria) this;
        }

        public Criteria andCostAttributionCodeGreaterThan(String value) {
            addCriterion("cost_attribution_code >", value, "costAttributionCode");
            return (Criteria) this;
        }

        public Criteria andCostAttributionCodeGreaterThanOrEqualTo(String value) {
            addCriterion("cost_attribution_code >=", value, "costAttributionCode");
            return (Criteria) this;
        }

        public Criteria andCostAttributionCodeLessThan(String value) {
            addCriterion("cost_attribution_code <", value, "costAttributionCode");
            return (Criteria) this;
        }

        public Criteria andCostAttributionCodeLessThanOrEqualTo(String value) {
            addCriterion("cost_attribution_code <=", value, "costAttributionCode");
            return (Criteria) this;
        }

        public Criteria andCostAttributionCodeLike(String value) {
            addCriterion("cost_attribution_code like", value, "costAttributionCode");
            return (Criteria) this;
        }

        public Criteria andCostAttributionCodeNotLike(String value) {
            addCriterion("cost_attribution_code not like", value, "costAttributionCode");
            return (Criteria) this;
        }

        public Criteria andCostAttributionCodeIn(List<String> values) {
            addCriterion("cost_attribution_code in", values, "costAttributionCode");
            return (Criteria) this;
        }

        public Criteria andCostAttributionCodeNotIn(List<String> values) {
            addCriterion("cost_attribution_code not in", values, "costAttributionCode");
            return (Criteria) this;
        }

        public Criteria andCostAttributionCodeBetween(String value1, String value2) {
            addCriterion("cost_attribution_code between", value1, value2, "costAttributionCode");
            return (Criteria) this;
        }

        public Criteria andCostAttributionCodeNotBetween(String value1, String value2) {
            addCriterion("cost_attribution_code not between", value1, value2, "costAttributionCode");
            return (Criteria) this;
        }

        public Criteria andIsDingIsNull() {
            addCriterion("is_ding is null");
            return (Criteria) this;
        }

        public Criteria andIsDingIsNotNull() {
            addCriterion("is_ding is not null");
            return (Criteria) this;
        }

        public Criteria andIsDingEqualTo(Byte value) {
            addCriterion("is_ding =", value, "isDing");
            return (Criteria) this;
        }

        public Criteria andIsDingNotEqualTo(Byte value) {
            addCriterion("is_ding <>", value, "isDing");
            return (Criteria) this;
        }

        public Criteria andIsDingGreaterThan(Byte value) {
            addCriterion("is_ding >", value, "isDing");
            return (Criteria) this;
        }

        public Criteria andIsDingGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_ding >=", value, "isDing");
            return (Criteria) this;
        }

        public Criteria andIsDingLessThan(Byte value) {
            addCriterion("is_ding <", value, "isDing");
            return (Criteria) this;
        }

        public Criteria andIsDingLessThanOrEqualTo(Byte value) {
            addCriterion("is_ding <=", value, "isDing");
            return (Criteria) this;
        }

        public Criteria andIsDingIn(List<Byte> values) {
            addCriterion("is_ding in", values, "isDing");
            return (Criteria) this;
        }

        public Criteria andIsDingNotIn(List<Byte> values) {
            addCriterion("is_ding not in", values, "isDing");
            return (Criteria) this;
        }

        public Criteria andIsDingBetween(Byte value1, Byte value2) {
            addCriterion("is_ding between", value1, value2, "isDing");
            return (Criteria) this;
        }

        public Criteria andIsDingNotBetween(Byte value1, Byte value2) {
            addCriterion("is_ding not between", value1, value2, "isDing");
            return (Criteria) this;
        }

        public Criteria andMetaInfoIsNull() {
            addCriterion("meta_info is null");
            return (Criteria) this;
        }

        public Criteria andMetaInfoIsNotNull() {
            addCriterion("meta_info is not null");
            return (Criteria) this;
        }

        public Criteria andMetaInfoEqualTo(String value) {
            addCriterion("meta_info =", value, "metaInfo");
            return (Criteria) this;
        }

        public Criteria andMetaInfoNotEqualTo(String value) {
            addCriterion("meta_info <>", value, "metaInfo");
            return (Criteria) this;
        }

        public Criteria andMetaInfoGreaterThan(String value) {
            addCriterion("meta_info >", value, "metaInfo");
            return (Criteria) this;
        }

        public Criteria andMetaInfoGreaterThanOrEqualTo(String value) {
            addCriterion("meta_info >=", value, "metaInfo");
            return (Criteria) this;
        }

        public Criteria andMetaInfoLessThan(String value) {
            addCriterion("meta_info <", value, "metaInfo");
            return (Criteria) this;
        }

        public Criteria andMetaInfoLessThanOrEqualTo(String value) {
            addCriterion("meta_info <=", value, "metaInfo");
            return (Criteria) this;
        }

        public Criteria andMetaInfoLike(String value) {
            addCriterion("meta_info like", value, "metaInfo");
            return (Criteria) this;
        }

        public Criteria andMetaInfoNotLike(String value) {
            addCriterion("meta_info not like", value, "metaInfo");
            return (Criteria) this;
        }

        public Criteria andMetaInfoIn(List<String> values) {
            addCriterion("meta_info in", values, "metaInfo");
            return (Criteria) this;
        }

        public Criteria andMetaInfoNotIn(List<String> values) {
            addCriterion("meta_info not in", values, "metaInfo");
            return (Criteria) this;
        }

        public Criteria andMetaInfoBetween(String value1, String value2) {
            addCriterion("meta_info between", value1, value2, "metaInfo");
            return (Criteria) this;
        }

        public Criteria andMetaInfoNotBetween(String value1, String value2) {
            addCriterion("meta_info not between", value1, value2, "metaInfo");
            return (Criteria) this;
        }

        public Criteria andFormIdIsNull() {
            addCriterion("form_id is null");
            return (Criteria) this;
        }

        public Criteria andFormIdIsNotNull() {
            addCriterion("form_id is not null");
            return (Criteria) this;
        }

        public Criteria andFormIdEqualTo(String value) {
            addCriterion("form_id =", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotEqualTo(String value) {
            addCriterion("form_id <>", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdGreaterThan(String value) {
            addCriterion("form_id >", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdGreaterThanOrEqualTo(String value) {
            addCriterion("form_id >=", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdLessThan(String value) {
            addCriterion("form_id <", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdLessThanOrEqualTo(String value) {
            addCriterion("form_id <=", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdLike(String value) {
            addCriterion("form_id like", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotLike(String value) {
            addCriterion("form_id not like", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdIn(List<String> values) {
            addCriterion("form_id in", values, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotIn(List<String> values) {
            addCriterion("form_id not in", values, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdBetween(String value1, String value2) {
            addCriterion("form_id between", value1, value2, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotBetween(String value1, String value2) {
            addCriterion("form_id not between", value1, value2, "formId");
            return (Criteria) this;
        }

        public Criteria andCreateParamSnapshotIsNull() {
            addCriterion("create_param_snapshot is null");
            return (Criteria) this;
        }

        public Criteria andCreateParamSnapshotIsNotNull() {
            addCriterion("create_param_snapshot is not null");
            return (Criteria) this;
        }

        public Criteria andCreateParamSnapshotEqualTo(String value) {
            addCriterion("create_param_snapshot =", value, "createParamSnapshot");
            return (Criteria) this;
        }

        public Criteria andCreateParamSnapshotNotEqualTo(String value) {
            addCriterion("create_param_snapshot <>", value, "createParamSnapshot");
            return (Criteria) this;
        }

        public Criteria andCreateParamSnapshotGreaterThan(String value) {
            addCriterion("create_param_snapshot >", value, "createParamSnapshot");
            return (Criteria) this;
        }

        public Criteria andCreateParamSnapshotGreaterThanOrEqualTo(String value) {
            addCriterion("create_param_snapshot >=", value, "createParamSnapshot");
            return (Criteria) this;
        }

        public Criteria andCreateParamSnapshotLessThan(String value) {
            addCriterion("create_param_snapshot <", value, "createParamSnapshot");
            return (Criteria) this;
        }

        public Criteria andCreateParamSnapshotLessThanOrEqualTo(String value) {
            addCriterion("create_param_snapshot <=", value, "createParamSnapshot");
            return (Criteria) this;
        }

        public Criteria andCreateParamSnapshotLike(String value) {
            addCriterion("create_param_snapshot like", value, "createParamSnapshot");
            return (Criteria) this;
        }

        public Criteria andCreateParamSnapshotNotLike(String value) {
            addCriterion("create_param_snapshot not like", value, "createParamSnapshot");
            return (Criteria) this;
        }

        public Criteria andCreateParamSnapshotIn(List<String> values) {
            addCriterion("create_param_snapshot in", values, "createParamSnapshot");
            return (Criteria) this;
        }

        public Criteria andCreateParamSnapshotNotIn(List<String> values) {
            addCriterion("create_param_snapshot not in", values, "createParamSnapshot");
            return (Criteria) this;
        }

        public Criteria andCreateParamSnapshotBetween(String value1, String value2) {
            addCriterion("create_param_snapshot between", value1, value2, "createParamSnapshot");
            return (Criteria) this;
        }

        public Criteria andCreateParamSnapshotNotBetween(String value1, String value2) {
            addCriterion("create_param_snapshot not between", value1, value2, "createParamSnapshot");
            return (Criteria) this;
        }

        public Criteria andThridRelationNoIsNull() {
            addCriterion("thrid_relation_no is null");
            return (Criteria) this;
        }

        public Criteria andThridRelationNoIsNotNull() {
            addCriterion("thrid_relation_no is not null");
            return (Criteria) this;
        }

        public Criteria andThridRelationNoEqualTo(String value) {
            addCriterion("thrid_relation_no =", value, "thridRelationNo");
            return (Criteria) this;
        }

        public Criteria andThridRelationNoNotEqualTo(String value) {
            addCriterion("thrid_relation_no <>", value, "thridRelationNo");
            return (Criteria) this;
        }

        public Criteria andThridRelationNoGreaterThan(String value) {
            addCriterion("thrid_relation_no >", value, "thridRelationNo");
            return (Criteria) this;
        }

        public Criteria andThridRelationNoGreaterThanOrEqualTo(String value) {
            addCriterion("thrid_relation_no >=", value, "thridRelationNo");
            return (Criteria) this;
        }

        public Criteria andThridRelationNoLessThan(String value) {
            addCriterion("thrid_relation_no <", value, "thridRelationNo");
            return (Criteria) this;
        }

        public Criteria andThridRelationNoLessThanOrEqualTo(String value) {
            addCriterion("thrid_relation_no <=", value, "thridRelationNo");
            return (Criteria) this;
        }

        public Criteria andThridRelationNoLike(String value) {
            addCriterion("thrid_relation_no like", value, "thridRelationNo");
            return (Criteria) this;
        }

        public Criteria andThridRelationNoNotLike(String value) {
            addCriterion("thrid_relation_no not like", value, "thridRelationNo");
            return (Criteria) this;
        }

        public Criteria andThridRelationNoIn(List<String> values) {
            addCriterion("thrid_relation_no in", values, "thridRelationNo");
            return (Criteria) this;
        }

        public Criteria andThridRelationNoNotIn(List<String> values) {
            addCriterion("thrid_relation_no not in", values, "thridRelationNo");
            return (Criteria) this;
        }

        public Criteria andThridRelationNoBetween(String value1, String value2) {
            addCriterion("thrid_relation_no between", value1, value2, "thridRelationNo");
            return (Criteria) this;
        }

        public Criteria andThridRelationNoNotBetween(String value1, String value2) {
            addCriterion("thrid_relation_no not between", value1, value2, "thridRelationNo");
            return (Criteria) this;
        }

        public Criteria andCreateVersionIsNull() {
            addCriterion("create_version is null");
            return (Criteria) this;
        }

        public Criteria andCreateVersionIsNotNull() {
            addCriterion("create_version is not null");
            return (Criteria) this;
        }

        public Criteria andCreateVersionEqualTo(Integer value) {
            addCriterion("create_version =", value, "createVersion");
            return (Criteria) this;
        }

        public Criteria andCreateVersionNotEqualTo(Integer value) {
            addCriterion("create_version <>", value, "createVersion");
            return (Criteria) this;
        }

        public Criteria andCreateVersionGreaterThan(Integer value) {
            addCriterion("create_version >", value, "createVersion");
            return (Criteria) this;
        }

        public Criteria andCreateVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("create_version >=", value, "createVersion");
            return (Criteria) this;
        }

        public Criteria andCreateVersionLessThan(Integer value) {
            addCriterion("create_version <", value, "createVersion");
            return (Criteria) this;
        }

        public Criteria andCreateVersionLessThanOrEqualTo(Integer value) {
            addCriterion("create_version <=", value, "createVersion");
            return (Criteria) this;
        }

        public Criteria andCreateVersionIn(List<Integer> values) {
            addCriterion("create_version in", values, "createVersion");
            return (Criteria) this;
        }

        public Criteria andCreateVersionNotIn(List<Integer> values) {
            addCriterion("create_version not in", values, "createVersion");
            return (Criteria) this;
        }

        public Criteria andCreateVersionBetween(Integer value1, Integer value2) {
            addCriterion("create_version between", value1, value2, "createVersion");
            return (Criteria) this;
        }

        public Criteria andCreateVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("create_version not between", value1, value2, "createVersion");
            return (Criteria) this;
        }

        public Criteria andIsAssociateIsNull() {
            addCriterion("is_associate is null");
            return (Criteria) this;
        }

        public Criteria andIsAssociateIsNotNull() {
            addCriterion("is_associate is not null");
            return (Criteria) this;
        }

        public Criteria andIsAssociateEqualTo(Boolean value) {
            addCriterion("is_associate =", value, "isAssociate");
            return (Criteria) this;
        }

        public Criteria andIsAssociateNotEqualTo(Boolean value) {
            addCriterion("is_associate <>", value, "isAssociate");
            return (Criteria) this;
        }

        public Criteria andIsAssociateGreaterThan(Boolean value) {
            addCriterion("is_associate >", value, "isAssociate");
            return (Criteria) this;
        }

        public Criteria andIsAssociateGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_associate >=", value, "isAssociate");
            return (Criteria) this;
        }

        public Criteria andIsAssociateLessThan(Boolean value) {
            addCriterion("is_associate <", value, "isAssociate");
            return (Criteria) this;
        }

        public Criteria andIsAssociateLessThanOrEqualTo(Boolean value) {
            addCriterion("is_associate <=", value, "isAssociate");
            return (Criteria) this;
        }

        public Criteria andIsAssociateIn(List<Boolean> values) {
            addCriterion("is_associate in", values, "isAssociate");
            return (Criteria) this;
        }

        public Criteria andIsAssociateNotIn(List<Boolean> values) {
            addCriterion("is_associate not in", values, "isAssociate");
            return (Criteria) this;
        }

        public Criteria andIsAssociateBetween(Boolean value1, Boolean value2) {
            addCriterion("is_associate between", value1, value2, "isAssociate");
            return (Criteria) this;
        }

        public Criteria andIsAssociateNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_associate not between", value1, value2, "isAssociate");
            return (Criteria) this;
        }

        public Criteria andReturnDateIsNull() {
            addCriterion("return_date is null");
            return (Criteria) this;
        }

        public Criteria andReturnDateIsNotNull() {
            addCriterion("return_date is not null");
            return (Criteria) this;
        }

        public Criteria andReturnDateEqualTo(Date value) {
            addCriterion("return_date =", value, "returnDate");
            return (Criteria) this;
        }

        public Criteria andReturnDateNotEqualTo(Date value) {
            addCriterion("return_date <>", value, "returnDate");
            return (Criteria) this;
        }

        public Criteria andReturnDateGreaterThan(Date value) {
            addCriterion("return_date >", value, "returnDate");
            return (Criteria) this;
        }

        public Criteria andReturnDateGreaterThanOrEqualTo(Date value) {
            addCriterion("return_date >=", value, "returnDate");
            return (Criteria) this;
        }

        public Criteria andReturnDateLessThan(Date value) {
            addCriterion("return_date <", value, "returnDate");
            return (Criteria) this;
        }

        public Criteria andReturnDateLessThanOrEqualTo(Date value) {
            addCriterion("return_date <=", value, "returnDate");
            return (Criteria) this;
        }

        public Criteria andReturnDateIn(List<Date> values) {
            addCriterion("return_date in", values, "returnDate");
            return (Criteria) this;
        }

        public Criteria andReturnDateNotIn(List<Date> values) {
            addCriterion("return_date not in", values, "returnDate");
            return (Criteria) this;
        }

        public Criteria andReturnDateBetween(Date value1, Date value2) {
            addCriterion("return_date between", value1, value2, "returnDate");
            return (Criteria) this;
        }

        public Criteria andReturnDateNotBetween(Date value1, Date value2) {
            addCriterion("return_date not between", value1, value2, "returnDate");
            return (Criteria) this;
        }

        public Criteria andInvoiceStateIsNull() {
            addCriterion("invoice_state is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceStateIsNotNull() {
            addCriterion("invoice_state is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceStateEqualTo(Integer value) {
            addCriterion("invoice_state =", value, "invoiceState");
            return (Criteria) this;
        }

        public Criteria andInvoiceStateNotEqualTo(Integer value) {
            addCriterion("invoice_state <>", value, "invoiceState");
            return (Criteria) this;
        }

        public Criteria andInvoiceStateGreaterThan(Integer value) {
            addCriterion("invoice_state >", value, "invoiceState");
            return (Criteria) this;
        }

        public Criteria andInvoiceStateGreaterThanOrEqualTo(Integer value) {
            addCriterion("invoice_state >=", value, "invoiceState");
            return (Criteria) this;
        }

        public Criteria andInvoiceStateLessThan(Integer value) {
            addCriterion("invoice_state <", value, "invoiceState");
            return (Criteria) this;
        }

        public Criteria andInvoiceStateLessThanOrEqualTo(Integer value) {
            addCriterion("invoice_state <=", value, "invoiceState");
            return (Criteria) this;
        }

        public Criteria andInvoiceStateIn(List<Integer> values) {
            addCriterion("invoice_state in", values, "invoiceState");
            return (Criteria) this;
        }

        public Criteria andInvoiceStateNotIn(List<Integer> values) {
            addCriterion("invoice_state not in", values, "invoiceState");
            return (Criteria) this;
        }

        public Criteria andInvoiceStateBetween(Integer value1, Integer value2) {
            addCriterion("invoice_state between", value1, value2, "invoiceState");
            return (Criteria) this;
        }

        public Criteria andInvoiceStateNotBetween(Integer value1, Integer value2) {
            addCriterion("invoice_state not between", value1, value2, "invoiceState");
            return (Criteria) this;
        }

        public Criteria andInvoiceReminderIsNull() {
            addCriterion("invoice_reminder is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceReminderIsNotNull() {
            addCriterion("invoice_reminder is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceReminderEqualTo(Boolean value) {
            addCriterion("invoice_reminder =", value, "invoiceReminder");
            return (Criteria) this;
        }

        public Criteria andInvoiceReminderNotEqualTo(Boolean value) {
            addCriterion("invoice_reminder <>", value, "invoiceReminder");
            return (Criteria) this;
        }

        public Criteria andInvoiceReminderGreaterThan(Boolean value) {
            addCriterion("invoice_reminder >", value, "invoiceReminder");
            return (Criteria) this;
        }

        public Criteria andInvoiceReminderGreaterThanOrEqualTo(Boolean value) {
            addCriterion("invoice_reminder >=", value, "invoiceReminder");
            return (Criteria) this;
        }

        public Criteria andInvoiceReminderLessThan(Boolean value) {
            addCriterion("invoice_reminder <", value, "invoiceReminder");
            return (Criteria) this;
        }

        public Criteria andInvoiceReminderLessThanOrEqualTo(Boolean value) {
            addCriterion("invoice_reminder <=", value, "invoiceReminder");
            return (Criteria) this;
        }

        public Criteria andInvoiceReminderIn(List<Boolean> values) {
            addCriterion("invoice_reminder in", values, "invoiceReminder");
            return (Criteria) this;
        }

        public Criteria andInvoiceReminderNotIn(List<Boolean> values) {
            addCriterion("invoice_reminder not in", values, "invoiceReminder");
            return (Criteria) this;
        }

        public Criteria andInvoiceReminderBetween(Boolean value1, Boolean value2) {
            addCriterion("invoice_reminder between", value1, value2, "invoiceReminder");
            return (Criteria) this;
        }

        public Criteria andInvoiceReminderNotBetween(Boolean value1, Boolean value2) {
            addCriterion("invoice_reminder not between", value1, value2, "invoiceReminder");
            return (Criteria) this;
        }

        public Criteria andFinalApproveTimeIsNull() {
            addCriterion("final_approve_time is null");
            return (Criteria) this;
        }

        public Criteria andFinalApproveTimeIsNotNull() {
            addCriterion("final_approve_time is not null");
            return (Criteria) this;
        }

        public Criteria andFinalApproveTimeEqualTo(Date value) {
            addCriterion("final_approve_time =", value, "finalApproveTime");
            return (Criteria) this;
        }

        public Criteria andFinalApproveTimeNotEqualTo(Date value) {
            addCriterion("final_approve_time <>", value, "finalApproveTime");
            return (Criteria) this;
        }

        public Criteria andFinalApproveTimeGreaterThan(Date value) {
            addCriterion("final_approve_time >", value, "finalApproveTime");
            return (Criteria) this;
        }

        public Criteria andFinalApproveTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("final_approve_time >=", value, "finalApproveTime");
            return (Criteria) this;
        }

        public Criteria andFinalApproveTimeLessThan(Date value) {
            addCriterion("final_approve_time <", value, "finalApproveTime");
            return (Criteria) this;
        }

        public Criteria andFinalApproveTimeLessThanOrEqualTo(Date value) {
            addCriterion("final_approve_time <=", value, "finalApproveTime");
            return (Criteria) this;
        }

        public Criteria andFinalApproveTimeIn(List<Date> values) {
            addCriterion("final_approve_time in", values, "finalApproveTime");
            return (Criteria) this;
        }

        public Criteria andFinalApproveTimeNotIn(List<Date> values) {
            addCriterion("final_approve_time not in", values, "finalApproveTime");
            return (Criteria) this;
        }

        public Criteria andFinalApproveTimeBetween(Date value1, Date value2) {
            addCriterion("final_approve_time between", value1, value2, "finalApproveTime");
            return (Criteria) this;
        }

        public Criteria andFinalApproveTimeNotBetween(Date value1, Date value2) {
            addCriterion("final_approve_time not between", value1, value2, "finalApproveTime");
            return (Criteria) this;
        }

        public Criteria andRelationApplyIdIsNull() {
            addCriterion("relation_apply_id is null");
            return (Criteria) this;
        }

        public Criteria andRelationApplyIdIsNotNull() {
            addCriterion("relation_apply_id is not null");
            return (Criteria) this;
        }

        public Criteria andRelationApplyIdEqualTo(String value) {
            addCriterion("relation_apply_id =", value, "relationApplyId");
            return (Criteria) this;
        }

        public Criteria andRelationApplyIdNotEqualTo(String value) {
            addCriterion("relation_apply_id <>", value, "relationApplyId");
            return (Criteria) this;
        }

        public Criteria andRelationApplyIdGreaterThan(String value) {
            addCriterion("relation_apply_id >", value, "relationApplyId");
            return (Criteria) this;
        }

        public Criteria andRelationApplyIdGreaterThanOrEqualTo(String value) {
            addCriterion("relation_apply_id >=", value, "relationApplyId");
            return (Criteria) this;
        }

        public Criteria andRelationApplyIdLessThan(String value) {
            addCriterion("relation_apply_id <", value, "relationApplyId");
            return (Criteria) this;
        }

        public Criteria andRelationApplyIdLessThanOrEqualTo(String value) {
            addCriterion("relation_apply_id <=", value, "relationApplyId");
            return (Criteria) this;
        }

        public Criteria andRelationApplyIdLike(String value) {
            addCriterion("relation_apply_id like", value, "relationApplyId");
            return (Criteria) this;
        }

        public Criteria andRelationApplyIdNotLike(String value) {
            addCriterion("relation_apply_id not like", value, "relationApplyId");
            return (Criteria) this;
        }

        public Criteria andRelationApplyIdIn(List<String> values) {
            addCriterion("relation_apply_id in", values, "relationApplyId");
            return (Criteria) this;
        }

        public Criteria andRelationApplyIdNotIn(List<String> values) {
            addCriterion("relation_apply_id not in", values, "relationApplyId");
            return (Criteria) this;
        }

        public Criteria andRelationApplyIdBetween(String value1, String value2) {
            addCriterion("relation_apply_id between", value1, value2, "relationApplyId");
            return (Criteria) this;
        }

        public Criteria andRelationApplyIdNotBetween(String value1, String value2) {
            addCriterion("relation_apply_id not between", value1, value2, "relationApplyId");
            return (Criteria) this;
        }

        public Criteria andInvoicePdfUrlIsNull() {
            addCriterion("invoice_pdf_url is null");
            return (Criteria) this;
        }

        public Criteria andInvoicePdfUrlIsNotNull() {
            addCriterion("invoice_pdf_url is not null");
            return (Criteria) this;
        }

        public Criteria andInvoicePdfUrlEqualTo(String value) {
            addCriterion("invoice_pdf_url =", value, "invoicePdfUrl");
            return (Criteria) this;
        }

        public Criteria andInvoicePdfUrlNotEqualTo(String value) {
            addCriterion("invoice_pdf_url <>", value, "invoicePdfUrl");
            return (Criteria) this;
        }

        public Criteria andInvoicePdfUrlGreaterThan(String value) {
            addCriterion("invoice_pdf_url >", value, "invoicePdfUrl");
            return (Criteria) this;
        }

        public Criteria andInvoicePdfUrlGreaterThanOrEqualTo(String value) {
            addCriterion("invoice_pdf_url >=", value, "invoicePdfUrl");
            return (Criteria) this;
        }

        public Criteria andInvoicePdfUrlLessThan(String value) {
            addCriterion("invoice_pdf_url <", value, "invoicePdfUrl");
            return (Criteria) this;
        }

        public Criteria andInvoicePdfUrlLessThanOrEqualTo(String value) {
            addCriterion("invoice_pdf_url <=", value, "invoicePdfUrl");
            return (Criteria) this;
        }

        public Criteria andInvoicePdfUrlLike(String value) {
            addCriterion("invoice_pdf_url like", value, "invoicePdfUrl");
            return (Criteria) this;
        }

        public Criteria andInvoicePdfUrlNotLike(String value) {
            addCriterion("invoice_pdf_url not like", value, "invoicePdfUrl");
            return (Criteria) this;
        }

        public Criteria andInvoicePdfUrlIn(List<String> values) {
            addCriterion("invoice_pdf_url in", values, "invoicePdfUrl");
            return (Criteria) this;
        }

        public Criteria andInvoicePdfUrlNotIn(List<String> values) {
            addCriterion("invoice_pdf_url not in", values, "invoicePdfUrl");
            return (Criteria) this;
        }

        public Criteria andInvoicePdfUrlBetween(String value1, String value2) {
            addCriterion("invoice_pdf_url between", value1, value2, "invoicePdfUrl");
            return (Criteria) this;
        }

        public Criteria andInvoicePdfUrlNotBetween(String value1, String value2) {
            addCriterion("invoice_pdf_url not between", value1, value2, "invoicePdfUrl");
            return (Criteria) this;
        }

        public Criteria andApplyPdfUrlIsNull() {
            addCriterion("apply_pdf_url is null");
            return (Criteria) this;
        }

        public Criteria andApplyPdfUrlIsNotNull() {
            addCriterion("apply_pdf_url is not null");
            return (Criteria) this;
        }

        public Criteria andApplyPdfUrlEqualTo(String value) {
            addCriterion("apply_pdf_url =", value, "applyPdfUrl");
            return (Criteria) this;
        }

        public Criteria andApplyPdfUrlNotEqualTo(String value) {
            addCriterion("apply_pdf_url <>", value, "applyPdfUrl");
            return (Criteria) this;
        }

        public Criteria andApplyPdfUrlGreaterThan(String value) {
            addCriterion("apply_pdf_url >", value, "applyPdfUrl");
            return (Criteria) this;
        }

        public Criteria andApplyPdfUrlGreaterThanOrEqualTo(String value) {
            addCriterion("apply_pdf_url >=", value, "applyPdfUrl");
            return (Criteria) this;
        }

        public Criteria andApplyPdfUrlLessThan(String value) {
            addCriterion("apply_pdf_url <", value, "applyPdfUrl");
            return (Criteria) this;
        }

        public Criteria andApplyPdfUrlLessThanOrEqualTo(String value) {
            addCriterion("apply_pdf_url <=", value, "applyPdfUrl");
            return (Criteria) this;
        }

        public Criteria andApplyPdfUrlLike(String value) {
            addCriterion("apply_pdf_url like", value, "applyPdfUrl");
            return (Criteria) this;
        }

        public Criteria andApplyPdfUrlNotLike(String value) {
            addCriterion("apply_pdf_url not like", value, "applyPdfUrl");
            return (Criteria) this;
        }

        public Criteria andApplyPdfUrlIn(List<String> values) {
            addCriterion("apply_pdf_url in", values, "applyPdfUrl");
            return (Criteria) this;
        }

        public Criteria andApplyPdfUrlNotIn(List<String> values) {
            addCriterion("apply_pdf_url not in", values, "applyPdfUrl");
            return (Criteria) this;
        }

        public Criteria andApplyPdfUrlBetween(String value1, String value2) {
            addCriterion("apply_pdf_url between", value1, value2, "applyPdfUrl");
            return (Criteria) this;
        }

        public Criteria andApplyPdfUrlNotBetween(String value1, String value2) {
            addCriterion("apply_pdf_url not between", value1, value2, "applyPdfUrl");
            return (Criteria) this;
        }

        public Criteria andMeaningNoIsNull() {
            addCriterion("meaning_no is null");
            return (Criteria) this;
        }

        public Criteria andMeaningNoIsNotNull() {
            addCriterion("meaning_no is not null");
            return (Criteria) this;
        }

        public Criteria andMeaningNoEqualTo(String value) {
            addCriterion("meaning_no =", value, "meaningNo");
            return (Criteria) this;
        }

        public Criteria andMeaningNoNotEqualTo(String value) {
            addCriterion("meaning_no <>", value, "meaningNo");
            return (Criteria) this;
        }

        public Criteria andMeaningNoGreaterThan(String value) {
            addCriterion("meaning_no >", value, "meaningNo");
            return (Criteria) this;
        }

        public Criteria andMeaningNoGreaterThanOrEqualTo(String value) {
            addCriterion("meaning_no >=", value, "meaningNo");
            return (Criteria) this;
        }

        public Criteria andMeaningNoLessThan(String value) {
            addCriterion("meaning_no <", value, "meaningNo");
            return (Criteria) this;
        }

        public Criteria andMeaningNoLessThanOrEqualTo(String value) {
            addCriterion("meaning_no <=", value, "meaningNo");
            return (Criteria) this;
        }

        public Criteria andMeaningNoLike(String value) {
            addCriterion("meaning_no like", value, "meaningNo");
            return (Criteria) this;
        }

        public Criteria andMeaningNoNotLike(String value) {
            addCriterion("meaning_no not like", value, "meaningNo");
            return (Criteria) this;
        }

        public Criteria andMeaningNoIn(List<String> values) {
            addCriterion("meaning_no in", values, "meaningNo");
            return (Criteria) this;
        }

        public Criteria andMeaningNoNotIn(List<String> values) {
            addCriterion("meaning_no not in", values, "meaningNo");
            return (Criteria) this;
        }

        public Criteria andMeaningNoBetween(String value1, String value2) {
            addCriterion("meaning_no between", value1, value2, "meaningNo");
            return (Criteria) this;
        }

        public Criteria andMeaningNoNotBetween(String value1, String value2) {
            addCriterion("meaning_no not between", value1, value2, "meaningNo");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table apply_order
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table apply_order
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}