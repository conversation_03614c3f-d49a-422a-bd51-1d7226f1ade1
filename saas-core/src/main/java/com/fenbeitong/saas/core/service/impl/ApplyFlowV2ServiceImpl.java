package com.fenbeitong.saas.core.service.impl;

import com.fenbeitong.finhub.common.constant.LogOperateActionEnum;
import com.fenbeitong.finhub.common.constant.LogOperateObjectEnum;
import com.fenbeitong.finhub.common.constant.LogOperatePageEnum;
import com.fenbeitong.finhub.kafka.msg.uc.KafkaCompanyLogMsg;
import com.fenbeitong.saas.core.common.GetQueryEmployeeListInfo;
import com.fenbeitong.saas.core.common.SaasHttpContext;
import com.fenbeitong.saas.core.common.base.SaasException;
import com.fenbeitong.saas.core.common.constant.GlobalResponseCode;
import com.fenbeitong.saas.core.common.constant.SaasApplyConstant;
import com.fenbeitong.saas.core.common.constant.SaasFlowConstant;
import com.fenbeitong.saas.core.contract.applyflow.*;
import com.fenbeitong.saas.core.contract.common.IdNameContract;
import com.fenbeitong.saas.core.contract.common.PageDataBaseContract;
import com.fenbeitong.saas.core.contract.user.UserInfoContract;
import com.fenbeitong.saas.core.dao.common.adapter.ApplyAdapterMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.OrgUnitMapper;
import com.fenbeitong.saas.core.dao.saas.*;
import com.fenbeitong.saas.core.language.CoreLanguage;
import com.fenbeitong.saas.core.model.enums.*;
import com.fenbeitong.saas.core.model.enums.apply.*;
import com.fenbeitong.saas.core.model.enums.user.UserRole;
import com.fenbeitong.saas.core.model.fenbeitong.OrgUnit;
import com.fenbeitong.saas.core.model.saas.*;
import com.fenbeitong.saas.core.service.*;
import com.fenbeitong.saas.core.service.kafka.CompanyLogKafkaProducerService;
import com.fenbeitong.saas.core.utils.tools.CompanyLogMsgUtil;
import com.fenbeitong.saas.core.utils.tools.IDTool;
import com.fenbeitong.saas.core.utils.tools.StringTool;
import com.fenbeitong.saas.core.utils.tools.VersionTool;
import com.fenbeitong.usercenter.api.model.dto.common.CommonIdAndNameDTO;
import com.fenbeitong.usercenter.api.model.dto.costcenter.CostInfoContract;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeDepartmentContract;
import com.fenbeitong.usercenter.api.model.dto.orgunit.ManagerDTO;
import com.fenbeitong.usercenter.api.model.dto.orgunit.ManagerListDTO;
import com.fenbeitong.usercenter.api.model.dto.orgunit.OrgUnitResult;
import com.fenbeitong.usercenter.api.service.company.IRCompanyService;
import com.fenbeitong.usercenter.api.service.costcenter.ICostCenterService;
import com.fenbeitong.usercenter.api.service.employee.IBaseEmployeeExtService;
import com.fenbeitong.usercenter.api.service.orgunit.IOrgUnitService;
import com.fenbeitong.usercenter.api.service.privilege.IPrivilegeService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.luastar.swift.base.entity.PageInfo;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by xiabin on 2017/4/21.
 */
@Service
public class ApplyFlowV2ServiceImpl implements IApplyFlowV2Service {
    private static Logger logger = LoggerFactory.getLogger(ApplyFlowV2ServiceImpl.class);
    @Autowired
    private CompanyApplySettingMapper companyApplySettingMapper;

    @Autowired
    private CompanyApplySettingLogMapper companyApplySettingLogMapper;

    @Autowired
    private ApplyFlowMapper applyFlowMapper;

    @Autowired
    private ApplyFlowExtMapper applyFlowExtMapper;

    @Autowired
    private ApplyFlowItemMapper applyFlowItemMapper;

    @Autowired
    private ApplyFlowItemExtMapper applyFlowItemExtMapper;

    @Autowired
    private ICustomRoleService customRoleService;

    @Autowired
    private IUserService userService;

    @Autowired
    private IOrganizationService organizationService;

    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyFlowUserMapper applyFlowUserMapper;

    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyFlowUserItemMapper applyFlowUserItemMapper;

    @Autowired
    private ApplyFlowCopyToMapper applyFlowCopyToMapper;

    @Autowired
    private ApplyFlowCopyToExtMapper applyFlowCopyToExtMapper;

    @Autowired
    private CompanyApplySettingUseMapper companyApplySettingUseMapper;

    @Autowired
    IBaseOrganizationService baseOrganizationService;

    @Autowired
    private OrgUnitMapper orgUnitMapper;

    @Autowired
    private ApplyAdapterMapper applyAdapterMapper;

    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyOrderMapper applyOrderMapper;

    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyOrderLogMapper applyOrderLogMapper;

    @Autowired
    private CompanyApplySettingExtMapper companyApplySettingExtMapper;

    @Autowired
    private CompanyApplySettingUseExtMapper companyApplySettingUseExtMapper;

    @Autowired
    private IMessageSetupService messageSetupService;

    @Autowired
    private IBaseOrganizationService iBaseOrganizationService;

    @Autowired
    private IBaseEmployeeExtService iBaseEmployeeExtService;

    @Autowired
    private IPrivilegeService iPrivilegeService;

    @Autowired
    private ICostCenterService costCenterService;

    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyOrderLogExtMapper applyOrderLogExtMapper;

    @Autowired
    private CompanyLogKafkaProducerService companyLogKafkaProducerService;

    @Autowired
    private IOrgUnitService orgUnitService;

    @Autowired
    private IRCompanyService irCompanyService;

    @Resource
    private GetQueryEmployeeListInfo getQueryEmployeeListInfo;

    /**
     * 设置审批流
     *
     * @param userRole 用户角色
     */
    @Override
    @Transactional
    public GlobalResponseCode setCompanyApplySetting(CompanyApplyFlowSetV2RequestContract requestContract, UserRole userRole, String ip, String operatorId, String companyId, String clientHeadVersion) {
        if (StringUtils.isNotBlank(clientHeadVersion) && requestContract.getCompany_setting_type() == CompanySettingType.CenterApply.getValue() && VersionTool.compare(clientHeadVersion, "3.4.0") < 0) {
            throw new SaasException(GlobalResponseCode.ApplyCenterAlert);
        }
        GlobalResponseCode responseCode = checkAndClearSetCompanySettingRequest(requestContract, userRole, companyId);
        if (responseCode != GlobalResponseCode.Success) {
            return responseCode;
        }
        if (requestContract.getCompany_setting_type() == null) {
            requestContract.setCompany_setting_type(SaasFlowConstant.SETTING_TYPE_CENTER);
        }
        if (requestContract.getCan_delete() == null) {
            requestContract.setCan_delete(SaasFlowConstant.CAN_NORMAL_STATUS);
        }
        CompanySettingType companySettingType = CompanySettingType.valueOf(requestContract.getCompany_setting_type());
        CompanyApplyType companyApplyType = CompanyApplyType.valueOf(requestContract.getCompany_apply_type());
        ApplyType applyType = ApplyType.valueOf(requestContract.getApply_type());
        if (requestContract.getUse_beyond_rule_flow() == null) {
            int use_beyond_rule_flow = 0;
            if (companySettingType == CompanySettingType.CenterApply
                    && applyType == ApplyType.ChaiLv) {
                use_beyond_rule_flow = 1;
            }
            requestContract.setUse_beyond_rule_flow(use_beyond_rule_flow);
        }
        String flowName = requestContract.getFlow_name();
        Integer roleApproveType = requestContract.getRole_approve_type();
        String companyApplySettingId;
        ActionType actionType;
        CompanyApplySetting companyApplySetting;

        Date now = new Date();
        if (StringUtils.isNotBlank(requestContract.getId())) {
            companyApplySetting = companyApplySettingMapper.selectByPrimaryKey(requestContract.getId());
            if (companyApplySetting == null || companyApplySetting.getApplyStatus() == 2) {
                return GlobalResponseCode.NotFound;
            }
            //审批流名称排重
            CompanyApplySettingExample companyApplySettingExample = new CompanyApplySettingExample();
            CompanyApplySettingExample.Criteria criteria = companyApplySettingExample.createCriteria();
            criteria.andFlowNameEqualTo(requestContract.getFlow_name()).andApplyStatusEqualTo(SaasFlowConstant.IS_NORMAL_STATUS)
                    .andCompanySettingTypeEqualTo(requestContract.getCompany_setting_type()).andCompanyIdEqualTo(companyId).andIdNotEqualTo(requestContract.getId()).andApplyTypeEqualTo(requestContract.getApply_type());
            List<CompanyApplySetting> companyApplySettings = companyApplySettingMapper.selectByExample(companyApplySettingExample);
            if (!CollectionUtils.isEmpty(companyApplySettings)) {
                return GlobalResponseCode.FlowNameRepetition;
            }
            actionType = ActionType.Edit;
            companyApplySettingId = companyApplySetting.getId();
            if (!checkModified(requestContract, companyApplySetting)) {
                return GlobalResponseCode.NotModified;
            }
            //删除审批流节点表
            applyFlowItemExtMapper.deleteByCompanyApplySettingId(companyApplySettingId);
            //删除抄送人
            applyFlowCopyToExtMapper.deleteByCompanyApplySettingId(companyApplySettingId);
            //删除审批流表
            applyFlowExtMapper.deleteByCompanyApplySettingId(companyApplySettingId);

            //companyApplySettingMapper.deleteByPrimaryKey(companyApplySetting.getId());
            CompanyApplySettingExample example = new CompanyApplySettingExample();
            example.createCriteria().andIdEqualTo(companyApplySettingId);
            CompanyApplySetting record = new CompanyApplySetting();
            record.setCompanyId(companyId);
            record.setOperatorId(operatorId);
            record.setUpdateTime(now);
            record.setFlowName(flowName);//审批流名称
            record.setRoleApproveType(roleApproveType);
            companyApplySettingMapper.updateByExampleSelective(record, example);
        } else {
            //新增保存
            actionType = ActionType.Create;
            companyApplySettingId = IDTool.CreateUniqueID();
            //审批流名称排重
            CompanyApplySettingExample companyApplySettingExample = new CompanyApplySettingExample();
            CompanyApplySettingExample.Criteria criteria = companyApplySettingExample.createCriteria();
            criteria.andFlowNameEqualTo(requestContract.getFlow_name()).andApplyStatusEqualTo(SaasFlowConstant.IS_NORMAL_STATUS).andCompanyIdEqualTo(companyId).andCompanySettingTypeEqualTo(requestContract.getCompany_setting_type()).andApplyTypeEqualTo(requestContract.getApply_type());
            List<CompanyApplySetting> companyApplySettings = companyApplySettingMapper.selectByExample(companyApplySettingExample);
            if (!CollectionUtils.isEmpty(companyApplySettings)) {
                return GlobalResponseCode.FlowNameRepetition;
            }
            companyApplySetting = new CompanyApplySetting();
            companyApplySetting.setId(companyApplySettingId);
            companyApplySetting.setCompanyId(companyId);
            companyApplySetting.setApplyType(applyType.getValue());
            companyApplySetting.setOperatorId(operatorId);
            companyApplySetting.setCreateTime(now);
            companyApplySetting.setUpdateTime(now);
            companyApplySetting.setFlowName(flowName);//审批流名称
            companyApplySetting.setApplyStatus(SaasFlowConstant.IS_NORMAL_STATUS);//删除状态 1：正常 2：删除
            companyApplySetting.setCompanySettingType(requestContract.getCompany_setting_type());
            companyApplySetting.setCanDelete(requestContract.getCan_delete());
            companyApplySetting.setCostAttributionCategory(SettingType.Department.getValue());
            companyApplySetting.setCostCenterStatus(CostCenterStateEnum.Start.getKey());
            companyApplySetting.setRoleApproveType(roleApproveType);
            companyApplySettingMapper.insertSelective(companyApplySetting);
        }

        // 除用餐 外 其它类型保存正常审批流配置
        if (applyType != ApplyType.Dinner) {
            List<CompanyApplyFlowItemSetV2RequestContract> fixationFlowList = requestContract.getFixation_flow_list();
            List<CompanyApplySettingConditionRequestContract> conditionalFlowList = requestContract.getConditional_flow_list();
            Integer ccNoticeType = requestContract.getCc_notice_type();
            List<CompanyApplyFlowItemSetV2RequestContract> ccList = requestContract.getCc_list();
            CompanyApplyType flowCompanyApplyType = companyApplyType;
            // 差旅订单审批正常审批流配置
            if (companySettingType == CompanySettingType.CenterApply && applyType == ApplyType.ChaiLv) {
                fixationFlowList = requestContract.getNormal_fixation_flow_list();
                conditionalFlowList = requestContract.getNormal_conditional_flow_list();
                ccNoticeType = requestContract.getNormal_cc_notice_type();
                ccList = requestContract.getNormal_cc_list();
                flowCompanyApplyType = CompanyApplyType.valueOf(requestContract.getNormal_company_apply_type());

                // 默认正常审批流配置
                if (flowCompanyApplyType == CompanyApplyType.Unknown) {
                    flowCompanyApplyType = CompanyApplyType.Elastic;
                }
                if (ccNoticeType == null) {
                    ccNoticeType = 1;
                }
            }
            saveFlowAndCc(companyApplySettingId, flowCompanyApplyType,
                    fixationFlowList, conditionalFlowList, ccNoticeType, ccList,
                    companyId, operatorId, now, SaasFlowConstant.NORMAL_FLOW_STATUS);
        }

        // 保存超规审批流配置
        if (applyType == ApplyType.Dinner ||
                (companySettingType == CompanySettingType.CenterApply
                        && applyType == ApplyType.ChaiLv
                        && requestContract.getUse_beyond_rule_flow() == 1)) {
            List<CompanyApplyFlowItemSetV2RequestContract> fixationFlowList = requestContract.getFixation_flow_list();
            List<CompanyApplySettingConditionRequestContract> conditionalFlowList = requestContract.getConditional_flow_list();
            Integer ccNoticeType = requestContract.getCc_notice_type();
            List<CompanyApplyFlowItemSetV2RequestContract> ccList = requestContract.getCc_list();
            CompanyApplyType flowCompanyApplyType = companyApplyType;

            saveFlowAndCc(companyApplySettingId, flowCompanyApplyType,
                    fixationFlowList, conditionalFlowList, ccNoticeType, ccList,
                    companyId, operatorId, now, SaasFlowConstant.EXCEED_FLOW_STATUS);
        }

        // 保存差旅退订审批超规审批流配置
        if (companySettingType == CompanySettingType.CenterApply
                && applyType == ApplyType.ChaiLv
                && requestContract.getUse_refund_rule_flow() != null && requestContract.getUse_refund_rule_flow() == 1) {
            List<CompanyApplyFlowItemSetV2RequestContract> fixationFlowList = requestContract.getRefund_fixation_flow_list();
            List<CompanyApplySettingConditionRequestContract> conditionalFlowList = requestContract.getRefund_conditional_flow_list();
            Integer ccNoticeType = requestContract.getCc_notice_type();
            List<CompanyApplyFlowItemSetV2RequestContract> ccList = requestContract.getRefund_cc_list();
            CompanyApplyType flowCompanyApplyType = CompanyApplyType.valueOf(requestContract.getRefund_company_apply_type());

            saveFlowAndCc(companyApplySettingId, flowCompanyApplyType,
                    fixationFlowList, conditionalFlowList, ccNoticeType, ccList,
                    companyId, operatorId, now, SaasFlowConstant.REFUND_FLOW_STATUS);
        }

        // 保存差旅改期审批超规审批流配置
        if (companySettingType == CompanySettingType.CenterApply
                && applyType == ApplyType.ChaiLv
                && requestContract.getUse_change_rule_flow() != null
                && requestContract.getUse_change_rule_flow() == 1) {
            List<CompanyApplyFlowItemSetV2RequestContract> fixationFlowList = requestContract.getChange_fixation_flow_list();
            List<CompanyApplySettingConditionRequestContract> conditionalFlowList = requestContract.getChange_conditional_flow_list();
            Integer ccNoticeType = requestContract.getCc_notice_type();
            List<CompanyApplyFlowItemSetV2RequestContract> ccList = requestContract.getChange_cc_list();
            CompanyApplyType flowCompanyApplyType = CompanyApplyType.valueOf(requestContract.getChange_company_apply_type());

            saveFlowAndCc(companyApplySettingId, flowCompanyApplyType,
                    fixationFlowList, conditionalFlowList, ccNoticeType, ccList,
                    companyId, operatorId, now, SaasFlowConstant.CHANGE_FLOW_STATUS);
        }

        CompanyApplySettingLog log = new CompanyApplySettingLog();
        log.setCreateTime(now);
        log.setAction(actionType.getValue());
        log.setOperatorId(operatorId);
        log.setCompanyApplySettingId(companyApplySettingId);
        log.setIp(ip);
        companyApplySettingLogMapper.insert(log);
        companyLogKafkaProducerService.sendCompanyLogMsg(getKafkaCompanyLogMsg(ApplyType.valueOf(requestContract.getApply_type()),
                CompanySettingType.valueOf(requestContract.getCompany_setting_type()),
                actionType,
                requestContract.getFlow_name()));
        return GlobalResponseCode.Success;
    }

    /**
     * 校验公司审批流配置是否修改
     *
     * @param requestContract
     * @param companyApplySetting
     * @return
     */
    private boolean checkModified(CompanyApplyFlowSetV2RequestContract requestContract, CompanyApplySetting companyApplySetting) {
        //判断审批流名称是否修改
        if (!requestContract.getFlow_name().equals(companyApplySetting.getFlowName())) {
            return true;
        }
        String companyApplySettingId = companyApplySetting.getId();
        List<ApplyFlow> applyFlowList = applyFlowExtMapper.queryByCompanyApplySettingId(companyApplySettingId);
        if (CollectionUtils.isEmpty(applyFlowList)) {
            return true;
        }

        ApplyFlow normalFlow = null;
        ApplyFlow exceedFlow = null;
        ApplyFlow refundFlow = null;
        ApplyFlow changeFlow = null;
        for (ApplyFlow applyFlow : applyFlowList) {
            if (applyFlow.getIsExceedBuyFlow() == 0) { //正常流程
                normalFlow = applyFlow;
            } else if (applyFlow.getIsExceedBuyFlow() == 1) { //超规流程
                exceedFlow = applyFlow;
            } else if (applyFlow.getIsExceedBuyFlow() == 2) { //退订流程
                refundFlow = applyFlow;
            } else if (applyFlow.getIsExceedBuyFlow() == 3) { //改签流程
                changeFlow = applyFlow;
            }
        }

        CompanySettingType companySettingType = CompanySettingType.valueOf(requestContract.getCompany_setting_type());
        CompanyApplyType companyApplyType = CompanyApplyType.valueOf(requestContract.getCompany_apply_type());
        ApplyType applyType = ApplyType.valueOf(requestContract.getApply_type());
        // 正常审批流配置
        List<CompanyApplyFlowItemSetV2RequestContract> normalFixationFlowList = null;
        List<CompanyApplySettingConditionRequestContract> normalConditionalFlowList = null;
        Integer normalCcNoticeType = null;
        List<CompanyApplyFlowItemSetV2RequestContract> normalCcList = null;
        CompanyApplyType normalCompanyApplyType = null;
        if (applyType != ApplyType.Dinner) {
            normalFixationFlowList = requestContract.getFixation_flow_list();
            normalConditionalFlowList = requestContract.getConditional_flow_list();
            normalCcNoticeType = requestContract.getCc_notice_type();
            normalCcList = requestContract.getCc_list();
            normalCompanyApplyType = companyApplyType;
            // 差旅订单审批正常审批流配置
            if (companySettingType == CompanySettingType.CenterApply && applyType == ApplyType.ChaiLv) {
                normalFixationFlowList = requestContract.getNormal_fixation_flow_list();
                normalConditionalFlowList = requestContract.getNormal_conditional_flow_list();
                normalCcNoticeType = requestContract.getNormal_cc_notice_type();
                normalCcList = requestContract.getNormal_cc_list();
                normalCompanyApplyType = CompanyApplyType.valueOf(requestContract.getNormal_company_apply_type());
            }
        }

        // 超规审批流配置
        List<CompanyApplyFlowItemSetV2RequestContract> exceedFixationFlowList = null;
        List<CompanyApplySettingConditionRequestContract> exceedConditionalFlowList = null;
        Integer exceedCcNoticeType = null;
        List<CompanyApplyFlowItemSetV2RequestContract> exceedCcList = null;
        CompanyApplyType exceedCompanyApplyType = null;
        if (applyType == ApplyType.Dinner ||
                (companySettingType == CompanySettingType.CenterApply
                        && applyType == ApplyType.ChaiLv
                        && requestContract.getUse_beyond_rule_flow() == 1)) {
            exceedFixationFlowList = requestContract.getFixation_flow_list();
            exceedConditionalFlowList = requestContract.getConditional_flow_list();
            exceedCcNoticeType = requestContract.getCc_notice_type();
            exceedCcList = requestContract.getCc_list();
            exceedCompanyApplyType = companyApplyType;
        }

        //退订审批流配置
        List<CompanyApplyFlowItemSetV2RequestContract> refundFixationFlowList = null;
        List<CompanyApplySettingConditionRequestContract> refundConditionalFlowList = null;
        Integer refundCcNoticeType = null;
        List<CompanyApplyFlowItemSetV2RequestContract> refundCcList = null;
        CompanyApplyType refundCompanyApplyType = null;
        if (companySettingType == CompanySettingType.CenterApply && applyType == ApplyType.ChaiLv && requestContract.getUse_refund_rule_flow() != null && requestContract.getUse_refund_rule_flow() == 1) {
            refundFixationFlowList = requestContract.getRefund_fixation_flow_list();
            refundConditionalFlowList = requestContract.getRefund_conditional_flow_list();
            refundCcNoticeType = requestContract.getCc_notice_type();
            refundCcList = requestContract.getCc_list();
            refundCompanyApplyType = companyApplyType;
        }

        //改签审批流配置
        List<CompanyApplyFlowItemSetV2RequestContract> changeFixationFlowList = null;
        List<CompanyApplySettingConditionRequestContract> changeConditionalFlowList = null;
        Integer changeCcNoticeType = null;
        List<CompanyApplyFlowItemSetV2RequestContract> changeCcList = null;
        CompanyApplyType changeCompanyApplyType = null;
        if (companySettingType == CompanySettingType.CenterApply && applyType == ApplyType.ChaiLv && requestContract.getUse_change_rule_flow() != null && requestContract.getUse_change_rule_flow() == 1) {
            changeFixationFlowList = requestContract.getChange_fixation_flow_list();
            changeConditionalFlowList = requestContract.getChange_conditional_flow_list();
            changeCcNoticeType = requestContract.getCc_notice_type();
            changeCcList = requestContract.getCc_list();
            changeCompanyApplyType = companyApplyType;
        }

        if (checkFlowModified(normalFlow, normalCompanyApplyType,
                normalFixationFlowList, normalConditionalFlowList,
                normalCcNoticeType, normalCcList)) {
            return true;
        }
        if (checkFlowModified(exceedFlow, exceedCompanyApplyType,
                exceedFixationFlowList, exceedConditionalFlowList,
                exceedCcNoticeType, exceedCcList)) {
            return true;
        }
        if (checkFlowModified(refundFlow, refundCompanyApplyType,
                refundFixationFlowList, refundConditionalFlowList,
                refundCcNoticeType, refundCcList)) {
            return true;
        }
        if (checkFlowModified(changeFlow, changeCompanyApplyType,
                changeFixationFlowList, changeConditionalFlowList,
                changeCcNoticeType, changeCcList)) {
            return true;
        }
        if (!companyApplySetting.getRoleApproveType().equals(requestContract.getRole_approve_type())) {
            return true;
        }

        return false;
    }

    /**
     * 校验单个流是否修改
     *
     * @param flow
     * @param companyApplyType
     * @param fixationFlowList
     * @param conditionalFlowList
     * @param ccNoticeType
     * @param ccList
     * @return
     */
    private boolean checkFlowModified(ApplyFlow flow, CompanyApplyType companyApplyType,
                                      List<CompanyApplyFlowItemSetV2RequestContract> fixationFlowList,
                                      List<CompanyApplySettingConditionRequestContract> conditionalFlowList,
                                      Integer ccNoticeType, List<CompanyApplyFlowItemSetV2RequestContract> ccList) {
        if (flow == null) {
            return !(companyApplyType == CompanyApplyType.Unknown && ccNoticeType == null
                    && CollectionUtils.isEmpty(fixationFlowList)
                    && CollectionUtils.isEmpty(conditionalFlowList)
                    && CollectionUtils.isEmpty(ccList));
        }
        //判断弹性审批流是否修改
        if (flow.getCompanyApplyType().equals(CompanyApplyType.Elastic.getValue())) {
            if (companyApplyType == CompanyApplyType.Elastic) {
                boolean modifiedCc = false;
                //判断抄送人是否修改
                List<ApplyFlowCopyTo> flowCcList = applyFlowCopyToExtMapper.selectListById(flow.getId());
                if (ccList != null) {
                    if (ccList.size() == flowCcList.size()) {
                        for (int i = 0; i < ccList.size(); i++) {
                            CompanyApplyFlowItemSetV2RequestContract item = ccList.get(i);
                            ApplyFlowCopyTo ccItem = flowCcList.get(i);
                            if (!item.getItem_type().equals(ccItem.getItemType()) || !StringTool.areEqual(item.getItem_id(), ccItem.getItemId())) {
                                modifiedCc = true;
                                break;
                            }
                        }
                    } else {
                        modifiedCc = true;
                    }
                } else {
                    if (flowCcList.size() > 0) {
                        modifiedCc = true;
                    }
                }
                if (!modifiedCc && flow.getCompanyApplyType() == companyApplyType.getValue() && flow.getCcNoticeType() == ccNoticeType) {
                    return false;
                }
            }
        }
        //判断固定审批流是否修改
        if (flow.getCompanyApplyType().equals(CompanyApplyType.Flow.getValue())) {
            if (companyApplyType == CompanyApplyType.Flow) {
                boolean modified = false;
                boolean modifiedCc = false;
                //判断审批人是否修改
                List<ApplyFlowItem> oldItems = applyFlowItemExtMapper.selectListByFlowId(flow.getId());
                if (fixationFlowList.size() == oldItems.size()) {
                    for (int i = 0; i < fixationFlowList.size(); i++) {
                        CompanyApplyFlowItemSetV2RequestContract item = fixationFlowList.get(i);
                        ApplyFlowItem oldItem = oldItems.get(i);
                        if (!item.getItem_type().equals(oldItem.getItemType()) || !StringTool.areEqual(item.getItem_id(), oldItem.getItemId())) {
                            modified = true;
                            break;
                        }
                    }
                } else {
                    modified = true;
                }
                //判断抄送人是否修改
                List<ApplyFlowCopyTo> flowCcList = applyFlowCopyToExtMapper.selectListById(flow.getId());
                if (ccList != null) {
                    if (ccList.size() == flowCcList.size()) {
                        for (int i = 0; i < ccList.size(); i++) {
                            CompanyApplyFlowItemSetV2RequestContract item = ccList.get(i);
                            ApplyFlowCopyTo ccItem = flowCcList.get(i);
                            if (!item.getItem_type().equals(ccItem.getItemType()) || !StringTool.areEqual(item.getItem_id(), ccItem.getItemId())) {
                                modifiedCc = true;
                                break;
                            }
                        }
                    } else {
                        modifiedCc = true;
                    }
                } else {
                    if (flowCcList.size() > 0) {
                        modifiedCc = true;
                    }
                }
                if (!modified && !modifiedCc && flow.getCompanyApplyType() == companyApplyType.getValue() && flow.getCcNoticeType() == ccNoticeType) {
                    return false;
                }
            }
        }
        //判断分条件审批流是否修改
        if (flow.getCompanyApplyType().equals(CompanyApplyType.CONDITIONAL.getValue())) {
            if (companyApplyType == CompanyApplyType.CONDITIONAL) {
                boolean modified = false;
                boolean modifiedCc = false;
                //判断审批人是否修改
                int conditionalFlag = 0;
                for (CompanyApplySettingConditionRequestContract conditionRequestContract : conditionalFlowList) {
                    conditionalFlag++;
                    BigDecimal conditionMin = conditionRequestContract.getCondition_min();
                    List<CompanyApplyFlowItemSetV2RequestContract> conditionalFlow = conditionRequestContract.getFlow();
                    List<ApplyFlowItem> oldItems = applyFlowItemExtMapper.selectConditionalListByFlowId(flow.getId(), conditionMin);
                    if (conditionalFlow.size() == oldItems.size()) {
                        for (int i = 0; i < conditionalFlow.size(); i++) {
                            CompanyApplyFlowItemSetV2RequestContract item = conditionalFlow.get(i);
                            ApplyFlowItem oldItem = oldItems.get(i);
                            if (conditionalFlag != conditionalFlowList.size()) {
                                if (!item.getItem_type().equals(oldItem.getItemType()) || !StringTool.areEqual(item.getItem_id(), oldItem.getItemId())
                                        || (conditionRequestContract.getCondition_max() != null && oldItem.getConditionMax() == null)
                                        || (conditionRequestContract.getCondition_max() == null && oldItem.getConditionMax() != null)
                                        || conditionRequestContract.getCondition_min().compareTo(oldItem.getConditionMin()) != 0
                                        || conditionRequestContract.getCondition_max().compareTo(oldItem.getConditionMax()) != 0) {
                                    modified = true;
                                    break;
                                }
                            }
                            if (conditionalFlag == conditionalFlowList.size()) {
                                if (!item.getItem_type().equals(oldItem.getItemType()) || !StringTool.areEqual(item.getItem_id(), oldItem.getItemId())
                                        || conditionRequestContract.getCondition_min().compareTo(oldItem.getConditionMin()) != 0) {
                                    modified = true;
                                    break;
                                }
                            }
                        }
                    } else {
                        modified = true;
                    }

                }
                //判断抄送人是否修改
                List<ApplyFlowCopyTo> flowCcList = applyFlowCopyToExtMapper.selectListById(flow.getId());
                if (ccList != null) {
                    if (ccList.size() == flowCcList.size()) {
                        for (int i = 0; i < ccList.size(); i++) {
                            CompanyApplyFlowItemSetV2RequestContract item = ccList.get(i);
                            ApplyFlowCopyTo ccItem = flowCcList.get(i);
                            if (!item.getItem_type().equals(ccItem.getItemType()) || !StringTool.areEqual(item.getItem_id(), ccItem.getItemId())) {
                                modifiedCc = true;
                                break;
                            }
                        }
                    } else {
                        modifiedCc = true;
                    }
                } else {
                    if (flowCcList.size() > 0) {
                        modifiedCc = true;
                    }
                }
                if (!modified && !modifiedCc && flow.getCompanyApplyType() == companyApplyType.getValue() && flow.getCcNoticeType() == ccNoticeType) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 保存审批流和抄送配置
     *
     * @param companyApplySettingId
     * @param companyApplyType
     * @param fixationFlowList
     * @param conditionalFlowList
     * @param ccNoticeType
     * @param ccList
     * @param companyId
     * @param operatorId
     * @param now
     * @param isExceedBuyFlow
     */
    private void saveFlowAndCc(String companyApplySettingId, CompanyApplyType companyApplyType,
                               List<CompanyApplyFlowItemSetV2RequestContract> fixationFlowList,
                               List<CompanyApplySettingConditionRequestContract> conditionalFlowList,
                               Integer ccNoticeType, List<CompanyApplyFlowItemSetV2RequestContract> ccList,
                               String companyId, String operatorId, Date now, int isExceedBuyFlow) {
        String applyFlowId = IDTool.CreateUniqueID();
        ApplyFlow applyFlow = new ApplyFlow();
        applyFlow.setId(applyFlowId);
        applyFlow.setCompanyId(companyId);
        applyFlow.setCreateTime(now);
        applyFlow.setOperatorId(operatorId);
        applyFlow.setCompanyApplySettingId(companyApplySettingId);
        applyFlow.setCompanyApplyType(companyApplyType.getValue());
        applyFlow.setCcNoticeType(ccNoticeType);
        applyFlow.setIsExceedBuyFlow(isExceedBuyFlow);
        applyFlowMapper.insert(applyFlow);

        //保存固定审批列表
        if (companyApplyType == CompanyApplyType.Flow) {
            int sort = 1;
            for (CompanyApplyFlowItemSetV2RequestContract itemContract : fixationFlowList) {
                ApplyFlowItem item = new ApplyFlowItem();
                item.setItemType(itemContract.getItem_type());
                item.setItemId(itemContract.getItem_id());
                item.setApplyFlowId(applyFlowId);
                item.setCreateTime(now);
                item.setSort(sort++);
                item.setId(IDTool.CreateUniqueID());
                applyFlowItemMapper.insert(item);
            }
        }
        //保存分条件审批列表
        if (companyApplyType == CompanyApplyType.CONDITIONAL) {
            int conditionalFlag = 0;
            List<CompanyApplySettingConditionRequestContract> conditionalList = conditionalFlowList;
            for (CompanyApplySettingConditionRequestContract conditionalRequestContract : conditionalList) {
                conditionalFlag++;
                List<CompanyApplyFlowItemSetV2RequestContract> flow = conditionalRequestContract.getFlow();
                int sort = 1;
                for (CompanyApplyFlowItemSetV2RequestContract flowItem : flow) {
                    ApplyFlowItem item = new ApplyFlowItem();
                    item.setConditionMin(conditionalRequestContract.getCondition_min());
                    if (conditionalFlag != conditionalList.size()) {
                        item.setConditionMax(conditionalRequestContract.getCondition_max());
                    }
                    item.setItemId(flowItem.getItem_id());
                    item.setItemType(flowItem.getItem_type());
                    item.setApplyFlowId(applyFlowId);
                    item.setCreateTime(now);
                    item.setSort(sort++);
                    item.setId(IDTool.CreateUniqueID());
                    applyFlowItemMapper.insert(item);
                }
            }
        }

        //保存抄送人列表
        if (!CollectionUtils.isEmpty(ccList)) {
            ApplyFlowCopyTo applyFlowCopyTo = new ApplyFlowCopyTo();
            applyFlowCopyTo.setApplyFlowId(applyFlowId);
            applyFlowCopyTo.setCreateTime(now);
            int ccSort = 1;
            for (CompanyApplyFlowItemSetV2RequestContract flowItemSetRequestContract : ccList) {
                applyFlowCopyTo.setId(IDTool.CreateUniqueID());
                applyFlowCopyTo.setItemId(flowItemSetRequestContract.getItem_id());
                applyFlowCopyTo.setItemType(flowItemSetRequestContract.getItem_type());
                applyFlowCopyTo.setSort(ccSort++);
                applyFlowCopyToMapper.insert(applyFlowCopyTo);
            }
        }
    }

    /**
     * 保存和编辑时检查参数是否合法
     *
     * @param requestContract
     * @param userRole
     * @param companyId
     * @return
     */
    private GlobalResponseCode checkAndClearSetCompanySettingRequest(CompanyApplyFlowSetV2RequestContract requestContract, UserRole userRole, String companyId) {
        if (requestContract == null) {
            return GlobalResponseCode.ParameterIsNull;
        }
        //判断审批流名称
        if (StringUtils.isBlank(requestContract.getFlow_name())) {
            return GlobalResponseCode.FlowNameInvalid;
        }
        if (requestContract.getFlow_name().length() > 20) {
            return GlobalResponseCode.FlowNameTooMuch;
        }
        //1.行程审批  2.订单审批（除采购） 3.订单审批（采购）
        CompanySettingType companySettingType = CompanySettingType.valueOf(requestContract.getCompany_setting_type());
        if (companySettingType == CompanySettingType.Unknown) {
            return GlobalResponseCode.CompanySettingTypeError;
        }
        CompanyApplyType companyApplyType = CompanyApplyType.valueOf(requestContract.getCompany_apply_type());
        CompanyApplyType normalCompanyApplyType = CompanyApplyType.valueOf(requestContract.getNormal_company_apply_type());
        CompanyApplyType changeCompanyApplyType = CompanyApplyType.valueOf(requestContract.getChange_company_apply_type());
        CompanyApplyType refundCompanyApplyType = CompanyApplyType.valueOf(requestContract.getRefund_company_apply_type());
        //公司设置的审批类型（1.弹性审批流，2.固定审批流，3.分条件审批流）
        if (companyApplyType == CompanyApplyType.Unknown && normalCompanyApplyType == CompanyApplyType.Unknown && changeCompanyApplyType == CompanyApplyType.Unknown && refundCompanyApplyType == CompanyApplyType.Unknown) {
            return GlobalResponseCode.CompanyApplyTypeInvalid;
        }
        //判断抄送通知类型
        CcNoticeType ccNoticeType = CcNoticeType.valueOf(requestContract.getCc_notice_type());
        CcNoticeType normalCcNoticeType = CcNoticeType.valueOf(requestContract.getNormal_cc_notice_type());
        if (ccNoticeType == CcNoticeType.Unknown && normalCcNoticeType == CcNoticeType.Unknown) {
            return GlobalResponseCode.CCNoticeTypeInvalid;
        }
        ApplyType applyType = ApplyType.valueOf(requestContract.getApply_type());
        //判断业务类型 1.差旅 2.用车 4.采购 5.用餐
        if (applyType == ApplyType.Unknown) {
            return GlobalResponseCode.ApplyTypeInvalid;
        }
        GlobalResponseCode responseCode = null;
        //判断超规审批流及抄送设置
        Integer useBeyondRuleFlow = requestContract.getUse_beyond_rule_flow();
        //if (useBeyondRuleFlow != null && useBeyondRuleFlow == 1) {
            responseCode = checkFlowAndCCList(companyApplyType, requestContract.getFixation_flow_list(),
                    requestContract.getConditional_flow_list(), requestContract.getCc_list(), companyId);
            if (responseCode != GlobalResponseCode.Success) {
                return responseCode;
            }
        //}
        //判断订单审批正常审批流及抄送设置
        responseCode = checkFlowAndCCList(normalCompanyApplyType, requestContract.getNormal_fixation_flow_list(),
                requestContract.getNormal_conditional_flow_list(), requestContract.getNormal_cc_list(), companyId);
        if (responseCode != GlobalResponseCode.Success) {
            return responseCode;
        }
        //判断改期审批流及抄送设置
        Integer useChangeRuleFlow = requestContract.getUse_change_rule_flow();
        if (useChangeRuleFlow != null && useChangeRuleFlow == 1) {
            responseCode = checkFlowAndCCList(companyApplyType, requestContract.getChange_fixation_flow_list(),
                    requestContract.getChange_conditional_flow_list(), requestContract.getChange_cc_list(), companyId);
            if (responseCode != GlobalResponseCode.Success) {
                return responseCode;
            }
        }
        //判断退订审批流及抄送设置
        Integer useRefundRuleFlow = requestContract.getUse_refund_rule_flow();
        if (useRefundRuleFlow != null && useRefundRuleFlow == 1) {
            responseCode = checkFlowAndCCList(companyApplyType, requestContract.getRefund_fixation_flow_list(),
                    requestContract.getRefund_conditional_flow_list(), requestContract.getRefund_cc_list(), companyId);
            if (responseCode != GlobalResponseCode.Success) {
                return responseCode;
            }
        }

        return GlobalResponseCode.Success;
    }

    /**
     * 校验审批流及抄送设置
     *
     * @param companyApplyType
     * @param fixation_flow_list
     * @param conditional_flow_list
     * @param cc_list
     * @param companyId
     * @return
     */
    private GlobalResponseCode checkFlowAndCCList(CompanyApplyType companyApplyType,
                                                  List<CompanyApplyFlowItemSetV2RequestContract> fixation_flow_list,
                                                  List<CompanyApplySettingConditionRequestContract> conditional_flow_list,
                                                  List<CompanyApplyFlowItemSetV2RequestContract> cc_list, String companyId) {
        //判断固定审批流
        if (companyApplyType == CompanyApplyType.Flow) {
            GlobalResponseCode flowResponseCode = checkFlowAndCC(fixation_flow_list, companyId);
            if (flowResponseCode.getCode() != 0) {
                return flowResponseCode;
            }
            if (!CollectionUtils.isEmpty(cc_list)) {
                if (cc_list.size() > 5) {
                    return GlobalResponseCode.FlowItemCCTooMuch;
                }
                GlobalResponseCode ccResponseCode = checkFlowAndCC(cc_list, companyId);
                if (ccResponseCode.getCode() != 0) {
                    return ccResponseCode;
                }
            }
            if (fixation_flow_list.size() > 20) {
                return GlobalResponseCode.FlowItemTooMuch;
            }
            //事中审批流只能一个审批节点
            /*if (requestContract.getCompany_setting_type() == 2 && requestContract.getFixation_flow_list().size() > 1) {
                return GlobalResponseCode.CenterApplyTooMuch;
            }*/
        }
        //判断分条件审批流
        if (companyApplyType == CompanyApplyType.CONDITIONAL) {
            if (CollectionUtils.isEmpty(conditional_flow_list)) {
                return GlobalResponseCode.ApplyFlowNoItems;
            }
            if (conditional_flow_list.size() > 9) {
                return GlobalResponseCode.ConditionFlowError;
            }
            for (int i = 0; i < conditional_flow_list.size(); i++) {
                BigDecimal conditionMin = conditional_flow_list.get(i).getCondition_min();
                BigDecimal conditionMax = conditional_flow_list.get(i).getCondition_max();
                if (i == 0) {
                    if (conditionMin == null || conditional_flow_list.get(0).getCondition_min().compareTo(BigDecimal.valueOf(0)) != 0) {
                        return GlobalResponseCode.ConditionError;
                    }
                } else {
                    if (conditionMin == null || conditional_flow_list.get(i).getCondition_min().compareTo(BigDecimal.valueOf(0)) != 1) {
                        return GlobalResponseCode.ConditionError;
                    }
                    if (conditionMax != null && conditionMax.compareTo(BigDecimal.valueOf(-1)) != 0) {
                        if (conditionMax.compareTo(BigDecimal.valueOf(0)) != 1) {
                            return GlobalResponseCode.ConditionError;
                        }
                        if ((conditionMax.compareTo(BigDecimal.valueOf(99999999)) == 1) || conditionMin.compareTo(BigDecimal.valueOf(99999999)) == 1) {
                            return GlobalResponseCode.ConditionInvalid;
                        }
                        if (conditionMin.compareTo(BigDecimal.valueOf(conditionMin.intValue())) == 1) {
                            return GlobalResponseCode.ConditionError;
                        }
                        if (conditionMin.compareTo(conditionMax) != -1) {
                            return GlobalResponseCode.ConditionError;
                        }
                    }
                }
                GlobalResponseCode flowResponseCode = checkFlowAndCC(conditional_flow_list.get(i).getFlow(), companyId);
                if (flowResponseCode.getCode() != 0) {
                    return flowResponseCode;
                }
                if (conditional_flow_list.get(i).getFlow().size() > 20) {
                    return GlobalResponseCode.FlowItemTooMuch;
                }
            }
            if (!CollectionUtils.isEmpty(cc_list)) {
                if (cc_list.size() > 5) {
                    return GlobalResponseCode.FlowItemCCTooMuch;
                }
                GlobalResponseCode ccResponseCode = checkFlowAndCC(cc_list, companyId);
                if (ccResponseCode.getCode() != 0) {
                    return ccResponseCode;
                }
            }
        }
        if (companyApplyType == CompanyApplyType.Elastic) {
            if (!CollectionUtils.isEmpty(cc_list)) {
                if (cc_list.size() > 5) {
                    return GlobalResponseCode.FlowItemCCTooMuch;
                }
                GlobalResponseCode elResponseCode = checkFlowAndCC(cc_list, companyId);
                if (elResponseCode.getCode() != 0) {
                    return elResponseCode;
                }
            }
        }
        return GlobalResponseCode.Success;
    }

    /**
     * 检查审批人和抄送人列表
     *
     * @param companyApplyFlowItemSetRequestContractList
     * @param companyId
     * @return
     */
    private GlobalResponseCode checkFlowAndCC(List<CompanyApplyFlowItemSetV2RequestContract> companyApplyFlowItemSetRequestContractList, String companyId) {
        //判断审批设置节点
        if (CollectionUtils.isEmpty(companyApplyFlowItemSetRequestContractList)) {
            return GlobalResponseCode.ApplyFlowNoItems;
        }
        List<String> customRoleIds = new ArrayList<>();
        List<String> employeeIds = new ArrayList<>();
        for (CompanyApplyFlowItemSetV2RequestContract item : companyApplyFlowItemSetRequestContractList) {
            ApplyFlowItemType itemType = ApplyFlowItemType.valueOf(item.getItem_type());
            //判断审批角色
            if (itemType == ApplyFlowItemType.Unknown) {
                return GlobalResponseCode.ApplyFlowItemTypeInvalid;
            }
            if (itemType == ApplyFlowItemType.CustomRole || itemType == ApplyFlowItemType.Employee) {
                //判断节点的id是否存在
                if (StringTool.isNullOrEmpty(item.getItem_id()) || item.getItem_id().length() > 24) {
                    return GlobalResponseCode.ApplyFlowItemIdNotSet;
                }
            } else if (itemType == ApplyFlowItemType.DeptManager) {
                if (StringTool.isNullOrEmpty(item.getItem_id())) {
                    return GlobalResponseCode.ApplyFlowItemIdNotSet;
                }
                int i = 0;
                try {
                    i = Integer.parseInt(item.getItem_id());
                } catch (Exception ex) {
                    i = 0;
                }
                if (i <= 0) {
                    return GlobalResponseCode.ApplyFlowDeptManagerLevelInvalid;
                }
            } else {
                //授权负责人和企业管理员的item_id为null
                item.setItem_id(null);
            }

            if (itemType == ApplyFlowItemType.CustomRole) {
                if (!customRoleIds.contains(item.getItem_id())) {
                    customRoleIds.add(item.getItem_id());
                }
            } else if (itemType == ApplyFlowItemType.Employee) {
                if (!employeeIds.contains(item.getItem_id())) {
                    employeeIds.add(item.getItem_id());
                }
            }
        }
        //检验自定义角色是否存在
        GlobalResponseCode code = checkCustomRoleAllExist(customRoleIds, companyId);
        if (code != GlobalResponseCode.Success) {
            return code;
        }
        //检验用户是否在公司存在
        code = checkEmployeesAllExist(employeeIds, companyId);
        if (code != GlobalResponseCode.Success) {
            return code;
        }
        return GlobalResponseCode.Success;
    }

    /**
     * 检验用户是否在公司存在
     *
     * @param employeeIds
     * @param companyId
     * @return
     */
    private GlobalResponseCode checkEmployeesAllExist(List<String> employeeIds, String companyId) {
        if (employeeIds == null || employeeIds.size() == 0) {
            return GlobalResponseCode.Success;
        }
        for (String employeeId : employeeIds) {
            if (!userService.isUserInCompany(employeeId, companyId)) {
                return GlobalResponseCode.UserIsBlockUp;
            }
        }
        return GlobalResponseCode.Success;
    }

    /**
     * 检验自定义角色是否存在
     *
     * @param customRoleIds
     * @param companyId
     * @return
     */
    private GlobalResponseCode checkCustomRoleAllExist(List<String> customRoleIds, String companyId) {
        if (customRoleIds == null || customRoleIds.size() == 0) {
            return GlobalResponseCode.Success;
        }
        List<com.fenbeitong.usercenter.api.model.po.privilege.Customrole> roles = customRoleService.getRoleListByCompanyId(companyId);
        if (roles == null || roles.size() == 0) {
            return GlobalResponseCode.CustomRoleNotExists;
        }
        for (String customRoleId : customRoleIds) {
            if (!"zhuguan".equals(customRoleId)) { //不判断主管
                if (!roles.stream().anyMatch(m -> m.getId().equals(customRoleId))) {
                    return GlobalResponseCode.CustomRoleNotExists;
                }
            }
        }
        return GlobalResponseCode.Success;
    }

    /**
     * 审批流详情
     *
     * @param id
     * @return
     */
    @Override
    public CompanyApplyFlowSetV2RequestContract getCompanySettingDetail(String id) {
        CompanyApplySettingExample companyApplySettingExample = new CompanyApplySettingExample();
        CompanyApplySettingExample.Criteria criteria = companyApplySettingExample.createCriteria();
        criteria.andIdEqualTo(id).andApplyStatusEqualTo(SaasFlowConstant.IS_NORMAL_STATUS);
        List<CompanyApplySetting> companyApplySettings = companyApplySettingMapper.selectByExample(companyApplySettingExample);
        if (CollectionUtils.isEmpty(companyApplySettings)) {
            return null;
        }
        CompanyApplySetting companyApplySetting = companyApplySettings.get(0);
        return appendCompanyApplySettingContractInfo(companyApplySetting);
    }

    //组装返回的信息
    private CompanyApplyFlowSetV2RequestContract appendCompanyApplySettingContractInfo(CompanyApplySetting companyApplySetting) {
        CompanyApplyFlowSetV2RequestContract flowSetRequestContract = new CompanyApplyFlowSetV2RequestContract();
        flowSetRequestContract.setCompany_setting_type(companyApplySetting.getCompanySettingType());
        flowSetRequestContract.setCan_delete(companyApplySetting.getCanDelete());
        flowSetRequestContract.setId(companyApplySetting.getId());
        flowSetRequestContract.setFlow_name(companyApplySetting.getFlowName());
        flowSetRequestContract.setApply_type(companyApplySetting.getApplyType());
        flowSetRequestContract.setCost_attribution_category(companyApplySetting.getCostAttributionCategory());
        flowSetRequestContract.setCost_center_status(companyApplySetting.getCostCenterStatus());
        flowSetRequestContract.setRole_approve_type(companyApplySetting.getRoleApproveType());
        flowSetRequestContract.setAbnormalApproval(false);//正常审批默认正常
        List<ApplyFlow> applyFlowList = applyFlowExtMapper.queryByCompanyApplySettingId(companyApplySetting.getId());
        if (CollectionUtils.isEmpty(applyFlowList)) {
            return flowSetRequestContract;
        }
        ApplyFlow normalFlow = null;
        ApplyFlow exceedFlow = null;
        ApplyFlow refundFlow = null;
        ApplyFlow changeFlow = null;
        String normalFlowId = null;
        String exceedFlowId = null;
        String refundFlowId = null;
        String changeFlowId = null;
        flowSetRequestContract.setUse_beyond_rule_flow(0);
        for (ApplyFlow applyFlow : applyFlowList) {
            if (applyFlow.getIsExceedBuyFlow() == 0) { //正常流程
                normalFlow = applyFlow;
                normalFlowId = applyFlow.getId();
            } else if (applyFlow.getIsExceedBuyFlow() == 1) { //超规流程
                exceedFlow = applyFlow;
                exceedFlowId = applyFlow.getId();
                flowSetRequestContract.setUse_beyond_rule_flow(1);
            } else if (applyFlow.getIsExceedBuyFlow() == 2) { //退订
                refundFlow = applyFlow;
                refundFlowId = applyFlow.getId();
                flowSetRequestContract.setUse_refund_rule_flow(1);
            } else if (applyFlow.getIsExceedBuyFlow() == 3) { //改期
                changeFlow = applyFlow;
                changeFlowId = applyFlow.getId();
                flowSetRequestContract.setUse_change_rule_flow(1);
            }
        }

        CompanySettingType companySettingType = CompanySettingType.valueOf(companyApplySetting.getCompanySettingType());
        ApplyType applyType = ApplyType.valueOf(companyApplySetting.getApplyType());
        String companyId = companyApplySetting.getCompanyId();

        // 员工id集合
        List<String> employeeIds = Lists.newArrayList();
        // 审批全部节点批量查询
        List<String> applyFlowIds = applyFlowList.stream().map(applyFlow -> applyFlow.getId()).collect(Collectors.toList());
        List<ApplyFlowItem> allFlowItemList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(applyFlowIds)) {
            allFlowItemList = applyFlowItemExtMapper.queryConditionalListByFlowIds(applyFlowIds);
        }
        if (CollectionUtils.isNotEmpty(allFlowItemList)) {
            for (ApplyFlowItem applyFlowItem : allFlowItemList) {
                // 审批节点对象类型为员工 添加审批员工id到列表
                if (applyFlowItem.getItemType() == ApplyFlowItemType.Employee.getValue() && !employeeIds.contains(applyFlowItem.getItemId())) {
                    employeeIds.add(applyFlowItem.getItemId());
                }
            }
        }

        // 抄送全部节点批量查询
        List<ApplyFlowCopyTo> allApplyFlowCopyToList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(applyFlowIds)) {
            allApplyFlowCopyToList = applyFlowCopyToExtMapper.selectListByIds(applyFlowIds);
        }
        if (CollectionUtils.isNotEmpty(allApplyFlowCopyToList)) {
            for (ApplyFlowCopyTo applyFlowCopyTo : allApplyFlowCopyToList) {
                // 抄送节点对象类型为员工 添加抄送员工id到列表
                if (applyFlowCopyTo.getItemType() == ApplyFlowItemType.Employee.getValue() && !employeeIds.contains(applyFlowCopyTo.getItemId())) {
                    employeeIds.add(applyFlowCopyTo.getItemId());
                }
            }
        }
        //员工id集合
        List<EmployeeContract> employeeList = iBaseOrganizationService.getEmployee(employeeIds, companyId);
        Map<String, EmployeeContract> employeeMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(employeeList)) {
            for (EmployeeContract employeeContract : employeeList) {
                employeeMap.put(employeeContract.getId(), employeeContract);
            }
        }

        // 正常审批流配置
        List<CompanyApplyFlowItemSetV2RequestContract> normalFixationFlowList = null;
        List<CompanyApplySettingConditionRequestContract> normalConditionalFlowList = null;
        Integer normalCcNoticeType = null;
        List<CompanyApplyFlowItemSetV2RequestContract> normalCcList = null;
        Integer normalCompanyApplyType = null;
        if (!StringUtils.isBlank(normalFlowId)) {
            normalCcNoticeType = normalFlow.getCcNoticeType();
            normalCompanyApplyType = normalFlow.getCompanyApplyType();
            //固定审批流
            if (normalCompanyApplyType == 2) {
                //审批人列表
                List<ApplyFlowItem> oldItems = applyFlowItemExtMapper.selectListByFlowId(normalFlowId);
                if (CollectionUtils.isEmpty(oldItems)) {
                    return null;
                }
                normalFixationFlowList = handleApplyFlowItems(oldItems, null, companyId, employeeMap, flowSetRequestContract);
            }
            //分条件审批流
            if (normalCompanyApplyType == 3) {
                //审批人列表
                List<ApplyFlowItem> oldItems = applyFlowItemExtMapper.queryConditionalListByFlowId(normalFlowId);
                List<BigDecimal> item = Lists.newLinkedList();
                List<CompanyApplySettingConditionRequestContract> companyApplySettingConditionRequestContracts = Lists.newArrayList();
                if (CollectionUtils.isEmpty(oldItems)) {
                    return null;
                }
                for (ApplyFlowItem applyFlowItem : oldItems) {
                    if (applyFlowItem.getSort() == 1) {
                        item.add(applyFlowItem.getConditionMin());
                    }
                }
                for (BigDecimal bg : item) {
                    CompanyApplySettingConditionRequestContract conditionDetail = new CompanyApplySettingConditionRequestContract();
                    List<ApplyFlowItem> applyFlowItems = applyFlowItemExtMapper.selectConditionalListByFlowId(normalFlowId, bg);
                    BigDecimal min = applyFlowItems.get(0).getConditionMin();
                    BigDecimal max = applyFlowItems.get(0).getConditionMax();
                    conditionDetail.setCondition_min(min);
                    if (max != null) {
                        conditionDetail.setCondition_max(max);
                    }
                    conditionDetail.setFlow(handleApplyFlowItems(applyFlowItems, null, companyId, employeeMap, flowSetRequestContract));
                    companyApplySettingConditionRequestContracts.add(conditionDetail);
                }
                normalConditionalFlowList = companyApplySettingConditionRequestContracts;
            }
            //抄送人列表
            List<ApplyFlowCopyTo> applyFlowCopyTos = applyFlowCopyToExtMapper.selectListById(normalFlowId);
            normalCcList = handleApplyFlowItems(null, applyFlowCopyTos, companyId, employeeMap, flowSetRequestContract);
        }
        //超规审批流配置
        List<CompanyApplyFlowItemSetV2RequestContract> exceedFixationFlowList = null;
        List<CompanyApplySettingConditionRequestContract> exceedConditionalFlowList = null;
        Integer exceedCcNoticeType = null;
        List<CompanyApplyFlowItemSetV2RequestContract> exceedCcList = null;
        Integer exceedCompanyApplyType = null;
        if (!StringUtils.isBlank(exceedFlowId)) {
            exceedCcNoticeType = exceedFlow.getCcNoticeType();
            exceedCompanyApplyType = exceedFlow.getCompanyApplyType();
            //固定审批流
            if (exceedCompanyApplyType == 2) {
                //审批人列表
                List<ApplyFlowItem> oldItems = applyFlowItemExtMapper.selectListByFlowId(exceedFlowId);
                if (CollectionUtils.isEmpty(oldItems)) {
                    return null;
                }
                exceedFixationFlowList = handleApplyFlowItems(oldItems, null, companyId, employeeMap, flowSetRequestContract);
            }
            //分条件审批流
            if (exceedCompanyApplyType == 3) {
                //审批人列表
                List<ApplyFlowItem> oldItems = applyFlowItemExtMapper.queryConditionalListByFlowId(exceedFlowId);
                List<BigDecimal> item = Lists.newLinkedList();
                List<CompanyApplySettingConditionRequestContract> companyApplySettingConditionRequestContracts = Lists.newArrayList();
                if (CollectionUtils.isEmpty(oldItems)) {
                    return null;
                }
                for (ApplyFlowItem applyFlowItem : oldItems) {
                    if (applyFlowItem.getSort() == 1) {
                        item.add(applyFlowItem.getConditionMin());
                    }
                }
                for (BigDecimal bg : item) {
                    CompanyApplySettingConditionRequestContract conditionDetail = new CompanyApplySettingConditionRequestContract();
                    List<ApplyFlowItem> applyFlowItems = applyFlowItemExtMapper.selectConditionalListByFlowId(exceedFlowId, bg);
                    BigDecimal min = applyFlowItems.get(0).getConditionMin();
                    BigDecimal max = applyFlowItems.get(0).getConditionMax();
                    conditionDetail.setCondition_min(min);
                    if (max != null) {
                        conditionDetail.setCondition_max(max);
                    }
                    conditionDetail.setFlow(handleApplyFlowItems(applyFlowItems, null, companyId, employeeMap, flowSetRequestContract));
                    companyApplySettingConditionRequestContracts.add(conditionDetail);
                }
                exceedConditionalFlowList = companyApplySettingConditionRequestContracts;
            }
            //抄送人列表
            List<ApplyFlowCopyTo> applyFlowCopyTos = applyFlowCopyToExtMapper.selectListById(exceedFlowId);
            exceedCcList = handleApplyFlowItems(null, applyFlowCopyTos, companyId, employeeMap, flowSetRequestContract);
        }

        //退订审批流配置
        List<CompanyApplyFlowItemSetV2RequestContract> refundFixationFlowList = null;
        List<CompanyApplySettingConditionRequestContract> refundConditionalFlowList = null;
        List<CompanyApplyFlowItemSetV2RequestContract> refundCcList = null;
        Integer refundCompanyApplyType = null;
        if (!StringUtils.isBlank(refundFlowId)) {
            refundCompanyApplyType = refundFlow.getCompanyApplyType();
            //固定审批流
            if (refundCompanyApplyType == 2) {
                //审批人列表
                List<ApplyFlowItem> oldItems = applyFlowItemExtMapper.selectListByFlowId(refundFlowId);
                if (CollectionUtils.isEmpty(oldItems)) {
                    return null;
                }
                refundFixationFlowList = handleApplyFlowItems(oldItems, null, companyId, flowSetRequestContract);
            }
            //分条件审批流
            if (refundCompanyApplyType == 3) {
                //审批人列表
                List<ApplyFlowItem> oldItems = applyFlowItemExtMapper.queryConditionalListByFlowId(refundFlowId);
                List<BigDecimal> item = Lists.newLinkedList();
                List<CompanyApplySettingConditionRequestContract> companyApplySettingConditionRequestContracts = Lists.newArrayList();
                if (CollectionUtils.isEmpty(oldItems)) {
                    return null;
                }
                for (ApplyFlowItem applyFlowItem : oldItems) {
                    if (applyFlowItem.getSort() == 1) {
                        item.add(applyFlowItem.getConditionMin());
                    }
                }
                for (BigDecimal bg : item) {
                    CompanyApplySettingConditionRequestContract conditionDetail = new CompanyApplySettingConditionRequestContract();
                    List<ApplyFlowItem> applyFlowItems = applyFlowItemExtMapper.selectConditionalListByFlowId(refundFlowId, bg);
                    BigDecimal min = applyFlowItems.get(0).getConditionMin();
                    BigDecimal max = applyFlowItems.get(0).getConditionMax();
                    conditionDetail.setCondition_min(min);
                    if (max != null) {
                        conditionDetail.setCondition_max(max);
                    }
                    conditionDetail.setFlow(handleApplyFlowItems(applyFlowItems, null, companyId, flowSetRequestContract));
                    companyApplySettingConditionRequestContracts.add(conditionDetail);
                }
                refundConditionalFlowList = companyApplySettingConditionRequestContracts;
            }
            //抄送人列表
            List<ApplyFlowCopyTo> applyFlowCopyTos = applyFlowCopyToExtMapper.selectListById(refundFlowId);
            refundCcList = handleApplyFlowItems(null, applyFlowCopyTos, companyId, flowSetRequestContract);
        }

        //改期审批流配置
        List<CompanyApplyFlowItemSetV2RequestContract> changeFixationFlowList = null;
        List<CompanyApplySettingConditionRequestContract> changeConditionalFlowList = null;
        List<CompanyApplyFlowItemSetV2RequestContract> changeCcList = null;
        Integer changeCompanyApplyType = null;
        if (!StringUtils.isBlank(changeFlowId)) {
            changeCompanyApplyType = changeFlow.getCompanyApplyType();
            //固定审批流
            if (changeCompanyApplyType == 2) {
                //审批人列表
                List<ApplyFlowItem> oldItems = applyFlowItemExtMapper.selectListByFlowId(changeFlowId);
                if (CollectionUtils.isEmpty(oldItems)) {
                    return null;
                }
                changeFixationFlowList = handleApplyFlowItems(oldItems, null, companyId, flowSetRequestContract);
            }
            //分条件审批流
            if (changeCompanyApplyType == 3) {
                //审批人列表
                List<ApplyFlowItem> oldItems = applyFlowItemExtMapper.queryConditionalListByFlowId(changeFlowId);
                List<BigDecimal> item = Lists.newLinkedList();
                List<CompanyApplySettingConditionRequestContract> companyApplySettingConditionRequestContracts = Lists.newArrayList();
                if (CollectionUtils.isEmpty(oldItems)) {
                    return null;
                }
                for (ApplyFlowItem applyFlowItem : oldItems) {
                    if (applyFlowItem.getSort() == 1) {
                        item.add(applyFlowItem.getConditionMin());
                    }
                }
                for (BigDecimal bg : item) {
                    CompanyApplySettingConditionRequestContract conditionDetail = new CompanyApplySettingConditionRequestContract();
                    List<ApplyFlowItem> applyFlowItems = applyFlowItemExtMapper.selectConditionalListByFlowId(changeFlowId, bg);
                    BigDecimal min = applyFlowItems.get(0).getConditionMin();
                    BigDecimal max = applyFlowItems.get(0).getConditionMax();
                    conditionDetail.setCondition_min(min);
                    if (max != null) {
                        conditionDetail.setCondition_max(max);
                    }
                    conditionDetail.setFlow(handleApplyFlowItems(applyFlowItems, null, companyId, flowSetRequestContract));
                    companyApplySettingConditionRequestContracts.add(conditionDetail);
                }
                changeConditionalFlowList = companyApplySettingConditionRequestContracts;
            }
            //抄送人列表
            List<ApplyFlowCopyTo> applyFlowCopyTos = applyFlowCopyToExtMapper.selectListById(changeFlowId);
            changeCcList = handleApplyFlowItems(null, applyFlowCopyTos, companyId, flowSetRequestContract);
        }

        if (applyType == ApplyType.Dinner ||
                (companySettingType == CompanySettingType.CenterApply
                        && applyType == ApplyType.ChaiLv)) {
            // 正常审批流配置
            if (normalFlow != null) {
                flowSetRequestContract.setNormal_company_apply_type(normalCompanyApplyType);
                flowSetRequestContract.setNormal_fixation_flow_list(normalFixationFlowList);
                flowSetRequestContract.setNormal_conditional_flow_list(normalConditionalFlowList);
                flowSetRequestContract.setNormal_cc_notice_type(normalCcNoticeType);
                if (normalCcNoticeType != null) {
                    flowSetRequestContract.setNormal_cc_notice_type_enums(new KvContract(normalCcNoticeType, CcNoticeType.valueOf(normalCcNoticeType).getDesc()));
                }
                flowSetRequestContract.setNormal_cc_list(normalCcList);
            }
            // 超规审批流配置
            if (exceedFlow != null) {
                flowSetRequestContract.setCompany_apply_type(exceedCompanyApplyType);
                flowSetRequestContract.setFixation_flow_list(exceedFixationFlowList);
                flowSetRequestContract.setConditional_flow_list(exceedConditionalFlowList);
                flowSetRequestContract.setCc_notice_type(exceedCcNoticeType);
                if (exceedCcNoticeType != null) {
                    flowSetRequestContract.setCc_notice_type_enums(new KvContract(exceedCcNoticeType, CcNoticeType.valueOf(exceedCcNoticeType).getDesc()));
                }
                flowSetRequestContract.setCc_list(exceedCcList);
            }
            // 退订审批流配置
            if (refundFlow != null) {
                flowSetRequestContract.setRefund_company_apply_type(refundCompanyApplyType);
                flowSetRequestContract.setRefund_fixation_flow_list(refundFixationFlowList);
                flowSetRequestContract.setRefund_conditional_flow_list(refundConditionalFlowList);
                flowSetRequestContract.setRefund_cc_list(refundCcList);
            }
            // 改期审批流配置
            if (changeFlow != null) {
                flowSetRequestContract.setChange_company_apply_type(changeCompanyApplyType);
                flowSetRequestContract.setChange_fixation_flow_list(changeFixationFlowList);
                flowSetRequestContract.setChange_conditional_flow_list(changeConditionalFlowList);
                flowSetRequestContract.setChange_cc_list(changeCcList);
            }
        } else {
            // 正常审批流配置
            if (normalFlow != null) {
                flowSetRequestContract.setCompany_apply_type(normalCompanyApplyType);
                flowSetRequestContract.setFixation_flow_list(normalFixationFlowList);
                flowSetRequestContract.setConditional_flow_list(normalConditionalFlowList);
                flowSetRequestContract.setCc_notice_type(normalCcNoticeType);
                if (normalCcNoticeType != null) {
                    flowSetRequestContract.setCc_notice_type_enums(new KvContract(normalCcNoticeType, CcNoticeType.valueOf(normalCcNoticeType).getDesc()));
                }
                flowSetRequestContract.setCc_list(normalCcList);
            }
        }
        return flowSetRequestContract;
    }

    /**
     * 批量组装返回的信息
     *
     * @param companyApplySettingList
     * @param historyVersion
     * @return
     */
    private List<CompanyApplyFlowSetV2RequestContract> batchAppendCompanyApplySettingContractInfo(List<CompanyApplySetting> companyApplySettingList, boolean historyVersion) {
        if (CollectionUtils.isEmpty(companyApplySettingList)) {
            return Lists.newArrayList();
        }
        // 审配流配置批量查询列表
        List<String> companyApplySettingIds = companyApplySettingList.stream().map(companyApplySetting -> companyApplySetting.getId()).collect(Collectors.toList());
        List<ApplyFlow> allApplyFlowList = applyFlowExtMapper.queryByCompanyApplySettingIds(companyApplySettingIds);
        Map<String, List<ApplyFlow>> applyFlowMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(allApplyFlowList)) {
            for (ApplyFlow applyFlow : allApplyFlowList) {
                String companyApplySettingId = applyFlow.getCompanyApplySettingId();
                List<ApplyFlow> tmpApplyFlowList;
                if (applyFlowMap.containsKey(companyApplySettingId)) {
                    tmpApplyFlowList = applyFlowMap.get(companyApplySettingId);
                } else {
                    tmpApplyFlowList = Lists.newArrayList();
                    applyFlowMap.put(companyApplySettingId, tmpApplyFlowList);
                }
                tmpApplyFlowList.add(applyFlow);
            }
        }

        // 员工id集合
        List<String> employeeIds = Lists.newArrayList();
        // 审批全部节点批量查询
        List<String> applyFlowIds = allApplyFlowList.stream().map(applyFlow -> applyFlow.getId()).collect(Collectors.toList());
        List<ApplyFlowItem> allFlowItemList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(applyFlowIds)) {
            allFlowItemList = applyFlowItemExtMapper.queryConditionalListByFlowIds(applyFlowIds);
        }
        Map<String, List<ApplyFlowItem>> applyFlowItemMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(allFlowItemList)) {
            for (ApplyFlowItem applyFlowItem : allFlowItemList) {
                String applyFlowId = applyFlowItem.getApplyFlowId();
                List<ApplyFlowItem> tmpApplyFlowItemList;
                if (applyFlowItemMap.containsKey(applyFlowId)) {
                    tmpApplyFlowItemList = applyFlowItemMap.get(applyFlowId);
                } else {
                    tmpApplyFlowItemList = Lists.newArrayList();
                    applyFlowItemMap.put(applyFlowId, tmpApplyFlowItemList);
                }
                tmpApplyFlowItemList.add(applyFlowItem);
                // 审批节点对象类型为员工 添加审批员工id到列表
                if (applyFlowItem.getItemType() == ApplyFlowItemType.Employee.getValue() && !employeeIds.contains(applyFlowItem.getItemId())) {
                    employeeIds.add(applyFlowItem.getItemId());
                }
            }
        }

        // 抄送全部节点批量查询
        List<ApplyFlowCopyTo> allApplyFlowCopyToList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(applyFlowIds)) {
            allApplyFlowCopyToList = applyFlowCopyToExtMapper.selectListByIds(applyFlowIds);
        }
        Map<String, List<ApplyFlowCopyTo>> applyFlowCopyToMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(allApplyFlowCopyToList)) {
            for (ApplyFlowCopyTo applyFlowCopyTo : allApplyFlowCopyToList) {
                String applyFlowId = applyFlowCopyTo.getApplyFlowId();
                List<ApplyFlowCopyTo> tmpApplyFlowCopyToList;
                if (applyFlowCopyToMap.containsKey(applyFlowId)) {
                    tmpApplyFlowCopyToList = applyFlowCopyToMap.get(applyFlowId);
                } else {
                    tmpApplyFlowCopyToList = Lists.newArrayList();
                    applyFlowCopyToMap.put(applyFlowId, tmpApplyFlowCopyToList);
                }
                tmpApplyFlowCopyToList.add(applyFlowCopyTo);
                // 抄送节点对象类型为员工 添加抄送员工id到列表
                if (applyFlowCopyTo.getItemType() == ApplyFlowItemType.Employee.getValue() && !employeeIds.contains(applyFlowCopyTo.getItemId())) {
                    employeeIds.add(applyFlowCopyTo.getItemId());
                }
            }
        }

        // 查询应用的部门总数
        Map<String, Integer> departmentCountMap = Maps.newHashMap();
        List<CountStatContract> departmentCountList = companyApplySettingUseExtMapper.getCountByCompanyApplySettingIds(companyApplySettingIds, SettingType.Department.getValue());
        for (CountStatContract countStat : departmentCountList) {
            departmentCountMap.put(countStat.getId(), countStat.getCount());
        }

        // 查询应用的项目总数
        Map<String, Integer> projectCountMap = Maps.newHashMap();
        List<CountStatContract> projectCountList = companyApplySettingUseExtMapper.getCountByCompanyApplySettingIds(companyApplySettingIds, SettingType.Project.getValue());
        for (CountStatContract countStat : projectCountList) {
            projectCountMap.put(countStat.getId(), countStat.getCount());
        }

        // 查询部门应用列表
        List<CompanyApplySettingUse> allCompanyApplySettingUseList = companyApplySettingUseExtMapper.querySettingUseByCompanyApplySettingIds(companyApplySettingIds, SettingType.Department.getValue());
        Map<String, List<CompanyApplySettingUse>> companyApplySettingUseMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(allCompanyApplySettingUseList)) {
            for (CompanyApplySettingUse companyApplySettingUse : allCompanyApplySettingUseList) {
                String companyApplySettingId = companyApplySettingUse.getCompanyApplySettingId();
                List<CompanyApplySettingUse> tmpCompanyApplySettingUseList;
                if (companyApplySettingUseMap.containsKey(companyApplySettingId)) {
                    tmpCompanyApplySettingUseList = companyApplySettingUseMap.get(companyApplySettingId);
                } else {
                    tmpCompanyApplySettingUseList = Lists.newArrayList();
                    companyApplySettingUseMap.put(companyApplySettingId, tmpCompanyApplySettingUseList);
                }
                tmpCompanyApplySettingUseList.add(companyApplySettingUse);
            }
        }

        //员工id集合
        //List<EmployeeContract> employeeList = iBaseOrganizationService.getEmployee(employeeIds, companyApplySettingList.get(0).getCompanyId());
//        List<EmployeeContract> employeeList = sloveEmployeeListInfo(employeeIds, companyApplySettingList.get(0).getCompanyId());

        List<EmployeeContract> employeeList =  getQueryEmployeeListInfo.employeeListInfo(employeeIds, companyApplySettingList.get(0).getCompanyId());
        Map<String, EmployeeContract> employeeMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(employeeList)) {
            for (EmployeeContract employeeContract : employeeList) {
                employeeMap.put(employeeContract.getId(), employeeContract);
            }
        }


        List<CompanyApplyFlowSetV2RequestContract> budgetSettingContractList = Lists.newArrayList();
        for (CompanyApplySetting companyApplySetting : companyApplySettingList) {
            CompanyApplyFlowSetV2RequestContract flowSetRequestContract = new CompanyApplyFlowSetV2RequestContract();
            flowSetRequestContract.setCompany_setting_type(companyApplySetting.getCompanySettingType());
            flowSetRequestContract.setCan_delete(companyApplySetting.getCanDelete());
            flowSetRequestContract.setId(companyApplySetting.getId());
            flowSetRequestContract.setFlow_name(companyApplySetting.getFlowName());
            flowSetRequestContract.setApply_type(companyApplySetting.getApplyType());
            flowSetRequestContract.setCost_attribution_category(companyApplySetting.getCostAttributionCategory());
            flowSetRequestContract.setCost_center_status(companyApplySetting.getCostCenterStatus());
            flowSetRequestContract.setRole_approve_type(companyApplySetting.getRoleApproveType());
            flowSetRequestContract.setAbnormalApproval(false);//默认正常
            List<ApplyFlow> applyFlowList = applyFlowMap.get(companyApplySetting.getId());
            if (CollectionUtils.isEmpty(applyFlowList)) {
                budgetSettingContractList.add(flowSetRequestContract);
                continue;
            }
            ApplyFlow normalFlow = null;
            ApplyFlow exceedFlow = null;
            ApplyFlow refundFlow = null;
            ApplyFlow changeFlow = null;
            String normalFlowId = null;
            String exceedFlowId = null;
            String refundFlowId = null;
            String changeFlowId = null;

            flowSetRequestContract.setUse_beyond_rule_flow(0);
            for (ApplyFlow applyFlow : applyFlowList) {
                if (applyFlow.getIsExceedBuyFlow() == 0) { //正常流程
                    normalFlow = applyFlow;
                    normalFlowId = applyFlow.getId();
                } else if (applyFlow.getIsExceedBuyFlow() == 1) { //超规流程
                    exceedFlow = applyFlow;
                    exceedFlowId = applyFlow.getId();
                    flowSetRequestContract.setUse_beyond_rule_flow(1);
                } else if (applyFlow.getIsExceedBuyFlow() == 2) { //退订
                    refundFlow = applyFlow;
                    refundFlowId = applyFlow.getId();
                    flowSetRequestContract.setUse_refund_rule_flow(1);
                } else if (applyFlow.getIsExceedBuyFlow() == 3) { //改期
                    changeFlow = applyFlow;
                    changeFlowId = applyFlow.getId();
                    flowSetRequestContract.setUse_change_rule_flow(1);
                }
            }
            if (historyVersion && flowSetRequestContract.getCompany_setting_type() == 2
                    && flowSetRequestContract.getUse_beyond_rule_flow() == 0) {
                throw new SaasException(GlobalResponseCode.VersionError404);
            }
            CompanySettingType companySettingType = CompanySettingType.valueOf(companyApplySetting.getCompanySettingType());
            ApplyType applyType = ApplyType.valueOf(companyApplySetting.getApplyType());
            String companyId = companyApplySetting.getCompanyId();
            String companyApplySettingId = companyApplySetting.getId();
            // 正常审批流配置
            List<CompanyApplyFlowItemSetV2RequestContract> normalFixationFlowList = null;
            List<CompanyApplySettingConditionRequestContract> normalConditionalFlowList = null;
            Integer normalCcNoticeType = null;
            List<CompanyApplyFlowItemSetV2RequestContract> normalCcList = null;
            Integer normalCompanyApplyType = null;
            if (!StringUtils.isBlank(normalFlowId)) {
                normalCcNoticeType = normalFlow.getCcNoticeType();
                normalCompanyApplyType = normalFlow.getCompanyApplyType();
                //固定审批流
                if (normalCompanyApplyType == 2) {
                    //审批人列表
                    List<ApplyFlowItem> oldItems = applyFlowItemMap.get(normalFlowId);
                    if (CollectionUtils.isEmpty(oldItems)) {
                        continue;
                        //return null;
                    }
                    normalFixationFlowList = handleApplyFlowItems(oldItems, null, companyId,  employeeMap, flowSetRequestContract);
                }
                //分条件审批流
                if (normalCompanyApplyType == 3) {
                    //审批人列表
                    List<ApplyFlowItem> oldItems = applyFlowItemMap.get(normalFlowId);
                    List<CompanyApplySettingConditionRequestContract> companyApplySettingConditionRequestContracts = Lists.newArrayList();
                    if (CollectionUtils.isEmpty(oldItems)) {
                        continue;
                        //return null;
                    }
                    Map<BigDecimal, List<ApplyFlowItem>> conditionMap = Maps.newLinkedHashMap();
                    for (ApplyFlowItem applyFlowItem : oldItems) {
                        List<ApplyFlowItem> items;
                        if (applyFlowItem.getSort() == 1) {
                            items = Lists.newArrayList();
                            conditionMap.put(applyFlowItem.getConditionMin(), items);
                        } else {
                            items = conditionMap.get(applyFlowItem.getConditionMin());
                        }
                        items.add(applyFlowItem);
                    }
                    for (BigDecimal bg : conditionMap.keySet()) {
                        CompanyApplySettingConditionRequestContract conditionDetail = new CompanyApplySettingConditionRequestContract();
                        List<ApplyFlowItem> applyFlowItems = conditionMap.get(bg);
                        BigDecimal min = applyFlowItems.get(0).getConditionMin();
                        BigDecimal max = applyFlowItems.get(0).getConditionMax();
                        conditionDetail.setCondition_min(min);
                        if (max != null) {
                            conditionDetail.setCondition_max(max);
                        }
                        conditionDetail.setFlow(handleApplyFlowItems(applyFlowItems, null, companyId,  employeeMap, flowSetRequestContract));
                        companyApplySettingConditionRequestContracts.add(conditionDetail);
                    }
                    normalConditionalFlowList = companyApplySettingConditionRequestContracts;
                }
                //抄送人列表
                List<ApplyFlowCopyTo> applyFlowCopyTos = applyFlowCopyToMap.get(normalFlowId);
                normalCcList = handleApplyFlowItems(null, applyFlowCopyTos, companyId,  employeeMap, flowSetRequestContract);
            }
            // 超规审批流配置
            List<CompanyApplyFlowItemSetV2RequestContract> exceedFixationFlowList = null;
            List<CompanyApplySettingConditionRequestContract> exceedConditionalFlowList = null;
            Integer exceedCcNoticeType = null;
            List<CompanyApplyFlowItemSetV2RequestContract> exceedCcList = null;
            Integer exceedCompanyApplyType = null;
            if (!StringUtils.isBlank(exceedFlowId)) {
                exceedCcNoticeType = exceedFlow.getCcNoticeType();
                exceedCompanyApplyType = exceedFlow.getCompanyApplyType();
                //固定审批流
                if (exceedCompanyApplyType == 2) {
                    //审批人列表
                    List<ApplyFlowItem> oldItems = applyFlowItemMap.get(exceedFlowId);
                    if (CollectionUtils.isEmpty(oldItems)) {
                        continue;
                        //return null;
                    }
                    exceedFixationFlowList = handleApplyFlowItems(oldItems, null, companyId, employeeMap, flowSetRequestContract);
                }
                //分条件审批流
                if (exceedCompanyApplyType == 3) {
                    //审批人列表
                    List<ApplyFlowItem> oldItems = applyFlowItemMap.get(exceedFlowId);
                    List<CompanyApplySettingConditionRequestContract> companyApplySettingConditionRequestContracts = Lists.newArrayList();
                    if (CollectionUtils.isEmpty(oldItems)) {
                        continue;
                        //return null;
                    }

                    Map<BigDecimal, List<ApplyFlowItem>> conditionMap = Maps.newLinkedHashMap();
                    for (ApplyFlowItem applyFlowItem : oldItems) {
                        List<ApplyFlowItem> items;
                        if (applyFlowItem.getSort() == 1) {
                            items = Lists.newArrayList();
                            conditionMap.put(applyFlowItem.getConditionMin(), items);
                        } else {
                            items = conditionMap.get(applyFlowItem.getConditionMin());
                        }
                        items.add(applyFlowItem);
                    }
                    for (BigDecimal bg : conditionMap.keySet()) {
                        CompanyApplySettingConditionRequestContract conditionDetail = new CompanyApplySettingConditionRequestContract();
                        List<ApplyFlowItem> applyFlowItems = conditionMap.get(bg);
                        BigDecimal min = applyFlowItems.get(0).getConditionMin();
                        BigDecimal max = applyFlowItems.get(0).getConditionMax();
                        conditionDetail.setCondition_min(min);
                        if (max != null) {
                            conditionDetail.setCondition_max(max);
                        }
                        conditionDetail.setFlow(handleApplyFlowItems(applyFlowItems, null, companyId, employeeMap, flowSetRequestContract));
                        companyApplySettingConditionRequestContracts.add(conditionDetail);
                    }
                    exceedConditionalFlowList = companyApplySettingConditionRequestContracts;
                }
                //抄送人列表
                List<ApplyFlowCopyTo> applyFlowCopyTos = applyFlowCopyToMap.get(exceedFlowId);
                exceedCcList = handleApplyFlowItems(null, applyFlowCopyTos, companyId, employeeMap, flowSetRequestContract);
            }

            //退订审批流配置
            List<CompanyApplyFlowItemSetV2RequestContract> refundFixationFlowList = null;
            List<CompanyApplySettingConditionRequestContract> refundConditionalFlowList = null;
            List<CompanyApplyFlowItemSetV2RequestContract> refundCcList = null;
            Integer refundCompanyApplyType = null;
            if (!StringUtils.isBlank(refundFlowId)) {
                refundCompanyApplyType = refundFlow.getCompanyApplyType();
                //固定审批流
                if (refundCompanyApplyType == 2) {
                    //审批人列表
                    List<ApplyFlowItem> oldItems = applyFlowItemExtMapper.selectListByFlowId(refundFlowId);
                    if (CollectionUtils.isEmpty(oldItems)) {
                        continue;
                        //return null;
                    }
                    refundFixationFlowList = handleApplyFlowItems(oldItems, null, companyId, flowSetRequestContract);
                }
                //分条件审批流
                if (refundCompanyApplyType == 3) {
                    //审批人列表
                    List<ApplyFlowItem> oldItems = applyFlowItemExtMapper.queryConditionalListByFlowId(refundFlowId);
                    List<BigDecimal> item = Lists.newLinkedList();
                    List<CompanyApplySettingConditionRequestContract> companyApplySettingConditionRequestContracts = Lists.newArrayList();
                    if (CollectionUtils.isEmpty(oldItems)) {
                        continue;
                        //return null;
                    }
                    for (ApplyFlowItem applyFlowItem : oldItems) {
                        if (applyFlowItem.getSort() == 1) {
                            item.add(applyFlowItem.getConditionMin());
                        }
                    }
                    for (BigDecimal bg : item) {
                        CompanyApplySettingConditionRequestContract conditionDetail = new CompanyApplySettingConditionRequestContract();
                        List<ApplyFlowItem> applyFlowItems = applyFlowItemExtMapper.selectConditionalListByFlowId(refundFlowId, bg);
                        BigDecimal min = applyFlowItems.get(0).getConditionMin();
                        BigDecimal max = applyFlowItems.get(0).getConditionMax();
                        conditionDetail.setCondition_min(min);
                        if (max != null) {
                            conditionDetail.setCondition_max(max);
                        }
                        conditionDetail.setFlow(handleApplyFlowItems(applyFlowItems, null, companyId, flowSetRequestContract));
                        companyApplySettingConditionRequestContracts.add(conditionDetail);
                    }
                    refundConditionalFlowList = companyApplySettingConditionRequestContracts;
                }
                //抄送人列表
                List<ApplyFlowCopyTo> applyFlowCopyTos = applyFlowCopyToExtMapper.selectListById(refundFlowId);
                refundCcList = handleApplyFlowItems(null, applyFlowCopyTos, companyId, flowSetRequestContract);
            }

            //改期审批流配置
            List<CompanyApplyFlowItemSetV2RequestContract> changeFixationFlowList = null;
            List<CompanyApplySettingConditionRequestContract> changeConditionalFlowList = null;
            List<CompanyApplyFlowItemSetV2RequestContract> changeCcList = null;
            Integer changeCompanyApplyType = null;
            if (!StringUtils.isBlank(changeFlowId)) {
                changeCompanyApplyType = changeFlow.getCompanyApplyType();
                //固定审批流
                if (changeCompanyApplyType == 2) {
                    //审批人列表
                    List<ApplyFlowItem> oldItems = applyFlowItemExtMapper.selectListByFlowId(changeFlowId);
                    if (CollectionUtils.isEmpty(oldItems)) {
                        continue;
                        //return null;
                    }
                    changeFixationFlowList = handleApplyFlowItems(oldItems, null, companyId, flowSetRequestContract);
                }
                //分条件审批流
                if (changeCompanyApplyType == 3) {
                    //审批人列表
                    List<ApplyFlowItem> oldItems = applyFlowItemExtMapper.queryConditionalListByFlowId(changeFlowId);
                    List<BigDecimal> item = Lists.newLinkedList();
                    List<CompanyApplySettingConditionRequestContract> companyApplySettingConditionRequestContracts = Lists.newArrayList();
                    if (CollectionUtils.isEmpty(oldItems)) {
                        continue;
                        //return null;
                    }
                    for (ApplyFlowItem applyFlowItem : oldItems) {
                        if (applyFlowItem.getSort() == 1) {
                            item.add(applyFlowItem.getConditionMin());
                        }
                    }
                    for (BigDecimal bg : item) {
                        CompanyApplySettingConditionRequestContract conditionDetail = new CompanyApplySettingConditionRequestContract();
                        List<ApplyFlowItem> applyFlowItems = applyFlowItemExtMapper.selectConditionalListByFlowId(changeFlowId, bg);
                        BigDecimal min = applyFlowItems.get(0).getConditionMin();
                        BigDecimal max = applyFlowItems.get(0).getConditionMax();
                        conditionDetail.setCondition_min(min);
                        if (max != null) {
                            conditionDetail.setCondition_max(max);
                        }
                        conditionDetail.setFlow(handleApplyFlowItems(applyFlowItems, null, companyId, flowSetRequestContract));
                        companyApplySettingConditionRequestContracts.add(conditionDetail);
                    }
                    changeConditionalFlowList = companyApplySettingConditionRequestContracts;
                }
                //抄送人列表
                List<ApplyFlowCopyTo> applyFlowCopyTos = applyFlowCopyToExtMapper.selectListById(changeFlowId);
                changeCcList = handleApplyFlowItems(null, applyFlowCopyTos, companyId, flowSetRequestContract);
            }

            if (applyType == ApplyType.Dinner ||
                    (companySettingType == CompanySettingType.CenterApply
                            && applyType == ApplyType.ChaiLv)) {
                // 正常审批流配置
                if (normalFlow != null) {
                    flowSetRequestContract.setNormal_company_apply_type(normalCompanyApplyType);
                    flowSetRequestContract.setNormal_fixation_flow_list(normalFixationFlowList);
                    flowSetRequestContract.setNormal_conditional_flow_list(normalConditionalFlowList);
                    flowSetRequestContract.setNormal_cc_notice_type(normalCcNoticeType);
                    if (normalCcNoticeType != null) {
                        flowSetRequestContract.setNormal_cc_notice_type_enums(new KvContract(normalCcNoticeType, CcNoticeType.valueOf(normalCcNoticeType).getDesc()));
                    }
                    flowSetRequestContract.setNormal_cc_list(normalCcList);
                }
                // 超规审批流配置
                if (exceedFlow != null) {
                    flowSetRequestContract.setCompany_apply_type(exceedCompanyApplyType);
                    flowSetRequestContract.setFixation_flow_list(exceedFixationFlowList);
                    flowSetRequestContract.setConditional_flow_list(exceedConditionalFlowList);
                    flowSetRequestContract.setCc_notice_type(exceedCcNoticeType);
                    if (exceedCcNoticeType != null) {
                        flowSetRequestContract.setCc_notice_type_enums(new KvContract(exceedCcNoticeType, CcNoticeType.valueOf(exceedCcNoticeType).getDesc()));
                    }
                    flowSetRequestContract.setCc_list(exceedCcList);
                }
                // 退订审批流配置
                if (refundFlow != null) {
                    flowSetRequestContract.setRefund_company_apply_type(refundCompanyApplyType);
                    flowSetRequestContract.setRefund_fixation_flow_list(refundFixationFlowList);
                    flowSetRequestContract.setRefund_conditional_flow_list(refundConditionalFlowList);
                    flowSetRequestContract.setRefund_cc_list(refundCcList);
                }
                // 改期审批流配置
                if (changeFlow != null) {
                    flowSetRequestContract.setChange_company_apply_type(changeCompanyApplyType);
                    flowSetRequestContract.setChange_fixation_flow_list(changeFixationFlowList);
                    flowSetRequestContract.setChange_conditional_flow_list(changeConditionalFlowList);
                    flowSetRequestContract.setChange_cc_list(changeCcList);
                }
            } else {
                // 正常审批流配置
                if (normalFlow != null) {
                    flowSetRequestContract.setCompany_apply_type(normalCompanyApplyType);
                    flowSetRequestContract.setFixation_flow_list(normalFixationFlowList);
                    flowSetRequestContract.setConditional_flow_list(normalConditionalFlowList);
                    flowSetRequestContract.setCc_notice_type(normalCcNoticeType);
                    if (normalCcNoticeType != null) {
                        flowSetRequestContract.setCc_notice_type_enums(new KvContract(normalCcNoticeType, CcNoticeType.valueOf(normalCcNoticeType).getDesc()));
                    }
                    flowSetRequestContract.setCc_list(normalCcList);
                }
            }
            String companySettingId = flowSetRequestContract.getId();
            //查询应用的部门总数
            int companySettingUseDepartmentCount = ObjUtils.toInteger(departmentCountMap.get(companySettingId), 0);
            //查询应用的项目总数
            int companySettingUseProjectCount = ObjUtils.toInteger(projectCountMap.get(companySettingId), 0);
            List<CompanyApplySettingUse> companyApplySettingUseList = companyApplySettingUseMap.get(companySettingId);
            //判断是否应用至全公司
            if (CollectionUtils.isEmpty(companyApplySettingUseList)) {
                flowSetRequestContract.setApply_for_company(false);
            } else {
                if (companyApplySettingUseList.size() == 1 && companyApplySettingUseList.get(0).getItemId().equals(companyId)) {
                    flowSetRequestContract.setApply_for_company(true);
                } else {
                    flowSetRequestContract.setApply_for_company(false);
                }
            }
            flowSetRequestContract.setCompany_apply_setting_use_count(companySettingUseDepartmentCount);
            flowSetRequestContract.setCompany_apply_setting_use_project_count(companySettingUseProjectCount);
            budgetSettingContractList.add(flowSetRequestContract);
        }
        return budgetSettingContractList;
    }

    /**
     * 代码提取，处理审批列表
     *
     * @param applyFlowItemsItems   审批人列表
     * @param applyFlowCopyTos      抄送人列表
     * @param companyId             公司id
     * @param employeeMap           员工集合
     * @return
     */
    private List<CompanyApplyFlowItemSetV2RequestContract> handleApplyFlowItems(List<ApplyFlowItem> applyFlowItemsItems, List<ApplyFlowCopyTo> applyFlowCopyTos, String companyId, Map<String, EmployeeContract> employeeMap,CompanyApplyFlowSetV2RequestContract flowSetRequestContract) {
        List<CompanyApplyFlowItemSetV2RequestContract> list = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(applyFlowCopyTos)) {
            for (ApplyFlowCopyTo applyFlowCopyTo : applyFlowCopyTos) {
                CompanyApplyFlowItemSetV2RequestContract flowItemSetRequestContract = new CompanyApplyFlowItemSetV2RequestContract();
                if (StringTool.isNullOrEmpty(applyFlowCopyTo.getItemId())) {
                    //app端需要此值=null时传回一个空字符串
                    applyFlowCopyTo.setItemId("");
                }
                flowItemSetRequestContract.setItem_id(applyFlowCopyTo.getItemId());
                flowItemSetRequestContract.setItem_type(applyFlowCopyTo.getItemType());
                list.add(flowItemSetRequestContract);
            }
            appendCustomRoleInfos(list, companyId);
            appendEmployeeInfos(list, companyId, employeeMap);
            appendSuperAdminInfos(list);
            appendAdminInfos(list);
            appendDeptManagerInfos(list);
        }
        if (!CollectionUtils.isEmpty(applyFlowItemsItems)) {
            for (ApplyFlowItem applyFlowItem : applyFlowItemsItems) {
                CompanyApplyFlowItemSetV2RequestContract flowItemSetRequestContract = new CompanyApplyFlowItemSetV2RequestContract();
                if (StringTool.isNullOrEmpty(applyFlowItem.getItemId())) {
                    //app端需要此值=null时传回一个空字符串
                    applyFlowItem.setItemId("");
                }
                flowItemSetRequestContract.setItem_id(applyFlowItem.getItemId());
                flowItemSetRequestContract.setItem_type(applyFlowItem.getItemType());
                list.add(flowItemSetRequestContract);
                if (ObjUtils.isNotEmpty(employeeMap) && applyFlowItem.getItemType() != null && applyFlowItem.getItemType() == 2) {
                    //判断是否公司员工
                    if (!isCompanyEmployee(employeeMap.get(applyFlowItem.getItemId()))) {
                        flowSetRequestContract.setAbnormalApproval(true);
                    }
                }
            }
            appendCustomRoleInfos(list, companyId);
            appendEmployeeInfosWhioutFilter(list, companyId, employeeMap);
            appendSuperAdminInfos(list);
            appendAdminInfos(list);
            appendDeptManagerInfos(list);
        }
        return list;
    }

    /**
     * 代码提取，处理审批列表
     *
     * @param applyFlowItemsItems    审批人列表
     * @param applyFlowCopyTos       抄送人列表
     * @param companyId              公司id
     * @param flowSetRequestContract
     * @return
     */
    private List<CompanyApplyFlowItemSetV2RequestContract> handleApplyFlowItems(List<ApplyFlowItem> applyFlowItemsItems, List<ApplyFlowCopyTo> applyFlowCopyTos, String companyId, CompanyApplyFlowSetV2RequestContract flowSetRequestContract) {
        List<CompanyApplyFlowItemSetV2RequestContract> list = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(applyFlowCopyTos)) {
            for (ApplyFlowCopyTo applyFlowCopyTo : applyFlowCopyTos) {
                CompanyApplyFlowItemSetV2RequestContract flowItemSetRequestContract = new CompanyApplyFlowItemSetV2RequestContract();
                if (StringTool.isNullOrEmpty(applyFlowCopyTo.getItemId())) {
                    //app端需要此值=null时传回一个空字符串
                    applyFlowCopyTo.setItemId("");
                }
                flowItemSetRequestContract.setItem_id(applyFlowCopyTo.getItemId());
                flowItemSetRequestContract.setItem_type(applyFlowCopyTo.getItemType());
                list.add(flowItemSetRequestContract);
            }
            appendCustomRoleInfos(list, companyId);
            appendEmployeeInfos(list, companyId);
            appendSuperAdminInfos(list);
            appendAdminInfos(list);
            appendDeptManagerInfos(list);
        }
        if (!CollectionUtils.isEmpty(applyFlowItemsItems)) {
            for (ApplyFlowItem applyFlowItem : applyFlowItemsItems) {
                CompanyApplyFlowItemSetV2RequestContract flowItemSetRequestContract = new CompanyApplyFlowItemSetV2RequestContract();
                if (StringTool.isNullOrEmpty(applyFlowItem.getItemId())) {
                    //app端需要此值=null时传回一个空字符串
                    applyFlowItem.setItemId("");
                }
                flowItemSetRequestContract.setItem_id(applyFlowItem.getItemId());
                flowItemSetRequestContract.setItem_type(applyFlowItem.getItemType());
                list.add(flowItemSetRequestContract);
                if (applyFlowItem.getItemType() != null && applyFlowItem.getItemType() == 2) {
                    //判断是否公司员工
                    if (!isCompanyEmployee(companyId, applyFlowItem.getItemId())) {
                        flowSetRequestContract.setAbnormalApproval(true);
                    }
                }
            }
            appendCustomRoleInfos(list, companyId);
            appendEmployeeInfosWhioutFilter(list, companyId);
            appendSuperAdminInfos(list);
            appendAdminInfos(list);
            appendDeptManagerInfos(list);
        }
        return list;
    }


    /**
     * 部门主管完善信息
     *
     * @param items
     */
    private void appendDeptManagerInfos(List<CompanyApplyFlowItemSetV2RequestContract> items) {
        for (CompanyApplyFlowItemSetV2RequestContract item : items) {
            if (item.getItem_type().equals(ApplyFlowItemType.DeptManager.getValue())) {
                if (item.getItem_id().equals("1")) {
                    item.setItem_name("直接主管");
                } else {
                    item.setItem_name(String.format("第%s级主管", item.getItem_id()));
                }
            }
        }
    }

    /**
     * 自定义角色完善信息
     *
     * @param items
     * @param companyId
     */
    private void appendCustomRoleInfos(List<CompanyApplyFlowItemSetV2RequestContract> items, String companyId) {
        List<String> customRoleIds = new ArrayList<>();
        for (CompanyApplyFlowItemSetV2RequestContract item : items) {
            if (item.getItem_type().equals(ApplyFlowItemType.CustomRole.getValue())) {
                if (!customRoleIds.contains(item.getItem_id())) {
                    customRoleIds.add(item.getItem_id());
                }
            }
        }
        if (customRoleIds.size() > 0) {
            List<com.fenbeitong.usercenter.api.model.po.privilege.Customrole> customRoles = customRoleService.getRoleListByCompanyId(companyId);
            if (customRoles == null || customRoles.size() == 0) {
                return;
            }
            List<CompanyApplyFlowItemSetV2RequestContract> needDeleteItems = new ArrayList<>();
            for (CompanyApplyFlowItemSetV2RequestContract item : items) {
                if (item.getItem_type().equals(ApplyFlowItemType.CustomRole.getValue())) {
                    Optional<com.fenbeitong.usercenter.api.model.po.privilege.Customrole> customRole = customRoles.stream().filter(m -> m.getId().equals(item.getItem_id())).findAny();
                    if (customRole.isPresent()) {
                        item.setItem_name(customRole.get().getName());
                    } else {
                        needDeleteItems.add(item);
                    }
                }
            }
            if (needDeleteItems.size() > 0) {
                items.removeAll(needDeleteItems);
            }
        }
    }

    /**
     * 员工完善信息
     *
     * @param items
     * @param companyId
     */
    private void appendEmployeeInfos(List<CompanyApplyFlowItemSetV2RequestContract> items, String companyId) {
        List<String> employeeIds = new ArrayList<>();
        for (CompanyApplyFlowItemSetV2RequestContract item : items) {
            if (item.getItem_type().equals(ApplyFlowItemType.Employee.getValue())) {
                if (!employeeIds.contains(item.getItem_id())) {
                    employeeIds.add(item.getItem_id());
                }
            }
        }
        if (employeeIds.size() > 0) {
            List<EmployeeContract> employeeNames = iBaseOrganizationService.getEmployee(employeeIds, companyId);
            if (CollectionUtils.isEmpty(employeeNames)) {
                return;
            }
            List<CompanyApplyFlowItemSetV2RequestContract> needDeleteItems = new ArrayList<>();
            for (CompanyApplyFlowItemSetV2RequestContract item : items) {
                if (item.getItem_type().equals(ApplyFlowItemType.Employee.getValue())) {
                    if (!userService.isUserInCompany(item.getItem_id(), companyId)) {
                        needDeleteItems.add(item);
                    } else {
                        Optional<EmployeeContract> employee = employeeNames.stream().filter(m -> m.getId().equals(item.getItem_id())).findAny();
                        if (employee.isPresent()) {
                            item.setItem_name(employee.get().getName());
                        } else {
                            needDeleteItems.add(item);
                        }
                    }
                }
            }
            if (needDeleteItems.size() > 0) {
                items.removeAll(needDeleteItems);
            }
        }
    }

    /**
     * 员工完善信息
     *
     * @param items
     * @param companyId
     * @param employeeMap
     */
    private void appendEmployeeInfos(List<CompanyApplyFlowItemSetV2RequestContract> items, String companyId, Map<String, EmployeeContract> employeeMap) {
        List<String> employeeIds = new ArrayList<>();
        for (CompanyApplyFlowItemSetV2RequestContract item : items) {
            if (item.getItem_type().equals(ApplyFlowItemType.Employee.getValue())) {
                if (!employeeIds.contains(item.getItem_id())) {
                    employeeIds.add(item.getItem_id());
                }
            }
        }
        if (employeeIds.size() > 0) {
            if (ObjUtils.isEmpty(employeeMap)) {
                return;
            }
            List<CompanyApplyFlowItemSetV2RequestContract> needDeleteItems = new ArrayList<>();
            for (CompanyApplyFlowItemSetV2RequestContract item : items) {
                if (item.getItem_type().equals(ApplyFlowItemType.Employee.getValue())) {
                    if (!userService.isUserInCompany(item.getItem_id(), companyId)) {
                        needDeleteItems.add(item);
                    } else {
                        EmployeeContract employee = employeeMap.get(item.getItem_id());
                        if (employee != null) {
                            item.setItem_name(employee.getName());
                        } else {
                            needDeleteItems.add(item);
                        }
                    }
                }
            }
            if (needDeleteItems.size() > 0) {
                items.removeAll(needDeleteItems);
            }
        }
    }

    /**
     * 员工完善信息（不过滤）
     *
     * @param items
     * @param companyId
     */
    private void appendEmployeeInfosWhioutFilter(List<CompanyApplyFlowItemSetV2RequestContract> items, String companyId) {
        List<String> employeeIds = new ArrayList<>();
        for (CompanyApplyFlowItemSetV2RequestContract item : items) {
            if (item.getItem_type().equals(ApplyFlowItemType.Employee.getValue())) {
                if (!employeeIds.contains(item.getItem_id())) {
                    employeeIds.add(item.getItem_id());
                }
            }
        }
        if (employeeIds.size() > 0) {
            List<EmployeeContract> employeeNames = iBaseOrganizationService.getEmployee(employeeIds, companyId);
            if (CollectionUtils.isEmpty(employeeNames)) {
                return;
            }
            for (CompanyApplyFlowItemSetV2RequestContract item : items) {
                if (item.getItem_type().equals(ApplyFlowItemType.Employee.getValue())) {
                    Optional<EmployeeContract> employee = employeeNames.stream().filter(m -> m.getId().equals(item.getItem_id())).findAny();
                    if (employee.isPresent()) {
                        item.setItem_name(employee.get().getName());
                    }
                }
            }
        }
    }

    /**
     * 员工完善信息（不过滤）
     *
     * @param items
     * @param companyId
     * @param employeeMap
     */
    private void appendEmployeeInfosWhioutFilter(List<CompanyApplyFlowItemSetV2RequestContract> items, String companyId, Map<String, EmployeeContract> employeeMap) {
        List<String> employeeIds = new ArrayList<>();
        for (CompanyApplyFlowItemSetV2RequestContract item : items) {
            if (item.getItem_type().equals(ApplyFlowItemType.Employee.getValue())) {
                if (!employeeIds.contains(item.getItem_id())) {
                    employeeIds.add(item.getItem_id());
                }
            }
        }
        if (employeeIds.size() > 0) {
            if (ObjUtils.isEmpty(employeeMap)) {
                return;
            }
            for (CompanyApplyFlowItemSetV2RequestContract item : items) {
                if (item.getItem_type().equals(ApplyFlowItemType.Employee.getValue())) {
                    EmployeeContract employee = employeeMap.get(item.getItem_id());
                    if (employee != null) {
                        item.setItem_name(employee.getName());
                    }
                }
            }
        }
    }

    /**
     * 授权负责人完善信息
     *
     * @param items
     */
    private void appendSuperAdminInfos(List<CompanyApplyFlowItemSetV2RequestContract> items) {
        for (CompanyApplyFlowItemSetV2RequestContract item : items) {
            if (item.getItem_type().equals(ApplyFlowItemType.SuperAdmin.getValue())) {
                item.setItem_name(ApplyFlowItemType.SuperAdmin.getDesc());
            }
        }
    }

    /**
     * 企业管理员完善信息
     *
     * @param items
     */
    private void appendAdminInfos(List<CompanyApplyFlowItemSetV2RequestContract> items) {
        for (CompanyApplyFlowItemSetV2RequestContract item : items) {
            if (item.getItem_type().equals(ApplyFlowItemType.Admin.getValue())) {
                item.setItem_name(ApplyFlowItemType.Admin.getDesc());
            }
        }
    }

    /**
     * 刪除审批流设置
     *
     * @param id
     * @param ip
     * @param operatorId
     * @param companyId
     * @return
     */
    @Override
    public GlobalResponseCode deleteCompanySetting(String id, String ip, String operatorId, String companyId) {
        //查询审批流设置数据
        CompanyApplySettingExample companyApplySettingExample = new CompanyApplySettingExample();
        CompanyApplySettingExample.Criteria criteria = companyApplySettingExample.createCriteria();
        criteria.andIdEqualTo(id).andApplyStatusEqualTo(SaasFlowConstant.IS_NORMAL_STATUS);
        List<CompanyApplySetting> companyApplySettings = companyApplySettingMapper.selectByExample(companyApplySettingExample);
        if (CollectionUtils.isEmpty(companyApplySettings)) {
            return GlobalResponseCode.ParameterError;
        }
        if (companyApplySettings.size() > 1) {
            return GlobalResponseCode.NotFound;
        }
        //逻辑删除审批流设置数据
        CompanyApplySetting companyApplySetting = new CompanyApplySetting();
        companyApplySetting.setApplyStatus(2);
        companyApplySetting.setUpdateTime(new Date());
        companyApplySetting.setOperatorId(operatorId);
        CompanyApplySettingExample cASExample = new CompanyApplySettingExample();
        CompanyApplySettingExample.Criteria cri = cASExample.createCriteria();
        cri.andIdEqualTo(id);
        companyApplySettingMapper.updateByExampleSelective(companyApplySetting, cASExample);
        //删除审批人表
        applyFlowItemExtMapper.deleteByCompanyApplySettingId(id);
        //删除抄送人表
        applyFlowCopyToExtMapper.deleteByCompanyApplySettingId(id);
        //删除审批流表
        applyFlowExtMapper.deleteByCompanyApplySettingId(id);
        //删除应用部门列表
        companyApplySettingUseExtMapper.deleteByCompanyApplySettingId(id);
        CompanyApplySettingLog log = new CompanyApplySettingLog();
        log.setCreateTime(new Date());
        log.setAction(ActionType.Delete.getValue());//删除操作
        log.setOperatorId(operatorId);
        log.setCompanyApplySettingId(id);
        log.setIp(ip);
        companyApplySettingLogMapper.insert(log);
        CompanyApplySetting companyApplySetting4Log = companyApplySettingMapper.selectByPrimaryKey(id);
        if (ObjUtils.isNotBlank(companyApplySetting4Log)) {
            companyLogKafkaProducerService.sendCompanyLogMsg(getKafkaCompanyLogMsg(ApplyType.valueOf(companyApplySetting4Log.getApplyType()),
                    CompanySettingType.valueOf(companyApplySetting4Log.getCompanySettingType()),
                    ActionType.Delete,
                    companyApplySetting4Log.getFlowName()));
        }
        return GlobalResponseCode.Success;
    }

    /**
     * 查询审批流设置列表
     *
     * @param companyId
     * @param applyType
     * @param page
     * @param pageSize
     * @param clientVersion
     * @return
     * @throws SaasException
     */
    @Override
    public PageDataBaseContract<List<CompanyApplyFlowSetV2RequestContract>> queryCompanySettingList(Integer companySettingType, String companyId, Integer applyType, int page, int pageSize, String clientVersion) throws SaasException {
        if (StringUtils.isBlank(companyId)) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        if (page < 1) {
            page = 1;
        }
        int pageStart = (page - 1) * pageSize;
        PageDataBaseContract<List<CompanyApplyFlowSetV2RequestContract>> resultList = new PageDataBaseContract<>();
        resultList.setPage_index(page);
        resultList.setPage_size(pageSize);
        if (companySettingType == null) {
            companySettingType = SaasFlowConstant.SETTING_TYPE_CENTER;
        }
        String version = "1.9.96";
        boolean historyVersion = false;
        if (!StringUtils.isBlank(clientVersion) && VersionTool.compare(clientVersion, version) < 0) {
            historyVersion = true;
        }
        int total = companyApplySettingExtMapper.getCountByApplyType(companySettingType, applyType, companyId, SaasFlowConstant.IS_NORMAL_STATUS);
        resultList.setTotal(total);
        if (total <= 0) {
            // 如果为空，前端需要显示成空数组
            resultList.setData(Lists.newArrayList());
            return resultList;
        }
        List<CompanyApplySetting> companyApplySettingList = companyApplySettingExtMapper.getCompanyApplySettingListByApplyType(companySettingType, applyType, companyId, pageStart, pageSize, SaasFlowConstant.IS_NORMAL_STATUS);
        if (CollectionUtils.isEmpty(companyApplySettingList)) {
            resultList.setData(Lists.newArrayList());
            return resultList;
        }
        List<CompanyApplyFlowSetV2RequestContract> budgetSettingContractList = batchAppendCompanyApplySettingContractInfo(companyApplySettingList, historyVersion);
        resultList.setData(budgetSettingContractList);
        return resultList;
    }

    /**
     * 保存设置应用
     *
     * @param companyApplySettingUseContract
     * @param userId
     * @param companyId
     * @return
     * @throws SaasException
     */
    @Override
    @Transactional
    public GlobalResponseCode saveDetail(CompanyApplySettingUseContract companyApplySettingUseContract, String userId, String companyId) {
        if (ObjUtils.isEmpty(companyApplySettingUseContract)) {
            return GlobalResponseCode.ParameterError;
        }
        Integer settingType = companyApplySettingUseContract.getSetting_type();
        if (settingType == null || SettingType.valueOf(settingType).equals(SettingType.Unknown)) {
            return GlobalResponseCode.ParameterError;
        }
        String companyApplySettingId = companyApplySettingUseContract.getCompany_apply_setting_id();
        if (StringUtils.isBlank(companyApplySettingId)) {
            return GlobalResponseCode.ParameterError;
        }
        Integer companySettingType = companyApplySettingUseContract.getCompany_setting_type() == null ?
                SaasFlowConstant.SETTING_TYPE_CENTER : companyApplySettingUseContract.getCompany_setting_type();
        // 校验审批流ID是否正确
        CompanyApplySettingExample settingExample = new CompanyApplySettingExample();
        CompanyApplySettingExample.Criteria settingCriteria = settingExample.createCriteria();
        settingCriteria.andIdEqualTo(companyApplySettingId).andCompanySettingTypeEqualTo(companySettingType).andApplyStatusEqualTo(SaasFlowConstant.IS_NORMAL_STATUS);
        List<CompanyApplySetting> companyApplySettings = companyApplySettingMapper.selectByExample(settingExample);
        if (CollectionUtils.isEmpty(companyApplySettings)) {
            logger.info("companyApplySettingId: {} is not exist", companyApplySettingId);
            return GlobalResponseCode.ParameterError;
        }
        companyApplySettingUseExtMapper.deleteByCompanyApplySettingIdAndSettingType(companyApplySettingId, settingType);
        // itemList为空删除所有，直接返回成功
        List<String> itemList = companyApplySettingUseContract.getItem_list();
        if (CollectionUtils.isEmpty(itemList)) {
            return GlobalResponseCode.Success;
        }
        // 新增更新以及log的统一时间
        Date date = new Date();
        itemList.forEach(itemId -> {
            if (StringUtils.isBlank(itemId)) {
                throw new SaasException(GlobalResponseCode.ParameterIsNull);
            }
            companyApplySettingUseContract.setItem_id(itemId.trim());
            // 先查询该部门是否应用审批流
            Integer applyType = companyApplySettings.get(0).getApplyType();
            List<CompanyApplySettingUse> companyApplySettingUses = companyApplySettingUseExtMapper.querySettingUseBySettingType(companySettingType, settingType, applyType, itemId.trim(), companyId);
            if (CollectionUtils.isEmpty(companyApplySettingUses)) {
                CompanyApplySettingUse companyApplySettingUse = new CompanyApplySettingUse();
                companyApplySettingUse.setId(IDTool.CreateUniqueID());
                companyApplySettingUse.setItemId(itemId.trim());
                companyApplySettingUse.setSettingType(settingType);
                companyApplySettingUse.setCompanyId(companyId);
                companyApplySettingUse.setCompanyApplySettingId(companyApplySettingId);
                companyApplySettingUse.setCreateUser(userId);
                companyApplySettingUse.setCreateTime(date);
                companyApplySettingUseMapper.insert(companyApplySettingUse);
            } else {
                CompanyApplySettingUse companyApplySettingUseInfo = new CompanyApplySettingUse();
                companyApplySettingUseInfo.setCompanyApplySettingId(companyApplySettingId);
                companyApplySettingUseInfo.setUpdateTime(date);
                companyApplySettingUseInfo.setUpdateUser(userId);
                CompanyApplySettingUseExample companyApplySettingUseExample = new CompanyApplySettingUseExample();
                companyApplySettingUseExample.createCriteria().andItemIdEqualTo(itemId.trim()).andSettingTypeEqualTo(settingType)
                        .andCompanyIdEqualTo(companyId).andCompanyApplySettingIdEqualTo(companyApplySettingUses.get(0).getCompanyApplySettingId());
                companyApplySettingUseMapper.updateByExampleSelective(companyApplySettingUseInfo, companyApplySettingUseExample);
            }
        });
        CompanyApplySettingLog log = new CompanyApplySettingLog();
        log.setCreateTime(date);
        // 使用Validate来作为应用操作日志枚举
        log.setAction(ActionType.Validate.getValue());
        log.setOperatorId(SaasHttpContext.getUserId());
        log.setCompanyApplySettingId(companyApplySettingId);
        log.setIp(SaasHttpContext.getIP());
        log.setOperateContent(JsonUtils.toJson(companyApplySettingUseContract));
        companyApplySettingLogMapper.insert(log);
        return GlobalResponseCode.Success;
    }

    /**
     * 查询设置应用列表
     *
     * @param companyApplySettingId
     * @param companyId
     * @param token
     * @return
     * @throws SaasException
     */
    @Override
    public CompanyApplySettingUseResponseContract queryCompanyApplySettingUseList(String companyApplySettingId, String companyId, String token, Integer settingType) throws SaasException {
        if (StringUtils.isBlank(companyApplySettingId) || StringUtils.isBlank(companyId) || StringUtils.isBlank(token)) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        if (settingType == null || SettingType.valueOf(settingType).equals(SettingType.Unknown)) {
            settingType = 1;
        }
        CompanyApplySettingUseResponseContract companyApplySettingUseResponseContract = new CompanyApplySettingUseResponseContract();
        CompanyApplySettingUseExample example = new CompanyApplySettingUseExample();
        CompanyApplySettingUseExample.Criteria criteria = example.createCriteria();
        criteria.andCompanyIdEqualTo(companyId).andSettingTypeEqualTo(settingType).andCompanyApplySettingIdEqualTo(companyApplySettingId);
        List<CompanyApplySettingUse> companyApplySettingUses = companyApplySettingUseMapper.selectByExample(example);
        List<CompanyApplySettingUseMessageContract> companyApplySettingUseMessageContractList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(companyApplySettingUses)) {
            companyApplySettingUseResponseContract.setApply_use_list(companyApplySettingUseMessageContractList);
            companyApplySettingUseResponseContract.setApply_for_company(false);
            return companyApplySettingUseResponseContract;
        }
        List<String> itemList = Lists.newArrayList();
        for (CompanyApplySettingUse casu : companyApplySettingUses) {
            itemList.add(casu.getItemId());
        }
        if (settingType == SettingType.Department.getValue()) {
            //部门
            List<OrgUnitResult> department = baseOrganizationService.getOrgUnit(itemList, companyId);
            for (OrgUnitResult de : department) {
                CompanyApplySettingUseMessageContract casumc = new CompanyApplySettingUseMessageContract();
                casumc.setId(de.getId());
                casumc.setName(de.getName());
                companyApplySettingUseMessageContractList.add(casumc);
            }
        } else if (settingType == SettingType.Project.getValue()) {
            List<CostInfoContract> costInfoContracts = costCenterService.costInfos(companyId, itemList);
            if (CollectionUtils.isNotEmpty(costInfoContracts)) {
                for (CostInfoContract costInfoContract : costInfoContracts) {
                    CompanyApplySettingUseMessageContract casumcInfo = new CompanyApplySettingUseMessageContract();
                    casumcInfo.setId(costInfoContract.getId());
                    casumcInfo.setName(costInfoContract.getName());
                    casumcInfo.setCode(costInfoContract.getCode());
                    casumcInfo.setManager_name(costInfoContract.getManager_name());
                    companyApplySettingUseMessageContractList.add(casumcInfo);
                }
            }
        }
        if (companyApplySettingUseMessageContractList.size() == 1 && companyApplySettingUseMessageContractList.get(0).getId().equals(companyId)) {
            companyApplySettingUseResponseContract.setApply_for_company(true);
        } else {
            companyApplySettingUseResponseContract.setApply_for_company(false);
        }
        companyApplySettingUseResponseContract.setApply_use_list(companyApplySettingUseMessageContractList);
        return companyApplySettingUseResponseContract;
    }

    /**
     * 获取公司设置的审批流，同时将各种类型的item下所属的所有员工信息返回
     *
     * @param settingType
     * @param companyId
     * @param applyType
     * @return
     */
    @Override
    public CompanyApplyFlowSetV2RequestContract getCompanySettingWithEmployee(Integer settingType, String companyId, ApplyType applyType, String userId, BigDecimal budget, Integer exceedBuyType, Integer businessType) {
        CompanyApplyFlowSetV2RequestContract result = getCompanySetting(settingType, companyId, applyType, userId, budget, exceedBuyType, businessType);
        if (result == null) {
            return result;
        }
        //固定审批流，获取下属所有员工信息，没有员工时，跳过此数据
        appendSettingEmployee(companyId, userId, result.getFixation_flow_list(), CostAttributionCategory.Dept.getKey(), null);
        appendSettingEmployee(companyId, userId, result.getCc_list(), CostAttributionCategory.Dept.getKey(), null);
        return result;
    }

    public CompanyApplyFlowSetV2RequestContract getCompanySetting(Integer settingType, String companyId, ApplyType applyType, String userId, BigDecimal budget, Integer exceedBuyType, Integer businessType) {
        if (companyId == null || applyType == null || applyType == ApplyType.Unknown) {
            return null;
        }
        CompanyApplyFlowSetV2RequestContract result = new CompanyApplyFlowSetV2RequestContract();
        result.setApply_type(applyType.getValue());

        //查询所属部门
        OrgUnit department = baseOrganizationService.getOrgUnit(userId, companyId);
        if (department == null && StringUtils.isNotBlank(department.getParentOrgUnitId())) {
            return null;
        }
        List<String> orgUnitPraentList = Lists.newLinkedList();
        String parentOrgUnitId = department.getParentOrgUnitId();
        orgUnitPraentList.add(department.getId());
        if (StringUtils.isNotBlank(parentOrgUnitId)) {
            orgUnitPraentList = appendOrgUnitPraentList(parentOrgUnitId, orgUnitPraentList);
        }
        //获取部门管理的审批流设置
        CompanyApplySetting companyApplySetting = appendCompanyApplySettingInfo(settingType, orgUnitPraentList, applyType.getValue(), companyId);
        CompanyApplyType companyApplyType = CompanyApplyType.Elastic;
        ApplyFlow applyFlowInfo = null;
        if (companyApplySetting != null) {
            applyFlowInfo = applyFlowExtMapper.queryApplyFlow(companyApplySetting.getId(), exceedBuyType);
            companyApplyType = CompanyApplyType.valueOf(applyFlowInfo.getCompanyApplyType());
            if (applyFlowInfo.getCompanyApplyType() == CompanyApplyType.Flow.getValue() || applyFlowInfo.getCompanyApplyType() == CompanyApplyType.CONDITIONAL.getValue()) {
                String flowName = StringUtils.isNotBlank(companyApplySetting.getFlowName()) ? companyApplySetting.getFlowName() : "";
                result.setFlow_name(flowName);
            } else {
                result.setFlow_name("");
            }
        }
        result.setCc_notice_type_enums(new KvContract(2, CcNoticeType.getKey(2)));
        result.setCompany_apply_type(companyApplyType.getValue());
        if (companyApplyType == CompanyApplyType.Flow) {
            result.setApply_flow_id(applyFlowInfo.getId());
            appendFlowItems(result, companyApplySetting, applyFlowInfo.getCcNoticeType());
        }
        if (companyApplyType == CompanyApplyType.CONDITIONAL) {
            result.setApply_flow_id(applyFlowInfo.getId());
            appendConditionalFlowItems(result, companyApplySetting, budget, applyFlowInfo.getCcNoticeType(), settingType, applyType);
        }
        if (companyApplySetting != null && companyApplyType == CompanyApplyType.Elastic) {
            if (applyFlowInfo.getCcNoticeType() != null) {
                result.setCc_notice_type_enums(new KvContract(applyFlowInfo.getCcNoticeType(), CcNoticeType.getKey(applyFlowInfo.getCcNoticeType())));
            }
            //抄送
            List<ApplyFlowCopyTo> applyFlowCopyTos = applyFlowCopyToExtMapper.selectListById(companyApplySetting.getId());
            if (!CollectionUtils.isEmpty(applyFlowCopyTos)) {
                List<CompanyApplyFlowItemSetV2RequestContract> ccflowItems = new ArrayList<>();
                for (ApplyFlowCopyTo itemDetail : applyFlowCopyTos) {
                    CompanyApplyFlowItemSetV2RequestContract requestFlowItem = CompanyApplyFlowItemSetV2RequestContract.ccFromModel(itemDetail);
                    if (StringTool.isNullOrEmpty(requestFlowItem.getItem_id())) {
                        requestFlowItem.setItem_id("");  //app端需要此值=null时传回一个空字符串
                    }
                    ccflowItems.add(requestFlowItem);
                }
                String ccCompanyId = companyApplySetting.getCompanyId();
                appendCustomRoleInfos(ccflowItems, ccCompanyId);
                appendEmployeeInfos(ccflowItems, ccCompanyId);
                appendSuperAdminInfos(ccflowItems);
                appendAdminInfos(ccflowItems);
                appendDeptManagerInfos(ccflowItems);
                result.setCc_list(ccflowItems);
            }
        }
        return result;
    }

    /**
     * 获取公司设置的审批流，同时将各种类型的item下所属的所有员工信息返回
     *
     * @param companyId
     * @param applyType
     * @return
     */
    @Override
    public CompanyApplyFlowSetV2RequestContract getSettingWithEmployee(Integer settingType, String companyId, ApplyType applyType, String userId, BigDecimal budget, Integer category, String costAttributionId, Integer exceedBuyType, Integer businessType) {
        if (budget != null && budget.compareTo(BigDecimal.valueOf(0)) == 1) {
            budget = budget.divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP);
        }
        CostAttributionCategory costAttributionCategory = CostAttributionCategory.getByKey(category);
        if (category != null && costAttributionCategory == null) {
            throw new SaasException(GlobalResponseCode.ParameterError, CoreLanguage.Common_Exception_FeeBelongTypeNotEmpty.getMessage());
        }
        if (settingType == SaasFlowConstant.SETTING_TYPE_ROUTE) {
            if (exceedBuyType == null) {
                exceedBuyType = ExceedBuyType.Supernormal.getValue();
            }
        } else if (settingType == SaasFlowConstant.SETTING_TYPE_CENTER || settingType == SaasFlowConstant.SETTING_TYPE_ORDER
                || settingType == SaasFlowConstant.SETTING_TYPE_DINNER||settingType == SaasFlowConstant.SETTING_TYPE_TAKEAWAY) {
            exceedBuyType = ExceedBuyType.UnSupernormal.getValue();
        }
        //老版本默认为部门
        if (category == null) {
            category = CostAttributionCategory.Dept.getKey();
        }
        //获取部门/项目的审批流配置
        CompanyApplyFlowSetV2RequestContract result = getSetting(settingType, companyId, applyType, userId, budget, category, costAttributionId, exceedBuyType, businessType);
        if (result == null) {
            return result;
        }
        //固定审批流和分条件审批流，获取下属所有员工信息，没有员工时，跳过此数据
        appendSettingEmployee(companyId, userId, result.getFixation_flow_list(), result.getCost_attribution_category(), costAttributionId);
        appendSettingEmployee(companyId, userId, result.getCc_list(), result.getCost_attribution_category(), costAttributionId);
        return result;
    }

    public CompanyApplyFlowSetV2RequestContract getSetting(Integer settingType, String companyId, ApplyType applyType, String userId, BigDecimal budget, Integer category, String costAttributionId, Integer exceedBuyType, Integer businessType) {
        if (StringUtils.isBlank(companyId) || applyType == null || applyType == ApplyType.Unknown) {
            return null;
        }
        CompanyApplyFlowSetV2RequestContract result = new CompanyApplyFlowSetV2RequestContract();
        result.setApply_type(applyType.getValue());
        CompanyApplySetting companyApplySetting = null;
        //获取部门/项目的审批流配置
        if (category == CostAttributionCategory.Dept.getKey()) {
            companyApplySetting = queryDeptCompanyApplySettingInfo(userId, settingType, applyType, companyId,costAttributionId);
            result.setCost_attribution_category(CostAttributionCategory.Dept.getKey());
        } else {
            if (StringUtils.isBlank(costAttributionId)) {
                throw new SaasException(GlobalResponseCode.ParameterError, CoreLanguage.Common_Exception_ProjectIdNotEmpty.getMessage());
            }
            companyApplySetting = queryCostCenterCompanyApplySettingInfo(userId, settingType, applyType, companyId, costAttributionId);
            if (companyApplySetting.getCostCenterStatus() == 0 && companyApplySetting.getCanDelete() == 2) {
                companyApplySetting = queryDeptCompanyApplySettingInfo(userId, settingType, applyType, companyId,null);
                result.setCost_attribution_category(CostAttributionCategory.Dept.getKey());
                result.setFlow_hint("无匹配的项目审批流，将使用部门流程进行审批");
            } else {
                result.setCost_attribution_category(CostAttributionCategory.CostCenter.getKey());
            }
        }
        if (companyApplySetting == null) {
            throw new SaasException(GlobalResponseCode.DefaultApplyOrderError, CoreLanguage.Common_Exception_DefaultFlowConfigGetError.getMessage());
        }
        result.setRole_approve_type(companyApplySetting.getRoleApproveType());
        ApplyFlow applyFlowInfo = applyFlowExtMapper.queryApplyFlow(companyApplySetting.getId(), exceedBuyType);
        if (applyFlowInfo == null && exceedBuyType == ExceedBuyType.UnSupernormal.getValue()) {
            throw new SaasException(GlobalResponseCode.DefaultApplyOrderError, CoreLanguage.Common_Exception_FlowConfigError.getMessage());
        }
        if (applyFlowInfo == null && exceedBuyType == ExceedBuyType.Supernormal.getValue() && settingType == SaasFlowConstant.SETTING_TYPE_ROUTE) {
            applyFlowInfo = applyFlowExtMapper.queryApplyFlow(companyApplySetting.getId(), ExceedBuyType.UnSupernormal.getValue());
            if (applyFlowInfo == null) {
                throw new SaasException(GlobalResponseCode.DefaultApplyOrderError, CoreLanguage.Common_Exception_FlowConfigError.getMessage());
            }
        }
        if (applyFlowInfo == null && exceedBuyType == ExceedBuyType.Refund.getValue() && settingType == SaasFlowConstant.SETTING_TYPE_ROUTE) {
            applyFlowInfo = applyFlowExtMapper.queryApplyFlow(companyApplySetting.getId(), ExceedBuyType.UnSupernormal.getValue());
            if (applyFlowInfo == null) {
                throw new SaasException(GlobalResponseCode.DefaultApplyOrderError, CoreLanguage.Common_Exception_FlowConfigError.getMessage());
            }
        }
        if (applyFlowInfo == null && exceedBuyType == ExceedBuyType.Change.getValue() && settingType == SaasFlowConstant.SETTING_TYPE_ROUTE) {
            applyFlowInfo = applyFlowExtMapper.queryApplyFlow(companyApplySetting.getId(), ExceedBuyType.UnSupernormal.getValue());
            if (applyFlowInfo == null) {
                throw new SaasException(GlobalResponseCode.DefaultApplyOrderError, CoreLanguage.Common_Exception_FlowConfigError.getMessage());
            }
        }
        CompanyApplyType companyApplyType = null;
        if (companyApplySetting != null) {
            companyApplyType = CompanyApplyType.valueOf(applyFlowInfo.getCompanyApplyType());
            if (applyFlowInfo.getCompanyApplyType() == 2 || applyFlowInfo.getCompanyApplyType() == 3) {
                String flowName = StringUtils.isNotBlank(companyApplySetting.getFlowName()) ? companyApplySetting.getFlowName() : "";
                result.setFlow_name(flowName);
            } else {
                result.setFlow_name("");
            }
        }
        result.setCompany_apply_type(companyApplyType.getValue());
        result.setCc_notice_type_enums(new KvContract(2, CcNoticeType.getKey(2)));
        if (companyApplyType == CompanyApplyType.Flow) {
            result.setApply_flow_id(applyFlowInfo.getId());
            appendFlowItems(result, companyApplySetting, applyFlowInfo.getCcNoticeType());
        }
        if (companyApplyType == CompanyApplyType.CONDITIONAL) {
            result.setApply_flow_id(applyFlowInfo.getId());
            appendConditionalFlowItems(result, companyApplySetting, budget, applyFlowInfo.getCcNoticeType(), settingType, applyType);
        }
        if (companyApplySetting != null && companyApplyType == CompanyApplyType.Elastic) {
            result.setCc_notice_type_enums(new KvContract(applyFlowInfo.getCcNoticeType(), CcNoticeType.getKey(applyFlowInfo.getCcNoticeType())));
            //抄送
            List<ApplyFlowCopyTo> applyFlowCopyTos = applyFlowCopyToExtMapper.selectListById(applyFlowInfo.getId());
            if (!CollectionUtils.isEmpty(applyFlowCopyTos)) {
                List<CompanyApplyFlowItemSetV2RequestContract> ccflowItems = new ArrayList<>();
                for (ApplyFlowCopyTo itemDetail : applyFlowCopyTos) {
                    CompanyApplyFlowItemSetV2RequestContract requestFlowItem = CompanyApplyFlowItemSetV2RequestContract.ccFromModel(itemDetail);
                    if (StringTool.isNullOrEmpty(requestFlowItem.getItem_id())) {
                        requestFlowItem.setItem_id("");  //app端需要此值=null时传回一个空字符串
                    }
                    ccflowItems.add(requestFlowItem);
                }
                String ccCompanyId = companyApplySetting.getCompanyId();
                appendCustomRoleInfos(ccflowItems, ccCompanyId);
                appendEmployeeInfos(ccflowItems, ccCompanyId);
                appendSuperAdminInfos(ccflowItems);
                appendAdminInfos(ccflowItems);
                appendDeptManagerInfos(ccflowItems);
                result.setCc_list(ccflowItems);
            }
        }
        return result;
    }

    //分条件
    private void appendConditionalFlowItems(CompanyApplyFlowSetV2RequestContract requestContract, CompanyApplySetting companyApplySetting, BigDecimal budget, Integer cCNoticeType, Integer settingType, ApplyType applyType) {
        List<ApplyFlowItem> oldItems = applyFlowItemExtMapper.queryConditionalListByFlowId(requestContract.getApply_flow_id());
        List<BigDecimal> item = Lists.newLinkedList();
        if (CollectionUtils.isEmpty(oldItems)) {
            return;
        }
        for (ApplyFlowItem applyFlowItem : oldItems) {
            if (applyFlowItem.getSort() == 1) {
                item.add(applyFlowItem.getConditionMin());
            }
        }
        BigDecimal sortFlag = BigDecimal.valueOf(0);
        if (settingType == 1 && applyType.getValue() == ApplyType.ChaiLv.getValue()) {
            MessageSetup messageSetup = messageSetupService.queryCompanyMessageSetupWithDefault(companyApplySetting.getCompanyId(), SaasApplyConstant.ITEM_CODE_WHETHER_TRIP_APPLY_BUDGET);
            if (messageSetup == null || messageSetup.getIsChecked() == 0) {
                sortFlag = item.get(0);
            } else {
                for (int i = 0; i < item.size(); i++) {
                    if (i == item.size() - 1) {
                        if (budget.compareTo(item.get(i)) != -1) {
                            sortFlag = item.get(i);
                        }
                    } else {
                        if (budget.compareTo(item.get(i)) != -1 && budget.compareTo(item.get(i + 1)) == -1) {
                            sortFlag = item.get(i);
                        }
                    }
                }
            }
        } else {
            for (int i = 0; i < item.size(); i++) {
                if (i == item.size() - 1) {
                    if (budget.compareTo(item.get(i)) != -1) {
                        sortFlag = item.get(i);
                    }
                } else {
                    if (budget.compareTo(item.get(i)) != -1 && budget.compareTo(item.get(i + 1)) == -1) {
                        sortFlag = item.get(i);
                    }
                }
            }
        }
        //根据最小值查询所属分条件审批流
        List<ApplyFlowItem> applyFlowItems = applyFlowItemExtMapper.selectConditionalListByFlowId(requestContract.getApply_flow_id(), sortFlag);

        if (CollectionUtils.isEmpty(applyFlowItems)) {
            return;
        }
        List<CompanyApplyFlowItemSetV2RequestContract> flowItems = new ArrayList<>();
        for (ApplyFlowItem itemDetail : applyFlowItems) {
            CompanyApplyFlowItemSetV2RequestContract requestFlowItem = CompanyApplyFlowItemSetV2RequestContract.fromModel(itemDetail);
            if (StringTool.isNullOrEmpty(requestFlowItem.getItem_id())) {
                requestFlowItem.setItem_id("");  //app端需要此值=null时传回一个空字符串
            }
            flowItems.add(requestFlowItem);
        }
        String companyId = companyApplySetting.getCompanyId();
        appendCustomRoleInfos(flowItems, companyId);
        appendEmployeeInfos(flowItems, companyId);
        appendSuperAdminInfos(flowItems);
        appendAdminInfos(flowItems);
        appendDeptManagerInfos(flowItems);
        requestContract.setFixation_flow_list(flowItems);
        if (cCNoticeType != null) {
            requestContract.setCc_notice_type_enums(new KvContract(cCNoticeType, CcNoticeType.getKey(cCNoticeType)));
        }
        //抄送
        List<ApplyFlowCopyTo> applyFlowCopyTos = applyFlowCopyToExtMapper.selectListById(requestContract.getApply_flow_id());
        if (!CollectionUtils.isEmpty(applyFlowCopyTos)) {
            List<CompanyApplyFlowItemSetV2RequestContract> ccflowItems = new ArrayList<>();
            for (ApplyFlowCopyTo itemDetail : applyFlowCopyTos) {
                CompanyApplyFlowItemSetV2RequestContract requestFlowItem = CompanyApplyFlowItemSetV2RequestContract.ccFromModel(itemDetail);
                if (StringTool.isNullOrEmpty(requestFlowItem.getItem_id())) {
                    requestFlowItem.setItem_id("");  //app端需要此值=null时传回一个空字符串
                }
                ccflowItems.add(requestFlowItem);
            }
            String ccCompanyId = companyApplySetting.getCompanyId();
            appendCustomRoleInfos(ccflowItems, ccCompanyId);
            appendEmployeeInfos(ccflowItems, ccCompanyId);
            appendSuperAdminInfos(ccflowItems);
            appendAdminInfos(ccflowItems);
            appendDeptManagerInfos(ccflowItems);
            requestContract.setCc_list(ccflowItems);
        }
    }

    //固定审批流
    private void appendFlowItems(CompanyApplyFlowSetV2RequestContract requestContract, CompanyApplySetting companyApplySetting, Integer cCNoticeType) {
        List<ApplyFlowItem> applyFlowItems = applyFlowItemExtMapper.selectListByFlowId(requestContract.getApply_flow_id());
        if (CollectionUtils.isEmpty(applyFlowItems)) {
            return;
        }
        List<CompanyApplyFlowItemSetV2RequestContract> flowItems = new ArrayList<>();
        for (ApplyFlowItem item : applyFlowItems) {
            CompanyApplyFlowItemSetV2RequestContract requestFlowItem = CompanyApplyFlowItemSetV2RequestContract.fromModel(item);
            if (StringTool.isNullOrEmpty(requestFlowItem.getItem_id())) {
                requestFlowItem.setItem_id("");  //app端需要此值=null时传回一个空字符串
            }
            flowItems.add(requestFlowItem);
        }
        String companyId = companyApplySetting.getCompanyId();
        appendCustomRoleInfos(flowItems, companyId);
        appendEmployeeInfos(flowItems, companyId);
        appendSuperAdminInfos(flowItems);
        appendAdminInfos(flowItems);
        appendDeptManagerInfos(flowItems);
        requestContract.setFixation_flow_list(flowItems);
        if (cCNoticeType != null) {
            requestContract.setCc_notice_type_enums(new KvContract(cCNoticeType, CcNoticeType.getKey(cCNoticeType)));
        }
        //抄送人
        List<ApplyFlowCopyTo> applyFlowCopyTos = applyFlowCopyToExtMapper.selectListById(requestContract.getApply_flow_id());
        if (!CollectionUtils.isEmpty(applyFlowCopyTos)) {
            List<CompanyApplyFlowItemSetV2RequestContract> ccflowItems = new ArrayList<>();
            for (ApplyFlowCopyTo item : applyFlowCopyTos) {
                CompanyApplyFlowItemSetV2RequestContract requestFlowItem = CompanyApplyFlowItemSetV2RequestContract.ccFromModel(item);
                if (StringTool.isNullOrEmpty(requestFlowItem.getItem_id())) {
                    requestFlowItem.setItem_id("");  //app端需要此值=null时传回一个空字符串
                }
                ccflowItems.add(requestFlowItem);
            }
            String ccCompanyId = companyApplySetting.getCompanyId();
            appendCustomRoleInfos(ccflowItems, ccCompanyId);
            appendEmployeeInfos(ccflowItems, ccCompanyId);
            appendSuperAdminInfos(ccflowItems);
            appendAdminInfos(ccflowItems);
            appendDeptManagerInfos(ccflowItems);
            requestContract.setCc_list(ccflowItems);
        }
    }

    /**
     * 固定审批流和分条件审批流，获取下属所有员工信息，没有员工时，跳过此数据
     *
     * @param companyId
     * @param userId
     * @param flowItems
     * @param category
     * @param costAttributionId
     */
    public void appendSettingEmployee(String companyId, String userId, List<CompanyApplyFlowItemSetV2RequestContract> flowItems, Integer category, String costAttributionId) {
        if (CollectionUtils.isEmpty(flowItems)) {
            return;
        }
        List<String> employeeIds = new ArrayList<>();
        List<String> customRoleIds = new ArrayList<>();
        boolean hasSuperAdmin = false;
        boolean hasAdmin = false;
        boolean hasDeptManager = false;
        for (CompanyApplyFlowItemSetV2RequestContract item : flowItems) {
            if (item.getItem_type().equals(ApplyFlowItemType.Employee.getValue())) {
                employeeIds.add(item.getItem_id());
            } else if (item.getItem_type().equals(ApplyFlowItemType.CustomRole.getValue())) {
                customRoleIds.add(item.getItem_id());
            } else if (item.getItem_type().equals(ApplyFlowItemType.SuperAdmin.getValue())) {
                hasSuperAdmin = true;
            } else if (item.getItem_type().equals(ApplyFlowItemType.Admin.getValue())) {
                hasAdmin = true;
            } else if (item.getItem_type().equals(ApplyFlowItemType.DeptManager.getValue())) {
                hasDeptManager = true;
            }
        }
        Map<String, List<String>> roleEmployeeIdsMap = new HashMap<>();
        if (customRoleIds.size() > 0) {
            for (String customRoleId : customRoleIds) {
                appendEmployeeIdsByRoleId(customRoleId, companyId, employeeIds, roleEmployeeIdsMap);
            }
        }
        if (hasSuperAdmin) {
            String superAdminRole = Integer.toString(UserRole.CompanySuperAdmin.getValue());
            appendEmployeeIdsByRoleId(superAdminRole, companyId, employeeIds, roleEmployeeIdsMap);

        }
        if (hasAdmin) {
            String adminRole = Integer.toString(UserRole.CompanyAdmin.getValue());
            appendEmployeeIdsByRoleId(adminRole, companyId, employeeIds, roleEmployeeIdsMap);
        }
        List<ManagerListDTO> managerListContracts = null;
        //部门主管
        if (hasDeptManager && category == CostAttributionCategory.Dept.getKey()) {
            managerListContracts = orgUnitService.getManagerListOfEmployee(companyId, userId);
            appendEmployeeIdsFromDeptManagers(flowItems, employeeIds, managerListContracts);
        }
        List<String> costCenterManager = null;
        //项目主管
        if (hasDeptManager && category == CostAttributionCategory.CostCenter.getKey()) {
            costCenterManager = costCenterService.managerIds(costAttributionId, companyId);
            if (CollectionUtils.isNotEmpty(flowItems) && flowItems.size() > 1) {
                if (!costCenterManager.contains(userId)) {
                    appendEmployeeIdsFromCostCenterManagers(flowItems, employeeIds, costCenterManager);
                } else {
                    costCenterManager = Lists.newArrayList();
                }
            } else {
                appendEmployeeIdsFromCostCenterManagers(flowItems, employeeIds, costCenterManager);
            }
        }
        if (employeeIds.size() > 0) {
            List<EmployeeDepartmentContract> employeeInfos = sloveEmployeeList(employeeIds, companyId);
            if (employeeInfos != null && employeeInfos.size() > 0) {
                appendUserInfosToItem(flowItems, employeeInfos, roleEmployeeIdsMap);
                if (hasDeptManager && category == CostAttributionCategory.Dept.getKey()) {
                    appendDeptManagerUserInfosToItem(flowItems, employeeInfos, managerListContracts);
                }
                if (hasDeptManager && category == CostAttributionCategory.CostCenter.getKey()) {
                    appendCostManagerUserInfosToItem(flowItems, employeeInfos, costCenterManager);
                }
            }

        }
        removeAllItemThatNoUsers(flowItems);

        if (flowItems == null) {
            flowItems = Lists.newArrayList();
        }
        if (flowItems.size() == 0 && !hasSuperAdmin) {
            //没有审批节点，添加授权负责人作为审批节点
            String superAdminRole = Integer.toString(UserRole.CompanySuperAdmin.getValue());
            List<String> superAdminEmployeeIds = customRoleService.getEmployeeIdByRoleId(superAdminRole, companyId);
            if (superAdminEmployeeIds != null && superAdminEmployeeIds.size() > 0) {
                List<EmployeeDepartmentContract> superAdminEmployeeInfos = sloveEmployeeList(superAdminEmployeeIds, companyId);
                if (superAdminEmployeeInfos != null && superAdminEmployeeInfos.size() > 0) {
                    CompanyApplyFlowItemSetV2RequestContract superAdminItem = new CompanyApplyFlowItemSetV2RequestContract();
                    superAdminItem.setUsers(genUserInfosByEmployeeIds(superAdminEmployeeInfos, superAdminEmployeeIds));
                    superAdminItem.setItem_type(ApplyFlowItemType.SuperAdmin.getValue());
                    superAdminItem.setItem_name(ApplyFlowItemType.SuperAdmin.getDesc());
                    flowItems.add(superAdminItem);
                }
            }

        }
    }

    /**
     * 分批次查询公司员工
     *
     * @param employeeIds
     * @param companyId
     * @return
     */
    private List<EmployeeDepartmentContract> sloveEmployeeList(List<String> employeeIds, String companyId) {
        List<EmployeeDepartmentContract> employeeDepartmentContractArrayList = Lists.newArrayList();
        int insertNum = 500;
        int totalNum = employeeIds.size() / insertNum;
        for (int i = 0; i <= totalNum; i++) {
            List<EmployeeDepartmentContract> employeeDepartmentContractArrays = Lists.newArrayList();
            List<String> employees = Lists.newArrayList();
            int fromIndex = i * insertNum;
            int toIndex = (i + 1) * insertNum;
            if (employeeIds.size() < toIndex && employeeIds.size() >= fromIndex) {
                employees = employeeIds.subList(fromIndex, employeeIds.size());
            } else {
                employees = employeeIds.subList(fromIndex, toIndex);
            }
            if (CollectionUtils.isNotEmpty(employees)) {
                employeeDepartmentContractArrays = orgUnitService.getEmployeeDepartmentInfo(employees, companyId);
                employeeDepartmentContractArrayList.addAll(employeeDepartmentContractArrays);
            }
        }
        return employeeDepartmentContractArrayList;
    }

    private void appendDeptManagerUserInfosToItem(List<CompanyApplyFlowItemSetV2RequestContract> flowItems, List<EmployeeDepartmentContract> employeeInfos, List<ManagerListDTO> managerListContracts) {
        if (managerListContracts != null && managerListContracts.size() > 0) {
            for (CompanyApplyFlowItemSetV2RequestContract item : flowItems) {
                if (!item.getItem_type().equals(ApplyFlowItemType.DeptManager.getValue())) {
                    continue;
                }
                int ind = Integer.valueOf(item.getItem_id()) - 1;
                if (ind >= managerListContracts.size() || ind < 0) continue;
                ManagerListDTO managerListContract = managerListContracts.get(ind);
                List<String> employeeIds = managerListContract.getManagerContractList() == null || managerListContract.getManagerContractList().size() == 0 ? null : managerListContract.getManagerContractList().stream().map(m -> m.getEmployeeId()).collect(Collectors.toList());
                if (employeeIds != null && employeeIds.size() > 0) {
                    item.setUsers(genUserInfosByEmployeeIds(employeeInfos, employeeIds));
                }
            }
        }
    }

    private List<UserInfoContract> genUserInfosByEmployeeIds(List<EmployeeDepartmentContract> employeeInfos, List<String> employeeList) {
        if (employeeList == null || employeeInfos.size() == 0) {
            return null;
        }
        List<UserInfoContract> users = new ArrayList<>();
        for (String employeeId : employeeList) {
            Optional<EmployeeDepartmentContract> optEmployee = employeeInfos.stream().filter(m -> m.getId().equals(employeeId)).findAny();
            if (optEmployee.isPresent()) {
                users.add(genUserInfoContactByEmployeeDepartmentDTO(optEmployee.get()));
            } else {
                logger.warn("未找到人：" + employeeId);
            }
        }
        return users;
    }

    private UserInfoContract genUserInfoContactByEmployeeDepartmentDTO(EmployeeDepartmentContract employeeDepartmentDTO) {
        UserInfoContract userInfoContract = new UserInfoContract();
        userInfoContract.setUser_id(employeeDepartmentDTO.getId());
        userInfoContract.setName(employeeDepartmentDTO.getName());
        userInfoContract.setAvatar_url(StringUtils.isBlank(employeeDepartmentDTO.getAvatar_url()) ? "" : employeeDepartmentDTO.getAvatar_url());
        if (employeeDepartmentDTO.getDepartmentList() != null && employeeDepartmentDTO.getDepartmentList().size() > 0) {
            List<String> deptNames = new ArrayList<>();
            for (CommonIdAndNameDTO departmentInfo : employeeDepartmentDTO.getDepartmentList()) {
                deptNames.add(departmentInfo.getName());
            }
            userInfoContract.setDepts(String.join(",", deptNames));
        }
        return userInfoContract;
    }

    private void appendEmployeeIdsFromDeptManagers(List<CompanyApplyFlowItemSetV2RequestContract> flowItems, List<String> employeeIds, List<ManagerListDTO> managerLists) {
        if (CollectionUtils.isNotEmpty(managerLists)) {
            for (CompanyApplyFlowItemSetV2RequestContract item : flowItems) {
                if (!item.getItem_type().equals(ApplyFlowItemType.DeptManager.getValue())) {
                    continue;
                }
                int ind = Integer.valueOf(item.getItem_id()) - 1;
                if (ind >= managerLists.size() || ind < 0) continue;
                ManagerListDTO managerListContract = managerLists.get(ind);
                List<ManagerDTO> managerContracts = managerListContract.getManagerContractList();
                if (managerContracts != null && managerContracts.size() > 0) {
                    for (ManagerDTO managerContract : managerContracts) {
                        String employeeId = managerContract.getEmployeeId();
                        if (!StringTool.isNullOrEmpty(employeeId) && !employeeIds.contains(employeeId)) {
                            employeeIds.add(employeeId);
                        }
                    }
                }
            }
        }
    }

    private void appendUserInfosToItem(List<CompanyApplyFlowItemSetV2RequestContract> flowItems, List<EmployeeDepartmentContract> employeeInfos, Map<String, List<String>> roleEmployeeIdsMap) {
        for (CompanyApplyFlowItemSetV2RequestContract item : flowItems) {
            if (item.getItem_type().equals(ApplyFlowItemType.Employee.getValue())) {
                List<String> employeeIds = new ArrayList<>();
                employeeIds.add(item.getItem_id());
                item.setUsers(genUserInfosByEmployeeIds(employeeInfos, employeeIds));
            } else if (item.getItem_type().equals(ApplyFlowItemType.CustomRole.getValue())) {
                List<String> employeeIds = roleEmployeeIdsMap.get(item.getItem_id());
                item.setUsers(genUserInfosByEmployeeIds(employeeInfos, employeeIds));
            } else if (item.getItem_type().equals(ApplyFlowItemType.SuperAdmin.getValue())) {
                List<String> employeeIds = roleEmployeeIdsMap.get(Integer.toString(UserRole.CompanySuperAdmin.getValue()));
                item.setUsers(genUserInfosByEmployeeIds(employeeInfos, employeeIds));
            } else if (item.getItem_type().equals(ApplyFlowItemType.Admin.getValue())) {
                List<String> employeeIds = roleEmployeeIdsMap.get(Integer.toString(UserRole.CompanyAdmin.getValue()));
                item.setUsers(genUserInfosByEmployeeIds(employeeInfos, employeeIds));
            }
        }
    }

    private void appendEmployeeIdsByRoleId(String roleId, String companyId, List<String> employeeIds, Map<String, List<String>> roleEmployeeIdsMap) {
        try {
            List<String> roleEmployeeIds = customRoleService.getEmployeeIdByRoleId(roleId, companyId);
            roleEmployeeIdsMap.put(roleId, roleEmployeeIds);
            for (String roleEmployeeId : roleEmployeeIds) {
                if (!employeeIds.contains(roleEmployeeId)) {
                    employeeIds.add(roleEmployeeId);
                }
            }
        } catch (Exception ex) {
            //忽略
        }
    }

    private void removeAllItemThatNoUsers(List<CompanyApplyFlowItemSetV2RequestContract> flowItems) {
        List<CompanyApplyFlowItemSetV2RequestContract> noUserData = new ArrayList<>();
        for (CompanyApplyFlowItemSetV2RequestContract item : flowItems) {
            if (item.getUsers() == null || item.getUsers().size() == 0) {
                noUserData.add(item);
            }
        }
        flowItems.removeAll(noUserData);
    }

    /**
     * 递归获取部门id
     *
     * @param parentOrgUnitId
     * @param orgUnitPraentList
     * @return
     */
    private List<String> appendOrgUnitPraentList(String parentOrgUnitId, List<String> orgUnitPraentList) {
        //OrgUnit orgUnit = orgUnitMapper.selectByPrimaryKey(parentOrgUnitId);
        com.fenbeitong.usercenter.api.model.po.orgunit.OrgUnit orgUnit = orgUnitService.getOrgUnitById(parentOrgUnitId);
        if (orgUnit != null) {
            orgUnitPraentList.add(orgUnit.getId());
            if (StringUtils.isNotBlank(orgUnit.getParent_org_unit_id())) {
                appendOrgUnitPraentList(orgUnit.getParent_org_unit_id(), orgUnitPraentList);
            }
        }
        return orgUnitPraentList;
    }

    /**
     * 获取部门审批流设置
     *
     * @param settingType       审批流类型
     * @param orgUnitPraentList 部门集合
     * @param applyType         业务类型
     * @param companyId
     * @return
     */
    private CompanyApplySetting appendCompanyApplySettingInfo(Integer settingType, List<String> orgUnitPraentList, int applyType, String companyId) {
        CompanyApplySetting companyApplySettingDetail = null;
        for (String str : orgUnitPraentList) {
            if (settingType == null) {
                settingType = 1;
            }
            companyApplySettingDetail = companyApplySettingExtMapper.queryCompanyApplySettingByItemId(settingType, str, applyType, SettingType.Department.getValue());
            if (companyApplySettingDetail != null) {
                return companyApplySettingDetail;
            }
            continue;
        }
        //行程审批
        if (settingType == SaasFlowConstant.SETTING_TYPE_CENTER && companyApplySettingDetail == null) {
            List<CompanyApplySetting> companyApplySettings = companyApplySettingExtMapper.queryDefaultFlow(companyId, SaasFlowConstant.CAN_DELETE_STATUS, applyType, CompanySettingType.TravelApply.getValue(), SettingType.Department.getValue());
            if (CollectionUtils.isNotEmpty(companyApplySettings)) {
                return companyApplySettings.get(0);
            }
        }
        //事中审批
        if (settingType == SaasFlowConstant.SETTING_TYPE_ROUTE && companyApplySettingDetail == null) {
            List<CompanyApplySetting> companyApplySettings = companyApplySettingExtMapper.queryDefaultFlow(companyId, SaasFlowConstant.CAN_DELETE_STATUS, applyType, CompanySettingType.CenterApply.getValue(), SettingType.Department.getValue());
            if (CollectionUtils.isNotEmpty(companyApplySettings)) {
                return companyApplySettings.get(0);
            }
        }
        //订单审批
        if (settingType == SaasFlowConstant.SETTING_TYPE_ORDER && companyApplySettingDetail == null) {
            List<CompanyApplySetting> companyApplySettings = companyApplySettingExtMapper.queryDefaultFlow(companyId, SaasFlowConstant.CAN_DELETE_STATUS, applyType, CompanySettingType.OrderApply.getValue(), SettingType.Department.getValue());
            if (CollectionUtils.isNotEmpty(companyApplySettings)) {
                return companyApplySettings.get(0);
            }
        }
        //用餐审批
        if (settingType == SaasFlowConstant.SETTING_TYPE_DINNER && companyApplySettingDetail == null) {
            List<CompanyApplySetting> companyApplySettings = companyApplySettingExtMapper.queryDefaultFlow(companyId, SaasFlowConstant.CAN_DELETE_STATUS, applyType, CompanySettingType.DinnerApply.getValue(), SettingType.Department.getValue());
            if (CollectionUtils.isNotEmpty(companyApplySettings)) {
                return companyApplySettings.get(0);
            }
        }
        //外卖审批
        if (settingType == SaasFlowConstant.SETTING_TYPE_TAKEAWAY && companyApplySettingDetail == null) {
            List<CompanyApplySetting> companyApplySettings = companyApplySettingExtMapper.queryDefaultFlow(companyId, SaasFlowConstant.CAN_DELETE_STATUS, applyType, CompanySettingType.TakeawayApply.getValue(), SettingType.Department.getValue());
            if (CollectionUtils.isNotEmpty(companyApplySettings)) {
                return companyApplySettings.get(0);
            }
        }
        //虚拟卡审批
        if (settingType == SaasFlowConstant.SETTING_TYPE_BANK_INDIVIDUAL && companyApplySettingDetail == null) {
            List<CompanyApplySetting> companyApplySettings = companyApplySettingExtMapper.queryDefaultFlow(companyId, SaasFlowConstant.CAN_DELETE_STATUS, applyType, CompanySettingType.BankIndividualApply.getValue(), SettingType.Department.getValue());
            if (CollectionUtils.isNotEmpty(companyApplySettings)) {
                return companyApplySettings.get(0);
            }
        }
        //虚拟卡核销审批
        if(settingType == SaasFlowConstant.SETTING_TYPE_VIRTUAL_CARD && companyApplySettingDetail == null){
            List<CompanyApplySetting> companyApplySettings =companyApplySettingExtMapper.queryDefaultFlow(companyId,SaasFlowConstant.CAN_DELETE_STATUS,applyType,CompanySettingType.VirtualCardWriteOffApply.getValue(),SettingType.Department.getValue());
            if (CollectionUtils.isNotEmpty(companyApplySettings)) {
                return companyApplySettings.get(0);
            }
        }
        //商务订单核审批
        if(settingType == SaasFlowConstant.SETTING_TYPE_BUSINESS_ORDER_WRITEOFF && companyApplySettingDetail == null){
            List<CompanyApplySetting> companyApplySettings =companyApplySettingExtMapper.queryDefaultFlow(companyId,SaasFlowConstant.CAN_DELETE_STATUS,applyType,CompanySettingType.BusinessOrderWriteOffApply.getValue(),SettingType.Department.getValue());
            if (CollectionUtils.isNotEmpty(companyApplySettings)) {
                return companyApplySettings.get(0);
            }
        }
        //对公付款审批
        if(settingType == SaasFlowConstant.SETTING_TYPE_PAYMENT && companyApplySettingDetail == null){
            List<CompanyApplySetting> companyApplySettings = companyApplySettingExtMapper.queryDefaultFlow(companyId, SaasFlowConstant.CAN_DELETE_STATUS, applyType, CompanySettingType.PaymentApply.getValue(), SettingType.Department.getValue());
            if (CollectionUtils.isNotEmpty(companyApplySettings)) {
                return companyApplySettings.get(0);
            }
        }
        //里程审批
        if(settingType == SaasFlowConstant.SETTING_TYPE_MILEAGE && companyApplySettingDetail == null){
            List<CompanyApplySetting> companyApplySettings = companyApplySettingExtMapper.queryDefaultFlow(companyId, SaasFlowConstant.CAN_DELETE_STATUS, applyType, CompanySettingType.MileageApply.getValue(), SettingType.Department.getValue());
            if (CollectionUtils.isNotEmpty(companyApplySettings)) {
                return companyApplySettings.get(0);
            }
        }
        return null;
    }

    @Override
    public CompanyApplyFlowSetV2RequestContract getFlowByApplyId(String applyId, String companyId) {
        if (StringTool.isNullOrEmpty(applyId)) {
            return null;
        }
        CompanyApplyFlowSetV2RequestContract result = new CompanyApplyFlowSetV2RequestContract();
        List<ApplyFlowUserItem> applyFlowUserItems = applyFlowUserItemMapper.selectListByApplyId(applyId);
        if (CollectionUtils.isEmpty(applyFlowUserItems)) {
            return result;
        }
        List<String> employeeIds = new ArrayList<>();
        boolean hasCustomRole = false;
        for (ApplyFlowUserItem applyFlowUserItem : applyFlowUserItems) {
            if (applyFlowUserItem.getItemType().equals(ApplyFlowItemType.CustomRole.getValue())) {
                hasCustomRole = true;
            }
            if (!employeeIds.contains(applyFlowUserItem.getUserId())) {
                employeeIds.add(applyFlowUserItem.getUserId());
            }
        }
        List<com.fenbeitong.usercenter.api.model.po.privilege.Customrole> customroles = null;
        if (hasCustomRole) {
            customroles = customRoleService.getRoleListByCompanyId(companyId);
        }
        List<CompanyApplyFlowItemSetV2RequestContract> flowItems = new ArrayList<>();
        List<IdNameContract> userNameContracts = userService.getUserNameByIds(employeeIds, companyId);
        for (ApplyFlowUserItem applyFlowUserItem : applyFlowUserItems) {
            CompanyApplyFlowItemSetV2RequestContract item = CompanyApplyFlowItemSetV2RequestContract.fromModel(applyFlowUserItem);
            Optional<IdNameContract> userNameContract = userNameContracts == null || userNameContracts.size() == 0 ? Optional.empty() : userNameContracts.stream().filter(m -> StringTool.areEqual(m.getId(), item.getUser().getUser_id())).findAny();
            if (item.getItem_type().equals(ApplyFlowItemType.Employee.getValue())) {
                if (userNameContract.isPresent()) {
                    String userName = userNameContract.get().getName();
//                    item.setItem_name(userName);
                    item.getUser().setName(userName);
                } else {
                    item.getUser().setName(item.getItem_name());
                }
                //app需求，item_name值为空串
                item.setItem_name("");
            } else {
                if (item.getItem_type().equals(ApplyFlowItemType.CustomRole.getValue()) && customroles != null) {
                    Optional<com.fenbeitong.usercenter.api.model.po.privilege.Customrole> customrole = customroles.stream().filter(m -> m.getId().equals(item.getItem_id())).findAny();
                    if (customrole.isPresent()) {
                        item.setItem_name(customrole.get().getName());
                    }
                }
                if (userNameContract.isPresent()) {
                    item.getUser().setName(userNameContract.get().getName());
                } else {
                    String userId = item.getUser().getUser_id();
                    if (StringUtils.isNotBlank(userId)) {
                        EmployeeContract employee = iBaseOrganizationService.getEmployee(userId, companyId);
                        if (employee != null) {
                            item.getUser().setName(employee.getName());
                        }
                    } else {
                        item.getUser().setName("未知员工");
                    }
                }
                if (item.getItem_type().equals(ApplyFlowItemType.SuperAdmin.getValue())
                        || item.getItem_type().equals(ApplyFlowItemType.Admin.getValue())) {
                    item.setItem_name(ApplyFlowItemType.valueOf(item.getItem_type()).getDesc());
                }
            }
            flowItems.add(item);
        }
        result.setFixation_flow_list(flowItems);
        return result;
    }

    @Override
    public CompanyApplyFlowSetV2RequestContract getFlowCCByApplyId(String applyId, CompanyApplyFlowSetV2RequestContract companyApplyFlowSetV2RequestContract, String companyId) {
        if (StringTool.isNullOrEmpty(applyId)) {
            return null;
        }
        if (companyApplyFlowSetV2RequestContract == null) {
            companyApplyFlowSetV2RequestContract = new CompanyApplyFlowSetV2RequestContract();
        }
        //抄送通知类型
        ApplyOrder applyOrder = applyOrderMapper.selectByPrimaryKey(applyId);
        if (applyOrder != null) {
            companyApplyFlowSetV2RequestContract.setCompany_apply_type(applyOrder.getFlowType());
            if (applyOrder.getFlowCcType() != null) {
                companyApplyFlowSetV2RequestContract.setCc_notice_type_enums(new KvContract(applyOrder.getFlowCcType(), CcNoticeType.getKey(applyOrder.getFlowCcType())));
            }
        }
        //抄送人列表
        List<ApplyOrderCopyTo> applyOrderCopyTos = applyAdapterMapper.getApplyOrderCopyToExtMapper(companyId).queryApplyOrderCCByApplyOrderId(applyId);
        if (CollectionUtils.isEmpty(applyOrderCopyTos)) {
            companyApplyFlowSetV2RequestContract.setCc_list(Lists.newArrayList());
            return companyApplyFlowSetV2RequestContract;
        }
        List<String> employeeIds = new ArrayList<>();
        boolean hasCustomRole = false;
        for (ApplyOrderCopyTo applyOrderCopyTo : applyOrderCopyTos) {
            if (applyOrderCopyTo.getItemType().equals(ApplyFlowItemType.CustomRole.getValue())) {
                hasCustomRole = true;
            }
            if (!employeeIds.contains(applyOrderCopyTo.getItemId())) {
                employeeIds.add(applyOrderCopyTo.getItemId());
            }
        }
        List<com.fenbeitong.usercenter.api.model.po.privilege.Customrole> customroles = null;
        if (hasCustomRole) {
            customroles = customRoleService.getRoleListByCompanyId(companyId);
        }
        List<CompanyApplyFlowItemSetV2RequestContract> flowItems = new ArrayList<>();
        List<IdNameContract> userNameContracts = userService.getUserNameByIds(employeeIds, companyId);
        for (ApplyOrderCopyTo applyOrderCopyTo : applyOrderCopyTos) {
            CompanyApplyFlowItemSetV2RequestContract item = CompanyApplyFlowItemSetV2RequestContract.fromModel(applyOrderCopyTo);
            Optional<IdNameContract> userNameContract = userNameContracts == null || userNameContracts.size() == 0 ? Optional.empty() : userNameContracts.stream().filter(m -> StringTool.areEqual(m.getId(), item.getUser().getUser_id())).findAny();
            if (item.getItem_type().equals(ApplyFlowItemType.Employee.getValue())) {
                if (userNameContract.isPresent()) {
                    String userName = userNameContract.get().getName();
                    String avatarUrl = userNameContract.get().getAvatar_url();
//                    item.setItem_name(userName);
                    item.getUser().setName(userName);
                    item.getUser().setAvatar_url(avatarUrl);
                } else {
                    item.getUser().setName(item.getItem_name());
                }
                //app需求，item_name值为空串
                item.setItem_name("");
            } else {
                if (item.getItem_type().equals(ApplyFlowItemType.CustomRole.getValue()) && customroles != null) {
                    Optional<com.fenbeitong.usercenter.api.model.po.privilege.Customrole> customrole = customroles.stream().filter(m -> m.getId().equals(item.getItem_id())).findAny();
                    if (customrole.isPresent()) {
                        item.setItem_name(customrole.get().getName());
                    }
                }
                if (userNameContract.isPresent()) {
                    item.getUser().setName(userNameContract.get().getName());
                } else {
                    String userId = item.getUser().getUser_id();
                    if (StringUtils.isNotBlank(userId)) {
                        EmployeeContract employee = iBaseOrganizationService.getEmployee(userId, companyId);
                        if (employee != null) {
                            item.getUser().setName(employee.getName());
                        }
                    } else {
                        item.getUser().setName("未知员工");
                    }
                }
                if (item.getItem_type().equals(ApplyFlowItemType.SuperAdmin.getValue())
                        || item.getItem_type().equals(ApplyFlowItemType.Admin.getValue())) {
                    item.setItem_name(ApplyFlowItemType.valueOf(item.getItem_type()).getDesc());
                }
            }
            if (StringUtils.isNotBlank(item.getUser().getName())) {
                flowItems.add(item);
            }
        }
        companyApplyFlowSetV2RequestContract.setCc_list(flowItems);
        return companyApplyFlowSetV2RequestContract;
    }

    /**
     * 应用审批流
     *
     * @param settingType
     * @param requestContract
     * @param applyId
     * @param userId
     * @param companyId
     * @param ip
     * @param category
     * @param costAttributionId
     * @param exceedBuyType
     * @param businessType
     * @param rootApplyId
     * @return
     * @throws SaasException
     */
    @Override
    public ApplyFlowApplicateResponseContract applicateFlow(Integer settingType, CompanyApplyFlowSetV2RequestContract requestContract, String applyId, String userId, String companyId, String ip, Integer category, String costAttributionId, Integer exceedBuyType, Integer businessType, String rootApplyId) throws SaasException {
        CompanyApplyFlowSetV2RequestContract companySetting = checkApplicateData(settingType, requestContract, companyId, userId, category, costAttributionId, exceedBuyType, businessType);
        CompanyApplyType companyApplyType = CompanyApplyType.valueOf(companySetting.getCompany_apply_type());
        ApplyFlowApplicateResponseContract response = new ApplyFlowApplicateResponseContract();
        response.setCompanyApplyType(companyApplyType);
        if (companyApplyType == CompanyApplyType.Flow || companyApplyType == CompanyApplyType.CONDITIONAL) {
            ApplyFlowUser oldApplyFlowUser = applyFlowUserMapper.selectByApplyOrderId(applyId);
            String applyFlowUserId;
            if (oldApplyFlowUser != null) {
                applyFlowUserId = oldApplyFlowUser.getId();
                applyFlowUserMapper.deleteByPrimaryKey(applyFlowUserId);
                applyFlowUserItemMapper.deleteByApplyFlowUserId(applyFlowUserId);
            } else {
                applyFlowUserId = IDTool.CreateUniqueID();
            }
            Date now = new Date();
            ApplyFlowUser applyFlowUser = new ApplyFlowUser();
            applyFlowUser.setId(applyFlowUserId);
            applyFlowUser.setApplyFlowId(companySetting.getApply_flow_id());
            applyFlowUser.setApplyOrderId(applyId);
            applyFlowUser.setCompanyId(companyId);
            applyFlowUser.setCreateTime(now);
            applyFlowUser.setUpdateTime(now);
            applyFlowUser.setUserId(userId);
            applyFlowUserMapper.insert(applyFlowUser);

            int sort = 1;
            boolean first = true;
            for (CompanyApplyFlowItemSetV2RequestContract item : requestContract.getFixation_flow_list()) {
                ApplyFlowUserItem applyFlowUserItem = new ApplyFlowUserItem();
                applyFlowUserItem.setApplyFlowUserId(applyFlowUserId);
                applyFlowUserItem.setApplyOrderId(applyId);
                applyFlowUserItem.setId(IDTool.CreateUniqueID());
                applyFlowUserItem.setItemId(item.getItem_id());
                applyFlowUserItem.setItemName(item.getItem_name());
                applyFlowUserItem.setItemType(item.getItem_type());
                applyFlowUserItem.setSort(sort++);
                ApplyFlowUserItemStatus status = first ? ApplyFlowUserItemStatus.PendingAudit : ApplyFlowUserItemStatus.Waiting;
                applyFlowUserItem.setStatus(status.getValue());
                applyFlowUserItem.setUserId(item.getUser().getUser_id());
                applyFlowUserItem.setUpdateTime(now);
                applyFlowUserItemMapper.insert(applyFlowUserItem);
                first = false;
            }

            List<CompanyApplyFlowItemSetV2RequestContract> fixationFlowList = requestContract.getFixation_flow_list();
            int logSort = 100;
            if (settingType==SaasFlowConstant.SETTING_TYPE_VIRTUAL_CARD||settingType==SaasFlowConstant.SETTING_TYPE_BUSINESS_ORDER_WRITEOFF){
                List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectListByCreateTimeDesc(applyId);
                if (!CollectionUtils.isEmpty(applyOrderLogList)) {
                    Integer lastSort = applyOrderLogList.get(0).getSort();
                    if (lastSort != null) {
                        logSort = lastSort + 100;
                    }
                }
            }
            if (!CollectionUtils.isEmpty(fixationFlowList)) {
                ApplyOrderLog log = new ApplyOrderLog();
                log.setApplyOrderId(applyId);
                log.setCreateTime(now);
                log.setIp(ip);
                log.setSponsorId(userId);
                log.setReceiverId(fixationFlowList.get(0).getUser().getUser_id());
                log.setCheckReason(null);
                log.setAction(ApplyLogAction.Submit.getValue());
                log.setSort(logSort);
                log.setRootApplyOrderId(rootApplyId);
                applyOrderLogMapper.insertSelective(log);
                response.setCurrentLogId(log.getId());
            }
            logSort = logSort+100;
            for (int i = 0; i < fixationFlowList.size(); i++) {
                ApplyOrderLog log = new ApplyOrderLog();
                log.setApplyOrderId(applyId);
                log.setIp(ip);
                log.setSponsorId(fixationFlowList.get(i).getUser().getUser_id());
                log.setReceiverId("");
                log.setCheckReason(null);
                //第一次进来
                if (i == 0) {
                    log.setAction(ApplyLogAction.Approval.getValue());
                } else {
                    log.setAction(ApplyLogAction.Unknown.getValue());
                }
                log.setSort(logSort);
                log.setRootApplyOrderId(rootApplyId);
                applyOrderLogMapper.insertSelective(log);
                if (i == 0) {
                    response.setNextLogId(log.getId());
                }
                logSort = logSort + 100;
            }
            response.setApprover(requestContract.getFixation_flow_list().get(0).getUser().getUser_id());
        }
        return response;
    }

    private CompanyApplyFlowSetV2RequestContract checkApplicateData(Integer settingType, CompanyApplyFlowSetV2RequestContract requestContract, String companyId, String userId, Integer category, String costAttributionId, Integer exceedBuyType, Integer businessType) throws SaasException {
        if (requestContract == null) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        CompanyApplyType companyApplyType = CompanyApplyType.valueOf(requestContract.getCompany_apply_type());
        if (companyApplyType == CompanyApplyType.Unknown) {
            throw new SaasException(GlobalResponseCode.CompanyApplyTypeInvalid);
        }
        ApplyType applyType = ApplyType.valueOf(requestContract.getApply_type());
        if (applyType == ApplyType.Unknown) {
            throw new SaasException(GlobalResponseCode.ApplyTypeInvalid);
        }
        if (applyType == ApplyType.ApplyTaxi) {
            applyType = ApplyType.Taxi;
        }
        if (applyType == ApplyType.Petty) {
            applyType = ApplyType.BankIndividual;
        }
        if (settingType == SaasFlowConstant.SETTING_TYPE_ROUTE) {
            if (applyType == ApplyType.Air || applyType == ApplyType.IntlAir || applyType == ApplyType.Hotel || applyType == ApplyType.Train) {
                applyType = ApplyType.ChaiLv;
            }
        }
        if (companyApplyType == CompanyApplyType.Flow || companyApplyType == CompanyApplyType.CONDITIONAL) {
            List<CompanyApplyFlowItemSetV2RequestContract> items = requestContract.getFixation_flow_list();
            if (items == null || items.size() == 0) {
                throw new SaasException(GlobalResponseCode.ApplyFlowItemIdNotSet);
            }
        }
        CompanyApplyFlowSetV2RequestContract companySetting = getSettingWithEmployee(settingType, companyId, applyType, userId, requestContract.getBudget(), category, costAttributionId, exceedBuyType, businessType);
        CompanyApplyType currentCompanyApplyType = CompanyApplyType.valueOf(companySetting.getCompany_apply_type());
        if (companyApplyType != currentCompanyApplyType) {
            throw new SaasException(GlobalResponseCode.CompanyApplyTypeNotMatched);
        }
        if (companyApplyType == CompanyApplyType.Flow || companyApplyType == CompanyApplyType.CONDITIONAL) {
            List<CompanyApplyFlowItemSetV2RequestContract> items = requestContract.getFixation_flow_list();
            //检测公司审批流是否已更新
            if (items.size() != companySetting.getFixation_flow_list().size()) {
                throw new SaasException(GlobalResponseCode.CompanyApplySettingHasUpdate);
            }
            for (int i = 0; i < items.size(); i++) {
                CompanyApplyFlowItemSetV2RequestContract item = items.get(i);
                CompanyApplyFlowItemSetV2RequestContract currentItem = companySetting.getFixation_flow_list().get(i);
                if (!item.getItem_type().equals(currentItem.getItem_type())) {
                    throw new SaasException(GlobalResponseCode.CompanyApplySettingHasUpdate);
                }
            }
            //检测节点是否指定员工，如果节点只有一个员工，直接赋值
            for (int i = 0; i < items.size(); i++) {
                CompanyApplyFlowItemSetV2RequestContract currentItem = companySetting.getFixation_flow_list().get(i);
                CompanyApplyFlowItemSetV2RequestContract item = items.get(i);
                List<UserInfoContract> users = currentItem.getUsers();
                //注：users.size()不可能=0，在生成此值的时候已做过检测
                if (users.size() == 1) {
                    //审批角色类型为不可选择自己人
                    if (companySetting.getRole_approve_type() == CompanyRoleApproveType.ExcludeOneself.getValue()) {
                        if (userId.equals(users.get(0).getUser_id()) && item.getItem_type() != ApplyFlowItemType.Employee.getValue()) {
                            throw new SaasException(GlobalResponseCode.ApplyApproverRoleCannotBeSelf);
                        }
                    }
                    //只有一个员工，直接指定至节点审批人
                    item.setUser(users.get(0));
                } else {
                    //有多个员工，客户端需要指定一个人，并且需要检测这个人是否还在users中
                    UserInfoContract selectedUser = item.getUser();
                    if (selectedUser == null || StringTool.isNullOrEmpty(selectedUser.getUser_id())) {
                        //throw new SaasException(GlobalResponseCode.ApplyFlowItemNeedSetUser, String.format("第%d个审批节点需要指定审批人", i + 1));
                        throw new SaasException(GlobalResponseCode.ApplyFlowItemSelectUser);
                    }
                    if (!users.stream().anyMatch(m -> m.getUser_id().equals(selectedUser.getUser_id()))) {
                        //throw new SaasException(GlobalResponseCode.ApplyFlowItemNeedSetUser, String.format("第%d个审批节点指定的审批人已失效，请选择其他人", i + 1));
                        throw new SaasException(GlobalResponseCode.ApplyFlowItemSelectUser);
                    }
                    CompanyRoleApproveType companyRoleApproveType = CompanyRoleApproveType.valueOf(companySetting.getRole_approve_type());
                    if (companyRoleApproveType == CompanyRoleApproveType.Unknown) {
                        throw new SaasException(GlobalResponseCode.CompanyRoleApproveTypeIsError);
                    }
                    //审批角色类型为不可选择自己人
                    if (companySetting.getRole_approve_type() == CompanyRoleApproveType.ExcludeOneself.getValue()) {
                        if (userId.equals(selectedUser.getUser_id()) && item.getItem_type() != ApplyFlowItemType.Employee.getValue()) {
                            throw new SaasException(GlobalResponseCode.ApplyApproverRoleCannotBeSelf);
                        }
                    }
                }
            }
            //检查抄送人
            List<CompanyApplyFlowItemSetV2RequestContract> ccitems = requestContract.getCc_list();
            if (CollectionUtils.isNotEmpty(ccitems)) {
                //检测节点是否指定员工，如果节点只有一个员工，直接赋值
                for (int i = 0; i < ccitems.size(); i++) {
                    CompanyApplyFlowItemSetV2RequestContract item = ccitems.get(i);
                    //注：users.size()不可能=0，在生成此值的时候已做过检测
                    //有多个员工，客户端需要指定一个人，并且需要检测这个人是否还在users中
                    UserInfoContract selectedUser = item.getUser();
                    if (selectedUser == null || StringTool.isNullOrEmpty(selectedUser.getUser_id())) {
                        //throw new SaasException(GlobalResponseCode.ApplyFlowItemNeedSetUser, String.format("第%d个审批节点需要指定审批人", i + 1));
                        throw new SaasException(GlobalResponseCode.ApplyFlowItemSelectUser, CoreLanguage.Common_Exception_PleaseSelectCopyUser.getMessage());
                    }
                }
            }
        }
        return companySetting;
    }

    @Override
    @Transactional(value = "saas")
    public void initCompanyApplySetting(int applyType) {
        logger.info("开始处理事中审批流默认审批流定时任务......");
        /*CompanyExample companyExample = new CompanyExample();
        companyExample.createCriteria();
        List<Company> companieList = companyMapper.selectByExample(companyExample);*/
        List<com.fenbeitong.usercenter.api.model.po.company.Company> companyList = sloveCompanyListInfo();
        List<String> idList = companyList.stream().map(x -> {
            String id = x.getId();
            return id;
        }).collect(Collectors.toList());
        CompanyApplySettingExample companyApplySettingExample = new CompanyApplySettingExample();
        companyApplySettingExample.createCriteria().andCompanyIdIn(idList).andApplyStatusEqualTo(SaasFlowConstant.IS_NORMAL_STATUS)
                .andCompanyApplyTypeEqualTo(2).andCanDeleteEqualTo(SaasFlowConstant.CAN_DELETE_STATUS).andApplyTypeEqualTo(applyType);
        List<CompanyApplySetting> companyApplySettings = companyApplySettingMapper.selectByExample(companyApplySettingExample);
        if (companyApplySettings != null && companyApplySettings.size() != idList.size()) {
            PageInfo pageInfo = new PageInfo(1, 500, ObjUtils.toLong(idList.size()));
            int pages = pageInfo.getPages();
            logger.info("共有{}个公司需要初始化审批流设置，分成{}次查询", companyApplySettings.size(), pages);
            for (int i = 1; i <= pages; i++) {
                pageInfo.setPageNum(i);
                //List<String> ids = companyMapperExt.queryCompanyId(pageInfo.getStartRow(), pageInfo.getPageSize());
                List<String> ids = irCompanyService.listAllCompanies(i, pageInfo.getPageSize()).stream().map(company -> company.getId()).collect(Collectors.toList());
                for (String idInfo : ids) {
                    CompanyApplySettingExample companyApplySettingExampleInfo = new CompanyApplySettingExample();
                    companyApplySettingExampleInfo.createCriteria().andCompanyIdEqualTo(idInfo).andApplyStatusEqualTo(SaasFlowConstant.IS_NORMAL_STATUS)
                            .andCompanyApplyTypeEqualTo(2).andCanDeleteEqualTo(SaasFlowConstant.CAN_DELETE_STATUS).andApplyTypeEqualTo(applyType);
                    List<CompanyApplySetting> companyApplySettingList = companyApplySettingMapper.selectByExample(companyApplySettingExampleInfo);
                    if (companyApplySettingList != null && companyApplySettingList.size() > 0) {
                        continue;
                    }
                    Date now = new Date();
                    String id = IDTool.CreateUniqueID();
                    ApplyFlow applyFlow = new ApplyFlow();
                    applyFlow.setId(id);
                    applyFlow.setCompanyId(idInfo);
                    applyFlow.setCreateTime(now);
                    applyFlow.setOperatorId("");
                    applyFlowMapper.insert(applyFlow);

                    CompanyApplySetting companyApplySetting = new CompanyApplySetting();
                    companyApplySetting.setId(IDTool.CreateUniqueID());
                    companyApplySetting.setCompanyId(idInfo);
                    //差旅
                    companyApplySetting.setApplyType(applyType);
                    companyApplySetting.setCreateTime(now);
                    companyApplySetting.setUpdateTime(now);
                    //1.弹性审批流，2.固定审批流，3.分条件审批流
                    companyApplySetting.setCompanyApplyType(2);
                    companyApplySetting.setApplyFlowId(id);
                    companyApplySetting.setOperatorId("");
                    companyApplySetting.setFlowName("默认审批流");
                    //1.仅全部同意后通知 2.仅发起时通知 3.发起时和全部同意时都通知
                    companyApplySetting.setCcNoticeType(2);
                    //删除状态1.正常 2.删除
                    companyApplySetting.setApplyStatus(1);
                    //1.不是默认审批流 2.默认审批流
                    companyApplySetting.setCanDelete(2);
                    //1.行程审批  2.事中审批
                    companyApplySetting.setCompanySettingType(2);
                    companyApplySetting.setCostCenterStatus(CostCenterStateEnum.Stop.getKey());
                    companyApplySettingMapper.insert(companyApplySetting);

                    ApplyFlowItem applyFlowItem = new ApplyFlowItem();
                    applyFlowItem.setId(IDTool.CreateUniqueID());
                    applyFlowItem.setApplyFlowId(id);
                    applyFlowItem.setSort(1);
                    applyFlowItem.setItemType(16);
                    applyFlowItem.setItemId("1");
                    applyFlowItem.setCreateTime(now);
                    applyFlowItemMapper.insertSelective(applyFlowItem);
                }
            }
        } else {
            logger.info("无公司需要事中审批单需要处理");
        }
        logger.info("结束处理事中审批流默认审批流定时任务......");
    }

    /**
     * 获取部门的审批流配置
     *
     * @param userId
     * @param settingType
     * @param applyType
     * @param companyId
     * @return
     */
    private CompanyApplySetting queryDeptCompanyApplySettingInfo(String userId, Integer settingType, ApplyType applyType, String companyId,String costAttributionId) {
        List<String> orgUnitPraentList = Lists.newLinkedList();
        String parentOrgUnitId=null;
        //虚拟卡传递项目id
        if (ObjUtils.isNotEmpty(costAttributionId)) {
            //从所传部门递归向上
            orgUnitPraentList.add(costAttributionId);
            com.fenbeitong.usercenter.api.model.po.orgunit.OrgUnit orgUnit = orgUnitService.getOrgUnitById(costAttributionId);
            //OrgUnit orgUnit = orgUnitMapper.selectByPrimaryKey(costAttributionId);
            if (ObjUtils.isNotEmpty(orgUnit)) {
                parentOrgUnitId = orgUnit.getParent_org_unit_id();
            }
        } else {
            //查询所属部门，从员工当前部门递归向上
            OrgUnit department = baseOrganizationService.getOrgUnit(userId, companyId);
            if (department == null) {
                return null;
            }
            parentOrgUnitId = department.getParentOrgUnitId();
            orgUnitPraentList.add(department.getId());
        }
        if (StringUtils.isNotBlank(parentOrgUnitId)) {
            orgUnitPraentList = appendOrgUnitPraentList(parentOrgUnitId, orgUnitPraentList);
        }
        return appendCompanyApplySettingInfo(settingType, orgUnitPraentList, applyType.getValue(), companyId);
    }

    /**
     * 获取项目的审批流配置
     *
     * @param userId
     * @param settingType
     * @param applyType
     * @param companyId
     * @return
     */
    private CompanyApplySetting queryCostCenterCompanyApplySettingInfo(String userId, Integer settingType, ApplyType applyType, String companyId, String costAttributionId) {
        CompanyApplySetting companyApplySettingDetail = null;
        companyApplySettingDetail = companyApplySettingExtMapper.queryCompanyApplySettingByItemId(settingType, costAttributionId, applyType.getValue(), SettingType.Project.getValue());
        if (companyApplySettingDetail != null) {
            return companyApplySettingDetail;
        }
        //行程审批
        if (settingType == SaasFlowConstant.SETTING_TYPE_CENTER && companyApplySettingDetail == null) {
            List<CompanyApplySetting> companyApplySettings = companyApplySettingExtMapper.queryDefaultFlow(companyId, SaasFlowConstant.CAN_DELETE_STATUS, applyType.getValue(), CompanySettingType.TravelApply.getValue(), SettingType.Project.getValue());
            if (CollectionUtils.isNotEmpty(companyApplySettings)) {
                return companyApplySettings.get(0);
            }
        }
        //事中审批
        if (settingType == SaasFlowConstant.SETTING_TYPE_ROUTE && companyApplySettingDetail == null) {
            List<CompanyApplySetting> companyApplySettings = companyApplySettingExtMapper.queryDefaultFlow(companyId, SaasFlowConstant.CAN_DELETE_STATUS, applyType.getValue(), CompanySettingType.CenterApply.getValue(), SettingType.Project.getValue());
            if (CollectionUtils.isNotEmpty(companyApplySettings)) {
                return companyApplySettings.get(0);
            }
        }
        //订单审批
        if (settingType == SaasFlowConstant.SETTING_TYPE_ORDER && companyApplySettingDetail == null) {
            List<CompanyApplySetting> companyApplySettings = companyApplySettingExtMapper.queryDefaultFlow(companyId, SaasFlowConstant.CAN_DELETE_STATUS, applyType.getValue(), CompanySettingType.OrderApply.getValue(), SettingType.Project.getValue());
            if (CollectionUtils.isNotEmpty(companyApplySettings)) {
                return companyApplySettings.get(0);
            }
        }
        //用餐审批
        if (settingType == SaasFlowConstant.SETTING_TYPE_DINNER && companyApplySettingDetail == null) {
            List<CompanyApplySetting> companyApplySettings = companyApplySettingExtMapper.queryDefaultFlow(companyId, SaasFlowConstant.CAN_DELETE_STATUS, applyType.getValue(), CompanySettingType.DinnerApply.getValue(), SettingType.Project.getValue());
            if (CollectionUtils.isNotEmpty(companyApplySettings)) {
                return companyApplySettings.get(0);
            }
        }
        //外卖
        if (settingType == SaasFlowConstant.SETTING_TYPE_TAKEAWAY && companyApplySettingDetail == null) {
            List<CompanyApplySetting> companyApplySettings = companyApplySettingExtMapper.queryDefaultFlow(companyId, SaasFlowConstant.CAN_DELETE_STATUS, applyType.getValue(), CompanySettingType.TakeawayApply.getValue(), SettingType.Project.getValue());
            if (CollectionUtils.isNotEmpty(companyApplySettings)) {
                return companyApplySettings.get(0);
            }
        }
        //虚拟卡审批
        if (settingType == SaasFlowConstant.SETTING_TYPE_BANK_INDIVIDUAL && companyApplySettingDetail == null) {
            List<CompanyApplySetting> companyApplySettings = companyApplySettingExtMapper.queryDefaultFlow(companyId, SaasFlowConstant.CAN_DELETE_STATUS, applyType.getValue(), CompanySettingType.BankIndividualApply.getValue(), SettingType.Project.getValue());
            if (CollectionUtils.isNotEmpty(companyApplySettings)) {
                return companyApplySettings.get(0);
            }
        }
        // 虚拟卡核销审批
        if(settingType == SaasFlowConstant.SETTING_TYPE_VIRTUAL_CARD && companyApplySettingDetail==null){
            List<CompanyApplySetting> companyApplySettings = companyApplySettingExtMapper.queryDefaultFlow(companyId, SaasFlowConstant.CAN_DELETE_STATUS, applyType.getValue(), CompanySettingType.VirtualCardWriteOffApply.getValue(), SettingType.Project.getValue());
            if (CollectionUtils.isNotEmpty(companyApplySettings)) {
                return companyApplySettings.get(0);
            }
        }
        // 商务订单核审批
        if(settingType == SaasFlowConstant.SETTING_TYPE_BUSINESS_ORDER_WRITEOFF && companyApplySettingDetail==null){
            List<CompanyApplySetting> companyApplySettings = companyApplySettingExtMapper.queryDefaultFlow(companyId, SaasFlowConstant.CAN_DELETE_STATUS, applyType.getValue(), CompanySettingType.BusinessOrderWriteOffApply.getValue(), SettingType.Project.getValue());
            if (CollectionUtils.isNotEmpty(companyApplySettings)) {
                return companyApplySettings.get(0);
            }
        }
        //对公付款审批
        if(settingType == SaasFlowConstant.SETTING_TYPE_PAYMENT && companyApplySettingDetail == null){
            List<CompanyApplySetting> companyApplySettings = companyApplySettingExtMapper.queryDefaultFlow(companyId, SaasFlowConstant.CAN_DELETE_STATUS, applyType.getValue(), CompanySettingType.PaymentApply.getValue(), SettingType.Project.getValue());
            if (CollectionUtils.isNotEmpty(companyApplySettings)) {
                return companyApplySettings.get(0);
            }
        }
        //里程审批
        if (settingType == SaasFlowConstant.SETTING_TYPE_MILEAGE && companyApplySettingDetail == null) {
            List<CompanyApplySetting> companyApplySettings = companyApplySettingExtMapper.queryDefaultFlow(companyId, SaasFlowConstant.CAN_DELETE_STATUS, applyType.getValue(), CompanySettingType.MileageApply.getValue(), SettingType.Project.getValue());
            if (CollectionUtils.isNotEmpty(companyApplySettings)) {
                return companyApplySettings.get(0);
            }
        }
        return null;
    }

    @Override
    @Transactional(value = "saas")
    public void initAllCompanyApplySetting(int applyType, int companySettingType, int companyApplyType, int costAttributionCategory) {
        logger.info("开始处理默认项目审批流和默认弹性审批流定时任务......");
        /*CompanyExample companyExample = new CompanyExample();
        companyExample.createCriteria();
        long companieList = companyMapper.countByExample(companyExample);*/
        long companyNum = irCompanyService.countCompanies();
        PageInfo pageInfo = new PageInfo(1, 1000, companyNum);
        int pages = pageInfo.getPages();
        for (int i = 1; i <= pages; i++) {
            pageInfo.setPageNum(i);
            //List<String> ids = companyMapperExt.queryCompanyId(pageInfo.getStartRow(), pageInfo.getPageSize());
            List<String> ids = irCompanyService.listAllCompanies(i, pageInfo.getPageSize()).stream().map(company -> company.getId()).collect(Collectors.toList());
            for (String idInfo : ids) {
                CompanyApplySettingExample companyApplySettingExampleInfo = new CompanyApplySettingExample();
                companyApplySettingExampleInfo.createCriteria().andCompanyIdEqualTo(idInfo).andApplyStatusEqualTo(SaasFlowConstant.IS_NORMAL_STATUS)
                        .andCompanySettingTypeEqualTo(companySettingType).andCanDeleteEqualTo(SaasFlowConstant.CAN_DELETE_STATUS).andApplyTypeEqualTo(applyType).andCostAttributionCategoryEqualTo(costAttributionCategory);
                List<CompanyApplySetting> companyApplySettingList = companyApplySettingMapper.selectByExample(companyApplySettingExampleInfo);
                if (companyApplySettingList != null && companyApplySettingList.size() > 0) {
                    continue;
                }
                Date now = new Date();
                String companyApplySettingId = IDTool.CreateUniqueID();
                CompanyApplySetting companyApplySetting = new CompanyApplySetting();
                companyApplySetting.setId(companyApplySettingId);
                companyApplySetting.setCompanyId(idInfo);
                //差旅
                companyApplySetting.setApplyType(applyType);
                companyApplySetting.setCreateTime(now);
                companyApplySetting.setUpdateTime(now);
                companyApplySetting.setOperatorId("");
                if (costAttributionCategory == SettingType.Department.getValue()) {
                    companyApplySetting.setFlowName("部门默认审批流");
                    companyApplySetting.setCostCenterStatus(CostCenterStateEnum.Start.getKey());
                } else if (costAttributionCategory == SettingType.Project.getValue()) {
                    companyApplySetting.setFlowName("项目默认审批流");
                    //项目默认审批流为停用状态
                    companyApplySetting.setCostCenterStatus(CostCenterStateEnum.Stop.getKey());
                }
                //删除状态1.正常 2.删除
                companyApplySetting.setApplyStatus(1);
                //1.不是默认审批流 2.默认审批流
                companyApplySetting.setCanDelete(2);
                //1.行程审批  2.事中审批 3.订单审批
                companyApplySetting.setCompanySettingType(companySettingType);
                //1.部门 2.项目
                companyApplySetting.setCostAttributionCategory(costAttributionCategory);
                companyApplySetting.setRoleApproveType(CompanyRoleApproveType.All.getValue());
                companyApplySettingMapper.insert(companyApplySetting);
                String applyFlowId = IDTool.CreateUniqueID();
                ApplyFlow applyFlow = new ApplyFlow();
                applyFlow.setId(applyFlowId);
                applyFlow.setCompanyId(idInfo);
                applyFlow.setCreateTime(now);
                applyFlow.setOperatorId("");
                applyFlow.setCompanyApplySettingId(companyApplySettingId);
                //1.弹性审批流，2.固定审批流，3.分条件审批流
                applyFlow.setCompanyApplyType(companyApplyType);
                //1.仅全部同意后通知 2.仅发起时通知 3.发起时和全部同意时都通知
                applyFlow.setCcNoticeType(2);
                Integer isExceedBuyFlow = 0;
                if (applyType == ApplyType.Dinner.getValue()) {
                    isExceedBuyFlow = 1;
                }
                applyFlow.setIsExceedBuyFlow(isExceedBuyFlow);
                applyFlowMapper.insert(applyFlow);
                if (companyApplyType == CompanyApplyType.Flow.getValue()) {
                    ApplyFlowItem applyFlowItem = new ApplyFlowItem();
                    applyFlowItem.setId(IDTool.CreateUniqueID());
                    applyFlowItem.setApplyFlowId(applyFlowId);
                    applyFlowItem.setSort(1);
                    if (applyType==ApplyType.BankIndividual.getValue()){
                        applyFlowItem.setItemType(4);
                    }else {
                        applyFlowItem.setItemType(16);
                        applyFlowItem.setItemId("1");
                    }
                    applyFlowItem.setCreateTime(now);
                    applyFlowItemMapper.insertSelective(applyFlowItem);
                }
            }
        }
        logger.info("结束处理默认项目审批流和默认弹性审批流定时任务......");
    }


    /**
     * 处理项目主管的员工数据
     *
     * @param flowItems
     * @param employeeIds
     * @param managerLists
     */
    private void appendEmployeeIdsFromCostCenterManagers(List<CompanyApplyFlowItemSetV2RequestContract> flowItems, List<String> employeeIds, List<String> managerLists) {
        if (CollectionUtils.isNotEmpty(managerLists)) {
            for (CompanyApplyFlowItemSetV2RequestContract item : flowItems) {
                if (!item.getItem_type().equals(ApplyFlowItemType.DeptManager.getValue())) {
                    continue;
                }
                if (CollectionUtils.isNotEmpty(managerLists)) {
                    for (String employeeId : managerLists) {
                        if (!StringTool.isNullOrEmpty(employeeId) && !employeeIds.contains(employeeId)) {
                            employeeIds.add(employeeId);
                        }
                    }
                }
            }
        }
    }

    private void appendCostManagerUserInfosToItem(List<CompanyApplyFlowItemSetV2RequestContract> flowItems, List<EmployeeDepartmentContract> employeeInfos, List<String> managerListContracts) {
        if (managerListContracts != null && managerListContracts.size() > 0) {
            for (CompanyApplyFlowItemSetV2RequestContract item : flowItems) {
                if (!item.getItem_type().equals(ApplyFlowItemType.DeptManager.getValue())) {
                    continue;
                }
                if (CollectionUtils.isNotEmpty(managerListContracts)) {
                    List<String> employeeIds = managerListContracts;
                    item.setUsers(genUserInfosByEmployeeIds(employeeInfos, employeeIds));
                }
            }
        }
    }

    @Override
    public GlobalResponseCode updateStatus(String id, Integer costCenterStatus) {
        if (StringUtils.isBlank(id) || CostCenterStateEnum.getByKey(costCenterStatus) == null) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
        CompanyApplySetting record = new CompanyApplySetting();
        record.setId(id);
        record.setCostCenterStatus(costCenterStatus);
        companyApplySettingMapper.updateByPrimaryKeySelective(record);
        CompanyApplySetting companyApplySetting = companyApplySettingMapper.selectByPrimaryKey(id);
        companyLogKafkaProducerService.sendCompanyLogMsg(getKafkaCompanyLogMsg(ApplyType.valueOf(companyApplySetting.getApplyType()),
                CompanySettingType.valueOf(companyApplySetting.getCompanySettingType()),
                CostCenterStateEnum.getByKey(costCenterStatus),
                companyApplySetting.getFlowName()));
        return GlobalResponseCode.Success;
    }

    /**
     * 审批流配置数据清洗
     */
    @Override
    @Transactional(value = "saas")
    public void updateApplyFlowSettingData() {
        // 1.补充非弹性审批流字段值
        int c1 = applyFlowExtMapper.updateNonElasticFlowFields();
        logger.info("1.补充非弹性审批流字段值成功,更新数据{}条。", c1);

        // 2.补全弹性审批流数据(超规审批流只支持固定审批流,所以弹性审批流均为非超规审批流)
        // 2.1 查询原弹性审批流数据
        List<ApplyFlow> elasticApplyFlowList = applyFlowExtMapper.queryElasticFlowData();
        if (!CollectionUtils.isEmpty(elasticApplyFlowList)) {
            Date now = new Date();
            int c2 = 0;
            // 2.2 遍历生成主键
            List<ApplyFlow> tmpApplyFlowList = Lists.newArrayList();
            for (ApplyFlow applyFlow : elasticApplyFlowList) {
                if (tmpApplyFlowList.size() == 500) {
                    // 2.3 批量插入弹性审批流数据
                    int c = applyFlowExtMapper.batchInsert(tmpApplyFlowList);
                    c2 += c;
                    tmpApplyFlowList = Lists.newArrayList();
                }
                applyFlow.setId(IDTool.CreateUniqueID());
                applyFlow.setCreateTime(now);
                tmpApplyFlowList.add(applyFlow);
            }
            if (!CollectionUtils.isEmpty(tmpApplyFlowList)) {
                int c = applyFlowExtMapper.batchInsert(tmpApplyFlowList);
                c2 += c;
            }
            logger.info("2.补全弹性审批流数据成功,更新数据{}条。", c2);
        }

        // 3.审批流配置抄送数据清洗
        int c3 = applyFlowCopyToExtMapper.updateApplyFlowCopyTo();
        logger.info("3.审批流配置抄送数据清洗成功,更新数据{}条。", c3);

        // 4.差旅订单审批默认正常审批流
        // 4.1 查询差旅订单审批流数据
        List<ApplyFlow> chailvOrderApplyFlowList = applyFlowExtMapper.queryChailvOrderFlowData();
        if (!CollectionUtils.isEmpty(chailvOrderApplyFlowList)) {
            Date now = new Date();
            int c4 = 0;
            // 4.2 遍历生成主键
            List<ApplyFlow> tmpOrderApplyFlowList = Lists.newArrayList();
            for (ApplyFlow applyFlow : chailvOrderApplyFlowList) {
                if (tmpOrderApplyFlowList.size() == 500) {
                    // 4.3 批量插入差旅订单审批默认正常审批流数据
                    int c = applyFlowExtMapper.batchInsert(tmpOrderApplyFlowList);
                    c4 += c;
                    tmpOrderApplyFlowList = Lists.newArrayList();
                }
                applyFlow.setId(IDTool.CreateUniqueID());
                applyFlow.setCreateTime(now);
                tmpOrderApplyFlowList.add(applyFlow);
            }

            if (!CollectionUtils.isEmpty(tmpOrderApplyFlowList)) {
                // 2.3 批量插入弹性审批流数据
                int c = applyFlowExtMapper.batchInsert(tmpOrderApplyFlowList);
                c4 += c;
            }
            logger.info("4.差旅订单审批默认正常审批流数据成功,更新数据{}条。", c4);
        }
    }

    @Override
    public List<CompanyApplySetting> queryCompanySettingByUserId(String companyId, String userId) {
        List<CompanyApplySetting> companyApplySettings = companyApplySettingExtMapper.queryCompanySettingByUserId(companyId, userId);
        List<CompanyApplySetting> moreCompanyApplySettingList = Lists.newArrayList();
        for (CompanyApplySetting companyApplySetting : companyApplySettings) {
            Integer integer = companyApplySettingExtMapper.queryApplyFlowItemByCompanyApplySettingId(companyId, companyApplySetting.getId());
            if (integer > 1) {
                moreCompanyApplySettingList.add(companyApplySetting);
            }
        }
        if (CollectionUtils.isNotEmpty(moreCompanyApplySettingList)) {
            companyApplySettings.removeAll(moreCompanyApplySettingList);
        }
        return companyApplySettings;
    }

    @Override
    public CompanyApplyContract queryAbnormalApproval(String companyId) {
        CompanyApplyContract companyApplyContract;
        Map<String, Object> map = iPrivilegeService.queryMoudleMenu(companyId, "approval_configuration", 2);
        Boolean approvalConfiguration = ObjUtils.toBoolean(map.get("approval_configuration"), false);
        if (!approvalConfiguration) {
            companyApplyContract = new CompanyApplyContract(false);
            return companyApplyContract;
        }
        List<CompanyApplyExt> companyApplyExtList = companyApplySettingExtMapper.queryAbnormalApproval(companyId);
        if (companyApplyExtList != null && companyApplyExtList.size() > 0) {
            boolean abnormalApproval = false;
            boolean clxcAbnormal = false;//差旅行程审批流
            boolean lsycAbnormal = false;//临时用车审批流
            boolean clddAbnormal = false;//差旅订单审批流（含超规）
            boolean cgycAbnormal = false;//超规用餐审批流
            boolean ycAbnormal = false;//用餐审批流
            boolean wmAbnormal = false;//外卖审批流
            boolean cgAbnormal = false;//采购审批流
            boolean xnkAbnormal = false;//虚拟卡审批流
            boolean xnkhxAbnormal = false;//虚拟卡核销审批流
            boolean ddhxAbnormal =false; //订单核销 审批流
            boolean paymentAbnormal = false; //对公付款审批流
            boolean mileageAbnormal = false; //里程审批流
            //员工id集合
            List<String> idList = companyApplyExtList.stream().map(companyApply -> companyApply.getItemId()).collect(Collectors.toList());

            //yuan
            List<EmployeeContract> employList =  getQueryEmployeeListInfo.employeeListInfo(idList, companyId);
            if (idList.size() > employList.size()) {
                //有效员工id集合
                List<String> vaildIdList = employList.stream().map(employ -> employ.getId()).collect(Collectors.toList());
                //有问题员工id集合
                List<String> inVaildIdList = idList.stream().filter(item -> !vaildIdList.contains(item)).collect(Collectors.toList());
                for (CompanyApplyExt companyApplyExt : companyApplyExtList) {
                    if (inVaildIdList.contains(companyApplyExt.getItemId())) {
                        abnormalApproval = true;
                        switch (companyApplyExt.getApplyType()) {
                            //差旅
                            case 1:
                                if (companyApplyExt.getCompanySettingType() == 1) {
                                    clxcAbnormal = true;//差旅行程
                                } else {
                                    clddAbnormal = true;//差旅订单
                                }
                                break;
                            //用车
                            case 2:
                                lsycAbnormal = true;
                                break;
                            //采购
                            case 4:
                                cgAbnormal = true;
                                break;
                            //超规用餐
                            case 5:
                                cgycAbnormal = true;
                                break;
                            //用餐(美食)
                            case 11:
                                ycAbnormal = true;
                                break;
                            //外卖
                            case 14:
                                wmAbnormal = true;
                                break;
                            //用餐(美食)
                            case 15:
                                xnkAbnormal = true;
                                break;
                            case 16:
                                xnkhxAbnormal= true;
                                break;
                            //订单核销
                            case 17:
                                ddhxAbnormal=true;
                                break;
                            //对公付款
                            case 18:
                                paymentAbnormal = true;
                                break;
                            //对公付款
                            case 20:
                                mileageAbnormal = true;
                                break;
                            default:
                                continue;
                        }
                    }
                }
            }
            companyApplyContract = new CompanyApplyContract(abnormalApproval, clxcAbnormal, lsycAbnormal, clddAbnormal, cgycAbnormal, ycAbnormal, cgAbnormal, wmAbnormal, xnkAbnormal, xnkhxAbnormal, ddhxAbnormal, paymentAbnormal, mileageAbnormal);
        } else {
            companyApplyContract = new CompanyApplyContract(false);
        }
        return companyApplyContract;
    }

    //是否公司员工
    private boolean isCompanyEmployee(String companyId, String userId) {
        boolean flag = true;
        try {
            EmployeeContract employeeContract = iBaseOrganizationService.getEmployee(userId, companyId);
            flag = isCompanyEmployee(employeeContract);
        } catch (Exception e) {
            logger.error("判断是否为公司用户，调用公共服务查询用户信息出错：" + e.getMessage());
        }
        return flag;
    }

    //是否公司员工
    private boolean isCompanyEmployee(EmployeeContract employeeContract) {
        boolean flag = true;
        if (employeeContract == null) {
            flag = false;
        }
        return flag;
    }

    /**
     * 新增，更新，删除时走此方法推送消息
     * @param applyType 业务类型
     * @param companySettingType 审批类型
     * @param actionType 操作动作
     * @param content 名称
     * @return KafkaCompanyLogMsg 消息体
     */
    private KafkaCompanyLogMsg getKafkaCompanyLogMsg(ApplyType applyType, CompanySettingType companySettingType,
                                                     ActionType actionType, String content) {
        // 差旅行程审批流
        if (applyType == ApplyType.ChaiLv) {
            if (companySettingType == CompanySettingType.TravelApply) {
                if (actionType == ActionType.Create) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.ADD, LogOperateObjectEnum.APPROVAL_TRAVEL, content);
                }
                if (actionType == ActionType.Edit) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.UPDATE, LogOperateObjectEnum.APPROVAL_TRAVEL, content);
                }
                if (actionType == ActionType.Delete) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.DELETE, LogOperateObjectEnum.APPROVAL_TRAVEL, content);
                }
            }
        }
        // 差旅订单审批流
        if (applyType == ApplyType.ChaiLv) {
            if (companySettingType == CompanySettingType.CenterApply) {
                if (actionType == ActionType.Create) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.ADD, LogOperateObjectEnum.APPROVAL_ORDER, content);
                }
                if (actionType == ActionType.Edit) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.UPDATE, LogOperateObjectEnum.APPROVAL_ORDER, content);
                }
                if (actionType == ActionType.Delete) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.DELETE, LogOperateObjectEnum.APPROVAL_ORDER, content);
                }
            }
        }
        // 用车审批流
        if (applyType == ApplyType.Taxi) {
            if (companySettingType == CompanySettingType.TravelApply) {
                if (actionType == ActionType.Create) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.ADD, LogOperateObjectEnum.APPROVAL_TAXI, content);
                }
                if (actionType == ActionType.Edit) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.UPDATE, LogOperateObjectEnum.APPROVAL_TAXI, content);
                }
                if (actionType == ActionType.Delete) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.DELETE, LogOperateObjectEnum.APPROVAL_TAXI, content);
                }
            }
        }
        // 用餐审批流
        if (applyType == ApplyType.Meishi) {
            if (companySettingType == CompanySettingType.DinnerApply) {
                if (actionType == ActionType.Create) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.ADD, LogOperateObjectEnum.APPROVAL_DINNER, content);
                }
                if (actionType == ActionType.Edit) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.UPDATE, LogOperateObjectEnum.APPROVAL_DINNER, content);
                }
                if (actionType == ActionType.Delete) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.DELETE, LogOperateObjectEnum.APPROVAL_DINNER, content);
                }
            }
        }
        // 外卖审批流
        if (applyType == ApplyType.TakeAway) {
            if (companySettingType == CompanySettingType.TakeawayApply) {
                if (actionType == ActionType.Create) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.ADD, LogOperateObjectEnum.APPROVAL_TAKEAWAY, content);
                }
                if (actionType == ActionType.Edit) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.UPDATE, LogOperateObjectEnum.APPROVAL_TAKEAWAY, content);
                }
                if (actionType == ActionType.Delete) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.DELETE, LogOperateObjectEnum.APPROVAL_TAKEAWAY, content);
                }
            }
        }
        // 采购审批流
        if (applyType == ApplyType.Mall) {
            if (companySettingType == CompanySettingType.OrderApply) {
                if (actionType == ActionType.Create) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.ADD, LogOperateObjectEnum.APPROVAL_MALL, content);
                }
                if (actionType == ActionType.Edit) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.UPDATE, LogOperateObjectEnum.APPROVAL_MALL, content);
                }
                if (actionType == ActionType.Delete) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.DELETE, LogOperateObjectEnum.APPROVAL_MALL, content);
                }
            }
        }
        // 分贝通虚拟卡审批流
        if (applyType == ApplyType.BankIndividual) {
            if (companySettingType == CompanySettingType.BankIndividualApply) {
                if (actionType == ActionType.Create) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.ADD, LogOperateObjectEnum.APPROVAL_VIRTUAL, content);
                }
                if (actionType == ActionType.Edit) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.UPDATE, LogOperateObjectEnum.APPROVAL_VIRTUAL, content);
                }
                if (actionType == ActionType.Delete) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.DELETE, LogOperateObjectEnum.APPROVAL_VIRTUAL, content);
                }
            }
        }
        // 分贝通虚拟卡核销申请审批流
        if (applyType == ApplyType.VirtualCardWriteOff) {
            if (companySettingType == CompanySettingType.VirtualCardWriteOffApply) {
                if (actionType == ActionType.Create) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.ADD, LogOperateObjectEnum.APPROVAL_VIRTUAL_APPLY, content);
                }
                if (actionType == ActionType.Edit) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.UPDATE, LogOperateObjectEnum.APPROVAL_VIRTUAL_APPLY, content);
                }
                if (actionType == ActionType.Delete) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.DELETE, LogOperateObjectEnum.APPROVAL_VIRTUAL_APPLY, content);
                }
            }
        }
        // 商务消费核销单审批流
        if (applyType == ApplyType.BusinessOrderWriteOff) {
            if (companySettingType == CompanySettingType.BusinessOrderWriteOffApply) {
                if (actionType == ActionType.Create) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.ADD, LogOperateObjectEnum.APPROVAL_CONSUME, content);
                }
                if (actionType == ActionType.Edit) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.UPDATE, LogOperateObjectEnum.APPROVAL_CONSUME, content);
                }
                if (actionType == ActionType.Delete) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.DELETE, LogOperateObjectEnum.APPROVAL_CONSUME, content);
                }
            }
        }
        // 付款申请单审批流
        if (applyType == ApplyType.Payment) {
            if (companySettingType == CompanySettingType.PaymentApply) {
                if (actionType == ActionType.Create) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.ADD, LogOperateObjectEnum.APPROVAL_APPLY, content);
                }
                if (actionType == ActionType.Edit) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.UPDATE, LogOperateObjectEnum.APPROVAL_APPLY, content);
                }
                if (actionType == ActionType.Delete) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.DELETE, LogOperateObjectEnum.APPROVAL_APPLY, content);
                }
            }
        }
        // 里程审批申请单审批流
        if (applyType == ApplyType.Mileage) {
            if (companySettingType == CompanySettingType.MileageApply) {
                if (actionType == ActionType.Create) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.ADD, LogOperateObjectEnum.APPROVAL_MILEAGE, content);
                }
                if (actionType == ActionType.Edit) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.UPDATE, LogOperateObjectEnum.APPROVAL_MILEAGE, content);
                }
                if (actionType == ActionType.Delete) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.DELETE, LogOperateObjectEnum.APPROVAL_MILEAGE, content);
                }
            }
        }

        return new KafkaCompanyLogMsg();
    }

    /**
     * 停用，启用时走此方法推送消息
     * @param applyType 业务类型
     * @param companySettingType 审批类型
     * @param costCenterStateEnum 操作动作
     * @param content 名称
     * @return KafkaCompanyLogMsg 消息体
     */
    private KafkaCompanyLogMsg getKafkaCompanyLogMsg(ApplyType applyType, CompanySettingType companySettingType,
                                                     CostCenterStateEnum costCenterStateEnum, String content) {
        // 差旅行程审批流
        if (applyType == ApplyType.ChaiLv) {
            if (companySettingType == CompanySettingType.TravelApply) {
                if (costCenterStateEnum == CostCenterStateEnum.Start) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.ACTIVE, LogOperateObjectEnum.APPROVAL_TRAVEL, content);
                }
                if (costCenterStateEnum == CostCenterStateEnum.Stop) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.UNACTIVE, LogOperateObjectEnum.APPROVAL_TRAVEL, content);
                }
            }
        }
        // 差旅订单审批流
        if (applyType == ApplyType.ChaiLv) {
            if (companySettingType == CompanySettingType.CenterApply) {
                if (costCenterStateEnum == CostCenterStateEnum.Start) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.ACTIVE, LogOperateObjectEnum.APPROVAL_ORDER, content);
                }
                if (costCenterStateEnum == CostCenterStateEnum.Stop) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.UNACTIVE, LogOperateObjectEnum.APPROVAL_ORDER, content);
                }
            }
        }
        // 用车审批流
        if (applyType == ApplyType.Taxi) {
            if (companySettingType == CompanySettingType.TravelApply) {
                if (costCenterStateEnum == CostCenterStateEnum.Start) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.ACTIVE, LogOperateObjectEnum.APPROVAL_TAXI, content);
                }
                if (costCenterStateEnum == CostCenterStateEnum.Stop) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.UNACTIVE, LogOperateObjectEnum.APPROVAL_TAXI, content);
                }
            }
        }
        // 用餐审批流
        if (applyType == ApplyType.Meishi) {
            if (companySettingType == CompanySettingType.DinnerApply) {
                if (costCenterStateEnum == CostCenterStateEnum.Start) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.ACTIVE, LogOperateObjectEnum.APPROVAL_DINNER, content);
                }
                if (costCenterStateEnum == CostCenterStateEnum.Stop) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.UNACTIVE, LogOperateObjectEnum.APPROVAL_DINNER, content);
                }
            }
        }
        // 外卖审批流
        if (applyType == ApplyType.TakeAway) {
            if (companySettingType == CompanySettingType.TakeawayApply) {
                if (costCenterStateEnum == CostCenterStateEnum.Start) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.ACTIVE, LogOperateObjectEnum.APPROVAL_TAKEAWAY, content);
                }
                if (costCenterStateEnum == CostCenterStateEnum.Stop) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.UNACTIVE, LogOperateObjectEnum.APPROVAL_TAKEAWAY, content);
                }
            }
        }
        // 采购审批流
        if (applyType == ApplyType.Mall) {
            if (companySettingType == CompanySettingType.OrderApply) {
                if (costCenterStateEnum == CostCenterStateEnum.Start) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.ACTIVE, LogOperateObjectEnum.APPROVAL_MALL, content);
                }
                if (costCenterStateEnum == CostCenterStateEnum.Stop) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.UNACTIVE, LogOperateObjectEnum.APPROVAL_MALL, content);
                }
            }
        }
        // 分贝通虚拟卡审批流
        if (applyType == ApplyType.BankIndividual) {
            if (companySettingType == CompanySettingType.BankIndividualApply) {
                if (costCenterStateEnum == CostCenterStateEnum.Start) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.ACTIVE, LogOperateObjectEnum.APPROVAL_VIRTUAL, content);
                }
                if (costCenterStateEnum == CostCenterStateEnum.Stop) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.UNACTIVE, LogOperateObjectEnum.APPROVAL_VIRTUAL, content);
                }
            }
        }
        // 分贝通虚拟卡核销申请审批流
        if (applyType == ApplyType.VirtualCardWriteOff) {
            if (companySettingType == CompanySettingType.VirtualCardWriteOffApply) {
                if (costCenterStateEnum == CostCenterStateEnum.Start) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.ACTIVE, LogOperateObjectEnum.APPROVAL_VIRTUAL_APPLY, content);
                }
                if (costCenterStateEnum == CostCenterStateEnum.Stop) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.UNACTIVE, LogOperateObjectEnum.APPROVAL_VIRTUAL_APPLY, content);
                }
            }
        }
        // 商务消费核销单审批流
        if (applyType == ApplyType.BusinessOrderWriteOff) {
            if (companySettingType == CompanySettingType.BusinessOrderWriteOffApply) {
                if (costCenterStateEnum == CostCenterStateEnum.Start) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.ACTIVE, LogOperateObjectEnum.APPROVAL_CONSUME, content);
                }
                if (costCenterStateEnum == CostCenterStateEnum.Stop) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.UNACTIVE, LogOperateObjectEnum.APPROVAL_CONSUME, content);
                }
            }
        }
        // 付款申请单审批流
        if (applyType == ApplyType.Payment) {
            if (companySettingType == CompanySettingType.PaymentApply) {
                if (costCenterStateEnum == CostCenterStateEnum.Start) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.ACTIVE, LogOperateObjectEnum.APPROVAL_APPLY, content);
                }
                if (costCenterStateEnum == CostCenterStateEnum.Stop) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.UNACTIVE, LogOperateObjectEnum.APPROVAL_APPLY, content);
                }
            }
        }
        // 里程补贴申请单审批流
        if (applyType == ApplyType.Mileage) {
            if (companySettingType == CompanySettingType.MileageApply) {
                if (costCenterStateEnum == CostCenterStateEnum.Start) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.ACTIVE, LogOperateObjectEnum.APPROVAL_MILEAGE, content);
                }
                if (costCenterStateEnum == CostCenterStateEnum.Stop) {
                    return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Approval_Process, LogOperateActionEnum.UNACTIVE, LogOperateObjectEnum.APPROVAL_MILEAGE, content);
                }
            }
        }

        return new KafkaCompanyLogMsg();
    }

    @Override
    public void initAllCompanyApplySetting(String companyId){
        //初始化差旅行程审批流设置
        initCompanyApplySetting(ApplyType.ChaiLv.getValue(), SaasFlowConstant.SETTING_TYPE_CENTER, CompanyApplyType.Elastic.getValue(), SettingType.Department.getValue(), companyId);
        initCompanyApplySetting(ApplyType.ChaiLv.getValue(), SaasFlowConstant.SETTING_TYPE_CENTER, CompanyApplyType.Flow.getValue(), SettingType.Project.getValue(), companyId);
        //初始化用车行程审批流设置
        initCompanyApplySetting(ApplyType.Taxi.getValue(), SaasFlowConstant.SETTING_TYPE_CENTER, CompanyApplyType.Elastic.getValue(), SettingType.Department.getValue(), companyId);
        initCompanyApplySetting(ApplyType.Taxi.getValue(), SaasFlowConstant.SETTING_TYPE_CENTER, CompanyApplyType.Flow.getValue(), SettingType.Project.getValue(), companyId);
        //初始差旅费用审批审批流设置
        initCompanyApplySetting(ApplyType.ChaiLv.getValue(), SaasFlowConstant.SETTING_TYPE_ROUTE, CompanyApplyType.Elastic.getValue(), SettingType.Department.getValue(), companyId);
        initCompanyApplySetting(ApplyType.ChaiLv.getValue(), SaasFlowConstant.SETTING_TYPE_ROUTE, CompanyApplyType.Elastic.getValue(), SettingType.Project.getValue(), companyId);
        //初始超规用餐审批审批流设置
        initCompanyApplySetting(ApplyType.Dinner.getValue(), SaasFlowConstant.SETTING_TYPE_ROUTE, CompanyApplyType.Flow.getValue(), SettingType.Department.getValue(), companyId);
        initCompanyApplySetting(ApplyType.Dinner.getValue(), SaasFlowConstant.SETTING_TYPE_ROUTE, CompanyApplyType.Flow.getValue(), SettingType.Project.getValue(), companyId);
        //初始用餐审批审批流设置
        initCompanyApplySetting(ApplyType.Meishi.getValue(), SaasFlowConstant.SETTING_TYPE_DINNER, CompanyApplyType.Flow.getValue(), SettingType.Department.getValue(), companyId);
        initCompanyApplySetting(ApplyType.Meishi.getValue(), SaasFlowConstant.SETTING_TYPE_DINNER, CompanyApplyType.Flow.getValue(), SettingType.Project.getValue(), companyId);
        //初始采购订单审批审批流设置
        initCompanyApplySetting(ApplyType.Mall.getValue(), SaasFlowConstant.SETTING_TYPE_ORDER, CompanyApplyType.Elastic.getValue(), SettingType.Department.getValue(), companyId);
        initCompanyApplySetting(ApplyType.Mall.getValue(), SaasFlowConstant.SETTING_TYPE_ORDER, CompanyApplyType.Flow.getValue(), SettingType.Project.getValue(), companyId);
        //初始外卖审批审批流设置
        initCompanyApplySetting(ApplyType.TakeAway.getValue(), SaasFlowConstant.SETTING_TYPE_TAKEAWAY, CompanyApplyType.Flow.getValue(), SettingType.Department.getValue(), companyId);
        initCompanyApplySetting(ApplyType.TakeAway.getValue(), SaasFlowConstant.SETTING_TYPE_TAKEAWAY, CompanyApplyType.Flow.getValue(), SettingType.Project.getValue(), companyId);

        //初始虚拟卡审批流设置
        initCompanyApplySetting(ApplyType.BankIndividual.getValue(), SaasFlowConstant.SETTING_TYPE_BANK_INDIVIDUAL, CompanyApplyType.Flow.getValue(), SettingType.Department.getValue(), companyId);
        initCompanyApplySetting(ApplyType.BankIndividual.getValue(), SaasFlowConstant.SETTING_TYPE_BANK_INDIVIDUAL, CompanyApplyType.Flow.getValue(), SettingType.Project.getValue(), companyId);

        //初始虚拟卡核销审批流设置
        initCompanyApplySetting(ApplyType.VirtualCardWriteOff.getValue(), SaasFlowConstant.SETTING_TYPE_VIRTUAL_CARD, CompanyApplyType.Elastic.getValue(), SettingType.Department.getValue(), companyId);
        initCompanyApplySetting(ApplyType.VirtualCardWriteOff.getValue(), SaasFlowConstant.SETTING_TYPE_VIRTUAL_CARD, CompanyApplyType.Flow.getValue(), SettingType.Project.getValue(), companyId);

        //初始商务订单核销审批流设置
        initCompanyApplySetting(ApplyType.BusinessOrderWriteOff.getValue(), SaasFlowConstant.SETTING_TYPE_BUSINESS_ORDER_WRITEOFF, CompanyApplyType.Elastic.getValue(), SettingType.Department.getValue(), companyId);
        initCompanyApplySetting(ApplyType.BusinessOrderWriteOff.getValue(), SaasFlowConstant.SETTING_TYPE_BUSINESS_ORDER_WRITEOFF, CompanyApplyType.Flow.getValue(), SettingType.Project.getValue(), companyId);

        //初始化对公付款审批流设置
        initCompanyApplySetting(ApplyType.Payment.getValue(), SaasFlowConstant.SETTING_TYPE_PAYMENT, CompanyApplyType.Elastic.getValue(), SettingType.Department.getValue(), companyId);
        initCompanyApplySetting(ApplyType.Payment.getValue(), SaasFlowConstant.SETTING_TYPE_PAYMENT, CompanyApplyType.Elastic.getValue(), SettingType.Project.getValue(), companyId);

        //初始化里程付款审批流设置
        initCompanyApplySetting(ApplyType.Mileage.getValue(), SaasFlowConstant.SETTING_TYPE_MILEAGE, CompanyApplyType.Elastic.getValue(), SettingType.Department.getValue(), companyId);
        initCompanyApplySetting(ApplyType.Mileage.getValue(), SaasFlowConstant.SETTING_TYPE_MILEAGE, CompanyApplyType.Elastic.getValue(), SettingType.Project.getValue(), companyId);
    }

    private void initCompanyApplySetting(int applyType, int companySettingType, int companyApplyType, int costAttributionCategory, String companyId) {
        logger.info("开始处理默认项目审批流和默认弹性审批流定时任务......");
        CompanyApplySettingExample companyApplySettingExampleInfo = new CompanyApplySettingExample();
        companyApplySettingExampleInfo.createCriteria().andCompanyIdEqualTo(companyId).andApplyStatusEqualTo(SaasFlowConstant.IS_NORMAL_STATUS)
                .andCompanySettingTypeEqualTo(companySettingType).andCanDeleteEqualTo(SaasFlowConstant.CAN_DELETE_STATUS).andApplyTypeEqualTo(applyType).andCostAttributionCategoryEqualTo(costAttributionCategory);
        List<CompanyApplySetting> companyApplySettingList = companyApplySettingMapper.selectByExample(companyApplySettingExampleInfo);
        if (companyApplySettingList != null && companyApplySettingList.size() > 0) {
            return;
        }
        Date now = new Date();
        String companyApplySettingId = IDTool.CreateUniqueID();
        CompanyApplySetting companyApplySetting = new CompanyApplySetting();
        companyApplySetting.setId(companyApplySettingId);
        companyApplySetting.setCompanyId(companyId);
        //差旅
        companyApplySetting.setApplyType(applyType);
        companyApplySetting.setCreateTime(now);
        companyApplySetting.setUpdateTime(now);
        companyApplySetting.setOperatorId("");
        if (costAttributionCategory == SettingType.Department.getValue()) {
            companyApplySetting.setFlowName("部门默认审批流");
            companyApplySetting.setCostCenterStatus(CostCenterStateEnum.Start.getKey());
        } else if (costAttributionCategory == SettingType.Project.getValue()) {
            companyApplySetting.setFlowName("项目默认审批流");
            //项目默认审批流为停用状态
            companyApplySetting.setCostCenterStatus(CostCenterStateEnum.Stop.getKey());
        }
        //删除状态1.正常 2.删除
        companyApplySetting.setApplyStatus(1);
        //1.不是默认审批流 2.默认审批流
        companyApplySetting.setCanDelete(2);
        //1.行程审批  2.事中审批 3.订单审批
        companyApplySetting.setCompanySettingType(companySettingType);
        //1.部门 2.项目
        companyApplySetting.setCostAttributionCategory(costAttributionCategory);
        companyApplySetting.setRoleApproveType(CompanyRoleApproveType.All.getValue());
        companyApplySettingMapper.insert(companyApplySetting);
        String applyFlowId = IDTool.CreateUniqueID();
        ApplyFlow applyFlow = new ApplyFlow();
        applyFlow.setId(applyFlowId);
        applyFlow.setCompanyId(companyId);
        applyFlow.setCreateTime(now);
        applyFlow.setOperatorId("");
        applyFlow.setCompanyApplySettingId(companyApplySettingId);
        //1.弹性审批流，2.固定审批流，3.分条件审批流
        applyFlow.setCompanyApplyType(companyApplyType);
        //1.仅全部同意后通知 2.仅发起时通知 3.发起时和全部同意时都通知
        applyFlow.setCcNoticeType(2);
        Integer isExceedBuyFlow = 0;
        if (applyType == ApplyType.Dinner.getValue()) {
            isExceedBuyFlow = 1;
        }
        applyFlow.setIsExceedBuyFlow(isExceedBuyFlow);
        applyFlowMapper.insert(applyFlow);
        if (companyApplyType == CompanyApplyType.Flow.getValue()) {
            ApplyFlowItem applyFlowItem = new ApplyFlowItem();
            applyFlowItem.setId(IDTool.CreateUniqueID());
            applyFlowItem.setApplyFlowId(applyFlowId);
            applyFlowItem.setSort(1);
            if (applyType == ApplyType.BankIndividual.getValue()) {
                applyFlowItem.setItemType(4);
            } else {
                applyFlowItem.setItemType(16);
                applyFlowItem.setItemId("1");
            }
            applyFlowItem.setCreateTime(now);
            applyFlowItemMapper.insertSelective(applyFlowItem);
        }
        logger.info("结束处理默认项目审批流和默认弹性审批流定时任务......");
    }




    /**
     * 分批次查询公司信息
     *
     * @return
     */
    private List<com.fenbeitong.usercenter.api.model.po.company.Company> sloveCompanyListInfo() {
        Long companyNum = irCompanyService.countCompanies();
        List<com.fenbeitong.usercenter.api.model.po.company.Company> companyList = Lists.newArrayList();
        Long insertNum = 500l;
        Long totalNum = companyNum / insertNum;
        for (int i = 0; i <= totalNum; i++) {
            List<com.fenbeitong.usercenter.api.model.po.company.Company> companies = irCompanyService.listAllCompanies(i, 500);
            companyList.addAll(companies);
        }
        return companyList;
    }


}
