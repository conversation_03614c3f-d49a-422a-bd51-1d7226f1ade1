package com.fenbeitong.saas.core.contract.reason;

/**
 * <AUTHOR>
 * @date 2020/5/11
 */
public class BusinessOrderWriteOffBizConfig {

    /**
     * 是否需要回票 1:开启 0:关闭
     */
    private Integer ticket_collect;

    public Integer getTicket_collect() {
        return ticket_collect;
    }

    public void setTicket_collect(Integer ticket_collect) {
        this.ticket_collect = ticket_collect;
    }
}
