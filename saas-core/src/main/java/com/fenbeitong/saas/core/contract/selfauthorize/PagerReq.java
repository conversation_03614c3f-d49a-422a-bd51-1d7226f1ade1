package com.fenbeitong.saas.core.contract.selfauthorize;

import lombok.Data;

/**
 * @ClassName PagerReq
 * @Description 分页查询条件
 * <AUTHOR>
 * @Date 2022/9/23 11:42
 * @Version 1.0
 */
@Data
public class PagerReq {
	private int pageSize = 10; // 每页记录数
	private int startNo = 0; // 当前页开始记录条目, 自动根据pageNo和pageSize 计算
	private int pageNo = 1; // 当前页

	public int getPageSize() {
		return pageSize;
	}

	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
		this.startNo = (this.pageNo - 1) * this.pageSize;
	}

	public int getPageNo() {
		return pageNo;
	}

	public void setPageNo(int pageNo) {
		this.pageNo = pageNo;
		this.startNo = (this.pageNo - 1) * this.pageSize;
	}
}
