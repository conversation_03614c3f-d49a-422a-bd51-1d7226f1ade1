package com.fenbeitong.saas.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.bank.api.constant.enums.BankCheckInfoOperationType;
import com.fenbeitong.bank.api.model.BankApplyCreditReqDTO;
import com.fenbeitong.bank.api.model.BankCheckAllInfoReqDTO;
import com.fenbeitong.bank.api.model.dto.UnbindBankCardPettyIdReqDTO;
import com.fenbeitong.bank.api.service.IBankHuPoFBTService;
import com.fenbeitong.fenbeipay.api.constant.enums.bank.BankCardStatus;
import com.fenbeitong.fenbeipay.api.model.dto.bank.resp.BankPettyDetailRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.bank.resp.BankSearchCardDetailRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.AccountSubFindReqRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountSubFindRespRPCDTO;
import com.fenbeitong.fenbeipay.api.service.bank.IBankCardSearchService;
import com.fenbeitong.fenbeipay.api.service.bank.IBankPettySearchService;
import com.fenbeitong.fenbeipay.api.service.na.IAccountSubService;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.constant.FundAccountSubType;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.saas.entity.CostAttribution;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import com.fenbeitong.saas.api.model.dto.reason.ReasonConfig;
import com.fenbeitong.saas.card.api.card.api.IBankCardRpcService;
import com.fenbeitong.saas.card.api.card.dto.res.BankSearchCardDetailRespRpcDTO;
import com.fenbeitong.saas.card.api.petty.api.search.IBankPettySearchRpcService;
import com.fenbeitong.saas.card.api.petty.dto.res.pay.PettyDetailRespDTO;
import com.fenbeitong.saas.core.common.base.SaasException;
import com.fenbeitong.saas.core.common.constant.GlobalResponseCode;
import com.fenbeitong.saas.core.common.constant.SaasBudgetConstant;
import com.fenbeitong.saas.core.common.constant.SaasFlowConstant;
import com.fenbeitong.saas.core.common.constant.SaasMessageConstant;
import com.fenbeitong.saas.core.contract.apply.*;
import com.fenbeitong.saas.core.contract.applyflow.ApplyFlowApplicateResponseContract;
import com.fenbeitong.saas.core.contract.applyflow.CompanyApplyFlowItemSetV2RequestContract;
import com.fenbeitong.saas.core.contract.applyflow.CompanyApplyFlowSetV2RequestContract;
import com.fenbeitong.saas.core.contract.applyflow.KvContract;
import com.fenbeitong.saas.core.contract.common.IdNameContract;
import com.fenbeitong.saas.core.contract.common.PushContract;
import com.fenbeitong.saas.core.contract.message.MessageSaveContract;
import com.fenbeitong.saas.core.contract.message.inner.ApplyInfo;
import com.fenbeitong.saas.core.contract.messagesettings.ApplySetupContract;
import com.fenbeitong.saas.core.contract.user.EmployeeNameAndDeptContract;
import com.fenbeitong.saas.core.dao.common.adapter.ApplyAdapterMapper;
import com.fenbeitong.saas.core.language.CoreLanguage;
import com.fenbeitong.saas.core.language.LanguageUtils;
import com.fenbeitong.saas.core.language.MessageLanguageEnum;
import com.fenbeitong.saas.core.model.enums.ApplyFlowUserItemStatus;
import com.fenbeitong.saas.core.model.enums.CompanyApplyType;
import com.fenbeitong.saas.core.model.enums.CostAttributionCategory;
import com.fenbeitong.saas.core.model.enums.OrderCategory;
import com.fenbeitong.saas.core.model.enums.apply.*;
import com.fenbeitong.saas.core.model.enums.budget.BudgetType;
import com.fenbeitong.saas.core.model.enums.message.MessageType;
import com.fenbeitong.saas.core.model.enums.message.SenderType;
import com.fenbeitong.saas.core.model.enums.reason.ReasonType;
import com.fenbeitong.saas.core.model.enums.user.UserRole;
import com.fenbeitong.saas.core.model.saas.*;
import com.fenbeitong.saas.core.service.*;
import com.fenbeitong.saas.core.utils.cache.RedisService;
import com.fenbeitong.saas.core.utils.flow.FlowCheckUtil;
import com.fenbeitong.saas.core.utils.tools.*;
import com.fenbeitong.saasplus.api.model.dto.budget.BizBudgetSettingAndUseContract;
import com.fenbeitong.saasplus.api.model.dto.budget.BudgetCondItem;
import com.fenbeitong.saasplus.api.model.enums.messageSetup.ItemCodeType;
import com.fenbeitong.usercenter.api.model.dto.common.CommonAuthDto;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyRuleDTO;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.fenbeitong.usercenter.api.model.dto.orgunit.OrgUnitResult;
import com.fenbeitong.usercenter.api.model.enums.employee.EmployeeStatus;
import com.fenbeitong.usercenter.api.service.common.ICommonService;
import com.fenbeitong.usercenter.api.service.orgunit.IOrgUnitService;
import com.fenbeitong.usercenter.api.service.privilege.IPrivilegeService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.StrUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Transactional(transactionManager = "dynamic", readOnly = false, rollbackFor = Exception.class)
public class ApplyPettyMoneyServiceImpl implements IApplyPettyMoneyService {

    private static final Logger logger = LoggerFactory.getLogger(ApplyPettyMoneyServiceImpl.class);

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyOrderMapper applyOrderMapper;

    @Autowired
    private IApplyFlowService applyFlowService;

    @Autowired
    IUserService userService;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyApproverMapMapper applyApproverMapMapper;

    @Autowired
    IPushService pushService;

    @Autowired
    IMessageService messageService;

    @Autowired
    private IApplyFlowV2Service applyFlowV2Service;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyOrderLogExtMapper applyOrderLogExtMapper;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyOrderExtMapper applyOrderExtMapper;

    @Autowired
    private ApplyAdapterMapper applyAdapterMapper;

    @Autowired
    private ICustomRoleService customRoleService;

    @Autowired
    private IBaseOrganizationService iBaseOrganizationService;

    @Autowired
    private RedisService redisService;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyOrderLogMapper applyOrderLogMapper;

    @Autowired
    ICommonService iCommonService;

    @Autowired
    IBankCardSearchService iBankCardSearchService;

    @Autowired
    IBankCardRpcService iBankCardRpcService;

    @Autowired
    private IAccountSubService iAccountSubService;

    @Autowired
    com.fenbeitong.saasplus.api.service.budget.IBudgetService iBudgetService;

    @Autowired
    IPrivilegeService iPrivilegeService;

    @Autowired
    IBankHuPoFBTService iBankHuPoFBTService;

    @Autowired
    IOrgUnitService iOrgUnitService;

    @Autowired
    private ICustomReasonService customReasonService;

    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyTripInfoMapper applyTripInfoMapper;

    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyTripInfoExtMapper applyTripInfoExtMapper;

    @Autowired
    IMessageSetupService messageSetupService;

    @Autowired
    com.fenbeitong.usercenter.api.service.company.ICompanyRuleService iCompanyRuleService;

    @Autowired
    private IBankPettySearchService iBankPettySearchService;

    @Autowired
    private IBankPettySearchRpcService iBankPettySearchRpcService;

    @Autowired
    private com.fenbeitong.saasplus.api.service.messageSetup.IMessageSetupService iMessageSetupService;

    @Autowired
    private IApplyV2Service applyV2Service;

    public void setMessageService(IMessageService messageService) {
        this.messageService = messageService;
    }


    /**
     * 创建申请单
     *
     * @param applyContract
     * @return
     */
    @Override
    //@Transactional(value = "saas")
    public GlobalResponseCode create(String token, ApplyBankIndividualContract applyContract, String userId, String companyId, String ip, String applyId, String clientHeadVersion) throws SaasException {
        FlowCheckUtil.check(companyId, clientHeadVersion);
        String bankName = BankNameEnum.XWBANK.getCode();
        if (applyContract != null && applyContract.getApply() != null && StringUtils.isNotBlank(applyContract.getApply().getBank_name())) {
            bankName = applyContract.getApply().getBank_name();
        }
        //备用金校验
        try {
            BankCheckAllInfoReqDTO bankCheckAllInfoReqDTO = new BankCheckAllInfoReqDTO();
            bankCheckAllInfoReqDTO.setBankName(bankName);
            bankCheckAllInfoReqDTO.setCategoryType(OrderCategory.BankIndividual.getKey());
            bankCheckAllInfoReqDTO.setCompanyId(companyId);
            bankCheckAllInfoReqDTO.setEmployeeId(userId);
            bankCheckAllInfoReqDTO.setOperationSrc(BankCheckInfoOperationType.SAAS.getCode());
            iBankHuPoFBTService.checkCardAllInfo(bankCheckAllInfoReqDTO);
            //如果上步检查通过，这里会把员工卡和备用金的绑定关系清除掉(余额为0，申请额度成功后，退款的话会追加到本次额度上的问题)，
            //解绑成功后，会直接退回到企业帐户,新版预算修改需求，需要把这些数据保存起来，以后进行预算退还。
            UnbindBankCardPettyIdReqDTO unbindBankCardPetty=new UnbindBankCardPettyIdReqDTO();
            unbindBankCardPetty.setCompanyId(companyId);
            unbindBankCardPetty.setEmployeeId(userId);
            unbindBankCardPetty.setBankName(bankName);
            iBankHuPoFBTService.unBindBankCardPettyId(unbindBankCardPetty);
        } catch (FinhubException ex) {
            throw new SaasException(ex.getCode(), ex.getMessage(), ex.getType(), ex.getTitle());
        }
        Integer logId = null;
        //检查数据信息
        GlobalResponseCode code = checkApplyData(applyContract, userId, clientHeadVersion, companyId);
        if (code != GlobalResponseCode.Success) {
            return code;
        }
        ApplyOrderV2Contract applyOrderContract = applyContract.getApply();
        applyOrderContract.setEmployee_id(userId);
        applyOrderContract.setCompany_id(companyId);
        ApplyType applyType = ApplyType.valueOf(applyOrderContract.getType().intValue());
        CompanyApplyType companyApplyType = CompanyApplyType.valueOf(applyOrderContract.getFlow_type());
        if (companyApplyType == CompanyApplyType.Unknown) {
            companyApplyType = CompanyApplyType.Elastic;
        }
        applyOrderContract.setFlow_type(companyApplyType.getValue());

        //如果是提交审批保存审批流设置和审批单日志记录
        String approverId = applyOrderContract.getApprover_id();
        CompanyApplyFlowSetV2RequestContract flowRequest = applyOrderContract.getFlow();
        if (flowRequest == null) {
            flowRequest = new CompanyApplyFlowSetV2RequestContract();
        }
        flowRequest.setCompany_apply_type(companyApplyType.getValue());
        flowRequest.setApply_type(applyType.getValue());
        flowRequest.setBudget(applyOrderContract.getBudget());
        ApplyFlowApplicateResponseContract flowResponse = null;
        //判断审批流的类型
        String costAttributionId = null;
        if (applyOrderContract.getCost_attribution_category() != null) {
            if (StringUtils.isBlank(applyOrderContract.getCost_attribution_name()) || StringUtils.isBlank(applyOrderContract.getCost_attribution_id())) {
                return GlobalResponseCode.CostAttributionIdIsNull;
            }
        }
        Integer category = applyOrderContract.getCost_attribution_category();
        costAttributionId = applyOrderContract.getCost_attribution_id();
        //处理超规类型
        Integer bussinessType = applyOrderContract.getType();
        //查询应用审批流
        flowResponse = applyFlowV2Service.applicateFlow(SaasFlowConstant.SETTING_TYPE_BANK_INDIVIDUAL, flowRequest, applyId, userId, companyId, ip, category, costAttributionId, ExceedBuyType.UnSupernormal.getValue(), bussinessType, applyId);
        if (flowResponse != null && !StringTool.isNullOrEmpty(flowResponse.getApprover())) {
            approverId = flowResponse.getApprover();
        }
        //设置当前审批人
        logId = flowResponse.getNextLogId();
        if (StringUtils.isNotBlank(approverId)) {
            applyOrderContract.setApprover_id(approverId);
        }
        applyOrderContract.setLog_id(ObjUtils.toLong(flowResponse.getNextLogId()));
        if (companyApplyType == CompanyApplyType.Elastic) {
            if (approverId != null && approverId.length() > 0) {
                //检测审批人是否属于当前这个公司
                Boolean isApproverInCompany = userService.isUserInCompany(approverId, companyId);
                if (!isApproverInCompany) {
                    return GlobalResponseCode.ApplyApproverNotInCompany;
                }
                Date now = new Date();
                ApplyOrderLog log = new ApplyOrderLog();
                log.setApplyOrderId(applyId);
                log.setRootApplyOrderId(applyId);
                log.setCreateTime(now);
                log.setIp(ip);
                log.setSponsorId(userId);
                log.setReceiverId(approverId);
                log.setCheckReason(null);
                log.setAction(ApplyLogAction.Submit.getValue());
                log.setSort(100);
                applyOrderLogMapper.insertSelective(log);

                ApplyOrderLog logApprove = new ApplyOrderLog();
                logApprove.setApplyOrderId(applyId);
                logApprove.setRootApplyOrderId(applyId);
                logApprove.setIp(ip);
                logApprove.setSponsorId(approverId);
                logApprove.setReceiverId("");
                logApprove.setCheckReason(null);
                logApprove.setAction(ApplyLogAction.Approval.getValue());
                logApprove.setSort(200);
                applyOrderLogMapper.insertSelective(logApprove);
                logId = logApprove.getId();
                applyOrderContract.setLog_id(ObjUtils.toLong(logId));
            }
        }
        //整理数据
        clearApplyData(applyContract);
        //待审核装填
        ApplyStatus applyState = ApplyStatus.PendingAudit;
        ApplyLogAction action = ApplyLogAction.Submit;
        Date now = new Date();

        ApplyOrder apply = applyOrderContract.ToModel();
        if (CollectionUtils.isNotEmpty(applyOrderContract.getExceed_buy_desc_list())) {
            apply.setExceedBuyDesc(String.join(";", applyOrderContract.getExceed_buy_desc_list()));
        }
        if (applyOrderContract.getCost_attribution_category() != null) {
            if (StringUtils.isBlank(applyOrderContract.getCost_attribution_name()) || StringUtils.isBlank(applyOrderContract.getCost_attribution_id())) {
                return GlobalResponseCode.CostAttributionIdIsNull;
            }
            apply.setCostAttributionId(applyOrderContract.getCost_attribution_id());
            apply.setCostAttributionName(applyOrderContract.getCost_attribution_name());
            apply.setCostAttributionCategory(applyOrderContract.getCost_attribution_category());
        }
        EmployeeContract employee = iBaseOrganizationService.getEmployee(userId, companyId);
        if (employee != null) {
            apply.setApplicantName(employee.getName());
        }
        apply.setUpdateTime(now);
        apply.setCreateTime(now);
        //创建申请单
        apply.setId(applyId);
        apply.setRootApplyOrderId(applyId);
        applyOrderContract.setId(applyId);
        insertSnapContent(apply, applyOrderContract);
        applyOrderMapper.insertSelective(apply);
        //判断审批单是否是待审核状态
        List<ApplyOrderCopyTo> applyOrderCopyTos = applyAdapterMapper.getApplyOrderCopyToExtMapper(companyId).queryApplyOrderCCByApplyOrderId(applyId);
        if (!CollectionUtils.isEmpty(applyOrderCopyTos)) {
            applyAdapterMapper.getApplyOrderCopyToExtMapper(companyId).deleteCCByApplyOrderId(applyId);
        }
        //保存行程信息
        insertTripContractList(applyContract, applyId, now);
        //获取抄送人数据
        CompanyApplyFlowSetV2RequestContract flow = applyOrderContract.getFlow();
        if (flow != null) {
            List<CompanyApplyFlowItemSetV2RequestContract> cc_list = flow.getCc_list();
            if (!CollectionUtils.isEmpty(cc_list)) {
                if (cc_list.size() > 5) {
                    throw new SaasException(GlobalResponseCode.FlowItemCCTooMuch);
                }
                int sort = 1;
                for (CompanyApplyFlowItemSetV2RequestContract ccFlowItem : cc_list) {
                    //保存抄送人
                    ApplyOrderCopyTo applyOrderCopyTo = new ApplyOrderCopyTo();
                    applyOrderCopyTo.setId(IDTool.CreateUniqueID());
                    applyOrderCopyTo.setApplyOrderId(applyId);
                    applyOrderCopyTo.setCreateTime(new Date());
                    applyOrderCopyTo.setItemId(ccFlowItem.getItem_id());
                    applyOrderCopyTo.setItemType(ccFlowItem.getItem_type());
                    applyOrderCopyTo.setSort(sort++);
                    applyOrderCopyTo.setRead(false);
                    if (ccFlowItem.getUser() == null || StringUtils.isBlank(ccFlowItem.getUser().getUser_id())) {
                        throw new SaasException(GlobalResponseCode.ApplyFlowItemNeedSetCCUser);
                    }
                    if (ccFlowItem.getUser() != null) {
                        applyOrderCopyTo.setUserId(ccFlowItem.getUser().getUser_id());
                    }
                    //待确认
                    applyOrderCopyTo.setIsDelete(ccFlowItem.getIs_delete());//1:不能删除 2:可以删除
                    applyAdapterMapper.getApplyOrderCopyToMapper(companyId).insert(applyOrderCopyTo);
                }
            }

        }
        if (applyState == ApplyStatus.PendingAudit) {
            insertApproverMap(applyId, applyOrderContract.getApprover_id(), now);
        }
        if (applyState == ApplyStatus.PendingAudit) {
            //push
            postMessage(apply, apply.getEmployeeId(), apply.getApproverId(), action, ApplyStatus.PendingAudit, logId, "");
            //push 抄送人
            pushCCMessage(apply, apply.getEmployeeId(), action, ApplyStatus.PendingAudit);
        }
        return GlobalResponseCode.Success;
    }

    private void insertSnapContent(ApplyOrder apply, ApplyOrderV2Contract applyOrderContract) {
        JSONObject jsonObject = new JSONObject();
        // 待核销交易记录总额
        if (ObjUtils.isNotBlank(applyOrderContract.getUnsolved_total())) {
            jsonObject.put("unsolved_total", ObjUtils.toBigDecimal(applyOrderContract.getUnsolved_total()));
        }
        // 可用额度
        if (ObjUtils.isNotBlank(applyOrderContract.getUnsolved_total())) {
            jsonObject.put("available_credit", ObjUtils.toBigDecimal(applyOrderContract.getAvailable_credit()));
        }
        if (ObjUtils.isNotBlank(jsonObject)) {
            apply.setSnapContent(jsonObject.toJSONString());
        }
    }

    private void insertTripContractList(ApplyBankIndividualContract applyContract, String applyId, Date now) {
        ApplyOrderV2Contract apply = applyContract.getApply();
        String bankName = apply.getBank_name();
        String costAttributionId = apply.getCost_attribution_id();
        Integer costAttributionCategory = apply.getCost_attribution_category();
        String costAttributionName = apply.getCost_attribution_name();
        Map<String, Object> costMap = Maps.newHashMap();
        costMap.put("id", costAttributionId);
        costMap.put("name", costAttributionName);
        costMap.put("category", costAttributionCategory);
        Integer type = BizType.Petty.getValue();
        Map<String, Object> costAttributionNameMap = Maps.newHashMap();
        if (applyContract != null) {
            List<ApplyTripInfoContract> tripList = applyContract.getTrip_list();
            List<String> attachmentList=null;
            if (CollectionUtils.isEmpty(tripList)) {
                costAttributionNameMap = costMap;
            } else {
                CostInfoContract costInfo = apply.getCost_info();
                if (ObjUtils.isNotEmpty(costInfo) && CollectionUtils.isNotEmpty(costInfo.getCost_attribution_group_list()) && CollectionUtils.isNotEmpty(costInfo.getCost_attribution_group_list().get(0).getCost_attribution_list())) {
                    CostAttribution costAttribution = costInfo.getCost_attribution_group_list().get(0).getCost_attribution_list().get(0);
                    costAttributionNameMap.put("id", costAttribution.getId());
                    costAttributionNameMap.put("name", costAttribution.getName());
                    costAttributionNameMap.put("category", costInfo.getCost_attribution_group_list().get(0).getCategory());
                } else {
                    costAttributionNameMap = tripList.get(0).getCost_attribution_name();
                }
                attachmentList=tripList.get(0).getAttachment_list();
            }
            ApplyTripInfo trip = new ApplyTripInfo();
            String id = IDTool.CreateUniqueID();
            trip.setId(id);
            trip.setApplyOrderId(applyId);
            trip.setCreateTime(now);
            trip.setType(type);
            trip.setStartCityId("0");
            trip.setState(ApplyTripStatus.Available.getValue());
            trip.setStartTime(now);
            trip.setUpdateTime(now);
            trip.setCostAttributionName(JSON.toJSONString(costAttributionNameMap));
            if (StringUtils.isNotBlank(bankName)) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("bank_name", bankName);
                if (ObjUtils.isNotEmpty(attachmentList)){
                    jsonObject.put("attachment_list",attachmentList);
                }
                CostInfoContract costInfo = apply.getCost_info();
                if (ObjUtils.isNotEmpty(costInfo) && CollectionUtils.isNotEmpty(costInfo.getCost_attribution_group_list()) && CollectionUtils.isNotEmpty(costInfo.getCost_attribution_group_list().get(0).getCost_attribution_list())) {
                    jsonObject.put("cost_info",costInfo);
                }
                trip.setTripContent(jsonObject.toJSONString());
            }
            applyTripInfoMapper.insert(trip);
        }
    }


    /**
     * 审批抄送人通知
     *
     * @param apply
     * @param senderUserId
     * @param action
     * @param desStatus
     */
    public void pushCCMessage(ApplyOrder apply, String senderUserId, ApplyLogAction action, ApplyStatus desStatus) {
        String applyId = apply.getId();
        ApplyType applyType = ApplyType.valueOf(apply.getType().intValue());
        List<ApplyOrderCopyTo> applyOrderCopyToList = applyAdapterMapper.getApplyOrderCopyToExtMapper(apply.getCompanyId()).queryApplyOrderCCByApplyOrderId(applyId);
        if (CollectionUtils.isEmpty(applyOrderCopyToList)) {
            return;
        }
        if (action.getValue() != ApplyLogAction.Submit.getValue() && desStatus.getValue() != ApplyStatus.Approved.getValue()) {
            return;
        }
        //提交审批时抄送通知类型判断
        if (action == ApplyLogAction.Submit) {
            Integer flowCcType = apply.getFlowCcType();
            if (flowCcType == null || flowCcType == 1) {
                return;
            }
        }
        //审批终审结束时抄送通知类型判断
        if (desStatus == ApplyStatus.Approved) {
            Integer flowCcType = apply.getFlowCcType();
            if (flowCcType == null || flowCcType == 2) {
                return;
            }
        }
        //业务类型
        Integer type = apply.getType();
        if (CollectionUtils.isEmpty(applyOrderCopyToList)) {
            return;
        }
        List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.queryApprovalInfo(applyId);
        List<String> sponsorIdList = applyOrderLogList.stream().filter(applyOrderLog -> !applyOrderLog.getAction().equals(ApplyLogAction.Submit.getValue())).map(applyOrderLog -> applyOrderLog.getSponsorId()).distinct().collect(Collectors.toList());
        List<String> applyCopyList = applyOrderCopyToList.stream().map(applyOrderCopyTo -> applyOrderCopyTo.getUserId()).distinct().collect(Collectors.toList());
        applyCopyList.removeAll(sponsorIdList);
        applyOrderCopyToList = applyOrderCopyToList.stream().filter(applyOrderCopyTo -> CollectionUtils.isNotEmpty(applyCopyList) && applyCopyList.contains(applyOrderCopyTo.getUserId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(applyOrderCopyToList)) {
            return;
        }
        String name = "";
        EmployeeContract employee = iBaseOrganizationService.getEmployee(senderUserId, apply.getCompanyId());
        if (employee == null || StringUtils.isBlank(employee.getName())) {
            name = apply.getApplicantName();
        } else {
            name = employee.getName();
        }
        Map<String, Map<String, String>> languageMap = Maps.newHashMap();
        String pushContent = "";
        String messageContent = "";
        messageContent = getCCMessageContent(name, type, languageMap);
        pushContent = messageContent;
        for (ApplyOrderCopyTo applyOrderCopyTo : applyOrderCopyToList) {
            if (StringUtils.isBlank(applyOrderCopyTo.getUserId())) {
                continue;
            }
            String settingType = "12";
            Map<String, Object> msgInfo = new HashMap<>();
            msgInfo.put("myself", "false");
            msgInfo.put("view_type", "3");
            msgInfo.put("id", applyId);
            msgInfo.put("setting_type", settingType);
            msgInfo.put("apply_type", apply.getType().intValue());
            String linkDetail = JSONObject.toJSONString(msgInfo);

            //存消息
            ApplyLogAction msgAction = action;
            if (msgAction == ApplyLogAction.Approve && desStatus != ApplyStatus.Approved) {
                //固定审批流中间审批完成
                msgAction = ApplyLogAction.Submit;
            }
            if (msgAction == ApplyLogAction.Skip) {
                if (desStatus == ApplyStatus.Approved) {
                    msgAction = ApplyLogAction.Approve;
                } else {
                    msgAction = ApplyLogAction.Submit;
                }
            }
            String messageTitle = genMessageTitle(msgAction, languageMap);
            MessageSaveContract messageSaveContract = new MessageSaveContract();
            messageSaveContract.setMessage_type(MessageType.Apply.getCode());
            messageSaveContract.setTitle(messageTitle);
            messageSaveContract.setContent(messageContent);
            messageSaveContract.setBiz_order(applyId);
            messageSaveContract.setLink(linkDetail);
            messageSaveContract.setSender(senderUserId);
            messageSaveContract.setSender_type(SenderType.Person.getCode());
            messageSaveContract.setReceiver(applyOrderCopyTo.getUserId());
            messageSaveContract.setCompany_id(apply.getCompanyId());

            ApplyInfo messageApplyInfo = new ApplyInfo();
            messageApplyInfo.setApply_type(applyType.getBizType().getCode());
            messageApplyInfo.setApply_time(DateTimeTool.fromDateTimeToString(new Date()));
            messageApplyInfo.setApply_msg(messageContent);
            messageApplyInfo.setMyself(false);
            messageApplyInfo.setView_type(3);
            messageApplyInfo.setSetting_type(12);
            messageSaveContract.setApply_info(messageApplyInfo);
            try {
                messageService.saveMessage(messageSaveContract);
            } catch (SaasException ex) {
                //不处理
            }
            PushContract pushInfo = new PushContract();
            pushInfo.setTitle(messageTitle);
            pushInfo.setContent(pushContent);
            pushInfo.setUser_id(applyOrderCopyTo.getUserId());
            pushInfo.setMsg_type("0");
            pushInfo.setDesc(pushContent);
            pushInfo.setAlert(true);
            pushInfo.setMsg(linkDetail);
            pushInfo.setTitleMultilingualMap(languageMap.get(MessageLanguageEnum.TITLE.getCode()));
            pushInfo.setCommentMultilingualMap(languageMap.get(MessageLanguageEnum.CONTENT.getCode()));
            pushService.push(pushInfo);
        }
    }

    private String getCCMessageContent(String sendUserName, Integer type, Map<String, Map<String, String>> languageMap) {
        switch (type) {
            case 1:
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_DinnerNoticeMsg.getLanguageCode(), sendUserName));
                return StrUtils.formatString(CoreLanguage.Common_Message_DinnerNoticeMsg.getMessage(), sendUserName);
            case 2:
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_CarNoticeMsg.getLanguageCode(), sendUserName));
                return StrUtils.formatString(CoreLanguage.Common_Message_CarNoticeMsg.getMessage(), sendUserName);
            case 4:
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_MallNoticeMsg.getLanguageCode(), sendUserName));
                return StrUtils.formatString(CoreLanguage.Common_Message_MallNoticeMsg.getMessage(), sendUserName);
            case 11:
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_DinnerNoticeMsg.getLanguageCode(), sendUserName));
                return StrUtils.formatString(CoreLanguage.Common_Message_DinnerNoticeMsg.getMessage(), sendUserName);
            case 12:
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_CarNoticeMsg.getLanguageCode(), sendUserName));
                return StrUtils.formatString(CoreLanguage.Common_Message_CarNoticeMsg.getMessage(), sendUserName);
            case 15:
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_VirtualCardNoticeMsg.getLanguageCode(), sendUserName));
                return StrUtils.formatString(CoreLanguage.Common_Message_VirtualCardNoticeMsg.getMessage(), sendUserName);
            case 19:
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_ByjNoticeMsg.getLanguageCode(), sendUserName));
                return StrUtils.formatString(CoreLanguage.Common_Message_ByjNoticeMsg.getMessage(), sendUserName);
            default:
                logger.error("生成apply push title时发现没有处理的type:" + type + ",系统暂时只返回了type:" + type);
                return type + "";
        }
    }

    /**
     * 发送差旅push和通知
     *
     * @param apply
     * @param senderUserId
     * @param receiverUserId
     * @param action
     * @param desStatus
     * @param logId
     */
    @Override
    //@Transactional
    public void postMessage(ApplyOrder apply, String senderUserId, String receiverUserId, ApplyLogAction action, ApplyStatus desStatus, Integer logId, String comment) {
        String applyId = apply.getId();
        ApplyType applyType = ApplyType.valueOf(apply.getType().intValue());
        List<String> userIds = new ArrayList<>();
        userIds.add(senderUserId);
        userIds.add(receiverUserId);
        if (!userIds.contains(apply.getEmployeeId())) {
            userIds.add(apply.getEmployeeId());
        }
        List<IdNameContract> userNames = userService.getUserNameByIds(userIds, apply.getCompanyId());
        String employeeName = null;
        String senderUserName = null;
        for (IdNameContract userName : userNames) {
            if (senderUserId.equals(userName.getId())) {
                senderUserName = userName.getName();
                break;
            }
        }
        for (IdNameContract userName : userNames) {
            if (apply.getEmployeeId().equals(userName.getId())) {
                employeeName = userName.getName();
                break;
            }
        }
        if (StringUtils.isBlank(employeeName)) {
            employeeName = apply.getApplicantName();
        }
        ApplyLogAction msgAction = action;
        String msgSender = senderUserName;
        if (StringUtils.isBlank(msgSender) && apply.getEmployeeId().equals(senderUserId)) {
            msgSender = apply.getApplicantName();
        }
        if (msgAction == ApplyLogAction.Approve && desStatus != ApplyStatus.Approved) {
            //固定审批流中间审批完成
            msgSender = employeeName;
            msgAction = ApplyLogAction.Submit;
        }
        if (msgAction == ApplyLogAction.Skip) {
            if (desStatus == ApplyStatus.Approved) {
                msgAction = ApplyLogAction.Approve;
            } else {
                msgAction = ApplyLogAction.Submit;
            }
        }
        Map<String, Map<String, String>> languageMap = Maps.newHashMap();
        String messageContent = "";
        String pushContent = "";
        String messageTitle = genMessageTitle(msgAction, languageMap);
        messageContent = genMessageContent(msgSender, msgAction, applyType, languageMap);
        pushContent = messageContent;
        boolean myself = true;
        if (msgAction == ApplyLogAction.Submit || msgAction == ApplyLogAction.Forward) {
            myself = false;
        }
        String settingType = "12";
        String viewType = myself ? "1" : "2";
        Map<String, Object> msgData = new HashMap<>();
        msgData.put("myself", myself ? "true" : "false");
        msgData.put("view_type", viewType);
        msgData.put("id", applyId);
        msgData.put("setting_type", settingType);
        String linkInfo = JSONObject.toJSONString(msgData); //跳转信息
        //存消息
        MessageSaveContract messageSaveContract = new MessageSaveContract();
        messageSaveContract.setMessage_type(MessageType.Apply.getCode());
        messageSaveContract.setTitle(messageTitle);
        messageSaveContract.setContent(messageContent);
        messageSaveContract.setBiz_order(applyId);
        messageSaveContract.setLink(linkInfo);
        messageSaveContract.setSender(senderUserId);
        messageSaveContract.setSender_type(SenderType.Person.getCode());
        messageSaveContract.setReceiver(receiverUserId);
        messageSaveContract.setCompany_id(apply.getCompanyId());

        ApplyInfo messageApplyInfo = new ApplyInfo();
        messageApplyInfo.setApply_type(applyType.getBizType().getCode());
        messageApplyInfo.setApply_time(DateTimeTool.fromDateTimeToString(new Date()));
        messageApplyInfo.setApply_msg(messageContent);
        messageApplyInfo.setMyself(myself);
        messageApplyInfo.setView_type(myself ? 1 : 2);
        messageApplyInfo.setSetting_type(12);
        if (logId != null && logId != 0) {
            messageApplyInfo.setLog_id(logId);
        }
        messageSaveContract.setApply_info(messageApplyInfo);

        try {
            messageService.saveMessage(messageSaveContract);
        } catch (SaasException ex) {
            //不处理
            logger.info("保存通知异常:" + ex.getMsg());
        }

        PushContract pushData = new PushContract();
        pushData.setTitle(messageTitle);
        pushData.setContent(pushContent);
        pushData.setUser_id(receiverUserId);
        pushData.setCompany_id(apply.getCompanyId());
        pushData.setMsg_type("0");
        pushData.setDesc(pushContent);
        pushData.setAlert(true);
        pushData.setMsg(linkInfo);
        pushData.setTitleMultilingualMap(languageMap.get(MessageLanguageEnum.TITLE.getCode()));
        pushData.setCommentMultilingualMap(languageMap.get(MessageLanguageEnum.CONTENT.getCode()));
        pushService.pushWithBudge(pushData);
    }

    private String genMessageContent(String sendUserName, ApplyLogAction action, ApplyType applyType, Map<String, Map<String, String>> languageMap) {
        String applyTypeDesc = applyType.getDesc();
        switch (action) {
            case Submit:
            case ReSubmit:
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_FlowSubmitMsg.getLanguageCode(), sendUserName, applyTypeDesc));
                return StrUtils.formatString(CoreLanguage.Common_Message_FlowSubmitMsg.getMessage(), sendUserName, applyTypeDesc);
            case Approve:
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_FlowSubmitAcceptMsg.getLanguageCode(), sendUserName, applyTypeDesc));
                return StrUtils.formatString(CoreLanguage.Common_Message_FlowSubmitAcceptMsg.getMessage(), sendUserName, applyTypeDesc);
            case Forward:
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_FlowForwardMsg.getLanguageCode(), sendUserName, applyTypeDesc));
                return StrUtils.formatString(CoreLanguage.Common_Message_FlowForwardMsg.getMessage(), sendUserName, applyTypeDesc);
            case Refuse:
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_FlowRefuse.getLanguageCode(), sendUserName, applyTypeDesc));
                return StrUtils.formatString(CoreLanguage.Common_Message_FlowRefuse.getMessage(), sendUserName, applyTypeDesc);
            case Revoke:
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_FlowRevokeMsg.getLanguageCode(), sendUserName, applyTypeDesc));
                return StrUtils.formatString(CoreLanguage.Common_Message_FlowRevokeMsg.getMessage(), sendUserName, applyTypeDesc);
            default:
                logger.error("生成apply push msg时发现没有处理的action:" + action.getValue() + ",系统暂时只返回了actionName:" + action.getDesc());
                return action.getDesc();
        }
    }

    private String genMessageTitle(ApplyLogAction action, Map<String, Map<String, String>> languageMap) {
        switch (action) {
            case Submit:
            case ReSubmit:
                languageMap.put(MessageLanguageEnum.TITLE.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Title_WaitCheck.getLanguageCode()));
                return CoreLanguage.Common_Title_WaitCheck.getMessage();
            case Approve:
                languageMap.put(MessageLanguageEnum.TITLE.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Title_Pass.getLanguageCode()));
                return CoreLanguage.Common_Title_Pass.getMessage();
            case Forward:
                languageMap.put(MessageLanguageEnum.TITLE.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Title_Forward.getLanguageCode()));
                return CoreLanguage.Common_Title_Forward.getMessage();
            case Refuse:
                languageMap.put(MessageLanguageEnum.TITLE.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Title_HasRefuse.getLanguageCode()));
                return CoreLanguage.Common_Title_HasRefuse.getMessage();
            case Revoke:
                languageMap.put(MessageLanguageEnum.TITLE.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Title_HasRevoke.getLanguageCode()));
                return CoreLanguage.Common_Title_HasRevoke.getMessage();
            case Overtime:
                languageMap.put(MessageLanguageEnum.TITLE.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Title_Timeout.getLanguageCode()));
                return CoreLanguage.Common_Title_Timeout.getMessage();
            default:
                logger.error("生成apply push title时发现没有处理的action:" + action.getValue() + ",系统暂时只返回了actionName:" + action.getDesc());
                return action.getDesc();
        }
    }

    //@Transactional(value = "saas")
    private void insertApproverMap(String applyId, String approverId, Date time) {
        ApplyApproverMapExample example = new ApplyApproverMapExample();
        example.createCriteria().andApplyIdEqualTo(applyId).andApproverIdEqualTo(approverId);
        List<ApplyApproverMap> list = applyApproverMapMapper.selectByExample(example);
        if (ObjUtils.isEmpty(list)) {
            ApplyApproverMap approverMap = new ApplyApproverMap();
            approverMap.setApplyId(applyId);
            approverMap.setApproverId(approverId);
            approverMap.setCreateTime(time);
            applyApproverMapMapper.insertSelective(approverMap);
        }
    }

    /**
     * 整理申请单数据
     *
     * @param applyContract
     */
    private void clearApplyData(ApplyBankIndividualContract applyContract) {
        ApplyOrderV2Contract applyorderContract = applyContract.getApply();
        if (applyorderContract.getId() == null || applyorderContract.getId().length() == 0) {
            applyorderContract.setCheck_reason(null);
        }
        if (applyorderContract.getBudget() == null) {
            applyorderContract.setBudget(BigDecimal.valueOf(0));
        }
        if (CollectionUtils.isEmpty(applyContract.getTrip_list())) {
            applyContract.setTrip_list(null);
        }
        if (applyorderContract.getApprover_id() == null) {
            applyorderContract.setApprover_id("");
        }

    }

    /**
     * 检查数据有效性
     *
     * @param applyContract
     * @return
     */
    private GlobalResponseCode checkApplyData(ApplyBankIndividualContract applyContract, String employeeId, String clientVersion, String companyId) {
        //检查申请单
        ApplyOrderV2Contract apply = applyContract.getApply();
        if (apply == null) {
            return GlobalResponseCode.ApplyIsNull;
        }
        if (apply.getState() == null) {
            return GlobalResponseCode.ParameterError;
        }
        if (apply.getState().intValue() != ApplyStatus.PendingAudit.getValue()) {
            //如果申请单的状态不是草稿,就置为提交
            return GlobalResponseCode.ParameterError;
        }
        if (apply.getType() == null
                || ApplyType.valueOf(apply.getType()) == ApplyType.Unknown) {
            return GlobalResponseCode.ApplyTypeInvalid;
        }
        if (StringUtils.isNotBlank(apply.getTitle()) && apply.getTitle().length() > 14) {
            //申请事由不能超过50字
            return GlobalResponseCode.ApplyPettryTitleInvalid;
        }
        if (StringUtils.isNotBlank(apply.getApply_reason()) && apply.getApply_reason().length() > 200) {
            //申请事由不能超过50字
            return GlobalResponseCode.ApplyReasonInvalid;
        } else if (StringUtils.isNotBlank(apply.getApply_reason_desc()) && apply.getApply_reason_desc().length() > 500) {
            return GlobalResponseCode.ApplyReasonInvalid;
        }
        CompanyRuleDTO companyRuleDTO= iCompanyRuleService.queryByCompanyId(companyId);
        if (ObjUtils.isEmpty(companyRuleDTO)){
            return GlobalResponseCode.ParameterError;
        }
        if (companyRuleDTO.getVirtualCardMode()==1){
            return GlobalResponseCode.ApplyVirtualModeIsNormal;
        }

        //检验申请事由和事由补充内容
        ReasonConfig reasonConfig = customReasonService.queryReasonConfigWithoutItems(companyId, ReasonType.APPLY_VIRTUAL_CARD);
        if (reasonConfig.getReason() == SaasMessageConstant.IS_CHECKED_TRUE) {
            if (StringUtils.isBlank(apply.getApply_reason())) {
                return GlobalResponseCode.ApplyReasonIsNull;
            }
            if (reasonConfig.getReason_desc() == SaasMessageConstant.IS_CHECKED_TRUE) {
                if (StringUtils.isBlank(apply.getApply_reason_desc())) {
                    return GlobalResponseCode.ApplyReasonDescIsNull;
                }
            }
        }
        //审批类型 1.弹性审批流 2.固定审批流 3.分条件审批流
        CompanyApplyType applyType = CompanyApplyType.valueOf(apply.getFlow_type());
        if (applyType.getValue() == CompanyApplyType.Unknown.getValue()) {
            return GlobalResponseCode.ApplyFlowTypeError;
        }
        if (applyType.getValue() == CompanyApplyType.Elastic.getValue() && apply.getState() == ApplyStatus.PendingAudit.getValue() && StringUtils.isBlank(apply.getApprover_id())) {
            //申请单必须指定一个审批人
            return GlobalResponseCode.ApplyApproveIdInvalid;
        }
        if (applyType.getValue() == CompanyApplyType.Elastic.getValue() && employeeId.equals(apply.getApprover_id())) {
            //审批人不能是自己
            return GlobalResponseCode.ApplyApproverCannotBeSelf;
        }
        Integer costAttributionCategory = apply.getCost_attribution_category();
        String costAttributionId = apply.getCost_attribution_id();
        String costAttributionName = apply.getCost_attribution_name();
        if (costAttributionCategory == null || StringUtils.isBlank(costAttributionId) || StringUtils.isBlank(costAttributionName)) {
            return GlobalResponseCode.CostAttributionNameIsNull;
        }
        if (VersionTool.compare(clientVersion, "3.8.3") < 0) {
            /*ApplySetupContract applySetupCostAttributionContract = messageSetupService.queryCompanyCostAttributionConfig(companyId);
            Integer applyCostAttributionCategory = applySetupCostAttributionContract.getCost_attribution_category();
            if (costAttributionCategory == null) {
                costAttributionCategory = 1;
            }
            if (costAttributionCategory == 1 && applyCostAttributionCategory != 1 && applyCostAttributionCategory != 3) {
                return GlobalResponseCode.ApplyCenterAlert;
            }
            if (costAttributionCategory == 2 && applyCostAttributionCategory != 2 && applyCostAttributionCategory != 3) {
                return GlobalResponseCode.ApplyCenterAlert;
            }*/
            return GlobalResponseCode.ApplyCenterAlert;
        }

        ApplyOrderExample example =new ApplyOrderExample();
        example.createCriteria().andEmployeeIdEqualTo(employeeId).andCompanyIdEqualTo(companyId)
                .andTypeIn(Lists.newArrayList(ApplyType.Petty.getValue(),ApplyType.BankIndividual.getValue())).andStateEqualTo(ApplyStatus.PendingAudit.getValue()).andDeleteStatusEqualTo(0);
        List<ApplyOrder> list=applyOrderMapper.selectByExample(example);
        if (ObjUtils.isNotEmpty(list)){
            return GlobalResponseCode.ApplyPettryIsApproval;
        }

        Integer attConfig = iMessageSetupService.queryCheckedByItemCode(companyId, ItemCodeType.ITEM_CODE_APPLY_BANK_INDIVIDUAL_ATTACHMENT.getCode());
        List<ApplyTripInfoContract> tripInfoContractList = applyContract.getTrip_list();
        //附件必填
        if (attConfig==1){
            if (ObjUtils.isEmpty(tripInfoContractList)||ObjUtils.isEmpty(tripInfoContractList.get(0).getAttachment_list())){
                //历史版本如果未填，提示升级
                if (VersionTool.compare(clientVersion, "4.7.1") < 0) {
                    return GlobalResponseCode.ApplyBankIndividualAttachmentIsNull;
                }else {
                    return GlobalResponseCode.ApplyBankIndividualAttachmentMustUpload;
                }
            }
        }
        return GlobalResponseCode.Success;
    }

    private ApplyV2Contract getApplyByIdAndUserIdAndCompanyId(String applyId, String userId, String companyId, UserRole userRole, String token, String clientVersion) throws SaasException {
        ApplyOrder applyModel = getApplyOrderModelByIdAndUserIdAndCompanyId(applyId, userId, companyId, userRole);
        if (applyModel == null) {
            applyModel = applyOrderExtMapper.getByIdAndApproverIdAndCompanyId(applyId, userId, companyId);
        }
        if (applyModel == null) {
            List<ApplyOrder> ccApplyOrderCountByApplyIdList = applyOrderExtMapper.getCCApplyOrderCountByApplyId(userId, companyId, 0);
            if (CollectionUtils.isEmpty(ccApplyOrderCountByApplyIdList)) {
                return null;
            }
            List<String> cclist = Lists.newArrayList();
            for (ApplyOrder applyOrder : ccApplyOrderCountByApplyIdList) {
                cclist.add(applyOrder.getId());
            }
            if (cclist.contains(applyId)) {
                applyModel = applyOrderMapper.selectByPrimaryKey(applyId);
            } else {
                return null;
            }
        }
        ApplyOrderV2Contract apply = ApplyOrderV2Contract.FromModel(applyModel);
        // 处理快照信息
        dealSnapContent(apply, applyModel);
        // 4.7.1 配置项目编码显示
        ApplySetupContract applySetupContract = messageSetupService.queryCompanyApplyCostAttributionConfig(companyId);
        if (applySetupContract != null
                && apply.getCost_attribution_category() == CostAttributionCategory.CostCenter.getKey()
                && SaasMessageConstant.IS_CHECKED_TRUE == applySetupContract.getApply_show_project_code()) {
            apply.setCost_attribution_code(applyModel.getCostAttributionCode());
        }
        //返回当前时间戳
        ApplyV2Contract applyContract = new ApplyV2Contract();
        applyContract.setApply(apply);
        if (VersionTool.compare(clientVersion, "3.8.2") > 0) {
            List<ApplyTripInfoContract> tripList = getTripListByApplyOrderId(applyId, apply);
            applyContract.setTrip_list(tripList);
        }
        return applyContract;
    }

    private List<ApplyTripInfoContract> getTripListByApplyOrderId(String applyOrderId, ApplyOrderV2Contract apply) {
        List<ApplyTripInfo> tripList = applyTripInfoExtMapper.selectListByApplyOrderId(applyOrderId);
        if (com.luastar.swift.base.utils.CollectionUtils.isEmpty(tripList)) {
            return null;
        }
        List<ApplyTripInfoContract> tripContractList = new ArrayList<ApplyTripInfoContract>();
        for (ApplyTripInfo trip : tripList) {
            ApplyTripInfoContract contract = ApplyTripInfoContract.FromModel(trip);
            if (StringUtils.isNotBlank(trip.getCostAttributionName())) {
                contract.setCost_attribution_name((Map<String, Object>) JSON.parse(trip.getCostAttributionName()));
            }
            if (StringUtils.isNotBlank(trip.getTripContent())){
                Map map =JsonUtils.toObj(trip.getTripContent(),Map.class);
                if (ObjUtils.isNotEmpty(map.get("attachment_list"))){
                    contract.setAttachment_list((List<String>) map.get("attachment_list"));
                }
                if (ObjUtils.isNotEmpty(map.get("cost_info"))){
                    apply.setCost_info(JsonUtils.toObj(JsonUtils.toJson(map.get("cost_info")), CostInfoContract.class));
                } else {
                    Map<String, Object> costAttribution = (Map<String, Object>) JSON.parse(trip.getCostAttributionName());
                    String id = ObjUtils.toString(costAttribution.get("id"), null);
                    String name = ObjUtils.toString(costAttribution.get("name"), null);
                    Integer category = ObjUtils.toInteger(costAttribution.get("category"), null);
                    CostInfoContract costInfoContract = new CostInfoContract();
                    List<CostInfoContract.CostAttributionGroup> costAttributionGroups = Lists.newArrayList();
                    CostInfoContract.CostAttributionGroup costAttributionGroup = new CostInfoContract.CostAttributionGroup();
                    costAttributionGroup.setCategory(category);
                    List<CostAttribution> costAttributionList = Lists.newArrayList();
                    CostAttribution costAttributionInfo = new CostAttribution();
                    costAttributionInfo.setId(id);
                    costAttributionInfo.setName(name);
                    costAttributionList.add(costAttributionInfo);
                    costAttributionGroup.setCost_attribution_list(costAttributionList);
                    costAttributionGroups.add(costAttributionGroup);
                    costInfoContract.setCost_attribution_group_list(costAttributionGroups);
                    apply.setCost_info(costInfoContract);
                }
            }
            tripContractList.add(contract);
        }
        return tripContractList;
    }

    /**
     * 获取审批是否是终审节点
     *
     * @param apply
     * @param applyFlowUserItems
     * @param skippedApprverIds
     * @param couldFinalApprove
     * @return
     * @throws SaasException
     */
    private FindNextApproverIdAndStatusResult findStatusInFlow(ApplyOrder apply, List<ApplyFlowUserItem> applyFlowUserItems, List<String> skippedApprverIds, boolean couldFinalApprove) throws SaasException {
        String approverId = null;
        String receiverId = null;
        ApplyStatus finalStatus;
        Integer applyStatus = null;
        int remainIndex = applyFlowUserItems.size();
        for (int i = 0; i < applyFlowUserItems.size(); i++) {
            if (ValueTool.areEqual(applyFlowUserItems.get(i).getStatus(), ApplyFlowUserItemStatus.Waiting.getValue())) {
                remainIndex = i;
                break;
            }
        }
        //找下一个审批人
        boolean findNextApprover = false;
        // 是否是最后一名审批人
        boolean finnalApprover = true;
        for (int i = remainIndex; i < applyFlowUserItems.size(); i++) {
            ApplyFlowUserItem item = applyFlowUserItems.get(i);
            if (ValueTool.areEqual(item.getStatus(), ApplyFlowUserItemStatus.Waiting.getValue())) {
                finnalApprover = false;
                if (userService.isUserInCompany(item.getUserId(), apply.getCompanyId())) {
                    findNextApprover = true;
                    receiverId = item.getUserId();
                    approverId = receiverId;
                    applyStatus = 1;
                    break;
                } else {
                    skippedApprverIds.add(item.getUserId());
                }
            }
        }
        if (finnalApprover && couldFinalApprove) {
            //当前审批人已经是最后一个审批人
            finalStatus = ApplyStatus.Approved;
            receiverId = apply.getEmployeeId();
            applyStatus = 2;
        } else {
            finalStatus = ApplyStatus.PendingAudit;
            if (!findNextApprover) {
                //后面的审批人都已不存在，直接跳到授权负责人
                List<String> superAdminEmployeeIds = customRoleService.getEmployeeIdByRoleId(Integer.toString(UserRole.CompanySuperAdmin.getValue()), apply.getCompanyId());
                if (superAdminEmployeeIds == null || superAdminEmployeeIds.size() == 0) {
                    throw new SaasException(GlobalResponseCode.ApplyCanotApproveBecauseHasNoSuperAdmin);
                } else {
                    receiverId = superAdminEmployeeIds.get(0);
                    approverId = receiverId;
                    applyStatus = 3;
                }
            }
        }
        FindNextApproverIdAndStatusResult result = new FindNextApproverIdAndStatusResult();
        result.setApproverId(approverId);
        result.setReceiverId(receiverId);
        result.setFinnalStatus(finalStatus);
        result.setApplyStatus(applyStatus);
        return result;
    }

    private ApplyOrder getApplyOrderModelByIdAndUserIdAndCompanyId(String id, String userId, String companyId, UserRole userRole) {
        if (StringTool.isNullOrEmpty(id) || StringTool.isNullOrEmpty(userId) || StringTool.isNullOrEmpty(companyId)) {
            return null;
        }
        ApplyOrder apply = applyOrderMapper.selectByPrimaryKey(id);
        if (apply == null) {
            return null;
        }
        if (apply.getCompanyId() == null || !apply.getCompanyId().equals(companyId)) {
            return null;
        }
        if (!apply.getEmployeeId().equals(userId)) {
            //如果不是本人，再判断一下userrole是不是分贝管理员或者企业管理员
            if (userRole == null || (userRole != UserRole.CompanyAdmin && userRole != UserRole.CompanySuperAdmin && userRole != UserRole.FbAdmin)) {
                return null;
            }
        }
        return apply;
    }

    private List<ApplyOrderLogContract> getLogsByApplyId(String applyId, String companyId) {
        List<ApplyOrderLog> logs = applyOrderLogExtMapper.selectListByApplyId(applyId);
        logs = logs.stream().filter(logInfo -> logInfo.getAction() != ApplyLogAction.CreateDraft.getValue() && logInfo.getAction() != ApplyLogAction.ModifyDraft.getValue()).collect(Collectors.toList());
        //查询是否有已拒绝、已撤销和超时状态的审批日志
        List<ApplyOrderLog> destinationLogList = logs.stream().filter(logInfo -> logInfo.getAction() == ApplyLogAction.Refuse.getValue() || logInfo.getAction() == ApplyLogAction.Revoke.getValue() || logInfo.getAction() == ApplyLogAction.Overtime.getValue()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(destinationLogList)) {
            logs = logs.stream().filter(logInfo -> logInfo.getAction() != ApplyLogAction.Approval.getValue() && logInfo.getAction() != ApplyLogAction.Unknown.getValue()).collect(Collectors.toList());
        }
        List<ApplyOrderLogContract> result = getLogContractList(logs, companyId);
        return result;
    }

    private void appendApplyDetail(String token, ApplyV2Contract detail, String userId) {
        ApplyOrderV2Contract order = detail.getApply();
        //申请人及审批人的姓名及部门
        List<String> userIds = new ArrayList<>();
        userIds.add(order.getEmployee_id());
        if (order.getApprover_id() != null && order.getApprover_id().length() > 0 && !userIds.contains(order.getApprover_id())) {
            userIds.add(order.getApprover_id());
        }
        List<EmployeeNameAndDeptContract> approverNameAndDepts = userService.getNamesAndDeptsByIds(token, order.getCompany_id(), userIds);
        if (approverNameAndDepts != null && approverNameAndDepts.size() > 0) {
            for (EmployeeNameAndDeptContract contract : approverNameAndDepts) {
                if (contract.getEmployee_id().equals(order.getEmployee_id())) {
                    order.setUser_name(contract.getName());
                    order.setUser_dept(String.join(",", contract.getDepts()));
                    break;
                }
            }
            if (order.getApprover_id() != null) {
                for (EmployeeNameAndDeptContract contract : approverNameAndDepts) {
                    if (contract.getEmployee_id().equals(order.getApprover_id())) {
                        order.setApprover_dept(String.join(",", contract.getDepts()));
                        order.setApprover_name(contract.getName());
                        break;
                    }
                }
            }
        }
        if (StringUtils.isBlank(order.getUser_name())) {
            order.setUser_name(detail.getApply().getApplicant_name());
        }
        if (StringUtils.isBlank(order.getUser_dept())) {
            order.setUser_dept(CoreLanguage.Common_Value_NoDepartmentInfo.getMessage());
        }
        //可操作权限
        Integer operateAuth = genApplyOperateAuth(order, userId);
        order.setOperate_auth(operateAuth);
        //处理审批流设置
        appendFlowData(order);
    }

    /**
     * 处理快照信息返回
     * @param
     */
    @Override
    public void dealSnapContent(ApplyOrderV2Contract apply, ApplyOrder applyOrder) {
        // 新增标志位，兼容老版本
        apply.setShow_credit_area(false);
        if (ObjUtils.isNotBlank(applyOrder.getSnapContent())) {
            Map<String, Object> contentMap = JSON.parseObject(applyOrder.getSnapContent(), Map.class);
            // 待核销交易记录总额
            if (ObjUtils.isNotBlank(contentMap.get("unsolved_total"))) {
                BigDecimal unsolvedTotal = ObjUtils.toBigDecimal(contentMap.get("unsolved_total"));
                apply.setUnsolved_total(unsolvedTotal);
                apply.setShow_credit_area(true);
            }
            // 可用额度
            if (ObjUtils.isNotBlank(contentMap.get("available_credit"))) {
                BigDecimal availableCredit = ObjUtils.toBigDecimal(contentMap.get("available_credit"));
                apply.setAvailable_credit(availableCredit);
                apply.setShow_credit_area(true);
            }
        }
    }

    private void appendFlowData(ApplyOrderV2Contract applyOrderContract) {
        CompanyApplyFlowSetV2RequestContract flowSet = null;
        if (applyOrderContract.getFlow_type() == CompanyApplyType.Flow.getValue() || applyOrderContract.getFlow_type() == CompanyApplyType.CONDITIONAL.getValue()) {
            flowSet = applyFlowV2Service.getFlowByApplyId(applyOrderContract.getId(), applyOrderContract.getCompany_id());
            flowSet = applyFlowV2Service.getFlowCCByApplyId(applyOrderContract.getId(), flowSet, applyOrderContract.getCompany_id());
        }
        if (applyOrderContract.getFlow_type() == CompanyApplyType.Elastic.getValue()) {
            flowSet = applyFlowV2Service.getFlowCCByApplyId(applyOrderContract.getId(), flowSet, applyOrderContract.getCompany_id());
            flowSet.setCompany_apply_type(1);
        }
        if (applyOrderContract.getFlow_type() == CompanyApplyType.OpenApi.getValue()) {
            flowSet = new CompanyApplyFlowSetV2RequestContract();
            flowSet.setFixation_flow_list(Lists.newArrayList());
            flowSet.setCc_list(Lists.newArrayList());
            flowSet.setCc_notice_type_enums(new KvContract());
        }
        if (flowSet != null) {
            applyOrderContract.setFlow_type(flowSet.getCompany_apply_type());
            applyOrderContract.setFlow(flowSet);
        }
    }

    /**
     * 判断操作权限
     *
     * @param order
     * @param userId
     * @return
     */
    private Integer genApplyOperateAuth(ApplyOrderV2Contract order, String userId) {
        ApplyStatus orderStatus = ApplyStatus.valueOf(order.getState());
        if (order.getEmployee_id().equals(userId)) {
            //自己的申请单
            if (orderStatus == ApplyStatus.PendingAudit) {
                if (userId.equals(order.getApprover_id())) {
                    return ApplyLogAction.Approve.getValue() + ApplyLogAction.Forward.getValue() + ApplyLogAction.Refuse.getValue() + ApplyLogAction.Revoke.getValue();
                }
                //状态为待审核时,用户可撤销申请
                return ApplyLogAction.Revoke.getValue();
                //} else if (orderStatus == ApplyStatus.Return || orderStatus == ApplyStatus.Draft) {
            } else if (orderStatus == ApplyStatus.Draft) {
                //状态为被驳回或草稿时,用户可提交申请或者修改
                return ApplyLogAction.Submit.getValue() + ApplyLogAction.ModifyDraft.getValue() + ApplyLogAction.Delete.getValue();
            }
        } else {
            //是需要自己审批,或者自己已经操作过的审批单
            if (orderStatus == ApplyStatus.PendingAudit || orderStatus == ApplyStatus.Transfer) {
                //只有待自己处理的审批单才可操作
                if (userId.equals(order.getApprover_id())) {
                    return ApplyLogAction.Approve.getValue() + ApplyLogAction.Forward.getValue() + ApplyLogAction.Refuse.getValue();
                }
            }
        }
        //不能做任何操作
        return 0;
    }

    private String genApplyLogDisplayString(ApplyOrderLog log, List<IdNameContract> userNames) {
        StringBuilder sb = new StringBuilder();
        IdNameContract sponsor = getNameFromListById(userNames, log.getSponsorId());
        if (sponsor != null) {
            sb.append(sponsor.getName() + " ");
        }
        ApplyLogAction action = ApplyLogAction.valueOf(log.getAction());
        String actionName = "";
        if (action == ApplyLogAction.Unknown) {
            actionName = "";
        } else {
            actionName = action.getDesc();
        }
        sb.append(actionName);
        if (action == ApplyLogAction.Forward
                || action == ApplyLogAction.Submit
                || action == ApplyLogAction.ReSubmit
                ) {
            sb.append(CoreLanguage.Common_Value_Give.getMessage() + " ");
            if (log.getReceiverId() != null) {
                IdNameContract receiver = getNameFromListById(userNames, log.getReceiverId());
                if (receiver != null) {
                    sb.append(receiver.getName());
                }
            }
        }
        return sb.toString();
    }

    private List<ApplyOrderLogContract> getLogContractList(List<ApplyOrderLog> logs, String companyId) {
        if (logs == null || logs.size() == 0) {
            return new ArrayList<>();
        }
        List<ApplyOrderLogContract> result = new ArrayList<>();
        List<String> userIds = new ArrayList<>();
        for (ApplyOrderLog log : logs) {
            if (!userIds.contains(log.getSponsorId())) {
                userIds.add(log.getSponsorId());
            }
            if (log.getReceiverId() != null && !userIds.contains(log.getReceiverId())) {
                userIds.add(log.getReceiverId());
            }
        }
        List<IdNameContract> userNames = userService.getUserNameByIds(userIds, companyId);
        for (ApplyOrderLog log : logs) {
            ApplyOrderLogContract logContract = genLogContract(log, userNames);
            if (logContract != null && logContract.getAction() == ApplyLogAction.Unknown.getValue() && StringUtils.isBlank(logContract.getSponsor()) && StringUtils.isBlank(logContract.getLog())) {
                logContract.setAction(ApplyLogAction.Skip.getValue());
                logContract.setCheck_reason(CoreLanguage.Common_Value_ApprovalPersonDelete.getMessage());
            }
            result.add(logContract);
        }
        return result;
    }

    private ApplyOrderLogContract genLogContract(ApplyOrderLog log, List<IdNameContract> userNames) {
        ApplyOrderLogContract contract = ApplyOrderLogContract.FromModel(log);
        String logContent = genApplyLogDisplayString(log, userNames);
        contract.setLog(logContent);
        if (StringUtils.isNotBlank(log.getSponsorId())) {
            for (IdNameContract idNameContract : userNames) {
                if (idNameContract.getId().equals(log.getSponsorId())) {
                    contract.setSponsor(idNameContract.getName());
                }
            }
        }
        if (StringUtils.isNotBlank(log.getReceiverId())) {
            for (IdNameContract idNameContract : userNames) {
                if (idNameContract.getId().equals(log.getReceiverId())) {
                    contract.setReceiver(idNameContract.getName());
                }
            }
        }
        return contract;
    }

    private IdNameContract getNameFromListById(List<IdNameContract> list, String id) {
        if (list == null || id == null) {
            return null;
        }
        for (IdNameContract item : list) {
            if (item.getId().equals(id)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 审批单详情
     *
     * @param token
     * @param applyId
     * @param userId
     * @param companyId
     * @param userRole
     * @return
     * @throws SaasException
     */
    @Override
    public ApplyV2Contract getDetail(String token, String applyId, String userId, String companyId, UserRole userRole, String clientVersion) throws SaasException {
        FlowCheckUtil.check(companyId, clientVersion);
        ApplyV2Contract apply = getApplyByIdAndUserIdAndCompanyId(applyId, userId, companyId, userRole, token, clientVersion);
        if (apply == null) {
            return null;
        }
        //1、申请人及审批人的姓名及部门 2、可操作权限
        appendApplyDetail(token, apply, userId);
        List<ApplyOrderLogContract> logs = getLogsByApplyId(applyId, companyId);
        for (ApplyOrderLogContract applyOrderLogContract : logs) {
            if (StringUtils.isBlank(applyOrderLogContract.getSponsor()) && apply.getApply().getEmployee_id().equals(applyOrderLogContract.getSponsor_id())) {
                if (applyOrderLogContract.getAction() != ApplyLogAction.Skip.getValue()) {
                    applyOrderLogContract.setSponsor(apply.getApply().getApplicant_name());
                }
            }
        }
        apply.setLog_list(logs);
        return apply;
    }

    /**
     * 订单审批单（同意）
     *
     * @param approveModel
     * @param userId
     * @param ip
     * @return
     */
    @Override
    //@Transactional(value = "saas")
    public GlobalResponseCode approve(ApplyApproveContract approveModel, String userId, String companyId, String ip, String clientVersion, String source) throws SaasException {
        FlowCheckUtil.check(companyId, clientVersion, source);
        if (approveModel == null) {
            throw new SaasException(GlobalResponseCode.ApplyIsNull);
        }
        if (StringTool.isNullOrEmpty(approveModel.getApply_id())) {
            throw new SaasException(GlobalResponseCode.ApplyIdInvalid);
        }
        if (approveModel.getComment() != null && approveModel.getComment().length() > 200) {
            throw new SaasException(GlobalResponseCode.ApplyCheckReasonInvalid);
        }
        String applyId = approveModel.getApply_id();
        ApplyOrder apply = applyOrderMapper.selectByPrimaryKey(approveModel.getApply_id());
        if (apply == null) {
            throw new SaasException(GlobalResponseCode.NotFound);
        }
        if (apply.getState() != null && apply.getState().intValue() == ApplyStatus.Overtime.getValue()) {
            //订单审批已超时
            throw new SaasException(GlobalResponseCode.CenterApplyOverTime);
        }
        if (apply.getState() != null && apply.getState().intValue() == ApplyStatus.Backout.getValue()) {
            //事中审批已撤销
            throw new SaasException(GlobalResponseCode.CenterApplyCancleOrder);
        }
        if (apply.getType()!=ApplyType.Petty.getValue()){
            throw new SaasException(GlobalResponseCode.ApplyFlowTypeError);
        }
        if (!apply.getCompanyId().equals(companyId)) {
            //申请单和当前人不在一个公司
            throw new SaasException(GlobalResponseCode.ApplyCompanyCannotMatch);
        }
        if (apply.getState() != ApplyStatus.PendingAudit.getValue()) {
            //该单不是待审核,不能审批
            throw new SaasException(GlobalResponseCode.ApplyStatusNotPendingAudit);
        }
        if (!apply.getApproverId().equals(userId)) {
            //不是当前人可以处理的审批单
            throw new SaasException(GlobalResponseCode.NoAuth);
        }
        //
        PettyDetailRespDTO respDTO = iBankPettySearchRpcService.queryBankPettyByEmployeeId(companyId, apply.getEmployeeId());
        if (ObjUtils.isNotEmpty(respDTO)&&respDTO.getBalance().compareTo(BigDecimal.ZERO)>0){
            return GlobalResponseCode.ApplyUserPettryIsNotUseUp;
        }

        ApplyStatus finalStatus;
        ApplyLogAction action;
        String receiverId; //下一个审批人/通知人
        String approverId; //审批人
        Integer logId = null;
        List<String> skippedApprverIds = new ArrayList<>();//固定审批流跳过的审批人
        Date now = new Date();
        action = ApplyLogAction.Approve;
        approverId = userId;
        receiverId = null;
        Integer finalJudgmentUser = null;
        boolean isFlow = (ValueTool.areEqual(apply.getFlowType(), CompanyApplyType.Flow.getValue()) || ValueTool.areEqual(apply.getFlowType(), CompanyApplyType.CONDITIONAL.getValue()));
        if (isFlow) {
            //固定审批流和分条件审批流
            List<ApplyFlowUserItem> applyFlowUserItems = applyFlowService.getFlowItemsByApplyId(applyId);
            if (applyFlowUserItems == null ||
                    applyFlowUserItems.size() == 0 ||
                    applyFlowUserItems.stream().allMatch(m ->
                            !ValueTool.areEqual(m.getStatus(), ApplyFlowUserItemStatus.PendingAudit.getValue())
                                    && !ValueTool.areEqual(m.getStatus(), ApplyFlowUserItemStatus.Transfered.getValue())
                    )) {
                //没有待审核记录，也没有转交记录，预示审批流已经审核通过
                finalStatus = ApplyStatus.Approved;
                receiverId = apply.getEmployeeId();
                //处理审批日志和审批单的状态
                logId = sloveApplyLog(applyId, receiverId, approverId, finalStatus, now, ip, userId, action, approveModel);
                finalJudgmentUser = 2;
            } else {
                int pendingAuditItemIndex = -1;
                for (int i = 0; i < applyFlowUserItems.size(); i++) {
                    if (ValueTool.areEqual(applyFlowUserItems.get(i).getStatus(), ApplyFlowUserItemStatus.PendingAudit.getValue())) {
                        pendingAuditItemIndex = i;
                        break;
                    }
                }
                ApplyFlowUserItem pendingAuditItem = null;
                if (pendingAuditItemIndex >= 0) {
                    pendingAuditItem = applyFlowUserItems.get(pendingAuditItemIndex);
                }
                if (pendingAuditItem != null && !pendingAuditItem.getUserId().equals(userId)) {
                    logger.info("当前待审批的item不是userid，userId：" + userId + " itemId:" + pendingAuditItem.getUserId());
                    throw new SaasException(GlobalResponseCode.NoAuth);
                } else {
                    if (pendingAuditItem != null) {
                        //将item status设置为已审核
                        applyFlowService.setApplyFlowUserItemStatus(pendingAuditItem.getId(), ApplyFlowUserItemStatus.Approved);
                    }
                    try {
                        FindNextApproverIdAndStatusResult findNextApproverIdAndStatusResult = findNextApproverIdAndStatusInFlow(apply, applyFlowUserItems, skippedApprverIds, true);
                        finalStatus = findNextApproverIdAndStatusResult.getFinnalStatus();
                        finalJudgmentUser = findNextApproverIdAndStatusResult.getApplyStatus();
                        if (!StringTool.isNullOrEmpty(findNextApproverIdAndStatusResult.getApproverId())) {
                            approverId = findNextApproverIdAndStatusResult.getApproverId();
                        }
                        if (!StringTool.isNullOrEmpty(findNextApproverIdAndStatusResult.getReceiverId())) {
                            receiverId = findNextApproverIdAndStatusResult.getReceiverId();
                        }
                        if (findNextApproverIdAndStatusResult.getApplyStatus() == 1) {
                            //不是最后一个审批人存在下一个审批人
                            //查询审核中的审批日志
                            List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectApproveUser(applyId, ApplyLogAction.Approval.getValue());
                            //有日志数据情况下修改操作无日志数据情况下插入操作
                            if (!CollectionUtils.isEmpty(applyOrderLogList)) {
                                applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Approve.getValue(), applyOrderLogList.get(0).getId(), receiverId, approveModel.getComment(), new Date(), approveModel.getPrice());
                                List<ApplyOrderLog> applyOrderNextUserLogList = applyOrderLogExtMapper.selectNextApproveUser(applyId, ApplyLogAction.Unknown.getValue());
                                if (!CollectionUtils.isEmpty(applyOrderNextUserLogList)) {
                                    for (ApplyOrderLog applyOrderLog : applyOrderNextUserLogList) {
                                        String sponsorId = applyOrderLog.getSponsorId();
                                        EmployeeContract employee = iBaseOrganizationService.getEmployee(sponsorId, companyId);
                                        if (employee == null) {
                                            applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Skip.getValue(), applyOrderLog.getId(), "", CoreLanguage.Common_Value_ApprovalPersonDelete.getMessage(), null, approveModel.getPrice());
                                        } else {
                                            //保存下一个审批人为审批中状态
                                            applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Approval.getValue(), applyOrderLog.getId(), "", null, null, approveModel.getPrice());
                                            logId = applyOrderLog.getId();
                                            break;
                                        }
                                    }
                                }
                                setApproverAndStatus(applyId, approverId, finalStatus, now, logId, approveModel);
                            } else {
                                logId = writeLog(applyId, now, ip, userId, receiverId, approveModel.getComment(), action);
                                setApproverAndStatus(applyId, approverId, finalStatus, now, logId, approveModel);
                            }
                        } else if (findNextApproverIdAndStatusResult.getApplyStatus() == 2) {
                            //最后一个审批人
                            logId = sloveApplyLog(applyId, receiverId, approverId, finalStatus, now, ip, userId, action, approveModel);
                        } else {
                            //后面的审批人都不存在直接跳到授权负责人
                            List<ApplyOrderLog> applyOrderApprovalLogList = applyOrderLogExtMapper.selectApproveUser(applyId, ApplyLogAction.Approval.getValue());
                            applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Approve.getValue(), applyOrderApprovalLogList.get(0).getId(), receiverId, approveModel.getComment(), new Date(), approveModel.getPrice());

                            List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectApproveUser(applyId, ApplyLogAction.Unknown.getValue());
                            if (!CollectionUtils.isEmpty(applyOrderLogList)) {
                                for (ApplyOrderLog applyOrderLog : applyOrderLogList) {
                                    applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Skip.getValue(), applyOrderLog.getId(), "", CoreLanguage.Common_Value_ApprovalPersonDelete.getMessage(), null, approveModel.getPrice());
                                }
                            } else {
                                if (skippedApprverIds != null && skippedApprverIds.size() > 0) {
                                    for (String skippedApproverId : skippedApprverIds) {
                                        writeLog(applyId, now, ip, skippedApproverId, skippedApproverId, CoreLanguage.Common_Value_ApprovalPersonDelete.getMessage(), ApplyLogAction.Skip);
                                    }
                                }
                            }
                            int sort = 0;
                            if (!CollectionUtils.isEmpty(applyOrderLogList)) {
                                sort = applyOrderLogList.get(applyOrderLogList.size() - 1).getSort() + 100;
                            }
                            ApplyOrderLog logApprove = new ApplyOrderLog();
                            logApprove.setApplyOrderId(applyId);
                            logApprove.setIp(ip);
                            logApprove.setSponsorId(approverId);
                            logApprove.setReceiverId("");
                            logApprove.setCheckReason(null);
                            logApprove.setAction(ApplyLogAction.Approval.getValue());
                            logApprove.setSort(sort);
                            logApprove.setRootApplyOrderId(applyId);
                            applyOrderLogMapper.insert(logApprove);
                            logId = logApprove.getId();
                            setApproverAndStatus(applyId, approverId, finalStatus, now, logId, approveModel);
                        }
                    } catch (SaasException ex) {
                        logger.info("审批异常：" + ex.getResponseCode());
                        throw new SaasException(GlobalResponseCode.PayOrderCancleOrderSystemError);
                    }
                }
            }
        } else {
            finalStatus = ApplyStatus.Approved;
            receiverId = apply.getEmployeeId();
            approverId = userId;
            //处理审批日志和审批单的状态
            logId = sloveApplyLog(applyId, receiverId, approverId, finalStatus, now, ip, userId, action, approveModel);
        }
        //最后一个审批节点
        if (apply.getFlowType() == CompanyApplyType.Elastic.getValue() || (finalJudgmentUser != null && finalJudgmentUser == 2)) {

            List<ApplyTripInfo> applyTripInfos = applyTripInfoExtMapper.queryAllTripListByApplyOrderId(applyId);
            Map<String, Object> parse = JSON.parseObject(applyTripInfos.get(0).getCostAttributionName(), Map.class);
            String id = ObjUtils.toString(parse.get("id"), "");
            Integer category = ObjUtils.toInteger(parse.get("category"));
            String name = ObjUtils.toString(parse.get("name"), "");
            BigDecimal totalPrice = BigDecimal.valueOf(apply.getBudget()).divide(ObjUtils.toBigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
            GlobalResponseCode validCode = bankIndividualValid(companyId, apply.getEmployeeId(), totalPrice, approveModel.getForce_sumbit(), id, category);
            if (validCode != GlobalResponseCode.Success) {
                throw new SaasException(validCode);
            }
            Map<String, Object> tripContent = JSON.parseObject(applyTripInfos.get(0).getTripContent(), Map.class);
            String bankName = BankNameEnum.XWBANK.getCode();
            if (ObjUtils.isNotEmpty(tripContent)) {
                bankName = ObjUtils.toString(tripContent.get("bank_name"), "");
            }
            try {
                BankApplyCreditReqDTO bankApplyCreditReqDTO = new BankApplyCreditReqDTO();
                bankApplyCreditReqDTO.setEmployeeId(apply.getEmployeeId());
                bankApplyCreditReqDTO.setCompanyId(companyId);
                bankApplyCreditReqDTO.setApplyCreditAmount(BigDecimal.valueOf(apply.getBudget()));
                bankApplyCreditReqDTO.setSaasApplyNo(applyId);
                bankApplyCreditReqDTO.setBankName(bankName);
                bankApplyCreditReqDTO.setCategoryType(OrderCategory.BankIndividual.getKey());
                bankApplyCreditReqDTO.setCostAttributionId(id);
                bankApplyCreditReqDTO.setCostAttributionName(name);
                bankApplyCreditReqDTO.setCostAttributionType(category);
                bankApplyCreditReqDTO.setApplyReason(apply.getApplyReason());
                bankApplyCreditReqDTO.setApplyReasonDesc(apply.getApplyReasonDesc());
                bankApplyCreditReqDTO.setPettyName(apply.getTitle());
                logger.info("备用金终审调用：" + JsonUtils.toJson(bankApplyCreditReqDTO));
                iBankHuPoFBTService.applyCredit(bankApplyCreditReqDTO);
            } catch (FinhubException ex) {
                logger.info(String.format("备用金终审错误：%s,%s,%s,%s", ex.getCode(), ex.getMessage(), ex.getType(), ex.getTitle()));
                throw new SaasException(ex.getCode(), ex.getMessage(), ex.getType(), ex.getTitle());
            }
            // 同步日志
            applyV2Service.syncLogOnFinishProcess(applyId, companyId);
        }
        setApplyApproverAndPushMessage(apply, userId, ip, finalStatus, receiverId, approverId, approveModel.getComment(), action, skippedApprverIds, logId);
        //push 抄送人
        pushCCMessage(apply, apply.getEmployeeId(), action, finalStatus);
        return GlobalResponseCode.Success;
    }


    /**
     * 审批、拒绝、转交情况下处理审批日志的状态和修改审批单的状态
     *
     * @param applyId
     * @param receiverId
     * @param approverId
     * @param finalStatus
     * @param now
     * @param ip
     * @param userId
     * @param action
     * @param approveModel
     */
    //@Transactional(value = "saas")
    public Integer sloveApplyLog(String applyId, String receiverId, String approverId, ApplyStatus finalStatus, Date now, String ip, String userId, ApplyLogAction action, ApplyApproveContract approveModel) {
        //查询审核中的审批日志
        List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectApproveUser(applyId, ApplyLogAction.Approval.getValue());
        //有审批日志情况下进行修改操作,无审批日志情况下插入操作
        if (!CollectionUtils.isEmpty(applyOrderLogList)) {
            applyOrderLogExtMapper.updateApplyOrderActionById(action.getValue(), applyOrderLogList.get(0).getId(), receiverId, approveModel.getComment(), new Date(), null);
            setApproverAndStatus(applyId, approverId, finalStatus, now, applyOrderLogList.get(0).getId(), approveModel);
            return applyOrderLogList.get(0).getId();
        } else {
            Integer logId = writeLog(applyId, now, ip, userId, receiverId, approveModel.getComment(), action);
            setApproverAndStatus(applyId, approverId, finalStatus, now, logId, approveModel);
            return logId;
        }
    }

    //@Transactional(value = "saas")
    private int setApproverAndStatus(String applyId, String approverId, ApplyStatus status, Date time, Integer logId, ApplyApproveContract approveModel) {
        if (approveModel == null) {
            approveModel.setComment("");
            approveModel.setPrice(null);
        }
        int count = applyOrderExtMapper.updateApproverIdAndStatus(applyId, approverId, status.getValue(), time, logId, approveModel.getComment(), null);
        return count;
    }

    //@Transactional(value = "saas")
    private Integer writeLog(String id, Date createTime, String ip, String sponsorId, String receiverId, String checkReason, ApplyLogAction action) {
        //插入日志
        ApplyOrderLog log = new ApplyOrderLog();
        log.setApplyOrderId(id);
        log.setCreateTime(createTime);
        log.setIp(ip);
        log.setSponsorId(sponsorId);
        if (receiverId == null) {
            receiverId = "";
        }
        log.setReceiverId(receiverId);
        log.setCheckReason(checkReason);
        log.setAction(action.getValue());
        log.setPrice(null);
        log.setRootApplyOrderId(id);
        applyOrderLogMapper.insert(log);
        return log.getId();
    }

    private FindNextApproverIdAndStatusResult findNextApproverIdAndStatusInFlow(ApplyOrder apply, List<ApplyFlowUserItem> applyFlowUserItems, List<String> skippedApprverIds, boolean couldFinalApprove) throws SaasException {
        String approverId = null;
        String receiverId = null;
        ApplyStatus finalStatus;
        Integer applyStatus = null;
        int remainIndex = applyFlowUserItems.size();
        for (int i = 0; i < applyFlowUserItems.size(); i++) {
            if (ValueTool.areEqual(applyFlowUserItems.get(i).getStatus(), ApplyFlowUserItemStatus.Waiting.getValue())) {
                remainIndex = i;
                break;
            }
        }
        //找下一个审批人
        boolean findNextApprover = false;
        // 是否是最后一名审批人
        boolean finnalApprover = true;
        for (int i = remainIndex; i < applyFlowUserItems.size(); i++) {
            ApplyFlowUserItem item = applyFlowUserItems.get(i);
            if (ValueTool.areEqual(item.getStatus(), ApplyFlowUserItemStatus.Waiting.getValue())) {
                finnalApprover = false;
                if (userService.isUserInCompany(item.getUserId(), apply.getCompanyId())) {
                    findNextApprover = true;
                    receiverId = item.getUserId();
                    approverId = receiverId;
                    applyStatus = 1;
                    applyFlowService.setApplyFlowUserItemStatus(item.getId(), ApplyFlowUserItemStatus.PendingAudit);
                    break;
                } else {
                    skippedApprverIds.add(item.getUserId());
                    applyFlowService.setApplyFlowUserItemStatus(item.getId(), ApplyFlowUserItemStatus.Skip);
                }
            }
        }
        if (finnalApprover && couldFinalApprove) {
            //当前审批人已经是最后一个审批人
            finalStatus = ApplyStatus.Approved;
            receiverId = apply.getEmployeeId();
            applyStatus = 2;
        } else {
            finalStatus = ApplyStatus.PendingAudit;
            if (!findNextApprover) {
                //后面的审批人都已不存在，直接跳到授权负责人
                List<String> superAdminEmployeeIds = customRoleService.getEmployeeIdByRoleId(Integer.toString(UserRole.CompanySuperAdmin.getValue()), apply.getCompanyId());
                if (superAdminEmployeeIds == null || superAdminEmployeeIds.size() == 0) {
                    throw new SaasException(GlobalResponseCode.ApplyCanotApproveBecauseHasNoSuperAdmin);
                } else {
                    receiverId = superAdminEmployeeIds.get(0);
                    approverId = receiverId;
                    applyStatus = 3;
                }
            }
        }
        FindNextApproverIdAndStatusResult result = new FindNextApproverIdAndStatusResult();
        result.setApproverId(approverId);
        result.setReceiverId(receiverId);
        result.setFinnalStatus(finalStatus);
        result.setApplyStatus(applyStatus);
        return result;
    }

    @Override
    //@Transactional(value = "saas")
    public void setApplyApproverAndPushMessage(ApplyOrder apply, String userId, String ip, ApplyStatus finalStatus, String receiverId, String approverId, String comment, ApplyLogAction action, List<String> skippedApprverIds, Integer logId) {
        Date now = new Date();
        String applyId = apply.getId();
        if (!StringTool.isNullOrEmpty(approverId) && !approverId.equals(apply.getApproverId())) {
            insertApproverMap(applyId, approverId, now);
        }
        postMessage(apply, userId, receiverId, action, finalStatus, logId, comment);
    }

    /**
     * 驳回
     *
     * @param approveModel
     * @param userId
     * @param ip
     * @return
     */
    @Override
    //@Transactional(value = "saas")
    public GlobalResponseCode repulse(ApplyApproveContract approveModel, String userId, String companyId, String ip, String clientVersion, String source) throws SaasException {
        FlowCheckUtil.check(companyId, clientVersion, source);
        if (approveModel == null) {
            return GlobalResponseCode.ApplyIsNull;
        }
        if (StringTool.isNullOrEmpty(approveModel.getApply_id())) {
            return GlobalResponseCode.ApplyIdInvalid;
        }
        if (approveModel.getComment() != null && approveModel.getComment().length() > 200) {
            return GlobalResponseCode.ApplyCheckReasonInvalid;
        }

        if (approveModel.getComment() == null || approveModel.getComment().length() == 0) {
            //驳回审批单必须要填写理由
            return GlobalResponseCode.ApplyRefuseMustWriteReason;
        }
        String applyId = approveModel.getApply_id();
        ApplyOrder apply = applyOrderMapper.selectByPrimaryKey(approveModel.getApply_id());
        if (apply == null) {
            return GlobalResponseCode.NotFound;
        }
        if (apply.getState() != null && apply.getState().intValue() == ApplyStatus.Overtime.getValue()) {
            //事中审批已超时
            return GlobalResponseCode.CenterApplyOverTime;
        }
        if (apply.getState() != null && apply.getState().intValue() == ApplyStatus.Backout.getValue()) {
            //事中审批已撤销
            return GlobalResponseCode.CenterApplyCancleOrder;
        }
        if (!apply.getCompanyId().equals(companyId)) {
            //申请单和当前人不在一个公司
            return GlobalResponseCode.ApplyCompanyCannotMatch;
        }
        if (apply.getState() != ApplyStatus.PendingAudit.getValue()) {
            //该单不是待审核,不能审批
            return GlobalResponseCode.ApplyStatusNotPendingAudit;
        }
        if (!apply.getApproverId().equals(userId)) {
            //不是当前人可以处理的审批单
            return GlobalResponseCode.NoAuth;
        }
        ApplyStatus finalStatus;
        ApplyLogAction action;
        String receiverId; //下一个审批人/通知人
        String approverId; //审批人
        Integer logId = null;
        List<String> skippedApprverIds = new ArrayList<>();//固定审批流跳过的审批人
        Date now = new Date();
        //拒绝
        List<ApplyFlowUserItem> applyFlowUserItems = applyFlowService.getFlowItemsByApplyId(applyId);
        for (ApplyFlowUserItem applyFlowUserItem : applyFlowUserItems) {
            if (ValueTool.areEqual(applyFlowUserItem.getStatus(), ApplyFlowUserItemStatus.PendingAudit.getValue())
                    && applyFlowUserItem.getUserId().equals(userId)) {
                applyFlowService.setApplyFlowUserItemStatus(applyFlowUserItem.getId(), ApplyFlowUserItemStatus.Refuse);
            }
        }
        finalStatus = ApplyStatus.Return;
        action = ApplyLogAction.Refuse;
        receiverId = apply.getEmployeeId();
        approverId = userId;
        //处理审批日志和审批单的状态
        logId = sloveApplyLog(applyId, receiverId, approverId, finalStatus, now, ip, userId, action, approveModel);
        // 同步日志
        applyV2Service.syncLogOnFinishProcess(applyId, companyId);
        setApplyApproverAndPushMessage(apply, userId, ip, finalStatus, receiverId, approverId, approveModel.getComment(), action, skippedApprverIds, logId);
        return GlobalResponseCode.Success;
    }

    /**
     * 撤销
     *
     * @param userId
     * @param companyId
     * @param ip
     * @return
     */
    @Override
    //@Transactional(value = "fenbeitong")
    public GlobalResponseCode revoke(String userId, String companyId, ApplyRevokeContract data, String ip, String clientVersion, String source) {
        FlowCheckUtil.check(companyId, clientVersion, source);
        ApplyOrder order = getApplyOrderModelByIdAndUserIdAndCompanyId(data.getId(), userId, companyId, null);
        if (order == null) {
            return GlobalResponseCode.NotFound;
        }
        if (order.getState() != ApplyStatus.PendingAudit.getValue()) {
            return GlobalResponseCode.ApplyStatusNotPendingAudit;
        }
        Date now = new Date();
        ApplyLogAction action = ApplyLogAction.Revoke;
        int logSort = 0;
        List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectListByCreateTimeDesc(data.getId());
        if (!CollectionUtils.isEmpty(applyOrderLogList)) {
            Integer sort = applyOrderLogList.get(0).getSort();
            if (sort != null) {
                logSort = sort + 100;
            }
        }
        //插入日志
        ApplyOrderLog log = new ApplyOrderLog();
        log.setApplyOrderId(data.getId());
        log.setRootApplyOrderId(data.getId());
        log.setCreateTime(now);
        log.setIp(ip);
        log.setSponsorId(userId);
        log.setReceiverId(userId);
        log.setCheckReason(null);
        log.setAction(action.getValue());
        log.setSort(logSort);
        applyOrderLogMapper.insertSelective(log);
        applyOrderExtMapper.setCenterStatus(data.getId(), ApplyStatus.Backout.getValue(), now, log.getId(), 12);
        // 同步日志
        applyV2Service.syncLogOnFinishProcess(data.getId(), companyId);
        return GlobalResponseCode.Success;
    }

    /**
     * 转发审批单
     *
     * @param approveModel
     * @param userId
     * @param ip
     * @return
     */
    @Override
    //@Transactional(value = "saas")
    public GlobalResponseCode forward(ApplyApproveContract approveModel, String userId, String companyId, String ip, String clientVersion, String source) throws SaasException {
        FlowCheckUtil.check(companyId, clientVersion, source);
        if (approveModel == null) {
            return GlobalResponseCode.ApplyIsNull;
        }
        if (StringTool.isNullOrEmpty(approveModel.getApply_id())) {
            return GlobalResponseCode.ApplyIdInvalid;
        }
        if (approveModel.getComment() != null && approveModel.getComment().length() > 200) {
            return GlobalResponseCode.ApplyCheckReasonInvalid;
        }
        int status = approveModel.getStatus();
        String nextApproverId = approveModel.getApprover_id();
        if (StringTool.isNullOrEmpty(nextApproverId)) {
            //转交需要一个承接人
            return GlobalResponseCode.ApplyApproveIdInvalid;
        } else if (nextApproverId.equals(userId)) {
            return GlobalResponseCode.ApplyTransferNotSelf;
        }
        String applyId = approveModel.getApply_id();
        ApplyOrder apply = applyOrderMapper.selectByPrimaryKey(approveModel.getApply_id());
        if (apply == null) {
            return GlobalResponseCode.NotFound;
        }
        boolean isFlow = (ValueTool.areEqual(apply.getFlowType(), CompanyApplyType.Flow.getValue()) || ValueTool.areEqual(apply.getFlowType(), CompanyApplyType.CONDITIONAL.getValue()));
        if (!apply.getCompanyId().equals(companyId)) {
            //申请单和当前人不在一个公司
            return GlobalResponseCode.ApplyCompanyCannotMatch;
        }
        if (apply.getState() != ApplyStatus.PendingAudit.getValue()) {
            //该单不是待审核或者转交状态,不能审批
            return GlobalResponseCode.ApplyStatusNotPendingAudit;
        }
        if (apply.getEmployeeId().equals(approveModel.getApprover_id())) {
            return GlobalResponseCode.ApplyApproverCannotBeSelf;
        }
        Boolean exist = userService.isUserInCompany(approveModel.getApprover_id(), apply.getCompanyId());
        if (!exist) {
            return GlobalResponseCode.ApplyCompanyCannotMatch;
        }
        if (!apply.getApproverId().equals(userId)) {
            //不是当前人可以处理的审批单
            return GlobalResponseCode.NoAuth;
        }
        ApplyStatus finalStatus;
        ApplyLogAction action;
        String receiverId; //下一个审批人/通知人
        String approverId; //审批人
        Integer logId = null;
        List<String> skippedApprverIds = new ArrayList<>();//固定审批流跳过的审批人
        Date now = new Date();
        //转交
        if (isFlow) {
            List<ApplyFlowUserItem> applyFlowUserItems = applyFlowService.getFlowItemsByApplyId(applyId);
            if (applyFlowUserItems != null && applyFlowUserItems.size() > 0) {
                Optional<ApplyFlowUserItem> optionalApplyFlowUserItem = applyFlowUserItems.stream().filter(m -> m.getUserId().equals(userId) &&
                        ValueTool.areEqual(m.getStatus(), ApplyFlowUserItemStatus.PendingAudit.getValue())).findAny();
                if (optionalApplyFlowUserItem.isPresent()) {
                    ApplyFlowUserItem applyFlowUserItem = optionalApplyFlowUserItem.get();
                    //当前审批人将此单转交给另一人，将此item状态置为Transfered
                    applyFlowService.setApplyFlowUserItemStatus(applyFlowUserItem.getId(), ApplyFlowUserItemStatus.Transfered);
                }
            }
        }
        finalStatus = ApplyStatus.PendingAudit;
        action = ApplyLogAction.Forward;
        receiverId = approveModel.getApprover_id();
        approverId = receiverId;

        //查询审核中的审批日志
        List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectApproveUser(applyId, ApplyLogAction.Approval.getValue());
        //有日志数据情况下修改操作无日志数据情况下插入操作
        if (!CollectionUtils.isEmpty(applyOrderLogList)) {
            applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Forward.getValue(), applyOrderLogList.get(0).getId(), receiverId, approveModel.getComment(), new Date(), approveModel.getPrice());
            Integer sort = applyOrderLogList.get(0).getSort() + 1;
            ApplyOrderLog log = new ApplyOrderLog();
            log.setApplyOrderId(applyId);
            log.setIp(ip);
            log.setSponsorId(receiverId);
            log.setReceiverId("");
            log.setCheckReason(null);
            log.setAction(ApplyLogAction.Approval.getValue());
            log.setSort(sort);
            log.setRootApplyOrderId(applyId);
            applyOrderLogMapper.insertSelective(log);
            logId = log.getId();
            setApproverAndStatus(applyId, approverId, finalStatus, now, log.getId(), approveModel);
        } else {
            writeLog(applyId, now, ip, userId, receiverId, approveModel.getComment(), action);
            ApplyOrderLog logApprove = new ApplyOrderLog();
            logApprove.setApplyOrderId(applyId);
            logApprove.setIp(ip);
            logApprove.setSponsorId(receiverId);
            logApprove.setReceiverId("");
            logApprove.setCheckReason(null);
            logApprove.setAction(ApplyLogAction.Approval.getValue());
            logApprove.setSort(0);
            logApprove.setRootApplyOrderId(applyId);
            applyOrderLogMapper.insertSelective(logApprove);
            logId = logApprove.getId();
            setApproverAndStatus(applyId, approverId, finalStatus, now, logId, approveModel);
        }
        setApplyApproverAndPushMessage(apply, userId, ip, finalStatus, receiverId, approverId, approveModel.getComment(), action, skippedApprverIds, logId);
        return GlobalResponseCode.Success;
    }

    /**
     * 虚拟卡有效性校验
     *
     * @param companyId
     * @param employeeId
     */
    private GlobalResponseCode bankIndividualValid(String companyId, String employeeId, BigDecimal totalPrice, Boolean forceSumbit, String costAttributionId, Integer costAttributionCategory) {
        //员工账号被禁用
        CommonAuthDto commonAuthDto = iCommonService.queryCompanyEmployeeStatus(companyId, employeeId);
        if (commonAuthDto != null) {
            Integer employeeStatus = commonAuthDto.getEmployeeStatus();
            if (EmployeeStatus.INACTIVE.getKey() == employeeStatus) {
                return GlobalResponseCode.ApplyEmployeeInactive;
            }
        }
        //员工个人虚拟卡被禁用
        BankSearchCardDetailRespRpcDTO bankSearchCardDetailRespDTO = iBankCardRpcService.queryByCompanyInfo(companyId, employeeId, BankNameEnum.XWBANK.getCode());
        if (bankSearchCardDetailRespDTO != null) {
            Integer cardStatus = bankSearchCardDetailRespDTO.getCardStatus();
            if (BankCardStatus.DISABLE.getKey() == cardStatus) {
                return GlobalResponseCode.ApplyEmployeeBankIndividualInactive;
            }
        }
        //员工被删除
        if (StringUtils.isBlank(companyId)) {
            return GlobalResponseCode.ApplyEmployeeDelete;
        }
        //企业余额不足
        AccountSubFindReqRPCDTO accountSubFindDTO = new AccountSubFindReqRPCDTO();
        accountSubFindDTO.setCompanyId(companyId);
        accountSubFindDTO.setAccountSubType(FundAccountSubType.BANK_VIRTUAL_ACCOUNT.getKey());
        AccountSubFindRespRPCDTO account = iAccountSubService.queryAccountSubInfo(accountSubFindDTO);
        if (ObjUtils.isNotEmpty(account)) {
            if (BigDecimalUtils.fen2yuan(account.getBalance()).compareTo(totalPrice) < 0) {
                return GlobalResponseCode.ApplyCompanyAccount;
            }
        }
        if (iMessageSetupService.queryBudgetNewSwitch(companyId) == 0){
            //个人预算
            BizBudgetSettingAndUseContract bizBudgetSettingAndUsePersonContract = iBudgetService.queryBudgetSettingAndUse(companyId, SaasBudgetConstant.BUDGET_TYPE_EMPLOYEE, employeeId, OrderCategory.BankIndividual.getKey());
            if (bizBudgetSettingAndUsePersonContract != null) {
                BigDecimal budgetLimit = bizBudgetSettingAndUsePersonContract.getAmountLimit().setScale(2, BigDecimal.ROUND_HALF_UP);
                Integer overLimioverLimitControltControl = bizBudgetSettingAndUsePersonContract.getOverLimitControl();
                if (budgetLimit.compareTo(BigDecimal.valueOf(0)) != -1) {
                    //获取个人差旅预算
                    BigDecimal useAmount = ObjUtils.ifNull(bizBudgetSettingAndUsePersonContract.getAmountUse(), BigDecimal.ZERO);
                    BigDecimal totalAmount = totalPrice.add(useAmount);
                    if (budgetLimit.doubleValue() >= 0 && totalAmount.compareTo(budgetLimit) > 0) {
                        if ((overLimioverLimitControltControl.equals(1) && !forceSumbit)) {
                            return GlobalResponseCode.ApplyPersonBudgetForceSumbit;
                        }
                        if (overLimioverLimitControltControl.equals(2)) {
                            return GlobalResponseCode.ApplyPersonBudget;
                        }
                    }
                }
            }
            //部门预算
            if (costAttributionCategory.equals(CostAttributionCategory.Dept.getKey())) {
                List<BudgetCondItem> budgetCondItemList = Lists.newArrayList();
                BudgetCondItem orgBudgetCondItem = new BudgetCondItem();
                orgBudgetCondItem.setBudgetType(BudgetType.Department.getValue());
                orgBudgetCondItem.setItemId(costAttributionId);
                budgetCondItemList.add(orgBudgetCondItem);
                OrgUnitResult orgUnitResult = iOrgUnitService.queryOrgUnitAndParentUnit(companyId, costAttributionId);
                List<Map<String, Object>> parentDeptList = orgUnitResult.getParent_dept_list();
                for (Map<String, Object> map : parentDeptList) {
                    BudgetCondItem orgBudgetCondItemInfo = new BudgetCondItem();
                    orgBudgetCondItemInfo.setBudgetType(BudgetType.Department.getValue());
                    orgBudgetCondItemInfo.setItemId(ObjUtils.toString(map.get("id")));
                    budgetCondItemList.add(orgBudgetCondItemInfo);
                }
                List<BizBudgetSettingAndUseContract> budgetSettingAndUseList = iBudgetService.queryBudgetSettingAndUseList(companyId, budgetCondItemList, OrderCategory.BankIndividual.getKey());
                if (CollectionUtils.isNotEmpty(budgetSettingAndUseList)) {
                    for (BizBudgetSettingAndUseContract bizBudgetSettingAndUseContract : budgetSettingAndUseList) {
                        BigDecimal budgetLimit = bizBudgetSettingAndUseContract.getAmountLimit().setScale(2, BigDecimal.ROUND_HALF_UP);
                        Integer overLimioverLimitControltControl = bizBudgetSettingAndUseContract.getOverLimitControl();
                        if (budgetLimit.compareTo(BigDecimal.valueOf(0)) != -1) {
                            //获取部门差旅预算
                            BigDecimal useAmount = ObjUtils.ifNull(bizBudgetSettingAndUseContract.getAmountUse(), BigDecimal.ZERO);
                            BigDecimal totalAmount = totalPrice.add(useAmount);
                            if (budgetLimit.doubleValue() >= 0 && totalAmount.compareTo(budgetLimit) > 0) {
                                if ((overLimioverLimitControltControl.equals(1) && !forceSumbit)) {
                                    return GlobalResponseCode.ApplyDeptBudgetForceSumbit;
                                }
                                if (overLimioverLimitControltControl.equals(2)) {
                                    return GlobalResponseCode.ApplyDeptBudget;
                                }
                            }
                        }
                    }
                }
            }
            //项目预算
            if (costAttributionCategory.equals(CostAttributionCategory.CostCenter.getKey())) {
                BizBudgetSettingAndUseContract bizBudgetSettingAndUseCenterContract = iBudgetService.queryBudgetSettingAndUse(companyId, SaasBudgetConstant.BUDGET_TYPE_COST_CENTER, costAttributionId, OrderCategory.BankIndividual.getKey());
                if (bizBudgetSettingAndUseCenterContract != null) {
                    BigDecimal budgetLimit = bizBudgetSettingAndUseCenterContract.getAmountLimit().setScale(2, BigDecimal.ROUND_HALF_UP);
                    Integer overLimioverLimitControltControl = bizBudgetSettingAndUseCenterContract.getOverLimitControl();
                    if (budgetLimit.compareTo(BigDecimal.valueOf(0)) != -1) {
                        //获取项目差旅预算
                        BigDecimal useAmount = ObjUtils.ifNull(bizBudgetSettingAndUseCenterContract.getAmountUse(), BigDecimal.ZERO);
                        BigDecimal totalAmount = totalPrice.add(useAmount);
                        if (budgetLimit.doubleValue() >= 0 && totalAmount.compareTo(budgetLimit) > 0) {
                            if ((overLimioverLimitControltControl.equals(1) && !forceSumbit)) {
                                return GlobalResponseCode.ApplyCostCenterBudgetForceSumbit;
                            }
                            if (overLimioverLimitControltControl.equals(2)) {
                                return GlobalResponseCode.ApplyCostCenterBudget;
                            }
                        }
                    }
                }
            }
        }
        //基础版套餐
        Map<String, Boolean> mBankIndividual = iPrivilegeService.queryFunctionMoudle(companyId, "m_bank_individual");
        if (ObjUtils.isNotEmpty(mBankIndividual)) {
            Boolean banIndividual = mBankIndividual.get("m_bank_individual");
            if (!banIndividual) {
                return GlobalResponseCode.ApplyCompanyPackage;
            }
        }
        return GlobalResponseCode.Success;
    }


}

