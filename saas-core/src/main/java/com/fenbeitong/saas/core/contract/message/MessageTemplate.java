package com.fenbeitong.saas.core.contract.message;

/**
 * 消息模板
 *
 * <AUTHOR>
 * @date 2021/9/15
 */
public interface MessageTemplate {
//    /**
//     * 国内机票：出票成功
//     */
//    String AirOrderTicketSuccess = "您预订的{0}，{1}，预计{2}起飞，预计{3}到达，{4}航班，票号: {5}，乘机人：{6}。请提前两个小时到达机场，祝您旅途愉快！";
//
//    /**
//     * 国内机票：出票失败
//     */
//    String AirOrderTicketFail = "您预订的{0}，{1}，预计{2}起飞，预计{3}到达，{4}航班，乘机人：{5}，出票失败，请您重新预订。";
//
//    /**
//     * 国内机票：改签成功
//     */
//    String AirOrderChangedSuccess = "您预订的{0}，{1}航班，已更改为{2}航班，{3}，预计{4}起飞，预计{5}到达，乘机人：{6}，票号：{7}。";
//
//    /**
//     * 国内机票：改签失败
//     */
//    String AirOrderChangedFail = "您预订的{0}，{1}，预计{2}起飞，预计{3}到达，{4}航班，乘机人：{5}，票号：{6}，改签失败。";
//
//    /**
//     * 国内机票：退票成功
//     */
//    String AirOrderRefundSuccess = "您预订的{0}，{1}，预计{2}起飞，预计{3}到达，{4}票号： {5}，乘机人：{6} ，已退票成功。";
//
//    /**
//     * 国内机票：退票失败
//     */
//    String AirOrderRefundFail = "您预订的{0}，{1}，预计{2}起飞，预计{3}到达，{4}航班，乘机人：{5}，票号: {6}，退票失败。";
//
//    /**
//     * 国际机票：退票成功
//     */
//    String IntlAirOrderRefundSuccess = "您预订的{0}，{1}，预计{2}起飞，预计{3}到达，{4}，乘机人：{5} ,已退票成功。";
//
//    /**
//     * 国际机票：退票失败
//     */
//    String IntlAirOrderRefundFail = "您预订的{0}，{1}，预计{2}起飞，预计{3}到达，{4}航班，乘机人：{5}，退票失败。";
//
//    /**
//     * 国际机票：改签成功
//     */
//    String IntlAirOrderChangedSuccess = "您预订的{0}，{1}航班，已更改为{2}航班，{3}，预计{4}起飞，预计{5}到达，乘机人：{6}。";
//
//    /**
//     * 国际机票：出票成功
//     */
//    String IntlAirOrderTicketSuccess = "您预订的{0}，{1}，预计{2}起飞，预计{3}到达，{4}，乘机人：{5}。";
//
//    /**
//     * 用车：订单超时标题
//     */
//    String TaxiOrderTimeoutTile = "用车-订单已关闭";
//
//    /**
//     * 用车：订单超时
//     */
//    String TaxiOrderTimeout = "抱歉，您从{0}到{1}的用车订单，超时无供应商接单已经关闭。";
//
//    /**
//     * 用车：司机已接单标题
//     */
//    String TaxiOrderDispatchedAndPickingTitle = "用车-司机已接单";
//
//    /**
//     * 用车：司机已接单（派单成功、等待接驾）
//     */
//    String TaxiOrderDispatchedAndPicking = "您前往{0}的行程，司机已接单，车型：{1}，司机：{2}，{3}。";
//
//    /**
//     * 用车：司机取消标题
//     */
//    String TaxiOrderDriverCancelTitle = "用车-订单取消";
//
//    /**
//     * 用车：司机取消
//     */
//    String TaxiOrderDriverCancel = "抱歉，您从{0}至{1}的用车订单已被司机取消。";
//
//    /**
//     * 用车：司机已到达标题
//     */
//    String TaxiOrderDriverArrivalTitle = "用车-司机已到达";
//
//    /**
//     * 用车：司机已到达
//     */
//    String TaxiOrderDriverArrival = "司机已到达，车型：{0}，司机：{1}，{2}，请尽快上车。";
//
//    /**
//     * 用车：行程已结束标题
//     */
//    String TaxiOrderFinishTtile = "用车-订单已完成";
//
//    /**
//     * 用车：行程已结束
//     */
//    String TaxiOrderFinish = "您前往{0}的用车行程已结束，费用{1}元。";
//
//    /**
//     * 用车：员工待支付
//     */
//    String TaxiOrderPersonalWaitPay = "您下单前往{0}的用车行程已结束，费用为{1}，已付款{2}，请支付剩余车费";
//
//    /**
//     * 用车：员工待支付标题
//     */
//    String TaxiOrderPersonalWaitPayTitle = "用车-订单待支付";
//
//    /**
//     * 火车：占座失败
//     */
//    String TrainOrderBookErr = "很抱歉，您预订的{0}，{1}次，车票占座失败，可登录分贝通重新购票。";
//
//    /**
//     * 火车：待支付
//     */
//    String TrainOrderUnpaid = "车票待支付：您预订的{0}，{1}次，请及时完成订单支付。";
//
//    /**
//     * 火车：改签待支付
//     */
//    String TrainOrderEndorseBooked = "车票待支付：您要改签的{0}，{1}次，请及时完成订单支付";
//
//    /**
//     * 火车：出票成功
//     */
//    String TrainOrderTicketed = "您预订的{0}，{1}次，发车时间：{2}，乘车人：{3}，{4}，{5}，取票号：{6}。";
//
//    /**
//     * 火车：出票失败
//     */
//    String TrainOrderTicketErr = "您预订的{0}，{1}，{2}列车，乘车人：{3}，座席：{4}，出票失败，请您重新预订。";
//
//    /**
//     * 火车：改签成功
//     */
//    String TrainOrderEndorseOK = "新票信息：{0}，{1}次，发车时间{2}，乘车人：{3}，{4}，{5}，取票号{6}。";
//
//    /**
//     * 火车：改签失败
//     */
//    String TrainOrderEndorseErr = "您预订的{0}，{1}，{2}列车，乘车人：{3}，座席：{4}，票号：{5}，改签为{6}，{7}，{8}列车，座席：{9}，改签失败。";
//
//    /**
//     * 火车：退票成功
//     */
//    String TrainOrderRefundOK = "您预订的{0}，{1}次列车，发车时间：{2}，乘车人：{3}，退票成功。";
//
//    /**
//     * 火车：退票失败
//     */
//    String TrainOrderRefundFailed = "您预订的{0}，{1}，{2}列车，乘车人：{3}，座席：{4}，票号：{5}，退票失败。";


}
