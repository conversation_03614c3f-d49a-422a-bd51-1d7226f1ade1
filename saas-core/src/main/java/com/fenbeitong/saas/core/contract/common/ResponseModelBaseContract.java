package com.fenbeitong.saas.core.contract.common;

import com.fenbeitong.saas.core.contract.city.VendorCityInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * Created by xiabin on 2017/3/1.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ResponseModelBaseContract {
    private int code;
    private String msg;

    private List<VendorCityInfo> data;
}
