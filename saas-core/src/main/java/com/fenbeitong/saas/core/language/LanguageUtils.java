package com.fenbeitong.saas.core.language;

import com.beust.jcommander.internal.Maps;
import com.fenbeitong.finhub.common.constant.MultiLanguageEnum;
import com.finhub.framework.i18n.manager.MessageSourceManager;
import com.luastar.swift.base.utils.StrUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/12/13 14:30
 */
@Slf4j
public class LanguageUtils {

    /**
     * 获取替换后的语言文案
     * @param key 文案编码
     * @param args 参数
     * @return
     */
    public static Map<String, String> getFullMessage(String key, Object... args) {
        Map<String, String> map = Maps.newHashMap();
        if(StringUtils.isNoneBlank(key)){
            try {
                String zhMsg = MessageSourceManager.me().getMessage(key, MultiLanguageEnum.ZH_CN.getCode());
                map.put(MultiLanguageEnum.ZH_CN.getCode(), StrUtils.formatString(zhMsg, args));
            }catch (Exception e){
                log.info("zh key is not exist : {}", key);
            }
            try{
                String enMsg = MessageSourceManager.me().getMessage(key, MultiLanguageEnum.EN_US.getCode());
                map.put(MultiLanguageEnum.EN_US.getCode(), StrUtils.formatString(enMsg, args));
            }catch (Exception e){
                log.info("en key is not exist : {}" , key);
            }
        }
        return map;
    }

}
