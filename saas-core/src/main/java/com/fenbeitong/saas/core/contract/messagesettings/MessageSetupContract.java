package com.fenbeitong.saas.core.contract.messagesettings;

import com.fenbeitong.saas.core.contract.messagesettings.inner.*;

/**
 * Created by zhangkai on 2017/6/2.
 */
public class MessageSetupContract {

    /**
     * 消费信息
     */
    private ConsumeInfo consume_info;
    /**
     * 大额异动
     */
    private LargeOver large_over;
    /**
     * 分析报告
     */
    private AnalysisReport analysis_report;
    /**
     * 余额提醒
     */
    private BalanceRemind balance_remind;
    /**
     * 充值结果
     */
    private RechargeResult recharge_result;
    /**
     * 分贝券发放失败
     */
    private CouponSendFail coupon_send_fail;

    /**
     * 虚拟卡未核销通知
     */
    private  VirtualCardWriteOffRemind virtual_card_write_off_remind;

    /**
     * 备用金到期通知
     */
    private PettyExpireRemind petty_expire_remind;

    /**
     * 额度回收通知
     */
    private CreditRefundRemind credit_refund_remind;

    private CreditRefundFailedRemind credit_refund_failed_remind;

    private  OverseaCreditFailedRemind oversea_credit_fail_remind;

    /**
     * 备用金到期通知
     */
    private VirtualCardNoTradeRemind virtual_card_no_trade_remind;


    private  OverseaLargeOverRemind oversea_large_over_remind;
    private  OverseaConsumeInfoRemind oversea_consume_info_remind;



    public InvoiceRedCheck getInvoice_red_check() {
        return invoice_red_check;
    }

    public void setInvoice_red_check(InvoiceRedCheck invoice_red_check) {
        this.invoice_red_check = invoice_red_check;
    }

    /**
     *
     *发票红冲状态
     */
    private InvoiceRedCheck invoice_red_check;

    /**
     * 电子档案四性检测
     */
    private ArchiveDetection archive_detection;



    public VirtualCardWriteOffRemind getVirtual_card_write_off_remind() {
        return virtual_card_write_off_remind;
    }

    public void setVirtual_card_write_off_remind(VirtualCardWriteOffRemind virtual_card_write_off_remind) {
        this.virtual_card_write_off_remind = virtual_card_write_off_remind;
    }


    public ConsumeInfo getConsume_info() {
        return consume_info;
    }

    public void setConsume_info(ConsumeInfo consume_info) {
        this.consume_info = consume_info;
    }

    public LargeOver getLarge_over() {
        return large_over;
    }

    public void setLarge_over(LargeOver large_over) {
        this.large_over = large_over;
    }

    public AnalysisReport getAnalysis_report() {
        return analysis_report;
    }

    public void setAnalysis_report(AnalysisReport analysis_report) {
        this.analysis_report = analysis_report;
    }

    public BalanceRemind getBalance_remind() {
        return balance_remind;
    }

    public void setBalance_remind(BalanceRemind balance_remind) {
        this.balance_remind = balance_remind;
    }

    public RechargeResult getRecharge_result() {
        return recharge_result;
    }

    public void setRecharge_result(RechargeResult recharge_result) {
        this.recharge_result = recharge_result;
    }

    public CouponSendFail getCoupon_send_fail() {
        return coupon_send_fail;
    }

    public void setCoupon_send_fail(CouponSendFail coupon_send_fail) {
        this.coupon_send_fail = coupon_send_fail;
    }

    public PettyExpireRemind getPetty_expire_remind() {
        return petty_expire_remind;
    }

    public void setPetty_expire_remind(PettyExpireRemind pettyExpireRemind) {
        this.petty_expire_remind = pettyExpireRemind;
    }
    public PackageServiceFlowvalveRemind getPackageServiceFlowvalveRemind() {
        return packageServiceFlowvalveRemind;
    }

    public void setPackageServiceFlowvalveRemind(PackageServiceFlowvalveRemind packageServiceFlowvalveRemind) {
        this.packageServiceFlowvalveRemind = packageServiceFlowvalveRemind;
    }

    private PackageServiceFlowvalveRemind packageServiceFlowvalveRemind;

    public VirtualCardNoTradeRemind getVirtual_card_no_trade_remind() {
        return virtual_card_no_trade_remind;
    }

    public void setVirtual_card_no_trade_remind(VirtualCardNoTradeRemind bank_card_no_trade_remind) {
        this.virtual_card_no_trade_remind = bank_card_no_trade_remind;
    }

    public ArchiveDetection getArchive_detection() {
        return archive_detection;
    }

    public void setArchive_detection(ArchiveDetection archive_detection) {
        this.archive_detection = archive_detection;
    }

    public CreditRefundRemind getCredit_refund_remind() {
        return credit_refund_remind;
    }

    public void setCredit_refund_remind(CreditRefundRemind credit_refund_remind) {
        this.credit_refund_remind = credit_refund_remind;
    }

    public CreditRefundFailedRemind getCredit_refund_failed_remind() {
        return credit_refund_failed_remind;
    }

    public void setCredit_refund_failed_remind(CreditRefundFailedRemind credit_refund_failed_remind) {
        this.credit_refund_failed_remind = credit_refund_failed_remind;
    }

    public OverseaCreditFailedRemind getOversea_credit_fail_remind() {
        return oversea_credit_fail_remind;
    }

    public void setOversea_credit_fail_remind(OverseaCreditFailedRemind oversea_credit_fail_remind) {
        this.oversea_credit_fail_remind = oversea_credit_fail_remind;
    }

    public OverseaLargeOverRemind getOversea_large_over_remind() {
        return oversea_large_over_remind;
    }

    public void setOversea_large_over_remind(OverseaLargeOverRemind oversea_large_over_remind) {
        this.oversea_large_over_remind = oversea_large_over_remind;
    }

    public OverseaConsumeInfoRemind getOversea_consume_info_remind() {
        return oversea_consume_info_remind;
    }

    public void setOversea_consume_info_remind(OverseaConsumeInfoRemind oversea_consume_info_remind) {
        this.oversea_consume_info_remind = oversea_consume_info_remind;
    }
}
