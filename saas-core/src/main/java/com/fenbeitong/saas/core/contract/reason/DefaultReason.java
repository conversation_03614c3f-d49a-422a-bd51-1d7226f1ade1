package com.fenbeitong.saas.core.contract.reason;

import com.fenbeitong.saas.core.model.enums.reason.ReasonCategory;
import com.fenbeitong.saas.core.model.enums.reason.ReasonType;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/11
 */
public class DefaultReason {

    /**
     * 事由类型
     * @see ReasonType
     */
    private Integer reason_type;

    /**
     * 事由大类型
     * @see ReasonCategory
     */
    private Integer reason_category;

    /**
     * 默认事由项
     */
    private List<String> default_reason_items;

    public Integer getReason_type() {
        return reason_type;
    }

    public void setReason_type(Integer reason_type) {
        this.reason_type = reason_type;
    }

    public Integer getReason_category() {
        return reason_category;
    }

    public void setReason_category(Integer reason_category) {
        this.reason_category = reason_category;
    }

    public List<String> getDefault_reason_items() {
        return default_reason_items;
    }

    public void setDefault_reason_items(List<String> default_reason_items) {
        this.default_reason_items = default_reason_items;
    }
}
