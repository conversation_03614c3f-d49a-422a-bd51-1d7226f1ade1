package com.fenbeitong.saas.core.contract.messagesettings;


import lombok.Data;

import java.util.List;

/**
 * Created by w<PERSON><PERSON> on 2018/6/25.
 */
@Data
public class ApplySetupContract {

    /**
     * 预估费用 0-不显示 1-显示且必填
     */
    private Integer whether_trip_apply_budget;

    /**
     * 差旅申请单 0-未勾选(非必填) 1-勾选(必填)
     */
    private Integer apply_reason_chailv;

    /**
     * 用车申请单 0-未勾选(非必填) 1-勾选(必填)
     */
    private Integer apply_reason_taxi;

    /**
     * 用餐申请单 0-未勾选(非必填) 1-勾选(必填)
     */
    private Integer apply_reason_dinner;

    /**
     * 虚拟卡申请单 0-未勾选(非必填) 1-勾选(必填)
     */
    private Integer apply_reason_bank_individual;

    /**
     * 事由补充内容 0-未勾选(非必填) 1-勾选(必填)
     */
    private Integer apply_reason_desc;

    private Integer whether_go_and_back;

    private Integer whether_fbb_manage;

    private Integer whether_fbb_type;

    private Integer whether_travel_statistics;

    //费用归属类型 1-部门 2-项目 3-部门或项目 4-部门和项目
    private Integer cost_attribution_category;

    /**
     * 差旅变更申请单 0-未勾选(非必填) 1-勾选(必填)
     */
    private Integer apply_reason_chailv_change;

    /**
     * 分贝券申请单 0-未勾选(非必填) 1-勾选(必填)
     */
    private Integer apply_reason_coupon;

    //费用归属类型为部门是是否默认员工所在部门 0.不勾选 1.勾选
    private Integer cost_attribution_deptment_default;

    //每次提交订单或申请时均需填写费用归属 0-不勾选 1-勾选
    private Integer cost_attribution_reset;

    //差旅统计是否必填 0-未勾选(非必填) 1-勾选(必填)
    private Integer whether_required;

    private Integer apply_reason_takeaway;

    //出发时间配置 0-精确时间（天） 1-范围时间
    private Integer apply_departure_date;

    //出行人控制 0-不勾选 1-勾选
    private Integer apply_pedestrians_control;

    //申请单次数限制 0-不限制 1-限制
    private Integer apply_count_limit;

    //审批设置（busi_code）
    private String busi_code;

    //审批设置功能点code
    private String item_code;

    private Integer item_value;

    private Integer apply_message_setting;

    private String notice_title;

    private String notice_content;

    private String notice_tips;

    //因私往返机票
    private Integer private_whether_go_and_back;

    // 差旅城市 1-显示且必填 2-显示非必填 3-不显示
    private Integer apply_trip_city;

    // 预估费用下单需校验 0-未勾选(否) 1-勾选(是)
    private Integer trip_apply_budget_check;
    // 预估费用下单需校验描述
    private String trip_apply_budget_check_desc;
    /**
     * 预估费用下单需校验描述国内机票
     */
    private String budget_check_air_desc;

    /**
     * 预估费用下单需校验描述酒店
     */
    private String budget_check_hotel_desc;

    /**
     * 预估费用下单需校验描述火车票
     */
    private String budget_check_train_desc;

    /**
     * 差旅费用归属  0：不展示，1：展示选填，2：展示必填
     */
    private Integer apply_attribution_category_travel;
    /**
     * 差旅申请单费用归属代入订单且不可修改
     * 是否勾选 0：未勾选，1：勾选
     */
    private Integer apply_attribution_category_modifiable_travel;
    /**
     * 差旅申请单费用归属代入订单且不可修改时返回描述
     *
     */
    private String apply_attribution_category_travel_desc;
    /**
     * 用车费用归属  0：不展示，1：展示选填，2：展示必填
     */
    private Integer apply_attribution_category_taxi;
    /**
     * 用车申请单费用归属代入订单且不可修改
     * 是否勾选 0：未勾选，1：勾选
     */
    private Integer apply_attribution_category_modifiable_taxi;
    /**
     * 用车申请单费用归属代入订单且不可修改时返回描述
     *
     */
    private String apply_attribution_category_taxi_desc;
    /**
     * 用餐费用归属  0：不展示，1：展示选填，2：展示必填
     */
    private Integer apply_attribution_category_meishi;
    /**
     * 用餐申请单费用归属代入订单且不可修改
     * 是否勾选 0：未勾选，1：勾选
     */
    private Integer apply_attribution_category_modifiable_meishi;
    /**
     * 用餐申请单费用归属代入订单且不可修改时返回描述
     *
     */
    private String apply_attribution_category_meishi_desc;
    /**
     * 外卖费用归属  0：不展示，1：展示选填，2：展示必填
     */
    private Integer apply_attribution_category_takeaway;

    /**
     * 外卖申请单费用归属代入订单且不可修改
     * 是否勾选 0：未勾选，1：勾选
     */
    private Integer apply_attribution_category_modifiable_takeaway;
    /**
     * 外卖申请单费用归属代入订单且不可修改时返回描述
     *
     */
    private String apply_attribution_category_takeaway_desc;

    //费用归属与审批流默认一致,是否勾选 0：未勾选，1：勾选
    private Integer apply_attribution_category_flag;

    /**
     * 付款申请单-合同配置 0：选填，1：必填，2：不显示
     *
     */
    private Integer apply_payment_contract;

    /**
     * 付款申请单-凭证配置 0：选填，1：必填，2：不显示
     *
     */
    private Integer apply_payment_proof;

    /**
     * 虚拟卡额度上传附件
     *
     */
    private Integer apply_bank_individual_attachment;

    private Integer apply_bank_individual_cost_category;

    /**
     * 通用配置-申请单详情展示配置 0-不展示 1-展示
     *
     */
    private Integer apply_show_project_code;

    /**
     * 付款申请单发票选项列表 1-已开发票 0-待开发票 2-无发票
     */
    private List<Integer> applyInvoiceOptionList;
    /**
     * 使用日期  0-精确时间 1-范围时间
     */
    private Integer apply_meishi_time_limit;
    /**
     * 使用次数  0-不限制 1-只可使用一次
     */
    private Integer apply_meishi_count_limit;
    /**
     * 用餐城市限制
     */
    private Integer apply_meishi_city_limit;
    /** 美食用餐费用 0：由员工填写， 1：按消费规则限制 */
    private Integer apply_meishi_fee_rule;
    /** 美食用餐时段 0：不展示， 1：必填*/
    private Integer apply_meishi_time_slot_limit;

    /**
     * 外卖费用 0：由员工填写， 1：按最高消费规则限制 2：按最低消费规则限制
     */
    private Integer apply_takeaway_fee_rule;
    /** 外卖-送餐时段 0：不展示， 1：必填*/
    private Integer apply_takeaway_time_slot_limit;

    /**
     * 差率行程形式 1-按行程填写申请单,2-仅填写城市、日期、出行方式等信息
     */
    private Integer apply_trip_type = 1;

    private List<Integer> apply_order_types;

    /**
     * 非行程预估金额描述文案
     */
    private String budget_check_multi_trip_desc;

    /**
     * 下载标识 0.不展示 1.展示
     */
    private Integer allow_apply_download;

    private Integer apply_attribution_category_mileage;

    private Integer cost_category_mileage;

    /**
     * 是否展示三方单号  0.不展示 1.展示
     */
    private Integer is_show_third_id = 0;

}
