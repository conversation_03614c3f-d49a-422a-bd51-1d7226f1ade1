package com.fenbeitong.saas.core.contract.reason;

/**
 * <AUTHOR>
 * @date 2019/12/10
 */
public class ApplyTripConfig {

    /**
     * apply_trip_city : 1
     * apply_departure_date : 0
     * whether_trip_apply_budget : 1
     * trip_apply_budget_check : 1
     * apply_pedestrians_control : 0
     * whether_travel_statistics : 1
     * whether_required : 1
     * apply_count_limit : 0
     */

    private Integer apply_trip_city = 1;
    private Integer apply_departure_date;
    private Integer whether_trip_apply_budget;
    private Integer trip_apply_budget_check;
    private Integer apply_pedestrians_control;
    private Integer whether_travel_statistics;
    private Integer whether_required;
    private Integer apply_count_limit;
    /**
     * 费用归属  0：不展示，1：展示选填，2：展示必填
     */
    private Integer apply_attribution_category;
    /**
     * 申请单费用归属代入订单且不可修改
     * 是否勾选 0：未勾选，1：勾选
     */
    private Integer apply_attribution_category_modifiable;

    public Integer getApply_trip_city() {
        return apply_trip_city;
    }

    public void setApply_trip_city(Integer apply_trip_city) {
        this.apply_trip_city = apply_trip_city;
    }

    public Integer getApply_departure_date() {
        return apply_departure_date;
    }

    public void setApply_departure_date(Integer apply_departure_date) {
        this.apply_departure_date = apply_departure_date;
    }

    public Integer getWhether_trip_apply_budget() {
        return whether_trip_apply_budget;
    }

    public void setWhether_trip_apply_budget(Integer whether_trip_apply_budget) {
        this.whether_trip_apply_budget = whether_trip_apply_budget;
    }

    public Integer getTrip_apply_budget_check() {
        return trip_apply_budget_check;
    }

    public void setTrip_apply_budget_check(Integer trip_apply_budget_check) {
        this.trip_apply_budget_check = trip_apply_budget_check;
    }

    public Integer getApply_pedestrians_control() {
        return apply_pedestrians_control;
    }

    public void setApply_pedestrians_control(Integer apply_pedestrians_control) {
        this.apply_pedestrians_control = apply_pedestrians_control;
    }

    public Integer getWhether_travel_statistics() {
        return whether_travel_statistics;
    }

    public void setWhether_travel_statistics(Integer whether_travel_statistics) {
        this.whether_travel_statistics = whether_travel_statistics;
    }

    public Integer getWhether_required() {
        return whether_required;
    }

    public void setWhether_required(Integer whether_required) {
        this.whether_required = whether_required;
    }

    public Integer getApply_count_limit() {
        return apply_count_limit;
    }

    public void setApply_count_limit(Integer apply_count_limit) {
        this.apply_count_limit = apply_count_limit;
    }

    public Integer getApply_attribution_category() {
        return apply_attribution_category;
    }

    public void setApply_attribution_category(Integer apply_attribution_category) {
        this.apply_attribution_category = apply_attribution_category;
    }

    public Integer getApply_attribution_category_modifiable() {
        return apply_attribution_category_modifiable;
    }

    public void setApply_attribution_category_modifiable(Integer apply_attribution_category_modifiable) {
        this.apply_attribution_category_modifiable = apply_attribution_category_modifiable;
    }
}
