package com.fenbeitong.saas.core.contract.assistant;

import com.fenbeitong.saas.api.model.assistant.AssistantRuleDTO;
import com.fenbeitong.saas.core.model.saasplus.AssistantRule;
import lombok.Data;

/**
 * <AUTHOR> 2022/4/12
 */
@Data
public class AssistantRuleContract {

    private String id;

    /**
     * 面向人群
     * {@link com.fenbeitong.saas.api.model.assistant.enums.CrowdEnum}
     */
    private Integer crowd;

    /**
     * 跳转时机
     * {@link com.fenbeitong.saas.api.model.assistant.enums.TimeTriggerEnum}
     */
    private Integer timeTrigger;

    /**
     * 当前页面
     * {@link com.fenbeitong.saas.api.model.assistant.enums.CurrentPageEnum}
     */
    private Integer currentPage;

    /**
     * 跳转类型
     * {@link com.fenbeitong.saas.api.model.assistant.enums.ForwardTypeEnum}
     */
    private Integer forwardType;

    /**
     * webapp跳转页面
     */
    private String forwardPage;

    /**
     * 企微 跳转页面
     */
    private String enterpriseForwardPage;

    /**
     * 钉钉 跳转页面
     */
    private String dingTalkForwardPage;

    /**
     * 操作人员id
     */
    private String operatorId;

    /**
     * 操作人员姓名
     */
    private String operatorName;

    /**
     * 提示语
     */
    private String noticeMessage;

    public static AssistantRuleContract fromModel(AssistantRule assistantRule) {
        AssistantRuleContract contract = new AssistantRuleContract();
        contract.setId(assistantRule.getId());
        contract.setCrowd(assistantRule.getCrowd());
        contract.setTimeTrigger(assistantRule.getTimeTrigger());
        contract.setCurrentPage(assistantRule.getCurrentPage());
        contract.setForwardType(assistantRule.getForwardType());
        contract.setForwardPage(assistantRule.getForwardPage());
        contract.setEnterpriseForwardPage(assistantRule.getEnterpriseForwardPage());
        contract.setDingTalkForwardPage(assistantRule.getDingTalkForwardPage());
        contract.setOperatorId(assistantRule.getOperatorId());
        contract.setOperatorName(assistantRule.getOperatorName());
        contract.setNoticeMessage(assistantRule.getNoticeMessage());
        return contract;
    }

    public static AssistantRuleContract fromModel(AssistantRuleDTO assistantRule) {
        AssistantRuleContract contract = new AssistantRuleContract();
        contract.setId(assistantRule.getId());
        contract.setCrowd(assistantRule.getCrowd());
        contract.setTimeTrigger(assistantRule.getTimeTrigger());
        contract.setCurrentPage(assistantRule.getCurrentPage());
        contract.setForwardType(assistantRule.getForwardType());
        contract.setForwardPage(assistantRule.getForwardPage());
        contract.setEnterpriseForwardPage(assistantRule.getEnterpriseForwardPage());
        contract.setDingTalkForwardPage(assistantRule.getDingTalkForwardPage());
        contract.setOperatorId(assistantRule.getOperatorId());
        contract.setOperatorName(assistantRule.getOperatorName());
        contract.setNoticeMessage(assistantRule.getNoticeMessage());
        return contract;
    }

    public AssistantRule toModel() {
        AssistantRule assistantRule = new AssistantRule();
        assistantRule.setId(getId());
        assistantRule.setCrowd(getCrowd());
        assistantRule.setTimeTrigger(getTimeTrigger());
        assistantRule.setCurrentPage(getCurrentPage());
        assistantRule.setForwardType(getForwardType());
        assistantRule.setForwardPage(getForwardPage());
        assistantRule.setEnterpriseForwardPage(getEnterpriseForwardPage());
        assistantRule.setDingTalkForwardPage(getDingTalkForwardPage());
        assistantRule.setOperatorId(getOperatorId());
        assistantRule.setOperatorName(getOperatorName());
        assistantRule.setNoticeMessage(getNoticeMessage());
        return assistantRule;
    }

    public AssistantRuleDTO toDTOModel() {
        AssistantRuleDTO assistantRule = new AssistantRuleDTO();
        assistantRule.setId(getId());
        assistantRule.setCrowd(getCrowd());
        assistantRule.setTimeTrigger(getTimeTrigger());
        assistantRule.setCurrentPage(getCurrentPage());
        assistantRule.setForwardType(getForwardType());
        assistantRule.setForwardPage(getForwardPage());
        assistantRule.setEnterpriseForwardPage(getEnterpriseForwardPage());
        assistantRule.setDingTalkForwardPage(getDingTalkForwardPage());
        assistantRule.setOperatorId(getOperatorId());
        assistantRule.setOperatorName(getOperatorName());
        assistantRule.setNoticeMessage(getNoticeMessage());
        return assistantRule;
    }

}
