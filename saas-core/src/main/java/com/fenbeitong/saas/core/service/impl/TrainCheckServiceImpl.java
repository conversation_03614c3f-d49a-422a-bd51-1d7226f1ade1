package com.fenbeitong.saas.core.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.finhub.common.constant.CategoryTypeEnum;
import com.fenbeitong.finhub.common.constant.CostAttributionScopeEnum;
import com.fenbeitong.finhub.common.constant.PayModelEnum;
import com.fenbeitong.finhub.common.utils.EventParams;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.saas.core.common.DynamicDataSourceDecision;
import com.fenbeitong.saas.core.common.base.SaasException;
import com.fenbeitong.saas.core.common.constant.GlobalResponseCode;
import com.fenbeitong.saas.core.common.constant.SaasApplyConstant;
import com.fenbeitong.saas.core.common.constant.SaasContentConstant;
import com.fenbeitong.saas.core.common.constant.SaasOrderThirdRuleConstant;
import com.fenbeitong.saas.core.contract.apply.ApplyThirdContract;
import com.fenbeitong.saas.core.contract.common.CostAttributionInfo;
import com.fenbeitong.saas.core.contract.messagesettings.CostAttrAndBudgetConf;
import com.fenbeitong.saas.core.contract.order.check.*;
import com.fenbeitong.saas.core.dao.fenbeitong.TrainInterceptRecordMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.TrainRuleMapper;
import com.fenbeitong.saas.core.language.CoreLanguage;
import com.fenbeitong.saas.core.model.enums.EmployeeStatusEnum;
import com.fenbeitong.saas.core.model.enums.OrderCategory;
import com.fenbeitong.saas.core.model.enums.message.BizType;
import com.fenbeitong.saas.core.model.enums.rule.*;
import com.fenbeitong.saas.core.model.fenbeitong.OrderCheckExt;
import com.fenbeitong.saas.core.model.fenbeitong.TrainInterceptRecord;
import com.fenbeitong.saas.core.model.fenbeitong.TrainRule;
import com.fenbeitong.saas.core.model.saas.ApplyRuleSetting;
import com.fenbeitong.saas.core.model.saas.MessageSetup;
import com.fenbeitong.saas.core.service.*;
import com.fenbeitong.saas.core.utils.tools.BigDecimalTool;
import com.fenbeitong.saas.core.utils.tools.DateTimeTool;
import com.fenbeitong.saas.core.utils.tools.HostPropertyConfigTool;
import com.fenbeitong.saas.core.utils.tools.IDTool;
import com.fenbeitong.saas.core.utils.tools.VersionTool;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyRuleDTO;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.fenbeitong.usercenter.api.model.enums.privilege.CodeTypeEnums;
import com.fenbeitong.usercenter.api.model.enums.privilege.CustomCodeType;
import com.fenbeitong.usercenter.api.model.po.rule.EmployeeTrainRule;
import com.fenbeitong.usercenter.api.service.company.ICompanyRuleService;
import com.fenbeitong.usercenter.api.service.privilege.IPrivilegeService;
import com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeTrainRuleService;
import com.google.common.collect.Lists;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.DateUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.StrUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * Created by mac on 18/6/23.
 */
@Service
public class TrainCheckServiceImpl implements ITrainCheckService {

    private static final Logger logger = LoggerFactory.getLogger(OrderCheckServiceImpl.class);

    private static final String URL_GET_APPROVE_DATA = HostPropertyConfigTool.HOST_OPENAPI + "/open/hgm/company/approve_type";

    @Autowired
    private IBaseOrganizationService baseOrganizationService;
    @Autowired
    private ICompanyRuleService iCompanyRuleService;
    @Autowired
    private IOrderCheckService iOrderCheckService;
    @Autowired
    private TrainRuleMapper trainRuleMapper;
    @Autowired
    private TrainInterceptRecordMapper trainInterceptRecordMapper;
    @Autowired
    private IApplyFlowV2Service iApplyFlowV2Service;
    @Autowired
    private IBaseEmployeeTrainRuleService iBaseEmployeeTrainRuleService;
    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyRuleSettingExtMapper applyRuleSettingExtMapper;
    @Autowired
    private IMessageSetupService iMessageSetupService;
    @Autowired
    private IPrivilegeService iPrivilegeService;

    /**
     * 校验火车订单规则
     *
     * @param reqContract
     * @return
     * @throws SaasException
     */
    @Override
    public TravelOnBusiOrderCheckResContract trainOrderCheck(TrainOrderCheckReqContract reqContract, String clientVersion) throws SaasException {
        DynamicDataSourceDecision.determineDataSource(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        // 返回结果
        TravelOnBusiOrderCheckResContract resContract = new TravelOnBusiOrderCheckResContract();
        // 校验权限问题
        TravelOnBusiOrderRuleCheckResult ruleCheckResult = trainOrderRuleCheckResult(reqContract, clientVersion);
        resContract.setErr_code(ruleCheckResult.getErrCode());
        resContract.setErr_msg(ruleCheckResult.getErrMsg());
        resContract.setExceed_msg(ruleCheckResult.getExceed_msg());
        resContract.setCost_attribution_scope(ruleCheckResult.getCost_attribution_scope());
        resContract.setBudget_cost_attr_type(ruleCheckResult.getBudget_cost_attr_type());
        resContract.setPay_model(ruleCheckResult.getPayModel());
        resContract.setCompany_pay_price(ruleCheckResult.getCompanyPayPrice());
        resContract.setPersonal_pay_price(ruleCheckResult.getPersonalPayPrice());
        resContract.setReimbursable_price(ruleCheckResult.getReimbursablePrice());
        resContract.setUnreimbursable_price(ruleCheckResult.getUnreimbursablePrice());
        resContract.setAmount_compliance(ruleCheckResult.getAmountCompliance());
        resContract.setAmount_non_compliance(ruleCheckResult.getAmountNonCompliance());
        if (ruleCheckResult.getIs_exceed() || reqContract.getTravel_on_busi_parameter_req_contract().getExceed_submit()) {
            resContract.setIs_exceed(true);
        }
        //添加拦截记录
        if (ruleCheckResult.getInterceptFlag() && GlobalResponseCode.Success.getCode() != ruleCheckResult.getErrCode()) {
            initTrainInterceptRecord(reqContract, ruleCheckResult);
        }
        // 错误信息类型
        TravelOnBusiOrderCheckResContract travelOnBusiOrderCheckResContract = iOrderCheckService.travelOnBusiOrderCheckResContractCommon(resContract, ruleCheckResult);
        // 记录下单校验日志
        iOrderCheckService.saveOrderCheckLog(
                reqContract.getTravel_on_busi_common_req_contract().getCompany_id(),
                reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(),
                CategoryTypeEnum.Train.getCode(),
                clientVersion,
                JsonUtils.toJson(reqContract),
                JsonUtils.toJson(travelOnBusiOrderCheckResContract),
                ObjUtils.toString(travelOnBusiOrderCheckResContract.getErr_code()),
                ruleCheckResult.getSnapshotInfo().toJSONString(),
                ruleCheckResult.getExtInfo().toJSONString());
        return travelOnBusiOrderCheckResContract;
    }

    /**
     * 校验火车规则
     *
     * @param reqContract
     * @return
     */
    private TravelOnBusiOrderRuleCheckResult trainOrderRuleCheckResult(TrainOrderCheckReqContract reqContract, String clientVersion) throws SaasException {
        // 默认初始化正常
        TravelOnBusiOrderRuleCheckResult checkResult = new TravelOnBusiOrderRuleCheckResult();
        checkResult.setResCode(GlobalResponseCode.Success);

        // 快照信息
        JSONObject snapshotInfo = new JSONObject();
        checkResult.setSnapshotInfo(snapshotInfo);
        // 查询是否个人垫付模式
        Map<String, Boolean> funcMap = iPrivilegeService.queryFunctionMoudle(CodeTypeEnums.CustomAuth.getKey(), reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(), CustomCodeType.M_ADVANCE_PAYMENT.getKey());
        Boolean advancePayment = funcMap.get(CustomCodeType.M_ADVANCE_PAYMENT.getKey());
        logger.info("[火车票下单校验]，是否个人垫付模式:{}", advancePayment);
        PayModelEnum payModel = advancePayment ? PayModelEnum.PERSONAL_PREPAY : PayModelEnum.COMPANY_PAY;
        checkResult.setPayModel(payModel.getCode());
        snapshotInfo.put("payModel", payModel.getCode());
        BigDecimal totalPrice = reqContract.getTravel_on_busi_common_req_contract().getTotal_price();
        if (advancePayment) {
            checkResult.setCompanyPayPrice(BigDecimal.ZERO);
            checkResult.setPersonalPayPrice(totalPrice);
            checkResult.setReimbursablePrice(totalPrice);
            checkResult.setUnreimbursablePrice(BigDecimal.ZERO);
        } else {
            checkResult.setCompanyPayPrice(totalPrice);
            checkResult.setPersonalPayPrice(BigDecimal.ZERO);
            checkResult.setReimbursablePrice(BigDecimal.ZERO);
            checkResult.setUnreimbursablePrice(BigDecimal.ZERO);
        }
        checkResult.setAmountCompliance(totalPrice);
        checkResult.setAmountNonCompliance(BigDecimal.ZERO);
        // 预订人状态
        EmployeeContract orderEmployee = baseOrganizationService.getEmployee(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(), reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        if (orderEmployee == null || orderEmployee.getStatus() != EmployeeStatusEnum.ACTIVE.getValue()) {
            checkResult.setResCode(GlobalResponseCode.OrderCheckEmployeeNotActive);
            return checkResult;
        }
        // 企业火车权限
        CompanyRuleDTO companyRule = iCompanyRuleService.queryByCompanyId(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        if (companyRule == null || companyRule.getTrainRule() != 1) {
            checkResult.setResCode(GlobalResponseCode.OrderCheckCompanyNoAuth.getCode(), CoreLanguage.Common_Exception_AirEmployeeNoAuthMsg.getMessage());
            return checkResult;
        }
        // 员工火车权限
        EmployeeTrainRule employeeTrainRule = iBaseEmployeeTrainRuleService.queryEmployeeTrainRuleByPrimaryKey(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(), reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        checkResult.setEmployeeTrainRule(employeeTrainRule);
        snapshotInfo.put("authInfo", employeeTrainRule);
        if (employeeTrainRule == null
                || employeeTrainRule.getTrain_rule() != TrainRuleType.AllowOther.getCode()) {
            checkResult.setResCode(GlobalResponseCode.OrderCheckEmployeeNoAuth.getCode(), CoreLanguage.Common_Exception_TrainEmployeeNoAuthMsg.getMessage());
            return checkResult;
        }
        //企业余额校验(余额不足或者账户被锁定)
        TravelOnBusiOrderRuleCheckResult checkCompanyAccountResult = iOrderCheckService.checkCompanyAccount(reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), reqContract.getTravel_on_busi_common_req_contract().getTotal_price(), OrderCategory.Train.getKey(), advancePayment);
        if (GlobalResponseCode.Success.getCode() != checkCompanyAccountResult.getErrCode()) {
            FinhubLogger.event(reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), EventParams.build(**********, false).put("account_sub_type", 2));
            return checkCompanyAccountResult;
        }
        CostAttrAndBudgetConf costAttrAndBudgetConf = iMessageSetupService.queryCostAttrAndBudgetConf(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());// 老版本配置"部门和项目"兼容为"部门或项目"
        if (costAttrAndBudgetConf.getCost_attribution_scope() == CostAttributionScopeEnum.DEPT_AND_PROJ.getCode()
                && ObjUtils.isNotBlank(clientVersion) && VersionTool.lessThan(clientVersion, "4.2.0")) {
            costAttrAndBudgetConf.setCost_attribution_scope(CostAttributionScopeEnum.DEPT_OR_PROJ.getCode());
        }
        checkResult.setCost_attribution_scope(costAttrAndBudgetConf.getCost_attribution_scope());
        checkResult.setBudget_cost_attr_type(costAttrAndBudgetConf.getBudget_cost_attr_type());
        //费用归属校验
        List<CostAttributionInfo> costAttributionList = reqContract.getTravel_on_busi_common_req_contract().getCost_attribution_list();
        //兼容老版本单费用归属
        if (ObjUtils.isEmpty(costAttributionList)) {
            costAttributionList = Lists.newArrayList();
            Integer costAttributionCategory = reqContract.getTravel_on_busi_common_req_contract().getAttribution_category();
            String costAttributionId = reqContract.getTravel_on_busi_common_req_contract().getAttribution_id();
            // 统一转成费用归属列表处理
            CostAttributionInfo costAttributionInfo = new CostAttributionInfo();
            costAttributionInfo.setCost_attribution_category(costAttributionCategory);
            costAttributionInfo.setCost_attribution_id(costAttributionId);
            costAttributionList.add(costAttributionInfo);
        }
        TravelOnBusiOrderRuleCheckResult checkCompanyCostAttributionResult = iOrderCheckService.checkCompanyCostAttributionList(reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), costAttributionList, costAttrAndBudgetConf.getCost_attribution_scope(), clientVersion);
        if (GlobalResponseCode.Success.getCode() != checkCompanyCostAttributionResult.getErrCode()) {
            return checkCompanyCostAttributionResult;
        }
        //审批单查询校验
        TravelOnBusiOrderRuleCheckResult travelOnBusiOrderRuleCheckResult = iOrderCheckService.travelOnbusiOrderApplyIdCheck(reqContract, employeeTrainRule.getTrain_verify_flag(), TravelType.Train.getCode());
        if (GlobalResponseCode.Success.getCode() != travelOnBusiOrderRuleCheckResult.getErrCode()) {
            return travelOnBusiOrderRuleCheckResult;
        }
        //预算校验
        TravelOnBusiOrderRuleCheckResult travelOrderBudgetCheckResult = iOrderCheckService.categoryOrderBudgetCheck(reqContract, costAttrAndBudgetConf, OrderCategory.Train, clientVersion);
        snapshotInfo.put("budgetList", travelOrderBudgetCheckResult.getBudgetSettingAndUseList());
        if (GlobalResponseCode.Success.getCode() != travelOrderBudgetCheckResult.getErrCode()) {
            return travelOrderBudgetCheckResult;
        }
        BigDecimal budgetAmountCompliance = travelOrderBudgetCheckResult.getAmountCompliance();
        BigDecimal budgetAmountNonCompliance = travelOrderBudgetCheckResult.getAmountNonCompliance();
        checkResult.setAmountCompliance(budgetAmountCompliance);
        checkResult.setAmountNonCompliance(budgetAmountNonCompliance);
        logger.info("[火车票下单校验]，预算合规金额:{}, 预算超规金额:{}", budgetAmountCompliance, budgetAmountNonCompliance);
        MessageSetup estimatedCheckSetup = iMessageSetupService.queryCompanyMessageSetupWithDefault(reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), SaasApplyConstant.ITEM_CODE_TRIP_APPLY_BUDGET_CHECK);

        // 预估费用校验
        if (estimatedCheckSetup.getIsChecked() == 1 && estimatedCheckSetup.getStrVal1().contains("15")) {
            String startTime=reqContract.getTravel_on_busi_parameter_req_contract().getStart_time();
            String endTime=reqContract.getTravel_on_busi_parameter_req_contract().getEnd_time();
            String startCity=reqContract.getTravel_on_busi_parameter_req_contract().getStart_city_id();
            String arrivalCity=reqContract.getTravel_on_busi_parameter_req_contract().getArrival_city_id();
            TravelOnBusiOrderRuleCheckResult checkTripEstimatedAmountResult= iOrderCheckService.checkTripEstimatedAmount(reqContract.getTravel_on_busi_common_req_contract().getTotal_price(),
                    reqContract.getTravel_on_busi_common_req_contract().getApply_id(), BizType.Train,DateTimeTool.fromStringToDateTime(startTime),DateTimeTool.fromStringToDateTime(endTime),
                    startCity,arrivalCity,reqContract.getTravel_on_busi_common_req_contract().getCompany_id(),reqContract.getTravel_on_busi_common_req_contract().getApply_trip_id(),clientVersion);
            if (GlobalResponseCode.Success.getCode() != checkTripEstimatedAmountResult.getErrCode()) {
                return checkTripEstimatedAmountResult;
            }
        }

        //需要行程审批
        if (employeeTrainRule.getTrain_verify_flag()) {
            Integer approveType = iOrderCheckService.queryApproveType(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
            //2:审批单中带有规则信息
            if (approveType == 2) {
                String applyId = reqContract.getTravel_on_busi_common_req_contract().getApply_id();
                ApplyRuleSetting applyRuleSetting = applyRuleSettingExtMapper.queryApplyRuleByApplyOrderId(applyId);
                if (applyRuleSetting != null && applyRuleSetting.getTrainInfo()!=null) {
                    List<ApplyThirdContract.KeyValueItem> trainRuleList = JSONArray.parseArray(applyRuleSetting.getTrainInfo(), ApplyThirdContract.KeyValueItem.class);
                    if (CollectionUtils.isEmpty(trainRuleList)) {
                        return checkResult;
                    }
                    return checkTrainThirdRule(trainRuleList, checkResult, reqContract);
                }
            }
        }
        //订单审批开关
        Boolean trainOrderVerifyFlag = employeeTrainRule.getTrain_order_verify_flag();
        if (advancePayment) {
            trainOrderVerifyFlag = false;
        }
        logger.info("[校验火车规则]，订单审批开关:{}", trainOrderVerifyFlag);
        //处理老板版兼容问题(提示升级)
        String oldVersion = "1.9.96";
        if (VersionTool.compare(clientVersion, oldVersion) < 0 && trainOrderVerifyFlag) {
            throw new SaasException(GlobalResponseCode.ApplyCenterAlert);
        }
        //规则校验
        if (employeeTrainRule.getTrain_rule_flag()) {
            String ruleId = ObjUtils.ifNull(employeeTrainRule.getManual_train_rule_id(), employeeTrainRule.getDefault_train_rule_id());
            TrainRule trainRule = trainRuleMapper.selectByPrimaryKey(ruleId);
            checkResult.setTrainRule(trainRule);
            snapshotInfo.put("ruleInfo", trainRule);
            if (trainRule == null) {
                checkResult.setResCode(GlobalResponseCode.OrderCheckRuleNotExist);
                return checkResult;
            }
            //超标规则（1：禁止 2：超规填写理由下单 3：超规需要提交费用审批）
            Integer exceedBuyType = employeeTrainRule.getExceed_buy_type();
            //火车规则校验
            List<TravelOnBusiOrderRuleCheckResult> travelOnBusiOrderRuleCheckResults = this.checkTrainExceedType(trainRule, reqContract);
            Boolean isPriceExceed = false;
            BigDecimal ruleAmountCompliance = totalPrice;
            if (CollectionUtils.isNotEmpty(travelOnBusiOrderRuleCheckResults) && travelOnBusiOrderRuleCheckResults.size() == 1) {
                if (travelOnBusiOrderRuleCheckResults.get(0).getErrCode() == GlobalResponseCode.OrderCheckAirMinPriceNoAuth.getCode()) {
                    checkResult.setMin_flight(travelOnBusiOrderRuleCheckResults.get(0).getMin_flight());
                }
                if (travelOnBusiOrderRuleCheckResults.get(0).getIsPriceExceed() != null && travelOnBusiOrderRuleCheckResults.get(0).getIsPriceExceed()) {
                    isPriceExceed = true;
                    ruleAmountCompliance = travelOnBusiOrderRuleCheckResults.get(0).getAmountCompliance();
                    BigDecimal ruleAmountNonCompliance = travelOnBusiOrderRuleCheckResults.get(0).getAmountNonCompliance();
                    logger.info("[火车下单校验]，规则合规金额:{}, 规则超规金额:{}", ruleAmountCompliance, ruleAmountNonCompliance);
                }
            }
            // 超规/合规金额处理
            if (CollectionUtils.isNotEmpty(travelOnBusiOrderRuleCheckResults)) {
                if (isPriceExceed) {
                    checkResult.setAmountCompliance(checkResult.getAmountCompliance().min(ruleAmountCompliance));
                    checkResult.setAmountNonCompliance(totalPrice.subtract(checkResult.getAmountCompliance()));
                } else {
                    checkResult.setAmountCompliance(BigDecimal.ZERO);
                    checkResult.setAmountNonCompliance(totalPrice);
                }
                logger.info("[火车下单校验]，合规金额:{}, 超规金额:{}", checkResult.getAmountCompliance(), checkResult.getAmountNonCompliance());
            }
            //强制提交逻辑
            TravelOnBusiOrderRuleCheckResult checkExceedAuthResult = iOrderCheckService.checkExceedAuth(reqContract, CategoryTypeEnum.Train);
            //开启订单审批
            if (trainOrderVerifyFlag) {
                //超标禁止下单(超标需要理由)
                if ((TravelExceedType.NotAllowed.getCode() == exceedBuyType || TravelExceedType.AllowedNeedReason.getCode() == exceedBuyType) && CollectionUtils.isNotEmpty(travelOnBusiOrderRuleCheckResults)) {
                    iOrderCheckService.setErrMsgInfoNotAllowed(checkResult, travelOnBusiOrderRuleCheckResults);
                    return checkResult;
                } else if ((TravelExceedType.AllowedNeedApply.getCode() == exceedBuyType && travelOnBusiOrderRuleCheckResults.size() > 0 &&
                        GlobalResponseCode.Success.getCode() != travelOnBusiOrderRuleCheckResults.get(0).getErrCode()) &&
                        !iOrderCheckService.checkExceedApplyAuth(reqContract)) {
                    logger.info("火车超规则，走订单审批");
                    iOrderCheckService.setErrMsgInfoForCheckApply(checkResult, travelOnBusiOrderRuleCheckResults);
                    checkResult.setErrCode(GlobalResponseCode.OrderCheckApplyExceedMore.getCode());
                    checkResult.setIs_exceed(true);
                    return checkResult;
                }
                //没有限制规则，但是开启了订单审批
                iOrderCheckService.airOrderApplyCheckRes(checkResult, trainOrderVerifyFlag, GlobalResponseCode.OrderCheckApplyExceedLess);
            } else {
                //未开启订单审批
                //超标禁止下单
                if (TravelExceedType.NotAllowed.getCode() == exceedBuyType && CollectionUtils.isNotEmpty(travelOnBusiOrderRuleCheckResults)) {
                    String version = "1.9.4";
                    if (VersionTool.compare(clientVersion, version) < 0) {
                        if (travelOnBusiOrderRuleCheckResults.size() > 0) {
                            return travelOnBusiOrderRuleCheckResults.get(0);
                        }
                    } else {
                        iOrderCheckService.setErrMsgInfoNotAllowed(checkResult, travelOnBusiOrderRuleCheckResults);
                        return checkResult;
                    }
                    //未超规，但是开启了订单审批开关
                    iOrderCheckService.airOrderApplyCheckRes(checkResult, trainOrderVerifyFlag, GlobalResponseCode.OrderCheckApplyExceedLess);
                }
                //超标需要理由
                else if (TravelExceedType.AllowedNeedReason.getCode() == exceedBuyType &&
                        travelOnBusiOrderRuleCheckResults.size() > 0 && GlobalResponseCode.Success.getCode() != travelOnBusiOrderRuleCheckResults.get(0).getErrCode() &&
                        GlobalResponseCode.Success.getCode() != checkExceedAuthResult.getErrCode()) {
                    //超规则返回
                    checkExceedAuthResult.setIs_exceed(true);
                    iOrderCheckService.setErrMsgInfoForReason(travelOnBusiOrderRuleCheckResults, checkExceedAuthResult);
                    return checkExceedAuthResult;
                } else if (!advancePayment && (TravelExceedType.AllowedNeedApply.getCode() == exceedBuyType && travelOnBusiOrderRuleCheckResults.size() > 0 && GlobalResponseCode.Success.getCode() != travelOnBusiOrderRuleCheckResults.get(0).getErrCode()) &&
                        !iOrderCheckService.checkExceedApplyAuth(reqContract)) {
                    logger.info("新版本火车超规则，走订单审批");
                    iOrderCheckService.setErrMsgInfoForCheckApply(checkResult, travelOnBusiOrderRuleCheckResults);
                    checkResult.setErrCode(GlobalResponseCode.OrderCheckApplyExceedMore.getCode());
                    checkResult.setIs_exceed(true);
                    return checkResult;
                }
            }
        } else {
            if (!advancePayment) {
                //没有限制规则，但是开启了订单审批
                iOrderCheckService.airOrderApplyCheckRes(checkResult, trainOrderVerifyFlag, GlobalResponseCode.OrderCheckApplyExceedLess);
            }
        }
        return checkResult;
    }

    /**
     * 校验审批规则信息
     *
     * @param trainRuleList
     * @return
     */
    private TravelOnBusiOrderRuleCheckResult checkTrainThirdRule(List<ApplyThirdContract.KeyValueItem> trainRuleList, TravelOnBusiOrderRuleCheckResult checkResult, TrainOrderCheckReqContract reqContract) {
        TrainRule trainRule = new TrainRule();
        for (ApplyThirdContract.KeyValueItem trainInfo : trainRuleList) {
            trainRule.setDayLimit(false);
            trainRule.setTrainSeatFlag(false);
            String ruleType = trainInfo.getType();
            Object ruleValue = trainInfo.getValue();
            if (SaasOrderThirdRuleConstant.TRAIN_PRICE.equals(ruleType)) {
                trainRule.setPriceLimit(ObjUtils.toBigDecimal(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.TRAIN_PRIV_DAY_MIN.equals(ruleType)) {
                trainRule.setPrivDayMin(ObjUtils.toInteger(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.TRAIN_PRIV_DAY_MAX.equals(ruleType)) {
                trainRule.setPrivDayMax(ObjUtils.toInteger(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.TRAIN_COMMON_SEAT_TYPE.equals(ruleType)) {
                trainRule.setTrainSeatFlag(true);
                String commonSeatType = StringUtils.join((List<Integer>) ruleValue, ",");
                trainRule.setCommonTrainSeatType(commonSeatType);
            }
            if (SaasOrderThirdRuleConstant.TRAIN_HIGHSPEED_SEAT_TYPE.equals(ruleType)) {
                trainRule.setTrainSeatFlag(true);
                String highSpeedSeatType = StringUtils.join((List<Integer>) ruleValue, ",");
                trainRule.setHighspeedTrainSeatType(highSpeedSeatType);
            }
            if (SaasOrderThirdRuleConstant.TRAIN_DAY_LIMIT.equals(ruleType)) {
                trainRule.setDayLimit(ObjUtils.toBoolean(ruleValue, false));
            }
        }
        List<TravelOnBusiOrderRuleCheckResult> travelOnBusiOrderRuleCheckResults = checkTrainExceedType(trainRule, reqContract);
        if (CollectionUtils.isNotEmpty(travelOnBusiOrderRuleCheckResults)) {
            //增加返回字段
            iOrderCheckService.setErrMsgInfoNotAllowed(checkResult, travelOnBusiOrderRuleCheckResults);
            return checkResult;
        }
        return checkResult;
    }

    //处理火车公用超标逻辑
    private List<TravelOnBusiOrderRuleCheckResult> checkTrainExceedType(TrainRule trainRule, TrainOrderCheckReqContract reqContract) {
        List<TravelOnBusiOrderRuleCheckResult> travelOnBusiOrderRuleCheckResults = new ArrayList<>();
        //判断是否有价格限制
        if (trainRule.getPriceLimit() != null) {
            if (reqContract.getOrder_parameter_json().getSeat_price().compareTo(trainRule.getPriceLimit()) == 1) {
                TravelOnBusiOrderRuleCheckResult checkResults = new TravelOnBusiOrderRuleCheckResult();
//                String msg = StrUtils.formatString("申请人违反了「只可预订{0}」的限制", trainRule.getPriceLimit());
                String msg = StrUtils.formatString("单张票价格需低于¥{0}",
                        BigDecimalTool.formatMoney(trainRule.getPriceLimit()));
//                checkResults.setResCode(GlobalResponseCode.OrderCheckTrainRuleBelowPriceLimitMsg,msg);
                checkResults.setErrCode(GlobalResponseCode.OrderCheckTrainRuleBelowPriceLimitMsg.getCode());
                checkResults.setErrMsg(msg);
                checkResults.setExceed_msg(msg);
                checkResults.setType(2);
                if(ObjUtils.isNotEmpty(reqContract.getTravel_on_busi_common_req_contract().getTotal_price())){
                    checkResults.setIsPriceExceed(true);
                    checkResults.setAmountCompliance(trainRule.getPriceLimit().multiply(new BigDecimal(reqContract.getPassengers().size())));
                    checkResults.setAmountNonCompliance(reqContract.getTravel_on_busi_common_req_contract().getTotal_price().subtract(checkResults.getAmountCompliance()));
                }
                travelOnBusiOrderRuleCheckResults.add(checkResults);
            }
        }
        String start_time = reqContract.getTravel_on_busi_parameter_req_contract().getStart_time();
        LocalDate startTime = LocalDateTime.ofInstant(DateTimeTool.fromStringToDateTime(start_time).toInstant(), ZoneId.systemDefault()).toLocalDate();
        LocalDate currentTime = LocalDate.now();
        //下单时间与预定时间相差天数
        long daysDiff = ChronoUnit.DAYS.between(currentTime, startTime);
        Integer privDayMin = trainRule.getPrivDayMin();
        if (privDayMin != null && daysDiff < privDayMin) {
            logger.info(StrUtils.formatString("申请人违反了「最少提前{0}天预订」的限制", privDayMin));
            TravelOnBusiOrderRuleCheckResult checkResults = new TravelOnBusiOrderRuleCheckResult();
//            String msg = StrUtils.formatString("申请人违反了「最少提前{0}天预订」的限制", privDayMin);
            String msg = StrUtils.formatString(CoreLanguage.TrainCheckServiceImpl_Value_MinDay.getMessage(), privDayMin);
            checkResults.setResCode(GlobalResponseCode.OrderCheckLeastDay.getCode(), msg);
            checkResults.setExceed_msg(msg);
            checkResults.setType(3);
            travelOnBusiOrderRuleCheckResults.add(checkResults);
        }
        Integer privDayMax = trainRule.getPrivDayMax();
        if (privDayMax != null && daysDiff > privDayMax) {
            logger.info(StrUtils.formatString("申请人违反了「最多提前{0}天预订」的限制", privDayMax));
            TravelOnBusiOrderRuleCheckResult checkResults = new TravelOnBusiOrderRuleCheckResult();
//            String msg = StrUtils.formatString("申请人违反了「最多提前{0}天预订」的限制", privDayMax);
            String msg = StrUtils.formatString(CoreLanguage.TrainCheckServiceImpl_Value_MaxDay.getMessage(), privDayMax);
            checkResults.setResCode(GlobalResponseCode.OrderCheckMostDay.getCode(), msg);
            checkResults.setExceed_msg(msg);
            checkResults.setType(3);
            travelOnBusiOrderRuleCheckResults.add(checkResults);
        }
        //夕发朝至限制 当数据库中的dayLimit为true的时候限制
        //查询数据库的 dayLimit数据
        Boolean dayLimit = trainRule.getDayLimit();
        if (Objects.equals(dayLimit, true) && reqContract.getIs_day_limit()) {
            //获取客户端传入的火车出发时间
            String beginTime = reqContract.getTravel_on_busi_parameter_req_contract().getStart_time();
            Date beginDate = DateUtils.parse(beginTime);
            SimpleDateFormat format = new SimpleDateFormat("HH:mm:ss");
            String dateBegin = format.format(beginDate);//客户端传入时间
            //获取客户端传入的火车结束时间
            String endTime = reqContract.getTravel_on_busi_parameter_req_contract().getEnd_time();
            Date endDate = DateUtils.parse(endTime);
            String dateEnd = format.format(endDate);//客户端传入结束时间
            Date dateBeginTime = null;//转换客户端传入的开始时间
            try {
                dateBeginTime = format.parse(dateBegin);
                Date dateEndTime = format.parse(dateEnd);//转入客户端传入的结束时间

                //16点至23点之间发车，次日5点至11点到达终点站的列车
                String comBeginTime = "16:00:00";
                Date beginTimeParse = format.parse(comBeginTime);
                String comBeginLastTime = "23:00:00";
                Date comBeginLastTimeParse = format.parse(comBeginLastTime);

                String comEndTime = "5:00:00";
                Date comEndTimeParse = format.parse(comEndTime);
                String comEndLastTime = "11:00:00";
                Date comEndLastTimeParse = format.parse(comEndLastTime);

                if (!(dateBeginTime.before(comBeginLastTimeParse) && dateBeginTime.after(beginTimeParse))
                        || !((dateEndTime.before(comEndLastTimeParse) && dateEndTime.after(comEndTimeParse)))) {
                    TravelOnBusiOrderRuleCheckResult checkResults = new TravelOnBusiOrderRuleCheckResult();
//                    String msg = StrUtils.formatString("申请人违反了「夕发朝至」的限制");
                    String msg = StrUtils.formatString(CoreLanguage.TrainCheckServiceImpl_Value_NeedDayToNight.getMessage());
                    checkResults.setResCode(GlobalResponseCode.OrderCheckTrainRuleDayAndOrder.getCode(), msg);
                    checkResults.setExceed_msg(msg);
                    checkResults.setType(4);
                    travelOnBusiOrderRuleCheckResults.add(checkResults);
                }
            } catch (ParseException e) {
                logger.error("checkTrainExceedType异常，{}", e.getLocalizedMessage());
            }
        }
        //校验火车规则
        if (Objects.equals(trainRule.getTrainSeatFlag() ,true)) {
            String msg = "";
            String hightStr = TrainSeatType.getNamesByCodes(trainRule.getHighspeedTrainSeatType());
            String commonStr = TrainSeatType.getNamesByCodes(trainRule.getCommonTrainSeatType());
            if (StringUtils.isNotEmpty(hightStr)) {
                msg = CoreLanguage.TrainCheckServiceImpl_Value_HighTrain.getMessage() + ":" + hightStr;
            }
            if (StringUtils.isNotEmpty(msg) && StringUtils.isNotEmpty(commonStr)) {
                msg = msg + "\n" + CoreLanguage.TrainCheckServiceImpl_Value_Train.getMessage() + ":" + commonStr;
            } else if (StringUtils.isEmpty(msg) && StringUtils.isNotEmpty(commonStr)) {
                msg = CoreLanguage.TrainCheckServiceImpl_Value_Train.getMessage() + ":" + commonStr;
            }
            //判断是否只允许定一种类型的火车
            if (StringUtils.isEmpty(trainRule.getCommonTrainSeatType()) && StringUtils.isEmpty(trainRule.getHighspeedTrainSeatType())) {
                TravelOnBusiOrderRuleCheckResult checkResults = new TravelOnBusiOrderRuleCheckResult();
                checkResults.setResCode(GlobalResponseCode.OrderCheckTrainNoAuth);
                travelOnBusiOrderRuleCheckResults.add(checkResults);
                return travelOnBusiOrderRuleCheckResults;
            } else if (StringUtils.isEmpty(trainRule.getCommonTrainSeatType()) || StringUtils.isEmpty(trainRule.getHighspeedTrainSeatType())) {
                if (reqContract.getIs_high_speed_flag() && !StringUtils.isEmpty(trainRule.getCommonTrainSeatType())) {
                    TravelOnBusiOrderRuleCheckResult checkResults = new TravelOnBusiOrderRuleCheckResult();
                    checkResults.setResCode(GlobalResponseCode.OrderCheckTrainCommonSeatTypeCompanyAccountNoAuth);
                    checkResults.setType(1);
                    travelOnBusiOrderRuleCheckResults.add(checkResults);
                    return travelOnBusiOrderRuleCheckResults;
                } else if (!reqContract.getIs_high_speed_flag() && !StringUtils.isEmpty(trainRule.getHighspeedTrainSeatType())) {
                    TravelOnBusiOrderRuleCheckResult checkResults = new TravelOnBusiOrderRuleCheckResult();
                    checkResults.setResCode(GlobalResponseCode.OrderCheckTrainHightSpeedSeatTypeNoAuth);
                    checkResults.setType(1);
                    travelOnBusiOrderRuleCheckResults.add(checkResults);
                    return travelOnBusiOrderRuleCheckResults;
                } else if (!reqContract.getIs_high_speed_flag() && StringUtils.isEmpty(trainRule.getHighspeedTrainSeatType()) &&
                        !trainRule.getCommonTrainSeatType().contains(TrainCommonSpeedType.getValueByCode(reqContract.getTrain_seat_type()))) {
                    TravelOnBusiOrderRuleCheckResult checkResults = new TravelOnBusiOrderRuleCheckResult();
                    checkResults.setType(1);
                    checkResults.setResCode(GlobalResponseCode.OrderCheckTrainSeatTypeNoAuth.getCode(), StrUtils.formatString(GlobalResponseCode.OrderCheckTrainSeatTypeNoAuth.getMsg(), msg));
                    travelOnBusiOrderRuleCheckResults.add(checkResults);
                    return travelOnBusiOrderRuleCheckResults;
                } else if (reqContract.getIs_high_speed_flag() && StringUtils.isEmpty(trainRule.getCommonTrainSeatType()) &&
                        !trainRule.getHighspeedTrainSeatType().contains(TrainHighSpeedType.getValueByCode(reqContract.getTrain_seat_type()))) {
                    TravelOnBusiOrderRuleCheckResult checkResults = new TravelOnBusiOrderRuleCheckResult();
                    checkResults.setResCode(GlobalResponseCode.OrderCheckTrainSeatTypeNoAuth.getCode(), StrUtils.formatString(GlobalResponseCode.OrderCheckTrainSeatTypeNoAuth.getMsg(), msg));
                    checkResults.setType(1);
                    travelOnBusiOrderRuleCheckResults.add(checkResults);
                    return travelOnBusiOrderRuleCheckResults;
                }
            } else {
                //判断火车席位问题
                logger.info("火车席位，是否高铁：{},席位：{}", reqContract.getIs_high_speed_flag(), reqContract.getTrain_seat_type());
                String trainHighType = TrainHighSpeedType.getValueByCode(reqContract.getTrain_seat_type());
                String trainCommonType = TrainCommonSpeedType.getValueByCode(reqContract.getTrain_seat_type());
                if (reqContract.getIs_high_speed_flag()) {
                    if (StringUtils.isNotBlank(trainRule.getHighspeedTrainSeatType())) {
                        List<String> highspeedTrainSeatTypeList = Arrays.asList(trainRule.getHighspeedTrainSeatType().split(","));
                        if (!highspeedTrainSeatTypeList.contains(trainHighType)) {
                            TravelOnBusiOrderRuleCheckResult checkResults = new TravelOnBusiOrderRuleCheckResult();
                            //checkResults.setResCode(GlobalResponseCode.OrderCheckTrainSeatTypeNoAuth.getCode(), StrUtils.formatString(GlobalResponseCode.OrderCheckTrainSeatTypeNoAuth.getMsg(), msg));
                            checkResults.setResCode(GlobalResponseCode.OrderCheckTrainSeatNoAuth.getCode(), StrUtils.formatString(GlobalResponseCode.OrderCheckTrainSeatNoAuth.getMsg(), msg));
                            checkResults.setType(1);
                            travelOnBusiOrderRuleCheckResults.add(checkResults);
                        }
                    }
                } else {
                    if (StringUtils.isNotBlank(trainRule.getCommonTrainSeatType())) {
                        List<String> commonTrainSeatTypeList = Arrays.asList(trainRule.getCommonTrainSeatType().split(","));
                        if (!commonTrainSeatTypeList.contains(trainCommonType)) {
                            TravelOnBusiOrderRuleCheckResult checkResults = new TravelOnBusiOrderRuleCheckResult();
                            checkResults.setResCode(GlobalResponseCode.OrderCheckTrainSeatNoAuth.getCode(), StrUtils.formatString(GlobalResponseCode.OrderCheckTrainSeatNoAuth.getMsg(), msg));
                            checkResults.setType(1);
                            travelOnBusiOrderRuleCheckResults.add(checkResults);
                        }
                    }
                }
                return travelOnBusiOrderRuleCheckResults;
            }
        }
        return travelOnBusiOrderRuleCheckResults;
    }

    /**
     * 添加火车拦截记录信息
     */
    private void initTrainInterceptRecord(TrainOrderCheckReqContract reqContract, TravelOnBusiOrderRuleCheckResult ruleCheckResult) {
        TrainInterceptRecord trainInterceptRecord = new TrainInterceptRecord();
        //拦截参数
        try {
            //处理规则信息
            EmployeeTrainRule employeeTrainRule = iBaseEmployeeTrainRuleService.queryEmployeeTrainRuleByPrimaryKey(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(), reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
            TrainRule trainRule = null;
            if (employeeTrainRule != null) {
                String ruleId = ObjUtils.ifNull(employeeTrainRule.getManual_train_rule_id(), employeeTrainRule.getDefault_train_rule_id());
                trainRule = trainRuleMapper.selectByPrimaryKey(ruleId);
            }
            //处理拦截信息
            TrainInterceptRecordContract trainInterceptRecordContract = reqContract.getOrder_parameter_json();
            trainInterceptRecord.setId(IDTool.CreateUniqueID());
            trainInterceptRecord.setEmployeeId(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id());
            trainInterceptRecord.setCompanyId(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
            trainInterceptRecord.setCreateTime(new Date());
            trainInterceptRecord.setContactName(reqContract.getTravel_on_busi_parameter_req_contract().getContact_name());
            trainInterceptRecord.setContactPhone(reqContract.getTravel_on_busi_parameter_req_contract().getContact_phone());
            trainInterceptRecord.setChannel(reqContract.getTravel_on_busi_common_req_contract().getChannel());
            trainInterceptRecord.setTotalPrice(reqContract.getTravel_on_busi_common_req_contract().getTotal_price());
            trainInterceptRecord.setTrainRule(employeeTrainRule == null ? -1 : employeeTrainRule.getTrain_rule());
            trainInterceptRecord.setTrainRuleFlag(employeeTrainRule == null ? false : employeeTrainRule.getTrain_rule_flag());
            trainInterceptRecord.setTrainVerifyFlag(employeeTrainRule == null ? false : employeeTrainRule.getTrain_verify_flag());
            trainInterceptRecord.setExceedBuyFlag(reqContract.getTravel_on_busi_parameter_req_contract().getExceed_submit());
            trainInterceptRecord.setExceedBuyType(employeeTrainRule == null ? -1 : employeeTrainRule.getExceed_buy_type());
            trainInterceptRecord.setTrainSeatFlag(trainRule == null ? false : trainRule.getTrainSeatFlag());
            trainInterceptRecord.setCommonTrainSeatType(trainRule == null ? "" : trainRule.getCommonTrainSeatType());
            trainInterceptRecord.setHighspeedTrainSeatType(trainRule == null ? "" : trainRule.getHighspeedTrainSeatType());
            trainInterceptRecord.setTrainCode(trainInterceptRecordContract.getTrain_code());
            trainInterceptRecord.setTrainNo(trainInterceptRecordContract.getTrain_no());
            trainInterceptRecord.setFromStationCode(trainInterceptRecordContract.getFrom_station_code());
            trainInterceptRecord.setFromStationName(trainInterceptRecordContract.getFrom_station_name());
            trainInterceptRecord.setToStationCode(trainInterceptRecordContract.getTo_station_code());
            trainInterceptRecord.setToStationName(trainInterceptRecordContract.getTo_station_name());
            trainInterceptRecord.setTrainStartDate(trainInterceptRecordContract.getTrain_start_date());
            trainInterceptRecord.setTrainEndDate(trainInterceptRecordContract.getTrain_end_date());
            trainInterceptRecord.setStartTime(trainInterceptRecordContract.getStart_time());
            trainInterceptRecord.setArriveTime(trainInterceptRecordContract.getArrive_time());
            trainInterceptRecord.setRunTime(trainInterceptRecordContract.getRun_time());
            trainInterceptRecord.setArriveDays(trainInterceptRecordContract.getArrive_days());
            trainInterceptRecord.setSeatType(trainInterceptRecordContract.getSeat_type());
            trainInterceptRecord.setSeatNo(trainInterceptRecordContract.getSeat_no());
            trainInterceptRecord.setSeatPrice(trainInterceptRecordContract.getSeat_price());
            trainInterceptRecord.setServiceFee(trainInterceptRecordContract.getService_fee());
            //需要处理订票人信息
            List<OrderCheckExt> passengerList = iOrderCheckService.getPassengerList(reqContract.getPassengers());
            String passengerInfoList = JsonUtils.toJson(passengerList);
            trainInterceptRecord.setPassengerInfoList(passengerInfoList);
            trainInterceptRecord.setErrCode(ruleCheckResult.getErrCode());
            String errorMsg = ruleCheckResult.getErrMsg() + (StringUtils.isEmpty(ruleCheckResult.getExceed_msg()) ? "" : "|" + ruleCheckResult.getExceed_msg());
            trainInterceptRecord.setErrMsg(errorMsg);
            trainInterceptRecord.setCostCenterId(reqContract.getTravel_on_busi_common_req_contract().getAttribution_id());
            trainInterceptRecord.setCostCenterType(reqContract.getTravel_on_busi_common_req_contract().getAttribution_category());
            trainInterceptRecordMapper.insertSelective(trainInterceptRecord);
        } catch (Exception e) {
            logger.error("添加火车拦截记录信息:{},发生异常:{}", JsonUtils.toJson(trainInterceptRecord), e.getLocalizedMessage());
        }
    }


    /**
     * 校验火车规则
     *
     * @param reqContract
     * @param clientVersion
     * @return
     * @throws SaasException
     */
    @Override
    public OrderCheckResContract trainRuleCheck(TrainOrderCheckReqContract reqContract, String clientVersion) throws SaasException {
        reqContract.setIs_day_limit(true);
        OrderCheckResContract orderCheckResContract = new OrderCheckResContract();
        List<String>  exceedInfoList = Lists.newArrayList();
        orderCheckResContract.setExceedInfo(exceedInfoList);
        // 员工火车权限
        EmployeeTrainRule employeeTrainRule = iBaseEmployeeTrainRuleService.queryEmployeeTrainRuleByPrimaryKey(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(), reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        if (employeeTrainRule == null || employeeTrainRule.getTrain_rule() != TrainRuleType.AllowOther.getCode()) {
            return orderCheckResContract;
        }
        //规则校验
        if (employeeTrainRule.getTrain_rule_flag()) {
            String ruleId = ObjUtils.ifNull(employeeTrainRule.getManual_train_rule_id(), employeeTrainRule.getDefault_train_rule_id());
            TrainRule trainRule = trainRuleMapper.selectByPrimaryKey(ruleId);
            if (trainRule == null) {
                return orderCheckResContract;
            }
            //超标规则（1：禁止 2：超规填写理由下单 3：超规需要提交费用审批）
            Integer exceedBuyType = employeeTrainRule.getExceed_buy_type();
            //火车规则校验
            List<TravelOnBusiOrderRuleCheckResult> travelOnBusiOrderRuleCheckResults = this.checkTrainExceedType(trainRule, reqContract);
            if (CollectionUtils.isNotEmpty(travelOnBusiOrderRuleCheckResults)) {
                List<String> exceedList = Lists.newArrayList();
                for (TravelOnBusiOrderRuleCheckResult travelOnBusiOrderRuleCheckResult : travelOnBusiOrderRuleCheckResults) {
                    if (travelOnBusiOrderRuleCheckResult != null) {
                        if (StringUtils.isNotBlank(travelOnBusiOrderRuleCheckResult.getErrMsg())) {
                            exceedList.add(travelOnBusiOrderRuleCheckResult.getErrMsg());
                        }
                    }
                }
                orderCheckResContract.setExceedInfo(exceedList);
                return orderCheckResContract;
            }
        } else {
            return orderCheckResContract;
        }
        return orderCheckResContract;
    }

}
