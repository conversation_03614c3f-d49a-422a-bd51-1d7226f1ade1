package com.fenbeitong.saas.core.contract.messagesettings.inner;

import com.fenbeitong.saas.core.contract.user.EmployeeInfoContract;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by zhaohaichao on 2019/7/9.
 */
public class RechargeResult {
    private List<EmployeeInfoContract> receiver_list = new ArrayList<>();

    public List<EmployeeInfoContract> getReceiver_list() {
        return receiver_list;
    }

    public void setReceiver_list(List<EmployeeInfoContract> receiver_list) {
        this.receiver_list = receiver_list;
    }
}
