package com.fenbeitong.saas.core.contract.organization;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/5/18.
 */
public class AllEmployeeOfCompanyContract {

    /**
     * count : 10
     * total_count : 230
     * page : 2
     * employee_list : [{"id":"575263e982f880a6d686ce2e","name":"test","phone":"13811372444","role":{"key":1,"value":"普通员工"},"is_own":true,"org_unit_list":[{"id":"575263e982f880a6d686ce2e","name":"研发部"},{"id":"575263e982f880a6d686ce2f","name":"财务部"}]}]
     */

    private int count;
    private int total_count;
    private int page;
    private List<EmployeeListBean> employee_list;

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public int getTotal_count() {
        return total_count;
    }

    public void setTotal_count(int total_count) {
        this.total_count = total_count;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public List<EmployeeListBean> getEmployee_list() {
        return employee_list;
    }

    public void setEmployee_list(List<EmployeeListBean> employee_list) {
        this.employee_list = employee_list;
    }

    public AllEmployeeOfCompanyContract(int count, int total_count, int page, List<EmployeeListBean> employee_list) {
        this.count = count;
        this.total_count = total_count;
        this.page = page;
        this.employee_list = employee_list;
    }

    public static class EmployeeListBean {
        /**
         * id : 575263e982f880a6d686ce2e
         * name : test
         * phone : 13811372444
         * role : {"key":1,"value":"普通员工"}
         * is_own : true
         * org_unit_list : [{"id":"575263e982f880a6d686ce2e","name":"研发部"},{"id":"575263e982f880a6d686ce2f","name":"财务部"}]
         */

        private String id;
        private String name;
        private String phone;
        private RoleBean role;
        private boolean is_manager;
        private List<OrgUnitListBean> org_unit_list;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }

        public RoleBean getRole() {
            return role;
        }

        public void setRole(RoleBean role) {
            this.role = role;
        }

        public List<OrgUnitListBean> getOrg_unit_list() {
            return org_unit_list;
        }

        public boolean isIs_manager() {
            return is_manager;
        }

        public void setIs_manager(boolean is_manager) {
            this.is_manager = is_manager;
        }

        public void setOrg_unit_list(List<OrgUnitListBean> org_unit_list) {
            this.org_unit_list = org_unit_list;
        }

        public EmployeeListBean(String id, String name, String phone, RoleBean role, boolean is_manager, List<OrgUnitListBean> org_unit_list) {
            this.id = id;
            this.name = name;
            this.phone = phone;
            this.role = role;
            this.is_manager = is_manager;
            this.org_unit_list = org_unit_list;
        }

        public static class RoleBean {
            /**
             * key : 1
             * value : 普通员工
             */

            private int key;
            private String value;

            public int getKey() {
                return key;
            }

            public void setKey(int key) {
                this.key = key;
            }

            public String getValue() {
                return value;
            }

            public void setValue(String value) {
                this.value = value;
            }

            public RoleBean() {
            }

            public RoleBean(int key, String value) {
                this.key = key;
                this.value = value;
            }
        }

        public static class OrgUnitListBean {
            /**
             * id : 575263e982f880a6d686ce2e
             * name : 研发部
             */

            private String id;
            private String name;
            /**
             * 三方部门ID
             */
            private String third_id;

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getThird_id() {
                return third_id;
            }

            public void setThird_id(String third_id) {
                this.third_id = third_id;
            }

            public OrgUnitListBean(String id, String name) {
                this.id = id;
                this.name = name;
            }

            public OrgUnitListBean(String id, String name, String third_id) {
                this.id = id;
                this.name = name;
                this.third_id = third_id;
            }
        }
    }
}
