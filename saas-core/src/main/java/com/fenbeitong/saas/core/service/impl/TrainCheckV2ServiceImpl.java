package com.fenbeitong.saas.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.expense.management.api.costAttribution.ICostAttributionService;
import com.fenbeitong.fenbeimeta.api.model.enums.mongo.ObjectNameEnum;
import com.fenbeitong.fenbeimeta.api.model.vo.data.CustomizeVO;
import com.fenbeitong.fenbeimeta.api.service.data.IMongoDataService;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.EventParams;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.saas.api.model.dto.apply.amount.AmountExceedCheckRes;
import com.fenbeitong.saas.api.model.dto.template.rule.ControlOrderRuleDto;
import com.fenbeitong.saas.core.common.DynamicDataSourceDecision;
import com.fenbeitong.saas.core.common.base.SaasException;
import com.fenbeitong.saas.core.common.constant.*;
import com.fenbeitong.saas.core.contract.apply.ApplyOrderContract;
import com.fenbeitong.saas.core.contract.apply.ApplyThirdContract;
import com.fenbeitong.saas.core.contract.apply.ApplyTripValidRequestContract;
import com.fenbeitong.saas.core.contract.apply.CheckApplyEstimatedAmountReq;
import com.fenbeitong.saas.core.contract.cost.CostCheckVO;
import com.fenbeitong.saas.core.contract.messagesettings.CostAttrAndBudgetConf;
import com.fenbeitong.saas.core.contract.messagesettings.ExceedPersonalPayCompanyConf;
import com.fenbeitong.saas.core.contract.order.check.*;
import com.fenbeitong.saas.core.contract.setup.MessageSetupVO;
import com.fenbeitong.saas.core.dao.fenbeitong.TrainInterceptRecordMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.TrainRuleMapper;
import com.fenbeitong.saas.core.language.CoreLanguage;
import com.fenbeitong.saas.core.model.enums.EmployeeStatusEnum;
import com.fenbeitong.saas.core.model.enums.OrderCategory;
import com.fenbeitong.saas.core.model.enums.apply.ApplyType;
import com.fenbeitong.saas.core.model.enums.apply.TotalEstimatedLimitType;
import com.fenbeitong.saas.core.model.enums.booking.BookingConfigEnum;
import com.fenbeitong.saas.core.model.enums.booking.BookingTypeEnum;
import com.fenbeitong.saas.core.model.enums.message.BizType;
import com.fenbeitong.saas.core.model.enums.rule.*;
import com.fenbeitong.saas.core.model.fenbeitong.OrderCheckExt;
import com.fenbeitong.saas.core.model.fenbeitong.TrainInterceptRecord;
import com.fenbeitong.saas.core.model.fenbeitong.TrainRule;
import com.fenbeitong.saas.core.model.saas.ApplyOrder;
import com.fenbeitong.saas.core.model.saas.ApplyRuleSetting;
import com.fenbeitong.saas.core.model.saas.MessageSetup;
import com.fenbeitong.saas.core.service.*;
import com.fenbeitong.saas.core.service.apply.TripEstimateService;
import com.fenbeitong.saas.core.service.apply.amount.ApplyAmountCheckResultService;
import com.fenbeitong.saas.core.service.rule.ExceedConfigTypeEnum;
import com.fenbeitong.saas.core.service.rule.multi.ConsumeTemplateRuleService;
import com.fenbeitong.saas.core.service.rule.train.TrainRuleCheckReq;
import com.fenbeitong.saas.core.service.rule.train.TrainRuleCheckRes;
import com.fenbeitong.saas.core.service.rule.train.TrainRuleMsg;
import com.fenbeitong.saas.core.service.rule.train.TrainRuleV2CheckService;
import com.fenbeitong.saas.core.service.setup.SetupService;
import com.fenbeitong.saas.core.utils.air.AirRuleUtils;
import com.fenbeitong.saas.core.utils.tools.*;
import com.fenbeitong.saas.core.utils.train.TrainRuleUtils;
import com.fenbeitong.saasplus.api.model.dto.custform.CustomFormApplyControlItemDTO;
import com.fenbeitong.saasplus.api.model.dto.custform.CustomFormTotalEstimatedOptionDTO;
import com.fenbeitong.saasplus.api.model.dto.finance.CustomDimension;
import com.fenbeitong.saasplus.api.model.dto.finance.PersonCost;
import com.fenbeitong.saasplus.api.model.enums.custform.SwitchTypeEnum;
import com.fenbeitong.saasplus.api.model.po.fenbeitong.rulenew.TrainRuleV2;
import com.fenbeitong.saasplus.api.service.custform.ICustomFormService;
import com.fenbeitong.saasplus.api.service.rule.IRuleV2Service;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyRuleDTO;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.fenbeitong.usercenter.api.model.enums.privilege.CodeTypeEnums;
import com.fenbeitong.usercenter.api.model.enums.privilege.CustomCodeType;
import com.fenbeitong.usercenter.api.model.po.rule.EmployeeTrainRule;
import com.fenbeitong.usercenter.api.service.company.ICompanyRuleService;
import com.fenbeitong.usercenter.api.service.privilege.IPrivilegeService;
import com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeTrainRuleService;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.DateUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.StrUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;


/**
 * Created by mac on 18/6/23.
 */
@Slf4j
@Service
public class TrainCheckV2ServiceImpl implements ITrainCheckV2Service {

    private static final Logger logger = LoggerFactory.getLogger(TrainCheckV2ServiceImpl.class);

    private static final String URL_GET_APPROVE_DATA = HostPropertyConfigTool.HOST_OPENAPI + "/open/hgm/company/approve_type";

    @Autowired
    private IBaseOrganizationService baseOrganizationService;
    @Autowired
    private ICompanyRuleService iCompanyRuleService;
    @Autowired
    private IOrderCheckService iOrderCheckService;
    @Autowired
    private TrainRuleMapper trainRuleMapper;
    @Autowired
    private TrainInterceptRecordMapper trainInterceptRecordMapper;
    @Autowired
    private IApplyFlowV2Service iApplyFlowV2Service;
    @Autowired
    private IBaseEmployeeTrainRuleService iBaseEmployeeTrainRuleService;
    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyRuleSettingExtMapper applyRuleSettingExtMapper;
    @Autowired
    private IMessageSetupService iMessageSetupService;
    @Autowired
    private IPrivilegeService iPrivilegeService;
    @Autowired
    private SetupService setupService;
    @Autowired
    private CurrencyCheckServiceImpl currencyCheckService;
    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyOrderMapper applyOrderMapper;
    @Autowired
    private ICustomFormService iCustomFormService;
    @Autowired
    private com.fenbeitong.saasplus.api.service.messageSetup.IMessageSetupService iMessageSetupRpcService;
    @Autowired
    private ICostAttributionService iCostAttributionService;
    @Autowired
    private ApplyAmountCheckResultService applyAmountCheckResultService;

    @Autowired
    private ICustomReasonService customReasonService;

    @Autowired
    private IRuleV2Service iRuleV2Service;

    @Autowired
    private TrainRuleV2CheckService trainRuleV2CheckService;

    @Autowired
    private IMongoDataService iMongoDataService;

    @Autowired
    private ConsumeTemplateRuleService consumeTemplateRuleService;

    @Autowired
    private ApplyV5ServiceImpl applyV5Service;

    @Autowired
    private TripEstimateService tripEstimateService;

    /**
     * 校验火车订单规则
     *
     * @param reqContract
     * @return
     * @throws SaasException
     */
    @Override
    public TravelOnBusiOrderCheckResContract trainOrderCheck(TrainOrderCheckReqV2Contract reqContract, String clientVersion) throws SaasException {
        DynamicDataSourceDecision.determineDataSource(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        // 返回结果
        TravelOnBusiOrderCheckResContract resContract = new TravelOnBusiOrderCheckResContract();
        //查询火车场景代预定配置，分预订人与出行人管控
        MessageSetupVO setupVO = setupService.queryBookingConfig(reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), BookingConfigEnum.TRAIN);
        TravelOnBusiOrderRuleCheckResult ruleCheckResult = new TravelOnBusiOrderRuleCheckResult();
        // 校验权限问题
        if (null != setupVO && setupVO.getIsChecked() && BookingTypeEnum.TRAVELER.getType().equals(setupVO.getBookingType())) {
            logger.info("按出行人管控,multitrainOrderRuleCheckResult");
            ruleCheckResult = multitrainOrderRuleCheckResult(reqContract, clientVersion);
            ruleCheckResult.setIsTraveBooking(true);
        } else {
            logger.info("按预订人管控,trainOrderRuleCheckResult");
            //申请单不是空 但是 tripid是空 需要兜底补齐tripid  和 业务申请单id
            if(StringUtils.isNotEmpty(reqContract.getTravel_on_busi_common_req_contract().getApply_id()) && StringUtils.isEmpty(reqContract.getTravel_on_busi_common_req_contract().getApply_trip_id())){
                //拼装酒店可用申请单请求数据
                ApplyTripValidRequestContract applyTripValidRequestContract = new ApplyTripValidRequestContract();
                applyTripValidRequestContract.setIsFilter(true);
                applyTripValidRequestContract.setCategory(BizType.Train.getCode());
                applyTripValidRequestContract.setStart_time(reqContract.getTravel_on_busi_parameter_req_contract().getStart_time());
                applyTripValidRequestContract.setEnd_time(reqContract.getTravel_on_busi_parameter_req_contract().getEnd_time());
                applyTripValidRequestContract.setArrival_city_id(reqContract.getTravel_on_busi_parameter_req_contract().getArrival_city_id());
                applyTripValidRequestContract.setStart_city_id(reqContract.getTravel_on_busi_parameter_req_contract().getStart_city_id());
                applyTripValidRequestContract.setVoyage_type(1);
                applyTripValidRequestContract.setCompany_id(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
                applyTripValidRequestContract.setUser_id(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id());
                applyTripValidRequestContract.setSource(1);
                applyTripValidRequestContract.setTrip_type(BizType.Train.getCode());
                List<ApplyOrderContract>  applyOrderList = applyV5Service.queryApplyList(applyTripValidRequestContract);
                List<ApplyOrderContract> applyList = applyOrderList.stream().filter(a->a.getUsable().equals(true) && a.getId().equals(reqContract.getTravel_on_busi_common_req_contract().getApply_id())).collect(Collectors.toList());
                log.info("可用申请单：{}",JsonUtils.toJson(applyList));
                if(ObjUtils.isNotEmpty(applyList)){
                    resContract.setTrip_id(applyList.get(0).getApply_trip_id());
                    resContract.setBusiness_apply_id(applyOrderList.get(0).getId());
                    reqContract.getTravel_on_busi_common_req_contract().setApply_trip_id(applyOrderList.get(0).getApply_trip_id());
                }
            }

            ruleCheckResult = trainOrderRuleCheckResult(reqContract, clientVersion);
            ruleCheckResult.setIsTraveBooking(false);
        }
        logger.info("校验火车订单规则，ruleCheckResult={}", JsonUtils.toJson(ruleCheckResult));
        if (StringUtils.isNotEmpty(ruleCheckResult.getDuring_reapply_id())) {
            ApplyOrder applyOrder = applyOrderMapper.selectByPrimaryKey(ruleCheckResult.getDuring_reapply_id());
            ruleCheckResult.setDuring_reapply_info(JsonUtils.toJson(applyOrder));
        }
        resContract.setTraveBooking(ruleCheckResult.getIsTraveBooking()); // 设置管控模式
        resContract.setTicket_price_info_list(ruleCheckResult.getTicket_price_info_list());
        resContract.setErr_code(ruleCheckResult.getErrCode());
        resContract.setErr_msg(ruleCheckResult.getErrMsg());
        resContract.setExceed_msg(ruleCheckResult.getExceed_msg());
        resContract.setPersonal_pay(ruleCheckResult.getPersonalPay());
        resContract.setLimit_price(ruleCheckResult.getLimitPrice());
        resContract.setLimit_type(ruleCheckResult.getLimitType());
        resContract.setIs_price_exceed(ruleCheckResult.getIsPriceExceed());
        resContract.setIs_estimated_amt_exceed(ruleCheckResult.getIs_estimated_amt_exceed());
        resContract.setMin_price_limit(ruleCheckResult.getMinPriceLimit());
        resContract.setPrice_exceed_sum(ruleCheckResult.getPriceExceedSum());
        resContract.setCost_attribution_scope(ruleCheckResult.getCost_attribution_scope());
        resContract.setBudget_cost_attr_type(ruleCheckResult.getBudget_cost_attr_type());
        resContract.setPay_model(ruleCheckResult.getPayModel());
        resContract.setCompany_pay_price(ruleCheckResult.getCompanyPayPrice());
        resContract.setPersonal_pay_price(ruleCheckResult.getPersonalPayPrice());
        resContract.setReimbursable_price(ruleCheckResult.getReimbursablePrice());
        resContract.setUnreimbursable_price(ruleCheckResult.getUnreimbursablePrice());
        resContract.setAmount_compliance(ruleCheckResult.getAmountCompliance());
        resContract.setAmount_non_compliance(ruleCheckResult.getAmountNonCompliance());
        resContract.setCoupon_used_amount(ruleCheckResult.getCouponUsedAmount());
        resContract.setCurrency_error_message(ruleCheckResult.getCurrencyErrorMessage());
        resContract.setTraveBooking(ruleCheckResult.getIsTraveBooking());
        resContract.setControl_dimension_desc(ruleCheckResult.getControlDimensionDesc());
        //处理返回信息替换文案
        if (null != ruleCheckResult.getCurrencyErrorMessage()) {
            String s = JsonUtils.toJsonSnake(ruleCheckResult.getCurrencyErrorMessage());
            s = s.replace(CoreLanguage.Common_Value_HotelPerson.getMessage(), CoreLanguage.Common_Value_TravelPerson.getMessage());
            CurrencyErrorMessage currencyErrorMessage = JSON.parseObject(s, CurrencyErrorMessage.class);
            if (null != currencyErrorMessage) {
                resContract.setCurrency_error_message(currencyErrorMessage);
            }
        }
        if (ruleCheckResult.getIs_exceed() || reqContract.getTravel_on_busi_parameter_req_contract().getExceed_submit()) {
            resContract.setIs_exceed(true);
        }
        resContract.setDuring_reapply_id(ruleCheckResult.getDuring_reapply_id());
        resContract.setDuring_reapply_info(ruleCheckResult.getDuring_reapply_info());
        if (ruleCheckResult.getErrCode() == GlobalResponseCode.Success.getCode()) {
            // 组装每个出行人的费用信息
            CompanyRuleSummary companyRuleSummary = ruleCheckResult.getCompanyRuleSummary();
            List<PersonCost> personCostList = new ArrayList<>();
            if (null != companyRuleSummary && !CollectionUtils.isEmpty(companyRuleSummary.getEmployeeRuleSummaryList())) {
                List<PersonCost> personCosts = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(rule -> {
                    PersonCost personCost = new PersonCost();
                    personCost.setEmployeeId(rule.getEmployeeId());
                    personCost.setAmount(rule.getCompanyPayPrice().subtract(rule.getCouponUsedAmount()));
                    personCost.setApplyId(rule.getApplyId());
                    personCost.setApplyTripId(rule.getTripId());
                    return personCost;
                }).collect(Collectors.toList());
                personCostList.addAll(personCosts);
                resContract.setPerson_cost_list(personCosts);
            }

            BigDecimal costAmount = resContract.getCompany_pay_price();

            try {
                Integer costInfoType = reqContract.getCost_info_type();
                if (costInfoType != null && costInfoType == 2) {
                    // 自定义字段转化
                    List<CustomDimension> customDimensionList =
                        ConvertUtils.convertToCustomDimension(reqContract.getCustomer_field_setting_list());

                    TempOrderCheckResContract costResult = iOrderCheckService.saveCostTicketNew(
                            reqContract.getTravel_on_busi_common_req_contract().getOrder_id(),
                            reqContract.getTravel_on_busi_common_req_contract().getCompany_id(),
                            reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(),
                            reqContract.getTravel_on_busi_common_req_contract().getApply_id(),
                            reqContract.getTravel_on_busi_common_req_contract().getApply_trip_id(),
                            BudgetCategoryTypeEnum.Train,
                            costAmount,
                            reqContract.getCost_info_ticket_list(),
                            reqContract.getCostInfoTicketListString(),
                            clientVersion, customDimensionList,
                            ruleCheckResult.getTicket_price_info_list(),reqContract.getTravel_on_busi_parameter_req_contract().getStart_time());






                    if (costResult.getErr_code() != GlobalResponseCode.Success.getCode()) {
                        resContract.setErr_code(costResult.getErr_code());
                        resContract.setErr_msg(costResult.getErr_msg());
                    } else {
                        resContract.setCost_id_ticket_list(costResult.getCost_id_ticket_list());
                    }
                } else {
                    // 自定义字段转化
                    List<CustomDimension> customDimensionList =
                        ConvertUtils.convertToCustomDimension(reqContract.getCustomer_field_setting_list());
                    TempOrderCheckResContract costResult = iOrderCheckService.saveCost(
                            reqContract.getTravel_on_busi_common_req_contract().getOrder_id(),
                            reqContract.getTravel_on_busi_common_req_contract().getCompany_id(),
                            reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(),
                            reqContract.getTravel_on_busi_common_req_contract().getApply_id(),
                            reqContract.getTravel_on_busi_common_req_contract().getApply_trip_id(),
                            BudgetCategoryTypeEnum.Train,
                            costAmount,
                            reqContract.getCost_info(),
                            reqContract.getCostInfoString(),
                            clientVersion,
                            null,
                            personCostList, customDimensionList,reqContract.getTravel_on_busi_parameter_req_contract().getStart_time());
                    if (costResult.getErr_code() != GlobalResponseCode.Success.getCode()) {
                        resContract.setErr_code(costResult.getErr_code());
                        resContract.setErr_msg(costResult.getErr_msg());
                    } else {
                        resContract.setCost_id(costResult.getCost_id());
                    }
                }
            } catch (FinhubException e) {
                if (e.getCode() == 20090) {
                    throw new SaasException(e.getCode(), e.getMessage(), FinhubMessageType.TIP_WINDOW);
                } else {
                    throw new SaasException(e.getCode(), e.getMessage(), FinhubMessageType.TIP_TOAST);
                }
            }
        }
        
        // errMsgInfo
        AirRuleUtils.setErrMsgInfo(resContract, ruleCheckResult);
        // 错误信息类型
        TravelOnBusiOrderCheckResContract travelOnBusiOrderCheckResContract = iOrderCheckService.travelOnBusiOrderCheckResContractCommon(resContract, ruleCheckResult);
        if (null != setupVO && setupVO.getIsChecked() && BookingTypeEnum.TRAVELER.getType().equals(setupVO.getBookingType())) {
            travelOnBusiOrderCheckResContract = resContract;
        }

        if(CollectionUtils.isNotEmpty(ruleCheckResult.getPersonnelInfoList())){
            //添加订单元数据  一人多规则 需要存储每个人的信息
            List<CustomizeVO> customizeVOs = com.google.common.collect.Lists.newArrayList();
            CustomizeVO customizeVO = new CustomizeVO();
            customizeVO.setDataId("saasControlDimension" + reqContract.getTravel_on_busi_common_req_contract().getOrder_id());
            customizeVO.setDataContent(JsonUtils.toJson(ruleCheckResult.getPersonnelInfoList()));
            customizeVOs.add(customizeVO);
            iMongoDataService.batchSave(customizeVOs, ObjectNameEnum.ORDER_INNER.getKey());
        }
        return travelOnBusiOrderCheckResContract;
    }


    /**
     * 校验机票订单申请改签规则
     *
     * @param reqContract
     * @return
     * @throws SaasException
     */
    @Override
    public TravelOnBusiOrderCheckResContract airChangeOrderCheck(ChangeOrderCheckReqContract reqContract, String clientVersion) throws SaasException {
        DynamicDataSourceDecision.determineDataSource(reqContract.getCompany_id());
        // 返回结果
        TravelOnBusiOrderCheckResContract resContract = new TravelOnBusiOrderCheckResContract();
        // 校验权限问题
        TravelOnBusiOrderRuleCheckResult ruleCheckResult = airChangeOrderRuleCheckResult(reqContract, clientVersion);
        if (StringUtils.isNotEmpty(ruleCheckResult.getDuring_reapply_id())) {
            ApplyOrder applyOrder = applyOrderMapper.selectByPrimaryKey(ruleCheckResult.getDuring_reapply_id());
            ruleCheckResult.setDuring_reapply_info(JsonUtils.toJson(applyOrder));
        }
        resContract.setErr_code(ruleCheckResult.getErrCode());
        resContract.setErr_msg(ruleCheckResult.getErrMsg());
        resContract.setExceed_msg(ruleCheckResult.getExceed_msg());
        resContract.setIs_price_exceed(ruleCheckResult.getIsPriceExceed());
        resContract.setCurrency_error_message(ruleCheckResult.getCurrencyErrorMessage());
        resContract.setCompany_pay_price(ruleCheckResult.getCompanyPayPrice());
        resContract.setTraveBooking(ruleCheckResult.getIsTraveBooking());
        resContract.setPersonal_pay(ruleCheckResult.getPersonalPay());
        resContract.setPersonal_pay_price(ruleCheckResult.getPersonalPayPrice());
        if (ruleCheckResult.getIs_exceed()) {
            resContract.setIs_exceed(true);
        }

        AirRuleUtils.setErrMsgInfo(resContract, ruleCheckResult);
        
        resContract.setIs_change_reason(ruleCheckResult.getIsChangeReason());
        resContract.setIs_change_exceed_reason(ruleCheckResult.getIsExceedReason());
        resContract.setDuring_reapply_id(ruleCheckResult.getDuring_reapply_id());
        resContract.setDuring_reapply_info(ruleCheckResult.getDuring_reapply_info());
        return resContract;
    }

    /**
     * 校验机票订单申请改签规则
     *
     * @param reqContract
     * @return
     * @throws SaasException
     */
    @Override
    public TravelOnBusiOrderCheckResContract intlAirChangeOrderCheck(ChangeOrderCheckReqContract reqContract,
        String clientVersion) throws SaasException {
        DynamicDataSourceDecision.determineDataSource(reqContract.getCompany_id());
        // 返回结果
        TravelOnBusiOrderCheckResContract resContract = new TravelOnBusiOrderCheckResContract();
        // 校验权限问题
        TravelOnBusiOrderRuleCheckResult ruleCheckResult = intlAirChangeOrderRuleCheckResult(reqContract, clientVersion);
        if (StringUtils.isNotEmpty(ruleCheckResult.getDuring_reapply_id())) {
            ApplyOrder applyOrder = applyOrderMapper.selectByPrimaryKey(ruleCheckResult.getDuring_reapply_id());
            ruleCheckResult.setDuring_reapply_info(JsonUtils.toJson(applyOrder));
        }
        resContract.setErr_code(ruleCheckResult.getErrCode());
        resContract.setErr_msg(ruleCheckResult.getErrMsg());
        resContract.setExceed_msg(ruleCheckResult.getExceed_msg());
        resContract.setIs_price_exceed(ruleCheckResult.getIsPriceExceed());
        resContract.setCurrency_error_message(ruleCheckResult.getCurrencyErrorMessage());
        resContract.setCompany_pay_price(ruleCheckResult.getCompanyPayPrice());
        resContract.setTraveBooking(ruleCheckResult.getIsTraveBooking());
        resContract.setPersonal_pay(ruleCheckResult.getPersonalPay());
        resContract.setPersonal_pay_price(ruleCheckResult.getPersonalPayPrice());
        if (ruleCheckResult.getIs_exceed()) {
            resContract.setIs_exceed(true);
        }

        AirRuleUtils.setErrMsgInfo(resContract, ruleCheckResult);

        resContract.setIs_change_reason(ruleCheckResult.getIsChangeReason());
        resContract.setIs_change_exceed_reason(ruleCheckResult.getIsExceedReason());
        resContract.setDuring_reapply_id(ruleCheckResult.getDuring_reapply_id());
        resContract.setDuring_reapply_info(ruleCheckResult.getDuring_reapply_info());
        return resContract;
    }

    /**
     * 校验机票订单申请改签规则
     * @param reqContract
     * @param clientVersion
     * @return
     * @throws SaasException
     */
    private TravelOnBusiOrderRuleCheckResult airChangeOrderRuleCheckResult(ChangeOrderCheckReqContract reqContract, String clientVersion) throws SaasException{
        // 默认初始化正常
        TravelOnBusiOrderRuleCheckResult checkResult = new TravelOnBusiOrderRuleCheckResult();
        String oldVersion = AirRuleConstant.AIR_EXCEED_PERSONAL_PAY_CLIENT_VERSION;
        //公司id
        String companyId = reqContract.getCompany_id();
        //预定人idpersonal_pay_price
        String employeeId = reqContract.getEmployee_id();
        // 预订人状态
        EmployeeContract orderEmployee = baseOrganizationService.getEmployee(employeeId, companyId);
        log.info("[预订人状态]:{},员工编号：{},公司ID：{}", JsonUtils.toJson(orderEmployee),employeeId,companyId);
        if (orderEmployee == null || orderEmployee.getStatus() != EmployeeStatusEnum.ACTIVE.getValue()) {
            //设置状态码：100001 您的状态未启用,不允许进行消费交易
            checkResult.setResCode(GlobalResponseCode.OrderCheckEmployeeNotActive);
            return checkResult;
        }

        //获取改期人员信息
        List<TravelOnBusiOrderCheckReqV2Contract.FrequentInfo> frequentInfoList = reqContract.getChange_travel_list();

        //初始化权限信息
        CompanyRuleSummary companyRuleSummary = currencyCheckService.assembleChangeEmployeeRuleSummary(companyId, BookingConfigEnum.AIR, frequentInfoList,reqContract,clientVersion);

        //设置是否是个人原因
        companyRuleSummary.setIsPersonalReasons(Objects.equals(reqContract.getAir_change_req().getIs_personal_reasons(),1));

        // 企业权限
        CompanyRuleDTO companyRule = iCompanyRuleService.queryByCompanyId(companyId);
        log.info("[企业权限]:{},公司ID：{}", JsonUtils.toJson(companyRule), companyId);

        companyRuleSummary.setCompanyRuleDTO(companyRule);
        if (companyRule == null || companyRule.getAirRule() != 1) {
            companyRuleSummary.setIsRule(true);
        }
        checkResult.setCompanyRuleSummary(companyRuleSummary);
        checkResult.setIsTraveBooking(companyRuleSummary.getIsTraveBooking());

        //初始化场景相关信息
        currencyCheckService.assemnleAirChangeSceneSummary(reqContract, companyRuleSummary, OrderCategory.Train, clientVersion);
        //设置改期每个人的最终结果集
        currencyCheckService.setEmployeeChangeResultEnum(companyRuleSummary);

        log.info("companyRuleSummary after assemnleAirChangeSceneSummary:{}", JsonUtils.toJson(companyRuleSummary));
        //企业统一阻断判断
        //1.企业火车权限未开启
        if (companyRuleSummary.getIsRule()) {
            //设置状态码：100002 您所在的公司不允许火车,具体情况请联系管理员
            checkResult.setResCode(GlobalResponseCode.OrderCheckCompanyNoAuth.getCode(), CoreLanguage.Common_Exception_TrainEmployeeNoAuthMsg.getMessage());
            return checkResult;
        }
        //2.企业余额不足
        else if(companyRuleSummary.getIsCompanyAccount()){
            //设置状态码：100002 您所在的公司不允许订购酒店,具体情况请联系管理员
            checkResult.setResCode(GlobalResponseCode.OrderCheckCompanyAccountNoAuth.getCode(), GlobalResponseCode.OrderCheckCompanyAccountNoAuth.getMsg());
            return checkResult;
        }
        //3.有改签规则 且开启新版规则且版本号低于 5.1.10 提示升级
        else if(companyRuleSummary.getIsOpenExceedConfig() && companyRuleSummary.getEmployeeRuleSummaryList().stream()
            .anyMatch(EmployeeRuleSummary::getChangeRuleFlag) && VersionTool.compare(clientVersion, oldVersion) < 0)
        {
            checkResult.setResCode(GlobalResponseCode.AirTrainCenterAlert.getCode(),GlobalResponseCode.AirTrainCenterAlert.getMsg());
            return checkResult;
        }
        //4.没有人有改签规则 且版本号 低于5.1.7 直接返回通过
        else if (VersionTool.compare(clientVersion, oldVersion) < 0 ){
            checkResult.setResCode(GlobalResponseCode.Success);
            return checkResult;
        }
        //5.多人切个人原因改期 ，个人原因规则不一致
        if(companyRuleSummary.getIsPersonalReasonConflict()){
            checkResult.setResCode(GlobalResponseCode.AirTrainCenterAlert.getCode(),GlobalResponseCode.ChangePersonRuleNotMatch.getMsg());
            return checkResult;
        }

        // 5.1 规则不一致 单独改期
        if (companyRuleSummary.getIsRuleConflict()) {
            checkResult.setResCode(GlobalResponseCode.AirTrainCenterAlert.getCode(),
                GlobalResponseCode.ChangeRuleNotSame.getMsg());
            return checkResult;
        }



        //校验机票是否超规
        currencyCheckService.checkChangeAirRule(reqContract, companyRuleSummary);

        log.info("校验超规后companyRuleSummary:{}", JsonUtils.toJson(companyRuleSummary));

        // 5.2 有超规且版本低于5.1.10提示升级
        if (companyRuleSummary.getEmployeeRuleSummaryList().stream().anyMatch(EmployeeRuleSummary::getIsExceed)
            && VersionTool.compare(clientVersion, oldVersion) < 0) {
            checkResult.setResCode(GlobalResponseCode.AirTrainCenterAlert.getCode(),GlobalResponseCode.AirTrainCenterAlert.getMsg());
            return checkResult;
        }

        //6.多人有超规的情况下 个人原因提示只能一个人一个人改签
        if(companyRuleSummary.getIsPersonalReasons() && companyRuleSummary.getEmployeeRuleSummaryList().size() > 1) {
            checkResult.setResCode(GlobalResponseCode.AirTrainCenterAlert.getCode(),GlobalResponseCode.ChangePersonRuleNotMatch.getMsg());
            return checkResult;
        }

        // 7. 多人有超规 提示一单单下
        if(companyRuleSummary.getEmployeeRuleSummaryList().stream().anyMatch(EmployeeRuleSummary::getIsExceed)
            && companyRuleSummary.getEmployeeRuleSummaryList().size() > 1) {
            checkResult.setResCode(GlobalResponseCode.AirTrainCenterAlert.getCode(),GlobalResponseCode.ChangeRuleNotMatch.getMsg());
            return checkResult;
        }

        checkResult.setCompanyPayPrice(reqContract.getAir_change_req().getAir_change_sum_price());

        //设置改期每个人的最终结果集
        currencyCheckService.setEmployeeChangeResultEnum(companyRuleSummary);
        //设置总体返回是否有费用项超规
        checkResult.setIsPriceExceed(companyRuleSummary.getEmployeeRuleSummaryList().stream().anyMatch(a->a.getIsPriceExceed()==true));
        //设置返回是否超规
        checkResult.setIs_exceed(companyRuleSummary.getEmployeeRuleSummaryList().stream().anyMatch(a->a.getIsExceed()==true));
        //获取多人校验结果 统一返回值处理
        CurrencyErrorMessage currencyErrorMassage = currencyCheckService.getChangePlayerResults(companyRuleSummary,
            checkResult, BizType.AirPlane.getCode(), reqContract.getRe_submit());
        log.info("currencyErrorMassage:{}",JsonUtils.toJson(currencyErrorMassage));
        //说明无法下单 需要给前端相应提示
        if(currencyErrorMassage.getCode() != GlobalResponseCode.Success.getCode()){
            checkResult.setCurrencyErrorMessage(currencyErrorMassage);
            checkResult.setResCode(GlobalResponseCode.CurrencyFrame);
            //判断一下是否是 订单审批 还是超规审批 查看历史有没有审批通过的审批单，如果有直接返回前端标识， 二次提交放行
            if(GlobalResponseCode.CheckOrderApproval.getCode()== currencyErrorMassage.getCode()
                    || GlobalResponseCode.RemindChangeApproval.getCode() == currencyErrorMassage.getCode()) {
                String duringApplyId = currencyCheckService.queryOrderApplyMsg(reqContract,CategoryTypeEnum.Air,
                    checkResult.getIs_exceed(), reqContract.getAir_change_req().getAir_change_sum_price(),
                    reqContract.getArrival_city_id());
                logger.info("duringApplyId:{}", duringApplyId);
                if (StringUtils.isNotBlank(duringApplyId)) {
                    //二次订单审批 直接放行
                    if (!reqContract.getExist_center_apply_submit()) {
                        logger.info("符合条件无需审批，弹窗提示");
                        checkResult.setErrCode(GlobalResponseCode.OrderCheckExceedApplytAirHint.getCode());
                        checkResult.setErrMsg(GlobalResponseCode.OrderCheckExceedApplytAirHint.getMsg());
                        return checkResult;
                    } else {
                        logger.info("符合条件无需审批，返回审批单id和超规标识");
                        checkResult.setDuring_reapply_id(duringApplyId);
                        checkResult.setResCode(GlobalResponseCode.Success);
                        return checkResult;
                    }
                }
            }
            //超规全额付 无需理由
            if(GlobalResponseCode.ChangeAllPayPriceDesc.getCode() == currencyErrorMassage.getCode()) {
                //设置公司支付金额 0
                checkResult.setCompanyPayPrice(BigDecimal.ZERO);
                checkResult.setPersonalPay(true);
                //个人支付金额为全额
                checkResult.setPersonalPayPrice(reqContract.getAir_change_req().getAir_change_sum_price());
            }

            // 填写理由+个人支付
            if (GlobalResponseCode.ChangeAllPayDescWithReason.getCode() == currencyErrorMassage.getCode()) {
                //设置公司支付金额
                BigDecimal companyPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
                    EmployeeRuleSummary::getCompanyPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal personalPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
                    EmployeeRuleSummary::getPersonalPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                checkResult.setCompanyPayPrice(companyPayPrice);
                checkResult.setPersonalPayPrice(personalPayPrice);
                checkResult.setPersonalPay(true);
            }

            //二次提交
            if(reqContract.getRe_submit()){
                //订单审批 || 全额付直接放行
                if(GlobalResponseCode.CheckOrderApproval.getCode()== currencyErrorMassage.getCode()
                    || GlobalResponseCode.ChangeAllPayPriceDesc.getCode()== currencyErrorMassage.getCode() ){
                    checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                    checkResult.setResCode(GlobalResponseCode.Success);
                }
                //二次提交且标记成全额个人支付后
                if(GlobalResponseCode.CheckExceedReasonAndPersonalPay.getCode() == currencyErrorMassage.getCode()
                    && reqContract.getRe_personal_pay_submit()){
                    checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                    checkResult.setResCode(GlobalResponseCode.Success);
                }

                // 填写理由+个人支付
                if (GlobalResponseCode.ChangeAllPayDescWithReason.getCode() == currencyErrorMassage.getCode()) {
                    //设置公司支付金额
                    BigDecimal companyPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
                        EmployeeRuleSummary::getCompanyPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal personalPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
                        EmployeeRuleSummary::getPersonalPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                    checkResult.setCompanyPayPrice(companyPayPrice);
                    checkResult.setPersonalPayPrice(personalPayPrice);
                    checkResult.setPersonalPay(true);

                    //校验填写项是否正确
                    boolean flag = checkResult.getIsChangeReason();
                    TravelOnBusiOrderRuleCheckResult travelOnBusiOrderRuleCheckResult =
                        currencyCheckService.checkExceedAuthV2(reqContract,currencyErrorMassage, CategoryTypeEnum.Air,
                            flag);
                    if (travelOnBusiOrderRuleCheckResult.getErrCode()!=GlobalResponseCode.Success.getCode()) {
                        checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                        checkResult.setErrCode(travelOnBusiOrderRuleCheckResult.getErrCode());
                        checkResult.setErrMsg(travelOnBusiOrderRuleCheckResult.getErrMsg());
                    } else {
                        // 标记成全额个人支付后
                        if (Objects.equals(reqContract.getRe_personal_pay_submit(), true)) {
                            checkResult.setResCode(GlobalResponseCode.Success);
                        }
                        checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                    }
                }

                //改签或超规填写理由
                if(GlobalResponseCode.CheckExceedReason.getCode() == currencyErrorMassage.getCode() ||
                    GlobalResponseCode.CheckChangeReason.getCode() == currencyErrorMassage.getCode() ||
                    GlobalResponseCode.CheckAndChangeReason.getCode() == currencyErrorMassage.getCode() ||
                    GlobalResponseCode.CheckReason.getCode()== currencyErrorMassage.getCode() ||
                    GlobalResponseCode.RemindChangeReason.getCode() == currencyErrorMassage.getCode()
                ){
                    // 添加是否是个人理由和企业理由
                    ExceedPersonalPayCompanyConf conf = iMessageSetupService.getPersonalPayReasonConfBy(companyId);
                    checkResult.setExceedPersonalPayCompanyConf(conf);

                    //校验填写项是否正确
                    boolean flag = checkResult.getIsChangeReason();
                    TravelOnBusiOrderRuleCheckResult travelOnBusiOrderRuleCheckResult =
                        currencyCheckService.checkExceedAuthV2(reqContract,currencyErrorMassage, CategoryTypeEnum.Air,
                            flag);
                    if(travelOnBusiOrderRuleCheckResult.getErrCode()!=GlobalResponseCode.Success.getCode()){
                        checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                        checkResult.setErrCode(travelOnBusiOrderRuleCheckResult.getErrCode());
                        checkResult.setErrMsg(travelOnBusiOrderRuleCheckResult.getErrMsg());
                    }else{
                        checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                        checkResult.setResCode(GlobalResponseCode.Success);
                    }
                }
            }
        }

        //增加快照信息
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("companyRuleSummary",companyRuleSummary);
        checkResult.setSnapshotInfo(jsonObject);
        //完整日志打印
        log.info("companyRuleSummary-{}",JsonUtils.toJson(companyRuleSummary));
        return checkResult;
    }


    /**
     * 校验机票订单申请改签规则
     * @param reqContract
     * @param clientVersion
     * @return
     * @throws SaasException
     */
    private TravelOnBusiOrderRuleCheckResult intlAirChangeOrderRuleCheckResult(ChangeOrderCheckReqContract reqContract,
        String clientVersion) throws SaasException{
        // 默认初始化正常
        TravelOnBusiOrderRuleCheckResult checkResult = new TravelOnBusiOrderRuleCheckResult();
        //公司id
        String companyId = reqContract.getCompany_id();
        //预定人idpersonal_pay_price
        String employeeId = reqContract.getEmployee_id();
        // 预订人状态
        EmployeeContract orderEmployee = baseOrganizationService.getEmployee(employeeId, companyId);
        log.info("[预订人状态]:{},员工编号：{},公司ID：{}", JsonUtils.toJson(orderEmployee),employeeId,companyId);
        if (orderEmployee == null || orderEmployee.getStatus() != EmployeeStatusEnum.ACTIVE.getValue()) {
            //设置状态码：100001 您的状态未启用,不允许进行消费交易
            checkResult.setResCode(GlobalResponseCode.OrderCheckEmployeeNotActive);
            return checkResult;
        }

        //获取改期人员信息
        List<TravelOnBusiOrderCheckReqV2Contract.FrequentInfo> frequentInfoList = reqContract.getChange_travel_list();

        //初始化权限信息
        CompanyRuleSummary companyRuleSummary = currencyCheckService.assembleChangeEmployeeRuleSummary(companyId,
            BookingConfigEnum.INTL_AIR, frequentInfoList, reqContract, clientVersion);

        // 企业权限
        CompanyRuleDTO companyRule = iCompanyRuleService.queryByCompanyId(companyId);
        log.info("[企业权限]:{},公司ID：{}", JsonUtils.toJson(companyRule), companyId);

        companyRuleSummary.setCompanyRuleDTO(companyRule);
        if (companyRule == null || companyRule.getAirRule() != 1) {
            companyRuleSummary.setIsRule(true);
        }
        checkResult.setCompanyRuleSummary(companyRuleSummary);
        checkResult.setIsTraveBooking(companyRuleSummary.getIsTraveBooking());

        //初始化场景相关信息
        currencyCheckService.assembleIntlAirChangeSceneSummary(reqContract, companyRuleSummary);
        //设置改期每个人的最终结果集
        currencyCheckService.setEmployeeChangeResultEnum(companyRuleSummary);

        log.info("companyRuleSummary after assembleIntlAirChangeSceneSummary:{}", JsonUtils.toJson(companyRuleSummary));
        //企业统一阻断判断
        //1.企业火车权限未开启
        if (companyRuleSummary.getIsRule()) {
            //设置状态码：100002 您所在的公司不允许火车,具体情况请联系管理员
            checkResult.setResCode(GlobalResponseCode.OrderCheckCompanyNoAuth.getCode(), CoreLanguage.Common_Exception_TrainEmployeeNoAuthMsg.getMessage());
            return checkResult;
        }
        //2.企业余额不足
        else if(companyRuleSummary.getIsCompanyAccount()){
            //设置状态码：100002 您所在的公司不允许订购酒店,具体情况请联系管理员
            checkResult.setResCode(GlobalResponseCode.OrderCheckCompanyAccountNoAuth.getCode(), GlobalResponseCode.OrderCheckCompanyAccountNoAuth.getMsg());
            return checkResult;
        }

        //设置改期每个人的最终结果集
        currencyCheckService.setEmployeeChangeResultEnum(companyRuleSummary);

        //获取多人校验结果 统一返回值处理
        CurrencyErrorMessage currencyErrorMassage = currencyCheckService.getChangePlayerResults(companyRuleSummary,
            checkResult, BizType.InternationalAirPlane.getCode(), reqContract.getRe_submit());
        log.info("currencyErrorMassage:{}",JsonUtils.toJson(currencyErrorMassage));
        //说明无法下单 需要给前端相应提示
        if(currencyErrorMassage.getCode() != GlobalResponseCode.Success.getCode()){
            checkResult.setCurrencyErrorMessage(currencyErrorMassage);
            checkResult.setResCode(GlobalResponseCode.CurrencyFrame);
//            //判断一下是否是 订单审批 还是超规审批 查看历史有没有审批通过的审批单，如果有直接返回前端标识， 二次提交放行
//            if(GlobalResponseCode.CheckOrderApproval.getCode()== currencyErrorMassage.getCode()
//                || GlobalResponseCode.RemindChangeApproval.getCode() == currencyErrorMassage.getCode()) {
//                String duringApplyId = currencyCheckService.queryOrderApplyMsg(reqContract,CategoryTypeEnum.Air,
//                    checkResult.getIs_exceed(), reqContract.getAir_change_req().getAir_change_sum_price(),
//                    reqContract.getArrival_city_id());
//                logger.info("duringApplyId:{}", duringApplyId);
//                if (StringUtils.isNotBlank(duringApplyId)) {
//                    //二次订单审批 直接放行
//                    if (!reqContract.getExist_center_apply_submit()) {
//                        logger.info("符合条件无需审批，弹窗提示");
//                        checkResult.setErrCode(GlobalResponseCode.OrderCheckExceedApplytAirHint.getCode());
//                        checkResult.setErrMsg(GlobalResponseCode.OrderCheckExceedApplytAirHint.getMsg());
//                        return checkResult;
//                    } else {
//                        logger.info("符合条件无需审批，返回审批单id和超规标识");
//                        checkResult.setDuring_reapply_id(duringApplyId);
//                        checkResult.setResCode(GlobalResponseCode.Success);
//                        return checkResult;
//                    }
//                }
//            }
//            //超规全额付 无需理由
//            if(GlobalResponseCode.ChangeAllPayPriceDesc.getCode() == currencyErrorMassage.getCode()) {
//                //设置公司支付金额 0
//                checkResult.setCompanyPayPrice(BigDecimal.ZERO);
//                checkResult.setPersonalPay(true);
//                //个人支付金额为全额
//                checkResult.setPersonalPayPrice(reqContract.getAir_change_req().getAir_change_sum_price());
//            }
//
//            // 填写理由+个人支付
//            if (GlobalResponseCode.ChangeAllPayDescWithReason.getCode() == currencyErrorMassage.getCode()) {
//                //设置公司支付金额
//                BigDecimal companyPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
//                    EmployeeRuleSummary::getCompanyPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
//                BigDecimal personalPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
//                    EmployeeRuleSummary::getPersonalPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
//                checkResult.setCompanyPayPrice(companyPayPrice);
//                checkResult.setPersonalPayPrice(personalPayPrice);
//                checkResult.setPersonalPay(true);
//            }

            //二次提交
            if(reqContract.getRe_submit()){
                //订单审批 || 全额付直接放行
                if(GlobalResponseCode.CheckOrderApproval.getCode()== currencyErrorMassage.getCode()
                    || GlobalResponseCode.ChangeAllPayPriceDesc.getCode()== currencyErrorMassage.getCode() ){
                    checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                    checkResult.setResCode(GlobalResponseCode.Success);
                }
                //二次提交且标记成全额个人支付后
                if(GlobalResponseCode.CheckExceedReasonAndPersonalPay.getCode() == currencyErrorMassage.getCode()
                    && reqContract.getRe_personal_pay_submit()){
                    checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                    checkResult.setResCode(GlobalResponseCode.Success);
                }

                // 填写理由+个人支付
                if (GlobalResponseCode.ChangeAllPayDescWithReason.getCode() == currencyErrorMassage.getCode()) {
                    //设置公司支付金额
                    BigDecimal companyPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
                        EmployeeRuleSummary::getCompanyPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal personalPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
                        EmployeeRuleSummary::getPersonalPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                    checkResult.setCompanyPayPrice(companyPayPrice);
                    checkResult.setPersonalPayPrice(personalPayPrice);
                    checkResult.setPersonalPay(true);

                    //校验填写项是否正确
                    boolean flag = checkResult.getIsChangeReason();
                    TravelOnBusiOrderRuleCheckResult travelOnBusiOrderRuleCheckResult =
                        currencyCheckService.checkExceedAuthV2(reqContract,currencyErrorMassage, CategoryTypeEnum.Air,
                            flag);
                    if (travelOnBusiOrderRuleCheckResult.getErrCode()!=GlobalResponseCode.Success.getCode()) {
                        checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                        checkResult.setErrCode(travelOnBusiOrderRuleCheckResult.getErrCode());
                        checkResult.setErrMsg(travelOnBusiOrderRuleCheckResult.getErrMsg());
                    } else {
                        // 标记成全额个人支付后
                        if (Objects.equals(reqContract.getRe_personal_pay_submit(), true)) {
                            checkResult.setResCode(GlobalResponseCode.Success);
                        }
                        checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                    }
                }

                //改签或超规填写理由
                if(GlobalResponseCode.CheckExceedReason.getCode() == currencyErrorMassage.getCode() ||
                    GlobalResponseCode.CheckChangeReason.getCode() == currencyErrorMassage.getCode() ||
                    GlobalResponseCode.CheckAndChangeReason.getCode() == currencyErrorMassage.getCode() ||
                    GlobalResponseCode.CheckReason.getCode()== currencyErrorMassage.getCode() ||
                    GlobalResponseCode.RemindChangeReason.getCode() == currencyErrorMassage.getCode()
                ){
                    // 添加是否是个人理由和企业理由
                    ExceedPersonalPayCompanyConf conf = iMessageSetupService.getPersonalPayReasonConfBy(companyId);
                    checkResult.setExceedPersonalPayCompanyConf(conf);

                    //校验填写项是否正确
                    boolean flag = checkResult.getIsChangeReason();
                    TravelOnBusiOrderRuleCheckResult travelOnBusiOrderRuleCheckResult =
                        currencyCheckService.checkExceedAuthV2(reqContract,currencyErrorMassage, CategoryTypeEnum.Air,
                            flag);
                    if(travelOnBusiOrderRuleCheckResult.getErrCode()!=GlobalResponseCode.Success.getCode()){
                        checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                        checkResult.setErrCode(travelOnBusiOrderRuleCheckResult.getErrCode());
                        checkResult.setErrMsg(travelOnBusiOrderRuleCheckResult.getErrMsg());
                    }else{
                        checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                        checkResult.setResCode(GlobalResponseCode.Success);
                    }
                }
            }
        }

        //增加快照信息
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("companyRuleSummary",companyRuleSummary);
        checkResult.setSnapshotInfo(jsonObject);
        //完整日志打印
        log.info("companyRuleSummary-{}",JsonUtils.toJson(companyRuleSummary));
        return checkResult;
    }

    /**
     * 校验火车订单申请改签规则
     *
     * @param reqContract
     * @return
     * @throws SaasException
     */
    @Override
    public TravelOnBusiOrderCheckResContract trainChangeOrderCheck(ChangeOrderCheckReqContract reqContract, String clientVersion) throws SaasException {
        DynamicDataSourceDecision.determineDataSource(reqContract.getCompany_id());
        // 返回结果
        TravelOnBusiOrderCheckResContract resContract = new TravelOnBusiOrderCheckResContract();
        // 校验权限问题
        TravelOnBusiOrderRuleCheckResult ruleCheckResult = trainChangeOrderRuleCheckResult(reqContract, clientVersion);
        if (StringUtils.isNotEmpty(ruleCheckResult.getDuring_reapply_id())) {
            ApplyOrder applyOrder = applyOrderMapper.selectByPrimaryKey(ruleCheckResult.getDuring_reapply_id());
            ruleCheckResult.setDuring_reapply_info(JsonUtils.toJson(applyOrder));
        }
        resContract.setErr_code(ruleCheckResult.getErrCode());
        resContract.setErr_msg(ruleCheckResult.getErrMsg());
        resContract.setExceed_msg(ruleCheckResult.getExceed_msg());
        resContract.setIs_price_exceed(ruleCheckResult.getIsPriceExceed());
        resContract.setCurrency_error_message(ruleCheckResult.getCurrencyErrorMessage());
        resContract.setTraveBooking(ruleCheckResult.getIsTraveBooking());
        resContract.setPersonal_pay(ruleCheckResult.getPersonalPay());
        resContract.setPersonal_pay_price(ruleCheckResult.getPersonalPayPrice());
        resContract.setCompany_pay_price(ruleCheckResult.getCompanyPayPrice());
        if (ruleCheckResult.getIs_exceed()) {
            resContract.setIs_exceed(true);
        }

        AirRuleUtils.setErrMsgInfo(resContract, ruleCheckResult);

        resContract.setIs_change_reason(ruleCheckResult.getIsChangeReason());
        resContract.setIs_change_exceed_reason(ruleCheckResult.getIsExceedReason());
        resContract.setDuring_reapply_id(ruleCheckResult.getDuring_reapply_id());
        resContract.setDuring_reapply_info(ruleCheckResult.getDuring_reapply_info());
        return resContract;
    }

    private TravelOnBusiOrderRuleCheckResult trainChangeOrderRuleCheckResult(ChangeOrderCheckReqContract reqContract, String clientVersion) throws SaasException{
        // 默认初始化正常
        TravelOnBusiOrderRuleCheckResult checkResult = new TravelOnBusiOrderRuleCheckResult();
        String oldVersion = "5.1.6";
        //公司id
        String companyId = reqContract.getCompany_id();
        //预定人id
        String employeeId = reqContract.getEmployee_id();
        // 预订人状态
        EmployeeContract orderEmployee = baseOrganizationService.getEmployee(employeeId, companyId);
        log.info("[预订人状态]:{},员工编号：{},公司ID：{}", JsonUtils.toJson(orderEmployee),employeeId,companyId);
        if (orderEmployee == null || orderEmployee.getStatus() != EmployeeStatusEnum.ACTIVE.getValue()) {
            //设置状态码：100001 您的状态未启用,不允许进行消费交易
            checkResult.setResCode(GlobalResponseCode.OrderCheckEmployeeNotActive);
            return checkResult;
        }

        //获取改期人员信息
        List<TravelOnBusiOrderCheckReqV2Contract.FrequentInfo> frequentInfoList = reqContract.getChange_travel_list();

        //初始化权限信息
        CompanyRuleSummary companyRuleSummary = currencyCheckService.assembleChangeEmployeeRuleSummary(companyId, BookingConfigEnum.TRAIN, frequentInfoList,reqContract,clientVersion);
        // 企业权限
        CompanyRuleDTO companyRule = iCompanyRuleService.queryByCompanyId(companyId);
        log.info("[企业权限]:{},公司ID：{}", JsonUtils.toJson(companyRule), companyId);

        companyRuleSummary.setCompanyRuleDTO(companyRule);
        if (companyRule == null || companyRule.getTrainRule() != 1) {
            companyRuleSummary.setIsRule(true);
        }
        checkResult.setCompanyRuleSummary(companyRuleSummary);
        checkResult.setIsTraveBooking(companyRuleSummary.getIsTraveBooking());

        //初始化场景相关信息
        currencyCheckService.assemnleChangeSceneSummary(reqContract, companyRuleSummary, OrderCategory.Train,clientVersion);
        //企业统一阻断判断
        //1.企业火车权限未开启
        if (companyRuleSummary.getIsRule()) {
            //设置状态码：100002 您所在的公司不允许火车,具体情况请联系管理员
            checkResult.setResCode(GlobalResponseCode.OrderCheckCompanyNoAuth.getCode(), CoreLanguage.Common_Exception_TrainEmployeeNoAuthMsg.getMessage());
            return checkResult;
        }
        //2.企业余额不足
        else if(companyRuleSummary.getIsCompanyAccount()){
            //设置状态码：100002 您所在的公司不允许订购酒店,具体情况请联系管理员
            checkResult.setResCode(GlobalResponseCode.OrderCheckCompanyAccountNoAuth.getCode(), GlobalResponseCode.OrderCheckCompanyAccountNoAuth.getMsg());
            return checkResult;
        }
        //3.有改签规则 且版本号低于 5.1.6 提示升级
        else if(companyRuleSummary.getEmployeeRuleSummaryList().stream().anyMatch(a->a.getChangeRuleFlag()) && VersionTool.compare(clientVersion, oldVersion) < 0 )
        {
            checkResult.setResCode(GlobalResponseCode.ApplyCenterAlert.getCode(),GlobalResponseCode.ApplyCenterAlert.getMsg());
            return checkResult;
        }
        //4.没有人有改签规则 且版本号 低于5.1.6 直接返回通过
        else if (VersionTool.compare(clientVersion, oldVersion) < 0 ){
            checkResult.setResCode(GlobalResponseCode.Success);
            return checkResult;
        }

        //校验火车是否超规
        currencyCheckService.checkChangeTrainRule(reqContract,companyRuleSummary);
        //设置改期每个人的最终结果集
        currencyCheckService.setEmployeeChangeResultEnum(companyRuleSummary);
        //设置总体返回是否有费用项超规
        checkResult.setIsPriceExceed(companyRuleSummary.getEmployeeRuleSummaryList().stream().anyMatch(a->a.getIsPriceExceed()==true));
        //设置返回是否超规
        checkResult.setIs_exceed(companyRuleSummary.getEmployeeRuleSummaryList().stream().anyMatch(a->a.getIsExceed()==true));

        //改签差价    改签票价 - 原票价， 设置公司默认值
        BigDecimal diffPirce = reqContract.getTrain_change_req().getChange_ticket_price().subtract(reqContract.getTrain_change_req().getTicket_price()).add(reqContract.getTrain_change_req().getService_fee());
        checkResult.setCompanyPayPrice(diffPirce);


        //获取多人校验结果 统一返回值处理
        CurrencyErrorMessage currencyErrorMassage = currencyCheckService.getChangePlayerResults(companyRuleSummary,
            checkResult, BizType.Train.getCode(), reqContract.getRe_submit());
        log.info("currencyErrorMassage:{}",JsonUtils.toJson(currencyErrorMassage));
        //说明无法下单 需要给前端相应提示
        if(currencyErrorMassage.getCode() != GlobalResponseCode.Success.getCode()){
            checkResult.setCurrencyErrorMessage(currencyErrorMassage);
            checkResult.setResCode(GlobalResponseCode.CurrencyFrame);
            if(GlobalResponseCode.RemindChangeApproval.getCode() == currencyErrorMassage.getCode() ||
                    GlobalResponseCode.RemindChangeReason.getCode() == currencyErrorMassage.getCode()
            ){
                checkResult.setResCode(GlobalResponseCode.CurrencyNoTitleFrame);
            }

            //判断一下是否是 订单审批 还是超规审批 查看历史有没有审批通过的审批单，如果有直接返回前端标识， 二次提交放行
            if (GlobalResponseCode.CheckOrderApproval.getCode() == currencyErrorMassage.getCode()
                    || GlobalResponseCode.RemindChangeApproval.getCode() == currencyErrorMassage.getCode()
                    || GlobalResponseCode.ExceedApprovalOrPartPayPriceDesc.getCode() == currencyErrorMassage.getCode()) {
                String duringApplyId = currencyCheckService.queryOrderApplyMsg(reqContract,CategoryTypeEnum.Train, checkResult.getIs_exceed(),reqContract.getTrain_change_req().getChange_ticket_price(),reqContract.getArrival_city_id());
                logger.info("duringApplyId:{}", duringApplyId);
                if (StringUtils.isNotBlank(duringApplyId)) {
                    //二次订单审批 直接放行
                    if (!reqContract.getExist_center_apply_submit()) {
                        logger.info("符合条件无需审批，弹窗提示");
                        if(checkResult.getIs_exceed()){
                            checkResult.setErrCode(GlobalResponseCode.OrderCheckExceedApplytTrainHint.getCode());
                            checkResult.setErrMsg(GlobalResponseCode.OrderCheckExceedApplytTrainHint.getMsg());
                        }else{
                            checkResult.setErrCode(GlobalResponseCode.OrderCheckApplytTrainHint.getCode());
                            checkResult.setErrMsg(GlobalResponseCode.OrderCheckApplytTrainHint.getMsg());
                        }

                        return checkResult;
                    } else {
                        logger.info("符合条件无需审批，返回审批单id和超规标识");
                        checkResult.setDuring_reapply_id(duringApplyId);
                        checkResult.setResCode(GlobalResponseCode.Success);
                        if (GlobalResponseCode.ExceedApprovalOrPartPayPriceDesc.getCode() == currencyErrorMassage.getCode()) {
                            //设置公司支付金额
                            BigDecimal companyPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
                                    EmployeeRuleSummary::getCompanyPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal personalPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
                                    EmployeeRuleSummary::getPersonalPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                            checkResult.setCompanyPayPrice(companyPayPrice.add(personalPayPrice));
                            checkResult.setPersonalPayPrice(BigDecimal.ZERO);
                            checkResult.setPersonalPay(false);
//                            for (PerTicketInfoRes perTicketInfoRes : checkResult.getTicket_price_info_list()) {
//                                // 公司支付金额 票价+保险-优惠券
//                                perTicketInfoRes.setCompany_pay_price(perTicketInfoRes.getCompany_pay_price().add(perTicketInfoRes.getPersonal_pay_price()));
//                                perTicketInfoRes.setPersonal_pay_price(BigDecimal.ZERO);
//                            }
                        }
                        return checkResult;
                    }
                }
            }

            //超规全额付 无需理由
            if(GlobalResponseCode.ChangeAllPayPriceDesc.getCode() == currencyErrorMassage.getCode()) {
                //设置公司支付金额 0
                checkResult.setCompanyPayPrice(BigDecimal.ZERO);
                checkResult.setPersonalPay(true);
                //个人支付金额为全额，火车只能一个人一个人改签
                checkResult.setPersonalPayPrice(companyRuleSummary.getEmployeeRuleSummaryList().get(0).getPersonalPayPrice());
            }

            // 填写理由+个人支付
            if (GlobalResponseCode.ChangeAllPayDescWithReason.getCode() == currencyErrorMassage.getCode()) {
                //设置公司支付金额
                BigDecimal companyPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
                    EmployeeRuleSummary::getCompanyPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal personalPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
                    EmployeeRuleSummary::getPersonalPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                checkResult.setCompanyPayPrice(companyPayPrice);
                checkResult.setPersonalPayPrice(personalPayPrice);
                checkResult.setPersonalPay(true);
            }

            //二次提交
            if(reqContract.getRe_submit()){
                //订单审批 直接放行
                if(GlobalResponseCode.CheckOrderApproval.getCode()== currencyErrorMassage.getCode()
                    || GlobalResponseCode.ChangeAllPayPriceDesc.getCode() == currencyErrorMassage.getCode()){
                    checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                    checkResult.setResCode(GlobalResponseCode.Success);
                }
                //二次提交且标记成全额个人支付后
                if(GlobalResponseCode.CheckExceedReasonAndPersonalPay.getCode() == currencyErrorMassage.getCode() && reqContract.getRe_personal_pay_submit()){
                    checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                    checkResult.setResCode(GlobalResponseCode.Success);
                }

                //二次提交审批或个人支付
                if (GlobalResponseCode.ExceedApprovalOrPartPayPriceDesc.getCode() == currencyErrorMassage.getCode() && reqContract.getRe_personal_pay_submit()) {
                    //设置公司支付金额
                    BigDecimal companyPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
                            EmployeeRuleSummary::getCompanyPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal personalPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
                            EmployeeRuleSummary::getPersonalPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                    checkResult.setCompanyPayPrice(companyPayPrice);
                    checkResult.setPersonalPayPrice(personalPayPrice);
                    checkResult.setPersonalPay(true);
                    checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                    checkResult.setResCode(GlobalResponseCode.Success);
                }

                // 填写理由+个人支付
                if (GlobalResponseCode.ChangeAllPayDescWithReason.getCode() == currencyErrorMassage.getCode()) {
                    //设置公司支付金额
                    BigDecimal companyPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
                        EmployeeRuleSummary::getCompanyPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal personalPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
                        EmployeeRuleSummary::getPersonalPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                    checkResult.setCompanyPayPrice(companyPayPrice);
                    checkResult.setPersonalPayPrice(personalPayPrice);
                    checkResult.setPersonalPay(true);

                    //校验填写项是否正确
                    // 火车改签兼容校验事由flag
                    boolean flag = checkResult.getIsChangeReason();
                    TravelOnBusiOrderRuleCheckResult travelOnBusiOrderRuleCheckResult =
                        currencyCheckService.checkExceedAuthV2(reqContract,currencyErrorMassage,
                            CategoryTypeEnum.Train, flag);
                    if (travelOnBusiOrderRuleCheckResult.getErrCode()!=GlobalResponseCode.Success.getCode()) {
                        checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                        checkResult.setErrCode(travelOnBusiOrderRuleCheckResult.getErrCode());
                        checkResult.setErrMsg(travelOnBusiOrderRuleCheckResult.getErrMsg());
                    } else {
                        // 标记成全额个人支付后
                        if (Objects.equals(reqContract.getRe_personal_pay_submit(), true)) {
                            checkResult.setResCode(GlobalResponseCode.Success);
                        }
                        checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                    }
                }

                //改签或超规填写理由
                if(GlobalResponseCode.CheckExceedReason.getCode() == currencyErrorMassage.getCode() ||
                        GlobalResponseCode.CheckChangeReason.getCode() == currencyErrorMassage.getCode() ||
                        GlobalResponseCode.CheckReason.getCode()== currencyErrorMassage.getCode() ||
                        GlobalResponseCode.CheckAndChangeReason.getCode()== currencyErrorMassage.getCode() ||
                        GlobalResponseCode.RemindChangeReason.getCode() ==  currencyErrorMassage.getCode() ||
                        GlobalResponseCode.ExceedAirReason.getCode() ==  currencyErrorMassage.getCode()
                ){
                    //校验填写项是否正确
                    // 火车改签兼容校验事由flag
                    boolean flag = checkResult.getIsChangeReason();
                    TravelOnBusiOrderRuleCheckResult travelOnBusiOrderRuleCheckResult =
                        currencyCheckService.checkExceedAuthV2(reqContract,currencyErrorMassage,
                            CategoryTypeEnum.Train, flag);
                    if(travelOnBusiOrderRuleCheckResult.getErrCode()!=GlobalResponseCode.Success.getCode()){
                        checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                        checkResult.setErrCode(travelOnBusiOrderRuleCheckResult.getErrCode());
                        checkResult.setErrMsg(travelOnBusiOrderRuleCheckResult.getErrMsg());
                    }else{
                        checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                        checkResult.setResCode(GlobalResponseCode.Success);
                    }
                }
            }
        }

        //增加快照信息
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("companyRuleSummary",companyRuleSummary);
        checkResult.setSnapshotInfo(jsonObject);
        checkResult.setIsTraveBooking(companyRuleSummary.getIsTraveBooking());
        //完整日志打印
        log.info("companyRuleSummary-{}",JsonUtils.toJson(companyRuleSummary));
        return checkResult;
    }

    /**
     * 校验火车规则
     *
     * @param reqContract
     * @return
     */
    private TravelOnBusiOrderRuleCheckResult trainOrderRuleCheckResult(TrainOrderCheckReqV2Contract reqContract, String clientVersion) throws SaasException {
        //人员信息集合
        List<PersonnelInfo> personnelInfoList = new ArrayList<>();

        BigDecimal totalPrice = BigDecimal.ZERO;
        //订单金额
        BigDecimal orderPrice = reqContract.getTravel_on_busi_common_req_contract().getOrder_price();
        //优惠券金额
        BigDecimal couponAmount = ObjUtils.toBigDecimal(reqContract.getTravel_on_busi_common_req_contract().getCoupon_amount(), BigDecimal.ZERO);

        BigDecimal insurancePrice = ObjUtils.toBigDecimal(
            reqContract.getTravel_on_busi_common_req_contract().getInsurance_price(), BigDecimal.ZERO);
        //查询优惠券配置 0.未开启 1.开启
        Integer couponExceedPriceSetting = iOrderCheckService.queryCouponExceedPriceSetting(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        logger.info("[火车票下单校验]，查询优惠券配置, couponExceedPriceSetting={}", JsonUtils.toJson(couponExceedPriceSetting));
        if (couponExceedPriceSetting == 1) {
            totalPrice = orderPrice.subtract(couponAmount).max(BigDecimal.ZERO);
            List<String> frequentIdList = reqContract.getTravel_on_busi_common_req_contract().getFrequent_id();
            if (couponAmount.compareTo(BigDecimal.valueOf(0)) == 1) {
                reqContract.getOrder_parameter_json().setSeat_price(reqContract.getOrder_parameter_json().getSeat_price().subtract(couponAmount.divide(BigDecimal.valueOf(frequentIdList.size()), 2, BigDecimal.ROUND_HALF_UP)).max(BigDecimal.ZERO));
            }
            totalPrice = totalPrice.add(insurancePrice);
        } else {
            // 含保险含优惠券金额
            totalPrice = reqContract.getTravel_on_busi_common_req_contract().getTotal_price();
        }




        // 默认初始化正常
        TravelOnBusiOrderRuleCheckResult checkResult = new TravelOnBusiOrderRuleCheckResult();
        checkResult.setPersonnelInfoList(personnelInfoList);
        checkResult.setResCode(GlobalResponseCode.Success);
        checkResult.setPersonalPay(false);

        // 快照信息
        JSONObject snapshotInfo = new JSONObject();
        checkResult.setSnapshotInfo(snapshotInfo);
        // 查询是否个人垫付模式
        Map<String, Boolean> funcMap = iPrivilegeService.queryFunctionMoudle(CodeTypeEnums.CustomAuth.getKey(), reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(), CustomCodeType.M_ADVANCE_PAYMENT.getKey());
        logger.info("[火车票下单校验]，是否个人垫付模式, funcMap:{}", JsonUtils.toJson(funcMap));
        Boolean advancePayment = funcMap.get(CustomCodeType.M_ADVANCE_PAYMENT.getKey());
        PayModelEnum payModel = advancePayment ? PayModelEnum.PERSONAL_PREPAY : PayModelEnum.COMPANY_PAY;
        checkResult.setPayModel(payModel.getCode());
        snapshotInfo.put("payModel", payModel.getCode());
        //BigDecimal totalPrice = reqContract.getTravel_on_busi_common_req_contract().getTotal_price();
        if (advancePayment) {
            checkResult.setCompanyPayPrice(BigDecimal.ZERO);
            checkResult.setPersonalPayPrice(totalPrice);
            checkResult.setReimbursablePrice(totalPrice);
            checkResult.setUnreimbursablePrice(BigDecimal.ZERO);
        } else {
            if(Objects.equals(reqContract.getTravel_on_busi_common_req_contract().getValue_added_pay_type(),3)){
                totalPrice = totalPrice.subtract(reqContract.getTravel_on_busi_common_req_contract().getValue_added_service_fee_sum());
                checkResult.setCompanyPayPrice(totalPrice);
                checkResult.setPersonalPayPrice(reqContract.getTravel_on_busi_common_req_contract().getValue_added_service_fee_sum());
            }else{
                checkResult.setCompanyPayPrice(totalPrice);
                checkResult.setPersonalPayPrice(BigDecimal.ZERO);
            }
            checkResult.setReimbursablePrice(BigDecimal.ZERO);
            checkResult.setUnreimbursablePrice(BigDecimal.ZERO);
        }

        checkResult.setAmountCompliance(totalPrice);
        checkResult.setAmountNonCompliance(BigDecimal.ZERO);
        // 预订人状态
        EmployeeContract orderEmployee = baseOrganizationService.getEmployee(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(), reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        logger.info("预订人状态, orderEmployee:{}", JsonUtils.toJson(orderEmployee));
        if (orderEmployee == null || orderEmployee.getStatus() != EmployeeStatusEnum.ACTIVE.getValue()) {
            checkResult.setResCode(GlobalResponseCode.OrderCheckEmployeeNotActive);
            return checkResult;
        }
        // 企业火车权限
        CompanyRuleDTO companyRule = iCompanyRuleService.queryByCompanyId(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        logger.info("企业火车权限, companyRule:{}", JsonUtils.toJson(companyRule));
        if (companyRule == null || companyRule.getTrainRule() != 1) {
            checkResult.setResCode(GlobalResponseCode.OrderCheckCompanyNoAuth.getCode(), CoreLanguage.Common_Exception_AirEmployeeNoAuthMsg.getMessage());
            return checkResult;
        }
        // 员工火车权限
        EmployeeTrainRule employeeTrainRule = iBaseEmployeeTrainRuleService.queryEmployeeTrainRuleByPrimaryKey(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(), reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        logger.info("员工火车权限, employeeTrainRule:{}", JsonUtils.toJson(employeeTrainRule));
        checkResult.setEmployeeTrainRule(employeeTrainRule);
        snapshotInfo.put("authInfo", employeeTrainRule);
        if (employeeTrainRule == null
                || employeeTrainRule.getTrain_rule() != TrainRuleType.AllowOther.getCode()) {
            checkResult.setResCode(GlobalResponseCode.OrderCheckEmployeeNoAuth.getCode(), CoreLanguage.Common_Exception_TrainEmployeeNoAuthMsg.getMessage());
            return checkResult;
        }

        //预定人员信息 (用于一人多规则的订单信息展示)
        PersonnelInfo personnelInfo = new PersonnelInfo();
        //设置预定人员工id
        personnelInfo.setId(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id());
        //设置预定人员工姓名
        personnelInfo.setName(orderEmployee.getName());
        //设置是否是多规则
        personnelInfo.setIs_rules(employeeTrainRule.getMulti_rule_switch());

        //企业余额校验(余额不足或者账户被锁定)
        TravelOnBusiOrderRuleCheckResult checkCompanyAccountResult = iOrderCheckService.checkCompanyAccount(reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), totalPrice, OrderCategory.Train.getKey(), advancePayment);
        logger.info("企业余额校验, checkCompanyAccountResult:{}", JsonUtils.toJson(checkCompanyAccountResult));
        if (GlobalResponseCode.Success.getCode() != checkCompanyAccountResult.getErrCode()) {
            FinhubLogger.event(reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), EventParams.build(**********, false).put("account_sub_type", 2));
            return checkCompanyAccountResult;
        }
        CostAttrAndBudgetConf costAttrAndBudgetConf = iMessageSetupService.queryCostAttrAndBudgetConf(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());// 老版本配置"部门和项目"兼容为"部门或项目"
        logger.info("企业余额校验, costAttrAndBudgetConf:{}", JsonUtils.toJson(costAttrAndBudgetConf));
        if (costAttrAndBudgetConf.getCost_attribution_scope() == CostAttributionScopeEnum.DEPT_AND_PROJ.getCode()
                && ObjUtils.isNotBlank(clientVersion) && VersionTool.lessThan(clientVersion, "4.2.0")) {
            costAttrAndBudgetConf.setCost_attribution_scope(CostAttributionScopeEnum.DEPT_OR_PROJ.getCode());
        }
        checkResult.setCost_attribution_scope(costAttrAndBudgetConf.getCost_attribution_scope());
        checkResult.setBudget_cost_attr_type(costAttrAndBudgetConf.getBudget_cost_attr_type());
        Integer costInfoType = reqContract.getCost_info_type();

        // 检查费用归属灰度开关 进行灰度校验
        Integer costAttributionNewSwitch = iMessageSetupRpcService.queryCostAttributionNewSwitch(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        log.info("costAttributionNewSwitch:{}",costAttributionNewSwitch);
        if (CommonSwitchConstant.OPEN.equals(costAttributionNewSwitch)){
            CostCheckVO costCheckVO = CostCheckVO.from(reqContract);
            currencyCheckService.newCheckCostAttribution(costCheckVO);
        }else{
            if (costInfoType != null && costInfoType == 2) {
                TravelOnBusiOrderRuleCheckResult checkCompanyCostAttributionResult = iOrderCheckService.checkCostTicketInfo(reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), reqContract.getCost_info_ticket_list(), costAttrAndBudgetConf, clientVersion);
                logger.info("企业余额校验, checkCompanyCostAttributionResult:{}", JsonUtils.toJson(checkCompanyCostAttributionResult));
                if (GlobalResponseCode.Success.getCode() != checkCompanyCostAttributionResult.getErrCode()) {
                    return checkCompanyCostAttributionResult;
                }
            } else {
                TravelOnBusiOrderRuleCheckResult checkCompanyCostAttributionResult = iOrderCheckService.checkCostInfo(reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), reqContract.getCost_info(), costAttrAndBudgetConf, clientVersion);
                logger.info("企业余额校验, checkCompanyCostAttributionResult:{}", JsonUtils.toJson(checkCompanyCostAttributionResult));
                if (GlobalResponseCode.Success.getCode() != checkCompanyCostAttributionResult.getErrCode()) {
                    return checkCompanyCostAttributionResult;
                }
            }
        }

        //审批单查询校验
        TravelOnBusiOrderRuleCheckResult travelOnBusiOrderRuleCheckResult = iOrderCheckService.travelOnbusiOrderApplyIdCheckV2(reqContract, employeeTrainRule.getTrain_verify_flag(), TravelType.Train.getCode());
        logger.info("审批单查询校验, travelOnBusiOrderRuleCheckResult:{}", JsonUtils.toJson(travelOnBusiOrderRuleCheckResult));
        if (GlobalResponseCode.Success.getCode() != travelOnBusiOrderRuleCheckResult.getErrCode()) {
            return travelOnBusiOrderRuleCheckResult;
        }

        //预算校验
        TravelOnBusiOrderRuleCheckResult travelOrderBudgetCheckResult = iOrderCheckService.categoryOrderBudgetCheckV2(reqContract, costAttrAndBudgetConf, OrderCategory.Train, clientVersion);
        logger.info("预算校验, travelOrderBudgetCheckResult:{}", JsonUtils.toJson(travelOrderBudgetCheckResult));
        snapshotInfo.put("budgetList", travelOrderBudgetCheckResult.getBudgetSettingAndUseList());
        if (GlobalResponseCode.Success.getCode() != travelOrderBudgetCheckResult.getErrCode()) {
            return travelOrderBudgetCheckResult;
        }
        BigDecimal budgetAmountCompliance = travelOrderBudgetCheckResult.getAmountCompliance();
        BigDecimal budgetAmountNonCompliance = travelOrderBudgetCheckResult.getAmountNonCompliance();
        checkResult.setAmountCompliance(budgetAmountCompliance);
        checkResult.setAmountNonCompliance(budgetAmountNonCompliance);
        logger.info("[火车票下单校验]，预算合规金额:{}, 预算超规金额:{}", budgetAmountCompliance, budgetAmountNonCompliance);
        MessageSetup estimatedCheckSetup = iMessageSetupService.queryCompanyMessageSetupWithDefault(reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), SaasApplyConstant.ITEM_CODE_TRIP_APPLY_BUDGET_CHECK);
        logger.info("预估费用校验, estimatedCheckSetup:{}", JsonUtils.toJson(estimatedCheckSetup));

        // 预估费用校验
        CheckApplyEstimatedAmountReq checkApplyEstimatedAmountReq = new CheckApplyEstimatedAmountReq();
        checkApplyEstimatedAmountReq.setApplyId(reqContract.getTravel_on_busi_common_req_contract().getApply_id());
        checkApplyEstimatedAmountReq.setTripId(reqContract.getTravel_on_busi_common_req_contract().getApply_trip_id());
        checkApplyEstimatedAmountReq.setCompanyId(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        checkApplyEstimatedAmountReq.setValidateAmount(totalPrice);
        checkApplyEstimatedAmountReq.setBizType(BizType.Train.getCode());
        checkApplyEstimatedAmountReq.setStartDate(reqContract.getTravel_on_busi_parameter_req_contract().getStart_time());
        checkApplyEstimatedAmountReq.setEndDate(reqContract.getTravel_on_busi_parameter_req_contract().getStart_time());
        AmountExceedCheckRes amountExceedCheckRes = tripEstimateService.checkTripEstimatedAmount(checkApplyEstimatedAmountReq);

        if(GlobalResponseCode.Success.getCode() != amountExceedCheckRes.getErrCode()){
            TravelOnBusiOrderRuleCheckResult result = new TravelOnBusiOrderRuleCheckResult();
            result.setErrCode(GlobalResponseCode.ApplyTripEstimatedAmountIsError.getCode());
            String content = "";
            // 单日上限单独message
            if (Objects.equals(amountExceedCheckRes.getTotalEstimatedLimitIntValue(),TotalEstimatedLimitType.DAILY_LIMIT.getCode())) {
                BigDecimal usedAmount = BigDecimal.ZERO;
                // 火车只有一天
                if (CollectionUtils.isNotEmpty(amountExceedCheckRes.getDailyUsedAmountList())) {
                    for (BigDecimal used : amountExceedCheckRes.getDailyUsedAmountList()) {
                        usedAmount = usedAmount.add(used);
                    }
                }
                content = String.format("您预订的行程实际费用超过申请单单日费用上限\n共用一个行程，\n"
                                + "行程单日费用上限金额：￥%s\n本次预订金额：￥%s",
                        amountExceedCheckRes.getTotalEstimatedLimitAmount().subtract(usedAmount).setScale(2, BigDecimal.ROUND_HALF_UP).toString(),
                        amountExceedCheckRes.getSettingAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                result.setEstimatedAmount(amountExceedCheckRes.getTotalEstimatedLimitAmount().subtract(usedAmount));
            } else {
                content = String.format("您预订的行程实际费用超过申请单费用上限\n共用一个行程，\n"
                                + "行程费用上限金额：￥%s\n本次预订金额：￥%s",
                        amountExceedCheckRes.getTotalEstimatedLimitAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString(),
                        amountExceedCheckRes.getSettingAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                if (amountExceedCheckRes.getUsedAmount() == null) {
                    result.setEstimatedAmount(amountExceedCheckRes.getTotalEstimatedLimitAmount());
                } else {
                    result.setEstimatedAmount(amountExceedCheckRes.getTotalEstimatedLimitAmount()
                        .subtract(amountExceedCheckRes.getUsedAmount()));
                }
            }
            result.setRealPrice(amountExceedCheckRes.getSettingAmount());
            result.setApplyTripId(reqContract.getTravel_on_busi_common_req_contract().getApply_trip_id());
            result.setErrMsg(content);
            return result;
        }

        //需要行程审批
        if (employeeTrainRule.getTrain_verify_flag()) {
            Integer approveType = iOrderCheckService.queryApproveType(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
            logger.info("行程审批, approveType:{}", JsonUtils.toJson(approveType));
            //2:审批单中带有规则信息
            if (approveType == 2) {
                String applyId = reqContract.getTravel_on_busi_common_req_contract().getApply_id();
                ApplyRuleSetting applyRuleSetting = applyRuleSettingExtMapper.queryApplyRuleByApplyOrderId(applyId);
                logger.info("行程审批, applyRuleSetting:{}", JsonUtils.toJson(applyRuleSetting));
                if (applyRuleSetting != null && applyRuleSetting.getTrainInfo()!=null) {
                    List<ApplyThirdContract.KeyValueItem> trainRuleList = JSONArray.parseArray(applyRuleSetting.getTrainInfo(), ApplyThirdContract.KeyValueItem.class);
                    if (CollectionUtils.isEmpty(trainRuleList)) {
                        return checkResult;
                    }
                    return checkTrainThirdRule(trainRuleList, checkResult, reqContract, totalPrice, null);
                }
            }
        }
        //订单审批开关
        Boolean trainOrderVerifyFlag = employeeTrainRule.getTrain_order_verify_flag();
        if (advancePayment) {
            trainOrderVerifyFlag = false;
        }
        logger.info("[校验火车规则]，订单审批开关:{}", trainOrderVerifyFlag);
        //处理老板版兼容问题(提示升级)
        String oldVersion = "1.9.96";
        if (VersionTool.compare(clientVersion, oldVersion) < 0 && trainOrderVerifyFlag) {
            throw new SaasException(GlobalResponseCode.ApplyCenterAlert);
        }

        String companyId = reqContract.getTravel_on_busi_common_req_contract().getCompany_id();
        Boolean isOpenExceedConfig = iMessageSetupRpcService.queryTrainRuleExceedConfigMessage(companyId);
        logger.info("火车超规个人付:{}", isOpenExceedConfig);

        //获取项目id
        List<String> productList = currencyCheckService.getProductIdByCostInfo(reqContract.getCost_info_type(), reqContract.getCost_info_ticket_list(), reqContract.getCost_info(), reqContract.getCostInfoString(), reqContract.getCostInfoTicketListString(),reqContract.getTravel_on_busi_common_req_contract().getCompany_id(),null);

        String ruleId = "";
        //如果是多规则 则重新取规则id
        if(employeeTrainRule.getMulti_rule_switch()){
            ControlOrderRuleDto controlOrderRuleDto = consumeTemplateRuleService.getConsumeRule(reqContract.getTravel_on_busi_common_req_contract().getReason_id(), employeeTrainRule.getTemplate_id(),reqContract.getTravel_on_busi_common_req_contract().getCompany_id(),productList, com.fenbeitong.saas.core.model.enums.apply.BizType.Train, clientVersion);
            employeeTrainRule.setTrain_rule_flag(controlOrderRuleDto.getIsEnable());
            ruleId =controlOrderRuleDto.getRuleId();
            personnelInfo.setRule_id(ruleId);
            personnelInfo.setIs_rule_flag(controlOrderRuleDto.getIsEnable());
            personnelInfo.setControl_dimension(String.format("管控维度-%s,适用范围-%s",controlOrderRuleDto.getName(),controlOrderRuleDto.getRange()));
            checkResult.setControlDimensionDesc(controlOrderRuleDto.getRangeDesc());
        }
        personnelInfo.setIs_rule_flag(employeeTrainRule.getTrain_rule_flag());


        //规则校验
        if (employeeTrainRule.getTrain_rule_flag()) {
            if(!employeeTrainRule.getMulti_rule_switch()){
                //获取当前预定人规则id
                ruleId = ObjUtils.ifNull(employeeTrainRule.getManual_train_rule_id(), employeeTrainRule.getDefault_train_rule_id());
                personnelInfo.setRule_id(ruleId);
            }
            //超标规则（1：禁止 2：超规填写理由下单 3：超规需要提交费用审批）
            Integer exceedBuyType = employeeTrainRule.getExceed_buy_type();

            if(reqContract.getIs_ticket_grab()){
                exceedBuyType = 1;
                log.info("抢票超规限制更改为禁止下单");
            }

            //校验规则
            if (isOpenExceedConfig) { // 火车超规个人付开始
                if (VersionTool.compare(clientVersion, AirRuleConstant.AIR_EXCEED_PERSONAL_PAY_CLIENT_VERSION) < 0) {
                    throw new SaasException(GlobalResponseCode.AirTrainCenterAlert);
                }
                //消费规则校验
                log.info("trainRuleId={}, companyId={}", ruleId, companyId);
                TrainRuleV2 trainRuleV2 = iRuleV2Service.getTrainRuleV2ById(ruleId, companyId);
                logger.info("trainRuleV2:{}", JsonUtils.toJson(trainRuleV2));
                personnelInfo.setRule_name(trainRuleV2.getName());
                personnelInfoList.add(personnelInfo);
                checkResult.setPersonnelInfoList(personnelInfoList);
                checkResult.setTrainRuleV2(trainRuleV2);
                snapshotInfo.put("ruleInfo", trainRuleV2);
                if (trainRuleV2 == null && employeeTrainRule.getTrain_rule_flag()) {
                    checkResult.setResCode(GlobalResponseCode.OrderCheckRuleNotExist);
                    return checkResult;
                }

                // 是否可以抵扣超规
                boolean allowDeductExceed = Objects.equals(couponExceedPriceSetting, 1);

                //构造预定火车规则校验航班信息参数
                TrainRuleCheckReq.TrainInfo trainInfo = TrainRuleUtils.buildBookInfo(reqContract, allowDeductExceed,
                    couponAmount, insurancePrice, BookingTypeEnum.BOOKER.getType());
                TrainRuleCheckReq trainRuleCheckReq = new TrainRuleCheckReq();
                trainRuleCheckReq.setTrainInfo(trainInfo);
                trainRuleCheckReq.setRuleInfo(trainRuleV2);
                //规则校验结果
                TrainRuleCheckRes trainRuleCheckRes = trainRuleV2CheckService.trainRuleV2CheckRes(trainRuleCheckReq, employeeTrainRule);
                checkResult.setTicket_price_info_list(trainRuleCheckRes.getTicketInfoResList());
                String duringApplyId = iOrderCheckService.queryOrderApplyMsg(reqContract.getTravel_on_busi_parameter_req_contract(),
                        reqContract.getTravel_on_busi_common_req_contract(), CategoryTypeEnum.Train, trainRuleCheckRes.getIsExceed(), null);
                logger.info("duringApplyId--:{}", duringApplyId);

                TravelOnBusiOrderCheckReqV2Contract.TravelOnBusiParameterReqContract travelOnBusiParameterReqContract
                        = reqContract.getTravel_on_busi_parameter_req_contract();

                // 超规类别
                Boolean isBookRuleExceed = trainRuleCheckRes.getIsBookRuleExceed();

                // 获取校验项
                List<TrainRuleCheckRes.PerRuleCheckRes> checkOpenList =
                        TrainRuleUtils.getCheckOpenList(trainRuleCheckRes.getPerRuleCheckResList());
                // 超规项
                List<TrainRuleCheckRes.PerRuleCheckRes> exceedList = checkOpenList.stream()
                        .filter(TrainRuleCheckRes.PerRuleCheckRes::getIsExceed)
                        .collect(Collectors.toList());

                if (trainRuleCheckRes.getIsExceed()) { // 超规
                    checkResult.setIs_exceed(true);

                    // 获取超规枚举
                    ExceedConfigTypeEnum enumByCompareOrder =
                            ExceedConfigTypeEnum.getEnumByCompareOrder(trainRuleCheckRes.getExceedConfigType());

                    if(reqContract.getIs_ticket_grab() && enumByCompareOrder != ExceedConfigTypeEnum.EMPLOYEE_PAY_ALL_BY_REASON && enumByCompareOrder != ExceedConfigTypeEnum.EMPLOYEE_PAY_ALL  && enumByCompareOrder != ExceedConfigTypeEnum.COMPANY_PAY_BY_REASON  ){
                        enumByCompareOrder = ExceedConfigTypeEnum.EXCEED_NOT_ALLOW;
                        log.info("抢票超规限制更改为禁止下单");
                    }

                    checkResult.setAmountCompliance(trainRuleCheckRes.getCompanyPayPrice());
                    checkResult.setAmountNonCompliance(trainRuleCheckRes.getExceedPrice());
                    // 构造返回结果
                    buildCheckExceedResult(checkResult, duringApplyId,
                            trainRuleCheckRes.getExceedPrice(),
                            trainRuleCheckRes.getCompanyPayPrice(), travelOnBusiParameterReqContract,
                            exceedList, enumByCompareOrder, isBookRuleExceed,
                            reqContract.getTravel_on_busi_parameter_req_contract().getExceed_submit(),
                        reqContract.getTravel_on_busi_common_req_contract());

                    if(checkResult.getErrCode() != GlobalResponseCode.Success.getCode()) {
                        CurrencyErrorMessage currencyErrorMessage = checkResult.getCurrencyErrorMessage();
                        //判断一下是否是 订单审批 还是超规审批 查看历史有没有审批通过的审批单，如果有直接返回前端标识， 二次提交放行
                        if (GlobalResponseCode.CheckOrderApproval.getCode() == currencyErrorMessage.getCode()
                                || GlobalResponseCode.ExceedApprovalOrPartPayPriceDesc.getCode() == currencyErrorMessage.getCode()) {
                            if (StringUtils.isNotBlank(duringApplyId)) {
                                //二次订单审批 直接放行
                                if (!travelOnBusiParameterReqContract.getExist_center_apply_submit()) {
                                    logger.info("符合条件无需审批，弹窗提示");
                                    checkResult.setErrCode(GlobalResponseCode.OrderCheckExceedApplytTrainHint.getCode());
                                    checkResult.setErrMsg(GlobalResponseCode.OrderCheckExceedApplytTrainHint.getMsg());
                                    return checkResult;
                                } else {
                                    logger.info("符合条件无需审批，返回审批单id和超规标识");
                                    checkResult.setDuring_reapply_id(duringApplyId);
                                    checkResult.setResCode(GlobalResponseCode.Success);
                                    if (GlobalResponseCode.ExceedApprovalOrPartPayPriceDesc.getCode() == currencyErrorMessage.getCode()) {
                                        checkResult.setCompanyPayPrice(trainRuleCheckRes.getCompanyPayPrice().add(trainRuleCheckRes.getExceedPrice()));
                                        checkResult.setPersonalPayPrice(BigDecimal.ZERO);
                                        checkResult.setPersonalPay(false);
                                        for (PerTicketInfoRes perTicketInfoRes : checkResult.getTicket_price_info_list()) {
                                            // 公司支付金额 票价+保险-优惠券
                                            perTicketInfoRes.setCompany_pay_price(perTicketInfoRes.getCompany_pay_price().add(perTicketInfoRes.getPersonal_pay_price()));
                                            perTicketInfoRes.setPersonal_pay_price(BigDecimal.ZERO);
                                        }
                                    }
                                    return checkResult;
                                }
                            }
                        }

                        //超规二次提交
                        if(reqContract.getTravel_on_busi_parameter_req_contract().getExceed_submit()){
                            //二次提交且标记成全额个人支付后
                            if ((GlobalResponseCode.PartPayPriceDesc.getCode() == currencyErrorMessage.getCode()
                                    || GlobalResponseCode.AllPayPriceDesc.getCode() == currencyErrorMessage.getCode()
                                    || GlobalResponseCode.ExceedApprovalOrPartPayPriceDesc.getCode() == currencyErrorMessage.getCode())
                                    && Objects.equals(reqContract.getTravel_on_busi_parameter_req_contract()
                                    .getExceed_personalpay_sumbit(), true)) {
                                // 设置成功结果
                                checkResult.setResCode(GlobalResponseCode.Success);
                            } else if (GlobalResponseCode.ExceedApprovalOrPartPayPriceDesc.getCode() == currencyErrorMessage.getCode()
                                    && !Objects.equals(reqContract.getTravel_on_busi_parameter_req_contract()
                                    .getExceed_personalpay_sumbit(), true)) {
                                // 设置成功结果
                                checkResult.setCompanyPayPrice(trainRuleCheckRes.getCompanyPayPrice());
                                checkResult.setPersonalPayPrice(BigDecimal.ZERO);
                                checkResult.setResCode(GlobalResponseCode.Success);
                                checkResult.setPersonalPay(false);
                                checkResult.setCurrencyErrorMessage(currencyErrorMessage);
                            } else if (GlobalResponseCode.ExceedAirReason.getCode() == currencyErrorMessage.getCode()) {
                                //填写理由校验 填写项是否正常
                                travelOnBusiOrderRuleCheckResult = iOrderCheckService.checkExceedAuthV2(reqContract, CategoryTypeEnum.Train);
                                if (travelOnBusiOrderRuleCheckResult.getErrCode() != GlobalResponseCode.Success.getCode()) {
                                    checkResult.setCurrencyErrorMessage(currencyErrorMessage);
                                    checkResult.setErrCode(travelOnBusiOrderRuleCheckResult.getErrCode());
                                    checkResult.setErrMsg(travelOnBusiOrderRuleCheckResult.getErrMsg());
                                } else {
                                    checkResult.setCurrencyErrorMessage(currencyErrorMessage);
                                    checkResult.setResCode(GlobalResponseCode.Success);
                                }
                            } else if (GlobalResponseCode.CheckOrderApproval.getCode() == currencyErrorMessage.getCode()) {
                                checkResult.setCurrencyErrorMessage(currencyErrorMessage);
                                checkResult.setResCode(GlobalResponseCode.Success);
                            }
                            // 填写理由+个人支付
                            else if (GlobalResponseCode.PartPayPriceDescWithReason.getCode() == currencyErrorMessage.getCode()
                                || GlobalResponseCode.AllPayPriceDescWithReason.getCode() == currencyErrorMessage.getCode()) {
                                checkResult.setPersonalPay(true);
                                //填写理由校验 填写项是否正常
                                travelOnBusiOrderRuleCheckResult = iOrderCheckService.checkExceedAuthV2(reqContract,
                                    CategoryTypeEnum.Train);
                                if (travelOnBusiOrderRuleCheckResult.getErrCode()!=GlobalResponseCode.Success.getCode()) {
                                    checkResult.setCurrencyErrorMessage(currencyErrorMessage);
                                    checkResult.setErrCode(travelOnBusiOrderRuleCheckResult.getErrCode());
                                    checkResult.setErrMsg(travelOnBusiOrderRuleCheckResult.getErrMsg());
                                } else {
                                    // 标记成全额个人支付后
                                    if (Objects.equals(reqContract.getTravel_on_busi_parameter_req_contract()
                                        .getExceed_personalpay_sumbit(), true)) {
                                        checkResult.setResCode(GlobalResponseCode.Success);
                                    }
                                    checkResult.setCurrencyErrorMessage(currencyErrorMessage);
                                }
                            }
                        }
                    }
                } else { // 未超规
                    if(Objects.equals(reqContract.getTravel_on_busi_common_req_contract()
                            .getValue_added_pay_type(), 3) && trainOrderVerifyFlag){
                        String desc = "";
                        if(Objects.equals(reqContract.getTravel_on_busi_common_req_contract().getValue_added_type(),2)){
                            desc = "优享预定";
                        }else{
                            desc = "抢票";
                        }
                        checkResult.setErrCode(GlobalResponseCode.CityRuleForbidden.getCode());
                        checkResult.setErrMsg(desc+"服务费个人支付模式暂不支持订单审批");
                        return checkResult;
                    }

                    if (trainOrderVerifyFlag) { // 开启订单审批逻辑
                        // 未超规，但是开启了订单审批开关
                        if (StringUtils.isNotBlank(duringApplyId)) {
                            if (!travelOnBusiParameterReqContract.getExist_center_apply_submit()) {
                                logger.info("符合条件无需审批，弹窗提示");
                                checkResult.setErrCode(GlobalResponseCode.OrderCheckApplytAirHint.getCode());
                                checkResult.setErrMsg(GlobalResponseCode.OrderCheckApplytAirHint.getMsg());
                            } else {
                                logger.info("符合条件无需审批，返回审批单id和超规标识");
                                checkResult.setDuring_reapply_id(duringApplyId);
                            }
                            checkResult.setIs_exceed(false);
                            return checkResult;
                        } else {
                            currencyCheckService.setOrderApproval(true, checkResult);
                        }
                    }
                    // 未开启订单审批直接通过
                }

                // 直接返回，不走之前老的企业配置规则逻辑
                return checkResult;
            }

            // 超规个人付之前旧版原有规则逻辑
            TrainRule trainRule = trainRuleMapper.selectByPrimaryKey(ruleId);
            logger.info("规则校验, trainRule:{}", JsonUtils.toJson(trainRule));
            personnelInfo.setRule_name(trainRule.getName());
            personnelInfoList.add(personnelInfo);
            checkResult.setPersonnelInfoList(personnelInfoList);
            checkResult.setTrainRule(trainRule);
            snapshotInfo.put("ruleInfo", trainRule);
            if (trainRule == null) {
                checkResult.setResCode(GlobalResponseCode.OrderCheckRuleNotExist);
                return checkResult;
            }
            //火车规则校验
            List<TravelOnBusiOrderRuleCheckResult> travelOnBusiOrderRuleCheckResults = this.checkTrainExceedType(trainRule, reqContract, totalPrice);
            Boolean isPriceExceed = false;
            BigDecimal ruleAmountCompliance = totalPrice;
            if (CollectionUtils.isNotEmpty(travelOnBusiOrderRuleCheckResults) && travelOnBusiOrderRuleCheckResults.size() == 1) {
                if (travelOnBusiOrderRuleCheckResults.get(0).getErrCode() == GlobalResponseCode.OrderCheckAirMinPriceNoAuth.getCode()) {
                    checkResult.setMin_flight(travelOnBusiOrderRuleCheckResults.get(0).getMin_flight());
                }
                if (travelOnBusiOrderRuleCheckResults.get(0).getIsPriceExceed() != null && travelOnBusiOrderRuleCheckResults.get(0).getIsPriceExceed()) {
                    isPriceExceed = true;
                    ruleAmountCompliance = travelOnBusiOrderRuleCheckResults.get(0).getAmountCompliance();
                    BigDecimal ruleAmountNonCompliance = travelOnBusiOrderRuleCheckResults.get(0).getAmountNonCompliance();
                    logger.info("[火车下单校验]，规则合规金额:{}, 规则超规金额:{}", ruleAmountCompliance, ruleAmountNonCompliance);
                }
            }
            // 超规/合规金额处理
            if (CollectionUtils.isNotEmpty(travelOnBusiOrderRuleCheckResults)) {
                if (isPriceExceed) {
                    checkResult.setAmountCompliance(checkResult.getAmountCompliance().min(ruleAmountCompliance));
                    checkResult.setAmountNonCompliance(totalPrice.subtract(checkResult.getAmountCompliance()));
                } else {
                    checkResult.setAmountCompliance(BigDecimal.ZERO);
                    checkResult.setAmountNonCompliance(totalPrice);
                }
                logger.info("[火车下单校验]，合规金额:{}, 超规金额:{}", checkResult.getAmountCompliance(), checkResult.getAmountNonCompliance());
            }
            //强制提交逻辑
            TravelOnBusiOrderRuleCheckResult checkExceedAuthResult = iOrderCheckService.checkExceedAuthV2(reqContract, CategoryTypeEnum.Train);
            logger.info("强制提交逻辑, checkExceedAuthResult:{}", JsonUtils.toJson(checkExceedAuthResult));
            //开启订单审批
            TravelOnBusiOrderCheckReqV2Contract.TravelOnBusiParameterReqContract travelOnBusiParameterReqContract = reqContract.getTravel_on_busi_parameter_req_contract();
            if(Objects.equals(reqContract.getTravel_on_busi_common_req_contract()
                .getValue_added_pay_type(), 3) && trainOrderVerifyFlag){
                String desc = "";
                if(Objects.equals(reqContract.getTravel_on_busi_common_req_contract().getValue_added_type(),2)){
                    desc = "优享预定";
                }else{
                    desc = "抢票";
                }
                checkResult.setErrCode(GlobalResponseCode.CityRuleForbidden.getCode());
                checkResult.setErrMsg(desc+"服务费个人支付模式暂不支持订单审批");
                return checkResult;
            }
            if (trainOrderVerifyFlag) {
                boolean isExceed = false;
                String duringApplyId = iOrderCheckService.queryOrderApplyMsg(reqContract.getTravel_on_busi_parameter_req_contract(), reqContract.getTravel_on_busi_common_req_contract(), CategoryTypeEnum.Train, false, null);
                logger.info("订单审批, duringApplyId:{}", JsonUtils.toJson(duringApplyId));
                //超标禁止下单(超标需要理由)
                if ((TravelExceedType.NotAllowed.getCode() == exceedBuyType || TravelExceedType.AllowedNeedReason.getCode() == exceedBuyType) && CollectionUtils.isNotEmpty(travelOnBusiOrderRuleCheckResults)) {
                    iOrderCheckService.setErrMsgInfoNotAllowed(checkResult, travelOnBusiOrderRuleCheckResults);
                    return checkResult;
                } else if ((TravelExceedType.AllowedNeedApply.getCode() == exceedBuyType && travelOnBusiOrderRuleCheckResults.size() > 0 &&
                        GlobalResponseCode.Success.getCode() != travelOnBusiOrderRuleCheckResults.get(0).getErrCode()) &&
                        !iOrderCheckService.checkExceedApplyAuthV2(reqContract)) {
                    logger.info("火车超规则，走订单审批");
                    isExceed = true;
                    duringApplyId = iOrderCheckService.queryOrderApplyMsg(reqContract.getTravel_on_busi_parameter_req_contract(), reqContract.getTravel_on_busi_common_req_contract(), CategoryTypeEnum.Train, true, null);
                    logger.info("订单审批, duringApplyId:{}", JsonUtils.toJson(duringApplyId));
                    if (StringUtils.isNotBlank(duringApplyId) && VersionTool.greaterThanOrEqualTo(clientVersion, "4.9.5")) {
                        if (!travelOnBusiParameterReqContract.getExist_center_apply_submit()) {
                            logger.info("符合条件无需审批，弹窗提示");
                            checkResult.setErrCode(GlobalResponseCode.OrderCheckExceedApplytTrainHint.getCode());
                            checkResult.setErrMsg(GlobalResponseCode.OrderCheckExceedApplytTrainHint.getMsg());
                            checkResult.setIs_exceed(isExceed);
                            return checkResult;
                        } else {
                            logger.info("符合条件无需审批，返回审批单id和超规标识");
                            checkResult.setDuring_reapply_id(duringApplyId);
                            checkResult.setIs_exceed(isExceed);
                            return checkResult;
                        }
                    } else {
                        iOrderCheckService.setErrMsgInfoForCheckApply(checkResult, travelOnBusiOrderRuleCheckResults);
                        checkResult.setErrCode(GlobalResponseCode.OrderCheckApplyExceedMore.getCode());
                        checkResult.setIs_exceed(isExceed);
                        return checkResult;
                    }
                }
                //没有限制规则，但是开启了订单审批
                if (StringUtils.isNotBlank(duringApplyId) && VersionTool.greaterThanOrEqualTo(clientVersion, "4.9.5")) {
                    if (!travelOnBusiParameterReqContract.getExist_center_apply_submit()) {
                        logger.info("符合条件无需审批，弹窗提示");
                        checkResult.setErrCode(GlobalResponseCode.OrderCheckExceedApplytTrainHint.getCode());
                        checkResult.setErrMsg(GlobalResponseCode.OrderCheckExceedApplytTrainHint.getMsg());
                        checkResult.setIs_exceed(isExceed);
                        return checkResult;
                    } else {
                        logger.info("符合条件无需审批，返回审批单id和超规标识");
                        checkResult.setDuring_reapply_id(duringApplyId);
                        checkResult.setIs_exceed(isExceed);
                        return checkResult;
                    }
                } else {
                    iOrderCheckService.airOrderApplyCheckRes(checkResult, trainOrderVerifyFlag, GlobalResponseCode.OrderCheckApplyExceedLess);
                }
            } else {
                //未开启订单审批
                //超标禁止下单
                if (TravelExceedType.NotAllowed.getCode() == exceedBuyType && CollectionUtils.isNotEmpty(travelOnBusiOrderRuleCheckResults)) {
                    String version = "1.9.4";
                    if (VersionTool.compare(clientVersion, version) < 0) {
                        if (travelOnBusiOrderRuleCheckResults.size() > 0) {
                            return travelOnBusiOrderRuleCheckResults.get(0);
                        }
                    } else {
                        iOrderCheckService.setErrMsgInfoNotAllowed(checkResult, travelOnBusiOrderRuleCheckResults);
                        return checkResult;
                    }
                    //未超规，但是开启了订单审批开关
                    iOrderCheckService.airOrderApplyCheckRes(checkResult, trainOrderVerifyFlag, GlobalResponseCode.OrderCheckApplyExceedLess);
                }
                //超标需要理由
                else if (TravelExceedType.AllowedNeedReason.getCode() == exceedBuyType &&
                        travelOnBusiOrderRuleCheckResults.size() > 0 && GlobalResponseCode.Success.getCode() != travelOnBusiOrderRuleCheckResults.get(0).getErrCode() &&
                        GlobalResponseCode.Success.getCode() != checkExceedAuthResult.getErrCode()) {
                    //超规则返回
                    checkExceedAuthResult.setIs_exceed(true);
                    iOrderCheckService.setErrMsgInfoForReason(travelOnBusiOrderRuleCheckResults, checkExceedAuthResult);
                    return checkExceedAuthResult;
                } else if (!advancePayment && (TravelExceedType.AllowedNeedApply.getCode() == exceedBuyType && travelOnBusiOrderRuleCheckResults.size() > 0 && GlobalResponseCode.Success.getCode() != travelOnBusiOrderRuleCheckResults.get(0).getErrCode()) &&
                        !iOrderCheckService.checkExceedApplyAuthV2(reqContract)) {
                    if(Objects.equals(reqContract.getTravel_on_busi_common_req_contract().getValue_added_pay_type(), 3)){
                        String desc = "";
                        if(Objects.equals(reqContract.getTravel_on_busi_common_req_contract().getValue_added_type(),2)){
                            desc = "优享预定";
                        }else{
                            desc = "抢票";
                        }
                        checkResult.setErrCode(GlobalResponseCode.CityRuleForbidden.getCode());
                        checkResult.setErrMsg(desc+"服务费个人支付模式暂不支持超规审批");
                        return checkResult;
                    }
                    logger.info("新版本火车超规则，走订单审批");
                    String duringApplyId = iOrderCheckService.queryOrderApplyMsg(reqContract.getTravel_on_busi_parameter_req_contract(), reqContract.getTravel_on_busi_common_req_contract(), CategoryTypeEnum.Train, true, null);
                    if (StringUtils.isNotBlank(duringApplyId) && VersionTool.greaterThanOrEqualTo(clientVersion, "4.9.5")) {
                        if (!travelOnBusiParameterReqContract.getExist_center_apply_submit()) {
                            logger.info("符合条件无需审批，弹窗提示");
                            checkResult.setErrCode(GlobalResponseCode.OrderCheckExceedApplytTrainHint.getCode());
                            checkResult.setErrMsg(GlobalResponseCode.OrderCheckExceedApplytTrainHint.getMsg());
                            checkResult.setIs_exceed(true);
                            return checkResult;
                        } else {
                            logger.info("符合条件无需审批，返回审批单id和超规标识");
                            checkResult.setDuring_reapply_id(duringApplyId);
                            checkResult.setIs_exceed(true);
                            return checkResult;
                        }
                    } else {
                        iOrderCheckService.setErrMsgInfoForCheckApply(checkResult, travelOnBusiOrderRuleCheckResults);
                        checkResult.setErrCode(GlobalResponseCode.OrderCheckApplyExceedMore.getCode());
                        checkResult.setIs_exceed(true);
                    }
                    return checkResult;
                }
            }

        } else {
            if (!advancePayment) {
                TravelOnBusiOrderCheckReqV2Contract.TravelOnBusiParameterReqContract travelOnBusiParameterReqContract = reqContract.getTravel_on_busi_parameter_req_contract();
                String duringApplyId = iOrderCheckService.queryOrderApplyMsg(reqContract.getTravel_on_busi_parameter_req_contract(), reqContract.getTravel_on_busi_common_req_contract(), CategoryTypeEnum.Train, false, null);
                if (StringUtils.isNotBlank(duringApplyId) && VersionTool.greaterThanOrEqualTo(clientVersion, "4.9.5")) {
                    if (!travelOnBusiParameterReqContract.getExist_center_apply_submit()) {
                        logger.info("符合条件无需审批，弹窗提示");
                        checkResult.setErrCode(GlobalResponseCode.OrderCheckApplytTrainHint.getCode());
                        checkResult.setErrMsg(GlobalResponseCode.OrderCheckApplytTrainHint.getMsg());
                        checkResult.setIs_exceed(false);
                        return checkResult;
                    } else {
                        logger.info("符合条件无需审批，返回审批单id和超规标识");
                        checkResult.setDuring_reapply_id(duringApplyId);
                        checkResult.setIs_exceed(false);
                        return checkResult;
                    }
                } else {
                    // 开启超规付并且版本大于等于5.1.10
                    if(Objects.equals(reqContract.getTravel_on_busi_common_req_contract()
                        .getValue_added_pay_type(), 3) && trainOrderVerifyFlag){
                        String desc = "";
                        if(Objects.equals(reqContract.getTravel_on_busi_common_req_contract().getValue_added_type(),2)){
                            desc = "优享预定";
                        }else{
                            desc = "抢票";
                        }
                        checkResult.setErrCode(GlobalResponseCode.CityRuleForbidden.getCode());
                        checkResult.setErrMsg(desc+"服务费个人支付模式暂不支持订单审批");
                        return checkResult;
                    }
                    if (isOpenExceedConfig && VersionTool.compare(clientVersion,
                        AirRuleConstant.AIR_EXCEED_PERSONAL_PAY_CLIENT_VERSION) >= 0) {
                        currencyCheckService.setOrderApproval(trainOrderVerifyFlag, checkResult);
                    } else {
                        //没有限制规则，但是开启了订单审批
                        iOrderCheckService.airOrderApplyCheckRes(checkResult, trainOrderVerifyFlag, GlobalResponseCode.OrderCheckApplyExceedLess);
                    }
                }
            }
        }
        return checkResult;
    }

    private void buildCheckExceedResult(TravelOnBusiOrderRuleCheckResult checkResult,
        String duringApplyId, BigDecimal personalPayPrice, BigDecimal companyPayPrice,
        TravelOnBusiOrderCheckReqV2Contract.TravelOnBusiParameterReqContract travelOnBusiParameterReqContract,
        List<TrainRuleCheckRes.PerRuleCheckRes> exceedList,
        ExceedConfigTypeEnum enumByCompareOrder, boolean isBookRuleExceed,
        boolean exceedSubmit,
        TravelOnBusiOrderCheckReqV2Contract.TravelOnBusiCommonReqContract travelOnBusiCommonReqContract) {

        List<CurrencyMsg> currencyMsgList = new ArrayList<>();
        for (TrainRuleCheckRes.PerRuleCheckRes perRuleCheckRes : exceedList) {
            CurrencyMsg currencyMsg = new CurrencyMsg();
            currencyMsg.setCode(perRuleCheckRes.getErrCode());
            currencyMsg.setErrMsg(perRuleCheckRes.getErrMsg());
            currencyMsgList.add(currencyMsg);
        }
        if (Objects.equals(travelOnBusiCommonReqContract.getValue_added_pay_type(), 3) && !Objects.equals(enumByCompareOrder, ExceedConfigTypeEnum.EXCEED_NOT_ALLOW)) {
            CurrencyMsg currencyMsg = new CurrencyMsg();
            String desc = "";
            if (Objects.equals(travelOnBusiCommonReqContract.getValue_added_type(), 2)) {
                desc = "优享预定";
            } else {
                desc = "抢票";
            }
            currencyMsg.setCode(TrainRuleMsg.PersonalPayAddedFee.getCode());
            currencyMsg.setErrMsg(StrUtils.formatString(TrainRuleMsg.PersonalPayAddedFee.getMsg(), desc,
                travelOnBusiCommonReqContract.getValue_added_service_fee_sum()));
            currencyMsgList.add(currencyMsg);
            personalPayPrice = personalPayPrice.add(travelOnBusiCommonReqContract.getValue_added_service_fee_sum());
        }

        checkResult.setIs_exceed(true); // 默认设置超规
        if (Objects.equals(enumByCompareOrder, ExceedConfigTypeEnum.EXCEED_NOT_ALLOW)) { // 禁止下单
            if (Objects.equals(isBookRuleExceed, true)) {
                iOrderCheckService.setErrMsgInfo(checkResult, currencyMsgList,
                        GlobalResponseCode.CheckResults.getCode(), null, exceedSubmit);
            }
        } else if (Objects.equals(enumByCompareOrder,
                ExceedConfigTypeEnum.INDIVIDUAL_PAY_DIFFERENCE_AMOUNT)) { // 需个人支付相同行程下飞机票与火车票的差值金额
            iOrderCheckService.setErrMsgInfo(checkResult, currencyMsgList,
                    GlobalResponseCode.CheckExceedReason.getCode(), String.valueOf(personalPayPrice), exceedSubmit);
            checkResult.setCompanyPayPrice(companyPayPrice);
            checkResult.setPersonalPayPrice(BigDecimal.ZERO);
        } else if (Objects.equals(enumByCompareOrder,
                ExceedConfigTypeEnum.COMPANY_PAY_SUBMIT_APPROVAL)) { // 企业支付 - 提交超规订单审批
            if(Objects.equals(travelOnBusiCommonReqContract.getValue_added_pay_type(), 3)) {
                String desc = "";
                if(Objects.equals(travelOnBusiCommonReqContract.getValue_added_type(),2)){
                    desc = "优享预定";
                }else{
                    desc = "抢票";
                }
                checkResult.setErrCode(GlobalResponseCode.CityRuleForbidden.getCode());
                checkResult.setErrMsg(desc+"服务费个人支付模式暂不支持超规审批");

                checkResult.setResCode(GlobalResponseCode.CurrencyFrame);
                //增加返回字段
                logger.info("超规规则如下：{}", JsonUtils.toJson(currencyMsgList));
                CurrencyErrorMessage currencyErrorMessage = new CurrencyErrorMessage();
                CurrencyEmployeeMsg currencyEmployeeMsg = new CurrencyEmployeeMsg();
                currencyEmployeeMsg.setControlDimensionDesc(checkResult.getControlDimensionDesc());
                currencyEmployeeMsg.setErrMsgList(currencyMsgList);
                currencyErrorMessage.setCurrencyEmployeeMsgList(com.google.common.collect.Lists.newArrayList(currencyEmployeeMsg));
                currencyErrorMessage.setTitle("订单超规");
                currencyErrorMessage.setDesc("您本次提交的订单超规，禁止下单"); // 黄字
                currencyErrorMessage.setMessage(GlobalResponseCode.CheckAllResults.getMsg()); // 灰字
                List<Integer> buttonIntList = com.google.common.collect.Lists.newArrayList(ActionButtonEnum.IsOk.getCode());
                currencyErrorMessage.setBtnList(iOrderCheckService.generateActionButton(buttonIntList));
                currencyErrorMessage.setCode(GlobalResponseCode.CheckDifferentResults.getCode());
                checkResult.setCurrencyErrorMessage(currencyErrorMessage);
                return;
            }
            checkResult.setCompanyPayPrice(companyPayPrice);
            checkResult.setPersonalPayPrice(BigDecimal.ZERO);
            iOrderCheckService.setErrMsgInfo(checkResult, currencyMsgList,
                    GlobalResponseCode.CheckOrderApproval.getCode(), null, exceedSubmit);
        } else if (Objects.equals(enumByCompareOrder, ExceedConfigTypeEnum.COMPANY_PAY_BY_REASON)) { // 企业支付 - 填写超规理由报备
            checkResult.setCompanyPayPrice(companyPayPrice);
            checkResult.setPersonalPayPrice(BigDecimal.ZERO);
            iOrderCheckService.setErrMsgInfo(checkResult, currencyMsgList,
                    GlobalResponseCode.CheckExceedReason.getCode(), String.valueOf(personalPayPrice), exceedSubmit);
        } else if (Objects.equals(enumByCompareOrder, ExceedConfigTypeEnum.EMPLOYEE_PAY_PART)) { // 员工支付超规部分 - 无需审批、无需报备

            checkResult.setCompanyPayPrice(companyPayPrice);
            checkResult.setPersonalPayPrice(personalPayPrice);
            iOrderCheckService.setErrMsgInfo(checkResult, currencyMsgList,
                    GlobalResponseCode.CheckPersonalPay.getCode(), String.valueOf(personalPayPrice), exceedSubmit);

        } else if (Objects.equals(enumByCompareOrder, ExceedConfigTypeEnum.EMPLOYEE_PAY_PART_BY_REASON)) { // 员工支付超规部分 - 填写超规理由报备
            checkResult.setCompanyPayPrice(companyPayPrice);
            checkResult.setPersonalPayPrice(personalPayPrice);
            iOrderCheckService.setErrMsgInfo(checkResult, currencyMsgList,
                    GlobalResponseCode.CheckExceedReasonAndPersonalPayPart.getCode(), String.valueOf(personalPayPrice), exceedSubmit);
        } else if (Objects.equals(enumByCompareOrder, ExceedConfigTypeEnum.EMPLOYEE_PAY_ALL)) { // 员工支付全部费用 - 无需审批、无需报备
            iOrderCheckService.setErrMsgInfo(checkResult, currencyMsgList,
                    GlobalResponseCode.CheckPersonalPayAll.getCode(), String.valueOf(personalPayPrice), exceedSubmit);
            checkResult.setCompanyPayPrice(BigDecimal.ZERO);
            checkResult.setPersonalPayPrice(personalPayPrice);
        } else if (Objects.equals(enumByCompareOrder,
                ExceedConfigTypeEnum.EMPLOYEE_PAY_ALL_BY_REASON)) { // 员工支付全部费用 - 填写超规理由报备
            checkResult.setCompanyPayPrice(BigDecimal.ZERO);
            checkResult.setPersonalPayPrice(personalPayPrice);
            iOrderCheckService.setErrMsgInfo(checkResult, currencyMsgList,
                    GlobalResponseCode.CheckExceedReasonAndPersonalPay.getCode(), String.valueOf(personalPayPrice), exceedSubmit);
        } else if (Objects.equals(enumByCompareOrder, ExceedConfigTypeEnum.EXCEED_SUBMIT_APPROVAL_OR_PERSONAL_PAY)) { // 员工选择超规订单审批或支付超规费用
            checkResult.setCompanyPayPrice(companyPayPrice);
            checkResult.setPersonalPayPrice(personalPayPrice);
            // 个人支付服务费不可以提交审批
            if(Objects.equals(travelOnBusiCommonReqContract.getValue_added_pay_type(), 3)) {
                iOrderCheckService.setErrMsgInfo(checkResult, currencyMsgList,
                    GlobalResponseCode.CheckPersonalPay.getCode(), String.valueOf(personalPayPrice), exceedSubmit);
            } else {
                iOrderCheckService.setErrMsgInfo(checkResult, currencyMsgList,
                    GlobalResponseCode.ExceedApprovalOrPartPayPriceDesc.getCode(), String.valueOf(personalPayPrice), exceedSubmit);
            }
        } else {
            throw new SaasException(GlobalResponseCode.InnerError, "超规措施配置有误");
        }
    }

    private TravelOnBusiOrderRuleCheckResult multitrainOrderRuleCheckResult(TrainOrderCheckReqV2Contract reqContract, String clientVersion) throws SaasException {
        logger.info("reqContract-{}", JsonUtils.toJson(reqContract));
        // 默认初始化正常
        TravelOnBusiOrderRuleCheckResult checkResult = new TravelOnBusiOrderRuleCheckResult();
        BigDecimal totalPrice = BigDecimal.ZERO;
        //订单金额
        BigDecimal orderPrice = reqContract.getTravel_on_busi_common_req_contract().getOrder_price();
        //优惠券金额
        BigDecimal couponAmount = ObjUtils.toBigDecimal(reqContract.getTravel_on_busi_common_req_contract().getCoupon_amount(), BigDecimal.ZERO);

        //保险价格
        BigDecimal insurancePrice = ObjUtils.toBigDecimal(reqContract.getTravel_on_busi_common_req_contract().getInsurance_price(), BigDecimal.ZERO);

        //查询优惠券配置 0.未开启 1.开启
        Integer couponExceedPriceSetting = iOrderCheckService.queryCouponExceedPriceSetting(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        logger.info("[火车票下单校验]，查询优惠券配置, couponExceedPriceSetting={}", JsonUtils.toJson(couponExceedPriceSetting));
        if (couponExceedPriceSetting == 1) {
            totalPrice = orderPrice.subtract(couponAmount).max(BigDecimal.ZERO);
            List<String> frequentIdList = reqContract.getTravel_on_busi_common_req_contract().getFrequent_id();
            if (couponAmount.compareTo(BigDecimal.valueOf(0)) == 1) {
                reqContract.getOrder_parameter_json().setSeat_price(reqContract.getOrder_parameter_json().getSeat_price().subtract(couponAmount.divide(BigDecimal.valueOf(frequentIdList.size()), 2, BigDecimal.ROUND_HALF_UP)).max(BigDecimal.ZERO));
            }
            totalPrice = totalPrice.add(insurancePrice);
        } else {
            totalPrice = reqContract.getTravel_on_busi_common_req_contract().getTotal_price();
        }
        //公司id
        String companyId = reqContract.getTravel_on_busi_common_req_contract().getCompany_id();
        //获取出行人信息
        List<TravelOnBusiOrderCheckReqV2Contract.FrequentInfo> frequentInfoList = reqContract.getTravel_on_busi_common_req_contract().getTraveler_list();

        BigDecimal avgInsurancePrice = insurancePrice.divide(BigDecimal.valueOf(frequentInfoList.size()),
            2, RoundingMode.HALF_UP);

        //初始化权限信息
        CompanyRuleSummary companyRuleSummary = currencyCheckService.assembleEmployeeRuleSummary(companyId, BizType.Train, frequentInfoList, reqContract.getCost_info(), clientVersion);
        //初始化场景相关信息
        currencyCheckService.assemnleSceneSummary(reqContract, companyRuleSummary, OrderCategory.Train,
            clientVersion, TravelType.Train);
        logger.info("初始化场景相关信息完成, companyRuleSummary:{}", JsonUtils.toJson(companyRuleSummary));
        checkResult.setCompanyRuleSummary(companyRuleSummary);
        //默认返回成功状态码
        checkResult.setResCode(GlobalResponseCode.Success);
        //默认不需要个人支付
        checkResult.setPersonalPay(false);
        //默认没有金额超规
        checkResult.setIsPriceExceed(false);
        //支付模式
        checkResult.setPayModel(PayModelEnum.COMPANY_PAY.getCode());
        //设置优惠卷使用金额
        checkResult.setCouponUsedAmount(ObjUtils.toBigDecimal(reqContract.getTravel_on_busi_common_req_contract().getCoupon_amount(), BigDecimal.ZERO));
        //设置默认可报销金额
        checkResult.setReimbursablePrice(BigDecimal.ZERO);
        //设置默认不可报销金额
        checkResult.setUnreimbursablePrice(BigDecimal.ZERO);
        //设置默认合规金额为订单总价
        checkResult.setAmountCompliance(totalPrice);
        //服务费为个人支付
        if(Objects.equals(reqContract.getTravel_on_busi_common_req_contract().getValue_added_pay_type(),3)){
            totalPrice = totalPrice.subtract(reqContract.getTravel_on_busi_common_req_contract().getValue_added_service_fee_sum());
            //设置默认企业支付金额
            checkResult.setCompanyPayPrice(totalPrice);
            //设置默认个人支付金额
            checkResult.setPersonalPayPrice(reqContract.getTravel_on_busi_common_req_contract().getValue_added_service_fee_sum());
        }else{
            //设置默认企业支付金额
            checkResult.setCompanyPayPrice(totalPrice);
            //设置默认个人支付金额
            checkResult.setPersonalPayPrice(BigDecimal.ZERO);
        }
        //设置默认超规金额
        checkResult.setAmountNonCompliance(BigDecimal.ZERO);
        // 保存快照信息
        JSONObject snapshotInfo = new JSONObject();
        checkResult.setSnapshotInfo(snapshotInfo);


        // 预订人状态
        EmployeeContract orderEmployee = baseOrganizationService.getEmployee(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(), companyId);
        log.info("[预订人状态]:{},员工编号：{},公司ID：{}", JsonUtils.toJson(orderEmployee), reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(), companyId);
        if (orderEmployee == null || orderEmployee.getStatus() != EmployeeStatusEnum.ACTIVE.getValue()) {
            //设置状态码：100001 您的状态未启用,不允许进行消费交易
            checkResult.setResCode(GlobalResponseCode.OrderCheckEmployeeNotActive);
            return checkResult;
        }
        // 企业火车权限
        CompanyRuleDTO companyRule = iCompanyRuleService.queryByCompanyId(companyId);
        logger.info("企业火车权限, companyRule:{}", JsonUtils.toJson(companyRule));
        if (companyRule == null || companyRule.getTrainRule() != 1) {
            companyRuleSummary.setIsRule(true);
            checkResult.setResCode(GlobalResponseCode.OrderCheckCompanyNoAuth.getCode(), CoreLanguage.Common_Exception_AirEmployeeNoAuthMsg.getMessage());
            return checkResult;
        }
        //企业余额校验(余额不足或者账户被锁定)
        TravelOnBusiOrderRuleCheckResult checkCompanyAccountResult = iOrderCheckService.checkCompanyAccount(reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), totalPrice, OrderCategory.Train.getKey(), false);
        logger.info("企业余额校验, checkCompanyAccountResult:{}", JsonUtils.toJson(checkCompanyAccountResult));
        if (GlobalResponseCode.Success.getCode() != checkCompanyAccountResult.getErrCode()) {
            FinhubLogger.event(reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), EventParams.build(**********, false).put("account_sub_type", 2));
            return checkCompanyAccountResult;
        }
        //费用归属及预算扣减配置查询
        CostAttrAndBudgetConf costAttrAndBudgetConf = iMessageSetupService.queryCostAttrAndBudgetConf(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());// 老版本配置"部门和项目"兼容为"部门或项目"
        logger.info("费用归属及预算扣减配置查询, costAttrAndBudgetConf:{}", JsonUtils.toJson(costAttrAndBudgetConf));
        if (costAttrAndBudgetConf.getCost_attribution_scope() == CostAttributionScopeEnum.DEPT_AND_PROJ.getCode()
                && ObjUtils.isNotBlank(clientVersion) && VersionTool.lessThan(clientVersion, "4.2.0")) {
            costAttrAndBudgetConf.setCost_attribution_scope(CostAttributionScopeEnum.DEPT_OR_PROJ.getCode());
        }
        checkResult.setCost_attribution_scope(costAttrAndBudgetConf.getCost_attribution_scope());
        checkResult.setBudget_cost_attr_type(costAttrAndBudgetConf.getBudget_cost_attr_type());
        // 检查费用归属灰度开关 进行灰度校验
        Integer costAttributionNewSwitch = iMessageSetupRpcService.queryCostAttributionNewSwitch(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        log.info("costAttributionNewSwitch:{}",costAttributionNewSwitch);
        if (CommonSwitchConstant.OPEN.equals(costAttributionNewSwitch)){
            CostCheckVO costCheckVO = CostCheckVO.from(reqContract);
            currencyCheckService.newCheckCostAttribution(costCheckVO);
        }
        else
        {
            Integer costInfoType = reqContract.getCost_info_type();
            if (costInfoType != null && costInfoType == 2) {
                TravelOnBusiOrderRuleCheckResult checkCompanyCostAttributionResult = iOrderCheckService.checkCostTicketInfo(reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), reqContract.getCost_info_ticket_list(), costAttrAndBudgetConf, clientVersion);
                logger.info("费用预算校验, checkCompanyCostAttributionResult:{}", JsonUtils.toJson(checkCompanyCostAttributionResult));
                if (GlobalResponseCode.Success.getCode() != checkCompanyCostAttributionResult.getErrCode()) {
                    return checkCompanyCostAttributionResult;
                }
            } else {
                TravelOnBusiOrderRuleCheckResult checkCompanyCostAttributionResult = iOrderCheckService.checkCostInfo(reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), reqContract.getCost_info(), costAttrAndBudgetConf, clientVersion);
                logger.info("费用预算校验, checkCompanyCostAttributionResult:{}", JsonUtils.toJson(checkCompanyCostAttributionResult));
                if (GlobalResponseCode.Success.getCode() != checkCompanyCostAttributionResult.getErrCode()) {
                    return checkCompanyCostAttributionResult;
                }
            }
        }


        if (CollectionUtils.isEmpty(companyRuleSummary.getEmployeeRuleSummaryList())) {
            return checkResult;
        }

        //预算校验
        TravelOnBusiOrderRuleCheckResult travelOrderBudgetCheckResult = iOrderCheckService.categoryOrderBudgetCheckV2(reqContract, costAttrAndBudgetConf, OrderCategory.Train, clientVersion);
        logger.info("预算校验, travelOrderBudgetCheckResult:{}", JsonUtils.toJson(travelOrderBudgetCheckResult));
        snapshotInfo.put("budgetList", travelOrderBudgetCheckResult.getBudgetSettingAndUseList());
        if (GlobalResponseCode.Success.getCode() != travelOrderBudgetCheckResult.getErrCode()) {
            return travelOrderBudgetCheckResult;
        }
        BigDecimal budgetAmountCompliance = travelOrderBudgetCheckResult.getAmountCompliance();
        BigDecimal budgetAmountNonCompliance = travelOrderBudgetCheckResult.getAmountNonCompliance();
        checkResult.setAmountCompliance(budgetAmountCompliance);
        checkResult.setAmountNonCompliance(budgetAmountNonCompliance);
        logger.info("[火车票下单校验]，预算合规金额:{}, 预算超规金额:{}", budgetAmountCompliance, budgetAmountNonCompliance);

        //人员信息集合
        List<PersonnelInfo> personnelInfoList = new ArrayList<>();
        //出行人按人员维度管控
        for(EmployeeRuleSummary employeeRuleSummary : companyRuleSummary.getEmployeeRuleSummaryList()){
            if (!employeeRuleSummary.getIsEmployee()) {
                continue;
            }
            String employeeCompanyId =  employeeRuleSummary.getEmployeeCompanyId();

            // 员工火车权限
            EmployeeTrainRule employeeTrainRule = iBaseEmployeeTrainRuleService.queryEmployeeTrainRuleByPrimaryKey(employeeRuleSummary.getEmployeeId(), employeeCompanyId);
            logger.info("员工火车权限, employeeTrainRule:{}", JsonUtils.toJson(employeeTrainRule));
            employeeRuleSummary.setOrderVerifyFlag(employeeTrainRule.getTrain_order_verify_flag());
            checkResult.setEmployeeTrainRule(employeeTrainRule);
            if (employeeTrainRule == null || employeeTrainRule.getTrain_rule() != TrainRuleType.AllowOther.getCode()) {
                employeeRuleSummary.setIsEmployeeRuleAuth(true);
                CurrencyMsg currencyMsg = new CurrencyMsg();
                currencyMsg.setCode(GlobalResponseCode.OrderCheckEmployeeNotActive.getCode());
                currencyMsg.setErrMsg(GlobalResponseCode.OrderCheckEmployeeNotActive.getMsg());
                employeeRuleSummary.getCurrencyMsgList().add(currencyMsg);
                continue;
            }
            employeeRuleSummary.setRuleFlag(employeeTrainRule.getTrain_rule_flag());
            //预定人员信息 (用于一人多规则的订单信息展示)
            PersonnelInfo personnelInfo = new PersonnelInfo();
            //设置预定人员工id
            personnelInfo.setId(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id());
            //设置预定人员工姓名
            personnelInfo.setName(employeeRuleSummary.getEmployeeName());
            //设置是否是多规则
            personnelInfo.setIs_rules(employeeTrainRule.getMulti_rule_switch());

            Integer exceedBuyType = employeeTrainRule.getExceed_buy_type();

            if(reqContract.getIs_ticket_grab()){
                exceedBuyType = 1;
                log.info("抢票超规限制更改为禁止下单");
            }

            employeeRuleSummary.setExceedBuyType(exceedBuyType);

            Boolean trainOrderVerifyFlag = employeeTrainRule.getTrain_order_verify_flag();

            //审批单查询校验
            TravelOnBusiOrderRuleCheckResult travelOnBusiOrderRuleCheckResult = currencyCheckService.travelOnbusiOrderApplyIdCheckV2(reqContract, employeeRuleSummary, employeeRuleSummary.getVerifyFlag(), TravelType.Train.getCode());
            log.info("[审批单查询校验]:{}", JsonUtils.toJson(travelOnBusiOrderRuleCheckResult));
            if (GlobalResponseCode.Success.getCode() != travelOnBusiOrderRuleCheckResult.getErrCode()) {
                //审批单校验失败返回预错误码
                employeeRuleSummary.setIsAvailableTravel(true);
                employeeRuleSummary.setAvailableTravelCode(travelOnBusiOrderRuleCheckResult.getErrCode());
                employeeRuleSummary.setAvailableTravelMsg(travelOnBusiOrderRuleCheckResult.getErrMsg());
                CurrencyMsg currencyMsg = new CurrencyMsg();
                currencyMsg.setErrMsg(travelOnBusiOrderRuleCheckResult.getErrMsg());
                currencyMsg.setCode(travelOnBusiOrderRuleCheckResult.getErrCode());
                employeeRuleSummary.getCurrencyMsgList().add(currencyMsg);
            }
            //分摊金额
            BigDecimal companyPayPrice = employeeRuleSummary.getBudgetMoney();

            if(Objects.equals(employeeRuleSummary.getValueAddedPayType(),3)){
                companyPayPrice = companyPayPrice.subtract(employeeRuleSummary.getValueAddedServiceFee());
                employeeRuleSummary.setPersonalPayPrice(employeeRuleSummary.getValueAddedServiceFee());
            }

            //平均优惠卷使用金额
            BigDecimal couponUsedAmount = employeeRuleSummary.getCouponUsedAmount();

            ApplyOrder applyOrder = null;
            Integer totalEstimatedLimitIntValue = null;
            // 计算申请单下的金额
            BigDecimal validateAmount = BigDecimal.ZERO;

            if (ObjUtils.isNotBlank(employeeRuleSummary.getApplyId())){
                applyOrder = applyOrderMapper.selectByPrimaryKey(employeeRuleSummary.getApplyId());
                if (applyOrder.getType().equals(ApplyType.CustomFromBeforehand.getValue())){
                    // 查询自定义申请单总预估费配置快照
                    log.info("可用申请单判断查询自定义申请单快照信息rpc请求, id:{}， formId:{}", applyOrder.getId(), applyOrder.getFormId());
                    CustomFormTotalEstimatedOptionDTO customFormSnapshot = iCustomFormService.queryTotalEstimatedOptionSnapshot(applyOrder.getId(), applyOrder.getFormId());
                    log.info("可用申请单判断查询自定义申请单快照信息rpc结果, customFormSnapshot:{}", JsonUtils.toJson(customFormSnapshot));
                    if (ObjUtils.isNotEmpty(customFormSnapshot)
                            && Objects.equals(SwitchTypeEnum.OPEN.getSwitchCode(), customFormSnapshot.getCheckTotalEstimatedFlag())
                            && customFormSnapshot.getTotalEstimatedCheckSceneWithCompatibilityMultiTrip().stream().anyMatch(a->Objects.equals(a,BizType.Train.getCode()))) {
                        totalEstimatedLimitIntValue = customFormSnapshot.getTotalEstimatedLimitType();
                    }
                }
            }

            // 预估费用校验
            if (!Objects.isNull(totalEstimatedLimitIntValue) && !Objects.equals(totalEstimatedLimitIntValue,TotalEstimatedLimitType.UNKNOWN.getCode())) { // 自定义申请单总预估费用校验
                for (EmployeeRuleSummary summary: companyRuleSummary.getEmployeeRuleSummaryList()) {
                    if (StringUtils.isNotBlank(employeeRuleSummary.getApplyId()) && StringUtils.equals(employeeRuleSummary.getApplyId(), summary.getApplyId())){
                        validateAmount = validateAmount.add(employeeRuleSummary.getBudgetMoney()).add(insurancePrice).subtract(couponUsedAmount);
                    }
                }
            }else{
                validateAmount = companyPayPrice.add(insurancePrice).subtract(couponUsedAmount);
            }

            // 预估费用校验
            CheckApplyEstimatedAmountReq checkApplyEstimatedAmountReq = new CheckApplyEstimatedAmountReq();
            checkApplyEstimatedAmountReq.setApplyId(employeeRuleSummary.getApplyId());
            checkApplyEstimatedAmountReq.setTripId(employeeRuleSummary.getTripId());
            checkApplyEstimatedAmountReq.setCompanyId(employeeRuleSummary.getCompanyId());
            checkApplyEstimatedAmountReq.setValidateAmount(validateAmount);
            checkApplyEstimatedAmountReq.setBizType(BizType.Train.getCode());
            checkApplyEstimatedAmountReq.setStartDate(reqContract.getTravel_on_busi_parameter_req_contract().getStart_time());
            checkApplyEstimatedAmountReq.setEndDate(reqContract.getTravel_on_busi_parameter_req_contract().getStart_time());
            AmountExceedCheckRes amountExceedCheckRes = tripEstimateService.checkTripEstimatedAmount(checkApplyEstimatedAmountReq);

            if(GlobalResponseCode.Success.getCode() != amountExceedCheckRes.getErrCode()){
                String content = "";
                // 单日上限单独message
                if (Objects.equals(amountExceedCheckRes.getTotalEstimatedLimitIntValue(),TotalEstimatedLimitType.DAILY_LIMIT.getCode())) {
                    BigDecimal usedAmount = BigDecimal.ZERO;
                    // 火车只有一天
                    if (CollectionUtils.isNotEmpty(amountExceedCheckRes.getDailyUsedAmountList())) {
                        for (BigDecimal used : amountExceedCheckRes.getDailyUsedAmountList()) {
                            usedAmount = usedAmount.add(used);
                        }
                    }
                    content = String.format("您预订的行程实际费用超过申请单单日费用上限\n共用一个行程，\n"
                                    + "行程单日费用上限金额：￥%s\n本次预订金额：￥%s",
                            amountExceedCheckRes.getTotalEstimatedLimitAmount().subtract(usedAmount)
                                .setScale(2, BigDecimal.ROUND_HALF_UP).toString(),
                            amountExceedCheckRes.getSettingAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString());

                    employeeRuleSummary.setIsEstimatedBlockOrder(true);
                    employeeRuleSummary.setIsEstimatedAmtExceed(true);
                    CurrencyMsg currencyMsg = new CurrencyMsg();
                    currencyMsg.setCode(GlobalResponseCode.ApplyTripEstimatedAmountIsError.getCode());
                    currencyMsg.setErrMsg(content);
                    employeeRuleSummary.getCurrencyMsgList().add(currencyMsg);
                } else {
                    BigDecimal totalEstimatedLimitAmount = amountExceedCheckRes.getTotalEstimatedLimitAmount();
                    if (amountExceedCheckRes.getUsedAmount() != null) {
                        totalEstimatedLimitAmount = totalEstimatedLimitAmount.subtract(amountExceedCheckRes.getUsedAmount());
                    }
                    content = String.format("您预订的行程实际费用超过申请单费用上限\n共用一个行程，\n"
                                    + "行程费用上限金额：￥%s\n本次预订金额：￥%s",
                            totalEstimatedLimitAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString(),
                            amountExceedCheckRes.getSettingAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                    employeeRuleSummary.setIsEstimatedBlockOrder(true);
                    employeeRuleSummary.setIsEstimatedAmtExceed(true);
                    CurrencyMsg currencyMsg = new CurrencyMsg();
                    currencyMsg.setCode(GlobalResponseCode.ApplyTripEstimatedAmountIsError.getCode());
                    currencyMsg.setErrMsg(content);
                    employeeRuleSummary.getCurrencyMsgList().add(currencyMsg);
                }
            }


            employeeRuleSummary.setCompanyPayPrice(companyPayPrice);
            employeeRuleSummary.setReimbursablePrice(companyPayPrice);
            employeeRuleSummary.setUnreimbursablePrice(BigDecimal.ZERO);
            employeeRuleSummary.setAmountCompliance(companyPayPrice);
            employeeRuleSummary.setAmountNonCompliance(BigDecimal.ZERO);


            String ruleId = "";
            //如果是多规则 则重新取规则id
            if(employeeTrainRule.getMulti_rule_switch()){

                //项目id
                //获取项目id
                List<String> productList = currencyCheckService.getProductIdByCostInfo(reqContract.getCost_info_type(), reqContract.getCost_info_ticket_list(), reqContract.getCost_info(), reqContract.getCostInfoString(), reqContract.getCostInfoTicketListString(),reqContract.getTravel_on_busi_common_req_contract().getCompany_id(),employeeRuleSummary.getEmployeeId());
                log.info("费用归属项目id:",JsonUtils.toJson(productList));
                ControlOrderRuleDto controlOrderRuleDto = consumeTemplateRuleService.getConsumeRule(reqContract.getTravel_on_busi_common_req_contract().getReason_id(), employeeTrainRule.getTemplate_id(),reqContract.getTravel_on_busi_common_req_contract().getCompany_id(),productList, com.fenbeitong.saas.core.model.enums.apply.BizType.Train, clientVersion);
                log.info("controlOrderRuleDto:{}",JsonUtils.toJson(controlOrderRuleDto));
                employeeRuleSummary.setRuleFlag(controlOrderRuleDto.getIsEnable());
                ruleId =controlOrderRuleDto.getRuleId();
                personnelInfo.setRule_id(controlOrderRuleDto.getRuleId());
                personnelInfo.setIs_rule_flag(controlOrderRuleDto.getIsEnable());
                personnelInfo.setControl_dimension(String.format("管控维度-%s,适用范围-%s",controlOrderRuleDto.getName(),controlOrderRuleDto.getRange()));
                employeeRuleSummary.setControlDimensionDesc(controlOrderRuleDto.getRangeDesc());
            }
            personnelInfo.setIs_rule_flag(employeeTrainRule.getTrain_rule_flag());
            //规则校验
            if (employeeRuleSummary.getRuleFlag()) {
                if (companyRuleSummary.getIsTrainOpenExceedConfig()) { // 火车超规个人付开始
                    if (VersionTool.compare(clientVersion, AirRuleConstant.AIR_EXCEED_PERSONAL_PAY_CLIENT_VERSION) < 0) {
                        throw new SaasException(GlobalResponseCode.AirTrainCenterAlert);
                    }
                    //超标规则（1：禁止 2：超规填写理由下单 3：超规需要提交费用审批）
                    employeeRuleSummary.setExceedBuyType(exceedBuyType);
                    if(!employeeTrainRule.getMulti_rule_switch()) {
                        //获取当前出行人规则id
                         ruleId = ObjUtils.ifNull(employeeTrainRule.getManual_train_rule_id(),
                                employeeTrainRule.getDefault_train_rule_id());
                        personnelInfo.setRule_id(ruleId);
                    }
                    TrainRuleV2 trainRuleV2 =
                        iRuleV2Service.getTrainRuleV2ById(ruleId,employeeCompanyId);
                    log.info("[员工]:{}, 员工编号:{}, 火车规则V2:{}", employeeRuleSummary.getEmployeeName(),
                        employeeRuleSummary.getEmployeeId(), JsonUtils.toJson(trainRuleV2));


                    if (trainRuleV2 == null && employeeTrainRule.getTrain_rule_flag()) {
                        employeeRuleSummary.setIsEmployeeRule(true);
                        employeeRuleSummary.setEmployeeRuleCode(GlobalResponseCode.OrderCheckRuleNotExist.getCode());
                        employeeRuleSummary.setEmployeeRuleMsg(GlobalResponseCode.OrderCheckRuleNotExist.getMsg());
                    }


                    if(trainRuleV2 == null){
                        continue;
                    }
                    personnelInfo.setRule_name(trainRuleV2.getName());
                    personnelInfoList.add(personnelInfo);

                    boolean allowDeductExceed = Objects.equals(couponExceedPriceSetting, 1);
                    TrainRuleCheckReq.TrainInfo trainInfo = TrainRuleUtils.buildBookInfo(reqContract,
                        allowDeductExceed, couponAmount, avgInsurancePrice, BookingTypeEnum.TRAVELER.getType());
                    TrainRuleCheckReq trainRuleCheckReq = new TrainRuleCheckReq();
                    trainInfo.setValueAddedServiceFeeSum(employeeRuleSummary.getValueAddedServiceFee());
                    trainRuleCheckReq.setTrainInfo(trainInfo);
                    trainRuleCheckReq.setRuleInfo(trainRuleV2);
                    //规则校验结果
                    TrainRuleCheckRes trainRuleCheckRes = trainRuleV2CheckService.trainRuleV2CheckRes(trainRuleCheckReq,
                        employeeTrainRule);
                    checkResult.setTicket_price_info_list(trainRuleCheckRes.getTicketInfoResList());

                    employeeRuleSummary.setIsExceed(trainRuleCheckRes.getIsExceed());
                    employeeRuleSummary.setIsPriceExceed(trainRuleCheckRes.getIsPriceExceed());
                    employeeRuleSummary.setIsNoPriceExceed(trainRuleCheckRes.getIsNotPriceExceed());
                    employeeRuleSummary.setExceedPriceSum(trainRuleCheckRes.getExceedPrice());
                    if (employeeRuleSummary.getIsExceed()) {
                        employeeRuleSummary.setCompanyPayPrice(trainRuleCheckRes.getCompanyPayPrice());
                        if(Objects.equals(employeeRuleSummary.getValueAddedPayType(),3 ) && !reqContract.getIs_ticket_grab()){
                            if (ObjUtils.isNotEmpty(trainRuleCheckRes.getExceedPrice())) {
                                employeeRuleSummary.setPersonalPayPrice(trainRuleCheckRes.getExceedPrice().add(employeeRuleSummary.getValueAddedServiceFee()));
                            } else {
                                employeeRuleSummary.setPersonalPayPrice(trainRuleCheckRes.getExceedPrice());
                            }
                        }else{
                            employeeRuleSummary.setPersonalPayPrice(trainRuleCheckRes.getExceedPrice());
                        }

                    }

                    // 获取校验项
                    List<TrainRuleCheckRes.PerRuleCheckRes> checkOpenList =
                        TrainRuleUtils.getCheckOpenList(trainRuleCheckRes.getPerRuleCheckResList());
                    // 超规项
                    List<TrainRuleCheckRes.PerRuleCheckRes> exceedList = checkOpenList.stream()
                        .filter(TrainRuleCheckRes.PerRuleCheckRes::getIsExceed)
                        .collect(Collectors.toList());

                    //组装超规信息
                    List<CurrencyMsg> currencyMsgList = new ArrayList<>();
                    for (TrainRuleCheckRes.PerRuleCheckRes perRuleCheckRes : exceedList) {
                        CurrencyMsg currencyMsg = new CurrencyMsg();
                        currencyMsg.setCode(perRuleCheckRes.getErrCode());
                        currencyMsg.setErrMsg(perRuleCheckRes.getErrMsg());
                        currencyMsgList.add(currencyMsg);
                    }
                    if(Objects.equals(employeeRuleSummary.getValueAddedPayType(),3)){
                            CurrencyMsg currencyMsg = new CurrencyMsg();
                            String desc = "";
                            if (Objects.equals(employeeRuleSummary.getValueAddedType(), 2)) {
                                desc = "优享预定";
                            } else {
                                desc = "抢票";
                            }
                            currencyMsg.setCode(TrainRuleMsg.PersonalPayAddedFee.getCode());
                            currencyMsg.setErrMsg(StrUtils.formatString(TrainRuleMsg.PersonalPayAddedFee.getMsg(), desc,employeeRuleSummary.getValueAddedServiceFee()));
                            currencyMsgList.add(currencyMsg);
                    }


                    if (CollectionUtils.isNotEmpty(currencyMsgList)) {
                        employeeRuleSummary.getCurrencyMsgList().addAll(currencyMsgList);
                    }
                    if(employeeRuleSummary.getIsExceed()){

                        if(reqContract.getIs_ticket_grab() && TravelExceedType.getEnumByCompareOrder(
                                trainRuleCheckRes.getExceedConfigType()).getCode() != TravelExceedType.AllowedNeedReason.getCode() &&
                                TravelExceedType.getEnumByCompareOrder(
                                        trainRuleCheckRes.getExceedConfigType()).getCode() != TravelExceedType.EMPLOYEE_PAY_All.getCode()
                                &&  TravelExceedType.getEnumByCompareOrder(
                                trainRuleCheckRes.getExceedConfigType()).getCode() != TravelExceedType.EMPLOYEE_PAY_All_BY_REASON.getCode()
                        ){
                            employeeRuleSummary.setExceedBuyType(TravelExceedType.NotAllowed.getCode());
                            log.info("抢票超规限制更改为禁止下单");
                        }else{
                            //设置他的超规措施
                            employeeRuleSummary.setExceedBuyType(TravelExceedType.getEnumByCompareOrder(
                                    trainRuleCheckRes.getExceedConfigType()).getCode());
                        }
                    }

                } else { // 原逻辑
                    if(!employeeTrainRule.getMulti_rule_switch()) {
                        ruleId = ObjUtils.ifNull(employeeTrainRule.getManual_train_rule_id(), employeeTrainRule.getDefault_train_rule_id());
                        personnelInfo.setRule_id(ruleId);
                    }
                    log.info("personelInfo:{},ruleId={}",JsonUtils.toJson(personnelInfo),ruleId);
                    TrainRule trainRule = trainRuleMapper.selectByPrimaryKey(ruleId);
                    logger.info("规则校验, trainRule:{}", JsonUtils.toJson(trainRule));
                    checkResult.setTrainRule(trainRule);
                    if (trainRule == null) {
                        checkResult.setResCode(GlobalResponseCode.OrderCheckRuleNotExist);
                        employeeRuleSummary.setIsEmployeeRule(true);
                        CurrencyMsg currencyMsg = new CurrencyMsg();
                        currencyMsg.setErrMsg(checkResult.getErrMsg());
                        currencyMsg.setCode(checkResult.getErrCode());
                        employeeRuleSummary.getCurrencyMsgList().add(currencyMsg);
                        continue;
                    }
                    personnelInfo.setRule_name(trainRule.getName());
                    personnelInfoList.add(personnelInfo);
                    //火车规则校验
                    List<TravelOnBusiOrderRuleCheckResult> travelOnBusiOrderRuleCheckResults = this.checkTrainExceedType(trainRule, reqContract, totalPrice);
                    log.info("travelOnBusiOrderRuleCheckResults:{}",travelOnBusiOrderRuleCheckResults);
                    Boolean isPriceExceed = false;
                    BigDecimal ruleAmountCompliance = totalPrice;
                    if (CollectionUtils.isNotEmpty(travelOnBusiOrderRuleCheckResults) && travelOnBusiOrderRuleCheckResults.size() > 0 ) {
                        employeeRuleSummary.setIsExceed(true);
                        if (travelOnBusiOrderRuleCheckResults.get(0).getErrCode() == GlobalResponseCode.OrderCheckAirMinPriceNoAuth.getCode()) {
                            checkResult.setMin_flight(travelOnBusiOrderRuleCheckResults.get(0).getMin_flight());
                        }
                        if (travelOnBusiOrderRuleCheckResults.get(0).getIsPriceExceed() != null && travelOnBusiOrderRuleCheckResults.get(0).getIsPriceExceed()) {
                            isPriceExceed = true;
                            ruleAmountCompliance = travelOnBusiOrderRuleCheckResults.get(0).getAmountCompliance();
                            BigDecimal ruleAmountNonCompliance = travelOnBusiOrderRuleCheckResults.get(0).getAmountNonCompliance();
                            logger.info("[火车下单校验]，规则合规金额:{}, 规则超规金额:{}", ruleAmountCompliance, ruleAmountNonCompliance);
                        }
                    }
                    // 超规/合规金额处理
                    if (CollectionUtils.isNotEmpty(travelOnBusiOrderRuleCheckResults)) {
                        if (isPriceExceed) {
                            employeeRuleSummary.setAmountCompliance(checkResult.getAmountCompliance().min(ruleAmountCompliance));
                            employeeRuleSummary.setAmountNonCompliance(totalPrice.subtract(checkResult.getAmountCompliance()));
                        } else {
                            employeeRuleSummary.setAmountCompliance(BigDecimal.ZERO);
                            employeeRuleSummary.setAmountNonCompliance(totalPrice);
                        }
                        logger.info("[火车下单校验]，合规金额:{}, 超规金额:{}", employeeRuleSummary.getAmountCompliance(), employeeRuleSummary.getAmountNonCompliance());
                    }
                    employeeRuleSummary.setIsPriceExceed(isPriceExceed);
                    //强制提交逻辑
                    TravelOnBusiOrderRuleCheckResult checkExceedAuthResult = iOrderCheckService.checkExceedAuthV2(reqContract, CategoryTypeEnum.Train);
                    logger.info("强制提交逻辑, checkExceedAuthResult:{}", JsonUtils.toJson(checkExceedAuthResult));
                    //开启订单审批
                    TravelOnBusiOrderCheckReqV2Contract.TravelOnBusiParameterReqContract travelOnBusiParameterReqContract = reqContract.getTravel_on_busi_parameter_req_contract();
                    if (trainOrderVerifyFlag) {

                        if(Objects.equals(employeeRuleSummary.getValueAddedPayType(),3)){
                            String desc = "";
                            if (Objects.equals(employeeRuleSummary.getValueAddedType(), 2)) {
                                desc = "优享预定";
                            } else {
                                desc = "抢票";
                            }
                            CurrencyMsg currencyMsg = new CurrencyMsg();
                            currencyMsg.setErrMsg(desc+"服务费个人支付模式暂不支持订单审批");
                            currencyMsg.setCode(GlobalResponseCode.OrderCheckApplyExceedLess.getCode());
                            employeeRuleSummary.getCurrencyMsgList().add(currencyMsg);
                            continue;
                        }

                        //超标禁止下单(超标需要理由)
                        if ((TravelExceedType.NotAllowed.getCode() == exceedBuyType || TravelExceedType.AllowedNeedReason.getCode() == exceedBuyType) && CollectionUtils.isNotEmpty(travelOnBusiOrderRuleCheckResults)) {
                            currencyCheckService.addMsgInfoInEmployee(travelOnBusiOrderRuleCheckResults, employeeRuleSummary);
                            employeeRuleSummary.setIsExceed(true);
                            continue;
//                        return checkResult;
                        } else if ((TravelExceedType.AllowedNeedApply.getCode() == exceedBuyType && travelOnBusiOrderRuleCheckResults.size() > 0 &&
                            GlobalResponseCode.Success.getCode() != travelOnBusiOrderRuleCheckResults.get(0).getErrCode()) &&
                            !iOrderCheckService.checkExceedApplyAuthV2(reqContract)) {
                            logger.info("火车超规则，走订单审批");
                                currencyCheckService.addMsgInfoInEmployee(travelOnBusiOrderRuleCheckResults, employeeRuleSummary);
                                checkResult.setErrCode(GlobalResponseCode.OrderCheckApplyExceedMore.getCode());
                                checkResult.setIs_exceed(true);
                                employeeRuleSummary.setIsExceed(true);

                        }
                        if (CollectionUtils.isEmpty(employeeRuleSummary.getCurrencyMsgList())) {
//                        iOrderCheckService.airOrderApplyCheckRes(checkResult, trainOrderVerifyFlag, GlobalResponseCode.OrderCheckApplyExceedLess);
                            CurrencyMsg currencyMsg = new CurrencyMsg();
                            currencyMsg.setErrMsg(GlobalResponseCode.OrderCheckApplyExceedLess.getMsg());
                            currencyMsg.setCode(GlobalResponseCode.OrderCheckApplyExceedLess.getCode());
                            employeeRuleSummary.getCurrencyMsgList().add(currencyMsg);
                        }
                    } else {
                        //增加个人支付的信息
                        if(Objects.equals(employeeRuleSummary.getValueAddedPayType(),3)){
                            CurrencyMsg currencyMsg = new CurrencyMsg();
                            String desc = "";
                            if (Objects.equals(employeeRuleSummary.getValueAddedType(), 2)) {
                                desc = "优享预定";
                            } else {
                                desc = "抢票";
                            }
                            currencyMsg.setCode(GlobalResponseCode.OrderCheckApplyExceedLess.getCode());
                            currencyMsg.setErrMsg("个人支付"+desc+"服务费 ¥ "+employeeRuleSummary.getValueAddedServiceFee());
                            employeeRuleSummary.getCurrencyMsgList().add(currencyMsg);
                        }


                        //未开启订单审批
                        //超标禁止下单
                        if (TravelExceedType.NotAllowed.getCode() == exceedBuyType && CollectionUtils.isNotEmpty(travelOnBusiOrderRuleCheckResults)) {
                            employeeRuleSummary.setIsExceed(true);
//                      iOrderCheckService.setErrMsgInfoNotAllowed(checkResult, travelOnBusiOrderRuleCheckResults);
                            currencyCheckService.addMsgInfoInEmployee(Lists.newArrayList(travelOnBusiOrderRuleCheckResults), employeeRuleSummary);
                            continue;
                        }
                        //超标需要理由
                        else if (TravelExceedType.AllowedNeedReason.getCode() == exceedBuyType &&
                            travelOnBusiOrderRuleCheckResults.size() > 0 && GlobalResponseCode.Success.getCode() != travelOnBusiOrderRuleCheckResults.get(0).getErrCode() &&
                            GlobalResponseCode.Success.getCode() != checkExceedAuthResult.getErrCode()) {
                            //超规则返回
                            checkExceedAuthResult.setIs_exceed(true);
                            employeeRuleSummary.setIsExceed(true);
//                        iOrderCheckService.setErrMsgInfoForReason(travelOnBusiOrderRuleCheckResults, checkExceedAuthResult);
                            currencyCheckService.addMsgInfoInEmployee(Lists.newArrayList(travelOnBusiOrderRuleCheckResults), employeeRuleSummary);
                        } else if ((TravelExceedType.AllowedNeedApply.getCode() == exceedBuyType && travelOnBusiOrderRuleCheckResults.size() > 0 && GlobalResponseCode.Success.getCode() != travelOnBusiOrderRuleCheckResults.get(0).getErrCode()) &&
                            !iOrderCheckService.checkExceedApplyAuthV2(reqContract)) {
                            logger.info("新版本火车超规则，走订单审批");
                                if(Objects.equals(employeeRuleSummary.getValueAddedPayType(),3)){
                                    String desc = "";
                                    if (Objects.equals(employeeRuleSummary.getValueAddedType(), 2)) {
                                        desc = "优享预定";
                                    } else {
                                        desc = "抢票";
                                    }
                                    CurrencyMsg currencyMsg = new CurrencyMsg();
                                    currencyMsg.setErrMsg(desc+"服务费个人支付模式暂不支持超规审批");
                                    currencyMsg.setCode(GlobalResponseCode.OrderCheckApplyExceedLess.getCode());
                                    employeeRuleSummary.getCurrencyMsgList().add(currencyMsg);
                                    continue;
                                }
                                currencyCheckService.addMsgInfoInEmployee(travelOnBusiOrderRuleCheckResults, employeeRuleSummary);
                        }
                    }
                }
            } else if (trainOrderVerifyFlag && CollectionUtils.isEmpty(employeeRuleSummary.getCurrencyMsgList())) {
                CurrencyMsg currencyMsg = new CurrencyMsg();
                currencyMsg.setErrMsg(CoreLanguage.TrainCheckServiceImpl_Value_OrderNeedCheck.getMessage());
                currencyMsg.setCode(GlobalResponseCode.OrderCheckApplyExceedLess.getCode());
                employeeRuleSummary.getCurrencyMsgList().add(currencyMsg);
            }
            checkResult.setPersonnelInfoList(personnelInfoList);
            if (CollectionUtils.isNotEmpty(employeeRuleSummary.getCurrencyMsgList())) {
                List<CurrencyMsg> currencyMsgList = employeeRuleSummary.getCurrencyMsgList().stream().distinct().collect(Collectors.toList());
                employeeRuleSummary.setCurrencyMsgList(currencyMsgList);
            }
            //如果有一项超规则设置超规
            if(employeeRuleSummary.getIsPriceExceed() || employeeRuleSummary.getIsNoPriceExceed()){
                employeeRuleSummary.setIsExceed(true);
            }
        }

        log.info("规则后companyRuleSummary:{}", JsonUtils.toJson(companyRuleSummary));
        //设置每个人的最终结果集
        currencyCheckService.setEmployeeResultEnum(companyRuleSummary);
        log.info("设置每个人的最终结果集后companyRuleSummary:{}", JsonUtils.toJson(companyRuleSummary));
        //设置总体返回是否有费用项超规
        checkResult.setIsPriceExceed(companyRuleSummary.getEmployeeRuleSummaryList().stream().anyMatch(a -> a.getIsPriceExceed() == true));
        //设置返回设置返回
        checkResult.setIs_exceed(companyRuleSummary.getEmployeeRuleSummaryList().stream().anyMatch(a -> a.getIsExceed() == true));
        // 设置个人优惠券
        currencyCheckService.setReSetCouponAmount(reqContract, companyRuleSummary);
        //获取多人校验结果 统一返回值处理
        CurrencyErrorMessage currencyErrorMassage = currencyCheckService.getMultiPlayerResults(companyRuleSummary, checkResult, reqContract.getTravel_on_busi_parameter_req_contract().getExceed_submit());
        log.info("currencyErrorMassage-{}", JsonUtils.toJson(currencyErrorMassage));
        //说明无法下单 需要给前端相应提示
        if(currencyErrorMassage.getCode() != GlobalResponseCode.Success.getCode()){
            checkResult.setCurrencyErrorMessage(currencyErrorMassage);
            checkResult.setResCode(GlobalResponseCode.CurrencyFrame);
            checkResult.setType(8);

            //判断一下是否是 订单审批 还是超规审批 查看历史有没有审批通过的审批单，如果有直接返回前端标识， 二次提交放行
            if (GlobalResponseCode.CheckOrderApproval.getCode() == currencyErrorMassage.getCode() ||
                    GlobalResponseCode.CheckExceedApprovalOrPersonalPay.getCode() == currencyErrorMassage.getCode() ||
                    GlobalResponseCode.ExceedApprovalOrPartPayPriceDesc.getCode() == currencyErrorMassage.getCode()
            ) {
                OrderApplyMsgReq orderApplyMsgReq = new OrderApplyMsgReq();
                orderApplyMsgReq.setArrivalCityId(reqContract.getTravel_on_busi_parameter_req_contract().getArrival_city_id());
                orderApplyMsgReq.setStartCityId(reqContract.getTravel_on_busi_parameter_req_contract().getStart_city_id());
                orderApplyMsgReq.setOrderPrice(reqContract.getTravel_on_busi_common_req_contract().getOrder_price());
                orderApplyMsgReq.setTravelList(reqContract.getTravel_on_busi_common_req_contract().getTraveler_list());
                orderApplyMsgReq.setCompanyId(companyId);
                orderApplyMsgReq.setEmployeeId(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id());
                orderApplyMsgReq.setCategoryTypeEnum(CategoryTypeEnum.Train);
                orderApplyMsgReq.setIsExceed(companyRuleSummary.getEmployeeRuleSummaryList().stream().anyMatch(a -> a.getIsExceed() == true));
                orderApplyMsgReq.setStartTime(reqContract.getTravel_on_busi_parameter_req_contract().getStart_time());
                orderApplyMsgReq.setEndTime(reqContract.getTravel_on_busi_parameter_req_contract().getEnd_time());
                String duringApplyId = currencyCheckService.queryOrderApplyMsgByMutil( orderApplyMsgReq);
                logger.info("duringApplyId:{}", duringApplyId);
                if (StringUtils.isNotBlank(duringApplyId)) {
                    //二次订单审批 直接放行
                    if (!reqContract.getTravel_on_busi_parameter_req_contract().getExist_center_apply_submit()) {
                        logger.info("符合条件无需审批，弹窗提示");
                        checkResult.setErrCode(GlobalResponseCode.OrderCheckApplytTrainHint.getCode());
                        if(orderApplyMsgReq.getIsExceed()){
                            checkResult.setErrMsg(GlobalResponseCode.OrderCheckExceedApplytTrainHint.getMsg());
                        }else{
                            checkResult.setErrMsg(GlobalResponseCode.OrderCheckApplytTrainHint.getMsg());
                        }
                        return checkResult;
                    } else {
                        logger.info("符合条件无需审批，返回审批单id和超规标识");
                        checkResult.setDuring_reapply_id(duringApplyId);
                        checkResult.setResCode(GlobalResponseCode.Success);
                        return checkResult;
                    }
                }
            }

            // 填写理由+个人支付
            if (GlobalResponseCode.PartPayPriceDescWithReason.getCode() == currencyErrorMassage.getCode()
                || GlobalResponseCode.AllPayPriceDescWithReason.getCode() == currencyErrorMassage.getCode()) {
                //设置公司支付金额
                BigDecimal companyPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
                    EmployeeRuleSummary::getCompanyPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal personalPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
                    EmployeeRuleSummary::getPersonalPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                checkResult.setCompanyPayPrice(companyPayPrice);
                checkResult.setPersonalPayPrice(personalPayPrice);
                checkResult.setPersonalPay(true);
            }

            if(reqContract.getTravel_on_busi_parameter_req_contract().getExceed_submit()){

                //超规全额付 无需理由
                if(GlobalResponseCode.AllPayPriceDesc.getCode() == currencyErrorMassage.getCode() ){
                    //设置公司支付金额 0
                    checkResult.setCompanyPayPrice(BigDecimal.ZERO);
                    checkResult.setPersonalPay(true);
                    //个人支付金额为全额
                    BigDecimal personalPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
                        EmployeeRuleSummary::getPersonalPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                    checkResult.setPersonalPayPrice(personalPayPrice);
                }

                //超规部分支付  无需理由
                if(GlobalResponseCode.PartPayPriceDesc.getCode() == currencyErrorMassage.getCode()){
                    //设置公司支付金额
                    BigDecimal companyPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
                        EmployeeRuleSummary::getCompanyPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal personalPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
                        EmployeeRuleSummary::getPersonalPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                    checkResult.setCompanyPayPrice(companyPayPrice);
                    checkResult.setPersonalPayPrice(personalPayPrice);
                    checkResult.setPersonalPay(true);
                }

                //二次提交且标记成全额个人支付后
                if((GlobalResponseCode.PartPayPriceDesc.getCode() == currencyErrorMassage.getCode()
                    || GlobalResponseCode.AllPayPriceDesc.getCode() == currencyErrorMassage.getCode()
                    ||  GlobalResponseCode.CheckExceedReasonAndPersonalPay.getCode() == currencyErrorMassage.getCode())
                    && Objects.equals(reqContract.getTravel_on_busi_parameter_req_contract()
                    .getExceed_personalpay_sumbit(), true)) {
                    checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                    //设置公司支付金额
                    BigDecimal companyPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
                        EmployeeRuleSummary::getCompanyPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal personalPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
                        EmployeeRuleSummary::getPersonalPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                    checkResult.setCompanyPayPrice(companyPayPrice);
                    checkResult.setPersonalPayPrice(personalPayPrice);
                    checkResult.setPersonalPay(true);
                    checkResult.setResCode(GlobalResponseCode.Success);
                }

                // 填写理由+个人支付
                if (GlobalResponseCode.PartPayPriceDescWithReason.getCode() == currencyErrorMassage.getCode()
                    || GlobalResponseCode.AllPayPriceDescWithReason.getCode() == currencyErrorMassage.getCode()) {
                    //设置公司支付金额
                    BigDecimal companyPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
                        EmployeeRuleSummary::getCompanyPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal personalPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
                        EmployeeRuleSummary::getPersonalPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                    checkResult.setCompanyPayPrice(companyPayPrice);
                    checkResult.setPersonalPayPrice(personalPayPrice);
                    checkResult.setPersonalPay(true);

                    //填写理由校验 填写项是否正常
                    TravelOnBusiOrderRuleCheckResult travelOnBusiOrderRuleCheckResult = iOrderCheckService
                        .checkExceedAuthV2(reqContract, CategoryTypeEnum.Train);
                    if (travelOnBusiOrderRuleCheckResult.getErrCode()!=GlobalResponseCode.Success.getCode()) {
                        checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                        checkResult.setErrCode(travelOnBusiOrderRuleCheckResult.getErrCode());
                        checkResult.setErrMsg(travelOnBusiOrderRuleCheckResult.getErrMsg());
                    } else {
                        // 标记成全额个人支付后
                        if (Objects.equals(reqContract.getTravel_on_busi_parameter_req_contract()
                            .getExceed_personalpay_sumbit(), true)) {
                            checkResult.setResCode(GlobalResponseCode.Success);
                        }
                        checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                    }
                }

                if(GlobalResponseCode.CheckExceedApprovalOrPersonalPay.getCode()== currencyErrorMassage.getCode() ||
                        GlobalResponseCode.CheckPersonalPay.getCode()== currencyErrorMassage.getCode() ||
                        GlobalResponseCode.CheckOrderApproval.getCode()== currencyErrorMassage.getCode()
                ){
                    checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                    checkResult.setResCode(GlobalResponseCode.Success);
                }
                else if (GlobalResponseCode.CheckExceedReasonOrPersonalPay.getCode()== currencyErrorMassage.getCode()
                    || GlobalResponseCode.CheckReason.getCode()== currencyErrorMassage.getCode()
                    || GlobalResponseCode.ExceedAirReason.getCode() == currencyErrorMassage.getCode()) {
                    //填写理由校验 填写项是否正常
                    TravelOnBusiOrderRuleCheckResult travelOnBusiOrderRuleCheckResult =
                        iOrderCheckService.checkExceedAuthV2(reqContract, CategoryTypeEnum.Train);
                    if(travelOnBusiOrderRuleCheckResult.getErrCode()!=GlobalResponseCode.Success.getCode()){
                        checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                        checkResult.setErrCode(travelOnBusiOrderRuleCheckResult.getErrCode());
                        checkResult.setErrMsg(travelOnBusiOrderRuleCheckResult.getErrMsg());
                    }else{
                        checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                        checkResult.setResCode(GlobalResponseCode.Success);
                    }
                }
            }
            //如果强制超规个人支付 则直接返回正确
            Boolean exceedPersonalpaySumbit = reqContract.getTravel_on_busi_parameter_req_contract().getExceed_personalpay_sumbit();
            if(checkResult.getPersonalPay() && exceedPersonalpaySumbit){
                checkResult.setResCode(GlobalResponseCode.Success);
            }
        }
        snapshotInfo.put("companyRuleSummary", companyRuleSummary);
        //完整日志打印
        log.info("companyRuleSummary-{}",JsonUtils.toJson(companyRuleSummary));
        return checkResult;
    }

    /**
     * 校验审批规则信息
     *
     * @param trainRuleList
     * @return
     */
    private TravelOnBusiOrderRuleCheckResult checkTrainThirdRule(List<ApplyThirdContract.KeyValueItem> trainRuleList, TravelOnBusiOrderRuleCheckResult checkResult, TrainOrderCheckReqV2Contract reqContract, BigDecimal totalPrice, EmployeeRuleSummary employeeRuleSummary) {
        TrainRule trainRule = new TrainRule();
        for (ApplyThirdContract.KeyValueItem trainInfo : trainRuleList) {
            trainRule.setDayLimit(false);
            trainRule.setTrainSeatFlag(false);
            String ruleType = trainInfo.getType();
            Object ruleValue = trainInfo.getValue();
            if (SaasOrderThirdRuleConstant.TRAIN_PRICE.equals(ruleType)) {
                trainRule.setPriceLimit(ObjUtils.toBigDecimal(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.TRAIN_PRIV_DAY_MIN.equals(ruleType)) {
                trainRule.setPrivDayMin(ObjUtils.toInteger(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.TRAIN_PRIV_DAY_MAX.equals(ruleType)) {
                trainRule.setPrivDayMax(ObjUtils.toInteger(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.TRAIN_COMMON_SEAT_TYPE.equals(ruleType)) {
                trainRule.setTrainSeatFlag(true);
                String commonSeatType = StringUtils.join((List<Integer>) ruleValue, ",");
                trainRule.setCommonTrainSeatType(commonSeatType);
            }
            if (SaasOrderThirdRuleConstant.TRAIN_HIGHSPEED_SEAT_TYPE.equals(ruleType)) {
                trainRule.setTrainSeatFlag(true);
                String highSpeedSeatType = StringUtils.join((List<Integer>) ruleValue, ",");
                trainRule.setHighspeedTrainSeatType(highSpeedSeatType);
            }
            if (SaasOrderThirdRuleConstant.TRAIN_DAY_LIMIT.equals(ruleType)) {
                trainRule.setDayLimit(ObjUtils.toBoolean(ruleValue, false));
            }
        }
        List<TravelOnBusiOrderRuleCheckResult> travelOnBusiOrderRuleCheckResults = checkTrainExceedType(trainRule, reqContract, totalPrice);
        if (CollectionUtils.isNotEmpty(travelOnBusiOrderRuleCheckResults)) {
            //增加返回字段
            iOrderCheckService.setErrMsgInfoNotAllowed(checkResult, travelOnBusiOrderRuleCheckResults);
            currencyCheckService.addMsgInfoInEmployee(travelOnBusiOrderRuleCheckResults, employeeRuleSummary);
            return checkResult;
        }
        return checkResult;
    }

    //处理火车公用超标逻辑
    private List<TravelOnBusiOrderRuleCheckResult> checkTrainExceedType(TrainRule trainRule, TrainOrderCheckReqV2Contract reqContract, BigDecimal totalPrice) {
        List<TravelOnBusiOrderRuleCheckResult> travelOnBusiOrderRuleCheckResults = new ArrayList<>();
        //判断是否有价格限制
        if (trainRule.getPriceLimit() != null) {
            if (reqContract.getTransfer()) {
                reqContract.getOrder_parameter_jsons().stream().forEach(orderjson -> {
                    if (orderjson.getSeat_price().compareTo(trainRule.getPriceLimit()) == 1) {
                        checkPriceLimit(trainRule, reqContract, totalPrice, travelOnBusiOrderRuleCheckResults);
                        return;
                    }
                });
            } else if (reqContract.getOrder_parameter_json().getSeat_price().compareTo(trainRule.getPriceLimit()) == 1) {
                checkPriceLimit(trainRule, reqContract, totalPrice, travelOnBusiOrderRuleCheckResults);
            }
        }
        String start_time = reqContract.getTravel_on_busi_parameter_req_contract().getStart_time();
        LocalDate startTime = LocalDateTime.ofInstant(DateTimeTool.fromStringToDateTime(start_time).toInstant(), ZoneId.systemDefault()).toLocalDate();
        LocalDate currentTime = LocalDate.now();
        //下单时间与预定时间相差天数
        long daysDiff = ChronoUnit.DAYS.between(currentTime, startTime);
        Integer privDayMin = trainRule.getPrivDayMin();
        if (privDayMin != null && daysDiff < privDayMin) {
            logger.info(StrUtils.formatString("申请人违反了「最少提前{0}天预订」的限制", privDayMin));
            TravelOnBusiOrderRuleCheckResult checkResults = new TravelOnBusiOrderRuleCheckResult();
//            String msg = StrUtils.formatString("申请人违反了「最少提前{0}天预订」的限制", privDayMin);
            String msg = StrUtils.formatString(CoreLanguage.TrainCheckServiceImpl_Value_MinDay.getMessage(), privDayMin);
            checkResults.setResCode(GlobalResponseCode.OrderCheckLeastDay.getCode(), msg);
            checkResults.setExceed_msg(msg);
            checkResults.setType(3);
            travelOnBusiOrderRuleCheckResults.add(checkResults);
        }
        Integer privDayMax = trainRule.getPrivDayMax();
        if (privDayMax != null && daysDiff > privDayMax) {
            logger.info(StrUtils.formatString("申请人违反了「最多提前{0}天预订」的限制", privDayMax));
            TravelOnBusiOrderRuleCheckResult checkResults = new TravelOnBusiOrderRuleCheckResult();
//            String msg = StrUtils.formatString("申请人违反了「最多提前{0}天预订」的限制", privDayMax);
            String msg = StrUtils.formatString(CoreLanguage.TrainCheckServiceImpl_Value_MaxDay.getMessage(), privDayMax);
            checkResults.setResCode(GlobalResponseCode.OrderCheckMostDay.getCode(), msg);
            checkResults.setExceed_msg(msg);
            checkResults.setType(3);
            travelOnBusiOrderRuleCheckResults.add(checkResults);
        }
        //夕发朝至限制 当数据库中的dayLimit为true的时候限制
        //查询数据库的 dayLimit数据
        Boolean dayLimit = trainRule.getDayLimit();
        if (dayLimit && reqContract.getIs_day_limit()) {
            //获取客户端传入的火车出发时间
            String beginTime = reqContract.getTravel_on_busi_parameter_req_contract().getStart_time();
            Date beginDate = DateUtils.parse(beginTime);
            SimpleDateFormat format = new SimpleDateFormat("HH:mm:ss");
            String dateBegin = format.format(beginDate);//客户端传入时间
            //获取客户端传入的火车结束时间
            String endTime = reqContract.getTravel_on_busi_parameter_req_contract().getEnd_time();
            Date endDate = DateUtils.parse(endTime);
            String dateEnd = format.format(endDate);//客户端传入结束时间
            Date dateBeginTime = null;//转换客户端传入的开始时间
            try {
                dateBeginTime = format.parse(dateBegin);
                Date dateEndTime = format.parse(dateEnd);//转入客户端传入的结束时间

                //16点至23点之间发车，次日5点至11点到达终点站的列车
                String comBeginTime = "16:00:00";
                Date beginTimeParse = format.parse(comBeginTime);
                String comBeginLastTime = "23:00:00";
                Date comBeginLastTimeParse = format.parse(comBeginLastTime);

                String comEndTime = "5:00:00";
                Date comEndTimeParse = format.parse(comEndTime);
                String comEndLastTime = "11:00:00";
                Date comEndLastTimeParse = format.parse(comEndLastTime);

                if (!(dateBeginTime.before(comBeginLastTimeParse) && dateBeginTime.after(beginTimeParse))
                        || !((dateEndTime.before(comEndLastTimeParse) && dateEndTime.after(comEndTimeParse)))) {
                    TravelOnBusiOrderRuleCheckResult checkResults = new TravelOnBusiOrderRuleCheckResult();
//                    String msg = StrUtils.formatString("申请人违反了「夕发朝至」的限制");
                    String msg = StrUtils.formatString(CoreLanguage.TrainCheckServiceImpl_Value_NeedDayToNight.getMessage());
                    checkResults.setResCode(GlobalResponseCode.OrderCheckTrainRuleDayAndOrder.getCode(), msg);
                    checkResults.setExceed_msg(msg);
                    checkResults.setType(4);
                    travelOnBusiOrderRuleCheckResults.add(checkResults);
                }
            } catch (ParseException e) {
                logger.error("checkTrainExceedType异常，{}", e.getLocalizedMessage());
            }
        }
        //校验火车规则
        if (trainRule.getTrainSeatFlag()) {
            String msg = "";
            String hightStr = TrainSeatType.getNamesByCodes(trainRule.getHighspeedTrainSeatType());
            String commonStr = TrainSeatType.getNamesByCodes(trainRule.getCommonTrainSeatType());
            if (StringUtils.isNotEmpty(hightStr)) {
                msg = CoreLanguage.TrainCheckServiceImpl_Value_HighTrain.getMessage() + ":" + hightStr;
            }
            if (StringUtils.isNotEmpty(msg) && StringUtils.isNotEmpty(commonStr)) {
                msg = msg + "\n" + CoreLanguage.TrainCheckServiceImpl_Value_Train.getMessage() + ":" + commonStr;
            } else if (StringUtils.isEmpty(msg) && StringUtils.isNotEmpty(commonStr)) {
                msg = CoreLanguage.TrainCheckServiceImpl_Value_Train.getMessage() + ":" + commonStr;
            }
            //判断是否只允许定一种类型的火车
            if (StringUtils.isEmpty(trainRule.getCommonTrainSeatType()) && StringUtils.isEmpty(trainRule.getHighspeedTrainSeatType())) {
                // 普通和高铁都没有
                TravelOnBusiOrderRuleCheckResult checkResults = new TravelOnBusiOrderRuleCheckResult();
                checkResults.setResCode(GlobalResponseCode.OrderCheckTrainNoAuth);
                travelOnBusiOrderRuleCheckResults.add(checkResults);
                return travelOnBusiOrderRuleCheckResults;
            } else if (StringUtils.isEmpty(trainRule.getCommonTrainSeatType()) || StringUtils.isEmpty(trainRule.getHighspeedTrainSeatType())) {
                // 普通和高铁有一种
                if (reqContract.getIs_high_speed_flag() && !StringUtils.isEmpty(trainRule.getCommonTrainSeatType())) {
                    TravelOnBusiOrderRuleCheckResult checkResults = new TravelOnBusiOrderRuleCheckResult();
                    checkResults.setResCode(GlobalResponseCode.OrderCheckTrainCommonSeatTypeCompanyAccountNoAuth);
                    checkResults.setType(1);
                    travelOnBusiOrderRuleCheckResults.add(checkResults);
                    return travelOnBusiOrderRuleCheckResults;
                } else if (reqContract.getIs_common_speed() && !StringUtils.isEmpty(trainRule.getHighspeedTrainSeatType())) {
                    TravelOnBusiOrderRuleCheckResult checkResults = new TravelOnBusiOrderRuleCheckResult();
                    checkResults.setResCode(GlobalResponseCode.OrderCheckTrainHightSpeedSeatTypeNoAuth);
                    checkResults.setType(1);
                    travelOnBusiOrderRuleCheckResults.add(checkResults);
                    return travelOnBusiOrderRuleCheckResults;
                }
                if (reqContract.getIs_common_speed() && StringUtils.isEmpty(trainRule.getHighspeedTrainSeatType())) {
                    if (reqContract.getTransfer()) {
                        for (TrainInterceptRecordContract orderjson : reqContract.getOrder_parameter_jsons()) {
                            if (!trainRule.getCommonTrainSeatType().contains(TrainCommonSpeedType.getValueByCode(orderjson.getSeat_no()))) {
                                TravelOnBusiOrderRuleCheckResult checkResults = new TravelOnBusiOrderRuleCheckResult();
                                checkResults.setType(1);
                                checkResults.setResCode(GlobalResponseCode.OrderCheckTrainSeatTypeNoAuth.getCode(), StrUtils.formatString(GlobalResponseCode.OrderCheckTrainSeatTypeNoAuth.getMsg(), msg));
                                travelOnBusiOrderRuleCheckResults.add(checkResults);
                                break;
                            }
                        }
                        return travelOnBusiOrderRuleCheckResults;
                    } else if (!trainRule.getCommonTrainSeatType().contains(TrainCommonSpeedType.getValueByCode(reqContract.getTrain_seat_type()))) {
                        TravelOnBusiOrderRuleCheckResult checkResults = new TravelOnBusiOrderRuleCheckResult();
                        checkResults.setType(1);
                        checkResults.setResCode(GlobalResponseCode.OrderCheckTrainSeatTypeNoAuth.getCode(), StrUtils.formatString(GlobalResponseCode.OrderCheckTrainSeatTypeNoAuth.getMsg(), msg));
                        travelOnBusiOrderRuleCheckResults.add(checkResults);
                        return travelOnBusiOrderRuleCheckResults;
                    }
                }
                if (reqContract.getIs_high_speed_flag() && StringUtils.isEmpty(trainRule.getCommonTrainSeatType())) {
                    if (reqContract.getTransfer()) {
                        for (TrainInterceptRecordContract orderjson : reqContract.getOrder_parameter_jsons()) {
                            if (!trainRule.getHighspeedTrainSeatType().contains(TrainHighSpeedType.getValueByCode(orderjson.getSeat_no()))) {
                                TravelOnBusiOrderRuleCheckResult checkResults = new TravelOnBusiOrderRuleCheckResult();
                                checkResults.setResCode(GlobalResponseCode.OrderCheckTrainSeatTypeNoAuth.getCode(), StrUtils.formatString(GlobalResponseCode.OrderCheckTrainSeatTypeNoAuth.getMsg(), msg));
                                checkResults.setType(1);
                                travelOnBusiOrderRuleCheckResults.add(checkResults);
                                break;
                            }
                        }
                        return travelOnBusiOrderRuleCheckResults;
                    } else if (!trainRule.getHighspeedTrainSeatType().contains(TrainHighSpeedType.getValueByCode(reqContract.getTrain_seat_type()))) {
                        TravelOnBusiOrderRuleCheckResult checkResults = new TravelOnBusiOrderRuleCheckResult();
                        checkResults.setResCode(GlobalResponseCode.OrderCheckTrainSeatTypeNoAuth.getCode(), StrUtils.formatString(GlobalResponseCode.OrderCheckTrainSeatTypeNoAuth.getMsg(), msg));
                        checkResults.setType(1);
                        travelOnBusiOrderRuleCheckResults.add(checkResults);
                        return travelOnBusiOrderRuleCheckResults;
                    }
                }
            } else {
                // 普通和高铁两种都有
                //判断火车席位问题
                logger.info("火车席位，是否包含高铁：{}，是否包含普通：{},席位：{}", reqContract.getIs_high_speed_flag(), reqContract.getIs_common_speed(), reqContract.getTrain_seat_type());
                if (reqContract.getTransfer()) {
                    for (TrainInterceptRecordContract orderjson : reqContract.getOrder_parameter_jsons()) {
                        String trainHighType = TrainHighSpeedType.getValueByCode(orderjson.getSeat_no());
                        String trainCommonType = TrainCommonSpeedType.getValueByCode(orderjson.getSeat_no());
                        Boolean high_speed_flag = orderjson.getIs_high_speed_flag();
                        checkSeatTypeByTrainRule(trainRule, travelOnBusiOrderRuleCheckResults, msg, trainHighType, trainCommonType, high_speed_flag);
                    }
                } else {
                    String trainHighType = TrainHighSpeedType.getValueByCode(reqContract.getTrain_seat_type());
                    String trainCommonType = TrainCommonSpeedType.getValueByCode(reqContract.getTrain_seat_type());
                    Boolean high_speed_flag = reqContract.getIs_high_speed_flag();
                    checkSeatTypeByTrainRule(trainRule, travelOnBusiOrderRuleCheckResults, msg, trainHighType, trainCommonType, high_speed_flag);
                }
                return travelOnBusiOrderRuleCheckResults;
            }
        }
        if (reqContract.getTransfer()) {
            return travelOnBusiOrderRuleCheckResults.stream().distinct().collect(Collectors.toList());
        }
        return travelOnBusiOrderRuleCheckResults;
    }

    /**
     * 根据火车规则及坐席限制判断是否违反规则
     *
     * @param trainRule
     * @param travelOnBusiOrderRuleCheckResults
     * @param msg
     * @param trainHighType
     * @param trainCommonType
     * @param high_speed_flag
     */
    private void checkSeatTypeByTrainRule(TrainRule trainRule, List<TravelOnBusiOrderRuleCheckResult> travelOnBusiOrderRuleCheckResults, String msg, String trainHighType, String trainCommonType, Boolean high_speed_flag) {
        if (high_speed_flag) {
            if (StringUtils.isNotBlank(trainRule.getHighspeedTrainSeatType())) {
                List<String> highspeedTrainSeatTypeList = Arrays.asList(trainRule.getHighspeedTrainSeatType().split(","));
                if (!highspeedTrainSeatTypeList.contains(trainHighType)) {
                    TravelOnBusiOrderRuleCheckResult checkResults = new TravelOnBusiOrderRuleCheckResult();
                    //checkResults.setResCode(GlobalResponseCode.OrderCheckTrainSeatTypeNoAuth.getCode(), StrUtils.formatString(GlobalResponseCode.OrderCheckTrainSeatTypeNoAuth.getMsg(), msg));
                    checkResults.setResCode(GlobalResponseCode.OrderCheckTrainSeatNoAuth.getCode(), StrUtils.formatString(GlobalResponseCode.OrderCheckTrainSeatNoAuth.getMsg(), msg));
                    checkResults.setType(1);
                    travelOnBusiOrderRuleCheckResults.add(checkResults);
                }
            }
        } else {
            if (StringUtils.isNotBlank(trainRule.getCommonTrainSeatType())) {
                List<String> commonTrainSeatTypeList = Arrays.asList(trainRule.getCommonTrainSeatType().split(","));
                if (!commonTrainSeatTypeList.contains(trainCommonType)) {
                    TravelOnBusiOrderRuleCheckResult checkResults = new TravelOnBusiOrderRuleCheckResult();
                    checkResults.setResCode(GlobalResponseCode.OrderCheckTrainSeatNoAuth.getCode(), StrUtils.formatString(GlobalResponseCode.OrderCheckTrainSeatNoAuth.getMsg(), msg));
                    checkResults.setType(1);
                    travelOnBusiOrderRuleCheckResults.add(checkResults);
                }
            }
        }
    }

    /**
     * 进行价格校验
     *
     * @param trainRule
     * @param reqContract
     * @param totalPrice
     * @param travelOnBusiOrderRuleCheckResults
     */
    private void checkPriceLimit(TrainRule trainRule, TrainOrderCheckReqV2Contract reqContract, BigDecimal totalPrice, List<TravelOnBusiOrderRuleCheckResult> travelOnBusiOrderRuleCheckResults) {
        TravelOnBusiOrderRuleCheckResult checkResults = new TravelOnBusiOrderRuleCheckResult();
        String msg = StrUtils.formatString("单张票价格需低于¥{0}", BigDecimalTool.formatMoney(trainRule.getPriceLimit()));
        checkResults.setErrCode(GlobalResponseCode.OrderCheckTrainRuleBelowPriceLimitMsg.getCode());
        checkResults.setErrMsg(msg);
        checkResults.setExceed_msg(msg);
        checkResults.setType(2);
        if (ObjUtils.isNotEmpty(totalPrice)) {
            checkResults.setIsPriceExceed(true);
            checkResults.setAmountCompliance(trainRule.getPriceLimit().multiply(new BigDecimal(reqContract.getPassengers().size())));
            checkResults.setAmountNonCompliance(totalPrice.subtract(checkResults.getAmountCompliance()));
        }
        travelOnBusiOrderRuleCheckResults.add(checkResults);
    }

    /**
     * 添加火车拦截记录信息
     */
    private void initTrainInterceptRecord(TrainOrderCheckReqV2Contract reqContract, TravelOnBusiOrderRuleCheckResult ruleCheckResult) {
        TrainInterceptRecord trainInterceptRecord = new TrainInterceptRecord();
        //拦截参数
        try {
            //处理规则信息
            EmployeeTrainRule employeeTrainRule = iBaseEmployeeTrainRuleService.queryEmployeeTrainRuleByPrimaryKey(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(), reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
            TrainRule trainRule = null;
            if (employeeTrainRule != null) {
                String ruleId = ObjUtils.ifNull(employeeTrainRule.getManual_train_rule_id(), employeeTrainRule.getDefault_train_rule_id());
                trainRule = trainRuleMapper.selectByPrimaryKey(ruleId);
            }
            //处理拦截信息
            TrainInterceptRecordContract trainInterceptRecordContract = reqContract.getOrder_parameter_json();
            trainInterceptRecord.setId(IDTool.CreateUniqueID());
            trainInterceptRecord.setEmployeeId(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id());
            trainInterceptRecord.setCompanyId(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
            trainInterceptRecord.setCreateTime(new Date());
            trainInterceptRecord.setContactName(reqContract.getTravel_on_busi_parameter_req_contract().getContact_name());
            trainInterceptRecord.setContactPhone(reqContract.getTravel_on_busi_parameter_req_contract().getContact_phone());
            trainInterceptRecord.setChannel(reqContract.getTravel_on_busi_common_req_contract().getChannel());
            trainInterceptRecord.setTotalPrice(reqContract.getTravel_on_busi_common_req_contract().getOrder_price());
            trainInterceptRecord.setTrainRule(employeeTrainRule == null ? -1 : employeeTrainRule.getTrain_rule());
            trainInterceptRecord.setTrainRuleFlag(employeeTrainRule == null ? false : employeeTrainRule.getTrain_rule_flag());
            trainInterceptRecord.setTrainVerifyFlag(employeeTrainRule == null ? false : employeeTrainRule.getTrain_verify_flag());
            trainInterceptRecord.setExceedBuyFlag(reqContract.getTravel_on_busi_parameter_req_contract().getExceed_submit());
            trainInterceptRecord.setExceedBuyType(employeeTrainRule == null ? -1 : employeeTrainRule.getExceed_buy_type());
            trainInterceptRecord.setTrainSeatFlag(trainRule == null ? false : trainRule.getTrainSeatFlag());
            trainInterceptRecord.setCommonTrainSeatType(trainRule == null ? "" : trainRule.getCommonTrainSeatType());
            trainInterceptRecord.setHighspeedTrainSeatType(trainRule == null ? "" : trainRule.getHighspeedTrainSeatType());
            trainInterceptRecord.setTrainCode(trainInterceptRecordContract.getTrain_code());
            trainInterceptRecord.setTrainNo(trainInterceptRecordContract.getTrain_no());
            trainInterceptRecord.setFromStationCode(trainInterceptRecordContract.getFrom_station_code());
            trainInterceptRecord.setFromStationName(trainInterceptRecordContract.getFrom_station_name());
            trainInterceptRecord.setToStationCode(trainInterceptRecordContract.getTo_station_code());
            trainInterceptRecord.setToStationName(trainInterceptRecordContract.getTo_station_name());
            trainInterceptRecord.setTrainStartDate(trainInterceptRecordContract.getTrain_start_date());
            trainInterceptRecord.setTrainEndDate(trainInterceptRecordContract.getTrain_end_date());
            trainInterceptRecord.setStartTime(trainInterceptRecordContract.getStart_time());
            trainInterceptRecord.setArriveTime(trainInterceptRecordContract.getArrive_time());
            trainInterceptRecord.setRunTime(trainInterceptRecordContract.getRun_time());
            trainInterceptRecord.setArriveDays(trainInterceptRecordContract.getArrive_days());
            trainInterceptRecord.setSeatType(trainInterceptRecordContract.getSeat_type());
            trainInterceptRecord.setSeatNo(trainInterceptRecordContract.getSeat_no());
            trainInterceptRecord.setSeatPrice(trainInterceptRecordContract.getSeat_price());
            trainInterceptRecord.setServiceFee(trainInterceptRecordContract.getService_fee());
            //需要处理订票人信息
            List<OrderCheckExt> passengerList = iOrderCheckService.getPassengerList(reqContract.getPassengers());
            String passengerInfoList = JsonUtils.toJson(passengerList);
            trainInterceptRecord.setPassengerInfoList(passengerInfoList);
            trainInterceptRecord.setErrCode(ruleCheckResult.getErrCode());
            String errorMsg = ruleCheckResult.getErrMsg() + (StringUtils.isEmpty(ruleCheckResult.getExceed_msg()) ? "" : "|" + ruleCheckResult.getExceed_msg());
            trainInterceptRecord.setErrMsg(errorMsg);
            //trainInterceptRecord.setCostCenterId(reqContract.getTravel_on_busi_common_req_contract().getAttribution_id());
            //trainInterceptRecord.setCostCenterType(reqContract.getTravel_on_busi_common_req_contract().getAttribution_category());
            trainInterceptRecordMapper.insertSelective(trainInterceptRecord);
        } catch (Exception e) {
            logger.error("添加火车拦截记录信息:{},发生异常:{}", JsonUtils.toJson(trainInterceptRecord), e.getLocalizedMessage());
        }
    }
}
