package com.fenbeitong.saas.core.contract.organization;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/5/24.
 */
public class InportResultContract {

    private JSONArray success;

    private JSONArray fail;

    public InportResultContract(JSONArray success, JSONArray fail) {
        this.success = success;
        this.fail = fail;
    }

    public JSONArray getSuccess() {
        return success;
    }

    public void setSuccess(JSONArray success) {
        this.success = success;
    }

    public JSONArray getFail() {
        return fail;
    }

    public void setFail(JSONArray fail) {
        this.fail = fail;
    }
}
