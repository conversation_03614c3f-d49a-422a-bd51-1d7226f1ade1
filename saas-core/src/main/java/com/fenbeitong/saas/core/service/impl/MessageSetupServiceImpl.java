package com.fenbeitong.saas.core.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.finhub.kafka.consumer.KafkaConsumerUtils;
import com.fenbeitong.finhub.kafka.msg.uc.KafkaCompanyLogMsg;
import com.fenbeitong.finhub.kafka.msg.uc.KafkaEmployeeUpdateMsg;
import com.fenbeitong.saas.core.common.base.SaasException;
import com.fenbeitong.saas.core.common.constant.*;
import com.fenbeitong.saas.core.contract.apply.MessageNumContract;
import com.fenbeitong.saas.core.contract.messagesettings.*;
import com.fenbeitong.saas.core.contract.messagesettings.inner.CommonNoticeContract;
import com.fenbeitong.saas.api.model.dto.user.EmployeeEmailContract;
import com.fenbeitong.saas.core.dao.saas.*;
import com.fenbeitong.saas.core.dao.saasplus.SelfAuthorizationMapper;
import com.fenbeitong.saas.core.language.CoreLanguage;
import com.fenbeitong.saas.core.model.enums.organization.OperateType;
import com.fenbeitong.saas.core.model.enums.selfauthorize.AuthorizationChangeEvent;
import com.fenbeitong.saas.core.model.enums.user.UserRole;
import com.fenbeitong.saas.core.model.saas.*;
import com.fenbeitong.saas.core.model.saasplus.SelfAuthorization;
import com.fenbeitong.saas.core.model.saasplus.SelfAuthorizationExample;
import com.fenbeitong.saas.core.service.IBaseOrganizationService;
import com.fenbeitong.saas.core.service.IMessageSetupService;
import com.fenbeitong.saas.core.service.IOrderCheckService;
import com.fenbeitong.saas.core.service.ISelfAuthorizeService;
import com.fenbeitong.saas.core.service.kafka.CompanyLogKafkaProducerService;
import com.fenbeitong.saas.core.utils.cache.RedisService;
import com.fenbeitong.saas.core.utils.tools.CompanyLogMsgUtil;
import com.fenbeitong.saasplus.api.model.dto.setting.CostAttributionSettingApiVO;
import com.fenbeitong.saasplus.api.model.po.fenbeitong.rulenew.AirManagerSetting;
import com.fenbeitong.saasplus.api.service.setting.ICostAttributionSettingService;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.fenbeitong.usercenter.api.service.employee.IREmployeeService;
import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Lists;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.DateUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.RandomUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by zhangkai on 2017/6/2.
 */
@Service
public class MessageSetupServiceImpl implements IMessageSetupService {

    private static final Logger logger = LoggerFactory.getLogger(MessageSetupServiceImpl.class);

    @Autowired
    private MessageSetupMapper messageSetupMapper;
    @Autowired
    private MessageSetupEmailMapper messageSetupEmailMapper;
    @Autowired
    private MessageSetupEmailRelationMapper messageSetupEmailRelationMapper;
    @Autowired
    private MessageSetupEmailExtMapper messageSetupEmailExtMapper;
    @Autowired
    private MessageSetupEmailRelationExtMapper messageSetupEmailRelationExtMapper;
    @Autowired
    private MessageSetupReceiverMapper messageSetupReceiverMapper;

    @Autowired
    private IBaseOrganizationService baseOrganizationService;

    @Autowired
    private MessageSetupExtMapper messageSetupExtMapper;

    @Autowired
    private MessageSetupPhoneNumMapper messageSetupPhoneNumMapper;

    @Autowired
    private FieldsOptionExtMapper fieldsOptionExtMapper;

    @Autowired
    private FieldsOptionMapper fieldsOptionMapper;

    @Autowired
    private CompanyLogKafkaProducerService companyLogKafkaProducerService;

    private static final int APPLY_ATTRIBUTION_CATEGORY=2;

    private static final int APPLY_ATTRIBUTION_CATEGORY_MODIFIABLEble=1;

    //虚拟卡提醒开启
    private static final Integer SAAS_VIRTUAL_CARD_WRITE_OFF_REMIND_OPEN=1;

    @Autowired private RedisService redisService;

    @Autowired
    ICostAttributionSettingService iCostAttributionSettingService;

    @Autowired
    IOrderCheckService orderCheckService;

    @Autowired
    private MessageSetupItemMapper messageSetupItemMapper;

    @Autowired
    private com.fenbeitong.saasplus.api.service.messageSetup.IMessageSetupService iMessageSetupRpcService;
    @Autowired
    private IREmployeeService irEmployeeService;

    @Autowired
    private SelfAuthorizationMapper selfAuthorizationMapper;

    @Autowired
    private ISelfAuthorizeService iSelfAuthorizeService;

    /**
     * 获取企业消息设置，没有设置的取默认值
     *
     * @param companyId
     * @param busiCodeList
     * @return
     */
    @Override
    public List<MessageSetup> queryCompanyMessageSetupWithDefault(String companyId, List<String> busiCodeList) {
        if (StringUtils.isEmpty(companyId)
                || CollectionUtils.isEmpty(busiCodeList)) {
            return null;
        }
        Map<String, Object> paramMap = new HashedMap();
        paramMap.put("companyId", companyId);
        paramMap.put("busiCodeList", busiCodeList);
        return messageSetupExtMapper.getMessageSetupListWithDefault(paramMap);
    }

    /**
     * 获取企业消息设置，没有设置的取默认值
     *
     * @param companyId
     * @param itemCodeList
     * @return
     */
    @Override
    public List<MessageSetup> queryCompanyMessageSetupWithDefaultByItemCodeList(String companyId, List<String> itemCodeList) {
        if (StringUtils.isEmpty(companyId)
                || CollectionUtils.isEmpty(itemCodeList)) {
            return null;
        }
        Map<String, Object> paramMap = new HashedMap();
        paramMap.put("companyId", companyId);
        paramMap.put("itemCodeList", itemCodeList);
        return messageSetupExtMapper.getMessageSetupListWithDefault(paramMap);
    }

    /**
     * 获取企业单个项消息设置，没有设置的取默认值
     *
     * @param companyId
     * @param itemCode
     * @return
     */
    @Override
    public MessageSetup queryCompanyMessageSetupWithDefault(String companyId, String itemCode) {
        if (StringUtils.isEmpty(companyId)
                || StringUtils.isEmpty(itemCode)) {
            return null;
        }
        Map<String, Object> paramMap = new HashedMap();
        paramMap.put("companyId", companyId);
        paramMap.put("itemCode", itemCode);
        List<MessageSetup> messageSetupList = messageSetupExtMapper.getMessageSetupListWithDefault(paramMap);
        if (CollectionUtils.isEmpty(messageSetupList)) {
            return null;
        }
        return messageSetupList.get(0);
    }

    /**
     * 获取企业消息实际设置
     *
     * @param companyId
     * @param itemCode
     * @return
     */
    @Override
    public MessageSetup queryCompanyMessageSetupWithoutDefault(String companyId, String itemCode) {
        if (StringUtils.isEmpty(companyId)
                || StringUtils.isEmpty(itemCode)) {
            return null;
        }
        MessageSetupExample messageSetupExample = new MessageSetupExample();
        messageSetupExample.createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andItemCodeEqualTo(itemCode);
        List<MessageSetup> messageSetupList = messageSetupMapper.selectByExample(messageSetupExample);
        if (CollectionUtils.isEmpty(messageSetupList)) {
            return null;
        }
        return messageSetupList.get(0);
    }

    /**
     * 获取企业消息实际设置
     *
     * @param companyId
     * @param itemCode
     * @return
     */
    @Override
    public List<MessageSetup> queryCompanyMessageSetupWithoutDefault(String companyId, List<String> itemCode) {
        if (StringUtils.isEmpty(companyId)
                || CollectionUtils.isEmpty(itemCode)) {
            return null;
        }
        MessageSetupExample messageSetupExample = new MessageSetupExample();
        messageSetupExample.createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andItemCodeIn(itemCode);
        List<MessageSetup> messageSetupList = messageSetupMapper.selectByExample(messageSetupExample);
        return messageSetupList;
    }

    /**
     * 保存企业消息设置
     *
     * @param messageSetupList
     * @param companyId
     */
    @Override
    @Transactional(value = "saas")
    public void saveCompanyMessageSetup(List<MessageSetup> messageSetupList, String companyId) {
        if (CollectionUtils.isEmpty(messageSetupList)
                || StringUtils.isEmpty(companyId)) {
            return;
        }
        MessageSetupExample query = new MessageSetupExample();
        query.createCriteria()
                .andCompanyIdEqualTo(companyId);
        List<MessageSetup> oldMessageList = messageSetupMapper.selectByExample(query);
//        logger.info("企业消息提醒保存记录,companyId={},oldMessageList={}", companyId, com.fenbeitong.common.utils.json.JsonUtils.toJsonStr(oldMessageList));
        for (MessageSetup setup : messageSetupList) {
            List<MessageSetup> oldMessageSetupList = getMessageSetupByItem(oldMessageList, setup.getItemCode());
            if (CollectionUtils.isEmpty(oldMessageSetupList)) {
                if (setup.getItemCode().startsWith(SaasMessageConstant.VIRTUAL_CARD_WRITE_OFF_REMIND)
                        || SaasMessageConstant.PETTY_EXPIRE_REMIND.equals(setup.getItemCode())
                        || SaasMessageConstant.CREDIT_REFUND_REMIND.equals(setup.getItemCode())
                        || SaasMessageConstant.CREDIT_REFUND_FAILED_REMIND.equals(setup.getItemCode())
                ) {
                    messageSetupMapper.insertSelective(setup);
                } else {
                    String strVal2 = setup.getStrVal2();
                    Integer intVal2 = setup.getIntVal2();
                    if (intVal2 != null && intVal2 == 1) {
                        setup.setStrVal2("");
                    }
                    messageSetupMapper.insertSelective(setup);
                    Integer messageSetupId = setup.getId();
                    //选项
                    if (setup != null && intVal2 != null && intVal2 == 1) {
                        List<String> optionList = JSONArray.parseArray(strVal2, String.class);
                        insertFieldsOption(companyId, messageSetupId, optionList);
                    }
                }

            } else {
                Integer messageSetupId = oldMessageSetupList.get(0).getId();
                if(Objects.equals(SaasMessageConstant.ITEM_CODE_LARGE_OVER_NOTICE,oldMessageSetupList.get(0).getItemCode())){
                    messageSetupMapper.deleteByPrimaryKey(messageSetupId);
                    setup.setDirectLeader(oldMessageSetupList.get(0).getDirectLeader());
                    setup.setDepartLeader(oldMessageSetupList.get(0).getDepartLeader());
                    messageSetupMapper.insertSelective(setup);
                }else{
                    MessageSetupExample exampleUpdate = new MessageSetupExample();
                    exampleUpdate.createCriteria()
                            .andCompanyIdEqualTo(companyId)
                            .andItemCodeEqualTo(setup.getItemCode());
                    MessageSetup updateMessageSetup = new MessageSetup();
                    updateMessageSetup.setIsChecked(ObjUtils.toInteger(setup.getIsChecked(), 0));
                    updateMessageSetup.setDistinguishCategoryType(setup.getDistinguishCategoryType());
                    updateMessageSetup.setCategoryTypeAmount(setup.getCategoryTypeAmount());
                    updateMessageSetup.setIntVal1(setup.getIntVal1());
                    updateMessageSetup.setIntVal2(setup.getIntVal2());
                    updateMessageSetup.setIntVal3(setup.getIntVal3());
                    updateMessageSetup.setStrVal1(setup.getStrVal1());
                    updateMessageSetup.setStrVal3(setup.getStrVal3());
                    if (setup.getItemCode().startsWith(SaasMessageConstant.VIRTUAL_CARD_WRITE_OFF_REMIND)
                            || SaasMessageConstant.PETTY_EXPIRE_REMIND.equals(setup.getItemCode())
                            || SaasMessageConstant.CREDIT_REFUND_REMIND.equals(setup.getItemCode())
                            || SaasMessageConstant.CREDIT_REFUND_FAILED_REMIND.equals(setup.getItemCode())
                    ){
                        // StrVal2 保存跳过节假日字段
                        updateMessageSetup.setStrVal2(setup.getStrVal2());
                        messageSetupMapper.updateByExampleSelective(updateMessageSetup, exampleUpdate);
                    }else{
                        if (setup != null && setup.getIntVal2() != null && setup.getIntVal2() == 1) {
                            updateMessageSetup.setStrVal2("");
                        } else {
                            updateMessageSetup.setStrVal2(setup.getStrVal2());
                        }
                        updateMessageSetup.setStrVal3(setup.getStrVal3());
                        messageSetupMapper.updateByExampleSelective(updateMessageSetup, exampleUpdate);
                        //选项
                        if (setup != null && setup.getIntVal2() != null && setup.getIntVal2() == 1) {
                            FieldsOptionExample fieldsOptionExample = new FieldsOptionExample();
                            fieldsOptionExample.createCriteria().andCompanyIdEqualTo(companyId).andMessageSetupIdEqualTo(messageSetupId);
                            List<FieldsOption> fieldsOptions = fieldsOptionMapper.selectByExample(fieldsOptionExample);
                            if (CollectionUtils.isEmpty(fieldsOptions)) {
                                List<String> optionList = JSONArray.parseArray(setup.getStrVal2(), String.class);
                                insertFieldsOption(companyId, messageSetupId, optionList);
                            } else {
                                FieldsOptionExample fieldsOptionInfoExample = new FieldsOptionExample();
                                fieldsOptionInfoExample.createCriteria().andCompanyIdEqualTo(companyId).andMessageSetupIdEqualTo(messageSetupId);
                                int count = fieldsOptionMapper.deleteByExample(fieldsOptionInfoExample);
                                if (count < 1) {
                                    throw new SaasException(GlobalResponseCode.NotFound);
                                }
                                List<String> optionList = JSONArray.parseArray(setup.getStrVal2(), String.class);
                                insertFieldsOption(companyId, messageSetupId, optionList);
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    public void refreshCompanyMessageSetup(List<MessageSetup> messageSetupList, String companyId) {
        if (CollectionUtils.isEmpty(messageSetupList)
                || StringUtils.isEmpty(companyId)) {
            return;
        }
        MessageSetupExample query = new MessageSetupExample();
        query.createCriteria()
                .andCompanyIdEqualTo(companyId);
        List<MessageSetup> oldMessageList = messageSetupMapper.selectByExample(query);
//        logger.info("企业消息提醒保存记录,companyId={},oldMessageList={}", companyId, com.fenbeitong.common.utils.json.JsonUtils.toJsonStr(oldMessageList));
        for (MessageSetup setup : messageSetupList) {
            List<MessageSetup> oldMessageSetupList = getMessageSetupByItem(oldMessageList, setup.getItemCode());
            if (CollectionUtils.isEmpty(oldMessageSetupList)) {
                if (setup.getItemCode().startsWith(SaasMessageConstant.VIRTUAL_CARD_WRITE_OFF_REMIND)
                        || SaasMessageConstant.PETTY_EXPIRE_REMIND.equals(setup.getItemCode())) {
                    messageSetupMapper.insertSelective(setup);
                } else {
                    String strVal2 = setup.getStrVal2();
                    Integer intVal2 = setup.getIntVal2();
                    if (intVal2 != null && intVal2 == 1) {
                        setup.setStrVal2("");
                    }
                    messageSetupMapper.insertSelective(setup);
                    Integer messageSetupId = setup.getId();
                    //选项
                    if (setup != null && intVal2 != null && intVal2 == 1) {
                        List<String> optionList = JSONArray.parseArray(strVal2, String.class);
                        insertFieldsOption(companyId, messageSetupId, optionList);
                    }
                }

            } else {
                Integer messageSetupId = oldMessageSetupList.get(0).getId();
                if(Objects.equals(SaasMessageConstant.ITEM_CODE_LARGE_OVER_NOTICE,oldMessageSetupList.get(0).getItemCode())){
                    messageSetupMapper.deleteByPrimaryKey(messageSetupId);
                    setup.setDirectLeader(oldMessageSetupList.get(0).getDirectLeader());
                    setup.setDepartLeader(oldMessageSetupList.get(0).getDepartLeader());
                    messageSetupMapper.insertSelective(setup);
                }else{
                    MessageSetupExample exampleUpdate = new MessageSetupExample();
                    exampleUpdate.createCriteria()
                            .andCompanyIdEqualTo(companyId)
                            .andItemCodeEqualTo(setup.getItemCode());
                    MessageSetup updateMessageSetup = new MessageSetup();
                    updateMessageSetup.setIsChecked(ObjUtils.toInteger(setup.getIsChecked(), 0));
                    updateMessageSetup.setDistinguishCategoryType(setup.getDistinguishCategoryType());
                    updateMessageSetup.setCategoryTypeAmount(setup.getCategoryTypeAmount());
                    updateMessageSetup.setIntVal1(setup.getIntVal1());
                    updateMessageSetup.setIntVal2(setup.getIntVal2());
                    updateMessageSetup.setIntVal3(setup.getIntVal3());
                    updateMessageSetup.setStrVal1(setup.getStrVal1());
                    updateMessageSetup.setStrVal3(setup.getStrVal3());
                    if (setup.getItemCode().startsWith(SaasMessageConstant.VIRTUAL_CARD_WRITE_OFF_REMIND) ||
                            SaasMessageConstant.PETTY_EXPIRE_REMIND.equals(setup.getItemCode())){
                        // StrVal2 保存跳过节假日字段
                        updateMessageSetup.setStrVal2(setup.getStrVal2());
                        messageSetupMapper.updateByExampleSelective(updateMessageSetup, exampleUpdate);
                    }else{
                        if (setup != null && setup.getIntVal2() != null && setup.getIntVal2() == 1) {
                            updateMessageSetup.setStrVal2("");
                        } else {
                            updateMessageSetup.setStrVal2(setup.getStrVal2());
                        }
                        updateMessageSetup.setStrVal3(setup.getStrVal3());
                        messageSetupMapper.updateByExampleSelective(updateMessageSetup, exampleUpdate);
                        //选项
                        if (setup != null && setup.getIntVal2() != null && setup.getIntVal2() == 1) {
                            FieldsOptionExample fieldsOptionExample = new FieldsOptionExample();
                            fieldsOptionExample.createCriteria().andCompanyIdEqualTo(companyId).andMessageSetupIdEqualTo(messageSetupId);
                            List<FieldsOption> fieldsOptions = fieldsOptionMapper.selectByExample(fieldsOptionExample);
                            if (CollectionUtils.isEmpty(fieldsOptions)) {
                                List<String> optionList = JSONArray.parseArray(setup.getStrVal2(), String.class);
                                insertFieldsOption(companyId, messageSetupId, optionList);
                            } else {
                                FieldsOptionExample fieldsOptionInfoExample = new FieldsOptionExample();
                                fieldsOptionInfoExample.createCriteria().andCompanyIdEqualTo(companyId).andMessageSetupIdEqualTo(messageSetupId);
                                int count = fieldsOptionMapper.deleteByExample(fieldsOptionInfoExample);
                                if (count < 1) {
                                    throw new SaasException(GlobalResponseCode.NotFound);
                                }
                                List<String> optionList = JSONArray.parseArray(setup.getStrVal2(), String.class);
                                insertFieldsOption(companyId, messageSetupId, optionList);
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    @Transactional(value = "saas")
    public void deleteCompanyMessageSetup(String companyId, String busiCode, String email) {
        if (StringUtils.isEmpty(companyId) || StringUtils.isEmpty(busiCode)
                || StringUtils.isEmpty(email)){
            return;
        }
        MessageSetupEmailExample example = new MessageSetupEmailExample();
        example.createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andBusiCodeEqualTo(busiCode)
                .andEmailEqualTo(email);
        messageSetupEmailMapper.deleteByExample(example);
    }

    @Override
    @Transactional(value = "saas")
    public void bindHistoryMailMessageSetup(String companyId, String busiCode, String email, String id) {
        if (StringUtils.isEmpty(companyId) || StringUtils.isEmpty(busiCode)
        || StringUtils.isEmpty(email) || StringUtils.isEmpty(id)){
            return;
        }
        MessageSetupEmailRelation relation = new MessageSetupEmailRelation();
        relation.setId(RandomUtils.bsonId());
        relation.setEmployeeId(id);
        relation.setEmail(email);
        relation.setBusiCode(busiCode);
        relation.setCompanyId(companyId);
        relation.setState(1);
        relation.setCreateTime(new Date());
        messageSetupEmailRelationMapper.insertSelective(relation);
    }

    private List<MessageSetup> getMessageSetupByItem(List<MessageSetup> oldMessageList, String itemCode) {
        List<MessageSetup> itemList = Lists.newArrayList();
        for (MessageSetup messageSetup: oldMessageList){
            if(itemCode.equals(messageSetup.getItemCode())){
                itemList.add(messageSetup);
            }
        }
        return itemList;
    }

    /**
     * 批量插入选项
     *
     * @param companyId
     * @param messageSetupId
     * @param optionList
     */
    private void insertFieldsOption(String companyId, Integer messageSetupId, List<String> optionList) {
        if (CollectionUtils.isEmpty(optionList)) {
            return;
        }
        List<FieldsOption> fieldsOptionList = Lists.newArrayList();
        Date now = new Date();
        for (String option : optionList) {
            FieldsOption fieldsOption = new FieldsOption();
            fieldsOption.setCompanyId(companyId);
            fieldsOption.setContent(option);
            fieldsOption.setCreateTime(now);
            fieldsOption.setMessageSetupId(messageSetupId);
            fieldsOptionList.add(fieldsOption);
        }
        fieldsOptionExtMapper.batchInsertFieldsOption(fieldsOptionList);
    }

    /**
     * 获取消息接收列表
     *
     * @param companyId
     * @param busi_code
     * @return
     */
    @Override
    public List<MessageSetupReceiver> queryMessageReceiverList(String companyId, String busi_code) {
        if (StringUtils.isEmpty(companyId)
                || StringUtils.isEmpty(busi_code)) {
            return null;
        }
        MessageSetupReceiverExample example = new MessageSetupReceiverExample();
        example.createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andBusiCodeEqualTo(busi_code);
        return messageSetupReceiverMapper.selectByExample(example);
    }

    /**
     * 获取消息接收列表
     *
     * @param companyId
     * @param busiCodeList
     * @return
     */
    @Override
    public List<MessageSetupReceiver> queryMessageReceiverListByCodeList(String companyId, List<String> busiCodeList) {
        if (ObjUtils.isEmpty(companyId)
                || ObjUtils.isEmpty(busiCodeList)) {
            return null;
        }
        MessageSetupReceiverExample example = new MessageSetupReceiverExample();
        example.createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andBusiCodeIn(busiCodeList);
        return messageSetupReceiverMapper.selectByExample(example);
    }

    /**
     * 保存消息接收人
     *
     * @param messageSetupReceiverList
     * @param companyId
     * @param busiCode
     */
    @Transactional(value = "saas")
    @Override
    public void saveMessageReceiverList(List<MessageSetupReceiver> messageSetupReceiverList, String companyId, String busiCode,ReceiverIdListContract receiverIdListContract) {
        if (StringUtils.isEmpty(companyId)
                || StringUtils.isEmpty(busiCode)) {
            return;
        }
        if(Objects.nonNull(receiverIdListContract)){
            updateDirectAndDepartLeader(receiverIdListContract.getDirect_leader(), receiverIdListContract.getDepart_leader(), companyId);
        }
        MessageSetupReceiverExample example = new MessageSetupReceiverExample();
        example.createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andBusiCodeEqualTo(busiCode);
        messageSetupReceiverMapper.deleteByExample(example);
        if (CollectionUtils.isEmpty(messageSetupReceiverList)) {
            return;
        }
        for (MessageSetupReceiver receiver : messageSetupReceiverList) {
            messageSetupReceiverMapper.insertSelective(receiver);
        }
    }

    /**
     * 删除报表接收人
     *
     * @param receiverList
     * @param companyId
     * @param busiCode
     */
    @Transactional(value = "saas")
    @Override
    public void deleteMessageReceiverList(List<String> receiverList, String companyId, String busiCode) {
        if (StringUtils.isEmpty(companyId)
                || StringUtils.isEmpty(busiCode) || CollectionUtils.isEmpty(receiverList)) {
            return;
        }
        MessageSetupReceiverExample example = new MessageSetupReceiverExample();
        example.createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andBusiCodeEqualTo(busiCode)
                .andUserIdIn(receiverList);
        int count = messageSetupReceiverMapper.deleteByExample(example);
        logger.info("[删除报表接受人]，受影响行数：count={}", count);
    }

    /**
     * 查询消息设置邮箱列表
     *
     * @param companyId
     * @param busiCode
     * @return
     */
    @Override
    public List<MessageSetupEmail> queryMessageEmailList(String companyId, String busiCode) {
        if (StringUtils.isEmpty(companyId)
                || StringUtils.isEmpty(busiCode)) {
            return null;
        }
        MessageSetupEmailExample example = new MessageSetupEmailExample();
        example.createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andBusiCodeEqualTo(busiCode);
        return messageSetupEmailMapper.selectByExample(example);
    }

    /**
     * 查询消息设置邮箱列表
     *
     * @param companyId
     * @param busiCodeList
     * @return
     */
    @Override
    public List<MessageSetupEmail> queryMessageEmailListByCodeList(String companyId, List<String> busiCodeList) {
        if (StringUtils.isEmpty(companyId)
                || ObjUtils.isEmpty(busiCodeList)) {
            return null;
        }
        MessageSetupEmailExample example = new MessageSetupEmailExample();
        example.createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andBusiCodeIn(busiCodeList);
        return messageSetupEmailMapper.selectByExample(example);
    }

    @Override
    public List<MessageSetupEmailRelation> queryMessageEmailRelationListByCodeList(String companyId, List<String> busiCodeList) {
        if (StringUtils.isEmpty(companyId)
                || ObjUtils.isEmpty(busiCodeList)) {
            return null;
        }
        MessageSetupEmailRelationExample example = new MessageSetupEmailRelationExample();
        example.createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andBusiCodeIn(busiCodeList)
                .andStateEqualTo(1);
        return messageSetupEmailRelationMapper.selectByExample(example);
    }


    /**
     * 保存消息设置邮箱
     *
     * @param messageSetupEmailList
     * @param companyId
     * @param busiCode
     */
    @Override
    @Transactional(value = "saas")
    public void saveMessageEmailList(List<MessageSetupEmail> messageSetupEmailList, String companyId, String busiCode) {
        if (StringUtils.isEmpty(companyId)
                || StringUtils.isEmpty(busiCode)) {
            return;
        }
        MessageSetupEmailExample example = new MessageSetupEmailExample();
        example.createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andBusiCodeEqualTo(busiCode);
        //更新前邮箱信息
        List<String> emailOld = messageSetupEmailMapper.selectByExample(example)
                .stream()
                .map(MessageSetupEmail::getEmail)
                .filter(t->!StringUtils.isEmpty(t))
                .distinct()
                .collect(Collectors.toList());

        // 删除邮箱绑定配置
        List<String> emailNew = messageSetupEmailList.stream()
                .map(MessageSetupEmail::getEmail)
                .filter(t -> !StringUtils.isEmpty(t))
                .distinct()
                .collect(Collectors.toList());
        emailOld.removeAll(emailNew);
        if (ObjectUtil.isNotEmpty(emailOld)){
            MessageSetupEmailRelationExample relationExample = new MessageSetupEmailRelationExample();
            relationExample.createCriteria()
                    .andCompanyIdEqualTo(companyId)
                    .andBusiCodeEqualTo(busiCode)
                    .andEmailIn(emailOld);
            messageSetupEmailRelationMapper.deleteByExample(relationExample);
        }
        messageSetupEmailMapper.deleteByExample(example);
        if (CollectionUtils.isEmpty(messageSetupEmailList)) {
            return;
        }
        for (MessageSetupEmail email : messageSetupEmailList) {
            messageSetupEmailMapper.insertSelective(email);
        }
    }

    @Override
    @Transactional(value ="saas")
    public void saveMessageEmailListV2(List<EmployeeEmailContract> messageSetupEmails, String companyId, String busiCode) {
        if (StringUtils.isEmpty(companyId) || StringUtils.isEmpty(busiCode)) {
            return;
        }
        MessageSetupEmailRelationExample relationExample = new MessageSetupEmailRelationExample();
        relationExample.createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andBusiCodeEqualTo(busiCode);
        List<String> emails = messageSetupEmailRelationMapper.selectByExample(relationExample)
                .stream()
                .map(MessageSetupEmailRelation::getEmail).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        messageSetupEmailRelationMapper.deleteByExample(relationExample);
        if (ObjectUtil.isNotEmpty(emails)){
            // 删除老数据
            MessageSetupEmailExample example = new MessageSetupEmailExample();
            List<List<String>> partitions = Lists.partition(emails, NumberConstant.FIVE_HUNDRED);
            for (List<String> list : partitions) {
                example.clear();
                example.createCriteria()
                        .andCompanyIdEqualTo(companyId)
                        .andEmailIn(list)
                        .andBusiCodeEqualTo(busiCode);
                messageSetupEmailMapper.deleteByExample(example);
            }
        }
        //更新入参数据
        if (ObjectUtil.isEmpty(messageSetupEmails)){
            return;
        }
        Date createTime = new Date();
        List<MessageSetupEmailRelation> relations = new ArrayList<>();
        List<MessageSetupEmail> emailList = new ArrayList<>();
        for (EmployeeEmailContract contract : messageSetupEmails) {
            MessageSetupEmailRelation relation = new MessageSetupEmailRelation();
            relation.setId(RandomUtils.bsonId());
            relation.setBusiCode(busiCode);
            relation.setCompanyId(contract.getCompanyId());
            relation.setEmployeeId(contract.getId());
            relation.setEmail(contract.getEmail());
            relation.setState(1);
            relation.setCreateTime(createTime);
            relations.add(relation);
            if (StringUtils.isNotEmpty(contract.getEmail())){
                MessageSetupEmail email = new MessageSetupEmail();
                email.setBusiCode(busiCode);
                email.setCompanyId(contract.getCompanyId());
                email.setEmail(contract.getEmail());
                email.setCreateTime(createTime);
                emailList.add(email);
            }
        }
        if (ObjectUtil.isNotEmpty(relations)){
            messageSetupEmailRelationExtMapper.batchInsert(relations);
        }
        if (ObjectUtil.isNotEmpty(emailList)){
            messageSetupEmailExtMapper.batchInsert(emailList);
        }
    }

    @Override
    public MessageSetupReceiver queryMessageReceiver(String userId, String companyId, String busiCode) {
        if (StringUtils.isEmpty(companyId)
                || StringUtils.isEmpty(userId)
                || StringUtils.isEmpty(busiCode)) {
            return null;
        }
        MessageSetupReceiverExample example = new MessageSetupReceiverExample();
        example.createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andBusiCodeEqualTo(busiCode)
                .andUserIdEqualTo(userId);
        List<MessageSetupReceiver> messageSetupReceiverList = messageSetupReceiverMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(messageSetupReceiverList)) {
            return null;
        }
        return messageSetupReceiverList.get(0);
    }

    @Override
    public Long countMessageSetup(String itemCode, Integer isCheck) {
        if (StringUtils.isEmpty(itemCode)
                || isCheck == null) {
            return 0L;
        }
        MessageSetupExample example = new MessageSetupExample();
        example.createCriteria()
                .andItemCodeEqualTo(itemCode)
                .andIsCheckedEqualTo(isCheck);
        return messageSetupMapper.countByExample(example);
    }

    @Override
    public List<MessageSetup> queryMessageSetupList(String itemCode, Integer isCheck, Integer limit, Integer offset) {
        if (StringUtils.isEmpty(itemCode)
                || isCheck == null) {
            return null;
        }
        MessageSetupExample example = new MessageSetupExample();
        example.createCriteria()
                .andItemCodeEqualTo(itemCode)
                .andIsCheckedEqualTo(isCheck);
        example.setLimit(limit);
        example.setOffset(offset);
        return messageSetupMapper.selectByExample(example);
    }

    /**
     * 是否有查看消费通知的权限，1=是，0=否
     *
     * @param companyId
     * @param userId
     * @return
     */
    @Override
    public int queryConsumeInfoPermission(String companyId, String userId) {
        if (StringUtils.isEmpty(companyId)
                || StringUtils.isEmpty(userId)) {
            return 0;
        }
        //查询公司消息接收设置
        MessageSetup messageSetup = queryCompanyMessageSetupWithDefault(companyId, SaasMessageConstant.ITEM_CODE_CONSUME_INFO_NOTICE);
        if (messageSetup == null
                || messageSetup.getIsChecked() == SaasMessageConstant.IS_CHECKED_FALSE) {
            return 0;
        }
        //查询是否为消息接收人
        MessageSetupReceiver messageSetupReceiver = queryMessageReceiver(userId, companyId, SaasMessageConstant.BUSI_CODE_CONSUME_INFO);
        if (messageSetupReceiver == null) {
            return 0;
        }
        return 1;
    }

    @Override
    public int queryCompanyDashboardPermission(String companyId, String userId) {
        if (StringUtils.isEmpty(companyId)
                || StringUtils.isEmpty(userId)) {
            return 0;
        }
        // 获取报表设置是否应用
        MessageSetup messageSetup = queryCompanyMessageSetupWithoutDefault(companyId, SaasDashboardConstant.BUSI_CODE_DASHBOARD_INFO);
        if (messageSetup == null) {
            return 0;
        } else {
            // 查询是否为报表查看接收人
            MessageSetupReceiver messageSetupReceiver = queryMessageReceiver(userId, companyId, SaasDashboardConstant.BUSI_CODE_DASHBOARD_INFO);
            if (messageSetupReceiver != null) {
                return 1;
            }
        }
        return 0;
    }

    /**
     * 获取公司审批设置
     *
     * @param companyId
     * @return
     */
    @Override
    public MessageNumContract queryCompanyApplySettingUp(String companyId) {
        MessageNumContract messageNumContract = new MessageNumContract();
        if (StringUtils.isEmpty(companyId)) {
            messageNumContract.setApply_setting_up(SaasMessageConstant.IS_CHECKED_TRUE);
        }
        MessageSetup messageSetup = queryCompanyMessageSetupWithDefault(companyId, SaasApplyConstant.BUSI_CODE_APPLY_INFO);
        if (messageSetup == null) {
            messageNumContract.setApply_setting_up(SaasMessageConstant.IS_CHECKED_TRUE);
        } else if (messageSetup.getIsChecked() != null && (messageSetup.getIsChecked() == SaasMessageConstant.IS_CHECKED_FALSE || messageSetup.getIsChecked() == SaasMessageConstant.IS_CHECKED_TRUE)) {
            messageNumContract.setApply_setting_up(messageSetup.getIsChecked());
            messageNumContract.setApply_reminder(messageSetup.getStrVal1());
        } else {
            messageNumContract.setApply_setting_up(SaasMessageConstant.IS_CHECKED_TRUE);
        }
        return messageNumContract;

    }

    /**
     * 国内机票往返配置查询
     *
     * @param companyId
     * @param itemCode
     * @return
     */
    @Override
    public ApplySetupContract queryCompanyAirSetupWithDefault(String companyId, String itemCode) {
        /*if (StringUtils.isEmpty(companyId)) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }*/
        ApplySetupContract applySetupContract = new ApplySetupContract();
        applySetupContract.setWhether_go_and_back(SaasMessageConstant.IS_CHECKED_FALSE);
        applySetupContract.setPrivate_whether_go_and_back(SaasMessageConstant.IS_CHECKED_FALSE);
        MessageSetup messageSetup = queryCompanyMessageSetupWithDefault(companyId, itemCode);
        if (messageSetup != null && messageSetup.getIsChecked() != null) {
            applySetupContract.setWhether_go_and_back(messageSetup.getIsChecked());
        }
        MessageSetup messageSetupInfo = queryCompanyMessageSetupWithDefault(companyId, SaasApplyConstant.PRIVATE_AIR_GO_THERE_AND_BACK);
        if (messageSetupInfo != null && messageSetupInfo.getIsChecked() != null) {
            applySetupContract.setPrivate_whether_go_and_back(messageSetupInfo.getIsChecked());
        }
        return applySetupContract;
    }

    /**
     * 分贝币管理是否显示控制
     *
     * @param companyId
     * @param itemCode
     * @param userId
     * @return
     */
    @Override
    public ApplySetupContract queryCompanyFbbManageSetupWithDefault(String companyId, String itemCode, String userId) {
        if (StringUtils.isEmpty(companyId)) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
        ApplySetupContract applySetupContract = new ApplySetupContract();
        applySetupContract.setWhether_fbb_manage(SaasMessageConstant.IS_CHECKED_FALSE);
        applySetupContract.setWhether_fbb_type(1);
        MessageSetup messageSetup = queryCompanyMessageSetupWithDefault(companyId, itemCode);
        EmployeeContract employee = baseOrganizationService.getEmployee(userId, companyId);
        if (messageSetup != null && messageSetup.getIsChecked() != null) {
            if (messageSetup.getIsChecked() == SaasMessageConstant.IS_CHECKED_FALSE) {
                applySetupContract.setWhether_fbb_type(1);
            } else if (messageSetup.getIsChecked() == SaasMessageConstant.IS_CHECKED_TRUE && employee != null && employee.getRole() != UserRole.CompanySuperAdmin.getValue()) {
                applySetupContract.setWhether_fbb_type(2);
                applySetupContract.setWhether_fbb_manage(SaasMessageConstant.IS_CHECKED_FALSE);
            } else if (messageSetup.getIsChecked() == SaasMessageConstant.IS_CHECKED_TRUE && employee != null && employee.getRole() == UserRole.CompanySuperAdmin.getValue()) {
                applySetupContract.setWhether_fbb_manage(SaasMessageConstant.IS_CHECKED_TRUE);
            }
        }
        return applySetupContract;
    }

    /**
     * 申请单配置保存
     *
     * @param companyId
     * @param applySetupContract
     */
    @Override
    public void saveCompanyApplyConfig(String companyId, ApplySetupContract applySetupContract) {
        if (StringUtils.isEmpty(companyId)) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
        if (applySetupContract == null) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        // 校验isChecked值
        validateIsCheckedValue(applySetupContract.getWhether_trip_apply_budget());
        validateIsCheckedValue(applySetupContract.getApply_reason_chailv());
        validateIsCheckedValue(applySetupContract.getApply_reason_taxi());
        validateIsCheckedValue(applySetupContract.getApply_reason_desc());
        validateIsCheckedValue(applySetupContract.getApply_reason_coupon());
        validateIsCheckedValue(applySetupContract.getApply_reason_dinner());
        validateIsCheckedValue(applySetupContract.getApply_reason_takeaway());
        validateIsCheckedValue(applySetupContract.getApply_departure_date());
        validateIsCheckedValue(applySetupContract.getApply_pedestrians_control());
        List<MessageSetup> messageSetupList = new ArrayList<>();
        messageSetupList.add(new MessageSetup(companyId, SaasApplyConstant.ITEM_CODE_WHETHER_TRIP_APPLY_BUDGET, applySetupContract.getWhether_trip_apply_budget()));
        messageSetupList.add(new MessageSetup(companyId, SaasApplyConstant.ITEM_CODE_APPLY_REASON_CHAILV, applySetupContract.getApply_reason_chailv()));
        messageSetupList.add(new MessageSetup(companyId, SaasApplyConstant.ITEM_CODE_APPLY_REASON_TAXI, applySetupContract.getApply_reason_taxi()));
        messageSetupList.add(new MessageSetup(companyId, SaasApplyConstant.ITEM_CODE_APPLY_REASON_DESC, applySetupContract.getApply_reason_desc()));
        messageSetupList.add(new MessageSetup(companyId, SaasApplyConstant.ITEM_CODE_APPLY_REASON_CHAILV_CHANGE, applySetupContract.getApply_reason_chailv_change()));
        messageSetupList.add(new MessageSetup(companyId, SaasApplyConstant.ITEM_CODE_APPLY_REASON_COUPON, applySetupContract.getApply_reason_coupon()));
        messageSetupList.add(new MessageSetup(companyId, SaasApplyConstant.ITEM_CODE_APPLY_REASON_DINNER, applySetupContract.getApply_reason_dinner()));
        messageSetupList.add(new MessageSetup(companyId, SaasApplyConstant.ITEM_CODE_APPLY_REASON_TAKEAWAY, applySetupContract.getApply_reason_takeaway()));
        messageSetupList.add(new MessageSetup(companyId, SaasApplyConstant.ITEM_CODE_APPLY_REASON_BANK_INDIVIDUAL, applySetupContract.getApply_reason_bank_individual()));
        messageSetupList.add(new MessageSetup(companyId, SaasApplyConstant.ITEM_CODE_APPLY_DEPARTURE_DATE, applySetupContract.getApply_departure_date()));
        messageSetupList.add(new MessageSetup(companyId, SaasApplyConstant.ITEM_CODE_APPLY_PEDESTRIANS_CONTROL, applySetupContract.getApply_pedestrians_control()));
        saveCompanyMessageSetup(messageSetupList, companyId);
    }

    /**
     * 申请单配置查询
     *
     * @param companyId
     * @return
     */
    @Override
    public ApplySetupContract queryCompanyApplyConfig(String companyId) {
        return queryCompanyApplyConfig(companyId, null);
    }

    /**
     * 申请单配置查询
     *
     * @param companyId
     * @param clientVersion
     * @return
     */
    @Override
    public ApplySetupContract queryCompanyApplyConfig(String companyId, String clientVersion) {
        if (StringUtils.isEmpty(companyId)) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
        ApplySetupContract applySetupContract = new ApplySetupContract();
        // 查询公司消息设置
        List<MessageSetup> messageSetupList = queryCompanyMessageSetupWithDefault(companyId, Lists.newArrayList(SaasApplyConstant.BUSI_CODE_APPLY_SETTING_UP));
        if (CollectionUtils.isEmpty(messageSetupList)) {
            return applySetupContract;
        }
        // 发票选项集合
        List<Integer> invoiceOptionList = Lists.newArrayList();
        for (MessageSetup messageSetup : messageSetupList) {
            if (messageSetup != null && messageSetup.getIsChecked() != null) {
                String itemCode = messageSetup.getItemCode();
                Integer isChecked = messageSetup.getIsChecked();
                Integer intVal1 = messageSetup.getIntVal1();
                String strVal1 = messageSetup.getStrVal1();
                if (SaasApplyConstant.ITEM_CODE_APPLY_TRIP_TYPE.equals(itemCode)) {
                    applySetupContract.setApply_trip_type(isChecked);
                }
                if (SaasApplyConstant.ITEM_CODE_APPLY_TRIP_CITY.equals(itemCode)) {
                    applySetupContract.setApply_trip_city(intVal1);
                }
                if (SaasApplyConstant.ITEM_CODE_WHETHER_TRIP_APPLY_BUDGET.equals(itemCode)) {
                    applySetupContract.setWhether_trip_apply_budget(isChecked);
                }
                if (SaasApplyConstant.ITEM_CODE_APPLY_DEPARTURE_DATE.equals(itemCode)) {
                    applySetupContract.setApply_departure_date(isChecked);
                }
                if (SaasApplyConstant.ITEM_CODE_APPLY_PEDESTRIANS_CONTROL.equals(itemCode)) {
                    applySetupContract.setApply_pedestrians_control(isChecked);
                }
                //if (ObjUtils.isBlank(clientVersion) || VersionTool.lessThan(clientVersion, "3.8.0")) {
                if (SaasApplyConstant.ITEM_CODE_APPLY_REASON_CHAILV.equals(itemCode)) {
                    applySetupContract.setApply_reason_chailv(isChecked);
                }
                if (SaasApplyConstant.ITEM_CODE_APPLY_REASON_TAXI.equals(itemCode)) {
                    applySetupContract.setApply_reason_taxi(isChecked);
                }
                if (SaasApplyConstant.ITEM_CODE_APPLY_REASON_CHAILV_CHANGE.equals(itemCode)) {
                    applySetupContract.setApply_reason_chailv_change(isChecked);
                }
                if (SaasApplyConstant.ITEM_CODE_APPLY_REASON_DINNER.equals(itemCode)) {
                    applySetupContract.setApply_reason_dinner(isChecked);
                }
                if (SaasApplyConstant.ITEM_CODE_APPLY_REASON_COUPON.equals(itemCode)) {
                    applySetupContract.setApply_reason_coupon(isChecked);
                }
                if (SaasApplyConstant.ITEM_CODE_APPLY_REASON_TAKEAWAY.equals(itemCode)) {
                    applySetupContract.setApply_reason_takeaway(isChecked);
                }
                if (SaasApplyConstant.ITEM_CODE_APPLY_REASON_BANK_INDIVIDUAL.equals(itemCode)) {
                    applySetupContract.setApply_reason_bank_individual(isChecked);
                }
                if (SaasApplyConstant.ITEM_CODE_APPLY_COUNT_LIMIT.equals(itemCode)) {
                    applySetupContract.setApply_count_limit(isChecked);
                }
                if (SaasApplyConstant.ITEM_CODE_APPLY_ATTRIBUTION_CATEGORY_TRAVEL.equals(itemCode)) {
                    applySetupContract.setApply_attribution_category_travel(isChecked);
                    applySetupContract.setApply_attribution_category_modifiable_travel(intVal1);
                    applySetupContract.setApply_attribution_category_travel_desc("");
                    if (SaasReasonConstant.IS_CHECKED_TRUE == intVal1) {
                        applySetupContract.setApply_attribution_category_travel_desc(CoreLanguage.Order_Value_DefaultLimitUpdate.getMessage());
                    }
                }
                if (SaasApplyConstant.ITEM_CODE_APPLY_ATTRIBUTION_CATEGORY_TAXI.equals(itemCode)) {
                    applySetupContract.setApply_attribution_category_taxi(isChecked);
                    applySetupContract.setApply_attribution_category_modifiable_taxi(intVal1);
                    applySetupContract.setApply_attribution_category_taxi_desc("");
                    if (SaasReasonConstant.IS_CHECKED_TRUE == intVal1) {
                        applySetupContract.setApply_attribution_category_taxi_desc(CoreLanguage.Order_Value_DefaultLimitUpdate.getMessage());
                    }
                }
                if (SaasApplyConstant.ITEM_CODE_APPLY_ATTRIBUTION_CATEGORY_MEISHI.equals(itemCode)) {
                    applySetupContract.setApply_attribution_category_meishi(isChecked);
                    applySetupContract.setApply_attribution_category_modifiable_meishi(intVal1);
                    applySetupContract.setApply_attribution_category_meishi_desc("");
                    if (SaasReasonConstant.IS_CHECKED_TRUE == intVal1) {
                        applySetupContract.setApply_attribution_category_meishi_desc(CoreLanguage.Order_Value_DefaultLimitUpdate.getMessage());
                    }
                }

                /** 美食用餐 */
                if (SaasApplyConstant.ITEM_CODE_APPLY_MEISHI_TIME_LIMIT.equals(itemCode)) {
                    applySetupContract.setApply_meishi_time_limit(isChecked);
                }
                if (SaasApplyConstant.ITEM_CODE_APPLY_MEISHI_COUNT_LIMIT.equals(itemCode)) {
                    applySetupContract.setApply_meishi_count_limit(isChecked);
                }
                if (SaasApplyConstant.ITEM_CODE_APPLY_MEISHI_CITY_LIMIT.equals(itemCode)) {
                    applySetupContract.setApply_meishi_city_limit(isChecked);
                }
                if (SaasApplyConstant.ITEM_CODE_APPLY_MEISHI_FEE_RULE.equals(itemCode)) {
                    applySetupContract.setApply_meishi_fee_rule(isChecked);
                }
                if (SaasApplyConstant.ITEM_CODE_APPLY_MEISHI_TIME_SLOT_LIMIT.equals(itemCode)) {
                    applySetupContract.setApply_meishi_time_slot_limit(isChecked);
                }
                /** 外卖 */
                if (SaasApplyConstant.ITEM_CODE_APPLY_TAKEAWAY_FEE_RULE.equals(itemCode)) {
                    applySetupContract.setApply_takeaway_fee_rule(isChecked);
                }
                if (SaasApplyConstant.ITEM_CODE_APPLY_TAKEAWAY_TIME_SLOT_LIMIT.equals(itemCode)) {
                    applySetupContract.setApply_takeaway_time_slot_limit(isChecked);
                }

                if (SaasApplyConstant.ITEM_CODE_APPLY_ATTRIBUTION_CATEGORY_TAKEAWAY.equals(itemCode)) {
                    applySetupContract.setApply_attribution_category_takeaway(isChecked);
                    applySetupContract.setApply_attribution_category_modifiable_takeaway(intVal1);
                    applySetupContract.setApply_attribution_category_takeaway_desc("");
                    if (SaasReasonConstant.IS_CHECKED_TRUE == intVal1) {
                        applySetupContract.setApply_attribution_category_takeaway_desc(CoreLanguage.Order_Value_DefaultLimitUpdate.getMessage());
                    }
                }
                String descItemCode = MessageFormat.format(SaasReasonConstant.ITEM_CODE_REASON_DESC_COMMON_FORMAT, SaasApplyConstant.ITEM_CODE_APPLY_REASON_CHAILV);
                if (descItemCode.equals(itemCode)) {
                    applySetupContract.setApply_reason_desc(isChecked);
                }
                //}
                if (SaasApplyConstant.ITEM_CODE_TRIP_APPLY_BUDGET_CHECK.equals(itemCode)) {
                    applySetupContract.setTrip_apply_budget_check(isChecked);
                    applySetupContract.setTrip_apply_budget_check_desc("");
                    applySetupContract.setBudget_check_air_desc("");
                    applySetupContract.setBudget_check_hotel_desc("");
                    applySetupContract.setBudget_check_train_desc("");
                    applySetupContract.setBudget_check_multi_trip_desc("");
                    if (isChecked == SaasReasonConstant.IS_CHECKED_TRUE) {
                        applySetupContract.setTrip_apply_budget_check_desc(CoreLanguage.Order_Value_SubmitOverLimit.getMessage());
                        if (StringUtils.isNotBlank(strVal1)) {
                            if (strVal1.contains("7")) {
                                applySetupContract.setBudget_check_air_desc(CoreLanguage.Order_Value_SubmitOverLimit.getMessage());
                            }
                            if (strVal1.contains("11")) {
                                applySetupContract.setBudget_check_hotel_desc(CoreLanguage.Order_Value_SubmitOverLimit.getMessage());
                            }
                            if (strVal1.contains("15")) {
                                applySetupContract.setBudget_check_train_desc(CoreLanguage.Order_Value_SubmitOverLimit.getMessage());
                            }
                        }
                        if (applySetupContract.getApply_trip_type().equals(2)) {
                            applySetupContract.setBudget_check_multi_trip_desc(CoreLanguage.Order_Value_PredictIsQuotaUpper.getMessage());
                        }
                    }
                }
                if (SaasApplyConstant.ITEM_CODE_APPLY_PAYMENT_CONTRACT.equals(itemCode)) {
                    applySetupContract.setApply_payment_contract(isChecked);
                }
                if (SaasApplyConstant.ITEM_CODE_APPLY_PAYMENT_PROOF.equals(itemCode)) {
                    applySetupContract.setApply_payment_proof(isChecked);
                }
                if (SaasApplyConstant.ITEM_CODE_APPLY_BANK_INDIVIDUAL_ATTACHMENT.equals(itemCode)) {
                    applySetupContract.setApply_bank_individual_attachment(isChecked);
                }
                if (SaasApplyConstant.ITEM_CODE_APPLY_BANK_INDIVIDUAL_COST_CATEGORY.equals(itemCode)) {
                    applySetupContract.setApply_bank_individual_cost_category(isChecked);
                }

                // 发票选项 1-已开发票 0-待开发票 2-无发票
                if (SaasApplyConstant.ITEM_CODE_APPLY_PAYMENT_HAS_INVOICE.equals(itemCode)) {
                    if (isChecked == SaasReasonConstant.IS_CHECKED_TRUE) {
                        invoiceOptionList.add(1);
                    }
                }
                if (SaasApplyConstant.ITEM_CODE_APPLY_PAYMENT_NEED_INVOICE.equals(itemCode)) {
                    if (isChecked == SaasReasonConstant.IS_CHECKED_TRUE) {
                        invoiceOptionList.add(0);
                    }
                }
                if (SaasApplyConstant.ITEM_CODE_APPLY_PAYMENT_NO_INVOICE.equals(itemCode)) {
                    if (isChecked == SaasReasonConstant.IS_CHECKED_TRUE) {
                        invoiceOptionList.add(2);
                    }
                }
                // 配置为必须有一个，这里一定会有值
                applySetupContract.setApplyInvoiceOptionList(invoiceOptionList);
                // 差旅行程申请单形式
                if (SaasApplyConstant.ITEM_CODE_APPLY_TRIP_TYPE.equals(itemCode)) {
                    applySetupContract.setApply_trip_type(isChecked);
                }

                if (SaasApplyConstant.ITEM_CODE_APPLY_ORDER_TYPES.equals(itemCode)) {
                    List<Integer> types = Lists.newArrayList();
                    if (ObjUtils.isNotBlank(strVal1)){
                        types = JsonUtils.toObj(strVal1,List.class);
                    }
                    applySetupContract.setApply_order_types(types);
                }
                if (SaasApplyConstant.APPLY_ATTRIBUTION_CATEGORY_MILEAGE.equals(itemCode)) {
                    applySetupContract.setApply_attribution_category_mileage(isChecked);
                }
                if (SaasApplyConstant.COST_CATEGORY.equals(itemCode)) {
                    applySetupContract.setCost_category_mileage(isChecked);
                }
            }
        }

        invoiceOptionSort(applySetupContract, invoiceOptionList);

        logger.info("配置详情信息：applySetupContract={}",JsonUtils.toJson(applySetupContract));
        return applySetupContract;
    }

    private void invoiceOptionSort(ApplySetupContract applySetupContract, List<Integer> invoiceOptionList) {
        //发票顺序1，0，2
        if(CollectionUtils.isNotEmpty(invoiceOptionList)){
            logger.info("发票配置排序前信息：invoiceOptionList={}",JsonUtils.toJson(invoiceOptionList));
            int[] invoiceSort= {1,0,2};
            List<Integer> invoiceSortOptionList = Lists.newArrayList();
            for(int invoice: invoiceSort){
                boolean contains = invoiceOptionList.contains(invoice);
                if (contains){
                    invoiceSortOptionList.add(invoice);
                }
            }
            logger.info("发票配置排序后信息：invoiceSortOptionList={}",JsonUtils.toJson(invoiceSortOptionList));

            applySetupContract.setApplyInvoiceOptionList(invoiceSortOptionList);
        }
    }

    /**
     * 差旅行程统计
     *
     * @param companyId
     * @param applySetupContract
     */
    @Override
    public void saveCompanyApplyTravelConfig(String companyId, ApplySetupContract applySetupContract) {
        if (StringUtils.isEmpty(companyId)) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
        if (applySetupContract == null) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        validateIsCheckedValue(applySetupContract.getWhether_travel_statistics());
        MessageSetup setup = new MessageSetup(companyId, SaasApplyConstant.ITEM_CODE_APPLY_TRAVEL_STATISTICS, applySetupContract.getWhether_travel_statistics(), applySetupContract.getWhether_required());
        MessageSetupExample exampleQuery = new MessageSetupExample();
        exampleQuery.createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andItemCodeEqualTo(SaasApplyConstant.ITEM_CODE_APPLY_TRAVEL_STATISTICS);
        List<MessageSetup> oldMessageSetupList = messageSetupMapper.selectByExample(exampleQuery);
        if (CollectionUtils.isEmpty(oldMessageSetupList)) {
            messageSetupMapper.insertSelective(setup);
        } else {
            MessageSetupExample exampleUpdate = new MessageSetupExample();
            exampleUpdate.createCriteria()
                    .andCompanyIdEqualTo(companyId)
                    .andItemCodeEqualTo(setup.getItemCode());
            MessageSetup updateMessageSetup = new MessageSetup();
            updateMessageSetup.setIsChecked(setup.getIsChecked());
            updateMessageSetup.setIntVal1(setup.getIntVal1());
            updateMessageSetup.setIntVal2(setup.getIntVal2());
            updateMessageSetup.setIntVal3(setup.getIntVal3());
            updateMessageSetup.setStrVal1(setup.getStrVal1());
            updateMessageSetup.setStrVal2(setup.getStrVal2());
            updateMessageSetup.setStrVal3(setup.getStrVal3());
            messageSetupMapper.updateByExampleSelective(updateMessageSetup, exampleUpdate);
        }
    }

    @Override
    public ApplySetupContract queryCompanyApplyTravelConfig(String companyId) {
        if (StringUtils.isEmpty(companyId)) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
        ApplySetupContract applySetupContract = new ApplySetupContract();
        applySetupContract.setWhether_travel_statistics(SaasMessageConstant.IS_CHECKED_FALSE);
        applySetupContract.setWhether_required(SaasMessageConstant.IS_CHECKED_FALSE);
        MessageSetup messageSetup = queryCompanyMessageSetupWithDefault(companyId, SaasApplyConstant.ITEM_CODE_APPLY_TRAVEL_STATISTICS);
        if (messageSetup != null && messageSetup.getIsChecked() != null) {
            applySetupContract.setWhether_travel_statistics(messageSetup.getIsChecked());
        }
        if (messageSetup != null && messageSetup.getIntVal1() != null) {
            applySetupContract.setWhether_required(messageSetup.getIntVal1());
        }
        return applySetupContract;
    }

    /**
     * 校验isChecked值
     *
     * @param isChecked
     */
    private void validateIsCheckedValue(Integer isChecked) {
        if (isChecked == null) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        if (isChecked != SaasMessageConstant.IS_CHECKED_TRUE && isChecked != SaasMessageConstant.IS_CHECKED_FALSE) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
    }

    /**
     * 校验费用归属isChecked值
     *
     * @param isChecked
     */
    private void validCostAttributionCategoryIsCheckedValue(Integer isChecked) {
        if (isChecked == null) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        if (isChecked != 1 && isChecked != 2 && isChecked != 3 && isChecked != 4) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
    }

    /**
     * 校验审批所在部门isChecked值
     *
     * @param isChecked
     */
    private void validApplyCostAttributionCategoryIsCheckedValue(Integer isChecked) {
        if (isChecked == null) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        if (isChecked != 1 && isChecked != 2 && isChecked != 3) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
    }

    private void validApplyAttributionCategoryFlag(Integer applyAttributionCategoryFlag){
        if (applyAttributionCategoryFlag == null) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        if (applyAttributionCategoryFlag != 0 && applyAttributionCategoryFlag != 1 ) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
    }

    /**
     * 查询报表配置数据sql
     *
     * @return
     */
    @Override
    public List<String> initStatementConfiguration() {
        List<String> commandList = new ArrayList<>();
        MessageSetupReceiverExample messageSetupReceiverExample = new MessageSetupReceiverExample();
        messageSetupReceiverExample.createCriteria().andBusiCodeEqualTo(SaasDashboardConstant.BUSI_CODE_DASHBOARD_INFO);
        List<MessageSetupReceiver> messageSetupReceiverList = messageSetupReceiverMapper.selectByExample(messageSetupReceiverExample);
        if (ObjUtils.isNotEmpty(messageSetupReceiverList)) {
            String dateFormateDate = DateUtils.format(new Date());
            String roleId = "5bcf0b2723445f71695d10a9";
            for (MessageSetupReceiver messageSetupReceiver : messageSetupReceiverList) {
                commandList.add(" insert into employee_customrole(employee_id,customrole_id,company_id,create_time) values('" + messageSetupReceiver.getUserId() + "','" + roleId + "','" + messageSetupReceiver.getCompanyId() + "','" + dateFormateDate + "');");
            }
        }
        return commandList;
    }

    @Override
    public void saveCompanyCostAttributionConfig(String companyId, ApplySetupContract applySetupContract) {
        if (StringUtils.isEmpty(companyId)) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
        if (applySetupContract == null) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        // 校验isChecked值
        validCostAttributionCategoryIsCheckedValue(applySetupContract.getCost_attribution_category());
        List<MessageSetup> messageSetupList = new ArrayList<>();
        messageSetupList.add(new MessageSetup(companyId, SaasApplyConstant.ITEM_CODE_COST_ATTRIBUTION_CATEGORY, applySetupContract.getCost_attribution_category(), applySetupContract.getCost_attribution_deptment_default(), applySetupContract.getCost_attribution_reset()));
        saveCompanyMessageSetup(messageSetupList, companyId);
        companyLogKafkaProducerService.sendCompanyLogMsg(getKafkaCompanyLogMsg(LogOperatePageEnum.Cost_Attribution, LogOperateActionEnum.MODIFY, LogOperateObjectEnum.COST_ATTRIBUTION));
    }

    @Override
    public ApplySetupContract queryCompanyCostAttributionConfig(String companyId) {
        if (StringUtils.isEmpty(companyId)) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
        ApplySetupContract applySetupContract = new ApplySetupContract();
        // 查询公司消息设置
        List<MessageSetup> messageSetupList = queryCompanyMessageSetupWithDefault(companyId, Lists.newArrayList(SaasApplyConstant.BUSI_CODE_COST_ATTRIBUTION_SETTING_UP));
        if (CollectionUtils.isEmpty(messageSetupList)) {
            return applySetupContract;
        }
        for (MessageSetup messageSetup : messageSetupList) {
            if (messageSetup != null && messageSetup.getIsChecked() != null) {
                String itemCode = messageSetup.getItemCode();
                Integer isChecked = messageSetup.getIsChecked();
                if (SaasApplyConstant.ITEM_CODE_COST_ATTRIBUTION_CATEGORY.equals(itemCode)) {
                    applySetupContract.setCost_attribution_category(isChecked);
                    applySetupContract.setCost_attribution_deptment_default(messageSetup.getIntVal1());
                    applySetupContract.setCost_attribution_reset(messageSetup.getIntVal2());
                }
            }
        }
        return applySetupContract;
    }

    @Override
    public void reset(String companyId) {
        //根据公司id、item_code查询是否配置过字段配置
        List<String> itemCode = Lists.newArrayList();
        itemCode.add(SaasMessageConstant.ITEM_CODE_CONSUME_INFO_NOTICE);
        itemCode.add(SaasMessageConstant.ITEM_CODE_LARGE_OVER_NOTICE);
        itemCode.add(SaasMessageConstant.ITEM_CODE_BALANCE_REMIND_NOTICE);
        //删除自定义字段配置
        MessageSetupExample example = new MessageSetupExample();
        example.createCriteria().andCompanyIdEqualTo(companyId).andItemCodeIn(itemCode);
        MessageSetup messageSetup = new MessageSetup();
        messageSetup.setIsChecked(0);
        messageSetupMapper.updateByExampleSelective(messageSetup, example);
    }

    @Override
    public void resetCompanyCostAttributionConfig(String companyId) {
        if (StringUtils.isEmpty(companyId)) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
        List<MessageSetup> messageSetupList = new ArrayList<>();
        messageSetupList.add(new MessageSetup(companyId, SaasApplyConstant.ITEM_CODE_COST_ATTRIBUTION_CATEGORY, 1));
        saveCompanyMessageSetup(messageSetupList, companyId);
    }

    @Override
    public List<MessageSetupCommonContrate> queryCommonSetting(MessageSetupCommonContrate messageSetupCommonContrates) {
        if (messageSetupCommonContrates != null && CollectionUtils.isEmpty(messageSetupCommonContrates.getCompany_ids()) && StringUtils.isNotBlank(messageSetupCommonContrates.getItem_code())) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
        List<MessageSetupCommonContrate> messageSetupCommonContrateList = Lists.newArrayList();
        Map<String, Object> paramMap = new HashedMap();
        paramMap.put("companyIdList", messageSetupCommonContrates.getCompany_ids());
        paramMap.put("itemCode", messageSetupCommonContrates.getItem_code());
        List<MessageSetup> messageSetupList = messageSetupExtMapper.queryMessageSetupListWithDefault(paramMap);
        if (CollectionUtils.isEmpty(messageSetupList)) {
            return null;
        }
        for (String companyId : messageSetupCommonContrates.getCompany_ids()) {
            MessageSetupCommonContrate messageSetupCommonContrate = new MessageSetupCommonContrate();
            messageSetupCommonContrate.setIs_checked(0);
            messageSetupCommonContrate.setCompany_id(companyId);
            for (MessageSetup messageSetup : messageSetupList) {
                if (companyId.equals(messageSetup.getCompanyId())) {
                    messageSetupCommonContrate.setIs_checked(messageSetup.getIsChecked());
                    break;
                }
            }
            messageSetupCommonContrateList.add(messageSetupCommonContrate);
        }
        return messageSetupCommonContrateList;
    }

    /**
     * 通用设置保存
     *
     * @param userId
     * @param applySetupContract
     */
    @Override
    public void saveUserApplyConfig(String userId, String companyId, ApplySetupContract applySetupContract) {
        if (StringUtils.isBlank(userId) || StringUtils.isBlank(companyId)) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
        if (applySetupContract == null) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        MessageSetup setup = new MessageSetup(companyId, applySetupContract.getItem_code(), applySetupContract.getItem_value(), userId);
        MessageSetupExample exampleQuery = new MessageSetupExample();
        exampleQuery.createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andItemCodeEqualTo(applySetupContract.getItem_code())
                .andUserIdEqualTo(userId);
        List<MessageSetup> oldMessageSetupList = messageSetupMapper.selectByExample(exampleQuery);
        if (CollectionUtils.isEmpty(oldMessageSetupList)) {
            messageSetupMapper.insertSelective(setup);
        } else {
            MessageSetupExample exampleUpdate = new MessageSetupExample();
            exampleUpdate.createCriteria()
                    .andCompanyIdEqualTo(companyId)
                    .andItemCodeEqualTo(setup.getItemCode())
                    .andUserIdEqualTo(userId);
            MessageSetup updateMessageSetup = new MessageSetup();
            updateMessageSetup.setIsChecked(setup.getIsChecked());
            updateMessageSetup.setIntVal1(setup.getIntVal1());
            updateMessageSetup.setIntVal2(setup.getIntVal2());
            updateMessageSetup.setIntVal3(setup.getIntVal3());
            updateMessageSetup.setStrVal1(setup.getStrVal1());
            updateMessageSetup.setStrVal2(setup.getStrVal2());
            updateMessageSetup.setStrVal3(setup.getStrVal3());
            messageSetupMapper.updateByExampleSelective(updateMessageSetup, exampleUpdate);
        }
    }

    /**
     * 通用设置查询
     *
     * @param userId
     */
    @Override
    public MessageSetupUserCommonContract queryUserApplyConfig(String userId, String companyId) {
        if (StringUtils.isEmpty(userId)) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
        MessageSetupUserCommonContract contract = new MessageSetupUserCommonContract();
        CommonNoticeContract noticeContract = new CommonNoticeContract();
        noticeContract.setTitle(SaasMessageConstant.ORDER_RELATION_NOTICE_TITLE);
        CommonNoticeContract.Tip tip = noticeContract.new Tip(SaasMessageConstant.ORDER_RELATION_NOTICE_TIP_ICON, SaasMessageConstant.ORDER_RELATION_NOTICE_TIP_CONTENT);
        noticeContract.setCommonJson(Lists.newArrayList(noticeContract.new QandA(SaasMessageConstant.ORDER_RELATION_NOTICE_CONTENT, tip)));
        contract.setNotice(noticeContract);
        contract.setApply_message_setting(SaasMessageConstant.IS_CHECKED_FALSE);
        MessageSetup messageSetup = queryCompanyUserMessageSetupWithDefault(companyId, SaasApplyConstant.ITEM_CODE_APPLY_MESSAGE_SETTING, userId);
        if (messageSetup != null && messageSetup.getIsChecked() != null) {
            contract.setApply_message_setting(messageSetup.getIsChecked());
        }
        MessageSetup customerMessageSetup = queryCompanyUserMessageSetupWithDefault(companyId, SaasApplyConstant.ITEM_CODE_CUSTOMER_SERVICE_NOTICE, userId);
        if (customerMessageSetup != null && customerMessageSetup.getIsChecked() != null) {
            contract.setCustomer_service_notice(customerMessageSetup.getIsChecked());
        }
        return contract;
    }

    /**
     * 获取用户在企业单个项消息设置，没有设置的取默认值
     *
     * @param companyId
     * @param itemCode
     * @return
     */
    public MessageSetup queryCompanyUserMessageSetupWithDefault(String companyId, String itemCode, String userId) {
        if (StringUtils.isEmpty(companyId)
                || StringUtils.isEmpty(itemCode)) {
            return null;
        }
        Map<String, Object> paramMap = new HashedMap();
        paramMap.put("companyId", companyId);
        paramMap.put("itemCode", itemCode);
        paramMap.put("userIdList", Lists.newArrayList(userId));
        List<MessageSetup> messageSetupList = messageSetupExtMapper.getUserMessageSetupListWithDefault(paramMap);
        if (CollectionUtils.isEmpty(messageSetupList)) {
            return null;
        }
        return messageSetupList.get(0);
    }

    @Override
    public void saveCompanyApplyCostAttributionConfig(String companyId, ApplySetupContract applySetupContract) {
        if (StringUtils.isEmpty(companyId)) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
        if (applySetupContract == null) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        // 校验isChecked值
        validApplyCostAttributionCategoryIsCheckedValue(applySetupContract.getCost_attribution_category());
        //校验intval2 是否是 0未勾选,1勾选
        validApplyAttributionCategoryFlag(applySetupContract.getApply_attribution_category_flag());
        validApplyAttributionCategoryFlag(applySetupContract.getApply_show_project_code());
        List<MessageSetup> messageSetupList = new ArrayList<>();
        messageSetupList.add(new MessageSetup(companyId, SaasApplyConstant.ITEM_CODE_APPLY_COST_ATTRIBUTION_CATEGORY, applySetupContract.getCost_attribution_category(), applySetupContract.getCost_attribution_deptment_default(),applySetupContract.getApply_attribution_category_flag()));
        messageSetupList.add(new MessageSetup(companyId, SaasApplyConstant.ITEM_CODE_APPLY_SHOW_PROJECT_CODE, applySetupContract.getApply_show_project_code()));
        messageSetupList.add(new MessageSetup(companyId, SaasApplyConstant.IS_SHOW_APPLY_THIRD_ID, applySetupContract.getIs_show_third_id()));

        saveCompanyMessageSetup(messageSetupList, companyId);
        companyLogKafkaProducerService.sendCompanyLogMsg(getKafkaCompanyLogMsg(LogOperatePageEnum.Appliy_form, LogOperateActionEnum.MODIFY, LogOperateObjectEnum.APPLY_ATTRIBUTION));
        //如果是勾选1情况下,需要把用餐/用车/差旅/外卖,申请单中的费用归属修改为(必填&&申请单费用归属代入订单且不可更改)
        if(applySetupContract.getApply_attribution_category_flag()==1){
            updateApplyAttributionCategoryModifiable(companyId);
        }
    }

    /**
     * 根据企业id修改:用餐,用车,差旅,外卖,中的费用归属(必填&&申请单费用归属代入订单且不可更改)
     * @param companyId
     */
    private void updateApplyAttributionCategoryModifiable(String companyId){
        List<MessageSetup> messageSetupList = new ArrayList<>();
        //行程申请单费用归属
        messageSetupList.add(new MessageSetup(companyId, SaasApplyConstant.ITEM_CODE_APPLY_ATTRIBUTION_CATEGORY_TRAVEL, APPLY_ATTRIBUTION_CATEGORY, APPLY_ATTRIBUTION_CATEGORY_MODIFIABLEble));
        // 用车申请单费用归属
        messageSetupList.add(new MessageSetup(companyId, SaasApplyConstant.ITEM_CODE_APPLY_ATTRIBUTION_CATEGORY_TAXI, APPLY_ATTRIBUTION_CATEGORY, APPLY_ATTRIBUTION_CATEGORY_MODIFIABLEble));
        // 用餐申请单费用归属
        messageSetupList.add(new MessageSetup(companyId, SaasApplyConstant.ITEM_CODE_APPLY_ATTRIBUTION_CATEGORY_MEISHI, APPLY_ATTRIBUTION_CATEGORY, APPLY_ATTRIBUTION_CATEGORY_MODIFIABLEble));
        // 外卖申请单费用归属
        messageSetupList.add(new MessageSetup(companyId, SaasApplyConstant.ITEM_CODE_APPLY_ATTRIBUTION_CATEGORY_TAKEAWAY, APPLY_ATTRIBUTION_CATEGORY, APPLY_ATTRIBUTION_CATEGORY_MODIFIABLEble));
        saveCompanyMessageSetup(messageSetupList, companyId);
    }

    @Override
    public ApplySetupContract queryCompanyApplyCostAttributionConfig(String companyId) {
        if (StringUtils.isEmpty(companyId)) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
        ApplySetupContract applySetupContract = new ApplySetupContract();
        // 查询公司消息设置
        List<String> itemCodeList = Lists.newArrayList();
        itemCodeList.add(SaasApplyConstant.ITEM_CODE_APPLY_COST_ATTRIBUTION_CATEGORY);
        itemCodeList.add(SaasApplyConstant.ITEM_CODE_APPLY_SHOW_PROJECT_CODE);
        itemCodeList.add(SaasApplyConstant.ITEM_CODE_ALLOW_APPLY_DOWNLOAD);
        itemCodeList.add(SaasApplyConstant.IS_SHOW_APPLY_THIRD_ID);

        // 全量查询配置改为只查需要返回的配置
        // List<MessageSetup> messageSetupList = queryCompanyMessageSetupWithDefault(companyId, Lists.newArrayList(SaasApplyConstant.BUSI_CODE_APPLY_SETTING_UP));
        List<MessageSetup> messageSetupList = queryCompanyMessageSetupWithDefaultByItemCodeList(companyId, itemCodeList);
        if (CollectionUtils.isEmpty(messageSetupList)) {
            return applySetupContract;
        }
        for (MessageSetup messageSetup : messageSetupList) {
            if (messageSetup != null && messageSetup.getIsChecked() != null) {
                String itemCode = messageSetup.getItemCode();
                Integer isChecked = messageSetup.getIsChecked();
                Integer intVal2 = messageSetup.getIntVal2();
                if (SaasApplyConstant.ITEM_CODE_APPLY_COST_ATTRIBUTION_CATEGORY.equals(itemCode)) {
                    applySetupContract.setCost_attribution_category(isChecked);
                    if(null==intVal2){
                        applySetupContract.setApply_attribution_category_flag(0);
                    }else {
                        applySetupContract.setApply_attribution_category_flag(intVal2);
                    }
                }
                if (SaasApplyConstant.ITEM_CODE_APPLY_SHOW_PROJECT_CODE.equals(itemCode)) {
                    applySetupContract.setApply_show_project_code(isChecked);
                }
                if (SaasApplyConstant.ITEM_CODE_ALLOW_APPLY_DOWNLOAD.equals(itemCode)) {
                    applySetupContract.setAllow_apply_download(isChecked);
                }
                if (SaasApplyConstant.IS_SHOW_APPLY_THIRD_ID.equals(itemCode)) {
                    applySetupContract.setIs_show_third_id(isChecked);
                }
            }
        }
        return applySetupContract;
    }

    @Override
    public Boolean getPhoneWhite(String phoneNum, String companyId) {
        // 查询公司消息设置
        List<String> busiCodeList = Lists.newArrayList();
        busiCodeList.add(SaasMessageConstant.BUSI_CODE_COMPANY_PHONE_NUM_SETTING);
        List<MessageSetup> setupList = queryCompanyMessageSetupWithDefault(companyId, busiCodeList);
        logger.info("手机号白名单设置：" + JsonUtils.toJson(setupList));
        if (CollectionUtils.isEmpty(setupList)) {
            return false;
        }
        MessageSetup messageSetup = setupList.get(0);
        if (!SaasMessageConstant.ITEM_CODE_PHONE_NUM.equals(messageSetup.getItemCode())) {
            return false;
        }
        Integer isChecked = messageSetup.getIsChecked();
        if (isChecked == null) {
            return false;
        }
        if (isChecked != null && isChecked == 0) {
            return true;
        }
        if (isChecked == 1) {
            List<MessageSetupPhoneNum> messageSetupPhoneNumList = queryMessagePhoneNumList(companyId, SaasMessageConstant.BUSI_CODE_COMPANY_PHONE_NUM_SETTING);
            if (CollectionUtils.isEmpty(messageSetupPhoneNumList)) {
                return false;
            }
            List<String> phoneNumList = messageSetupPhoneNumList.stream().map(setupPhoneNum -> setupPhoneNum.getPhoneNum()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(phoneNumList) && phoneNumList.contains(phoneNum)) {
                return true;
            } else {
                return false;
            }
        }
        return false;
    }

    @Override
    public List<MessageSetupPhoneNum> queryMessagePhoneNumList(String companyId, String busiCode) {
        if (StringUtils.isEmpty(companyId)
                || StringUtils.isEmpty(busiCode)) {
            return null;
        }
        MessageSetupPhoneNumExample example = new MessageSetupPhoneNumExample();
        example.createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andBusiCodeEqualTo(busiCode);
        return messageSetupPhoneNumMapper.selectByExample(example);
    }

    @Override
    public ApplySetupContract queryCompanyApplySettingConfig(String companyId) {
        ApplySetupContract applySetupContract = new ApplySetupContract();
        applySetupContract.setApply_count_limit(SaasMessageConstant.IS_CHECKED_FALSE);
        MessageSetup messageSetup = queryCompanyMessageSetupWithDefault(companyId, SaasApplyConstant.ITEM_CODE_APPLY_COUNT_LIMIT);
        if (messageSetup != null && messageSetup.getIsChecked() != null) {
            applySetupContract.setApply_count_limit(messageSetup.getIsChecked());
        }
        return applySetupContract;
    }

    @Override
    public List<String> getDistinctCompanyIds4VirtualCardWriteOffRemind(){
        return messageSetupExtMapper.getDistinctCompanyIds4VirtualCardWriteOffRemind();
    }

    /**
     * 根据公司ID列表获取企业消息设置，没有设置的取默认值
     *
     * @param companyIdList
     * @param itemCodeList
     * @return
     */
    @Override
    public List<MessageSetup> listCompanyMessageSetupByCompanyIdList(List<String> companyIdList, List<String> itemCodeList) {
        if (CollectionUtils.isEmpty(companyIdList)
                || CollectionUtils.isEmpty(itemCodeList)) {
            return null;
        }
        Map<String, Object> paramMap = new HashedMap();
        paramMap.put("companyIdList", companyIdList);
        paramMap.put("itemCodeList", itemCodeList);
        return messageSetupExtMapper.listCompanyMessageSetupByCompanyIdList(paramMap);
    }

    /**
     * 费用归属及预算扣减配置查询
     *
     * @param companyId
     * @return
     */
    @Override
    public CostAttrAndBudgetConf queryCostAttrAndBudgetConf(String companyId) {
        if (StringUtils.isEmpty(companyId)) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
        CostAttrAndBudgetConf costAttrAndBudgetConf = new CostAttrAndBudgetConf();
        /*// 查询公司费用归属设置
        MessageSetup costAttrMessageSetup = queryCompanyMessageSetupWithDefault(companyId, SaasApplyConstant.ITEM_CODE_COST_ATTRIBUTION_CATEGORY);
        if (costAttrMessageSetup != null && costAttrMessageSetup.getIsChecked() != null) {
            costAttrAndBudgetConf.setCost_attribution_scope(costAttrMessageSetup.getIsChecked());
        }
        // 查询公司预算扣减设置
        MessageSetup budgetMessageSetup = queryCompanyMessageSetupWithDefault(companyId, SaasApplyConstant.ITEM_CODE_BUDGET_COST_ATTR_TYPE);
        if (budgetMessageSetup != null && budgetMessageSetup.getIntVal1() != null) {
            costAttrAndBudgetConf.setBudget_cost_attr_type(budgetMessageSetup.getIntVal1());
        }*/
        costAttrAndBudgetConf.setCost_attribution_scope(CostAttributionScopeEnum.DEPT_OR_PROJ.getCode());
        costAttrAndBudgetConf.setBudget_cost_attr_type(BudgetCostAttrTypeEnum.DEPT_AND_PROJ.getCode());
        CostAttributionSettingApiVO costAttributionSettingApiVO = iCostAttributionSettingService.queryCostAttribution(companyId);
        logger.info("queryCostAttrAndBudgetConf: costAttributionSettingApiVO={},companyId={}", JsonUtils.toJson(costAttributionSettingApiVO), companyId);
        costAttrAndBudgetConf.setCostAttributionSettingApiVO(costAttributionSettingApiVO);
        Integer costAttributionFirstCategory = costAttributionSettingApiVO.getCostAttributionFirstCategory();
        Integer costAttributionSecondCategory = costAttributionSettingApiVO.getCostAttributionSecondCategory();
        if (!orderCheckService.isNewBudgetCompany(companyId) && (costAttributionFirstCategory == 3 || (ObjUtils.isNotEmpty(costAttributionSecondCategory) && costAttributionSecondCategory == 3))) {
            throw new SaasException(GlobalResponseCode.CostAttributionSettingIsError);
        }
        //费用归属范围 1.必填 2.选填
        Integer costAttributionScope = costAttributionSettingApiVO.getCostAttributionScope();
        //优先扣减费用归属 0.全部占用 1.范围一 2.范围二
        Integer budgetCostAttrType = costAttributionSettingApiVO.getBudgetCostAttrType();
        if (ObjUtils.isEmpty(costAttributionSecondCategory)) {
            if (costAttributionFirstCategory == 1 || costAttributionFirstCategory == 2) {
                costAttrAndBudgetConf.setCost_attribution_scope(costAttributionFirstCategory);
                costAttrAndBudgetConf.setBudget_cost_attr_type(BudgetCostAttrTypeEnum.DEPT_AND_PROJ.getCode());
                return costAttrAndBudgetConf;
            }
        }
        //必填
        if (costAttributionScope == 1) {
            costAttrAndBudgetConf.setCost_attribution_scope(CostAttributionScopeEnum.DEPT_AND_PROJ.getCode());
            //0.全部占用
            if (budgetCostAttrType == 0) {
                costAttrAndBudgetConf.setBudget_cost_attr_type(BudgetCostAttrTypeEnum.DEPT_AND_PROJ.getCode());
                return costAttrAndBudgetConf;
                //1.范围一
            } else if (budgetCostAttrType == 1) {
                costAttrAndBudgetConf.setBudget_cost_attr_type(costAttributionFirstCategory);
                return costAttrAndBudgetConf;
                //2.范围二
            } else if (budgetCostAttrType == 2) {
                costAttrAndBudgetConf.setBudget_cost_attr_type(costAttributionSecondCategory);
                return costAttrAndBudgetConf;
            }
        }
        //选填
        if (costAttributionScope == 2) {
            costAttrAndBudgetConf.setCost_attribution_scope(CostAttributionScopeEnum.DEPT_OR_PROJ.getCode());
            costAttrAndBudgetConf.setBudget_cost_attr_type(BudgetCostAttrTypeEnum.DEPT_AND_PROJ.getCode());
            return costAttrAndBudgetConf;
        }
        return costAttrAndBudgetConf;
    }

//    /**
//     * 读取员工变更信息
//     *
//     * @param record
//     */
//    @KafkaListener(group = "${group.id:default}", topics = {"usercenter_employee_change"})
//    @Transactional(transactionManager = "saas", readOnly = false, rollbackFor = Exception.class)
//    public void employeeUpdateListener(ConsumerRecord<?, ?> record) {
//        logger.info("【消费员工变更信息】消费者线程：{},[消息 来自kafkatopic：{},分区：{}]消息内容如下：{}",
//                Thread.currentThread().getName(),
//                record.topic(),
//                record.partition(),
//                record.value());
//        KafkaEmployeeUpdateMsg kafkaEmployeeUpdateMsg = KafkaConsumerUtils.invokeIMessage(record.value().toString(), KafkaEmployeeUpdateMsg.class);
//        logger.info("【消费员工变更信息】{}", JsonUtils.toJson(kafkaEmployeeUpdateMsg));
//        if (kafkaEmployeeUpdateMsg != null
//                && kafkaEmployeeUpdateMsg.getOperateStatus().equals(OperateType.DELETE_OP.getCode())) {
//            // 删除员工消息接收配置
//            MessageSetupReceiverExample example = new MessageSetupReceiverExample();
//            example.createCriteria()
//                    .andCompanyIdEqualTo(kafkaEmployeeUpdateMsg.getCompanyId())
//                    .andBusiCodeEqualTo(kafkaEmployeeUpdateMsg.getEmployeeId());
//            messageSetupReceiverMapper.deleteByExample(example);
//        }
//
//        if (kafkaEmployeeUpdateMsg != null && kafkaEmployeeUpdateMsg.getOperateStatus() == com.fenbeitong.usercenter.api.model.enums.employee.OperateType.DELETE_OP.getKey()) {
//            // 失效授权状态
//            String employeeId = kafkaEmployeeUpdateMsg.getEmployeeId();
//            String companyId = kafkaEmployeeUpdateMsg.getCompanyId();
//            AuthorizationChangeEvent event = AuthorizationChangeEvent.EXPIRED_AUTHORIZATION_DUE_TO_STUFF_LEAVE;
//            SelfAuthorizationExample selfAuthorizationExample = new SelfAuthorizationExample();
//            selfAuthorizationExample.createCriteria().andAuthEmployeeIdEqualTo(employeeId).andCompanyIdEqualTo(companyId);
//            List<SelfAuthorization> selfAuthorizations = selfAuthorizationMapper.selectByExample(selfAuthorizationExample);
//            if (org.apache.commons.collections4.CollectionUtils.isEmpty(selfAuthorizations)) {
//                logger.info("selfAuthorizations is empty: employeeId={}, companyId={}", employeeId, companyId);
//                return;
//            }
//            SelfAuthorization selfAuthorization = selfAuthorizations.get(0);
//            // 发送自主授权失效push和短信
//            iSelfAuthorizeService.postMessage(event, selfAuthorization, null, null);
//        }
//        logger.info("【消费员工变更信息】,返回信息：{}", JsonUtils.toJson(kafkaEmployeeUpdateMsg));
//    }




    private KafkaCompanyLogMsg getKafkaCompanyLogMsg(LogOperatePageEnum logOperatePageEnum, LogOperateActionEnum logOperateActionEnum, LogOperateObjectEnum logOperateObjectEnum) {
        return CompanyLogMsgUtil.getKafkaCompanyLogMsg(logOperatePageEnum, logOperateActionEnum, logOperateObjectEnum);
    }

    /**
     * 查询审批流配置
     *
     * @param companyId
     * @return
     */
    @Override
    public int queryFlowableConfig(String companyId){
        String redisKey = MessageFormat.format(RedisKeyConstant.APPLY_FLOWABLE_REDIS,companyId);
        RedisTemplate redisTemplate = redisService.getRedisTemplate();
        Boolean flag = redisTemplate.hasKey(redisKey);
        if(flag){
            int obj =(int) redisTemplate.boundValueOps(redisKey).get();
            logger.info("-------redis新审批流配置查询结果:{},{}",redisKey, obj);
            return obj;
        }
        MessageSetup messageSetup = queryCompanyMessageSetupWithDefault(companyId, SaasApplyConstant.APPLY_FLOWABLE);
        if(null !=messageSetup && messageSetup.getIsChecked()== SaasMessageConstant.IS_CHECKED_TRUE){
            //放入redis
            redisTemplate.boundValueOps(redisKey).set(SaasMessageConstant.IS_CHECKED_TRUE);
            logger.info("-------新审批流放入redis结果:{},{}", redisKey,SaasMessageConstant.IS_CHECKED_TRUE);
            return SaasMessageConstant.IS_CHECKED_TRUE;
        }else {
            //放入redis
            redisTemplate.boundValueOps(redisKey).set(SaasMessageConstant.IS_CHECKED_FALSE);
            logger.info("-------新审批流放入redis结果:{},{}", redisKey,SaasMessageConstant.IS_CHECKED_FALSE);
            return SaasMessageConstant.IS_CHECKED_FALSE;
        }
    }

    /**
     * 查询分贝券审批流配置
     *
     * @param companyId
     * @return
     */
    @Override
    public int queryCouponFlowableConfig(String companyId) {
        //查询redis.
        String redisKey = MessageFormat.format(RedisKeyConstant.APPLY_COUPON_FLOWABLE_REDIS, companyId);
        RedisTemplate redisTemplate = redisService.getRedisTemplate();
        Boolean flag = redisTemplate.hasKey(redisKey);
        if (flag) {
            int obj = (int) redisTemplate.boundValueOps(redisKey).get();
            logger.info("-------redis分贝券新审批流配置查询结果:{},{}", redisKey, obj);
            return obj;
        }
        MessageSetup messageSetup = queryCompanyMessageSetupWithDefault(companyId, SaasApplyConstant.APPLY_COUPON_FLOWABLE);
        if (null != messageSetup && messageSetup.getIsChecked() == SaasMessageConstant.IS_CHECKED_TRUE) {
            //放入redis
            redisTemplate.boundValueOps(redisKey).set(SaasMessageConstant.IS_CHECKED_TRUE);
            logger.info("-------分贝券新审批流放入redis结果:{},{}", redisKey, SaasMessageConstant.IS_CHECKED_TRUE);
            return SaasMessageConstant.IS_CHECKED_TRUE;
        } else {
            //放入redis
            redisTemplate.boundValueOps(redisKey).set(SaasMessageConstant.IS_CHECKED_FALSE);
            logger.info("-------分贝券新审批流放入redis结果:{},{}", redisKey, SaasMessageConstant.IS_CHECKED_FALSE);
            return SaasMessageConstant.IS_CHECKED_FALSE;
        }
    }

    /**
     * 查询默认初始化审批流开关
     *
     * @return
     */
    @Override
    public int queryFlowableApplyInitSwitch() {
        String redisKey = RedisKeyConstant.FLOWABLE_APPLY_INIT_SWITCH_KEY;
        RedisTemplate redisTemplate = redisService.getRedisTemplate();
        //查询redis.
        if (redisTemplate.hasKey(redisKey)) {
            int obj = (int) redisTemplate.boundValueOps(redisKey).get();
            logger.info("-------redis查询默认初始化审批流开关配置查询结果:{},{}", redisKey, obj);
            return obj;
        }
        int flag = SaasMessageConstant.IS_CHECKED_TRUE;
        MessageSetupItemExample messageSetupItemExample = new MessageSetupItemExample();
        messageSetupItemExample.createCriteria().
                andBusiCodeEqualTo(com.fenbeitong.saasplus.api.model.enums.messageSetup.MessageType.BUSI_CODE_APPLY_SETTING.getCode())
                .andItemCodeEqualTo(SaasMessageConstant.ITEM_CODE_APPLY_FLOWABLE_INIT);
        List<MessageSetupItem> messageSetupItemList = messageSetupItemMapper.selectByExample(messageSetupItemExample);
        if (ObjUtils.isNotEmpty(messageSetupItemList) && messageSetupItemList.size() == 1) {
            MessageSetupItem messageSetupItem = messageSetupItemList.get(0);
            if (null != messageSetupItem && messageSetupItem.getDefaultChecked() == SaasMessageConstant.IS_CHECKED_FALSE) {
                flag = SaasMessageConstant.IS_CHECKED_FALSE;
            }
        }
        //放入redis
        redisTemplate.boundValueOps(redisKey).set(flag);
        logger.info("-------默认初始化审批流开关配置放入redis结果:{},{}", redisKey, flag);
        return flag;
    }

    @Override
    public void insertOrUpdateWhiteList(MessageSetup messageSetup) {
        if (Objects.isNull(messageSetup)) {
            logger.info("messageSetup is blank");
            return;
        }

        // 白名单code
        messageSetup.setItemCode(SaasApplyConstant.ITEM_CODE_WHITE_LIST_TAKEAWAY);
        insertOrUpdate(messageSetup);
    }

    @Override
    public int deleteWhiteList(String companyId) {
        if (StringUtils.isBlank(companyId)) {
            logger.info("companyId is blank");
            return 0;
        }
        return deleteMessageSetupBy(companyId, SaasApplyConstant.ITEM_CODE_WHITE_LIST_TAKEAWAY);
    }

    @Override
    public List<MessageSetup> queryWhiteList() {
        return queryMessageSetupList(null, SaasApplyConstant.ITEM_CODE_WHITE_LIST_TAKEAWAY, null);
    }


    @Override
    public void updateDirectAndDepartLeader(Integer directLeader, Integer partLeader,String companyId) {
        if(StringUtils.isEmpty(companyId)){
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        MessageSetupExample exampleUpdate = new MessageSetupExample();
        exampleUpdate.createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andItemCodeEqualTo(SaasMessageConstant.ITEM_CODE_LARGE_OVER_NOTICE);
        MessageSetup updateMessageSetup = new MessageSetup();
        if(Objects.nonNull(partLeader)){
            updateMessageSetup.setDepartLeader(partLeader);
        }
        if(Objects.nonNull(directLeader)){
            updateMessageSetup.setDirectLeader(directLeader);
        }
        if(Objects.nonNull(updateMessageSetup.getDepartLeader()) || Objects.nonNull(updateMessageSetup.getDirectLeader())){
            messageSetupMapper.updateByExampleSelective(updateMessageSetup,exampleUpdate);
        }
    }

    /**
     * 查询新版预算开关
     *
     * @param companyId
     * @return
     */
    @Override
    public int queryBudgetNewSwitch(String companyId) {
        //查询redis.
        String redisKey = MessageFormat.format(RedisKeyConstant.BUDGET_NEW_SWITCH_REDIS_KEY, companyId);
        RedisTemplate redisTemplate = redisService.getRedisTemplate();
        if (redisTemplate.hasKey(redisKey)) {
            int obj = (int) redisTemplate.boundValueOps(redisKey).get();
            logger.info("-------redis查询新版预算开关配置查询结果:{},{}", redisKey, obj);
            return obj;
        }
        MessageSetup messageSetup = queryCompanyMessageSetupWithDefault(companyId, SaasApplyConstant.ITEM_CODE_BUDGET_NEW_SWITCH);
        int flag = SaasMessageConstant.IS_CHECKED_FALSE;
        if (null != messageSetup && messageSetup.getIsChecked() == SaasMessageConstant.IS_CHECKED_TRUE) {
            flag = SaasMessageConstant.IS_CHECKED_TRUE;
        }
        //放入redis
        redisTemplate.boundValueOps(redisKey).set(flag);
        logger.info("-------新版预算开关配置放入redis结果:{},{}", redisKey, flag);
        return flag;
    }

    @Override
    public MessageSetup queryCompanyMessageSetupWithDefault(String companyId, String busiCode, String itemCode) {
        if (StringUtils.isEmpty(companyId) || StringUtils.isEmpty(busiCode) || StringUtils.isEmpty(itemCode)) {
            return null;
        }
        Map<String, Object> paramMap = new HashedMap();
        paramMap.put("companyId", companyId);
        paramMap.put("itemCode", itemCode);
        paramMap.put("busiCodeList", Lists.newArrayList(busiCode));
        List<MessageSetup> messageSetupListWithDefault = messageSetupExtMapper.getCompanyMessageSetupListWithDefault(paramMap);
        if(CollectionUtils.isNotEmpty(messageSetupListWithDefault)){
            return messageSetupListWithDefault.get(0);
        }
        return null;
    }

    public List<MessageSetup> queryMessageSetupList(MessageSetupRequest request) {
        MessageSetupExample messageSetupExample = new MessageSetupExample();
        messageSetupExample.createCriteria()
                .andItemCodeEqualTo(request.getType())
                .andIsCheckedEqualTo(request.getIsCheck() == true ? 1 : 0)
                .andStrVal3Between(request.getStartTime(), request.getEndTime());
        return messageSetupMapper.selectByExample(messageSetupExample);
    }

    /**
     * 配置保存
     */
    @Override
    public void saveMessageSetupConfig(MessageSetup messageSetup) {
        if (messageSetup == null) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        if (StringUtils.isEmpty(messageSetup.getItemCode())) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        if (StringUtils.isEmpty(messageSetup.getCompanyId())) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
        // 校验isChecked值
        validateIsCheckedValue(messageSetup.getIsChecked());
        MessageSetupExample query = new MessageSetupExample();
        query.createCriteria()
                .andCompanyIdEqualTo(messageSetup.getCompanyId())
                .andItemCodeEqualTo(messageSetup.getItemCode());
        List<MessageSetup> oldMessageList = messageSetupMapper.selectByExample(query);
        //原来有值则更新,默认取第一个
        if (!CollectionUtils.isEmpty(oldMessageList)) {
            messageSetup.setId(oldMessageList.get(0).getId());
            messageSetupMapper.updateByPrimaryKeySelective(messageSetup);
        } else {
            //如果没有就新增
            messageSetupMapper.insertSelective(messageSetup);
        }
    }


    @Override
    public void insertOrUpdate(MessageSetup messageSetup) {
        if (Objects.isNull(messageSetup)) {
            logger.info("messageSetup is null, return");
            return;
        }

        if (Objects.isNull(messageSetup.getItemCode())) {
            logger.info("messageSetup.ItemCode is null, return");
            return;
        }

        if (Objects.nonNull(messageSetup.getId())) {
            messageSetup.setUpdateTime(new Date());
            MessageSetupExample messageSetupExample = new MessageSetupExample();
            messageSetupExample.createCriteria().andIdEqualTo(messageSetup.getId());
            messageSetupMapper.updateByExampleSelective(messageSetup, messageSetupExample);
            return;
        }
        messageSetupMapper.insertSelective(messageSetup);
    }

    @Override
    public int deleteMessageSetupBy(String companyId, String itemCode) {
        if (StringUtils.isBlank(itemCode) || StringUtils.isBlank(companyId)) {
            logger.info("itemCode is null or id is null:companyId={}, code={}", companyId, itemCode);
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }

        MessageSetupExample messageSetupExample = new MessageSetupExample();
        messageSetupExample.createCriteria()
                .andItemCodeEqualTo(itemCode)
                .andCompanyIdEqualTo(companyId);

        return messageSetupMapper.deleteByExample(messageSetupExample);
    }

    @Override
    public List<MessageSetup> queryMessageSetupList(String companyId, String itemCode, Integer isChecked) {
        if (StringUtils.isBlank(itemCode) ) {
            logger.info("itemCode is null: code={}", itemCode);
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }

        MessageSetupExample messageSetupExample = new MessageSetupExample();
        MessageSetupExample.Criteria criteria = messageSetupExample.createCriteria();
        criteria.andItemCodeEqualTo(itemCode);
        if (StringUtils.isNotBlank(companyId)) {
            criteria.andCompanyIdEqualTo(companyId);
        }
        if (Objects.nonNull(isChecked)) {
            criteria.andIsCheckedEqualTo(isChecked);
        }

        List<MessageSetup> messageSetups = messageSetupMapper.selectByExample(messageSetupExample);
        if (CollectionUtils.isEmpty(messageSetups)) {
            return new ArrayList<>(0);
        }
        return messageSetups;

    }

    @Override
    public List<MessageSetup> messagesetupList(List<String> companyIdList, Date date) {
        String format = String.format("%tR", date);
        logger.info("MessageSetupServiceImpl->messagesetupList:companyIdList={},date={}",companyIdList,format);
        MessageSetupExample messageSetupExample = new MessageSetupExample();
        if (!companyIdList.isEmpty()){
            messageSetupExample.createCriteria()
                    .andItemCodeEqualTo(SaasMessageConstant.VIRTUAL_CARD_WRITE_OFF_REMIND)  //虚拟卡核销code
                    .andIsCheckedEqualTo(SAAS_VIRTUAL_CARD_WRITE_OFF_REMIND_OPEN)
                    .andStrVal3EqualTo(format)      //当前时间
                    .andCompanyIdNotIn(companyIdList);
        }else{
            messageSetupExample.createCriteria()
                    .andItemCodeEqualTo(SaasMessageConstant.VIRTUAL_CARD_WRITE_OFF_REMIND)  //虚拟卡核销code
                    .andIsCheckedEqualTo(SAAS_VIRTUAL_CARD_WRITE_OFF_REMIND_OPEN)
                    .andStrVal3EqualTo(format);      //当前时间;
        }
        List<MessageSetup> messageSetups = messageSetupMapper.selectByExample(messageSetupExample);
        return messageSetups;
    }
    
    @Override
    public ExceedPersonalPayCompanyConf getPersonalPayReasonConfBy(String companyId) {
        ExceedPersonalPayCompanyConf res = new ExceedPersonalPayCompanyConf();
        // 默认都不开启
        res.setIsShowRefundReason(false);
        res.setIsShowChangeReason(false);
        if (StringUtils.isBlank(companyId)) {
            return res;
        }
        logger.info("请求companyId={}", companyId);
        AirManagerSetting airManagerSetting = iMessageSetupRpcService.queryAirRuleExceedConfigMessage(companyId);
        logger.info("请求airManagerSetting:{}", JsonUtils.toJson(airManagerSetting));

        if (Objects.nonNull(airManagerSetting)
                && Objects.nonNull(airManagerSetting.getIsOpenExceedConfig())
                && Objects.equals(airManagerSetting.getIsOpenExceedConfig(), Boolean.TRUE)
                && Objects.nonNull(airManagerSetting.getCustomConfig())) {
            boolean isShowRefundReason = Objects.equals(airManagerSetting.getCustomConfig().getRefundTicketReasonShow(), Boolean.TRUE);
            boolean isShowChangeReason = Objects.equals(airManagerSetting.getCustomConfig().getChangeTicketReasonShow(), Boolean.TRUE);
            res.setIsShowRefundReason(isShowRefundReason);
            res.setIsShowChangeReason(isShowChangeReason);
        }
        return res;
    }

    @Override
    public int saveCommonSettingConfig(MessageSetupDTO messageSetup) {
        //公司和itemCode不能为空
        if (messageSetup == null || StringUtils.isAnyBlank(messageSetup.getCompanyId(), messageSetup.getItemCode())) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        // add
        if (Objects.isNull(messageSetup.getId())) {
            MessageSetup queryMessageSetup =
                    queryCompanyMessageSetupWithoutDefault(messageSetup.getCompanyId(), messageSetup.getItemCode());
            if (Objects.nonNull(queryMessageSetup)) {
                logger.info("saveCommonSettingConfig already added, {}", JsonUtils.toJson(queryMessageSetup));
                return 0;
            }

            MessageSetup messageSetupNew = convertMessageSetup(messageSetup);
            logger.info("saveCommonSettingConfig add:{}", JsonUtils.toJson(messageSetupNew));
           return messageSetupMapper.insertSelective(messageSetupNew);
        }
        // update
        MessageSetup messageSetupNew = convertMessageSetup(messageSetup);
        logger.info("saveCommonSettingConfig update:{}", JsonUtils.toJson(messageSetupNew));
        MessageSetupExample messageSetupExample = new MessageSetupExample();
        messageSetupExample.createCriteria().andIdEqualTo(messageSetup.getId());
        return messageSetupMapper.updateByExampleSelective(messageSetupNew, messageSetupExample);
    }

    @Override
    @Transactional(transactionManager = "saas", readOnly = false, rollbackFor = Exception.class)
    public void syncUpdateEmailRelation(String companyId, Map<String, List<EmployeeEmailContract>> filterEmailRMap, Map<String, List<MessageSetupEmailRelation>> emailRelationMap) {
        if (StringUtil.isEmpty(companyId)){
            return;
        }
        if (ObjectUtil.isAllEmpty(filterEmailRMap,emailRelationMap)){
            return;
        }
        Set<String> busiCodes = emailRelationMap.keySet();
        for (String busiCode : busiCodes) {
            List<MessageSetupEmailRelation> relations = emailRelationMap.get(busiCode);
            Map<String,String> emailMap = filterEmailRMap.getOrDefault(busiCode,Collections.emptyList())
                    .stream()
                    .collect(HashMap::new, (map, item) -> map.put(item.getId(), item.getEmail()), HashMap::putAll);
            List<MessageSetupEmailRelation> deleteRelations = relations.stream().filter(t -> !emailMap.containsKey(t.getEmployeeId())).collect(Collectors.toList());
            relations = relations.stream().filter(t -> {
                if (!emailMap.containsKey(t.getEmployeeId())){
                    return Boolean.FALSE;
                }
                String email = emailMap.get(t.getEmployeeId());
                String sourceEmail = t.getEmail();
                if (ObjUtils.isEmpty(email) && ObjUtils.isEmpty(sourceEmail)){
                    return Boolean.FALSE;
                }
                if (ObjUtils.isNotEmpty(email) && ObjUtils.isNotEmpty(sourceEmail) && email.equals(sourceEmail)){
                    return Boolean.FALSE;
                }
                return Boolean.TRUE;
            }).collect(Collectors.toList());
            List<String> emails = relations.stream().filter(t -> StringUtils.isNotEmpty(t.getEmail())).map(MessageSetupEmailRelation::getEmail).distinct().collect(Collectors.toList());
            relations.forEach(t->t.setEmail(emailMap.get(t.getEmployeeId())));
            deleteMessageRelation(companyId,busiCode,deleteRelations);
            updateMessageRelation(companyId,busiCode,relations,emails);
        }
    }

    private void deleteMessageRelation(String companyId,String busiCode,List<MessageSetupEmailRelation> relations){
        if (ObjectUtil.isEmpty(companyId) || ObjectUtil.isEmpty(busiCode) ||ObjectUtil.isEmpty(relations)){
            return;
        }
        List<String> ids = relations.stream().map(MessageSetupEmailRelation::getId).distinct().collect(Collectors.toList());
        MessageSetupEmailRelationExample example = new MessageSetupEmailRelationExample();
        example.createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andBusiCodeEqualTo(busiCode)
                .andIdIn(ids);
        messageSetupEmailRelationMapper.deleteByExample(example);
        List<String> emails = relations.stream().filter(t -> StringUtils.isNotEmpty(t.getEmail())).map(MessageSetupEmailRelation::getEmail).distinct().collect(Collectors.toList());
        if (ObjUtils.isNotEmpty(emails)){
            MessageSetupEmailExample emailExample = new MessageSetupEmailExample();
            emailExample.createCriteria()
                    .andCompanyIdEqualTo(companyId)
                    .andBusiCodeEqualTo(busiCode)
                    .andEmailIn(emails);
            messageSetupEmailMapper.deleteByExample(emailExample);
        }
    }
    private void updateMessageRelation(String companyId,String busiCode,List<MessageSetupEmailRelation> relations,List<String> emails){
        if (ObjectUtil.isEmpty(companyId) || ObjectUtil.isEmpty(busiCode) ||ObjectUtil.isEmpty(relations)){
            return;
        }
        Date UpdateTime = new Date();
        for (MessageSetupEmailRelation relation : relations) {
            relation.setUpdateTime(UpdateTime);
            messageSetupEmailRelationMapper.updateByPrimaryKey(relation);
        }
        if (ObjUtils.isNotEmpty(emails)){
            MessageSetupEmailExample emailExample = new MessageSetupEmailExample();
            emailExample.createCriteria()
                    .andCompanyIdEqualTo(companyId)
                    .andBusiCodeEqualTo(busiCode)
                    .andEmailIn(emails);
            messageSetupEmailMapper.deleteByExample(emailExample);
        }
    }
    private MessageSetup convertMessageSetup(MessageSetupDTO messageSetup) {
        MessageSetup result = new MessageSetup();
        result.setId(messageSetup.getId());
        result.setCompanyId(messageSetup.getCompanyId());
        result.setItemCode(messageSetup.getItemCode());
        result.setIsChecked(messageSetup.getIsChecked());
        result.setIntVal1(messageSetup.getIntVal1());
        result.setIntVal2(messageSetup.getIntVal2());
        result.setIntVal3(messageSetup.getIntVal3());
        result.setStrVal1(messageSetup.getStrVal1());
        result.setStrVal2(messageSetup.getStrVal2());
        result.setStrVal3(messageSetup.getStrVal3());
        result.setCreateTime(new Date());
        result.setUpdateTime(new Date());
        result.setUserId(messageSetup.getUserId());
        result.setDistinguishCategoryType(messageSetup.getDistinguishCategoryType());
        result.setCategoryTypeAmount(messageSetup.getCategoryTypeAmount());
        result.setDirectLeader(messageSetup.getDirectLeader());
        result.setDepartLeader(messageSetup.getDepartLeader());
        return result;
    }
}
