package com.fenbeitong.saas.core.contract.reason;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/11
 */
public class InitCompanyDefaultReason {

    /**
     * 公司id 非必需 不传按所有公司处理
     */
    private String company_id;

    /**
     * 需要初始化的默认事由项列表
     */
    private List<DefaultReason> default_reason_list;

    public String getCompany_id() {
        return company_id;
    }

    public void setCompany_id(String company_id) {
        this.company_id = company_id;
    }

    public List<DefaultReason> getDefault_reason_list() {
        return default_reason_list;
    }

    public void setDefault_reason_list(List<DefaultReason> default_reason_list) {
        this.default_reason_list = default_reason_list;
    }
}
