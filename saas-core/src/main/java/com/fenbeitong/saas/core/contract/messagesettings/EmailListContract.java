package com.fenbeitong.saas.core.contract.messagesettings;

import com.fenbeitong.saas.api.model.dto.user.EmployeeEmailContract;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by zhangkai on 2017/6/4.
 */
public class EmailListContract {

    private String item_code;
    private List<String> email_list = new ArrayList<>();

    private List<EmployeeEmailContract> emails = new ArrayList<>();

    public EmailListContract() {
    }

    public EmailListContract(String item_code, List<String> email_list) {
        this.item_code = item_code;
        this.email_list = email_list;
    }

    public String getItem_code() {
        return item_code;
    }

    public void setItem_code(String item_code) {
        this.item_code = item_code;
    }

    public List<String> getEmail_list() {
        return email_list;
    }

    public List<EmployeeEmailContract> getEmails() {
        return emails;
    }

    public void setEmails(List<EmployeeEmailContract> emails) {
        this.emails = emails;
    }

    public void setEmail_list(List<String> email_list) {
        this.email_list = email_list;
    }

}
