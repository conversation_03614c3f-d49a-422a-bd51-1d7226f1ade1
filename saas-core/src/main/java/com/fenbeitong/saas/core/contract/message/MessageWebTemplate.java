package com.fenbeitong.saas.core.contract.message;

/**
 * 消息模板
 *
 * <AUTHOR>
 * @date 2021/9/15
 */
public interface MessageWebTemplate {

    /**
     * 大额异动提醒-通知标题
     */
//    String LargeOverTitle = "[大额消费]{0}提交了一笔{1}的{2}订单。";

    /**
     * 大额异动提醒-通知内容
     */
//    String LargeOverContent = "大额消费：{0}在分贝通中提交了一笔{1}的{2}订单，请关注。";

    /**
     * 分贝券发券失败通知-通知标题
     */
//    String CouponSendFailTitle = "分贝券发券失败通知";

    /**
     * 分贝券发券失败通知-通知内容
     */
//    String CouponSendFailContent = "尊敬的用户您好，您的企业{0}分贝券发放失败，您可检查企业个人账户余额是否充足，充值后请进入商户后台重新操作发放。";

    /**
     * 企业余额提醒-商务消费账户余额通知(新版)-通知标题
     */
//    String AccountBalanceWarnNewTitle = "商务消费账户余额通知";

    /**
     * 企业余额提醒-商务消费账户余额通知(新版)-通知内容
     */
//    String AccountBalanceWarnNewContent = "尊敬的用户，您好。截止到{0}，您公司商务消费场景的生效账户余额为{1}，已小于您设置的告警阈值，为不影响员工正常使用，建议您及时{2}。";

    /**
     * 企业余额提醒-商务消费账户余额通知(旧版)-通知标题
     */
//    String AccountBalanceWarnOldTitle = "商务消费账户余额通知";

    /**
     * 企业余额提醒-商务消费账户余额通知(旧版)-通知内容
     */
//    String AccountBalanceWarnOldContent = "尊敬的用户，您好。截止到{0}，您公司的可用余额不足{1}。";

    /**
     * 企业余额提醒-虚拟卡账户余额通知(旧版)-通知标题
     */
//    String VirtualCardAccountBalanceWarnTitle = "虚拟卡账户余额不足";

    /**
     * 企业余额提醒-虚拟卡账户余额通知(旧版)-通知内容
     */
//    String VirtualCardAccountBalanceWarnContent = "尊敬的用户，您好。截止到{0}，您公司虚拟卡场景的生效账户余额为{1}，已小于您设置的告警阈值，为不影响员工正常使用，建议您及时充值。";

    /**
     * 企业余额提醒-福利补助账户余额通知(旧版)-通知标题
     */
//    String acct_individual_remind_notice_title = "福利补助账户余额不足";

    /**
     * 企业余额提醒-福利补助账户余额通知(旧版)-通知内容
     */
//    String acct_individual_remind_notice_content = "尊敬的用户，您好。截止到{0}，您公司福利补助场景的生效账户余额为{1}，已小于您设置的告警阈值，为不影响员工正常使用，建议您及时{2}。";

    /**
     * 企业余额提醒-对公付款账户余额通知(旧版)-通知标题
     */
//    String acct_public_remind_notice_title = "对公付款账户余额不足";

    /**
     * 企业余额提醒-对公付款账户余额通知(旧版)-通知内容
     */
//    String acct_public_remind_notice_content = "尊敬的用户，您好。截止到{0}，您公司对公付款场景的{1}尾号为{2}账户余额为{3}，已小于您设置的告警阈值，为不影响员工正常使用，建议您及时充值";

}
