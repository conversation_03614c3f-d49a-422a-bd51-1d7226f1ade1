package com.fenbeitong.saas.core.contract.message.inner;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.eventbus.event.bank.BankCardTxnAuthResult;

/**
 * 交易通知附加信息
 */
public class TransactionInfo {

    /**
     * 126 个人虚拟卡消费
     */
    private Integer order_type;

    /**
     * 格式固定为yyyy-MM-dd HH:mm:ss
     */
    private String create_time;

    private String order_msg;

    /**
     * 跳转订单号
     */
    private String redirect_order_id;

    /**
     * 跳转类型 1交易详情，2交易失败
     */
    private Integer sub_type;

    private JSONObject result;

    public Integer getOrder_type() {
        return order_type;
    }

    public void setOrder_type(Integer order_type) {
        this.order_type = order_type;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public String getOrder_msg() {
        return order_msg;
    }

    public void setOrder_msg(String order_msg) {
        this.order_msg = order_msg;
    }

    public String getRedirect_order_id() {
        return redirect_order_id;
    }

    public void setRedirect_order_id(String redirect_order_id) {
        this.redirect_order_id = redirect_order_id;
    }

    public Integer getSub_type() {
        return sub_type;
    }

    public void setSub_type(Integer sub_type) {
        this.sub_type = sub_type;
    }

    public JSONObject getResult() {
        return result;
    }

    public void setResult(JSONObject result) {
        this.result = result;
    }

    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }
}


