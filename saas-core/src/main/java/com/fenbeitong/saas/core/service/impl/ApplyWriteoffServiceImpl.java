package com.fenbeitong.saas.core.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.extra.qrcode.QrCodeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.api.service.bank.IBankCardSearchService;
import com.fenbeitong.finhub.common.constant.BudgetCategoryTypeEnum;
import com.fenbeitong.finhub.common.saas.entity.CostAttribution;
import com.fenbeitong.finhub.common.saas.entity.CostAttributionGroup;
import com.fenbeitong.finhub.common.saas.entity.CostCategory;
import com.fenbeitong.finhub.common.saas.entity.CostInfo;
import com.fenbeitong.finhub.common.utils.PdfUtils;
import com.fenbeitong.finhub.kafka.msg.saas.KafkaBankApplyMsg;
import com.fenbeitong.invoice.api.model.dto.InvoicePicResRPCDTO;
import com.fenbeitong.invoice.api.service.IFbtInvoiceService;
import com.fenbeitong.noc.api.service.bank.model.vo.HistoryInvoiceVO;
import com.fenbeitong.noc.api.service.bank.service.IBankOrderService;
import com.fenbeitong.saas.card.api.order.api.channel.saas.IOrderUpdateRpcCallerSaasApi;
import com.fenbeitong.saas.card.api.order.dto.req.channel.saas.SaasHistoryInvoiceVO;
import com.fenbeitong.saas.card.common.utils.ModelConverterUtils;
import com.fenbeitong.saas.core.common.DynamicDataSourceDecision;
import com.fenbeitong.saas.core.common.base.SaasException;
import com.fenbeitong.saas.core.common.constant.GlobalResponseCode;
import com.fenbeitong.saas.core.common.constant.SaasApplyConstant;
import com.fenbeitong.saas.core.common.constant.SaasFlowConstant;
import com.fenbeitong.saas.core.common.constant.SaasMessageConstant;
import com.fenbeitong.saas.core.contract.apply.*;
import com.fenbeitong.saas.core.contract.applyflow.ApplyFlowApplicateResponseContract;
import com.fenbeitong.saas.core.contract.applyflow.CompanyApplyFlowItemSetV2RequestContract;
import com.fenbeitong.saas.core.contract.applyflow.CompanyApplyFlowSetV2RequestContract;
import com.fenbeitong.saas.core.contract.common.IdNameContract;
import com.fenbeitong.saas.core.contract.common.PageResultBaseContract;
import com.fenbeitong.saas.core.contract.common.PushContract;
import com.fenbeitong.saas.core.contract.message.MessageSaveContract;
import com.fenbeitong.saas.core.contract.message.inner.ApplyInfo;
import com.fenbeitong.saas.core.contract.messagesettings.ApplySetupContract;
import com.fenbeitong.saas.core.contract.user.EmployeeNameAndDeptContract;
import com.fenbeitong.saas.core.dao.common.adapter.ApplyAdapterMapper;
import com.fenbeitong.saas.core.language.CoreLanguage;
import com.fenbeitong.saas.core.language.LanguageUtils;
import com.fenbeitong.saas.core.language.MessageLanguageEnum;
import com.fenbeitong.saas.core.model.enums.ApplyFlowUserItemStatus;
import com.fenbeitong.saas.core.model.enums.CompanyApplyType;
import com.fenbeitong.saas.core.model.enums.CostAttributionCategory;
import com.fenbeitong.saas.core.model.enums.apply.*;
import com.fenbeitong.saas.core.model.enums.message.MessageType;
import com.fenbeitong.saas.core.model.enums.message.SenderType;
import com.fenbeitong.saas.core.model.enums.sysConfig.MethodCodeType;
import com.fenbeitong.saas.core.model.enums.sysConfig.MethodType;
import com.fenbeitong.saas.core.model.enums.user.UserRole;
import com.fenbeitong.saas.core.model.saas.*;
import com.fenbeitong.saas.core.service.*;
import com.fenbeitong.saas.core.service.kafka.ApplyOrderProducerService;
import com.fenbeitong.saas.core.utils.flow.FlowCheckUtil;
import com.fenbeitong.saas.core.utils.notice.TemplateConstant;
import com.fenbeitong.saas.core.utils.tools.*;
import com.fenbeitong.saasplus.api.model.dto.apply.RpcCommonResult;
import com.fenbeitong.saasplus.api.model.dto.finance.BatchDeductAndReleaseReq;
import com.fenbeitong.saasplus.api.model.dto.finance.FinanceCostInfoVO;
import com.fenbeitong.saasplus.api.model.dto.finance.OrderCostInfoReq;
import com.fenbeitong.saasplus.api.model.dto.finance.PettyRelationInfo;
import com.fenbeitong.saasplus.api.model.dto.messageSetup.PettyConfigRpcDto;
import com.fenbeitong.saasplus.api.model.enums.common.ApplyOrderActionType;
import com.fenbeitong.saasplus.api.model.enums.cost.BudgetObjectEnum;
import com.fenbeitong.saasplus.api.service.finance.IFinanceCostService;
import com.fenbeitong.saasplus.api.service.finance.IOrderCostService;
import com.fenbeitong.usercenter.api.model.dto.costcenter.CostCenterAndGroupDto;
import com.fenbeitong.usercenter.api.model.dto.costcenter.CostCenterGroupDto;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.fenbeitong.usercenter.api.model.dto.invoice.InvoiceConfigDTO;
import com.fenbeitong.usercenter.api.model.dto.orgunit.OrgUnitResult;
import com.fenbeitong.usercenter.api.service.company.ICompanyRuleService;
import com.fenbeitong.usercenter.api.service.costcenter.ICostCenterService;
import com.fenbeitong.usercenter.api.service.employee.IBaseEmployeeExtService;
import com.fenbeitong.usercenter.api.service.invoice.IInvoiceConfigService;
import com.fenbeitong.usercenter.api.service.meta.IMetaService;
import com.fenbeitong.usercenter.api.service.orgunit.IOrgUnitService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.net.HttpClientUtils;
import com.luastar.swift.base.utils.DateUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.StrUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Transactional(transactionManager = "dynamic", readOnly = false, rollbackFor = Exception.class)
public class ApplyWriteoffServiceImpl implements IApplyWriteoffService {

    private static final Logger logger = LoggerFactory.getLogger(ApplyWriteoffServiceImpl.class);

    private static final String URL_TEMPLATE_DETAIL = HostPropertyConfigTool.HOST_HARMONY + "/harmony/mail/template/name/detail";

    private static final String URL_PDF_UPDATE = HostPropertyConfigTool.HOST_SAAS_PLUS;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyOrderMapper applyOrderMapper;

    @Autowired
    private IApplyFlowService applyFlowService;

    @Autowired
    IUserService userService;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyApproverMapMapper applyApproverMapMapper;

    @Autowired
    IPushService pushService;

    @Autowired
    IMessageService messageService;

    @Autowired
    private IApplyFlowV2Service applyFlowV2Service;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyOrderLogExtMapper applyOrderLogExtMapper;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyOrderExtMapper applyOrderExtMapper;

    @Autowired
    private ApplyAdapterMapper applyAdapterMapper;

    @Autowired
    private ICustomRoleService customRoleService;

    @Autowired
    private IBaseOrganizationService iBaseOrganizationService;


    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyOrderLogMapper applyOrderLogMapper;


    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyTripInfoMapper applyTripInfoMapper;

    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyTripInfoExtMapper applyTripInfoExtMapper;

    @Autowired
    IMessageSetupService messageSetupService;

    @Autowired
    IBankOrderService iBankOrderService;

    @Autowired
    IOrderUpdateRpcCallerSaasApi iOrderUpdateRpcCallerSaasApi;

    @Autowired
    IFbtInvoiceService iFbtInvoiceService;

    @Autowired
    IMetaService iMetaService;

    @Autowired
    IBaseOrganizationService baseOrganizationService;

    @Autowired
    IFinanceCostService iFinanceCostService;

    @Autowired
    ISysCommonConfigService iSysCommonConfigService;

    @Autowired
    private ICompanyRuleService iCompanyRuleService;

    @Autowired
    private IBankCardSearchService iBankCardSearchService;

    @Autowired
    private IApplyOrderService iApplyOrderService;

    @Autowired
    private com.fenbeitong.saasplus.api.service.messageSetup.IMessageSetupService iMessageSetupRpcService;

    @Autowired
    private IApplyOrderKafkaProducerService iApplyOrderKafkaProducerService;

    @Autowired
    private IApplyV2Service applyV2Service;

    @Autowired
    private IOrderCostService orderCostService;

    @Autowired
    private IInvoiceConfigService iInvoiceConfigService;

    @Autowired
    private ApplyOrderProducerService applyOrderProducerService;

    @Autowired
    private IOrgUnitService iOrgUnitService;

    @Autowired
    private IBaseEmployeeExtService iBaseEmployeeExtService;

    @Autowired
    private ICostCenterService iCostCenterService;

    public void setMessageService(IMessageService messageService) {
        this.messageService = messageService;
    }


    /**
     * 创建申请单
     *
     * @param applyContract
     * @return
     */
    @Override
    public GlobalResponseCode create(String token, ApplyBankIndividualContract applyContract, String userId, String companyId, String ip, String clientHeadVersion,String userName) throws SaasException {
        FlowCheckUtil.check(companyId, clientHeadVersion);
        //检查数据信息
        GlobalResponseCode code = checkApplyData(token, applyContract, userId, companyId,clientHeadVersion);
        if (code != GlobalResponseCode.Success) {
            return code;
        }
        ApplyOrderV2Contract applyOrderV2ContractInfo = applyContract.getApply();
        if (applyOrderV2ContractInfo.getState() == ApplyStatus.PendingAudit.getValue()) {
            //占用预算
            List<FinanceCostInfoVO> costListDataList = applyOrderV2ContractInfo.getCostList();
            if (CollectionUtils.isNotEmpty(costListDataList)) {
                List<Integer> costIdList = costListDataList.stream().map(financeCostInfoVO -> financeCostInfoVO.getId()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(costIdList)) {
                    List<OrderCostInfoReq> orderCostInfoReqList = Lists.newArrayList();
                    List<FinanceCostInfoVO> costList = iFinanceCostService.getCostListById(costIdList);
                    if (CollectionUtils.isNotEmpty(costList)) {
                        List<FinanceCostInfoVO> offlineCosts = costList.stream().filter(financeCostInfoVO -> financeCostInfoVO.getType().equals(1)).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(offlineCosts)) {
                            for (FinanceCostInfoVO financeCostInfoVO : offlineCosts) {
                                CostInfo costInfo = new CostInfo();
                                CostCategory costCategory = new CostCategory();
                                costCategory.setId(ObjUtils.toString(financeCostInfoVO.getCostCategoryId()));
                                costCategory.setName(financeCostInfoVO.getCostCategory());
                                List<CostAttributionGroup> costAttributionGroupList = financeCostInfoVO.getCostAttributionGroupList();
                                costInfo.setCostCategory(costCategory);
                                costInfo.setCostAttributionGroupList(costAttributionGroupList);
                                OrderCostInfoReq orderCostInfoReq = new OrderCostInfoReq();
                                orderCostInfoReq.setAmount(financeCostInfoVO.getTotalAmount());
                                orderCostInfoReq.setApplyId(applyOrderV2ContractInfo.getId());
                                orderCostInfoReq.setCostId(ObjUtils.toString(financeCostInfoVO.getId()));
                                orderCostInfoReq.setCategory(BudgetCategoryTypeEnum.VIRTUAL_CARD.getCategory());
                                orderCostInfoReq.setForce(false);
                                orderCostInfoReq.setUserId(userId);
                                orderCostInfoReq.setCompanyId(companyId);
                                orderCostInfoReq.setCostAttributionInfo(costInfo);
                                orderCostInfoReq.setClientVersion(clientHeadVersion);
                                orderCostInfoReqList.add(orderCostInfoReq);
                            }
                        }
                        if (CollectionUtils.isNotEmpty(orderCostInfoReqList)) {
                            RpcCommonResult rpcCommonResult = orderCostService.batchOccupied(orderCostInfoReqList);
                            //状态 1成功 0失败
                            if (rpcCommonResult == null || ObjUtils.toInteger(rpcCommonResult.getCode()) == 0) {
                                throw new SaasException(GlobalResponseCode.BudgetUpdateIsError.getCode(), rpcCommonResult.getMessage(), 0, null);
                            }
                        }
                    }
                }
            }
        }
        ApplyOrder record= applyOrderMapper.selectByPrimaryKey(applyContract.getApply().getId());
        if (ObjUtils.isNotEmpty(record)&&record.getState()==2){
            return GlobalResponseCode.ApplyOrderIsSumbit;
        }
        //老版本 ->拒绝||撤销不允许再次提交
        if(VersionTool.compare(clientHeadVersion,"4.2.0")<0){
            ApplyOrder oApplyOrder = applyOrderMapper.selectByPrimaryKey(applyContract.getApply().getId());
            //不是草稿 && 以前申请的单子是撤回 || 拒绝状态下 ->重置新申请单id
            if(null != oApplyOrder &&(oApplyOrder.getState()==ApplyStatus.Backout.getValue()||oApplyOrder.getState()==ApplyStatus.Return.getValue())){
                //提示升级版本
                return GlobalResponseCode.ApplyWriteoffVersionUpgrade;
            }
        }
        List<FinanceCostInfoVO> costListData = applyContract.getApply().getCostList();
        List<Integer> costIds = new ArrayList<>();
        if (null != costListData && costListData.size() > 0) {
            costListData.forEach(
                    e -> {
                        costIds.add(e.getId());
                    });
        }
        //草稿
        if (applyContract.getApply().getState()==1){
            ApplyOrderV2Contract applyOrderV2Contract=applyContract.getApply();
            applyOrderV2Contract.setEmployee_id(userId);
            applyOrderV2Contract.setCompany_id(companyId);
            ApplyOrder applyOrder= applyOrderV2Contract.ToModel();
            applyOrder.setUpdateTime(new Date());
            applyOrder.setApplicantName(userName);
            applyOrder.setRootApplyOrderId(applyOrder.getId());
            if (ObjUtils.isEmpty(applyOrder.getBudget())){
                applyOrder.setBudget(0);
            }
            ApplyOrder  ao = applyOrderMapper.selectByPrimaryKey(applyOrder.getId());
            if (ObjUtils.isNotEmpty(ao)){
                applyOrderMapper.updateByPrimaryKeySelective(applyOrder);
            }else {
                applyOrder.setCreateTime(new Date());
                applyOrderMapper.insertSelective(applyOrder);
            }
            //保存费用id到apply_trip_info
            insertCost(applyContract);
            // 申请单绑定费用
//            updateCost(costIds,applyOrder.getId(),BoundStatusType.AlreadyBound.getValue());
//            //更新费用表中state状态
//            rpcUpdateCostState(costIds,ApplyStatus.Draft.getValue());
            iFinanceCostService.bindingCostInApply(costIds,applyOrder.getId(),ApplyStatus.Draft.getValue());
            return GlobalResponseCode.Success;
        }
        Integer logId = null;
        ApplyOrderV2Contract applyOrderContract = applyContract.getApply();
        String applyId=applyOrderContract.getId();
        applyOrderContract.setEmployee_id(userId);
        applyOrderContract.setCompany_id(companyId);
        ApplyType applyType = ApplyType.valueOf(applyOrderContract.getType().intValue());
        CompanyApplyType companyApplyType = CompanyApplyType.valueOf(applyOrderContract.getFlow_type());
        if (companyApplyType == CompanyApplyType.Unknown) {
            companyApplyType = CompanyApplyType.Elastic;
        }
        applyOrderContract.setFlow_type(companyApplyType.getValue());

        //如果是提交审批保存审批流设置和审批单日志记录
        String approverId = applyOrderContract.getApprover_id();
        CompanyApplyFlowSetV2RequestContract flowRequest = applyOrderContract.getFlow();
        if (flowRequest == null) {
            flowRequest = new CompanyApplyFlowSetV2RequestContract();
        }
        flowRequest.setCompany_apply_type(companyApplyType.getValue());
        flowRequest.setApply_type(applyType.getValue());
        flowRequest.setBudget(applyOrderContract.getBudget());
        ApplyFlowApplicateResponseContract flowResponse = null;
        //判断审批流的类型
        Integer category = applyOrderContract.getCost_attribution_category();
        String costAttributionId = applyOrderContract.getCost_attribution_id();
        //处理超规类型
        Integer bussinessType = applyOrderContract.getType();
        //查询应用审批流
        flowResponse = applyFlowV2Service.applicateFlow(SaasFlowConstant.SETTING_TYPE_VIRTUAL_CARD, flowRequest, applyId, userId, companyId, ip, category, costAttributionId, ExceedBuyType.UnSupernormal.getValue(), bussinessType, applyId);
        if (flowResponse != null && !StringTool.isNullOrEmpty(flowResponse.getApprover())) {
            approverId = flowResponse.getApprover();
        }
        //设置当前审批人
        logId = flowResponse.getNextLogId();
        if (StringUtils.isNotBlank(approverId)) {
            applyOrderContract.setApprover_id(approverId);
        }
        applyOrderContract.setLog_id(ObjUtils.toLong(flowResponse.getNextLogId()));
        if (companyApplyType == CompanyApplyType.Elastic) {
            if (approverId != null && approverId.length() > 0) {
                int logSort = 100;
                List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectListByCreateTimeDesc(applyId);
                if (!CollectionUtils.isEmpty(applyOrderLogList)) {
                    Integer sort = applyOrderLogList.get(0).getSort();
                    if (sort != null) {
                        logSort = sort + 100;
                    }
                }
                //检测审批人是否属于当前这个公司
                Boolean isApproverInCompany = userService.isUserInCompany(approverId, companyId);
                if (!isApproverInCompany) {
                    return GlobalResponseCode.ApplyApproverNotInCompany;
                }
                Date now = new Date();
                ApplyOrderLog log = new ApplyOrderLog();
                log.setApplyOrderId(applyId);
                log.setRootApplyOrderId(applyId);
                log.setCreateTime(now);
                log.setIp(ip);
                log.setSponsorId(userId);
                log.setReceiverId(approverId);
                log.setCheckReason(null);
                log.setAction(ApplyLogAction.Submit.getValue());
                log.setSort(logSort);
                applyOrderLogMapper.insertSelective(log);

                ApplyOrderLog logApprove = new ApplyOrderLog();
                logApprove.setApplyOrderId(applyId);
                logApprove.setRootApplyOrderId(applyId);
                logApprove.setIp(ip);
                logApprove.setSponsorId(approverId);
                logApprove.setReceiverId("");
                logApprove.setCheckReason(null);
                logApprove.setAction(ApplyLogAction.Approval.getValue());
                logApprove.setSort(logSort+100);
                applyOrderLogMapper.insertSelective(logApprove);
                logId = logApprove.getId();
                applyOrderContract.setLog_id(ObjUtils.toLong(logId));
            }
        }
        //整理数据
        clearApplyData(applyContract);
        //待审核装填
        ApplyStatus applyState = ApplyStatus.PendingAudit;
        ApplyLogAction action = ApplyLogAction.Submit;
        Date now = new Date();
        ApplyOrder apply = applyOrderContract.ToModel();
        apply.setApplicantName(userName);
        apply.setUpdateTime(now);
        apply.setCreateTime(now);
        //创建申请单
        apply.setId(applyId);
        apply.setRootApplyOrderId(applyId);
        apply.setVoucherStatus(ApplyVoucherStatus.Uncreate.getValue());
        apply.setState(applyState.getValue());
        applyOrderContract.setId(applyId);
        apply.setHastenStatu(HastenType.NotHasten.getValue());
        apply.setReturnDownload(ReturnTicketType.NotReturn.getValue());
        apply.setReturnTicket(ReturnTicketType.NotReturn.getValue());

        ApplyOrder oldApplyOrder= applyOrderMapper.selectByPrimaryKey(applyId);
        if(null !=oldApplyOrder){
            applyOrderMapper.updateByPrimaryKeySelective(apply);
        }else {
            applyOrderMapper.insertSelective(apply);
        }
        //判断审批单是否是待审核状态
        List<ApplyOrderCopyTo> applyOrderCopyTos = applyAdapterMapper.getApplyOrderCopyToExtMapper(companyId).queryApplyOrderCCByApplyOrderId(applyId);
        if (!CollectionUtils.isEmpty(applyOrderCopyTos)) {
            applyAdapterMapper.getApplyOrderCopyToExtMapper(companyId).deleteCCByApplyOrderId(applyId);
        }
        //获取抄送人数据
        CompanyApplyFlowSetV2RequestContract flow = applyOrderContract.getFlow();
        if (flow != null) {
            List<CompanyApplyFlowItemSetV2RequestContract> cc_list = flow.getCc_list();
            if (!CollectionUtils.isEmpty(cc_list)) {
                if (cc_list.size() > 5) {
                    throw new SaasException(GlobalResponseCode.FlowItemCCTooMuch);
                }
                int sort = 1;
                for (CompanyApplyFlowItemSetV2RequestContract ccFlowItem : cc_list) {
                    //保存抄送人
                    ApplyOrderCopyTo applyOrderCopyTo = new ApplyOrderCopyTo();
                    applyOrderCopyTo.setId(IDTool.CreateUniqueID());
                    applyOrderCopyTo.setApplyOrderId(applyId);
                    applyOrderCopyTo.setCreateTime(new Date());
                    applyOrderCopyTo.setItemId(ccFlowItem.getItem_id());
                    applyOrderCopyTo.setItemType(ccFlowItem.getItem_type());
                    applyOrderCopyTo.setSort(sort++);
                    applyOrderCopyTo.setRead(false);
                    if (ccFlowItem.getUser() == null || StringUtils.isBlank(ccFlowItem.getUser().getUser_id())) {
                        throw new SaasException(GlobalResponseCode.ApplyFlowItemNeedSetCCUser);
                    }
                    if (ccFlowItem.getUser() != null) {
                        applyOrderCopyTo.setUserId(ccFlowItem.getUser().getUser_id());
                    }
                    //待确认
                    applyOrderCopyTo.setIsDelete(ccFlowItem.getIs_delete());//1:不能删除 2:可以删除
                    applyAdapterMapper.getApplyOrderCopyToMapper(companyId).insert(applyOrderCopyTo);
                }
            }

        }
        if (applyState == ApplyStatus.PendingAudit) {
            insertApproverMap(applyId, applyOrderContract.getApprover_id(), now);
        }
        if (applyState == ApplyStatus.PendingAudit) {
            //push
            postMessage(apply, apply.getEmployeeId(), apply.getApproverId(), action, ApplyStatus.PendingAudit, logId, "");
            //push 抄送人
            pushCCMessage(apply, apply.getEmployeeId(), action, ApplyStatus.PendingAudit);
        }
        //保存费用id到apply_trip_info
        insertCost(applyContract);
        // 申请单绑定费用
//        updateCost(costIds,applyId,BoundStatusType.AlreadyBound.getValue());
//        //更新费用表中state状态
//        rpcUpdateCostState(costIds,ApplyStatus.PendingAudit.getValue());

        //修改交易订单状态
        iFinanceCostService.bindingCostInApply(costIds,applyId,ApplyStatus.PendingAudit.getValue());
        try {
            iFinanceCostService.updateStatusByApplyOrderId(applyId, ApplyOrderActionType.Create,userId,companyId,1);
        }catch (Exception e){
            logger.error("新增审批单失败"+e);
            //异常:费用致为未绑定状态 && state状态==0 && applyOrderId=0
//            updateCost(costIds,"",BoundStatusType.NoBound.getValue());
//            rpcUpdateCostState(costIds,ApplyStatus.Unknown.getValue());
            iFinanceCostService.relieveBinding(ApplyOrderActionType.Delete,applyId,costIds,companyId);
            throw new SaasException(GlobalResponseCode.InnerError);
        }
        Map<String, Object> writeoffMap = sloveWriteoffTemplate(apply);
        //生成电子单
        new Thread(new Runnable() {
            @Override
            public void run() {
                createElecApplyOrder(apply,writeoffMap);
            }
        }).start();
        return GlobalResponseCode.Success;
    }
    private void createElecApplyOrder(ApplyOrder applyOrder, Map writeoffMap){
        try {
            //生成pdf
            producePdf(applyOrder,writeoffMap);
        }catch (Exception e){
            logger.error("生成核销电子单失败："+e);
        }
    }

    private void updateCost(List<Integer> costIds,String applyId,int boundStatusType){
        if (costIds.size() > 0) {
            try {
                // 更新费用中申请单id
                iFinanceCostService.updateCost(costIds, applyId,boundStatusType);
            } catch (Exception e) {
                logger.warn("更新费用申请单id失败");
                throw new SaasException(GlobalResponseCode.InnerError);
            }
        }
    }

    private void rpcUpdateCostState(List<Integer> costIds,int state){
        try{
            if(null != costIds && costIds.size()>0){
                for(Integer id :costIds){
                    FinanceCostInfoVO financeCostInfoVO=new FinanceCostInfoVO();
                    financeCostInfoVO.setId(id);
                    financeCostInfoVO.setState(state);
                    int flag = iFinanceCostService.updateState(financeCostInfoVO);
                    logger.info("updateState result=:{}",flag);
                }
            }
        }catch (Exception e){
            logger.warn("rpcUpdateCostState e",e);
        }
    }

    /**
     * 申请单保存费用信息
     * @param applyContract
     */
    private void insertCost(ApplyBankIndividualContract applyContract) {
        if (applyContract != null) {
            String applyId = applyContract.getApply().getId();
            //1.1先查询
            ApplyTripInfoExample applyTripInfoExample=new ApplyTripInfoExample();
            applyTripInfoExample.createCriteria().andApplyOrderIdEqualTo(applyId);
            List<ApplyTripInfo> applyTripInfoList = applyTripInfoMapper.selectByExample(applyTripInfoExample);
            if(null ==applyTripInfoList || applyTripInfoList.size()==0){
                List<FinanceCostInfoVO> costList = applyContract.getApply().getCostList();
                if(null!=costList && costList.size()>0){
                    Date date = new Date();
                    ApplyTripInfo trip = new ApplyTripInfo();
                    String id = IDTool.CreateUniqueID();
                    trip.setId(id);
                    trip.setApplyOrderId(applyId);
                    trip.setCreateTime(date);
                    trip.setType(BizType.BankIndividualWriteoff.getValue());
                    trip.setStartCityId("0");
                    trip.setState(ApplyTripStatus.Available.getValue());
                    trip.setStartTime(date);
                    trip.setUpdateTime(date);
                    trip.setEstimatedAmount(new BigDecimal("0"));
                    ApplyCostContract applyCostContract=new ApplyCostContract();
                    List<ApplyCostInfo> applyCostInfoList=new ArrayList<>();
                    for(FinanceCostInfoVO f:costList){
                        ApplyCostInfo applyCostInfo=new ApplyCostInfo();
                        applyCostInfo.setCostId(f.getId());
                        applyCostInfoList.add(applyCostInfo);
                    }
                    applyCostContract.setApplyCostInfoList(applyCostInfoList);
                    trip.setTripContent(JSON.toJSONString(applyCostContract));
                    applyTripInfoMapper.insert(trip);
                }
            }else {
                //更新
                ApplyTripInfo applyTripInfo = applyTripInfoList.get(0);
                Date date = new Date();
                applyTripInfo.setStartTime(date);
                applyTripInfo.setUpdateTime(date);
                applyTripInfo.setCreateTime(date);
                ApplyCostContract applyCostContract=new ApplyCostContract();
                List<ApplyCostInfo> applyCostInfoList=new ArrayList<>();
                List<FinanceCostInfoVO> costList = applyContract.getApply().getCostList();
                if(null!=costList && costList.size()>0){
                    for(FinanceCostInfoVO f:costList){
                        ApplyCostInfo applyCostInfo=new ApplyCostInfo();
                        applyCostInfo.setCostId(f.getId());
                        applyCostInfoList.add(applyCostInfo);
                    }
                }
                applyCostContract.setApplyCostInfoList(applyCostInfoList);
                applyTripInfo.setTripContent(JSON.toJSONString(applyCostContract));
                applyTripInfoMapper.updateByPrimaryKey(applyTripInfo);
            }

        }
    }

    /**
     * 审批抄送人通知
     *
     * @param apply
     * @param senderUserId
     * @param action
     * @param desStatus
     */
    public void pushCCMessage(ApplyOrder apply, String senderUserId, ApplyLogAction action, ApplyStatus desStatus) {
        String applyId = apply.getId();
        ApplyType applyType = ApplyType.valueOf(apply.getType().intValue());
        List<ApplyOrderCopyTo> applyOrderCopyToList = applyAdapterMapper.getApplyOrderCopyToExtMapper(apply.getCompanyId()).queryApplyOrderCCByApplyOrderId(applyId);
        if (CollectionUtils.isEmpty(applyOrderCopyToList)) {
            return;
        }
        if (action.getValue() != ApplyLogAction.Submit.getValue() && desStatus.getValue() != ApplyStatus.Approved.getValue()) {
            return;
        }
        //提交审批时抄送通知类型判断
        if (action.getValue() == ApplyLogAction.Submit.getValue()) {
            Integer flowCcType = apply.getFlowCcType();
            if (flowCcType == null || flowCcType == 1) {
                return;
            }
        }
        //审批终审结束时抄送通知类型判断
        if (desStatus.getValue() == ApplyStatus.Approved.getValue()) {
            Integer flowCcType = apply.getFlowCcType();
            if (flowCcType == null || flowCcType == 2) {
                return;
            }
        }
        //业务类型
        if (CollectionUtils.isEmpty(applyOrderCopyToList)) {
            return;
        }
        List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.queryApprovalInfo(applyId);
        List<String> sponsorIdList = applyOrderLogList.stream().filter(applyOrderLog -> !applyOrderLog.getAction().equals(ApplyLogAction.Submit.getValue())).map(applyOrderLog -> applyOrderLog.getSponsorId()).distinct().collect(Collectors.toList());
        List<String> applyCopyList = applyOrderCopyToList.stream().map(applyOrderCopyTo -> applyOrderCopyTo.getUserId()).distinct().collect(Collectors.toList());
        applyCopyList.removeAll(sponsorIdList);
        applyOrderCopyToList = applyOrderCopyToList.stream().filter(applyOrderCopyTo -> CollectionUtils.isNotEmpty(applyCopyList) && applyCopyList.contains(applyOrderCopyTo.getUserId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(applyOrderCopyToList)) {
            return;
        }
        String name = "";
        EmployeeContract employee = iBaseOrganizationService.getEmployee(senderUserId, apply.getCompanyId());
        if (employee == null || StringUtils.isBlank(employee.getName())) {
            name = apply.getApplicantName();
        } else {
            name = employee.getName();
        }
        Map<String, Map<String, String>> languageMap = Maps.newHashMap();
        String pushContent = "";
        String messageContent = "";
        messageContent = StrUtils.formatString(CoreLanguage.Common_Message_VirtualCardSendMsg.getMessage(), name);
        languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_VirtualCardSendMsg.getLanguageCode(), name));
        pushContent = messageContent;
        for (ApplyOrderCopyTo applyOrderCopyTo : applyOrderCopyToList) {
            if (StringUtils.isBlank(applyOrderCopyTo.getUserId())) {
                continue;
            }
            String settingType = "9";
            Map<String, Object> msgInfo = new HashMap<>();
            msgInfo.put("myself", "false");
            msgInfo.put("view_type", "3");
            msgInfo.put("id", applyId);
            msgInfo.put("setting_type", settingType);
            msgInfo.put("apply_type", apply.getType().intValue());
            String linkDetail = JSONObject.toJSONString(msgInfo);
            //存消息
            ApplyLogAction msgAction = action;
            if (msgAction == ApplyLogAction.Approve && desStatus != ApplyStatus.Approved) {
                //固定审批流中间审批完成
                msgAction = ApplyLogAction.Submit;
            }
            if (msgAction == ApplyLogAction.Skip) {
                if (desStatus == ApplyStatus.Approved) {
                    msgAction = ApplyLogAction.Approve;
                } else {
                    msgAction = ApplyLogAction.Submit;
                }
            }
            String messageTitle = genMessageTitle(msgAction, languageMap);
            MessageSaveContract messageSaveContract = new MessageSaveContract();
            messageSaveContract.setMessage_type(MessageType.Apply.getCode());
            messageSaveContract.setTitle(messageTitle);
            messageSaveContract.setContent(messageContent);
            messageSaveContract.setBiz_order(applyId);
            messageSaveContract.setLink(linkDetail);
            messageSaveContract.setSender(senderUserId);
            messageSaveContract.setSender_type(SenderType.Person.getCode());
            messageSaveContract.setReceiver(applyOrderCopyTo.getUserId());
            messageSaveContract.setCompany_id(apply.getCompanyId());

            ApplyInfo messageApplyInfo = new ApplyInfo();
            messageApplyInfo.setApply_type(applyType.getBizType().getCode());
            messageApplyInfo.setApply_time(DateTimeTool.fromDateTimeToString(new Date()));
            messageApplyInfo.setApply_msg(messageContent);
            messageApplyInfo.setMyself(false);
            messageApplyInfo.setView_type(3);
            messageApplyInfo.setSetting_type(9);
            messageSaveContract.setApply_info(messageApplyInfo);
            try {
                messageService.saveMessage(messageSaveContract);
            } catch (SaasException ex) {
                //不处理
            }
            PushContract pushInfo = new PushContract();
            pushInfo.setTitle(messageTitle);
            pushInfo.setContent(pushContent);
            pushInfo.setUser_id(applyOrderCopyTo.getUserId());
            pushInfo.setMsg_type("0");
            pushInfo.setDesc(pushContent);
            pushInfo.setAlert(true);
            pushInfo.setMsg(linkDetail);
            pushInfo.setTitleMultilingualMap(languageMap.get(MessageLanguageEnum.TITLE.getCode()));
            pushInfo.setCommentMultilingualMap(languageMap.get(MessageLanguageEnum.CONTENT.getCode()));
            pushService.push(pushInfo);
        }
    }

    /**
     * 发送差旅push和通知
     *
     * @param apply
     * @param senderUserId
     * @param receiverUserId
     * @param action
     * @param desStatus
     * @param logId
     */
    @Override
   // @Transactional
    public void postMessage(ApplyOrder apply, String senderUserId, String receiverUserId, ApplyLogAction action, ApplyStatus desStatus, Integer logId, String comment) {
        String applyId = apply.getId();
        ApplyType applyType = ApplyType.valueOf(apply.getType().intValue());
        List<String> userIds = new ArrayList<>();
        userIds.add(senderUserId);
        userIds.add(receiverUserId);
        if (!userIds.contains(apply.getEmployeeId())) {
            userIds.add(apply.getEmployeeId());
        }
        List<IdNameContract> userNames = userService.getUserNameByIds(userIds, apply.getCompanyId());
        String employeeName = null;
        String senderUserName = null;
        for (IdNameContract userName : userNames) {
            if (senderUserId.equals(userName.getId())) {
                senderUserName = userName.getName();
                break;
            }
        }
        for (IdNameContract userName : userNames) {
            if (apply.getEmployeeId().equals(userName.getId())) {
                employeeName = userName.getName();
                break;
            }
        }
        if (StringUtils.isBlank(employeeName)) {
            employeeName = apply.getApplicantName();
        }
        ApplyLogAction msgAction = action;
        String msgSender = senderUserName;
        if (StringUtils.isBlank(msgSender) && apply.getEmployeeId().equals(senderUserId)) {
            msgSender = apply.getApplicantName();
        }
        if (msgAction == ApplyLogAction.Approve && desStatus != ApplyStatus.Approved) {
            //固定审批流中间审批完成
            msgSender = employeeName;
            msgAction = ApplyLogAction.Submit;
        }
        if (msgAction == ApplyLogAction.Skip) {
            if (desStatus == ApplyStatus.Approved) {
                msgAction = ApplyLogAction.Approve;
            } else {
                msgAction = ApplyLogAction.Submit;
            }
        }
        Map<String, Map<String, String>> languageMap = Maps.newHashMap();
        String messageContent = "";
        String pushContent = "";
        String messageTitle = genMessageTitle(msgAction, languageMap);
        messageContent = genMessageContent(msgSender, msgAction, applyType);
        pushContent = getPushContent(msgSender, msgAction, applyType, languageMap);

        boolean myself = true;
        if (msgAction == ApplyLogAction.Submit || msgAction == ApplyLogAction.Forward) {
            myself = false;
        }
        String settingType = "9";
        String viewType = myself ? "1" : "2";
        Map<String, Object> msgData = new HashMap<>();
        msgData.put("myself", myself ? "true" : "false");
        msgData.put("view_type", viewType);
        msgData.put("id", applyId);
        msgData.put("setting_type", settingType);
        String linkInfo = JSONObject.toJSONString(msgData); //跳转信息
        //存消息
        MessageSaveContract messageSaveContract = new MessageSaveContract();
        messageSaveContract.setMessage_type(MessageType.Apply.getCode());
        messageSaveContract.setTitle(messageTitle);
        messageSaveContract.setContent(messageContent);
        messageSaveContract.setBiz_order(applyId);
        messageSaveContract.setLink(linkInfo);
        messageSaveContract.setSender(senderUserId);
        messageSaveContract.setSender_type(SenderType.Person.getCode());
        messageSaveContract.setReceiver(receiverUserId);
        messageSaveContract.setCompany_id(apply.getCompanyId());

        ApplyInfo messageApplyInfo = new ApplyInfo();
        messageApplyInfo.setApply_type(applyType.getBizType().getCode());
        messageApplyInfo.setApply_time(DateTimeTool.fromDateTimeToString(new Date()));
        messageApplyInfo.setApply_msg(messageContent);
        messageApplyInfo.setMyself(myself);
        messageApplyInfo.setView_type(myself ? 1 : 2);
        messageApplyInfo.setSetting_type(9);
        if (logId != null && logId != 0) {
            messageApplyInfo.setLog_id(logId);
        }
        messageSaveContract.setApply_info(messageApplyInfo);

        try {
            messageService.saveMessage(messageSaveContract);
        } catch (SaasException ex) {
            //不处理
            logger.info("保存通知异常:" + ex.getMsg());
        }

        PushContract pushData = new PushContract();
        pushData.setTitle(messageTitle);
        pushData.setContent(pushContent);
        pushData.setUser_id(receiverUserId);
        pushData.setCompany_id(apply.getCompanyId());
        pushData.setMsg_type("0");
        pushData.setDesc(pushContent);
        pushData.setAlert(true);
        pushData.setMsg(linkInfo);
        pushData.setTitleMultilingualMap(languageMap.get(MessageLanguageEnum.TITLE.getCode()));
        pushData.setCommentMultilingualMap(languageMap.get(MessageLanguageEnum.CONTENT.getCode()));
        pushService.pushWithBudge(pushData);
    }

    private String genMessageContent(String sendUserName, ApplyLogAction action, ApplyType applyType) {
        String applyTypeDesc = applyType.getDesc();
        switch (action) {
            case Submit:
            case ReSubmit:
                return StrUtils.formatString(CoreLanguage.Common_Message_FlowSubmitMsg.getMessage(), sendUserName, applyTypeDesc);
            case Approve:
                return StrUtils.formatString(CoreLanguage.Common_Message_FlowApproveMsg.getMessage(), sendUserName, applyTypeDesc);
            case Forward:
                return StrUtils.formatString(CoreLanguage.Common_Message_FlowForwardMsg.getMessage(), sendUserName, applyTypeDesc);
            case Refuse:
                return StrUtils.formatString(CoreLanguage.Common_Message_FlowRefuseMsg.getMessage(), sendUserName, applyTypeDesc);
            case Revoke:
                return StrUtils.formatString(CoreLanguage.Common_Message_FlowRevokeMsg.getMessage(), sendUserName, applyTypeDesc);
            default:
                logger.error("生成apply push msg时发现没有处理的action:" + action.getValue() + ",系统暂时只返回了actionName:" + action.getDesc());
                return action.getDesc();
        }
    }

    private String genMessageTitle(ApplyLogAction action, Map<String, Map<String, String>> languageMap) {
        switch (action) {
            case Submit:
            case ReSubmit:
                languageMap.put(MessageLanguageEnum.TITLE.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Title_WaitCheck.getLanguageCode()));
                return CoreLanguage.Common_Title_WaitCheck.getMessage();
            case Approve:
                languageMap.put(MessageLanguageEnum.TITLE.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Title_Pass.getLanguageCode()));
                return CoreLanguage.Common_Title_Pass.getMessage();
            case Forward:
                languageMap.put(MessageLanguageEnum.TITLE.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Title_Forward.getLanguageCode()));
                return CoreLanguage.Common_Title_Forward.getMessage();
            case Refuse:
                languageMap.put(MessageLanguageEnum.TITLE.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Title_HasRefuse.getLanguageCode()));
                return CoreLanguage.Common_Title_HasRefuse.getMessage();
            case Revoke:
                languageMap.put(MessageLanguageEnum.TITLE.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Title_HasRevoke.getLanguageCode()));
                return CoreLanguage.Common_Title_HasRevoke.getMessage();
            case Overtime:
                languageMap.put(MessageLanguageEnum.TITLE.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Title_Timeout.getLanguageCode()));
                return CoreLanguage.Common_Title_Timeout.getMessage();
            default:
                logger.error("生成apply push title时发现没有处理的action:" + action.getValue() + ",系统暂时只返回了actionName:" + action.getDesc());
                return action.getDesc();
        }
    }

    private String getPushContent(String sendUserName, ApplyLogAction action, ApplyType applyType, Map<String, Map<String, String>> languageMap) {
        String applyTypeDesc = applyType.getDesc();
        switch (action) {
            case Submit:
            case ReSubmit:
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_FlowSubmitMsg.getLanguageCode(), sendUserName, applyTypeDesc));
                return StrUtils.formatString(CoreLanguage.Common_Message_FlowSubmitMsg.getMessage(), sendUserName, applyTypeDesc);
            case Approve:
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_FlowApproveMsg.getLanguageCode(), sendUserName, applyTypeDesc));
                return StrUtils.formatString(CoreLanguage.Common_Message_FlowApproveMsg.getMessage(), sendUserName, applyTypeDesc);
            case Forward:
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_FlowForwardMsg.getLanguageCode(), sendUserName, applyTypeDesc));
                return StrUtils.formatString(CoreLanguage.Common_Message_FlowForwardMsg.getMessage(), sendUserName, applyTypeDesc);
            case Refuse:
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_FlowRefuse.getLanguageCode(), sendUserName, applyTypeDesc));
                return StrUtils.formatString(CoreLanguage.Common_Message_FlowRefuse.getMessage(), sendUserName, applyTypeDesc);
            case Revoke:
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_FlowRevokeApplyMsg.getLanguageCode(), sendUserName, applyTypeDesc));
                return StrUtils.formatString(CoreLanguage.Common_Message_FlowRevokeApplyMsg.getMessage(), sendUserName, applyTypeDesc);
            default:
                logger.error("生成apply push msg时发现没有处理的action:" + action.getValue() + ",系统暂时只返回了actionName:" + action.getDesc());
                return action.getDesc();
        }
    }

    //@Transactional(value = "saas")
    private void insertApproverMap(String applyId, String approverId, Date time) {
        ApplyApproverMapExample example = new ApplyApproverMapExample();
        example.createCriteria().andApplyIdEqualTo(applyId).andApproverIdEqualTo(approverId);
        List<ApplyApproverMap> list = applyApproverMapMapper.selectByExample(example);
        if (ObjUtils.isEmpty(list)) {
            ApplyApproverMap approverMap = new ApplyApproverMap();
            approverMap.setApplyId(applyId);
            approverMap.setApproverId(approverId);
            approverMap.setCreateTime(time);
            applyApproverMapMapper.insertSelective(approverMap);
        }
    }

    /**
     * 整理申请单数据
     *
     * @param applyContract
     */
    private void clearApplyData(ApplyBankIndividualContract applyContract) {
        ApplyOrderV2Contract applyorderContract = applyContract.getApply();
        if (applyorderContract.getBudget() == null) {
            applyorderContract.setBudget(BigDecimal.valueOf(0));
        }
        if (CollectionUtils.isEmpty(applyContract.getTrip_list())) {
            applyContract.setTrip_list(null);
        }
        if (applyorderContract.getApprover_id() == null) {
            applyorderContract.setApprover_id("");
        }

    }

    /**
     * 检查数据有效性
     *
     * @param applyContract
     * @return
     */
    private GlobalResponseCode checkApplyData(String token, ApplyBankIndividualContract applyContract, String employeeId, String companyId,String clientHeadVersion) {
            //检查申请单
        SysCommonConfig sysCommonConfig= iSysCommonConfigService.getSysConfig(MethodCodeType.WriteoffCreate.getValue(), MethodType.Http.getValue(),clientHeadVersion);
        if (ObjUtils.isNotEmpty(sysCommonConfig)){
            throw new SaasException(sysCommonConfig.getCode(),sysCommonConfig.getMsg(),sysCommonConfig.getType());
        }
        ApplyOrderV2Contract apply = applyContract.getApply();
        if (apply == null) {
            return GlobalResponseCode.ApplyIsNull;
        }
        if (apply.getState() == null) {
            return GlobalResponseCode.ParameterError;
        }
        if (apply.getType() == null
                || ApplyType.valueOf(apply.getType()) == ApplyType.Unknown
                ||!ApplyType.valueOf(apply.getType()).equals(ApplyType.VirtualCardWriteOff)) {
            return GlobalResponseCode.ApplyTypeInvalid;
        }
        // 校验费用是否已经存在绑定状态？
        List<FinanceCostInfoVO> costListData = apply.getCostList();
        if(null!=costListData && costListData.size()>0){
            //备用金限制
            PettyConfigRpcDto configRpcDto= iMessageSetupRpcService.queryPettyConfigByCompanyId(companyId);
            logger.info("获取备用金限制配置："+JsonUtils.toJson(configRpcDto));
            if (ObjUtils.isEmpty(configRpcDto)){
                return GlobalResponseCode.NotFound;
            }
            if (configRpcDto.getVirtualCardType()==2&&VersionTool.compare(clientHeadVersion, "4.4.0") < 0){
                return GlobalResponseCode.ApplyVersoinERROR;
            }
            Set pettySet= Sets.newHashSet();
            for(FinanceCostInfoVO f:costListData){
                FinanceCostInfoVO financeCostInfoVO = iFinanceCostService.getCostInfoByCostId(f.getId());
                if(null==financeCostInfoVO){
                    return GlobalResponseCode.ApplyWriteoffCostListIsNull;
                }
                //绑定状态 && 不是同一个审批单
                if(!apply.getId().equals(financeCostInfoVO.getApplyOrderId()) &&financeCostInfoVO.getBoundStatus()==BoundStatusType.AlreadyBound.getValue()){
                    return GlobalResponseCode.ApplyWriteoffCostListIsUse;
                }
                //低于4.7.6版本
//                if(VersionTool.compare(clientHeadVersion, "4.7.6") < 0){
//                    //校验费用信息是否完整
//                    //费用类别
//                    if(ObjUtils.isBlank(financeCostInfoVO.getCostCategoryId())){
//                        return GlobalResponseCode.ApplyWriteoffCostCategoryIsNull;
//                    }
//                    if(VersionTool.compare(clientHeadVersion, "4.2.1") <= 0){
//                        //事由
//                        if(ObjUtils.isBlank(financeCostInfoVO.getCostCauseId())){
//                            return GlobalResponseCode.ApplyWriteoffCostCategoryIsNull;
//                        }
//                    }
//                    //费用描述
//                    if(ObjUtils.isBlank(financeCostInfoVO.getCostDesc())){
//                        return GlobalResponseCode.ApplyWriteoffCostCategoryIsNull;
//                    }
//                    //发票
//                    if(ObjUtils.isBlank(financeCostInfoVO.getInvoiceList())){
//                        return GlobalResponseCode.ApplyWriteoffCostCategoryIsNull;
//                    }
//                    //交易记录
//                    if(ObjUtils.isBlank(financeCostInfoVO.getTradeInformationList())){
//                        return GlobalResponseCode.ApplyWriteoffCostCategoryIsNull;
//                    }
//                }
                Set<String> pettyRelationIds= financeCostInfoVO.getPettyIdSet();
                //备用金列表
                if (ObjUtils.isNotEmpty(pettyRelationIds)){
                    //费用备用金超限制
                    if (configRpcDto.getVirtualCardType()==2&&configRpcDto.getCostSinglePettyLimit()==1&&pettyRelationIds.size()>1){
                        return GlobalResponseCode.PettyLimitSingle;
                    }
                    pettySet.addAll(pettyRelationIds);
                }
            }
            //大于等于4.7.6版本.读取配置文件校验
            List<Integer> costIdList = apply.getCostList().stream().map(FinanceCostInfoVO::getId).collect(Collectors.toList());
            //if(VersionTool.compare(clientHeadVersion, "4.7.6")>=0){
                boolean flag = iFinanceCostService.checkCostFieldConfig(companyId, costIdList,clientHeadVersion);
                if(!flag){
                    return GlobalResponseCode.ApplyWriteoffCostCategoryIsNull;
                }
            //}
            //核销单单笔备用金,开启 && 备用金 >1
            if (configRpcDto.getVirtualCardType()==2&&configRpcDto.getWriteoffApplySinglePettyLimit()==1&&pettySet.size()>1){
                return GlobalResponseCode.PettyLimitSingle;
            }

        }

        Integer state=apply.getState();
        //草稿
        if (state==ApplyStatus.Draft.getValue()){
            if (StringUtils.isNotBlank(apply.getApply_reason_desc()) && apply.getApply_reason_desc().length() > 100) {
                return GlobalResponseCode.ApplyMallReasonInvalid;
            }
            if (ObjUtils.isNotEmpty(apply.getBudget())&&apply.getBudget().compareTo(BigDecimal.ZERO)<0){
                return  GlobalResponseCode.ApplyTripBudgetError;
            }
        }else {
            if (StringUtils.isBlank(apply.getApply_reason_desc()) || apply.getApply_reason_desc().length() > 200) {
                return GlobalResponseCode.ApplyReasonDescIsError;
            }
            //审批类型 1.弹性审批流 2.固定审批流 3.分条件审批流
            CompanyApplyType applyType = CompanyApplyType.valueOf(apply.getFlow_type());
            if (applyType.getValue() == CompanyApplyType.Unknown.getValue()) {
                return GlobalResponseCode.ApplyFlowTypeError;
            }
            if (applyType.getValue() == CompanyApplyType.Elastic.getValue() && apply.getState() == ApplyStatus.PendingAudit.getValue() && StringUtils.isBlank(apply.getApprover_id())) {
                //申请单必须指定一个审批人
                return GlobalResponseCode.ApplyApproveIdInvalid;
            }
            if (applyType.getValue() == CompanyApplyType.Elastic.getValue() && employeeId.equals(apply.getApprover_id())) {
                //审批人不能是自己
                return GlobalResponseCode.ApplyApproverCannotBeSelf;
            }
            Integer applyCostAttributionCategory = apply.getCost_attribution_category();
            String costAttributionId = apply.getCost_attribution_id();
            String costAttributionName = apply.getCost_attribution_name();
            if (applyCostAttributionCategory == null || StringUtils.isBlank(costAttributionId) || StringUtils.isBlank(costAttributionName)) {
                return GlobalResponseCode.CostAttributionNameIsNull;
            }
            ApplySetupContract applySetupCostAttributionContract = messageSetupService.queryCompanyApplyCostAttributionConfig(companyId);
            Integer costAttributionCategory = applySetupCostAttributionContract.getCost_attribution_category();
            if (applyCostAttributionCategory == null) {
                applyCostAttributionCategory = 1;
            }
            if (costAttributionCategory == 1 && applyCostAttributionCategory != 1) {
                return GlobalResponseCode.CostAttributionCategoryIsDept;
            }
            if (costAttributionCategory == 2 && applyCostAttributionCategory != 2) {
                return GlobalResponseCode.CostAttributionCategoryIsCostCenter;
            }
        }

//        List<String> orderList = Lists.newArrayList();
//        BigDecimal tripTotalMoney = BigDecimal.valueOf(0);
//        for (ApplyTripInfoContract applyTripInfoContract : tripList) {
//            tripTotalMoney = tripTotalMoney.add(applyTripInfoContract.getEstimated_amount());
//            List<Map<String, Object>> tradeInformationList = applyTripInfoContract.getTrade_information();
//            for (Map<String, Object> tradeInformation : tradeInformationList) {
//                //分贝订单号（交易编号）
//                String orderId = ObjUtils.toString(tradeInformation.get("orderId"));
//                orderList.add(orderId);
//                tradeInformationList = tradeInformationList.stream().sorted(Comparator.comparing(tradeInformationMap -> ObjUtils.toLong(tradeInformationMap.get("costCategoryId")))).collect(Collectors.toList());
//                applyTripInfoContract.setTrade_information(tradeInformationList);
//                List<FinancialManagementVO> financialManagementVOList = Lists.newArrayList();
//                FinancialManagementVO financialManagementVO = new FinancialManagementVO();
//                financialManagementVO.setId(ObjUtils.toInteger(tradeInformation.get("costCategoryId")));
//                financialManagementVO.setName(ObjUtils.toString(tradeInformation.get("costCategory")));
//                financialManagementVOList.add(financialManagementVO);
//                Integer replaceCostCategoryId = ObjUtils.toInteger(tradeInformation.get("replaceCostCategoryId"));
//                String replaceCostCategory = ObjUtils.toString(tradeInformation.get("replaceCostCategory"));
//                if (ObjUtils.isNotEmpty(replaceCostCategoryId) && replaceCostCategoryId != 0) {
//                    FinancialManagementVO financialManagementVOInfo = new FinancialManagementVO();
//                    financialManagementVOInfo.setId(replaceCostCategoryId);
//                    financialManagementVOInfo.setName(replaceCostCategory);
//                    financialManagementVOList.add(financialManagementVOInfo);
//                }
//                String userName = null;
//                EmployeeContract employee = iBaseOrganizationService.getEmployee(employeeId, companyId);
//                if (employee != null) {
//                    userName = employee.getName();
//                }
//                List<FinancialManagementVO> financialManagementVOS = iMetaService.queryCategory(token, employeeId, userName, financialManagementVOList);
//                if (financialManagementVOList.size() != financialManagementVOS.size()) {
//                    return GlobalResponseCode.ApplyWriteoffCostCategoryIsError;
//                }
//            }
//        }
//        BigDecimal budget = apply.getBudget().divide(ObjUtils.toBigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
//        if (tripTotalMoney.setScale(2, BigDecimal.ROUND_HALF_UP).compareTo(budget) != 0) {
//            return GlobalResponseCode.ApplyTripBudgetError;
//        }
//        List<String> notAvailableList = iBankOrderService.checkApplyOrder(orderList);
//        if (CollectionUtils.isNotEmpty(notAvailableList)) {
//            return GlobalResponseCode.ApplyWriteoffIsResubmit;
//        }
        return GlobalResponseCode.Success;
    }

    private ApplyV2Contract getApplyByIdAndUserIdAndCompanyId(String applyId, String userId, String companyId, UserRole userRole, String token, String clientVersion) throws SaasException {
        ApplyOrder applyModel = getApplyOrderModelByIdAndUserIdAndCompanyId(applyId, userId, companyId, userRole);
        if (applyModel == null) {
            applyModel = applyOrderExtMapper.getByIdAndApproverIdAndCompanyId(applyId, userId, companyId);
        }
        if (applyModel == null) {
            List<ApplyOrder> ccApplyOrderCountByApplyIdList = applyOrderExtMapper.getCCApplyOrderCountByApplyId(userId, companyId, 0);
            if (CollectionUtils.isEmpty(ccApplyOrderCountByApplyIdList)) {
                return null;
            }
            List<String> cclist = Lists.newArrayList();
            for (ApplyOrder applyOrder : ccApplyOrderCountByApplyIdList) {
                cclist.add(applyOrder.getId());
            }
            if (cclist.contains(applyId)) {
                applyModel = applyOrderMapper.selectByPrimaryKey(applyId);
            } else {
                return null;
            }
        }
        if (ObjUtils.isNotEmpty(applyModel.getDeleteStatus())&&applyModel.getDeleteStatus()==1){
            throw new SaasException(GlobalResponseCode.ApplyOrderIsDelete);
        }
        ApplyOrderV2Contract apply = ApplyOrderV2Contract.FromModel(applyModel);
        // 4.7.1 配置项目编码显示
        ApplySetupContract applySetupContract = messageSetupService.queryCompanyApplyCostAttributionConfig(companyId);
        if (applySetupContract != null
                && apply.getCost_attribution_category() == CostAttributionCategory.CostCenter.getKey()
                && SaasMessageConstant.IS_CHECKED_TRUE == applySetupContract.getApply_show_project_code()) {
            apply.setCost_attribution_code(applyModel.getCostAttributionCode());
        }
        //核销单和报销单是否打印模板
        if (applySetupContract != null) {
            apply.setAllow_apply_download(applySetupContract.getAllow_apply_download());
        }
        //返回当前时间戳
        ApplyV2Contract applyContract = new ApplyV2Contract();
        applyContract.setApply(apply);
        List<ApplyTripInfoContract> tripList = getTripListByApplyOrderId(applyId, apply);
        applyContract.setTrip_list(tripList);
        if (apply.getType()==ApplyType.VirtualCardWriteOff.getValue()){
            if (ObjUtils.isNotBlank(clientVersion)){
                SysCommonConfig sysCommonConfig= iSysCommonConfigService.getSysConfig(MethodCodeType.GetWriteoffDetail.getValue(), MethodType.Http.getValue(),clientVersion);
                if (ObjUtils.isNotEmpty(sysCommonConfig)){
                    throw new SaasException(sysCommonConfig.getCode(),sysCommonConfig.getMsg(),sysCommonConfig.getType());
                }
            }
            List<Integer> costIds = selectCostIdByApplyOrderId(applyId);
            List<FinanceCostInfoVO> costList= iFinanceCostService.getCostListById(costIds);
            //List<FinanceCostInfoVO> costList= iFinanceCostService.getCostList(Lists.newArrayList(applyId));
            if (ObjUtils.isNotEmpty(costList)){
                //只有同意的才会有回票
                if (apply.getState()==ApplyStatus.Approved.getValue()){
                    List<Map> alreadyReturnList=Lists.newArrayList();
                    List<Map> notReturnList=Lists.newArrayList();
                    Map downloadMap=Maps.newHashMap();
                    downloadMap.put("applyId",apply.getId());
                    downloadMap.put("type",1);
                    downloadMap.put("downloadLink",apply.getDownload_link());
                    if (apply.getReturnDownload()==1){
                        alreadyReturnList.add(downloadMap);
                    }else {
                        notReturnList.add(downloadMap);
                    }
                    for (FinanceCostInfoVO costInfoVO:costList){
                        List<Map> invoiceList=costInfoVO.getInvoiceList();
                        if (ObjUtils.isNotEmpty(invoiceList)){
                            invoiceList.forEach(v->{
                                if (ObjUtils.isNotEmpty(v.get("returnTicket"))&&ObjUtils.toInteger(v.get("returnTicket"))==1){
                                    alreadyReturnList.add(v);
                                }else {
                                    notReturnList.add(v);
                                }
                            });
                        }
                    }
                    applyContract.setAlreadyReturnList(alreadyReturnList);
                    applyContract.setNotReturnList(notReturnList);
                    applyContract.setAlreadyReturnCount(alreadyReturnList.size());
                    applyContract.setAllNeedReturnCount(alreadyReturnList.size()+notReturnList.size());
                }
                //金额
                if (apply.getState()==ApplyStatus.Draft.getValue()){
                    BigDecimal totalAmount = costList.stream().map(FinanceCostInfoVO::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    applyContract.getApply().setBudget(totalAmount.multiply(new BigDecimal(100)));
                }
            }
            //老版本
            if (VersionTool.compare(clientVersion, "4.2.0") < 0) {
                //申请单 是撤销||拒绝单
            if(apply.getState()==ApplyStatus.Backout.getValue()||apply.getState()==ApplyStatus.Return.getValue()){
                // 费用是 绑定 || 删除(不会返回)
                Iterator<FinanceCostInfoVO> iterator = costList.iterator();
                while (iterator.hasNext()){
                    FinanceCostInfoVO next = iterator.next();
                    if(next.getBoundStatus()==BoundStatusType.AlreadyBound.getValue()){
                        iterator.remove();
                    }
                }
                applyContract.setCostList(costList);
            }else {
                applyContract.setCostList(costList);
            }
            }else {
                applyContract.setCostList(costList);
            }
        }
        return applyContract;
    }

    /**
     * 根据申请单id查询费用ids
     * @param applyId
     * @return
     */
    private List<Integer> selectCostIdByApplyOrderId(String applyId){
        List<Integer> result=new ArrayList<>();
        if(StringUtils.isEmpty(applyId)){
            return result;
        }

        ApplyTripInfoExample applyTripInfoExample=new ApplyTripInfoExample();
        applyTripInfoExample.createCriteria().andApplyOrderIdEqualTo(applyId)
                .andTypeEqualTo(BizType.BankIndividualWriteoff.getValue());
        List<ApplyTripInfo> applyTripInfoList = applyTripInfoMapper.selectByExample(applyTripInfoExample);
        if(null!=applyTripInfoList && applyTripInfoList.size()==1){
            ApplyTripInfo applyTripInfo = applyTripInfoList.get(0);
            ApplyCostContract applyCostContract = JSON.parseObject(applyTripInfo.getTripContent(), ApplyCostContract.class);
            if(null !=applyCostContract){
                List<ApplyCostInfo> applyCostInfoList = applyCostContract.getApplyCostInfoList();
                if(null !=applyCostInfoList && applyCostInfoList.size()>0){
                    applyCostInfoList.forEach( e ->{
                        result.add(e.getCostId());
                    });
                }
                return result;
            }
        }
        return result;
    }

    private List<ApplyTripInfoContract> getTripListByApplyOrderId(String applyOrderId, ApplyOrderV2Contract apply) {
        List<ApplyTripInfo> tripList = applyTripInfoExtMapper.selectListByApplyOrderId(applyOrderId);
        List<ApplyTripInfoContract> tripContractList = new ArrayList<ApplyTripInfoContract>();
        if (ObjUtils.isNotEmpty(tripList)){
            for (ApplyTripInfo trip : tripList) {
                ApplyTripInfoContract contract = ApplyTripInfoContract.FromModel(trip);
                Map<String, Object> tradeInformationMap = JSON.parseObject(trip.getTripContent(), Map.class);
                List<Map<String, Object>> tradeInformationList = (List<Map<String, Object>>) tradeInformationMap.get("trade_information_list");
                contract.setTrade_information(tradeInformationList);
                tripContractList.add(contract);
            }
        }
        return tripContractList;
    }

    /**
     * 获取审批是否是终审节点
     *
     * @param apply
     * @param applyFlowUserItems
     * @param skippedApprverIds
     * @param couldFinalApprove
     * @return
     * @throws SaasException
     */
    private FindNextApproverIdAndStatusResult findStatusInFlow(ApplyOrder apply, List<ApplyFlowUserItem> applyFlowUserItems, List<String> skippedApprverIds, boolean couldFinalApprove) throws SaasException {
        String approverId = null;
        String receiverId = null;
        ApplyStatus finalStatus;
        Integer applyStatus = null;
        int remainIndex = applyFlowUserItems.size();
        for (int i = 0; i < applyFlowUserItems.size(); i++) {
            if (ValueTool.areEqual(applyFlowUserItems.get(i).getStatus(), ApplyFlowUserItemStatus.Waiting.getValue())) {
                remainIndex = i;
                break;
            }
        }
        //找下一个审批人
        boolean findNextApprover = false;
        // 是否是最后一名审批人
        boolean finnalApprover = true;
        for (int i = remainIndex; i < applyFlowUserItems.size(); i++) {
            ApplyFlowUserItem item = applyFlowUserItems.get(i);
            if (ValueTool.areEqual(item.getStatus(), ApplyFlowUserItemStatus.Waiting.getValue())) {
                finnalApprover = false;
                if (userService.isUserInCompany(item.getUserId(), apply.getCompanyId())) {
                    findNextApprover = true;
                    receiverId = item.getUserId();
                    approverId = receiverId;
                    applyStatus = 1;
                    break;
                } else {
                    skippedApprverIds.add(item.getUserId());
                }
            }
        }
        if (finnalApprover && couldFinalApprove) {
            //当前审批人已经是最后一个审批人
            finalStatus = ApplyStatus.Approved;
            receiverId = apply.getEmployeeId();
            applyStatus = 2;
        } else {
            finalStatus = ApplyStatus.PendingAudit;
            if (!findNextApprover) {
                //后面的审批人都已不存在，直接跳到授权负责人
                List<String> superAdminEmployeeIds = customRoleService.getEmployeeIdByRoleId(Integer.toString(UserRole.CompanySuperAdmin.getValue()), apply.getCompanyId());
                if (superAdminEmployeeIds == null || superAdminEmployeeIds.size() == 0) {
                    throw new SaasException(GlobalResponseCode.ApplyCanotApproveBecauseHasNoSuperAdmin);
                } else {
                    receiverId = superAdminEmployeeIds.get(0);
                    approverId = receiverId;
                    applyStatus = 3;
                }
            }
        }
        FindNextApproverIdAndStatusResult result = new FindNextApproverIdAndStatusResult();
        result.setApproverId(approverId);
        result.setReceiverId(receiverId);
        result.setFinnalStatus(finalStatus);
        result.setApplyStatus(applyStatus);
        return result;
    }

    private ApplyOrder getApplyOrderModelByIdAndUserIdAndCompanyId(String id, String userId, String companyId, UserRole userRole) {
        if (StringTool.isNullOrEmpty(id) || StringTool.isNullOrEmpty(userId) || StringTool.isNullOrEmpty(companyId)) {
            return null;
        }
        ApplyOrder apply = applyOrderMapper.selectByPrimaryKey(id);
        if (apply == null) {
            return null;
        }
        if (apply.getCompanyId() == null || !apply.getCompanyId().equals(companyId)) {
            return null;
        }
        if (!apply.getEmployeeId().equals(userId)) {
            //如果不是本人，再判断一下userrole是不是分贝管理员或者企业管理员
            if (userRole == null || (userRole != UserRole.CompanyAdmin && userRole != UserRole.CompanySuperAdmin && userRole != UserRole.FbAdmin)) {
                return null;
            }
        }
        return apply;
    }

    private List<ApplyOrderLogContract> getLogsByApplyId(String applyId, String companyId) {
        List<ApplyOrderLog> logs = applyOrderLogExtMapper.selectListByApplyId(applyId);
        logs = logs.stream().filter(logInfo -> logInfo.getAction() != ApplyLogAction.CreateDraft.getValue() && logInfo.getAction() != ApplyLogAction.ModifyDraft.getValue()).collect(Collectors.toList());
        //查询是否有已拒绝、已撤销和超时状态的审批日志
        List<ApplyOrderLog> destinationLogList = logs.stream().filter(logInfo -> logInfo.getAction() == ApplyLogAction.Refuse.getValue() || logInfo.getAction() == ApplyLogAction.Revoke.getValue() || logInfo.getAction() == ApplyLogAction.Overtime.getValue()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(destinationLogList)) {
            logs = logs.stream().filter(logInfo -> logInfo.getAction() != ApplyLogAction.Approval.getValue() && logInfo.getAction() != ApplyLogAction.Unknown.getValue()).collect(Collectors.toList());
        }
        List<ApplyOrderLogContract> result = getLogContractList(logs, companyId);
        return result;
    }

    private void appendApplyDetail(String token, ApplyV2Contract detail, String userId) {
        ApplyOrderV2Contract order = detail.getApply();
        //可操作权限
        Integer operateAuth = genApplyOperateAuth(order, userId);
        order.setOperate_auth(operateAuth);
        //处理审批流设置
        appendFlowData(order);
    }

    private void appendFlowData(ApplyOrderV2Contract applyOrderContract) {
        CompanyApplyFlowSetV2RequestContract flowSet = null;
        if (applyOrderContract.getFlow_type() == CompanyApplyType.Flow.getValue() || applyOrderContract.getFlow_type() == CompanyApplyType.CONDITIONAL.getValue()) {
            flowSet = applyFlowV2Service.getFlowByApplyId(applyOrderContract.getId(), applyOrderContract.getCompany_id());
            flowSet = applyFlowV2Service.getFlowCCByApplyId(applyOrderContract.getId(), flowSet, applyOrderContract.getCompany_id());
        }
        if (applyOrderContract.getFlow_type() == CompanyApplyType.Elastic.getValue()) {
            flowSet = applyFlowV2Service.getFlowCCByApplyId(applyOrderContract.getId(), flowSet, applyOrderContract.getCompany_id());
            flowSet.setCompany_apply_type(1);
        }
        if (flowSet != null) {
            applyOrderContract.setFlow_type(flowSet.getCompany_apply_type());
            applyOrderContract.setFlow(flowSet);
        }
    }

    /**
     * 判断操作权限
     *
     * @param order
     * @param userId
     * @return
     */
    private Integer genApplyOperateAuth(ApplyOrderV2Contract order, String userId) {
        ApplyStatus orderStatus = ApplyStatus.valueOf(order.getState());
        if (order.getEmployee_id().equals(userId)) {
            //自己的申请单
            if (orderStatus == ApplyStatus.PendingAudit) {
                if (userId.equals(order.getApprover_id())) {
                    return ApplyLogAction.Approve.getValue() + ApplyLogAction.Forward.getValue() + ApplyLogAction.Refuse.getValue() + ApplyLogAction.Revoke.getValue();
                }
                //状态为待审核时,用户可撤销申请
                return ApplyLogAction.Revoke.getValue();
                //} else if (orderStatus == ApplyStatus.Return || orderStatus == ApplyStatus.Draft) {
            } else if (orderStatus == ApplyStatus.Draft) {
                //状态为被驳回或草稿时,用户可提交申请或者修改
                return ApplyLogAction.Submit.getValue() + ApplyLogAction.ModifyDraft.getValue() + ApplyLogAction.Delete.getValue();
            }else if (orderStatus == ApplyStatus.Backout){
                return ApplyLogAction.Delete.getValue()+ApplyLogAction.ReSubmit.getValue();
            }
            else if (orderStatus == ApplyStatus.Return){
                return ApplyLogAction.ReSubmit.getValue();
            }
        } else {
            //是需要自己审批,或者自己已经操作过的审批单
            if (orderStatus == ApplyStatus.PendingAudit || orderStatus == ApplyStatus.Transfer) {
                //只有待自己处理的审批单才可操作
                if (userId.equals(order.getApprover_id())) {
                    return ApplyLogAction.Approve.getValue() + ApplyLogAction.Forward.getValue() + ApplyLogAction.Refuse.getValue();
                }
            }
        }
        //不能做任何操作
        return 0;
    }

    private String genApplyLogDisplayString(ApplyOrderLog log, List<IdNameContract> userNames) {
        StringBuilder sb = new StringBuilder();
        IdNameContract sponsor = getNameFromListById(userNames, log.getSponsorId());
        if (sponsor != null) {
            sb.append(sponsor.getName() + " ");
        }
        ApplyLogAction action = ApplyLogAction.valueOf(log.getAction());
        String actionName = "";
        if (action == ApplyLogAction.Unknown) {
            actionName = "";
        } else {
            actionName = action.getDesc();
        }
        sb.append(actionName);
        if (action == ApplyLogAction.Forward
                || action == ApplyLogAction.Submit
                || action == ApplyLogAction.ReSubmit
                ) {
            sb.append(CoreLanguage.Common_Value_Give.getMessage() + " ");
            if (log.getReceiverId() != null) {
                IdNameContract receiver = getNameFromListById(userNames, log.getReceiverId());
                if (receiver != null) {
                    sb.append(receiver.getName());
                }
            }
        }
        return sb.toString();
    }

    private List<ApplyOrderLogContract> getLogContractList(List<ApplyOrderLog> logs, String companyId) {
        if (logs == null || logs.size() == 0) {
            return new ArrayList<>();
        }
        List<ApplyOrderLogContract> result = new ArrayList<>();
        List<String> userIds = new ArrayList<>();
        for (ApplyOrderLog log : logs) {
            if (!userIds.contains(log.getSponsorId())) {
                userIds.add(log.getSponsorId());
            }
            if (log.getReceiverId() != null && !userIds.contains(log.getReceiverId())) {
                userIds.add(log.getReceiverId());
            }
        }
        List<IdNameContract> userNames = userService.getUserNameByIds(userIds, companyId);
        for (ApplyOrderLog log : logs) {
            ApplyOrderLogContract logContract = genLogContract(log, userNames);
            if (logContract != null && logContract.getAction() == ApplyLogAction.Unknown.getValue() && StringUtils.isBlank(logContract.getSponsor()) && StringUtils.isBlank(logContract.getLog())) {
                logContract.setAction(ApplyLogAction.Skip.getValue());
                logContract.setCheck_reason(CoreLanguage.Common_Value_ApprovalPersonDelete.getMessage());
            }
            result.add(logContract);
        }
        return result;
    }

    private ApplyOrderLogContract genLogContract(ApplyOrderLog log, List<IdNameContract> userNames) {
        ApplyOrderLogContract contract = ApplyOrderLogContract.FromModel(log);
        String logContent = genApplyLogDisplayString(log, userNames);
        contract.setLog(logContent);
        if (StringUtils.isNotBlank(log.getSponsorId())) {
            for (IdNameContract idNameContract : userNames) {
                if (idNameContract.getId().equals(log.getSponsorId())) {
                    contract.setSponsor(idNameContract.getName());
                }
            }
        }
        if (StringUtils.isNotBlank(log.getReceiverId())) {
            for (IdNameContract idNameContract : userNames) {
                if (idNameContract.getId().equals(log.getReceiverId())) {
                    contract.setReceiver(idNameContract.getName());
                }
            }
        }
        return contract;
    }

    private IdNameContract getNameFromListById(List<IdNameContract> list, String id) {
        if (list == null || id == null) {
            return null;
        }
        for (IdNameContract item : list) {
            if (item.getId().equals(id)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 审批单详情
     *
     * @param token
     * @param applyId
     * @param userId
     * @param companyId
     * @param userRole
     * @return
     * @throws SaasException
     */
    @Override
    public ApplyV2Contract getDetail(String token, String applyId, String userId, String companyId, UserRole userRole, String clientVersion) throws SaasException {
        FlowCheckUtil.check(companyId, clientVersion);
        ApplyV2Contract apply = getApplyByIdAndUserIdAndCompanyId(applyId, userId, companyId, userRole, token, clientVersion);
        if (apply == null) {
            return null;
        }
        //1、申请人及审批人的姓名及部门 2、可操作权限
        appendApplyDetail(token, apply, userId);
        List<ApplyOrderLogContract> logs = getLogsByApplyId(applyId, companyId);
        for (ApplyOrderLogContract applyOrderLogContract : logs) {
            if (StringUtils.isBlank(applyOrderLogContract.getSponsor()) && apply.getApply().getEmployee_id().equals(applyOrderLogContract.getSponsor_id())) {
                if (applyOrderLogContract.getAction() != ApplyLogAction.Skip.getValue()) {
                    applyOrderLogContract.setSponsor(apply.getApply().getApplicant_name());
                }
            }
        }
        apply.setLog_list(logs);
        return apply;
    }

    /**
     * 订单审批单（同意）
     *
     * @param approveModel
     * @param userId
     * @param ip
     * @return
     */
    @Override
    //@Transactional(value = "saas")
    public GlobalResponseCode approve(ApplyApproveContract approveModel, String userId, String companyId, String ip, String clientVersion, String source) throws SaasException {
        FlowCheckUtil.check(companyId, clientVersion, source);
        if (approveModel == null) {
            throw new SaasException(GlobalResponseCode.ApplyIsNull);
        }
        if (StringTool.isNullOrEmpty(approveModel.getApply_id())) {
            throw new SaasException(GlobalResponseCode.ApplyIdInvalid);
        }
        if (approveModel.getComment() != null && approveModel.getComment().length() > 200) {
            throw new SaasException(GlobalResponseCode.ApplyCheckReasonInvalid);
        }
        String applyId = approveModel.getApply_id();
        ApplyOrder apply = applyOrderMapper.selectByPrimaryKey(approveModel.getApply_id());
        if (apply == null) {
            throw new SaasException(GlobalResponseCode.NotFound);
        }
        if (apply.getState() != null && apply.getState().intValue() == ApplyStatus.Backout.getValue()) {
            //已撤销
            throw new SaasException(GlobalResponseCode.CenterApplyCancleOrder);
        }
        if (!apply.getCompanyId().equals(companyId)) {
            //申请单和当前人不在一个公司
            throw new SaasException(GlobalResponseCode.ApplyCompanyCannotMatch);
        }
        if (apply.getState() != ApplyStatus.PendingAudit.getValue()) {
            //该单不是待审核,不能审批
            throw new SaasException(GlobalResponseCode.ApplyStatusNotPendingAudit);
        }
        if (!apply.getApproverId().equals(userId)) {
            //不是当前人可以处理的审批单
            throw new SaasException(GlobalResponseCode.NoAuth);
        }
        ApplyStatus finalStatus;
        ApplyLogAction action;
        String receiverId; //下一个审批人/通知人
        String approverId; //审批人
        Integer logId = null;
        List<String> skippedApprverIds = new ArrayList<>();//固定审批流跳过的审批人
        Date now = new Date();
        action = ApplyLogAction.Approve;
        approverId = userId;
        receiverId = null;
        Integer finalJudgmentUser = null;
        boolean isFlow = (ValueTool.areEqual(apply.getFlowType(), CompanyApplyType.Flow.getValue()) || ValueTool.areEqual(apply.getFlowType(), CompanyApplyType.CONDITIONAL.getValue()));
        if (isFlow) {
            //固定审批流和分条件审批流
            List<ApplyFlowUserItem> applyFlowUserItems = applyFlowService.getFlowItemsByApplyId(applyId);
            if (applyFlowUserItems == null ||
                    applyFlowUserItems.size() == 0 ||
                    applyFlowUserItems.stream().allMatch(m ->
                            !ValueTool.areEqual(m.getStatus(), ApplyFlowUserItemStatus.PendingAudit.getValue())
                                    && !ValueTool.areEqual(m.getStatus(), ApplyFlowUserItemStatus.Transfered.getValue())
                    )) {
                //没有待审核记录，也没有转交记录，预示审批流已经审核通过
                finalStatus = ApplyStatus.Approved;
                receiverId = apply.getEmployeeId();
                //处理审批日志和审批单的状态
                logId = sloveApplyLog(applyId, receiverId, approverId, finalStatus, now, ip, userId, action, approveModel);
                finalJudgmentUser = 2;
            } else {
                int pendingAuditItemIndex = -1;
                for (int i = 0; i < applyFlowUserItems.size(); i++) {
                    if (ValueTool.areEqual(applyFlowUserItems.get(i).getStatus(), ApplyFlowUserItemStatus.PendingAudit.getValue())) {
                        pendingAuditItemIndex = i;
                        break;
                    }
                }
                ApplyFlowUserItem pendingAuditItem = null;
                if (pendingAuditItemIndex >= 0) {
                    pendingAuditItem = applyFlowUserItems.get(pendingAuditItemIndex);
                }
                if (pendingAuditItem != null && !pendingAuditItem.getUserId().equals(userId)) {
                    logger.info("当前待审批的item不是userid，userId：" + userId + " itemId:" + pendingAuditItem.getUserId());
                    throw new SaasException(GlobalResponseCode.NoAuth);
                } else {
                    if (pendingAuditItem != null) {
                        //将item status设置为已审核
                        applyFlowService.setApplyFlowUserItemStatus(pendingAuditItem.getId(), ApplyFlowUserItemStatus.Approved);
                    }
                    try {
                        FindNextApproverIdAndStatusResult findNextApproverIdAndStatusResult = findNextApproverIdAndStatusInFlow(apply, applyFlowUserItems, skippedApprverIds, true);
                        finalStatus = findNextApproverIdAndStatusResult.getFinnalStatus();
                        finalJudgmentUser = findNextApproverIdAndStatusResult.getApplyStatus();
                        if (!StringTool.isNullOrEmpty(findNextApproverIdAndStatusResult.getApproverId())) {
                            approverId = findNextApproverIdAndStatusResult.getApproverId();
                        }
                        if (!StringTool.isNullOrEmpty(findNextApproverIdAndStatusResult.getReceiverId())) {
                            receiverId = findNextApproverIdAndStatusResult.getReceiverId();
                        }
                        if (findNextApproverIdAndStatusResult.getApplyStatus() == 1) {
                            //不是最后一个审批人存在下一个审批人
                            //查询审核中的审批日志
                            List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectApproveUser(applyId, ApplyLogAction.Approval.getValue());
                            //有日志数据情况下修改操作无日志数据情况下插入操作
                            if (!CollectionUtils.isEmpty(applyOrderLogList)) {
                                applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Approve.getValue(), applyOrderLogList.get(0).getId(), receiverId, approveModel.getComment(), new Date(), approveModel.getPrice());
                                List<ApplyOrderLog> applyOrderNextUserLogList = applyOrderLogExtMapper.selectNextApproveUser(applyId, ApplyLogAction.Unknown.getValue());
                                if (!CollectionUtils.isEmpty(applyOrderNextUserLogList)) {
                                    for (ApplyOrderLog applyOrderLog : applyOrderNextUserLogList) {
                                        String sponsorId = applyOrderLog.getSponsorId();
                                        EmployeeContract employee = iBaseOrganizationService.getEmployee(sponsorId, companyId);
                                        if (employee == null) {
                                            applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Skip.getValue(), applyOrderLog.getId(), "", CoreLanguage.Common_Value_ApprovalPersonDelete.getMessage(), null, approveModel.getPrice());
                                        } else {
                                            //保存下一个审批人为审批中状态
                                            applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Approval.getValue(), applyOrderLog.getId(), "", null, null, approveModel.getPrice());
                                            logId = applyOrderLog.getId();
                                            break;
                                        }
                                    }
                                }
                                setApproverAndStatus(applyId, approverId, finalStatus, now, logId, approveModel);
                            } else {
                                logId = writeLog(applyId, now, ip, userId, receiverId, approveModel.getComment(), action);
                                setApproverAndStatus(applyId, approverId, finalStatus, now, logId, approveModel);
                            }
                        } else if (findNextApproverIdAndStatusResult.getApplyStatus() == 2) {
                            //最后一个审批人
                            logId = sloveApplyLog(applyId, receiverId, approverId, finalStatus, now, ip, userId, action, approveModel);
                        } else {
                            //后面的审批人都不存在直接跳到授权负责人
                            List<ApplyOrderLog> applyOrderApprovalLogList = applyOrderLogExtMapper.selectApproveUser(applyId, ApplyLogAction.Approval.getValue());
                            applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Approve.getValue(), applyOrderApprovalLogList.get(0).getId(), receiverId, approveModel.getComment(), new Date(), approveModel.getPrice());

                            List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectApproveUser(applyId, ApplyLogAction.Unknown.getValue());
                            if (!CollectionUtils.isEmpty(applyOrderLogList)) {
                                for (ApplyOrderLog applyOrderLog : applyOrderLogList) {
                                    applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Skip.getValue(), applyOrderLog.getId(), "", CoreLanguage.Common_Value_ApprovalPersonDelete.getMessage(), null, approveModel.getPrice());
                                }
                            } else {
                                if (skippedApprverIds != null && skippedApprverIds.size() > 0) {
                                    for (String skippedApproverId : skippedApprverIds) {
                                        writeLog(applyId, now, ip, skippedApproverId, skippedApproverId, CoreLanguage.Common_Value_ApprovalPersonDelete.getMessage(), ApplyLogAction.Skip);
                                    }
                                }
                            }
                            int sort = 0;
                            if (!CollectionUtils.isEmpty(applyOrderLogList)) {
                                sort = applyOrderLogList.get(applyOrderLogList.size() - 1).getSort() + 100;
                            }
                            ApplyOrderLog logApprove = new ApplyOrderLog();
                            logApprove.setApplyOrderId(applyId);
                            logApprove.setIp(ip);
                            logApprove.setSponsorId(approverId);
                            logApprove.setReceiverId("");
                            logApprove.setCheckReason(null);
                            logApprove.setAction(ApplyLogAction.Approval.getValue());
                            logApprove.setSort(sort);
                            logApprove.setRootApplyOrderId(applyId);
                            applyOrderLogMapper.insertSelective(logApprove);
                            logId = logApprove.getId();
                            setApproverAndStatus(applyId, approverId, finalStatus, now, logId, approveModel);
                        }
                    } catch (SaasException ex) {
                        logger.info("审批异常：" + ex.getResponseCode());
                        throw new SaasException(GlobalResponseCode.PayOrderCancleOrderSystemError);
                    }
                }
            }
        } else {
            finalStatus = ApplyStatus.Approved;
            receiverId = apply.getEmployeeId();
            approverId = userId;
            //处理审批日志和审批单的状态
            logId = sloveApplyLog(applyId, receiverId, approverId, finalStatus, now, ip, userId, action, approveModel);
        }
        //最后一个审批节点
        if (apply.getFlowType() == CompanyApplyType.Elastic.getValue() || (finalJudgmentUser != null && finalJudgmentUser == 2)) {
            //修改交易订单状态
//            List<ApplyTripInfo> applyTripInfoList = applyTripInfoExtMapper.queryAllTripListByApplyOrderId(applyId);
//            List<String> orderIdList = Lists.newLinkedList();
//            for (ApplyTripInfo applyTripInfo : applyTripInfoList) {
//                Map<String, Object> tradeInformationMap = JSON.parseObject(applyTripInfo.getTripContent(), Map.class);
//                List<Map<String, Object>> tradeInformationList = (List<Map<String, Object>>) tradeInformationMap.get("trade_information_list");
//                for (Map<String, Object> tradeInformation : tradeInformationList) {
//                    orderIdList.add(ObjUtils.toString(tradeInformation.get("orderId")));
//                }
//            }
//            logger.info(String.format("同意时修改交易记录的订单为:%s", JSON.toJSON(orderIdList)));
//            iBankOrderService.applyUpdate(orderIdList, applyId, com.fenbeitong.noc.api.service.constant.enums.ApplyStatus.DONE.getKey());
            try {
                iFinanceCostService.updateStatusByApplyOrderId(applyId, ApplyOrderActionType.Approve,userId,companyId,1);
            }catch (Exception e){
                logger.error("终审同意审批单更改费用状态失败"+e);
                throw new SaasException(GlobalResponseCode.InnerError);
            }
            //更新费用表中state状态
            List<Integer> costIds = selectCostIdByApplyOrderId(applyId);
            rpcUpdateCostState(costIds,ApplyStatus.Approved.getValue());
            //生成pdf
//            new Thread(new Runnable() {
//                @Override
//                public void run() {
//                    producePdf(applyOrder, writeoffMap);
//                }
//            }).start();
            apply.setState(ApplyStatus.Approved.getValue());

            boolean autoReturn = getAutoReturnConfig(companyId);
            if(autoReturn) {
                applyOrderAutoReturn(applyId);
            }

            // 同步日志
            applyV2Service.syncLogOnFinishProcess(applyId, companyId);
            //批量扣减预算
            batchDeduct(costIds, applyId);
        }
        Map<String, Object> writeoffMap = sloveWriteoffTemplate(apply);
        //生成电子单
        new Thread(new Runnable() {
            @Override
            public void run() {
                createElecApplyOrder(apply,writeoffMap);
            }
        }).start();

        setApplyApproverAndPushMessage(apply, userId, ip, finalStatus, receiverId, approverId, approveModel.getComment(), action, skippedApprverIds, logId);
        //push 抄送人
        pushCCMessage(apply, apply.getEmployeeId(), action, finalStatus);
        return GlobalResponseCode.Success;
    }

    private void applyOrderAutoReturn(String applyId) {
        ApplyOrder applyOrder = applyOrderMapper.selectByPrimaryKey(applyId);
        applyOrder.setReturnTicket(ReturnTicketType.AllReturn.getValue());
        applyOrder.setReturnDownload(ReturnTicketType.AllReturn.getValue());
        applyOrderMapper.updateByPrimaryKey(applyOrder);

        KafkaBankApplyMsg applyMsg = new KafkaBankApplyMsg();
        applyMsg.setApplyId(applyOrder.getId());
        applyMsg.setCompanyId(applyOrder.getCompanyId());
        applyMsg.setPushTime(DateUtils.format(new Date()));
        applyMsg.setStatus(applyOrder.getReturnTicket());
        applyMsg.setUserId(applyOrder.getEmployeeId());
        applyOrderProducerService.syncApplyorderReturnTicket(applyMsg);
    }

    private boolean getAutoReturnConfig(String companyId) {
        // TODO UC接口迁出，待确认新接口
        //InvoiceConfigDTO invoiceConfig = iInvoiceConfigService.getCompanyInvoiceConfig(companyId);
        InvoiceConfigDTO invoiceConfig = new InvoiceConfigDTO();

        if (invoiceConfig == null) {
            return false;
        }

        return invoiceConfig.getTicketAutoConfirm();
    }

    /**
     * 处理核销单模板
     * @param applyOrder
     * @return
     */
    @Override
    public Map<String, Object> sloveWriteoffTemplate(ApplyOrder applyOrder) {
        EmployeeContract employeeContract = iBaseEmployeeExtService.queryEmployeeInfo(applyOrder.getEmployeeId(), applyOrder.getCompanyId());
        Map<String, Object> templateMap = Maps.newHashMap();
        templateMap.put("createTime", DateTimeTool.fromDateTimeToString(applyOrder.getCreateTime()));
        templateMap.put("userName", applyOrder.getApplicantName());
        templateMap.put("userDept", ObjUtils.isEmpty(employeeContract)?"":employeeContract.getOrg_sub_name());
        templateMap.put("applyId", applyOrder.getId());
        templateMap.put("applyReason", applyOrder.getApplyReasonDesc());
        //审批状态
        String applyStatus="";
        if (ApplyStatus.valueOf(applyOrder.getState()).equals(ApplyStatus.Approved)){
            applyStatus = CoreLanguage.Common_Value_HasAccept.getMessage();
        }else if (ApplyStatus.valueOf(applyOrder.getState()).equals(ApplyStatus.PendingAudit)){
            applyStatus = CoreLanguage.Common_Value_CheckLoading.getMessage();
        }
        templateMap.put("applyStatus", applyStatus);
        templateMap.put("returnDate","");
        List<Map<String, Object>> costList = Lists.newArrayList();
        templateMap.put("totalPrice", new DecimalFormat("0.00").format(applyOrder.getBudget() / 100D));
        String attributionName ="";
        //部门
        if (applyOrder.getCostAttributionCategory()==1){
            OrgUnitResult orgUnitResult = iOrgUnitService.queryOrgUnitAndParentUnit(applyOrder.getCompanyId(),applyOrder.getCostAttributionId());
            if (ObjUtils.isNotEmpty(orgUnitResult)){
                attributionName = orgUnitResult.getFullName();
            }
        }else if(applyOrder.getCostAttributionCategory()==2){//项目
            List<CostCenterAndGroupDto> centerAndGroupDtos = iCostCenterService.queryCostCenterAndGroupListByIds(applyOrder.getCompanyId(),Lists.newArrayList(applyOrder.getCostAttributionId()));
            attributionName = applyOrder.getCostAttributionName();
            if (CollectionUtils.isNotEmpty(centerAndGroupDtos)){
                CostCenterAndGroupDto costCenterAndGroupDto = centerAndGroupDtos.get(0);
                List<CostCenterGroupDto> costCenterAndGroupDtoList = costCenterAndGroupDto.getCostCenterGroupList();
                if (CollectionUtils.isNotEmpty(costCenterAndGroupDtoList)){
                    StringBuffer buffer = new StringBuffer();
                    buffer.append("(");
                    for (CostCenterGroupDto dto :costCenterAndGroupDtoList){
                        buffer.append(dto.getGroupName()).append("/");
                    }
                    buffer.delete(buffer.length()-1,buffer.length());
                    buffer.append(")");
                    attributionName = attributionName+buffer;
                }
            }
        }
        templateMap.put("attributionName", attributionName);
        List<Map<String, Object>> applyList = Lists.newArrayList();
        String applyId = applyOrder.getId();
        List<Integer> costIds = selectCostIdByApplyOrderId(applyId);
        List<FinanceCostInfoVO> list= iFinanceCostService.getCostListById(costIds);
        //List<FinanceCostInfoVO> list= iFinanceCostService.getCostList(Lists.newArrayList(applyId));
        //List<ApplyTripInfo> applyTripInfoList = applyTripInfoExtMapper.queryAllTripListByApplyOrderId(applyId);
        List<String> invoiceIdList = Lists.newArrayList();
        List<Map<String,Object>> pettyList = Lists.newArrayList();
        List<String> attachmentList = Lists.newArrayList();
        int no = 0;
        for (FinanceCostInfoVO financeCostInfoVO : list) {
            //费用描述
            String costDesc = financeCostInfoVO.getCostDesc();
            //费用类别
            String costCategory = financeCostInfoVO.getCostCategory();
            //金额
            BigDecimal showPrice= ObjUtils.toBigDecimal(new DecimalFormat("0.00").format(financeCostInfoVO.getTotalAmount())).setScale(2, BigDecimal.ROUND_HALF_UP);
            List<Map> invoiceList=financeCostInfoVO.getInvoiceList();
            if (ObjUtils.isNotEmpty(invoiceList)){
                List<String> invoIdList =invoiceList.stream().map(v->{return (String)v.get("fbInvId");}).collect(Collectors.toList());
                invoiceIdList.addAll(invoIdList);
            }
            List<PettyRelationInfo> pettyRelationInfos= financeCostInfoVO.getPettyList();
            if (ObjUtils.isNotEmpty(pettyRelationInfos)){
                pettyList.addAll(JsonUtils.toObj(JsonUtils.toJson(pettyRelationInfos),List.class));
            }
            Map<String, Object> item = Maps.newHashMap();
            item.put("no", no += 1);
            item.put("costCategory", costCategory);
            item.put("price", showPrice);
            item.put("costDesc", costDesc);
            //费用归属
            List<Map> attributionList = Lists.newArrayList();
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(financeCostInfoVO.getCostAttributionGroupList())){
                List<CostAttributionGroup> groupList = financeCostInfoVO.getCostAttributionGroupList();
                for (CostAttributionGroup costAttributionGroup:groupList){
                    Map attributionMap = Maps.newHashMap();
                    Integer category = costAttributionGroup.getCategory();
                    String categoryName;
                    if (category==3){
                        categoryName = costAttributionGroup.getCategoryName();
                    }else {
                        categoryName = BudgetObjectEnum.valueOf(category).getName();
                    }
                    List<CostAttribution> costAttributionList = costAttributionGroup.getCostAttributionList();
                    StringBuffer buffer = new StringBuffer();
                    for (CostAttribution costAttribution : costAttributionList) {
                        buffer.append(costAttribution.getName()).append(" ¥").append(ObjUtils.toBigDecimal(costAttribution.getPrice(),BigDecimal.ZERO).setScale(2,BigDecimal.ROUND_HALF_UP)).append("；");
                    }
                    attributionMap.put("categoryName",categoryName);
                    attributionMap.put("categoryDesc",buffer.append("\n").toString());
                    attributionList.add(attributionMap);
                }
            }
            item.put("attributionList",attributionList);
            costList.add(item);
            if (ObjUtils.isNotEmpty(financeCostInfoVO.getAttachmentList())){
                attachmentList.addAll(financeCostInfoVO.getAttachmentList());
            }
        }
        templateMap.put("costList", costList);
        templateMap.put("pettyList",pettyList);
        //处理审批流程
        List<ApplyOrderLog> logs = applyOrderLogExtMapper.selectListByApplyId(applyId);
        List<ApplyOrderLogContract> applyOrderLogContractList = getLogContractList(logs, applyOrder.getCompanyId());
        if (ObjUtils.isNotEmpty(applyOrderLogContractList)){
            for (ApplyOrderLogContract applyOrderLogContract : applyOrderLogContractList) {
                String sponsor = applyOrderLogContract.getSponsor();
                Integer action = applyOrderLogContract.getAction();
                String checkReason = applyOrderLogContract.getCheck_reason();
                Map<String, Object> itemInfo = Maps.newHashMap();
                itemInfo.put("name", sponsor);
                itemInfo.put("action", ApplyLogAction.valueOf(action).getDesc());
                if (action == ApplyLogAction.Forward.getValue()) {
                    itemInfo.put("action", CoreLanguage.Common_Value_ActionForward.getMessage() + " " + applyOrderLogContract.getReceiver());
                }
                if (StringUtils.isNotBlank(checkReason)) {
                    itemInfo.put("comment", CoreLanguage.Common_Value_ActionComment.getMessage() + "：" + checkReason);
                }
                itemInfo.put("time",applyOrderLogContract.getTime());
                applyList.add(itemInfo);
            }
        }
        templateMap.put("applyList", applyList);
        Map<String, Object> writeoffMap = Maps.newHashMap();
        writeoffMap.put("templateInfo", templateMap);
        writeoffMap.put("invoiceIdList", invoiceIdList);
        writeoffMap.put("attachmentList",attachmentList);
        return writeoffMap;
    }

    //处理PDF
    @Override
    public Boolean producePdf(ApplyOrder applyOrder, Map<String, Object> writeoffMap) {
        File file = null;
        try {
            String result = HttpClientUtils.get(URL_TEMPLATE_DETAIL + "?templateName=" + TemplateConstant.EMAIL_TEMP_WRITEOFF);
            logger.info("查询模板id：{}", result);
            JSONObject resultObj = JSON.parseObject(result);
            logger.info("查询模板结果：{}", resultObj.toJSONString());
            if (resultObj == null) {
                return false;
            }
            JSONObject data = resultObj.getJSONObject("data");
            if (data == null || data.isEmpty()) {
                return false;
            }
            String template = ObjUtils.toString(data.get("template"));
            logger.info("核销申请单发票和模板信息：" + JsonUtils.toJson(writeoffMap));
            Map<String, Object> templateMap = (Map<String, Object>) writeoffMap.get("templateInfo");
            templateMap.put("imageUrl","data:image/png;base64,"+ Base64.encode(QrCodeUtil.generatePng(applyOrder.getId(), 300,300)));
            List<String> invoiceIdList = (List<String>) writeoffMap.get("invoiceIdList");
            List<String> pdfUrlList = Lists.newArrayList();
            logger.info("核销申请单html数据封装完毕");
            String applyOrderHtml = FileUtils.getTempDirectoryPath() + "/"+applyOrder.getId()+".pdf";
            String writeoffPdfPath = applyOrderHtml;
            logger.info("核销申请单数据的参数信息：" + JsonUtils.toJson(templateMap));
            PdfUtils.htmlText2Pdf(template, applyOrderHtml, templateMap);
            logger.info("核销申请单数据处理完毕并转成pdf");
            if (CollectionUtils.isNotEmpty(invoiceIdList)) {
                logger.info("核销申请单数据发票信息：" + JsonUtils.toJson(invoiceIdList));
                List<InvoicePicResRPCDTO> batchInvoicePicList = iFbtInvoiceService.getBatchInvoicePic(applyOrder.getCompanyId(), invoiceIdList);
                logger.info("核销申请单发票url集合：" + JsonUtils.toJson(batchInvoicePicList));
                for (InvoicePicResRPCDTO invoicePicResRPCDTO : batchInvoicePicList) {
                    String invPdfUrl = invoicePicResRPCDTO.getInvPdfUrl();
                    if (StringUtils.isNotBlank(invPdfUrl)) {
                        pdfUrlList.add(invPdfUrl);
                    }else {
                        pdfUrlList.add(invoicePicResRPCDTO.getInvPicUrl());
                    }
                }
            }
            //文件上传oss
            // 保存到临时文件
            file = new File(writeoffPdfPath);
//            file.renameTo(new File(file.getParent()+ "/虚拟卡消费核销审批单.pdf"));
//            File fileInfo = new File(file.getParent()+ "/虚拟卡消费核销审批单.pdf");
            //File file1 = new File(file.getParent() + "/虚拟卡消费核销审批单.pdf");
            Map<String, Object> paramMap = com.google.common.collect.Maps.newHashMap();
            paramMap.put("busi_code", "saas");
            paramMap.put("user_id", applyOrder.getEmployeeId());
            paramMap.put("file1", file);
            Map<String, String> fileNameMap = Maps.newHashMap();
            fileNameMap.put("file1", "虚拟卡消费核销审批单.pdf");
            JSONArray jsonArray = UploadOssTool.uploadOss(paramMap, fileNameMap);
            //完整链接
            String url = MapUtils.getString(jsonArray.getJSONObject(0), "url");
            MessageSetup picMessageSetup = messageSetupService.queryCompanyMessageSetupWithDefault(applyOrder.getCompanyId(), SaasApplyConstant.ITEM_CODE_ENABLE_PIC_EXPORT);
            Integer needPicExport = picMessageSetup.getIsChecked();
            if (needPicExport == SaasMessageConstant.IS_CHECKED_TRUE) {
                if (ObjUtils.isNotEmpty(writeoffMap.get("attachmentList"))){
                    List<String> attachmentList = (List<String>) writeoffMap.get("attachmentList");
                    Integer configMax = picMessageSetup.getIntVal1();
                    if (attachmentList.size() > configMax) {
                        attachmentList = attachmentList.subList(0, configMax);
                    }
                    pdfUrlList.addAll(attachmentList);
                }
            }
            if (CollectionUtils.isNotEmpty(pdfUrlList)) {
                List<String> urls =Lists.newArrayList();
                urls.add(url);
                urls.addAll(pdfUrlList);
                logger.info("核销申请单发票数据的pdfUrl集合：" + urls);
                Map<String,Object> map =Maps.newHashMap();
                Map<String,Object> upload =Maps.newHashMap();
                upload.put("busi_code", "saas");
                upload.put("user_id", applyOrder.getEmployeeId());
                upload.put("file_name", "虚拟卡消费核销审批单.pdf");
                map.put("urls",urls);
                map.put("upload",upload);
                String newUrl = UploadOssTool.mergePdf(map);
                if (ObjUtils.isNotEmpty(newUrl)){
                    Map urlMap =Maps.newHashMap();
                    urlMap.put("urls",Lists.newArrayList(url));
                    UploadOssTool.deleteFiles(urlMap);
                    url = newUrl;
                }
            }
            ApplyOrder applyOrderInfo = new ApplyOrder();
            applyOrderInfo.setId(applyOrder.getId());
            applyOrderInfo.setDownloadLink(url);
            DynamicDataSourceDecision.determineDataSource(applyOrder.getCompanyId());
            applyOrderMapper.updateByPrimaryKeySelective(applyOrderInfo);
        } catch (Exception e) {
            // 删除本地临时文件
            logger.info("生成pdf异常：" + e.getMessage());
            return false;
        }  finally {
            // 删除本地临时文件
            FileUtils.deleteQuietly(file);
        }
        return true;
    }

    /**
     * 审批、拒绝、转交情况下处理审批日志的状态和修改审批单的状态
     *
     * @param applyId
     * @param receiverId
     * @param approverId
     * @param finalStatus
     * @param now
     * @param ip
     * @param userId
     * @param action
     * @param approveModel
     */
    //@Transactional(value = "saas")
    public Integer sloveApplyLog(String applyId, String receiverId, String approverId, ApplyStatus finalStatus, Date now, String ip, String userId, ApplyLogAction action, ApplyApproveContract approveModel) {
        //查询审核中的审批日志
        List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectListByCreateTimeDescAndAction(applyId, ApplyLogAction.Approval.getValue());
        //有审批日志情况下进行修改操作,无审批日志情况下插入操作
        if (!CollectionUtils.isEmpty(applyOrderLogList)) {
            applyOrderLogExtMapper.updateApplyOrderActionById(action.getValue(), applyOrderLogList.get(0).getId(), receiverId, approveModel.getComment(), new Date(), null);
            setApproverAndStatus(applyId, approverId, finalStatus, now, applyOrderLogList.get(0).getId(), approveModel);
            return applyOrderLogList.get(0).getId();
        } else {
            Integer logId = writeLog(applyId, now, ip, userId, receiverId, approveModel.getComment(), action);
            setApproverAndStatus(applyId, approverId, finalStatus, now, logId, approveModel);
            return logId;
        }
    }

    //@Transactional(value = "saas")
    private int setApproverAndStatus(String applyId, String approverId, ApplyStatus status, Date time, Integer logId, ApplyApproveContract approveModel) {
        if (approveModel == null) {
            approveModel.setComment("");
            approveModel.setPrice(null);
        }
        int count = applyOrderExtMapper.updateApproverIdAndStatus(applyId, approverId, status.getValue(), time, logId, approveModel.getComment(), null);
        return count;
    }

    //@Transactional(value = "saas")
    private Integer writeLog(String id, Date createTime, String ip, String sponsorId, String receiverId, String checkReason, ApplyLogAction action) {
        //插入日志
        ApplyOrderLog log = new ApplyOrderLog();
        log.setApplyOrderId(id);
        log.setCreateTime(createTime);
        log.setIp(ip);
        log.setSponsorId(sponsorId);
        if (receiverId == null) {
            receiverId = "";
        }
        log.setReceiverId(receiverId);
        log.setCheckReason(checkReason);
        log.setAction(action.getValue());
        log.setPrice(null);
        log.setRootApplyOrderId(id);
        applyOrderLogMapper.insert(log);
        return log.getId();
    }

    private FindNextApproverIdAndStatusResult findNextApproverIdAndStatusInFlow(ApplyOrder apply, List<ApplyFlowUserItem> applyFlowUserItems, List<String> skippedApprverIds, boolean couldFinalApprove) throws SaasException {
        String approverId = null;
        String receiverId = null;
        ApplyStatus finalStatus;
        Integer applyStatus = null;
        int remainIndex = applyFlowUserItems.size();
        for (int i = 0; i < applyFlowUserItems.size(); i++) {
            if (ValueTool.areEqual(applyFlowUserItems.get(i).getStatus(), ApplyFlowUserItemStatus.Waiting.getValue())) {
                remainIndex = i;
                break;
            }
        }
        //找下一个审批人
        boolean findNextApprover = false;
        // 是否是最后一名审批人
        boolean finnalApprover = true;
        for (int i = remainIndex; i < applyFlowUserItems.size(); i++) {
            ApplyFlowUserItem item = applyFlowUserItems.get(i);
            if (ValueTool.areEqual(item.getStatus(), ApplyFlowUserItemStatus.Waiting.getValue())) {
                finnalApprover = false;
                if (userService.isUserInCompany(item.getUserId(), apply.getCompanyId())) {
                    findNextApprover = true;
                    receiverId = item.getUserId();
                    approverId = receiverId;
                    applyStatus = 1;
                    applyFlowService.setApplyFlowUserItemStatus(item.getId(), ApplyFlowUserItemStatus.PendingAudit);
                    break;
                } else {
                    skippedApprverIds.add(item.getUserId());
                    applyFlowService.setApplyFlowUserItemStatus(item.getId(), ApplyFlowUserItemStatus.Skip);
                }
            }
        }
        if (finnalApprover && couldFinalApprove) {
            //当前审批人已经是最后一个审批人
            finalStatus = ApplyStatus.Approved;
            receiverId = apply.getEmployeeId();
            applyStatus = 2;
        } else {
            finalStatus = ApplyStatus.PendingAudit;
            if (!findNextApprover) {
                //后面的审批人都已不存在，直接跳到授权负责人
                List<String> superAdminEmployeeIds = customRoleService.getEmployeeIdByRoleId(Integer.toString(UserRole.CompanySuperAdmin.getValue()), apply.getCompanyId());
                if (superAdminEmployeeIds == null || superAdminEmployeeIds.size() == 0) {
                    throw new SaasException(GlobalResponseCode.ApplyCanotApproveBecauseHasNoSuperAdmin);
                } else {
                    receiverId = superAdminEmployeeIds.get(0);
                    approverId = receiverId;
                    applyStatus = 3;
                }
            }
        }
        FindNextApproverIdAndStatusResult result = new FindNextApproverIdAndStatusResult();
        result.setApproverId(approverId);
        result.setReceiverId(receiverId);
        result.setFinnalStatus(finalStatus);
        result.setApplyStatus(applyStatus);
        return result;
    }

    @Override
    //@Transactional(value = "saas")
    public void setApplyApproverAndPushMessage(ApplyOrder apply, String userId, String ip, ApplyStatus finalStatus, String receiverId, String approverId, String comment, ApplyLogAction action, List<String> skippedApprverIds, Integer logId) {
        Date now = new Date();
        String applyId = apply.getId();
        if (!StringTool.isNullOrEmpty(approverId) && !approverId.equals(apply.getApproverId())) {
            insertApproverMap(applyId, approverId, now);
        }
        postMessage(apply, userId, receiverId, action, finalStatus, logId, comment);
    }

    /**
     * 驳回
     *
     * @param approveModel
     * @param userId
     * @param ip
     * @return
     */
    @Override
    //@Transactional(value = "saas")
    public GlobalResponseCode repulse(ApplyApproveContract approveModel, String userId, String companyId, String ip, String clientVersion, String source) throws SaasException {
        FlowCheckUtil.check(companyId, clientVersion, source);
        if (approveModel == null) {
            return GlobalResponseCode.ApplyIsNull;
        }
        if (StringTool.isNullOrEmpty(approveModel.getApply_id())) {
            return GlobalResponseCode.ApplyIdInvalid;
        }
        if (approveModel.getComment() != null && approveModel.getComment().length() > 200) {
            return GlobalResponseCode.ApplyCheckReasonInvalid;
        }

        if (approveModel.getComment() == null || approveModel.getComment().length() == 0) {
            //驳回审批单必须要填写理由
            return GlobalResponseCode.ApplyRefuseMustWriteReason;
        }
        String applyId = approveModel.getApply_id();
        ApplyOrder apply = applyOrderMapper.selectByPrimaryKey(approveModel.getApply_id());
        if (apply == null) {
            return GlobalResponseCode.NotFound;
        }
        if (apply.getState() != null && apply.getState().intValue() == ApplyStatus.Backout.getValue()) {
            //审批已撤销
            return GlobalResponseCode.CenterApplyCancleOrder;
        }
        if (!apply.getCompanyId().equals(companyId)) {
            //申请单和当前人不在一个公司
            return GlobalResponseCode.ApplyCompanyCannotMatch;
        }
        if (apply.getState() != ApplyStatus.PendingAudit.getValue()) {
            //该单不是待审核,不能审批
            return GlobalResponseCode.ApplyStatusNotPendingAudit;
        }
        if (!apply.getApproverId().equals(userId)) {
            //不是当前人可以处理的审批单
            return GlobalResponseCode.NoAuth;
        }
        ApplyStatus finalStatus;
        ApplyLogAction action;
        String receiverId; //下一个审批人/通知人
        String approverId; //审批人
        Integer logId = null;
        List<String> skippedApprverIds = new ArrayList<>();//固定审批流跳过的审批人
        Date now = new Date();
        //拒绝
        List<ApplyFlowUserItem> applyFlowUserItems = applyFlowService.getFlowItemsByApplyId(applyId);
        for (ApplyFlowUserItem applyFlowUserItem : applyFlowUserItems) {
            if (ValueTool.areEqual(applyFlowUserItem.getStatus(), ApplyFlowUserItemStatus.PendingAudit.getValue())
                    && applyFlowUserItem.getUserId().equals(userId)) {
                applyFlowService.setApplyFlowUserItemStatus(applyFlowUserItem.getId(), ApplyFlowUserItemStatus.Refuse);
            }
        }
        finalStatus = ApplyStatus.Return;
        action = ApplyLogAction.Refuse;
        receiverId = apply.getEmployeeId();
        approverId = userId;
        List<Integer> costIds = Lists.newArrayList();
        // 费用解绑
        try{
            //查询费用id
            costIds = selectCostIdByApplyOrderId(applyId);
            iFinanceCostService.relieveBinding(ApplyOrderActionType.Repulse,applyId,costIds,companyId);
        } catch (Exception e) {
            logger.error("驳回审批单解绑费用失败" + e);
            throw new SaasException(GlobalResponseCode.InnerError);
        }
        // 同步日志
        applyV2Service.syncLogOnFinishProcess(applyId, companyId);
        //批量释放预算
        batchRelease(costIds, applyId);
        //处理审批日志和审批单的状态
        logId = sloveApplyLog(applyId, receiverId, approverId, finalStatus, now, ip, userId, action, approveModel);
        setApplyApproverAndPushMessage(apply, userId, ip, finalStatus, receiverId, approverId, approveModel.getComment(), action, skippedApprverIds, logId);
        return GlobalResponseCode.Success;
    }

    /**
     * 撤销
     *
     * @param userId
     * @param companyId
     * @param ip
     * @return
     */
    @Override
    //@Transactional(value = "fenbeitong")
    public GlobalResponseCode revoke(String userId, String companyId, ApplyRevokeContract data, String ip, String clientVersion, String source) {
        FlowCheckUtil.check(companyId, clientVersion, source);
        ApplyOrder order = getApplyOrderModelByIdAndUserIdAndCompanyId(data.getId(), userId, companyId, null);
        if (order == null) {
            return GlobalResponseCode.NotFound;
        }
        if (order.getState() != ApplyStatus.PendingAudit.getValue()) {
            return GlobalResponseCode.ApplyStatusNotPendingAudit;
        }
        Date now = new Date();
        ApplyLogAction action = ApplyLogAction.Revoke;
        int logSort = 0;
        List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectListByCreateTimeDesc(data.getId());
        if (!CollectionUtils.isEmpty(applyOrderLogList)) {
            Integer sort = applyOrderLogList.get(0).getSort();
            if (sort != null) {
                logSort = sort + 100;
            }
        }
        //插入日志
        ApplyOrderLog log = new ApplyOrderLog();
        log.setApplyOrderId(data.getId());
        log.setRootApplyOrderId(data.getId());
        log.setCreateTime(now);
        log.setIp(ip);
        log.setSponsorId(userId);
        log.setReceiverId(userId);
        log.setCheckReason(null);
        log.setAction(action.getValue());
        log.setSort(logSort);
        applyOrderLogMapper.insertSelective(log);
        applyOrderExtMapper.setCenterStatus(data.getId(), ApplyStatus.Backout.getValue(), now, log.getId(), SaasFlowConstant.SETTING_TYPE_VIRTUAL_CARD);
        List<Integer> costIds = Lists.newArrayList();
        // 费用解绑
        try {
            //查询费用id
            costIds = selectCostIdByApplyOrderId(data.getId());
            iFinanceCostService.relieveBinding(ApplyOrderActionType.Revoke, data.getId(), costIds, companyId);
        } catch (Exception e) {
            logger.error("驳回审批单解绑费用失败" + e);
            throw new SaasException(GlobalResponseCode.InnerError);
        }
        // 同步日志
        applyV2Service.syncLogOnFinishProcess(data.getId(), companyId);
        //批量释放预算
        batchRelease(costIds, data.getId());
        return GlobalResponseCode.Success;
    }

    /**
     * 转发审批单
     *
     * @param approveModel
     * @param userId
     * @param ip
     * @return
     */
    @Override
    //@Transactional(value = "saas")
    public GlobalResponseCode forward(ApplyApproveContract approveModel, String userId, String companyId, String ip, String clientVersion, String source) throws SaasException {
        FlowCheckUtil.check(companyId, clientVersion, source);
        if (approveModel == null) {
            return GlobalResponseCode.ApplyIsNull;
        }
        if (StringTool.isNullOrEmpty(approveModel.getApply_id())) {
            return GlobalResponseCode.ApplyIdInvalid;
        }
        if (approveModel.getComment() != null && approveModel.getComment().length() > 200) {
            return GlobalResponseCode.ApplyCheckReasonInvalid;
        }
        int status = approveModel.getStatus();
        String nextApproverId = approveModel.getApprover_id();
        if (StringTool.isNullOrEmpty(nextApproverId)) {
            //转交需要一个承接人
            return GlobalResponseCode.ApplyApproveIdInvalid;
        } else if (nextApproverId.equals(userId)) {
            return GlobalResponseCode.ApplyTransferNotSelf;
        }
        String applyId = approveModel.getApply_id();
        ApplyOrder apply = applyOrderMapper.selectByPrimaryKey(approveModel.getApply_id());
        if (apply == null) {
            return GlobalResponseCode.NotFound;
        }
        boolean isFlow = (ValueTool.areEqual(apply.getFlowType(), CompanyApplyType.Flow.getValue()) || ValueTool.areEqual(apply.getFlowType(), CompanyApplyType.CONDITIONAL.getValue()));
        if (!apply.getCompanyId().equals(companyId)) {
            //申请单和当前人不在一个公司
            return GlobalResponseCode.ApplyCompanyCannotMatch;
        }
        if (apply.getState() != ApplyStatus.PendingAudit.getValue()) {
            //该单不是待审核或者转交状态,不能审批
            return GlobalResponseCode.ApplyStatusNotPendingAudit;
        }
        if (apply.getEmployeeId().equals(approveModel.getApprover_id())) {
            return GlobalResponseCode.ApplyApproverCannotBeSelf;
        }
        Boolean exist = userService.isUserInCompany(approveModel.getApprover_id(), apply.getCompanyId());
        if (!exist) {
            return GlobalResponseCode.ApplyCompanyCannotMatch;
        }
        if (!apply.getApproverId().equals(userId)) {
            //不是当前人可以处理的审批单
            return GlobalResponseCode.NoAuth;
        }
        ApplyStatus finalStatus;
        ApplyLogAction action;
        String receiverId; //下一个审批人/通知人
        String approverId; //审批人
        Integer logId = null;
        List<String> skippedApprverIds = new ArrayList<>();//固定审批流跳过的审批人
        Date now = new Date();
        //转交
        if (isFlow) {
            List<ApplyFlowUserItem> applyFlowUserItems = applyFlowService.getFlowItemsByApplyId(applyId);
            if (applyFlowUserItems != null && applyFlowUserItems.size() > 0) {
                Optional<ApplyFlowUserItem> optionalApplyFlowUserItem = applyFlowUserItems.stream().filter(m -> m.getUserId().equals(userId) &&
                        ValueTool.areEqual(m.getStatus(), ApplyFlowUserItemStatus.PendingAudit.getValue())).findAny();
                if (optionalApplyFlowUserItem.isPresent()) {
                    ApplyFlowUserItem applyFlowUserItem = optionalApplyFlowUserItem.get();
                    //当前审批人将此单转交给另一人，将此item状态置为Transfered
                    applyFlowService.setApplyFlowUserItemStatus(applyFlowUserItem.getId(), ApplyFlowUserItemStatus.Transfered);
                }
            }
        }
        finalStatus = ApplyStatus.PendingAudit;
        action = ApplyLogAction.Forward;
        receiverId = approveModel.getApprover_id();
        approverId = receiverId;

        //查询审核中的审批日志
        List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectListByCreateTimeDescAndAction(applyId, ApplyLogAction.Approval.getValue());
        //有日志数据情况下修改操作无日志数据情况下插入操作
        if (!CollectionUtils.isEmpty(applyOrderLogList)) {
            applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Forward.getValue(), applyOrderLogList.get(0).getId(), receiverId, approveModel.getComment(), new Date(), approveModel.getPrice());
            int logSort = 0;
            List<ApplyOrderLog> allApplyOrderLogList = applyOrderLogExtMapper.selectListByCreateTimeDesc(applyId);
            Integer sort = allApplyOrderLogList.get(0).getSort();
            if (sort != null) {
                logSort = sort + 100;
            }
            //Integer sort = applyOrderLogList.get(0).getSort() + 1;
            ApplyOrderLog log = new ApplyOrderLog();
            log.setApplyOrderId(applyId);
            log.setIp(ip);
            log.setSponsorId(receiverId);
            log.setReceiverId("");
            log.setCheckReason(null);
            log.setAction(ApplyLogAction.Approval.getValue());
            log.setSort(logSort);
            log.setRootApplyOrderId(applyId);
            applyOrderLogMapper.insertSelective(log);
            logId = log.getId();
            setApproverAndStatus(applyId, approverId, finalStatus, now, log.getId(), approveModel);
        } else {
            writeLog(applyId, now, ip, userId, receiverId, approveModel.getComment(), action);
            ApplyOrderLog logApprove = new ApplyOrderLog();
            logApprove.setApplyOrderId(applyId);
            logApprove.setIp(ip);
            logApprove.setSponsorId(receiverId);
            logApprove.setReceiverId("");
            logApprove.setCheckReason(null);
            logApprove.setAction(ApplyLogAction.Approval.getValue());
            logApprove.setSort(0);
            logApprove.setRootApplyOrderId(applyId);
            applyOrderLogMapper.insertSelective(logApprove);
            logId = logApprove.getId();
            setApproverAndStatus(applyId, approverId, finalStatus, now, logId, approveModel);
        }
        setApplyApproverAndPushMessage(apply, userId, ip, finalStatus, receiverId, approverId, approveModel.getComment(), action, skippedApprverIds, logId);
        return GlobalResponseCode.Success;
    }

    /**
     * 处理核销审批单的pdf生成问题
     */
    @Override
    public void disposeWriteoffPdf() {
        ApplyOrderExample applyOrderExample = new ApplyOrderExample();
        applyOrderExample.createCriteria()
                .andApplyOrderTypeEqualTo(SaasFlowConstant.SETTING_TYPE_VIRTUAL_CARD)
                .andTypeEqualTo(ApplyType.VirtualCardWriteOff.getValue())
                .andDownloadLinkIsNull().andStateIn(Lists.newArrayList(ApplyStatus.Approved.getValue(),ApplyStatus.PendingAudit.getValue()));
        List<ApplyOrder> applyOrderList = applyOrderMapper.selectByExample(applyOrderExample);
        if (CollectionUtils.isEmpty(applyOrderList)) {
            logger.info("核销审批单无pdf生成失败问题");
            return;
        }
        logger.info(String.format("核销审批单处理pdf的数量是:%s", applyOrderList.size()));
        for (ApplyOrder applyOrder : applyOrderList) {
            Map<String, Object> writeoffMap = sloveWriteoffTemplate(applyOrder);
//            iApplyOrderService.producePdf(applyOrder,writeoffMap,ApplyType.VirtualCardWriteOff);
            Boolean generateFlag = producePdf(applyOrder, writeoffMap);
            logger.info(String.format("核销审批单id:%s", applyOrder.getId()));
        }
    }

    /**
     * 核销单列表（凭证管理）
     *
     * @param companyId
     * @param pageIndex
     * @param pageSize
     * @return
     */
    @Override
    public PageReturnResultBaseContract<List<ApplyOrderApproveContract>> queryWriteoffApplyList(String companyId, int pageIndex, int pageSize) {
        if (pageIndex < 0) {
            pageIndex = 1;
        }
        PageReturnResultBaseContract<List<ApplyOrderApproveContract>> resultList = new PageReturnResultBaseContract<List<ApplyOrderApproveContract>>();
        resultList.setPageIndex(pageIndex);
        resultList.setPageSize(pageSize);
        int pageStart = (pageIndex - 1) * pageSize;
        //获取审批单总数
        Integer total = applyOrderExtMapper.queryWriteoffApplyOrderTotalByCompanyId(companyId);
        if (total == null) {
            resultList.setTotalCount(0);
        } else {
            resultList.setTotalCount(total);
        }
        if (total == null || total <= 0) {
            resultList.setResults(Lists.newArrayList());
            return resultList;
        }
        List<ApplyOrderApproveContract> applyOrderApproveContractList = Lists.newArrayList();
        //获取审批单数据
        List<ApplyOrder> applyOrderList = applyOrderExtMapper.queryWriteoffApplyOrderByCompanyId(companyId, pageStart, pageSize);
        if (CollectionUtils.isEmpty(applyOrderList)) {
            resultList.setResults(Lists.newArrayList());
            return resultList;
        }
        List<String> applyOrderIdList = Lists.newArrayList();
        List<String> employeeIdList = Lists.newArrayList();
        for (ApplyOrder applyOrder : applyOrderList) {
            String applyOrderId = applyOrder.getId();
            if (!applyOrderIdList.contains(applyOrderId)) {
                applyOrderIdList.add(applyOrderId);
            }
            String employeeId = applyOrder.getEmployeeId();
            if (!employeeIdList.contains(employeeId)) {
                employeeIdList.add(employeeId);
            }
        }
        Map<String, EmployeeContract> employeeMap = com.google.common.collect.Maps.newHashMap();
        if (!employeeIdList.isEmpty()) {
            List<EmployeeContract> employeeInfoList = baseOrganizationService.getEmployee(employeeIdList, companyId);
            for (EmployeeContract employee : employeeInfoList) {
                employeeMap.put(employee.getId(), employee);
            }
        }
        for (ApplyOrder applyOrder : applyOrderList) {
            ApplyOrderApproveContract applyOrderApproveContract = new ApplyOrderApproveContract();
            //描述
            applyOrderApproveContract.setApply_reason(applyOrder.getApplyReasonDesc());
            //总金额
            DecimalFormat df = new DecimalFormat("0.00");
            applyOrderApproveContract.setBudget(df.format(applyOrder.getBudget()/100D));
            EmployeeContract employee = employeeMap.get(applyOrder.getEmployeeId());
            //申请人
            String proposerName = employee != null ? employee.getName() : "";
            applyOrderApproveContract.setProposer(proposerName);
            //部门
            applyOrderApproveContract.setDepartment(employee.getOrg_name());
            //申请时间
            applyOrderApproveContract.setCreate_time(DateTimeTool.fromDateTimeToString(applyOrder.getCreateTime()));
            //申请单号
            applyOrderApproveContract.setId(applyOrder.getId());
            int totalTrades = 0;
            // 行程
            List<ApplyTripInfo> allApplyTripInfoList = applyTripInfoExtMapper.selectListByApplyOrderId(applyOrder.getId());
            if (CollectionUtils.isNotEmpty(allApplyTripInfoList)) {
                for (ApplyTripInfo applyTripInfo : allApplyTripInfoList) {
                    Map<String, Object> tradeInformationMap = JSON.parseObject(applyTripInfo.getTripContent(), Map.class);
                    List<Map<String, Object>> tradeInformationList = (List<Map<String, Object>>) tradeInformationMap.get("trade_information_list");
                    totalTrades += tradeInformationList.size();
                }
            }
            //交易笔数
            applyOrderApproveContract.setTotal_trades(totalTrades);
            //凭证状态
            applyOrderApproveContract.setVoucher_status(applyOrder.getVoucherStatus());
            //凭证号
            applyOrderApproveContract.setVoucher_code(applyOrder.getVoucherCode());
            applyOrderApproveContractList.add(applyOrderApproveContract);
        }
        resultList.setResults(applyOrderApproveContractList);
        return resultList;
    }

    @Override
    public PageResultBaseContract<List<ApplyContract>> queryWriteoffDraftList(Integer page, Integer pageSize, String userId, String companyId, Integer type, String name,String token) {
        if (pageSize <= 0 || pageSize > 50) {
            pageSize = 50;
        }
        if (page < 1) {
            page = 1;
        }
        int pageStart = (page - 1) * pageSize;
        PageResultBaseContract<List<ApplyContract>> resultBaseContract=new PageResultBaseContract<>();
        resultBaseContract.setPage(page);
        resultBaseContract.setP_size(pageSize);

        ApplyOrderExample example=new ApplyOrderExample();
        example.setOrderByClause("update_time desc");
        ApplyOrderExample.Criteria criteria=example.createCriteria();
        criteria.andCompanyIdEqualTo(companyId).andEmployeeIdEqualTo(userId).andStateEqualTo(1).andDeleteStatusEqualTo(0);
        if (ObjUtils.isNotEmpty(type)&&type!=0){
            criteria.andTypeEqualTo(type);
        }else {
            criteria.andTypeIn(Lists.newArrayList(ApplyType.VirtualCardWriteOff.getValue(),ApplyType.BusinessOrderWriteOff.getValue()));
        }
        if (StringUtils.isNotBlank(name)){
            criteria.andApplicantNameLike("%"+name+"%");
        }
        long size= applyOrderMapper.countByExample(example);
        if (ObjUtils.isEmpty(size)||size<=0L){
            resultBaseContract.setData(Lists.newArrayList());
            return resultBaseContract;
        }
        resultBaseContract.setCount(ObjUtils.toInteger(size));
        example.setOffset(pageStart);
        example.setLimit(pageSize);
        List<ApplyOrder> applyOrderList=applyOrderMapper.selectByExample(example);
        if (ObjUtils.isEmpty(applyOrderList)){
            resultBaseContract.setData(Lists.newArrayList());
            return resultBaseContract;
        }
        List<ApplyContract> list=applyOrderList.stream().map(v->{
             ApplyContract applyContract=new ApplyContract();
             applyContract.setApply(ApplyOrderContract.FromModel(v));
            return applyContract;
        }).collect(Collectors.toList());
        List<String> applyIdList=applyOrderList.stream().map(ApplyOrder::getId).collect(Collectors.toList());

        List<FinanceCostInfoVO> financeCostInfoVOS= iFinanceCostService.getCostList(applyIdList);
        if (ObjUtils.isNotEmpty(financeCostInfoVOS)){
            Map<String,List<FinanceCostInfoVO>> map=Maps.newHashMap();
            financeCostInfoVOS.forEach(v->{
                List<FinanceCostInfoVO> costList;
                if (ObjUtils.isEmpty(map.get(v.getApplyOrderId()))){
                    costList=Lists.newArrayList();
                }else {
                    costList=map.get(v.getApplyOrderId());
                }
                costList.add(v);
                map.put(v.getApplyOrderId(),costList);
            });
            list.forEach(v->{
                List<FinanceCostInfoVO> costInfoVOList=map.get(v.getApply().getId());
                if (ObjUtils.isNotEmpty(costInfoVOList)){
                    v.getApply().setTotal_trades(costInfoVOList.size());
                    BigDecimal totalAmount = costInfoVOList.stream().map(FinanceCostInfoVO::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    v.getApply().setBudget(ObjUtils.toInteger(totalAmount.multiply(new BigDecimal("100"))));
                }else {
                    v.getApply().setBudget(0);
                }
            });
        }else {
            list.forEach(v->{
                v.getApply().setBudget(0);
            });
        }
        appendApplyEmployeeNameAndDept(token,companyId,list);
        resultBaseContract.setData(list);
        return resultBaseContract;
    }

    /**
     * 写入员工姓名及所属部门,多个部门以,隔开
     *
     * @param token
     * @param companyId
     * @param data
     */
    private void appendApplyEmployeeNameAndDept(String token, String companyId, List<ApplyContract> data) {
        if (StringTool.isNullOrEmpty(token)) {
            return;
        }
        List<String> employeeIds = new ArrayList<>();
        data.stream().forEach(v->{employeeIds.add(v.getApply().getEmployee_id());});
        List<EmployeeNameAndDeptContract> employeeNameAndDeptList = userService.getNamesAndDeptsByIds(token, companyId, employeeIds);
        if (employeeNameAndDeptList != null && employeeNameAndDeptList.size() > 0) {
            for (ApplyContract order : data) {
                for (EmployeeNameAndDeptContract nditem : employeeNameAndDeptList) {
                    if (order != null && nditem != null && StringUtils.isNotBlank(nditem.getEmployee_id()) && nditem.getEmployee_id().equals(order.getApply().getEmployee_id())) {
                        order.getApply().setUser_name(nditem.getName());
                        if (nditem.getDepts() != null) {
                            order.getApply().setUser_dept(String.join(",", nditem.getDepts()));
                        }
                        break;
                    }
                }
                if (StringUtils.isBlank(order.getApply().getUser_name())) {
                    order.getApply().setUser_name(order.getApply().getApplicant_name());
                }
            }
        }

        List<String> appproverIds = new ArrayList<>();
        data.stream().forEach(v->{appproverIds.add(v.getApply().getEmployee_id());});
        List<EmployeeNameAndDeptContract> approverNameAndDeptList = userService.getNamesAndDeptsByIds(token, companyId, appproverIds);
        if (!com.luastar.swift.base.utils.CollectionUtils.isEmpty(approverNameAndDeptList)) {
            for (ApplyContract order : data) {
                for (EmployeeNameAndDeptContract nditem : approverNameAndDeptList) {
                    if (order != null && nditem != null && StringUtils.isNotBlank(order.getApply().getApprover_id()) && order.getApply().getApprover_id().equals(nditem.getEmployee_id())) {
                        order.getApply().setApprover_name(nditem.getName());
                        if (nditem.getDepts() != null) {
                            order.getApply().setApprover_dept(String.join(",", nditem.getDepts()));
                        }
                        break;
                    }
                }
            }
        }
    }


    @Override
   // @Transactional(value = "saas")
    public GlobalResponseCode handleHistroyData() {
        ApplyOrderExample example=new ApplyOrderExample();
        example.createCriteria().andTypeEqualTo(16).andStateEqualTo(4);
        List<ApplyOrder> applyOrderList= applyOrderMapper.selectByExample(example);
        List<HistoryInvoiceVO> historyInvoiceVOS=Lists.newArrayList();
        applyOrderList.forEach(v->{
            HistoryInvoiceVO invoiceVO=new HistoryInvoiceVO();
            invoiceVO.setApplyId(v.getId());
            invoiceVO.setStatus(v.getReturnTicket());
            invoiceVO.setUserId(v.getEmployeeId());
            historyInvoiceVOS.add(invoiceVO);
        });
        logger.info("历史回票数据"+JsonUtils.toJson(historyInvoiceVOS));
        List<SaasHistoryInvoiceVO> convert = ModelConverterUtils.convert(historyInvoiceVOS, SaasHistoryInvoiceVO.class);
        logger.info("历史回票转换后数据"+JsonUtils.toJson(convert));
        iOrderUpdateRpcCallerSaasApi.hitoryInvoice(convert);
        return GlobalResponseCode.Success;
    }

    @Override
    @Transactional(value = "saas")
    public void createApplyByTradeInfo(List<HashMap> tradeList) {
        if (ObjUtils.isNotEmpty(tradeList)){
            List<ApplyOrder> applyOrderList=Lists.newArrayList();
            List<FinanceCostInfoVO> financeCostInfoVOList=Lists.newArrayList();
            for (HashMap trade:tradeList){
                ApplyOrder applyOrder=new ApplyOrder();
                String applyId= IDTool.CreateUniqueID();
                applyOrder.setId(applyId);
                applyOrder.setState(1);
                applyOrder.setCreateTime(new Date());
                applyOrder.setUpdateTime(new Date());
                applyOrder.setEmployeeId((String) trade.get("userId"));
                applyOrder.setCompanyId((String) trade.get("companyId"));
                applyOrder.setType(16);
                applyOrder.setApplyOrderType(9);
                applyOrder.setBudget(0);
                applyOrder.setRootApplyOrderId(applyId);
                handleFinanceCostList(trade,applyId,financeCostInfoVOList,(String) trade.get("userId"));
                applyOrderList.add(applyOrder);
            }
            applyOrderExtMapper.batchInsert(applyOrderList);
            iFinanceCostService.batchInsert(financeCostInfoVOList);
        }
    }

    @Override
    public GlobalResponseCode updateReturnStatus(ApplyWriteoffReturnStatusContract returnStatusContract,String userId,String companyId) {
        if (ObjUtils.isEmpty(returnStatusContract.getApplyReurnStatus())){
            return GlobalResponseCode.ParameterError;
        }
        ApplyOrder order = applyOrderMapper.selectByPrimaryKey(returnStatusContract.getApplyId());
        iApplyOrderService.updateReturnStatus(returnStatusContract,order);
        KafkaBankApplyMsg applyMsg=new KafkaBankApplyMsg();
        applyMsg.setApplyId(order.getId());
        applyMsg.setCompanyId(order.getCompanyId());
        applyMsg.setPushTime(DateUtils.format(new Date()));
        applyMsg.setStatus(order.getReturnTicket());
        applyMsg.setUserId(order.getEmployeeId());
        iApplyOrderKafkaProducerService.syncApplyorderReturnTicket(applyMsg);
        return GlobalResponseCode.Success;
    }

    @Override
    public Map queryReturnticketStatusByApplyIds(List<String> applyIdList) {
        logger.info("查询申请单回票状态数据:"+JsonUtils.toJson(applyIdList));
        ApplyOrderExample example =new ApplyOrderExample();
        example.createCriteria().andTypeEqualTo(ApplyType.VirtualCardWriteOff.getValue()).andStateEqualTo(ApplyStatus.Approved.getValue())
                .andIdIn(applyIdList);
        List<ApplyOrder> orderList= applyOrderMapper.selectByExample(example);
        //Map<String,Integer> map= orderList.stream().collect(Collectors.toMap(ApplyOrder::getId,ApplyOrder::getReturnTicket));
        Map <String,Integer> map =Maps.newHashMap();
        orderList.forEach(v->{
            map.put(v.getId(),v.getReturnTicket());
        });
        return map;
    }

    @Override
    public void updateWriteoffPdf(String id) {
        ApplyOrder applyOrder = applyOrderMapper.selectByPrimaryKey(id);
        Map<String, Object> writeoffMap = sloveWriteoffTemplate(applyOrder);
        producePdf(applyOrder, writeoffMap);
        logger.info(String.format("核销审批单id:%s", applyOrder.getId()));
    }

    @Override
    public void updateWriteoffPdfByCompanyId(String companyId) {
        ApplyOrderExample example =new ApplyOrderExample();
        example.createCriteria().andCompanyIdEqualTo(companyId).andTypeEqualTo(ApplyType.VirtualCardWriteOff.getValue()).andStateEqualTo(2).andDeleteStatusEqualTo(0);
        List<ApplyOrder> applyOrderList = applyOrderMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(applyOrderList)){
            for (ApplyOrder applyOrder : applyOrderList){
                try {
                    Map<String, Object> writeoffMap = sloveWriteoffTemplate(applyOrder);
                    producePdf(applyOrder, writeoffMap);
                    logger.info(String.format("核销审批单id:%s", applyOrder.getId()));
                }catch (Exception e){
                    logger.info("核销审批单pdf错误"+applyOrder.getId());
                }
            }
        }
    }

    private void handleFinanceCostList(Map trade,String applyId,List<FinanceCostInfoVO> financeCostInfoVOList,String userId){
        FinanceCostInfoVO financeCostInfoVO=new FinanceCostInfoVO();
        financeCostInfoVO.setApplyOrderId(applyId);
        financeCostInfoVO.setCreateUser(userId);
        if (ObjUtils.isNotEmpty(trade.get("replaceCostCauseId"))){
            financeCostInfoVO.setCostCauseId((Integer) trade.get("replaceCostCauseId"));
            financeCostInfoVO.setCostCause(ObjUtils.toString(ObjUtils.ifNull(trade.get("replaceCostCause"),"")));
            financeCostInfoVO.setCostCauseExt((String) trade.get("replaceCostCauseExt"));
        }else {
            financeCostInfoVO.setCostCauseId((Integer) trade.get("costCauseId"));
            financeCostInfoVO.setCostCause((String) trade.get("costCause"));
            financeCostInfoVO.setCostCauseExt((String) trade.get("costCauseExt"));
        }
        if (ObjUtils.isNotEmpty(trade.get("replaceCostCategoryId"))){
            financeCostInfoVO.setCostCategoryId(ObjUtils.toInteger(trade.get("replaceCostCategoryId")));
            financeCostInfoVO.setCostCategory((String) trade.get("replaceCostCategory"));
            financeCostInfoVO.setCostCategoryExt((String) trade.get("replaceCostCategoryExt"));
        }else {
            financeCostInfoVO.setCostCategoryId((Integer) trade.get("costCategoryId"));
            financeCostInfoVO.setCostCategory((String) trade.get("costCategory"));
            financeCostInfoVO.setCostCategoryExt((String) trade.get("costCategoryExt"));
        }
        financeCostInfoVO.setReason((String) trade.get("replaceReason"));
        if (ObjUtils.isNotEmpty(trade.get("costDesc"))){
            financeCostInfoVO.setCostDesc((String) trade.get("costDesc"));
        }else {
            financeCostInfoVO.setCostDesc((String) trade.get("replaceCostDesc"));
        }
        Map priceMap= (Map) trade.get("totalPrice");
        BigDecimal totalPrice= ObjUtils.toBigDecimal(priceMap.get("price"),BigDecimal.ZERO);
        financeCostInfoVO.setTotalAmount(totalPrice);
        Map tradeMap=Maps.newHashMap();
        tradeMap.put("bankAccountNo",trade.get("bankAccountNo"));
        tradeMap.put("orderId",trade.get("orderId"));
        tradeMap.put("shopName",trade.get("shopName"));
        tradeMap.put("totalPrice",trade.get("totalPrice"));
        tradeMap.put("transactionType",trade.get("transactionType"));
        tradeMap.put("createTime",trade.get("createTime"));
        tradeMap.put("monthType",trade.get("monthType"));
        tradeMap.put("checkStatus",trade.get("checkStatus"));
        List<Map> tradeList=Lists.newArrayList(tradeMap);
        List<Map> invoiceMapList=Lists.newArrayList();
        financeCostInfoVO.setTradeInformationList(tradeList);
        List<Map> invoiceList= (List<Map>) trade.get("invoiceList");
        if (ObjUtils.isNotEmpty(invoiceList)){
            for (Map invoice:invoiceList){
                Map invoiceMap=Maps.newHashMap();
                invoiceMap.put("fbInvId",invoice.get("invoiceId"));
                invoiceMap.put("invCode",invoice.get("invoiceCode"));
                invoiceMap.put("invNo",invoice.get("invoiceNo"));
                Map invoicePrice= (Map) invoice.get("invoicePrice");
                if (ObjUtils.isNotEmpty(invoicePrice)){
                    invoiceMap.put("totalPricePlusTax",invoicePrice.get("price"));
                }
                invoiceMap.put("issuedDate",invoice.get("invoiceDate"));
                invoiceMap.put("invTitle",invoice.get("invoiceReceiver"));
                invoiceMap.put("invFromSource",invoice.get("invoiceSource"));
                Map invoiceTag= (Map) invoice.get("invoiceTag");
                if (ObjUtils.isNotEmpty(invoiceTag)){
                    invoiceMap.put("einvStatus",invoiceTag.get("key"));
                }
                invoiceMapList.add(invoiceMap);
            }
            financeCostInfoVO.setInvoiceList(invoiceMapList);
        }
        financeCostInfoVOList.add(financeCostInfoVO);
    }

    /**
     * 批量释放预算
     * @param costIds
     */
    private void batchRelease(List<Integer> costIds, String applyId) {
        List<FinanceCostInfoVO> costs = iFinanceCostService.getCostListById(costIds);
        List<FinanceCostInfoVO> offlineCosts = costs.stream().filter(financeCostInfoVO -> financeCostInfoVO.getType().equals(1)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(offlineCosts)) {
            List<String> releaseCostIds = offlineCosts.stream().map(financeCostInfoVO -> ObjUtils.toString(financeCostInfoVO.getId())).collect(Collectors.toList());
            List<BatchDeductAndReleaseReq> batchDeductAndReleaseReqArrayList = Lists.newArrayList();
            for (String costId : releaseCostIds) {
                BatchDeductAndReleaseReq batchDeductAndReleaseReq = new BatchDeductAndReleaseReq();
                batchDeductAndReleaseReq.setApplyId(applyId);
                batchDeductAndReleaseReq.setCostId(costId);
                batchDeductAndReleaseReqArrayList.add(batchDeductAndReleaseReq);
            }
            orderCostService.batchRelease(batchDeductAndReleaseReqArrayList);
        }
    }

    /**
     * 批量扣减预算
     * @param costIds
     */
    private void batchDeduct(List<Integer> costIds, String applyId){
        List<FinanceCostInfoVO> costs = iFinanceCostService.getCostListById(costIds);
        List<FinanceCostInfoVO> offlineCosts = costs.stream().filter(financeCostInfoVO -> financeCostInfoVO.getType().equals(1)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(offlineCosts)) {
            List<String> deductCostIds = offlineCosts.stream().map(financeCostInfoVO -> ObjUtils.toString(financeCostInfoVO.getId())).collect(Collectors.toList());
            List<BatchDeductAndReleaseReq> batchDeductAndReleaseReqArrayList = Lists.newArrayList();
            for (String costId : deductCostIds) {
                BatchDeductAndReleaseReq batchDeductAndReleaseReq = new BatchDeductAndReleaseReq();
                batchDeductAndReleaseReq.setApplyId(applyId);
                batchDeductAndReleaseReq.setCostId(costId);
                batchDeductAndReleaseReqArrayList.add(batchDeductAndReleaseReq);
            }
            orderCostService.batchDeduct(batchDeductAndReleaseReqArrayList);
        }
    }

}

