package com.fenbeitong.saas.core.model.fenbeitong;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class BizTripRuleExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table biz_trip_rule
     *
     * @mbg.generated Sat Aug 26 10:59:12 CST 2017
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table biz_trip_rule
     *
     * @mbg.generated Sat Aug 26 10:59:12 CST 2017
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table biz_trip_rule
     *
     * @mbg.generated Sat Aug 26 10:59:12 CST 2017
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table biz_trip_rule
     *
     * @mbg.generated Sat Aug 26 10:59:12 CST 2017
     */
    public BizTripRuleExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table biz_trip_rule
     *
     * @mbg.generated Sat Aug 26 10:59:12 CST 2017
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table biz_trip_rule
     *
     * @mbg.generated Sat Aug 26 10:59:12 CST 2017
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table biz_trip_rule
     *
     * @mbg.generated Sat Aug 26 10:59:12 CST 2017
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table biz_trip_rule
     *
     * @mbg.generated Sat Aug 26 10:59:12 CST 2017
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table biz_trip_rule
     *
     * @mbg.generated Sat Aug 26 10:59:12 CST 2017
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table biz_trip_rule
     *
     * @mbg.generated Sat Aug 26 10:59:12 CST 2017
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table biz_trip_rule
     *
     * @mbg.generated Sat Aug 26 10:59:12 CST 2017
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table biz_trip_rule
     *
     * @mbg.generated Sat Aug 26 10:59:12 CST 2017
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table biz_trip_rule
     *
     * @mbg.generated Sat Aug 26 10:59:12 CST 2017
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table biz_trip_rule
     *
     * @mbg.generated Sat Aug 26 10:59:12 CST 2017
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table biz_trip_rule
     *
     * @mbg.generated Sat Aug 26 10:59:12 CST 2017
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(String value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(String value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(String value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(String value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(String value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(String value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLike(String value) {
            addCriterion("company_id like", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotLike(String value) {
            addCriterion("company_id not like", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<String> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<String> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(String value1, String value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(String value1, String value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andAirRuleIdIsNull() {
            addCriterion("air_rule_id is null");
            return (Criteria) this;
        }

        public Criteria andAirRuleIdIsNotNull() {
            addCriterion("air_rule_id is not null");
            return (Criteria) this;
        }

        public Criteria andAirRuleIdEqualTo(String value) {
            addCriterion("air_rule_id =", value, "airRuleId");
            return (Criteria) this;
        }

        public Criteria andAirRuleIdNotEqualTo(String value) {
            addCriterion("air_rule_id <>", value, "airRuleId");
            return (Criteria) this;
        }

        public Criteria andAirRuleIdGreaterThan(String value) {
            addCriterion("air_rule_id >", value, "airRuleId");
            return (Criteria) this;
        }

        public Criteria andAirRuleIdGreaterThanOrEqualTo(String value) {
            addCriterion("air_rule_id >=", value, "airRuleId");
            return (Criteria) this;
        }

        public Criteria andAirRuleIdLessThan(String value) {
            addCriterion("air_rule_id <", value, "airRuleId");
            return (Criteria) this;
        }

        public Criteria andAirRuleIdLessThanOrEqualTo(String value) {
            addCriterion("air_rule_id <=", value, "airRuleId");
            return (Criteria) this;
        }

        public Criteria andAirRuleIdLike(String value) {
            addCriterion("air_rule_id like", value, "airRuleId");
            return (Criteria) this;
        }

        public Criteria andAirRuleIdNotLike(String value) {
            addCriterion("air_rule_id not like", value, "airRuleId");
            return (Criteria) this;
        }

        public Criteria andAirRuleIdIn(List<String> values) {
            addCriterion("air_rule_id in", values, "airRuleId");
            return (Criteria) this;
        }

        public Criteria andAirRuleIdNotIn(List<String> values) {
            addCriterion("air_rule_id not in", values, "airRuleId");
            return (Criteria) this;
        }

        public Criteria andAirRuleIdBetween(String value1, String value2) {
            addCriterion("air_rule_id between", value1, value2, "airRuleId");
            return (Criteria) this;
        }

        public Criteria andAirRuleIdNotBetween(String value1, String value2) {
            addCriterion("air_rule_id not between", value1, value2, "airRuleId");
            return (Criteria) this;
        }

        public Criteria andHotelRuleIdIsNull() {
            addCriterion("hotel_rule_id is null");
            return (Criteria) this;
        }

        public Criteria andHotelRuleIdIsNotNull() {
            addCriterion("hotel_rule_id is not null");
            return (Criteria) this;
        }

        public Criteria andHotelRuleIdEqualTo(String value) {
            addCriterion("hotel_rule_id =", value, "hotelRuleId");
            return (Criteria) this;
        }

        public Criteria andHotelRuleIdNotEqualTo(String value) {
            addCriterion("hotel_rule_id <>", value, "hotelRuleId");
            return (Criteria) this;
        }

        public Criteria andHotelRuleIdGreaterThan(String value) {
            addCriterion("hotel_rule_id >", value, "hotelRuleId");
            return (Criteria) this;
        }

        public Criteria andHotelRuleIdGreaterThanOrEqualTo(String value) {
            addCriterion("hotel_rule_id >=", value, "hotelRuleId");
            return (Criteria) this;
        }

        public Criteria andHotelRuleIdLessThan(String value) {
            addCriterion("hotel_rule_id <", value, "hotelRuleId");
            return (Criteria) this;
        }

        public Criteria andHotelRuleIdLessThanOrEqualTo(String value) {
            addCriterion("hotel_rule_id <=", value, "hotelRuleId");
            return (Criteria) this;
        }

        public Criteria andHotelRuleIdLike(String value) {
            addCriterion("hotel_rule_id like", value, "hotelRuleId");
            return (Criteria) this;
        }

        public Criteria andHotelRuleIdNotLike(String value) {
            addCriterion("hotel_rule_id not like", value, "hotelRuleId");
            return (Criteria) this;
        }

        public Criteria andHotelRuleIdIn(List<String> values) {
            addCriterion("hotel_rule_id in", values, "hotelRuleId");
            return (Criteria) this;
        }

        public Criteria andHotelRuleIdNotIn(List<String> values) {
            addCriterion("hotel_rule_id not in", values, "hotelRuleId");
            return (Criteria) this;
        }

        public Criteria andHotelRuleIdBetween(String value1, String value2) {
            addCriterion("hotel_rule_id between", value1, value2, "hotelRuleId");
            return (Criteria) this;
        }

        public Criteria andHotelRuleIdNotBetween(String value1, String value2) {
            addCriterion("hotel_rule_id not between", value1, value2, "hotelRuleId");
            return (Criteria) this;
        }

        public Criteria andTrainRuleIdIsNull() {
            addCriterion("train_rule_id is null");
            return (Criteria) this;
        }

        public Criteria andTrainRuleIdIsNotNull() {
            addCriterion("train_rule_id is not null");
            return (Criteria) this;
        }

        public Criteria andTrainRuleIdEqualTo(String value) {
            addCriterion("train_rule_id =", value, "trainRuleId");
            return (Criteria) this;
        }

        public Criteria andTrainRuleIdNotEqualTo(String value) {
            addCriterion("train_rule_id <>", value, "trainRuleId");
            return (Criteria) this;
        }

        public Criteria andTrainRuleIdGreaterThan(String value) {
            addCriterion("train_rule_id >", value, "trainRuleId");
            return (Criteria) this;
        }

        public Criteria andTrainRuleIdGreaterThanOrEqualTo(String value) {
            addCriterion("train_rule_id >=", value, "trainRuleId");
            return (Criteria) this;
        }

        public Criteria andTrainRuleIdLessThan(String value) {
            addCriterion("train_rule_id <", value, "trainRuleId");
            return (Criteria) this;
        }

        public Criteria andTrainRuleIdLessThanOrEqualTo(String value) {
            addCriterion("train_rule_id <=", value, "trainRuleId");
            return (Criteria) this;
        }

        public Criteria andTrainRuleIdLike(String value) {
            addCriterion("train_rule_id like", value, "trainRuleId");
            return (Criteria) this;
        }

        public Criteria andTrainRuleIdNotLike(String value) {
            addCriterion("train_rule_id not like", value, "trainRuleId");
            return (Criteria) this;
        }

        public Criteria andTrainRuleIdIn(List<String> values) {
            addCriterion("train_rule_id in", values, "trainRuleId");
            return (Criteria) this;
        }

        public Criteria andTrainRuleIdNotIn(List<String> values) {
            addCriterion("train_rule_id not in", values, "trainRuleId");
            return (Criteria) this;
        }

        public Criteria andTrainRuleIdBetween(String value1, String value2) {
            addCriterion("train_rule_id between", value1, value2, "trainRuleId");
            return (Criteria) this;
        }

        public Criteria andTrainRuleIdNotBetween(String value1, String value2) {
            addCriterion("train_rule_id not between", value1, value2, "trainRuleId");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table biz_trip_rule
     *
     * @mbg.generated do_not_delete_during_merge Sat Aug 26 10:59:12 CST 2017
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table biz_trip_rule
     *
     * @mbg.generated Sat Aug 26 10:59:12 CST 2017
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}