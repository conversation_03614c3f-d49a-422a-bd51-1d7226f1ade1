package com.fenbeitong.saas.core.contract.organization.inner;

public class DinnerPolicyBean {

    private Boolean dinner_priv_flag;
    private Boolean rule_limit_flag;
    private String rule_id;
    private Integer exceed_buy_flag;

    public DinnerPolicyBean(Boolean dinner_priv_flag, Boolean rule_limit_flag, String rule_id, Integer exceed_buy_flag) {
        this.dinner_priv_flag = dinner_priv_flag;
        this.rule_limit_flag = rule_limit_flag;
        this.rule_id = rule_id;
        this.exceed_buy_flag = exceed_buy_flag;
    }

    public Boolean getDinner_priv_flag() {
        return dinner_priv_flag;
    }

    public void setDinner_priv_flag(Boolean dinner_priv_flag) {
        this.dinner_priv_flag = dinner_priv_flag;
    }

    public Boolean getRule_limit_flag() {
        return rule_limit_flag;
    }

    public Integer getExceed_buy_flag() {
        return exceed_buy_flag;
    }

    public void setExceed_buy_flag(Integer exceed_buy_flag) {
        this.exceed_buy_flag = exceed_buy_flag;
    }

    public Boolean isRule_limit_flag() {
        return rule_limit_flag;
    }

    public void setRule_limit_flag(Boolean rule_limit_flag) {
        this.rule_limit_flag = rule_limit_flag;
    }

    public String getRule_id() {
        return rule_id;
    }

    public void setRule_id(String rule_id) {
        this.rule_id = rule_id;
    }

    public DinnerPolicyBean() {
    }

}
