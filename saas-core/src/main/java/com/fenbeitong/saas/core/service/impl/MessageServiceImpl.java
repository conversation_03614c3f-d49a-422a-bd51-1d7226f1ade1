package com.fenbeitong.saas.core.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.finhub.common.constant.AccountTypeEnum;
import com.fenbeitong.finhub.common.constant.CategoryTypeEnum;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.kafka.consumer.KafkaConsumerUtils;
import com.fenbeitong.finhub.kafka.msg.saturn.KafkaSaturnOrderMsg;
import com.fenbeitong.noc.api.service.base.SaasCostDetailVO;
import com.fenbeitong.noc.api.service.bus.enums.BusOrderStatusEnum;
import com.fenbeitong.noc.api.service.bus.enums.BusRefundOrderStatusEnum;
import com.fenbeitong.noc.api.service.bus.model.vo.BusOrderAppDetailVO;
import com.fenbeitong.noc.api.service.bus.service.IBusOrderSearchService;
import com.fenbeitong.noc.api.service.model.dto.resp.NotifierInfoRespVo;
import com.fenbeitong.noc.api.service.transport.enums.TransportOrderStatusEnum;
import com.fenbeitong.saas.core.common.ApplicationContextUtils;
import com.fenbeitong.saas.core.common.DynamicDataSourceDecision;
import com.fenbeitong.saas.core.common.base.SaasException;
import com.fenbeitong.saas.core.common.constant.GlobalResponseCode;
import com.fenbeitong.saas.core.common.constant.OrderCategoryTypeEnum;
import com.fenbeitong.saas.core.common.constant.SaasMessageConstant;
import com.fenbeitong.saas.core.contract.common.PageResultContract;
import com.fenbeitong.saas.core.contract.message.*;
import com.fenbeitong.saas.core.contract.message.inner.ApplyInfo;
import com.fenbeitong.saas.core.contract.message.inner.ConsumptionInfo;
import com.fenbeitong.saas.core.contract.message.inner.OrderInfo;
import com.fenbeitong.saas.core.dao.fenbeitong.TaxiMapperExt;
import com.fenbeitong.saas.core.dao.saas.MessageMapper;
import com.fenbeitong.saas.core.dao.saas.MessageMapperEx;
import com.fenbeitong.saas.core.dao.saas.MessageMapperVoEx;
import com.fenbeitong.saas.core.language.CoreLanguage;
import com.fenbeitong.saas.core.model.enums.apply.ApplyLogAction;
import com.fenbeitong.saas.core.model.enums.apply.ApplyStatus;
import com.fenbeitong.saas.core.model.enums.message.BizType;
import com.fenbeitong.saas.core.model.enums.message.MessageType;
import com.fenbeitong.saas.core.model.enums.message.SenderType;
import com.fenbeitong.saas.core.model.enums.message.TaxiScheduleType;
import com.fenbeitong.saas.core.model.enums.user.UserRole;
import com.fenbeitong.saas.core.model.saas.*;
import com.fenbeitong.saas.core.service.*;
import com.fenbeitong.saas.core.utils.notice.EmailContract;
import com.fenbeitong.saas.core.utils.notice.NoticeUtils;
import com.fenbeitong.saas.core.utils.notice.TemplateConstant;
import com.fenbeitong.saas.core.utils.tools.DateTimeTool;
import com.fenbeitong.saas.core.utils.tools.PageUtils;
import com.fenbeitong.saas.core.utils.transfer.MessageTransferUtil;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.fenbeitong.usercenter.api.service.privilege.IPrivilegeService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.luastar.swift.base.entity.PageInfo;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.StrUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Created by kinakihiro on 2017/4/12.
 */
@Service("messageService")
public class MessageServiceImpl implements IMessageService {

    private static Logger LOGGER = LoggerFactory.getLogger(MessageServiceImpl.class);

    @Autowired
    private TransportLargeOverService transportLargeOverService;
    @Resource
    private MessageMapper messageMapper;
    @Resource
    private MessageMapperEx messageMapperEx;

    @Autowired
    private MessageMapperVoEx messageMapperVoEx;

    @Resource
    private ICustomRoleService customRoleService;
    @Resource
    private IMessageSetupService messageSetupService;

    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyOrderMapper applyOrderMapper;

    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyOrderLogMapper applyOrderLogMapper;

    @Autowired
    private TaxiMapperExt taxiMapperExt;

    @Autowired
    private IBaseOrganizationService iBaseOrganizationService;

    @Autowired
    private IPrivilegeService iPrivilegeService;

    @Autowired
    private IBusOrderSearchService iBusOrderSearchService;

    private void validateMessage(MessageSaveContract saveContract) throws SaasException {
        if (saveContract == null) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        MessageType messageType = MessageType.getEnum(saveContract.getMessage_type());
        if (messageType == null) {
            throw new SaasException(GlobalResponseCode.MessageTypeError);
        }
        if (messageType.equals(MessageType.Apply)) {
            validateApply(saveContract.getApply_info());
        }
        if (messageType.equals(MessageType.Consume)) {
            validateConsume(saveContract.getConsumption_info());
        }
        if (messageType.equals(MessageType.Order)) {
            validateOrderInfo(saveContract.getOrder_info());
        }
        if (messageType.equals(MessageType.OrderAlert) && saveContract.getOrder_alert_info() == null) {
            throw new SaasException(GlobalResponseCode.MessageTypeNoMatchInfo);
        }
        if (!messageType.equals(MessageType.System) && !messageType.equals(MessageType.Transaction) && StringUtils.isEmpty(saveContract.getBiz_order())) {
            throw new SaasException(GlobalResponseCode.BizOrderIdNotNull);
        }
        SenderType senderType = SenderType.getEnum(saveContract.getSender_type());
        if (senderType == null) {
            throw new SaasException(GlobalResponseCode.SenderTypeError);
        }
        if (senderType.equals(SenderType.Person) && StringUtils.isEmpty(saveContract.getSender())) {
            throw new SaasException(GlobalResponseCode.SenderNotNull);
        }
        if (StringUtils.isEmpty(saveContract.getTitle())
                || StringUtils.isEmpty(saveContract.getContent())
                || StringUtils.isEmpty(saveContract.getReceiver())) {
            throw new SaasException(GlobalResponseCode.MsgNotNull);
        }
    }

    private void validateApply(ApplyInfo applyInfo) throws SaasException {
        if (applyInfo == null) {
            throw new SaasException(GlobalResponseCode.MessageTypeNoMatchInfo);
        }
        BizType bizType = BizType.getEnum(applyInfo.getApply_type());
        if (bizType == null || bizType.equals(BizType.Nothing)) {
            throw new SaasException(GlobalResponseCode.ApplyTypeError);
        }
        if (!BizType.getApplyType().contains(bizType)) {
            throw new SaasException(GlobalResponseCode.ApplyTypeError);
        }
        if (StringUtils.isEmpty(applyInfo.getApply_msg()) || StringUtils.isEmpty(applyInfo.getApply_time())) {
            throw new SaasException(GlobalResponseCode.MsgNotNull);
        }
        if (DateTimeTool.fromStringToDateTime(applyInfo.getApply_time()) == null) {
            Date apply_time = DateTimeTool.fromDefaultFormatStringToDateTime(applyInfo.getApply_time());
            if (apply_time == null) {
                throw new SaasException(GlobalResponseCode.DateFormatError);
            }
            applyInfo.setApply_time(DateTimeTool.fromDateTimeToString(apply_time));
        }
    }

    private void validateConsume(ConsumptionInfo consumptionInfo) throws SaasException {
        if (consumptionInfo == null) {
            throw new SaasException(GlobalResponseCode.MessageTypeNoMatchInfo);
        }
        BizType bizType = BizType.getEnum(consumptionInfo.getConsumption_type());
        if (bizType == null || bizType.equals(BizType.Nothing)) {
            throw new SaasException(GlobalResponseCode.ConsumptionTypeError);
        }
        if (!BizType.getBizType().contains(bizType)) {
            throw new SaasException(GlobalResponseCode.ConsumptionTypeError);
        }
        if (StringUtils.isEmpty(consumptionInfo.getConsume_time())
                || StringUtils.isEmpty(consumptionInfo.getCreator_msg())
                || StringUtils.isEmpty(consumptionInfo.getPrice_msg())) {
            throw new SaasException(GlobalResponseCode.MsgNotNull);
        }
        if (DateTimeTool.fromStringToDateTime(consumptionInfo.getConsume_time()) == null) {
            Date consume_time = DateTimeTool.fromDefaultFormatStringToDateTime(consumptionInfo.getConsume_time());
            if (consume_time == null) {
                throw new SaasException(GlobalResponseCode.DateFormatError);
            }
            consumptionInfo.setConsume_time(DateTimeTool.fromDateTimeToString(consume_time));
        }
        if (bizType.equals(BizType.AirPlane)) {
            if (consumptionInfo.getAir_info() == null) {
                throw new SaasException(GlobalResponseCode.ConsumptionTypeError);
            }
            if (StringUtils.isEmpty(consumptionInfo.getAir_info().getPassenger_msg())
                    || StringUtils.isEmpty(consumptionInfo.getAir_info().getTravel_msg())) {
                throw new SaasException(GlobalResponseCode.MsgNotNull);
            }
        }
        if (bizType.equals(BizType.InternationalAirPlane)) {
            if (consumptionInfo.getAir_info() == null) {
                throw new SaasException(GlobalResponseCode.ConsumptionTypeError);
            }
            if (StringUtils.isEmpty(consumptionInfo.getAir_info().getPassenger_msg())
                    || StringUtils.isEmpty(consumptionInfo.getAir_info().getTravel_msg())) {
                throw new SaasException(GlobalResponseCode.MsgNotNull);
            }
        }
        if (bizType.equals(BizType.Hotel)) {
            if (consumptionInfo.getHotel_info() == null) {
                throw new SaasException(GlobalResponseCode.ConsumptionTypeError);
            }
            if (StringUtils.isEmpty(consumptionInfo.getHotel_info().getGuest_msg())
                    || StringUtils.isEmpty(consumptionInfo.getHotel_info().getTravel_msg())) {
                throw new SaasException(GlobalResponseCode.MsgNotNull);
            }
        }
        if (bizType.equals(BizType.Taxi)) {
            if (consumptionInfo.getTaxi_info() == null) {
                throw new SaasException(GlobalResponseCode.ConsumptionTypeError);
            }
            if (StringUtils.isEmpty(consumptionInfo.getTaxi_info().getPassenger_msg())
                    || StringUtils.isEmpty(consumptionInfo.getTaxi_info().getTravel_msg())) {
                throw new SaasException(GlobalResponseCode.MsgNotNull);
            }
        }
        if (bizType.equals(BizType.Train)) {
            if (consumptionInfo.getTrain_info() == null) {
                throw new SaasException(GlobalResponseCode.ConsumptionTypeError);
            }
            if (StringUtils.isEmpty(consumptionInfo.getTrain_info().getPassenger_msg())
                    || StringUtils.isEmpty(consumptionInfo.getTrain_info().getTravel_msg())) {
                throw new SaasException(GlobalResponseCode.MsgNotNull);
            }
        }
        if (bizType.equals(BizType.Mall)) {
            if (consumptionInfo.getMall_info() == null) {
                throw new SaasException(GlobalResponseCode.ConsumptionTypeError);
            }
            if (StringUtils.isEmpty(consumptionInfo.getMall_info().getProducts_msg())) {
                throw new SaasException(GlobalResponseCode.MsgNotNull);
            }
        }
    }

    private void validateOrderInfo(OrderInfo orderInfo) throws SaasException {
        if (orderInfo == null) {
            throw new SaasException(GlobalResponseCode.MessageTypeNoMatchInfo);
        }
        BizType bizType = BizType.getEnum(orderInfo.getOrder_type());
        if (bizType == null || bizType.equals(BizType.Nothing)) {
            throw new SaasException(GlobalResponseCode.OrderTypeError);
        }
        if (!BizType.getOrderType().contains(bizType)) {
            throw new SaasException(GlobalResponseCode.OrderTypeError);
        }
        if (StringUtils.isEmpty(orderInfo.getCreate_time())
                || StringUtils.isEmpty(orderInfo.getOrder_msg())
                || StringUtils.isEmpty(orderInfo.getOrder_status_msg())
                || StringUtils.isEmpty(orderInfo.getRedirect_order_id())) {
            throw new SaasException(GlobalResponseCode.MsgNotNull);
        }
        if (DateTimeTool.fromStringToDateTime(orderInfo.getCreate_time()) == null) {
            Date create_time = DateTimeTool.fromDefaultFormatStringToDateTime(orderInfo.getCreate_time());
            if (create_time == null) {
                throw new SaasException(GlobalResponseCode.DateFormatError);
            }
            orderInfo.setCreate_time(DateTimeTool.fromDateTimeToString(create_time));
        }
    }

    @Override
    public boolean saveMessage(MessageSaveContract saveContract) throws SaasException {
        validateMessage(saveContract);
        Message message = MessageTransferUtil.getMessage(saveContract);
        messageMapper.insertSelective(message);
        LOGGER.info("创建消息成功！id={}, comment={}", message.getId(), message.getComment());
        return true;
    }

    @Override
    public JSONObject getGeneralMessages(String companyId, String receiver) throws SaasException {
        MessageExample example = new MessageExample();
        MessageExample.Criteria criteria = example.createCriteria()
                .andReceiverEqualTo(receiver)
                .andReadEqualTo(false);
        int consumePermission = messageSetupService.queryConsumeInfoPermission(companyId, receiver);
        if (consumePermission == 1) {
            criteria.andMsgTypeIn(Lists.newArrayList(
                    MessageType.Consume.getCode(),
                    MessageType.Apply.getCode(),
                    MessageType.Order.getCode(),
                    MessageType.System.getCode()
            ));
        } else {
            criteria.andMsgTypeIn(Lists.newArrayList(
                    MessageType.Apply.getCode(),
                    MessageType.Order.getCode(),
                    MessageType.System.getCode()
            ));
        }
        long totalCount = messageMapper.countByExample(example);
        // 兼容老版本，默认都置为0和空
        JSONObject messageJson = new JSONObject();
        messageJson.put("unread_count", 0);
        messageJson.put("unread_state", false);
        messageJson.put("msg_title", "");
        messageJson.put("msg_desc", "");
        // 返回结果
        JSONObject response = new JSONObject();
        response.put("spend_notify", messageJson);
        response.put("approval_notify", messageJson);
        response.put("order_notify", messageJson);
        response.put("booking_notify", messageJson);
        response.put("system_notify", messageJson);
        response.put("total_unread_count", totalCount);
        response.put("total_unread_state", totalCount > 0);
        return response;
    }

    @Override
    public JSONObject getMessage(String receiver,
                                 BizType consumeType,
                                 String lastId,
                                 Integer pageSize,
                                 MessageType messageType,
                                 String ip) throws SaasException {
        if (StringUtils.isEmpty(lastId)) {
            lastId = null;
        }
        MessageExample example = getMessageExample(receiver, consumeType, messageType);
        long totalCount = messageMapper.countByExample(example);
        List<Message> messageList = messageMapperEx.selectByExample(example, lastId, pageSize);
        messageRead(messageType, receiver, ip);
        return getMessageJson(messageList, totalCount);
    }

    private MessageExample getMessageExample(String receiver, BizType bizType, MessageType messageType) {
        MessageExample example = new MessageExample();
        example.setOrderByClause("id desc");
        MessageExample.Criteria criteria = example.createCriteria();
        criteria.andMsgTypeEqualTo(messageType.getCode());
        switch (messageType) {
            case Consume:
                criteria.andReceiverEqualTo(receiver);
                if (bizType != BizType.AllConsume) {
                    criteria.andBizTypeEqualTo(bizType.getCode());
                }
                break;
            case Order:
                criteria.andReceiverEqualTo(receiver);
                if (bizType != BizType.AllOrder) {
                    criteria.andBizTypeEqualTo(bizType.getCode());
                }
                break;
            case Apply:
                criteria.andReceiverEqualTo(receiver);
                if (bizType != BizType.AllApply) {
                    criteria.andBizTypeEqualTo(bizType.getCode());
                }
                break;
            case OrderAlert:
                criteria.andReceiverEqualTo(receiver);
                break;
            default:
                break;
        }
        return example;
    }

    private void messageRead(MessageType messageType, String user, String ip) {
        // 所有未读消息置为已读
        MessageExample example = new MessageExample();
        MessageExample.Criteria criteria = example.createCriteria()
                .andReceiverEqualTo(user)
                .andReadEqualTo(false);
        if (messageType != null && messageType != MessageType.All) {
            criteria.andMsgTypeEqualTo(messageType.getCode());
        }
        Message message = new Message();
        message.setRead(true);
        message.setUpdateTime(new Date());
        messageMapper.updateByExampleSelective(message, example);
    }

    @Override
    public Set<String> getReceivers(String employeeId, String companyId) {
        Set<String> receiverIdSet = new HashSet<>();
        // 获取消费消息设置
        MessageSetup messageSetup = messageSetupService.queryCompanyMessageSetupWithoutDefault(companyId, SaasMessageConstant.ITEM_CODE_CONSUME_INFO_NOTICE);
        // 消息设置是否设置过
        if (messageSetup == null) {
            List<String> adminIds = customRoleService.getEmployeeIdByRoleId(Integer.toString(UserRole.CompanySuperAdmin.getValue()), companyId);
            if (CollectionUtils.isNotEmpty(adminIds)) {
                receiverIdSet.addAll(adminIds);
            }
            List<String> companyAdminIds = customRoleService.getEmployeeIdByRoleId(Integer.toString(UserRole.CompanyAdmin.getValue()), companyId);
            if (CollectionUtils.isNotEmpty(companyAdminIds)) {
                receiverIdSet.addAll(companyAdminIds);
            }
        } else {
            // 是否接收消费消息
            if (messageSetup.getIsChecked() != null
                    && messageSetup.getIsChecked().intValue() == SaasMessageConstant.IS_CHECKED_TRUE) {
                // 改成从企业消息设置的消费设置接收人列表
                List<MessageSetupReceiver> messageSetupReceiverList = messageSetupService.queryMessageReceiverList(companyId, SaasMessageConstant.BUSI_CODE_CONSUME_INFO);
                if (CollectionUtils.isNotEmpty(messageSetupReceiverList)) {
                    for (MessageSetupReceiver receiver : messageSetupReceiverList) {
                        receiverIdSet.add(receiver.getUserId());
                    }
                }
            }
        }
        return receiverIdSet;
    }

    private JSONObject getMessageJson(List<Message> messageList, long totalCount) {
        JSONObject resp = new JSONObject();
        resp.put("total_count", totalCount);
        JSONArray array = new JSONArray();
        if (CollectionUtils.isNotEmpty(messageList)) {
            for (Message message : messageList) {
                JSONObject messageJson = new JSONObject();
                messageJson.put("id", message.getId());
                messageJson.put("title", message.getTitle());
                messageJson.put("content", message.getComment());
                messageJson.put("message_type", message.getMsgType());
                messageJson.put("link", message.getLink());
                messageJson.put("biz_order", message.getBizOrder());
                messageJson.putAll(JSONObject.parseObject(message.getInfo()));
                if (message.getMsgType() == MessageType.Consume.getCode()) {
                    addBizMessageInConsumeInfo(messageJson, JSONObject.parseObject(message.getInfo()));
                }
                array.add(messageJson);
            }
        }
        resp.put("count", array.size());
        resp.put("message_list", array);
        return resp;
    }

    private void addBizMessageInConsumeInfo(JSONObject message, JSONObject consumeInfo) {
        StringBuilder bizMessage = new StringBuilder();
        BizType bizType = BizType.getEnum(consumeInfo.getInteger("consumption_type"));
        switch (bizType) {
            case Mall:
                bizMessage.append(consumeInfo.getString("creator_msg"))
                        .append(CoreLanguage.Order_Value_MsgBuy.getMessage())
                        .append(consumeInfo.getJSONObject("mall_info").getString("products_msg"))
                        .append(";" + CoreLanguage.Order_Value_MsgAmount.getMessage() + ":")
                        .append(consumeInfo.getString("price_msg"))
                        .append(";");
                break;
            case Taxi:
                bizMessage.append(consumeInfo.getString("creator_msg"))
                        .append(CoreLanguage.Order_Value_MsgReserved.getMessage())
                        .append(consumeInfo.getJSONObject("taxi_info").getString("travel_msg"))
                        .append(";" + CoreLanguage.Order_Value_MsgAmount.getMessage() + ":")
                        .append(consumeInfo.getString("price_msg"))
                        .append(";" + CoreLanguage.Order_Value_MsgCarPassengers.getMessage() + ":")
                        .append(consumeInfo.getJSONObject("taxi_info").getString("passenger_msg"));
                break;
            case Hotel:
                bizMessage.append(consumeInfo.getString("creator_msg"))
                        .append(CoreLanguage.Order_Value_MsgReserved.getMessage())
                        .append(consumeInfo.getJSONObject("hotel_info").getString("travel_msg"))
                        .append(";" + CoreLanguage.Order_Value_MsgAmount.getMessage() + ":")
                        .append(consumeInfo.getString("price_msg"))
                        .append(";" + CoreLanguage.Order_Value_MsgOccupant.getMessage() + ":")
                        .append(consumeInfo.getJSONObject("hotel_info").getString("guest_msg"));
                break;
            case Train:
                bizMessage.append(consumeInfo.getString("creator_msg"))
                        .append(CoreLanguage.Order_Value_MsgReserved.getMessage())
                        .append(consumeInfo.getJSONObject("train_info").getString("travel_msg"))
                        .append(";" + CoreLanguage.Order_Value_MsgAmount.getMessage() + ":")
                        .append(consumeInfo.getString("price_msg"))
                        .append(";" + CoreLanguage.Order_Value_MsgCarPassengers.getMessage() + ":")
                        .append(consumeInfo.getJSONObject("train_info").getString("passenger_msg"));
                break;
            case AirPlane:
                bizMessage.append(consumeInfo.getString("creator_msg"))
                        .append(CoreLanguage.Order_Value_MsgReserved.getMessage())
                        .append(consumeInfo.getJSONObject("air_info").getString("travel_msg"))
                        .append(";" + CoreLanguage.Order_Value_MsgAmount.getMessage() + ":")
                        .append(consumeInfo.getString("price_msg"))
                        .append(";" + CoreLanguage.Order_Value_MsgAirPassengers.getMessage() + ":")
                        .append(consumeInfo.getJSONObject("air_info").getString("passenger_msg"));
                break;
            default:
                break;
        }
        message.put("biz_message", bizMessage);
    }

    /**
     * 消息列表查询
     *
     * @param msgType
     * @param orderType
     * @param consumeType
     * @param pageNum
     * @param pageSize
     * @param userId
     * @param companyId
     * @return
     * @auth Lelonta
     */
    @Override
    public PageResultContract<MessageListContract> msgList(MessageType msgType,
                                                           BizType orderType,
                                                           BizType consumeType,
                                                           Integer pageNum,
                                                           Integer pageSize,
                                                           String userId,
                                                           String companyId,
                                                           String ip) throws ParseException {


        // 创建消息的example
        MessageExample example = new MessageExample();
        example.setPageSize(pageSize);
        example.setOffset(pageSize * (pageNum - 1));
        MessageExample.Criteria criteria = example.createCriteria();
        criteria.andReceiverEqualTo(userId);
        switch (msgType) {
            case All:
                if (StringUtils.isBlank(companyId)) {
                    criteria.andMsgTypeIn(Lists.newArrayList(
                            MessageType.Order.getCode(),
                            MessageType.System.getCode()
                    )).andCompanyIdEqualTo(userId);
                } else {
                    disposeApplyMessage(companyId, userId, criteria);
                }
                break;
            case Consume:
                criteria.andMsgTypeEqualTo(msgType.getCode());
                if (StringUtils.isBlank(companyId)) {
                    criteria.andCompanyIdEqualTo(userId);
                } else {
                    criteria.andCompanyIdEqualTo(companyId);
                }
                if (consumeType == BizType.AllConsume) {
                    criteria.andBizTypeIn(Lists.newArrayList(
                            BizType.AirPlane.getCode(),
                            BizType.InternationalAirPlane.getCode(),
                            BizType.Hotel.getCode(),
                            BizType.Train.getCode(),
                            BizType.Mall.getCode(),
                            BizType.Taxi.getCode(),
                            BizType.Dinner.getCode(),
                            BizType.Takeaway.getCode(),
                            BizType.MeiShi.getCode()
                    ));
                } else {
                    criteria.andBizTypeEqualTo(consumeType.getCode());
                }
                break;
            case Apply:
                if (StringUtils.isBlank(companyId)) {
                    PageResultContract pageResultContract = new PageResultContract(0, pageSize.longValue(), Lists.newArrayList());
                    return pageResultContract;
                }
                criteria.andMsgTypeEqualTo(msgType.getCode());
                break;
            case Order:
                if (StringUtils.isBlank(companyId)) {
                    criteria.andCompanyIdEqualTo(userId);
                }
                criteria.andMsgTypeEqualTo(msgType.getCode());
                // (7,"机票"), (40,"国际机票"), (11,"酒店"), (15,"火车"),(30,"用餐"),(102,"所有订单提醒");
                if (orderType == BizType.AllOrder) {
                    criteria.andBizTypeIn(Lists.newArrayList(
                            BizType.AirPlane.getCode(),
                            BizType.InternationalAirPlane.getCode(),
                            BizType.Hotel.getCode(),
                            BizType.Train.getCode(),
                            BizType.Dinner.getCode(),
                            BizType.ElectronicCoupon.getCode(),
                            BizType.Takeaway.getCode(),
                            BizType.Ticket.getCode(),
                            BizType.MeiShi.getCode()
                    ));
                } else {
                    criteria.andBizTypeEqualTo(orderType.getCode());
                }
                break;
            case System:
                if (StringUtils.isBlank(companyId)) {
                    criteria.andCompanyIdEqualTo(userId);
                }
                criteria.andMsgTypeEqualTo(msgType.getCode());
                break;
            default:
                criteria.andMsgTypeEqualTo(msgType.getCode());
                break;
        }
        long totalCount = messageMapperVoEx.countMessage(example);
        example.setOrderByClause("create_time desc");
        List<MessageListContract> msgList = messageMapperVoEx.selectMsgList(example);
        LOGGER.info("获取消息列表：{}", JsonUtils.toJson(msgList));
        List<String> applyIdList = Lists.newArrayList();
        List<Integer> logIdList = Lists.newArrayList();
        if (msgType == MessageType.Apply || msgType == MessageType.All) {
            for (MessageListContract msg : msgList) {
                MessageType dbMsgType = MessageType.getEnum(msg.getMsgType());
                if (dbMsgType != MessageType.Apply) {
                    continue;
                }
                String applyId = ObjUtils.toString(msg.getBizOrder(), "");
                if (!applyIdList.contains(applyId)) {
                    applyIdList.add(applyId);
                }
                JSONObject infoObject = com.alibaba.fastjson.JSON.parseObject(msg.getInfo());
                if (infoObject == null || infoObject.isEmpty()) {
                    continue;
                }
                Integer logId = ObjUtils.toInteger(infoObject.get("log_id"));
                if (logId == null) {
                    continue;
                }
                if (!logIdList.contains(logId)) {
                    logIdList.add(logId);
                }
            }
        }
        Map<String, ApplyOrder> applyOrderMap = Maps.newHashMap();
        Map<Integer, ApplyOrderLog> applyOrderLogMap = Maps.newHashMap();
        Map<String, List<ApplyOrderLog>> applyOrderLogListMap = Maps.newHashMap();
        if (ObjUtils.isNotEmpty(applyIdList)) {
            ApplyOrderExample applyOrderExample = new ApplyOrderExample();
            applyOrderExample.createCriteria().andIdIn(applyIdList).andDeleteStatusEqualTo(0);
            List<ApplyOrder> applyOrderList = applyOrderMapper.selectByExample(applyOrderExample);
            if (ObjUtils.isNotEmpty(applyOrderList)) {
                for (ApplyOrder applyOrder : applyOrderList) {
                    applyOrderMap.put(applyOrder.getId(), applyOrder);
                }
            }
            ApplyOrderLogExample applyOrderLogExample = new ApplyOrderLogExample();
            applyOrderLogExample.createCriteria().andApplyOrderIdIn(applyIdList).andSponsorIdEqualTo(userId).andCreateTimeIsNotNull();
            applyOrderLogExample.setOrderByClause("sort, create_time ASC");
            List<ApplyOrderLog> allApplyOrderLogList = applyOrderLogMapper.selectByExample(applyOrderLogExample);
            if (ObjUtils.isNotEmpty(allApplyOrderLogList)) {
                for (ApplyOrderLog applyOrderLog : allApplyOrderLogList) {
                    String applyOrderId = applyOrderLog.getApplyOrderId();
                    List<ApplyOrderLog> applyOrderLogList;
                    if (applyOrderLogListMap.containsKey(applyOrderId)) {
                        applyOrderLogList = applyOrderLogListMap.get(applyOrderId);
                    } else {
                        applyOrderLogList = Lists.newArrayList();
                        applyOrderLogListMap.put(applyOrderId, applyOrderLogList);
                    }
                    applyOrderLogList.add(applyOrderLog);
                }
            }
        }
        if (ObjUtils.isNotEmpty(logIdList)) {
            ApplyOrderLogExample applyOrderLogExample = new ApplyOrderLogExample();
            applyOrderLogExample.createCriteria().andIdIn(logIdList);
            List<ApplyOrderLog> applyOrderLogList = applyOrderLogMapper.selectByExample(applyOrderLogExample);
            if (ObjUtils.isNotEmpty(applyOrderLogList)) {
                for (ApplyOrderLog applyOrderLog : applyOrderLogList) {
                    applyOrderLogMap.put(applyOrderLog.getId(), applyOrderLog);
                }
            }
        }
        List<Object> resultList = new ArrayList<>();
        // 遍历这个list
        for (MessageListContract msg : msgList) {
            MessageType dbMsgType = MessageType.getEnum(msg.getMsgType());
            switch (dbMsgType) {
                case Consume:
                    resultList.add(listConsumeConstract(msg));
                    break;
                case Apply:
                    resultList.add(listApplyContract(msg, applyOrderMap, applyOrderLogMap, applyOrderLogListMap));
                    break;
                case Order:
                    resultList.add(listOrderContract(msg));
                    break;
                case System:
                    resultList.add(listSystemContract(msg));
                    break;
                default:
                    break;
            }
        }
        PageResultContract pageResultContract = new PageResultContract(totalCount, pageSize.longValue(), resultList);
        // 将消息置为已读
        messageRead(msgType, userId, ip);
        return pageResultContract;
    }

    /**
     * 获取所有审批消息
     *
     * @param msg
     * @param applyOrderMap
     * @param applyOrderLogMap
     * @param applyOrderLogListMap
     * @return
     */
    public MessageAppContract listApplyContract(MessageListContract msg, Map<String, ApplyOrder> applyOrderMap, Map<Integer, ApplyOrderLog> applyOrderLogMap, Map<String, List<ApplyOrderLog>> applyOrderLogListMap) throws ParseException {
        MessageAppContract messageAppContract = new MessageAppContract();
        messageAppContract.setMsg_type(msg.getMsgType());
        messageAppContract.setComment(msg.getComment());
        String applyId = ObjUtils.toString(msg.getBizOrder(), "");
        messageAppContract.setApply_id(applyId);
        JSONObject infoObject = com.alibaba.fastjson.JSON.parseObject(msg.getInfo());
        JSONObject linkObject = com.alibaba.fastjson.JSON.parseObject(msg.getLink());
        String orderId = ObjUtils.toString(linkObject.get("order_id"), "");
        Integer orderType = ObjUtils.toInteger(linkObject.get("order_type"));
        Integer settingType = ObjUtils.toInteger(linkObject.get("setting_type"));
        if (orderId != null) {
            messageAppContract.setOrder_id(orderId);
        }
        if (orderType != null) {
            messageAppContract.setOrder_type(orderType);
        }
        if (settingType != null) {
            messageAppContract.setSetting_type(settingType);
        } else {
            messageAppContract.setSetting_type(1);//1行程审批 2事中审批
        }
        if (infoObject == null || infoObject.isEmpty()) {
            return messageAppContract;
        }
        // 申请时间
        String applyTime = ObjUtils.toString(infoObject.get("apply_time"));
        if (applyTime != null) {
            messageAppContract.setApply_time(dateFormat(applyTime));
        }
        // 审批类型
        BizType applyType = BizType.getEnum(ObjUtils.toInteger(infoObject.get("apply_type")));
        if (applyType != BizType.Nothing) {
            messageAppContract.setApply_type(applyType.getCode());
        }
        // myself
        boolean myself = ObjUtils.toBoolean(infoObject.get("myself"), false);
        messageAppContract.setMyself(myself);
        // viewType
        Integer viewType = ObjUtils.toInteger(infoObject.get("view_type"));
        if (viewType == null) {
            // 如果viewType，判断myself
            viewType = myself ? 1 : 2;
        }
        messageAppContract.setView_type(viewType);
        // 设置审批状态
        if (viewType == 1 || viewType == 3) {
            // 我提交的或抄给送我的
            ApplyOrder order = applyOrderMap.get(applyId);
            if (order == null) {
                messageAppContract.setApply_status(-1); // 删除状态
            } else {
                messageAppContract.setApply_status(order.getState());
            }
            setTitle(messageAppContract, orderType, settingType, applyType, viewType);
        } else if (viewType == 2) {
            // 提交给我的
            Integer logId = ObjUtils.toInteger(infoObject.get("log_id"));
            if (logId == null) {
                ApplyOrder order = applyOrderMap.get(applyId);
                if (order == null) {
                    messageAppContract.setApply_status(-1); // 删除状态
                } else {
                    // 查询审批日志
                    /*ApplyOrderLogVoContract applyOrderLogVoContract = new ApplyOrderLogVoContract();
                    applyOrderLogVoContract.setApplyOrderId(applyId);
                    applyOrderLogVoContract.setSponsorId(msg.getReceiver());
                    applyOrderLogVoContract.setEndTime(msg.getCreateTime());
                    List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectByForConditon(applyOrderLogVoContract);*/
                    List<ApplyOrderLog> applyOrderLogList = applyOrderLogListMap.get(applyId);
                    if (CollectionUtils.isEmpty(applyOrderLogList)) {
                        messageAppContract.setApply_status(ApplyLogAction.Approval.getValue());
                    } else {
                        ApplyOrderLog log = applyOrderLogList.get(0);
                        if (log.getCreateTime().compareTo(msg.getCreateTime()) > 0) {
                            if (log.getAction() == ApplyLogAction.Approval.getValue()) {
                                int stateNo = stateORorther(applyId);
                                messageAppContract.setApply_status(stateNo);
                            } else {
                                messageAppContract.setApply_status(log.getAction());
                            }
                        } else {
                            messageAppContract.setApply_status(ApplyLogAction.Approval.getValue());
                        }
                    }
                }
            } else {
                ApplyOrderLog log = applyOrderLogMap.get(logId);
                if (log != null) {

                    if (log.getAction() == ApplyLogAction.Approval.getValue()) {
                        int stateNo = stateORorther(applyId);
                        messageAppContract.setApply_status(stateNo);
                    } else {
                        messageAppContract.setApply_status(log.getAction());
                    }
                }
            }
            setTitle(messageAppContract, orderType, settingType, applyType, viewType);
        }
        return messageAppContract;
    }

    private void setTitle(MessageAppContract messageAppContract, Integer orderType, Integer settingType, BizType applyType, Integer viewType) {
        if (viewType == 1) {
            //根据审批类型设置标题
            if (applyType != BizType.Nothing) {
                //settingType 1行程审批 2订单审批 3采购审批
                if (settingType != null && settingType == 2) {
                    LOGGER.info("获取审批消息：viewType={}，settingType={}，orderType={}", viewType, settingType, orderType);
                    orderType = (orderType == null) ? 0 : orderType;
                    switch (orderType) {
                        case 7:
                            messageAppContract.setTitle(MessageType.ApplyResult.getName() + "-" + BizType.AirPlaneApply.getName());
                            break;
                        case 40:
                            messageAppContract.setTitle(MessageType.ApplyResult.getName() + "-" + BizType.IntlAirPlaneApply.getName());
                            break;
                        case 15:
                            messageAppContract.setTitle(MessageType.ApplyResult.getName() + "-" + BizType.TrainApply.getName());
                            break;
                        case 11:
                            messageAppContract.setTitle(MessageType.ApplyResult.getName() + "-" + BizType.HotelApply.getName());
                            break;
                        case 30:
                            messageAppContract.setTitle(MessageType.ApplyResult.getName() + "-" + BizType.OverruleMealApply.getName());
                            break;
                        case 20:
                            messageAppContract.setTitle(MessageType.ApplyResult.getName() + "-" + BizType.MallApply.getName());
                            break;
                        default:
                            messageAppContract.setTitle(MessageType.ApplyResult.getName());
                            break;
                    }
                } else if (settingType != null && settingType == 3) {
                    messageAppContract.setTitle(MessageType.ApplyResult.getName() + "-" + BizType.MallApply.getName());
                } else {
                    switch (applyType) {
                        case AirPlane:
                            messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.AirPlane.getName());
                            break;
                        case InternationalAirPlane:
                            messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.InternationalAirPlane.getName());
                            break;
                        case Hotel:
                            messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.Hotel.getName());
                            break;
                        case Train:
                            messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.Train.getName());
                            break;
                        case TravelApply:
                            messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.TravelApply.getName());
                            break;
                        case TaxiApply:
                            messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.TaxiApply.getName());
                            break;
                        case MallApply:
                            messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.MallApply.getName());
                            break;
                        default:
                            break;
                    }
                }
            }
        } else if (viewType == 3) {
            //根据审批类型设置标题
            if (applyType != BizType.Nothing) {
                //settingType 1行程审批 2订单审批 3采购审批
                if (settingType != null && settingType == 2) {
                    LOGGER.info("获取审批消息：viewType={}，settingType={}，orderType={}", viewType, settingType, orderType);
                    orderType = (orderType == null) ? 0 : orderType;
                    switch (orderType) {
                        case 7:
                            messageAppContract.setTitle(MessageType.ApplyCopy.getName() + "-" + BizType.AirPlaneApply.getName());
                            break;
                        case 40:
                            messageAppContract.setTitle(MessageType.ApplyCopy.getName() + "-" + BizType.IntlAirPlaneApply.getName());
                            break;
                        case 15:
                            messageAppContract.setTitle(MessageType.ApplyCopy.getName() + "-" + BizType.TrainApply.getName());
                            break;
                        case 11:
                            messageAppContract.setTitle(MessageType.ApplyCopy.getName() + "-" + BizType.HotelApply.getName());
                            break;
                        case 30:
                            messageAppContract.setTitle(MessageType.ApplyCopy.getName() + "-" + BizType.OverruleMealApply.getName());
                            break;
                        case 20:
                            messageAppContract.setTitle(MessageType.ApplyCopy.getName() + "-" + BizType.MallApply.getName());
                            break;
                        default:
                            messageAppContract.setTitle(MessageType.ApplyCopy.getName());
                            break;
                    }
                } else if (settingType != null && settingType == 3) {
                    messageAppContract.setTitle(MessageType.ApplyCopy.getName() + "-" + BizType.MallApply.getName());
                } else {
                    switch (applyType) {
                        case AirPlane:
                            messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.AirPlane.getName());
                            break;
                        case InternationalAirPlane:
                            messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.InternationalAirPlane.getName());
                            break;
                        case Hotel:
                            messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.Hotel.getName());
                            break;
                        case Train:
                            messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.Train.getName());
                            break;
                        case TravelApply:
                            messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.TravelApply.getName());
                            break;
                        case TaxiApply:
                            messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.TaxiApply.getName());
                            break;
                        case MallApply:
                            messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.MallApply.getName());
                            break;
                        default:
                            break;
                    }
                }
            }
        } else {
            //根据审批类型设置标题
            if (applyType != BizType.Nothing) {
                //settingType 1行程审批 2订单审批 3采购审批
                if (settingType != null && settingType == 2) {
                    LOGGER.info("获取审批消息：viewType={}，settingType={}，orderType={}", viewType, settingType, orderType);
                    orderType = (orderType == null) ? 0 : orderType;
                    switch (orderType) {
                        case 7:
                            messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.AirPlaneApply.getName());
                            break;
                        case 40:
                            messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.IntlAirPlaneApply.getName());
                            break;
                        case 15:
                            messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.TrainApply.getName());
                            break;
                        case 11:
                            messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.HotelApply.getName());
                            break;
                        case 30:
                            messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.OverruleMealApply.getName());
                            break;
                        case 20:
                            messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.MallApply.getName());
                            break;
                        default:
                            messageAppContract.setTitle(MessageType.Apply.getName());
                            break;
                    }
                } else if (settingType != null && settingType == 3) {
                    messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.MallApply.getName());
                } else {
                    switch (applyType) {
                        case AirPlane:
                            messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.AirPlane.getName());
                            break;
                        case InternationalAirPlane:
                            messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.InternationalAirPlane.getName());
                            break;
                        case Hotel:
                            messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.Hotel.getName());
                            break;
                        case Train:
                            messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.Train.getName());
                            break;
                        case TravelApply:
                            messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.TravelApply.getName());
                            break;
                        case TaxiApply:
                            messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.TaxiApply.getName());
                            break;
                        case MallApply:
                            messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.MallApply.getName());
                            break;
                        default:
                            break;
                    }
                }
            }
        }
    }

//    /**
//     * 获取所有审批消息
//     *
//     * @param msg
//     * @return
//     */
//    public MessageAppContract listApplyContract(MessageListContract msg) {
//        MessageAppContract messageAppContract = new MessageAppContract();
//        messageAppContract.setMsg_type(msg.getMsgType());
//        messageAppContract.setComment(msg.getComment());
//        String applyId = ObjUtils.toString(msg.getBizOrder(), "");
//        messageAppContract.setApply_id(applyId);
//        JSONObject infoObject = com.alibaba.fastjson.JSON.parseObject(msg.getInfo());
//        JSONObject linkObject = com.alibaba.fastjson.JSON.parseObject(msg.getLink());
//        String orderId = ObjUtils.toString(linkObject.get("order_id"), "");
//        Integer orderType = ObjUtils.toInteger(linkObject.get("order_type"));
//        Integer settingType = ObjUtils.toInteger(linkObject.get("setting_type"));
//        if (orderId != null) {
//            messageAppContract.setOrder_id(orderId);
//        }
//        if (orderType != null) {
//            messageAppContract.setOrder_type(orderType);
//        }
//
//        if (settingType != null) {
//            messageAppContract.setSetting_type(settingType);
//        } else {
//            messageAppContract.setSetting_type(1);//1行程审批 2事中审批
//        }
//        if (infoObject == null || infoObject.isEmpty()) {
//            return messageAppContract;
//        }
//        // 申请时间
//        String applyTime = ObjUtils.toString(infoObject.get("apply_time"));
//        if (applyTime != null) {
//            messageAppContract.setApply_time(timeStrToString(applyTime));
//        }
//        // 审批类型
//        BizType applyType = BizType.getEnum(ObjUtils.toInteger(infoObject.get("apply_type")));
//        if (applyType != BizType.Nothing) {
//            messageAppContract.setApply_type(applyType.getCode());
//            //settingType 1行程审批 2事中审批
//            if (settingType != null && settingType == 2) {
//                messageAppContract.setTitle(MessageType.Apply.getName() + "-" + "费用审批");
//            } else {
//                switch (applyType) {
//                    case AirPlane:
//                        messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.AirPlane.getName());
//                        break;
//                    case InternationalAirPlane:
//                        messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.InternationalAirPlane.getName());
//                        break;
//                    case Hotel:
//                        messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.Hotel.getName());
//                        break;
//                    case Train:
//                        messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.Train.getName());
//                        break;
//                    case TravelApply:
//                        messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.TravelApply.getName());
//                        break;
//                    case TaxiApply:
//                        messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.TaxiApply.getName());
//                        break;
//                    case MallApply:
//                        messageAppContract.setTitle(MessageType.Apply.getName() + "-" + BizType.MallApply.getName());
//                        break;
//                    default:
//                        break;
//                }
//            }
//        }
//        // myself
//        boolean myself = ObjUtils.toBoolean(infoObject.get("myself"), false);
//        messageAppContract.setMyself(myself);
//        // viewType
//        Integer viewType = ObjUtils.toInteger(infoObject.get("view_type"));
//        if (viewType == null) {
//            // 如果viewType，判断myself
//            viewType = myself ? 1 : 2;
//        }
//        messageAppContract.setView_type(viewType);
//        // 设置审批状态
//        if (viewType == 1 || viewType == 3) {
//            // 我提交的或抄给送我的
//            ApplyOrder order = applyOrderMapper.selectByPrimaryKey(applyId);
//            if (order == null) {
//                messageAppContract.setApply_status(-1); // 删除状态
//            } else {
//                messageAppContract.setApply_status(order.getState());
//            }
//        } else if (viewType == 2) {
//            // 提交给我的
//            Integer logId = ObjUtils.toInteger(infoObject.get("log_id"));
//            if (logId == null) {
//                ApplyOrder order = applyOrderMapper.selectByPrimaryKey(applyId);
//                if (order == null) {
//                    messageAppContract.setApply_status(-1); // 删除状态
//                } else {
//                    // 查询审批日志
//                    ApplyOrderLogVoContract applyOrderLogVoContract = new ApplyOrderLogVoContract();
//                    applyOrderLogVoContract.setApplyOrderId(applyId);
//                    applyOrderLogVoContract.setSponsorId(msg.getReceiver());
//                    applyOrderLogVoContract.setEndTime(msg.getCreateTime());
//                    List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectByForConditon(applyOrderLogVoContract);
//                    if (CollectionUtils.isEmpty(applyOrderLogList)) {
//                        messageAppContract.setApply_status(ApplyLogAction.Approval.getValue());
//                    } else {
//                        ApplyOrderLog log = applyOrderLogList.get(0);
//                        if (log.getAction() == ApplyLogAction.Approval.getValue()) {
//                            int stateNo = stateORorther(applyId);
//                            messageAppContract.setApply_status(stateNo);
//                        } else {
//                            messageAppContract.setApply_status(log.getAction());
//                        }
//                    }
//                }
//            } else {
//                ApplyOrderLog log = applyOrderLogMapper.selectByPrimaryKey(logId);
//                if (log != null) {
//
//                    if (log.getAction() == ApplyLogAction.Approval.getValue()) {
//                        int stateNo = stateORorther(applyId);
//                        messageAppContract.setApply_status(stateNo);
//                    } else {
//                        messageAppContract.setApply_status(log.getAction());
//                    }
//                }
//
//            }
//        }
//        return messageAppContract;
//    }

    /**
     * 获取所有消费消息
     *
     * @param msg
     * @return
     */
    public MessageConsuContract listConsumeConstract(MessageListContract msg) throws ParseException {
        MessageConsuContract messageConsuContract = new MessageConsuContract();
        messageConsuContract.setMsg_type(msg.getMsgType());
        messageConsuContract.setOrder_type(msg.getBizType());
        messageConsuContract.setOrder_id(msg.getBizOrder());
        JSONObject infoObject = com.alibaba.fastjson.JSON.parseObject(msg.getInfo());
        messageConsuContract.setConsume_time("");
        messageConsuContract.setCreator_msg("");
        messageConsuContract.setPrice_msg("");
        if (infoObject != null && !infoObject.isEmpty()) {
            String consumeTime = infoObject.getString("consume_time");
            if (StringUtils.isNotBlank(consumeTime)) {
                messageConsuContract.setConsume_time(dateFormat(consumeTime));
            }
            messageConsuContract.setCreator_msg(infoObject.getString("creator_msg"));
            String priceMsg = infoObject.getString("price_msg");
            String reg = "[\u4e00-\u9fa5]";
            Pattern pat = Pattern.compile(reg);
            Matcher mat = pat.matcher(priceMsg);
            String repickStr = mat.replaceAll("");
            if (repickStr.contains("¥")) {
                String str2 = priceMsg.substring(1, repickStr.length());
                messageConsuContract.setPrice_msg(str2);
            } else {
                BizType bizType = BizType.getEnum(msg.getBizType());
                if (bizType.equals(BizType.InternationalAirPlane)) {
                    messageConsuContract.setPrice_msg(ObjUtils.toBigDecimal(repickStr).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                } else {
                    messageConsuContract.setPrice_msg(repickStr);
                }
            }
        }
        // (3,"用车"),(7,"机票"),(40,"国际机票"),(11,"酒店"),(15,"火车"),(20,"采购"),(100,"所有消费")
        BizType bizType = BizType.getEnum(msg.getBizType());
        switch (bizType) {
            case Taxi:
                StringBuilder title = new StringBuilder()
                        .append(MessageType.Consume.getName())
                        .append("-")
                        .append(BizType.Taxi.getName());
                messageConsuContract.setPassenger_msg("");
                JSONObject taxi_info = infoObject.getJSONObject("taxi_info");
                if (taxi_info != null) {
                    messageConsuContract.setPassenger_msg(taxi_info.getString("passenger_msg"));
                    // 用车类型
                    TaxiScheduleType scheduleType = TaxiScheduleType.getByCode(taxi_info.getInteger("schedule_type"));
                    if (scheduleType != null) {
                        title.append("-").append(scheduleType.getName());
                    }
                }
                messageConsuContract.setTitle(title.toString());
                break;
            case AirPlane:
                messageConsuContract.setTitle(MessageType.Consume.getName() + "-" + BizType.AirPlane.getName());
                messageConsuContract.setPassenger_msg("");
                JSONObject air_info = infoObject.getJSONObject("air_info");
                if (air_info != null) {
                    messageConsuContract.setPassenger_msg(air_info.getString("passenger_msg"));
                }
                break;
            case InternationalAirPlane:
                messageConsuContract.setTitle(MessageType.Consume.getName() + "-" + BizType.InternationalAirPlane.getName());
                messageConsuContract.setPassenger_msg("");
                JSONObject intl_air_info = infoObject.getJSONObject("air_info");
                if (intl_air_info != null) {
                    messageConsuContract.setPassenger_msg(intl_air_info.getString("passenger_msg"));
                }
                break;
            case Hotel:
                messageConsuContract.setTitle(MessageType.Consume.getName() + "-" + BizType.Hotel.getName());
                messageConsuContract.setPassenger_msg("");
                JSONObject hotel_info = infoObject.getJSONObject("hotel_info");
                if (hotel_info != null) {
                    messageConsuContract.setPassenger_msg(hotel_info.getString("guest_msg"));
                }
                break;
            case Train:
                messageConsuContract.setTitle(MessageType.Consume.getName() + "-" + BizType.Train.getName());
                messageConsuContract.setPassenger_msg("");
                JSONObject train_info = infoObject.getJSONObject("train_info");
                if (train_info != null) {
                    messageConsuContract.setPassenger_msg(train_info.getString("passenger_msg"));
                }
                break;
            case Mall:
                messageConsuContract.setTitle(MessageType.Consume.getName() + "-" + BizType.Mall.getName());
                messageConsuContract.setPassenger_msg(infoObject.getString("creator_msg"));
                break;
            case Dinner:
                messageConsuContract.setTitle(MessageType.Consume.getName() + "-" + BizType.Dinner.getName());
                messageConsuContract.setPassenger_msg(infoObject.getString("creator_msg"));
                break;
            case Takeaway:
                messageConsuContract.setTitle(MessageType.Consume.getName() + "-" + BizType.Takeaway.getName());
                messageConsuContract.setPassenger_msg(infoObject.getString("creator_msg"));
                break;
            case MeiShi:
                messageConsuContract.setTitle(MessageType.Consume.getName() + "-" + BizType.Dinner.getName());
                messageConsuContract.setPassenger_msg(infoObject.getString("creator_msg"));
                break;
            default:
                break;
        }
        return messageConsuContract;
    }

    // 获取所有系统消息
    public MessageSysContract listSystemContract(MessageListContract msg) throws ParseException {
        MessageSysContract messageSysContract = new MessageSysContract();
        messageSysContract.setMsg_type(msg.getMsgType());
        messageSysContract.setTitle(msg.getTitle());
        messageSysContract.setComment(msg.getComment());
        messageSysContract.setBiz_type(msg.getBizType());
        messageSysContract.setBiz_order(msg.getBizOrder());
        //(3,"打车"),(7,"机票"),(40,"国际机票"),(11,"酒店"),(15,"火车"),(20,"采购"),(30,"用餐")(100,"所有消费")
        /*BizType bizType = BizType.getEnum(msg.getBizType());
        switch (bizType) {
            case Taxi:
                messageSysContract.setTitle(MessageType.Order.getName() + "-" + BizType.Taxi.getName());
                break;
            case AirPlane:
                messageSysContract.setTitle(MessageType.Order.getName() + "-" + BizType.AirPlane.getName());
                break;
            case InternationalAirPlane:
                messageSysContract.setTitle(MessageType.Order.getName() + "-" + BizType.InternationalAirPlane.getName());
                break;
            case Hotel:
                messageSysContract.setTitle(MessageType.Order.getName() + "-" + BizType.Hotel.getName());
                break;
            case Train:
                messageSysContract.setTitle(MessageType.Order.getName() + "-" + BizType.Train.getName());
                break;
            case Mall:
                messageSysContract.setTitle(MessageType.Order.getName() + "-" + BizType.Mall.getName());
                break;
            case Dinner:
                messageSysContract.setTitle(MessageType.Order.getName() + "-" + BizType.Dinner.getName());
                break;
            default:
                break;
        }*/
        JSONObject infoObject = com.alibaba.fastjson.JSON.parseObject(msg.getInfo());
        Boolean has_image = infoObject.getBoolean("has_image");
        if (has_image != null) {
            messageSysContract.setHas_image(has_image);
        }

        Object has_link = infoObject.get("has_link");
        if (has_link != null) {
            boolean has_link_boolean = Boolean.parseBoolean(has_link.toString());
            messageSysContract.setHas_link(has_link_boolean);
        }

        messageSysContract.setImage_url(infoObject.get("image_url").toString());
        messageSysContract.setRedirect_url(infoObject.get("redirect_url").toString());
        String generateTime = infoObject.get("generate_time").toString();
        if (StringUtils.isNotBlank(generateTime)) {
            messageSysContract.setGenerate_time(dateFormat(generateTime));
        }
        return messageSysContract;
    }



    //获取所有订单消息
    public MessageOdContract listOrderContract(MessageListContract msg) throws ParseException {
        MessageOdContract messageOdContract = new MessageOdContract();
        messageOdContract.setMsg_type(msg.getMsgType());
        messageOdContract.setOrder_id(msg.getBizOrder());
        messageOdContract.setComment(msg.getComment());
        messageOdContract.setTitle(msg.getTitle());
        messageOdContract.setCreate_time("");
        JSONObject infoObject = com.alibaba.fastjson.JSON.parseObject(msg.getInfo());
        if (infoObject != null && !infoObject.isEmpty()) {
            String createTime = infoObject.getString("create_time");
            if (StringUtils.isNotEmpty(createTime)) {
                messageOdContract.setCreate_time(dateFormat(createTime));
            }
            Integer order_type = infoObject.getInteger("order_type");
            if (order_type != null) {
                messageOdContract.setOrder_type(order_type);
            }
        }
        return messageOdContract;
    }


    public static String timeStrToString(String date_str) {
        try {
            int i = date_str.lastIndexOf(":");
            String reg = date_str.substring(0, i);
            return reg;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public int stateORorther(String applyId) {
        ApplyOrder order = applyOrderMapper.selectByPrimaryKey(applyId);
        int stateNo = order.getState();
        if (stateNo == ApplyStatus.Backout.getValue()) {
            return ApplyLogAction.Revoke.getValue();
        } else {
            return ApplyLogAction.Approval.getValue();
        }
    }

    @Override
    public void updateTaxiMessage() {
        LOGGER.info("开始更新用车消费通知数据");
        Integer pageNum = 1;
        Integer pageSize = 500;
        while (true) {
            LOGGER.info("更新用车消费通知数据：处理第{}页，每页{}条", pageNum, pageSize);
            PageInfo pageInfo = new PageInfo(pageNum, pageSize);
            MessageExample example = new MessageExample();
            example.setOffset(pageInfo.getStartRow());
            example.setPageSize(pageSize);
            example.setOrderByClause("create_time desc");
            example.createCriteria()
                    .andMsgTypeEqualTo(MessageType.Consume.getCode())
                    .andBizTypeEqualTo(BizType.Taxi.getCode());
            List<Message> messageList = messageMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(messageList)) {
                break;
            }
            for (Message message : messageList) {
                String orderId = message.getBizOrder();
                if (StringUtils.isEmpty(orderId)) {
                    continue;
                }
                // 获取用车类型
                List<Map<String, Object>> taxiInfoMapList = taxiMapperExt.getTaxiInfoByOrderId(orderId);
                if (CollectionUtils.isEmpty(taxiInfoMapList)) {
                    continue;
                }
                Map<String, Object> taxiMap = taxiInfoMapList.get(0);
                TaxiScheduleType scheduleType = TaxiScheduleType.getByCode(ObjUtils.toInteger(taxiMap.get("schedule_type")));
                if (scheduleType == null) {
                    continue;
                }
                JSONObject infoMap = JSONObject.parseObject(message.getInfo());
                if (infoMap == null) {
                    infoMap = new JSONObject(true);
                }
                JSONObject taxiInfoMap = infoMap.getJSONObject("taxi_info");
                if (taxiInfoMap == null) {
                    taxiInfoMap = new JSONObject();
                }
                if (taxiInfoMap.containsKey("schedule_type")) {
                    continue;
                }
                // 增加用车类型信息
                taxiInfoMap.put("schedule_type", scheduleType.getCode());
                infoMap.put("taxi_info", taxiInfoMap);
                // 更新信息
                LOGGER.info("更新订单{}用车类型{}", orderId, scheduleType);
                Message upMessage = new Message();
                upMessage.setId(message.getId());
                upMessage.setInfo(infoMap.toJSONString());
                messageMapper.updateByPrimaryKeySelective(upMessage);
            }
            pageNum = pageNum + 1;
        }
        LOGGER.info("结束更新用车消费通知数据");
    }

    /**
     * 首页查询最近几条消息
     *
     * @param number
     * @param companyId
     * @param userId
     * @return
     * @throws SaasException
     */
    @Override
    public List<MessageListContract> queryNewestMessage(Integer number, String companyId, String userId) throws SaasException, ParseException {
        List<MessageListContract> newMessageList = Lists.newArrayList();
        if (StringUtils.isBlank(userId)) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        if (number != null && number < 1) {
            number = 10;
        }
        // 创建消息的example
        MessageExample example = new MessageExample();
        example.setPageSize(number);
        example.setOffset(0);
        MessageExample.Criteria criteria = example.createCriteria();
        criteria.andReceiverEqualTo(userId);
        if (StringUtils.isBlank(companyId)) {
            criteria.andMsgTypeIn(Lists.newArrayList(
                    MessageType.Order.getCode(),
                    MessageType.System.getCode()
            )).andCompanyIdEqualTo(userId);
        } else {
            disposeApplyMessage(companyId, userId, criteria);
        }
        List<MessageListContract> msgList = messageMapperVoEx.selectMsgList(example);
        LOGGER.info("获取消息列表：{}", JsonUtils.toJson(msgList));
        for (MessageListContract messageListContract : msgList) {
            MessageListContract messageListContractInfo = new MessageListContract();
            messageListContractInfo.setComment(messageListContract.getComment());
            messageListContractInfo.setMsg_type(messageListContract.getMsgType());
            Integer msgType = messageListContract.getMsgType();
            if (msgType == MessageType.Consume.getCode()) {
                Integer bizType = messageListContract.getBizType();
                messageListContractInfo.setComment(getConsumeMessageComment(BizType.getEnum(bizType)));
            }
            if (msgType == MessageType.Order.getCode()) {
                JSONObject jo = JSONObject.parseObject(messageListContract.getInfo());
                if (jo != null) {
                    String orderStatusMsg = ObjUtils.toString(jo.get("order_status_msg"));
                    BizType orderType = BizType.getEnum(ObjUtils.toInteger(jo.get("order_type")));
                    messageListContractInfo.setComment(getOrderMessageComment(orderType, orderStatusMsg, companyId, messageListContract));
                }
            }
            if (msgType == MessageType.Apply.getCode()) {
                String comment = messageListContractInfo.getComment();
                if (StringUtils.isNotBlank(comment)) {
                    if (comment.contains("，")) {
                        messageListContractInfo.setComment(comment.split("，")[0]);
                    } else if (comment.contains(",")) {
                        messageListContractInfo.setComment(comment.split(",")[0]);
                    } else {
                        messageListContractInfo.setComment(comment);
                    }

                }
            }
            if (msgType == MessageType.System.getCode()) {
                if (messageListContract.getBizType().equals(BizType.FenbeiCoupon.getCode())) {
                    messageListContractInfo.setComment(messageListContract.getTitle());
                }
            }
            messageListContractInfo.setCreate_time(dateFormat(DateTimeTool.fromDateTimeToString(messageListContract.getCreateTime())));
            newMessageList.add(messageListContractInfo);
        }
        return newMessageList;
    }

    @Override
    public void updateMessageData() {
        long count = messageMapperEx.queryMessageByCompanyId();
        if (count <= 0) {
            LOGGER.info("无通知数据需要处理");
        }
        PageInfo pageInfo = new PageInfo(1, 2000, count);
        int pages = pageInfo.getPages();
        LOGGER.info("共有{}个需要处理的通知，分成{}次查询", count, pages);
        for (int i = 1; i <= pages; i++) {
            pageInfo.setPageNum(i);
            MessageExample exampleInfo = new MessageExample();
            exampleInfo.setPageSize(pageInfo.getPageSize());
            exampleInfo.setOffset(pageInfo.getStartRow());
            exampleInfo.createCriteria().andCompanyIdIsNull();
            exampleInfo.setOrderByClause("create_time desc");
            List<Message> messages = messageMapper.selectByExample(exampleInfo);
            for (Message message : messages) {
                String receiver = message.getReceiver();
                List<EmployeeContract> employeeList = iBaseOrganizationService.getEmployee(Lists.newArrayList(receiver), null);
                if (CollectionUtils.isNotEmpty(employeeList)) {
                    Message messageInfo = new Message();
                    messageInfo.setId(message.getId());
                    messageInfo.setCompanyId(employeeList.get(0).getCompany_id());
                    messageMapper.updateByPrimaryKeySelective(messageInfo);
                }
            }
        }
    }

    @Override
    public int queryUserOrderStatus(String companyId, String userId, String orderId, String title) {
        if (ObjUtils.isEmpty(companyId) || ObjUtils.isEmpty(userId) || ObjUtils.isEmpty(orderId) || ObjUtils.isEmpty(title)) {
            return 0;
        }
        MessageExample messageExample = new MessageExample();
        messageExample.createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andReceiverEqualTo(userId)
                .andBizOrderEqualTo(orderId)
                .andTitleEqualTo(title);
        return ObjUtils.toInteger(messageMapper.countByExample(messageExample));
    }

    @Override
    public void batchInsert(List<Message> messageList) {
        if (CollectionUtils.isEmpty(messageList)){
            return;
        }
        int totalCount = messageList.size();
        int pageSize = 500;
        int pageCount = PageUtils.countTotalPage(ObjUtils.toLong(totalCount), pageSize);
        for (int i = 1; i <= pageCount; i++) {
            List<Message> dataList;
            if (i == pageCount) {
                dataList = messageList.subList((i - 1) * pageSize, totalCount);
            } else {
                dataList = messageList.subList((i - 1) * pageSize, i * pageSize);
            }
            FinhubLogger.info("第{}次批量App消息，批量插入条数为：{}", i, dataList.size());
            messageMapperEx.batchInsert(messageList);
        }
    }

    /**
     * 判断时间显示
     *
     * @param time
     * @return
     */
    private String getTimeContent(Date time) {
        long currentTime = System.currentTimeMillis();
        long createTime = time.getTime();
        long minuteTime = createTime + 60000L;
        long hourTime = createTime + 3600000L;
        long dayTime = createTime + 86400000L;
        long thirtyDayTime = createTime + 2592000000L;
        if (currentTime < minuteTime) {
            return "刚刚";
        } else if (currentTime >= minuteTime && currentTime < hourTime) {
            long numberMinute = (currentTime - createTime) / 60000L;
            return numberMinute + "分钟前";
        } else if (currentTime >= hourTime && currentTime < dayTime) {
            long numberHour = (currentTime - createTime) / 3600000L;
            return numberHour + "小时前";
        } else if (currentTime >= dayTime && currentTime < thirtyDayTime) {
            long numberDay = (currentTime - createTime) / 86400000L;
            return numberDay + "天前";
        } else {
            return "30天前";
        }
    }

    /**
     * 处理订单通知的comment内容
     *
     * @param bizType
     * @return
     */
    private String getOrderMessageComment(BizType bizType, String orderStatusMsg, String companyId, MessageListContract messageListContract) {
        switch (bizType) {
            case AirPlane:
                InternationalAirPlane:
                return StrUtils.formatString(CoreLanguage.Order_Value_MsgPersonOrder.getMessage(), BizType.AirPlane.getName(),
                        orderStatusMsg);
            case Hotel:
                return StrUtils.formatString(CoreLanguage.Order_Value_MsgPersonApplyOrder.getMessage(), BizType.Hotel.getName(), orderStatusMsg);
            case Train:
                return StrUtils.formatString(CoreLanguage.Order_Value_MsgPersonOrder.getMessage(), BizType.Train.getName(), orderStatusMsg);
            case MeiShi:
                if (StringUtils.isNotBlank(companyId) && companyId.equals(messageListContract.getCompanyId())) {
                    return StrUtils.formatString(CoreLanguage.Order_Value_MsgPersonDinner.getMessage(), orderStatusMsg);
                }
                return StrUtils.formatString(CoreLanguage.Order_Value_MsgPersonTakeaway.getMessage(), orderStatusMsg);
            case Recharge:
                return messageListContract.getComment();
            default:
                LOGGER.error("生成apply push msg时发现没有处理的action:" + bizType.getName() + ",系统暂时只返回了actionName:" + bizType.getName());
                return StrUtils.formatString(CoreLanguage.Order_Value_MsgPersonOrder.getMessage(), bizType.getName(), orderStatusMsg);
        }
    }

    /**
     * 处理消费通知的comment内容
     *
     * @param bizType
     * @return
     */
    private String getConsumeMessageComment(BizType bizType) {
        switch (bizType) {
            case AirPlane:
                InternationalAirPlane:
                return StrUtils.formatString(CoreLanguage.Order_Value_MsgCompanyNotice.getMessage(), BizType.AirPlane.getName());
            case Hotel:
                return StrUtils.formatString(CoreLanguage.Order_Value_MsgCompanyNotice.getMessage(), BizType.Hotel.getName());
            case Train:
                return StrUtils.formatString(CoreLanguage.Order_Value_MsgCompanyNotice.getMessage(), BizType.Train.getName());
            case Mall:
                return StrUtils.formatString(CoreLanguage.Order_Value_MsgCompanyNotice.getMessage(), BizType.Mall.getName());
            case Dinner:
                return StrUtils.formatString(CoreLanguage.Order_Value_MsgCompanyNotice.getMessage(), BizType.Dinner.getName());
            case Taxi:
                return StrUtils.formatString(CoreLanguage.Order_Value_MsgCompanyNotice.getMessage(), BizType.Taxi.getName());
            case Takeaway:
                return StrUtils.formatString(CoreLanguage.Order_Value_MsgCompanyNotice.getMessage(), BizType.Takeaway.getName());
            case MeiShi:
                return StrUtils.formatString(CoreLanguage.Order_Value_MsgCompanyNotice.getMessage(), BizType.Dinner.getName());
            default:
                LOGGER.error("生成apply push msg时发现没有处理的action:" + bizType.getName() + ",系统暂时只返回了actionName:" + bizType.getName());
                return StrUtils.formatString(CoreLanguage.Order_Value_MsgCompanyNotice.getMessage(), bizType.getName());
        }
    }

    /**
     * 时间格式转换
     *
     * @param time 较小的时间
     * @return 格式：今天 HH:mm, 昨天 HH:mm, MM-dd HH:mm, yyyy-MM-dd HH:mm
     * @throws ParseException 转换异常
     * <AUTHOR>
     */
    private static String dateFormat(String time) throws ParseException {
        if (StringUtils.isEmpty(time)) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        SimpleDateFormat sdf1 = new SimpleDateFormat("HH:mm");
        SimpleDateFormat sdf2 = new SimpleDateFormat("M月d日");
//        SimpleDateFormat sdf2 = new SimpleDateFormat("MM-dd HH:mm");
//        SimpleDateFormat sdf3 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        SimpleDateFormat sdf4 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date oldTime = sdf4.parse(time);
        Date newTime = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String todayStr = format.format(newTime);
        Date today = format.parse(todayStr);
        if (newTime.getTime() - oldTime.getTime() < 60 * 1000) {
            return CoreLanguage.Common_Value_TimeCurrent.getMessage();
        } else if (newTime.getTime() - oldTime.getTime() < 6 * 60 * 1000) {
            long mins = (newTime.getTime() - oldTime.getTime()) / 1000 / 60;
            return mins + CoreLanguage.Common_Value_TimeSecondBefore.getMessage();
        } else if (today.getTime() - oldTime.getTime() <= 0) { //至少是今天
            return CoreLanguage.Common_Value_TimeToday.getMessage() + " " + sdf1.format(oldTime);
        } else if (today.getTime() - oldTime.getTime() <= 24 * 60 * 60 * 1000) {
            return CoreLanguage.Common_Value_TimeYesterday.getMessage() + " " + sdf1.format(oldTime);
        } else { //至少是前天或去年
            /*String newYear = todayStr.substring(0, 4);
            String oldYear = format.format(oldTime).substring(0, 4);
            return newYear.equals(oldYear) ? sdf2.format(oldTime) : sdf3.format(oldTime);*/
            return sdf2.format(oldTime);
        }
    }

    private void disposeApplyMessage(String companyId, String userId, MessageExample.Criteria criteria) {
        Map<String, Object> map = iPrivilegeService.queryMoudleMenu(companyId, "approval_configuration", 2);
        Boolean approvalConfiguration = ObjUtils.toBoolean(map.get("approval_configuration"), false);
        int permission = messageSetupService.queryConsumeInfoPermission(companyId, userId);
        if (permission == 1) {
            if (approvalConfiguration) {
                criteria.andMsgTypeIn(Lists.newArrayList(
                        MessageType.Consume.getCode(),
                        MessageType.Apply.getCode(),
                        MessageType.Order.getCode(),
                        MessageType.System.getCode()
                ));
            } else {
                criteria.andMsgTypeIn(Lists.newArrayList(
                        MessageType.Consume.getCode(),
                        MessageType.Order.getCode(),
                        MessageType.System.getCode()
                ));
            }
        } else {
            if (approvalConfiguration) {
                criteria.andMsgTypeIn(Lists.newArrayList(
                        MessageType.Apply.getCode(),
                        MessageType.Order.getCode(),
                        MessageType.System.getCode()
                ));
            } else {
                criteria.andMsgTypeIn(Lists.newArrayList(
                        MessageType.Order.getCode(),
                        MessageType.System.getCode()
                ));
            }
        }
    }

    /**
     * 读取企业变更信息
     *
     * @param record
     */
    @KafkaListener(group = "${group.id}", topics = {"fenbei_saturn_order_update"})
    public void addMessage(ConsumerRecord<?, ?> record) {
        LOGGER.info("【保存消息】消费者线程：{},[消息 来自kafkatopic：{},分区：{}]消息内容如下：{}",
                Thread.currentThread().getName(),
                record.topic(),
                record.partition(),
                record.value());
        KafkaSaturnOrderMsg saturnOrderMsg = KafkaConsumerUtils.invokeIMessage(record.value().toString(), KafkaSaturnOrderMsg.class);
        if (ObjectUtil.isNotNull(saturnOrderMsg)) {
            // 设置动态数据源
            DynamicDataSourceDecision.determineDataSource(saturnOrderMsg.getCompanyId());
            if (Objects.equals(saturnOrderMsg.getCategoryType(), CategoryTypeEnum.BUS.getCode())) {
                LOGGER.info("获取汽车票订单详情:"+saturnOrderMsg.getFbOrderId());
                BusOrderAppDetailVO busOrderAppDetailVO = iBusOrderSearchService.appDetailSearchNotToken(saturnOrderMsg.getFbOrderId());
                LOGGER.info("获取汽车票订单详情返回:"+JsonUtils.toJson(busOrderAppDetailVO));
                if (saturnOrderMsg.getBizOrderStatus().equals(BusOrderStatusEnum.DONE.getKey())) {
                    sendConsumeInfo(saturnOrderMsg, busOrderAppDetailVO);
                    sendLargeOver(saturnOrderMsg, busOrderAppDetailVO);
//                    sendWarnPercentInfo(saturnOrderMsg, busOrderAppDetailVO);
                    sendApplyMessageInfo(saturnOrderMsg, busOrderAppDetailVO);
                    sendOrderMessageInfo(saturnOrderMsg, busOrderAppDetailVO);
                }
                if (saturnOrderMsg.getBizOrderStatus().equals(BusOrderStatusEnum.CLOSE.getKey() )
                        || saturnOrderMsg.getBizOrderStatus().equals(BusOrderStatusEnum.DONE.getKey())
                        || saturnOrderMsg.getBizOrderStatus().equals(BusOrderStatusEnum.CANCEL_PAY.getKey())
                        || saturnOrderMsg.getBizOrderStatus().equals(BusRefundOrderStatusEnum.REFUND_DONE.getKey())
                        || saturnOrderMsg.getBizOrderStatus().equals(BusRefundOrderStatusEnum.REFUND_ING.getKey())
                        || saturnOrderMsg.getBizOrderStatus().equals(BusRefundOrderStatusEnum.REFUND_FAIL.getKey())) {
                    sendOrderInfo(saturnOrderMsg, busOrderAppDetailVO);
                }
            }
            transportLargeOverService.sendLargeOver(saturnOrderMsg);
        }
        LOGGER.info("【保存消息】,返回信息：{}", JsonUtils.toJson(saturnOrderMsg));
    }

    public void sendOrderInfo(KafkaSaturnOrderMsg saturnOrderMsg, BusOrderAppDetailVO busOrderAppDetailVO) {
        //TODO 临时处理方案
        if (Objects.isNull(busOrderAppDetailVO)){
            return;
        }
        IMessageService messageService = ApplicationContextUtils.getBean("messageService", IMessageService.class);
        MessageSaveContract messageSaveContract = new MessageSaveContract();
        OrderInfo orderInfo = new OrderInfo();
        messageSaveContract.setMessage_type(MessageType.Order.getCode());
        messageSaveContract.setTitle(CoreLanguage.Rule_Value_BusText.getMessage() + "-" + busOrderAppDetailVO.getOrderStatus().getValue());
        StringBuilder content = new StringBuilder()
                .append(StrUtils.formatString(CoreLanguage.Order_Value_MsgBusOrder.getMessage(),
                        busOrderAppDetailVO.getTravelVO().getFromStationName(),
                        busOrderAppDetailVO.getTravelVO().getToStationName(),
                        BizType.BUS.getName() + busOrderAppDetailVO.getOrderStatus().getValue()
                ));
        messageSaveContract.setContent(content.toString());
        messageSaveContract.setSender_type(SenderType.HL.getCode());
        messageSaveContract.setSender("");
        messageSaveContract.setReceiver(busOrderAppDetailVO.getUserId());
        messageSaveContract.setBiz_order(busOrderAppDetailVO.getOrderId());
        messageSaveContract.setOrder_info(orderInfo);
        //因私订单公司id存用户id
        if (saturnOrderMsg.getAccountType() != null && saturnOrderMsg.getAccountType().equals(AccountTypeEnum.PERSONAL_TYPE.getCode())) {
            messageSaveContract.setCompany_id(busOrderAppDetailVO.getUserId());
        } else {
            messageSaveContract.setCompany_id(busOrderAppDetailVO.getCompanyId());
        }
        orderInfo.setOrder_type(BizType.BUS.getCode());
        orderInfo.setCreate_time(busOrderAppDetailVO.getCreateTime());
        orderInfo.setOrder_status_msg(busOrderAppDetailVO.getOrderStatus().getValue());
        orderInfo.setOrder_msg(content.toString());
        orderInfo.setCustomer_msg(busOrderAppDetailVO.getPassengerNames());
        orderInfo.setRedirect_order_id(busOrderAppDetailVO.getOrderId());
        try {
            messageService.saveMessage(messageSaveContract);
        } catch (SaasException e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    public void sendConsumeInfo(KafkaSaturnOrderMsg saturnOrderMsg, BusOrderAppDetailVO busOrderAppDetailVO) {
        // 只有出票成功的状态才触发
        if (!busOrderAppDetailVO.getOrderStatus().getKey().equals(BusOrderStatusEnum.DONE.getKey())) {
            return;
        }
        IMessageService messageService = ApplicationContextUtils.getBean("messageService", IMessageService.class);
        Set<String> receivers = messageService.getReceivers(busOrderAppDetailVO.getUserId(), busOrderAppDetailVO.getCompanyId());
        for (String receiver : receivers) {
            MessageSaveContract messageSaveContract = new MessageSaveContract();
            ConsumptionInfo consumptionInfo = new ConsumptionInfo();
            messageSaveContract.setMessage_type(MessageType.Consume.getCode());
            messageSaveContract.setTitle(CoreLanguage.Order_Value_BusOrder.getMessage());
            StringBuilder content = new StringBuilder()
                    .append(StrUtils.formatString(CoreLanguage.Order_Value_MsgUserBusOrder.getMessage(),
                            busOrderAppDetailVO.getUserName(),
                            busOrderAppDetailVO.getTravelVO().getFromStationName(),
                            busOrderAppDetailVO.getTravelVO().getToStationName(),
                            BizType.BUS.getName()
                    ));
            messageSaveContract.setContent(content.toString());
            messageSaveContract.setSender_type(SenderType.Person.getCode());
            messageSaveContract.setSender(busOrderAppDetailVO.getUserName());
            messageSaveContract.setReceiver(receiver);
            messageSaveContract.setBiz_order(busOrderAppDetailVO.getOrderId());
            messageSaveContract.setConsumption_info(consumptionInfo);
            messageSaveContract.setCompany_id(busOrderAppDetailVO.getCompanyId());
            consumptionInfo.setConsumption_type(BizType.BUS.getCode());
            consumptionInfo.setCreator_msg(busOrderAppDetailVO.getUserName());
            consumptionInfo.setPrice_msg(NumberFormat.getCurrencyInstance(Locale.CHINA).format(busOrderAppDetailVO.getTotalPrice().getPrice()) + "元");
            consumptionInfo.setConsume_time(busOrderAppDetailVO.getCreateTime());
            ConsumptionInfo.BusInfo busInfo = new ConsumptionInfo.BusInfo();
            StringBuilder travelMsg = new StringBuilder()
                    .append(StrUtils.formatString(CoreLanguage.Order_Value_TravelOrder.getMessage(),
                            busOrderAppDetailVO.getTravelVO().getFromStationName(),
                            busOrderAppDetailVO.getTravelVO().getToStationName()
                    ))
                    .append(" ")
                    .append(DateTimeTool.fromDateTimeToDisplay(busOrderAppDetailVO.getTravelVO().getFromDateTime()));
            busInfo.setTravel_msg(travelMsg.toString());
            busInfo.setPassenger_msg(busOrderAppDetailVO.getPassengerNames());
            consumptionInfo.setBus_info(busInfo);
            if (ObjectUtil.isNotNull(busOrderAppDetailVO.getSaasInfoVO())) {
                consumptionInfo.setRemark(busOrderAppDetailVO.getSaasInfoVO().getOrderReasonName() + ";" + busOrderAppDetailVO.getSaasInfoVO().getOrderReasonExt());
            }
            if (ObjectUtil.isNotNull(busOrderAppDetailVO.getCostInfoType()) && busOrderAppDetailVO.getCostInfoType().equals(2))  {
                List<SaasCostDetailVO> costInfoTicketList = busOrderAppDetailVO.getCostInfoTicketList();
                consumptionInfo.setSaasCostDetailVOList(costInfoTicketList);
            } else {
                SaasCostDetailVO saasCostDetailVO = busOrderAppDetailVO.getCostDetail();
                consumptionInfo.setSaasCostDetailVO(saasCostDetailVO);
            }
            consumptionInfo.setData_type(2);
            try {
                messageService.saveMessage(messageSaveContract);
            } catch (SaasException e) {
                LOGGER.error(e.getMessage(), e);
            }
        }
    }

    /**
     * 大额异动
     */
    public void sendLargeOver(KafkaSaturnOrderMsg saturnOrderMsg, BusOrderAppDetailVO busOrderAppDetailVO) {
        // 只有出票成功的状态才触发 && 改签成功才触发
        if (!busOrderAppDetailVO.getOrderStatus().getKey().equals(BusOrderStatusEnum.DONE.getKey())
                && !busOrderAppDetailVO.getOrderStatus().getKey().equals(BusOrderStatusEnum.DONE_CHANG.getKey())) {
            return;
        }
        // 获取大额异动配置
        IMessageSetupService messageSetupService = ApplicationContextUtils.getBean(IMessageSetupService.class);
        LargeOverService largeOverService = ApplicationContextUtils.getBean(LargeOverService.class);
        String companyId = busOrderAppDetailVO.getCompanyId();
        MessageSetup messageSetup = messageSetupService.queryCompanyMessageSetupWithDefault(companyId, SaasMessageConstant.ITEM_CODE_LARGE_OVER_NOTICE);
        if (messageSetup == null
                || messageSetup.getIsChecked() == SaasMessageConstant.IS_CHECKED_FALSE
                || messageSetup.getIntVal1() <= 0) {
            return;
        }
        // 判断订单是否大额
        BigDecimal price = busOrderAppDetailVO.getTotalPrice().getPrice();
        LOGGER.info("汽车大额异动消息处理：订单金额：{}，大额设置：{}", price, messageSetup.getIntVal1());
        // 订单金额没有超过设置的大额
        if (price.compareTo(new BigDecimal(messageSetup.getIntVal1())) < 0) {
            LOGGER.info("汽车大额异动消息处理：订单金额没有超过设置的大额，不提醒。");
            return;
        }
        // 发送邮件
        List<MessageSetupEmail> largeOverEmailList = largeOverService.queryMessageEmailList(companyId, SaasMessageConstant.BUSI_CODE_LARGE_OVER);
        if (CollectionUtils.isNotEmpty(largeOverEmailList)) {
            Set<String> emailSet = largeOverEmailList.stream()
                    .map(MessageSetupEmail::getEmail)
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(emailSet)) {
                EmailContract emailContract = new EmailContract();
                // 收件人
                emailContract.setToList(emailSet);
                // 邮件标题
                String subject = MessageFormat.format(CoreLanguage.Order_Message_FbtConsumerReminder.getMessage(),
                        busOrderAppDetailVO.getUserUnitName(),
                        busOrderAppDetailVO.getUserName(),
                        NumberFormat.getCurrencyInstance(Locale.CHINA).format(price),
                        BizType.BUS.getName());
                emailContract.setSubject(subject);
                // html模板及内容 出票成功
                if (busOrderAppDetailVO.getOrderStatus().getKey().equals(BusOrderStatusEnum.DONE.getKey())) {
                    emailContract.setTemplateId(TemplateConstant.EMAIL_TEMP_LARGE_OVER);
                }
                // html模板及内容 改签成功
                if (busOrderAppDetailVO.getOrderStatus().getKey().equals(BusOrderStatusEnum.DONE_CHANG.getKey())) {
                    emailContract.setTemplateId(TemplateConstant.EMAIL_TEMP_TICKET_MODIFY_LARGE_OVER);
                }

                Map<String, Object> dataMap = new LinkedHashMap<>();
                dataMap.put("orderTime", busOrderAppDetailVO.getCreateTime());
                dataMap.put("userName", busOrderAppDetailVO.getUserName() + " " + busOrderAppDetailVO.getUserPhone());
                dataMap.put("userDept", busOrderAppDetailVO.getUserUnitName());
                dataMap.put("bizType", BizType.BUS.getName());
                dataMap.put("orderPrice", StrUtils.formatString(CoreLanguage.Rule_Value_ChineseUnit.getMessage(), NumberFormat.getCurrencyInstance(Locale.CHINA).format(price)));
                // 出票成功 明细内容
                if (busOrderAppDetailVO.getOrderStatus().getKey().equals(BusOrderStatusEnum.DONE.getKey())) {
                    // yyyy/mm/dd，出发地-目的地， 席别
                    dataMap.put("orderDetail", MessageFormat.format("{0}，{1}-{2}，{3}",
                            DateTimeTool.fromDateToString(DateTimeTool.fromStringToDateTime(busOrderAppDetailVO.getTravelVO().getFromDateTime())),
                            busOrderAppDetailVO.getTravelVO().getFromStationName(),
                            busOrderAppDetailVO.getTravelVO().getToStationName()));
                }
                // 改签成功 明细内容
                if (busOrderAppDetailVO.getOrderStatus().getKey().equals(BusOrderStatusEnum.DONE_CHANG.getKey())) {
                    // 行程开始日期：MM月DD日,起止城市,班次号
                    dataMap.put("orderDetail", MessageFormat.format("{0}, {1}-{2},{3}",
                            DateTimeTool.fromDateToDisplayString(DateTimeTool.fromStringToDateTime(busOrderAppDetailVO.getTravelVO().getFromDateTime())),
                            busOrderAppDetailVO.getTravelVO().getFromStationName(),
                            busOrderAppDetailVO.getTravelVO().getToStationName()));
                }
                emailContract.setData(dataMap);
                // 发送邮件
                NoticeUtils.sendEmail(emailContract);
            }
        }
    }

//    /**
//     * 预算报警
//     */
//    public void sendWarnPercentInfo(KafkaSaturnOrderMsg saturnOrderMsg, BusOrderAppDetailVO busOrderAppDetailVO) {
//        // 只有出票成功的状态才触发
//        if (!busOrderAppDetailVO.getOrderStatus().getKey().equals(BusOrderStatusEnum.DONE.getKey())) {
//            return;
//        }
//        IBudgetService budgetService = ApplicationContextUtils.getBean(IBudgetService.class);
//        String companyAuthEmail = event.userInfo().companyAuthEmail().get();
//        String companyName = busOrderAppDetailVO.getCompanyName();
//        String companyId = busOrderAppDetailVO.getCompanyId();
//        String employeeId = busOrderAppDetailVO.getUserId();
//        Integer costAttributionScope = event.orderInfo().costOpt().isEmpty() ? null : ObjUtils.toInteger(event.orderInfo().costOpt().get());
//        Integer budgetCostAttrType = event.orderInfo().budgetOpt().isEmpty() ? null : ObjUtils.toInteger(event.orderInfo().budgetOpt().get());
//        if (ObjectUtil.isNotNull(busOrderAppDetailVO.getCostDetail())) {
//            if (ObjectUtil.isNotNull(busOrderAppDetailVO.getCostDetail())) {
//
//            }
//            List<OrderCostAttribution> orderCostAttributionList = JavaConversions.seqAsJavaList(event.orderInfo().costAttributionList().get());
//            List<CostAttributionInfo> costAttributionList = Lists.newArrayList();
//            for (OrderCostAttribution orderCostAttribution : orderCostAttributionList) {
//                CostAttributionInfo costAttributionInfo = new CostAttributionInfo();
//                costAttributionInfo.setCost_attribution_category(ObjUtils.toInteger(orderCostAttribution.category().get()));
//                costAttributionInfo.setCost_attribution_id(orderCostAttribution.id());
//                costAttributionInfo.setCost_attribution_name(orderCostAttribution.name());
//                costAttributionList.add(costAttributionInfo);
//            }
//            budgetService.sendWarnPercentInfo(companyAuthEmail, companyId, companyName, employeeId, costAttributionList, OrderCategory.Bus, costAttributionScope, budgetCostAttrType);
//        }
//        if (!event.orderInfo().costAttributionList().isEmpty()) {
//            List<OrderCostAttribution> orderCostAttributionList = JavaConversions.seqAsJavaList(event.orderInfo().costAttributionList().get());
//            List<CostAttributionInfo> costAttributionList = Lists.newArrayList();
//            for (OrderCostAttribution orderCostAttribution : orderCostAttributionList) {
//                CostAttributionInfo costAttributionInfo = new CostAttributionInfo();
//                costAttributionInfo.setCost_attribution_category(ObjUtils.toInteger(orderCostAttribution.category().get()));
//                costAttributionInfo.setCost_attribution_id(orderCostAttribution.id());
//                costAttributionInfo.setCost_attribution_name(orderCostAttribution.name());
//                costAttributionList.add(costAttributionInfo);
//            }
//            budgetService.sendWarnPercentInfo(companyAuthEmail, companyId, companyName, employeeId, costAttributionList, OrderCategory.Bus, costAttributionScope, budgetCostAttrType);
//        }
//    }

    /**
     * 审批通知提醒
     */
    public void sendApplyMessageInfo(KafkaSaturnOrderMsg saturnOrderMsg, BusOrderAppDetailVO busOrderAppDetailVO) {
        //只有出票成功的状态才触发
        if (!busOrderAppDetailVO.getOrderStatus().getKey().equals(BusOrderStatusEnum.DONE.getKey())
                && !busOrderAppDetailVO.getOrderStatus().getKey().equals(BusOrderStatusEnum.DONE_CHANG.getKey())) {
            return;
        }
        if (ObjectUtil.isNull(busOrderAppDetailVO.getSaasInfoVO())) {
            return;
        }
        String applyId = busOrderAppDetailVO.getSaasInfoVO().getDuringApplyId();
        if (StringUtils.isBlank(applyId)) {
            return;
        }
        String orderPrice = NumberFormat.getCurrencyInstance(Locale.CHINA).format(busOrderAppDetailVO.getTotalPrice().getPrice());
        IApplyV2Service applyV2Service = ApplicationContextUtils.getBean(IApplyV2Service.class);
        applyV2Service.sloveBusApplyMessage(applyId, busOrderAppDetailVO.getCompanyId(), busOrderAppDetailVO.getUserId(), orderPrice, BizType.BUS.getName());
    }

    /**
     * 订单通知人
     */
    public void sendOrderMessageInfo(KafkaSaturnOrderMsg saturnOrderMsg, BusOrderAppDetailVO busOrderAppDetailVO) {
        // 只有出票成功的状态才触发
        if (!busOrderAppDetailVO.getOrderStatus().getKey().equals(BusOrderStatusEnum.DONE.getKey())
                && !busOrderAppDetailVO.getOrderStatus().getKey().equals(BusOrderStatusEnum.DONE_CHANG.getKey())) {
            return;
        }
        IMessageService messageService = ApplicationContextUtils.getBean("messageService", IMessageService.class);
        if (CollectionUtils.isEmpty(busOrderAppDetailVO.getNotifierList())) {
            LOGGER.info("无订单通知人");
            return;
        }
        List<NotifierInfoRespVo> userInfoList = busOrderAppDetailVO.getNotifierList();
        for (NotifierInfoRespVo userInfo : userInfoList) {
            MessageSaveContract messageSaveContract = new MessageSaveContract();
            ConsumptionInfo consumptionInfo = new ConsumptionInfo();
            messageSaveContract.setMessage_type(MessageType.System.getCode());
            messageSaveContract.setTitle(CoreLanguage.Order_Value_BusOrder.getMessage());
            StringBuilder content = new StringBuilder()
                    .append(StrUtils.formatString(CoreLanguage.Order_Value_MsgUserBusOrderArrive.getMessage(),
                            busOrderAppDetailVO.getUserName(),
                            busOrderAppDetailVO.getTravelVO().getFromStationName(),
                            busOrderAppDetailVO.getTravelVO().getToStationName(),
                            BizType.BUS.getName()
                    ));
            messageSaveContract.setContent(content.toString());
            messageSaveContract.setSender_type(SenderType.Person.getCode());
            messageSaveContract.setSender(busOrderAppDetailVO.getUserId());
            messageSaveContract.setReceiver(userInfo.getNotifierId());
            messageSaveContract.setBiz_order(busOrderAppDetailVO.getOrderId());
            messageSaveContract.setConsumption_info(consumptionInfo);
            messageSaveContract.setCompany_id(busOrderAppDetailVO.getCompanyId());
            consumptionInfo.setConsumption_type(BizType.BUS.getCode());
            consumptionInfo.setCreator_msg(busOrderAppDetailVO.getUserName());
            consumptionInfo.setPrice_msg(NumberFormat.getCurrencyInstance(Locale.CHINA).format(busOrderAppDetailVO.getTotalPrice().getPrice()) + "元");
            consumptionInfo.setConsume_time(busOrderAppDetailVO.getCreateTime());
            ConsumptionInfo.BusInfo busInfo = new ConsumptionInfo.BusInfo();
            StringBuilder travelMsg = new StringBuilder()
                    .append(StrUtils.formatString(CoreLanguage.Order_Value_TravelOrder.getMessage(), busOrderAppDetailVO.getTravelVO().getFromStationName(), busOrderAppDetailVO.getTravelVO().getToStationName()))
                    .append(" ")
                    .append(DateTimeTool.fromDateTimeToDisplay(busOrderAppDetailVO.getTravelVO().getFromDateTime()));
            busInfo.setTravel_msg(travelMsg.toString());
            busInfo.setPassenger_msg(busOrderAppDetailVO.getPassengerNames());
            consumptionInfo.setBus_info(busInfo);
            if (ObjectUtil.isNotNull(busOrderAppDetailVO.getSaasInfoVO())) {
                consumptionInfo.setRemark(busOrderAppDetailVO.getSaasInfoVO().getRemark());
            }
            if (ObjectUtil.isNotNull(busOrderAppDetailVO.getCostInfoType()) && busOrderAppDetailVO.getCostInfoType().equals(2))  {
                List<SaasCostDetailVO> costInfoTicketList = busOrderAppDetailVO.getCostInfoTicketList();
                consumptionInfo.setSaasCostDetailVOList(costInfoTicketList);
            } else {
                SaasCostDetailVO saasCostDetailVO = busOrderAppDetailVO.getCostDetail();
                consumptionInfo.setSaasCostDetailVO(saasCostDetailVO);
            }
            consumptionInfo.setData_type(2);
            try {
                messageService.saveMessage(messageSaveContract);
            } catch (SaasException e) {
                LOGGER.error(e.getMessage(), e);
            }
        }
    }

}


