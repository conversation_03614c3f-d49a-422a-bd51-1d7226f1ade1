package com.fenbeitong.saas.core.contract.selfauthorize;

import lombok.Data;

import java.util.List;

@Data
public class CreateApplyOtherAuthorizationReq {

    /**
     * 授权权限类型,默认1
     *
     * @see com.fenbeitong.saas.core.model.enums.selfauthorize.AuthPermissionTypeEnum
     */
    private Integer auth_permission_type;

    /**
     * 授权场景
     */
    private List<Integer> apply_auth_scene_list;

    /**
     * 授权开始时间
     */
    private String auth_start_time;

    /**
     * 授权有效期：单位为天
     */
    private Integer validity_period;

    /**
     * 是否为永久授权：1为永久，0为非永久
     */
    private Integer is_forever;

    /**
     * 被授权人列表
     */
    private List<String> auth_employee_id_list;

    /**
     * 来源类型
     *
     * @see com.fenbeitong.saas.core.model.enums.selfauthorize.AuthSourceEnum
     */
    private Integer auth_source;

}
