package com.fenbeitong.saas.core.contract.message;

/**
 * Created by xuzn on 17/9/23.
 */
public class MessageConsuContract {

    private int msg_type;
    private String title;

    private String consume_time;

    private int order_type;
    private String creator_msg;

    private String order_id;
    private String passenger_msg;
    private String price_msg;

    public int getMsg_type() {
        return msg_type;
    }

    public void setMsg_type(int msg_type) {
        this.msg_type = msg_type;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getConsume_time() {
        return consume_time;
    }

    public void setConsume_time(String consume_time) {
        this.consume_time = consume_time;
    }

    public int getOrder_type() {
        return order_type;
    }

    public void setOrder_type(int order_type) {
        this.order_type = order_type;
    }

    public String getCreator_msg() {
        return creator_msg;
    }

    public void setCreator_msg(String creator_msg) {
        this.creator_msg = creator_msg;
    }

    public String getOrder_id() {
        return order_id;
    }

    public void setOrder_id(String order_id) {
        this.order_id = order_id;
    }

    public String getPassenger_msg() {
        return passenger_msg;
    }

    public void setPassenger_msg(String passenger_msg) {
        this.passenger_msg = passenger_msg;
    }

    public String getPrice_msg() {
        return price_msg;
    }

    public void setPrice_msg(String price_msg) {
        this.price_msg = price_msg;
    }
}
