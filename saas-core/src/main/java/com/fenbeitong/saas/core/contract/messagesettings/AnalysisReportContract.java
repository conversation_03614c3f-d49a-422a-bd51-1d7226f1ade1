package com.fenbeitong.saas.core.contract.messagesettings;

import java.util.List;

/**
 * Created by zhuminghua on 2017/6/22.
 */
public class AnalysisReportContract {

    private Integer page_index;

    private Integer page_size;

    private Long total;

    private List<AnalysisReportCompany> company_list;

    public Integer getPage_index() {
        return page_index;
    }

    public void setPage_index(Integer page_index) {
        this.page_index = page_index;
    }

    public Integer getPage_size() {
        return page_size;
    }

    public void setPage_size(Integer page_size) {
        this.page_size = page_size;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public List<AnalysisReportCompany> getCompany_list() {
        return company_list;
    }

    public void setCompany_list(List<AnalysisReportCompany> company_list) {
        this.company_list = company_list;
    }

    public static class AnalysisReportCompany {
        private String company_id;
        private String company_name;
        private List<String> email_list;

        public String getCompany_id() {
            return company_id;
        }

        public void setCompany_id(String company_id) {
            this.company_id = company_id;
        }

        public String getCompany_name() {
            return company_name;
        }

        public void setCompany_name(String company_name) {
            this.company_name = company_name;
        }

        public List<String> getEmail_list() {
            return email_list;
        }

        public void setEmail_list(List<String> email_list) {
            this.email_list = email_list;
        }
    }

}
