package com.fenbeitong.saas.core.contract.customrole;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

/**
 * Created by ch<PERSON><PERSON> on 2017/4/24.
 */
public class RoleEmployeeDetail {
    @JSONField(ordinal = 0)
    String id;
    @JSO<PERSON>ield(ordinal = 1)
    String name;
    @JSO<PERSON>ield(ordinal = 2)
    String phone;
    @JSO<PERSON>ield(ordinal = 3)
    List<String> orgs;

    public RoleEmployeeDetail(String id, String name, String phone, List<String> orgs) {
        this.id = id;
        this.name = name;
        this.phone = phone;
        this.orgs = orgs;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public List<String> getOrgs() {
        return orgs;
    }

    public void setOrgs(List<String> orgs) {
        this.orgs = orgs;
    }

}
