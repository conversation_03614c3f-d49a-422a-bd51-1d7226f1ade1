package com.fenbeitong.saas.core.model.fenbeitong;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table taxi_order_pay_detail
 */
public class TaxiOrderPayDetail {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column taxi_order_pay_detail.id
     *
     * @mbg.generated
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column taxi_order_pay_detail.company_id
     *
     * @mbg.generated
     */
    private String companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column taxi_order_pay_detail.order_id
     *
     * @mbg.generated
     */
    private String orderId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column taxi_order_pay_detail.order_price
     *
     * @mbg.generated
     */
    private BigDecimal orderPrice;

    /**
     * Database Column Remarks:
     *   用车类型 1用车 2申请用车
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column taxi_order_pay_detail.type
     *
     * @mbg.generated
     */
    private Integer type;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column taxi_order_pay_detail.is_check_single_order
     *
     * @mbg.generated
     */
    private Integer isCheckSingleOrder;

    /**
     * Database Column Remarks:
     *   单笔上限
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column taxi_order_pay_detail.single_order
     *
     * @mbg.generated
     */
    private BigDecimal singleOrder;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column taxi_order_pay_detail.is_check_per_day
     *
     * @mbg.generated
     */
    private Integer isCheckPerDay;

    /**
     * Database Column Remarks:
     *   单日上限
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column taxi_order_pay_detail.per_day
     *
     * @mbg.generated
     */
    private BigDecimal perDay;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column taxi_order_pay_detail.is_check_budget
     *
     * @mbg.generated
     */
    private Integer isCheckBudget;

    /**
     * Database Column Remarks:
     *   预算
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column taxi_order_pay_detail.budget
     *
     * @mbg.generated
     */
    private BigDecimal budget;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column taxi_order_pay_detail.is_check_city_single_order
     *
     * @mbg.generated
     */
    private Integer isCheckCitySingleOrder;

    /**
     * Database Column Remarks:
     *   当前城市单笔上限
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column taxi_order_pay_detail.city_single_order
     *
     * @mbg.generated
     */
    private BigDecimal citySingleOrder;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column taxi_order_pay_detail.is_check_city_per_day
     *
     * @mbg.generated
     */
    private Integer isCheckCityPerDay;

    /**
     * Database Column Remarks:
     *   当前城市单日上限
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column taxi_order_pay_detail.city_per_day
     *
     * @mbg.generated
     */
    private BigDecimal cityPerDay;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column taxi_order_pay_detail.is_check_apply_per_day
     *
     * @mbg.generated
     */
    private Integer isCheckApplyPerDay;

    /**
     * Database Column Remarks:
     *   申请单单日上限
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column taxi_order_pay_detail.apply_per_day
     *
     * @mbg.generated
     */
    private BigDecimal applyPerDay;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column taxi_order_pay_detail.apply_per_day_desc
     *
     * @mbg.generated
     */
    private String applyPerDayDesc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column taxi_order_pay_detail.is_check_apply_amount
     *
     * @mbg.generated
     */
    private Integer isCheckApplyAmount;

    /**
     * Database Column Remarks:
     *   申请单费用限制
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column taxi_order_pay_detail.apply_amount
     *
     * @mbg.generated
     */
    private BigDecimal applyAmount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column taxi_order_pay_detail.apply_amount_desc
     *
     * @mbg.generated
     */
    private String applyAmountDesc;

    /**
     * Database Column Remarks:
     *   升级后规则 0未升级 1升级
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column taxi_order_pay_detail.is_open_exceed_config
     *
     * @mbg.generated
     */
    private Integer isOpenExceedConfig;

    /**
     * Database Column Remarks:
     *   个人支付 0未开启 1开启
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column taxi_order_pay_detail.is_personal_pay
     *
     * @mbg.generated
     */
    private Integer isPersonalPay;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column taxi_order_pay_detail.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column taxi_order_pay_detail.buget_desc
     *
     * @mbg.generated
     */
    private String bugetDesc;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column taxi_order_pay_detail.id
     *
     * @return the value of taxi_order_pay_detail.id
     *
     * @mbg.generated
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column taxi_order_pay_detail.id
     *
     * @param id the value for taxi_order_pay_detail.id
     *
     * @mbg.generated
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column taxi_order_pay_detail.company_id
     *
     * @return the value of taxi_order_pay_detail.company_id
     *
     * @mbg.generated
     */
    public String getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column taxi_order_pay_detail.company_id
     *
     * @param companyId the value for taxi_order_pay_detail.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column taxi_order_pay_detail.order_id
     *
     * @return the value of taxi_order_pay_detail.order_id
     *
     * @mbg.generated
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column taxi_order_pay_detail.order_id
     *
     * @param orderId the value for taxi_order_pay_detail.order_id
     *
     * @mbg.generated
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column taxi_order_pay_detail.order_price
     *
     * @return the value of taxi_order_pay_detail.order_price
     *
     * @mbg.generated
     */
    public BigDecimal getOrderPrice() {
        return orderPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column taxi_order_pay_detail.order_price
     *
     * @param orderPrice the value for taxi_order_pay_detail.order_price
     *
     * @mbg.generated
     */
    public void setOrderPrice(BigDecimal orderPrice) {
        this.orderPrice = orderPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column taxi_order_pay_detail.type
     *
     * @return the value of taxi_order_pay_detail.type
     *
     * @mbg.generated
     */
    public Integer getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column taxi_order_pay_detail.type
     *
     * @param type the value for taxi_order_pay_detail.type
     *
     * @mbg.generated
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column taxi_order_pay_detail.is_check_single_order
     *
     * @return the value of taxi_order_pay_detail.is_check_single_order
     *
     * @mbg.generated
     */
    public Integer getIsCheckSingleOrder() {
        return isCheckSingleOrder;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column taxi_order_pay_detail.is_check_single_order
     *
     * @param isCheckSingleOrder the value for taxi_order_pay_detail.is_check_single_order
     *
     * @mbg.generated
     */
    public void setIsCheckSingleOrder(Integer isCheckSingleOrder) {
        this.isCheckSingleOrder = isCheckSingleOrder;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column taxi_order_pay_detail.single_order
     *
     * @return the value of taxi_order_pay_detail.single_order
     *
     * @mbg.generated
     */
    public BigDecimal getSingleOrder() {
        return singleOrder;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column taxi_order_pay_detail.single_order
     *
     * @param singleOrder the value for taxi_order_pay_detail.single_order
     *
     * @mbg.generated
     */
    public void setSingleOrder(BigDecimal singleOrder) {
        this.singleOrder = singleOrder;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column taxi_order_pay_detail.is_check_per_day
     *
     * @return the value of taxi_order_pay_detail.is_check_per_day
     *
     * @mbg.generated
     */
    public Integer getIsCheckPerDay() {
        return isCheckPerDay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column taxi_order_pay_detail.is_check_per_day
     *
     * @param isCheckPerDay the value for taxi_order_pay_detail.is_check_per_day
     *
     * @mbg.generated
     */
    public void setIsCheckPerDay(Integer isCheckPerDay) {
        this.isCheckPerDay = isCheckPerDay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column taxi_order_pay_detail.per_day
     *
     * @return the value of taxi_order_pay_detail.per_day
     *
     * @mbg.generated
     */
    public BigDecimal getPerDay() {
        return perDay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column taxi_order_pay_detail.per_day
     *
     * @param perDay the value for taxi_order_pay_detail.per_day
     *
     * @mbg.generated
     */
    public void setPerDay(BigDecimal perDay) {
        this.perDay = perDay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column taxi_order_pay_detail.is_check_budget
     *
     * @return the value of taxi_order_pay_detail.is_check_budget
     *
     * @mbg.generated
     */
    public Integer getIsCheckBudget() {
        return isCheckBudget;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column taxi_order_pay_detail.is_check_budget
     *
     * @param isCheckBudget the value for taxi_order_pay_detail.is_check_budget
     *
     * @mbg.generated
     */
    public void setIsCheckBudget(Integer isCheckBudget) {
        this.isCheckBudget = isCheckBudget;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column taxi_order_pay_detail.budget
     *
     * @return the value of taxi_order_pay_detail.budget
     *
     * @mbg.generated
     */
    public BigDecimal getBudget() {
        return budget;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column taxi_order_pay_detail.budget
     *
     * @param budget the value for taxi_order_pay_detail.budget
     *
     * @mbg.generated
     */
    public void setBudget(BigDecimal budget) {
        this.budget = budget;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column taxi_order_pay_detail.is_check_city_single_order
     *
     * @return the value of taxi_order_pay_detail.is_check_city_single_order
     *
     * @mbg.generated
     */
    public Integer getIsCheckCitySingleOrder() {
        return isCheckCitySingleOrder;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column taxi_order_pay_detail.is_check_city_single_order
     *
     * @param isCheckCitySingleOrder the value for taxi_order_pay_detail.is_check_city_single_order
     *
     * @mbg.generated
     */
    public void setIsCheckCitySingleOrder(Integer isCheckCitySingleOrder) {
        this.isCheckCitySingleOrder = isCheckCitySingleOrder;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column taxi_order_pay_detail.city_single_order
     *
     * @return the value of taxi_order_pay_detail.city_single_order
     *
     * @mbg.generated
     */
    public BigDecimal getCitySingleOrder() {
        return citySingleOrder;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column taxi_order_pay_detail.city_single_order
     *
     * @param citySingleOrder the value for taxi_order_pay_detail.city_single_order
     *
     * @mbg.generated
     */
    public void setCitySingleOrder(BigDecimal citySingleOrder) {
        this.citySingleOrder = citySingleOrder;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column taxi_order_pay_detail.is_check_city_per_day
     *
     * @return the value of taxi_order_pay_detail.is_check_city_per_day
     *
     * @mbg.generated
     */
    public Integer getIsCheckCityPerDay() {
        return isCheckCityPerDay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column taxi_order_pay_detail.is_check_city_per_day
     *
     * @param isCheckCityPerDay the value for taxi_order_pay_detail.is_check_city_per_day
     *
     * @mbg.generated
     */
    public void setIsCheckCityPerDay(Integer isCheckCityPerDay) {
        this.isCheckCityPerDay = isCheckCityPerDay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column taxi_order_pay_detail.city_per_day
     *
     * @return the value of taxi_order_pay_detail.city_per_day
     *
     * @mbg.generated
     */
    public BigDecimal getCityPerDay() {
        return cityPerDay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column taxi_order_pay_detail.city_per_day
     *
     * @param cityPerDay the value for taxi_order_pay_detail.city_per_day
     *
     * @mbg.generated
     */
    public void setCityPerDay(BigDecimal cityPerDay) {
        this.cityPerDay = cityPerDay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column taxi_order_pay_detail.is_check_apply_per_day
     *
     * @return the value of taxi_order_pay_detail.is_check_apply_per_day
     *
     * @mbg.generated
     */
    public Integer getIsCheckApplyPerDay() {
        return isCheckApplyPerDay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column taxi_order_pay_detail.is_check_apply_per_day
     *
     * @param isCheckApplyPerDay the value for taxi_order_pay_detail.is_check_apply_per_day
     *
     * @mbg.generated
     */
    public void setIsCheckApplyPerDay(Integer isCheckApplyPerDay) {
        this.isCheckApplyPerDay = isCheckApplyPerDay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column taxi_order_pay_detail.apply_per_day
     *
     * @return the value of taxi_order_pay_detail.apply_per_day
     *
     * @mbg.generated
     */
    public BigDecimal getApplyPerDay() {
        return applyPerDay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column taxi_order_pay_detail.apply_per_day
     *
     * @param applyPerDay the value for taxi_order_pay_detail.apply_per_day
     *
     * @mbg.generated
     */
    public void setApplyPerDay(BigDecimal applyPerDay) {
        this.applyPerDay = applyPerDay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column taxi_order_pay_detail.apply_per_day_desc
     *
     * @return the value of taxi_order_pay_detail.apply_per_day_desc
     *
     * @mbg.generated
     */
    public String getApplyPerDayDesc() {
        return applyPerDayDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column taxi_order_pay_detail.apply_per_day_desc
     *
     * @param applyPerDayDesc the value for taxi_order_pay_detail.apply_per_day_desc
     *
     * @mbg.generated
     */
    public void setApplyPerDayDesc(String applyPerDayDesc) {
        this.applyPerDayDesc = applyPerDayDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column taxi_order_pay_detail.is_check_apply_amount
     *
     * @return the value of taxi_order_pay_detail.is_check_apply_amount
     *
     * @mbg.generated
     */
    public Integer getIsCheckApplyAmount() {
        return isCheckApplyAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column taxi_order_pay_detail.is_check_apply_amount
     *
     * @param isCheckApplyAmount the value for taxi_order_pay_detail.is_check_apply_amount
     *
     * @mbg.generated
     */
    public void setIsCheckApplyAmount(Integer isCheckApplyAmount) {
        this.isCheckApplyAmount = isCheckApplyAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column taxi_order_pay_detail.apply_amount
     *
     * @return the value of taxi_order_pay_detail.apply_amount
     *
     * @mbg.generated
     */
    public BigDecimal getApplyAmount() {
        return applyAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column taxi_order_pay_detail.apply_amount
     *
     * @param applyAmount the value for taxi_order_pay_detail.apply_amount
     *
     * @mbg.generated
     */
    public void setApplyAmount(BigDecimal applyAmount) {
        this.applyAmount = applyAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column taxi_order_pay_detail.apply_amount_desc
     *
     * @return the value of taxi_order_pay_detail.apply_amount_desc
     *
     * @mbg.generated
     */
    public String getApplyAmountDesc() {
        return applyAmountDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column taxi_order_pay_detail.apply_amount_desc
     *
     * @param applyAmountDesc the value for taxi_order_pay_detail.apply_amount_desc
     *
     * @mbg.generated
     */
    public void setApplyAmountDesc(String applyAmountDesc) {
        this.applyAmountDesc = applyAmountDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column taxi_order_pay_detail.is_open_exceed_config
     *
     * @return the value of taxi_order_pay_detail.is_open_exceed_config
     *
     * @mbg.generated
     */
    public Integer getIsOpenExceedConfig() {
        return isOpenExceedConfig;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column taxi_order_pay_detail.is_open_exceed_config
     *
     * @param isOpenExceedConfig the value for taxi_order_pay_detail.is_open_exceed_config
     *
     * @mbg.generated
     */
    public void setIsOpenExceedConfig(Integer isOpenExceedConfig) {
        this.isOpenExceedConfig = isOpenExceedConfig;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column taxi_order_pay_detail.is_personal_pay
     *
     * @return the value of taxi_order_pay_detail.is_personal_pay
     *
     * @mbg.generated
     */
    public Integer getIsPersonalPay() {
        return isPersonalPay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column taxi_order_pay_detail.is_personal_pay
     *
     * @param isPersonalPay the value for taxi_order_pay_detail.is_personal_pay
     *
     * @mbg.generated
     */
    public void setIsPersonalPay(Integer isPersonalPay) {
        this.isPersonalPay = isPersonalPay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column taxi_order_pay_detail.create_time
     *
     * @return the value of taxi_order_pay_detail.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column taxi_order_pay_detail.create_time
     *
     * @param createTime the value for taxi_order_pay_detail.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column taxi_order_pay_detail.buget_desc
     *
     * @return the value of taxi_order_pay_detail.buget_desc
     *
     * @mbg.generated
     */
    public String getBugetDesc() {
        return bugetDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column taxi_order_pay_detail.buget_desc
     *
     * @param bugetDesc the value for taxi_order_pay_detail.buget_desc
     *
     * @mbg.generated
     */
    public void setBugetDesc(String bugetDesc) {
        this.bugetDesc = bugetDesc;
    }
}