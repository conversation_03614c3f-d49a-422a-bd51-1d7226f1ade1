package com.fenbeitong.saas.core.contract.message.inner;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.noc.api.service.base.SaasCostDetailVO;
import com.fenbeitong.saas.core.contract.common.CostAttributionInfo;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/4/14.
 */
public class ConsumptionInfo {

    /**
     * 3 打车, 7 机票, 11 酒店, 15 火车, 16 采购
     */
    private Integer consumption_type;

    /**
     * 下单人 (名字)
     */
    private String creator_msg;

    /**
     * 消费金额
     */
    private String price_msg;

    /**
     * 消费时间
     */
    private String consume_time;
    /**
     * 发件人
     */
    private String sender_msg;

    private String biz_message;

    private AirInfo air_info;

    private TaxiInfo taxi_info;

    private HotelInfo hotel_info;

    private TrainInfo train_info;

    private BusInfo bus_info;

    private MallInfo mall_info;

    private DinnerInfo dinner_info;

    private TakeawayInfo takeaway_info;

    private ExpressInfo express_info;

    //费用归属名称
    private String cost_attribution_name;

    private List<CostAttributionInfo> cost_attribution_list;

    private List<SaasCostDetailVO> saasCostDetailVOList;

    private SaasCostDetailVO saasCostDetailVO;

    //订单事由
    private String remark;
    //数据类型
    private Integer data_type;

    public String getBiz_message() {
        return biz_message;
    }

    public void setBiz_message(String biz_message) {
        this.biz_message = biz_message;
    }

    public Integer getConsumption_type() {
        return consumption_type;
    }

    public void setConsumption_type(Integer consumption_type) {
        this.consumption_type = consumption_type;
    }

    public String getCreator_msg() {
        return creator_msg;
    }

    public void setCreator_msg(String creator_msg) {
        this.creator_msg = creator_msg;
    }

    public String getPrice_msg() {
        return price_msg;
    }

    public void setPrice_msg(String price_msg) {
        this.price_msg = price_msg;
    }

    public String getConsume_time() {
        return consume_time;
    }

    public void setConsume_time(String consume_time) {
        this.consume_time = consume_time;
    }

    public AirInfo getAir_info() {
        return air_info;
    }

    public void setAir_info(AirInfo air_info) {
        this.air_info = air_info;
    }

    public TaxiInfo getTaxi_info() {
        return taxi_info;
    }

    public void setTaxi_info(TaxiInfo taxi_info) {
        this.taxi_info = taxi_info;
    }

    public HotelInfo getHotel_info() {
        return hotel_info;
    }

    public void setHotel_info(HotelInfo hotel_info) {
        this.hotel_info = hotel_info;
    }

    public TrainInfo getTrain_info() {
        return train_info;
    }

    public void setTrain_info(TrainInfo train_info) {
        this.train_info = train_info;
    }

    public BusInfo getBus_info() {
        return bus_info;
    }

    public void setBus_info(BusInfo bus_info) {
        this.bus_info = bus_info;
    }

    public List<SaasCostDetailVO> getSaasCostDetailVOList() {
        return saasCostDetailVOList;
    }

    public void setSaasCostDetailVOList(List<SaasCostDetailVO> saasCostDetailVOList) {
        this.saasCostDetailVOList = saasCostDetailVOList;
    }

    public SaasCostDetailVO getSaasCostDetailVO() {
        return saasCostDetailVO;
    }

    public void setSaasCostDetailVO(SaasCostDetailVO saasCostDetailVO) {
        this.saasCostDetailVO = saasCostDetailVO;
    }

    public MallInfo getMall_info() {
        return mall_info;
    }

    public void setMall_info(MallInfo mall_info) {
        this.mall_info = mall_info;
    }

    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }

    public static class AirInfo {

        /**
         * 行程
         */
        private String travel_msg;

        /**
         * 乘机人 (名字)
         */
        private String passenger_msg;

        public String getTravel_msg() {
            return travel_msg;
        }

        public void setTravel_msg(String travel_msg) {
            this.travel_msg = travel_msg;
        }

        public String getPassenger_msg() {
            return passenger_msg;
        }

        @Override
        public String toString() {
            return JSONObject.toJSONString(this);
        }

        public void setPassenger_msg(String passenger_msg) {
            this.passenger_msg = passenger_msg;
        }
    }

    public static class TaxiInfo {

        /**
         * 行程
         */
        private String travel_msg;

        /**
         * 乘机人 (名字)
         */
        private String passenger_msg;

        /**
         * 用车类型 0：实时；1：预约：2：接机；3：送机
         */
        private Integer schedule_type;

        public String getTravel_msg() {
            return travel_msg;
        }

        public void setTravel_msg(String travel_msg) {
            this.travel_msg = travel_msg;
        }

        public String getPassenger_msg() {
            return passenger_msg;
        }

        public void setPassenger_msg(String passenger_msg) {
            this.passenger_msg = passenger_msg;
        }

        public Integer getSchedule_type() {
            return schedule_type;
        }

        public void setSchedule_type(Integer schedule_type) {
            this.schedule_type = schedule_type;
        }

        @Override
        public String toString() {
            return JSONObject.toJSONString(this);
        }
    }

    public static class HotelInfo {

        /**
         * 酒店详情
         */
        private String travel_msg;

        /**
         * 入住人 (名字)
         */
        private String guest_msg;

        public String getTravel_msg() {
            return travel_msg;
        }

        public void setTravel_msg(String travel_msg) {
            this.travel_msg = travel_msg;
        }

        public String getGuest_msg() {
            return guest_msg;
        }

        public void setGuest_msg(String guest_msg) {
            this.guest_msg = guest_msg;
        }

        @Override
        public String toString() {
            return JSONObject.toJSONString(this);
        }
    }

    public static class TrainInfo {

        /**
         * 行程
         */
        private String travel_msg;

        /**
         * 乘机人 (名字)
         */
        private String passenger_msg;

        public String getTravel_msg() {
            return travel_msg;
        }

        public void setTravel_msg(String travel_msg) {
            this.travel_msg = travel_msg;
        }

        public String getPassenger_msg() {
            return passenger_msg;
        }

        public void setPassenger_msg(String passenger_msg) {
            this.passenger_msg = passenger_msg;
        }

        @Override
        public String toString() {
            return JSONObject.toJSONString(this);
        }
    }

    public static class BusInfo {

        /**
         * 行程
         */
        private String travel_msg;

        /**
         * 乘机人 (名字)
         */
        private String passenger_msg;

        public String getTravel_msg() {
            return travel_msg;
        }

        public void setTravel_msg(String travel_msg) {
            this.travel_msg = travel_msg;
        }

        public String getPassenger_msg() {
            return passenger_msg;
        }

        public void setPassenger_msg(String passenger_msg) {
            this.passenger_msg = passenger_msg;
        }

        @Override
        public String toString() {
            return JSONObject.toJSONString(this);
        }
    }

    public static class MallInfo {

        /**
         * 采购物品
         */
        private String products_msg;

        public String getProducts_msg() {
            return products_msg;
        }

        public void setProducts_msg(String products_msg) {
            this.products_msg = products_msg;
        }

        @Override
        public String toString() {
            return JSONObject.toJSONString(this);
        }
    }

    public static class DinnerInfo {

        /**
         * 用餐地点信息
         */
        private String restaurant_msg;

        /**
         * 用餐人
         */
        private String contact_msg;

        public String getRestaurant_msg() {
            return restaurant_msg;
        }

        public void setRestaurant_msg(String restaurant_msg) {
            this.restaurant_msg = restaurant_msg;
        }

        public String getContact_msg() {
            return contact_msg;
        }

        public void setContact_msg(String contact_msg) {
            this.contact_msg = contact_msg;
        }
    }

    public DinnerInfo getDinner_info() {
        return dinner_info;
    }

    public void setDinner_info(DinnerInfo dinner_info) {
        this.dinner_info = dinner_info;
    }

    public static class TakeawayInfo {

        /**
         * 商家店铺信息
         */
        private String restaurant_msg;

        /**
         * 收货人
         */
        private String receiver_name;

        /**
         * 收货人手机号
         */
        private String receiver_phone;

        /**
         * 收货人地址
         */
        private String receiver_address;

        public String getRestaurant_msg() {
            return restaurant_msg;
        }

        public void setRestaurant_msg(String restaurant_msg) {
            this.restaurant_msg = restaurant_msg;
        }

        public String getReceiver_name() {
            return receiver_name;
        }

        public void setReceiver_name(String receiver_name) {
            this.receiver_name = receiver_name;
        }

        public String getReceiver_phone() {
            return receiver_phone;
        }

        public void setReceiver_phone(String receiver_phone) {
            this.receiver_phone = receiver_phone;
        }

        public String getReceiver_address() {
            return receiver_address;
        }

        public void setReceiver_address(String receiver_address) {
            this.receiver_address = receiver_address;
        }
    }

    public TakeawayInfo getTakeaway_info() {
        return takeaway_info;
    }

    public void setTakeaway_info(TakeawayInfo takeaway_info) {
        this.takeaway_info = takeaway_info;
    }

    public static class ExpressInfo {

        /**
         * 类型
         */
        private String goods_type;

        /**
         * 重量
         */
        private String good_weight;

        /**
         * 采购价
         */
        private BigDecimal cost_price;

        /**
         * 手续费
         */
        private BigDecimal transfer_pay_price;

        /**
         * 劵金额
         */
        private BigDecimal coupon_amount;

        /**
         * 订单完成时间
         */
        private String finish_time;

        public String getGoods_type() {
            return goods_type;
        }

        public void setGoods_type(String goods_type) {
            this.goods_type = goods_type;
        }

        public String getGood_weight() {
            return good_weight;
        }

        public void setGood_weight(String good_weight) {
            this.good_weight = good_weight;
        }

        public BigDecimal getCost_price() {
            return cost_price;
        }

        public void setCost_price(BigDecimal cost_price) {
            this.cost_price = cost_price;
        }

        public BigDecimal getTransfer_pay_price() {
            return transfer_pay_price;
        }

        public void setTransfer_pay_price(BigDecimal transfer_pay_price) {
            this.transfer_pay_price = transfer_pay_price;
        }

        public BigDecimal getCoupon_amount() {
            return coupon_amount;
        }

        public void setCoupon_amount(BigDecimal coupon_amount) {
            this.coupon_amount = coupon_amount;
        }

        public String getFinish_time() {
            return finish_time;
        }

        public void setFinish_time(String finish_time) {
            this.finish_time = finish_time;
        }
    }

    public ExpressInfo getExpress_info() {
        return express_info;
    }

    public void setExpress_info(ExpressInfo express_info) {
        this.express_info = express_info;
    }

    public String getCost_attribution_name() {
        return cost_attribution_name;
    }

    public void setCost_attribution_name(String cost_attribution_name) {
        this.cost_attribution_name = cost_attribution_name;
    }

    public List<CostAttributionInfo> getCost_attribution_list() {
        return cost_attribution_list;
    }

    public void setCost_attribution_list(List<CostAttributionInfo> cost_attribution_list) {
        this.cost_attribution_list = cost_attribution_list;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getData_type() {
        return data_type;
    }

    public void setData_type(Integer data_type) {
        this.data_type = data_type;
    }

    public String getSender_msg() {
        return sender_msg;
    }

    public void setSender_msg(String sender_msg) {
        this.sender_msg = sender_msg;
    }
}
