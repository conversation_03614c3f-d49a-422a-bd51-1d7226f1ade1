package com.fenbeitong.saas.core.contract.costcenter;

import java.util.List;

/**
 * Created by xuzn on 18/3/23.
 */
public class BatchAddContract {

    private Integer autoFlag;
    private Integer count;
    private List<CodeBean> codeList;
    private List<ProjectInfoBean> projectInfo;
    private Integer costCenterNum;

    public Integer getAutoFlag() {
        return autoFlag;
    }

    public void setAutoFlag(Integer autoFlag) {
        this.autoFlag = autoFlag;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public List<CodeBean> getCodeList() {
        return codeList;
    }

    public void setCodeList(List<CodeBean> codeList) {
        this.codeList = codeList;
    }

    public List<ProjectInfoBean> getProjectInfo() {
        return projectInfo;
    }

    public void setProjectInfo(List<ProjectInfoBean> projectInfo) {
        this.projectInfo = projectInfo;
    }

    public Integer getCostCenterNum() {
        return costCenterNum;
    }

    public void setCostCenterNum(Integer costCenterNum) {
        this.costCenterNum = costCenterNum;
    }

    public static class ProjectInfoBean {
        private String code;
        private String name;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    public static class CodeBean{
        private String code;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }
    }
}
