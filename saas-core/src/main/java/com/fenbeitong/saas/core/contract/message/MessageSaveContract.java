package com.fenbeitong.saas.core.contract.message;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.saas.core.contract.auth.SelfAuthInfo;
import com.fenbeitong.saas.core.contract.message.inner.*;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/4/12.
 */
public class MessageSaveContract {

    /**
     * 2 消费通知 4 审批通知 8 订单通知 16 订票提醒 32 系统通知
     */
    private Integer message_type;

    private String title;

    private String content;

    /**
     * 发送人类型: 1 人, 2 HL, 3 SAAS
     */
    private Integer sender_type;

    /**
     * 发送人id
     */
    private String sender;

    /**
     * 接收人id
     */
    private String receiver;

    /**
     * (消费通知 订单号, 订单通知 原订单号 , 审批通知 applyId, 订票提醒 applyId, 系统通知 无)
     * 新增 自主授权 - 授权id
     */
    private String biz_order;

    private String link;

    private ConsumptionInfo consumption_info;

    private OrderInfo order_info;

    private ApplyInfo apply_info;

    private OrderAlertInfo order_alert_info;

    private SystemInfo system_info;

    private TransactionInfo transaction_info;
    
    private SelfAuthInfo selfAuthInfo;

    private String company_id;

    public SelfAuthInfo getSelfAuthInfo() {
        return selfAuthInfo;
    }

    public void setSelfAuthInfo(SelfAuthInfo selfAuthInfo) {
        this.selfAuthInfo = selfAuthInfo;
    }

    public Integer getMessage_type() {
        return message_type;
    }

    public void setMessage_type(Integer message_type) {
        this.message_type = message_type;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getSender_type() {
        return sender_type;
    }

    public void setSender_type(Integer sender_type) {
        this.sender_type = sender_type;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    public String getBiz_order() {
        return biz_order;
    }

    public void setBiz_order(String biz_order) {
        this.biz_order = biz_order;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public ConsumptionInfo getConsumption_info() {
        return consumption_info;
    }

    public void setConsumption_info(ConsumptionInfo consumption_info) {
        this.consumption_info = consumption_info;
    }

    public OrderInfo getOrder_info() {
        return order_info;
    }

    public void setOrder_info(OrderInfo order_info) {
        this.order_info = order_info;
    }

    public ApplyInfo getApply_info() {
        return apply_info;
    }

    public void setApply_info(ApplyInfo apply_info) {
        this.apply_info = apply_info;
    }

    public OrderAlertInfo getOrder_alert_info() {
        return order_alert_info;
    }

    public void setOrder_alert_info(OrderAlertInfo order_alert_info) {
        this.order_alert_info = order_alert_info;
    }

    public SystemInfo getSystem_info() {
        return system_info;
    }

    public void setSystem_info(SystemInfo system_info) {
        this.system_info = system_info;
    }

    public TransactionInfo getTransaction_info() {
        return transaction_info;
    }

    public void setTransaction_info(TransactionInfo transaction_info) {
        this.transaction_info = transaction_info;
    }

    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }

    public String getCompany_id() {
        return company_id;
    }

    public void setCompany_id(String company_id) {
        this.company_id = company_id;
    }
}
