package com.fenbeitong.saas.core.model.fenbeitong;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TrainInterceptRecordExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table train_intercept_record
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table train_intercept_record
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table train_intercept_record
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table train_intercept_record
     *
     * @mbg.generated
     */
    public TrainInterceptRecordExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table train_intercept_record
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table train_intercept_record
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table train_intercept_record
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table train_intercept_record
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table train_intercept_record
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table train_intercept_record
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table train_intercept_record
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table train_intercept_record
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table train_intercept_record
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table train_intercept_record
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table train_intercept_record
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdIsNull() {
            addCriterion("employee_id is null");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdIsNotNull() {
            addCriterion("employee_id is not null");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdEqualTo(String value) {
            addCriterion("employee_id =", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdNotEqualTo(String value) {
            addCriterion("employee_id <>", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdGreaterThan(String value) {
            addCriterion("employee_id >", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdGreaterThanOrEqualTo(String value) {
            addCriterion("employee_id >=", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdLessThan(String value) {
            addCriterion("employee_id <", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdLessThanOrEqualTo(String value) {
            addCriterion("employee_id <=", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdLike(String value) {
            addCriterion("employee_id like", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdNotLike(String value) {
            addCriterion("employee_id not like", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdIn(List<String> values) {
            addCriterion("employee_id in", values, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdNotIn(List<String> values) {
            addCriterion("employee_id not in", values, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdBetween(String value1, String value2) {
            addCriterion("employee_id between", value1, value2, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdNotBetween(String value1, String value2) {
            addCriterion("employee_id not between", value1, value2, "employeeId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(String value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(String value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(String value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(String value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(String value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(String value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLike(String value) {
            addCriterion("company_id like", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotLike(String value) {
            addCriterion("company_id not like", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<String> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<String> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(String value1, String value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(String value1, String value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andContactNameIsNull() {
            addCriterion("contact_name is null");
            return (Criteria) this;
        }

        public Criteria andContactNameIsNotNull() {
            addCriterion("contact_name is not null");
            return (Criteria) this;
        }

        public Criteria andContactNameEqualTo(String value) {
            addCriterion("contact_name =", value, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameNotEqualTo(String value) {
            addCriterion("contact_name <>", value, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameGreaterThan(String value) {
            addCriterion("contact_name >", value, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameGreaterThanOrEqualTo(String value) {
            addCriterion("contact_name >=", value, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameLessThan(String value) {
            addCriterion("contact_name <", value, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameLessThanOrEqualTo(String value) {
            addCriterion("contact_name <=", value, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameLike(String value) {
            addCriterion("contact_name like", value, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameNotLike(String value) {
            addCriterion("contact_name not like", value, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameIn(List<String> values) {
            addCriterion("contact_name in", values, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameNotIn(List<String> values) {
            addCriterion("contact_name not in", values, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameBetween(String value1, String value2) {
            addCriterion("contact_name between", value1, value2, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameNotBetween(String value1, String value2) {
            addCriterion("contact_name not between", value1, value2, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactPhoneIsNull() {
            addCriterion("contact_phone is null");
            return (Criteria) this;
        }

        public Criteria andContactPhoneIsNotNull() {
            addCriterion("contact_phone is not null");
            return (Criteria) this;
        }

        public Criteria andContactPhoneEqualTo(String value) {
            addCriterion("contact_phone =", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotEqualTo(String value) {
            addCriterion("contact_phone <>", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneGreaterThan(String value) {
            addCriterion("contact_phone >", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("contact_phone >=", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneLessThan(String value) {
            addCriterion("contact_phone <", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneLessThanOrEqualTo(String value) {
            addCriterion("contact_phone <=", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneLike(String value) {
            addCriterion("contact_phone like", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotLike(String value) {
            addCriterion("contact_phone not like", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneIn(List<String> values) {
            addCriterion("contact_phone in", values, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotIn(List<String> values) {
            addCriterion("contact_phone not in", values, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneBetween(String value1, String value2) {
            addCriterion("contact_phone between", value1, value2, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotBetween(String value1, String value2) {
            addCriterion("contact_phone not between", value1, value2, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andChannelIsNull() {
            addCriterion("channel is null");
            return (Criteria) this;
        }

        public Criteria andChannelIsNotNull() {
            addCriterion("channel is not null");
            return (Criteria) this;
        }

        public Criteria andChannelEqualTo(Integer value) {
            addCriterion("channel =", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelNotEqualTo(Integer value) {
            addCriterion("channel <>", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelGreaterThan(Integer value) {
            addCriterion("channel >", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelGreaterThanOrEqualTo(Integer value) {
            addCriterion("channel >=", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelLessThan(Integer value) {
            addCriterion("channel <", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelLessThanOrEqualTo(Integer value) {
            addCriterion("channel <=", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelIn(List<Integer> values) {
            addCriterion("channel in", values, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelNotIn(List<Integer> values) {
            addCriterion("channel not in", values, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelBetween(Integer value1, Integer value2) {
            addCriterion("channel between", value1, value2, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelNotBetween(Integer value1, Integer value2) {
            addCriterion("channel not between", value1, value2, "channel");
            return (Criteria) this;
        }

        public Criteria andTotalPriceIsNull() {
            addCriterion("total_price is null");
            return (Criteria) this;
        }

        public Criteria andTotalPriceIsNotNull() {
            addCriterion("total_price is not null");
            return (Criteria) this;
        }

        public Criteria andTotalPriceEqualTo(BigDecimal value) {
            addCriterion("total_price =", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotEqualTo(BigDecimal value) {
            addCriterion("total_price <>", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceGreaterThan(BigDecimal value) {
            addCriterion("total_price >", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_price >=", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceLessThan(BigDecimal value) {
            addCriterion("total_price <", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_price <=", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceIn(List<BigDecimal> values) {
            addCriterion("total_price in", values, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotIn(List<BigDecimal> values) {
            addCriterion("total_price not in", values, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_price between", value1, value2, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_price not between", value1, value2, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTrainRuleIsNull() {
            addCriterion("train_rule is null");
            return (Criteria) this;
        }

        public Criteria andTrainRuleIsNotNull() {
            addCriterion("train_rule is not null");
            return (Criteria) this;
        }

        public Criteria andTrainRuleEqualTo(Integer value) {
            addCriterion("train_rule =", value, "trainRule");
            return (Criteria) this;
        }

        public Criteria andTrainRuleNotEqualTo(Integer value) {
            addCriterion("train_rule <>", value, "trainRule");
            return (Criteria) this;
        }

        public Criteria andTrainRuleGreaterThan(Integer value) {
            addCriterion("train_rule >", value, "trainRule");
            return (Criteria) this;
        }

        public Criteria andTrainRuleGreaterThanOrEqualTo(Integer value) {
            addCriterion("train_rule >=", value, "trainRule");
            return (Criteria) this;
        }

        public Criteria andTrainRuleLessThan(Integer value) {
            addCriterion("train_rule <", value, "trainRule");
            return (Criteria) this;
        }

        public Criteria andTrainRuleLessThanOrEqualTo(Integer value) {
            addCriterion("train_rule <=", value, "trainRule");
            return (Criteria) this;
        }

        public Criteria andTrainRuleIn(List<Integer> values) {
            addCriterion("train_rule in", values, "trainRule");
            return (Criteria) this;
        }

        public Criteria andTrainRuleNotIn(List<Integer> values) {
            addCriterion("train_rule not in", values, "trainRule");
            return (Criteria) this;
        }

        public Criteria andTrainRuleBetween(Integer value1, Integer value2) {
            addCriterion("train_rule between", value1, value2, "trainRule");
            return (Criteria) this;
        }

        public Criteria andTrainRuleNotBetween(Integer value1, Integer value2) {
            addCriterion("train_rule not between", value1, value2, "trainRule");
            return (Criteria) this;
        }

        public Criteria andTrainRuleFlagIsNull() {
            addCriterion("train_rule_flag is null");
            return (Criteria) this;
        }

        public Criteria andTrainRuleFlagIsNotNull() {
            addCriterion("train_rule_flag is not null");
            return (Criteria) this;
        }

        public Criteria andTrainRuleFlagEqualTo(Boolean value) {
            addCriterion("train_rule_flag =", value, "trainRuleFlag");
            return (Criteria) this;
        }

        public Criteria andTrainRuleFlagNotEqualTo(Boolean value) {
            addCriterion("train_rule_flag <>", value, "trainRuleFlag");
            return (Criteria) this;
        }

        public Criteria andTrainRuleFlagGreaterThan(Boolean value) {
            addCriterion("train_rule_flag >", value, "trainRuleFlag");
            return (Criteria) this;
        }

        public Criteria andTrainRuleFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("train_rule_flag >=", value, "trainRuleFlag");
            return (Criteria) this;
        }

        public Criteria andTrainRuleFlagLessThan(Boolean value) {
            addCriterion("train_rule_flag <", value, "trainRuleFlag");
            return (Criteria) this;
        }

        public Criteria andTrainRuleFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("train_rule_flag <=", value, "trainRuleFlag");
            return (Criteria) this;
        }

        public Criteria andTrainRuleFlagIn(List<Boolean> values) {
            addCriterion("train_rule_flag in", values, "trainRuleFlag");
            return (Criteria) this;
        }

        public Criteria andTrainRuleFlagNotIn(List<Boolean> values) {
            addCriterion("train_rule_flag not in", values, "trainRuleFlag");
            return (Criteria) this;
        }

        public Criteria andTrainRuleFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("train_rule_flag between", value1, value2, "trainRuleFlag");
            return (Criteria) this;
        }

        public Criteria andTrainRuleFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("train_rule_flag not between", value1, value2, "trainRuleFlag");
            return (Criteria) this;
        }

        public Criteria andTrainVerifyFlagIsNull() {
            addCriterion("train_verify_flag is null");
            return (Criteria) this;
        }

        public Criteria andTrainVerifyFlagIsNotNull() {
            addCriterion("train_verify_flag is not null");
            return (Criteria) this;
        }

        public Criteria andTrainVerifyFlagEqualTo(Boolean value) {
            addCriterion("train_verify_flag =", value, "trainVerifyFlag");
            return (Criteria) this;
        }

        public Criteria andTrainVerifyFlagNotEqualTo(Boolean value) {
            addCriterion("train_verify_flag <>", value, "trainVerifyFlag");
            return (Criteria) this;
        }

        public Criteria andTrainVerifyFlagGreaterThan(Boolean value) {
            addCriterion("train_verify_flag >", value, "trainVerifyFlag");
            return (Criteria) this;
        }

        public Criteria andTrainVerifyFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("train_verify_flag >=", value, "trainVerifyFlag");
            return (Criteria) this;
        }

        public Criteria andTrainVerifyFlagLessThan(Boolean value) {
            addCriterion("train_verify_flag <", value, "trainVerifyFlag");
            return (Criteria) this;
        }

        public Criteria andTrainVerifyFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("train_verify_flag <=", value, "trainVerifyFlag");
            return (Criteria) this;
        }

        public Criteria andTrainVerifyFlagIn(List<Boolean> values) {
            addCriterion("train_verify_flag in", values, "trainVerifyFlag");
            return (Criteria) this;
        }

        public Criteria andTrainVerifyFlagNotIn(List<Boolean> values) {
            addCriterion("train_verify_flag not in", values, "trainVerifyFlag");
            return (Criteria) this;
        }

        public Criteria andTrainVerifyFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("train_verify_flag between", value1, value2, "trainVerifyFlag");
            return (Criteria) this;
        }

        public Criteria andTrainVerifyFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("train_verify_flag not between", value1, value2, "trainVerifyFlag");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagIsNull() {
            addCriterion("exceed_buy_flag is null");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagIsNotNull() {
            addCriterion("exceed_buy_flag is not null");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagEqualTo(Boolean value) {
            addCriterion("exceed_buy_flag =", value, "exceedBuyFlag");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagNotEqualTo(Boolean value) {
            addCriterion("exceed_buy_flag <>", value, "exceedBuyFlag");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagGreaterThan(Boolean value) {
            addCriterion("exceed_buy_flag >", value, "exceedBuyFlag");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("exceed_buy_flag >=", value, "exceedBuyFlag");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagLessThan(Boolean value) {
            addCriterion("exceed_buy_flag <", value, "exceedBuyFlag");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("exceed_buy_flag <=", value, "exceedBuyFlag");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagIn(List<Boolean> values) {
            addCriterion("exceed_buy_flag in", values, "exceedBuyFlag");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagNotIn(List<Boolean> values) {
            addCriterion("exceed_buy_flag not in", values, "exceedBuyFlag");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("exceed_buy_flag between", value1, value2, "exceedBuyFlag");
            return (Criteria) this;
        }

        public Criteria andExceedBuyFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("exceed_buy_flag not between", value1, value2, "exceedBuyFlag");
            return (Criteria) this;
        }

        public Criteria andTrainSeatFlagIsNull() {
            addCriterion("train_seat_flag is null");
            return (Criteria) this;
        }

        public Criteria andTrainSeatFlagIsNotNull() {
            addCriterion("train_seat_flag is not null");
            return (Criteria) this;
        }

        public Criteria andTrainSeatFlagEqualTo(Boolean value) {
            addCriterion("train_seat_flag =", value, "trainSeatFlag");
            return (Criteria) this;
        }

        public Criteria andTrainSeatFlagNotEqualTo(Boolean value) {
            addCriterion("train_seat_flag <>", value, "trainSeatFlag");
            return (Criteria) this;
        }

        public Criteria andTrainSeatFlagGreaterThan(Boolean value) {
            addCriterion("train_seat_flag >", value, "trainSeatFlag");
            return (Criteria) this;
        }

        public Criteria andTrainSeatFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("train_seat_flag >=", value, "trainSeatFlag");
            return (Criteria) this;
        }

        public Criteria andTrainSeatFlagLessThan(Boolean value) {
            addCriterion("train_seat_flag <", value, "trainSeatFlag");
            return (Criteria) this;
        }

        public Criteria andTrainSeatFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("train_seat_flag <=", value, "trainSeatFlag");
            return (Criteria) this;
        }

        public Criteria andTrainSeatFlagIn(List<Boolean> values) {
            addCriterion("train_seat_flag in", values, "trainSeatFlag");
            return (Criteria) this;
        }

        public Criteria andTrainSeatFlagNotIn(List<Boolean> values) {
            addCriterion("train_seat_flag not in", values, "trainSeatFlag");
            return (Criteria) this;
        }

        public Criteria andTrainSeatFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("train_seat_flag between", value1, value2, "trainSeatFlag");
            return (Criteria) this;
        }

        public Criteria andTrainSeatFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("train_seat_flag not between", value1, value2, "trainSeatFlag");
            return (Criteria) this;
        }

        public Criteria andCommonTrainSeatTypeIsNull() {
            addCriterion("common_train_seat_type is null");
            return (Criteria) this;
        }

        public Criteria andCommonTrainSeatTypeIsNotNull() {
            addCriterion("common_train_seat_type is not null");
            return (Criteria) this;
        }

        public Criteria andCommonTrainSeatTypeEqualTo(String value) {
            addCriterion("common_train_seat_type =", value, "commonTrainSeatType");
            return (Criteria) this;
        }

        public Criteria andCommonTrainSeatTypeNotEqualTo(String value) {
            addCriterion("common_train_seat_type <>", value, "commonTrainSeatType");
            return (Criteria) this;
        }

        public Criteria andCommonTrainSeatTypeGreaterThan(String value) {
            addCriterion("common_train_seat_type >", value, "commonTrainSeatType");
            return (Criteria) this;
        }

        public Criteria andCommonTrainSeatTypeGreaterThanOrEqualTo(String value) {
            addCriterion("common_train_seat_type >=", value, "commonTrainSeatType");
            return (Criteria) this;
        }

        public Criteria andCommonTrainSeatTypeLessThan(String value) {
            addCriterion("common_train_seat_type <", value, "commonTrainSeatType");
            return (Criteria) this;
        }

        public Criteria andCommonTrainSeatTypeLessThanOrEqualTo(String value) {
            addCriterion("common_train_seat_type <=", value, "commonTrainSeatType");
            return (Criteria) this;
        }

        public Criteria andCommonTrainSeatTypeLike(String value) {
            addCriterion("common_train_seat_type like", value, "commonTrainSeatType");
            return (Criteria) this;
        }

        public Criteria andCommonTrainSeatTypeNotLike(String value) {
            addCriterion("common_train_seat_type not like", value, "commonTrainSeatType");
            return (Criteria) this;
        }

        public Criteria andCommonTrainSeatTypeIn(List<String> values) {
            addCriterion("common_train_seat_type in", values, "commonTrainSeatType");
            return (Criteria) this;
        }

        public Criteria andCommonTrainSeatTypeNotIn(List<String> values) {
            addCriterion("common_train_seat_type not in", values, "commonTrainSeatType");
            return (Criteria) this;
        }

        public Criteria andCommonTrainSeatTypeBetween(String value1, String value2) {
            addCriterion("common_train_seat_type between", value1, value2, "commonTrainSeatType");
            return (Criteria) this;
        }

        public Criteria andCommonTrainSeatTypeNotBetween(String value1, String value2) {
            addCriterion("common_train_seat_type not between", value1, value2, "commonTrainSeatType");
            return (Criteria) this;
        }

        public Criteria andHighspeedTrainSeatTypeIsNull() {
            addCriterion("highspeed_train_seat_type is null");
            return (Criteria) this;
        }

        public Criteria andHighspeedTrainSeatTypeIsNotNull() {
            addCriterion("highspeed_train_seat_type is not null");
            return (Criteria) this;
        }

        public Criteria andHighspeedTrainSeatTypeEqualTo(String value) {
            addCriterion("highspeed_train_seat_type =", value, "highspeedTrainSeatType");
            return (Criteria) this;
        }

        public Criteria andHighspeedTrainSeatTypeNotEqualTo(String value) {
            addCriterion("highspeed_train_seat_type <>", value, "highspeedTrainSeatType");
            return (Criteria) this;
        }

        public Criteria andHighspeedTrainSeatTypeGreaterThan(String value) {
            addCriterion("highspeed_train_seat_type >", value, "highspeedTrainSeatType");
            return (Criteria) this;
        }

        public Criteria andHighspeedTrainSeatTypeGreaterThanOrEqualTo(String value) {
            addCriterion("highspeed_train_seat_type >=", value, "highspeedTrainSeatType");
            return (Criteria) this;
        }

        public Criteria andHighspeedTrainSeatTypeLessThan(String value) {
            addCriterion("highspeed_train_seat_type <", value, "highspeedTrainSeatType");
            return (Criteria) this;
        }

        public Criteria andHighspeedTrainSeatTypeLessThanOrEqualTo(String value) {
            addCriterion("highspeed_train_seat_type <=", value, "highspeedTrainSeatType");
            return (Criteria) this;
        }

        public Criteria andHighspeedTrainSeatTypeLike(String value) {
            addCriterion("highspeed_train_seat_type like", value, "highspeedTrainSeatType");
            return (Criteria) this;
        }

        public Criteria andHighspeedTrainSeatTypeNotLike(String value) {
            addCriterion("highspeed_train_seat_type not like", value, "highspeedTrainSeatType");
            return (Criteria) this;
        }

        public Criteria andHighspeedTrainSeatTypeIn(List<String> values) {
            addCriterion("highspeed_train_seat_type in", values, "highspeedTrainSeatType");
            return (Criteria) this;
        }

        public Criteria andHighspeedTrainSeatTypeNotIn(List<String> values) {
            addCriterion("highspeed_train_seat_type not in", values, "highspeedTrainSeatType");
            return (Criteria) this;
        }

        public Criteria andHighspeedTrainSeatTypeBetween(String value1, String value2) {
            addCriterion("highspeed_train_seat_type between", value1, value2, "highspeedTrainSeatType");
            return (Criteria) this;
        }

        public Criteria andHighspeedTrainSeatTypeNotBetween(String value1, String value2) {
            addCriterion("highspeed_train_seat_type not between", value1, value2, "highspeedTrainSeatType");
            return (Criteria) this;
        }

        public Criteria andTrainCodeIsNull() {
            addCriterion("train_code is null");
            return (Criteria) this;
        }

        public Criteria andTrainCodeIsNotNull() {
            addCriterion("train_code is not null");
            return (Criteria) this;
        }

        public Criteria andTrainCodeEqualTo(String value) {
            addCriterion("train_code =", value, "trainCode");
            return (Criteria) this;
        }

        public Criteria andTrainCodeNotEqualTo(String value) {
            addCriterion("train_code <>", value, "trainCode");
            return (Criteria) this;
        }

        public Criteria andTrainCodeGreaterThan(String value) {
            addCriterion("train_code >", value, "trainCode");
            return (Criteria) this;
        }

        public Criteria andTrainCodeGreaterThanOrEqualTo(String value) {
            addCriterion("train_code >=", value, "trainCode");
            return (Criteria) this;
        }

        public Criteria andTrainCodeLessThan(String value) {
            addCriterion("train_code <", value, "trainCode");
            return (Criteria) this;
        }

        public Criteria andTrainCodeLessThanOrEqualTo(String value) {
            addCriterion("train_code <=", value, "trainCode");
            return (Criteria) this;
        }

        public Criteria andTrainCodeLike(String value) {
            addCriterion("train_code like", value, "trainCode");
            return (Criteria) this;
        }

        public Criteria andTrainCodeNotLike(String value) {
            addCriterion("train_code not like", value, "trainCode");
            return (Criteria) this;
        }

        public Criteria andTrainCodeIn(List<String> values) {
            addCriterion("train_code in", values, "trainCode");
            return (Criteria) this;
        }

        public Criteria andTrainCodeNotIn(List<String> values) {
            addCriterion("train_code not in", values, "trainCode");
            return (Criteria) this;
        }

        public Criteria andTrainCodeBetween(String value1, String value2) {
            addCriterion("train_code between", value1, value2, "trainCode");
            return (Criteria) this;
        }

        public Criteria andTrainCodeNotBetween(String value1, String value2) {
            addCriterion("train_code not between", value1, value2, "trainCode");
            return (Criteria) this;
        }

        public Criteria andTrainNoIsNull() {
            addCriterion("train_no is null");
            return (Criteria) this;
        }

        public Criteria andTrainNoIsNotNull() {
            addCriterion("train_no is not null");
            return (Criteria) this;
        }

        public Criteria andTrainNoEqualTo(String value) {
            addCriterion("train_no =", value, "trainNo");
            return (Criteria) this;
        }

        public Criteria andTrainNoNotEqualTo(String value) {
            addCriterion("train_no <>", value, "trainNo");
            return (Criteria) this;
        }

        public Criteria andTrainNoGreaterThan(String value) {
            addCriterion("train_no >", value, "trainNo");
            return (Criteria) this;
        }

        public Criteria andTrainNoGreaterThanOrEqualTo(String value) {
            addCriterion("train_no >=", value, "trainNo");
            return (Criteria) this;
        }

        public Criteria andTrainNoLessThan(String value) {
            addCriterion("train_no <", value, "trainNo");
            return (Criteria) this;
        }

        public Criteria andTrainNoLessThanOrEqualTo(String value) {
            addCriterion("train_no <=", value, "trainNo");
            return (Criteria) this;
        }

        public Criteria andTrainNoLike(String value) {
            addCriterion("train_no like", value, "trainNo");
            return (Criteria) this;
        }

        public Criteria andTrainNoNotLike(String value) {
            addCriterion("train_no not like", value, "trainNo");
            return (Criteria) this;
        }

        public Criteria andTrainNoIn(List<String> values) {
            addCriterion("train_no in", values, "trainNo");
            return (Criteria) this;
        }

        public Criteria andTrainNoNotIn(List<String> values) {
            addCriterion("train_no not in", values, "trainNo");
            return (Criteria) this;
        }

        public Criteria andTrainNoBetween(String value1, String value2) {
            addCriterion("train_no between", value1, value2, "trainNo");
            return (Criteria) this;
        }

        public Criteria andTrainNoNotBetween(String value1, String value2) {
            addCriterion("train_no not between", value1, value2, "trainNo");
            return (Criteria) this;
        }

        public Criteria andFromStationCodeIsNull() {
            addCriterion("from_station_code is null");
            return (Criteria) this;
        }

        public Criteria andFromStationCodeIsNotNull() {
            addCriterion("from_station_code is not null");
            return (Criteria) this;
        }

        public Criteria andFromStationCodeEqualTo(String value) {
            addCriterion("from_station_code =", value, "fromStationCode");
            return (Criteria) this;
        }

        public Criteria andFromStationCodeNotEqualTo(String value) {
            addCriterion("from_station_code <>", value, "fromStationCode");
            return (Criteria) this;
        }

        public Criteria andFromStationCodeGreaterThan(String value) {
            addCriterion("from_station_code >", value, "fromStationCode");
            return (Criteria) this;
        }

        public Criteria andFromStationCodeGreaterThanOrEqualTo(String value) {
            addCriterion("from_station_code >=", value, "fromStationCode");
            return (Criteria) this;
        }

        public Criteria andFromStationCodeLessThan(String value) {
            addCriterion("from_station_code <", value, "fromStationCode");
            return (Criteria) this;
        }

        public Criteria andFromStationCodeLessThanOrEqualTo(String value) {
            addCriterion("from_station_code <=", value, "fromStationCode");
            return (Criteria) this;
        }

        public Criteria andFromStationCodeLike(String value) {
            addCriterion("from_station_code like", value, "fromStationCode");
            return (Criteria) this;
        }

        public Criteria andFromStationCodeNotLike(String value) {
            addCriterion("from_station_code not like", value, "fromStationCode");
            return (Criteria) this;
        }

        public Criteria andFromStationCodeIn(List<String> values) {
            addCriterion("from_station_code in", values, "fromStationCode");
            return (Criteria) this;
        }

        public Criteria andFromStationCodeNotIn(List<String> values) {
            addCriterion("from_station_code not in", values, "fromStationCode");
            return (Criteria) this;
        }

        public Criteria andFromStationCodeBetween(String value1, String value2) {
            addCriterion("from_station_code between", value1, value2, "fromStationCode");
            return (Criteria) this;
        }

        public Criteria andFromStationCodeNotBetween(String value1, String value2) {
            addCriterion("from_station_code not between", value1, value2, "fromStationCode");
            return (Criteria) this;
        }

        public Criteria andFromStationNameIsNull() {
            addCriterion("from_station_name is null");
            return (Criteria) this;
        }

        public Criteria andFromStationNameIsNotNull() {
            addCriterion("from_station_name is not null");
            return (Criteria) this;
        }

        public Criteria andFromStationNameEqualTo(String value) {
            addCriterion("from_station_name =", value, "fromStationName");
            return (Criteria) this;
        }

        public Criteria andFromStationNameNotEqualTo(String value) {
            addCriterion("from_station_name <>", value, "fromStationName");
            return (Criteria) this;
        }

        public Criteria andFromStationNameGreaterThan(String value) {
            addCriterion("from_station_name >", value, "fromStationName");
            return (Criteria) this;
        }

        public Criteria andFromStationNameGreaterThanOrEqualTo(String value) {
            addCriterion("from_station_name >=", value, "fromStationName");
            return (Criteria) this;
        }

        public Criteria andFromStationNameLessThan(String value) {
            addCriterion("from_station_name <", value, "fromStationName");
            return (Criteria) this;
        }

        public Criteria andFromStationNameLessThanOrEqualTo(String value) {
            addCriterion("from_station_name <=", value, "fromStationName");
            return (Criteria) this;
        }

        public Criteria andFromStationNameLike(String value) {
            addCriterion("from_station_name like", value, "fromStationName");
            return (Criteria) this;
        }

        public Criteria andFromStationNameNotLike(String value) {
            addCriterion("from_station_name not like", value, "fromStationName");
            return (Criteria) this;
        }

        public Criteria andFromStationNameIn(List<String> values) {
            addCriterion("from_station_name in", values, "fromStationName");
            return (Criteria) this;
        }

        public Criteria andFromStationNameNotIn(List<String> values) {
            addCriterion("from_station_name not in", values, "fromStationName");
            return (Criteria) this;
        }

        public Criteria andFromStationNameBetween(String value1, String value2) {
            addCriterion("from_station_name between", value1, value2, "fromStationName");
            return (Criteria) this;
        }

        public Criteria andFromStationNameNotBetween(String value1, String value2) {
            addCriterion("from_station_name not between", value1, value2, "fromStationName");
            return (Criteria) this;
        }

        public Criteria andToStationCodeIsNull() {
            addCriterion("to_station_code is null");
            return (Criteria) this;
        }

        public Criteria andToStationCodeIsNotNull() {
            addCriterion("to_station_code is not null");
            return (Criteria) this;
        }

        public Criteria andToStationCodeEqualTo(String value) {
            addCriterion("to_station_code =", value, "toStationCode");
            return (Criteria) this;
        }

        public Criteria andToStationCodeNotEqualTo(String value) {
            addCriterion("to_station_code <>", value, "toStationCode");
            return (Criteria) this;
        }

        public Criteria andToStationCodeGreaterThan(String value) {
            addCriterion("to_station_code >", value, "toStationCode");
            return (Criteria) this;
        }

        public Criteria andToStationCodeGreaterThanOrEqualTo(String value) {
            addCriterion("to_station_code >=", value, "toStationCode");
            return (Criteria) this;
        }

        public Criteria andToStationCodeLessThan(String value) {
            addCriterion("to_station_code <", value, "toStationCode");
            return (Criteria) this;
        }

        public Criteria andToStationCodeLessThanOrEqualTo(String value) {
            addCriterion("to_station_code <=", value, "toStationCode");
            return (Criteria) this;
        }

        public Criteria andToStationCodeLike(String value) {
            addCriterion("to_station_code like", value, "toStationCode");
            return (Criteria) this;
        }

        public Criteria andToStationCodeNotLike(String value) {
            addCriterion("to_station_code not like", value, "toStationCode");
            return (Criteria) this;
        }

        public Criteria andToStationCodeIn(List<String> values) {
            addCriterion("to_station_code in", values, "toStationCode");
            return (Criteria) this;
        }

        public Criteria andToStationCodeNotIn(List<String> values) {
            addCriterion("to_station_code not in", values, "toStationCode");
            return (Criteria) this;
        }

        public Criteria andToStationCodeBetween(String value1, String value2) {
            addCriterion("to_station_code between", value1, value2, "toStationCode");
            return (Criteria) this;
        }

        public Criteria andToStationCodeNotBetween(String value1, String value2) {
            addCriterion("to_station_code not between", value1, value2, "toStationCode");
            return (Criteria) this;
        }

        public Criteria andToStationNameIsNull() {
            addCriterion("to_station_name is null");
            return (Criteria) this;
        }

        public Criteria andToStationNameIsNotNull() {
            addCriterion("to_station_name is not null");
            return (Criteria) this;
        }

        public Criteria andToStationNameEqualTo(String value) {
            addCriterion("to_station_name =", value, "toStationName");
            return (Criteria) this;
        }

        public Criteria andToStationNameNotEqualTo(String value) {
            addCriterion("to_station_name <>", value, "toStationName");
            return (Criteria) this;
        }

        public Criteria andToStationNameGreaterThan(String value) {
            addCriterion("to_station_name >", value, "toStationName");
            return (Criteria) this;
        }

        public Criteria andToStationNameGreaterThanOrEqualTo(String value) {
            addCriterion("to_station_name >=", value, "toStationName");
            return (Criteria) this;
        }

        public Criteria andToStationNameLessThan(String value) {
            addCriterion("to_station_name <", value, "toStationName");
            return (Criteria) this;
        }

        public Criteria andToStationNameLessThanOrEqualTo(String value) {
            addCriterion("to_station_name <=", value, "toStationName");
            return (Criteria) this;
        }

        public Criteria andToStationNameLike(String value) {
            addCriterion("to_station_name like", value, "toStationName");
            return (Criteria) this;
        }

        public Criteria andToStationNameNotLike(String value) {
            addCriterion("to_station_name not like", value, "toStationName");
            return (Criteria) this;
        }

        public Criteria andToStationNameIn(List<String> values) {
            addCriterion("to_station_name in", values, "toStationName");
            return (Criteria) this;
        }

        public Criteria andToStationNameNotIn(List<String> values) {
            addCriterion("to_station_name not in", values, "toStationName");
            return (Criteria) this;
        }

        public Criteria andToStationNameBetween(String value1, String value2) {
            addCriterion("to_station_name between", value1, value2, "toStationName");
            return (Criteria) this;
        }

        public Criteria andToStationNameNotBetween(String value1, String value2) {
            addCriterion("to_station_name not between", value1, value2, "toStationName");
            return (Criteria) this;
        }

        public Criteria andTrainStartDateIsNull() {
            addCriterion("train_start_date is null");
            return (Criteria) this;
        }

        public Criteria andTrainStartDateIsNotNull() {
            addCriterion("train_start_date is not null");
            return (Criteria) this;
        }

        public Criteria andTrainStartDateEqualTo(String value) {
            addCriterion("train_start_date =", value, "trainStartDate");
            return (Criteria) this;
        }

        public Criteria andTrainStartDateNotEqualTo(String value) {
            addCriterion("train_start_date <>", value, "trainStartDate");
            return (Criteria) this;
        }

        public Criteria andTrainStartDateGreaterThan(String value) {
            addCriterion("train_start_date >", value, "trainStartDate");
            return (Criteria) this;
        }

        public Criteria andTrainStartDateGreaterThanOrEqualTo(String value) {
            addCriterion("train_start_date >=", value, "trainStartDate");
            return (Criteria) this;
        }

        public Criteria andTrainStartDateLessThan(String value) {
            addCriterion("train_start_date <", value, "trainStartDate");
            return (Criteria) this;
        }

        public Criteria andTrainStartDateLessThanOrEqualTo(String value) {
            addCriterion("train_start_date <=", value, "trainStartDate");
            return (Criteria) this;
        }

        public Criteria andTrainStartDateLike(String value) {
            addCriterion("train_start_date like", value, "trainStartDate");
            return (Criteria) this;
        }

        public Criteria andTrainStartDateNotLike(String value) {
            addCriterion("train_start_date not like", value, "trainStartDate");
            return (Criteria) this;
        }

        public Criteria andTrainStartDateIn(List<String> values) {
            addCriterion("train_start_date in", values, "trainStartDate");
            return (Criteria) this;
        }

        public Criteria andTrainStartDateNotIn(List<String> values) {
            addCriterion("train_start_date not in", values, "trainStartDate");
            return (Criteria) this;
        }

        public Criteria andTrainStartDateBetween(String value1, String value2) {
            addCriterion("train_start_date between", value1, value2, "trainStartDate");
            return (Criteria) this;
        }

        public Criteria andTrainStartDateNotBetween(String value1, String value2) {
            addCriterion("train_start_date not between", value1, value2, "trainStartDate");
            return (Criteria) this;
        }

        public Criteria andTrainEndDateIsNull() {
            addCriterion("train_end_date is null");
            return (Criteria) this;
        }

        public Criteria andTrainEndDateIsNotNull() {
            addCriterion("train_end_date is not null");
            return (Criteria) this;
        }

        public Criteria andTrainEndDateEqualTo(String value) {
            addCriterion("train_end_date =", value, "trainEndDate");
            return (Criteria) this;
        }

        public Criteria andTrainEndDateNotEqualTo(String value) {
            addCriterion("train_end_date <>", value, "trainEndDate");
            return (Criteria) this;
        }

        public Criteria andTrainEndDateGreaterThan(String value) {
            addCriterion("train_end_date >", value, "trainEndDate");
            return (Criteria) this;
        }

        public Criteria andTrainEndDateGreaterThanOrEqualTo(String value) {
            addCriterion("train_end_date >=", value, "trainEndDate");
            return (Criteria) this;
        }

        public Criteria andTrainEndDateLessThan(String value) {
            addCriterion("train_end_date <", value, "trainEndDate");
            return (Criteria) this;
        }

        public Criteria andTrainEndDateLessThanOrEqualTo(String value) {
            addCriterion("train_end_date <=", value, "trainEndDate");
            return (Criteria) this;
        }

        public Criteria andTrainEndDateLike(String value) {
            addCriterion("train_end_date like", value, "trainEndDate");
            return (Criteria) this;
        }

        public Criteria andTrainEndDateNotLike(String value) {
            addCriterion("train_end_date not like", value, "trainEndDate");
            return (Criteria) this;
        }

        public Criteria andTrainEndDateIn(List<String> values) {
            addCriterion("train_end_date in", values, "trainEndDate");
            return (Criteria) this;
        }

        public Criteria andTrainEndDateNotIn(List<String> values) {
            addCriterion("train_end_date not in", values, "trainEndDate");
            return (Criteria) this;
        }

        public Criteria andTrainEndDateBetween(String value1, String value2) {
            addCriterion("train_end_date between", value1, value2, "trainEndDate");
            return (Criteria) this;
        }

        public Criteria andTrainEndDateNotBetween(String value1, String value2) {
            addCriterion("train_end_date not between", value1, value2, "trainEndDate");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNull() {
            addCriterion("start_time is null");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNotNull() {
            addCriterion("start_time is not null");
            return (Criteria) this;
        }

        public Criteria andStartTimeEqualTo(String value) {
            addCriterion("start_time =", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotEqualTo(String value) {
            addCriterion("start_time <>", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThan(String value) {
            addCriterion("start_time >", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThanOrEqualTo(String value) {
            addCriterion("start_time >=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThan(String value) {
            addCriterion("start_time <", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThanOrEqualTo(String value) {
            addCriterion("start_time <=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLike(String value) {
            addCriterion("start_time like", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotLike(String value) {
            addCriterion("start_time not like", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeIn(List<String> values) {
            addCriterion("start_time in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotIn(List<String> values) {
            addCriterion("start_time not in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeBetween(String value1, String value2) {
            addCriterion("start_time between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotBetween(String value1, String value2) {
            addCriterion("start_time not between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andArriveTimeIsNull() {
            addCriterion("arrive_time is null");
            return (Criteria) this;
        }

        public Criteria andArriveTimeIsNotNull() {
            addCriterion("arrive_time is not null");
            return (Criteria) this;
        }

        public Criteria andArriveTimeEqualTo(String value) {
            addCriterion("arrive_time =", value, "arriveTime");
            return (Criteria) this;
        }

        public Criteria andArriveTimeNotEqualTo(String value) {
            addCriterion("arrive_time <>", value, "arriveTime");
            return (Criteria) this;
        }

        public Criteria andArriveTimeGreaterThan(String value) {
            addCriterion("arrive_time >", value, "arriveTime");
            return (Criteria) this;
        }

        public Criteria andArriveTimeGreaterThanOrEqualTo(String value) {
            addCriterion("arrive_time >=", value, "arriveTime");
            return (Criteria) this;
        }

        public Criteria andArriveTimeLessThan(String value) {
            addCriterion("arrive_time <", value, "arriveTime");
            return (Criteria) this;
        }

        public Criteria andArriveTimeLessThanOrEqualTo(String value) {
            addCriterion("arrive_time <=", value, "arriveTime");
            return (Criteria) this;
        }

        public Criteria andArriveTimeLike(String value) {
            addCriterion("arrive_time like", value, "arriveTime");
            return (Criteria) this;
        }

        public Criteria andArriveTimeNotLike(String value) {
            addCriterion("arrive_time not like", value, "arriveTime");
            return (Criteria) this;
        }

        public Criteria andArriveTimeIn(List<String> values) {
            addCriterion("arrive_time in", values, "arriveTime");
            return (Criteria) this;
        }

        public Criteria andArriveTimeNotIn(List<String> values) {
            addCriterion("arrive_time not in", values, "arriveTime");
            return (Criteria) this;
        }

        public Criteria andArriveTimeBetween(String value1, String value2) {
            addCriterion("arrive_time between", value1, value2, "arriveTime");
            return (Criteria) this;
        }

        public Criteria andArriveTimeNotBetween(String value1, String value2) {
            addCriterion("arrive_time not between", value1, value2, "arriveTime");
            return (Criteria) this;
        }

        public Criteria andRunTimeIsNull() {
            addCriterion("run_time is null");
            return (Criteria) this;
        }

        public Criteria andRunTimeIsNotNull() {
            addCriterion("run_time is not null");
            return (Criteria) this;
        }

        public Criteria andRunTimeEqualTo(String value) {
            addCriterion("run_time =", value, "runTime");
            return (Criteria) this;
        }

        public Criteria andRunTimeNotEqualTo(String value) {
            addCriterion("run_time <>", value, "runTime");
            return (Criteria) this;
        }

        public Criteria andRunTimeGreaterThan(String value) {
            addCriterion("run_time >", value, "runTime");
            return (Criteria) this;
        }

        public Criteria andRunTimeGreaterThanOrEqualTo(String value) {
            addCriterion("run_time >=", value, "runTime");
            return (Criteria) this;
        }

        public Criteria andRunTimeLessThan(String value) {
            addCriterion("run_time <", value, "runTime");
            return (Criteria) this;
        }

        public Criteria andRunTimeLessThanOrEqualTo(String value) {
            addCriterion("run_time <=", value, "runTime");
            return (Criteria) this;
        }

        public Criteria andRunTimeLike(String value) {
            addCriterion("run_time like", value, "runTime");
            return (Criteria) this;
        }

        public Criteria andRunTimeNotLike(String value) {
            addCriterion("run_time not like", value, "runTime");
            return (Criteria) this;
        }

        public Criteria andRunTimeIn(List<String> values) {
            addCriterion("run_time in", values, "runTime");
            return (Criteria) this;
        }

        public Criteria andRunTimeNotIn(List<String> values) {
            addCriterion("run_time not in", values, "runTime");
            return (Criteria) this;
        }

        public Criteria andRunTimeBetween(String value1, String value2) {
            addCriterion("run_time between", value1, value2, "runTime");
            return (Criteria) this;
        }

        public Criteria andRunTimeNotBetween(String value1, String value2) {
            addCriterion("run_time not between", value1, value2, "runTime");
            return (Criteria) this;
        }

        public Criteria andArriveDaysIsNull() {
            addCriterion("arrive_days is null");
            return (Criteria) this;
        }

        public Criteria andArriveDaysIsNotNull() {
            addCriterion("arrive_days is not null");
            return (Criteria) this;
        }

        public Criteria andArriveDaysEqualTo(Integer value) {
            addCriterion("arrive_days =", value, "arriveDays");
            return (Criteria) this;
        }

        public Criteria andArriveDaysNotEqualTo(Integer value) {
            addCriterion("arrive_days <>", value, "arriveDays");
            return (Criteria) this;
        }

        public Criteria andArriveDaysGreaterThan(Integer value) {
            addCriterion("arrive_days >", value, "arriveDays");
            return (Criteria) this;
        }

        public Criteria andArriveDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("arrive_days >=", value, "arriveDays");
            return (Criteria) this;
        }

        public Criteria andArriveDaysLessThan(Integer value) {
            addCriterion("arrive_days <", value, "arriveDays");
            return (Criteria) this;
        }

        public Criteria andArriveDaysLessThanOrEqualTo(Integer value) {
            addCriterion("arrive_days <=", value, "arriveDays");
            return (Criteria) this;
        }

        public Criteria andArriveDaysIn(List<Integer> values) {
            addCriterion("arrive_days in", values, "arriveDays");
            return (Criteria) this;
        }

        public Criteria andArriveDaysNotIn(List<Integer> values) {
            addCriterion("arrive_days not in", values, "arriveDays");
            return (Criteria) this;
        }

        public Criteria andArriveDaysBetween(Integer value1, Integer value2) {
            addCriterion("arrive_days between", value1, value2, "arriveDays");
            return (Criteria) this;
        }

        public Criteria andArriveDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("arrive_days not between", value1, value2, "arriveDays");
            return (Criteria) this;
        }

        public Criteria andSeatTypeIsNull() {
            addCriterion("seat_type is null");
            return (Criteria) this;
        }

        public Criteria andSeatTypeIsNotNull() {
            addCriterion("seat_type is not null");
            return (Criteria) this;
        }

        public Criteria andSeatTypeEqualTo(String value) {
            addCriterion("seat_type =", value, "seatType");
            return (Criteria) this;
        }

        public Criteria andSeatTypeNotEqualTo(String value) {
            addCriterion("seat_type <>", value, "seatType");
            return (Criteria) this;
        }

        public Criteria andSeatTypeGreaterThan(String value) {
            addCriterion("seat_type >", value, "seatType");
            return (Criteria) this;
        }

        public Criteria andSeatTypeGreaterThanOrEqualTo(String value) {
            addCriterion("seat_type >=", value, "seatType");
            return (Criteria) this;
        }

        public Criteria andSeatTypeLessThan(String value) {
            addCriterion("seat_type <", value, "seatType");
            return (Criteria) this;
        }

        public Criteria andSeatTypeLessThanOrEqualTo(String value) {
            addCriterion("seat_type <=", value, "seatType");
            return (Criteria) this;
        }

        public Criteria andSeatTypeLike(String value) {
            addCriterion("seat_type like", value, "seatType");
            return (Criteria) this;
        }

        public Criteria andSeatTypeNotLike(String value) {
            addCriterion("seat_type not like", value, "seatType");
            return (Criteria) this;
        }

        public Criteria andSeatTypeIn(List<String> values) {
            addCriterion("seat_type in", values, "seatType");
            return (Criteria) this;
        }

        public Criteria andSeatTypeNotIn(List<String> values) {
            addCriterion("seat_type not in", values, "seatType");
            return (Criteria) this;
        }

        public Criteria andSeatTypeBetween(String value1, String value2) {
            addCriterion("seat_type between", value1, value2, "seatType");
            return (Criteria) this;
        }

        public Criteria andSeatTypeNotBetween(String value1, String value2) {
            addCriterion("seat_type not between", value1, value2, "seatType");
            return (Criteria) this;
        }

        public Criteria andSeatNoIsNull() {
            addCriterion("seat_no is null");
            return (Criteria) this;
        }

        public Criteria andSeatNoIsNotNull() {
            addCriterion("seat_no is not null");
            return (Criteria) this;
        }

        public Criteria andSeatNoEqualTo(String value) {
            addCriterion("seat_no =", value, "seatNo");
            return (Criteria) this;
        }

        public Criteria andSeatNoNotEqualTo(String value) {
            addCriterion("seat_no <>", value, "seatNo");
            return (Criteria) this;
        }

        public Criteria andSeatNoGreaterThan(String value) {
            addCriterion("seat_no >", value, "seatNo");
            return (Criteria) this;
        }

        public Criteria andSeatNoGreaterThanOrEqualTo(String value) {
            addCriterion("seat_no >=", value, "seatNo");
            return (Criteria) this;
        }

        public Criteria andSeatNoLessThan(String value) {
            addCriterion("seat_no <", value, "seatNo");
            return (Criteria) this;
        }

        public Criteria andSeatNoLessThanOrEqualTo(String value) {
            addCriterion("seat_no <=", value, "seatNo");
            return (Criteria) this;
        }

        public Criteria andSeatNoLike(String value) {
            addCriterion("seat_no like", value, "seatNo");
            return (Criteria) this;
        }

        public Criteria andSeatNoNotLike(String value) {
            addCriterion("seat_no not like", value, "seatNo");
            return (Criteria) this;
        }

        public Criteria andSeatNoIn(List<String> values) {
            addCriterion("seat_no in", values, "seatNo");
            return (Criteria) this;
        }

        public Criteria andSeatNoNotIn(List<String> values) {
            addCriterion("seat_no not in", values, "seatNo");
            return (Criteria) this;
        }

        public Criteria andSeatNoBetween(String value1, String value2) {
            addCriterion("seat_no between", value1, value2, "seatNo");
            return (Criteria) this;
        }

        public Criteria andSeatNoNotBetween(String value1, String value2) {
            addCriterion("seat_no not between", value1, value2, "seatNo");
            return (Criteria) this;
        }

        public Criteria andSeatPriceIsNull() {
            addCriterion("seat_price is null");
            return (Criteria) this;
        }

        public Criteria andSeatPriceIsNotNull() {
            addCriterion("seat_price is not null");
            return (Criteria) this;
        }

        public Criteria andSeatPriceEqualTo(BigDecimal value) {
            addCriterion("seat_price =", value, "seatPrice");
            return (Criteria) this;
        }

        public Criteria andSeatPriceNotEqualTo(BigDecimal value) {
            addCriterion("seat_price <>", value, "seatPrice");
            return (Criteria) this;
        }

        public Criteria andSeatPriceGreaterThan(BigDecimal value) {
            addCriterion("seat_price >", value, "seatPrice");
            return (Criteria) this;
        }

        public Criteria andSeatPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("seat_price >=", value, "seatPrice");
            return (Criteria) this;
        }

        public Criteria andSeatPriceLessThan(BigDecimal value) {
            addCriterion("seat_price <", value, "seatPrice");
            return (Criteria) this;
        }

        public Criteria andSeatPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("seat_price <=", value, "seatPrice");
            return (Criteria) this;
        }

        public Criteria andSeatPriceIn(List<BigDecimal> values) {
            addCriterion("seat_price in", values, "seatPrice");
            return (Criteria) this;
        }

        public Criteria andSeatPriceNotIn(List<BigDecimal> values) {
            addCriterion("seat_price not in", values, "seatPrice");
            return (Criteria) this;
        }

        public Criteria andSeatPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("seat_price between", value1, value2, "seatPrice");
            return (Criteria) this;
        }

        public Criteria andSeatPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("seat_price not between", value1, value2, "seatPrice");
            return (Criteria) this;
        }

        public Criteria andServiceFeeIsNull() {
            addCriterion("service_fee is null");
            return (Criteria) this;
        }

        public Criteria andServiceFeeIsNotNull() {
            addCriterion("service_fee is not null");
            return (Criteria) this;
        }

        public Criteria andServiceFeeEqualTo(BigDecimal value) {
            addCriterion("service_fee =", value, "serviceFee");
            return (Criteria) this;
        }

        public Criteria andServiceFeeNotEqualTo(BigDecimal value) {
            addCriterion("service_fee <>", value, "serviceFee");
            return (Criteria) this;
        }

        public Criteria andServiceFeeGreaterThan(BigDecimal value) {
            addCriterion("service_fee >", value, "serviceFee");
            return (Criteria) this;
        }

        public Criteria andServiceFeeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("service_fee >=", value, "serviceFee");
            return (Criteria) this;
        }

        public Criteria andServiceFeeLessThan(BigDecimal value) {
            addCriterion("service_fee <", value, "serviceFee");
            return (Criteria) this;
        }

        public Criteria andServiceFeeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("service_fee <=", value, "serviceFee");
            return (Criteria) this;
        }

        public Criteria andServiceFeeIn(List<BigDecimal> values) {
            addCriterion("service_fee in", values, "serviceFee");
            return (Criteria) this;
        }

        public Criteria andServiceFeeNotIn(List<BigDecimal> values) {
            addCriterion("service_fee not in", values, "serviceFee");
            return (Criteria) this;
        }

        public Criteria andServiceFeeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("service_fee between", value1, value2, "serviceFee");
            return (Criteria) this;
        }

        public Criteria andServiceFeeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("service_fee not between", value1, value2, "serviceFee");
            return (Criteria) this;
        }

        public Criteria andPassengerInfoListIsNull() {
            addCriterion("passenger_info_list is null");
            return (Criteria) this;
        }

        public Criteria andPassengerInfoListIsNotNull() {
            addCriterion("passenger_info_list is not null");
            return (Criteria) this;
        }

        public Criteria andPassengerInfoListEqualTo(String value) {
            addCriterion("passenger_info_list =", value, "passengerInfoList");
            return (Criteria) this;
        }

        public Criteria andPassengerInfoListNotEqualTo(String value) {
            addCriterion("passenger_info_list <>", value, "passengerInfoList");
            return (Criteria) this;
        }

        public Criteria andPassengerInfoListGreaterThan(String value) {
            addCriterion("passenger_info_list >", value, "passengerInfoList");
            return (Criteria) this;
        }

        public Criteria andPassengerInfoListGreaterThanOrEqualTo(String value) {
            addCriterion("passenger_info_list >=", value, "passengerInfoList");
            return (Criteria) this;
        }

        public Criteria andPassengerInfoListLessThan(String value) {
            addCriterion("passenger_info_list <", value, "passengerInfoList");
            return (Criteria) this;
        }

        public Criteria andPassengerInfoListLessThanOrEqualTo(String value) {
            addCriterion("passenger_info_list <=", value, "passengerInfoList");
            return (Criteria) this;
        }

        public Criteria andPassengerInfoListLike(String value) {
            addCriterion("passenger_info_list like", value, "passengerInfoList");
            return (Criteria) this;
        }

        public Criteria andPassengerInfoListNotLike(String value) {
            addCriterion("passenger_info_list not like", value, "passengerInfoList");
            return (Criteria) this;
        }

        public Criteria andPassengerInfoListIn(List<String> values) {
            addCriterion("passenger_info_list in", values, "passengerInfoList");
            return (Criteria) this;
        }

        public Criteria andPassengerInfoListNotIn(List<String> values) {
            addCriterion("passenger_info_list not in", values, "passengerInfoList");
            return (Criteria) this;
        }

        public Criteria andPassengerInfoListBetween(String value1, String value2) {
            addCriterion("passenger_info_list between", value1, value2, "passengerInfoList");
            return (Criteria) this;
        }

        public Criteria andPassengerInfoListNotBetween(String value1, String value2) {
            addCriterion("passenger_info_list not between", value1, value2, "passengerInfoList");
            return (Criteria) this;
        }

        public Criteria andErrMsgIsNull() {
            addCriterion("err_msg is null");
            return (Criteria) this;
        }

        public Criteria andErrMsgIsNotNull() {
            addCriterion("err_msg is not null");
            return (Criteria) this;
        }

        public Criteria andErrMsgEqualTo(String value) {
            addCriterion("err_msg =", value, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgNotEqualTo(String value) {
            addCriterion("err_msg <>", value, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgGreaterThan(String value) {
            addCriterion("err_msg >", value, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgGreaterThanOrEqualTo(String value) {
            addCriterion("err_msg >=", value, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgLessThan(String value) {
            addCriterion("err_msg <", value, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgLessThanOrEqualTo(String value) {
            addCriterion("err_msg <=", value, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgLike(String value) {
            addCriterion("err_msg like", value, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgNotLike(String value) {
            addCriterion("err_msg not like", value, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgIn(List<String> values) {
            addCriterion("err_msg in", values, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgNotIn(List<String> values) {
            addCriterion("err_msg not in", values, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgBetween(String value1, String value2) {
            addCriterion("err_msg between", value1, value2, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrMsgNotBetween(String value1, String value2) {
            addCriterion("err_msg not between", value1, value2, "errMsg");
            return (Criteria) this;
        }

        public Criteria andErrCodeIsNull() {
            addCriterion("err_code is null");
            return (Criteria) this;
        }

        public Criteria andErrCodeIsNotNull() {
            addCriterion("err_code is not null");
            return (Criteria) this;
        }

        public Criteria andErrCodeEqualTo(Integer value) {
            addCriterion("err_code =", value, "errCode");
            return (Criteria) this;
        }

        public Criteria andErrCodeNotEqualTo(Integer value) {
            addCriterion("err_code <>", value, "errCode");
            return (Criteria) this;
        }

        public Criteria andErrCodeGreaterThan(Integer value) {
            addCriterion("err_code >", value, "errCode");
            return (Criteria) this;
        }

        public Criteria andErrCodeGreaterThanOrEqualTo(Integer value) {
            addCriterion("err_code >=", value, "errCode");
            return (Criteria) this;
        }

        public Criteria andErrCodeLessThan(Integer value) {
            addCriterion("err_code <", value, "errCode");
            return (Criteria) this;
        }

        public Criteria andErrCodeLessThanOrEqualTo(Integer value) {
            addCriterion("err_code <=", value, "errCode");
            return (Criteria) this;
        }

        public Criteria andErrCodeIn(List<Integer> values) {
            addCriterion("err_code in", values, "errCode");
            return (Criteria) this;
        }

        public Criteria andErrCodeNotIn(List<Integer> values) {
            addCriterion("err_code not in", values, "errCode");
            return (Criteria) this;
        }

        public Criteria andErrCodeBetween(Integer value1, Integer value2) {
            addCriterion("err_code between", value1, value2, "errCode");
            return (Criteria) this;
        }

        public Criteria andErrCodeNotBetween(Integer value1, Integer value2) {
            addCriterion("err_code not between", value1, value2, "errCode");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdIsNull() {
            addCriterion("cost_center_id is null");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdIsNotNull() {
            addCriterion("cost_center_id is not null");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdEqualTo(String value) {
            addCriterion("cost_center_id =", value, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdNotEqualTo(String value) {
            addCriterion("cost_center_id <>", value, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdGreaterThan(String value) {
            addCriterion("cost_center_id >", value, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdGreaterThanOrEqualTo(String value) {
            addCriterion("cost_center_id >=", value, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdLessThan(String value) {
            addCriterion("cost_center_id <", value, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdLessThanOrEqualTo(String value) {
            addCriterion("cost_center_id <=", value, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdLike(String value) {
            addCriterion("cost_center_id like", value, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdNotLike(String value) {
            addCriterion("cost_center_id not like", value, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdIn(List<String> values) {
            addCriterion("cost_center_id in", values, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdNotIn(List<String> values) {
            addCriterion("cost_center_id not in", values, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdBetween(String value1, String value2) {
            addCriterion("cost_center_id between", value1, value2, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterIdNotBetween(String value1, String value2) {
            addCriterion("cost_center_id not between", value1, value2, "costCenterId");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeIsNull() {
            addCriterion("cost_center_type is null");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeIsNotNull() {
            addCriterion("cost_center_type is not null");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeEqualTo(Integer value) {
            addCriterion("cost_center_type =", value, "costCenterType");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeNotEqualTo(Integer value) {
            addCriterion("cost_center_type <>", value, "costCenterType");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeGreaterThan(Integer value) {
            addCriterion("cost_center_type >", value, "costCenterType");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("cost_center_type >=", value, "costCenterType");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeLessThan(Integer value) {
            addCriterion("cost_center_type <", value, "costCenterType");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeLessThanOrEqualTo(Integer value) {
            addCriterion("cost_center_type <=", value, "costCenterType");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeIn(List<Integer> values) {
            addCriterion("cost_center_type in", values, "costCenterType");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeNotIn(List<Integer> values) {
            addCriterion("cost_center_type not in", values, "costCenterType");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeBetween(Integer value1, Integer value2) {
            addCriterion("cost_center_type between", value1, value2, "costCenterType");
            return (Criteria) this;
        }

        public Criteria andCostCenterTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("cost_center_type not between", value1, value2, "costCenterType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeIsNull() {
            addCriterion("exceed_buy_type is null");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeIsNotNull() {
            addCriterion("exceed_buy_type is not null");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeEqualTo(Integer value) {
            addCriterion("exceed_buy_type =", value, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeNotEqualTo(Integer value) {
            addCriterion("exceed_buy_type <>", value, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeGreaterThan(Integer value) {
            addCriterion("exceed_buy_type >", value, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("exceed_buy_type >=", value, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeLessThan(Integer value) {
            addCriterion("exceed_buy_type <", value, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeLessThanOrEqualTo(Integer value) {
            addCriterion("exceed_buy_type <=", value, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeIn(List<Integer> values) {
            addCriterion("exceed_buy_type in", values, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeNotIn(List<Integer> values) {
            addCriterion("exceed_buy_type not in", values, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeBetween(Integer value1, Integer value2) {
            addCriterion("exceed_buy_type between", value1, value2, "exceedBuyType");
            return (Criteria) this;
        }

        public Criteria andExceedBuyTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("exceed_buy_type not between", value1, value2, "exceedBuyType");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table train_intercept_record
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table train_intercept_record
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}