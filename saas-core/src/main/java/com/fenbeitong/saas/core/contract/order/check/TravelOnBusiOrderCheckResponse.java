package com.fenbeitong.saas.core.contract.order.check;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fenbeitong.saas.core.model.fenbeitong.AirRule;
import com.fenbeitong.saas.core.model.fenbeitong.HotelRule;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TravelOnBusiOrderCheckResponse {
    //人员id
    @JsonProperty("user_id")
    private String userId;
    //人员名称
    @JsonProperty("user_name")
    private String userName;
    //申请单id
    @JsonProperty("apply_id")
    private String applyId;
    //行程id
    @JsonProperty("trip_id")
    private String tripId;
    //当前人超规信息
    @JsonProperty("msg_info_list")
    private List<ErrorMessageInfo> msgInfoList;
    //酒店规则快照
    @JsonProperty("hotel_rule")
    private HotelRule hotelRule;
    //机票规则快照
    @JsonProperty("air_rule")
    private AirRule airRule;

    @Data
    public static class ErrorMessageInfo{
        private Integer code;
        private String msg;
    }

}
