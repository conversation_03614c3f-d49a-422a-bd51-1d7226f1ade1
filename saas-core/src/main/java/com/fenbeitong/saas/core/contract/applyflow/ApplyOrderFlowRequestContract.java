package com.fenbeitong.saas.core.contract.applyflow;

import java.util.List;

/**
 * Created by xiabin on 2017/4/21.
 */
public class ApplyOrderFlowRequestContract {
    List<CompanyApplyFlowItemSetV2RequestContract> flow_list;
    List<CompanyApplyFlowItemSetV2RequestContract> cc_list;

    public List<CompanyApplyFlowItemSetV2RequestContract> getFlow_list() {
        return flow_list;
    }

    public void setFlow_list(List<CompanyApplyFlowItemSetV2RequestContract> flow_list) {
        this.flow_list = flow_list;
    }

    public List<CompanyApplyFlowItemSetV2RequestContract> getCc_list() {
        return cc_list;
    }

    public void setCc_list(List<CompanyApplyFlowItemSetV2RequestContract> cc_list) {
        this.cc_list = cc_list;
    }
}
