package com.fenbeitong.saas.core.contract.messagesettings.inner;

import com.fenbeitong.saas.api.model.dto.user.EmployeeEmailContract;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by zhangkai on 2017/6/2.
 */
public class AnalysisReport {
    private int weekly;
    private int monthly;
    private List<String> email_list = new ArrayList<>();

    private Boolean hasUnbind = false;
    /**
     *  新版邮箱绑定人员列表
     **/
    private List<EmployeeEmailContract>  emails = new ArrayList<>();

    public AnalysisReport() {
    }

    public AnalysisReport(int weekly, int monthly) {
        this.weekly = weekly;
        this.monthly = monthly;
    }

    public AnalysisReport(int weekly, int monthly, List<String> email_list) {
        this.weekly = weekly;
        this.monthly = monthly;
        this.email_list = email_list;
    }

    public int getWeekly() {
        return weekly;
    }

    public void setWeekly(int weekly) {
        this.weekly = weekly;
    }

    public int getMonthly() {
        return monthly;
    }

    public void setMonthly(int monthly) {
        this.monthly = monthly;
    }

    public List<String> getEmail_list() {
        return email_list;
    }

    public void setEmail_list(List<String> email_list) {
        this.email_list = email_list;
    }

    public Boolean getHasUnbind() {
        return hasUnbind;
    }

    public void setHasUnbind(Boolean hasUnbind) {
        this.hasUnbind = hasUnbind;
    }

    public List<EmployeeEmailContract> getEmails() {
        return emails;
    }

    public void setEmails(List<EmployeeEmailContract> emails) {
        this.emails = emails;
    }
}
