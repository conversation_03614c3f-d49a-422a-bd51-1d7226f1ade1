package com.fenbeitong.saas.core.contract.messagesettings;

import com.fenbeitong.saas.api.model.dto.BaseDto;
import lombok.Data;

/**
 * @ClassName ExceedPersonalPayCompanyConf
 * @Description 超规则个人付企业个人原因配置
 * <AUTHOR>
 * @Date 2022/12/5 10:19
 * @Version 1.0
 */
@Data
public class ExceedPersonalPayCompanyConf extends BaseDto {

	/**
	 * 企业是否开启退票个人原因* 
	 */
	private Boolean isShowRefundReason;

	/**
	 * 企业是否开启改签个人原因* 
	 */
	private Boolean isShowChangeReason;
}
