package com.fenbeitong.saas.core.contract.organization.inner;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/5/22.
 */
public class BizTripPolicyBean {
    /**
     * 本人机票权限
     */
    private Boolean air_priv_flag;

    /**
     * 为其他员工预定权限
     */
    private Boolean air_other_flag;
    /**
     * 机票是否需要审批
     */
    private Boolean air_verify_flag;
    /**
     * 限制非企业员工预定机票标识
     */
    private Boolean unemployee_air;
    /**
     * 本人酒店权限
     */
    private Boolean hotel_priv_flag;
    /**
     * 为其他员工预定权限
     */
    private Boolean hotel_other_flag;

    /**
     * 酒店是否需要审批
     */
    private Boolean hotel_verify_flag;
    /**
     * 限制非企业员工预定酒店标识
     */
    private Boolean unemployee_hotel;
    /**
     * 本人火车权限
     */
    private Boolean train_priv_flag;
    /**
     * 为其他员工预定权限
     */
    private Boolean train_other_flag;

    /**
     * 火车是否需要审批
     */
    private Boolean train_verify_flag;
    /**
     * 限制非企业员工预定火车标识
     */
    private Boolean unemployee_train;
    /**
     * 是否限制采购规则
     */
    private Boolean rule_limit_flag;
    /**
     * 差旅规则 id
     */
    private String rule_id;
    /**
     * 是否允超标
     */
    private Boolean exceed_buy_flag;

    private Integer exceed_buy_type;


    private int air_rule;
    private int hotel_rule;
    private int train_rule;

    private String rule_name;

    public Boolean getAir_other_flag() {
        return air_other_flag;
    }

    public void setAir_other_flag(Boolean air_other_flag) {
        this.air_other_flag = air_other_flag;
    }

    public Boolean getHotel_other_flag() {
        return hotel_other_flag;
    }

    public void setHotel_other_flag(Boolean hotel_other_flag) {
        this.hotel_other_flag = hotel_other_flag;
    }

    public Boolean getTrain_other_flag() {
        return train_other_flag;
    }

    public void setTrain_other_flag(Boolean train_other_flag) {
        this.train_other_flag = train_other_flag;
    }

    public Integer getExceed_buy_type() {
        return exceed_buy_type;
    }

    public void setExceed_buy_type(Integer exceed_buy_type) {
        this.exceed_buy_type = exceed_buy_type;
    }

    public Boolean isAir_priv_flag() {
        return air_priv_flag;
    }

    public void setAir_priv_flag(Boolean air_priv_flag) {
        this.air_priv_flag = air_priv_flag;
    }

    public Boolean isAir_verify_flag() {
        return air_verify_flag;
    }

    public void setAir_verify_flag(Boolean air_verify_flag) {
        this.air_verify_flag = air_verify_flag;
    }

    public Boolean isHotel_priv_flag() {
        return hotel_priv_flag;
    }

    public void setHotel_priv_flag(Boolean hotel_priv_flag) {
        this.hotel_priv_flag = hotel_priv_flag;
    }

    public Boolean isHotel_verify_flag() {
        return hotel_verify_flag;
    }

    public void setHotel_verify_flag(Boolean hotel_verify_flag) {
        this.hotel_verify_flag = hotel_verify_flag;
    }

    public Boolean isTrain_priv_flag() {
        return train_priv_flag;
    }

    public void setTrain_priv_flag(Boolean train_priv_flag) {
        this.train_priv_flag = train_priv_flag;
    }

    public Boolean isTrain_verify_flag() {
        return train_verify_flag;
    }

    public void setTrain_verify_flag(Boolean train_verify_flag) {
        this.train_verify_flag = train_verify_flag;
    }

    public Boolean isRule_limit_flag() {
        return rule_limit_flag;
    }

    public void setRule_limit_flag(Boolean rule_limit_flag) {
        this.rule_limit_flag = rule_limit_flag;
    }

    public String getRule_id() {
        return rule_id;
    }

    public void setRule_id(String rule_id) {
        this.rule_id = rule_id;
    }

    public Boolean isExceed_buy_flag() {
        return exceed_buy_flag;
    }

    public void setExceed_buy_flag(Boolean exceed_buy_flag) {
        this.exceed_buy_flag = exceed_buy_flag;
    }

    public Boolean getUnemployee_air() {
        return unemployee_air;
    }

    public void setUnemployee_air(Boolean unemployee_air) {
        this.unemployee_air = unemployee_air;
    }

    public Boolean getUnemployee_hotel() {
        return unemployee_hotel;
    }

    public void setUnemployee_hotel(Boolean unemployee_hotel) {
        this.unemployee_hotel = unemployee_hotel;
    }

    public Boolean getUnemployee_train() {
        return unemployee_train;
    }

    public void setUnemployee_train(Boolean unemployee_train) {
        this.unemployee_train = unemployee_train;
    }



    public int getAir_rule() {
        return air_rule;
    }

    public void setAir_rule(int air_rule) {
        this.air_rule = air_rule;
    }

    public int getHotel_rule() {
        return hotel_rule;
    }

    public void setHotel_rule(int hotel_rule) {
        this.hotel_rule = hotel_rule;
    }

    public int getTrain_rule() {
        return train_rule;
    }

    public void setTrain_rule(int train_rule) {
        this.train_rule = train_rule;
    }

    public String getRule_name() {
        return rule_name;
    }

    public void setRule_name(String rule_name) {
        this.rule_name = rule_name;
    }
}
