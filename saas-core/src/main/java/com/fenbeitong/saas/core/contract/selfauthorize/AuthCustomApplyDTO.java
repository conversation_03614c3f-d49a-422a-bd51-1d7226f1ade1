package com.fenbeitong.saas.core.contract.selfauthorize;

import lombok.Data;

import java.util.List;


@Data
public class AuthCustomApplyDTO {

    /**
     * 申请单id
     */
    private String apply_id;

    /**
     * 表单名称
     */
    private String form_name;

    /**
     * 申请单预估金额(以分计)
     */
    private Integer budget;

    /**
     * 创建时间
     */
    private String create_time;

    /**
     * 第一条申请项场景code
     */
    private Integer first_trip_scene_code;

    /**
     * 第一条申请项出发城市名称
     */
    private String first_trip_start_city_name;

    /**
     * 第一条申请项到达城市名称
     */
    private String first_trip_arrival_city_name;

    /**
     * 第一条申请项出发日期
     */
    private String first_trip_start_date;

}
