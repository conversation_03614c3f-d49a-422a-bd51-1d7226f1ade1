package com.fenbeitong.saas.core.contract.costcenter;

import java.util.List;

/**
 * Created by xuzn on 18/3/30.
 */
public class CostResultParamContract {
    private Integer state;
    private Integer pageSize;
    private Integer offset;
    private String companyId;
    private List<String> projectList;

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public List<String> getProjectList() {
        return projectList;
    }

    public void setProjectList(List<String> projectList) {
        this.projectList = projectList;
    }
}
