package com.fenbeitong.saas.core.contract.apply;

public class TripGuestInfoDTO {
    private String id_number;
    private TripGuestInfoContract.TypeBean id_type;
    private String name;
    private String phone;
    private String birth_date;
    private String desc;
    private boolean exist;
    private TripGuestInfoContract.TypeBean gender;
    private String id;
    private boolean is_employee;
    private String apply_id;


    public String getApply_id() {
        return apply_id;
    }

    public void setApply_id(String apply_id) {
        this.apply_id = apply_id;
    }



    public String getId_number() {
        return id_number;
    }

    public void setId_number(String id_number) {
        this.id_number = id_number;
    }

    public TripGuestInfoContract.TypeBean getId_type() {
        return id_type;
    }

    public void setId_type(TripGuestInfoContract.TypeBean id_type) {
        this.id_type = id_type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getBirth_date() {
        return birth_date;
    }

    public void setBirth_date(String birth_date) {
        this.birth_date = birth_date;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public boolean isExist() {
        return exist;
    }

    public void setExist(boolean exist) {
        this.exist = exist;
    }

    public TripGuestInfoContract.TypeBean getGender() {
        return gender;
    }

    public void setGender(TripGuestInfoContract.TypeBean gender) {
        this.gender = gender;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public boolean isIs_employee() {
        return is_employee;
    }

    public void setIs_employee(boolean is_employee) {
        this.is_employee = is_employee;
    }


}