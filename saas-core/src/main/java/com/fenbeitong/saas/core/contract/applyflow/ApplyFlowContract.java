package com.fenbeitong.saas.core.contract.applyflow;

import com.fenbeitong.saas.core.model.saas.ApplyFlowCopyTo;
import com.fenbeitong.saas.core.model.saas.ApplyFlowItem;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/11/21.
 */
public class ApplyFlowContract {
    private Integer index;
    private String companySettingType; //审批流类型
    private Integer applyType;
    private String flowName;    //审批流名称
    private List<ApplyFlowItem> fixationFlowList;
    private List<ApplyFlowCopyTo> ccList;
    private String usedByDepartments;
    private List<String> departmentIds;
    private String usedByProject;
    private List<String> projectIds;
    private String roleApproveType;//角色审批类型
    private String ccNoticeType;//抄送通知类型
    private List<String> errorMsgList;

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getCompanySettingType() {
        return companySettingType;
    }

    public void setCompanySettingType(String companySettingType) {
        this.companySettingType = companySettingType;
    }

    public String getFlowName() {
        return flowName;
    }

    public void setFlowName(String flowName) {
        this.flowName = flowName;
    }

    public List<ApplyFlowItem> getFixationFlowList() {
        return fixationFlowList;
    }

    public void setFixationFlowList(List<ApplyFlowItem> fixationFlowList) {
        this.fixationFlowList = fixationFlowList;
    }

    public List<ApplyFlowCopyTo> getCcList() {
        return ccList;
    }

    public void setCcList(List<ApplyFlowCopyTo> ccList) {
        this.ccList = ccList;
    }

    public String getUsedByDepartments() {
        return usedByDepartments;
    }

    public void setUsedByDepartments(String usedByDepartments) {
        this.usedByDepartments = usedByDepartments;
    }

    public String getUsedByProject() {
        return usedByProject;
    }

    public void setUsedByProject(String usedByProject) {
        this.usedByProject = usedByProject;
    }

    public String getRoleApproveType() {
        return roleApproveType;
    }

    public void setRoleApproveType(String roleApproveType) {
        this.roleApproveType = roleApproveType;
    }

    public String getCcNoticeType() {
        return ccNoticeType;
    }

    public void setCcNoticeType(String ccNoticeType) {
        this.ccNoticeType = ccNoticeType;
    }

    public List<String> getErrorMsgList() {
        return errorMsgList;
    }

    public void setErrorMsgList(List<String> errorMsgList) {
        this.errorMsgList = errorMsgList;
    }

    public Integer getApplyType() {
        return applyType;
    }

    public void setApplyType(Integer applyType) {
        this.applyType = applyType;
    }

    public List<String> getDepartmentIds() {
        return departmentIds;
    }

    public void setDepartmentIds(List<String> departmentIds) {
        this.departmentIds = departmentIds;
    }

    public List<String> getProjectIds() {
        return projectIds;
    }

    public void setProjectIds(List<String> projectIds) {
        this.projectIds = projectIds;
    }
}
