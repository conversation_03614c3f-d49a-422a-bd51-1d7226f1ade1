package com.fenbeitong.saas.core.contract.organization.inner;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/5/22.
 */
public class MallPolicyBean {
    /**
     * mall_priv_flag : true
     */

    private Boolean mall_priv_flag;
    private Boolean rule_limit_flag;
    private String rule_id;
    private Boolean exceed_buy_flag;
    private Boolean mall_verify_flag;

    public MallPolicyBean(Boolean mall_priv_flag, Boolean rule_limit_flag, String rule_id, Boolean exceed_buy_flag) {
        this.mall_priv_flag = mall_priv_flag;
        this.rule_limit_flag = rule_limit_flag;
        this.rule_id = rule_id;
        this.exceed_buy_flag = exceed_buy_flag;
    }

    public Boolean isExceed_buy_flag() {
        return exceed_buy_flag;
    }

    public void setExceed_buy_flag(Boolean exceed_buy_flag) {
        this.exceed_buy_flag = exceed_buy_flag;
    }

    public Boolean isMall_priv_flag() {
        return mall_priv_flag;
    }

    public void setMall_priv_flag(Boolean mall_priv_flag) {
        this.mall_priv_flag = mall_priv_flag;
    }

    public Boolean isRule_limit_flag() {
        return rule_limit_flag;
    }

    public void setRule_limit_flag(Boolean rule_limit_flag) {
        this.rule_limit_flag = rule_limit_flag;
    }

    public String getRule_id() {
        return rule_id;
    }

    public void setRule_id(String rule_id) {
        this.rule_id = rule_id;
    }

    public Boolean getMall_verify_flag() {
        return mall_verify_flag;
    }

    public void setMall_verify_flag(Boolean mall_verify_flag) {
        this.mall_verify_flag = mall_verify_flag;
    }

    public MallPolicyBean() {
    }

}
