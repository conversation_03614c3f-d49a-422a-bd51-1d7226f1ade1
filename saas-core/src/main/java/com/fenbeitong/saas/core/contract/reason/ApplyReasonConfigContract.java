package com.fenbeitong.saas.core.contract.reason;

import com.fenbeitong.saas.api.model.dto.reason.ReasonConfigContract;

/**
 * <AUTHOR>
 * @date 2019/12/10
 */
public class ApplyReasonConfigContract extends ReasonConfigContract {

    /**
     * config_type : 201
     * reason_config : {"reason":1,"reason_desc":1,"reason_items":[{"id":1,"name":"商务洽谈"},{"id":2,"name":"开发市场"},{"id":3,"name":"学习培训"},{"id":4,"name":"其他"}]}
     * apply_trip_config : {"apply_departure_date":0,"whether_trip_apply_budget":1,"apply_pedestrians_control":0,"whether_travel_statistics":1,"whether_required":1}
     */

    private ApplyTripConfig apply_trip_config;

    private BusinessOrderWriteOffConfig business_order_writeoff_config;

    /**
     *
     */
    private CostRelationConfig cost_relation_config;

    /**
     * 用车申请单配置
     */
    private ApplyTaxiConfig apply_taxi_config;
    /**
     * 用餐申请单配置
     */
    private ApplyMeishiConfig apply_meishi_config;
    /**
     * 外卖申请单配置
     */
    private ApplyTakeawayConfig apply_takeaway_config;
    /**
     * 付款申请单配置
     */
    private ApplyPaymentConfig apply_payment_config;

    /**
     * 是否是不可修改状态 0不是,1是
     */
    private Integer irrevocable_flag;

    public ApplyTripConfig getApply_trip_config() {
        return apply_trip_config;
    }

    public void setApply_trip_config(ApplyTripConfig apply_trip_config) {
        this.apply_trip_config = apply_trip_config;
    }

    public BusinessOrderWriteOffConfig getBusiness_order_writeoff_config() {
        return business_order_writeoff_config;
    }

    public void setBusiness_order_writeoff_config(BusinessOrderWriteOffConfig business_order_writeoff_config) {
        this.business_order_writeoff_config = business_order_writeoff_config;
    }

    public ApplyTaxiConfig getApply_taxi_config() {
        return apply_taxi_config;
    }

    public void setApply_taxi_config(ApplyTaxiConfig apply_taxi_config) {
        this.apply_taxi_config = apply_taxi_config;
    }

    public ApplyMeishiConfig getApply_meishi_config() {
        return apply_meishi_config;
    }

    public void setApply_meishi_config(ApplyMeishiConfig apply_meishi_config) {
        this.apply_meishi_config = apply_meishi_config;
    }

    public ApplyTakeawayConfig getApply_takeaway_config() {
        return apply_takeaway_config;
    }

    public void setApply_takeaway_config(ApplyTakeawayConfig apply_takeaway_config) {
        this.apply_takeaway_config = apply_takeaway_config;
    }

    public ApplyPaymentConfig getApply_payment_config() {
        return apply_payment_config;
    }

    public void setApply_payment_config(ApplyPaymentConfig apply_payment_config) {
        this.apply_payment_config = apply_payment_config;
    }

    public Integer getIrrevocable_flag() {
        return irrevocable_flag;
    }

    public void setIrrevocable_flag(Integer irrevocable_flag) {
        this.irrevocable_flag = irrevocable_flag;
    }


    public CostRelationConfig getCost_relation_config() {
        return cost_relation_config;
    }

    public void setCost_relation_config(CostRelationConfig cost_relation_config) {
        this.cost_relation_config = cost_relation_config;
    }
}
