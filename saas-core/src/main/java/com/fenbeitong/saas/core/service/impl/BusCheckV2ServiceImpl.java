package com.fenbeitong.saas.core.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.EventParams;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.saas.api.model.dto.apply.amount.AmountExceedCheckRes;
import com.fenbeitong.saas.core.common.DynamicDataSourceDecision;
import com.fenbeitong.saas.core.common.base.SaasException;
import com.fenbeitong.saas.core.common.constant.CommonSwitchConstant;
import com.fenbeitong.saas.core.common.constant.GlobalResponseCode;
import com.fenbeitong.saas.core.common.constant.SaasApplyConstant;
import com.fenbeitong.saas.core.common.constant.SaasContentConstant;
import com.fenbeitong.saas.core.common.constant.SaasOrderThirdRuleConstant;
import com.fenbeitong.saas.core.contract.apply.ApplyOrderContract;
import com.fenbeitong.saas.core.contract.apply.ApplyThirdContract;
import com.fenbeitong.saas.core.contract.apply.ApplyTripValidRequestContract;
import com.fenbeitong.saas.core.contract.apply.CheckApplyEstimatedAmountReq;
import com.fenbeitong.saas.core.contract.cost.CostCheckVO;
import com.fenbeitong.saas.core.contract.messagesettings.CostAttrAndBudgetConf;
import com.fenbeitong.saas.core.contract.order.check.*;
import com.fenbeitong.saas.core.dao.fenbeitong.BusRuleMapper;
import com.fenbeitong.saas.core.language.CoreLanguage;
import com.fenbeitong.saas.core.model.enums.EmployeeStatusEnum;
import com.fenbeitong.saas.core.model.enums.OrderCategory;
import com.fenbeitong.saas.core.model.enums.apply.ApplyType;
import com.fenbeitong.saas.core.model.enums.apply.TotalEstimatedLimitType;
import com.fenbeitong.saas.core.model.enums.message.BizType;
import com.fenbeitong.saas.core.model.enums.rule.*;
import com.fenbeitong.saas.core.model.fenbeitong.OrderCheckExt;
import com.fenbeitong.saas.core.model.fenbeitong.BusRule;
import com.fenbeitong.saas.core.model.saas.ApplyOrder;
import com.fenbeitong.saas.core.model.saas.ApplyRuleSetting;
import com.fenbeitong.saas.core.model.saas.MessageSetup;
import com.fenbeitong.saas.core.service.*;
import com.fenbeitong.saas.core.service.apply.TripEstimateService;
import com.fenbeitong.saas.core.service.apply.amount.ApplyAmountCheckResultService;
import com.fenbeitong.saas.core.utils.tools.BigDecimalTool;
import com.fenbeitong.saas.core.utils.tools.ConvertUtils;
import com.fenbeitong.saas.core.utils.tools.DateTimeTool;
import com.fenbeitong.saas.core.utils.tools.IDTool;
import com.fenbeitong.saas.core.utils.tools.VersionTool;
import com.fenbeitong.saasplus.api.model.dto.custform.CustomFormTotalEstimatedOptionDTO;
import com.fenbeitong.saasplus.api.model.dto.finance.CustomDimension;
import com.fenbeitong.saasplus.api.model.dto.custform.CustomFormApplyControlItemDTO;
import com.fenbeitong.saasplus.api.model.enums.custform.SwitchTypeEnum;
import com.fenbeitong.saasplus.api.service.custform.ICustomFormService;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyRuleDTO;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.fenbeitong.usercenter.api.model.dto.rule.EmployeeBusRuleDto;
import com.fenbeitong.usercenter.api.model.enums.privilege.CodeTypeEnums;
import com.fenbeitong.usercenter.api.model.enums.privilege.CustomCodeType;
import com.fenbeitong.usercenter.api.service.company.ICompanyRuleService;
import com.fenbeitong.usercenter.api.service.privilege.IPrivilegeService;
import com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeBusRuleService;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.StrUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.format.DateTimeFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 汽车订单规则校验实现
 *
 * <AUTHOR>
 * @date 2021/5/25
 */
@Slf4j
@Service
public class BusCheckV2ServiceImpl implements BusCheckV2Service {

    private static final Logger logger = LoggerFactory.getLogger(OrderCheckServiceImpl.class);

    @Autowired
    private IBaseOrganizationService baseOrganizationService;
    @Autowired
    private ICompanyRuleService iCompanyRuleService;
    @Autowired
    private IOrderCheckService iOrderCheckService;
    @Autowired
    private BusRuleMapper busRuleMapper;
//    @Autowired
//    private BusInterceptRecordMapper busInterceptRecordMapper;
    @Autowired
    private IBaseEmployeeBusRuleService iBaseEmployeeBusRuleService;
    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyRuleSettingExtMapper applyRuleSettingExtMapper;
    @Autowired
    private IMessageSetupService iMessageSetupService;
    @Autowired
    private IPrivilegeService iPrivilegeService;
    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyOrderMapper applyOrderMapper;
    @Autowired
    private ICustomFormService iCustomFormService;
    @Autowired
    private ApplyAmountCheckResultService applyAmountCheckResultService;
    @Autowired
    private com.fenbeitong.saasplus.api.service.messageSetup.IMessageSetupService iMessageSetupRpcService;
    @Autowired
    private ICurrencyCheckService currencyCheckService;
    @Autowired
    private ApplyV5ServiceImpl applyV5Service;

    @Autowired
    private TripEstimateService tripEstimateService;

    /**
     * 校验汽车订单规则
     *
     * @param reqContract
     * @return
     * @throws SaasException
     */
    @Override
    public TravelOnBusiOrderCheckResContract busOrderCheck(BusOrderCheckReqV2Contract reqContract, String clientVersion) throws SaasException {
        DynamicDataSourceDecision.determineDataSource(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        // 返回结果
        TravelOnBusiOrderCheckResContract resContract = new TravelOnBusiOrderCheckResContract();

        //申请单不是空 但是 tripid是空 需要兜底补齐tripid  和 业务申请单id
        if(StringUtils.isNotEmpty(reqContract.getTravel_on_busi_common_req_contract().getApply_id()) && StringUtils.isEmpty(reqContract.getTravel_on_busi_common_req_contract().getApply_trip_id())){
            //拼装酒店可用申请单请求数据
            ApplyTripValidRequestContract applyTripValidRequestContract = new ApplyTripValidRequestContract();
            applyTripValidRequestContract.setIsFilter(true);
            applyTripValidRequestContract.setCategory(BizType.BUS.getCode());
            applyTripValidRequestContract.setStart_time(reqContract.getTravel_on_busi_parameter_req_contract().getStart_time());
            applyTripValidRequestContract.setEnd_time(reqContract.getTravel_on_busi_parameter_req_contract().getEnd_time());
            applyTripValidRequestContract.setArrival_city_id(reqContract.getTravel_on_busi_parameter_req_contract().getArrival_city_id());
            applyTripValidRequestContract.setStart_city_id(reqContract.getTravel_on_busi_parameter_req_contract().getStart_city_id());
            applyTripValidRequestContract.setVoyage_type(1);
            applyTripValidRequestContract.setCompany_id(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
            applyTripValidRequestContract.setUser_id(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id());
            applyTripValidRequestContract.setSource(1);
            applyTripValidRequestContract.setTrip_type(BizType.BUS.getCode());
            List<ApplyOrderContract>  applyOrderList = applyV5Service.queryApplyList(applyTripValidRequestContract);
            List<ApplyOrderContract> applyList = applyOrderList.stream().filter(a->a.getUsable().equals(true) && a.getId().equals(reqContract.getTravel_on_busi_common_req_contract().getApply_id())).collect(Collectors.toList());
            log.info("可用申请单：{}",JsonUtils.toJson(applyList));
            if(ObjUtils.isNotEmpty(applyList)){
                resContract.setTrip_id(applyList.get(0).getApply_trip_id());
                resContract.setBusiness_apply_id(applyOrderList.get(0).getId());
                reqContract.getTravel_on_busi_common_req_contract().setApply_trip_id(applyOrderList.get(0).getApply_trip_id());
            }
        }
        // 校验权限问题
        TravelOnBusiOrderRuleCheckResult ruleCheckResult = busOrderRuleCheckResult(reqContract, clientVersion);
        resContract.setErr_code(ruleCheckResult.getErrCode());
        resContract.setErr_msg(ruleCheckResult.getErrMsg());
        resContract.setExceed_msg(ruleCheckResult.getExceed_msg());
        resContract.setCost_attribution_scope(ruleCheckResult.getCost_attribution_scope());
        resContract.setBudget_cost_attr_type(ruleCheckResult.getBudget_cost_attr_type());
        resContract.setPay_model(ruleCheckResult.getPayModel());
        resContract.setCompany_pay_price(ruleCheckResult.getCompanyPayPrice());
        resContract.setPersonal_pay_price(ruleCheckResult.getPersonalPayPrice());
        resContract.setReimbursable_price(ruleCheckResult.getReimbursablePrice());
        resContract.setUnreimbursable_price(ruleCheckResult.getUnreimbursablePrice());
        resContract.setAmount_compliance(ruleCheckResult.getAmountCompliance());
        resContract.setAmount_non_compliance(ruleCheckResult.getAmountNonCompliance());
        if (ruleCheckResult.getIs_exceed() || reqContract.getTravel_on_busi_parameter_req_contract().getExceed_submit()) {
            resContract.setIs_exceed(true);
        }
        resContract.setDuring_reapply_id(ruleCheckResult.getDuring_reapply_id());
        if (ruleCheckResult.getErrCode() == GlobalResponseCode.Success.getCode()) {
            BigDecimal costAmount = reqContract.getTravel_on_busi_common_req_contract().getOrder_price()
                    .add(ObjUtils.toBigDecimal(reqContract.getTravel_on_busi_common_req_contract().getInsurance_price(), BigDecimal.ZERO));
            try {
                Integer costInfoType = reqContract.getCost_info_type();
                if (costInfoType != null && costInfoType == 2) {
                    // 自定义字段转化
                    List<CustomDimension> customDimensionList =
                        ConvertUtils.convertToCustomDimension(reqContract.getCustomer_field_setting_list());

                    TempOrderCheckResContract costResult = iOrderCheckService.saveCostTicket(
                            reqContract.getTravel_on_busi_common_req_contract().getOrder_id(),
                            reqContract.getTravel_on_busi_common_req_contract().getCompany_id(),
                            reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(),
                            reqContract.getTravel_on_busi_common_req_contract().getApply_id(),
                            reqContract.getTravel_on_busi_common_req_contract().getApply_trip_id(),
                            BudgetCategoryTypeEnum.Bus,
                            costAmount,
                            reqContract.getCost_info_ticket_list(),
                            clientVersion, customDimensionList,"");
                    if (costResult.getErr_code() != GlobalResponseCode.Success.getCode()) {
                        resContract.setErr_code(costResult.getErr_code());
                        resContract.setErr_msg(costResult.getErr_msg());
                    } else {
                        resContract.setCost_id_ticket_list(costResult.getCost_id_ticket_list());
                    }
                } else {
                    // 自定义字段转化
                    List<CustomDimension> customDimensionList =
                        ConvertUtils.convertToCustomDimension(reqContract.getCustomer_field_setting_list());

                    TempOrderCheckResContract costResult = iOrderCheckService.saveCost(
                            reqContract.getTravel_on_busi_common_req_contract().getOrder_id(),
                            reqContract.getTravel_on_busi_common_req_contract().getCompany_id(),
                            reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(),
                            reqContract.getTravel_on_busi_common_req_contract().getApply_id(),
                            reqContract.getTravel_on_busi_common_req_contract().getApply_trip_id(),
                            BudgetCategoryTypeEnum.Bus,
                            costAmount,
                            reqContract.getCost_info(),
                            reqContract.getCostInfoString(),
                            clientVersion, customDimensionList,"");
                    if (costResult.getErr_code() != GlobalResponseCode.Success.getCode()) {
                        resContract.setErr_code(costResult.getErr_code());
                        resContract.setErr_msg(costResult.getErr_msg());
                    } else {
                        resContract.setCost_id(costResult.getCost_id());
                    }
                }
            } catch (FinhubException e) {
                if (e.getCode() == 20090) {
                    throw new SaasException(e.getCode(), e.getMessage(), FinhubMessageType.TIP_WINDOW);
                } else {
                    throw new SaasException(e.getCode(), e.getMessage(), FinhubMessageType.TIP_TOAST);
                }
            }
        }
//        //添加拦截记录
//        if (ruleCheckResult.getInterceptFlag() && GlobalResponseCode.Success.getCode() != ruleCheckResult.getErrCode()) {
//            initBusInterceptRecord(reqContract, ruleCheckResult);
//        }
        // 错误信息类型
        TravelOnBusiOrderCheckResContract travelOnBusiOrderCheckResContract = iOrderCheckService.travelOnBusiOrderCheckResContractCommon(resContract, ruleCheckResult);
        // 记录下单校验日志
        iOrderCheckService.saveOrderCheckLog(
                reqContract.getTravel_on_busi_common_req_contract().getCompany_id(),
                reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(),
                CategoryTypeEnum.BUS.getCode(),
                clientVersion,
                JsonUtils.toJson(reqContract),
                JsonUtils.toJson(travelOnBusiOrderCheckResContract),
                ObjUtils.toString(travelOnBusiOrderCheckResContract.getErr_code()),
                ruleCheckResult.getSnapshotInfo().toJSONString(),
                ruleCheckResult.getExtInfo().toJSONString());
        return travelOnBusiOrderCheckResContract;
    }

    /**
     * 校验汽车规则
     *
     * @param reqContract
     * @return
     */
    private TravelOnBusiOrderRuleCheckResult busOrderRuleCheckResult(BusOrderCheckReqV2Contract reqContract, String clientVersion) throws SaasException {
        TravelOnBusiOrderRuleCheckResult checkResult = new TravelOnBusiOrderRuleCheckResult();
        checkResult.setResCode(GlobalResponseCode.Success);
        BigDecimal totalPrice = BigDecimal.ZERO;
        //订单金额
        BigDecimal orderPrice = reqContract.getTravel_on_busi_common_req_contract().getOrder_price();
        //优惠券金额
        BigDecimal couponAmount = ObjUtils.toBigDecimal(reqContract.getTravel_on_busi_common_req_contract().getCoupon_amount(), BigDecimal.ZERO);
        //查询优惠券配置 0.未开启 1.开启
        Integer couponExceedPriceSetting = iOrderCheckService.queryCouponExceedPriceSetting(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        if (couponExceedPriceSetting == 1) {
            totalPrice = orderPrice.subtract(couponAmount).max(BigDecimal.ZERO);
            List<String> frequentIdList = reqContract.getTravel_on_busi_common_req_contract().getFrequent_id();
            if (couponAmount.compareTo(BigDecimal.valueOf(0)) == 1) {
                reqContract.getOrder_parameter_json().setTicket_price(reqContract.getOrder_parameter_json().getTicket_price().subtract(couponAmount.divide(BigDecimal.valueOf(frequentIdList.size()), 2, BigDecimal.ROUND_HALF_UP)).max(BigDecimal.ZERO));
            }
        } else {
            totalPrice = reqContract.getTravel_on_busi_common_req_contract().getTotal_price();
        }
        // 快照信息
        JSONObject snapshotInfo = new JSONObject();
        checkResult.setSnapshotInfo(snapshotInfo);
        // 查询是否个人垫付模式
        Map<String, Boolean> funcMap = iPrivilegeService.queryFunctionMoudle(CodeTypeEnums.CustomAuth.getKey(), reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(), CustomCodeType.M_ADVANCE_PAYMENT.getKey());
        Boolean advancePayment = funcMap.get(CustomCodeType.M_ADVANCE_PAYMENT.getKey());
        logger.info("[汽车票下单校验]，是否个人垫付模式:{}", advancePayment);
        PayModelEnum payModel = advancePayment ? PayModelEnum.PERSONAL_PREPAY : PayModelEnum.COMPANY_PAY;
        checkResult.setPayModel(payModel.getCode());
        snapshotInfo.put("payModel", payModel.getCode());
        if (advancePayment) {
            checkResult.setCompanyPayPrice(BigDecimal.ZERO);
            checkResult.setPersonalPayPrice(totalPrice);
            checkResult.setReimbursablePrice(totalPrice);
            checkResult.setUnreimbursablePrice(BigDecimal.ZERO);
        } else {
            checkResult.setCompanyPayPrice(totalPrice);
            checkResult.setPersonalPayPrice(BigDecimal.ZERO);
            checkResult.setReimbursablePrice(BigDecimal.ZERO);
            checkResult.setUnreimbursablePrice(BigDecimal.ZERO);
        }
        checkResult.setAmountCompliance(totalPrice);
        checkResult.setAmountNonCompliance(BigDecimal.ZERO);
        // 预订人状态
        EmployeeContract orderEmployee = baseOrganizationService.getEmployee(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(), reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        if (orderEmployee == null || orderEmployee.getStatus() != EmployeeStatusEnum.ACTIVE.getValue()) {
            checkResult.setResCode(GlobalResponseCode.OrderCheckEmployeeNotActive);
            return checkResult;
        }
        // 企业汽车权限
        CompanyRuleDTO companyRule = iCompanyRuleService.queryByCompanyId(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        if (companyRule == null || companyRule.getBusRule() != 1) {
            checkResult.setResCode(GlobalResponseCode.OrderCheckCompanyNoAuth.getCode(), CoreLanguage.Common_Exception_BusCompanyNoAuthMsg.getMessage());
            return checkResult;
        }
        // 员工汽车权限
        EmployeeBusRuleDto employeeBusRule = iBaseEmployeeBusRuleService.queryEmployeeBusRuleByPrimaryKey(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(), reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        checkResult.setEmployeeBusRule(employeeBusRule);
        snapshotInfo.put("authInfo", employeeBusRule);
        if (employeeBusRule == null
                || employeeBusRule.getBus_rule() != BusRuleType.AllowOther.getCode()) {
            checkResult.setResCode(GlobalResponseCode.OrderCheckEmployeeNoAuth.getCode(), CoreLanguage.Common_Exception_BusEmployeeNoAuthMsg.getMessage());
            return checkResult;
        }
        // 企业余额校验(余额不足或者账户被锁定)
        TravelOnBusiOrderRuleCheckResult checkCompanyAccountResult = iOrderCheckService.checkCompanyAccount(reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), reqContract.getTravel_on_busi_common_req_contract().getTotal_price(), OrderCategory.Bus.getKey(), advancePayment);
        if (GlobalResponseCode.Success.getCode() != checkCompanyAccountResult.getErrCode()) {
            FinhubLogger.event(reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), EventParams.build(**********, false).put("account_sub_type", 2));
            return checkCompanyAccountResult;
        }
        // 费用归属v2开关
        Integer costAttributionNewSwitch = iMessageSetupRpcService.queryCostAttributionNewSwitch(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        log.info("costAttributionNewSwitch:{}", costAttributionNewSwitch);
        if (CommonSwitchConstant.OPEN.equals(costAttributionNewSwitch)) {
            CostCheckVO costCheckVO = CostCheckVO.from(reqContract);
            currencyCheckService.newCheckCostAttribution(costCheckVO);
        }
        // 老版本费用归属校验
        else {
            CostAttrAndBudgetConf costAttrAndBudgetConf = iMessageSetupService.queryCostAttrAndBudgetConf(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());// 老版本配置"部门和项目"兼容为"部门或项目"
            checkResult.setCost_attribution_scope(costAttrAndBudgetConf.getCost_attribution_scope());
            checkResult.setBudget_cost_attr_type(costAttrAndBudgetConf.getBudget_cost_attr_type());
            Integer costInfoType = reqContract.getCost_info_type();
            if (costInfoType != null && costInfoType == 2) {
                TravelOnBusiOrderRuleCheckResult checkCompanyCostAttributionResult = iOrderCheckService.checkCostTicketInfo(reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), reqContract.getCost_info_ticket_list(), costAttrAndBudgetConf, clientVersion);
                if (GlobalResponseCode.Success.getCode() != checkCompanyCostAttributionResult.getErrCode()) {
                    return checkCompanyCostAttributionResult;
                }
            } else {
                TravelOnBusiOrderRuleCheckResult checkCompanyCostAttributionResult = iOrderCheckService.checkCostInfo(reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), reqContract.getCost_info(), costAttrAndBudgetConf, clientVersion);
                if (GlobalResponseCode.Success.getCode() != checkCompanyCostAttributionResult.getErrCode()) {
                    return checkCompanyCostAttributionResult;
                }
            }
            // 预算校验
            TravelOnBusiOrderRuleCheckResult travelOrderBudgetCheckResult = iOrderCheckService.categoryOrderBudgetCheckV2(reqContract, costAttrAndBudgetConf, OrderCategory.Bus, clientVersion);
            snapshotInfo.put("budgetList", travelOrderBudgetCheckResult.getBudgetSettingAndUseList());
            if (GlobalResponseCode.Success.getCode() != travelOrderBudgetCheckResult.getErrCode()) {
                return travelOrderBudgetCheckResult;
            }
            BigDecimal budgetAmountCompliance = travelOrderBudgetCheckResult.getAmountCompliance();
            BigDecimal budgetAmountNonCompliance = travelOrderBudgetCheckResult.getAmountNonCompliance();
            checkResult.setAmountCompliance(budgetAmountCompliance);
            checkResult.setAmountNonCompliance(budgetAmountNonCompliance);
            logger.info("[汽车票下单校验]，预算合规金额:{}, 预算超规金额:{}", budgetAmountCompliance, budgetAmountNonCompliance);
        }
        // 审批单查询校验
        TravelOnBusiOrderRuleCheckResult travelOnBusiOrderRuleCheckResult = iOrderCheckService.travelOnbusiOrderApplyIdCheckV2(reqContract, employeeBusRule.getBus_verify_flag(), TravelType.BUS.getCode());
        if (GlobalResponseCode.Success.getCode() != travelOnBusiOrderRuleCheckResult.getErrCode()) {
            return travelOnBusiOrderRuleCheckResult;
        }

        // 预估费用校验
        CheckApplyEstimatedAmountReq checkApplyEstimatedAmountReq = new CheckApplyEstimatedAmountReq();
        checkApplyEstimatedAmountReq.setApplyId(reqContract.getTravel_on_busi_common_req_contract().getApply_id());
        checkApplyEstimatedAmountReq.setTripId(reqContract.getTravel_on_busi_common_req_contract().getApply_trip_id());
        checkApplyEstimatedAmountReq.setCompanyId(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        checkApplyEstimatedAmountReq.setValidateAmount(totalPrice);
        checkApplyEstimatedAmountReq.setBizType(BizType.BUS.getCode());
        checkApplyEstimatedAmountReq.setStartDate(reqContract.getTravel_on_busi_parameter_req_contract().getStart_time());
        checkApplyEstimatedAmountReq.setEndDate(reqContract.getTravel_on_busi_parameter_req_contract().getStart_time());
        AmountExceedCheckRes amountExceedCheckRes = tripEstimateService.checkTripEstimatedAmount(checkApplyEstimatedAmountReq);

        if(GlobalResponseCode.Success.getCode() != amountExceedCheckRes.getErrCode()){
            TravelOnBusiOrderRuleCheckResult result = new TravelOnBusiOrderRuleCheckResult();
            result.setErrCode(GlobalResponseCode.ApplyTripEstimatedAmountIsError.getCode());
            String content = "";
            // 单日上限单独message
            if (Objects.equals(amountExceedCheckRes.getTotalEstimatedLimitIntValue(),TotalEstimatedLimitType.DAILY_LIMIT.getCode())) {
                result.setErrCode(GlobalResponseCode.CustomApplyTripEstimatedAmountDailyExceed.getCode());
                content = "您预订的行程实际费用超过申请单单日费用上限";
//                content = String.format("您预订的行程实际费用超过申请单单日费用上限\n共用一个行程，\n"
//                                + "行程单日费用上限金额：￥%s\n本次预订金额：￥%s",
//                        amountExceedCheckRes.getTotalEstimatedLimitAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString(),
//                        amountExceedCheckRes.getSettingAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            } else {
                content = "您预订的行程实际费用超过申请单费用上限";
//                content = String.format("您预订的行程实际费用超过申请单费用上限\n共用一个行程，\n"
//                                + "行程费用上限金额：￥%s\n本次预订金额：￥%s",
//                        amountExceedCheckRes.getTotalEstimatedLimitAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString(),
//                        amountExceedCheckRes.getSettingAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                if (amountExceedCheckRes.getUsedAmount() == null) {
                    result.setEstimatedAmount(amountExceedCheckRes.getTotalEstimatedLimitAmount());
                } else {
                    result.setEstimatedAmount(amountExceedCheckRes.getTotalEstimatedLimitAmount()
                        .subtract(amountExceedCheckRes.getUsedAmount()));
                }
                result.setRealPrice(amountExceedCheckRes.getSettingAmount());
            }
            result.setApplyTripId(reqContract.getTravel_on_busi_common_req_contract().getApply_trip_id());
            result.setErrMsg(content);
            return result;
        }

//        MessageSetup estimatedCheckSetup = iMessageSetupService.queryCompanyMessageSetupWithDefault(reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), SaasApplyConstant.ITEM_CODE_TRIP_APPLY_BUDGET_CHECK);
//
//
//        ApplyOrder applyOrder = null;
//        Integer totalEstimatedLimitIntValue = null;
//        BigDecimal totalEstimatedLimitAmount = null;
//        //需要行程审批
//        if (employeeBusRule.getBus_verify_flag()) {
//            //如果需要行程审批，并且申请单id 不是空 则需要校验申请单是否是 事前审批单的行程模式
//            if (ObjUtils.isNotBlank(reqContract.getTravel_on_busi_common_req_contract().getApply_id())){
//                applyOrder = applyOrderMapper.selectByPrimaryKey(reqContract.getTravel_on_busi_common_req_contract().getApply_id());
//                if (applyOrder.getType().equals(ApplyType.CustomFromBeforehand.getValue())){
//                    //获取自定义申请单配置
//                    //        "sceneCode": 7, // 同bizType的code
//                    //        "estimatedCostCheckSwitch": 1, // 下单时校验预估费用开关:1为开启，0为关闭
//                    //        "allOrderEstimatedCostCheck": 1, // 总订单预估费用校验：1为开启，0为关闭
//                    //        "multiTripApplyEstimatedCostCheck": 1, // 非行程申请校验：1为开启，0为关闭
//                    //        "personalPaySwitch": 1, // 个人支付：1为开启，0为关闭
//                    //        "travelerFillType": 1, // 出行人填写类型：0-选填，1-必填
//                    //        "orderCheckTravelerFlag": 1 //是否与订单出行人一致：0-未勾选，1-勾选
//                    CustomFormApplyControlItemDTO customFormApplyControlItemDTO = iCustomFormService.queryCustomFormApplyControlConfig(reqContract.getTravel_on_busi_common_req_contract().getCompany_id(),applyOrder.getFormId(),BizType.BUS.getCode(),applyOrder.getId());
//                    log.info("自定义申请单配置：{}", JsonUtils.toJson(customFormApplyControlItemDTO));
//                    if(ObjUtils.isNotEmpty(customFormApplyControlItemDTO)){
//                        estimatedCheckSetup.setStrVal1("135");
//                        estimatedCheckSetup.setIntVal1(customFormApplyControlItemDTO.getPersonalPaySwitch());
//                        estimatedCheckSetup.setIsChecked(customFormApplyControlItemDTO.getEstimatedCostCheckSwitch());
//                        logger.info("[自定义申请单审批设置-]:{}", JsonUtils.toJson(estimatedCheckSetup));
//                    }
//                }
//                // 自定义申请单配置获取
//                if (Objects.equals(applyOrder.getType(), ApplyType.CustomFromBeforehand.getValue())) {
//                    // 查询自定义申请单总预估费配置快照
//                    log.info("可用申请单判断查询自定义申请单快照信息rpc请求, id:{}， formId:{}", applyOrder.getId(), applyOrder.getFormId());
//                    CustomFormTotalEstimatedOptionDTO customFormSnapshot =
//                            iCustomFormService.queryTotalEstimatedOptionSnapshot(applyOrder.getId(), applyOrder.getFormId());
//                    log.info("可用申请单判断查询自定义申请单快照信息rpc结果, customFormSnapshot:{}", JsonUtils.toJson(customFormSnapshot));
//                    if (ObjUtils.isNotEmpty(customFormSnapshot)
//                            && Objects.equals(SwitchTypeEnum.OPEN.getSwitchCode(), customFormSnapshot.getCheckTotalEstimatedFlag())
//                            && customFormSnapshot.getTotalEstimatedCheckSceneWithCompatibilityMultiTrip().stream().anyMatch(a->Objects.equals(a,BizType.BUS.getCode()))) {
//                        estimatedCheckSetup.setIsChecked(SwitchTypeEnum.OPEN.getSwitchCode());
//                        totalEstimatedLimitIntValue = customFormSnapshot.getTotalEstimatedLimitType();
//                        if (Objects.equals(TotalEstimatedLimitType.DAILY_LIMIT.getCode(),
//                                customFormSnapshot.getTotalEstimatedLimitType())) {
//                            totalEstimatedLimitAmount = customFormSnapshot.getTotalEstimatedLimitAmount();
//                        } else {
//                            totalEstimatedLimitAmount = customFormSnapshot.getApplyBudgeInYuan();
//                        }
//                    }
//                }
//            }
//        }
//
//        // 预估费用校验
//        if (estimatedCheckSetup.getIsChecked() == 1 && estimatedCheckSetup.getStrVal1().contains("135")) {
//
//            // 预估费用校验
//            if (!Objects.isNull(totalEstimatedLimitIntValue) && !Objects.equals(totalEstimatedLimitIntValue,TotalEstimatedLimitType.UNKNOWN.getCode())) { // 自定义申请单总预估费用校验
//
//                TravelOnBusiOrderRuleCheckResult result = applyAmountCheckResultService.checkBusAmountResult(totalPrice, applyOrder.getId(),
//                                totalEstimatedLimitIntValue, totalEstimatedLimitAmount,
//                                reqContract.getTravel_on_busi_parameter_req_contract().getStart_time());
//                logger.info("applyAmountCheckResultService.checkBusAmountResult:{}", JsonUtils.toJson(result));
//                if (GlobalResponseCode.Success.getCode() != result.getErrCode()) {
//                    return result;
//                }
//            } else {
//                Date startTime = DateUtil.parse(reqContract.getTravel_on_busi_parameter_req_contract().getStart_time());
//                Date endTime = DateUtil.parse(reqContract.getTravel_on_busi_parameter_req_contract().getEnd_time());
//                String startCity = reqContract.getTravel_on_busi_parameter_req_contract().getStart_city_id();
//                String arrivalCity = reqContract.getTravel_on_busi_parameter_req_contract().getArrival_city_id();
//                TravelOnBusiOrderRuleCheckResult checkTripEstimatedAmountResult = iOrderCheckService.checkTripEstimatedAmount(reqContract.getTravel_on_busi_common_req_contract().getTotal_price(),
//                        reqContract.getTravel_on_busi_common_req_contract().getApply_id(), BizType.BUS, startTime, endTime, startCity, arrivalCity,
//                        reqContract.getTravel_on_busi_common_req_contract().getCompany_id(),
//                        reqContract.getTravel_on_busi_common_req_contract().getApply_trip_id(), clientVersion);
//                if (GlobalResponseCode.Success.getCode() != checkTripEstimatedAmountResult.getErrCode()) {
//                    return checkTripEstimatedAmountResult;
//                }
//            }
//        }

        /// 需要行程审批 后期接对接
//        if (employeeBusRule.getBus_verify_flag()) {
//            Integer approveType = iOrderCheckService.queryApproveType(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
//            //2:审批单中带有规则信息
//            if (approveType == 2) {
//                String applyId = reqContract.getTravel_on_busi_common_req_contract().getApply_id();
//                ApplyRuleSetting applyRuleSetting = applyRuleSettingExtMapper.queryApplyRuleByApplyOrderId(applyId);
//                if (applyRuleSetting != null && applyRuleSetting.getBusInfo()!=null) {
//                    List<ApplyThirdContract.KeyValueItem> busRuleList = JSONArray.parseArray(applyRuleSetting.getBusInfo(), ApplyThirdContract.KeyValueItem.class);
//                    if (CollectionUtils.isEmpty(busRuleList)) {
//                        return checkResult;
//                    }
//                    return checkBusThirdRule(busRuleList, checkResult, reqContract);
//                }
//            }
//        }
        // 订单审批开关
        Boolean busOrderVerifyFlag = employeeBusRule.getBus_order_verify_flag();
        if (advancePayment) {
            busOrderVerifyFlag = false;
        }
        logger.info("[校验汽车规则]，订单审批开关:{}", busOrderVerifyFlag);
        // 规则校验
        if (employeeBusRule.getBus_rule_flag()) {
            Integer ruleId = employeeBusRule.getBus_rule_id();
            if (ObjUtils.isBlank(ruleId)) {
                checkResult.setResCode(GlobalResponseCode.OrderCheckRuleNotExist);
                return checkResult;
            }
            BusRule busRule = busRuleMapper.selectByPrimaryKey(ruleId);
            checkResult.setBusRule(busRule);
            snapshotInfo.put("ruleInfo", busRule);
            if (busRule == null) {
                checkResult.setResCode(GlobalResponseCode.OrderCheckRuleNotExist);
                return checkResult;
            }
            // 超标规则（1：禁止 2：超规填写理由下单 3：超规需要提交费用审批）
            Integer exceedBuyType = employeeBusRule.getExceed_buy_type();
            // 汽车规则校验
            List<TravelOnBusiOrderRuleCheckResult> travelOnBusiOrderRuleCheckResults = this.checkBusExceedType(busRule, reqContract);
            boolean isPriceExceed = false;
            BigDecimal ruleAmountCompliance = totalPrice;
            if (CollectionUtils.isNotEmpty(travelOnBusiOrderRuleCheckResults) && travelOnBusiOrderRuleCheckResults.size() == 1) {
                if (travelOnBusiOrderRuleCheckResults.get(0).getIsPriceExceed() != null && travelOnBusiOrderRuleCheckResults.get(0).getIsPriceExceed()) {
                    isPriceExceed = true;
                    ruleAmountCompliance = travelOnBusiOrderRuleCheckResults.get(0).getAmountCompliance();
                    BigDecimal ruleAmountNonCompliance = travelOnBusiOrderRuleCheckResults.get(0).getAmountNonCompliance();
                    logger.info("[汽车下单校验]，规则合规金额:{}, 规则超规金额:{}", ruleAmountCompliance, ruleAmountNonCompliance);
                }
            }
            // 超规/合规金额处理
            if (CollectionUtils.isNotEmpty(travelOnBusiOrderRuleCheckResults)) {
                if (isPriceExceed) {
                    checkResult.setAmountCompliance(checkResult.getAmountCompliance().min(ruleAmountCompliance));
                    checkResult.setAmountNonCompliance(totalPrice.subtract(checkResult.getAmountCompliance()));
                } else {
                    checkResult.setAmountCompliance(BigDecimal.ZERO);
                    checkResult.setAmountNonCompliance(totalPrice);
                }
                logger.info("[汽车下单校验]，合规金额:{}, 超规金额:{}", checkResult.getAmountCompliance(), checkResult.getAmountNonCompliance());
            }
            //强制提交逻辑
            TravelOnBusiOrderRuleCheckResult checkExceedAuthResult = iOrderCheckService.checkExceedAuthV2(reqContract, CategoryTypeEnum.BUS);
            // 开启订单审批
            TravelOnBusiOrderCheckReqV2Contract.TravelOnBusiParameterReqContract travelOnBusiParameterReqContract = reqContract.getTravel_on_busi_parameter_req_contract();
            if (busOrderVerifyFlag) {
                String duringApplyId = iOrderCheckService.queryOrderApplyMsg(reqContract.getTravel_on_busi_parameter_req_contract(), reqContract.getTravel_on_busi_common_req_contract(), CategoryTypeEnum.BUS, true, null);
                // 超标禁止下单(超标需要理由)
                if ((TravelExceedType.NotAllowed.getCode() == exceedBuyType || TravelExceedType.AllowedNeedReason.getCode() == exceedBuyType) && CollectionUtils.isNotEmpty(travelOnBusiOrderRuleCheckResults)) {
                    iOrderCheckService.setErrMsgInfoNotAllowed(checkResult, travelOnBusiOrderRuleCheckResults);
                    return checkResult;
                } else if ((TravelExceedType.AllowedNeedApply.getCode() == exceedBuyType && travelOnBusiOrderRuleCheckResults.size() > 0 &&
                        GlobalResponseCode.Success.getCode() != travelOnBusiOrderRuleCheckResults.get(0).getErrCode()) &&
                        !iOrderCheckService.checkExceedApplyAuthV2(reqContract)) {
                    if (StringUtils.isNotBlank(duringApplyId) && VersionTool.greaterThanOrEqualTo(clientVersion, "4.9.5")) {
                        if (!travelOnBusiParameterReqContract.getExist_center_apply_submit()) {
                            logger.info("符合条件无需审批，弹窗提示");
                            checkResult.setErrCode(GlobalResponseCode.OrderCheckExceedApplytBusHint.getCode());
                            checkResult.setErrMsg(GlobalResponseCode.OrderCheckExceedApplytBusHint.getMsg());
                            checkResult.setIs_exceed(true);
                            return checkResult;
                        } else {
                            logger.info("符合条件无需审批，返回审批单id和超规标识");
                            checkResult.setDuring_reapply_id(duringApplyId);
                            checkResult.setIs_exceed(true);
                            return checkResult;
                        }
                    } else {
                        logger.info("汽车超规则，走订单审批");
                        iOrderCheckService.setErrMsgInfoForCheckApply(checkResult, travelOnBusiOrderRuleCheckResults);
                        checkResult.setErrCode(GlobalResponseCode.OrderCheckApplyExceedMore.getCode());
                        checkResult.setIs_exceed(true);
                        return checkResult;
                    }
                }
                if (StringUtils.isNotBlank(duringApplyId) && VersionTool.greaterThanOrEqualTo(clientVersion, "4.9.5")) {
                    if (!travelOnBusiParameterReqContract.getExist_center_apply_submit()) {
                        logger.info("符合条件无需审批，弹窗提示");
                        checkResult.setErrCode(GlobalResponseCode.OrderCheckExceedApplytBusHint.getCode());
                        checkResult.setErrMsg(GlobalResponseCode.OrderCheckExceedApplytBusHint.getMsg());
                        checkResult.setIs_exceed(true);
                        return checkResult;
                    } else {
                        logger.info("符合条件无需审批，返回审批单id和超规标识");
                        checkResult.setDuring_reapply_id(duringApplyId);
                        checkResult.setIs_exceed(true);
                        return checkResult;
                    }
                } else {
                    // 没有限制规则，但是开启了订单审批
                    iOrderCheckService.airOrderApplyCheckRes(checkResult, busOrderVerifyFlag, GlobalResponseCode.OrderCheckApplyExceedLess);
                }
            } else {
                // 未开启订单审批
                // 超标禁止下单
                if (TravelExceedType.NotAllowed.getCode() == exceedBuyType && CollectionUtils.isNotEmpty(travelOnBusiOrderRuleCheckResults)) {
                    iOrderCheckService.setErrMsgInfoNotAllowed(checkResult, travelOnBusiOrderRuleCheckResults);
                    return checkResult;
                }
                // 超标需要理由
                else if (TravelExceedType.AllowedNeedReason.getCode() == exceedBuyType &&
                        travelOnBusiOrderRuleCheckResults.size() > 0 && GlobalResponseCode.Success.getCode() != travelOnBusiOrderRuleCheckResults.get(0).getErrCode() &&
                        GlobalResponseCode.Success.getCode() != checkExceedAuthResult.getErrCode()) {
                    // 超规则返回
                    checkExceedAuthResult.setIs_exceed(true);
                    iOrderCheckService.setErrMsgInfoForReason(travelOnBusiOrderRuleCheckResults, checkExceedAuthResult);
                    return checkExceedAuthResult;
                } else if (!advancePayment && (TravelExceedType.AllowedNeedApply.getCode() == exceedBuyType && travelOnBusiOrderRuleCheckResults.size() > 0 && GlobalResponseCode.Success.getCode() != travelOnBusiOrderRuleCheckResults.get(0).getErrCode()) &&
                        !iOrderCheckService.checkExceedApplyAuthV2(reqContract)) {
                    logger.info("新版本汽车超规则，走订单审批");
                    String duringApplyId = iOrderCheckService.queryOrderApplyMsg(reqContract.getTravel_on_busi_parameter_req_contract(), reqContract.getTravel_on_busi_common_req_contract(), CategoryTypeEnum.BUS, true, null);
                    if (StringUtils.isNotBlank(duringApplyId) && VersionTool.greaterThanOrEqualTo(clientVersion, "4.9.5")) {
                        if (!travelOnBusiParameterReqContract.getExist_center_apply_submit()) {
                            logger.info("符合条件无需审批，弹窗提示");
                            checkResult.setErrCode(GlobalResponseCode.OrderCheckExceedApplytBusHint.getCode());
                            checkResult.setErrMsg(GlobalResponseCode.OrderCheckExceedApplytBusHint.getMsg());
                            checkResult.setIs_exceed(true);
                            return checkResult;
                        } else {
                            logger.info("符合条件无需审批，返回审批单id和超规标识");
                            checkResult.setDuring_reapply_id(duringApplyId);
                            checkResult.setIs_exceed(true);
                            return checkResult;
                        }
                    } else {
                        iOrderCheckService.setErrMsgInfoForCheckApply(checkResult, travelOnBusiOrderRuleCheckResults);
                        checkResult.setErrCode(GlobalResponseCode.OrderCheckApplyExceedMore.getCode());
                        checkResult.setIs_exceed(true);
                        return checkResult;
                    }
                }
            }
        } else {
            if (!advancePayment) {
                //没有限制规则，但是开启了订单审批
                TravelOnBusiOrderCheckReqV2Contract.TravelOnBusiParameterReqContract travelOnBusiParameterReqContract = reqContract.getTravel_on_busi_parameter_req_contract();
                String duringApplyId = iOrderCheckService.queryOrderApplyMsg(reqContract.getTravel_on_busi_parameter_req_contract(), reqContract.getTravel_on_busi_common_req_contract(), CategoryTypeEnum.BUS, false,null);
                if (StringUtils.isNotBlank(duringApplyId) && VersionTool.greaterThanOrEqualTo(clientVersion, "4.9.5")) {
                    if (!travelOnBusiParameterReqContract.getExist_center_apply_submit()) {
                        logger.info("符合条件无需审批，弹窗提示");
                        checkResult.setErrCode(GlobalResponseCode.OrderCheckApplytBusHint.getCode());
                        checkResult.setErrMsg(GlobalResponseCode.OrderCheckApplytBusHint.getMsg());
                        checkResult.setIs_exceed(false);
                        return checkResult;
                    } else {
                        logger.info("符合条件无需审批，返回审批单id和超规标识");
                        checkResult.setDuring_reapply_id(duringApplyId);
                        checkResult.setIs_exceed(false);
                        return checkResult;
                    }
                } else {
                    iOrderCheckService.airOrderApplyCheckRes(checkResult, busOrderVerifyFlag, GlobalResponseCode.OrderCheckApplyExceedLess);
                }
            }
        }
        return checkResult;
    }

//    /**
//     * 校验审批规则信息
//     *
//     * @param busRuleList
//     * @return
//     */
//    private TravelOnBusiOrderRuleCheckResult checkBusThirdRule(List<ApplyThirdContract.KeyValueItem> busRuleList, TravelOnBusiOrderRuleCheckResult checkResult, BusOrderCheckReqV2Contract reqContract) {
//        BusRule busRule = new BusRule();
//        for (ApplyThirdContract.KeyValueItem busInfo : busRuleList) {
//            busRule.setPriceLimitFlag(false);
//            String ruleType = busInfo.getType();
//            Object ruleValue = busInfo.getValue();
//            if (SaasOrderThirdRuleConstant.TRAIN_PRICE.equals(ruleType)) {
//                busRule.setPriceLimit(ObjUtils.toBigDecimal(ruleValue));
//            }
//            if (SaasOrderThirdRuleConstant.TRAIN_PRIV_DAY_MIN.equals(ruleType)) {
//                busRule.setPrevDayMin(ObjUtils.toInteger(ruleValue));
//            }
//            if (SaasOrderThirdRuleConstant.TRAIN_PRIV_DAY_MAX.equals(ruleType)) {
//                busRule.setPrevDayMax(ObjUtils.toInteger(ruleValue));
//            }
//        }
//        List<TravelOnBusiOrderRuleCheckResult> travelOnBusiOrderRuleCheckResults = checkBusExceedType(busRule, reqContract);
//        if (CollectionUtils.isNotEmpty(travelOnBusiOrderRuleCheckResults)) {
//            //增加返回字段
//            iOrderCheckService.setErrMsgInfoNotAllowed(checkResult, travelOnBusiOrderRuleCheckResults);
//            return checkResult;
//        }
//        return checkResult;
//    }

    /**
     * 处理汽车公用超标逻辑
     *
     * @param busRule
     * @param reqContract
     * @return
     */
    private List<TravelOnBusiOrderRuleCheckResult> checkBusExceedType(BusRule busRule, BusOrderCheckReqV2Contract reqContract) {
        List<TravelOnBusiOrderRuleCheckResult> travelOnBusiOrderRuleCheckResults = new ArrayList<>();
        if (busRule.getPriceLimitFlag()) {
            if (reqContract.getOrder_parameter_json().getTicket_price().compareTo(busRule.getPriceLimit()) > 0) {
                TravelOnBusiOrderRuleCheckResult checkResults = new TravelOnBusiOrderRuleCheckResult();
                String msg = StrUtils.formatString("单张票价格需低于¥{0}", BigDecimalTool.formatMoney(busRule.getPriceLimit()));
                checkResults.setErrCode(GlobalResponseCode.OrderCheckTrainRuleBelowPriceLimitMsg.getCode());
                checkResults.setErrMsg(msg);
                checkResults.setExceed_msg(msg);
                checkResults.setType(2);
                if(ObjUtils.isNotEmpty(reqContract.getTravel_on_busi_common_req_contract().getTotal_price())){
                    checkResults.setIsPriceExceed(true);
                    checkResults.setAmountCompliance(busRule.getPriceLimit().multiply(new BigDecimal(reqContract.getPassengers().size())));
                    checkResults.setAmountNonCompliance(reqContract.getTravel_on_busi_common_req_contract().getTotal_price().subtract(checkResults.getAmountCompliance()));
                }
                travelOnBusiOrderRuleCheckResults.add(checkResults);
            }
        }
        String start_time = reqContract.getTravel_on_busi_parameter_req_contract().getStart_time();
        LocalDate startTime = LocalDateTime.ofInstant(DateTimeTool.fromStringToDate(start_time, DateTimeFormat.forPattern("yyyy-MM-dd HH:mm")).toInstant(), ZoneId.systemDefault()).toLocalDate();
        LocalDate currentTime = LocalDate.now();
        // 下单时间与预定时间相差天数
        long daysDiff = ChronoUnit.DAYS.between(currentTime, startTime);
        Integer prevDayMin = busRule.getPrevDayMin();
        if (prevDayMin != null && daysDiff < prevDayMin) {
            logger.info(StrUtils.formatString("申请人违反了「最少提前{0}天预订」的限制", prevDayMin));
            TravelOnBusiOrderRuleCheckResult checkResults = new TravelOnBusiOrderRuleCheckResult();
            String msg = StrUtils.formatString(CoreLanguage.TrainCheckServiceImpl_Value_MinDay.getMessage(), prevDayMin);
            checkResults.setResCode(GlobalResponseCode.OrderCheckLeastDay.getCode(), msg);
            checkResults.setExceed_msg(msg);
            checkResults.setType(3);
            travelOnBusiOrderRuleCheckResults.add(checkResults);
        }
        Integer prevDayMax = busRule.getPrevDayMax();
        if (prevDayMax != null && daysDiff > prevDayMax) {
            logger.info(StrUtils.formatString("申请人违反了「最多提前{0}天预订」的限制", prevDayMax));
            TravelOnBusiOrderRuleCheckResult checkResults = new TravelOnBusiOrderRuleCheckResult();
            String msg = StrUtils.formatString(CoreLanguage.TrainCheckServiceImpl_Value_MaxDay.getMessage(), prevDayMax);
            checkResults.setResCode(GlobalResponseCode.OrderCheckMostDay.getCode(), msg);
            checkResults.setExceed_msg(msg);
            checkResults.setType(3);
            travelOnBusiOrderRuleCheckResults.add(checkResults);
        }
        return travelOnBusiOrderRuleCheckResults;
    }

    /**
     * 添加汽车拦截记录信息
     */
//    private void initBusInterceptRecord(BusOrderCheckReqV2Contract reqContract, TravelOnBusiOrderRuleCheckResult ruleCheckResult) {
//        BusInterceptRecord busInterceptRecord = new BusInterceptRecord();
//        //拦截参数
//        try {
//            //处理规则信息
//            EmployeeBusRule employeeBusRule = iBaseEmployeeBusRuleService.selectByPrimaryKey(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(), reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
//            BusRule busRule = null;
//            if (employeeBusRule != null) {
//                String ruleId = ObjUtils.ifNull(employeeBusRule.getManual_bus_rule_id(), employeeBusRule.getDefault_bus_rule_id());
//                busRule = busRuleMapper.selectByPrimaryKey(ruleId);
//            }
//            //处理拦截信息
//            BusInterceptRecordContract busInterceptRecordContract = reqContract.getOrder_parameter_json();
//            busInterceptRecord.setId(IDTool.CreateUniqueID());
//            busInterceptRecord.setEmployeeId(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id());
//            busInterceptRecord.setCompanyId(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
//            busInterceptRecord.setCreateTime(new Date());
//            busInterceptRecord.setContactName(reqContract.getTravel_on_busi_parameter_req_contract().getContact_name());
//            busInterceptRecord.setContactPhone(reqContract.getTravel_on_busi_parameter_req_contract().getContact_phone());
//            busInterceptRecord.setChannel(reqContract.getTravel_on_busi_common_req_contract().getChannel());
//            busInterceptRecord.setTotalPrice(reqContract.getTravel_on_busi_common_req_contract().getTotal_price());
//            busInterceptRecord.setBusRule(employeeBusRule == null ? -1 : employeeBusRule.getBus_rule());
//            busInterceptRecord.setBusRuleFlag(employeeBusRule == null ? false : employeeBusRule.getBus_rule_flag());
//            busInterceptRecord.setBusVerifyFlag(employeeBusRule == null ? false : employeeBusRule.getBus_verify_flag());
//            busInterceptRecord.setExceedBuyFlag(reqContract.getTravel_on_busi_parameter_req_contract().getExceed_submit());
//            busInterceptRecord.setExceedBuyType(employeeBusRule == null ? -1 : employeeBusRule.getExceed_buy_type());
//            busInterceptRecord.setBusSeatFlag(busRule == null ? false : busRule.getBusSeatFlag());
//            busInterceptRecord.setCommonBusSeatType(busRule == null ? "" : busRule.getCommonBusSeatType());
//            busInterceptRecord.setHighspeedBusSeatType(busRule == null ? "" : busRule.getHighspeedBusSeatType());
//            busInterceptRecord.setBusCode(busInterceptRecordContract.getBus_code());
//            busInterceptRecord.setBusNo(busInterceptRecordContract.getBus_no());
//            busInterceptRecord.setFromStationCode(busInterceptRecordContract.getFrom_station_code());
//            busInterceptRecord.setFromStationName(busInterceptRecordContract.getFrom_station_name());
//            busInterceptRecord.setToStationCode(busInterceptRecordContract.getTo_station_code());
//            busInterceptRecord.setToStationName(busInterceptRecordContract.getTo_station_name());
//            busInterceptRecord.setBusStartDate(busInterceptRecordContract.getBus_start_date());
//            busInterceptRecord.setBusEndDate(busInterceptRecordContract.getBus_end_date());
//            busInterceptRecord.setStartTime(busInterceptRecordContract.getStart_time());
//            busInterceptRecord.setArriveTime(busInterceptRecordContract.getArrive_time());
//            busInterceptRecord.setRunTime(busInterceptRecordContract.getRun_time());
//            busInterceptRecord.setArriveDays(busInterceptRecordContract.getArrive_days());
//            busInterceptRecord.setSeatType(busInterceptRecordContract.getSeat_type());
//            busInterceptRecord.setSeatNo(busInterceptRecordContract.getSeat_no());
//            busInterceptRecord.setSeatPrice(busInterceptRecordContract.getSeat_price());
//            busInterceptRecord.setServiceFee(busInterceptRecordContract.getService_fee());
//            //需要处理订票人信息
//            List<OrderCheckExt> passengerList = iOrderCheckService.getPassengerList(reqContract.getPassengers());
//            String passengerInfoList = JsonUtils.toJson(passengerList);
//            busInterceptRecord.setPassengerInfoList(passengerInfoList);
//            busInterceptRecord.setErrCode(ruleCheckResult.getErrCode());
//            String errorMsg = ruleCheckResult.getErrMsg() + (StringUtils.isEmpty(ruleCheckResult.getExceed_msg()) ? "" : "|" + ruleCheckResult.getExceed_msg());
//            busInterceptRecord.setErrMsg(errorMsg);
//            //busInterceptRecord.setCostCenterId(reqContract.getTravel_on_busi_common_req_contract().getAttribution_id());
//            //busInterceptRecord.setCostCenterType(reqContract.getTravel_on_busi_common_req_contract().getAttribution_category());
//            busInterceptRecordMapper.insertSelective(busInterceptRecord);
//        } catch (Exception e) {
//            logger.error("添加汽车拦截记录信息:{},发生异常:{}", JsonUtils.toJson(busInterceptRecord), e.getLocalizedMessage());
//        }
//    }

    
}
