package com.fenbeitong.saas.core.contract.selfauthorize;

import lombok.Data;

import java.util.List;

@Data
public class Auth2MeItemDTO {

    private String self_authorization_id;

    private String title;

    /**
     * 授权场景代码
     */
    private List<AuthSceneItem> auth_scene_item_list;

    /**
     * 授权开始时间
     */
    private String auth_start_time;

    /**
     * 授权结束时间
     */
    private String auth_end_time;

    /**
     * 授权状态
     */
    private Integer auth_state;

    /**
     * 是否永久：0为非永久，1为永久
     */
    private Integer is_forever;

    private String auth_employee_id;

    /**
     * 授权人姓名
     */
    private String auth_employee_name;

    /**
     * 授权人
     */
    private String auth_employee_org_name;

    private Integer auth_permission_type;

    private String apply_id;

    private Long past_date;

    private String form_name;

}
