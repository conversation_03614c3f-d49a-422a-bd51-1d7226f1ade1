package com.fenbeitong.saas.core.contract.messagesettings.inner;

import com.fenbeitong.saas.core.contract.user.EmployeeInfoContract;

import java.util.ArrayList;
import java.util.List;

public class CouponSendFail {

    private List<EmployeeInfoContract> receiver_list = new ArrayList<>();
    private List<String> email_list = new ArrayList<>();

    public CouponSendFail() {
    }

    public CouponSendFail(List<EmployeeInfoContract> receiver_list, List<String> email_list) {
        this.receiver_list = receiver_list;
        this.email_list = email_list;
    }

    public List<EmployeeInfoContract> getReceiver_list() {
        return receiver_list;
    }

    public void setReceiver_list(List<EmployeeInfoContract> receiver_list) {
        this.receiver_list = receiver_list;
    }

    public List<String> getEmail_list() {
        return email_list;
    }

    public void setEmail_list(List<String> email_list) {
        this.email_list = email_list;
    }

    public static class BalanceRemindNotice {
        private Integer is_check;
        private Integer amount;

        public BalanceRemindNotice() {
        }

        public BalanceRemindNotice(Integer is_check, Integer amount) {
            this.is_check = is_check;
            this.amount = amount;
        }

        public Integer getIs_check() {
            return is_check;
        }

        public void setIs_check(Integer is_check) {
            this.is_check = is_check;
        }

        public Integer getAmount() {
            return amount;
        }

        public void setAmount(Integer amount) {
            this.amount = amount;
        }
    }

}
