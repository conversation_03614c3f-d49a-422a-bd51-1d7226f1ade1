package com.fenbeitong.saas.core.contract.messagesettings.inner;

import com.fenbeitong.saas.core.common.constant.SaasMessageConstant;
import com.fenbeitong.saas.core.model.saas.MessageSetup;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 虚拟卡交易记录核销提醒
 */
public class VirtualCardWriteOffRemind {
    private Integer is_check;
    private Integer app_notice;
    private Integer mail_notice;
    private Integer phone_notice;
    private String time;
    private WriteOffCommonRemind write_off_common_remind;
    private ExceedAmountRemind exceed_amount_remind;
    private ExceedDaysRemind exceed_days_remind;
    private Integer skip_holiday;

    /**
     * 常规提醒：虚拟卡消费完成后，每隔 day日就提醒用户进行核销
     */
    @Data
    @NoArgsConstructor
    public static class WriteOffCommonRemind {
        /**
         * 是否设置了预警
         */
        private Integer is_check;
        /**
         * 未核销记录总额超过此限额提醒用户
         */
        private String day;
    }
    /**
     * 超额提醒：未核销交易额达到此额度会提醒用户
     */
    @Data
    @NoArgsConstructor
    public static class ExceedAmountRemind {
        /**
         * 是否设置了超额预警
         */
        private Integer is_check;
        /**
         * 未核销记录总额超过此限额提醒用户
         */
        private String amountLimit;
    }

    /**
     * 超期预警：交易记录超过一定天数未核销会提醒用户
     */
    @Data
    public static class ExceedDaysRemind {
        /**
         * 是否设置了超期预警
         */
        private Integer is_check;
        /**
         * 超过此天数仍然未核销提醒用户
         */
        private String days_limit;

        public ExceedDaysRemind() {
            this.is_check = 0;
        }
    }


    public VirtualCardWriteOffRemind(){}

    public VirtualCardWriteOffRemind(Integer app_notice,Integer mail_notice,Integer phone_notice, String time){
        this.app_notice=app_notice;
        this.mail_notice=mail_notice;
        this.phone_notice=phone_notice;
        this.time=time;
    }
    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public Integer getIs_check() {
        return is_check;
    }

    public void setIs_check(Integer is_check) {
        this.is_check = is_check;
    }

    public Integer getApp_notice() {
        return app_notice;
    }

    public void setApp_notice(Integer app_notice) {
        this.app_notice = app_notice;
    }

    public Integer getMail_notice() {
        return mail_notice;
    }

    public void setMail_notice(Integer mail_notice) {
        this.mail_notice = mail_notice;
    }

    public Integer getPhone_notice() {
        return phone_notice;
    }

    public void setPhone_notice(Integer phone_notice) {
        this.phone_notice = phone_notice;
    }

    public ExceedAmountRemind getExceed_amount_remind() {
        return exceed_amount_remind;
    }

    public void setExceed_amount_remind(ExceedAmountRemind exceed_amount_remind) {
        this.exceed_amount_remind = exceed_amount_remind;
    }

    public ExceedDaysRemind getExceed_days_remind() {
        return exceed_days_remind;
    }

    public void setExceed_days_remind(ExceedDaysRemind exceed_days_remind) {
        this.exceed_days_remind = exceed_days_remind;
    }

    public WriteOffCommonRemind getWrite_off_common_remind() {
        return write_off_common_remind;
    }

    public void setWrite_off_common_remind(WriteOffCommonRemind write_off_common_remind) {
        this.write_off_common_remind = write_off_common_remind;
    }

    public Integer getSkip_holiday() {
        return skip_holiday;
    }

    public void setSkip_holiday(Integer skip_holiday) {
        this.skip_holiday = skip_holiday;
    }

    public void build(MessageSetup setup) {
        this.setIs_check(1);
        this.setApp_notice(setup.getIntVal1());
        this.setMail_notice(setup.getIntVal2());
        this.setPhone_notice(setup.getIntVal3());
        this.setTime(setup.getStrVal3());
        if (org.apache.commons.lang3.StringUtils.isNumeric(setup.getStrVal2())) {
            this.setSkip_holiday(Integer.valueOf(setup.getStrVal2()));
        } else {
            this.setSkip_holiday(0);
        }
        // 设置常规提醒(同原有的配置）
        if (SaasMessageConstant.VIRTUAL_CARD_WRITE_OFF_REMIND.equals(setup.getItemCode())) {
            VirtualCardWriteOffRemind.WriteOffCommonRemind writeOffCommonRemind = new VirtualCardWriteOffRemind.WriteOffCommonRemind();
            writeOffCommonRemind.setIs_check(setup.getIsChecked() == null? 0: setup.getIsChecked());
            writeOffCommonRemind.setDay(setup.getStrVal1());
            this.write_off_common_remind = writeOffCommonRemind;
   //     } else if (SaasMessageConstant.VIRTUAL_CARD_WRITE_OFF_REMIND_EXCEED_AMOUNT.equals(setup.getItemCode())) { // 备用金核销超额预警
   //       VirtualCardWriteOffRemind.ExceedAmountRemind exceedAmountRemind = new VirtualCardWriteOffRemind.ExceedAmountRemind();
   //         exceedAmountRemind.setIs_check(setup.getIsChecked());
   //       exceedAmountRemind.setAmountLimit(setup.getStrVal2());
   //      this.exceed_amount_remind = exceedAmountRemind;
        } else if (SaasMessageConstant.VIRTUAL_CARD_WRITE_OFF_REMIND_EXCEED_DAYS.equals(setup.getItemCode())) { // 备用金核销超期预警
            VirtualCardWriteOffRemind.ExceedDaysRemind exceedDaysRemind = new VirtualCardWriteOffRemind.ExceedDaysRemind();
            exceedDaysRemind.setIs_check(setup.getIsChecked() == null? 0: setup.getIsChecked());
            exceedDaysRemind.setDays_limit(setup.getStrVal1());
            this.exceed_days_remind = exceedDaysRemind;
        }
    }
}
