package com.fenbeitong.saas.core.contract.assistant;

import com.github.pagehelper.PageInfo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> 2022/4/12
 */
@Data
public class BasePageContract<T> {

    /**
     * 当前页码
     */
    private Integer pageNo;

    /**
     * 当前页结果数量
     */
    private Integer pageSize;

    /**
     * 全部结果集数量
     */
    private Long total;

    /**
     * 当前页数据
     */
    private List<T> page;

    public static <T>BasePageContract<T> fromPageInfo(PageInfo<?> pageInfo, List<T> list) {
        BasePageContract<T> pageContract = new BasePageContract<>();
        pageContract.setPageNo(pageInfo.getPageNum());
        pageContract.setPageSize(pageInfo.getPageSize());
        pageContract.setTotal(pageInfo.getTotal());
        pageContract.setPage(list);
        return pageContract;
    }

}
