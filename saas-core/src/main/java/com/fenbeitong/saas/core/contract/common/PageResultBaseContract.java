package com.fenbeitong.saas.core.contract.common;

import com.fenbeitong.saas.core.contract.apply.ApplyMatchReasonDTO;
import lombok.Data;

import java.util.List;

/**
 * Created by xiabin on 2017/3/1.
 */
@Data
public class PageResultBaseContract<TData> {
    private int page;
    private int p_size;
    private int count;
    private List<ApplyMatchReasonDTO> disableApplyList;

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getP_size() {
        return p_size;
    }

    public void setP_size(int p_size) {
        this.p_size = p_size;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public TData getData() {
        return data;
    }

    public void setData(TData data) {
        this.data = data;
    }

    private TData data;
}
