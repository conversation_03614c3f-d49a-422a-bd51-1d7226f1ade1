package com.fenbeitong.saas.core.contract.messagesettings.inner;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/12/14.
 * 通用通知模版
 */
public class CommonNoticeContract {
    private List<QandA> commonJson;
    private String title;

    public class QandA{

        public QandA(String answer, Tip tip) {
            this.answer = answer;
            this.tip = tip;
        }

        public QandA(String ask, String answer, Tip tip) {
            this.ask = ask;
            this.answer = answer;
            this.tip = tip;
        }

        private String ask;
        private String answer;
        private Tip tip;

        public Tip getTip() {
            return tip;
        }

        public void setTip(Tip tip) {
            this.tip = tip;
        }

        public String getAsk() {
            return ask;
        }

        public void setAsk(String ask) {
            this.ask = ask;
        }

        public String getAnswer() {
            return answer;
        }

        public void setAnswer(String answer) {
            this.answer = answer;
        }
    }

    public class Tip{

        public Tip(String icon, String content) {
            this.icon = icon;
            this.content = content;
        }

        private String icon;
        private String content;

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }
    }

    public List<QandA> getCommonJson() {
        return commonJson;
    }

    public void setCommonJson(List<QandA> commonJson) {
        this.commonJson = commonJson;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}


