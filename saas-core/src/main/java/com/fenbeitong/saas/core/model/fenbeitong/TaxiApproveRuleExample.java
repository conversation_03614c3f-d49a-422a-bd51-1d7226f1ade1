package com.fenbeitong.saas.core.model.fenbeitong;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TaxiApproveRuleExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table taxi_approve_rule
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table taxi_approve_rule
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table taxi_approve_rule
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table taxi_approve_rule
     *
     * @mbg.generated
     */
    public TaxiApproveRuleExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table taxi_approve_rule
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table taxi_approve_rule
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table taxi_approve_rule
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table taxi_approve_rule
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table taxi_approve_rule
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table taxi_approve_rule
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table taxi_approve_rule
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table taxi_approve_rule
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table taxi_approve_rule
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table taxi_approve_rule
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table taxi_approve_rule
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(String value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(String value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(String value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(String value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(String value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(String value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLike(String value) {
            addCriterion("company_id like", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotLike(String value) {
            addCriterion("company_id not like", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<String> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<String> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(String value1, String value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(String value1, String value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andAllowedTaxiTypeIsNull() {
            addCriterion("allowed_taxi_type is null");
            return (Criteria) this;
        }

        public Criteria andAllowedTaxiTypeIsNotNull() {
            addCriterion("allowed_taxi_type is not null");
            return (Criteria) this;
        }

        public Criteria andAllowedTaxiTypeEqualTo(String value) {
            addCriterion("allowed_taxi_type =", value, "allowedTaxiType");
            return (Criteria) this;
        }

        public Criteria andAllowedTaxiTypeNotEqualTo(String value) {
            addCriterion("allowed_taxi_type <>", value, "allowedTaxiType");
            return (Criteria) this;
        }

        public Criteria andAllowedTaxiTypeGreaterThan(String value) {
            addCriterion("allowed_taxi_type >", value, "allowedTaxiType");
            return (Criteria) this;
        }

        public Criteria andAllowedTaxiTypeGreaterThanOrEqualTo(String value) {
            addCriterion("allowed_taxi_type >=", value, "allowedTaxiType");
            return (Criteria) this;
        }

        public Criteria andAllowedTaxiTypeLessThan(String value) {
            addCriterion("allowed_taxi_type <", value, "allowedTaxiType");
            return (Criteria) this;
        }

        public Criteria andAllowedTaxiTypeLessThanOrEqualTo(String value) {
            addCriterion("allowed_taxi_type <=", value, "allowedTaxiType");
            return (Criteria) this;
        }

        public Criteria andAllowedTaxiTypeLike(String value) {
            addCriterion("allowed_taxi_type like", value, "allowedTaxiType");
            return (Criteria) this;
        }

        public Criteria andAllowedTaxiTypeNotLike(String value) {
            addCriterion("allowed_taxi_type not like", value, "allowedTaxiType");
            return (Criteria) this;
        }

        public Criteria andAllowedTaxiTypeIn(List<String> values) {
            addCriterion("allowed_taxi_type in", values, "allowedTaxiType");
            return (Criteria) this;
        }

        public Criteria andAllowedTaxiTypeNotIn(List<String> values) {
            addCriterion("allowed_taxi_type not in", values, "allowedTaxiType");
            return (Criteria) this;
        }

        public Criteria andAllowedTaxiTypeBetween(String value1, String value2) {
            addCriterion("allowed_taxi_type between", value1, value2, "allowedTaxiType");
            return (Criteria) this;
        }

        public Criteria andAllowedTaxiTypeNotBetween(String value1, String value2) {
            addCriterion("allowed_taxi_type not between", value1, value2, "allowedTaxiType");
            return (Criteria) this;
        }

        public Criteria andPriceLimitFlagIsNull() {
            addCriterion("price_limit_flag is null");
            return (Criteria) this;
        }

        public Criteria andPriceLimitFlagIsNotNull() {
            addCriterion("price_limit_flag is not null");
            return (Criteria) this;
        }

        public Criteria andPriceLimitFlagEqualTo(Integer value) {
            addCriterion("price_limit_flag =", value, "priceLimitFlag");
            return (Criteria) this;
        }

        public Criteria andPriceLimitFlagNotEqualTo(Integer value) {
            addCriterion("price_limit_flag <>", value, "priceLimitFlag");
            return (Criteria) this;
        }

        public Criteria andPriceLimitFlagGreaterThan(Integer value) {
            addCriterion("price_limit_flag >", value, "priceLimitFlag");
            return (Criteria) this;
        }

        public Criteria andPriceLimitFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("price_limit_flag >=", value, "priceLimitFlag");
            return (Criteria) this;
        }

        public Criteria andPriceLimitFlagLessThan(Integer value) {
            addCriterion("price_limit_flag <", value, "priceLimitFlag");
            return (Criteria) this;
        }

        public Criteria andPriceLimitFlagLessThanOrEqualTo(Integer value) {
            addCriterion("price_limit_flag <=", value, "priceLimitFlag");
            return (Criteria) this;
        }

        public Criteria andPriceLimitFlagIn(List<Integer> values) {
            addCriterion("price_limit_flag in", values, "priceLimitFlag");
            return (Criteria) this;
        }

        public Criteria andPriceLimitFlagNotIn(List<Integer> values) {
            addCriterion("price_limit_flag not in", values, "priceLimitFlag");
            return (Criteria) this;
        }

        public Criteria andPriceLimitFlagBetween(Integer value1, Integer value2) {
            addCriterion("price_limit_flag between", value1, value2, "priceLimitFlag");
            return (Criteria) this;
        }

        public Criteria andPriceLimitFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("price_limit_flag not between", value1, value2, "priceLimitFlag");
            return (Criteria) this;
        }

        public Criteria andPriceLimitIsNull() {
            addCriterion("price_limit is null");
            return (Criteria) this;
        }

        public Criteria andPriceLimitIsNotNull() {
            addCriterion("price_limit is not null");
            return (Criteria) this;
        }

        public Criteria andPriceLimitEqualTo(BigDecimal value) {
            addCriterion("price_limit =", value, "priceLimit");
            return (Criteria) this;
        }

        public Criteria andPriceLimitNotEqualTo(BigDecimal value) {
            addCriterion("price_limit <>", value, "priceLimit");
            return (Criteria) this;
        }

        public Criteria andPriceLimitGreaterThan(BigDecimal value) {
            addCriterion("price_limit >", value, "priceLimit");
            return (Criteria) this;
        }

        public Criteria andPriceLimitGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("price_limit >=", value, "priceLimit");
            return (Criteria) this;
        }

        public Criteria andPriceLimitLessThan(BigDecimal value) {
            addCriterion("price_limit <", value, "priceLimit");
            return (Criteria) this;
        }

        public Criteria andPriceLimitLessThanOrEqualTo(BigDecimal value) {
            addCriterion("price_limit <=", value, "priceLimit");
            return (Criteria) this;
        }

        public Criteria andPriceLimitIn(List<BigDecimal> values) {
            addCriterion("price_limit in", values, "priceLimit");
            return (Criteria) this;
        }

        public Criteria andPriceLimitNotIn(List<BigDecimal> values) {
            addCriterion("price_limit not in", values, "priceLimit");
            return (Criteria) this;
        }

        public Criteria andPriceLimitBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("price_limit between", value1, value2, "priceLimit");
            return (Criteria) this;
        }

        public Criteria andPriceLimitNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("price_limit not between", value1, value2, "priceLimit");
            return (Criteria) this;
        }

        public Criteria andDayPriceLimitIsNull() {
            addCriterion("day_price_limit is null");
            return (Criteria) this;
        }

        public Criteria andDayPriceLimitIsNotNull() {
            addCriterion("day_price_limit is not null");
            return (Criteria) this;
        }

        public Criteria andDayPriceLimitEqualTo(BigDecimal value) {
            addCriterion("day_price_limit =", value, "dayPriceLimit");
            return (Criteria) this;
        }

        public Criteria andDayPriceLimitNotEqualTo(BigDecimal value) {
            addCriterion("day_price_limit <>", value, "dayPriceLimit");
            return (Criteria) this;
        }

        public Criteria andDayPriceLimitGreaterThan(BigDecimal value) {
            addCriterion("day_price_limit >", value, "dayPriceLimit");
            return (Criteria) this;
        }

        public Criteria andDayPriceLimitGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("day_price_limit >=", value, "dayPriceLimit");
            return (Criteria) this;
        }

        public Criteria andDayPriceLimitLessThan(BigDecimal value) {
            addCriterion("day_price_limit <", value, "dayPriceLimit");
            return (Criteria) this;
        }

        public Criteria andDayPriceLimitLessThanOrEqualTo(BigDecimal value) {
            addCriterion("day_price_limit <=", value, "dayPriceLimit");
            return (Criteria) this;
        }

        public Criteria andDayPriceLimitIn(List<BigDecimal> values) {
            addCriterion("day_price_limit in", values, "dayPriceLimit");
            return (Criteria) this;
        }

        public Criteria andDayPriceLimitNotIn(List<BigDecimal> values) {
            addCriterion("day_price_limit not in", values, "dayPriceLimit");
            return (Criteria) this;
        }

        public Criteria andDayPriceLimitBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("day_price_limit between", value1, value2, "dayPriceLimit");
            return (Criteria) this;
        }

        public Criteria andDayPriceLimitNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("day_price_limit not between", value1, value2, "dayPriceLimit");
            return (Criteria) this;
        }

        public Criteria andTaxiSchedulingFeeIsNull() {
            addCriterion("taxi_scheduling_fee is null");
            return (Criteria) this;
        }

        public Criteria andTaxiSchedulingFeeIsNotNull() {
            addCriterion("taxi_scheduling_fee is not null");
            return (Criteria) this;
        }

        public Criteria andTaxiSchedulingFeeEqualTo(Integer value) {
            addCriterion("taxi_scheduling_fee =", value, "taxiSchedulingFee");
            return (Criteria) this;
        }

        public Criteria andTaxiSchedulingFeeNotEqualTo(Integer value) {
            addCriterion("taxi_scheduling_fee <>", value, "taxiSchedulingFee");
            return (Criteria) this;
        }

        public Criteria andTaxiSchedulingFeeGreaterThan(Integer value) {
            addCriterion("taxi_scheduling_fee >", value, "taxiSchedulingFee");
            return (Criteria) this;
        }

        public Criteria andTaxiSchedulingFeeGreaterThanOrEqualTo(Integer value) {
            addCriterion("taxi_scheduling_fee >=", value, "taxiSchedulingFee");
            return (Criteria) this;
        }

        public Criteria andTaxiSchedulingFeeLessThan(Integer value) {
            addCriterion("taxi_scheduling_fee <", value, "taxiSchedulingFee");
            return (Criteria) this;
        }

        public Criteria andTaxiSchedulingFeeLessThanOrEqualTo(Integer value) {
            addCriterion("taxi_scheduling_fee <=", value, "taxiSchedulingFee");
            return (Criteria) this;
        }

        public Criteria andTaxiSchedulingFeeIn(List<Integer> values) {
            addCriterion("taxi_scheduling_fee in", values, "taxiSchedulingFee");
            return (Criteria) this;
        }

        public Criteria andTaxiSchedulingFeeNotIn(List<Integer> values) {
            addCriterion("taxi_scheduling_fee not in", values, "taxiSchedulingFee");
            return (Criteria) this;
        }

        public Criteria andTaxiSchedulingFeeBetween(Integer value1, Integer value2) {
            addCriterion("taxi_scheduling_fee between", value1, value2, "taxiSchedulingFee");
            return (Criteria) this;
        }

        public Criteria andTaxiSchedulingFeeNotBetween(Integer value1, Integer value2) {
            addCriterion("taxi_scheduling_fee not between", value1, value2, "taxiSchedulingFee");
            return (Criteria) this;
        }

        public Criteria andAllowSameCityIsNull() {
            addCriterion("allow_same_city is null");
            return (Criteria) this;
        }

        public Criteria andAllowSameCityIsNotNull() {
            addCriterion("allow_same_city is not null");
            return (Criteria) this;
        }

        public Criteria andAllowSameCityEqualTo(Boolean value) {
            addCriterion("allow_same_city =", value, "allowSameCity");
            return (Criteria) this;
        }

        public Criteria andAllowSameCityNotEqualTo(Boolean value) {
            addCriterion("allow_same_city <>", value, "allowSameCity");
            return (Criteria) this;
        }

        public Criteria andAllowSameCityGreaterThan(Boolean value) {
            addCriterion("allow_same_city >", value, "allowSameCity");
            return (Criteria) this;
        }

        public Criteria andAllowSameCityGreaterThanOrEqualTo(Boolean value) {
            addCriterion("allow_same_city >=", value, "allowSameCity");
            return (Criteria) this;
        }

        public Criteria andAllowSameCityLessThan(Boolean value) {
            addCriterion("allow_same_city <", value, "allowSameCity");
            return (Criteria) this;
        }

        public Criteria andAllowSameCityLessThanOrEqualTo(Boolean value) {
            addCriterion("allow_same_city <=", value, "allowSameCity");
            return (Criteria) this;
        }

        public Criteria andAllowSameCityIn(List<Boolean> values) {
            addCriterion("allow_same_city in", values, "allowSameCity");
            return (Criteria) this;
        }

        public Criteria andAllowSameCityNotIn(List<Boolean> values) {
            addCriterion("allow_same_city not in", values, "allowSameCity");
            return (Criteria) this;
        }

        public Criteria andAllowSameCityBetween(Boolean value1, Boolean value2) {
            addCriterion("allow_same_city between", value1, value2, "allowSameCity");
            return (Criteria) this;
        }

        public Criteria andAllowSameCityNotBetween(Boolean value1, Boolean value2) {
            addCriterion("allow_same_city not between", value1, value2, "allowSameCity");
            return (Criteria) this;
        }

        public Criteria andAllowCalledForotherIsNull() {
            addCriterion("allow_called_forother is null");
            return (Criteria) this;
        }

        public Criteria andAllowCalledForotherIsNotNull() {
            addCriterion("allow_called_forother is not null");
            return (Criteria) this;
        }

        public Criteria andAllowCalledForotherEqualTo(Boolean value) {
            addCriterion("allow_called_forother =", value, "allowCalledForother");
            return (Criteria) this;
        }

        public Criteria andAllowCalledForotherNotEqualTo(Boolean value) {
            addCriterion("allow_called_forother <>", value, "allowCalledForother");
            return (Criteria) this;
        }

        public Criteria andAllowCalledForotherGreaterThan(Boolean value) {
            addCriterion("allow_called_forother >", value, "allowCalledForother");
            return (Criteria) this;
        }

        public Criteria andAllowCalledForotherGreaterThanOrEqualTo(Boolean value) {
            addCriterion("allow_called_forother >=", value, "allowCalledForother");
            return (Criteria) this;
        }

        public Criteria andAllowCalledForotherLessThan(Boolean value) {
            addCriterion("allow_called_forother <", value, "allowCalledForother");
            return (Criteria) this;
        }

        public Criteria andAllowCalledForotherLessThanOrEqualTo(Boolean value) {
            addCriterion("allow_called_forother <=", value, "allowCalledForother");
            return (Criteria) this;
        }

        public Criteria andAllowCalledForotherIn(List<Boolean> values) {
            addCriterion("allow_called_forother in", values, "allowCalledForother");
            return (Criteria) this;
        }

        public Criteria andAllowCalledForotherNotIn(List<Boolean> values) {
            addCriterion("allow_called_forother not in", values, "allowCalledForother");
            return (Criteria) this;
        }

        public Criteria andAllowCalledForotherBetween(Boolean value1, Boolean value2) {
            addCriterion("allow_called_forother between", value1, value2, "allowCalledForother");
            return (Criteria) this;
        }

        public Criteria andAllowCalledForotherNotBetween(Boolean value1, Boolean value2) {
            addCriterion("allow_called_forother not between", value1, value2, "allowCalledForother");
            return (Criteria) this;
        }

        public Criteria andTimesLimitFlagIsNull() {
            addCriterion("times_limit_flag is null");
            return (Criteria) this;
        }

        public Criteria andTimesLimitFlagIsNotNull() {
            addCriterion("times_limit_flag is not null");
            return (Criteria) this;
        }

        public Criteria andTimesLimitFlagEqualTo(Integer value) {
            addCriterion("times_limit_flag =", value, "timesLimitFlag");
            return (Criteria) this;
        }

        public Criteria andTimesLimitFlagNotEqualTo(Integer value) {
            addCriterion("times_limit_flag <>", value, "timesLimitFlag");
            return (Criteria) this;
        }

        public Criteria andTimesLimitFlagGreaterThan(Integer value) {
            addCriterion("times_limit_flag >", value, "timesLimitFlag");
            return (Criteria) this;
        }

        public Criteria andTimesLimitFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("times_limit_flag >=", value, "timesLimitFlag");
            return (Criteria) this;
        }

        public Criteria andTimesLimitFlagLessThan(Integer value) {
            addCriterion("times_limit_flag <", value, "timesLimitFlag");
            return (Criteria) this;
        }

        public Criteria andTimesLimitFlagLessThanOrEqualTo(Integer value) {
            addCriterion("times_limit_flag <=", value, "timesLimitFlag");
            return (Criteria) this;
        }

        public Criteria andTimesLimitFlagIn(List<Integer> values) {
            addCriterion("times_limit_flag in", values, "timesLimitFlag");
            return (Criteria) this;
        }

        public Criteria andTimesLimitFlagNotIn(List<Integer> values) {
            addCriterion("times_limit_flag not in", values, "timesLimitFlag");
            return (Criteria) this;
        }

        public Criteria andTimesLimitFlagBetween(Integer value1, Integer value2) {
            addCriterion("times_limit_flag between", value1, value2, "timesLimitFlag");
            return (Criteria) this;
        }

        public Criteria andTimesLimitFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("times_limit_flag not between", value1, value2, "timesLimitFlag");
            return (Criteria) this;
        }

        public Criteria andTimesLimitIsNull() {
            addCriterion("times_limit is null");
            return (Criteria) this;
        }

        public Criteria andTimesLimitIsNotNull() {
            addCriterion("times_limit is not null");
            return (Criteria) this;
        }

        public Criteria andTimesLimitEqualTo(Integer value) {
            addCriterion("times_limit =", value, "timesLimit");
            return (Criteria) this;
        }

        public Criteria andTimesLimitNotEqualTo(Integer value) {
            addCriterion("times_limit <>", value, "timesLimit");
            return (Criteria) this;
        }

        public Criteria andTimesLimitGreaterThan(Integer value) {
            addCriterion("times_limit >", value, "timesLimit");
            return (Criteria) this;
        }

        public Criteria andTimesLimitGreaterThanOrEqualTo(Integer value) {
            addCriterion("times_limit >=", value, "timesLimit");
            return (Criteria) this;
        }

        public Criteria andTimesLimitLessThan(Integer value) {
            addCriterion("times_limit <", value, "timesLimit");
            return (Criteria) this;
        }

        public Criteria andTimesLimitLessThanOrEqualTo(Integer value) {
            addCriterion("times_limit <=", value, "timesLimit");
            return (Criteria) this;
        }

        public Criteria andTimesLimitIn(List<Integer> values) {
            addCriterion("times_limit in", values, "timesLimit");
            return (Criteria) this;
        }

        public Criteria andTimesLimitNotIn(List<Integer> values) {
            addCriterion("times_limit not in", values, "timesLimit");
            return (Criteria) this;
        }

        public Criteria andTimesLimitBetween(Integer value1, Integer value2) {
            addCriterion("times_limit between", value1, value2, "timesLimit");
            return (Criteria) this;
        }

        public Criteria andTimesLimitNotBetween(Integer value1, Integer value2) {
            addCriterion("times_limit not between", value1, value2, "timesLimit");
            return (Criteria) this;
        }

        public Criteria andCreateUserIsNull() {
            addCriterion("create_user is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIsNotNull() {
            addCriterion("create_user is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserEqualTo(String value) {
            addCriterion("create_user =", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotEqualTo(String value) {
            addCriterion("create_user <>", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserGreaterThan(String value) {
            addCriterion("create_user >", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserGreaterThanOrEqualTo(String value) {
            addCriterion("create_user >=", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLessThan(String value) {
            addCriterion("create_user <", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLessThanOrEqualTo(String value) {
            addCriterion("create_user <=", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLike(String value) {
            addCriterion("create_user like", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotLike(String value) {
            addCriterion("create_user not like", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserIn(List<String> values) {
            addCriterion("create_user in", values, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotIn(List<String> values) {
            addCriterion("create_user not in", values, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserBetween(String value1, String value2) {
            addCriterion("create_user between", value1, value2, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotBetween(String value1, String value2) {
            addCriterion("create_user not between", value1, value2, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIsNull() {
            addCriterion("update_user is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIsNotNull() {
            addCriterion("update_user is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserEqualTo(String value) {
            addCriterion("update_user =", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotEqualTo(String value) {
            addCriterion("update_user <>", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserGreaterThan(String value) {
            addCriterion("update_user >", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserGreaterThanOrEqualTo(String value) {
            addCriterion("update_user >=", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLessThan(String value) {
            addCriterion("update_user <", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLessThanOrEqualTo(String value) {
            addCriterion("update_user <=", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLike(String value) {
            addCriterion("update_user like", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotLike(String value) {
            addCriterion("update_user not like", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIn(List<String> values) {
            addCriterion("update_user in", values, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotIn(List<String> values) {
            addCriterion("update_user not in", values, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserBetween(String value1, String value2) {
            addCriterion("update_user between", value1, value2, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotBetween(String value1, String value2) {
            addCriterion("update_user not between", value1, value2, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andDeleteStatusIsNull() {
            addCriterion("delete_status is null");
            return (Criteria) this;
        }

        public Criteria andDeleteStatusIsNotNull() {
            addCriterion("delete_status is not null");
            return (Criteria) this;
        }

        public Criteria andDeleteStatusEqualTo(Integer value) {
            addCriterion("delete_status =", value, "deleteStatus");
            return (Criteria) this;
        }

        public Criteria andDeleteStatusNotEqualTo(Integer value) {
            addCriterion("delete_status <>", value, "deleteStatus");
            return (Criteria) this;
        }

        public Criteria andDeleteStatusGreaterThan(Integer value) {
            addCriterion("delete_status >", value, "deleteStatus");
            return (Criteria) this;
        }

        public Criteria andDeleteStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("delete_status >=", value, "deleteStatus");
            return (Criteria) this;
        }

        public Criteria andDeleteStatusLessThan(Integer value) {
            addCriterion("delete_status <", value, "deleteStatus");
            return (Criteria) this;
        }

        public Criteria andDeleteStatusLessThanOrEqualTo(Integer value) {
            addCriterion("delete_status <=", value, "deleteStatus");
            return (Criteria) this;
        }

        public Criteria andDeleteStatusIn(List<Integer> values) {
            addCriterion("delete_status in", values, "deleteStatus");
            return (Criteria) this;
        }

        public Criteria andDeleteStatusNotIn(List<Integer> values) {
            addCriterion("delete_status not in", values, "deleteStatus");
            return (Criteria) this;
        }

        public Criteria andDeleteStatusBetween(Integer value1, Integer value2) {
            addCriterion("delete_status between", value1, value2, "deleteStatus");
            return (Criteria) this;
        }

        public Criteria andDeleteStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("delete_status not between", value1, value2, "deleteStatus");
            return (Criteria) this;
        }

        public Criteria andUseRuleDayPriceLimitIsNull() {
            addCriterion("use_rule_day_price_limit is null");
            return (Criteria) this;
        }

        public Criteria andUseRuleDayPriceLimitIsNotNull() {
            addCriterion("use_rule_day_price_limit is not null");
            return (Criteria) this;
        }

        public Criteria andUseRuleDayPriceLimitEqualTo(Integer value) {
            addCriterion("use_rule_day_price_limit =", value, "useRuleDayPriceLimit");
            return (Criteria) this;
        }

        public Criteria andUseRuleDayPriceLimitNotEqualTo(Integer value) {
            addCriterion("use_rule_day_price_limit <>", value, "useRuleDayPriceLimit");
            return (Criteria) this;
        }

        public Criteria andUseRuleDayPriceLimitGreaterThan(Integer value) {
            addCriterion("use_rule_day_price_limit >", value, "useRuleDayPriceLimit");
            return (Criteria) this;
        }

        public Criteria andUseRuleDayPriceLimitGreaterThanOrEqualTo(Integer value) {
            addCriterion("use_rule_day_price_limit >=", value, "useRuleDayPriceLimit");
            return (Criteria) this;
        }

        public Criteria andUseRuleDayPriceLimitLessThan(Integer value) {
            addCriterion("use_rule_day_price_limit <", value, "useRuleDayPriceLimit");
            return (Criteria) this;
        }

        public Criteria andUseRuleDayPriceLimitLessThanOrEqualTo(Integer value) {
            addCriterion("use_rule_day_price_limit <=", value, "useRuleDayPriceLimit");
            return (Criteria) this;
        }

        public Criteria andUseRuleDayPriceLimitIn(List<Integer> values) {
            addCriterion("use_rule_day_price_limit in", values, "useRuleDayPriceLimit");
            return (Criteria) this;
        }

        public Criteria andUseRuleDayPriceLimitNotIn(List<Integer> values) {
            addCriterion("use_rule_day_price_limit not in", values, "useRuleDayPriceLimit");
            return (Criteria) this;
        }

        public Criteria andUseRuleDayPriceLimitBetween(Integer value1, Integer value2) {
            addCriterion("use_rule_day_price_limit between", value1, value2, "useRuleDayPriceLimit");
            return (Criteria) this;
        }

        public Criteria andUseRuleDayPriceLimitNotBetween(Integer value1, Integer value2) {
            addCriterion("use_rule_day_price_limit not between", value1, value2, "useRuleDayPriceLimit");
            return (Criteria) this;
        }

        public Criteria andUsePersonalBudgetIsNull() {
            addCriterion("use_personal_budget is null");
            return (Criteria) this;
        }

        public Criteria andUsePersonalBudgetIsNotNull() {
            addCriterion("use_personal_budget is not null");
            return (Criteria) this;
        }

        public Criteria andUsePersonalBudgetEqualTo(Integer value) {
            addCriterion("use_personal_budget =", value, "usePersonalBudget");
            return (Criteria) this;
        }

        public Criteria andUsePersonalBudgetNotEqualTo(Integer value) {
            addCriterion("use_personal_budget <>", value, "usePersonalBudget");
            return (Criteria) this;
        }

        public Criteria andUsePersonalBudgetGreaterThan(Integer value) {
            addCriterion("use_personal_budget >", value, "usePersonalBudget");
            return (Criteria) this;
        }

        public Criteria andUsePersonalBudgetGreaterThanOrEqualTo(Integer value) {
            addCriterion("use_personal_budget >=", value, "usePersonalBudget");
            return (Criteria) this;
        }

        public Criteria andUsePersonalBudgetLessThan(Integer value) {
            addCriterion("use_personal_budget <", value, "usePersonalBudget");
            return (Criteria) this;
        }

        public Criteria andUsePersonalBudgetLessThanOrEqualTo(Integer value) {
            addCriterion("use_personal_budget <=", value, "usePersonalBudget");
            return (Criteria) this;
        }

        public Criteria andUsePersonalBudgetIn(List<Integer> values) {
            addCriterion("use_personal_budget in", values, "usePersonalBudget");
            return (Criteria) this;
        }

        public Criteria andUsePersonalBudgetNotIn(List<Integer> values) {
            addCriterion("use_personal_budget not in", values, "usePersonalBudget");
            return (Criteria) this;
        }

        public Criteria andUsePersonalBudgetBetween(Integer value1, Integer value2) {
            addCriterion("use_personal_budget between", value1, value2, "usePersonalBudget");
            return (Criteria) this;
        }

        public Criteria andUsePersonalBudgetNotBetween(Integer value1, Integer value2) {
            addCriterion("use_personal_budget not between", value1, value2, "usePersonalBudget");
            return (Criteria) this;
        }

        public Criteria andIsDingIsNull() {
            addCriterion("is_ding is null");
            return (Criteria) this;
        }

        public Criteria andIsDingIsNotNull() {
            addCriterion("is_ding is not null");
            return (Criteria) this;
        }

        public Criteria andIsDingEqualTo(Integer value) {
            addCriterion("is_ding =", value, "isDing");
            return (Criteria) this;
        }

        public Criteria andIsDingNotEqualTo(Integer value) {
            addCriterion("is_ding <>", value, "isDing");
            return (Criteria) this;
        }

        public Criteria andIsDingGreaterThan(Integer value) {
            addCriterion("is_ding >", value, "isDing");
            return (Criteria) this;
        }

        public Criteria andIsDingGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_ding >=", value, "isDing");
            return (Criteria) this;
        }

        public Criteria andIsDingLessThan(Integer value) {
            addCriterion("is_ding <", value, "isDing");
            return (Criteria) this;
        }

        public Criteria andIsDingLessThanOrEqualTo(Integer value) {
            addCriterion("is_ding <=", value, "isDing");
            return (Criteria) this;
        }

        public Criteria andIsDingIn(List<Integer> values) {
            addCriterion("is_ding in", values, "isDing");
            return (Criteria) this;
        }

        public Criteria andIsDingNotIn(List<Integer> values) {
            addCriterion("is_ding not in", values, "isDing");
            return (Criteria) this;
        }

        public Criteria andIsDingBetween(Integer value1, Integer value2) {
            addCriterion("is_ding between", value1, value2, "isDing");
            return (Criteria) this;
        }

        public Criteria andIsDingNotBetween(Integer value1, Integer value2) {
            addCriterion("is_ding not between", value1, value2, "isDing");
            return (Criteria) this;
        }

        public Criteria andPriceAcrossCityFlagIsNull() {
            addCriterion("price_across_city_flag is null");
            return (Criteria) this;
        }

        public Criteria andPriceAcrossCityFlagIsNotNull() {
            addCriterion("price_across_city_flag is not null");
            return (Criteria) this;
        }

        public Criteria andPriceAcrossCityFlagEqualTo(Integer value) {
            addCriterion("price_across_city_flag =", value, "priceAcrossCityFlag");
            return (Criteria) this;
        }

        public Criteria andPriceAcrossCityFlagNotEqualTo(Integer value) {
            addCriterion("price_across_city_flag <>", value, "priceAcrossCityFlag");
            return (Criteria) this;
        }

        public Criteria andPriceAcrossCityFlagGreaterThan(Integer value) {
            addCriterion("price_across_city_flag >", value, "priceAcrossCityFlag");
            return (Criteria) this;
        }

        public Criteria andPriceAcrossCityFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("price_across_city_flag >=", value, "priceAcrossCityFlag");
            return (Criteria) this;
        }

        public Criteria andPriceAcrossCityFlagLessThan(Integer value) {
            addCriterion("price_across_city_flag <", value, "priceAcrossCityFlag");
            return (Criteria) this;
        }

        public Criteria andPriceAcrossCityFlagLessThanOrEqualTo(Integer value) {
            addCriterion("price_across_city_flag <=", value, "priceAcrossCityFlag");
            return (Criteria) this;
        }

        public Criteria andPriceAcrossCityFlagIn(List<Integer> values) {
            addCriterion("price_across_city_flag in", values, "priceAcrossCityFlag");
            return (Criteria) this;
        }

        public Criteria andPriceAcrossCityFlagNotIn(List<Integer> values) {
            addCriterion("price_across_city_flag not in", values, "priceAcrossCityFlag");
            return (Criteria) this;
        }

        public Criteria andPriceAcrossCityFlagBetween(Integer value1, Integer value2) {
            addCriterion("price_across_city_flag between", value1, value2, "priceAcrossCityFlag");
            return (Criteria) this;
        }

        public Criteria andPriceAcrossCityFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("price_across_city_flag not between", value1, value2, "priceAcrossCityFlag");
            return (Criteria) this;
        }

        public Criteria andApplicantApplyMaxTimesLimitIsNull() {
            addCriterion("applicant_apply_max_times_limit is null");
            return (Criteria) this;
        }

        public Criteria andApplicantApplyMaxTimesLimitIsNotNull() {
            addCriterion("applicant_apply_max_times_limit is not null");
            return (Criteria) this;
        }

        public Criteria andApplicantApplyMaxTimesLimitEqualTo(Integer value) {
            addCriterion("applicant_apply_max_times_limit =", value, "applicantApplyMaxTimesLimit");
            return (Criteria) this;
        }

        public Criteria andApplicantApplyMaxTimesLimitNotEqualTo(Integer value) {
            addCriterion("applicant_apply_max_times_limit <>", value, "applicantApplyMaxTimesLimit");
            return (Criteria) this;
        }

        public Criteria andApplicantApplyMaxTimesLimitGreaterThan(Integer value) {
            addCriterion("applicant_apply_max_times_limit >", value, "applicantApplyMaxTimesLimit");
            return (Criteria) this;
        }

        public Criteria andApplicantApplyMaxTimesLimitGreaterThanOrEqualTo(Integer value) {
            addCriterion("applicant_apply_max_times_limit >=", value, "applicantApplyMaxTimesLimit");
            return (Criteria) this;
        }

        public Criteria andApplicantApplyMaxTimesLimitLessThan(Integer value) {
            addCriterion("applicant_apply_max_times_limit <", value, "applicantApplyMaxTimesLimit");
            return (Criteria) this;
        }

        public Criteria andApplicantApplyMaxTimesLimitLessThanOrEqualTo(Integer value) {
            addCriterion("applicant_apply_max_times_limit <=", value, "applicantApplyMaxTimesLimit");
            return (Criteria) this;
        }

        public Criteria andApplicantApplyMaxTimesLimitIn(List<Integer> values) {
            addCriterion("applicant_apply_max_times_limit in", values, "applicantApplyMaxTimesLimit");
            return (Criteria) this;
        }

        public Criteria andApplicantApplyMaxTimesLimitNotIn(List<Integer> values) {
            addCriterion("applicant_apply_max_times_limit not in", values, "applicantApplyMaxTimesLimit");
            return (Criteria) this;
        }

        public Criteria andApplicantApplyMaxTimesLimitBetween(Integer value1, Integer value2) {
            addCriterion("applicant_apply_max_times_limit between", value1, value2, "applicantApplyMaxTimesLimit");
            return (Criteria) this;
        }

        public Criteria andApplicantApplyMaxTimesLimitNotBetween(Integer value1, Integer value2) {
            addCriterion("applicant_apply_max_times_limit not between", value1, value2, "applicantApplyMaxTimesLimit");
            return (Criteria) this;
        }

        public Criteria andDayPriceLimitTypeIsNull() {
            addCriterion("day_price_limit_type is null");
            return (Criteria) this;
        }

        public Criteria andDayPriceLimitTypeIsNotNull() {
            addCriterion("day_price_limit_type is not null");
            return (Criteria) this;
        }

        public Criteria andDayPriceLimitTypeEqualTo(Integer value) {
            addCriterion("day_price_limit_type =", value, "dayPriceLimitType");
            return (Criteria) this;
        }

        public Criteria andDayPriceLimitTypeNotEqualTo(Integer value) {
            addCriterion("day_price_limit_type <>", value, "dayPriceLimitType");
            return (Criteria) this;
        }

        public Criteria andDayPriceLimitTypeGreaterThan(Integer value) {
            addCriterion("day_price_limit_type >", value, "dayPriceLimitType");
            return (Criteria) this;
        }

        public Criteria andDayPriceLimitTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("day_price_limit_type >=", value, "dayPriceLimitType");
            return (Criteria) this;
        }

        public Criteria andDayPriceLimitTypeLessThan(Integer value) {
            addCriterion("day_price_limit_type <", value, "dayPriceLimitType");
            return (Criteria) this;
        }

        public Criteria andDayPriceLimitTypeLessThanOrEqualTo(Integer value) {
            addCriterion("day_price_limit_type <=", value, "dayPriceLimitType");
            return (Criteria) this;
        }

        public Criteria andDayPriceLimitTypeIn(List<Integer> values) {
            addCriterion("day_price_limit_type in", values, "dayPriceLimitType");
            return (Criteria) this;
        }

        public Criteria andDayPriceLimitTypeNotIn(List<Integer> values) {
            addCriterion("day_price_limit_type not in", values, "dayPriceLimitType");
            return (Criteria) this;
        }

        public Criteria andDayPriceLimitTypeBetween(Integer value1, Integer value2) {
            addCriterion("day_price_limit_type between", value1, value2, "dayPriceLimitType");
            return (Criteria) this;
        }

        public Criteria andDayPriceLimitTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("day_price_limit_type not between", value1, value2, "dayPriceLimitType");
            return (Criteria) this;
        }

        public Criteria andRuleTypeIsNull() {
            addCriterion("rule_type is null");
            return (Criteria) this;
        }

        public Criteria andRuleTypeIsNotNull() {
            addCriterion("rule_type is not null");
            return (Criteria) this;
        }

        public Criteria andRuleTypeEqualTo(Integer value) {
            addCriterion("rule_type =", value, "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleTypeNotEqualTo(Integer value) {
            addCriterion("rule_type <>", value, "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleTypeGreaterThan(Integer value) {
            addCriterion("rule_type >", value, "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("rule_type >=", value, "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleTypeLessThan(Integer value) {
            addCriterion("rule_type <", value, "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleTypeLessThanOrEqualTo(Integer value) {
            addCriterion("rule_type <=", value, "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleTypeIn(List<Integer> values) {
            addCriterion("rule_type in", values, "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleTypeNotIn(List<Integer> values) {
            addCriterion("rule_type not in", values, "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleTypeBetween(Integer value1, Integer value2) {
            addCriterion("rule_type between", value1, value2, "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("rule_type not between", value1, value2, "ruleType");
            return (Criteria) this;
        }

        public Criteria andModifyFlagIsNull() {
            addCriterion("modify_flag is null");
            return (Criteria) this;
        }

        public Criteria andModifyFlagIsNotNull() {
            addCriterion("modify_flag is not null");
            return (Criteria) this;
        }

        public Criteria andModifyFlagEqualTo(Boolean value) {
            addCriterion("modify_flag =", value, "modifyFlag");
            return (Criteria) this;
        }

        public Criteria andModifyFlagNotEqualTo(Boolean value) {
            addCriterion("modify_flag <>", value, "modifyFlag");
            return (Criteria) this;
        }

        public Criteria andModifyFlagGreaterThan(Boolean value) {
            addCriterion("modify_flag >", value, "modifyFlag");
            return (Criteria) this;
        }

        public Criteria andModifyFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("modify_flag >=", value, "modifyFlag");
            return (Criteria) this;
        }

        public Criteria andModifyFlagLessThan(Boolean value) {
            addCriterion("modify_flag <", value, "modifyFlag");
            return (Criteria) this;
        }

        public Criteria andModifyFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("modify_flag <=", value, "modifyFlag");
            return (Criteria) this;
        }

        public Criteria andModifyFlagIn(List<Boolean> values) {
            addCriterion("modify_flag in", values, "modifyFlag");
            return (Criteria) this;
        }

        public Criteria andModifyFlagNotIn(List<Boolean> values) {
            addCriterion("modify_flag not in", values, "modifyFlag");
            return (Criteria) this;
        }

        public Criteria andModifyFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("modify_flag between", value1, value2, "modifyFlag");
            return (Criteria) this;
        }

        public Criteria andModifyFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("modify_flag not between", value1, value2, "modifyFlag");
            return (Criteria) this;
        }

        public Criteria andLimitTimeIsNull() {
            addCriterion("limit_time is null");
            return (Criteria) this;
        }

        public Criteria andLimitTimeIsNotNull() {
            addCriterion("limit_time is not null");
            return (Criteria) this;
        }

        public Criteria andLimitTimeEqualTo(Boolean value) {
            addCriterion("limit_time =", value, "limitTime");
            return (Criteria) this;
        }

        public Criteria andLimitTimeNotEqualTo(Boolean value) {
            addCriterion("limit_time <>", value, "limitTime");
            return (Criteria) this;
        }

        public Criteria andLimitTimeGreaterThan(Boolean value) {
            addCriterion("limit_time >", value, "limitTime");
            return (Criteria) this;
        }

        public Criteria andLimitTimeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("limit_time >=", value, "limitTime");
            return (Criteria) this;
        }

        public Criteria andLimitTimeLessThan(Boolean value) {
            addCriterion("limit_time <", value, "limitTime");
            return (Criteria) this;
        }

        public Criteria andLimitTimeLessThanOrEqualTo(Boolean value) {
            addCriterion("limit_time <=", value, "limitTime");
            return (Criteria) this;
        }

        public Criteria andLimitTimeIn(List<Boolean> values) {
            addCriterion("limit_time in", values, "limitTime");
            return (Criteria) this;
        }

        public Criteria andLimitTimeNotIn(List<Boolean> values) {
            addCriterion("limit_time not in", values, "limitTime");
            return (Criteria) this;
        }

        public Criteria andLimitTimeBetween(Boolean value1, Boolean value2) {
            addCriterion("limit_time between", value1, value2, "limitTime");
            return (Criteria) this;
        }

        public Criteria andLimitTimeNotBetween(Boolean value1, Boolean value2) {
            addCriterion("limit_time not between", value1, value2, "limitTime");
            return (Criteria) this;
        }

        public Criteria andLimitPathIsNull() {
            addCriterion("limit_path is null");
            return (Criteria) this;
        }

        public Criteria andLimitPathIsNotNull() {
            addCriterion("limit_path is not null");
            return (Criteria) this;
        }

        public Criteria andLimitPathEqualTo(Boolean value) {
            addCriterion("limit_path =", value, "limitPath");
            return (Criteria) this;
        }

        public Criteria andLimitPathNotEqualTo(Boolean value) {
            addCriterion("limit_path <>", value, "limitPath");
            return (Criteria) this;
        }

        public Criteria andLimitPathGreaterThan(Boolean value) {
            addCriterion("limit_path >", value, "limitPath");
            return (Criteria) this;
        }

        public Criteria andLimitPathGreaterThanOrEqualTo(Boolean value) {
            addCriterion("limit_path >=", value, "limitPath");
            return (Criteria) this;
        }

        public Criteria andLimitPathLessThan(Boolean value) {
            addCriterion("limit_path <", value, "limitPath");
            return (Criteria) this;
        }

        public Criteria andLimitPathLessThanOrEqualTo(Boolean value) {
            addCriterion("limit_path <=", value, "limitPath");
            return (Criteria) this;
        }

        public Criteria andLimitPathIn(List<Boolean> values) {
            addCriterion("limit_path in", values, "limitPath");
            return (Criteria) this;
        }

        public Criteria andLimitPathNotIn(List<Boolean> values) {
            addCriterion("limit_path not in", values, "limitPath");
            return (Criteria) this;
        }

        public Criteria andLimitPathBetween(Boolean value1, Boolean value2) {
            addCriterion("limit_path between", value1, value2, "limitPath");
            return (Criteria) this;
        }

        public Criteria andLimitPathNotBetween(Boolean value1, Boolean value2) {
            addCriterion("limit_path not between", value1, value2, "limitPath");
            return (Criteria) this;
        }

        public Criteria andAllowCallForOtherSubsidiaryIsNull() {
            addCriterion("allow_call_for_other_subsidiary is null");
            return (Criteria) this;
        }

        public Criteria andAllowCallForOtherSubsidiaryIsNotNull() {
            addCriterion("allow_call_for_other_subsidiary is not null");
            return (Criteria) this;
        }

        public Criteria andAllowCallForOtherSubsidiaryEqualTo(Boolean value) {
            addCriterion("allow_call_for_other_subsidiary =", value, "allowCallForOtherSubsidiary");
            return (Criteria) this;
        }

        public Criteria andAllowCallForOtherSubsidiaryNotEqualTo(Boolean value) {
            addCriterion("allow_call_for_other_subsidiary <>", value, "allowCallForOtherSubsidiary");
            return (Criteria) this;
        }

        public Criteria andAllowCallForOtherSubsidiaryGreaterThan(Boolean value) {
            addCriterion("allow_call_for_other_subsidiary >", value, "allowCallForOtherSubsidiary");
            return (Criteria) this;
        }

        public Criteria andAllowCallForOtherSubsidiaryGreaterThanOrEqualTo(Boolean value) {
            addCriterion("allow_call_for_other_subsidiary >=", value, "allowCallForOtherSubsidiary");
            return (Criteria) this;
        }

        public Criteria andAllowCallForOtherSubsidiaryLessThan(Boolean value) {
            addCriterion("allow_call_for_other_subsidiary <", value, "allowCallForOtherSubsidiary");
            return (Criteria) this;
        }

        public Criteria andAllowCallForOtherSubsidiaryLessThanOrEqualTo(Boolean value) {
            addCriterion("allow_call_for_other_subsidiary <=", value, "allowCallForOtherSubsidiary");
            return (Criteria) this;
        }

        public Criteria andAllowCallForOtherSubsidiaryIn(List<Boolean> values) {
            addCriterion("allow_call_for_other_subsidiary in", values, "allowCallForOtherSubsidiary");
            return (Criteria) this;
        }

        public Criteria andAllowCallForOtherSubsidiaryNotIn(List<Boolean> values) {
            addCriterion("allow_call_for_other_subsidiary not in", values, "allowCallForOtherSubsidiary");
            return (Criteria) this;
        }

        public Criteria andAllowCallForOtherSubsidiaryBetween(Boolean value1, Boolean value2) {
            addCriterion("allow_call_for_other_subsidiary between", value1, value2, "allowCallForOtherSubsidiary");
            return (Criteria) this;
        }

        public Criteria andAllowCallForOtherSubsidiaryNotBetween(Boolean value1, Boolean value2) {
            addCriterion("allow_call_for_other_subsidiary not between", value1, value2, "allowCallForOtherSubsidiary");
            return (Criteria) this;
        }

        public Criteria andCityLimitFlagIsNull() {
            addCriterion("city_limit_flag is null");
            return (Criteria) this;
        }

        public Criteria andCityLimitFlagIsNotNull() {
            addCriterion("city_limit_flag is not null");
            return (Criteria) this;
        }

        public Criteria andCityLimitFlagEqualTo(Integer value) {
            addCriterion("city_limit_flag =", value, "cityLimitFlag");
            return (Criteria) this;
        }

        public Criteria andCityLimitFlagNotEqualTo(Integer value) {
            addCriterion("city_limit_flag <>", value, "cityLimitFlag");
            return (Criteria) this;
        }

        public Criteria andCityLimitFlagGreaterThan(Integer value) {
            addCriterion("city_limit_flag >", value, "cityLimitFlag");
            return (Criteria) this;
        }

        public Criteria andCityLimitFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("city_limit_flag >=", value, "cityLimitFlag");
            return (Criteria) this;
        }

        public Criteria andCityLimitFlagLessThan(Integer value) {
            addCriterion("city_limit_flag <", value, "cityLimitFlag");
            return (Criteria) this;
        }

        public Criteria andCityLimitFlagLessThanOrEqualTo(Integer value) {
            addCriterion("city_limit_flag <=", value, "cityLimitFlag");
            return (Criteria) this;
        }

        public Criteria andCityLimitFlagIn(List<Integer> values) {
            addCriterion("city_limit_flag in", values, "cityLimitFlag");
            return (Criteria) this;
        }

        public Criteria andCityLimitFlagNotIn(List<Integer> values) {
            addCriterion("city_limit_flag not in", values, "cityLimitFlag");
            return (Criteria) this;
        }

        public Criteria andCityLimitFlagBetween(Integer value1, Integer value2) {
            addCriterion("city_limit_flag between", value1, value2, "cityLimitFlag");
            return (Criteria) this;
        }

        public Criteria andCityLimitFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("city_limit_flag not between", value1, value2, "cityLimitFlag");
            return (Criteria) this;
        }

        public Criteria andAllowSameCityTypeIsNull() {
            addCriterion("allow_same_city_type is null");
            return (Criteria) this;
        }

        public Criteria andAllowSameCityTypeIsNotNull() {
            addCriterion("allow_same_city_type is not null");
            return (Criteria) this;
        }

        public Criteria andAllowSameCityTypeEqualTo(Integer value) {
            addCriterion("allow_same_city_type =", value, "allowSameCityType");
            return (Criteria) this;
        }

        public Criteria andAllowSameCityTypeNotEqualTo(Integer value) {
            addCriterion("allow_same_city_type <>", value, "allowSameCityType");
            return (Criteria) this;
        }

        public Criteria andAllowSameCityTypeGreaterThan(Integer value) {
            addCriterion("allow_same_city_type >", value, "allowSameCityType");
            return (Criteria) this;
        }

        public Criteria andAllowSameCityTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("allow_same_city_type >=", value, "allowSameCityType");
            return (Criteria) this;
        }

        public Criteria andAllowSameCityTypeLessThan(Integer value) {
            addCriterion("allow_same_city_type <", value, "allowSameCityType");
            return (Criteria) this;
        }

        public Criteria andAllowSameCityTypeLessThanOrEqualTo(Integer value) {
            addCriterion("allow_same_city_type <=", value, "allowSameCityType");
            return (Criteria) this;
        }

        public Criteria andAllowSameCityTypeIn(List<Integer> values) {
            addCriterion("allow_same_city_type in", values, "allowSameCityType");
            return (Criteria) this;
        }

        public Criteria andAllowSameCityTypeNotIn(List<Integer> values) {
            addCriterion("allow_same_city_type not in", values, "allowSameCityType");
            return (Criteria) this;
        }

        public Criteria andAllowSameCityTypeBetween(Integer value1, Integer value2) {
            addCriterion("allow_same_city_type between", value1, value2, "allowSameCityType");
            return (Criteria) this;
        }

        public Criteria andAllowSameCityTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("allow_same_city_type not between", value1, value2, "allowSameCityType");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table taxi_approve_rule
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table taxi_approve_rule
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}