package com.fenbeitong.saas.core.contract.selfauthorize;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class ResponsePaginationRequired {

    private Integer page_size;

    private Integer page_count;

    private Integer total_page;

    @JSONField(serialize = false)
    private Integer offset;

    public void setPaginationInfo(long totalCount, Integer pageSize, Integer pageCount) {
        // 分页逻辑
        Integer currentPageSize = pageSize;
        Integer currentPageCount = pageCount;
        if (currentPageSize == null || currentPageSize <= 0) {
            currentPageSize = 20;
        }
        if (pageCount == null || pageCount <= 0) {
            currentPageCount = 1;
        }
        long pageNum = totalCount / currentPageSize;
        // 有余数则加一页
        if (totalCount % currentPageSize != 0) {
            pageNum += 1;
        }
        int offset = (currentPageCount - 1) * currentPageSize;

        this.setTotal_page((int) pageNum);
        this.setPage_count(currentPageCount);
        this.setPage_size(currentPageSize);
        this.setOffset(offset);
    }

}
