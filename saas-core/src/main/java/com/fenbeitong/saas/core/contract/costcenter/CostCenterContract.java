package com.fenbeitong.saas.core.contract.costcenter;

import java.util.List;

/**
 * Created by xuzn on 18/3/22.
 */
public class CostCenterContract {
    private boolean force_commit;
    private String id;
    private String code;
    private String name;
    private String description;
    private String begin_date;
    private String end_date;
    private Integer expired_state;
    private Integer usable_range;
    private Integer state;
    private List<ManagerBean> manager;
    private List<ManagerBean> member;
    private List<String> idList;
    private List<ManagerBean> member_dept;

    public boolean isForce_commit() {
        return force_commit;
    }

    public void setForce_commit(boolean force_commit) {
        this.force_commit = force_commit;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getBegin_date() {
        return begin_date;
    }

    public void setBegin_date(String begin_date) {
        this.begin_date = begin_date;
    }

    public String getEnd_date() {
        return end_date;
    }

    public void setEnd_date(String end_date) {
        this.end_date = end_date;
    }

    public Integer getExpired_state() {
        return expired_state;
    }

    public void setExpired_state(Integer expired_state) {
        this.expired_state = expired_state;
    }

    public Integer getUsable_range() {
        return usable_range;
    }

    public void setUsable_range(Integer usable_range) {
        this.usable_range = usable_range;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public List<ManagerBean> getManager() {
        return manager;
    }

    public void setManager(List<ManagerBean> manager) {
        this.manager = manager;
    }

    public List<ManagerBean> getMember() {
        return member;
    }

    public void setMember(List<ManagerBean> member) {
        this.member = member;
    }

    public List<String> getIdList() {
        return idList;
    }

    public void setIdList(List<String> idList) {
        this.idList = idList;
    }

    public List<ManagerBean> getMember_dept() {
        return member_dept;
    }

    public void setMember_dept(List<ManagerBean> member_dept) {
        this.member_dept = member_dept;
    }

    public static class ManagerBean{
        private String member_id;
        private String member_name;
        private Boolean is_manager;
        private String dept_name;
        private String company_id;

        public String getCompany_id() {
            return company_id;
        }

        public void setCompany_id(String company_id) {
            this.company_id = company_id;
        }

        public String getDept_name() {
            return dept_name;
        }

        public void setDept_name(String dept_name) {
            this.dept_name = dept_name;
        }

        public String getMember_id() {
            return member_id;
        }

        public void setMember_id(String member_id) {
            this.member_id = member_id;
        }

        public String getMember_name() {
            return member_name;
        }

        public void setMember_name(String member_name) {
            this.member_name = member_name;
        }

        public Boolean getIs_manager() {
            return is_manager;
        }

        public void setIs_manager(Boolean is_manager) {
            this.is_manager = is_manager;
        }
    }
}
