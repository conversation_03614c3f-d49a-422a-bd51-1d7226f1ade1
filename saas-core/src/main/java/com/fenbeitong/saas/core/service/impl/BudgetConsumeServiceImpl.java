package com.fenbeitong.saas.core.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.finhub.common.constant.BudgetCostAttrTypeEnum;
import com.fenbeitong.finhub.common.constant.CostAttributionScopeEnum;
import com.fenbeitong.finhub.common.constant.UsePersonalBudgetTypeEnum;
import com.fenbeitong.finhub.common.utils.CheckUtils;
import com.fenbeitong.saas.core.common.base.SaasException;
import com.fenbeitong.saas.core.common.constant.GlobalResponseCode;
import com.fenbeitong.saas.core.common.constant.RedisKeyConstant;
import com.fenbeitong.saas.core.common.constant.SaasBudgetConstant;
import com.fenbeitong.saas.core.contract.budget.BudgetConsumeReqContract;
import com.fenbeitong.saas.core.contract.common.CostAttributionInfo;
import com.fenbeitong.saas.core.contract.messagesettings.CostAttrAndBudgetConf;
import com.fenbeitong.saas.core.contract.order.check.OrderBudgetCheckContract;
import com.fenbeitong.saas.core.contract.order.check.TravelOnBusiOrderRuleCheckResult;
import com.fenbeitong.saas.core.dao.fenbeitong.OrgConsumeRecordExtMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.OrgConsumeRecordLogExtMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.OrgConsumeRecordMapper;
import com.fenbeitong.saas.core.language.CoreLanguage;
import com.fenbeitong.saas.core.model.enums.CostAttributionCategory;
import com.fenbeitong.saas.core.model.enums.OrderCategory;
import com.fenbeitong.saas.core.model.enums.apply.BizType;
import com.fenbeitong.saas.core.model.enums.budget.BudgetType;
import com.fenbeitong.saas.core.model.fenbeitong.OrgConsumeRecord;
import com.fenbeitong.saas.core.model.fenbeitong.OrgConsumeRecordExample;
import com.fenbeitong.saas.core.model.fenbeitong.OrgConsumeRecordLog;
import com.fenbeitong.saas.core.service.*;
import com.fenbeitong.saas.core.utils.cache.RedisService;
import com.fenbeitong.saas.core.utils.redis.RedisDistributionLock;
import com.fenbeitong.saas.core.utils.tools.ThreadPoolUtils;
import com.fenbeitong.saasplus.api.service.budget.IBudgetService;
import com.fenbeitong.usercenter.api.model.dto.costcenter.CostCenter;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.fenbeitong.usercenter.api.service.costcenter.ICostCenterService;
import com.google.common.collect.Lists;
import com.luastar.swift.base.utils.DateUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.StrUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by zhuminghua on 2017/10/27.
 */
@Service
public class BudgetConsumeServiceImpl implements IBudgetConsumeService {

    private static Logger logger = LoggerFactory.getLogger(BudgetConsumeServiceImpl.class);

    @Autowired
    private IOrganizationService organizationService;

    @Autowired
    private IBaseOrganizationService baseOrganizationService;

    @Autowired
    private OrgConsumeRecordMapper orgConsumeRecordMapper;

    @Autowired
    private OrgConsumeRecordExtMapper orgConsumeRecordExtMapper;

    @Autowired
    private IOrderCheckService iOrderCheckService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private OrgConsumeRecordLogExtMapper orgConsumeRecordLogExtMapper;

    @Autowired
    private ICostCenterService costCenterService;

    @Autowired
    private IBudgetService iBudgetService;

    @Autowired
    private IApplyThirdService applyThirdService;

    @Override
    public void saveOrgConsumeRecord(BudgetConsumeReqContract reqContract) {
        if (StringUtils.isEmpty(reqContract.getCompany_id())) {
            throw new SaasException(GlobalResponseCode.ParameterError, CoreLanguage.Common_Exception_CompanyIdNotEmpty.getMessage());
        }
        // 是否是新预算公司
        if (iOrderCheckService.isNewBudgetCompany(reqContract.getCompany_id())) {
            logger.info("【saveOrgConsumeRecord】新预算公司{}, 不计预算", reqContract.getCompany_id());
            return;
        }
        if (StringUtils.isEmpty(reqContract.getEmployee_id())) {
            throw new SaasException(GlobalResponseCode.ParameterError, CoreLanguage.Common_Exception_UserIdNotEmpty.getMessage());
        }
        BizType orderCategory = BizType.valueOf(reqContract.getOrder_category());
        if (orderCategory == null || orderCategory == BizType.Unknown) {
            throw new SaasException(GlobalResponseCode.ParameterError, CoreLanguage.Common_Exception_OrderTypeNotCorrect.getMessage());
        }
        if (StringUtils.isEmpty(reqContract.getOrder_id())) {
            throw new SaasException(GlobalResponseCode.ParameterError, CoreLanguage.Common_Exception_OrderIdNotEmpty.getMessage());
        }
        if (reqContract.getOrder_amount() == null) {
            throw new SaasException(GlobalResponseCode.ParameterError, CoreLanguage.Common_Exception_OrderMoneyNotEmpty.getMessage());
        }
        List<CostAttributionInfo> costAttributionList = reqContract.getCost_attribution_list();
        //兼容老版本单费用归属
        if (ObjUtils.isEmpty(costAttributionList)) {
            costAttributionList = Lists.newArrayList();
            CostAttributionCategory costAttributionCategory = CostAttributionCategory.getByKey(reqContract.getCost_attribution_category());
            if (costAttributionCategory == null) {
                throw new SaasException(GlobalResponseCode.ParameterError, CoreLanguage.Common_Exception_FeeBelongTypeNotEmpty.getMessage());
            }
            if (StringUtils.isEmpty(reqContract.getCost_attribution_id())) {
                throw new SaasException(GlobalResponseCode.ParameterError, CoreLanguage.Common_Exception_FeeBelongIdNotEmpty.getMessage());
            }
            // 统一转成费用归属列表处理
            CostAttributionInfo costAttributionInfo = new CostAttributionInfo();
            costAttributionInfo.setCost_attribution_category(costAttributionCategory.getKey());
            costAttributionInfo.setCost_attribution_id(reqContract.getCost_attribution_id());
            costAttributionList.add(costAttributionInfo);
        } else {
            for (CostAttributionInfo costAttributionInfo : costAttributionList) {
                CostAttributionCategory costAttributionCategory = CostAttributionCategory.getByKey(costAttributionInfo.getCost_attribution_category());
                if (costAttributionCategory == null) {
                    throw new SaasException(GlobalResponseCode.ParameterError, CoreLanguage.Common_Exception_FeeBelongTypeNotEmpty.getMessage());
                }
                if (StringUtils.isEmpty(costAttributionInfo.getCost_attribution_id())) {
                    throw new SaasException(GlobalResponseCode.ParameterError, CoreLanguage.Common_Exception_FeeBelongIdNotEmpty.getMessage());
                }
            }
        }
        String createTime = reqContract.getCreate_time();
        if (StringUtils.isEmpty(createTime)) {
            throw new SaasException(GlobalResponseCode.ParameterError, CoreLanguage.Common_Exception_CreateDateTimeNotEmpty.getMessage());
        }
        String pattern = "yyyy-MM-dd HH:mm:ss";
        Date createTimeDate = DateUtils.parse(createTime, pattern);
        if (createTimeDate == null) {
            throw new SaasException(GlobalResponseCode.ParameterError, StrUtils.formatString(CoreLanguage.Common_Exception_CreateDateTimeFormatNotCorrect.getMessage(), pattern));
        }
        saveOrgConsumeRecord(reqContract.getCompany_id(),
                reqContract.getEmployee_id(),
                orderCategory,
                reqContract.getOrder_id(),
                reqContract.getOrder_amount(),
                costAttributionList,
                createTimeDate,
                reqContract.getPre_order_id(),
                reqContract.getOrigin_order_id(),
                reqContract.getCost_attribution_scope(),
                reqContract.getBudget_cost_attr_type(),
                reqContract.getUse_personal_budget(),
                JSONObject.toJSONString(reqContract));
    }

    @Transactional(transactionManager = "fenbeitong", rollbackFor = Exception.class)
    public void saveOrgConsumeRecord(String companyId,
                                     String employeeId,
                                     BizType orderCategory,
                                     String orderId,
                                     BigDecimal orderAmount,
                                     List<CostAttributionInfo> costAttributionList,
                                     Date createTime,
                                     String preOrderId,
                                     String originOrderId,
                                     Integer costAttributionScope,
                                     Integer budgetCostAttrType,
                                     Integer usePersonalBudget,
                                     String paramJson) {
        if (StringUtils.isEmpty(companyId)
                || StringUtils.isEmpty(employeeId)
                || orderCategory == null
                || StringUtils.isEmpty(orderId)
                || orderAmount == null
                || ObjUtils.isEmpty(costAttributionList)
                || createTime == null) {
            logger.warn("【saveOrgConsumeRecord】参数不能为空");
            return;
        }
        if (orderCategory.getValue() == (BizType.Meishi.getValue())) {
            orderCategory = BizType.Dinner;
        }
        try {
            costAttributionList = iOrderCheckService.getCostAttributionInfo(orderId, costAttributionList);
            // 是否是对接预算校验公司
            boolean isDocking = applyThirdService.isBudgetDockingCompany(companyId);
            if (isDocking) {
                logger.info("【saveOrgConsumeRecord】三方对接预算公司{}, 不计预算", companyId);
                thirdCategoryOrderBudgetCheck(companyId, employeeId, orderCategory,
                        orderAmount, costAttributionList, costAttributionScope, budgetCostAttrType);
                return;
            }
            Date requestTime = new Date();
            paramJson = "save:" + paramJson;
            //预算保存信息
            OrgConsumeRecord orgConsumeRecord = new OrgConsumeRecord();
            orgConsumeRecord.setCompanyId(companyId);
            orgConsumeRecord.setOrderId(orderId);
            orgConsumeRecord.setAmount(orderAmount);
            orgConsumeRecord.setConsumeCategory(orderCategory.getValue());
            orgConsumeRecord.setCreateTime(createTime);
            orgConsumeRecord.setPreOrderId(preOrderId);
            orgConsumeRecord.setOriginOrderId(originOrderId);
            orgConsumeRecord.setIsLatest(true);
            orgConsumeRecord.setRequestTime(requestTime);
            orgConsumeRecord.setParamJson(paramJson);

            //预算校验
            OrderBudgetCheckContract orderBudgetCheckContract = new OrderBudgetCheckContract();
            orderBudgetCheckContract.setEmployee_id(employeeId);
            orderBudgetCheckContract.setCompany_id(companyId);
            orderBudgetCheckContract.setCostAttributionList(costAttributionList);
            orderBudgetCheckContract.setPrice(orderAmount);
            //允许超预算提交不限制
            orderBudgetCheckContract.setForce_sumbit(true);

            // 占用个人预算
            if (ObjUtils.toInteger(usePersonalBudget, UsePersonalBudgetTypeEnum.USE.getCode()) != UsePersonalBudgetTypeEnum.NOT_USE.getCode()) {
                // 员工
                EmployeeContract employee = baseOrganizationService.getEmployee(employeeId, companyId);
                if (employee != null) {
                    orderBudgetCheckContract.setForce_sumbit(false);
                    checkAndSaveOrgConsumeRecord(orderBudgetCheckContract, orgConsumeRecord, orderCategory, BudgetType.Employee, employeeId, employee.getName());
                }
            }
            for (CostAttributionInfo costAttributionInfo : costAttributionList) {
                Integer costAttributionCategory = costAttributionInfo.getCost_attribution_category();
                String costAttributionId = costAttributionInfo.getCost_attribution_id();
                if (ObjUtils.isNotBlank(costAttributionScope) && costAttributionScope == CostAttributionScopeEnum.DEPT_AND_PROJ.getCode()) {
                    if (ObjUtils.isNotBlank(budgetCostAttrType) && budgetCostAttrType == BudgetCostAttrTypeEnum.DEPT.getCode() && costAttributionCategory != CostAttributionCategory.Dept.getKey()) {
                        continue;
                    }
                    if (ObjUtils.isNotBlank(budgetCostAttrType) && budgetCostAttrType == BudgetCostAttrTypeEnum.PROJ.getCode() && costAttributionCategory != CostAttributionCategory.CostCenter.getKey()) {
                        continue;
                    }
                }
                if (costAttributionCategory == CostAttributionCategory.Dept.getKey()) {
                    List<com.fenbeitong.usercenter.api.model.po.orgunit.OrgUnit> deptList = organizationService.queryParentOrgUnitList(companyId, costAttributionId);
                    if (CollectionUtils.isNotEmpty(deptList)) {
                        // 部门
                        for (com.fenbeitong.usercenter.api.model.po.orgunit.OrgUnit dept : deptList) {
                            checkAndSaveOrgConsumeRecord(orderBudgetCheckContract, orgConsumeRecord, orderCategory, BudgetType.Department, dept.getId(), dept.getName());
                        }
                    }
                } else if (costAttributionCategory == CostAttributionCategory.CostCenter.getKey()) {
                    // 成本中心
                    CostCenter costCenter = costCenterService.queryByCostId(costAttributionId);
                    if (costCenter != null) {
                        checkAndSaveOrgConsumeRecord(orderBudgetCheckContract, orgConsumeRecord, orderCategory, BudgetType.CostCenter, costCenter.getId(), costCenter.getName());
                    }
                }
            }
        } catch (Exception e) {
            logger.error("【saveOrgConsumeRecord】保存预算异常", e);
            if (e instanceof SaasException) {
                throw e;
            }
            throw new SaasException(GlobalResponseCode.InnerError, CoreLanguage.Common_Exception_ServerErrorRetry.getMessage());
        }
        // 已统计月份(3个月之前)的订单发生退款等需要重新统计当月预算
        statMonthBudget(createTime, companyId, orderCategory.getValue());
    }

    /**
     * 预算校验及保存预算
     *
     * @param orderBudgetCheckContract
     * @param orgConsumeRecord
     * @param orderCategory
     * @param budgetType
     * @param consumerId
     * @param consumerName
     */
    @Transactional(transactionManager = "fenbeitong", rollbackFor = Exception.class)
    protected void checkAndSaveOrgConsumeRecord(OrderBudgetCheckContract orderBudgetCheckContract, OrgConsumeRecord orgConsumeRecord, BizType orderCategory, BudgetType budgetType, String consumerId, String consumerName) {
        RedisTemplate redisTemp = redisService.getRedisTemplate();
        String lockKey = MessageFormat.format(RedisKeyConstant.BUDGET_OPERATE_REDIS_KEY, consumerId);
        logger.info("预算保存等待锁..., consumerId:{}", consumerId);
        Long locktime = 0L;
        try {
            locktime = RedisDistributionLock.lockWaitTimeOut(lockKey, redisTemp);
            logger.info("预算保存获取锁成功, consumerId:{}", consumerId);
            // 1.用车入预算不限制 2.还款(金额负值)入预算不限制 3.新预算入预算不限制
            if (orderCategory != BizType.Taxi && orgConsumeRecord.getAmount().compareTo(BigDecimal.ZERO) > 0) {
                TravelOnBusiOrderRuleCheckResult travelOrderBudgetCheckResult = iOrderCheckService.categoryOrderBudgetCheck(orderBudgetCheckContract, orderCategory.getValue(), budgetType.getValue(), consumerId);
                logger.info("预算校验返回结果, ErrCode:{}, ErrMsg:{}", travelOrderBudgetCheckResult.getErrCode(), travelOrderBudgetCheckResult.getErrMsg());
                if (GlobalResponseCode.Success.getCode() != travelOrderBudgetCheckResult.getErrCode()) {
                    if (travelOrderBudgetCheckResult.getErrCode() != GlobalResponseCode.OrderCheckOverBudgetCanSubmit.getCode()) {
                        throw new SaasException(GlobalResponseCode.InnerError, travelOrderBudgetCheckResult.getErrMsg());
                    }
                    if (travelOrderBudgetCheckResult.getPersonalPay()) {
                        throw new SaasException(GlobalResponseCode.InnerError, travelOrderBudgetCheckResult.getErrMsg());
                    }
                }
            }
            // 保存消费记录
            orgConsumeRecord.setConsumerType(budgetType.getValue());
            orgConsumeRecord.setConsumerId(consumerId);
            orgConsumeRecord.setConsumerName(consumerName);
            orgConsumeRecordMapper.insertSelective(orgConsumeRecord);
            logger.info("【checkAndSaveOrgConsumeRecord】预算校验及保存预算成功");
        } catch (Exception e) {
            logger.error("【checkAndSaveOrgConsumeRecord】预算校验及保存预算异常", e);
            if (e instanceof SaasException) {
                throw e;
            }
            throw new SaasException(GlobalResponseCode.InnerError, CoreLanguage.Common_Exception_ServerErrorRetry.getMessage());
        } finally {
            //redis解锁
            RedisDistributionLock.unlock(lockKey, locktime, redisTemp);
            logger.info("预算保存释放锁, consumerId:{}", consumerId);
        }
    }

    /**
     * 批量迁移数据
     */
    @Override
    public void insertOrgConsumeRecord() {
        Date overTime = DateUtils.parse("2019-12-31 23:59:59", "yyyy-MM-dd HH:mm:ss");
        List<OrgConsumeRecord> orgConsumeRecordList = orgConsumeRecordExtMapper.queryOrgConsumeRecordNum(overTime);
        if (CollectionUtils.isEmpty(orgConsumeRecordList)) {
            logger.info("无预算数据需要处理");
        }
        List<OrgConsumeRecordLog> orgConsumeRecordLogList = Lists.newArrayList();
        for (OrgConsumeRecord orgConsumeRecord : orgConsumeRecordList) {
            OrgConsumeRecordLog orgConsumeRecordLog = new OrgConsumeRecordLog();
            //id
            orgConsumeRecordLog.setId(orgConsumeRecord.getId());
            //amount
            orgConsumeRecordLog.setAmount(orgConsumeRecord.getAmount());
            //companyId
            orgConsumeRecordLog.setCompanyId(orgConsumeRecord.getCompanyId());
            //consumeCategory
            orgConsumeRecordLog.setConsumeCategory(orgConsumeRecord.getConsumeCategory());
            //consumerId
            orgConsumeRecordLog.setConsumerId(orgConsumeRecord.getConsumerId());
            //consumerName
            orgConsumeRecordLog.setConsumerName(orgConsumeRecord.getConsumerName());
            //consumerType
            orgConsumeRecordLog.setConsumerType(orgConsumeRecord.getConsumerType());
            //requestTime
            orgConsumeRecordLog.setRequestTime(orgConsumeRecord.getRequestTime());
            //isLatest
            orgConsumeRecordLog.setIsLatest(orgConsumeRecord.getIsLatest());
            //createTime
            orgConsumeRecordLog.setCreateTime(orgConsumeRecord.getCreateTime());
            //orderId
            orgConsumeRecordLog.setOrderId(orgConsumeRecord.getOrderId());
            //preOrderId
            orgConsumeRecordLog.setPreOrderId(orgConsumeRecord.getPreOrderId());
            //paramJson
            orgConsumeRecordLog.setParamJson(orgConsumeRecord.getParamJson());
            //originOrderId
            orgConsumeRecordLog.setOriginOrderId(orgConsumeRecord.getOriginOrderId());
            orgConsumeRecordLogList.add(orgConsumeRecordLog);
        }
        orgConsumeRecordLogExtMapper.batchInsertOrgConsumeRecordLog(orgConsumeRecordLogList);
        List<Long> idList = orgConsumeRecordList.stream().map(orgConsumeRecord -> orgConsumeRecord.getId()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(idList)) {
            OrgConsumeRecordExample orgConsumeRecordExample = new OrgConsumeRecordExample();
            orgConsumeRecordExample.createCriteria().andIdIn(idList);
            orgConsumeRecordMapper.deleteByExample(orgConsumeRecordExample);
        }
    }

    /**
     * 统计预算
     *
     * @param orderTime
     * @param companyId
     * @param bizCategory
     */
    private void statMonthBudget(Date orderTime, String companyId, Integer bizCategory) {
        try {
            CheckUtils.create()
                    .addCheckNull(orderTime, CoreLanguage.Common_Exception_OrderDateTimeNotEmpty.getMessage())
                    .addCheckBlank(companyId, CoreLanguage.Common_Exception_CompanyIdNotEmpty.getMessage())
                    .addCheckNull(bizCategory, CoreLanguage.Common_Exception_BusinessTypeNotEmpty.getMessage())
                    .check();

            // 分隔时间点 前推n-1月 1号0点
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.MONTH, -SaasBudgetConstant.BUDGET_STAT_PERIOD_MONTH + 1);
            calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), 1, 0, 0, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            Date cutPoint = calendar.getTime();
            // 在分隔点之前 需要重新统计该公司当月该场景的预算
            if (orderTime.before(cutPoint)) {
                // 异步执行
                ThreadPoolUtils.getExecutorService().execute(() ->
                    iBudgetService.executeMonthStat(DateUtils.format(orderTime, DateUtils.FORMAT_MONTH), companyId, bizCategory)
                );
            }
        } catch (Exception e) {
            logger.error("统计预算异常", e);
        }
    }

    /**
     * 三方对接预算校验
     *
     * @param companyId
     * @param employeeId
     * @param orderCategory
     * @param orderAmount
     * @param costAttributionList
     * @param costAttributionScope
     * @param budgetCostAttrType
     */
    private void thirdCategoryOrderBudgetCheck(String companyId,
                                               String employeeId,
                                               BizType orderCategory,
                                               BigDecimal orderAmount,
                                               List<CostAttributionInfo> costAttributionList,
                                               Integer costAttributionScope,
                                               Integer budgetCostAttrType) {
        // 1.用车入预算不限制 2.还款(金额负值)入预算不限制
        if (orderCategory != BizType.Taxi && orderAmount.compareTo(BigDecimal.ZERO) > 0) {
            CostAttrAndBudgetConf costAttrAndBudgetConf = new CostAttrAndBudgetConf();
            costAttrAndBudgetConf.setCost_attribution_scope(costAttributionScope);
            costAttrAndBudgetConf.setBudget_cost_attr_type(budgetCostAttrType);
            //预算校验
            OrderBudgetCheckContract orderBudgetCheckContract = new OrderBudgetCheckContract();
            orderBudgetCheckContract.setEmployee_id(employeeId);
            orderBudgetCheckContract.setCompany_id(companyId);
            orderBudgetCheckContract.setCostAttributionList(costAttributionList);
            orderBudgetCheckContract.setPrice(orderAmount);
            TravelOnBusiOrderRuleCheckResult result = iOrderCheckService.thirdCategoryOrderBudgetCheck(orderBudgetCheckContract, costAttrAndBudgetConf, OrderCategory.getByKey(orderCategory.getValue()));
            if (GlobalResponseCode.Success.getCode() != result.getErrCode()) {
                throw new SaasException(GlobalResponseCode.InnerError, CoreLanguage.Common_Exception_ThirdBudgetNotEnough.getMessage());
            }
        }
    }
}
