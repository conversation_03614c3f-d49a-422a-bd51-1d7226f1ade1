package com.fenbeitong.saas.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.CheckUtils;
import com.fenbeitong.finhub.common.utils.DistanceUtils;
import com.fenbeitong.finhub.common.utils.EventParams;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.harmony.city.contrail.dto.city.FullPathAreaDTO;
import com.fenbeitong.harmony.city.contrail.service.city.IAreaService;
import com.fenbeitong.noc.api.service.common.OrderSaasResDTO;
import com.fenbeitong.noc.api.service.takeaway.model.dto.req.CompanyPayStatisticsReqRpcDTO;
import com.fenbeitong.noc.api.service.takeaway.model.dto.resp.CompanyPayStatisticsResRpcDTO;
import com.fenbeitong.noc.api.service.takeaway.service.ISaaSStatisticsService;
import com.fenbeitong.noc.api.service.takeaway.service.ITakeawayOrderSearchService;
import com.fenbeitong.saas.api.model.dto.apply.amount.AmountExceedCheckRes;
import com.fenbeitong.saas.api.model.dto.apply.amount.TotalEstimatedCheckDto;
import com.fenbeitong.saas.api.model.dto.reason.ReasonConfig;
import com.fenbeitong.saas.core.common.DynamicDataSourceDecision;
import com.fenbeitong.saas.core.common.base.SaasException;
import com.fenbeitong.saas.core.common.constant.*;
import com.fenbeitong.saas.core.common.constant.apply.ApplyRequireEnum;
import com.fenbeitong.saas.core.contract.apply.CheckApplyEstimatedAmountReq;
import com.fenbeitong.saas.core.contract.cost.CostCheckVO;
import com.fenbeitong.saas.core.contract.messagesettings.CostAttrAndBudgetConf;
import com.fenbeitong.saas.core.contract.order.check.*;
import com.fenbeitong.saas.core.contract.order.check.takeaway.TakeawayCostCheckDto;
import com.fenbeitong.saas.core.contract.rule.LimitPayTipContract;
import com.fenbeitong.saas.core.contract.setup.MessageSetupVO;
import com.fenbeitong.saas.core.dao.fenbeitong.TakeawayInterceptRecordMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.TakeawayRuleMapper;
import com.fenbeitong.saas.core.dao.saasplus.CustomFormApplyConfigMapper;
import com.fenbeitong.saas.core.language.CoreLanguage;
import com.fenbeitong.saas.core.model.enums.CompanySettingType;
import com.fenbeitong.saas.core.model.enums.EmployeeStatusEnum;
import com.fenbeitong.saas.core.model.enums.OrderCategory;
import com.fenbeitong.saas.core.model.enums.apply.ApplyStatus;
import com.fenbeitong.saas.core.model.enums.apply.ApplyType;
import com.fenbeitong.saas.core.model.enums.apply.TotalEstimatedLimitType;
import com.fenbeitong.saas.core.model.enums.booking.BookingConfigEnum;
import com.fenbeitong.saas.core.model.enums.booking.DinnerTakeawayBookingTypeEnum;
import com.fenbeitong.saas.core.model.enums.message.BizType;
import com.fenbeitong.saas.core.model.enums.reason.ReasonType;
import com.fenbeitong.saas.core.model.enums.rule.DayTempType;
import com.fenbeitong.saas.core.model.enums.rule.TakeawayVendor;
import com.fenbeitong.saas.core.model.enums.rule.TravelExceedType;
import com.fenbeitong.saas.core.model.fenbeitong.TakeawayInterceptRecord;
import com.fenbeitong.saas.core.model.saasplus.TakeawayManageSetting;
import com.fenbeitong.saas.core.model.fenbeitong.TakeawayRule;
import com.fenbeitong.saas.core.model.fenbeitong.TakeawayRuleExample;
import com.fenbeitong.saas.core.model.saas.*;
import com.fenbeitong.saas.core.service.*;
import com.fenbeitong.saas.core.service.apply.TripEstimateService;
import com.fenbeitong.saas.core.service.apply.amount.ApplyAmountService;
import com.fenbeitong.saas.core.service.order.TakeawayOrderService;
import com.fenbeitong.saas.core.service.setup.SetupService;
import com.fenbeitong.saas.core.utils.icon.IconChangeUtil;
import com.fenbeitong.saas.core.utils.tools.ConvertUtils;
import com.fenbeitong.saas.core.utils.tools.DateTimeTool;
import com.fenbeitong.saas.core.utils.tools.HostPropertyConfigTool;
import com.fenbeitong.saas.core.utils.tools.HttpTool;
import com.fenbeitong.saas.core.utils.tools.IDTool;
import com.fenbeitong.saas.core.utils.tools.VersionTool;
import com.fenbeitong.saasplus.api.model.dto.custform.CustomFormApplyControlItemDTO;
import com.fenbeitong.saasplus.api.model.dto.rule.EmployeeBaseRuleV2;
import com.fenbeitong.saasplus.api.model.dto.finance.CustomDimension;
import com.fenbeitong.saasplus.api.model.dto.rule.TakeawayAuthContract;
import com.fenbeitong.saasplus.api.model.dto.rule.TakeawayContract;
import com.fenbeitong.saasplus.api.model.enums.custform.SwitchTypeEnum;
import com.fenbeitong.saasplus.api.service.custform.ICustomFormService;
import com.fenbeitong.saasplus.api.service.rule.IBaseTakeawayRuleExtService;
import com.fenbeitong.usercenter.api.model.dto.calendar.CalendarRpcVO;
import com.fenbeitong.usercenter.api.model.dto.company.MeiTuanTakeawayRuleDTO;
import com.fenbeitong.usercenter.api.model.dto.company.SceneAuthDto;
import com.fenbeitong.usercenter.api.model.dto.company.SkTakeawayAuthDto;
import com.fenbeitong.usercenter.api.model.dto.company.TakeawayAuthDto;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.fenbeitong.usercenter.api.model.enums.privilege.CodeTypeEnums;
import com.fenbeitong.usercenter.api.model.enums.privilege.CustomCodeType;
import com.fenbeitong.usercenter.api.model.po.rule.EmployeeBaseRule;
import com.fenbeitong.usercenter.api.service.calendar.RpcCompanyCalendarService;
import com.fenbeitong.usercenter.api.service.company.ICompanyService;
import com.fenbeitong.usercenter.api.service.privilege.IPrivilegeService;
import com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeRuleService;
import com.google.common.collect.Lists;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.DateUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.StrUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 *
 * <AUTHOR>
 * @date 2018/12/29
 */
@Slf4j
@Service
public class TakeawayCheckV3Service implements ITakeawayCheckV3Service {

    private static final String URL_GET_CITY_CODE = HostPropertyConfigTool.HOST_HARMONY + "/business/common/map"
        + "/areas/getCityCodeByName";

    private static final Logger logger = LoggerFactory.getLogger(TakeawayCheckV3Service.class);
    @Autowired
    private IBaseOrganizationService baseOrganizationService;
    @Autowired
    private IOrderCheckService iOrderCheckService;
    @Autowired
    private IBaseTakeawayRuleExtService iBaseTakeawayRuleExtService;
    @Autowired
    private ISaaSStatisticsService iSaaSStatisticsService;
    @Autowired
    private TakeawayInterceptRecordMapper takeawayInterceptRecordMapper;
    @Autowired
    private ICompanyService iCompanyService;
    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyOrderMapper applyOrderMapper;
    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyTripInfoMapper applyTripInfoMapper;
    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyTripApplicateMapper applyTripApplicateMapper;
    @Autowired
    private IMessageSetupService iMessageSetupService;
    @Autowired
    private ICustomReasonService customReasonService;
    @Autowired
    private ITakeawayOrderSearchService iTakeawayOrderSearchService;
    @Autowired
    private IPrivilegeService iPrivilegeService;
    @Autowired
    private RpcCompanyCalendarService rpcCompanyCalendarService;
    @Autowired
    private ICustomFieldsService iCustomFieldsService;
    @Autowired
    private com.fenbeitong.saasplus.api.service.messageSetup.IMessageSetupService iMessageSetupRpcService;
    @Autowired
    private CurrencyCheckServiceImpl currencyCheckService;
    @Autowired
    private IBaseEmployeeRuleService iBaseEmployeeRuleService;
    @Autowired
    private TakeawayRuleMapper takeawayRuleMapper;
    @Autowired
    private ITakeawayCheckV4Service takeawayCheckV4Service;
    @Autowired
    private IMessageSetupService messageSetupService;
    @Autowired
    private ApplyAmountService applyAmountService;
    @Autowired
    private SetupService setupService;
    @Resource
    ICustomFormService iCustomFormService;
    @Autowired
    private IAreaService areaService;
    @Autowired
    private ApplyV5ServiceImpl applyV5Service;

    @Autowired
    private CustomFormApplyConfigMapper customFormApplyConfigMapper;

    @Autowired
    private TripEstimateService tripEstimateService;

    @Autowired
    private TakeawayOrderService takeawayOrderService;

    /**
     * 校验外卖订单规则
     *
     * @param takeawayOrderCheckReqContract
     * @param clientVersion
     * @return
     */
    @Override
    public TempOrderCheckResContract takeawayOrderCheck(TakeawayOrderCheckReqV2Contract takeawayOrderCheckReqContract, String clientVersion) {
        DynamicDataSourceDecision.determineDataSource(takeawayOrderCheckReqContract.getCompany_id());
        logger.info("【预订人模式】校验外卖订单规则请求参数V3：{}", JsonUtils.toJson(takeawayOrderCheckReqContract));
        // 返回结果
        TakeawayOrderCheckResContract resContract = new TakeawayOrderCheckResContract();

        // 强制版本升级
        boolean needUpdate = iCustomFieldsService.checkBudget(takeawayOrderCheckReqContract.getCompany_id(), ReasonType.ORDER_MEISHI, clientVersion);
        if (needUpdate) {
            resContract.setErr_code(GlobalResponseCode.ApplyCenterAlert.getCode());
            resContract.setErr_msg(GlobalResponseCode.ApplyCenterAlert.getMsg());
            resContract.setErr_type(1);
            return resContract;
        }
        // 检查费用归属灰度开关及客户端版本号
        Integer costAttributionNewSwitch = iMessageSetupRpcService.queryCostAttributionNewSwitch(takeawayOrderCheckReqContract.getCompany_id());
        log.info("[ takeawayOrderCheck ] costAttributionNewSwitch={}", costAttributionNewSwitch);
        if (CommonSwitchConstant.OPEN.equals(costAttributionNewSwitch)
                && VersionTool.lessThan(clientVersion, ClientVersionConstant.COST_ATTRIBUTION_UPDATE_CLIENT_VERSION)) {
            resContract.setErr_code(GlobalResponseCode.OrderCheckUpdateTost.getCode());
            resContract.setErr_msg(GlobalResponseCode.OrderCheckUpdateTost.getMsg());
            resContract.setErr_type(1);
            return resContract;
        }
        // 校验权限问题
        TakeawayOrderRuleCheckResult ruleCheckResult = takeawayOrderRuleCheckResult(takeawayOrderCheckReqContract, clientVersion);
        logger.info("校验权限结果：{}", JsonUtils.toJson(ruleCheckResult));
        resContract.setErr_code(ruleCheckResult.getErrCode());
        resContract.setErr_msg(ruleCheckResult.getErrMsg());
        resContract.setExceed_msg(ruleCheckResult.getExceed_msg());
        resContract.setApply_id(ruleCheckResult.getApplyId());
        resContract.setRule_info(ruleCheckResult.getRule_info());
        resContract.setCost_attribution_scope(ruleCheckResult.getCost_attribution_scope());
        resContract.setBudget_cost_attr_type(ruleCheckResult.getBudget_cost_attr_type());
        resContract.setPay_model(ruleCheckResult.getPayModel());
        resContract.setCompany_pay_price(ruleCheckResult.getCompanyPayPrice());
        resContract.setPersonal_pay_price(ruleCheckResult.getPersonalPayPrice());
        resContract.setReimbursable_price(ruleCheckResult.getReimbursablePrice());
        resContract.setUnreimbursable_price(ruleCheckResult.getUnreimbursablePrice());
        resContract.setAmount_compliance(ruleCheckResult.getAmountCompliance());
        resContract.setAmount_non_compliance(ruleCheckResult.getAmountNonCompliance());
        resContract.setCoupon_used_amount(ruleCheckResult.getCoupon_used_amount());
        if (ruleCheckResult.getIs_exceed() || (takeawayOrderCheckReqContract.getExceed_submit() != null && takeawayOrderCheckReqContract.getExceed_submit())) {
            resContract.setIs_exceed(true);
        }
        if (ruleCheckResult.getErrCode() == GlobalResponseCode.Success.getCode()) {
            // 自定义字段转化
            List<CustomDimension> customDimensionList =
                ConvertUtils.convertToCustomDimension(takeawayOrderCheckReqContract.getCustomer_field_setting_list());

            String costCompanyId = StringUtils.isNotBlank(takeawayOrderCheckReqContract.getCost_company_id()) ? takeawayOrderCheckReqContract.getCost_company_id() : takeawayOrderCheckReqContract.getCompany_id();
            TempOrderCheckResContract costResult = iOrderCheckService.saveCost(takeawayOrderCheckReqContract.getOrder_id(),
                    costCompanyId,
                    takeawayOrderCheckReqContract.getEmployee_id(),
                    takeawayOrderCheckReqContract.getApply_id(),
                    takeawayOrderCheckReqContract.getTrip_id(),
                    BudgetCategoryTypeEnum.Takeaway,
                    takeawayOrderCheckReqContract.getOrder_price(),
                    takeawayOrderCheckReqContract.getCost_info(),
                    takeawayOrderCheckReqContract.getCostInfoString(),
                    clientVersion, customDimensionList,takeawayOrderCheckReqContract.getOrder_time());
            if (costResult.getErr_code() != GlobalResponseCode.Success.getCode()) {
                //特殊处理
                if (costResult.getErr_code() == GlobalResponseCode.OrderCheckOverBudgetCanSubmit.getCode()) {
                    resContract.setErr_code(TemporaryResponseCode.OrderCheckOverBudgetCanSubmit.getCode());
                } else if (costResult.getErr_code() == GlobalResponseCode.OrderCheckOverBudgetCanNotSubmit.getCode()) {
                    resContract.setErr_code(TemporaryResponseCode.OrderCheckOverBudgetCanNotSubmit.getCode());
                } else {
                    resContract.setErr_code(costResult.getErr_code());
                }
                resContract.setErr_msg(costResult.getErr_msg());
            } else {
                resContract.setCost_id(costResult.getCost_id());
            }
        }
        //添加拦截记录
        if (ruleCheckResult.getInterceptFlag() && GlobalResponseCode.Success.getCode() != ruleCheckResult.getErrCode()) {
            initTakeawayInterceptRecord(takeawayOrderCheckReqContract, ruleCheckResult);
        }
        // 错误信息类型
        TempOrderCheckResContract orderCheckResContract = takeawayCheckResContractCommon(resContract, ruleCheckResult);
        // 记录下单校验日志
        iOrderCheckService.saveOrderCheckLog(
                takeawayOrderCheckReqContract.getCompany_id(),
                takeawayOrderCheckReqContract.getEmployee_id(),
                CategoryTypeEnum.Takeaway.getCode(),
                clientVersion,
                JsonUtils.toJson(takeawayOrderCheckReqContract),
                JsonUtils.toJson(orderCheckResContract),
                ObjUtils.toString(orderCheckResContract.getErr_code()),
                ruleCheckResult.getSnapshotInfo().toJSONString(),
                ruleCheckResult.getExtInfo().toJSONString());
        return orderCheckResContract;
    }

    /**
     * 外卖返回结果处理
     *
     * @param tempOrderCheckResContract
     * @param ruleCheckResult
     * @return
     */
    private TempOrderCheckResContract takeawayCheckResContractCommon(TempOrderCheckResContract tempOrderCheckResContract, TempOrderRuleCheckResult ruleCheckResult) {
        if (tempOrderCheckResContract.getErr_code() == GlobalResponseCode.Success.getCode()) {
            // 成功
            tempOrderCheckResContract.setErr_type(0);
            ErrMsgInfo errMsgInfo1 = ruleCheckResult.getErrMsgInfo();
            if (errMsgInfo1 != null) {
                ErrMsgInfo errMsgInfo = new ErrMsgInfo();
                errMsgInfo.setTitle(SaasOrderConstant.ORDER_OVERRULE_REASON);
                errMsgInfo.setErr_code_list(errMsgInfo1.getErr_code_list());
                tempOrderCheckResContract.setErr_msg_info(errMsgInfo);
            }
        } else if (tempOrderCheckResContract.getErr_code() == GlobalResponseCode.ApplyTakeawayNotAvailable.getCode()) {
            throw new SaasException(tempOrderCheckResContract.getErr_code(), tempOrderCheckResContract.getErr_msg(),
                    FinhubMessageType.TIP_WINDOW, CoreLanguage.Common_Exception_RetrySelectApply.getMessage());
        } else if (tempOrderCheckResContract.getErr_code() == TemporaryResponseCode.OrderCheckExceedNeedReason.getCode()
                || tempOrderCheckResContract.getErr_code() == TemporaryResponseCode.OrderCheckOverBudgetCanSubmit.getCode()
                || tempOrderCheckResContract.getErr_code() == TemporaryResponseCode.OrderCheckOverBudgetCanNotSubmit.getCode()
                || tempOrderCheckResContract.getErr_code() == TemporaryResponseCode.OrderCanNotSubmit.getCode()
                || tempOrderCheckResContract.getErr_code() == TemporaryResponseCode.OrderCheckPersonalPayTip.getCode()
                || tempOrderCheckResContract.getErr_code() == GlobalResponseCode.ApplyPersonalPay.getCode()
                ) {
            // 提示(弹窗)
            tempOrderCheckResContract.setErr_type(2);
            ErrMsgInfo errMsgInfo1 = ruleCheckResult.getErrMsgInfo();
            if (tempOrderCheckResContract.getErr_code() == TemporaryResponseCode.OrderCheckExceedNeedReason.getCode()) {
                if (errMsgInfo1 != null) {
                    ErrMsgInfo errMsgInfo = new ErrMsgInfo();
                    errMsgInfo.setTitle(SaasOrderConstant.ORDER_OVERRULE_REASON);
                    errMsgInfo.setErr_code_list(errMsgInfo1.getErr_code_list());
                    tempOrderCheckResContract.setErr_msg_info(errMsgInfo);
                }
            } else if (tempOrderCheckResContract.getErr_code() == TemporaryResponseCode.OrderCanNotSubmit.getCode()) {
                if (errMsgInfo1 != null) {
                    ErrMsgInfo errMsgInfo = new ErrMsgInfo();
                    errMsgInfo.setTitle(SaasOrderConstant.ORDER_PROHIBIT_TAKEAWAY);
                    errMsgInfo.setErr_code_list(errMsgInfo1.getErr_code_list());
                    tempOrderCheckResContract.setErr_msg_info(errMsgInfo);
                }
            } else {
                setErrMsgInfo(tempOrderCheckResContract);
            }
        } else {
            // 异常(toast)
            tempOrderCheckResContract.setErr_type(1);
            setErrMsgInfo(tempOrderCheckResContract);
        }
        return tempOrderCheckResContract;
    }

    private void setErrMsgInfo(TempOrderCheckResContract tempOrderCheckResContract) {
        ErrMsgInfo errMsgInfo = new ErrMsgInfo();
        errMsgInfo.setErr_code_list(new ArrayList<>());
        errMsgInfo.setTitle(tempOrderCheckResContract.getErr_msg());
        tempOrderCheckResContract.setErr_msg_info(errMsgInfo);
    }

    /**
     * 校验规则
     *
     * @param takeawayOrderCheckReqContract
     * @param clientVersion
     * @return
     */
    private TakeawayOrderRuleCheckResult takeawayOrderRuleCheckResult(TakeawayOrderCheckReqV2Contract takeawayOrderCheckReqContract, String clientVersion) {
        BigDecimal totalPrice = takeawayOrderCheckReqContract.getOrder_price();
        BigDecimal couponAmount = ObjUtils.ifNull(takeawayOrderCheckReqContract.getCoupon_amount(), BigDecimal.ZERO);

        /** 费用主体 */
        String costCompanyId = StringUtils.isNotBlank(takeawayOrderCheckReqContract.getCost_company_id())? takeawayOrderCheckReqContract.getCost_company_id() : takeawayOrderCheckReqContract.getCompany_id();
        /** 消费主体 */
        String consumerCompanyId = StringUtils.isNotBlank(takeawayOrderCheckReqContract.getConsumer_company_id())? takeawayOrderCheckReqContract.getConsumer_company_id() : takeawayOrderCheckReqContract.getCompany_id();
        log.info("外卖预定人模式, 消费主体: {}, 费用主体: {}", consumerCompanyId, costCompanyId);

        Integer subSceneId = takeawayOrderCheckReqContract.getSub_scene_id();
        Integer couponExceedPriceSetting = iOrderCheckService.queryCouponExceedPriceSetting(costCompanyId);
        logger.info("couponExceedPriceSetting：{}",couponExceedPriceSetting);
        if (couponExceedPriceSetting == 1) {
            totalPrice = totalPrice.subtract(couponAmount).max(BigDecimal.ZERO);
        }
        // 默认初始化正常
        TakeawayOrderRuleCheckResult checkResult = new TakeawayOrderRuleCheckResult();
        checkResult.setResCode(TemporaryResponseCode.Success);

        String employeeId = takeawayOrderCheckReqContract.getEmployee_id();
        checkResult.setAmountCompliance(totalPrice);
        checkResult.setAmountNonCompliance(BigDecimal.ZERO);
        // 公司支付金额 默认订单金额
        BigDecimal companyPayPrice = totalPrice;
        // 快照信息
        JSONObject snapshotInfo = new JSONObject();
        checkResult.setSnapshotInfo(snapshotInfo);
        // 查询是否个人垫付模式
        Map<String, Boolean> funcMap = iPrivilegeService.queryFunctionMoudle(CodeTypeEnums.CustomAuth.getKey(), consumerCompanyId, employeeId, CustomCodeType.M_ADVANCE_PAYMENT.getKey());
        Boolean advancePayment = funcMap.get(CustomCodeType.M_ADVANCE_PAYMENT.getKey());
        logger.info("[外卖下单校验]，是否个人垫付模式:{}", advancePayment);
        PayModelEnum payModel = advancePayment ? PayModelEnum.PERSONAL_PREPAY : PayModelEnum.COMPANY_PAY;
        checkResult.setPayModel(payModel.getCode());
        snapshotInfo.put("payModel", payModel.getCode());
        // 预订人状态
        EmployeeContract orderEmployee = baseOrganizationService.getEmployee(employeeId, consumerCompanyId);
        logger.info("orderEmployee:{}", JsonUtils.toJson(orderEmployee));
        if (orderEmployee == null || orderEmployee.getStatus() != EmployeeStatusEnum.ACTIVE.getValue()) {
            checkResult.setResCode(TemporaryResponseCode.OrderCheckEmployeeNotActive);
            return checkResult;
        }
        // 校验订单事由及自定义字段
        TempOrderRuleCheckResult result = iOrderCheckService.checkOrderReasonAndCustomFields(costCompanyId, CategoryTypeEnum.Takeaway, takeawayOrderCheckReqContract.getOrder_reason(), takeawayOrderCheckReqContract.getOrder_reason_comment(), takeawayOrderCheckReqContract.getOrder_custom_fields(),null,null);
        logger.info("校验订单事由及自定义字段result:{}", JsonUtils.toJson(result));
        if (result.getErrCode() != TemporaryResponseCode.Success.getCode()) {
            checkResult.setResCode(result.getErrCode(), result.getErrMsg());
            return checkResult;
        }
        // 企业外卖权限
        Integer vendorCode = takeawayOrderCheckReqContract.getVendor_code();
        TakeawayVendor vendor = TakeawayVendor.getEnum(vendorCode);
        if (ObjUtils.isEmpty(vendor)) {
            checkResult.setResCode(TemporaryResponseCode.OrderTakeawayUnkownVendor);
            return checkResult;
        }
        SceneAuthDto sceneAuthDto = iCompanyService.querySecenAuth(consumerCompanyId, employeeId);
        logger.info("sceneAuthDto:{}", JsonUtils.toJson(sceneAuthDto));
        if (ObjUtils.isEmpty(sceneAuthDto)) {
            checkResult.setResCode(TemporaryResponseCode.OrderTakeawayCheckCompanyNoAuth);
            return checkResult;
        }
        if (vendor == TakeawayVendor.Eleme) {
            TakeawayAuthDto takeawayAuthDto = sceneAuthDto.getTakeawayAuth();
            if (ObjUtils.isEmpty(takeawayAuthDto)) {
                checkResult.setResCode(TemporaryResponseCode.OrderTakeawayCheckCompanyNoAuth);
                return checkResult;
            }
            if (takeawayAuthDto.getPublicTakeaway() != 1) {
                checkResult.setResCode(TemporaryResponseCode.OrderTakeawayCheckCompanyNoAuth);
                return checkResult;
            }
        } else if (vendor == TakeawayVendor.Meituan) {
            SkTakeawayAuthDto takeawayAuthDto = sceneAuthDto.getSkTakeawayAuth();
            if (ObjUtils.isEmpty(takeawayAuthDto)) {
                checkResult.setResCode(TemporaryResponseCode.OrderTakeawayCheckCompanyNoAuth);
                return checkResult;
            }
            if (takeawayAuthDto.getPublicSkTakeaway() != 1) {
                checkResult.setResCode(TemporaryResponseCode.OrderTakeawayCheckCompanyNoAuth);
                return checkResult;
            }
        }
        else if (vendor == TakeawayVendor.MeituanH5) {
            MeiTuanTakeawayRuleDTO meiTuanTakeawayRuleDTO = sceneAuthDto.getMeiTuanTakeawayRuleDTO();
            if (ObjUtils.isEmpty(meiTuanTakeawayRuleDTO)) {
                checkResult.setResCode(TemporaryResponseCode.OrderTakeawayCheckCompanyNoAuth);
                return checkResult;
            }
            if (meiTuanTakeawayRuleDTO.getPublicMeiTuanRule() != 1) {
                checkResult.setResCode(TemporaryResponseCode.OrderTakeawayCheckCompanyNoAuth);
                return checkResult;
            }
        }
        // 员工外卖权限
        EmployeeBaseRuleV2 takeawayAuthContract = queryEmployeeTakeawayRule(employeeId, consumerCompanyId, subSceneId);
//        TakeawayAuthContract takeawayAuthContract = queryEmployeeTakeawayRule(employeeId, companyId, subSceneId);
        logger.info("员工外卖权限:{}", JsonUtils.toJson(takeawayAuthContract));
//        checkResult.setTakeawayRule(takeawayAuthContract);
        snapshotInfo.put("authInfo", takeawayAuthContract);

        /**
         * ruleSwitch 使用权限：true:有权限 false：无权限
         * ruleFlag   规则是否限制：true：限制 false：无限制
         **/
        if (ObjUtils.isNull(takeawayAuthContract) || !takeawayAuthContract.getRuleSwitch()) {
            checkResult.setResCode(TemporaryResponseCode.OrderTakeawayCheckEmployeeNoAuth);
            return checkResult;
        }
        //企业余额校验(余额不足或者账户被锁定)
        TravelOnBusiOrderRuleCheckResult checkCompanyAccountResult = iOrderCheckService.checkCompanyAccount(costCompanyId, takeawayOrderCheckReqContract.getOrder_price(), OrderCategory.Takeaway.getKey(), advancePayment);
        logger.info("checkCompanyAccountResult:{}", JsonUtils.toJson(checkCompanyAccountResult));
        if (GlobalResponseCode.Success.getCode() != checkCompanyAccountResult.getErrCode()) {
            FinhubLogger.event(costCompanyId, EventParams.build(**********, false).put("account_sub_type", 2));
            checkResult.setResCode(checkCompanyAccountResult.getErrCode(), checkCompanyAccountResult.getErrMsg());
            return checkResult;
        }
        CostAttrAndBudgetConf costAttrAndBudgetConf = iMessageSetupService.queryCostAttrAndBudgetConf(costCompanyId);
        logger.info("costAttrAndBudgetConf:{}", JsonUtils.toJson(costAttrAndBudgetConf));
        // 老版本配置"部门和项目"兼容为"部门或项目"
        if (costAttrAndBudgetConf.getCost_attribution_scope() == CostAttributionScopeEnum.DEPT_AND_PROJ.getCode()
                && ObjUtils.isNotBlank(clientVersion) && VersionTool.lessThan(clientVersion, "4.2.0")) {
            costAttrAndBudgetConf.setCost_attribution_scope(CostAttributionScopeEnum.DEPT_OR_PROJ.getCode());
        }
        checkResult.setCost_attribution_scope(costAttrAndBudgetConf.getCost_attribution_scope());
        checkResult.setBudget_cost_attr_type(costAttrAndBudgetConf.getBudget_cost_attr_type());


        // 检查费用归属灰度开关 进行灰度校验
        Integer costAttributionNewSwitch = iMessageSetupRpcService.queryCostAttributionNewSwitch(costCompanyId);
        log.info("costAttributionNewSwitch:{}",costAttributionNewSwitch);
        if (CommonSwitchConstant.OPEN.equals(costAttributionNewSwitch)){
            CostCheckVO costCheckVO = new CostCheckVO();
            costCheckVO.setCostInfo(takeawayOrderCheckReqContract.getCost_info());
            costCheckVO.setCostInfoString(takeawayOrderCheckReqContract.getCostInfoString());
            costCheckVO.setUserIdList(takeawayOrderCheckReqContract.getUser_id_list());
            costCheckVO.setCategory(takeawayOrderCheckReqContract.getCategory());
            costCheckVO.setCompanyId(costCompanyId);
            costCheckVO.setApplyId(takeawayOrderCheckReqContract.getApply_id());
            costCheckVO.setSceneCode(takeawayOrderCheckReqContract.getSub_scene_id());
            currencyCheckService.newCheckCostAttribution(costCheckVO);
        }else{
            TravelOnBusiOrderRuleCheckResult checkCompanyCostAttributionResult = iOrderCheckService.checkCostInfo(costCompanyId, takeawayOrderCheckReqContract.getCost_info(), costAttrAndBudgetConf, clientVersion);
            logger.info("checkCompanyCostAttributionResult:{}", JsonUtils.toJson(checkCompanyCostAttributionResult));
            if (GlobalResponseCode.Success.getCode() != checkCompanyCostAttributionResult.getErrCode()) {
                checkResult.setResCode(checkCompanyCostAttributionResult.getErrCode(), checkCompanyCostAttributionResult.getErrMsg());
                return checkResult;
            }
        }

        //预算校验
        OrderBudgetCheckV2Contract orderBudgetCheckContract = new OrderBudgetCheckV2Contract();
        orderBudgetCheckContract.setEmployee_id(takeawayOrderCheckReqContract.getEmployee_id());
        orderBudgetCheckContract.setCompany_id(costCompanyId);
        orderBudgetCheckContract.setCost_info(takeawayOrderCheckReqContract.getCost_info());
        orderBudgetCheckContract.setPrice(takeawayOrderCheckReqContract.getOrder_price());
        orderBudgetCheckContract.setForce_sumbit(takeawayOrderCheckReqContract.getForce_sumbit());
        TravelOnBusiOrderRuleCheckResult travelOrderBudgetCheckResult = iOrderCheckService.categoryOrderBudgetCheckV2(orderBudgetCheckContract, costAttrAndBudgetConf, OrderCategory.Takeaway, clientVersion);
        logger.info("travelOrderBudgetCheckResult:{}", JsonUtils.toJson(travelOrderBudgetCheckResult));
        snapshotInfo.put("budgetList", travelOrderBudgetCheckResult.getBudgetSettingAndUseList());
        if (GlobalResponseCode.Success.getCode() != travelOrderBudgetCheckResult.getErrCode()) {
            //特殊处理
            if (travelOrderBudgetCheckResult.getErrCode() == GlobalResponseCode.OrderCheckOverBudgetCanSubmit.getCode()) {
                checkResult.setResCode(TemporaryResponseCode.OrderCheckOverBudgetCanSubmit.getCode(), travelOrderBudgetCheckResult.getErrMsg());
            } else if (travelOrderBudgetCheckResult.getErrCode() == GlobalResponseCode.OrderCheckOverBudgetCanNotSubmit.getCode()) {
                checkResult.setResCode(TemporaryResponseCode.OrderCheckOverBudgetCanNotSubmit.getCode(), travelOrderBudgetCheckResult.getErrMsg());
            } else {
                checkResult.setResCode(travelOrderBudgetCheckResult.getErrCode(), travelOrderBudgetCheckResult.getErrMsg());
            }
            return checkResult;
        }
        BigDecimal budgetAmountCompliance = travelOrderBudgetCheckResult.getAmountCompliance();
        BigDecimal budgetAmountNonCompliance = travelOrderBudgetCheckResult.getAmountNonCompliance();
        checkResult.setAmountCompliance(budgetAmountCompliance);
        checkResult.setAmountNonCompliance(budgetAmountNonCompliance);
        logger.info("[外卖下单校验]，预算合规金额:{}, 预算超规金额:{}", budgetAmountCompliance, budgetAmountNonCompliance);

        //审批单
        String applyId = takeawayOrderCheckReqContract.getApply_id();
        String tripId = takeawayOrderCheckReqContract.getTrip_id();
        boolean hasApply = false;
        if (ObjUtils.isNotBlank(applyId)) {
            hasApply = true;
            applyOrderCheck(takeawayOrderCheckReqContract, takeawayAuthContract, checkResult, applyId, tripId, advancePayment,couponExceedPriceSetting,couponAmount);
            log.info("apply check result:{}", JsonUtils.toJson(checkResult));
            // 二次提交 直接返回
            if (ObjUtils.toBoolean(takeawayOrderCheckReqContract.getApply_personalpay_sumbit(), false)) {
                return checkResult;
            }
            // 审批校验通过 或 仅预估费超规 继续走规则校验
            if (checkResult.getErrCode() != TemporaryResponseCode.Success.getCode()
                    && checkResult.getErrCode() != GlobalResponseCode.ApplyPersonalPay.getCode()
                    && checkResult.getErrCode() != GlobalResponseCode.ApplyPersonalPay2.getCode()) {
                return checkResult;
            }
        }

        //个人支付
        Boolean personalPay = takeawayAuthContract.getPersonalPay();

        //节省规则信息
        CommonSavingRuleInfo ruleInfo = new CommonSavingRuleInfo();
        ruleInfo.setRule_limit(takeawayAuthContract.getRuleFlag());
        //规则校验
        if (takeawayAuthContract.getRuleFlag()) {
            TakeawayContract takeawayRule = takeawayAuthContract.getRuleInfo();
            if (takeawayRule == null) {
                checkResult.setResCode(TemporaryResponseCode.OrderTakeawayCheckRuleNotExist);
                return checkResult;
            }

            if(null != takeawayRule.getIsLimitLocation() && takeawayRule.getIsLimitLocation() == 1){
                if(ObjUtils.isNotBlank(clientVersion) && VersionTool.lessThan(clientVersion, "5.2.57")){
                    //版本检查
                    checkResult.setResCode(TemporaryResponseCode.OrderCheckOrderVersionAlert);
                    return checkResult;
                }

                BigDecimal limitLocationRangeKm = null == takeawayRule.getLimitLocationRange() ? new BigDecimal("0.1") : takeawayRule.getLimitLocationRange();
                Double lat = takeawayOrderCheckReqContract.getAddress_lat();
                Double lng = takeawayOrderCheckReqContract.getAddress_lng();
                Double bookerCurrentLat = takeawayOrderCheckReqContract.getBooker_current_latitude();
                Double bookerCurrentLng = takeawayOrderCheckReqContract.getBooker_current_longitude();
                log.info("送餐地址和预订人所在地址距离：order=[{},{}], booker=[{},{}]", lat, lng, bookerCurrentLat, bookerCurrentLng);
                if(null == lat || null == lng || null == bookerCurrentLat || null == bookerCurrentLng){
                    checkResult.setResCode(TemporaryResponseCode.OrderTakeawayCheckRuleCurrentPositionLost);
                    return checkResult;
                }

                Double des = DistanceUtils.getDistance(lng, lat, bookerCurrentLng, bookerCurrentLat);
                log.info("送餐地址和预订人所在地址距离：{}，order=[{},{}], booker=[{},{}]",des, lat, lng, bookerCurrentLat, bookerCurrentLng);
                if (new BigDecimal(des).compareTo(limitLocationRangeKm.multiply(new BigDecimal("1000"))) > 0) {
                    checkResult.setErrMsg(StrUtils.formatString(TemporaryResponseCode.OrderTakeawayCheckRuleCurrentPosition.getMsg(), limitLocationRangeKm));
                    checkResult.setErrCode(TemporaryResponseCode.OrderTakeawayCheckRuleCurrentPosition.getCode());
                    return checkResult;
                }
            }


            if (!hasApply) {
                Integer requireApplyOrder = takeawayRule.getRequireApplyOrder();
                if(ApplyRequireEnum.APPLY_ORDER_REQUIRE.getType().equals(requireApplyOrder) && ObjUtils.isBlank(applyId)){
                    /** 用餐规则中的金额，仅用于申请单 */
                    checkResult.setResCode(TemporaryResponseCode.OrderTakeawayCheckOnlyApplyOrder);
                    return checkResult;
                }
            }

            Boolean checkRule = ObjUtils.toBoolean(takeawayRule.getCheckRule(), false);
            log.info("hasApply:{}, checkRule:{}", hasApply, checkRule);
            TakeawayRuleCheckResult takeawayRuleCheckResult = new TakeawayRuleCheckResult();
            takeawayRuleCheckResult.setNotPriceExceedType(Lists.newArrayList());
            // 校验订单用餐人数和申请单用餐人数是否一致
            if (hasApply  && Objects.equals(takeawayRule.getCheckDinerNumber(), 1) && ObjUtils.isNotEmpty(takeawayOrderCheckReqContract.getTrip_id())) {
                ApplyTripInfo applyTripInfo = applyTripInfoMapper.selectByPrimaryKey(takeawayOrderCheckReqContract.getTrip_id());
                //校验申请单用餐人数比较类型 0：需等于 1：需小于 2：必须不大于 3：需大于 4：必须不小于
                Integer compareType = ObjUtils.toInteger(takeawayRule.getCompareType(),0) ;

                if(null == takeawayOrderCheckReqContract.getDiner_count()){
                    checkResult.setResCode(TemporaryResponseCode.OrderTakeawayCheckDinerCount);
                    return checkResult;
                }

                if (null != applyTripInfo && null != applyTripInfo.getPersonCount()) {
                    if(compareType == 0 && !Objects.equals(takeawayOrderCheckReqContract.getDiner_count(), applyTripInfo.getPersonCount())){
                        TempOrderRuleCheckResult checkResult2 = new TempOrderRuleCheckResult();
                        checkResult2.setResCode(TemporaryResponseCode.OrderTakeawayCheckDinerNumberNotEqual.getCode(),
                                StrUtils.formatString(TemporaryResponseCode.OrderTakeawayCheckDinerNumberNotEqual.getMsg(), takeawayOrderCheckReqContract.getDiner_count(), applyTripInfo.getPersonCount()));
                        takeawayRuleCheckResult.getNotPriceExceedType().add(checkResult2);
                    }else if(compareType == 1 && takeawayOrderCheckReqContract.getDiner_count() >= applyTripInfo.getPersonCount() ){
                        TempOrderRuleCheckResult checkResult2 = new TempOrderRuleCheckResult();
                        checkResult2.setResCode(TemporaryResponseCode.OrderTakeawayCheckDinerNumberNotEqual.getCode(),
                                StrUtils.formatString(TemporaryResponseCode.OrderTakeawayCheckDinerNumberNotEqual1.getMsg(), takeawayOrderCheckReqContract.getDiner_count(), applyTripInfo.getPersonCount()));
                        takeawayRuleCheckResult.getNotPriceExceedType().add(checkResult2);
                    }else if( compareType == 2 && takeawayOrderCheckReqContract.getDiner_count() > applyTripInfo.getPersonCount()){
                        TempOrderRuleCheckResult checkResult2 = new TempOrderRuleCheckResult();
                        checkResult2.setResCode(TemporaryResponseCode.OrderTakeawayCheckDinerNumberNotEqual.getCode(),
                                StrUtils.formatString(TemporaryResponseCode.OrderTakeawayCheckDinerNumberNotEqual2.getMsg(), takeawayOrderCheckReqContract.getDiner_count(), applyTripInfo.getPersonCount()));
                        takeawayRuleCheckResult.getNotPriceExceedType().add(checkResult2);
                    }else if( compareType == 3 && takeawayOrderCheckReqContract.getDiner_count() <= applyTripInfo.getPersonCount() ){
                        TempOrderRuleCheckResult checkResult2 = new TempOrderRuleCheckResult();
                        checkResult2.setResCode(TemporaryResponseCode.OrderTakeawayCheckDinerNumberNotEqual.getCode(),
                                StrUtils.formatString(TemporaryResponseCode.OrderTakeawayCheckDinerNumberNotEqual3.getMsg(), takeawayOrderCheckReqContract.getDiner_count(), applyTripInfo.getPersonCount()));
                        takeawayRuleCheckResult.getNotPriceExceedType().add(checkResult2);
                    }else if( compareType == 4 &&  takeawayOrderCheckReqContract.getDiner_count() < applyTripInfo.getPersonCount()  ){
                        TempOrderRuleCheckResult checkResult2 = new TempOrderRuleCheckResult();
                        checkResult2.setResCode(TemporaryResponseCode.OrderTakeawayCheckDinerNumberNotEqual.getCode(),
                                StrUtils.formatString(TemporaryResponseCode.OrderTakeawayCheckDinerNumberNotEqual4.getMsg(), takeawayOrderCheckReqContract.getDiner_count(), applyTripInfo.getPersonCount()));
                        takeawayRuleCheckResult.getNotPriceExceedType().add(checkResult2);
                    }
                }
            }

            log.info("校验订单用餐人数和申请单用餐人数是否一致:{}",JsonUtils.toJson(takeawayRuleCheckResult));
            //是否有非费用规则超规
            Boolean hasNotPriceExceed = CollectionUtils.isNotEmpty(takeawayRuleCheckResult.getNotPriceExceedType());
            if(!checkRule && !hasNotPriceExceed){
            // 使用申请单 且未开启同时校验规则  直接返回
            if (hasApply && !checkRule) {
                return checkResult;
            }
            }
            snapshotInfo.put("ruleInfo", takeawayRule);
            //超标规则（1：禁止 2：超规填写理由下单）
            Integer exceedBuyType = takeawayAuthContract.getExceedBuyType();

            if(checkRule || !hasApply) {
                //外卖规则
                takeawayRuleCheckResult = checkTakeawayExceedType(hasApply, checkRule,
                        takeawayRule, takeawayOrderCheckReqContract, personalPay, totalPrice, takeawayRuleCheckResult);
                logger.info("takeawayRuleCheckResult:{}", JsonUtils.toJson(takeawayRuleCheckResult));
                logger.info("takeawayRuleCheckResult-param, rule={}, req={}, personalPay={}, totalPrice={}", JsonUtils.toJson(takeawayRule), JsonUtils.toJson(takeawayOrderCheckReqContract), personalPay, totalPrice);
            }
            snapshotInfo.put("useInfo", takeawayRuleCheckResult.getUseInfo());
            // 申请单校验企业支付金额
            BigDecimal applyCompanyPayPrice = ObjUtils.toBigDecimal(checkResult.getCompanyPayPrice(), BigDecimal.ZERO);
            // 规则校验企业支付金额
            BigDecimal ruleCompanyPayPrice = ObjUtils.toBigDecimal(takeawayRuleCheckResult.getCompanyPayPrice(), BigDecimal.ZERO);

            //节省规则信息
            ruleInfo.setId(takeawayRule.getId().toString());
            ruleInfo.setName(takeawayRule.getName());
            ruleInfo.setPrice_limit(takeawayRuleCheckResult.getPriceLimit());

            //费用
            List<TempOrderRuleCheckResult> priceExceedType = takeawayRuleCheckResult.getPriceExceedType();
            //非费用
            List<TempOrderRuleCheckResult> notPriceExceedType = takeawayRuleCheckResult.getNotPriceExceedType();
            //强制提交逻辑
            TakeawayOrderRuleCheckResult checkExceedAuthResult = checkExceedAuth(takeawayOrderCheckReqContract);
            logger.info("TakeawayOrderRuleCheckResult:{}", JsonUtils.toJson(checkExceedAuthResult));
            //是否有规则超规
            Boolean hasExceedRule = CollectionUtils.isNotEmpty(priceExceedType) || CollectionUtils.isNotEmpty(notPriceExceedType);
            //是否有非费用规则超规
            Boolean hasNotPriceExceedRule = CollectionUtils.isNotEmpty(notPriceExceedType);
            //超标禁止下单
            if (TravelExceedType.NotAllowed.getCode() == exceedBuyType
                    && ((!personalPay && hasExceedRule) || (personalPay && hasNotPriceExceedRule))) {
                List<TempOrderRuleCheckResult> newList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(notPriceExceedType)) {
                    newList.addAll(notPriceExceedType);
                }
                if (CollectionUtils.isNotEmpty(priceExceedType)) {
                    newList.addAll(priceExceedType);
                }
                List<ResponseCodeContract> tempOrderListResultContracts = new ArrayList<>();
                for (TempOrderRuleCheckResult tempOrderRuleCheckResult : newList) {
                    ResponseCodeContract tempOrderListResultContract = new ResponseCodeContract();
                    tempOrderListResultContract.setCode(tempOrderRuleCheckResult.getErrCode());
                    tempOrderListResultContract.setContent(tempOrderRuleCheckResult.getErrMsg());
                    tempOrderListResultContract.setType(IconChangeUtil.getTypeByCode(tempOrderRuleCheckResult.getErrCode()));
                    tempOrderListResultContracts.add(tempOrderListResultContract);
                }
                Map<String, Object> codeListMap = new HashMap<>();
                codeListMap.put("codeList", tempOrderListResultContracts);
                checkResult.setResCode(TemporaryResponseCode.OrderCanNotSubmit.getCode(), JsonUtils.toJson(codeListMap));
                ErrMsgInfo errMsgInfo = new ErrMsgInfo();
                logger.info("无法继续下单，超规规则如下：{}", JsonUtils.toJson(tempOrderListResultContracts));
                errMsgInfo.setTitle(checkResult.getErrMsg());
                errMsgInfo.setErr_code_list(tempOrderListResultContracts);
                checkResult.setErrMsgInfo(errMsgInfo);
                return checkResult;
            }
            //超标需要理由
            else if (TravelExceedType.AllowedNeedReason.getCode() == exceedBuyType
                    && ((!personalPay && hasExceedRule) || (personalPay && hasNotPriceExceedRule))) {

                if (hasApply && applyCompanyPayPrice.compareTo(ruleCompanyPayPrice) < 0) {
                    return checkResult;
                }

                List<TempOrderRuleCheckResult> newList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(notPriceExceedType)) {
                    newList.addAll(notPriceExceedType);
                }
                if (CollectionUtils.isNotEmpty(priceExceedType)) {
                    newList.addAll(priceExceedType);
                }
                List<ResponseCodeContract> tempOrderListResultContracts = new ArrayList<>();
                for (TempOrderRuleCheckResult tempOrderRuleCheckResult : newList) {
                    ResponseCodeContract tempOrderListResultContract = new ResponseCodeContract();
                    tempOrderListResultContract.setCode(tempOrderRuleCheckResult.getErrCode());
                    tempOrderListResultContract.setContent(tempOrderRuleCheckResult.getErrMsg());
                    tempOrderListResultContract.setType(IconChangeUtil.getTypeByCode(tempOrderRuleCheckResult.getErrCode()));
                    tempOrderListResultContracts.add(tempOrderListResultContract);
                }
                ErrMsgInfo errMsgInfo = new ErrMsgInfo();
                errMsgInfo.setTitle(checkExceedAuthResult.getErrMsg());
                errMsgInfo.setErr_code_list(tempOrderListResultContracts);
                if (checkExceedAuthResult.getErrCode() != TemporaryResponseCode.Success.getCode()) {
                    logger.info("超规填写理由，超规规则如下：{}", JsonUtils.toJson(tempOrderListResultContracts));
                    //超规则返回
                    checkExceedAuthResult.setIs_exceed(true);
                    checkExceedAuthResult.setErrMsgInfo(errMsgInfo);
                    return checkExceedAuthResult;
                } else {
                    checkResult.setErrMsgInfo(errMsgInfo);
                }
            }

            if (hasApply && applyCompanyPayPrice.compareTo(ruleCompanyPayPrice) < 0) {
                return checkResult;
            }

            clearApplyCheckResult(checkResult);

            // 使用申请单 且未开启同时校验规则  直接返回
            if (hasApply && !checkRule) {
                return checkResult;
            }

            companyPayPrice = ObjUtils.toBigDecimal(takeawayRuleCheckResult.getCompanyPayPrice(), BigDecimal.ZERO);
//            if (advancePayment && ObjUtils.isNotBlank(clientVersion) && VersionTool.greaterThanOrEqualTo(clientVersion, "4.5.0")) {
//                // 个人支付提示
//                if (takeawayOrderCheckReqContract.getPersonalpay_sumbit() == null || !takeawayOrderCheckReqContract.getPersonalpay_sumbit()) {
//                    LimitPayTipContract limitPayTipContract = null;
//                    if (couponExceedPriceSetting == 1) {
//                        limitPayTipContract = priceLimitPayTips(personalPay, companyPayPrice, totalPrice, BigDecimal.valueOf(0));
//                        logger.info("limitPayTipContract1:{}", JsonUtils.toJson(limitPayTipContract));
//                    } else {
//                        limitPayTipContract = priceLimitPayTips(personalPay, companyPayPrice, totalPrice, couponAmount);
//                        logger.info("limitPayTipContract2:{}", JsonUtils.toJson(limitPayTipContract));
//                    }
//                    if (limitPayTipContract.isTip_flag()) {
//                        checkResult.setResCode(TemporaryResponseCode.OrderCheckPersonalPayTip, limitPayTipContract.getTip().getContent());
//                        checkResult.setErrMsg(SaasOrderConstant.ORDER_OVERRULE_TIP);
//                        return checkResult;
//                    }
//                }
//            }
            // 超规/合规金额处理
            if (hasExceedRule) {
                if (!hasNotPriceExceedRule) {
                    checkResult.setAmountCompliance(checkResult.getAmountCompliance().min(companyPayPrice));
                    checkResult.setAmountNonCompliance(totalPrice.subtract(checkResult.getAmountCompliance()));
                } else {
                    checkResult.setAmountCompliance(BigDecimal.ZERO);
                    checkResult.setAmountNonCompliance(totalPrice);
                }
                logger.info("[外卖下单校验]，合规金额:{}, 超规金额:{}", checkResult.getAmountCompliance(), checkResult.getAmountNonCompliance());
            }
        }
//        BigDecimal personalPayPrice = totalPrice.subtract(companyPayPrice);
//        if (advancePayment) {
//            checkResult.setCompanyPayPrice(BigDecimal.ZERO);
//            checkResult.setPersonalPayPrice(totalPrice);
//            checkResult.setReimbursablePrice(companyPayPrice);
//            checkResult.setUnreimbursablePrice(personalPayPrice);
//        } else {
//            checkResult.setCompanyPayPrice(companyPayPrice);
//            checkResult.setPersonalPayPrice(personalPayPrice);
//            checkResult.setReimbursablePrice(BigDecimal.ZERO);
//            checkResult.setUnreimbursablePrice(personalPayPrice);
//        }


        //开启个人支付
        if (personalPay) {
            //个人支付金额
            BigDecimal employeePayMoney = totalPrice.compareTo(companyPayPrice) > 0 ? totalPrice.subtract(companyPayPrice) : BigDecimal.ZERO;
            if (advancePayment) {
                checkResult.setCompanyPayPrice(BigDecimal.ZERO);
                checkResult.setPersonalPayPrice(totalPrice);
                checkResult.setReimbursablePrice(companyPayPrice);
                checkResult.setUnreimbursablePrice(employeePayMoney);
            } else {
                checkResult.setCompanyPayPrice(companyPayPrice);
                checkResult.setPersonalPayPrice(employeePayMoney);
                checkResult.setReimbursablePrice(BigDecimal.ZERO);
                checkResult.setUnreimbursablePrice(employeePayMoney);
            }
            if (advancePayment && ObjUtils.isNotBlank(clientVersion) && VersionTool.greaterThanOrEqualTo(clientVersion, "4.5.0")) {
                // 个人支付提示
                if (takeawayOrderCheckReqContract.getPersonalpay_sumbit() == null || !takeawayOrderCheckReqContract.getPersonalpay_sumbit()) {
                    LimitPayTipContract limitPayTipContract = null;
                    if (couponExceedPriceSetting == 1) {
                        limitPayTipContract = priceLimitPayTips(personalPay, companyPayPrice, totalPrice, BigDecimal.valueOf(0));
                        logger.info("limitPayTipContract1:{}", JsonUtils.toJson(limitPayTipContract));
                    } else {
                        limitPayTipContract = priceLimitPayTips(personalPay, companyPayPrice, totalPrice, couponAmount);
                        logger.info("limitPayTipContract2:{}", JsonUtils.toJson(limitPayTipContract));
                    }
                    if (limitPayTipContract.isTip_flag()) {
                        checkResult.setResCode(TemporaryResponseCode.OrderCheckPersonalPayTip, limitPayTipContract.getTip().getContent());
                        checkResult.setErrMsg(SaasOrderConstant.ORDER_OVERRULE_TIP);

                        ErrMsgInfo errMsgInfo = new ErrMsgInfo();
                        errMsgInfo.setTitle(SaasOrderConstant.ORDER_OVERRULE_TIP);
                        checkResult.setErrMsgInfo(errMsgInfo);
                        return checkResult;
                    }
                }
            }
        }else{
            //个人垫付
            if (advancePayment) {
                checkResult.setCompanyPayPrice(BigDecimal.ZERO);
                checkResult.setPersonalPayPrice(totalPrice);
                checkResult.setReimbursablePrice(totalPrice);
                checkResult.setUnreimbursablePrice(BigDecimal.ZERO);
            } else {
                checkResult.setCompanyPayPrice(totalPrice);
                checkResult.setPersonalPayPrice(BigDecimal.ZERO);
                checkResult.setReimbursablePrice(BigDecimal.ZERO);
                checkResult.setUnreimbursablePrice(BigDecimal.ZERO);
            }
        }
        //节省规则信息
        BigDecimal priceLimit = ruleInfo.getPrice_limit();
        if (!ObjUtils.isNull(priceLimit) && priceLimit.compareTo(BigDecimal.valueOf(-1)) != 0) {
            ruleInfo.setPrice_limit_flag(true);
            ruleInfo.setDesc(StrUtils.formatString(CoreLanguage.TakeawayCheckServiceImpl_Value_DinnerSingleLess.getMessage(),
                    priceLimit.setScale(2, BigDecimal.ROUND_HALF_UP)));
        } else {
            ruleInfo.setPrice_limit_flag(false);
            ruleInfo.setDesc("");
        }
        ruleInfo.setIs_apply(false);
        checkResult.setRule_info(ruleInfo);
        checkResult.setCoupon_used_amount(couponAmount);
        if (couponExceedPriceSetting == 0) {
            BigDecimal couponValue = couponAmount;
            BigDecimal companyActPayPrice = checkResult.getCompanyPayPrice();
            if (advancePayment) {
                checkResult.setCompanyPayPrice(BigDecimal.ZERO);
                BigDecimal reimbursablePrice = Optional.ofNullable(checkResult.getReimbursablePrice()).orElse(BigDecimal.ZERO);
                if (reimbursablePrice.compareTo(couponAmount) < 0) {
                    couponValue = reimbursablePrice;
                }
                checkResult.setPersonalPayPrice(takeawayOrderCheckReqContract.getOrder_price().subtract(couponValue).setScale(2, RoundingMode.HALF_UP));
                checkResult.setReimbursablePrice(takeawayOrderCheckReqContract.getOrder_price().subtract(couponValue).setScale(2, RoundingMode.HALF_UP));
                checkResult.setUnreimbursablePrice(BigDecimal.ZERO);
            } else {
                //公司支付金额
                if (companyActPayPrice.compareTo(couponValue) >= 0) {
                    companyActPayPrice = companyActPayPrice.subtract(couponValue).setScale(2, RoundingMode.HALF_UP);
                } else {
                    couponValue = companyActPayPrice;
                    companyActPayPrice = BigDecimal.ZERO;
                }
            }
            checkResult.setCompanyPayPrice(companyActPayPrice);
            checkResult.setCoupon_used_amount(couponValue);
        }
        logger.info("checkResult:{}", JsonUtils.toJson(checkResult));
        return checkResult;
    }

    private void clearApplyCheckResult(TakeawayOrderRuleCheckResult result) {
        result.setResCode(TemporaryResponseCode.Success);
    }

    /**
     * 校验外卖申请单 相关校验
     * @param takeawayOrderCheckReqContract
     */
    @Override
    public ApplyCheckRes checkTakeawayByApply(TakeawayOrderCheckReqV2Contract takeawayOrderCheckReqContract){

        //申请单id
        String applyId = takeawayOrderCheckReqContract.getApply_id();
        //行程id
        String tripId = takeawayOrderCheckReqContract.getTrip_id();

        //是否有申请单
        boolean hasApply = ObjUtils.isNotBlank(takeawayOrderCheckReqContract.getApply_id()) ? true : false;

        //公司id
        String companyId = takeawayOrderCheckReqContract.getCompany_id();
        //员工id
        String employeeId = takeawayOrderCheckReqContract.getEmployee_id();
        ApplyCheckRes applyCheckRes = new ApplyCheckRes();
        applyCheckRes.setApplyId(applyId);
        applyCheckRes.setTripId(tripId);
        applyCheckRes.setCompanyId(companyId);
        applyCheckRes.setEmployeeId(employeeId);
        applyCheckRes.setHasApply(hasApply);

        if(applyCheckRes.getHasApply()){
            ApplyOrder applyOrder = applyOrderMapper.selectByPrimaryKey(applyId);
            logger.info("申请单信息:{}",  JsonUtils.toJson(applyOrder));

            ApplyTripInfo applyTripInfo =  applyTripInfoMapper.selectByPrimaryKey(tripId);
            logger.info("行程信息:{}",  JsonUtils.toJson(applyTripInfo));
            applyCheckRes.setApplyTripPersonCount(applyTripInfo.getPersonCount());

            //地址验证标记，true：要验证申请单地址； false：不验证【自定义申请单】地址
            boolean addressValidateFlag = true;
            boolean cityValidateFlag = false;

            // 申请单是否限制使用次数
            boolean useCountLimitCheck = false;

            List<String> tripCityIdList = new ArrayList<>();
            //自定义申请单
            if(applyOrder.getType().equals(ApplyType.CustomFromBeforehand.getValue())){
                CustomFormApplyControlItemDTO customApply = iCustomFormService.queryCustomFormApplyControlConfig(companyId,applyOrder.getFormId(),BizType.Takeaway.getCode(),applyOrder.getId());
                if(null != customApply && null != customApply.getAddressType() && customApply.getAddressType().intValue() == 0){
                    addressValidateFlag = false;
                }
                if (null != customApply && Objects.equals(customApply.getCityType(), 1)) {
                    cityValidateFlag = true;
                    tripCityIdList = Arrays.stream(applyTripInfo.getStartCityId().split(",")).collect(Collectors.toList());
                }
                if (customApply != null && Objects.equals(SwitchTypeEnum.OPEN.getSwitchCode(),customApply.getUseCountLimit())) {
                    useCountLimitCheck = true;
                }
            }else{
                MessageSetup takeawayApplyCountLimit = iMessageSetupService.queryCompanyMessageSetupWithDefault(companyId, SaasApplyConstant.ITEM_CODE_APPLY_COUNT_LIMIT_TAKEAWAY);
                logger.info("takeawayApplyCountLimit={}",  JsonUtils.toJson(takeawayApplyCountLimit));
                useCountLimitCheck = takeawayApplyCountLimit.getIsChecked() == SaasReasonConstant.IS_CHECKED_TRUE;
            }
            //申请单时段
            Date startTime = ObjUtils.isEmpty(applyTripInfo.getFloatStartTime()) ? applyTripInfo.getStartTime() : applyTripInfo.getFloatStartTime();
            Date endTime = ObjUtils.isEmpty(applyTripInfo.getFloatEndTime()) ? applyTripInfo.getEndTime() : applyTripInfo.getFloatEndTime();
            //订单时间
            Date orderTime = DateUtils.parse(takeawayOrderCheckReqContract.getOrder_time());
            //送餐地址经纬度
            Double addressLat = takeawayOrderCheckReqContract.getAddress_lat();
            Double addressLng = takeawayOrderCheckReqContract.getAddress_lng();


            //您填写的送餐时间与所选用的申请单不匹配
            if (orderTime.before(startTime) || !orderTime.before(endTime)) {
                CurrencyMsg currencyMsg = new CurrencyMsg();
                currencyMsg.setErrMsg(GlobalResponseCode.ApplyTakeawayNotAvailable.getMsg());
                currencyMsg.setCode(GlobalResponseCode.ApplyTakeawayNotAvailable.getCode());
                applyCheckRes.setIsExceed(true);
                applyCheckRes.setIsNoPriceExceed(true);
                applyCheckRes.getCurrencyMsgList().add(currencyMsg);
            }

            //地址校验
            if(addressValidateFlag){
                //解析申请单地址信息
                String tripContent = applyTripInfo.getTripContent();
                JSONObject jo = JsonUtils.toObj(tripContent, JSONObject.class);
                Double applyAddressLat = jo.getDouble("address_lat");
                Double applyAddressLng = jo.getDouble("address_lng");
                Double radius = 50D;
                Double des = DistanceUtils.getDistance(addressLng, addressLat, applyAddressLng, applyAddressLat);
                log.info("送餐地址和申请单填写地址距离：{}，order=[{},{}], apply=[{},{}]",des, addressLat, addressLng, applyAddressLat, applyAddressLng);
                if (des > radius) {
                    //您填写的送餐地址和所选用的申请单地址距离过远
                    CurrencyMsg currencyMsg = new CurrencyMsg();
                    currencyMsg.setErrMsg(GlobalResponseCode.ApplyTakeawayAddressTooFar.getMsg());
                    currencyMsg.setCode(GlobalResponseCode.ApplyTakeawayAddressTooFar.getCode());
                    applyCheckRes.setIsExceed(true);
                    applyCheckRes.setIsNoPriceExceed(true);
                    applyCheckRes.getCurrencyMsgList().add(currencyMsg);
                }
            }

            // 校验城市必填是否与行程里的城市一致
            if (cityValidateFlag) {
                String cityCode = "";
                try {
                    logger.info(String.format("获取城市信息调用参数：%s,%s", addressLng, addressLat));
                    String responseData = HttpTool.get(URL_GET_CITY_CODE + "?longitude=" + addressLng + "&latitude=" +
                            addressLat + "&category=" + BizType.Takeaway.getCode());
                    logger.info(String.format("获取城市信息调用结果：%s", responseData));
                    JSONObject responseDataMap = JSONObject.parseObject(responseData, JSONObject.class);
                    JSONObject data = (JSONObject) responseDataMap.get("data");
                    cityCode = (String) data.get("city_code");
                } catch (Exception e) {
                    log.warn("获取城市信息异常：", e);
                }
                FullPathAreaDTO AreaPath = areaService.getFullPathAreaById(applyV5Service.getCityId(cityCode));
                List<String> cityCodeList = applyV5Service.setCityPathList(AreaPath);

                if (tripCityIdList.stream().noneMatch(cityCodeList::contains)) {
                    //您填写的送餐城市和所选用的申请单城市不一致
                    CurrencyMsg currencyMsg = new CurrencyMsg();
                    currencyMsg.setErrMsg(GlobalResponseCode.ApplyTakeawayAddressNotMatchApply.getMsg());
                    currencyMsg.setCode(GlobalResponseCode.ApplyTakeawayAddressNotMatchApply.getCode());
                    applyCheckRes.setIsExceed(true);
                    applyCheckRes.setIsNoPriceExceed(true);
                    applyCheckRes.getCurrencyMsgList().add(currencyMsg);
                }
            }

            // 限制使用次数时
            if (useCountLimitCheck) {
                List<OrderSaasResDTO> resList = takeawayOrderService.queryOrderList(applyTripInfo.getId());
                if (resList.size() >= 1) {
                    //申请单次数限制不通过
                    CurrencyMsg currencyMsg = new CurrencyMsg();
                    currencyMsg.setErrMsg("申请单次数限制不通过");
                    currencyMsg.setCode(GlobalResponseCode.ApplyTakeawayAddressNotMatchApply.getCode());
                    applyCheckRes.setIsExceed(true);
                    applyCheckRes.setIsNoPriceExceed(true);
                    applyCheckRes.getCurrencyMsgList().add(currencyMsg);
                }
            }


            //审批单金额
            BigDecimal companyPayPrice;
            //订单金额
            BigDecimal totalPrice = takeawayOrderCheckReqContract.getOrder_price();
            //优惠券金额
            BigDecimal couponAmount = ObjUtils.ifNull(takeawayOrderCheckReqContract.getCoupon_amount(), BigDecimal.ZERO);
            //优惠券抵扣超规
            Integer couponExceedPriceSetting = iOrderCheckService.queryCouponExceedPriceSetting(companyId);
            if (couponExceedPriceSetting == 1) {
                totalPrice = totalPrice.subtract(couponAmount).max(BigDecimal.ZERO);
            }


            // 校验预估费
            String startDateStr = DateTimeTool.fromDateToStringByFormatter(orderTime, DateTimeTool.dateTimeFormator);
            CheckApplyEstimatedAmountReq req = new CheckApplyEstimatedAmountReq();
            req.setApplyId(applyId);
            req.setTripId(tripId);
            req.setCompanyId(companyId);
            req.setValidateAmount(totalPrice);
            req.setBizType(BizType.Takeaway.getCode());
            req.setStartDate(startDateStr);
            req.setEndDate(startDateStr);

            AmountExceedCheckRes amountExceedCheckRes = tripEstimateService.checkTripEstimatedAmount(req);
            boolean isEstimatedExceedPersonalPay = amountExceedCheckRes.getIsPersonalPay();
            boolean isEstimatedExceed = GlobalResponseCode.Success.getCode() != amountExceedCheckRes.getErrCode();
            if (isEstimatedExceed) {
                applyCheckRes.setIsExceed(true);
                applyCheckRes.setIsPriceExceed(true);
                // 如果超过预估费且未开启个人支付的情况 固定单据isEstimatedExceedPersonalPay始终为true
                if (!Objects.equals(amountExceedCheckRes.getApplyType(), ApplyType.CustomFromBeforehand.getValue())) {
                    if (!isEstimatedExceedPersonalPay) {
                        String content = String.format("您预订的行程实际费用超过申请单费用上限，请修改申请单或订单信息\n"
                                        + "行程费用上限金额：￥%s\n本次预订金额：￥%s",
                                amountExceedCheckRes.getLeftAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString(),
                                totalPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                        CurrencyMsg currencyMsg = new CurrencyMsg();
                        currencyMsg.setErrMsg(content);
                        currencyMsg.setCode(GlobalResponseCode.ApplyTripEstimatedAmountExceed.getCode());
                        applyCheckRes.getCurrencyMsgList().add(currencyMsg);
                    }
                } else { // 自定义申请单的情况
                    if (amountExceedCheckRes.getIsCheckSumEstimateAmount()) {
                        if (TotalEstimatedLimitType.DAILY_LIMIT.equalsTo(amountExceedCheckRes.getTotalEstimatedLimitIntValue())) {
                            BigDecimal left = BigDecimal.ZERO;
                            for (BigDecimal bigDecimal : amountExceedCheckRes.getDailyLeftAmountList()) {
                                left = left.add(bigDecimal);
                            }
                            amountExceedCheckRes.setLeftAmount(left); // 设置值 防止后面判断空指针
                        }
                        // 首次提交
                        if (!ObjUtils.ifNull(takeawayOrderCheckReqContract.getApply_personalpay_sumbit(), Boolean.FALSE)) {
                            if (TotalEstimatedLimitType.DAILY_LIMIT.equalsTo(amountExceedCheckRes.getTotalEstimatedLimitIntValue())) {
                                CurrencyMsg currencyMsg = new CurrencyMsg();
                                currencyMsg.setErrMsg(TemporaryResponseCode.OrderTakeawayDailyLimitCostOverLeftEstimatedCost.getMsg());
                                currencyMsg.setCode(TemporaryResponseCode.OrderTakeawayDailyLimitCostOverLeftEstimatedCost.getCode());
                                applyCheckRes.getCurrencyMsgList().add(currencyMsg);
                            } else if (TotalEstimatedLimitType.DAILY_AVERAGE.equalsTo(amountExceedCheckRes.getTotalEstimatedLimitIntValue())) {
                                CurrencyMsg currencyMsg = new CurrencyMsg();
                                currencyMsg.setErrMsg(TemporaryResponseCode.OrderTakeawayDailyAverageCostOverLeftEstimatedCost.getMsg());
                                currencyMsg.setCode(TemporaryResponseCode.OrderTakeawayDailyAverageCostOverLeftEstimatedCost.getCode());
                                applyCheckRes.getCurrencyMsgList().add(currencyMsg);
                            } else {
                                CurrencyMsg currencyMsg = new CurrencyMsg();
                                currencyMsg.setErrMsg(TemporaryResponseCode.OrderTakeawayEmployeeCostOverLeftEstimatedCost.getMsg());
                                currencyMsg.setCode(TemporaryResponseCode.OrderTakeawayEmployeeCostOverLeftEstimatedCost.getCode());
                                applyCheckRes.getCurrencyMsgList().add(currencyMsg);
                            }
                        }
                    } else {
                        if (!isEstimatedExceedPersonalPay) {
                            String content = String.format("您预订的行程实际费用超过申请单费用上限，请修改申请单或订单信息\n"
                                            + "行程费用上限金额：￥%s\n本次预订金额：￥%s",
                                    amountExceedCheckRes.getLeftAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString(),
                                    totalPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                            CurrencyMsg currencyMsg = new CurrencyMsg();
                            currencyMsg.setErrMsg(content);
                            currencyMsg.setCode(GlobalResponseCode.ApplyTripEstimatedAmountExceed.getCode());
                            applyCheckRes.getCurrencyMsgList().add(currencyMsg);
                        }
                    }
                }
            }
            if (isEstimatedExceed && isEstimatedExceedPersonalPay && totalPrice.compareTo(amountExceedCheckRes.getLeftAmount()) > 0) {
                log.info("预估费超规且预估费开启个人支付且订单金额大于剩余金额，申请单多余部分走个人支付，支付金额：{}",amountExceedCheckRes.getLeftAmount());
                applyCheckRes.setCompanyPayPrice(amountExceedCheckRes.getLeftAmount());
                applyCheckRes.setPersonalPay(true);
            } else {
                applyCheckRes.setCompanyPayPrice(totalPrice);
                applyCheckRes.setPersonalPay(false);
            }
            BigDecimal personalPayPrice = totalPrice.subtract(applyCheckRes.getCompanyPayPrice());
            applyCheckRes.setPersonalPayPrice(personalPayPrice);
        }
        log.info("外卖申请单校验结果:{}",JsonUtils.toJson(applyCheckRes));
        return applyCheckRes;
    }



    /**
     *
     * @param takeawayOrderCheckReqContract
     * @param takeawayAuthContract
     * @param checkResult
     * @param applyId
     * @return
     */
    private TakeawayOrderRuleCheckResult applyOrderCheck(TakeawayOrderCheckReqV2Contract takeawayOrderCheckReqContract,
        EmployeeBaseRuleV2 takeawayAuthContract, TakeawayOrderRuleCheckResult checkResult, String applyId,
        String tripId, Boolean advancePayment,Integer couponExceedPriceSetting,BigDecimal couponAmount) {
        ApplyOrder applyOrder = applyOrderMapper.selectByPrimaryKey(applyId);
        logger.info("applyOrder={}",  JsonUtils.toJson(applyOrder));
        if (ObjUtils.isNull(applyOrder)) {
            checkResult.setResCode(GlobalResponseCode.ApplyNotFound.getCode(), GlobalResponseCode.ApplyNotFound.getMsg());
            return checkResult;
        }
        String applyOrderEmployeeId = applyOrder.getEmployeeId();

        String applyOrderCompanyId = applyOrder.getCompanyId();
        if ( !takeawayOrderCheckReqContract.getCompany_id().equals(applyOrderCompanyId)) {
            checkResult.setResCode(GlobalResponseCode.ApplyNotFound.getCode(), GlobalResponseCode.ApplyNotFound.getMsg());
            return checkResult;
        }
        Integer applyOrderType = applyOrder.getApplyOrderType();
        if (applyOrderType != CompanySettingType.TakeawayApply.getValue()
                && applyOrderType != CompanySettingType.CustomFormApply.getValue()) {
            checkResult.setResCode(GlobalResponseCode.ApplyTypeNotAvailable.getCode(), GlobalResponseCode.ApplyTypeNotAvailable.getMsg());
            return checkResult;
        }
        Integer state = applyOrder.getState();
        if (state != ApplyStatus.Approved.getValue()) {
            checkResult.setResCode(GlobalResponseCode.ApplyStateNotAvailable.getCode(), GlobalResponseCode.ApplyStateNotAvailable.getMsg());
            return checkResult;
        }
        /** 地址验证标记，true：要验证申请单地址； false：不验证【自定义申请单】地址 */
        boolean addressValidateFlag = true;
        boolean cityValidateFlag = false;
        List<String> tripCityIdList = new ArrayList<>();
        ApplyTripInfo applyTripInfo;
        if (ObjUtils.isBlank(tripId)) {
            checkResult.setResCode(GlobalResponseCode.ApplyTripNotFound.getCode(), GlobalResponseCode.ApplyTripNotFound.getMsg());
            return checkResult;
        }
        if (applyOrderType == CompanySettingType.CustomFormApply.getValue()) {
            applyTripInfo = applyTripInfoMapper.selectByPrimaryKey(tripId);
            logger.info("applyTripInfo={}",  JsonUtils.toJson(applyTripInfo));
            if (ObjUtils.isEmpty(applyTripInfo)) {
                checkResult.setResCode(GlobalResponseCode.ApplyTripNotFound.getCode(), GlobalResponseCode.ApplyTripNotFound.getMsg());
                return checkResult;
            }
            CustomFormApplyControlItemDTO customApply =
                    iCustomFormService.queryCustomFormApplyControlConfig(applyOrderCompanyId,applyOrder.getFormId(),BizType.Takeaway.getCode(),applyOrder.getId());
            if(null != customApply && null != customApply.getAddressType() && customApply.getAddressType().intValue() == 0){
                addressValidateFlag = false;
            }
            if (null != customApply && Objects.equals(customApply.getCityType(), 1)) {
                cityValidateFlag = true;
                if (StringUtils.isEmpty(applyTripInfo.getStartCityId())) {
                    checkResult.setResCode(GlobalResponseCode.ApplyTripNotFound.getCode(), GlobalResponseCode.ApplyTripNotFound.getMsg());
                    return checkResult;
                }
                tripCityIdList = Arrays.stream(applyTripInfo.getStartCityId().split(",")).collect(Collectors.toList());
            }
        } else {
            applyTripInfo = applyTripInfoMapper.selectByPrimaryKey(tripId);
            if (ObjUtils.isEmpty(applyTripInfo)) {
                checkResult.setResCode(GlobalResponseCode.ApplyNotFound.getCode(), GlobalResponseCode.ApplyNotFound.getMsg());
                return checkResult;
            }

        }
        //时段
        Date startTime = ObjUtils.isEmpty(applyTripInfo.getFloatStartTime()) ? applyTripInfo.getStartTime() : applyTripInfo.getFloatStartTime();
        Date endTime = ObjUtils.isEmpty(applyTripInfo.getFloatEndTime()) ? applyTripInfo.getEndTime() : applyTripInfo.getFloatEndTime();

        Date orderTime = DateUtils.parse(takeawayOrderCheckReqContract.getOrder_time());
        if (orderTime.before(startTime) || !orderTime.before(endTime)) {
            checkResult.setResCode(GlobalResponseCode.ApplyTakeawayNotAvailable.getCode(), GlobalResponseCode.ApplyTakeawayNotAvailable.getMsg());
            return checkResult;
        }
        Double addressLat = takeawayOrderCheckReqContract.getAddress_lat();
        Double addressLng = takeawayOrderCheckReqContract.getAddress_lng();
        if(addressValidateFlag){
            //送餐地址
            String tripContent = applyTripInfo.getTripContent();
            if (ObjUtils.isBlank(tripContent)) {
                checkResult.setResCode(GlobalResponseCode.ApplyTakeawayAddressNotAvailable.getCode(), GlobalResponseCode.ApplyTakeawayAddressNotAvailable.getMsg());
                return checkResult;
            }
            JSONObject jo = JsonUtils.toObj(tripContent, JSONObject.class);
            Double applyAddressLat = jo.getDouble("address_lat");
            Double applyAddressLng = jo.getDouble("address_lng");
            Double radius = 50D;
            Double des = DistanceUtils.getDistance(addressLng, addressLat, applyAddressLng, applyAddressLat);
            logger.info("计算距离：order=[{},{}], apply=[{},{}], radius={}, des={}", addressLat, addressLng, applyAddressLat, applyAddressLng, radius, des);
            if (des > radius) {
                checkResult.setResCode(GlobalResponseCode.ApplyTakeawayAddressTooFar.getCode(), GlobalResponseCode.ApplyTakeawayAddressTooFar.getMsg());
                return checkResult;
            }
        }

        // 校验城市必填是否与行程里的城市一致
        if (cityValidateFlag) {
            String cityCode = "";
            try {
                logger.info(String.format("获取城市信息调用参数：%s,%s", addressLng, addressLat));
                String responseData = HttpTool.get(URL_GET_CITY_CODE + "?longitude=" + addressLng + "&latitude=" +
                    addressLat + "&category=" + BizType.Takeaway.getCode());
                logger.info(String.format("获取城市信息调用结果：%s", responseData));
                JSONObject responseDataMap = JSONObject.parseObject(responseData, JSONObject.class);
                JSONObject data = (JSONObject) responseDataMap.get("data");
                cityCode = (String) data.get("city_code");
            } catch (Exception e) {
                log.warn("getCityCodeByName error, ", e);
            }

            FullPathAreaDTO AreaPath = areaService.getFullPathAreaById(applyV5Service.getCityId(cityCode));
            List<String> cityCodeList = applyV5Service.setCityPathList(AreaPath);

            if (tripCityIdList.stream().noneMatch(cityCodeList::contains)) {
                checkResult.setResCode(GlobalResponseCode.ApplyTakeawayAddressNotMatchApply.getCode(),
                    GlobalResponseCode.ApplyTakeawayAddressNotMatchApply.getMsg());
                return checkResult;
            }
        }

        //审批单金额
        BigDecimal companyPayPrice;
        //初始化可用金额
//        BigDecimal applyPriceLimit = ObjUtils.ifNull(applyTripInfo.getEstimatedAmount(), BigDecimal.ZERO);
        BigDecimal totalPrice = takeawayOrderCheckReqContract.getOrder_price();
        MessageSetup takeawayApplyCountLimit = iMessageSetupService.queryCompanyMessageSetupWithDefault(applyOrderCompanyId, SaasApplyConstant.ITEM_CODE_APPLY_COUNT_LIMIT_TAKEAWAY);
        logger.info("takeawayApplyCountLimit={}",  JsonUtils.toJson(takeawayApplyCountLimit));

        // 申请单是否限制使用次数
        boolean useCountLimitCheck = false;
        // 申请单是否需要预估费用校验
//        boolean applyEstimatedCheck = false;
        boolean personalPay;
//        boolean isEstimatedExceedPersonalPay = false; // 是否开启预估费个人支付
        if (applyOrderType == CompanySettingType.CustomFormApply.getValue()) {
            CustomFormApplyControlItemDTO customFormApplyConfig =
                iCustomFormService.queryCustomFormApplyControlConfig(applyOrder.getCompanyId(), applyOrder.getFormId(),
                    BizType.Takeaway.getCode(), applyOrder.getId());
            log.info("自定义申请单配置：{}", JsonUtils.toJson(customFormApplyConfig));
            if (customFormApplyConfig != null && Objects.equals(SwitchTypeEnum.OPEN.getSwitchCode(),
                customFormApplyConfig.getUseCountLimit())) {
                useCountLimitCheck = true;
            }
//            if (customFormApplyConfig != null && Objects.equals(SwitchTypeEnum.OPEN.getSwitchCode(),
//                customFormApplyConfig.getEstimatedCostCheckSwitch())) {
//                applyEstimatedCheck = true;
//            }
//            if (customFormApplyConfig != null && Objects.equals(SwitchTypeEnum.OPEN.getSwitchCode(),
//                customFormApplyConfig.getPersonalPaySwitch())) {
//                isEstimatedExceedPersonalPay = true;
//            }
        } else {
            useCountLimitCheck = takeawayApplyCountLimit.getIsChecked() == SaasReasonConstant.IS_CHECKED_TRUE;
//            applyEstimatedCheck = true; // 非自定义申请单默认开启
//            isEstimatedExceedPersonalPay = true;  // 外卖历史逻辑默认开启
        }

        if (useCountLimitCheck) { // 限制使用次数时
            List<OrderSaasResDTO> resList = takeawayOrderService.queryOrderList(applyTripInfo.getId());
            if (resList.size() >= 1) {
                checkResult.setResCode(GlobalResponseCode.ApplyNotAvailable.getCode(), GlobalResponseCode.ApplyNotAvailable.getMsg());
                return checkResult;
            }
        }

        // 校验预估费
        String startDateStr = DateTimeTool.fromDateToStringByFormatter(orderTime, DateTimeTool.dateTimeFormator);
        CheckApplyEstimatedAmountReq req = new CheckApplyEstimatedAmountReq();
        req.setApplyId(applyId);
        req.setTripId(tripId);
        req.setCompanyId(applyOrderCompanyId);
        req.setValidateAmount(totalPrice);
        req.setBizType(BizType.Takeaway.getCode());
        req.setStartDate(startDateStr);
        req.setEndDate(startDateStr);
        AmountExceedCheckRes amountExceedCheckRes = tripEstimateService.checkTripEstimatedAmount(req);
        boolean isEstimatedExceedPersonalPay = amountExceedCheckRes.getIsPersonalPay();
        boolean isEstimatedExceed = GlobalResponseCode.Success.getCode() != amountExceedCheckRes.getErrCode();
        if (isEstimatedExceed) {
            // 如果超过预估费且未开启个人支付的情况 固定单据isEstimatedExceedPersonalPay始终为true
            if (!Objects.equals(amountExceedCheckRes.getApplyType(), ApplyType.CustomFromBeforehand.getValue())) {
                if (!isEstimatedExceedPersonalPay) {
                    String content = String.format("您预订的行程实际费用超过申请单费用上限，请修改申请单或订单信息\n"
                            + "行程费用上限金额：￥%s\n本次预订金额：￥%s",
                        amountExceedCheckRes.getLeftAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString(),
                        totalPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                    checkResult.setResCode(GlobalResponseCode.ApplyTripEstimatedAmountExceed.getCode(), content);
                    return checkResult;
                }
            } else { // 自定义申请单的情况
                if (amountExceedCheckRes.getIsCheckSumEstimateAmount()) {
                    if (TotalEstimatedLimitType.DAILY_LIMIT.equalsTo(amountExceedCheckRes.getTotalEstimatedLimitIntValue())) {
                        BigDecimal left = BigDecimal.ZERO;
                        for (BigDecimal bigDecimal : amountExceedCheckRes.getDailyLeftAmountList()) {
                            left = left.add(bigDecimal);
                        }
                        amountExceedCheckRes.setLeftAmount(left); // 设置值 防止后面判断空指针
                    }
                    if (!ObjUtils.ifNull(takeawayOrderCheckReqContract.getApply_personalpay_sumbit(), Boolean.FALSE)) { // 个人支付二次提交
                        if (TotalEstimatedLimitType.DAILY_LIMIT.equalsTo(amountExceedCheckRes.getTotalEstimatedLimitIntValue())) {
                            checkResult.setResCode(TemporaryResponseCode.OrderTakeawayDailyLimitCostOverLeftEstimatedCost);
                        } else if (TotalEstimatedLimitType.DAILY_AVERAGE.equalsTo(amountExceedCheckRes.getTotalEstimatedLimitIntValue())) {
                            checkResult.setResCode(TemporaryResponseCode.OrderTakeawayDailyAverageCostOverLeftEstimatedCost);
                        } else {
                            checkResult.setResCode(TemporaryResponseCode.OrderTakeawayEmployeeCostOverLeftEstimatedCost);
                        }
                    }
                } else {
                    if (!isEstimatedExceedPersonalPay) {
                        String content = String.format("您预订的行程实际费用超过申请单费用上限，请修改申请单或订单信息\n"
                                + "行程费用上限金额：￥%s\n本次预订金额：￥%s",
                            amountExceedCheckRes.getLeftAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString(),
                            totalPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                        checkResult.setResCode(GlobalResponseCode.ApplyTripEstimatedAmountExceed.getCode(), content);
                        return checkResult;
                    }
                }
            }
        }

//        if (applyOrderType != CompanySettingType.CustomFormApply.getValue()) { // 非自定义申请单
//            if (applyEstimatedCheck) { // 非自定义的默认走预估费用校验
//                applyPriceLimit = getApplyEstimatedLeft(applyId, applyTripInfo, applyPriceLimit);
//                // 如果超过预估费且为开启个人支付的情况 固定单据isEstimatedExceedPersonalPay始终为true
//                if (totalPrice.compareTo(applyPriceLimit) > 0 && !isEstimatedExceedPersonalPay) {
//                    String content = String.format("您预订的行程实际费用超过申请单费用上限，请修改申请单或订单信息\n"
//                            + "行程费用上限金额：￥%s\n本次预订金额：￥%s",
//                        applyPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString(),
//                        totalPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
//                    checkResult.setResCode(GlobalResponseCode.ApplyTripEstimatedAmountExceed.getCode(), content);
//                    return checkResult;
//                }
//            }
//        } else {
//            // 总预估费
//            TotalEstimatedCheckDto totalEstimatedCheckDto = applyAmountService.isEstimatedAmountExcessive(applyId, totalPrice, orderTime, BizType.Takeaway.getCode());
//            log.info("totalEstimatedCheckDto={}", JsonUtils.toJson(totalEstimatedCheckDto));
//            // 自定义申请单存在 & 总预估费开关打开
//            if (totalEstimatedCheckDto.getHasApplyOrder()
//                && totalEstimatedCheckDto.getIsCustformApplyOrder()
//                && Objects.equals(CommonSwitchConstant.OPEN, totalEstimatedCheckDto.getCheckTotalEstimatedFlag())) {  // 总预估费默认开启预估费用个人支付
//                isEstimatedExceedPersonalPay = true; // 总预估默认开启超预估费用个人支付
//                // 预估费剩余金额等于总预估费减去已使用金额
//                applyPriceLimit = totalEstimatedCheckDto.getTotalEstimatedCost().subtract(totalEstimatedCheckDto.getUsedAmount());
//                if(applyPriceLimit.compareTo(BigDecimal.ZERO) < 0){
//                    log.error("外卖剩余预估费小于0异常");
//                }
//                log.info("advancePayment={}, apply_personalpay_sumbit={}", advancePayment,
//                    takeawayOrderCheckReqContract.getApply_personalpay_sumbit());
//                if (totalEstimatedCheckDto.getIsExceed() // 预估费超规，
//                    && !ObjUtils.ifNull(takeawayOrderCheckReqContract.getApply_personalpay_sumbit(), Boolean.FALSE)) {
//                    // 个人支付二次提交
//                    if (TotalEstimatedLimitType.DAILY_LIMIT.equalsTo(totalEstimatedCheckDto.getTotalEstimatedCostType())) {
//                        checkResult.setResCode(TemporaryResponseCode.OrderTakeawayDailyLimitCostOverLeftEstimatedCost);
//                    } else if (TotalEstimatedLimitType.DAILY_AVERAGE.equalsTo(totalEstimatedCheckDto.getTotalEstimatedCostType())) {
//                        checkResult.setResCode(TemporaryResponseCode.OrderTakeawayDailyAverageCostOverLeftEstimatedCost);
//                    } else {
//                        checkResult.setResCode(TemporaryResponseCode.OrderTakeawayEmployeeCostOverLeftEstimatedCost);
//                    }
//                }
//            } else { // 总预估费如果没有开启
//                if (applyEstimatedCheck) { // 自定义申请单预估费用是否开启
//                    applyPriceLimit = getApplyEstimatedLeft(applyId, applyTripInfo, applyPriceLimit);
//                    // 如果超过预估费且为开启超预估费个人支付则提示禁止下单
//                    if (totalPrice.compareTo(applyPriceLimit) > 0 && !isEstimatedExceedPersonalPay) {
//                        String content = String.format("您预订的行程实际费用超过申请单费用上限，请修改申请单或订单信息\n"
//                                + "行程费用上限金额：￥%s\n本次预订金额：￥%s",
//                            applyPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString(),
//                            totalPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
//                        checkResult.setResCode(GlobalResponseCode.ApplyTripEstimatedAmountExceed.getCode(), content);
//                        return checkResult;
//                    }
//                } else {
//                    applyPriceLimit = BigDecimal.valueOf(Long.MAX_VALUE); // 兼容下面逻辑  给预估费设置超大值
//                }
//            }
//        }

        if (isEstimatedExceed && isEstimatedExceedPersonalPay && totalPrice.compareTo(amountExceedCheckRes.getLeftAmount()) > 0) {
            companyPayPrice = amountExceedCheckRes.getLeftAmount();
            personalPay = true;
        } else {
            companyPayPrice = totalPrice;
            personalPay = false;
        }

        checkResult.setCompanyPayPrice(companyPayPrice);

        if (takeawayOrderCheckReqContract.getApply_personalpay_sumbit() == null) {
            takeawayOrderCheckReqContract.setApply_personalpay_sumbit(false);
        }
        if (!takeawayOrderCheckReqContract.getApply_personalpay_sumbit() && personalPay) {
            GlobalResponseCode tip;
            if (advancePayment) {
                tip = GlobalResponseCode.ApplyPersonalPay2;
            } else {
                tip = GlobalResponseCode.ApplyPersonalPay;
            }
            checkResult.setResCode(tip.getCode(), StrUtils.formatString(tip.getMsg(), companyPayPrice));
            return checkResult;
        }
        BigDecimal personalPayPrice = totalPrice.subtract(companyPayPrice);
        if (advancePayment) {
            checkResult.setCompanyPayPrice(BigDecimal.ZERO);
            checkResult.setPersonalPayPrice(totalPrice);
            checkResult.setReimbursablePrice(companyPayPrice);
            checkResult.setUnreimbursablePrice(personalPayPrice);
        } else {
            checkResult.setCompanyPayPrice(companyPayPrice);
            checkResult.setPersonalPayPrice(personalPayPrice);
            checkResult.setReimbursablePrice(BigDecimal.ZERO);
            checkResult.setUnreimbursablePrice(personalPayPrice);
        }
        checkResult.setAmountCompliance(companyPayPrice);
        checkResult.setAmountNonCompliance(personalPayPrice);
        //节省规则信息
        CommonSavingRuleInfo ruleInfo = new CommonSavingRuleInfo();
        ruleInfo.setRule_limit(true);
        ruleInfo.setDesc("");
        ruleInfo.setPrice_limit_flag(true);
        ruleInfo.setPrice_limit(new BigDecimal(-1));
        if (takeawayAuthContract.getRuleFlag()) {
            TakeawayContract takeawayRule = takeawayAuthContract.getRuleInfo();
            if (takeawayRule == null) {
                checkResult.setResCode(TemporaryResponseCode.OrderTakeawayCheckRuleNotExist);
                return checkResult;
            }
            //节省规则信息
            ruleInfo.setId(takeawayRule.getId().toString());
            ruleInfo.setName(takeawayRule.getName());
        }
        ruleInfo.setIs_apply(true);
        checkResult.setRule_info(ruleInfo);
        checkResult.setCoupon_used_amount(couponAmount);
        BigDecimal couponValue = couponAmount;
        BigDecimal companyActPayPrice = checkResult.getCompanyPayPrice();
        if (advancePayment) {
            checkResult.setCompanyPayPrice(BigDecimal.ZERO);
            BigDecimal reimbursablePrice = Optional.ofNullable(checkResult.getReimbursablePrice()).orElse(BigDecimal.ZERO);
            if (reimbursablePrice.compareTo(couponAmount) < 0) {
                couponValue = reimbursablePrice;
            }
            checkResult.setPersonalPayPrice(takeawayOrderCheckReqContract.getOrder_price().subtract(couponValue).setScale(2, RoundingMode.HALF_UP));
            checkResult.setReimbursablePrice(takeawayOrderCheckReqContract.getOrder_price().subtract(couponValue).setScale(2, RoundingMode.HALF_UP));
            checkResult.setUnreimbursablePrice(BigDecimal.ZERO);
        } else {
            if (couponExceedPriceSetting == 0) {
                //公司支付金额
                if (companyActPayPrice.compareTo(couponValue) >= 0) {
                    companyActPayPrice = companyActPayPrice.subtract(couponValue).setScale(2, RoundingMode.HALF_UP);
                } else {
                    couponValue = companyActPayPrice;
                    companyActPayPrice = BigDecimal.ZERO;
                }
            }else {
                if (personalPayPrice.compareTo(couponAmount)>=0){
                    checkResult.setPersonalPayPrice(personalPayPrice.subtract(couponAmount).setScale(2, RoundingMode.HALF_UP));
                }else {
                    checkResult.setPersonalPayPrice(BigDecimal.ZERO);
                    companyActPayPrice = takeawayOrderCheckReqContract.getOrder_price().subtract(couponAmount).setScale(2, RoundingMode.HALF_UP).max(BigDecimal.ZERO);
                }
            }
        }
        checkResult.setCompanyPayPrice(companyActPayPrice);
        checkResult.setCoupon_used_amount(couponValue);

        return checkResult;
    }

//    private BigDecimal getApplyEstimatedLeft(String applyId, ApplyTripInfo applyTripInfo, BigDecimal applyPriceLimit) {
//        BigDecimal applyTotalPrice = takeawayCheckV4Service.getOrderUsedTotalPrice(applyId, applyTripInfo.getId());
//        logger.info("applyTotalPrice：{}, applyPriceLimit：{}", applyTotalPrice, applyPriceLimit);
//
//        //如果订单总金额超出初始化可用金额则重置成0
//        applyTotalPrice = ObjUtils.toBigDecimal(applyTotalPrice, BigDecimal.ZERO);
//        applyPriceLimit = applyPriceLimit.subtract(applyTotalPrice).setScale(2, BigDecimal.ROUND_HALF_UP);
//        if (applyPriceLimit.compareTo(BigDecimal.ZERO) < 0) {
//            applyPriceLimit = BigDecimal.ZERO;
//        }
//        logger.info("applyPriceLimit left：{}", applyPriceLimit);
//        return applyPriceLimit;
//    }


    private TakeawayRuleCheckResult checkTakeawayExceedType(Boolean hasApply, Boolean checkRule, TakeawayContract takeawayRule,
     TakeawayOrderCheckReqV2Contract takeawayOrderCheckReqContract, Boolean personalPay, BigDecimal totalPrice, TakeawayRuleCheckResult takeawayRuleCheckResult) {
        Integer subSceneId = takeawayOrderCheckReqContract.getSub_scene_id();
        //费用
        List<TempOrderRuleCheckResult> priceExceedType = new ArrayList<>();
        //非费用
        List<TempOrderRuleCheckResult> notPriceExceedType = takeawayRuleCheckResult.getNotPriceExceedType();

        //使用信息
        JSONObject useInfo = new JSONObject();
        takeawayRuleCheckResult.setUseInfo(useInfo);
        BigDecimal price = totalPrice;
        //默认订单金额
        takeawayRuleCheckResult.setCompanyPayPrice(price);
        //地址校验
        if (!hasApply) {
            List<TempOrderRuleCheckResult> addressExceedType = checkTakeawayAddress(takeawayRule,
                    takeawayOrderCheckReqContract.getCompany_address_id());
            if (ObjUtils.isNotEmpty(addressExceedType)) {
                notPriceExceedType.addAll(addressExceedType);
            }
        }

        String employeeId = takeawayOrderCheckReqContract.getEmployee_id();
//        String companyId = takeawayOrderCheckReqContract.getCompany_id();
        String companyId = StringUtils.isNotBlank(takeawayOrderCheckReqContract.getConsumer_company_id()) ?
         takeawayOrderCheckReqContract.getConsumer_company_id() : takeawayOrderCheckReqContract.getCompany_id();
        String orderTime = takeawayOrderCheckReqContract.getOrder_time();
        String applyId = takeawayOrderCheckReqContract.getApply_id(); // 申请单id


        String takeRuleType = "oldRule";
        //节假日/工作日/休息日 - 时段校验
        if (takeawayRule.getLimitTime() && ObjUtils.isNotEmpty(takeawayRule.getTimeRangeList())) {
            List<TakeawayContract.TimeRange> trj = takeawayRule.getTimeRangeList();
            List<TakeawayContract.TimeRange.Range> rangeListj = trj.get(0).getRangeList();
            if (ObjUtils.isNotEmpty(rangeListj)) {
                /** 由于规则互斥，存在新规则时，任意元素的规则都是新规则。 */
                int dayTypej = rangeListj.get(0).getDayType();
                if (dayTypej >= 10 && dayTypej <= 12) {
                    takeRuleType = "newRule";
                    boolean timeExceed = true;
                    int dayTypeOrder = getDayTypeHolidayWorkday(companyId, orderTime);

                    List<TakeawayContract.TimeRange> timeRangeList = takeawayRule.getTimeRangeList();
                    for (TakeawayContract.TimeRange tr : timeRangeList) {
                        List<TakeawayContract.TimeRange.Range> rangeList = tr.getRangeList();
                        for (TakeawayContract.TimeRange.Range r : rangeList) {
                            int dayType = r.getDayType();
                            /** 规则类型和当前日期类型是否匹配。不匹配则继续找下一条规则 */
                            if (dayType != dayTypeOrder) {
                                continue;
                            }
                            TimeRange inRange = checkoutTimeAuthNoOver(orderTime, r);
                            if (null == inRange) {
                                continue;
                            }

                            /** 存在合规日期范围(inRange)，设置标识为不违规 */
                            timeExceed = false;

                            /** 判定是否金额超规 */
                            BigDecimal priceLimit = tr.getFrequencyLimitPrice();
                            BigDecimal sumPriceLimit = tr.getAccumulativeLimitPrice();
                            Integer orderCountLimit = tr.getOrderLimitNum();
                            takeawayRuleCheckResult.setTimeRange(inRange);
                            takeawayRuleCheckResult.setPriceLimit(priceLimit);
                            takeawayRuleCheckResult.setSumPriceLimit(sumPriceLimit);
                            takeawayRuleCheckResult.setOrderCountLimit(orderCountLimit);
                            if ((ObjUtils.isNull(priceLimit) || priceLimit.compareTo(BigDecimal.valueOf(-1)) == 0)
                                    && (ObjUtils.isNull(sumPriceLimit) || sumPriceLimit.compareTo(BigDecimal.valueOf(-1)) == 0)
                                    && (ObjUtils.isNull(orderCountLimit) || orderCountLimit == -1)) {
                                // 时间段合规，金额、次数不限制。验证通过
                                break;
                            }
                            TakeawayCostCheckDto takeawayCostCheckDto = TakeawayCostCheckDto.builder()
                                    .applyId(applyId)
                                    .orderTime(orderTime)
                                    .orderPrice(price)
                                    .build();
                            takeawayMoneyRuleCheck(tr, takeawayRuleCheckResult, employeeId, companyId, price,
                                    priceExceedType, notPriceExceedType, inRange, personalPay, subSceneId,
                                    takeawayCostCheckDto, takeawayOrderCheckReqContract);
                            break;
                        }
                    }

                    if (!timeExceed) {
                        /** 如果经过上面处理合规了，那么查询前一天的配置，是否有重叠合规的时段，但是金额不合规的情况。 */
                        //前一天
                        String dateReduce = dateAddV2(DateUtils.parse(orderTime), -1, "yyyy-MM-dd HH:mm:ss");
                        logger.info("getDayTypeHolidayWorkday(companyId={}, dateReduce={})", companyId, dateReduce);
                        dayTypeOrder = getDayTypeHolidayWorkday(companyId, dateReduce);
                        for (TakeawayContract.TimeRange tr : timeRangeList) {
                            List<TakeawayContract.TimeRange.Range> rangeList = tr.getRangeList();
                            for (TakeawayContract.TimeRange.Range r : rangeList) {
                                if (!r.getIsOvernight() || r.getDayType() != dayTypeOrder) {
                                    continue;
                                }
                                TimeRange timeRange = checkoutTimeAuthOverDay(dateReduce, orderTime, r);
                                if (null == timeRange) {
                                    continue;
                                }
                                /** 判定是否金额超规 */
                                BigDecimal priceLimit = tr.getFrequencyLimitPrice();
                                BigDecimal sumPriceLimit = tr.getAccumulativeLimitPrice();
                                Integer orderCountLimit = tr.getOrderLimitNum();
                                takeawayRuleCheckResult.setTimeRange(timeRange);
                                takeawayRuleCheckResult.setPriceLimit(priceLimit);
                                takeawayRuleCheckResult.setSumPriceLimit(sumPriceLimit);
                                takeawayRuleCheckResult.setOrderCountLimit(orderCountLimit);
                                if ((ObjUtils.isNull(priceLimit) || priceLimit.compareTo(BigDecimal.valueOf(-1)) == 0)
                                        && (ObjUtils.isNull(sumPriceLimit) || sumPriceLimit.compareTo(BigDecimal.valueOf(-1)) == 0)
                                        && (ObjUtils.isNull(orderCountLimit) || orderCountLimit == -1)) {
                                    // 时间段合规，金额、次数不限制。验证通过
                                    break;
                                }
                                TakeawayCostCheckDto takeawayCostCheckDto = TakeawayCostCheckDto.builder()
                                        .applyId(applyId)
                                        .orderTime(orderTime)
                                        .orderPrice(price)
                                        .build();
                                takeawayMoneyRuleCheck(tr, takeawayRuleCheckResult, employeeId, companyId, price,
                                        priceExceedType, notPriceExceedType, timeRange, personalPay, subSceneId,
                                        takeawayCostCheckDto, takeawayOrderCheckReqContract);
                            }
                        }
                    }

                    /** 如果经过上面处理仍然超规，那么查询前一天的配置，有没有跨天的可以符合时段规则 */
                    if (timeExceed) {
                        //前一天
                        String dateReduce = dateAddV2(DateUtils.parse(orderTime), -1, "yyyy-MM-dd HH:mm:ss");
                        logger.info("getDayTypeHolidayWorkday(companyId={}, dateReduce={})", companyId, dateReduce);
                        dayTypeOrder = getDayTypeHolidayWorkday(companyId, dateReduce);
                        for (TakeawayContract.TimeRange tr : timeRangeList) {
                            List<TakeawayContract.TimeRange.Range> rangeList = tr.getRangeList();
                            for (TakeawayContract.TimeRange.Range r : rangeList) {
                                if (!r.getIsOvernight() || r.getDayType() != dayTypeOrder) {
                                    continue;
                                }
                                TimeRange timeRange = checkoutTimeAuthOverDay(dateReduce, orderTime, r);
                                if (null == timeRange) {
                                    continue;
                                }
                                /** 存在合规日期范围(inRange)，设置标识为不违规 */
                                timeExceed = false;

                                /** 判定是否金额超规 */
                                BigDecimal priceLimit = tr.getFrequencyLimitPrice();
                                BigDecimal sumPriceLimit = tr.getAccumulativeLimitPrice();
                                Integer orderCountLimit = tr.getOrderLimitNum();
                                takeawayRuleCheckResult.setTimeRange(timeRange);
                                takeawayRuleCheckResult.setPriceLimit(priceLimit);
                                takeawayRuleCheckResult.setSumPriceLimit(sumPriceLimit);
                                takeawayRuleCheckResult.setOrderCountLimit(orderCountLimit);
                                if ((ObjUtils.isNull(priceLimit) || priceLimit.compareTo(BigDecimal.valueOf(-1)) == 0)
                                        && (ObjUtils.isNull(sumPriceLimit) || sumPriceLimit.compareTo(BigDecimal.valueOf(-1)) == 0)
                                        && (ObjUtils.isNull(orderCountLimit) || orderCountLimit == -1)) {
                                    // 时间段合规，金额、次数不限制。验证通过
                                    break;
                                }
                                TakeawayCostCheckDto takeawayCostCheckDto = TakeawayCostCheckDto.builder()
                                        .applyId(applyId)
                                        .orderTime(orderTime)
                                        .orderPrice(price)
                                        .build();
                                takeawayMoneyRuleCheck(tr, takeawayRuleCheckResult, employeeId, companyId, price,
                                        priceExceedType, notPriceExceedType, timeRange, personalPay, subSceneId,
                                        takeawayCostCheckDto, takeawayOrderCheckReqContract);
                            }
                        }
                    }

                    if (timeExceed) {
                        TempOrderRuleCheckResult checkResult = new TempOrderRuleCheckResult();
                        checkResult.setResCode(TemporaryResponseCode.OrderTakeawayTimeRangeNotAuth);
                        notPriceExceedType.add(checkResult);
                    }
                }
            }
        }


        //周中 - 时段校验(老规则)
        if ("oldRule".equals(takeRuleType) && takeawayRule.getLimitTime() && ObjUtils.isNotEmpty(takeawayRule.getTimeRangeList())) {
            Integer dayType = getDayType(orderTime);
            List<Integer> dayTypeList = new ArrayList<>();
            dayTypeList.add(dayType);
            if (dayType >= DayTempType.FIRSTDAY.getCode() && dayType <= DayTempType.FIVEDAY.getCode()) {
                //周一时，默认加入周日的时间规则
                if (dayType == DayTempType.FIRSTDAY.getCode()) {
                    dayTypeList.add(DayTempType.SEVENDAY.getCode());
                    dayTypeList.add(9);
                } else {
                    dayTypeList.add(dayType - 1);
                }
                dayTypeList.add(8);
            } else {
                if (dayType == DayTempType.SIXDAY.getCode()) {
                    dayTypeList.add(dayType - 1);
                    dayTypeList.add(8);
                } else {
                    dayTypeList.add(dayType - 1);
                }
                dayTypeList.add(9);
            }

            boolean timeExceed = true;
            List<TakeawayContract.TimeRange> timeRangeList = takeawayRule.getTimeRangeList();
            for (TakeawayContract.TimeRange timeRange : timeRangeList) {
                // 时段列表为空 不限制
                List<TakeawayContract.TimeRange.Range> ruleRangeList = timeRange.getRangeList();
                if (CollectionUtils.isEmpty(ruleRangeList)) {
                    continue;
                }
                List<TakeawayContract.TimeRange.Range> rangeList = Lists.newArrayList();
                for (TakeawayContract.TimeRange.Range range : ruleRangeList) {
                    if (dayTypeList.contains(range.getDayType())) {
                        rangeList.add(range);
                    }
                }
                if (CollectionUtils.isEmpty(rangeList)) {
                    continue;
                }
                TimeRange inRange = checkoutTimeAuth(dayType, rangeList, orderTime);
                if (!ObjUtils.isNull(inRange)) {
                    logger.info("外卖下单时间:{}, 所在时段:{}-{}", orderTime, DateUtils.format(inRange.getBeginTime()),
                     DateUtils.format(inRange.getEndTime()));
                    timeExceed = false;
                    BigDecimal priceLimit = timeRange.getFrequencyLimitPrice();
                    BigDecimal sumPriceLimit = timeRange.getAccumulativeLimitPrice();
                    Integer orderCountLimit = timeRange.getOrderLimitNum();
                    takeawayRuleCheckResult.setTimeRange(inRange);
                    takeawayRuleCheckResult.setPriceLimit(priceLimit);
                    takeawayRuleCheckResult.setSumPriceLimit(sumPriceLimit);
                    takeawayRuleCheckResult.setOrderCountLimit(orderCountLimit);
                    // 单次和累计金额次数不限制
                    if ((ObjUtils.isNull(priceLimit) || priceLimit.compareTo(BigDecimal.valueOf(-1)) == 0)
                            && (ObjUtils.isNull(sumPriceLimit) || sumPriceLimit.compareTo(BigDecimal.valueOf(-1)) == 0)
                            && (ObjUtils.isNull(orderCountLimit) || orderCountLimit == -1)) {
                        break;
                    }

                    TakeawayCostCheckDto takeawayCostCheckDto = TakeawayCostCheckDto.builder()
                            .applyId(applyId)
                            .orderTime(orderTime)
                            .orderPrice(price)
                            .build();
                    takeawayMoneyRuleCheck(timeRange, takeawayRuleCheckResult, employeeId, companyId, price,
                            priceExceedType, notPriceExceedType, inRange, personalPay, subSceneId,
                            takeawayCostCheckDto, takeawayOrderCheckReqContract);
                    break;
                }
            }
            if (timeExceed) {
                TempOrderRuleCheckResult checkResult = new TempOrderRuleCheckResult();
                checkResult.setResCode(TemporaryResponseCode.OrderTakeawayTimeRangeNotAuth);
                notPriceExceedType.add(checkResult);
            }
        }

//        if (!hasApply) {
            Boolean booking_order = takeawayOrderCheckReqContract.getBooking_order();
            if (PreBookingTypeEnum.ONLY_CURRENT_ORDER.getType().equals(takeawayRule.getPreBooking()) && null != booking_order && booking_order) {
                /** 仅可创建实时单，当前为预约单，提示超规 */
                log.info("仅可创建实时单，当前为预约单，提示超规");
                TempOrderRuleCheckResult checkResult = new TempOrderRuleCheckResult();
                checkResult.setResCode(TemporaryResponseCode.OrderTakeawayTimeOnlyRealTime);
                notPriceExceedType.add(checkResult);
                notPriceExceedType = notPriceExceedType.stream().distinct().collect(Collectors.toList());
            }
//        }
        takeawayRuleCheckResult.setPriceExceedType(priceExceedType);
        takeawayRuleCheckResult.setNotPriceExceedType(notPriceExceedType);
        return takeawayRuleCheckResult;
    }

    private String dateAddV2(Date date, int amount, String style) {
        Date targetDate = com.fenbeitong.finhub.common.utils.DateUtils.addDay(date, amount);
        return new SimpleDateFormat(style).format(targetDate);
    }

    //不跨天的检查
    private TimeRange checkoutTimeAuthNoOver(String baseTime, TakeawayContract.TimeRange.Range r){
        TimeRange timeRange = null;
        String dateSuffix = baseTime.trim().substring(0, 11); // 含后面空格，拼接用截取11位
        String beginTime = dateSuffix + r.getBeginTime() + ":00";
        //今天的规则，如果跨天的话，是begin->24：00，不跨天，那就是begin->end
        String endTime = dateSuffix + (r.getIsOvernight() ? "24:00:00" : r.getEndTime() + ":00");

        Date date1 = DateUtils.parse(baseTime.trim());
        Date beginTime1 = DateUtils.parse(beginTime);
        Date endTime1 = DateUtils.parse(endTime);

        if(date1.getTime() >= beginTime1.getTime() && date1.getTime() <= endTime1.getTime()){
            timeRange = new TimeRange();
            timeRange.setBeginTime(beginTime1);
            timeRange.setEndTime(endTime1);
        }
        return timeRange;
    }

    //跨天的检查
    private TimeRange checkoutTimeAuthOverDay(String preDayTime, String orderTime, TakeawayContract.TimeRange.Range r){
        TimeRange timeRange = null;
        String preDaySuffix = preDayTime.trim().substring(0, 11); // 含后面空格，拼接用截取11位
        String orderTimeSuffix = orderTime.trim().substring(0, 11); // 含后面空格，拼接用截取11位
        //昨天的规则，如果跨天的话，是begin->24:00，不跨天，那就是begin->end
        String beginTime = preDaySuffix + r.getBeginTime() + ":00";

        String endTime = r.getIsOvernight() ? orderTimeSuffix + r.getEndTime() + ":00" : preDaySuffix + r.getEndTime() + ":00";

        Date date1 = DateUtils.parse(orderTime.trim());
        Date beginTime1 = DateUtils.parse(beginTime);
        Date endTime1 = DateUtils.parse(endTime);

        if(date1.getTime() >= beginTime1.getTime() && date1.getTime() <= endTime1.getTime()){
            timeRange = new TimeRange();
            timeRange.setBeginTime(beginTime1);
            timeRange.setEndTime(endTime1);
        }
        return timeRange;
    }



    public int getDayTypeHolidayWorkday(String companyId, String orderDate){
        /** 调用接口判定日期对应的类型 */
        String date = orderDate.trim().substring(0, 10);
        List<CalendarRpcVO> calendarRpcVOS = null;
        try{
            calendarRpcVOS = rpcCompanyCalendarService.queryNonWorkDaysInRange(companyId, date, date);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("rpcCompanyCalendarService.queryNonWorkDaysInRange", e);
        }
        logger.info("getDayTypeHolidayWorkday, companyId={}, date={}, calendarRpcVOS={}",
                    companyId, date, JsonUtils.toJson(calendarRpcVOS));
        if(ObjectUtils.isEmpty(calendarRpcVOS)){
            return -1;
        }
        // 优先级： 节假日11 > 休息日12 > 工作日10
        for(CalendarRpcVO vo : calendarRpcVOS){
            CalendarRpcVO.CalendarItems calendarItems = vo.getCalendarItems();
            if(ObjectUtils.isEmpty(calendarItems)){
                continue;
            }
            List<Long> holidays = calendarItems.getHolidays();
            List<Long> restdays = calendarItems.getRestdays();
            List<Long> workDays = calendarItems.getWorkDays();
            if(null != holidays && holidays.size() > 0){
                return 11;
            }
            if(null != restdays && restdays.size() > 0){
                return 12;
            }
            if(null != workDays && workDays.size() > 0){
                return 10;
            }
        }

        return -1;
    }


    /** 外卖时段对应的单笔金额、总金额、笔数 合规验证 */
    public void takeawayMoneyRuleCheck(TakeawayContract.TimeRange timeRange, TakeawayRuleCheckResult takeawayRuleCheckResult,
                                       String employeeId, String companyId, BigDecimal price, List<TempOrderRuleCheckResult> priceExceedType,
                                       List<TempOrderRuleCheckResult> notPriceExceedType, TimeRange inRange, Boolean personalPay, int subSceneId,
                                       TakeawayCostCheckDto takeawayCostCheckDto, TakeawayOrderCheckReqV2Contract reqParm){

        logger.info("{}, {}, 命中时段:{}, 时段限制规则: {}", employeeId, companyId, JsonUtils.toJson(inRange), JSONObject.toJSON(timeRange));

        BigDecimal priceLimit = timeRange.getFrequencyLimitPrice();
        BigDecimal sumPriceLimit = timeRange.getAccumulativeLimitPrice();
        Integer orderCountLimit = timeRange.getOrderLimitNum();

        CompanyPayStatisticsResRpcDTO companyPayStat = queryTakeawayStat(employeeId, companyId, inRange.getBeginTime(), inRange.getEndTime(), subSceneId);
        JSONObject useInfo = takeawayRuleCheckResult.getUseInfo();
        if(null != useInfo){
            useInfo.put("companyPayStat", companyPayStat);
        }
        if (ObjUtils.isNull(companyPayStat)) {
            companyPayStat = CompanyPayStatisticsResRpcDTO.empty();
        }
        if (ObjUtils.isNull(companyPayStat.getCompanyTotalPay())) {
            companyPayStat.setCompanyTotalPay(BigDecimal.ZERO);
        }
        if (ObjUtils.isNull(companyPayStat.getOrderCount())) {
            companyPayStat.setOrderCount(0);
        }
        logger.info("查询时段外卖订单统计, 累计金额:{}, 累计次数:{}", companyPayStat.getCompanyTotalPay(), companyPayStat.getOrderCount());
        BigDecimal companyPayPrice = price;
        
        // 查询总预估费开关配置
        String applyId = takeawayCostCheckDto.getApplyId();
        log.info("applyId={}, price={}, date={}", applyId, takeawayCostCheckDto.getOrderPrice(), takeawayCostCheckDto.getOrderTime());
        Date orderTime = null;
        if (StringUtils.isNotBlank(takeawayCostCheckDto.getOrderTime())) {
            try {
                SimpleDateFormat sdfDateTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                orderTime = sdfDateTime.parse(takeawayCostCheckDto.getOrderTime());
            } catch (Exception e) {
                log.info("orderTime={}", takeawayCostCheckDto.getOrderTime());
                throw new SaasException(GlobalResponseCode.DateFormatError);
            }
        }
        TotalEstimatedCheckDto totalEstimatedCheckDto = applyAmountService.isEstimatedAmountExcessive(applyId, takeawayCostCheckDto.getOrderPrice(), orderTime, BizType.Takeaway.getCode());
        log.info("totalEstimatedCheckDto={}", JsonUtils.toJson(totalEstimatedCheckDto));
        // 自定义申请单存在 & 总预估费开关打开
        if (totalEstimatedCheckDto.getHasApplyOrder()
                && totalEstimatedCheckDto.getIsCustformApplyOrder() 
                && Objects.equals(CommonSwitchConstant.OPEN, totalEstimatedCheckDto.getCheckTotalEstimatedFlag())
                && !personalPay
        ) {
            if (totalEstimatedCheckDto.getIsExceed()) { // 预估费超规
                TempOrderRuleCheckResult checkResult = new TempOrderRuleCheckResult();
                if (TotalEstimatedLimitType.DAILY_LIMIT.equalsTo(totalEstimatedCheckDto.getTotalEstimatedCostType())) {
                    checkResult.setResCode(TemporaryResponseCode.OrderTakeawayDailyLimitCostOverLeftEstimatedCost);
                    priceExceedType.add(checkResult);
                } else if (TotalEstimatedLimitType.DAILY_AVERAGE.equalsTo(totalEstimatedCheckDto.getTotalEstimatedCostType())) {
                    checkResult.setResCode(TemporaryResponseCode.OrderTakeawayDailyAverageCostOverLeftEstimatedCost);
                    priceExceedType.add(checkResult);
                } else {
                    checkResult.setResCode(TemporaryResponseCode.OrderTakeawayEmployeeCostOverLeftEstimatedCost);
                    priceExceedType.add(checkResult);
                }
            }
        } else {
            /** 判定单次金额是否乘以人数 */
            List<MessageSetupVO> messageSetupVOS = setupService.queryBookingConfig(companyId);
            if(CollectionUtils.isNotEmpty(messageSetupVOS)){
                MessageSetupVO takeawayType = messageSetupVOS.stream().filter(a -> a.getItemCode().equals(BookingConfigEnum.TAKEAWAY.getItemCode())).findAny().orElseGet(() -> new MessageSetupVO());
                if(DinnerTakeawayBookingTypeEnum.N_BOOKER.getType().equals(takeawayType.getBookingType())){
                    log.info("返回预订人差标乘以用餐人数");
                    if(ObjUtils.isNull(reqParm.getDiner_count()) || reqParm.getDiner_count() <=0){
                        TempOrderRuleCheckResult checkResult = new TempOrderRuleCheckResult();
                        checkResult.setResCode(TemporaryResponseCode.OrderTakeawayCheckDinerCount.getCode(), TemporaryResponseCode.OrderTakeawayCheckDinerCount.getMsg());
                        notPriceExceedType.add(checkResult);
                    }else{
                        priceLimit = isNBookingType(priceLimit, reqParm.getDiner_count());
                    }
                }
            }

//            priceLimit = isNBookingType(companyId, priceLimit, reqParm.getDiner_count());
            // 单次金额校验
            if (!ObjUtils.isNull(priceLimit) && priceLimit.compareTo(BigDecimal.valueOf(-1)) != 0
                    && price.compareTo(priceLimit) > 0) {
                TempOrderRuleCheckResult checkResult = new TempOrderRuleCheckResult();
                checkResult.setResCode(TemporaryResponseCode.OrderTakeawayPriceLimit.getCode(), StrUtils.formatString(TemporaryResponseCode.OrderTakeawayPriceLimit.getMsg(), priceLimit.setScale(2, BigDecimal.ROUND_HALF_UP)));
                priceExceedType.add(checkResult);
                companyPayPrice = priceLimit;
            }
            // 累计金额校验
            if (!ObjUtils.isNull(sumPriceLimit) && sumPriceLimit.compareTo(BigDecimal.valueOf(-1)) != 0
                    && price.add(companyPayStat.getCompanyTotalPay()).compareTo(sumPriceLimit) > 0) {
                TempOrderRuleCheckResult checkResult = new TempOrderRuleCheckResult();
                checkResult.setResCode(TemporaryResponseCode.OrderTakeawaySumPriceLimit.getCode(), StrUtils.formatString(TemporaryResponseCode.OrderTakeawaySumPriceLimit.getMsg(), sumPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP)));
                priceExceedType.add(checkResult);
                if (companyPayStat.getCompanyTotalPay().compareTo(sumPriceLimit) >= 0) {
                    companyPayPrice = BigDecimal.ZERO;
                } else {
                    companyPayPrice = new BigDecimal(Math.min(sumPriceLimit.subtract(companyPayStat.getCompanyTotalPay()).doubleValue(), companyPayPrice.doubleValue())).setScale(2, BigDecimal.ROUND_HALF_UP);
                }
            }
            if (personalPay) {
                takeawayRuleCheckResult.setCompanyPayPrice(companyPayPrice);
            }
        }
        
        // 累计次数校验
        if (!ObjUtils.isNull(orderCountLimit) && orderCountLimit != -1
                && companyPayStat.getOrderCount() + 1 > orderCountLimit) {
            TempOrderRuleCheckResult checkResult = new TempOrderRuleCheckResult();
            checkResult.setResCode(TemporaryResponseCode.OrderTakeawayTotalOrderLimit.getCode(), StrUtils.formatString(TemporaryResponseCode.OrderTakeawayTotalOrderLimit.getMsg(), orderCountLimit));
            notPriceExceedType.add(checkResult);
        }

    }

    /** 判定当前多人用餐规则是否为：预订人差标乘以用餐人数。  **/
    public BigDecimal isNBookingType(BigDecimal priceLimit, Integer dinerCount){
        log.info("isNBookingType: {}, {}", priceLimit, dinerCount);
        if(ObjUtils.isNull(priceLimit) || priceLimit.compareTo(BigDecimal.valueOf(-1)) == 0){
            log.info("不处理单次相乘");
            return priceLimit;
        }
        return priceLimit.multiply(new BigDecimal(dinerCount));
    }


    /**
     * 校验地址信息
     *
     * @param takeawayRule
     * @param companyAddressId
     * @return
     */
    private List<TempOrderRuleCheckResult> checkTakeawayAddress(TakeawayContract takeawayRule, String companyAddressId) {
        //非费用
        List<TempOrderRuleCheckResult> notPriceExceedType = new ArrayList<>();
        //校验外卖地址规则
        if (takeawayRule.getLimitLocation() && ObjUtils.isNotEmpty(takeawayRule.getTakeawayLocationIds())) {
            List<String> takeawayLocationIds = takeawayRule.getTakeawayLocationIds();
            if (!takeawayLocationIds.contains(companyAddressId)) {
                TempOrderRuleCheckResult checkResult = new TempOrderRuleCheckResult();
                checkResult.setResCode(TemporaryResponseCode.OrderTakeawayLocationNoAuth);
                notPriceExceedType.add(checkResult);
            }
        }
        return notPriceExceedType;
    }

    /**
     * 校验日期规则问题
     * 注意点：日期出现次日时，天数加一天
     *
     * @param dayType
     * @param rangeList
     * @param startTime      yyyy-MM-dd HH:mm:ss
     * @return
     */
    private TimeRange checkoutTimeAuth(Integer dayType, List<TakeawayContract.TimeRange.Range> rangeList, String startTime) {
        TimeRange timeRange = null;
        try {
            SimpleDateFormat sdfDateTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat sdfDate = new SimpleDateFormat("yyyy-MM-dd");
            //无时间限制
            if (CollectionUtils.isEmpty(rangeList)) {
                return timeRange;
            }
            //获取外卖日期
            Date taxiStartDate = null;
            String date = startTime.substring(0, 10);
            taxiStartDate = sdfDate.parse(startTime);
            //日期加一天
            String dateAdd = dateAdd(taxiStartDate, 1, sdfDate);
            Boolean isAllowed = false;
            //日期减一天
            String dateReduce = dateAdd(taxiStartDate, -1, sdfDate);
            Date taxiDateTime = sdfDateTime.parse(startTime);
            for (TakeawayContract.TimeRange.Range range : rangeList) {
                String startDate = "";
                String endDate = "";
                //当天日期
                if (range.getDayType() == dayType.intValue()) {
                    startDate = date + " " + range.getBeginTime() + ":00";
                    endDate = date + " " + range.getEndTime() + ":00";
                    //跨日日期加一天
                    if (range.getIsOvernight()) {
                        endDate = dateAdd + " " + range.getEndTime() + ":00";
                    }
                }
                //前一天
                else if (range.getDayType() == (dayType.intValue() - 1)) {
                    if (range.getIsOvernight()) {
                        startDate = dateReduce + " " + range.getBeginTime() + ":00";
                        endDate = date + " " + range.getEndTime() + ":00";
                    } else {
                        continue;
                    }
                }
                //周末或者工作日
                else {
                    //周一特殊处理周末
                    if (dayType == DayTempType.FIRSTDAY.getCode()) {
                        //周末
                        if (range.getDayType() == 9 || range.getDayType() == DayTempType.SEVENDAY.getCode()) {
                            if (range.getIsOvernight()) {
                                startDate = dateReduce + " " + range.getBeginTime() + ":00";
                                endDate = date + " " + range.getEndTime() + ":00";
                            } else {
                                continue;
                            }
                        }
                        else {
                            startDate = date + " " + range.getBeginTime() + ":00";
                            endDate = date + " " + range.getEndTime() + ":00";
                            //跨日日期加一天
                            if (range.getIsOvernight()) {
                                endDate = dateAdd + " " + range.getEndTime() + ":00";
                            }
                        }

                    }
                    //周六特殊处理工作日
                    else if (dayType == DayTempType.SIXDAY.getCode()) {
                        if (range.getDayType() == 8) {
                            if (range.getIsOvernight()) {
                                startDate = dateReduce + " " + range.getBeginTime() + ":00";
                                endDate = date + " " + range.getEndTime() + ":00";
                            } else {
                                continue;
                            }
                        }
                        else {
                            startDate = date + " " + range.getBeginTime() + ":00";
                            endDate = date + " " + range.getEndTime() + ":00";
                            //跨日日期加一天
                            if (range.getIsOvernight()) {
                                endDate = dateAdd + " " + range.getEndTime() + ":00";
                            }
                        }
                    }
                    //非周一和周六
                    else {
                        startDate = date + " " + range.getBeginTime() + ":00";
                        endDate = date + " " + range.getEndTime() + ":00";
                        //跨日日期加一天
                        if (range.getIsOvernight()) {
                            endDate = dateAdd + " " + range.getEndTime() + ":00";
                        }
                        if ((dayType > DayTempType.FIRSTDAY.getCode() && dayType <= DayTempType.FIVEDAY.getCode() && range.getDayType() == 8)
                                || (dayType > DayTempType.SIXDAY.getCode() && dayType <= DayTempType.SEVENDAY.getCode() && range.getDayType() == 9)) {
                            isAllowed = true;
                        }

                    }
                }
                //时间比较
                Date startDateTime = sdfDateTime.parse(startDate);
                Date endDateTime = sdfDateTime.parse(endDate);
                if (taxiDateTime.getTime() >= startDateTime.getTime() && taxiDateTime.getTime() <= endDateTime.getTime()) {
                    timeRange = new TimeRange();
                    timeRange.setBeginTime(startDateTime);
                    timeRange.setEndTime(endDateTime);
                    break;
                }
                //特殊处理周末，工作日
                if (isAllowed) {
                    if (range.getIsOvernight()) {
                        startDate = dateReduce + " " + range.getBeginTime() + ":00";
                        endDate = date + " " + range.getEndTime() + ":00";
                        Date startDateTimeW = sdfDateTime.parse(startDate);
                        Date endDateTimeW = sdfDateTime.parse(endDate);
                        if (taxiDateTime.getTime() >= startDateTimeW.getTime() && taxiDateTime.getTime() <= endDateTimeW.getTime()) {
                            timeRange = new TimeRange();
                            timeRange.setBeginTime(startDateTimeW);
                            timeRange.setEndTime(endDateTimeW);
                            break;
                        }
                    } else {
                        continue;
                    }
                }

            }
            return timeRange;
        } catch (ParseException e) {
            logger.error("外卖日期格式化异常" + e.getLocalizedMessage());
        }
        return timeRange;
    }

    /**
     * 获取xx天后日期
     *
     * @param date
     * @param amount
     * @param sdf
     * @return
     */
    private String dateAdd(Date date, int amount, SimpleDateFormat sdf) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DAY_OF_MONTH, amount);
        Date targetDate = c.getTime();
        return sdf.format(targetDate);
    }

    /**
     * 强制提交提示（超标但需要理由）
     *
     * @param takeawayOrderCheckReqContract
     * @return
     */
    private TakeawayOrderRuleCheckResult checkExceedAuth(TakeawayOrderCheckReqV2Contract takeawayOrderCheckReqContract) {
        //默认初始化
        TakeawayOrderRuleCheckResult result = new TakeawayOrderRuleCheckResult();
        result.setResCode(TemporaryResponseCode.Success);
        boolean exceedSubmit = ObjUtils.ifNull(takeawayOrderCheckReqContract.getExceed_submit(), Boolean.FALSE);
        if (exceedSubmit) {
            // 超标后强制提交
            if (StringUtils.isEmpty(takeawayOrderCheckReqContract.getExceed_reason())
                    || takeawayOrderCheckReqContract.getExceed_reason().length() > 50) {
                result.setResCode(TemporaryResponseCode.OrderCheckExceedReasonInvalid);
                return result;
            }
            if (StringUtils.isNotEmpty(takeawayOrderCheckReqContract.getExceed_reason_comment())
                    && takeawayOrderCheckReqContract.getExceed_reason_comment().length() > 200) {
                result.setResCode(TemporaryResponseCode.OrderCheckExceedReasonCommentInvalid);
                return result;
            }
            ReasonConfig reasonConfig = customReasonService.queryReasonConfigWithoutItems(takeawayOrderCheckReqContract.getCompany_id(), ReasonType.EXCEED_ORDER_TAKEAWAY);
            if (reasonConfig.getReason() == SaasMessageConstant.IS_CHECKED_TRUE) {
                if (StringUtils.isBlank(takeawayOrderCheckReqContract.getExceed_reason())) {
                    result.setResCode(TemporaryResponseCode.OrderCheckExceedReasonIsNull);
                    return result;
                }
                if (reasonConfig.getReason_desc() == SaasMessageConstant.IS_CHECKED_TRUE) {
                    if (StringUtils.isBlank(takeawayOrderCheckReqContract.getExceed_reason_comment())) {
                        result.setResCode(TemporaryResponseCode.OrderCheckExceedReasonCommentIsNull);
                        return result;
                    }
                }
            }
        } else {
            // 超标，提示用户
            result.setResCode(TemporaryResponseCode.OrderCheckExceedNeedReason);
            return result;
        }
        return result;
    }

    /**
     * 查询员工外卖权限
     *
     * @param employeeId
     * @param companyId
     */
    private EmployeeBaseRuleV2 queryEmployeeTakeawayRule(String employeeId, String companyId, Integer subSceneId) {
        CheckUtils.create().addCheckBlank(employeeId, CoreLanguage.Common_Exception_EmployeeIdValueNotEmpty.getMessage())
                .addCheckBlank(companyId, CoreLanguage.Common_Exception_CompanyIdValueNotEmpty.getMessage())
                .check();
        try {
            logger.info("queryEmployeeTakeawayRule param {}, {}", employeeId, companyId);
            List<EmployeeBaseRuleV2> takeawayRuleList = iBaseTakeawayRuleExtService.takeawayRuleList(Lists.newArrayList(employeeId), companyId, subSceneId);
            logger.info("queryEmployeeTakeawayRule result {}, {}, {}", employeeId, companyId, takeawayRuleList);
            return takeawayRuleList.get(0);
        }catch (Exception e){
            logger.error("queryEmployeeTakeawayRuleList error: {}", e);
            throw e;
        }


    }

    /**
     * 查询外卖统计数据
     *
     * @param employeeId
     * @param companyId
     * @param beginTime
     * @param endTime
     * @return
     */
    private CompanyPayStatisticsResRpcDTO queryTakeawayStat(String employeeId, String companyId, Date beginTime, Date endTime, int subSceneId) {
        CheckUtils.create().addCheckBlank(employeeId, CoreLanguage.Common_Exception_EmployeeIdValueNotEmpty.getMessage())
                .addCheckBlank(companyId, CoreLanguage.Common_Exception_CompanyIdValueNotEmpty.getMessage())
                .addCheckNull(beginTime, CoreLanguage.Common_Exception_BeginTimeValueNotEmpty.getMessage())
                .addCheckNull(endTime, CoreLanguage.Common_Exception_EndTimeValueNotEmpty.getMessage())
                .check();
        CompanyPayStatisticsReqRpcDTO companyPayStatisticsReqRpcDTO = new CompanyPayStatisticsReqRpcDTO();
        companyPayStatisticsReqRpcDTO.setUserId(employeeId);
        companyPayStatisticsReqRpcDTO.setCompanyId(companyId);
        companyPayStatisticsReqRpcDTO.setCreateTimeBegin(beginTime);
        companyPayStatisticsReqRpcDTO.setCreateTimeEnd(endTime);
        
        // 白名单商家
        List<MessageSetup> messageSetups = messageSetupService.queryWhiteList();
        Set<String> whiteListSet = null;
        if (CollectionUtils.isNotEmpty(messageSetups)) {
            whiteListSet = messageSetups.stream().map(MessageSetup::getCompanyId).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        }
        if (CollectionUtils.isNotEmpty(whiteListSet) && whiteListSet.contains(companyId)) {
            companyPayStatisticsReqRpcDTO.setSubSceneIds(Lists.newArrayList(subSceneId));
            return iSaaSStatisticsService.saasCountUser(companyPayStatisticsReqRpcDTO);
        }
        
        // 非白名单
        // 查询该员工具有的场景id权限
        List<EmployeeBaseRule> authList = iBaseEmployeeRuleService.queryRuleListByCompanyIdAndEmployeeIds(companyId, Lists.newArrayList(employeeId), CategoryTypeEnum.Takeaway.getCode(), null);
        if(ObjUtils.isEmpty(authList)) {
            throw new FinhubException(-1, CoreLanguage.Common_Exception_TakeawayRuleNotFound.getMessage());
        }

        EmployeeBaseRule employeeBaseRule = authList.get(0);
        logger.info("employeeBaseRule={}, subSeceneId={}", JsonUtils.toJson(employeeBaseRule), subSceneId);
        /**
         * ruleSwitch 使用权限：true:有权限 false：无权限
         * ruleFlag   规则是否限制：true：限制 false：无限制
         **/
        if(!employeeBaseRule.getRuleSwitch()) {
            logger.info("未查到外卖权限2:employeeBaseRule={}", JsonUtils.toJson(employeeBaseRule));
            throw new FinhubException(-1, CoreLanguage.Common_Exception_TakeawayAuthNotFound.getMessage());
        }
        if(!employeeBaseRule.getRuleFlag()) {
            /** 场景列表 */
            List<TakeawayManageSetting> list = takeawayCheckV4Service.initSceneAndGetSceneList(companyId);
            //权限筛选
//            List<TakeawayManageSetting> list = takeawayCheckV4Service.sceneTypeAuthFilterList(companyId, list0);
            //不限制规则时，返回全部场景(外卖只显示默认的那个即可)
            List<Integer> defaultTakeawaySceneIds = Arrays.stream(TakeawayCategoryType.values()).map(TakeawayCategoryType::getCode).collect(Collectors.toList());
            List<Integer> sceneIdsDefault = list.stream().map(TakeawayManageSetting::getSceneId).filter(defaultTakeawaySceneIds::contains).collect(Collectors.toList());
            companyPayStatisticsReqRpcDTO.setSubSceneIds(sceneIdsDefault);
            return iSaaSStatisticsService.saasCountUser(companyPayStatisticsReqRpcDTO);
        }
        
        // 如果是新版的场景：sceneId >= 20时，直接取 subSceneId
        if (subSceneId >= 20) {
            companyPayStatisticsReqRpcDTO.setSubSceneIds(Lists.newArrayList(subSceneId));
        } else {
            // 旧版场景（场景id: 1-6）,查询其他的已配置的场景id，并赋值
            // 查询该员工具有的场景id权限
            List<Integer> sceneIds = getSceneIdsBy(companyId, employeeBaseRule);

            //只保留基础场景（1-6）
            List<Integer> defaultTakeawaySceneIds = Arrays.stream(TakeawayCategoryType.values()).map(TakeawayCategoryType::getCode).collect(Collectors.toList());
            List<Integer> sceneIdsDefault = sceneIds.stream().filter(defaultTakeawaySceneIds::contains).collect(Collectors.toList());
            companyPayStatisticsReqRpcDTO.setSubSceneIds(sceneIdsDefault);
        }
        return iSaaSStatisticsService.saasCountUser(companyPayStatisticsReqRpcDTO);
    }

    /**
     * @MethodName getSceneIdsBy
     * @Description 获取场景id
     * @param: companyId
     * @param: employeeBaseRule
     * @return: java.util.List<java.lang.Integer>
     * <AUTHOR> Yunpeng
     * @Date 2022/10/11 15:29
     */
    private List<Integer> getSceneIdsBy(String companyId, EmployeeBaseRule employeeBaseRule) {
        List<String> ruleIdsString = employeeBaseRule.getRuleIds();
        List<Integer> ruleIds = ruleIdsString.stream().map(Integer::parseInt).collect(Collectors.toList());
        TakeawayRuleExample takeawayRuleExample = new TakeawayRuleExample();
        takeawayRuleExample.createCriteria().andCompanyIdEqualTo(companyId).andIdIn(ruleIds);
        List<TakeawayRule> takeawayRuleList = takeawayRuleMapper.selectByExample(takeawayRuleExample);
        log.info("takeawayRuleList: {}", JSON.toJSON(takeawayRuleList));
        if (ObjUtils.isEmpty(takeawayRuleList)) {
            log.info("takeaway sceneList not exist: {}", ruleIds);
            throw new FinhubException(-1, CoreLanguage.Common_Exception_TakeawayRuleNotFound.getMessage());
        }

        List<Integer> sceneIds = takeawayRuleList.stream().map(TakeawayRule::getSceneId).collect(Collectors.toList());
        return sceneIds;
    }

    /**
     * 获取星期几
     *
     * @param time yyyy-MM-dd HH:mm:ss
     * @return
     */
    private Integer getDayType(String time) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(DateUtils.parse(time));
        Integer dayType = calendar.get(Calendar.DAY_OF_WEEK);
        return dayType == 1 ? 7 : dayType - 1;
    }

    /**
     * 添加外卖拦截记录信息
     *
     * @param takeawayOrderCheckReqContract
     * @param ruleCheckResult
     */
    private void initTakeawayInterceptRecord(TakeawayOrderCheckReqV2Contract takeawayOrderCheckReqContract, TempOrderRuleCheckResult ruleCheckResult) {
        TakeawayInterceptRecord takeawayInterceptRecord = new TakeawayInterceptRecord();
        //拦截参数
        try {
            TakeawayAuthContract employeeTakeawayRule = ruleCheckResult.getTakeawayRule();
            TakeawayContract takeawayRule = null;
            if (ObjUtils.isNotEmpty(employeeTakeawayRule) && employeeTakeawayRule.getRule_limit_flag()) {
                takeawayRule = employeeTakeawayRule.getRule_info();
            }
            takeawayInterceptRecord.setId(IDTool.CreateUniqueID());
            takeawayInterceptRecord.setEmployeeId(takeawayOrderCheckReqContract.getEmployee_id());
            takeawayInterceptRecord.setCompanyId(takeawayOrderCheckReqContract.getCompany_id());
            takeawayInterceptRecord.setCreateTime(new Date());
            takeawayInterceptRecord.setContactName(takeawayOrderCheckReqContract.getContact_name());
            takeawayInterceptRecord.setContactPhone(takeawayOrderCheckReqContract.getContact_phone());
            takeawayInterceptRecord.setChannel(takeawayOrderCheckReqContract.getChannel());
            takeawayInterceptRecord.setTotalPrice(takeawayOrderCheckReqContract.getOrder_price());
            takeawayInterceptRecord.setTakeawayRule(employeeTakeawayRule == null ? -1 : employeeTakeawayRule.getTakeaway_rule());
            takeawayInterceptRecord.setTakeawayRuleFlag(employeeTakeawayRule == null ? false : employeeTakeawayRule.getRule_limit_flag());
            takeawayInterceptRecord.setLimitTime(takeawayRule == null ? false : takeawayRule.getLimitTime());
            takeawayInterceptRecord.setTakeawayTimeRangeInfo(takeawayRule == null ? null : JsonUtils.toJson(takeawayRule.getTimeRangeList()));
            takeawayInterceptRecord.setLimitLocation(takeawayRule == null ? false : takeawayRule.getLimitLocation());
            takeawayInterceptRecord.setTakeawayLocationInfo(takeawayRule == null ? null : JsonUtils.toJson(takeawayRule.getTakeawayLocationIds()));
            takeawayInterceptRecord.setErrCode(ruleCheckResult.getErrCode());
            String errorMsg = ruleCheckResult.getErrMsg() + (StringUtils.isEmpty(ruleCheckResult.getExceed_msg()) ? "" : "|" + ruleCheckResult.getExceed_msg());
            takeawayInterceptRecord.setErrMsg(errorMsg);
            //takeawayInterceptRecord.setCostCenterId(takeawayOrderCheckReqContract.getAttribution_id());
            //takeawayInterceptRecord.setCostCenterType(takeawayOrderCheckReqContract.getAttribution_category());
            takeawayInterceptRecord.setExceedBuyType(employeeTakeawayRule == null ? -1 : employeeTakeawayRule.getExceed_buy_type());
            takeawayInterceptRecord.setPersonalPay(employeeTakeawayRule == null ? false : employeeTakeawayRule.getPersonal_pay());
            //处理拦截信息
            takeawayInterceptRecordMapper.insertSelective(takeawayInterceptRecord);
        } catch (Exception e) {
            logger.error("添加外卖拦截记录信息:{},发生异常:{}", JsonUtils.toJson(takeawayInterceptRecord), e.getLocalizedMessage());
        }
    }

    /**
     * 外卖限额个人支付提示
     *
     * @param personalPay
     * @param companyPayPrice
     * @param totalPrice
     * @param couponAmount
     * @return
     */
    public LimitPayTipContract priceLimitPayTips(Boolean personalPay, BigDecimal companyPayPrice, BigDecimal totalPrice, BigDecimal couponAmount) {
        LimitPayTipContract limitPayTipContract = new LimitPayTipContract();
        limitPayTipContract.setTip_flag(false);
        // 未开启个人支付
        if (!personalPay) {
            return limitPayTipContract;
        }
        String title = CoreLanguage.TakeawayCheckServiceImpl_Value_OverTitle.getMessage();
//        BigDecimal priceLimit = takeawayRuleCheckResult.getPriceLimit();
//        BigDecimal sumPriceLimit = takeawayRuleCheckResult.getSumPriceLimit();
//        if (priceLimit != null && priceLimit.compareTo(BigDecimal.ZERO) >= 0
//                && sumPriceLimit != null && sumPriceLimit.compareTo(BigDecimal.ZERO) > 0) {
//            limitPayTipContract.setTip_flag(true);
//            LimitPayTipContract.Tip tip = new LimitPayTipContract.Tip();
//            tip.setTitle(title);
//            tip.setContent(String.format("您的单次外卖上限为%s元，时段外卖上限为%s元，超出部分将需要您进行个人支付。", priceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString(), sumPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
//            limitPayTipContract.setTip(tip);
//        } else if (priceLimit != null && priceLimit.compareTo(BigDecimal.ZERO) >= 0) {
//            limitPayTipContract.setTip_flag(true);
//            LimitPayTipContract.Tip tip = new LimitPayTipContract.Tip();
//            tip.setTitle(title);
//            tip.setContent(String.format("您的单次外卖上限为%s元，超出部分将需要您进行个人支付。", priceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
//            limitPayTipContract.setTip(tip);
//        } else if (sumPriceLimit != null && sumPriceLimit.compareTo(BigDecimal.ZERO) > 0) {
//            limitPayTipContract.setTip_flag(true);
//            LimitPayTipContract.Tip tip = new LimitPayTipContract.Tip();
//            tip.setTitle(title);
//            tip.setContent(String.format("您的时段外卖上限为%s元，超出部分将需要您进行个人支付。", sumPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
//            limitPayTipContract.setTip(tip);
//        }
        if (totalPrice.compareTo(companyPayPrice) > 0) {
            BigDecimal personalPayPrice = totalPrice.subtract(companyPayPrice);
            companyPayPrice = companyPayPrice.subtract(couponAmount).max(BigDecimal.ZERO);
            limitPayTipContract.setTip_flag(true);
            LimitPayTipContract.Tip tip = new LimitPayTipContract.Tip();
            tip.setTitle(title);
            tip.setContent(StrUtils.formatString(CoreLanguage.TakeawayCheckServiceImpl_Value_OverContent.getMessage(),
                    companyPayPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString(),
                    personalPayPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
            limitPayTipContract.setTip(tip);
        }
        return limitPayTipContract;
    }
}
