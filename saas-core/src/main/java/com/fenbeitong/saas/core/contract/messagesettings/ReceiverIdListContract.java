package com.fenbeitong.saas.core.contract.messagesettings;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by zhangkai on 2017/6/7.
 */
public class ReceiverIdListContract {

    private String company_id;
    private String source;
    private String item_code;
    //直接上级 前端配置 1：选中直接上级 2：未选中直接上级
    private Integer direct_leader;
    //部门主管 前端配置 1：选中部门主管 2：未选中部门主管
    private Integer depart_leader;
    private List<String> receiver_list = new ArrayList<>();

    public ReceiverIdListContract() {
    }

    public ReceiverIdListContract(String item_code, List<String> receiver_list) {
        this.item_code = item_code;
        this.receiver_list = receiver_list;
    }

    public Integer getDirect_leader() {
        return direct_leader;
    }

    public void setDirect_leader(Integer direct_leader) {
        this.direct_leader = direct_leader;
    }

    public Integer getDepart_leader() {
        return depart_leader;
    }

    public void setDepart_leader(Integer depart_leader) {
        this.depart_leader = depart_leader;
    }

    public String getCompany_id() {
        return company_id;
    }

    public void setCompany_id(String company_id) {
        this.company_id = company_id;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getItem_code() {
        return item_code;
    }

    public void setItem_code(String item_code) {
        this.item_code = item_code;
    }

    public List<String> getReceiver_list() {
        return receiver_list;
    }

    public void setReceiver_list(List<String> receiver_list) {
        this.receiver_list = receiver_list;
    }

}
