package com.fenbeitong.saas.core.contract.customrole;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;

/**
 * Created by ch<PERSON><PERSON> on 2017/4/21.
 */
public class CustomRoleDetailContract {
    @<PERSON><PERSON><PERSON>ield(ordinal = 0)
    String id;
    @JSO<PERSON>ield(ordinal = 1)
    String name;
    @JSONField(ordinal = 2)
    long count;
    @JSONField(ordinal = 3)
    int item_type;
    @JSONField(ordinal = 4)
    String role_code;
    @JSONField(ordinal = 5)
    int role_type;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public long getCount() {
        return count;
    }

    public void setCount(long count) {
        this.count = count;
    }

    public int getItem_type() {
        return item_type;
    }

    public void setItem_type(int item_type) {
        this.item_type = item_type;
    }

    public String getRole_code() {
        return role_code;
    }

    public void setRole_code(String role_code) {
        this.role_code = role_code;
    }

    public int getRole_type() {
        return role_type;
    }

    public void setRole_type(int role_type) {
        this.role_type = role_type;
    }

    public CustomRoleDetailContract() {
    }

    public CustomRoleDetailContract(String id, String name, long count, int item_type) {
        this.id = id;
        this.name = name;
        this.count = count;
        this.item_type = item_type;
    }

    public CustomRoleDetailContract(String id, String name, long count, int item_type, String role_code, int role_type) {
        this.id = id;
        this.name = name;
        this.count = count;
        this.item_type = item_type;
        this.role_code = role_code;
        this.role_type = role_type;
    }

    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }
}
