package com.fenbeitong.saas.core.contract.organization;

import com.alibaba.fastjson.JSONObject;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/6/3.
 */
public class EmployeeOperateRespContract {

    private IdBean result;
    private JSONObject sync_data;

    public IdBean getResult() {
        return result;
    }

    public void setResult(IdBean result) {
        this.result = result;
    }

    public JSONObject getSync_data() {
        return sync_data;
    }

    public void setSync_data(JSONObject sync_data) {
        this.sync_data = sync_data;
    }

    public EmployeeOperateRespContract() {
    }

    public EmployeeOperateRespContract(IdBean result, JSONObject sync_data) {
        this.result = result;
        this.sync_data = sync_data;
    }

    public static class IdBean{
        private String id ;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public IdBean() {
        }

        public IdBean(String id) {
            this.id = id;
        }
    }
}
