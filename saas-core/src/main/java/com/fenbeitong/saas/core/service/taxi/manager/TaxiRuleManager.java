package com.fenbeitong.saas.core.service.taxi.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.noc.api.car.resp.CarOrderConsumeInfo;
import com.fenbeitong.saas.api.model.dto.rule.EmployeeTaxiRuleInfo;
import com.fenbeitong.saas.api.model.dto.rule.RuleTimeRange;
import com.fenbeitong.saas.api.model.enums.TaxiSubType;
import com.fenbeitong.saas.core.common.base.SaasException;
import com.fenbeitong.saas.core.common.constant.GlobalResponseCode;
import com.fenbeitong.saas.core.common.constant.SaasOrderThirdRuleConstant;
import com.fenbeitong.saas.core.common.constant.TemporaryResponseCode;
import com.fenbeitong.saas.core.contract.apply.ApplyTaxiContract;
import com.fenbeitong.saas.core.contract.apply.ApplyThirdContract;
import com.fenbeitong.saas.core.contract.apply.ApplyTripInfoContract;
import com.fenbeitong.saas.core.contract.applyflow.KvContract;
import com.fenbeitong.saas.core.contract.order.check.TaxiApplyRule;
import com.fenbeitong.saas.core.contract.order.check.TaxiOrderCheckRule;
import com.fenbeitong.saas.core.contract.rule.TaxiRuleListContract;
import com.fenbeitong.saas.core.dao.dynamic.ApplyTripInfoMapper;
import com.fenbeitong.saas.core.language.CoreLanguage;
import com.fenbeitong.saas.core.model.enums.apply.ApplyStatus;
import com.fenbeitong.saas.core.model.enums.apply.ApplyType;
import com.fenbeitong.saas.core.model.enums.rule.AllowedTaxiType;
import com.fenbeitong.saas.core.model.enums.rule.ExceedAllowType;
import com.fenbeitong.saas.core.model.enums.rule.taxi.AllowSameCityLimitType;
import com.fenbeitong.saas.core.model.saas.ApplyOrder;
import com.fenbeitong.saas.core.model.saas.ApplyRuleSetting;
import com.fenbeitong.saas.core.model.saas.ApplyRuleSettingExample;
import com.fenbeitong.saas.core.model.saas.ApplyTripInfo;
import com.fenbeitong.saas.core.service.IApplyV2Service;
import com.fenbeitong.saas.core.service.IRuleService;
import com.fenbeitong.saas.core.service.taxi.helper.TaxiApplyHelper;
import com.fenbeitong.saas.core.service.taxi.helper.TaxiRuleTimeHelper;
import com.fenbeitong.saas.core.utils.tools.BigDecimalTool;
import com.fenbeitong.saas.core.utils.tools.VersionTool;
import com.fenbeitong.saasplus.api.model.dto.rule.*;
import com.fenbeitong.saasplus.api.model.dto.rule.taxi.TaxiTypeBasedPriceFloatingConfigVO;
import com.fenbeitong.saasplus.api.model.enums.rule.CityRestrictionType;
import com.fenbeitong.saasplus.api.model.enums.rule.SpecifyCityType;
import com.fenbeitong.saasplus.api.model.enums.rule.TaxiApproveCityLimitTypeEnum;
import com.fenbeitong.saasplus.api.model.enums.rule.TaxiUsedTypeEnum;
import com.fenbeitong.saasplus.api.model.enums.savings.LimitType;
import com.fenbeitong.saasplus.api.model.po.fenbeitong.rulenew.TimesLimitTypeEnum;
import com.fenbeitong.saasplus.api.model.po.fenbeitong.taxi.TaxiExceedConfigPo;
import com.fenbeitong.saasplus.api.model.vo.rule.TaxiExceedConfigQueryRes;
import com.fenbeitong.saasplus.api.service.messageSetup.IMessageSetupService;
import com.fenbeitong.saasplus.api.service.rule.ITaxiApproveRuleService;
import com.fenbeitong.usercenter.api.model.dto.rule.CarPolicyBean;
import com.fenbeitong.usercenter.api.model.dto.rule.RuleIdDto;
import com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeTaxiRuleExtService;
import com.google.common.collect.Lists;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.StrUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 *     用车叫车前管控业务处理
 * </p>
 */
@Slf4j
@Component
public class TaxiRuleManager {

    private Logger logger = LoggerFactory.getLogger(TaxiRuleManager.class);

    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyOrderMapper applyOrderMapper;
    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyRuleSettingMapper applyRuleSettingMapper;
    @Autowired
    private IRuleService ruleService;
    @Autowired
    private TaxiUserUsedDataManager taxiUserUsedDataManager;
    @Autowired
    private ITaxiApproveRuleService iTaxiApproveRuleService;
    @Autowired
    private IBaseEmployeeTaxiRuleExtService iBaseEmployeeTaxiRuleExtService;

    @Autowired
    private IMessageSetupService iMessageSetupRpcService;

    @Autowired
    private ApplyTripInfoMapper applyTripMapper;

    @Autowired
    private IApplyV2Service iApplyV2Service;

    public TaxiApplyRule queryTaxiApplyRule(String employeeId, String companyId, String applyId, String departure_time,
                                             Boolean addOnTrip, String orderId, Integer ruleId, String tripId,
                                             String departureCityId, String arrivalCityId) {

        TaxiApplyRule taxiApplyRule = getTaxiApplyRuleWithoutUseInfo(applyId,tripId, companyId, employeeId);
        logger.info("queryTaxiApplyRule departureCityId={},arrivalCityId={}, taxiApplyRule:{}", departureCityId,
            arrivalCityId, JsonUtils.toJson(taxiApplyRule));
        // 只有按照城市分组限制时  需要根据城市过滤订单
        if (!TaxiApplyHelper.isCityPriceLimit(taxiApplyRule)) {
            departureCityId = null;
            arrivalCityId = null;
        }
        setApplyUsedInfo(employeeId, companyId, applyId, taxiApplyRule, departure_time, addOnTrip, orderId, ruleId, tripId,
                departureCityId, arrivalCityId);
        logger.info("申请用车规则, taxiApplyRule: {}", JsonUtils.toJson(taxiApplyRule));
        return taxiApplyRule;
    }

    public TaxiApplyRule getTaxiApplyRuleWithoutUseInfo(String applyId, String tripId, String companyId, String employeeId) {
        ApplyOrder applyOrder = applyOrderMapper.selectByPrimaryKey(applyId);
        ApplyRuleSettingExample example = new ApplyRuleSettingExample();
        ApplyRuleSettingExample.Criteria criteria = example.createCriteria();
        criteria.andApplyOrderIdEqualTo(applyId);
        if (applyOrder != null && applyOrder.getType() != ApplyType.ApplyTaxi.getValue()
                && ObjUtils.isNotBlank(tripId)) {
            criteria.andTripIdEqualTo(tripId);
        }
        logger.info("查询用车规则快照, applyId:{}, tripId:{}", applyId, tripId);
        List<ApplyRuleSetting> applyRuleSettingList = applyRuleSettingMapper.selectByExample(example);
        // 兼容tripId为空的情况
        if (ObjUtils.isEmpty(applyRuleSettingList)) {
            example.clear();
            example.createCriteria().andApplyOrderIdEqualTo(applyId);

            applyRuleSettingList = applyRuleSettingMapper.selectByExample(example);
        }
        logger.info("查询用车规则快照, applyRuleSettingList:{}", JsonUtils.toJson(applyRuleSettingList));
        if (ObjUtils.isEmpty(applyRuleSettingList)) {
            throw new SaasException(GlobalResponseCode.ApplyRuleDataError.getCode(), GlobalResponseCode.ApplyRuleDataError.getMsg());
        }
        ApplyRuleSetting applyRuleSetting = applyRuleSettingList.get(0);
        if (ObjUtils.isEmpty(applyRuleSetting.getTaxiInfo())) {
            throw new SaasException(GlobalResponseCode.ApplyRuleDataError.getCode(), GlobalResponseCode.ApplyRuleDataError.getMsg());
        }
        TaxiApplyRule taxiApplyRule = json2TaxiApplyRule(applyRuleSetting.getTaxiInfo(), companyId, employeeId);
        logger.info("queryTaxiApplyRule taxiApplyRule:{}", JsonUtils.toJson(taxiApplyRule));
        return taxiApplyRule;
    }

    private void setApplyUsedInfo(String userId, String companyId, String applyId, TaxiApplyRule taxiApplyRule,
                                  String departureTime, Boolean addOnTrip, String orderId, Integer ruleId,
                                  String tripId, String departureCityId, String arrivalCityId) {
        BigDecimal ruleDayPriceLimit;
        if (ObjUtils.isBlank(ruleId)) {
            ruleDayPriceLimit = BigDecimal.ZERO;
        } else {
            logger.info("根据ruleId查询用车规则，ruleId：{}", ruleId);
            TaxiRuleListContract taxiRuleListContract = ruleService.queryTaxiRuleDetailv3(companyId, ruleId);
            logger.info("根据ruleId查询用车规则，结果：{}", taxiRuleListContract);
            if (taxiRuleListContract == null) {
                throw new SaasException(TemporaryResponseCode.OrderTaxiCheckRuleNotExist.getCode(), TemporaryResponseCode.OrderTaxiCheckRuleNotExist.getMsg());
            }
            ruleDayPriceLimit = taxiRuleListContract.getDayPriceLimit();
        }
        setApplyUsedInfo(userId, companyId, applyId, taxiApplyRule, departureTime, addOnTrip, orderId, ruleDayPriceLimit,
                tripId, departureCityId, arrivalCityId);
    }

    private void setApplyUsedInfo(String userId, String companyId, String applyId, TaxiApplyRule taxiApplyRule,
                                  String departureTime, Boolean addOnTrip, String orderId, BigDecimal ruleDayPriceLimit,
                                  String tripId, String departureCityId, String arrivalCityId) {
        if (ObjUtils.isEmpty(taxiApplyRule)) {
            return;
        }
        if (TaxiApplyHelper.isNotPriceLimit(taxiApplyRule) && taxiApplyRule.getTimesLimitFlag() == 0) {
            return;
        }
        Integer priceAcrossCityFlag = taxiApplyRule.getPriceAcrossCityFlag();

        // 该申请关联的相关使用信息
        ApplyTaxiContract applyTaxiContract = taxiUserUsedDataManager.getApplyUsed(priceAcrossCityFlag, userId, companyId, applyId, departureTime,
                addOnTrip, orderId, tripId, departureCityId, arrivalCityId);

        taxiApplyRule.setApplyPriceUsed(ObjUtils.toBigDecimal(applyTaxiContract.getUsed_amount(), BigDecimal.ZERO));
        taxiApplyRule.setApplyDayPriceUsed(ObjUtils.toBigDecimal(applyTaxiContract.getUsed_date_amount(), BigDecimal.ZERO));
        taxiApplyRule.setApplyTimesUsed(ObjUtils.toInteger(applyTaxiContract.getUsed_times(), 0));
        taxiApplyRule.setApplyDayTimesUsed(ObjUtils.toInteger(applyTaxiContract.getDay_used_times(), 0));

        taxiApplyRule.setCityApplyPriceUsed(ObjUtils.toBigDecimal(applyTaxiContract.getCity_used_amount(), BigDecimal.ZERO));
        taxiApplyRule.setCityApplyDayPriceUsed(ObjUtils.toBigDecimal(applyTaxiContract.getCity_used_date_amount(), BigDecimal.ZERO));
        taxiApplyRule.setCityApplyTimesUsed(ObjUtils.toInteger(applyTaxiContract.getCity_used_times(), 0));

        taxiApplyRule.setRuleDayPriceLimit(BigDecimal.ZERO);
        taxiApplyRule.setRuleDayPriceUsed(BigDecimal.ZERO);

        // 如果设置了按单日限额，则查询计算出该用户当日申请用车使用额度
        if(taxiApplyRule.getPerDayLimitPrice()!=null && taxiApplyRule.getPerDayLimitPrice().compareTo(BigDecimal.ZERO)>0){
            taxiUserUsedDataManager.setSceneUsedQuota(priceAcrossCityFlag, userId, companyId, departureTime, addOnTrip, orderId,
                    taxiApplyRule.getSceneType(), departureCityId, arrivalCityId,taxiApplyRule);
        }
        Boolean timesLimitFlag = ObjUtils.toInteger(taxiApplyRule.getTimesLimitFlag(), 0) != 0;
        Integer timesLimitFlagIntVal = ObjUtils.toInteger(taxiApplyRule.getTimesLimitFlag(), 0);
        Integer timesLimitType = ObjUtils.toInteger(taxiApplyRule.getTimesLimitType(), TimesLimitTypeEnum.total_count_limit.getCode());
        Integer timesLimit = ObjUtils.toInteger(taxiApplyRule.getTimesLimit(), 0);
        if (timesLimitFlag && timesLimit > 0 && Objects.equals(timesLimitFlagIntVal, LimitType.LIMIT.getCode()) && Objects.equals(timesLimitType,TimesLimitTypeEnum.day_count_limit.getCode())) {
            List<CarOrderConsumeInfo> list = taxiUserUsedDataManager.getUserCustomizeDailyCarOrders(userId, companyId, departureTime, 0, 0, taxiApplyRule.getSceneType());
            log.info("场景单日次数限制.list：{}", JsonUtils.toJson(list));
            long dayUsed = list.stream().filter(CarOrderConsumeInfo::getUseApply).count();
            taxiApplyRule.setSceneDayTimesUsed(Long.valueOf(dayUsed).intValue());
        }
        // 合并用车剩余额度
        if (taxiApplyRule.getUseRuleDayPriceLimit() != 1) {
            return;
        }
        if (ruleDayPriceLimit.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }

        // 查询用户用车日累计用车消费，如果没有设置用车剩余额度，则下面逻辑不需要走
        BigDecimal ruleDayPriceUsed = taxiUserUsedDataManager.getUserDailyConsumption(priceAcrossCityFlag, userId, companyId, departureTime, addOnTrip, orderId,
                TaxiSubType.Taxi.getSubtype(), departureCityId, arrivalCityId);
        taxiApplyRule.setRuleDayPriceLimit(ruleDayPriceLimit);
        taxiApplyRule.setRuleDayPriceUsed(ruleDayPriceUsed);
    }

    public TaxiApplyRule getTaxiAddApplyRule(String employeeId, String companyId, String applyId, String departure_time,
                                              Boolean addOnTrip, String orderId, EmployeeTaxiRuleInfo employeeTaxiRuleInfo,
                                              String tripId, String departureCityId, String arrivalCityId) {
        // 走快照获取申请用车规则
        ApplyRuleSettingExample example = new ApplyRuleSettingExample();
        ApplyRuleSettingExample.Criteria criteria = example.createCriteria();
        criteria.andApplyOrderIdEqualTo(applyId);
        if (ObjUtils.isNotBlank(tripId)) {
            criteria.andTripIdEqualTo(tripId);
        }
        List<ApplyRuleSetting> applyRuleSettingList = applyRuleSettingMapper.selectByExample(example);
        // 兼容tripId为空的情况
        if (ObjUtils.isEmpty(applyRuleSettingList)) {
            example.clear();
            example.createCriteria().andApplyOrderIdEqualTo(applyId);
        }
        applyRuleSettingList = applyRuleSettingMapper.selectByExample(example);
        logger.info("获取申请用车的快照规则, applyRuleSettingList: {}", JsonUtils.toJson(applyRuleSettingList));
        if (CollectionUtils.isEmpty(applyRuleSettingList)) {
            throw new SaasException(GlobalResponseCode.ApplyRuleDataError.getCode(), GlobalResponseCode.ApplyRuleDataError.getMsg());
        }
        ApplyRuleSetting applyRuleSetting = applyRuleSettingList.get(0);
        if (ObjUtils.isEmpty(applyRuleSetting.getTaxiInfo())) {
            throw new SaasException(GlobalResponseCode.ApplyRuleDataError.getCode(), GlobalResponseCode.ApplyRuleDataError.getMsg());
        }
        TaxiApplyRule taxiApplyRule = json2TaxiApplyRule(applyRuleSetting.getTaxiInfo(), companyId,employeeId);
        Integer cityLimitType = taxiApplyRule.getCityLimitType();
        logger.info("TaxiApprovePriceLimitType:{}", cityLimitType);
        // 只有按照城市分组限制时  需要根据城市过滤订单
        if (TaxiApproveCityLimitTypeEnum.CITY_LEVEL.getType() != cityLimitType
                && TaxiApproveCityLimitTypeEnum.FIX_CITY.getType() != cityLimitType) {
            departureCityId = null;
            arrivalCityId = null;
        }
        setApplyUsedInfo(employeeId, companyId, applyId, taxiApplyRule, departure_time, addOnTrip, orderId, employeeTaxiRuleInfo.getRuleDayPriceLimit(), tripId, departureCityId, arrivalCityId);

        return taxiApplyRule;
    }

    public TaxiRuleListContract getTaxiAddRule(EmployeeTaxiRuleInfo employeeTaxiRuleInfo, TaxiRuleListContract taxiRuleListContract) {
        TaxiRuleListContract taxiRule = new TaxiRuleListContract();
        //车型
        taxiRule.setLimitTaxiType(employeeTaxiRuleInfo.getTaxiTypeLimitFlag());
        if (employeeTaxiRuleInfo.getTaxiTypeLimitFlag() && StringUtils.isNotEmpty(employeeTaxiRuleInfo.getAllowedTaxiType())) {
            List<KvContract> allowedTaxiTypes = Lists.newArrayList();
            String[] allowTaxiTypeList = employeeTaxiRuleInfo.getAllowedTaxiType().split(",");
            for (String taxiType : allowTaxiTypeList) {
                allowedTaxiTypes.add(new KvContract(ObjUtils.toInteger(taxiType), AllowedTaxiType.getName(ObjUtils.toInteger(taxiType))));
            }
            taxiRule.setAllowedTaxiType(allowedTaxiTypes);
        }
        taxiRule.setPriceLimitFlag(employeeTaxiRuleInfo.getPriceLimitFlag());
        taxiRule.setPriceLimit(employeeTaxiRuleInfo.getPriceLimit());
        taxiRule.setDayPriceLimit(employeeTaxiRuleInfo.getDayPriceLimit());
        taxiRule.setTaxiSchedulingFee(employeeTaxiRuleInfo.getTaxiSchedulingFee());
        taxiRule.setLimitAdvance(employeeTaxiRuleInfo.getLimitAdvance());
        taxiRule.setCityLimitType(employeeTaxiRuleInfo.getCityLimitType());
        taxiRule.setPriceAcrossCityFlag(employeeTaxiRuleInfo.getPriceAcrossCityFlag());
        taxiRule.setExtraPerCountLimitPrice(employeeTaxiRuleInfo.getExtraPerCountLimitPrice());
        taxiRule.setExtraPerDayLimitPrice(employeeTaxiRuleInfo.getExtraPerDayLimitPrice());
        // 分组信息实时查询
        taxiRule.setCityPriceList(taxiRuleListContract.getCityPriceList());
        return taxiRule;
    }

    /**
     * 获取员工规则信息
     *
     * @param carPolicyBean
     * @param taxiOrderCheckRule
     * @param clientVersion
     * @return
     */
    public EmployeeTaxiRuleInfo getEmployeeTaxiRule(CarPolicyBean carPolicyBean, TaxiOrderCheckRule taxiOrderCheckRule, String clientVersion) {
        EmployeeTaxiRuleInfo employeeTaxiRuleInfoContract = new EmployeeTaxiRuleInfo();
        if (carPolicyBean == null) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull, CoreLanguage.Common_Exception_UserRuleIsEmpty.getMessage());
        }
        employeeTaxiRuleInfoContract.setExceedBuyType(carPolicyBean.getExceed_buy_type());
        employeeTaxiRuleInfoContract.setTaxiRuleFlag(carPolicyBean.getRule_limit_flag());
        employeeTaxiRuleInfoContract.setAllowShuttle(carPolicyBean.getAllow_shuttle() == null ? false : carPolicyBean.getAllow_shuttle());
        employeeTaxiRuleInfoContract.setPersonalPay(carPolicyBean.getPersonal_pay() == null ? false : carPolicyBean.getPersonal_pay());
        if (carPolicyBean.getCar_priv_flag() == null || !carPolicyBean.getCar_priv_flag()) {
            employeeTaxiRuleInfoContract.setTaxiRule(false);
        } else {
            employeeTaxiRuleInfoContract.setTaxiRule(true);
        }
        employeeTaxiRuleInfoContract.setPriceLimitFlag(false);
        employeeTaxiRuleInfoContract.setPriceLimit(BigDecimal.ZERO);
        employeeTaxiRuleInfoContract.setDayPriceLimitFlag(false);
        employeeTaxiRuleInfoContract.setDayPriceLimit(BigDecimal.ZERO);
        employeeTaxiRuleInfoContract.setAllowCalledForOther(true);
        employeeTaxiRuleInfoContract.setTaxiTypeLimitFlag(false);
        employeeTaxiRuleInfoContract.setLimitAdvance(false);
        employeeTaxiRuleInfoContract.setRuleLimitAdvance(false);
        if (carPolicyBean.getRule_limit_flag() != null && carPolicyBean.getRule_limit_flag()) {
            Integer useRule = taxiOrderCheckRule.getUseRule();
            if (useRule != 1 && useRule != 2 && useRule != 3) {
                logger.error("使用规则类型未知, useRule:{}", useRule);
                return employeeTaxiRuleInfoContract;
            }
            if (useRule == 1) {//普通用车规则
                TaxiRuleListContract taxiRuleListContract = taxiOrderCheckRule.getTaxiRuleListContract();
                setEmployeeTaxiRule(employeeTaxiRuleInfoContract, taxiRuleListContract);
            }
            if (useRule == 2) {//审批用车审批单快照规则
                TaxiApplyRule taxiApplyRule = taxiOrderCheckRule.getTaxiApplyRule();
                setEmployeeTaxiRule(employeeTaxiRuleInfoContract, taxiApplyRule, taxiOrderCheckRule.getCompanyId());
            }
            if (useRule == 3) {//审批用车规则
                TaxiApproveRuleContract taxiApproveRuleContract = taxiOrderCheckRule.getTaxiApproveRuleContract();
                setEmployeeTaxiRule(employeeTaxiRuleInfoContract, taxiApproveRuleContract);
            }
        }
        adaptVersion4EmployeeTaxiRuleInfo(employeeTaxiRuleInfoContract, clientVersion);
        return employeeTaxiRuleInfoContract;
    }

    public EmployeeTaxiRuleInfo getEmployeeTaxiPolicy(String employeeId, String companyId, String applyId, String clientVersion) {
        // 员工用车权限
        CarPolicyBean carPolicyBean = iBaseEmployeeTaxiRuleExtService.getCarPolicyBean(employeeId, companyId);
        logger.info("员工:{} 用车权限, carPolicyBean:{}", employeeId, JSON.toJSONString(carPolicyBean));
        if (carPolicyBean == null) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull, CoreLanguage.Common_Exception_UserRuleIsEmpty.getMessage());
        }
        EmployeeTaxiRuleInfo employeeTaxiRuleInfoContract = new EmployeeTaxiRuleInfo();
        employeeTaxiRuleInfoContract.setExceedBuyType(carPolicyBean.getExceed_buy_type());
        employeeTaxiRuleInfoContract.setTaxiRuleFlag(carPolicyBean.getRule_limit_flag());
        employeeTaxiRuleInfoContract.setAllowShuttle(carPolicyBean.getAllow_shuttle() == null ? false : carPolicyBean.getAllow_shuttle());
        employeeTaxiRuleInfoContract.setPersonalPay(carPolicyBean.getPersonal_pay() == null ? false : carPolicyBean.getPersonal_pay());
        if (carPolicyBean.getCar_priv_flag() == null || !carPolicyBean.getCar_priv_flag()) {
            employeeTaxiRuleInfoContract.setTaxiRule(false);
        } else {
            employeeTaxiRuleInfoContract.setTaxiRule(true);
        }
        employeeTaxiRuleInfoContract.setPriceLimitFlag(false);
        employeeTaxiRuleInfoContract.setPriceLimit(BigDecimal.ZERO);
        employeeTaxiRuleInfoContract.setDayPriceLimitFlag(false);
        employeeTaxiRuleInfoContract.setDayPriceLimit(BigDecimal.ZERO);
        employeeTaxiRuleInfoContract.setAllowCalledForOther(true);
        employeeTaxiRuleInfoContract.setTaxiTypeLimitFlag(false);
        employeeTaxiRuleInfoContract.setLimitAdvance(false);
        employeeTaxiRuleInfoContract.setRuleLimitAdvance(false);
        adaptVersion4EmployeeTaxiRuleInfo(employeeTaxiRuleInfoContract, clientVersion);
        return employeeTaxiRuleInfoContract;
    }

    public EmployeeTaxiRuleInfo getEmployeeTaxiRule(String employeeId, String companyId, String applyId, String clientVersion, Integer sceneCode) {
        // 员工用车权限
        CarPolicyBean carPolicyBean = iBaseEmployeeTaxiRuleExtService.getCarPolicyBean(employeeId, companyId);
        logger.info("员工:{} 用车权限, carPolicyBean:{}", employeeId, JSON.toJSONString(carPolicyBean));
        if (carPolicyBean == null) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull, CoreLanguage.Common_Exception_UserRuleIsEmpty.getMessage());
        }
        // 查询用户用车规则
        TaxiOrderCheckRule taxiOrderCheckRule = new TaxiOrderCheckRule();
        taxiOrderCheckRule.setCompanyId(companyId);
        if (carPolicyBean.getRule_limit_flag()) {
            List<RuleIdDto> ruleInfos = carPolicyBean.getRule_infos();
            if (ObjUtils.isEmpty(ruleInfos)) {
                throw new SaasException(TemporaryResponseCode.OrderTaxiCheckRuleNotExist.getCode(), TemporaryResponseCode.OrderTaxiCheckRuleNotExist.getMsg());
            }
            if (ObjUtils.isNotEmpty(applyId)) {
                sceneCode = ObjUtils.toInteger(sceneCode, 2);
                // 审批用车
                ApplyOrder applyOrder = applyOrderMapper.selectByPrimaryKey(applyId);
                if (ObjUtils.isNull(applyOrder)) {
                    throw new SaasException(GlobalResponseCode.ApplyIsNull);
                }
                if (!applyOrder.getEmployeeId().equals(employeeId)) {
                    throw new SaasException(GlobalResponseCode.ApplyIsNull);
                }
                if (applyOrder.getType() != ApplyType.ApplyTaxi.getValue()
                        && applyOrder.getType() != ApplyType.CustomFromBeforehand.getValue()) {
                    throw new SaasException(GlobalResponseCode.ApplyFlowTypeError);
                }
                if (applyOrder.getState() != ApplyStatus.Approved.getValue()) {
                    throw new SaasException(GlobalResponseCode.ApplyNotApproved);
                }

                ApplyRuleSettingExample example = new ApplyRuleSettingExample();
                example.createCriteria().andApplyOrderIdEqualTo(applyId);
                List<ApplyRuleSetting> applyRuleSettingList = applyRuleSettingMapper.selectByExample(example);
                if (ObjUtils.isEmpty(applyRuleSettingList)) {
                    throw new SaasException(GlobalResponseCode.ApplyRuleDataError);
                }
                ApplyRuleSetting applyRuleSetting = applyRuleSettingList.get(0);
                if (ObjUtils.isEmpty(applyRuleSetting.getTaxiInfo())) {
                    throw new SaasException(GlobalResponseCode.ApplyRuleDataError);
                }
                TaxiApplyRule taxiApplyRule = json2TaxiApplyRule(applyRuleSetting.getTaxiInfo(), companyId, employeeId);
                taxiOrderCheckRule.setUseRule(2);
                taxiOrderCheckRule.setTaxiApplyRule(taxiApplyRule);
            } else { // 用车
                sceneCode = ObjUtils.toInteger(sceneCode, 1);
                Integer ruleId = null;
                for (RuleIdDto ruleInfo : ruleInfos) {
                    if (ruleInfo.getType().getKey().equals(sceneCode) && ObjUtils.isNotEmpty(ruleInfo.getRule_info())) {
                        ruleId = ObjUtils.toInteger(ruleInfo.getRule_info().get(0).getRule_id());
                    }
                }
                // 用车入口 无用车规则
                if (ObjUtils.isEmpty(ruleId)) {
                    // 有接送机权限(兼容)
                    if (carPolicyBean.getAllow_shuttle()) {
                        return getEmployeeTaxiPolicy(employeeId, companyId, applyId, clientVersion);
                    }
                    String version332 = "3.3.2";
                    if (VersionTool.greaterThanOrEqualTo(clientVersion, version332)) {
                        throw new SaasException(TemporaryResponseCode.OrderTaxiCheckRuleNotExist.getCode(), TemporaryResponseCode.OrderTaxiCheckRuleNotExist.getMsg());
                    } else {
                        // 老版本 提示升级
                        throw new SaasException(GlobalResponseCode.ApplyCenterAlert);
                    }
                }
                TaxiRuleListContract taxiRuleListContract = ruleService.queryTaxiRuleDetailv3(companyId, ruleId);
                if (taxiRuleListContract == null) {
                    throw new SaasException(TemporaryResponseCode.OrderTaxiCheckRuleNotExist.getCode(), TemporaryResponseCode.OrderTaxiCheckRuleNotExist.getMsg());
                }
                taxiOrderCheckRule.setUseRule(1);
                taxiOrderCheckRule.setTaxiRuleListContract(taxiRuleListContract);
            }
        }
        return getEmployeeTaxiRule(carPolicyBean, taxiOrderCheckRule, clientVersion);
    }


    private void setEmployeeTaxiRule(EmployeeTaxiRuleInfo employeeTaxiRuleInfoContract, TaxiRuleListContract taxiRuleListContract) {
        if (taxiRuleListContract != null) {
            employeeTaxiRuleInfoContract.setPriceLimitFlag(taxiRuleListContract.getPriceLimitFlag());
            employeeTaxiRuleInfoContract.setPriceLimit(taxiRuleListContract.getPriceLimit());
            employeeTaxiRuleInfoContract.setDayPriceLimitFlag(taxiRuleListContract.getDayPriceLimit() != null && taxiRuleListContract.getDayPriceLimit().compareTo(BigDecimal.ZERO) > 0);
            employeeTaxiRuleInfoContract.setDayPriceLimit(taxiRuleListContract.getDayPriceLimit());
            if (employeeTaxiRuleInfoContract.getPriceLimitFlag() || employeeTaxiRuleInfoContract.getDayPriceLimitFlag()) {
                employeeTaxiRuleInfoContract.setPriceLimitType(1);
            } else {
                employeeTaxiRuleInfoContract.setPriceLimitType(0);
            }
            employeeTaxiRuleInfoContract.setTaxiSchedulingFee(taxiRuleListContract.getTaxiSchedulingFee());
            employeeTaxiRuleInfoContract.setAllowCalledForOther(taxiRuleListContract.getAllowCalledForOther());
            employeeTaxiRuleInfoContract.setTaxiRuleId(taxiRuleListContract.getId());
            employeeTaxiRuleInfoContract.setTaxiRuleName(taxiRuleListContract.getName());
            employeeTaxiRuleInfoContract.setTaxiTypeLimitFlag(taxiRuleListContract.getLimitTaxiType());
            employeeTaxiRuleInfoContract.setLimitAdvance(taxiRuleListContract.getLimitAdvance() == null ? false : taxiRuleListContract.getLimitAdvance());
            employeeTaxiRuleInfoContract.setRuleLimitAdvance(taxiRuleListContract.getLimitAdvance() == null ? false : taxiRuleListContract.getLimitAdvance());
            employeeTaxiRuleInfoContract.setTimesLimitFlag(0);
            List<KvContract> allowedTaxiTypeList = taxiRuleListContract.getAllowedTaxiType();
            List<String> keyList = Lists.newArrayList();
            List<String> valueList = Lists.newArrayList();
            if (ObjUtils.isNotEmpty(allowedTaxiTypeList)) {
                for (KvContract kvContract : allowedTaxiTypeList) {
                    keyList.add(String.valueOf(kvContract.getKey()));
                    valueList.add(kvContract.getValue());
                }
            }
            employeeTaxiRuleInfoContract.setAllowedTaxiType(String.join(",", keyList));
            List<String> ruleDescList = Lists.newArrayList();
            if (taxiRuleListContract.getPriceLimitFlag()) {
                ruleDescList.add(StrUtils.formatString(CoreLanguage.Rule_Value_SingleTripLess.getMessage(),
                        taxiRuleListContract.getPriceLimit().setScale(2,
                                BigDecimal.ROUND_HALF_UP)));
            }
            if (taxiRuleListContract.getLimitTaxiType()) {
                ruleDescList.add(StrUtils.formatString(CoreLanguage.Rule_Value_UsedCall.getMessage(), String.join("、", valueList)));
            }
            employeeTaxiRuleInfoContract.setTaxiRuleDesc(String.join("，", ruleDescList));
            // 限制时段
            if (taxiRuleListContract.getLimitTime() && CollectionUtils.isNotEmpty(taxiRuleListContract.getTimeRange())) {
                List<RuleTimeRange> timeRangeList = taxiRuleListContract.getTimeRange();
                employeeTaxiRuleInfoContract.setRuleTimeRange(timeRangeList);
                employeeTaxiRuleInfoContract.setTimeRange(TaxiRuleTimeHelper.convertTimeRange(timeRangeList));
            } else {
                // 未限制时段 但限制提前上车
                if (employeeTaxiRuleInfoContract.getLimitAdvance()) {
                    employeeTaxiRuleInfoContract.setLimitAdvance(false);
                }
            }
            employeeTaxiRuleInfoContract.setCityLimitType(taxiRuleListContract.getCityLimitType());
            employeeTaxiRuleInfoContract.setPriceAcrossCityFlag(taxiRuleListContract.getPriceAcrossCityFlag());
            employeeTaxiRuleInfoContract.setExtraPerCountLimitPrice(taxiRuleListContract.getExtraPerCountLimitPrice());
            employeeTaxiRuleInfoContract.setExtraPerDayLimitPrice(taxiRuleListContract.getExtraPerDayLimitPrice());
        }
    }

    private void setEmployeeTaxiRule(EmployeeTaxiRuleInfo employeeTaxiRuleInfoContract, TaxiApplyRule taxiApplyRule, String companyId) {
        if (taxiApplyRule != null) {
            TaxiApproveRuleContract taxiApproveRuleContract = null;
            if (ObjUtils.isNotEmpty(taxiApplyRule.getId())) {
                taxiApproveRuleContract = iTaxiApproveRuleService.queryRuleById(taxiApplyRule.getId(), companyId);
            }
            if (ObjUtils.isEmpty(taxiApplyRule.getName())) {
                taxiApplyRule.setName("");
            }
            logger.info("EmployeeTaxiRule，规则信息：{}", com.fenbeitong.common.utils.json.JsonUtils.toJsonStr(taxiApproveRuleContract));
            employeeTaxiRuleInfoContract.setPriceLimitType(taxiApplyRule.getPriceLimitFlag());
            employeeTaxiRuleInfoContract.setTaxiApproveRuleFlag(taxiApplyRule.getTaxiApproveRuleFlag());
            employeeTaxiRuleInfoContract.setPriceLimitFlag(taxiApplyRule.getPriceLimit() != null && taxiApplyRule.getPriceLimit().compareTo(BigDecimal.ZERO) > 0);
            employeeTaxiRuleInfoContract.setPriceLimit(taxiApplyRule.getPriceLimit());
            employeeTaxiRuleInfoContract.setDayPriceLimitFlag(taxiApplyRule.getDayPriceLimit() != null && taxiApplyRule.getDayPriceLimit().compareTo(BigDecimal.ZERO) > 0);
            employeeTaxiRuleInfoContract.setDayPriceLimit(BigDecimalTool.lessThanOrEqualZero(taxiApplyRule.getDayPriceLimit()) && !ObjUtils.isNull(taxiApproveRuleContract)
                    ? taxiApproveRuleContract.getDayPriceLimit() : taxiApplyRule.getDayPriceLimit());
            employeeTaxiRuleInfoContract.setApplyPriceLimit(taxiApplyRule.getApplyPriceLimit());
            employeeTaxiRuleInfoContract.setTaxiSchedulingFee(new BigDecimal(taxiApplyRule.getTaxiSchedulingFee()));
            employeeTaxiRuleInfoContract.setAllowCalledForOther(taxiApplyRule.getAllowCalledForother());
            employeeTaxiRuleInfoContract.setTaxiRuleId(taxiApplyRule.getId());
            employeeTaxiRuleInfoContract.setTaxiRuleName(!ObjUtils.isNull(taxiApproveRuleContract) ? taxiApproveRuleContract.getName() : taxiApplyRule.getName());
            employeeTaxiRuleInfoContract.setTaxiTypeLimitFlag(ObjUtils.isNotBlank(taxiApplyRule.getAllowedTaxiType()));
            employeeTaxiRuleInfoContract.setLimitAdvance(false);
            employeeTaxiRuleInfoContract.setRuleLimitAdvance(false);
            employeeTaxiRuleInfoContract.setTimesLimitFlag(taxiApplyRule.getTimesLimitFlag());
            employeeTaxiRuleInfoContract.setTimesLimitType(taxiApplyRule.getTimesLimitType());
            employeeTaxiRuleInfoContract.setTimesLimit(taxiApplyRule.getTimesLimit());
            employeeTaxiRuleInfoContract.setUseRuleDayPriceLimit(taxiApplyRule.getUseRuleDayPriceLimit());
            employeeTaxiRuleInfoContract.setRuleDayPriceLimit(taxiApplyRule.getRuleDayPriceLimit());
            String allowedTaxiType = taxiApplyRule.getAllowedTaxiType();
            employeeTaxiRuleInfoContract.setAllowedTaxiType(allowedTaxiType);
            List<String> valueList = Lists.newArrayList();
            String[] allowedTaxiTypeList = StringUtils.split(allowedTaxiType, ",");
            if (ObjUtils.isNotEmpty(allowedTaxiTypeList)) {
                for (String key : allowedTaxiTypeList) {
                    valueList.add(AllowedTaxiType.getName(ObjUtils.toInteger(key)));
                }
            }
            List<String> ruleDescList = Lists.newArrayList();
            if (employeeTaxiRuleInfoContract.getPriceLimitFlag()) {
                ruleDescList.add(StrUtils.formatString(CoreLanguage.Rule_Value_SingleTripLess.getMessage(),
                        taxiApplyRule.getPriceLimit().setScale(2,
                                BigDecimal.ROUND_HALF_UP)));
            }
            if (employeeTaxiRuleInfoContract.getTaxiTypeLimitFlag()) {
                ruleDescList.add(StrUtils.formatString(CoreLanguage.Rule_Value_UsedCall.getMessage(), String.join("、", valueList)));
            }
            employeeTaxiRuleInfoContract.setTaxiRuleDesc(String.join("，", ruleDescList));
            employeeTaxiRuleInfoContract.setDayPriceLimitType(taxiApplyRule.getDayPriceLimitType());
            employeeTaxiRuleInfoContract.setStartTime(taxiApplyRule.getStartTime());
            employeeTaxiRuleInfoContract.setEndTime(taxiApplyRule.getEndTime());
            employeeTaxiRuleInfoContract.setApplyPriceUsed(taxiApplyRule.getApplyPriceUsed());
        }
    }

    private void setEmployeeTaxiRule(EmployeeTaxiRuleInfo employeeTaxiRuleInfoContract, TaxiApproveRuleContract taxiApproveRuleContract) {
        if (taxiApproveRuleContract != null) {
            Integer priceLimitFlag = taxiApproveRuleContract.getPriceLimitFlag();
            employeeTaxiRuleInfoContract.setPriceLimitType(priceLimitFlag);
            employeeTaxiRuleInfoContract.setPriceLimitFlag(taxiApproveRuleContract.getPriceLimit() != null && taxiApproveRuleContract.getPriceLimit().compareTo(BigDecimal.ZERO) > 0);
            employeeTaxiRuleInfoContract.setPriceLimit(taxiApproveRuleContract.getPriceLimit());
            employeeTaxiRuleInfoContract.setDayPriceLimitFlag(taxiApproveRuleContract.getDayPriceLimit() != null && taxiApproveRuleContract.getDayPriceLimit().compareTo(BigDecimal.ZERO) > 0);
            employeeTaxiRuleInfoContract.setDayPriceLimit(taxiApproveRuleContract.getDayPriceLimit());
            employeeTaxiRuleInfoContract.setTaxiSchedulingFee(new BigDecimal(taxiApproveRuleContract.getTaxiSchedulingFee()));
            employeeTaxiRuleInfoContract.setAllowCalledForOther(taxiApproveRuleContract.getAllowCalledForother());
            employeeTaxiRuleInfoContract.setTaxiRuleId(taxiApproveRuleContract.getId());
            employeeTaxiRuleInfoContract.setTaxiRuleName(taxiApproveRuleContract.getName());
            employeeTaxiRuleInfoContract.setTaxiTypeLimitFlag(ObjUtils.isNotEmpty(taxiApproveRuleContract.getAllowedTaxiTypeList()));
            employeeTaxiRuleInfoContract.setLimitAdvance(false);
            employeeTaxiRuleInfoContract.setRuleLimitAdvance(false);
            employeeTaxiRuleInfoContract.setTimesLimitFlag(taxiApproveRuleContract.getTimesLimitFlag());
            employeeTaxiRuleInfoContract.setTimesLimit(taxiApproveRuleContract.getTimesLimit());
            employeeTaxiRuleInfoContract.setTimesLimitType(taxiApproveRuleContract.getTimesLimitType());
            List<com.fenbeitong.saasplus.api.model.dto.util.KvContract> allowedTaxiTypeList = taxiApproveRuleContract.getAllowedTaxiTypeList();
            List<String> keyList = Lists.newArrayList();
            List<String> valueList = Lists.newArrayList();
            if (ObjUtils.isNotEmpty(allowedTaxiTypeList)) {
                for (com.fenbeitong.saasplus.api.model.dto.util.KvContract kvContract : allowedTaxiTypeList) {
                    keyList.add(String.valueOf(kvContract.getKey()));
                    valueList.add(kvContract.getValue());
                }
            }
            employeeTaxiRuleInfoContract.setAllowedTaxiType(String.join(",", keyList));
            List<String> ruleDescList = Lists.newArrayList();
            if (employeeTaxiRuleInfoContract.getPriceLimitFlag()) {
                ruleDescList.add(StrUtils.formatString(CoreLanguage.Rule_Value_SingleTripLess.getMessage(),
                        taxiApproveRuleContract.getPriceLimit().setScale(2,
                                BigDecimal.ROUND_HALF_UP)));
            }
            if (employeeTaxiRuleInfoContract.getTaxiTypeLimitFlag()) {
                ruleDescList.add(StrUtils.formatString(CoreLanguage.Rule_Value_UsedCall.getMessage(), String.join("、", valueList)));
            }
            employeeTaxiRuleInfoContract.setTaxiRuleDesc(String.join("，", ruleDescList));
        }
    }

    public TaxiApplyRule getTaxiApplyRule(String applyId, String tripId, String companyId, String userId) {
        ApplyTripInfo applyTripInfo = applyTripMapper.selectByPrimaryKey(tripId);
        ApplyTripInfoContract applyTripInfoContract = ApplyTripInfoContract.FromModel(applyTripInfo);

        return iApplyV2Service.getTaxiApplyRule(applyTripInfoContract, applyId, userId, companyId, tripId);
    }

    /**
     * 审批用车规则快照解析
     *
     * @param taxiInfo
     * @return
     */
    public TaxiApplyRule json2TaxiApplyRule(String taxiInfo, String companyId, String userId) {
        if (ObjUtils.isEmpty(taxiInfo)) {
            return null;
        }
        TaxiApplyRule taxiApplyRule = new TaxiApplyRule();
        taxiApplyRule.setUseRuleDayPriceLimit(0);
        List<ApplyThirdContract.KeyValueItem> taxiRuleList = JSONArray.parseArray(taxiInfo, ApplyThirdContract.KeyValueItem.class);
        List<TaxiApproveRuleGroupContract> groupListSnapshot = null;
        Integer allowSameCityType = null;
        Boolean allowSameCity = false;
        Boolean limitPath = false;
        Integer limitPathType = null;
        for (ApplyThirdContract.KeyValueItem rule : taxiRuleList) {
            String ruleType = rule.getType();
            Object ruleValue = rule.getValue();
            if (SaasOrderThirdRuleConstant.ID.equals(ruleType)) {
                taxiApplyRule.setId(ObjUtils.toInteger(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.NAME.equals(ruleType)) {
                taxiApplyRule.setName(ObjUtils.toString(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.TAXI_ALLOWED_TAXI_TYPE.equals(ruleType)) {
                taxiApplyRule.setAllowedTaxiType(ObjUtils.toString(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.TAXI_PRICE_LIMIT_FLAG.equals(ruleType)) {
                taxiApplyRule.setPriceLimitFlag(ObjUtils.toInteger(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.TAXI_PRICE_LIMIT.equals(ruleType)) {
                taxiApplyRule.setPriceLimit(ObjUtils.toBigDecimal(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.TAXI_DAY_PRICE_LIMIT.equals(ruleType)) {
                taxiApplyRule.setDayPriceLimit(ObjUtils.toBigDecimal(ruleValue, BigDecimal.ZERO));
            }
            if (SaasOrderThirdRuleConstant.TAXI_TOTAL_PRICE.equals(ruleType)) {
                taxiApplyRule.setApplyPriceLimit(ObjUtils.toBigDecimal(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.TAXI_TAXI_SCHEDULING_FEE.equals(ruleType)) {
                taxiApplyRule.setTaxiSchedulingFee(ObjUtils.toInteger(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.TAXI_ALLOW_SAME_CITY.equals(ruleType)) {
                taxiApplyRule.setAllowSameCity(ObjUtils.toBoolean(ruleValue));
                allowSameCity = ObjUtils.toBoolean(ruleValue);
            }
            if (SaasOrderThirdRuleConstant.TAXI_ALLOW_SAME_CITY_TYPE.equals(ruleType)) {
                allowSameCityType = ObjUtils.toInteger(ruleValue);
            }
            if (SaasOrderThirdRuleConstant.TAXI_ALLOW_CALLED_FOR_OTHER.equals(ruleType)) {
                taxiApplyRule.setAllowCalledForother(ObjUtils.toBoolean(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.TAXI_TIMES_LIMIT_FLAG.equals(ruleType)) {
                taxiApplyRule.setTimesLimitFlag(ObjUtils.toInteger(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.TAXI_TIMES_LIMIT_TYPE.equals(ruleType)) {
                taxiApplyRule.setTimesLimitType(ObjUtils.toInteger(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.TAXI_TIMES_LIMIT.equals(ruleType)) {
                taxiApplyRule.setTimesLimit(ObjUtils.toInteger(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.TAXI_START_TIME.equals(ruleType)) {
                taxiApplyRule.setStartTime(ObjUtils.toString(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.TAXI_END_TIME.equals(ruleType)) {
                taxiApplyRule.setEndTime(ObjUtils.toString(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.TAXI_START_DATE_TIME.equals(ruleType)) {
                taxiApplyRule.setStartDateTime(ObjUtils.toString(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.TAXI_END_DATE_TIME.equals(ruleType)) {
                taxiApplyRule.setEndDateTime(ObjUtils.toString(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.TAXI_SCENE_TYPE.equals(ruleType)) {
                taxiApplyRule.setSceneType(ObjUtils.toInteger(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.TAXI_SCENE_TYPE2.equals(ruleType)) {
                taxiApplyRule.setSceneType(ObjUtils.toInteger(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.TAXI_START_CITY_IDS.equals(ruleType)) {
                List<String> startCityIds = JSONArray.parseArray(JSON.toJSONString(ruleValue)).toJavaList(String.class);
                taxiApplyRule.setStartCityIds(startCityIds);
            }
            if (SaasOrderThirdRuleConstant.TAXI_CITY_LIMIT.equals(ruleType)) {
                taxiApplyRule.setCityLimit(ObjUtils.toInteger(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.TAXI_USE_RULE_DAY_PRICE_LIMIT.equals(ruleType)) {
                taxiApplyRule.setUseRuleDayPriceLimit(ObjUtils.toInteger(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.TAXI_USE_PERSONAL_BUDGET.equals(ruleType)) {
                taxiApplyRule.setUsePersonalBudget(ObjUtils.toInteger(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.TAXI_RULE_GROUP_LIST.equals(ruleType)) {
                taxiApplyRule.setRuleGroupContractList(JSON.parseArray(JSON.toJSONString(ruleValue), TaxiApproveRuleGroupContract.class));
            }
            if (SaasOrderThirdRuleConstant.TAXI_PRICE_ACROSS_CITY_FLAG.equals(ruleType)) {
                taxiApplyRule.setPriceAcrossCityFlag(ObjUtils.toInteger(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.DAY_PRICE_LIMIT_TYPE.equals(ruleType)) {
                taxiApplyRule.setDayPriceLimitType(ObjUtils.toInteger(ruleValue));
            }
            //时段限制
            if (SaasOrderThirdRuleConstant.LIMIT_TIME.equals(ruleType)) {
                taxiApplyRule.setLimitTime(ObjUtils.toBoolean(ruleValue));
            }
            // https://wiki.fenbeijinfu.com/pages/viewpage.action?pageId=60014420 新增属性
            if (SaasOrderThirdRuleConstant.perDayLimitPrice.equals(ruleType)) {
                taxiApplyRule.setPerDayLimitPrice(ObjUtils.toBigDecimal(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.employeeLimitType.equals(ruleType)) {
                taxiApplyRule.setEmployeeLimitType(ObjUtils.toInteger(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.cityLimitType.equals(ruleType)) {
                taxiApplyRule.setCityLimitType(ObjUtils.toInteger(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.taxiApproveRuleFlag.equals(ruleType)) {
                taxiApplyRule.setTaxiApproveRuleFlag(ObjUtils.toInteger(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.extraPerCountLimitPrice.equals(ruleType)) {
                taxiApplyRule.setExtraPerCountLimitPrice(ObjUtils.toBigDecimal(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.extraPerDayLimitPrice.equals(ruleType)) {
                taxiApplyRule.setExtraPerDayLimitPrice(ObjUtils.toBigDecimal(ruleValue));
            }

            if (SaasOrderThirdRuleConstant.TIME_RANGE_LIST.equals(ruleType)) {
                List<List<RuleTimeRange>> timeRangeList = new ArrayList<>();
                List<RuleTimeRange> allTimeRangeList = new ArrayList<>();
                List<Object> timeRangeListTemp = JSONArray.parseArray(ruleValue.toString(), Object.class);
                if (CollectionUtils.isNotEmpty(timeRangeListTemp)) {
                    timeRangeListTemp.forEach(o -> {
                        List<RuleTimeRange> timeRanges = JSON.parseArray(o.toString(), RuleTimeRange.class);
                        timeRangeList.add(timeRanges);
                        timeRanges.forEach(ruleTimeRange -> {
                            RuleTimeRange timeRange = new RuleTimeRange();
                            timeRange.setId(ruleTimeRange.getId());
                            timeRange.setRuleId(ruleTimeRange.getRuleId());
                            timeRange.setDayType(ruleTimeRange.getDayType());
                            timeRange.setIsOvernight(ruleTimeRange.getIsOvernight());
                            timeRange.setBeginTime(ruleTimeRange.getBeginTime());
                            timeRange.setEndTime(ruleTimeRange.getEndTime());
                            allTimeRangeList.add(timeRange);
                        });
                    });
                }
                taxiApplyRule.setTimeRange(allTimeRangeList);
                taxiApplyRule.setTimeRangeList(timeRangeList);
            }
            //地点限制
            if (SaasOrderThirdRuleConstant.LIMIT_PATH.equals(ruleType)) {
                taxiApplyRule.setLimitPath(ObjUtils.toBoolean(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.PATH_LOCATION_INFOS.equals(ruleType)) {
                if(ruleValue!=null && StringUtils.isNotBlank(ruleValue.toString())) {
                    List<TaxiRulePath> taxiRulePaths = JSON.parseArray(ruleValue.toString(), TaxiRulePath.class);
                    taxiApplyRule.setPathLocationInfos(taxiRulePaths);
                }
            }
            if (SaasOrderThirdRuleConstant.PATH_MESSAGE.equals(ruleType)) {
                if(ruleValue!=null && StringUtils.isNotBlank(ruleValue.toString())) {
                    List<TaxiRulePathVo> taxiRulePathVos = JSON.parseArray(ruleValue.toString(), TaxiRulePathVo.class);
                    taxiApplyRule.setPathMessage(taxiRulePathVos);
                }
            }
            if (SaasOrderThirdRuleConstant.CITY_RESTRICTION_TYPE.equals(ruleType)) {
                taxiApplyRule.setCityRestrictionType(ObjUtils.toInteger(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.RESIDENT_CITY_SWITCH.equals(ruleType)) {
                taxiApplyRule.setResidentCitySwitch(ObjUtils.toInteger(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.SPECIFY_CITY_TYPE.equals(ruleType)) {
                taxiApplyRule.setSpecifyCityType(ObjUtils.toInteger(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.IS_OPEN_EXCEED_CONFIG.equals(ruleType)) {
                taxiApplyRule.setOpenExceedConfig(ObjUtils.toBoolean(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.EXCEED_CONFIG_INFO.equals(ruleType)) {
                TaxiExceedConfigPo exceedConfigPo = JsonUtils.toObj(ruleValue.toString(), TaxiExceedConfigPo.class);
                taxiApplyRule.setExceedConfigInfo(exceedConfigPo);
            }
            if (SaasOrderThirdRuleConstant.CHECK_START_ADDRESS.equals(ruleType) && null != ruleValue) {
                taxiApplyRule.setCheckStartAddress(ObjUtils.toInteger(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.CHECK_ARRIVAL_ADDRESS.equals(ruleType) && null != ruleValue) {
                taxiApplyRule.setCheckArrivalAddress(ObjUtils.toInteger(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.TAXI_START_ADDRESS.equals(ruleType) && null != ruleValue) {
                taxiApplyRule.setStartTaxiAddress(JSONObject.parseObject(ruleValue.toString()));
            }
            if (SaasOrderThirdRuleConstant.TAXI_ARRIVAL_ADDRESS.equals(ruleType) && null != ruleValue) {
                taxiApplyRule.setArrivalTaxiAddress(JSONObject.parseObject(ruleValue.toString()));
            }
            if (SaasOrderThirdRuleConstant.APPLY_LOCATION_LIMIT_RADIUS.equals(ruleType)) {
                taxiApplyRule.setApplyLocationLimitRadius(Double.parseDouble(ruleValue.toString()));
            }
            if ("limitPath".equals(ruleType)) {
                limitPath = ObjUtils.toBoolean(ruleValue);
                taxiApplyRule.setLimitPath(limitPath);
            }
            if ("limitPathType".equals(ruleType)) {
                limitPathType = ObjUtils.toInteger(ruleValue);
            }
            if (SaasOrderThirdRuleConstant.TAXI_USED_TYPE.equals(ruleType)) {
                taxiApplyRule.setTaxiUsedType(ObjUtils.toString(ruleValue));
            }
            if (Objects.equals(SaasOrderThirdRuleConstant.TAXI_TYPE_LIMIT_FLAG, ruleType)) {
                taxiApplyRule.setTaxiTypeLimitFlag(ObjUtils.toInteger(ruleValue, 1));
            }
            if (Objects.equals(SaasOrderThirdRuleConstant.TAXI_TYPE_BASED, ruleType)) {
                taxiApplyRule.setTaxiTypeBased(ObjUtils.toString(ruleValue));
            }
            if (Objects.equals(SaasOrderThirdRuleConstant.FLOATING_CONFIG_LIST, ruleType) && ObjUtils.isNotEmpty(ruleValue)) {
                taxiApplyRule.setFloatingConfigList(JSON.parseArray(JSON.toJSONString(ruleValue), TaxiTypeBasedPriceFloatingConfigVO.class));
            }
        }
        if (taxiApplyRule.getTaxiUsedType() == null) {
            taxiApplyRule.setTaxiUsedType(String.valueOf(TaxiUsedTypeEnum.NORMAL.getType()));
        }
        // 兼容逻辑
        if (Objects.nonNull(allowSameCityType)) {
            taxiApplyRule.setAllowSameCityType(allowSameCityType);
        } else {
            taxiApplyRule.setAllowSameCityType(AllowSameCityLimitType.mapToAllowSameCityLimitTypeBy(allowSameCity).getCode());
        }
        if (Objects.nonNull(limitPathType)) {
            taxiApplyRule.setLimitPathType(limitPathType);
        }
//        else {
//            taxiApplyRule.setLimitPathType(limitPath ? 1 : 0);
//        }

        // 兼容逻辑，如果快照找那个没有存储城市限制信息，则实时查询，添加 ruleGroupContractList
        // 只有按照城市分组限制时  需要根据城市过滤订单
        if (TaxiApplyHelper.isCityPriceLimit(taxiApplyRule)) {
            if (Objects.nonNull(userId) && Objects.nonNull(companyId) && CollectionUtils.isEmpty(taxiApplyRule.getRuleGroupContractList())) {
                TaxiRuleForApproveContract taxiRuleForApproveContract =
                    iTaxiApproveRuleService.queryTaxiApproveRuleDetail(userId, companyId, taxiApplyRule.getSceneType());
                logger.info("RPC-taxiRuleForApproveContract={}", com.fenbeitong.common.utils.json.JsonUtils.toJsonStr(taxiRuleForApproveContract));
                if (Objects.nonNull(taxiRuleForApproveContract) && CollectionUtils.isNotEmpty(taxiRuleForApproveContract.getRuleGroupContractList())) {
                    taxiApplyRule.setRuleGroupContractList(taxiRuleForApproveContract.getRuleGroupContractList());
                }
                // 用车城市限制
                // 如果在这里调用rpc，则先判断一遍，然后赋值
                if (TaxiApplyHelper.isNeedQuerySpecifyCityRpc(taxiApplyRule)) {
                    log.info("如果在这里调用rpc，则先判断一遍，然后赋值");
                    TaxiApplyHelper.buildSpecifyCityList(taxiApplyRule, taxiRuleForApproveContract);
                }
            }
        }

        // 用车城市限制
        // 是否需要查询rpc以获取 用车城市限制 城市列表
        if (TaxiApplyHelper.isNeedQuerySpecifyCityRpc(taxiApplyRule)) {
            log.info("是否需要查询rpc以获取 用车城市限制 城市列表");
            TaxiRuleForApproveContract taxiRuleForApproveContract =
                iTaxiApproveRuleService.queryTaxiApproveRuleDetail(userId, companyId, taxiApplyRule.getSceneType());
            TaxiApplyHelper.buildSpecifyCityList(taxiApplyRule, taxiRuleForApproveContract);
        }

        if (ObjUtils.isEmpty(taxiApplyRule.getDayPriceLimit())) {
            taxiApplyRule.setDayPriceLimit(BigDecimal.ZERO);
        }

        if(taxiApplyRule.getSceneType()==null){
            taxiApplyRule.setSceneType(TaxiSubType.TaxiApply.getSubtype());
        }

        return taxiApplyRule;
    }

    /**
     * 用车规则版本兼容
     * @param employeeTaxiRuleInfoContract
     * @param clientVersion
     */
    private void adaptVersion4EmployeeTaxiRuleInfo(EmployeeTaxiRuleInfo employeeTaxiRuleInfoContract, String clientVersion) {
        logger.info("员工用车规则版本兼容处理前: ClientVersion:{}, PersonalPay:{}, ExceedBuyType:{}", clientVersion, employeeTaxiRuleInfoContract.getPersonalPay(), employeeTaxiRuleInfoContract.getExceedBuyType());
        String version210 = "2.1.0";
        if (clientVersion == null || VersionTool.greaterThanOrEqualTo(clientVersion, version210)) {
            //服务端调用或新版本
            if (employeeTaxiRuleInfoContract.getPersonalPay()) {
                return;
            }
            if (employeeTaxiRuleInfoContract.getExceedBuyType() == ExceedAllowType.ExceedAllowWithFee.getCode()) {
                employeeTaxiRuleInfoContract.setExceedBuyType(ExceedAllowType.ExceedNotAllow.getCode());
                employeeTaxiRuleInfoContract.setPersonalPay(true);
            }
        } else {
            //老版本
            if (!employeeTaxiRuleInfoContract.getPersonalPay()) {
                return;
            }
            if (employeeTaxiRuleInfoContract.getExceedBuyType() != ExceedAllowType.ExceedAllowWithFee.getCode()) {
                employeeTaxiRuleInfoContract.setExceedBuyType(ExceedAllowType.ExceedAllowWithFee.getCode());
            }
        }
        logger.info("员工用车规则版本兼容处理后: ClientVersion:{}, PersonalPay:{}, ExceedBuyType:{}", clientVersion, employeeTaxiRuleInfoContract.getPersonalPay(), employeeTaxiRuleInfoContract.getExceedBuyType());
    }


}
