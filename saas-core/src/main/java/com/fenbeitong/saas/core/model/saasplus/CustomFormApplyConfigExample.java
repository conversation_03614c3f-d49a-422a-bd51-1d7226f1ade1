package com.fenbeitong.saas.core.model.saasplus;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CustomFormApplyConfigExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table custom_form_apply_config
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table custom_form_apply_config
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table custom_form_apply_config
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table custom_form_apply_config
     *
     * @mbg.generated
     */
    public CustomFormApplyConfigExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table custom_form_apply_config
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table custom_form_apply_config
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table custom_form_apply_config
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table custom_form_apply_config
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table custom_form_apply_config
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table custom_form_apply_config
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table custom_form_apply_config
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table custom_form_apply_config
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table custom_form_apply_config
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table custom_form_apply_config
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table custom_form_apply_config
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andFormIdIsNull() {
            addCriterion("form_id is null");
            return (Criteria) this;
        }

        public Criteria andFormIdIsNotNull() {
            addCriterion("form_id is not null");
            return (Criteria) this;
        }

        public Criteria andFormIdEqualTo(String value) {
            addCriterion("form_id =", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotEqualTo(String value) {
            addCriterion("form_id <>", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdGreaterThan(String value) {
            addCriterion("form_id >", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdGreaterThanOrEqualTo(String value) {
            addCriterion("form_id >=", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdLessThan(String value) {
            addCriterion("form_id <", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdLessThanOrEqualTo(String value) {
            addCriterion("form_id <=", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdLike(String value) {
            addCriterion("form_id like", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotLike(String value) {
            addCriterion("form_id not like", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdIn(List<String> values) {
            addCriterion("form_id in", values, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotIn(List<String> values) {
            addCriterion("form_id not in", values, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdBetween(String value1, String value2) {
            addCriterion("form_id between", value1, value2, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotBetween(String value1, String value2) {
            addCriterion("form_id not between", value1, value2, "formId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(String value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(String value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(String value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(String value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(String value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(String value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLike(String value) {
            addCriterion("company_id like", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotLike(String value) {
            addCriterion("company_id not like", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<String> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<String> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(String value1, String value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(String value1, String value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCityTypeIsNull() {
            addCriterion("city_type is null");
            return (Criteria) this;
        }

        public Criteria andCityTypeIsNotNull() {
            addCriterion("city_type is not null");
            return (Criteria) this;
        }

        public Criteria andCityTypeEqualTo(Integer value) {
            addCriterion("city_type =", value, "cityType");
            return (Criteria) this;
        }

        public Criteria andCityTypeNotEqualTo(Integer value) {
            addCriterion("city_type <>", value, "cityType");
            return (Criteria) this;
        }

        public Criteria andCityTypeGreaterThan(Integer value) {
            addCriterion("city_type >", value, "cityType");
            return (Criteria) this;
        }

        public Criteria andCityTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("city_type >=", value, "cityType");
            return (Criteria) this;
        }

        public Criteria andCityTypeLessThan(Integer value) {
            addCriterion("city_type <", value, "cityType");
            return (Criteria) this;
        }

        public Criteria andCityTypeLessThanOrEqualTo(Integer value) {
            addCriterion("city_type <=", value, "cityType");
            return (Criteria) this;
        }

        public Criteria andCityTypeIn(List<Integer> values) {
            addCriterion("city_type in", values, "cityType");
            return (Criteria) this;
        }

        public Criteria andCityTypeNotIn(List<Integer> values) {
            addCriterion("city_type not in", values, "cityType");
            return (Criteria) this;
        }

        public Criteria andCityTypeBetween(Integer value1, Integer value2) {
            addCriterion("city_type between", value1, value2, "cityType");
            return (Criteria) this;
        }

        public Criteria andCityTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("city_type not between", value1, value2, "cityType");
            return (Criteria) this;
        }

        public Criteria andStartDateTypeIsNull() {
            addCriterion("start_date_type is null");
            return (Criteria) this;
        }

        public Criteria andStartDateTypeIsNotNull() {
            addCriterion("start_date_type is not null");
            return (Criteria) this;
        }

        public Criteria andStartDateTypeEqualTo(Integer value) {
            addCriterion("start_date_type =", value, "startDateType");
            return (Criteria) this;
        }

        public Criteria andStartDateTypeNotEqualTo(Integer value) {
            addCriterion("start_date_type <>", value, "startDateType");
            return (Criteria) this;
        }

        public Criteria andStartDateTypeGreaterThan(Integer value) {
            addCriterion("start_date_type >", value, "startDateType");
            return (Criteria) this;
        }

        public Criteria andStartDateTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("start_date_type >=", value, "startDateType");
            return (Criteria) this;
        }

        public Criteria andStartDateTypeLessThan(Integer value) {
            addCriterion("start_date_type <", value, "startDateType");
            return (Criteria) this;
        }

        public Criteria andStartDateTypeLessThanOrEqualTo(Integer value) {
            addCriterion("start_date_type <=", value, "startDateType");
            return (Criteria) this;
        }

        public Criteria andStartDateTypeIn(List<Integer> values) {
            addCriterion("start_date_type in", values, "startDateType");
            return (Criteria) this;
        }

        public Criteria andStartDateTypeNotIn(List<Integer> values) {
            addCriterion("start_date_type not in", values, "startDateType");
            return (Criteria) this;
        }

        public Criteria andStartDateTypeBetween(Integer value1, Integer value2) {
            addCriterion("start_date_type between", value1, value2, "startDateType");
            return (Criteria) this;
        }

        public Criteria andStartDateTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("start_date_type not between", value1, value2, "startDateType");
            return (Criteria) this;
        }

        public Criteria andReturnTripDateTypeIsNull() {
            addCriterion("return_trip_date_type is null");
            return (Criteria) this;
        }

        public Criteria andReturnTripDateTypeIsNotNull() {
            addCriterion("return_trip_date_type is not null");
            return (Criteria) this;
        }

        public Criteria andReturnTripDateTypeEqualTo(Integer value) {
            addCriterion("return_trip_date_type =", value, "returnTripDateType");
            return (Criteria) this;
        }

        public Criteria andReturnTripDateTypeNotEqualTo(Integer value) {
            addCriterion("return_trip_date_type <>", value, "returnTripDateType");
            return (Criteria) this;
        }

        public Criteria andReturnTripDateTypeGreaterThan(Integer value) {
            addCriterion("return_trip_date_type >", value, "returnTripDateType");
            return (Criteria) this;
        }

        public Criteria andReturnTripDateTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("return_trip_date_type >=", value, "returnTripDateType");
            return (Criteria) this;
        }

        public Criteria andReturnTripDateTypeLessThan(Integer value) {
            addCriterion("return_trip_date_type <", value, "returnTripDateType");
            return (Criteria) this;
        }

        public Criteria andReturnTripDateTypeLessThanOrEqualTo(Integer value) {
            addCriterion("return_trip_date_type <=", value, "returnTripDateType");
            return (Criteria) this;
        }

        public Criteria andReturnTripDateTypeIn(List<Integer> values) {
            addCriterion("return_trip_date_type in", values, "returnTripDateType");
            return (Criteria) this;
        }

        public Criteria andReturnTripDateTypeNotIn(List<Integer> values) {
            addCriterion("return_trip_date_type not in", values, "returnTripDateType");
            return (Criteria) this;
        }

        public Criteria andReturnTripDateTypeBetween(Integer value1, Integer value2) {
            addCriterion("return_trip_date_type between", value1, value2, "returnTripDateType");
            return (Criteria) this;
        }

        public Criteria andReturnTripDateTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("return_trip_date_type not between", value1, value2, "returnTripDateType");
            return (Criteria) this;
        }

        public Criteria andFoodDeliveryDateTypeIsNull() {
            addCriterion("food_delivery_date_type is null");
            return (Criteria) this;
        }

        public Criteria andFoodDeliveryDateTypeIsNotNull() {
            addCriterion("food_delivery_date_type is not null");
            return (Criteria) this;
        }

        public Criteria andFoodDeliveryDateTypeEqualTo(Integer value) {
            addCriterion("food_delivery_date_type =", value, "foodDeliveryDateType");
            return (Criteria) this;
        }

        public Criteria andFoodDeliveryDateTypeNotEqualTo(Integer value) {
            addCriterion("food_delivery_date_type <>", value, "foodDeliveryDateType");
            return (Criteria) this;
        }

        public Criteria andFoodDeliveryDateTypeGreaterThan(Integer value) {
            addCriterion("food_delivery_date_type >", value, "foodDeliveryDateType");
            return (Criteria) this;
        }

        public Criteria andFoodDeliveryDateTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("food_delivery_date_type >=", value, "foodDeliveryDateType");
            return (Criteria) this;
        }

        public Criteria andFoodDeliveryDateTypeLessThan(Integer value) {
            addCriterion("food_delivery_date_type <", value, "foodDeliveryDateType");
            return (Criteria) this;
        }

        public Criteria andFoodDeliveryDateTypeLessThanOrEqualTo(Integer value) {
            addCriterion("food_delivery_date_type <=", value, "foodDeliveryDateType");
            return (Criteria) this;
        }

        public Criteria andFoodDeliveryDateTypeIn(List<Integer> values) {
            addCriterion("food_delivery_date_type in", values, "foodDeliveryDateType");
            return (Criteria) this;
        }

        public Criteria andFoodDeliveryDateTypeNotIn(List<Integer> values) {
            addCriterion("food_delivery_date_type not in", values, "foodDeliveryDateType");
            return (Criteria) this;
        }

        public Criteria andFoodDeliveryDateTypeBetween(Integer value1, Integer value2) {
            addCriterion("food_delivery_date_type between", value1, value2, "foodDeliveryDateType");
            return (Criteria) this;
        }

        public Criteria andFoodDeliveryDateTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("food_delivery_date_type not between", value1, value2, "foodDeliveryDateType");
            return (Criteria) this;
        }

        public Criteria andEstimatedCostCheckSwitchIsNull() {
            addCriterion("estimated_cost_check_switch is null");
            return (Criteria) this;
        }

        public Criteria andEstimatedCostCheckSwitchIsNotNull() {
            addCriterion("estimated_cost_check_switch is not null");
            return (Criteria) this;
        }

        public Criteria andEstimatedCostCheckSwitchEqualTo(Integer value) {
            addCriterion("estimated_cost_check_switch =", value, "estimatedCostCheckSwitch");
            return (Criteria) this;
        }

        public Criteria andEstimatedCostCheckSwitchNotEqualTo(Integer value) {
            addCriterion("estimated_cost_check_switch <>", value, "estimatedCostCheckSwitch");
            return (Criteria) this;
        }

        public Criteria andEstimatedCostCheckSwitchGreaterThan(Integer value) {
            addCriterion("estimated_cost_check_switch >", value, "estimatedCostCheckSwitch");
            return (Criteria) this;
        }

        public Criteria andEstimatedCostCheckSwitchGreaterThanOrEqualTo(Integer value) {
            addCriterion("estimated_cost_check_switch >=", value, "estimatedCostCheckSwitch");
            return (Criteria) this;
        }

        public Criteria andEstimatedCostCheckSwitchLessThan(Integer value) {
            addCriterion("estimated_cost_check_switch <", value, "estimatedCostCheckSwitch");
            return (Criteria) this;
        }

        public Criteria andEstimatedCostCheckSwitchLessThanOrEqualTo(Integer value) {
            addCriterion("estimated_cost_check_switch <=", value, "estimatedCostCheckSwitch");
            return (Criteria) this;
        }

        public Criteria andEstimatedCostCheckSwitchIn(List<Integer> values) {
            addCriterion("estimated_cost_check_switch in", values, "estimatedCostCheckSwitch");
            return (Criteria) this;
        }

        public Criteria andEstimatedCostCheckSwitchNotIn(List<Integer> values) {
            addCriterion("estimated_cost_check_switch not in", values, "estimatedCostCheckSwitch");
            return (Criteria) this;
        }

        public Criteria andEstimatedCostCheckSwitchBetween(Integer value1, Integer value2) {
            addCriterion("estimated_cost_check_switch between", value1, value2, "estimatedCostCheckSwitch");
            return (Criteria) this;
        }

        public Criteria andEstimatedCostCheckSwitchNotBetween(Integer value1, Integer value2) {
            addCriterion("estimated_cost_check_switch not between", value1, value2, "estimatedCostCheckSwitch");
            return (Criteria) this;
        }

        public Criteria andAllOrderEstimatedCostCheckIsNull() {
            addCriterion("all_order_estimated_cost_check is null");
            return (Criteria) this;
        }

        public Criteria andAllOrderEstimatedCostCheckIsNotNull() {
            addCriterion("all_order_estimated_cost_check is not null");
            return (Criteria) this;
        }

        public Criteria andAllOrderEstimatedCostCheckEqualTo(Integer value) {
            addCriterion("all_order_estimated_cost_check =", value, "allOrderEstimatedCostCheck");
            return (Criteria) this;
        }

        public Criteria andAllOrderEstimatedCostCheckNotEqualTo(Integer value) {
            addCriterion("all_order_estimated_cost_check <>", value, "allOrderEstimatedCostCheck");
            return (Criteria) this;
        }

        public Criteria andAllOrderEstimatedCostCheckGreaterThan(Integer value) {
            addCriterion("all_order_estimated_cost_check >", value, "allOrderEstimatedCostCheck");
            return (Criteria) this;
        }

        public Criteria andAllOrderEstimatedCostCheckGreaterThanOrEqualTo(Integer value) {
            addCriterion("all_order_estimated_cost_check >=", value, "allOrderEstimatedCostCheck");
            return (Criteria) this;
        }

        public Criteria andAllOrderEstimatedCostCheckLessThan(Integer value) {
            addCriterion("all_order_estimated_cost_check <", value, "allOrderEstimatedCostCheck");
            return (Criteria) this;
        }

        public Criteria andAllOrderEstimatedCostCheckLessThanOrEqualTo(Integer value) {
            addCriterion("all_order_estimated_cost_check <=", value, "allOrderEstimatedCostCheck");
            return (Criteria) this;
        }

        public Criteria andAllOrderEstimatedCostCheckIn(List<Integer> values) {
            addCriterion("all_order_estimated_cost_check in", values, "allOrderEstimatedCostCheck");
            return (Criteria) this;
        }

        public Criteria andAllOrderEstimatedCostCheckNotIn(List<Integer> values) {
            addCriterion("all_order_estimated_cost_check not in", values, "allOrderEstimatedCostCheck");
            return (Criteria) this;
        }

        public Criteria andAllOrderEstimatedCostCheckBetween(Integer value1, Integer value2) {
            addCriterion("all_order_estimated_cost_check between", value1, value2, "allOrderEstimatedCostCheck");
            return (Criteria) this;
        }

        public Criteria andAllOrderEstimatedCostCheckNotBetween(Integer value1, Integer value2) {
            addCriterion("all_order_estimated_cost_check not between", value1, value2, "allOrderEstimatedCostCheck");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedCostCheckTypeIsNull() {
            addCriterion("travel_form_estimated_cost_check_type is null");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedCostCheckTypeIsNotNull() {
            addCriterion("travel_form_estimated_cost_check_type is not null");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedCostCheckTypeEqualTo(Integer value) {
            addCriterion("travel_form_estimated_cost_check_type =", value, "travelFormEstimatedCostCheckType");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedCostCheckTypeNotEqualTo(Integer value) {
            addCriterion("travel_form_estimated_cost_check_type <>", value, "travelFormEstimatedCostCheckType");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedCostCheckTypeGreaterThan(Integer value) {
            addCriterion("travel_form_estimated_cost_check_type >", value, "travelFormEstimatedCostCheckType");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedCostCheckTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("travel_form_estimated_cost_check_type >=", value, "travelFormEstimatedCostCheckType");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedCostCheckTypeLessThan(Integer value) {
            addCriterion("travel_form_estimated_cost_check_type <", value, "travelFormEstimatedCostCheckType");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedCostCheckTypeLessThanOrEqualTo(Integer value) {
            addCriterion("travel_form_estimated_cost_check_type <=", value, "travelFormEstimatedCostCheckType");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedCostCheckTypeIn(List<Integer> values) {
            addCriterion("travel_form_estimated_cost_check_type in", values, "travelFormEstimatedCostCheckType");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedCostCheckTypeNotIn(List<Integer> values) {
            addCriterion("travel_form_estimated_cost_check_type not in", values, "travelFormEstimatedCostCheckType");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedCostCheckTypeBetween(Integer value1, Integer value2) {
            addCriterion("travel_form_estimated_cost_check_type between", value1, value2, "travelFormEstimatedCostCheckType");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedCostCheckTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("travel_form_estimated_cost_check_type not between", value1, value2, "travelFormEstimatedCostCheckType");
            return (Criteria) this;
        }

        public Criteria andMultiTripApplyEstimatedCostCheckIsNull() {
            addCriterion("multi_trip_apply_estimated_cost_check is null");
            return (Criteria) this;
        }

        public Criteria andMultiTripApplyEstimatedCostCheckIsNotNull() {
            addCriterion("multi_trip_apply_estimated_cost_check is not null");
            return (Criteria) this;
        }

        public Criteria andMultiTripApplyEstimatedCostCheckEqualTo(Integer value) {
            addCriterion("multi_trip_apply_estimated_cost_check =", value, "multiTripApplyEstimatedCostCheck");
            return (Criteria) this;
        }

        public Criteria andMultiTripApplyEstimatedCostCheckNotEqualTo(Integer value) {
            addCriterion("multi_trip_apply_estimated_cost_check <>", value, "multiTripApplyEstimatedCostCheck");
            return (Criteria) this;
        }

        public Criteria andMultiTripApplyEstimatedCostCheckGreaterThan(Integer value) {
            addCriterion("multi_trip_apply_estimated_cost_check >", value, "multiTripApplyEstimatedCostCheck");
            return (Criteria) this;
        }

        public Criteria andMultiTripApplyEstimatedCostCheckGreaterThanOrEqualTo(Integer value) {
            addCriterion("multi_trip_apply_estimated_cost_check >=", value, "multiTripApplyEstimatedCostCheck");
            return (Criteria) this;
        }

        public Criteria andMultiTripApplyEstimatedCostCheckLessThan(Integer value) {
            addCriterion("multi_trip_apply_estimated_cost_check <", value, "multiTripApplyEstimatedCostCheck");
            return (Criteria) this;
        }

        public Criteria andMultiTripApplyEstimatedCostCheckLessThanOrEqualTo(Integer value) {
            addCriterion("multi_trip_apply_estimated_cost_check <=", value, "multiTripApplyEstimatedCostCheck");
            return (Criteria) this;
        }

        public Criteria andMultiTripApplyEstimatedCostCheckIn(List<Integer> values) {
            addCriterion("multi_trip_apply_estimated_cost_check in", values, "multiTripApplyEstimatedCostCheck");
            return (Criteria) this;
        }

        public Criteria andMultiTripApplyEstimatedCostCheckNotIn(List<Integer> values) {
            addCriterion("multi_trip_apply_estimated_cost_check not in", values, "multiTripApplyEstimatedCostCheck");
            return (Criteria) this;
        }

        public Criteria andMultiTripApplyEstimatedCostCheckBetween(Integer value1, Integer value2) {
            addCriterion("multi_trip_apply_estimated_cost_check between", value1, value2, "multiTripApplyEstimatedCostCheck");
            return (Criteria) this;
        }

        public Criteria andMultiTripApplyEstimatedCostCheckNotBetween(Integer value1, Integer value2) {
            addCriterion("multi_trip_apply_estimated_cost_check not between", value1, value2, "multiTripApplyEstimatedCostCheck");
            return (Criteria) this;
        }

        public Criteria andAirChangeEstimatedCostCheckSwitchIsNull() {
            addCriterion("air_change_estimated_cost_check_switch is null");
            return (Criteria) this;
        }

        public Criteria andAirChangeEstimatedCostCheckSwitchIsNotNull() {
            addCriterion("air_change_estimated_cost_check_switch is not null");
            return (Criteria) this;
        }

        public Criteria andAirChangeEstimatedCostCheckSwitchEqualTo(Integer value) {
            addCriterion("air_change_estimated_cost_check_switch =", value, "airChangeEstimatedCostCheckSwitch");
            return (Criteria) this;
        }

        public Criteria andAirChangeEstimatedCostCheckSwitchNotEqualTo(Integer value) {
            addCriterion("air_change_estimated_cost_check_switch <>", value, "airChangeEstimatedCostCheckSwitch");
            return (Criteria) this;
        }

        public Criteria andAirChangeEstimatedCostCheckSwitchGreaterThan(Integer value) {
            addCriterion("air_change_estimated_cost_check_switch >", value, "airChangeEstimatedCostCheckSwitch");
            return (Criteria) this;
        }

        public Criteria andAirChangeEstimatedCostCheckSwitchGreaterThanOrEqualTo(Integer value) {
            addCriterion("air_change_estimated_cost_check_switch >=", value, "airChangeEstimatedCostCheckSwitch");
            return (Criteria) this;
        }

        public Criteria andAirChangeEstimatedCostCheckSwitchLessThan(Integer value) {
            addCriterion("air_change_estimated_cost_check_switch <", value, "airChangeEstimatedCostCheckSwitch");
            return (Criteria) this;
        }

        public Criteria andAirChangeEstimatedCostCheckSwitchLessThanOrEqualTo(Integer value) {
            addCriterion("air_change_estimated_cost_check_switch <=", value, "airChangeEstimatedCostCheckSwitch");
            return (Criteria) this;
        }

        public Criteria andAirChangeEstimatedCostCheckSwitchIn(List<Integer> values) {
            addCriterion("air_change_estimated_cost_check_switch in", values, "airChangeEstimatedCostCheckSwitch");
            return (Criteria) this;
        }

        public Criteria andAirChangeEstimatedCostCheckSwitchNotIn(List<Integer> values) {
            addCriterion("air_change_estimated_cost_check_switch not in", values, "airChangeEstimatedCostCheckSwitch");
            return (Criteria) this;
        }

        public Criteria andAirChangeEstimatedCostCheckSwitchBetween(Integer value1, Integer value2) {
            addCriterion("air_change_estimated_cost_check_switch between", value1, value2, "airChangeEstimatedCostCheckSwitch");
            return (Criteria) this;
        }

        public Criteria andAirChangeEstimatedCostCheckSwitchNotBetween(Integer value1, Integer value2) {
            addCriterion("air_change_estimated_cost_check_switch not between", value1, value2, "airChangeEstimatedCostCheckSwitch");
            return (Criteria) this;
        }

        public Criteria andTrainChangeEstimatedCostCheckSwitchIsNull() {
            addCriterion("train_change_estimated_cost_check_switch is null");
            return (Criteria) this;
        }

        public Criteria andTrainChangeEstimatedCostCheckSwitchIsNotNull() {
            addCriterion("train_change_estimated_cost_check_switch is not null");
            return (Criteria) this;
        }

        public Criteria andTrainChangeEstimatedCostCheckSwitchEqualTo(Integer value) {
            addCriterion("train_change_estimated_cost_check_switch =", value, "trainChangeEstimatedCostCheckSwitch");
            return (Criteria) this;
        }

        public Criteria andTrainChangeEstimatedCostCheckSwitchNotEqualTo(Integer value) {
            addCriterion("train_change_estimated_cost_check_switch <>", value, "trainChangeEstimatedCostCheckSwitch");
            return (Criteria) this;
        }

        public Criteria andTrainChangeEstimatedCostCheckSwitchGreaterThan(Integer value) {
            addCriterion("train_change_estimated_cost_check_switch >", value, "trainChangeEstimatedCostCheckSwitch");
            return (Criteria) this;
        }

        public Criteria andTrainChangeEstimatedCostCheckSwitchGreaterThanOrEqualTo(Integer value) {
            addCriterion("train_change_estimated_cost_check_switch >=", value, "trainChangeEstimatedCostCheckSwitch");
            return (Criteria) this;
        }

        public Criteria andTrainChangeEstimatedCostCheckSwitchLessThan(Integer value) {
            addCriterion("train_change_estimated_cost_check_switch <", value, "trainChangeEstimatedCostCheckSwitch");
            return (Criteria) this;
        }

        public Criteria andTrainChangeEstimatedCostCheckSwitchLessThanOrEqualTo(Integer value) {
            addCriterion("train_change_estimated_cost_check_switch <=", value, "trainChangeEstimatedCostCheckSwitch");
            return (Criteria) this;
        }

        public Criteria andTrainChangeEstimatedCostCheckSwitchIn(List<Integer> values) {
            addCriterion("train_change_estimated_cost_check_switch in", values, "trainChangeEstimatedCostCheckSwitch");
            return (Criteria) this;
        }

        public Criteria andTrainChangeEstimatedCostCheckSwitchNotIn(List<Integer> values) {
            addCriterion("train_change_estimated_cost_check_switch not in", values, "trainChangeEstimatedCostCheckSwitch");
            return (Criteria) this;
        }

        public Criteria andTrainChangeEstimatedCostCheckSwitchBetween(Integer value1, Integer value2) {
            addCriterion("train_change_estimated_cost_check_switch between", value1, value2, "trainChangeEstimatedCostCheckSwitch");
            return (Criteria) this;
        }

        public Criteria andTrainChangeEstimatedCostCheckSwitchNotBetween(Integer value1, Integer value2) {
            addCriterion("train_change_estimated_cost_check_switch not between", value1, value2, "trainChangeEstimatedCostCheckSwitch");
            return (Criteria) this;
        }

        public Criteria andPersonalPaySwitchIsNull() {
            addCriterion("personal_pay_switch is null");
            return (Criteria) this;
        }

        public Criteria andPersonalPaySwitchIsNotNull() {
            addCriterion("personal_pay_switch is not null");
            return (Criteria) this;
        }

        public Criteria andPersonalPaySwitchEqualTo(Integer value) {
            addCriterion("personal_pay_switch =", value, "personalPaySwitch");
            return (Criteria) this;
        }

        public Criteria andPersonalPaySwitchNotEqualTo(Integer value) {
            addCriterion("personal_pay_switch <>", value, "personalPaySwitch");
            return (Criteria) this;
        }

        public Criteria andPersonalPaySwitchGreaterThan(Integer value) {
            addCriterion("personal_pay_switch >", value, "personalPaySwitch");
            return (Criteria) this;
        }

        public Criteria andPersonalPaySwitchGreaterThanOrEqualTo(Integer value) {
            addCriterion("personal_pay_switch >=", value, "personalPaySwitch");
            return (Criteria) this;
        }

        public Criteria andPersonalPaySwitchLessThan(Integer value) {
            addCriterion("personal_pay_switch <", value, "personalPaySwitch");
            return (Criteria) this;
        }

        public Criteria andPersonalPaySwitchLessThanOrEqualTo(Integer value) {
            addCriterion("personal_pay_switch <=", value, "personalPaySwitch");
            return (Criteria) this;
        }

        public Criteria andPersonalPaySwitchIn(List<Integer> values) {
            addCriterion("personal_pay_switch in", values, "personalPaySwitch");
            return (Criteria) this;
        }

        public Criteria andPersonalPaySwitchNotIn(List<Integer> values) {
            addCriterion("personal_pay_switch not in", values, "personalPaySwitch");
            return (Criteria) this;
        }

        public Criteria andPersonalPaySwitchBetween(Integer value1, Integer value2) {
            addCriterion("personal_pay_switch between", value1, value2, "personalPaySwitch");
            return (Criteria) this;
        }

        public Criteria andPersonalPaySwitchNotBetween(Integer value1, Integer value2) {
            addCriterion("personal_pay_switch not between", value1, value2, "personalPaySwitch");
            return (Criteria) this;
        }

        public Criteria andTravelerControlFlagIsNull() {
            addCriterion("traveler_control_flag is null");
            return (Criteria) this;
        }

        public Criteria andTravelerControlFlagIsNotNull() {
            addCriterion("traveler_control_flag is not null");
            return (Criteria) this;
        }

        public Criteria andTravelerControlFlagEqualTo(Integer value) {
            addCriterion("traveler_control_flag =", value, "travelerControlFlag");
            return (Criteria) this;
        }

        public Criteria andTravelerControlFlagNotEqualTo(Integer value) {
            addCriterion("traveler_control_flag <>", value, "travelerControlFlag");
            return (Criteria) this;
        }

        public Criteria andTravelerControlFlagGreaterThan(Integer value) {
            addCriterion("traveler_control_flag >", value, "travelerControlFlag");
            return (Criteria) this;
        }

        public Criteria andTravelerControlFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("traveler_control_flag >=", value, "travelerControlFlag");
            return (Criteria) this;
        }

        public Criteria andTravelerControlFlagLessThan(Integer value) {
            addCriterion("traveler_control_flag <", value, "travelerControlFlag");
            return (Criteria) this;
        }

        public Criteria andTravelerControlFlagLessThanOrEqualTo(Integer value) {
            addCriterion("traveler_control_flag <=", value, "travelerControlFlag");
            return (Criteria) this;
        }

        public Criteria andTravelerControlFlagIn(List<Integer> values) {
            addCriterion("traveler_control_flag in", values, "travelerControlFlag");
            return (Criteria) this;
        }

        public Criteria andTravelerControlFlagNotIn(List<Integer> values) {
            addCriterion("traveler_control_flag not in", values, "travelerControlFlag");
            return (Criteria) this;
        }

        public Criteria andTravelerControlFlagBetween(Integer value1, Integer value2) {
            addCriterion("traveler_control_flag between", value1, value2, "travelerControlFlag");
            return (Criteria) this;
        }

        public Criteria andTravelerControlFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("traveler_control_flag not between", value1, value2, "travelerControlFlag");
            return (Criteria) this;
        }

        public Criteria andTravelerFillTypeIsNull() {
            addCriterion("traveler_fill_type is null");
            return (Criteria) this;
        }

        public Criteria andTravelerFillTypeIsNotNull() {
            addCriterion("traveler_fill_type is not null");
            return (Criteria) this;
        }

        public Criteria andTravelerFillTypeEqualTo(Integer value) {
            addCriterion("traveler_fill_type =", value, "travelerFillType");
            return (Criteria) this;
        }

        public Criteria andTravelerFillTypeNotEqualTo(Integer value) {
            addCriterion("traveler_fill_type <>", value, "travelerFillType");
            return (Criteria) this;
        }

        public Criteria andTravelerFillTypeGreaterThan(Integer value) {
            addCriterion("traveler_fill_type >", value, "travelerFillType");
            return (Criteria) this;
        }

        public Criteria andTravelerFillTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("traveler_fill_type >=", value, "travelerFillType");
            return (Criteria) this;
        }

        public Criteria andTravelerFillTypeLessThan(Integer value) {
            addCriterion("traveler_fill_type <", value, "travelerFillType");
            return (Criteria) this;
        }

        public Criteria andTravelerFillTypeLessThanOrEqualTo(Integer value) {
            addCriterion("traveler_fill_type <=", value, "travelerFillType");
            return (Criteria) this;
        }

        public Criteria andTravelerFillTypeIn(List<Integer> values) {
            addCriterion("traveler_fill_type in", values, "travelerFillType");
            return (Criteria) this;
        }

        public Criteria andTravelerFillTypeNotIn(List<Integer> values) {
            addCriterion("traveler_fill_type not in", values, "travelerFillType");
            return (Criteria) this;
        }

        public Criteria andTravelerFillTypeBetween(Integer value1, Integer value2) {
            addCriterion("traveler_fill_type between", value1, value2, "travelerFillType");
            return (Criteria) this;
        }

        public Criteria andTravelerFillTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("traveler_fill_type not between", value1, value2, "travelerFillType");
            return (Criteria) this;
        }

        public Criteria andOrderCheckTravelerFlagIsNull() {
            addCriterion("order_check_traveler_flag is null");
            return (Criteria) this;
        }

        public Criteria andOrderCheckTravelerFlagIsNotNull() {
            addCriterion("order_check_traveler_flag is not null");
            return (Criteria) this;
        }

        public Criteria andOrderCheckTravelerFlagEqualTo(Integer value) {
            addCriterion("order_check_traveler_flag =", value, "orderCheckTravelerFlag");
            return (Criteria) this;
        }

        public Criteria andOrderCheckTravelerFlagNotEqualTo(Integer value) {
            addCriterion("order_check_traveler_flag <>", value, "orderCheckTravelerFlag");
            return (Criteria) this;
        }

        public Criteria andOrderCheckTravelerFlagGreaterThan(Integer value) {
            addCriterion("order_check_traveler_flag >", value, "orderCheckTravelerFlag");
            return (Criteria) this;
        }

        public Criteria andOrderCheckTravelerFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_check_traveler_flag >=", value, "orderCheckTravelerFlag");
            return (Criteria) this;
        }

        public Criteria andOrderCheckTravelerFlagLessThan(Integer value) {
            addCriterion("order_check_traveler_flag <", value, "orderCheckTravelerFlag");
            return (Criteria) this;
        }

        public Criteria andOrderCheckTravelerFlagLessThanOrEqualTo(Integer value) {
            addCriterion("order_check_traveler_flag <=", value, "orderCheckTravelerFlag");
            return (Criteria) this;
        }

        public Criteria andOrderCheckTravelerFlagIn(List<Integer> values) {
            addCriterion("order_check_traveler_flag in", values, "orderCheckTravelerFlag");
            return (Criteria) this;
        }

        public Criteria andOrderCheckTravelerFlagNotIn(List<Integer> values) {
            addCriterion("order_check_traveler_flag not in", values, "orderCheckTravelerFlag");
            return (Criteria) this;
        }

        public Criteria andOrderCheckTravelerFlagBetween(Integer value1, Integer value2) {
            addCriterion("order_check_traveler_flag between", value1, value2, "orderCheckTravelerFlag");
            return (Criteria) this;
        }

        public Criteria andOrderCheckTravelerFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("order_check_traveler_flag not between", value1, value2, "orderCheckTravelerFlag");
            return (Criteria) this;
        }

        public Criteria andTravelerLimitFlagIsNull() {
            addCriterion("traveler_limit_flag is null");
            return (Criteria) this;
        }

        public Criteria andTravelerLimitFlagIsNotNull() {
            addCriterion("traveler_limit_flag is not null");
            return (Criteria) this;
        }

        public Criteria andTravelerLimitFlagEqualTo(Integer value) {
            addCriterion("traveler_limit_flag =", value, "travelerLimitFlag");
            return (Criteria) this;
        }

        public Criteria andTravelerLimitFlagNotEqualTo(Integer value) {
            addCriterion("traveler_limit_flag <>", value, "travelerLimitFlag");
            return (Criteria) this;
        }

        public Criteria andTravelerLimitFlagGreaterThan(Integer value) {
            addCriterion("traveler_limit_flag >", value, "travelerLimitFlag");
            return (Criteria) this;
        }

        public Criteria andTravelerLimitFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("traveler_limit_flag >=", value, "travelerLimitFlag");
            return (Criteria) this;
        }

        public Criteria andTravelerLimitFlagLessThan(Integer value) {
            addCriterion("traveler_limit_flag <", value, "travelerLimitFlag");
            return (Criteria) this;
        }

        public Criteria andTravelerLimitFlagLessThanOrEqualTo(Integer value) {
            addCriterion("traveler_limit_flag <=", value, "travelerLimitFlag");
            return (Criteria) this;
        }

        public Criteria andTravelerLimitFlagIn(List<Integer> values) {
            addCriterion("traveler_limit_flag in", values, "travelerLimitFlag");
            return (Criteria) this;
        }

        public Criteria andTravelerLimitFlagNotIn(List<Integer> values) {
            addCriterion("traveler_limit_flag not in", values, "travelerLimitFlag");
            return (Criteria) this;
        }

        public Criteria andTravelerLimitFlagBetween(Integer value1, Integer value2) {
            addCriterion("traveler_limit_flag between", value1, value2, "travelerLimitFlag");
            return (Criteria) this;
        }

        public Criteria andTravelerLimitFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("traveler_limit_flag not between", value1, value2, "travelerLimitFlag");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedControlFlagIsNull() {
            addCriterion("total_estimated_control_flag is null");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedControlFlagIsNotNull() {
            addCriterion("total_estimated_control_flag is not null");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedControlFlagEqualTo(Integer value) {
            addCriterion("total_estimated_control_flag =", value, "totalEstimatedControlFlag");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedControlFlagNotEqualTo(Integer value) {
            addCriterion("total_estimated_control_flag <>", value, "totalEstimatedControlFlag");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedControlFlagGreaterThan(Integer value) {
            addCriterion("total_estimated_control_flag >", value, "totalEstimatedControlFlag");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedControlFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("total_estimated_control_flag >=", value, "totalEstimatedControlFlag");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedControlFlagLessThan(Integer value) {
            addCriterion("total_estimated_control_flag <", value, "totalEstimatedControlFlag");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedControlFlagLessThanOrEqualTo(Integer value) {
            addCriterion("total_estimated_control_flag <=", value, "totalEstimatedControlFlag");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedControlFlagIn(List<Integer> values) {
            addCriterion("total_estimated_control_flag in", values, "totalEstimatedControlFlag");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedControlFlagNotIn(List<Integer> values) {
            addCriterion("total_estimated_control_flag not in", values, "totalEstimatedControlFlag");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedControlFlagBetween(Integer value1, Integer value2) {
            addCriterion("total_estimated_control_flag between", value1, value2, "totalEstimatedControlFlag");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedControlFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("total_estimated_control_flag not between", value1, value2, "totalEstimatedControlFlag");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedFillTypeIsNull() {
            addCriterion("total_estimated_fill_type is null");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedFillTypeIsNotNull() {
            addCriterion("total_estimated_fill_type is not null");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedFillTypeEqualTo(Integer value) {
            addCriterion("total_estimated_fill_type =", value, "totalEstimatedFillType");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedFillTypeNotEqualTo(Integer value) {
            addCriterion("total_estimated_fill_type <>", value, "totalEstimatedFillType");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedFillTypeGreaterThan(Integer value) {
            addCriterion("total_estimated_fill_type >", value, "totalEstimatedFillType");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedFillTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("total_estimated_fill_type >=", value, "totalEstimatedFillType");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedFillTypeLessThan(Integer value) {
            addCriterion("total_estimated_fill_type <", value, "totalEstimatedFillType");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedFillTypeLessThanOrEqualTo(Integer value) {
            addCriterion("total_estimated_fill_type <=", value, "totalEstimatedFillType");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedFillTypeIn(List<Integer> values) {
            addCriterion("total_estimated_fill_type in", values, "totalEstimatedFillType");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedFillTypeNotIn(List<Integer> values) {
            addCriterion("total_estimated_fill_type not in", values, "totalEstimatedFillType");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedFillTypeBetween(Integer value1, Integer value2) {
            addCriterion("total_estimated_fill_type between", value1, value2, "totalEstimatedFillType");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedFillTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("total_estimated_fill_type not between", value1, value2, "totalEstimatedFillType");
            return (Criteria) this;
        }

        public Criteria andCheckTotalEstimatedFlagIsNull() {
            addCriterion("check_total_estimated_flag is null");
            return (Criteria) this;
        }

        public Criteria andCheckTotalEstimatedFlagIsNotNull() {
            addCriterion("check_total_estimated_flag is not null");
            return (Criteria) this;
        }

        public Criteria andCheckTotalEstimatedFlagEqualTo(Integer value) {
            addCriterion("check_total_estimated_flag =", value, "checkTotalEstimatedFlag");
            return (Criteria) this;
        }

        public Criteria andCheckTotalEstimatedFlagNotEqualTo(Integer value) {
            addCriterion("check_total_estimated_flag <>", value, "checkTotalEstimatedFlag");
            return (Criteria) this;
        }

        public Criteria andCheckTotalEstimatedFlagGreaterThan(Integer value) {
            addCriterion("check_total_estimated_flag >", value, "checkTotalEstimatedFlag");
            return (Criteria) this;
        }

        public Criteria andCheckTotalEstimatedFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("check_total_estimated_flag >=", value, "checkTotalEstimatedFlag");
            return (Criteria) this;
        }

        public Criteria andCheckTotalEstimatedFlagLessThan(Integer value) {
            addCriterion("check_total_estimated_flag <", value, "checkTotalEstimatedFlag");
            return (Criteria) this;
        }

        public Criteria andCheckTotalEstimatedFlagLessThanOrEqualTo(Integer value) {
            addCriterion("check_total_estimated_flag <=", value, "checkTotalEstimatedFlag");
            return (Criteria) this;
        }

        public Criteria andCheckTotalEstimatedFlagIn(List<Integer> values) {
            addCriterion("check_total_estimated_flag in", values, "checkTotalEstimatedFlag");
            return (Criteria) this;
        }

        public Criteria andCheckTotalEstimatedFlagNotIn(List<Integer> values) {
            addCriterion("check_total_estimated_flag not in", values, "checkTotalEstimatedFlag");
            return (Criteria) this;
        }

        public Criteria andCheckTotalEstimatedFlagBetween(Integer value1, Integer value2) {
            addCriterion("check_total_estimated_flag between", value1, value2, "checkTotalEstimatedFlag");
            return (Criteria) this;
        }

        public Criteria andCheckTotalEstimatedFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("check_total_estimated_flag not between", value1, value2, "checkTotalEstimatedFlag");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedLimitTypeIsNull() {
            addCriterion("total_estimated_limit_type is null");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedLimitTypeIsNotNull() {
            addCriterion("total_estimated_limit_type is not null");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedLimitTypeEqualTo(Integer value) {
            addCriterion("total_estimated_limit_type =", value, "totalEstimatedLimitType");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedLimitTypeNotEqualTo(Integer value) {
            addCriterion("total_estimated_limit_type <>", value, "totalEstimatedLimitType");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedLimitTypeGreaterThan(Integer value) {
            addCriterion("total_estimated_limit_type >", value, "totalEstimatedLimitType");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedLimitTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("total_estimated_limit_type >=", value, "totalEstimatedLimitType");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedLimitTypeLessThan(Integer value) {
            addCriterion("total_estimated_limit_type <", value, "totalEstimatedLimitType");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedLimitTypeLessThanOrEqualTo(Integer value) {
            addCriterion("total_estimated_limit_type <=", value, "totalEstimatedLimitType");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedLimitTypeIn(List<Integer> values) {
            addCriterion("total_estimated_limit_type in", values, "totalEstimatedLimitType");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedLimitTypeNotIn(List<Integer> values) {
            addCriterion("total_estimated_limit_type not in", values, "totalEstimatedLimitType");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedLimitTypeBetween(Integer value1, Integer value2) {
            addCriterion("total_estimated_limit_type between", value1, value2, "totalEstimatedLimitType");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedLimitTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("total_estimated_limit_type not between", value1, value2, "totalEstimatedLimitType");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedLimitAmountIsNull() {
            addCriterion("total_estimated_limit_amount is null");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedLimitAmountIsNotNull() {
            addCriterion("total_estimated_limit_amount is not null");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedLimitAmountEqualTo(BigDecimal value) {
            addCriterion("total_estimated_limit_amount =", value, "totalEstimatedLimitAmount");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedLimitAmountNotEqualTo(BigDecimal value) {
            addCriterion("total_estimated_limit_amount <>", value, "totalEstimatedLimitAmount");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedLimitAmountGreaterThan(BigDecimal value) {
            addCriterion("total_estimated_limit_amount >", value, "totalEstimatedLimitAmount");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedLimitAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_estimated_limit_amount >=", value, "totalEstimatedLimitAmount");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedLimitAmountLessThan(BigDecimal value) {
            addCriterion("total_estimated_limit_amount <", value, "totalEstimatedLimitAmount");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedLimitAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_estimated_limit_amount <=", value, "totalEstimatedLimitAmount");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedLimitAmountIn(List<BigDecimal> values) {
            addCriterion("total_estimated_limit_amount in", values, "totalEstimatedLimitAmount");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedLimitAmountNotIn(List<BigDecimal> values) {
            addCriterion("total_estimated_limit_amount not in", values, "totalEstimatedLimitAmount");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedLimitAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_estimated_limit_amount between", value1, value2, "totalEstimatedLimitAmount");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedLimitAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_estimated_limit_amount not between", value1, value2, "totalEstimatedLimitAmount");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedCheckSceneIsNull() {
            addCriterion("total_estimated_check_scene is null");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedCheckSceneIsNotNull() {
            addCriterion("total_estimated_check_scene is not null");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedCheckSceneEqualTo(String value) {
            addCriterion("total_estimated_check_scene =", value, "totalEstimatedCheckScene");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedCheckSceneNotEqualTo(String value) {
            addCriterion("total_estimated_check_scene <>", value, "totalEstimatedCheckScene");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedCheckSceneGreaterThan(String value) {
            addCriterion("total_estimated_check_scene >", value, "totalEstimatedCheckScene");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedCheckSceneGreaterThanOrEqualTo(String value) {
            addCriterion("total_estimated_check_scene >=", value, "totalEstimatedCheckScene");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedCheckSceneLessThan(String value) {
            addCriterion("total_estimated_check_scene <", value, "totalEstimatedCheckScene");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedCheckSceneLessThanOrEqualTo(String value) {
            addCriterion("total_estimated_check_scene <=", value, "totalEstimatedCheckScene");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedCheckSceneLike(String value) {
            addCriterion("total_estimated_check_scene like", value, "totalEstimatedCheckScene");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedCheckSceneNotLike(String value) {
            addCriterion("total_estimated_check_scene not like", value, "totalEstimatedCheckScene");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedCheckSceneIn(List<String> values) {
            addCriterion("total_estimated_check_scene in", values, "totalEstimatedCheckScene");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedCheckSceneNotIn(List<String> values) {
            addCriterion("total_estimated_check_scene not in", values, "totalEstimatedCheckScene");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedCheckSceneBetween(String value1, String value2) {
            addCriterion("total_estimated_check_scene between", value1, value2, "totalEstimatedCheckScene");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedCheckSceneNotBetween(String value1, String value2) {
            addCriterion("total_estimated_check_scene not between", value1, value2, "totalEstimatedCheckScene");
            return (Criteria) this;
        }

        public Criteria andMaxAverageDailyTravelCostAmountIsNull() {
            addCriterion("max_average_daily_travel_cost_amount is null");
            return (Criteria) this;
        }

        public Criteria andMaxAverageDailyTravelCostAmountIsNotNull() {
            addCriterion("max_average_daily_travel_cost_amount is not null");
            return (Criteria) this;
        }

        public Criteria andMaxAverageDailyTravelCostAmountEqualTo(Long value) {
            addCriterion("max_average_daily_travel_cost_amount =", value, "maxAverageDailyTravelCostAmount");
            return (Criteria) this;
        }

        public Criteria andMaxAverageDailyTravelCostAmountNotEqualTo(Long value) {
            addCriterion("max_average_daily_travel_cost_amount <>", value, "maxAverageDailyTravelCostAmount");
            return (Criteria) this;
        }

        public Criteria andMaxAverageDailyTravelCostAmountGreaterThan(Long value) {
            addCriterion("max_average_daily_travel_cost_amount >", value, "maxAverageDailyTravelCostAmount");
            return (Criteria) this;
        }

        public Criteria andMaxAverageDailyTravelCostAmountGreaterThanOrEqualTo(Long value) {
            addCriterion("max_average_daily_travel_cost_amount >=", value, "maxAverageDailyTravelCostAmount");
            return (Criteria) this;
        }

        public Criteria andMaxAverageDailyTravelCostAmountLessThan(Long value) {
            addCriterion("max_average_daily_travel_cost_amount <", value, "maxAverageDailyTravelCostAmount");
            return (Criteria) this;
        }

        public Criteria andMaxAverageDailyTravelCostAmountLessThanOrEqualTo(Long value) {
            addCriterion("max_average_daily_travel_cost_amount <=", value, "maxAverageDailyTravelCostAmount");
            return (Criteria) this;
        }

        public Criteria andMaxAverageDailyTravelCostAmountIn(List<Long> values) {
            addCriterion("max_average_daily_travel_cost_amount in", values, "maxAverageDailyTravelCostAmount");
            return (Criteria) this;
        }

        public Criteria andMaxAverageDailyTravelCostAmountNotIn(List<Long> values) {
            addCriterion("max_average_daily_travel_cost_amount not in", values, "maxAverageDailyTravelCostAmount");
            return (Criteria) this;
        }

        public Criteria andMaxAverageDailyTravelCostAmountBetween(Long value1, Long value2) {
            addCriterion("max_average_daily_travel_cost_amount between", value1, value2, "maxAverageDailyTravelCostAmount");
            return (Criteria) this;
        }

        public Criteria andMaxAverageDailyTravelCostAmountNotBetween(Long value1, Long value2) {
            addCriterion("max_average_daily_travel_cost_amount not between", value1, value2, "maxAverageDailyTravelCostAmount");
            return (Criteria) this;
        }

        public Criteria andOnlyCheckEstimatedAmountSwitchIsNull() {
            addCriterion("only_check_estimated_amount_switch is null");
            return (Criteria) this;
        }

        public Criteria andOnlyCheckEstimatedAmountSwitchIsNotNull() {
            addCriterion("only_check_estimated_amount_switch is not null");
            return (Criteria) this;
        }

        public Criteria andOnlyCheckEstimatedAmountSwitchEqualTo(Integer value) {
            addCriterion("only_check_estimated_amount_switch =", value, "onlyCheckEstimatedAmountSwitch");
            return (Criteria) this;
        }

        public Criteria andOnlyCheckEstimatedAmountSwitchNotEqualTo(Integer value) {
            addCriterion("only_check_estimated_amount_switch <>", value, "onlyCheckEstimatedAmountSwitch");
            return (Criteria) this;
        }

        public Criteria andOnlyCheckEstimatedAmountSwitchGreaterThan(Integer value) {
            addCriterion("only_check_estimated_amount_switch >", value, "onlyCheckEstimatedAmountSwitch");
            return (Criteria) this;
        }

        public Criteria andOnlyCheckEstimatedAmountSwitchGreaterThanOrEqualTo(Integer value) {
            addCriterion("only_check_estimated_amount_switch >=", value, "onlyCheckEstimatedAmountSwitch");
            return (Criteria) this;
        }

        public Criteria andOnlyCheckEstimatedAmountSwitchLessThan(Integer value) {
            addCriterion("only_check_estimated_amount_switch <", value, "onlyCheckEstimatedAmountSwitch");
            return (Criteria) this;
        }

        public Criteria andOnlyCheckEstimatedAmountSwitchLessThanOrEqualTo(Integer value) {
            addCriterion("only_check_estimated_amount_switch <=", value, "onlyCheckEstimatedAmountSwitch");
            return (Criteria) this;
        }

        public Criteria andOnlyCheckEstimatedAmountSwitchIn(List<Integer> values) {
            addCriterion("only_check_estimated_amount_switch in", values, "onlyCheckEstimatedAmountSwitch");
            return (Criteria) this;
        }

        public Criteria andOnlyCheckEstimatedAmountSwitchNotIn(List<Integer> values) {
            addCriterion("only_check_estimated_amount_switch not in", values, "onlyCheckEstimatedAmountSwitch");
            return (Criteria) this;
        }

        public Criteria andOnlyCheckEstimatedAmountSwitchBetween(Integer value1, Integer value2) {
            addCriterion("only_check_estimated_amount_switch between", value1, value2, "onlyCheckEstimatedAmountSwitch");
            return (Criteria) this;
        }

        public Criteria andOnlyCheckEstimatedAmountSwitchNotBetween(Integer value1, Integer value2) {
            addCriterion("only_check_estimated_amount_switch not between", value1, value2, "onlyCheckEstimatedAmountSwitch");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedPersonalPaySwitchIsNull() {
            addCriterion("total_estimated_personal_pay_switch is null");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedPersonalPaySwitchIsNotNull() {
            addCriterion("total_estimated_personal_pay_switch is not null");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedPersonalPaySwitchEqualTo(Integer value) {
            addCriterion("total_estimated_personal_pay_switch =", value, "totalEstimatedPersonalPaySwitch");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedPersonalPaySwitchNotEqualTo(Integer value) {
            addCriterion("total_estimated_personal_pay_switch <>", value, "totalEstimatedPersonalPaySwitch");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedPersonalPaySwitchGreaterThan(Integer value) {
            addCriterion("total_estimated_personal_pay_switch >", value, "totalEstimatedPersonalPaySwitch");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedPersonalPaySwitchGreaterThanOrEqualTo(Integer value) {
            addCriterion("total_estimated_personal_pay_switch >=", value, "totalEstimatedPersonalPaySwitch");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedPersonalPaySwitchLessThan(Integer value) {
            addCriterion("total_estimated_personal_pay_switch <", value, "totalEstimatedPersonalPaySwitch");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedPersonalPaySwitchLessThanOrEqualTo(Integer value) {
            addCriterion("total_estimated_personal_pay_switch <=", value, "totalEstimatedPersonalPaySwitch");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedPersonalPaySwitchIn(List<Integer> values) {
            addCriterion("total_estimated_personal_pay_switch in", values, "totalEstimatedPersonalPaySwitch");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedPersonalPaySwitchNotIn(List<Integer> values) {
            addCriterion("total_estimated_personal_pay_switch not in", values, "totalEstimatedPersonalPaySwitch");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedPersonalPaySwitchBetween(Integer value1, Integer value2) {
            addCriterion("total_estimated_personal_pay_switch between", value1, value2, "totalEstimatedPersonalPaySwitch");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedPersonalPaySwitchNotBetween(Integer value1, Integer value2) {
            addCriterion("total_estimated_personal_pay_switch not between", value1, value2, "totalEstimatedPersonalPaySwitch");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedPersonalPaySceneListIsNull() {
            addCriterion("total_estimated_personal_pay_scene_list is null");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedPersonalPaySceneListIsNotNull() {
            addCriterion("total_estimated_personal_pay_scene_list is not null");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedPersonalPaySceneListEqualTo(String value) {
            addCriterion("total_estimated_personal_pay_scene_list =", value, "totalEstimatedPersonalPaySceneList");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedPersonalPaySceneListNotEqualTo(String value) {
            addCriterion("total_estimated_personal_pay_scene_list <>", value, "totalEstimatedPersonalPaySceneList");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedPersonalPaySceneListGreaterThan(String value) {
            addCriterion("total_estimated_personal_pay_scene_list >", value, "totalEstimatedPersonalPaySceneList");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedPersonalPaySceneListGreaterThanOrEqualTo(String value) {
            addCriterion("total_estimated_personal_pay_scene_list >=", value, "totalEstimatedPersonalPaySceneList");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedPersonalPaySceneListLessThan(String value) {
            addCriterion("total_estimated_personal_pay_scene_list <", value, "totalEstimatedPersonalPaySceneList");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedPersonalPaySceneListLessThanOrEqualTo(String value) {
            addCriterion("total_estimated_personal_pay_scene_list <=", value, "totalEstimatedPersonalPaySceneList");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedPersonalPaySceneListLike(String value) {
            addCriterion("total_estimated_personal_pay_scene_list like", value, "totalEstimatedPersonalPaySceneList");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedPersonalPaySceneListNotLike(String value) {
            addCriterion("total_estimated_personal_pay_scene_list not like", value, "totalEstimatedPersonalPaySceneList");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedPersonalPaySceneListIn(List<String> values) {
            addCriterion("total_estimated_personal_pay_scene_list in", values, "totalEstimatedPersonalPaySceneList");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedPersonalPaySceneListNotIn(List<String> values) {
            addCriterion("total_estimated_personal_pay_scene_list not in", values, "totalEstimatedPersonalPaySceneList");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedPersonalPaySceneListBetween(String value1, String value2) {
            addCriterion("total_estimated_personal_pay_scene_list between", value1, value2, "totalEstimatedPersonalPaySceneList");
            return (Criteria) this;
        }

        public Criteria andTotalEstimatedPersonalPaySceneListNotBetween(String value1, String value2) {
            addCriterion("total_estimated_personal_pay_scene_list not between", value1, value2, "totalEstimatedPersonalPaySceneList");
            return (Criteria) this;
        }

        public Criteria andApplyReasonBringInOrderIsNull() {
            addCriterion("apply_reason_bring_in_order is null");
            return (Criteria) this;
        }

        public Criteria andApplyReasonBringInOrderIsNotNull() {
            addCriterion("apply_reason_bring_in_order is not null");
            return (Criteria) this;
        }

        public Criteria andApplyReasonBringInOrderEqualTo(Integer value) {
            addCriterion("apply_reason_bring_in_order =", value, "applyReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andApplyReasonBringInOrderNotEqualTo(Integer value) {
            addCriterion("apply_reason_bring_in_order <>", value, "applyReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andApplyReasonBringInOrderGreaterThan(Integer value) {
            addCriterion("apply_reason_bring_in_order >", value, "applyReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andApplyReasonBringInOrderGreaterThanOrEqualTo(Integer value) {
            addCriterion("apply_reason_bring_in_order >=", value, "applyReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andApplyReasonBringInOrderLessThan(Integer value) {
            addCriterion("apply_reason_bring_in_order <", value, "applyReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andApplyReasonBringInOrderLessThanOrEqualTo(Integer value) {
            addCriterion("apply_reason_bring_in_order <=", value, "applyReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andApplyReasonBringInOrderIn(List<Integer> values) {
            addCriterion("apply_reason_bring_in_order in", values, "applyReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andApplyReasonBringInOrderNotIn(List<Integer> values) {
            addCriterion("apply_reason_bring_in_order not in", values, "applyReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andApplyReasonBringInOrderBetween(Integer value1, Integer value2) {
            addCriterion("apply_reason_bring_in_order between", value1, value2, "applyReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andApplyReasonBringInOrderNotBetween(Integer value1, Integer value2) {
            addCriterion("apply_reason_bring_in_order not between", value1, value2, "applyReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andCostAttributionControlFlagIsNull() {
            addCriterion("cost_attribution_control_flag is null");
            return (Criteria) this;
        }

        public Criteria andCostAttributionControlFlagIsNotNull() {
            addCriterion("cost_attribution_control_flag is not null");
            return (Criteria) this;
        }

        public Criteria andCostAttributionControlFlagEqualTo(Integer value) {
            addCriterion("cost_attribution_control_flag =", value, "costAttributionControlFlag");
            return (Criteria) this;
        }

        public Criteria andCostAttributionControlFlagNotEqualTo(Integer value) {
            addCriterion("cost_attribution_control_flag <>", value, "costAttributionControlFlag");
            return (Criteria) this;
        }

        public Criteria andCostAttributionControlFlagGreaterThan(Integer value) {
            addCriterion("cost_attribution_control_flag >", value, "costAttributionControlFlag");
            return (Criteria) this;
        }

        public Criteria andCostAttributionControlFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("cost_attribution_control_flag >=", value, "costAttributionControlFlag");
            return (Criteria) this;
        }

        public Criteria andCostAttributionControlFlagLessThan(Integer value) {
            addCriterion("cost_attribution_control_flag <", value, "costAttributionControlFlag");
            return (Criteria) this;
        }

        public Criteria andCostAttributionControlFlagLessThanOrEqualTo(Integer value) {
            addCriterion("cost_attribution_control_flag <=", value, "costAttributionControlFlag");
            return (Criteria) this;
        }

        public Criteria andCostAttributionControlFlagIn(List<Integer> values) {
            addCriterion("cost_attribution_control_flag in", values, "costAttributionControlFlag");
            return (Criteria) this;
        }

        public Criteria andCostAttributionControlFlagNotIn(List<Integer> values) {
            addCriterion("cost_attribution_control_flag not in", values, "costAttributionControlFlag");
            return (Criteria) this;
        }

        public Criteria andCostAttributionControlFlagBetween(Integer value1, Integer value2) {
            addCriterion("cost_attribution_control_flag between", value1, value2, "costAttributionControlFlag");
            return (Criteria) this;
        }

        public Criteria andCostAttributionControlFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("cost_attribution_control_flag not between", value1, value2, "costAttributionControlFlag");
            return (Criteria) this;
        }

        public Criteria andCostAttributionFillTypeIsNull() {
            addCriterion("cost_attribution_fill_type is null");
            return (Criteria) this;
        }

        public Criteria andCostAttributionFillTypeIsNotNull() {
            addCriterion("cost_attribution_fill_type is not null");
            return (Criteria) this;
        }

        public Criteria andCostAttributionFillTypeEqualTo(Integer value) {
            addCriterion("cost_attribution_fill_type =", value, "costAttributionFillType");
            return (Criteria) this;
        }

        public Criteria andCostAttributionFillTypeNotEqualTo(Integer value) {
            addCriterion("cost_attribution_fill_type <>", value, "costAttributionFillType");
            return (Criteria) this;
        }

        public Criteria andCostAttributionFillTypeGreaterThan(Integer value) {
            addCriterion("cost_attribution_fill_type >", value, "costAttributionFillType");
            return (Criteria) this;
        }

        public Criteria andCostAttributionFillTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("cost_attribution_fill_type >=", value, "costAttributionFillType");
            return (Criteria) this;
        }

        public Criteria andCostAttributionFillTypeLessThan(Integer value) {
            addCriterion("cost_attribution_fill_type <", value, "costAttributionFillType");
            return (Criteria) this;
        }

        public Criteria andCostAttributionFillTypeLessThanOrEqualTo(Integer value) {
            addCriterion("cost_attribution_fill_type <=", value, "costAttributionFillType");
            return (Criteria) this;
        }

        public Criteria andCostAttributionFillTypeIn(List<Integer> values) {
            addCriterion("cost_attribution_fill_type in", values, "costAttributionFillType");
            return (Criteria) this;
        }

        public Criteria andCostAttributionFillTypeNotIn(List<Integer> values) {
            addCriterion("cost_attribution_fill_type not in", values, "costAttributionFillType");
            return (Criteria) this;
        }

        public Criteria andCostAttributionFillTypeBetween(Integer value1, Integer value2) {
            addCriterion("cost_attribution_fill_type between", value1, value2, "costAttributionFillType");
            return (Criteria) this;
        }

        public Criteria andCostAttributionFillTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("cost_attribution_fill_type not between", value1, value2, "costAttributionFillType");
            return (Criteria) this;
        }

        public Criteria andCostAttributionBringInOrderFlagIsNull() {
            addCriterion("cost_attribution_bring_in_order_flag is null");
            return (Criteria) this;
        }

        public Criteria andCostAttributionBringInOrderFlagIsNotNull() {
            addCriterion("cost_attribution_bring_in_order_flag is not null");
            return (Criteria) this;
        }

        public Criteria andCostAttributionBringInOrderFlagEqualTo(Integer value) {
            addCriterion("cost_attribution_bring_in_order_flag =", value, "costAttributionBringInOrderFlag");
            return (Criteria) this;
        }

        public Criteria andCostAttributionBringInOrderFlagNotEqualTo(Integer value) {
            addCriterion("cost_attribution_bring_in_order_flag <>", value, "costAttributionBringInOrderFlag");
            return (Criteria) this;
        }

        public Criteria andCostAttributionBringInOrderFlagGreaterThan(Integer value) {
            addCriterion("cost_attribution_bring_in_order_flag >", value, "costAttributionBringInOrderFlag");
            return (Criteria) this;
        }

        public Criteria andCostAttributionBringInOrderFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("cost_attribution_bring_in_order_flag >=", value, "costAttributionBringInOrderFlag");
            return (Criteria) this;
        }

        public Criteria andCostAttributionBringInOrderFlagLessThan(Integer value) {
            addCriterion("cost_attribution_bring_in_order_flag <", value, "costAttributionBringInOrderFlag");
            return (Criteria) this;
        }

        public Criteria andCostAttributionBringInOrderFlagLessThanOrEqualTo(Integer value) {
            addCriterion("cost_attribution_bring_in_order_flag <=", value, "costAttributionBringInOrderFlag");
            return (Criteria) this;
        }

        public Criteria andCostAttributionBringInOrderFlagIn(List<Integer> values) {
            addCriterion("cost_attribution_bring_in_order_flag in", values, "costAttributionBringInOrderFlag");
            return (Criteria) this;
        }

        public Criteria andCostAttributionBringInOrderFlagNotIn(List<Integer> values) {
            addCriterion("cost_attribution_bring_in_order_flag not in", values, "costAttributionBringInOrderFlag");
            return (Criteria) this;
        }

        public Criteria andCostAttributionBringInOrderFlagBetween(Integer value1, Integer value2) {
            addCriterion("cost_attribution_bring_in_order_flag between", value1, value2, "costAttributionBringInOrderFlag");
            return (Criteria) this;
        }

        public Criteria andCostAttributionBringInOrderFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("cost_attribution_bring_in_order_flag not between", value1, value2, "costAttributionBringInOrderFlag");
            return (Criteria) this;
        }

        public Criteria andTakeawayUseCountLimitTypeIsNull() {
            addCriterion("takeaway_use_count_limit_type is null");
            return (Criteria) this;
        }

        public Criteria andTakeawayUseCountLimitTypeIsNotNull() {
            addCriterion("takeaway_use_count_limit_type is not null");
            return (Criteria) this;
        }

        public Criteria andTakeawayUseCountLimitTypeEqualTo(Integer value) {
            addCriterion("takeaway_use_count_limit_type =", value, "takeawayUseCountLimitType");
            return (Criteria) this;
        }

        public Criteria andTakeawayUseCountLimitTypeNotEqualTo(Integer value) {
            addCriterion("takeaway_use_count_limit_type <>", value, "takeawayUseCountLimitType");
            return (Criteria) this;
        }

        public Criteria andTakeawayUseCountLimitTypeGreaterThan(Integer value) {
            addCriterion("takeaway_use_count_limit_type >", value, "takeawayUseCountLimitType");
            return (Criteria) this;
        }

        public Criteria andTakeawayUseCountLimitTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("takeaway_use_count_limit_type >=", value, "takeawayUseCountLimitType");
            return (Criteria) this;
        }

        public Criteria andTakeawayUseCountLimitTypeLessThan(Integer value) {
            addCriterion("takeaway_use_count_limit_type <", value, "takeawayUseCountLimitType");
            return (Criteria) this;
        }

        public Criteria andTakeawayUseCountLimitTypeLessThanOrEqualTo(Integer value) {
            addCriterion("takeaway_use_count_limit_type <=", value, "takeawayUseCountLimitType");
            return (Criteria) this;
        }

        public Criteria andTakeawayUseCountLimitTypeIn(List<Integer> values) {
            addCriterion("takeaway_use_count_limit_type in", values, "takeawayUseCountLimitType");
            return (Criteria) this;
        }

        public Criteria andTakeawayUseCountLimitTypeNotIn(List<Integer> values) {
            addCriterion("takeaway_use_count_limit_type not in", values, "takeawayUseCountLimitType");
            return (Criteria) this;
        }

        public Criteria andTakeawayUseCountLimitTypeBetween(Integer value1, Integer value2) {
            addCriterion("takeaway_use_count_limit_type between", value1, value2, "takeawayUseCountLimitType");
            return (Criteria) this;
        }

        public Criteria andTakeawayUseCountLimitTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("takeaway_use_count_limit_type not between", value1, value2, "takeawayUseCountLimitType");
            return (Criteria) this;
        }

        public Criteria andMeishiUseCountLimitTypeIsNull() {
            addCriterion("meishi_use_count_limit_type is null");
            return (Criteria) this;
        }

        public Criteria andMeishiUseCountLimitTypeIsNotNull() {
            addCriterion("meishi_use_count_limit_type is not null");
            return (Criteria) this;
        }

        public Criteria andMeishiUseCountLimitTypeEqualTo(Integer value) {
            addCriterion("meishi_use_count_limit_type =", value, "meishiUseCountLimitType");
            return (Criteria) this;
        }

        public Criteria andMeishiUseCountLimitTypeNotEqualTo(Integer value) {
            addCriterion("meishi_use_count_limit_type <>", value, "meishiUseCountLimitType");
            return (Criteria) this;
        }

        public Criteria andMeishiUseCountLimitTypeGreaterThan(Integer value) {
            addCriterion("meishi_use_count_limit_type >", value, "meishiUseCountLimitType");
            return (Criteria) this;
        }

        public Criteria andMeishiUseCountLimitTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("meishi_use_count_limit_type >=", value, "meishiUseCountLimitType");
            return (Criteria) this;
        }

        public Criteria andMeishiUseCountLimitTypeLessThan(Integer value) {
            addCriterion("meishi_use_count_limit_type <", value, "meishiUseCountLimitType");
            return (Criteria) this;
        }

        public Criteria andMeishiUseCountLimitTypeLessThanOrEqualTo(Integer value) {
            addCriterion("meishi_use_count_limit_type <=", value, "meishiUseCountLimitType");
            return (Criteria) this;
        }

        public Criteria andMeishiUseCountLimitTypeIn(List<Integer> values) {
            addCriterion("meishi_use_count_limit_type in", values, "meishiUseCountLimitType");
            return (Criteria) this;
        }

        public Criteria andMeishiUseCountLimitTypeNotIn(List<Integer> values) {
            addCriterion("meishi_use_count_limit_type not in", values, "meishiUseCountLimitType");
            return (Criteria) this;
        }

        public Criteria andMeishiUseCountLimitTypeBetween(Integer value1, Integer value2) {
            addCriterion("meishi_use_count_limit_type between", value1, value2, "meishiUseCountLimitType");
            return (Criteria) this;
        }

        public Criteria andMeishiUseCountLimitTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("meishi_use_count_limit_type not between", value1, value2, "meishiUseCountLimitType");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andTravelDateControlFlagIsNull() {
            addCriterion("travel_date_control_flag is null");
            return (Criteria) this;
        }

        public Criteria andTravelDateControlFlagIsNotNull() {
            addCriterion("travel_date_control_flag is not null");
            return (Criteria) this;
        }

        public Criteria andTravelDateControlFlagEqualTo(Integer value) {
            addCriterion("travel_date_control_flag =", value, "travelDateControlFlag");
            return (Criteria) this;
        }

        public Criteria andTravelDateControlFlagNotEqualTo(Integer value) {
            addCriterion("travel_date_control_flag <>", value, "travelDateControlFlag");
            return (Criteria) this;
        }

        public Criteria andTravelDateControlFlagGreaterThan(Integer value) {
            addCriterion("travel_date_control_flag >", value, "travelDateControlFlag");
            return (Criteria) this;
        }

        public Criteria andTravelDateControlFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("travel_date_control_flag >=", value, "travelDateControlFlag");
            return (Criteria) this;
        }

        public Criteria andTravelDateControlFlagLessThan(Integer value) {
            addCriterion("travel_date_control_flag <", value, "travelDateControlFlag");
            return (Criteria) this;
        }

        public Criteria andTravelDateControlFlagLessThanOrEqualTo(Integer value) {
            addCriterion("travel_date_control_flag <=", value, "travelDateControlFlag");
            return (Criteria) this;
        }

        public Criteria andTravelDateControlFlagIn(List<Integer> values) {
            addCriterion("travel_date_control_flag in", values, "travelDateControlFlag");
            return (Criteria) this;
        }

        public Criteria andTravelDateControlFlagNotIn(List<Integer> values) {
            addCriterion("travel_date_control_flag not in", values, "travelDateControlFlag");
            return (Criteria) this;
        }

        public Criteria andTravelDateControlFlagBetween(Integer value1, Integer value2) {
            addCriterion("travel_date_control_flag between", value1, value2, "travelDateControlFlag");
            return (Criteria) this;
        }

        public Criteria andTravelDateControlFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("travel_date_control_flag not between", value1, value2, "travelDateControlFlag");
            return (Criteria) this;
        }

        public Criteria andTravelDateFillTypeIsNull() {
            addCriterion("travel_date_fill_type is null");
            return (Criteria) this;
        }

        public Criteria andTravelDateFillTypeIsNotNull() {
            addCriterion("travel_date_fill_type is not null");
            return (Criteria) this;
        }

        public Criteria andTravelDateFillTypeEqualTo(Integer value) {
            addCriterion("travel_date_fill_type =", value, "travelDateFillType");
            return (Criteria) this;
        }

        public Criteria andTravelDateFillTypeNotEqualTo(Integer value) {
            addCriterion("travel_date_fill_type <>", value, "travelDateFillType");
            return (Criteria) this;
        }

        public Criteria andTravelDateFillTypeGreaterThan(Integer value) {
            addCriterion("travel_date_fill_type >", value, "travelDateFillType");
            return (Criteria) this;
        }

        public Criteria andTravelDateFillTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("travel_date_fill_type >=", value, "travelDateFillType");
            return (Criteria) this;
        }

        public Criteria andTravelDateFillTypeLessThan(Integer value) {
            addCriterion("travel_date_fill_type <", value, "travelDateFillType");
            return (Criteria) this;
        }

        public Criteria andTravelDateFillTypeLessThanOrEqualTo(Integer value) {
            addCriterion("travel_date_fill_type <=", value, "travelDateFillType");
            return (Criteria) this;
        }

        public Criteria andTravelDateFillTypeIn(List<Integer> values) {
            addCriterion("travel_date_fill_type in", values, "travelDateFillType");
            return (Criteria) this;
        }

        public Criteria andTravelDateFillTypeNotIn(List<Integer> values) {
            addCriterion("travel_date_fill_type not in", values, "travelDateFillType");
            return (Criteria) this;
        }

        public Criteria andTravelDateFillTypeBetween(Integer value1, Integer value2) {
            addCriterion("travel_date_fill_type between", value1, value2, "travelDateFillType");
            return (Criteria) this;
        }

        public Criteria andTravelDateFillTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("travel_date_fill_type not between", value1, value2, "travelDateFillType");
            return (Criteria) this;
        }

        public Criteria andTravelDateDuplicateVerificationIsNull() {
            addCriterion("travel_date_duplicate_verification is null");
            return (Criteria) this;
        }

        public Criteria andTravelDateDuplicateVerificationIsNotNull() {
            addCriterion("travel_date_duplicate_verification is not null");
            return (Criteria) this;
        }

        public Criteria andTravelDateDuplicateVerificationEqualTo(Integer value) {
            addCriterion("travel_date_duplicate_verification =", value, "travelDateDuplicateVerification");
            return (Criteria) this;
        }

        public Criteria andTravelDateDuplicateVerificationNotEqualTo(Integer value) {
            addCriterion("travel_date_duplicate_verification <>", value, "travelDateDuplicateVerification");
            return (Criteria) this;
        }

        public Criteria andTravelDateDuplicateVerificationGreaterThan(Integer value) {
            addCriterion("travel_date_duplicate_verification >", value, "travelDateDuplicateVerification");
            return (Criteria) this;
        }

        public Criteria andTravelDateDuplicateVerificationGreaterThanOrEqualTo(Integer value) {
            addCriterion("travel_date_duplicate_verification >=", value, "travelDateDuplicateVerification");
            return (Criteria) this;
        }

        public Criteria andTravelDateDuplicateVerificationLessThan(Integer value) {
            addCriterion("travel_date_duplicate_verification <", value, "travelDateDuplicateVerification");
            return (Criteria) this;
        }

        public Criteria andTravelDateDuplicateVerificationLessThanOrEqualTo(Integer value) {
            addCriterion("travel_date_duplicate_verification <=", value, "travelDateDuplicateVerification");
            return (Criteria) this;
        }

        public Criteria andTravelDateDuplicateVerificationIn(List<Integer> values) {
            addCriterion("travel_date_duplicate_verification in", values, "travelDateDuplicateVerification");
            return (Criteria) this;
        }

        public Criteria andTravelDateDuplicateVerificationNotIn(List<Integer> values) {
            addCriterion("travel_date_duplicate_verification not in", values, "travelDateDuplicateVerification");
            return (Criteria) this;
        }

        public Criteria andTravelDateDuplicateVerificationBetween(Integer value1, Integer value2) {
            addCriterion("travel_date_duplicate_verification between", value1, value2, "travelDateDuplicateVerification");
            return (Criteria) this;
        }

        public Criteria andTravelDateDuplicateVerificationNotBetween(Integer value1, Integer value2) {
            addCriterion("travel_date_duplicate_verification not between", value1, value2, "travelDateDuplicateVerification");
            return (Criteria) this;
        }

        public Criteria andTravelDateAutoCalculationIsNull() {
            addCriterion("travel_date_auto_calculation is null");
            return (Criteria) this;
        }

        public Criteria andTravelDateAutoCalculationIsNotNull() {
            addCriterion("travel_date_auto_calculation is not null");
            return (Criteria) this;
        }

        public Criteria andTravelDateAutoCalculationEqualTo(Integer value) {
            addCriterion("travel_date_auto_calculation =", value, "travelDateAutoCalculation");
            return (Criteria) this;
        }

        public Criteria andTravelDateAutoCalculationNotEqualTo(Integer value) {
            addCriterion("travel_date_auto_calculation <>", value, "travelDateAutoCalculation");
            return (Criteria) this;
        }

        public Criteria andTravelDateAutoCalculationGreaterThan(Integer value) {
            addCriterion("travel_date_auto_calculation >", value, "travelDateAutoCalculation");
            return (Criteria) this;
        }

        public Criteria andTravelDateAutoCalculationGreaterThanOrEqualTo(Integer value) {
            addCriterion("travel_date_auto_calculation >=", value, "travelDateAutoCalculation");
            return (Criteria) this;
        }

        public Criteria andTravelDateAutoCalculationLessThan(Integer value) {
            addCriterion("travel_date_auto_calculation <", value, "travelDateAutoCalculation");
            return (Criteria) this;
        }

        public Criteria andTravelDateAutoCalculationLessThanOrEqualTo(Integer value) {
            addCriterion("travel_date_auto_calculation <=", value, "travelDateAutoCalculation");
            return (Criteria) this;
        }

        public Criteria andTravelDateAutoCalculationIn(List<Integer> values) {
            addCriterion("travel_date_auto_calculation in", values, "travelDateAutoCalculation");
            return (Criteria) this;
        }

        public Criteria andTravelDateAutoCalculationNotIn(List<Integer> values) {
            addCriterion("travel_date_auto_calculation not in", values, "travelDateAutoCalculation");
            return (Criteria) this;
        }

        public Criteria andTravelDateAutoCalculationBetween(Integer value1, Integer value2) {
            addCriterion("travel_date_auto_calculation between", value1, value2, "travelDateAutoCalculation");
            return (Criteria) this;
        }

        public Criteria andTravelDateAutoCalculationNotBetween(Integer value1, Integer value2) {
            addCriterion("travel_date_auto_calculation not between", value1, value2, "travelDateAutoCalculation");
            return (Criteria) this;
        }

        public Criteria andTravelDateFormatIsNull() {
            addCriterion("travel_date_format is null");
            return (Criteria) this;
        }

        public Criteria andTravelDateFormatIsNotNull() {
            addCriterion("travel_date_format is not null");
            return (Criteria) this;
        }

        public Criteria andTravelDateFormatEqualTo(Integer value) {
            addCriterion("travel_date_format =", value, "travelDateFormat");
            return (Criteria) this;
        }

        public Criteria andTravelDateFormatNotEqualTo(Integer value) {
            addCriterion("travel_date_format <>", value, "travelDateFormat");
            return (Criteria) this;
        }

        public Criteria andTravelDateFormatGreaterThan(Integer value) {
            addCriterion("travel_date_format >", value, "travelDateFormat");
            return (Criteria) this;
        }

        public Criteria andTravelDateFormatGreaterThanOrEqualTo(Integer value) {
            addCriterion("travel_date_format >=", value, "travelDateFormat");
            return (Criteria) this;
        }

        public Criteria andTravelDateFormatLessThan(Integer value) {
            addCriterion("travel_date_format <", value, "travelDateFormat");
            return (Criteria) this;
        }

        public Criteria andTravelDateFormatLessThanOrEqualTo(Integer value) {
            addCriterion("travel_date_format <=", value, "travelDateFormat");
            return (Criteria) this;
        }

        public Criteria andTravelDateFormatIn(List<Integer> values) {
            addCriterion("travel_date_format in", values, "travelDateFormat");
            return (Criteria) this;
        }

        public Criteria andTravelDateFormatNotIn(List<Integer> values) {
            addCriterion("travel_date_format not in", values, "travelDateFormat");
            return (Criteria) this;
        }

        public Criteria andTravelDateFormatBetween(Integer value1, Integer value2) {
            addCriterion("travel_date_format between", value1, value2, "travelDateFormat");
            return (Criteria) this;
        }

        public Criteria andTravelDateFormatNotBetween(Integer value1, Integer value2) {
            addCriterion("travel_date_format not between", value1, value2, "travelDateFormat");
            return (Criteria) this;
        }

        public Criteria andTravelDateCrossFormIsNull() {
            addCriterion("travel_date_cross_form is null");
            return (Criteria) this;
        }

        public Criteria andTravelDateCrossFormIsNotNull() {
            addCriterion("travel_date_cross_form is not null");
            return (Criteria) this;
        }

        public Criteria andTravelDateCrossFormEqualTo(Integer value) {
            addCriterion("travel_date_cross_form =", value, "travelDateCrossForm");
            return (Criteria) this;
        }

        public Criteria andTravelDateCrossFormNotEqualTo(Integer value) {
            addCriterion("travel_date_cross_form <>", value, "travelDateCrossForm");
            return (Criteria) this;
        }

        public Criteria andTravelDateCrossFormGreaterThan(Integer value) {
            addCriterion("travel_date_cross_form >", value, "travelDateCrossForm");
            return (Criteria) this;
        }

        public Criteria andTravelDateCrossFormGreaterThanOrEqualTo(Integer value) {
            addCriterion("travel_date_cross_form >=", value, "travelDateCrossForm");
            return (Criteria) this;
        }

        public Criteria andTravelDateCrossFormLessThan(Integer value) {
            addCriterion("travel_date_cross_form <", value, "travelDateCrossForm");
            return (Criteria) this;
        }

        public Criteria andTravelDateCrossFormLessThanOrEqualTo(Integer value) {
            addCriterion("travel_date_cross_form <=", value, "travelDateCrossForm");
            return (Criteria) this;
        }

        public Criteria andTravelDateCrossFormIn(List<Integer> values) {
            addCriterion("travel_date_cross_form in", values, "travelDateCrossForm");
            return (Criteria) this;
        }

        public Criteria andTravelDateCrossFormNotIn(List<Integer> values) {
            addCriterion("travel_date_cross_form not in", values, "travelDateCrossForm");
            return (Criteria) this;
        }

        public Criteria andTravelDateCrossFormBetween(Integer value1, Integer value2) {
            addCriterion("travel_date_cross_form between", value1, value2, "travelDateCrossForm");
            return (Criteria) this;
        }

        public Criteria andTravelDateCrossFormNotBetween(Integer value1, Integer value2) {
            addCriterion("travel_date_cross_form not between", value1, value2, "travelDateCrossForm");
            return (Criteria) this;
        }

        public Criteria andAddressTypeIsNull() {
            addCriterion("address_type is null");
            return (Criteria) this;
        }

        public Criteria andAddressTypeIsNotNull() {
            addCriterion("address_type is not null");
            return (Criteria) this;
        }

        public Criteria andAddressTypeEqualTo(Integer value) {
            addCriterion("address_type =", value, "addressType");
            return (Criteria) this;
        }

        public Criteria andAddressTypeNotEqualTo(Integer value) {
            addCriterion("address_type <>", value, "addressType");
            return (Criteria) this;
        }

        public Criteria andAddressTypeGreaterThan(Integer value) {
            addCriterion("address_type >", value, "addressType");
            return (Criteria) this;
        }

        public Criteria andAddressTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("address_type >=", value, "addressType");
            return (Criteria) this;
        }

        public Criteria andAddressTypeLessThan(Integer value) {
            addCriterion("address_type <", value, "addressType");
            return (Criteria) this;
        }

        public Criteria andAddressTypeLessThanOrEqualTo(Integer value) {
            addCriterion("address_type <=", value, "addressType");
            return (Criteria) this;
        }

        public Criteria andAddressTypeIn(List<Integer> values) {
            addCriterion("address_type in", values, "addressType");
            return (Criteria) this;
        }

        public Criteria andAddressTypeNotIn(List<Integer> values) {
            addCriterion("address_type not in", values, "addressType");
            return (Criteria) this;
        }

        public Criteria andAddressTypeBetween(Integer value1, Integer value2) {
            addCriterion("address_type between", value1, value2, "addressType");
            return (Criteria) this;
        }

        public Criteria andAddressTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("address_type not between", value1, value2, "addressType");
            return (Criteria) this;
        }

        public Criteria andApplySceneDefaultValueIsNull() {
            addCriterion("apply_scene_default_value is null");
            return (Criteria) this;
        }

        public Criteria andApplySceneDefaultValueIsNotNull() {
            addCriterion("apply_scene_default_value is not null");
            return (Criteria) this;
        }

        public Criteria andApplySceneDefaultValueEqualTo(Integer value) {
            addCriterion("apply_scene_default_value =", value, "applySceneDefaultValue");
            return (Criteria) this;
        }

        public Criteria andApplySceneDefaultValueNotEqualTo(Integer value) {
            addCriterion("apply_scene_default_value <>", value, "applySceneDefaultValue");
            return (Criteria) this;
        }

        public Criteria andApplySceneDefaultValueGreaterThan(Integer value) {
            addCriterion("apply_scene_default_value >", value, "applySceneDefaultValue");
            return (Criteria) this;
        }

        public Criteria andApplySceneDefaultValueGreaterThanOrEqualTo(Integer value) {
            addCriterion("apply_scene_default_value >=", value, "applySceneDefaultValue");
            return (Criteria) this;
        }

        public Criteria andApplySceneDefaultValueLessThan(Integer value) {
            addCriterion("apply_scene_default_value <", value, "applySceneDefaultValue");
            return (Criteria) this;
        }

        public Criteria andApplySceneDefaultValueLessThanOrEqualTo(Integer value) {
            addCriterion("apply_scene_default_value <=", value, "applySceneDefaultValue");
            return (Criteria) this;
        }

        public Criteria andApplySceneDefaultValueIn(List<Integer> values) {
            addCriterion("apply_scene_default_value in", values, "applySceneDefaultValue");
            return (Criteria) this;
        }

        public Criteria andApplySceneDefaultValueNotIn(List<Integer> values) {
            addCriterion("apply_scene_default_value not in", values, "applySceneDefaultValue");
            return (Criteria) this;
        }

        public Criteria andApplySceneDefaultValueBetween(Integer value1, Integer value2) {
            addCriterion("apply_scene_default_value between", value1, value2, "applySceneDefaultValue");
            return (Criteria) this;
        }

        public Criteria andApplySceneDefaultValueNotBetween(Integer value1, Integer value2) {
            addCriterion("apply_scene_default_value not between", value1, value2, "applySceneDefaultValue");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedAllOrderSwitchIsNull() {
            addCriterion("travel_form_estimated_all_order_switch is null");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedAllOrderSwitchIsNotNull() {
            addCriterion("travel_form_estimated_all_order_switch is not null");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedAllOrderSwitchEqualTo(Integer value) {
            addCriterion("travel_form_estimated_all_order_switch =", value, "travelFormEstimatedAllOrderSwitch");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedAllOrderSwitchNotEqualTo(Integer value) {
            addCriterion("travel_form_estimated_all_order_switch <>", value, "travelFormEstimatedAllOrderSwitch");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedAllOrderSwitchGreaterThan(Integer value) {
            addCriterion("travel_form_estimated_all_order_switch >", value, "travelFormEstimatedAllOrderSwitch");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedAllOrderSwitchGreaterThanOrEqualTo(Integer value) {
            addCriterion("travel_form_estimated_all_order_switch >=", value, "travelFormEstimatedAllOrderSwitch");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedAllOrderSwitchLessThan(Integer value) {
            addCriterion("travel_form_estimated_all_order_switch <", value, "travelFormEstimatedAllOrderSwitch");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedAllOrderSwitchLessThanOrEqualTo(Integer value) {
            addCriterion("travel_form_estimated_all_order_switch <=", value, "travelFormEstimatedAllOrderSwitch");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedAllOrderSwitchIn(List<Integer> values) {
            addCriterion("travel_form_estimated_all_order_switch in", values, "travelFormEstimatedAllOrderSwitch");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedAllOrderSwitchNotIn(List<Integer> values) {
            addCriterion("travel_form_estimated_all_order_switch not in", values, "travelFormEstimatedAllOrderSwitch");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedAllOrderSwitchBetween(Integer value1, Integer value2) {
            addCriterion("travel_form_estimated_all_order_switch between", value1, value2, "travelFormEstimatedAllOrderSwitch");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedAllOrderSwitchNotBetween(Integer value1, Integer value2) {
            addCriterion("travel_form_estimated_all_order_switch not between", value1, value2, "travelFormEstimatedAllOrderSwitch");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedSingleUpSwitchIsNull() {
            addCriterion("travel_form_estimated_single_up_switch is null");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedSingleUpSwitchIsNotNull() {
            addCriterion("travel_form_estimated_single_up_switch is not null");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedSingleUpSwitchEqualTo(Integer value) {
            addCriterion("travel_form_estimated_single_up_switch =", value, "travelFormEstimatedSingleUpSwitch");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedSingleUpSwitchNotEqualTo(Integer value) {
            addCriterion("travel_form_estimated_single_up_switch <>", value, "travelFormEstimatedSingleUpSwitch");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedSingleUpSwitchGreaterThan(Integer value) {
            addCriterion("travel_form_estimated_single_up_switch >", value, "travelFormEstimatedSingleUpSwitch");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedSingleUpSwitchGreaterThanOrEqualTo(Integer value) {
            addCriterion("travel_form_estimated_single_up_switch >=", value, "travelFormEstimatedSingleUpSwitch");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedSingleUpSwitchLessThan(Integer value) {
            addCriterion("travel_form_estimated_single_up_switch <", value, "travelFormEstimatedSingleUpSwitch");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedSingleUpSwitchLessThanOrEqualTo(Integer value) {
            addCriterion("travel_form_estimated_single_up_switch <=", value, "travelFormEstimatedSingleUpSwitch");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedSingleUpSwitchIn(List<Integer> values) {
            addCriterion("travel_form_estimated_single_up_switch in", values, "travelFormEstimatedSingleUpSwitch");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedSingleUpSwitchNotIn(List<Integer> values) {
            addCriterion("travel_form_estimated_single_up_switch not in", values, "travelFormEstimatedSingleUpSwitch");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedSingleUpSwitchBetween(Integer value1, Integer value2) {
            addCriterion("travel_form_estimated_single_up_switch between", value1, value2, "travelFormEstimatedSingleUpSwitch");
            return (Criteria) this;
        }

        public Criteria andTravelFormEstimatedSingleUpSwitchNotBetween(Integer value1, Integer value2) {
            addCriterion("travel_form_estimated_single_up_switch not between", value1, value2, "travelFormEstimatedSingleUpSwitch");
            return (Criteria) this;
        }

        public Criteria andControlUpgradeFlagIsNull() {
            addCriterion("control_upgrade_flag is null");
            return (Criteria) this;
        }

        public Criteria andControlUpgradeFlagIsNotNull() {
            addCriterion("control_upgrade_flag is not null");
            return (Criteria) this;
        }

        public Criteria andControlUpgradeFlagEqualTo(Integer value) {
            addCriterion("control_upgrade_flag =", value, "controlUpgradeFlag");
            return (Criteria) this;
        }

        public Criteria andControlUpgradeFlagNotEqualTo(Integer value) {
            addCriterion("control_upgrade_flag <>", value, "controlUpgradeFlag");
            return (Criteria) this;
        }

        public Criteria andControlUpgradeFlagGreaterThan(Integer value) {
            addCriterion("control_upgrade_flag >", value, "controlUpgradeFlag");
            return (Criteria) this;
        }

        public Criteria andControlUpgradeFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("control_upgrade_flag >=", value, "controlUpgradeFlag");
            return (Criteria) this;
        }

        public Criteria andControlUpgradeFlagLessThan(Integer value) {
            addCriterion("control_upgrade_flag <", value, "controlUpgradeFlag");
            return (Criteria) this;
        }

        public Criteria andControlUpgradeFlagLessThanOrEqualTo(Integer value) {
            addCriterion("control_upgrade_flag <=", value, "controlUpgradeFlag");
            return (Criteria) this;
        }

        public Criteria andControlUpgradeFlagIn(List<Integer> values) {
            addCriterion("control_upgrade_flag in", values, "controlUpgradeFlag");
            return (Criteria) this;
        }

        public Criteria andControlUpgradeFlagNotIn(List<Integer> values) {
            addCriterion("control_upgrade_flag not in", values, "controlUpgradeFlag");
            return (Criteria) this;
        }

        public Criteria andControlUpgradeFlagBetween(Integer value1, Integer value2) {
            addCriterion("control_upgrade_flag between", value1, value2, "controlUpgradeFlag");
            return (Criteria) this;
        }

        public Criteria andControlUpgradeFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("control_upgrade_flag not between", value1, value2, "controlUpgradeFlag");
            return (Criteria) this;
        }

        public Criteria andUseCountLimitTypeIsNull() {
            addCriterion("use_count_limit_type is null");
            return (Criteria) this;
        }

        public Criteria andUseCountLimitTypeIsNotNull() {
            addCriterion("use_count_limit_type is not null");
            return (Criteria) this;
        }

        public Criteria andUseCountLimitTypeEqualTo(Integer value) {
            addCriterion("use_count_limit_type =", value, "useCountLimitType");
            return (Criteria) this;
        }

        public Criteria andUseCountLimitTypeNotEqualTo(Integer value) {
            addCriterion("use_count_limit_type <>", value, "useCountLimitType");
            return (Criteria) this;
        }

        public Criteria andUseCountLimitTypeGreaterThan(Integer value) {
            addCriterion("use_count_limit_type >", value, "useCountLimitType");
            return (Criteria) this;
        }

        public Criteria andUseCountLimitTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("use_count_limit_type >=", value, "useCountLimitType");
            return (Criteria) this;
        }

        public Criteria andUseCountLimitTypeLessThan(Integer value) {
            addCriterion("use_count_limit_type <", value, "useCountLimitType");
            return (Criteria) this;
        }

        public Criteria andUseCountLimitTypeLessThanOrEqualTo(Integer value) {
            addCriterion("use_count_limit_type <=", value, "useCountLimitType");
            return (Criteria) this;
        }

        public Criteria andUseCountLimitTypeIn(List<Integer> values) {
            addCriterion("use_count_limit_type in", values, "useCountLimitType");
            return (Criteria) this;
        }

        public Criteria andUseCountLimitTypeNotIn(List<Integer> values) {
            addCriterion("use_count_limit_type not in", values, "useCountLimitType");
            return (Criteria) this;
        }

        public Criteria andUseCountLimitTypeBetween(Integer value1, Integer value2) {
            addCriterion("use_count_limit_type between", value1, value2, "useCountLimitType");
            return (Criteria) this;
        }

        public Criteria andUseCountLimitTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("use_count_limit_type not between", value1, value2, "useCountLimitType");
            return (Criteria) this;
        }

        public Criteria andCityLimitTypeIsNull() {
            addCriterion("city_limit_type is null");
            return (Criteria) this;
        }

        public Criteria andCityLimitTypeIsNotNull() {
            addCriterion("city_limit_type is not null");
            return (Criteria) this;
        }

        public Criteria andCityLimitTypeEqualTo(Integer value) {
            addCriterion("city_limit_type =", value, "cityLimitType");
            return (Criteria) this;
        }

        public Criteria andCityLimitTypeNotEqualTo(Integer value) {
            addCriterion("city_limit_type <>", value, "cityLimitType");
            return (Criteria) this;
        }

        public Criteria andCityLimitTypeGreaterThan(Integer value) {
            addCriterion("city_limit_type >", value, "cityLimitType");
            return (Criteria) this;
        }

        public Criteria andCityLimitTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("city_limit_type >=", value, "cityLimitType");
            return (Criteria) this;
        }

        public Criteria andCityLimitTypeLessThan(Integer value) {
            addCriterion("city_limit_type <", value, "cityLimitType");
            return (Criteria) this;
        }

        public Criteria andCityLimitTypeLessThanOrEqualTo(Integer value) {
            addCriterion("city_limit_type <=", value, "cityLimitType");
            return (Criteria) this;
        }

        public Criteria andCityLimitTypeIn(List<Integer> values) {
            addCriterion("city_limit_type in", values, "cityLimitType");
            return (Criteria) this;
        }

        public Criteria andCityLimitTypeNotIn(List<Integer> values) {
            addCriterion("city_limit_type not in", values, "cityLimitType");
            return (Criteria) this;
        }

        public Criteria andCityLimitTypeBetween(Integer value1, Integer value2) {
            addCriterion("city_limit_type between", value1, value2, "cityLimitType");
            return (Criteria) this;
        }

        public Criteria andCityLimitTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("city_limit_type not between", value1, value2, "cityLimitType");
            return (Criteria) this;
        }

        public Criteria andTripApplyReasonBringInOrderIsNull() {
            addCriterion("trip_apply_reason_bring_in_order is null");
            return (Criteria) this;
        }

        public Criteria andTripApplyReasonBringInOrderIsNotNull() {
            addCriterion("trip_apply_reason_bring_in_order is not null");
            return (Criteria) this;
        }

        public Criteria andTripApplyReasonBringInOrderEqualTo(Integer value) {
            addCriterion("trip_apply_reason_bring_in_order =", value, "tripApplyReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andTripApplyReasonBringInOrderNotEqualTo(Integer value) {
            addCriterion("trip_apply_reason_bring_in_order <>", value, "tripApplyReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andTripApplyReasonBringInOrderGreaterThan(Integer value) {
            addCriterion("trip_apply_reason_bring_in_order >", value, "tripApplyReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andTripApplyReasonBringInOrderGreaterThanOrEqualTo(Integer value) {
            addCriterion("trip_apply_reason_bring_in_order >=", value, "tripApplyReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andTripApplyReasonBringInOrderLessThan(Integer value) {
            addCriterion("trip_apply_reason_bring_in_order <", value, "tripApplyReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andTripApplyReasonBringInOrderLessThanOrEqualTo(Integer value) {
            addCriterion("trip_apply_reason_bring_in_order <=", value, "tripApplyReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andTripApplyReasonBringInOrderIn(List<Integer> values) {
            addCriterion("trip_apply_reason_bring_in_order in", values, "tripApplyReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andTripApplyReasonBringInOrderNotIn(List<Integer> values) {
            addCriterion("trip_apply_reason_bring_in_order not in", values, "tripApplyReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andTripApplyReasonBringInOrderBetween(Integer value1, Integer value2) {
            addCriterion("trip_apply_reason_bring_in_order between", value1, value2, "tripApplyReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andTripApplyReasonBringInOrderNotBetween(Integer value1, Integer value2) {
            addCriterion("trip_apply_reason_bring_in_order not between", value1, value2, "tripApplyReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andAddReasonBringInOrderIsNull() {
            addCriterion("add_reason_bring_in_order is null");
            return (Criteria) this;
        }

        public Criteria andAddReasonBringInOrderIsNotNull() {
            addCriterion("add_reason_bring_in_order is not null");
            return (Criteria) this;
        }

        public Criteria andAddReasonBringInOrderEqualTo(Integer value) {
            addCriterion("add_reason_bring_in_order =", value, "addReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andAddReasonBringInOrderNotEqualTo(Integer value) {
            addCriterion("add_reason_bring_in_order <>", value, "addReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andAddReasonBringInOrderGreaterThan(Integer value) {
            addCriterion("add_reason_bring_in_order >", value, "addReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andAddReasonBringInOrderGreaterThanOrEqualTo(Integer value) {
            addCriterion("add_reason_bring_in_order >=", value, "addReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andAddReasonBringInOrderLessThan(Integer value) {
            addCriterion("add_reason_bring_in_order <", value, "addReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andAddReasonBringInOrderLessThanOrEqualTo(Integer value) {
            addCriterion("add_reason_bring_in_order <=", value, "addReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andAddReasonBringInOrderIn(List<Integer> values) {
            addCriterion("add_reason_bring_in_order in", values, "addReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andAddReasonBringInOrderNotIn(List<Integer> values) {
            addCriterion("add_reason_bring_in_order not in", values, "addReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andAddReasonBringInOrderBetween(Integer value1, Integer value2) {
            addCriterion("add_reason_bring_in_order between", value1, value2, "addReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andAddReasonBringInOrderNotBetween(Integer value1, Integer value2) {
            addCriterion("add_reason_bring_in_order not between", value1, value2, "addReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andTripAddReasonBringInOrderIsNull() {
            addCriterion("trip_add_reason_bring_in_order is null");
            return (Criteria) this;
        }

        public Criteria andTripAddReasonBringInOrderIsNotNull() {
            addCriterion("trip_add_reason_bring_in_order is not null");
            return (Criteria) this;
        }

        public Criteria andTripAddReasonBringInOrderEqualTo(Integer value) {
            addCriterion("trip_add_reason_bring_in_order =", value, "tripAddReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andTripAddReasonBringInOrderNotEqualTo(Integer value) {
            addCriterion("trip_add_reason_bring_in_order <>", value, "tripAddReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andTripAddReasonBringInOrderGreaterThan(Integer value) {
            addCriterion("trip_add_reason_bring_in_order >", value, "tripAddReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andTripAddReasonBringInOrderGreaterThanOrEqualTo(Integer value) {
            addCriterion("trip_add_reason_bring_in_order >=", value, "tripAddReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andTripAddReasonBringInOrderLessThan(Integer value) {
            addCriterion("trip_add_reason_bring_in_order <", value, "tripAddReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andTripAddReasonBringInOrderLessThanOrEqualTo(Integer value) {
            addCriterion("trip_add_reason_bring_in_order <=", value, "tripAddReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andTripAddReasonBringInOrderIn(List<Integer> values) {
            addCriterion("trip_add_reason_bring_in_order in", values, "tripAddReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andTripAddReasonBringInOrderNotIn(List<Integer> values) {
            addCriterion("trip_add_reason_bring_in_order not in", values, "tripAddReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andTripAddReasonBringInOrderBetween(Integer value1, Integer value2) {
            addCriterion("trip_add_reason_bring_in_order between", value1, value2, "tripAddReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andTripAddReasonBringInOrderNotBetween(Integer value1, Integer value2) {
            addCriterion("trip_add_reason_bring_in_order not between", value1, value2, "tripAddReasonBringInOrder");
            return (Criteria) this;
        }

        public Criteria andTravelDateItemLimitIsNull() {
            addCriterion("travel_date_item_limit is null");
            return (Criteria) this;
        }

        public Criteria andTravelDateItemLimitIsNotNull() {
            addCriterion("travel_date_item_limit is not null");
            return (Criteria) this;
        }

        public Criteria andTravelDateItemLimitEqualTo(Integer value) {
            addCriterion("travel_date_item_limit =", value, "travelDateItemLimit");
            return (Criteria) this;
        }

        public Criteria andTravelDateItemLimitNotEqualTo(Integer value) {
            addCriterion("travel_date_item_limit <>", value, "travelDateItemLimit");
            return (Criteria) this;
        }

        public Criteria andTravelDateItemLimitGreaterThan(Integer value) {
            addCriterion("travel_date_item_limit >", value, "travelDateItemLimit");
            return (Criteria) this;
        }

        public Criteria andTravelDateItemLimitGreaterThanOrEqualTo(Integer value) {
            addCriterion("travel_date_item_limit >=", value, "travelDateItemLimit");
            return (Criteria) this;
        }

        public Criteria andTravelDateItemLimitLessThan(Integer value) {
            addCriterion("travel_date_item_limit <", value, "travelDateItemLimit");
            return (Criteria) this;
        }

        public Criteria andTravelDateItemLimitLessThanOrEqualTo(Integer value) {
            addCriterion("travel_date_item_limit <=", value, "travelDateItemLimit");
            return (Criteria) this;
        }

        public Criteria andTravelDateItemLimitIn(List<Integer> values) {
            addCriterion("travel_date_item_limit in", values, "travelDateItemLimit");
            return (Criteria) this;
        }

        public Criteria andTravelDateItemLimitNotIn(List<Integer> values) {
            addCriterion("travel_date_item_limit not in", values, "travelDateItemLimit");
            return (Criteria) this;
        }

        public Criteria andTravelDateItemLimitBetween(Integer value1, Integer value2) {
            addCriterion("travel_date_item_limit between", value1, value2, "travelDateItemLimit");
            return (Criteria) this;
        }

        public Criteria andTravelDateItemLimitNotBetween(Integer value1, Integer value2) {
            addCriterion("travel_date_item_limit not between", value1, value2, "travelDateItemLimit");
            return (Criteria) this;
        }

        public Criteria andDeliveryPeriodLimitIsNull() {
            addCriterion("delivery_period_limit is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryPeriodLimitIsNotNull() {
            addCriterion("delivery_period_limit is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryPeriodLimitEqualTo(Integer value) {
            addCriterion("delivery_period_limit =", value, "deliveryPeriodLimit");
            return (Criteria) this;
        }

        public Criteria andDeliveryPeriodLimitNotEqualTo(Integer value) {
            addCriterion("delivery_period_limit <>", value, "deliveryPeriodLimit");
            return (Criteria) this;
        }

        public Criteria andDeliveryPeriodLimitGreaterThan(Integer value) {
            addCriterion("delivery_period_limit >", value, "deliveryPeriodLimit");
            return (Criteria) this;
        }

        public Criteria andDeliveryPeriodLimitGreaterThanOrEqualTo(Integer value) {
            addCriterion("delivery_period_limit >=", value, "deliveryPeriodLimit");
            return (Criteria) this;
        }

        public Criteria andDeliveryPeriodLimitLessThan(Integer value) {
            addCriterion("delivery_period_limit <", value, "deliveryPeriodLimit");
            return (Criteria) this;
        }

        public Criteria andDeliveryPeriodLimitLessThanOrEqualTo(Integer value) {
            addCriterion("delivery_period_limit <=", value, "deliveryPeriodLimit");
            return (Criteria) this;
        }

        public Criteria andDeliveryPeriodLimitIn(List<Integer> values) {
            addCriterion("delivery_period_limit in", values, "deliveryPeriodLimit");
            return (Criteria) this;
        }

        public Criteria andDeliveryPeriodLimitNotIn(List<Integer> values) {
            addCriterion("delivery_period_limit not in", values, "deliveryPeriodLimit");
            return (Criteria) this;
        }

        public Criteria andDeliveryPeriodLimitBetween(Integer value1, Integer value2) {
            addCriterion("delivery_period_limit between", value1, value2, "deliveryPeriodLimit");
            return (Criteria) this;
        }

        public Criteria andDeliveryPeriodLimitNotBetween(Integer value1, Integer value2) {
            addCriterion("delivery_period_limit not between", value1, value2, "deliveryPeriodLimit");
            return (Criteria) this;
        }

        public Criteria andStartCityTypeIsNull() {
            addCriterion("start_city_type is null");
            return (Criteria) this;
        }

        public Criteria andStartCityTypeIsNotNull() {
            addCriterion("start_city_type is not null");
            return (Criteria) this;
        }

        public Criteria andStartCityTypeEqualTo(Integer value) {
            addCriterion("start_city_type =", value, "startCityType");
            return (Criteria) this;
        }

        public Criteria andStartCityTypeNotEqualTo(Integer value) {
            addCriterion("start_city_type <>", value, "startCityType");
            return (Criteria) this;
        }

        public Criteria andStartCityTypeGreaterThan(Integer value) {
            addCriterion("start_city_type >", value, "startCityType");
            return (Criteria) this;
        }

        public Criteria andStartCityTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("start_city_type >=", value, "startCityType");
            return (Criteria) this;
        }

        public Criteria andStartCityTypeLessThan(Integer value) {
            addCriterion("start_city_type <", value, "startCityType");
            return (Criteria) this;
        }

        public Criteria andStartCityTypeLessThanOrEqualTo(Integer value) {
            addCriterion("start_city_type <=", value, "startCityType");
            return (Criteria) this;
        }

        public Criteria andStartCityTypeIn(List<Integer> values) {
            addCriterion("start_city_type in", values, "startCityType");
            return (Criteria) this;
        }

        public Criteria andStartCityTypeNotIn(List<Integer> values) {
            addCriterion("start_city_type not in", values, "startCityType");
            return (Criteria) this;
        }

        public Criteria andStartCityTypeBetween(Integer value1, Integer value2) {
            addCriterion("start_city_type between", value1, value2, "startCityType");
            return (Criteria) this;
        }

        public Criteria andStartCityTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("start_city_type not between", value1, value2, "startCityType");
            return (Criteria) this;
        }

        public Criteria andArrivalCityTypeIsNull() {
            addCriterion("arrival_city_type is null");
            return (Criteria) this;
        }

        public Criteria andArrivalCityTypeIsNotNull() {
            addCriterion("arrival_city_type is not null");
            return (Criteria) this;
        }

        public Criteria andArrivalCityTypeEqualTo(Integer value) {
            addCriterion("arrival_city_type =", value, "arrivalCityType");
            return (Criteria) this;
        }

        public Criteria andArrivalCityTypeNotEqualTo(Integer value) {
            addCriterion("arrival_city_type <>", value, "arrivalCityType");
            return (Criteria) this;
        }

        public Criteria andArrivalCityTypeGreaterThan(Integer value) {
            addCriterion("arrival_city_type >", value, "arrivalCityType");
            return (Criteria) this;
        }

        public Criteria andArrivalCityTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("arrival_city_type >=", value, "arrivalCityType");
            return (Criteria) this;
        }

        public Criteria andArrivalCityTypeLessThan(Integer value) {
            addCriterion("arrival_city_type <", value, "arrivalCityType");
            return (Criteria) this;
        }

        public Criteria andArrivalCityTypeLessThanOrEqualTo(Integer value) {
            addCriterion("arrival_city_type <=", value, "arrivalCityType");
            return (Criteria) this;
        }

        public Criteria andArrivalCityTypeIn(List<Integer> values) {
            addCriterion("arrival_city_type in", values, "arrivalCityType");
            return (Criteria) this;
        }

        public Criteria andArrivalCityTypeNotIn(List<Integer> values) {
            addCriterion("arrival_city_type not in", values, "arrivalCityType");
            return (Criteria) this;
        }

        public Criteria andArrivalCityTypeBetween(Integer value1, Integer value2) {
            addCriterion("arrival_city_type between", value1, value2, "arrivalCityType");
            return (Criteria) this;
        }

        public Criteria andArrivalCityTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("arrival_city_type not between", value1, value2, "arrivalCityType");
            return (Criteria) this;
        }

        public Criteria andEstimatedAmountControlRequiredIsNull() {
            addCriterion("estimated_amount_control_required is null");
            return (Criteria) this;
        }

        public Criteria andEstimatedAmountControlRequiredIsNotNull() {
            addCriterion("estimated_amount_control_required is not null");
            return (Criteria) this;
        }

        public Criteria andEstimatedAmountControlRequiredEqualTo(Integer value) {
            addCriterion("estimated_amount_control_required =", value, "estimatedAmountControlRequired");
            return (Criteria) this;
        }

        public Criteria andEstimatedAmountControlRequiredNotEqualTo(Integer value) {
            addCriterion("estimated_amount_control_required <>", value, "estimatedAmountControlRequired");
            return (Criteria) this;
        }

        public Criteria andEstimatedAmountControlRequiredGreaterThan(Integer value) {
            addCriterion("estimated_amount_control_required >", value, "estimatedAmountControlRequired");
            return (Criteria) this;
        }

        public Criteria andEstimatedAmountControlRequiredGreaterThanOrEqualTo(Integer value) {
            addCriterion("estimated_amount_control_required >=", value, "estimatedAmountControlRequired");
            return (Criteria) this;
        }

        public Criteria andEstimatedAmountControlRequiredLessThan(Integer value) {
            addCriterion("estimated_amount_control_required <", value, "estimatedAmountControlRequired");
            return (Criteria) this;
        }

        public Criteria andEstimatedAmountControlRequiredLessThanOrEqualTo(Integer value) {
            addCriterion("estimated_amount_control_required <=", value, "estimatedAmountControlRequired");
            return (Criteria) this;
        }

        public Criteria andEstimatedAmountControlRequiredIn(List<Integer> values) {
            addCriterion("estimated_amount_control_required in", values, "estimatedAmountControlRequired");
            return (Criteria) this;
        }

        public Criteria andEstimatedAmountControlRequiredNotIn(List<Integer> values) {
            addCriterion("estimated_amount_control_required not in", values, "estimatedAmountControlRequired");
            return (Criteria) this;
        }

        public Criteria andEstimatedAmountControlRequiredBetween(Integer value1, Integer value2) {
            addCriterion("estimated_amount_control_required between", value1, value2, "estimatedAmountControlRequired");
            return (Criteria) this;
        }

        public Criteria andEstimatedAmountControlRequiredNotBetween(Integer value1, Integer value2) {
            addCriterion("estimated_amount_control_required not between", value1, value2, "estimatedAmountControlRequired");
            return (Criteria) this;
        }

        public Criteria andTravelerRangeTypeIsNull() {
            addCriterion("traveler_range_type is null");
            return (Criteria) this;
        }

        public Criteria andTravelerRangeTypeIsNotNull() {
            addCriterion("traveler_range_type is not null");
            return (Criteria) this;
        }

        public Criteria andTravelerRangeTypeEqualTo(Integer value) {
            addCriterion("traveler_range_type =", value, "travelerRangeType");
            return (Criteria) this;
        }

        public Criteria andTravelerRangeTypeNotEqualTo(Integer value) {
            addCriterion("traveler_range_type <>", value, "travelerRangeType");
            return (Criteria) this;
        }

        public Criteria andTravelerRangeTypeGreaterThan(Integer value) {
            addCriterion("traveler_range_type >", value, "travelerRangeType");
            return (Criteria) this;
        }

        public Criteria andTravelerRangeTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("traveler_range_type >=", value, "travelerRangeType");
            return (Criteria) this;
        }

        public Criteria andTravelerRangeTypeLessThan(Integer value) {
            addCriterion("traveler_range_type <", value, "travelerRangeType");
            return (Criteria) this;
        }

        public Criteria andTravelerRangeTypeLessThanOrEqualTo(Integer value) {
            addCriterion("traveler_range_type <=", value, "travelerRangeType");
            return (Criteria) this;
        }

        public Criteria andTravelerRangeTypeIn(List<Integer> values) {
            addCriterion("traveler_range_type in", values, "travelerRangeType");
            return (Criteria) this;
        }

        public Criteria andTravelerRangeTypeNotIn(List<Integer> values) {
            addCriterion("traveler_range_type not in", values, "travelerRangeType");
            return (Criteria) this;
        }

        public Criteria andTravelerRangeTypeBetween(Integer value1, Integer value2) {
            addCriterion("traveler_range_type between", value1, value2, "travelerRangeType");
            return (Criteria) this;
        }

        public Criteria andTravelerRangeTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("traveler_range_type not between", value1, value2, "travelerRangeType");
            return (Criteria) this;
        }

        public Criteria andTravelerEmployeeRangeIsNull() {
            addCriterion("traveler_employee_range is null");
            return (Criteria) this;
        }

        public Criteria andTravelerEmployeeRangeIsNotNull() {
            addCriterion("traveler_employee_range is not null");
            return (Criteria) this;
        }

        public Criteria andTravelerEmployeeRangeEqualTo(Integer value) {
            addCriterion("traveler_employee_range =", value, "travelerEmployeeRange");
            return (Criteria) this;
        }

        public Criteria andTravelerEmployeeRangeNotEqualTo(Integer value) {
            addCriterion("traveler_employee_range <>", value, "travelerEmployeeRange");
            return (Criteria) this;
        }

        public Criteria andTravelerEmployeeRangeGreaterThan(Integer value) {
            addCriterion("traveler_employee_range >", value, "travelerEmployeeRange");
            return (Criteria) this;
        }

        public Criteria andTravelerEmployeeRangeGreaterThanOrEqualTo(Integer value) {
            addCriterion("traveler_employee_range >=", value, "travelerEmployeeRange");
            return (Criteria) this;
        }

        public Criteria andTravelerEmployeeRangeLessThan(Integer value) {
            addCriterion("traveler_employee_range <", value, "travelerEmployeeRange");
            return (Criteria) this;
        }

        public Criteria andTravelerEmployeeRangeLessThanOrEqualTo(Integer value) {
            addCriterion("traveler_employee_range <=", value, "travelerEmployeeRange");
            return (Criteria) this;
        }

        public Criteria andTravelerEmployeeRangeIn(List<Integer> values) {
            addCriterion("traveler_employee_range in", values, "travelerEmployeeRange");
            return (Criteria) this;
        }

        public Criteria andTravelerEmployeeRangeNotIn(List<Integer> values) {
            addCriterion("traveler_employee_range not in", values, "travelerEmployeeRange");
            return (Criteria) this;
        }

        public Criteria andTravelerEmployeeRangeBetween(Integer value1, Integer value2) {
            addCriterion("traveler_employee_range between", value1, value2, "travelerEmployeeRange");
            return (Criteria) this;
        }

        public Criteria andTravelerEmployeeRangeNotBetween(Integer value1, Integer value2) {
            addCriterion("traveler_employee_range not between", value1, value2, "travelerEmployeeRange");
            return (Criteria) this;
        }

        public Criteria andTravelerOtherEmployeeRangeIsNull() {
            addCriterion("traveler_other_employee_range is null");
            return (Criteria) this;
        }

        public Criteria andTravelerOtherEmployeeRangeIsNotNull() {
            addCriterion("traveler_other_employee_range is not null");
            return (Criteria) this;
        }

        public Criteria andTravelerOtherEmployeeRangeEqualTo(Integer value) {
            addCriterion("traveler_other_employee_range =", value, "travelerOtherEmployeeRange");
            return (Criteria) this;
        }

        public Criteria andTravelerOtherEmployeeRangeNotEqualTo(Integer value) {
            addCriterion("traveler_other_employee_range <>", value, "travelerOtherEmployeeRange");
            return (Criteria) this;
        }

        public Criteria andTravelerOtherEmployeeRangeGreaterThan(Integer value) {
            addCriterion("traveler_other_employee_range >", value, "travelerOtherEmployeeRange");
            return (Criteria) this;
        }

        public Criteria andTravelerOtherEmployeeRangeGreaterThanOrEqualTo(Integer value) {
            addCriterion("traveler_other_employee_range >=", value, "travelerOtherEmployeeRange");
            return (Criteria) this;
        }

        public Criteria andTravelerOtherEmployeeRangeLessThan(Integer value) {
            addCriterion("traveler_other_employee_range <", value, "travelerOtherEmployeeRange");
            return (Criteria) this;
        }

        public Criteria andTravelerOtherEmployeeRangeLessThanOrEqualTo(Integer value) {
            addCriterion("traveler_other_employee_range <=", value, "travelerOtherEmployeeRange");
            return (Criteria) this;
        }

        public Criteria andTravelerOtherEmployeeRangeIn(List<Integer> values) {
            addCriterion("traveler_other_employee_range in", values, "travelerOtherEmployeeRange");
            return (Criteria) this;
        }

        public Criteria andTravelerOtherEmployeeRangeNotIn(List<Integer> values) {
            addCriterion("traveler_other_employee_range not in", values, "travelerOtherEmployeeRange");
            return (Criteria) this;
        }

        public Criteria andTravelerOtherEmployeeRangeBetween(Integer value1, Integer value2) {
            addCriterion("traveler_other_employee_range between", value1, value2, "travelerOtherEmployeeRange");
            return (Criteria) this;
        }

        public Criteria andTravelerOtherEmployeeRangeNotBetween(Integer value1, Integer value2) {
            addCriterion("traveler_other_employee_range not between", value1, value2, "travelerOtherEmployeeRange");
            return (Criteria) this;
        }

        public Criteria andTravelerAllowGroupAllIsNull() {
            addCriterion("traveler_allow_group_all is null");
            return (Criteria) this;
        }

        public Criteria andTravelerAllowGroupAllIsNotNull() {
            addCriterion("traveler_allow_group_all is not null");
            return (Criteria) this;
        }

        public Criteria andTravelerAllowGroupAllEqualTo(Integer value) {
            addCriterion("traveler_allow_group_all =", value, "travelerAllowGroupAll");
            return (Criteria) this;
        }

        public Criteria andTravelerAllowGroupAllNotEqualTo(Integer value) {
            addCriterion("traveler_allow_group_all <>", value, "travelerAllowGroupAll");
            return (Criteria) this;
        }

        public Criteria andTravelerAllowGroupAllGreaterThan(Integer value) {
            addCriterion("traveler_allow_group_all >", value, "travelerAllowGroupAll");
            return (Criteria) this;
        }

        public Criteria andTravelerAllowGroupAllGreaterThanOrEqualTo(Integer value) {
            addCriterion("traveler_allow_group_all >=", value, "travelerAllowGroupAll");
            return (Criteria) this;
        }

        public Criteria andTravelerAllowGroupAllLessThan(Integer value) {
            addCriterion("traveler_allow_group_all <", value, "travelerAllowGroupAll");
            return (Criteria) this;
        }

        public Criteria andTravelerAllowGroupAllLessThanOrEqualTo(Integer value) {
            addCriterion("traveler_allow_group_all <=", value, "travelerAllowGroupAll");
            return (Criteria) this;
        }

        public Criteria andTravelerAllowGroupAllIn(List<Integer> values) {
            addCriterion("traveler_allow_group_all in", values, "travelerAllowGroupAll");
            return (Criteria) this;
        }

        public Criteria andTravelerAllowGroupAllNotIn(List<Integer> values) {
            addCriterion("traveler_allow_group_all not in", values, "travelerAllowGroupAll");
            return (Criteria) this;
        }

        public Criteria andTravelerAllowGroupAllBetween(Integer value1, Integer value2) {
            addCriterion("traveler_allow_group_all between", value1, value2, "travelerAllowGroupAll");
            return (Criteria) this;
        }

        public Criteria andTravelerAllowGroupAllNotBetween(Integer value1, Integer value2) {
            addCriterion("traveler_allow_group_all not between", value1, value2, "travelerAllowGroupAll");
            return (Criteria) this;
        }

        public Criteria andTravelerRelationListIsNull() {
            addCriterion("traveler_relation_list is null");
            return (Criteria) this;
        }

        public Criteria andTravelerRelationListIsNotNull() {
            addCriterion("traveler_relation_list is not null");
            return (Criteria) this;
        }

        public Criteria andTravelerRelationListEqualTo(String value) {
            addCriterion("traveler_relation_list =", value, "travelerRelationList");
            return (Criteria) this;
        }

        public Criteria andTravelerRelationListNotEqualTo(String value) {
            addCriterion("traveler_relation_list <>", value, "travelerRelationList");
            return (Criteria) this;
        }

        public Criteria andTravelerRelationListGreaterThan(String value) {
            addCriterion("traveler_relation_list >", value, "travelerRelationList");
            return (Criteria) this;
        }

        public Criteria andTravelerRelationListGreaterThanOrEqualTo(String value) {
            addCriterion("traveler_relation_list >=", value, "travelerRelationList");
            return (Criteria) this;
        }

        public Criteria andTravelerRelationListLessThan(String value) {
            addCriterion("traveler_relation_list <", value, "travelerRelationList");
            return (Criteria) this;
        }

        public Criteria andTravelerRelationListLessThanOrEqualTo(String value) {
            addCriterion("traveler_relation_list <=", value, "travelerRelationList");
            return (Criteria) this;
        }

        public Criteria andTravelerRelationListLike(String value) {
            addCriterion("traveler_relation_list like", value, "travelerRelationList");
            return (Criteria) this;
        }

        public Criteria andTravelerRelationListNotLike(String value) {
            addCriterion("traveler_relation_list not like", value, "travelerRelationList");
            return (Criteria) this;
        }

        public Criteria andTravelerRelationListIn(List<String> values) {
            addCriterion("traveler_relation_list in", values, "travelerRelationList");
            return (Criteria) this;
        }

        public Criteria andTravelerRelationListNotIn(List<String> values) {
            addCriterion("traveler_relation_list not in", values, "travelerRelationList");
            return (Criteria) this;
        }

        public Criteria andTravelerRelationListBetween(String value1, String value2) {
            addCriterion("traveler_relation_list between", value1, value2, "travelerRelationList");
            return (Criteria) this;
        }

        public Criteria andTravelerRelationListNotBetween(String value1, String value2) {
            addCriterion("traveler_relation_list not between", value1, value2, "travelerRelationList");
            return (Criteria) this;
        }

        public Criteria andApplyReasonIsEditIsNull() {
            addCriterion("apply_reason_is_edit is null");
            return (Criteria) this;
        }

        public Criteria andApplyReasonIsEditIsNotNull() {
            addCriterion("apply_reason_is_edit is not null");
            return (Criteria) this;
        }

        public Criteria andApplyReasonIsEditEqualTo(Integer value) {
            addCriterion("apply_reason_is_edit =", value, "applyReasonIsEdit");
            return (Criteria) this;
        }

        public Criteria andApplyReasonIsEditNotEqualTo(Integer value) {
            addCriterion("apply_reason_is_edit <>", value, "applyReasonIsEdit");
            return (Criteria) this;
        }

        public Criteria andApplyReasonIsEditGreaterThan(Integer value) {
            addCriterion("apply_reason_is_edit >", value, "applyReasonIsEdit");
            return (Criteria) this;
        }

        public Criteria andApplyReasonIsEditGreaterThanOrEqualTo(Integer value) {
            addCriterion("apply_reason_is_edit >=", value, "applyReasonIsEdit");
            return (Criteria) this;
        }

        public Criteria andApplyReasonIsEditLessThan(Integer value) {
            addCriterion("apply_reason_is_edit <", value, "applyReasonIsEdit");
            return (Criteria) this;
        }

        public Criteria andApplyReasonIsEditLessThanOrEqualTo(Integer value) {
            addCriterion("apply_reason_is_edit <=", value, "applyReasonIsEdit");
            return (Criteria) this;
        }

        public Criteria andApplyReasonIsEditIn(List<Integer> values) {
            addCriterion("apply_reason_is_edit in", values, "applyReasonIsEdit");
            return (Criteria) this;
        }

        public Criteria andApplyReasonIsEditNotIn(List<Integer> values) {
            addCriterion("apply_reason_is_edit not in", values, "applyReasonIsEdit");
            return (Criteria) this;
        }

        public Criteria andApplyReasonIsEditBetween(Integer value1, Integer value2) {
            addCriterion("apply_reason_is_edit between", value1, value2, "applyReasonIsEdit");
            return (Criteria) this;
        }

        public Criteria andApplyReasonIsEditNotBetween(Integer value1, Integer value2) {
            addCriterion("apply_reason_is_edit not between", value1, value2, "applyReasonIsEdit");
            return (Criteria) this;
        }

        public Criteria andTripAddReasonIsEditIsNull() {
            addCriterion("trip_add_reason_is_edit is null");
            return (Criteria) this;
        }

        public Criteria andTripAddReasonIsEditIsNotNull() {
            addCriterion("trip_add_reason_is_edit is not null");
            return (Criteria) this;
        }

        public Criteria andTripAddReasonIsEditEqualTo(Integer value) {
            addCriterion("trip_add_reason_is_edit =", value, "tripAddReasonIsEdit");
            return (Criteria) this;
        }

        public Criteria andTripAddReasonIsEditNotEqualTo(Integer value) {
            addCriterion("trip_add_reason_is_edit <>", value, "tripAddReasonIsEdit");
            return (Criteria) this;
        }

        public Criteria andTripAddReasonIsEditGreaterThan(Integer value) {
            addCriterion("trip_add_reason_is_edit >", value, "tripAddReasonIsEdit");
            return (Criteria) this;
        }

        public Criteria andTripAddReasonIsEditGreaterThanOrEqualTo(Integer value) {
            addCriterion("trip_add_reason_is_edit >=", value, "tripAddReasonIsEdit");
            return (Criteria) this;
        }

        public Criteria andTripAddReasonIsEditLessThan(Integer value) {
            addCriterion("trip_add_reason_is_edit <", value, "tripAddReasonIsEdit");
            return (Criteria) this;
        }

        public Criteria andTripAddReasonIsEditLessThanOrEqualTo(Integer value) {
            addCriterion("trip_add_reason_is_edit <=", value, "tripAddReasonIsEdit");
            return (Criteria) this;
        }

        public Criteria andTripAddReasonIsEditIn(List<Integer> values) {
            addCriterion("trip_add_reason_is_edit in", values, "tripAddReasonIsEdit");
            return (Criteria) this;
        }

        public Criteria andTripAddReasonIsEditNotIn(List<Integer> values) {
            addCriterion("trip_add_reason_is_edit not in", values, "tripAddReasonIsEdit");
            return (Criteria) this;
        }

        public Criteria andTripAddReasonIsEditBetween(Integer value1, Integer value2) {
            addCriterion("trip_add_reason_is_edit between", value1, value2, "tripAddReasonIsEdit");
            return (Criteria) this;
        }

        public Criteria andTripAddReasonIsEditNotBetween(Integer value1, Integer value2) {
            addCriterion("trip_add_reason_is_edit not between", value1, value2, "tripAddReasonIsEdit");
            return (Criteria) this;
        }

        public Criteria andFloatValIsNull() {
            addCriterion("float_val is null");
            return (Criteria) this;
        }

        public Criteria andFloatValIsNotNull() {
            addCriterion("float_val is not null");
            return (Criteria) this;
        }

        public Criteria andFloatValEqualTo(Integer value) {
            addCriterion("float_val =", value, "floatVal");
            return (Criteria) this;
        }

        public Criteria andFloatValNotEqualTo(Integer value) {
            addCriterion("float_val <>", value, "floatVal");
            return (Criteria) this;
        }

        public Criteria andFloatValGreaterThan(Integer value) {
            addCriterion("float_val >", value, "floatVal");
            return (Criteria) this;
        }

        public Criteria andFloatValGreaterThanOrEqualTo(Integer value) {
            addCriterion("float_val >=", value, "floatVal");
            return (Criteria) this;
        }

        public Criteria andFloatValLessThan(Integer value) {
            addCriterion("float_val <", value, "floatVal");
            return (Criteria) this;
        }

        public Criteria andFloatValLessThanOrEqualTo(Integer value) {
            addCriterion("float_val <=", value, "floatVal");
            return (Criteria) this;
        }

        public Criteria andFloatValIn(List<Integer> values) {
            addCriterion("float_val in", values, "floatVal");
            return (Criteria) this;
        }

        public Criteria andFloatValNotIn(List<Integer> values) {
            addCriterion("float_val not in", values, "floatVal");
            return (Criteria) this;
        }

        public Criteria andFloatValBetween(Integer value1, Integer value2) {
            addCriterion("float_val between", value1, value2, "floatVal");
            return (Criteria) this;
        }

        public Criteria andFloatValNotBetween(Integer value1, Integer value2) {
            addCriterion("float_val not between", value1, value2, "floatVal");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table custom_form_apply_config
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table custom_form_apply_config
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}