package com.fenbeitong.saas.core.contract.messagesettings.inner;

import com.fenbeitong.saas.api.model.dto.user.EmployeeEmailContract;
import com.fenbeitong.saas.core.contract.user.EmployeeInfoContract;
import com.fenbeitong.saas.core.model.saas.CategoryAmount;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by zhangkai on 2017/6/2.
 */
public class LargeOver {

    private LargeOverNotice large_over_notice;
    private List<EmployeeInfoContract> receiver_list = new ArrayList<>();
    private List<String> email_list = new ArrayList<>();
    private Boolean hasUnbind = false;
    /**
     *  新版邮箱绑定人员列表
     **/
    private List<EmployeeEmailContract>  emails = new ArrayList<>();
    public LargeOver() {
    }

    public LargeOver(LargeOverNotice large_over_notice) {
        this.large_over_notice = large_over_notice;
    }

    public LargeOver(LargeOverNotice large_over_notice, List<String> email_list, List<EmployeeInfoContract> receiver_list) {
        this.large_over_notice = large_over_notice;
        this.email_list = email_list;
        this.receiver_list = receiver_list;
    }

    public LargeOverNotice getLarge_over_notice() {
        return large_over_notice;
    }

    public void setLarge_over_notice(LargeOverNotice large_over_notice) {
        this.large_over_notice = large_over_notice;
    }

    public List<EmployeeInfoContract> getReceiver_list() {
        return receiver_list;
    }

    public void setReceiver_list(List<EmployeeInfoContract> receiver_list) {
        this.receiver_list = receiver_list;
    }

    public List<String> getEmail_list() {
        return email_list;
    }

    public void setEmail_list(List<String> email_list) {
        this.email_list = email_list;
    }

    public Boolean getHasUnbind() {
        return hasUnbind;
    }

    public void setHasUnbind(Boolean hasUnbind) {
        this.hasUnbind = hasUnbind;
    }

    public List<EmployeeEmailContract> getEmails() {
        return emails;
    }

    public void setEmails(List<EmployeeEmailContract> emails) {
        this.emails = emails;
    }

    public static class LargeOverNotice {
        Integer is_check;
        //不区分场景时全场景金额限制
        Integer over_amount;
        //是否区分场景类型
        Integer distinguish_category_type;
        //区分场景时，每个场景限制金额信息
        List<CategoryAmount> category_amount_list;

        //区分场景时，每个场景限制金额信息

        //直接上级 前端配置 1：选中直接上级 2：未选中直接上级
        private Integer direct_leader;
        //部门主管 前端配置 1：选中部门主管 2：未选中部门主管
        private Integer depart_leader;

        public LargeOverNotice() {
        }

        public List<CategoryAmount> getCategory_amount_list() {
            return category_amount_list;
        }

        public void setCategory_amount_list(List<CategoryAmount> categoryAmountList) {
            this.category_amount_list = categoryAmountList;
        }

        public Integer getDirect_leader() {
            return direct_leader;
        }

        public void setDirect_leader(Integer direct_leader) {
            this.direct_leader = direct_leader;
        }

        public Integer getDepart_leader() {
            return depart_leader;
        }

        public void setDepart_leader(Integer depart_leader) {
            this.depart_leader = depart_leader;
        }

        public LargeOverNotice(Integer is_check, Integer over_amount, Integer distinguish_category_type) {
            this.is_check = is_check;
            this.over_amount = over_amount;
            this.distinguish_category_type = distinguish_category_type;
        }

        public Integer getDistinguish_category_type() {
            return distinguish_category_type;
        }

        public void setDistinguish_category_type(Integer distinguish_category_type) {
            this.distinguish_category_type = distinguish_category_type;
        }

        public Integer getIs_check() {
            return is_check;
        }

        public void setIs_check(Integer is_check) {
            this.is_check = is_check;
        }

        public Integer getOver_amount() {
            return over_amount;
        }

        public void setOver_amount(Integer over_amount) {
            this.over_amount = over_amount;
        }
    }

}
