package com.fenbeitong.saas.core.contract.organization;

import com.fenbeitong.saas.core.model.enums.user.GenderType;
import com.fenbeitong.saas.core.model.enums.user.UserCert;
import com.fenbeitong.saas.core.model.enums.user.UserRole;
import com.fenbeitong.saas.core.utils.tools.excel.EnumsTrans;
import com.fenbeitong.saas.core.utils.tools.excel.Title;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/5/23.
 */
public class ExcelUserInsertContract {

    @Title(name = "姓名")
    private String name;

    @Title(name = "手机号(不可修改)", type = "string")
    private String phone;

    @Title(name = "部门")
    private String orgUnitIds;

    @EnumsTrans(enumClazz = UserRole.class)
    @Title(name = "角色")
    private Integer role;

    @EnumsTrans(enumClazz = UserCert.class)
    @Title(name = "证件类型")
    private Integer certType;

    @Title(name = "证件号", type = "string")
    private String certNo;

    @EnumsTrans(enumClazz = GenderType.class)
    @Title(name = "性别")
    private Integer gender;

    @Title(name = "生日(例:19820201)", type = "string")
    private String birthDate;

    public ExcelUserInsertContract() {
    }

    public ExcelUserInsertContract(String name, String phone, String orgUnitIds, Integer role, Integer certType, String certNo, Integer gender, String birthDate) {
        this.name = name;
        this.phone = phone;
        this.orgUnitIds = orgUnitIds;
        this.role = role;
        this.certType = certType;
        this.certNo = certNo;
        this.gender = gender;
        this.birthDate = birthDate;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getOrgUnitIds() {
        return orgUnitIds;
    }

    public void setOrgUnitIds(String orgUnitIds) {
        this.orgUnitIds = orgUnitIds;
    }

    public Integer getRole() {
        return role;
    }

    public void setRole(Integer role) {
        this.role = role;
    }

    public Integer getCertType() {
        return certType;
    }

    public void setCertType(Integer certType) {
        this.certType = certType;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public String getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(String birthDate) {
        this.birthDate = birthDate;
    }

    @Override
    public String toString() {
        return "ExcelUserInsertContract{" +
                "name='" + name + '\'' +
                ", phone='" + phone + '\'' +
                ", orgUnitIds='" + orgUnitIds + '\'' +
                ", role=" + role +
                ", certType=" + certType +
                ", certNo='" + certNo + '\'' +
                ", gender=" + gender +
                ", birthDate='" + birthDate + '\'' +
                '}';
    }

}
