package com.fenbeitong.saas.core.service.v5.impl;

import cn.hutool.core.util.ObjectUtil;
import com.fenbeitong.common.utils.json.JsonUtils;
import com.fenbeitong.finhub.common.constant.CategoryTypeEnum;
import com.fenbeitong.finhub.common.utils.VersionUtils;
import com.fenbeitong.finhub.saturn.order.entity.base.SaturnEsOrderDetailsDTO;
import com.fenbeitong.saas.core.common.base.SaasException;
import com.fenbeitong.saas.core.common.constant.GlobalResponseCode;
import com.fenbeitong.saas.core.contract.apply.CostInfoContract;
import com.fenbeitong.saas.core.contract.common.IdNameContract;
import com.fenbeitong.saas.core.contract.harmony.AirportCityContract;
import com.fenbeitong.saas.core.contract.scene.SceneInfo;
import com.fenbeitong.saas.core.dao.dynamic.ApplyTripInfoMapper;
import com.fenbeitong.saas.core.dao.saasplus.ApplyOrderExtendMapper;
import com.fenbeitong.saas.core.dao.saasplus.CustformFormMapper;
import com.fenbeitong.saas.core.dao.saasplus.CustformReimbursementConfigMapper;
import com.fenbeitong.saas.core.language.CoreLanguage;
import com.fenbeitong.saas.core.model.enums.apply.ApplyStatus;
import com.fenbeitong.saas.core.model.enums.apply.ApplyTripStatus;
import com.fenbeitong.saas.core.model.enums.apply.ApplyType;
import com.fenbeitong.saas.core.model.enums.apply.BizType;
import com.fenbeitong.saas.core.model.enums.user.UserRole;
import com.fenbeitong.saas.core.model.saas.*;
import com.fenbeitong.saas.core.model.saasplus.ApplyOrderExtend;
import com.fenbeitong.saas.core.model.saasplus.ApplyOrderExtendExample;
import com.fenbeitong.saas.core.model.saasplus.CustformForm;
import com.fenbeitong.saas.core.model.saasplus.CustformReimbursementConfig;
import com.fenbeitong.saas.core.model.saasplus.CustformReimbursementConfigExample;
import com.fenbeitong.saas.core.model.v5.apply.vo.*;
import com.fenbeitong.saas.core.model.v5.apply.vo.strategy.ApplyStrategyContext;
import com.fenbeitong.saas.core.model.v5.apply.vo.strategy.ApplyTripBuild;
import com.fenbeitong.saas.core.service.IApplyV5Service;
import com.fenbeitong.saas.core.service.apply.ApplySceneInfoService;
import com.fenbeitong.saas.core.service.harmony.HarmonyAirCityService;
import com.fenbeitong.saas.core.service.v5.ApplyV5CenterService;
import com.fenbeitong.saasplus.api.service.setting.ICommonSettingService;
import com.fenbeitong.usercenter.api.model.dto.company.EmployeeSwitchableCompanyDTO;
import com.fenbeitong.usercenter.api.service.group.IGroupEmployeeService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.luastar.swift.base.utils.ObjUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service("applyV5CenterService")
public class ApplyV5CenterServiceImpl implements ApplyV5CenterService {
    @Autowired
    private IApplyV5Service iApplyV5Service;

//    private final static String NO_APPLY = "你当前没有符合要求的申请单，需先提交申请再预订";
//    private final static String APPLYING = "你当前已有申请单正在审批中，通过后才可预定";

    @Autowired
    private ApplyTripInfoMapper applyTripInfoMapper;
    @Autowired
    private HarmonyAirCityService harmonyAirCityService;
    @Autowired
    private ApplySceneInfoService applySceneInfoService;
    @Autowired
    private CustformFormMapper custformFormMapper;
    @Autowired
    private ApplyOrderExtendMapper applyOrderExtendMapper;
    @Autowired
    private ICommonSettingService iCommonSettingService;
    @Autowired
    private IGroupEmployeeService iGroupEmployeeService;
    @Autowired
    private CustformReimbursementConfigMapper custformReimbursementConfigMapper;
    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyTripApplicateExtMapper applyTripApplicateExtMapper;


    @Override
    public Apply4AppHomeVO getRecentOneApply(String token, String userId, String companyId, UserRole userRole, String clientVersion) {
        //入参日志
        log.info("token:{}，userId:{}，companyId:{}，userRole:{}，clientVersion:{}", token, userId, companyId, userRole, clientVersion);

        //创建返回结果集
        Apply4AppHomeVO appHomeVO = new Apply4AppHomeVO();

        //创建待提交,待提交,审批中,已审核,被驳回 状态集合
        List<Integer> applyStatus = Lists.newArrayList(ApplyStatus.Draft.getValue(), ApplyStatus.PendingAudit.getValue(), ApplyStatus.Approved.getValue(), ApplyStatus.Return.getValue());
        //获取首页分组后各审批单数量
        List<ApplyOrderStateGroup> applyGroupDataList = iApplyV5Service.getApplyGroupData(companyId, userId, applyStatus, clientVersion,false);
//        log.info("getRecentOneApply.applyGroupDataList:{}", JsonUtils.toJsonStr(applyGroupDataList));

        appHomeVO.setApplyGroupData(ApplyStatisticsVO.buildStatisticsVOS(applyGroupDataList));
        if (CollectionUtils.isEmpty(applyGroupDataList)) {
            return appHomeVO;
        }
        //判断是否有审批单
        boolean hasApply = false;
        for (ApplyOrderStateGroup applyGroupData : applyGroupDataList) {
            if (applyGroupData != null && applyGroupData.getCount() != null && applyGroupData.getCount() > 0) {
                hasApply = true;
                break;
            }
        }

        //如果没有有效审批单直接返回
        if (!hasApply) {
            return appHomeVO;
        }

        //查询当前用户所有审批单列表
        List<ApplyOrder> applyOrders = iApplyV5Service.applyList(0, 5096, companyId, userId, 1, 1, "", "");
//        log.info("getRecentOneApply.applyOrders:{}", JsonUtils.toJsonStr(applyOrders));
        if (CollectionUtils.isEmpty(applyOrders) || applyOrders.get(0) == null) {
            return appHomeVO;
        }
        //设置最近时间的审批单ID
        ApplyOrder applyOrder = new ApplyOrder();
        applyOrder = applyOrders.get(0);
//        Date date = null;
        //查询所有审批单行程数据
//        for (ApplyOrder apply: applyOrders) {
//            List<ApplyTripInfo> tripInfoList = iApplyV5Service.getApplyTripInfo(apply);
//            for(ApplyTripInfo tripInfo : tripInfoList){
//                if(tripInfo.getEndTime()== null || tripInfo.getState() != ApplyTripStatus.Available.getValue()){
//                    continue;
//                }
//                //格式化日期做比较   去掉时分秒
//                Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("GMT+8"));
//                calendar.setTime(new Date());
//                calendar.set(Calendar.HOUR_OF_DAY,0);
//                calendar.set(Calendar.MINUTE,0);
//                calendar.set(Calendar.SECOND,0);
//                calendar.set(Calendar.MILLISECOND, 0);
//
//                //格式化日期做比较   去掉时分秒
//                Calendar calendar1 = Calendar.getInstance(TimeZone.getTimeZone("GMT+8"));
//                calendar1.setTime(tripInfo.getEndTime());
//                calendar1.set(Calendar.HOUR_OF_DAY,0);
//                calendar1.set(Calendar.MINUTE,0);
//                calendar1.set(Calendar.SECOND,0);
//                calendar1.set(Calendar.MILLISECOND, 0);
//
//                //当天 < 出发日期   然后取出发时间最早的行程
//                log.info("calendar.getTime:{},tripInfo.getEndTime:{}", JsonUtils.toJsonStr(calendar.getTime()),calendar1.getTime());
//                if(calendar.getTime().before(calendar1.getTime())|| calendar.getTime().equals(calendar1.getTime())){
//                    log.info("满足行程：{}" ,tripInfo.getId());
//                    if(date == null){
//                        date = tripInfo.getStartTime();
//                        applyOrder = apply;
//                    }else{
//                        //判断时间 < 行程出发时间
//                        if(tripInfo.getStartTime().before(date)){
//                            date = tripInfo.getStartTime();
//                            applyOrder = apply;
//                        }
//                    }
//                }
//            }
//        }

        List<ApplyTripInfo> tripInfoList = iApplyV5Service.getApplyTripInfo(applyOrder);
//        log.info("getRecentOneApply.tripInfoList:{}", JsonUtils.toJsonStr(tripInfoList));
        //设置出发城市名称
        addCity2TripInfo(tripInfoList);
//        log.info("getRecentOneApply.tripInfoList:{}", JsonUtils.toJsonStr(tripInfoList));


        List<SaturnEsOrderDetailsDTO> orderList = null;
        Integer type = applyOrder.getType();
        Integer state = applyOrder.getState();
        if (type == null) {
            return appHomeVO;
        }

        List<SaturnEsOrderDetailsDTO> distinctOrderList = Lists.newArrayList();
        //如果是差旅行程单  并且是已审核 去查询订单信息
        if (type == ApplyType.ChaiLv.getValue() && state != null && ApplyStatus.Approved.getValue() == state) {
            orderList = iApplyV5Service.getOrderList(applyOrder.getId());
//            log.info("getRecentOneApply.orderList:{}", JsonUtils.toJsonStr(orderList));
            // 根据 tripApplyId 分组，转为 map
            HashMap<String, List<SaturnEsOrderDetailsDTO>> map = orderList.stream()
                    .collect(Collectors.groupingBy(SaturnEsOrderDetailsDTO::getTripApplyId, HashMap::new, Collectors.toList()));
            // 每个 tripApplyId 相同的分组根据时间排序，取第一个
            map.forEach((k, v) -> distinctOrderList.add(v.stream()
                    .sorted(Comparator.comparing(SaturnEsOrderDetailsDTO::getCreateTime).reversed())
                    .collect(Collectors.toList())
                    .get(0)));
        }

//        log.info("getRecentOneApply.distinctOrderList:{}", JsonUtils.toJsonStr(distinctOrderList));
//        String formName = fetchCustomFormName(applyOrder);
        //组装订单,行程,审批单
        ApplyV5DetailVO applyV5DetailVO = new ApplyV5DetailVO(applyOrder, tripInfoList, distinctOrderList, false);

        // 补充用餐外卖场景信息
        SceneInfo sceneInfo = applySceneInfoService.getSceneInfo(companyId, applyOrder);
        applyV5DetailVO.setScene_id(sceneInfo.getSceneId());
        applyV5DetailVO.setScene_name(sceneInfo.getSceneName());
        applyV5DetailVO.setView_name(sceneInfo.getViewName());

        List<ApplyV5TripVO> tripList = applyV5DetailVO.getTripList();
        addCityICAOCode2TripInfo(token, tripList, clientVersion);
//        log.info("getRecentOneApply.tripList:{}", JsonUtils.toJsonStr(tripList));
        applyV5DetailVO.setTripList(tripList);
        //外卖需要给前端传费用归属相关信息
        if (type == ApplyType.TakeAway.getValue()) {
            int bringIn = iApplyV5Service.getBringIn(companyId, type);
//            log.info("getRecentOneApply.bringIn:{}", JsonUtils.toJsonStr(bringIn));
            applyV5DetailVO.setBringIn(bringIn);
            if (bringIn == 1) {
                CostInfoContract costAttribution = iApplyV5Service.getApplyCostAttribution(applyOrder.getId(), companyId);
//                log.info("getRecentOneApply.costAttribution:{}", JsonUtils.toJsonStr(costAttribution));
                applyV5DetailVO.setCostInfoContract(new CostInfoContractVO(costAttribution));
            }
        }

        // 自定义申请单 标题处理
        if (applyOrder.getType() == ApplyType.CustomFromBeforehand.getValue()){
            //查询标题数据
            CustformForm custformForm = custformFormMapper.selectByPrimaryKey(applyOrder.getFormId());
            applyV5DetailVO.setMainTitle(custformForm.getFormName());
        }
        // 设置跨主体消费权限
        Boolean openConsumer = iCommonSettingService.openCrossEnterpriseConsumption(companyId);
        applyV5DetailVO.setOpen_consumer(openConsumer);
        if(null == applyV5DetailVO.getIssue()){
            applyV5DetailVO.setIssue(0);
        }
        // 自定义申请单，消费主体
        if (applyOrder.getType() == ApplyType.CustomFromBeforehand.getValue()){
            ApplyOrderExtendExample example = new ApplyOrderExtendExample();
            example.createCriteria().andApplyOrderIdEqualTo(applyOrder.getId());
            List<ApplyOrderExtend> applyOrderExtendList = applyOrderExtendMapper.selectByExample(example);
            if(CollectionUtils.isNotEmpty(applyOrderExtendList)){
                applyV5DetailVO.setConsumer_company_id(applyOrderExtendList.get(0).getConsumeCompanyId());
                applyV5DetailVO.setConsumer_company_name(applyOrderExtendList.get(0).getConsumeCompanyName());
                if (openConsumer) {
                    if (companyId.equals(applyOrderExtendList.get(0).getConsumeCompanyId())){
                        applyV5DetailVO.setConsumer_flag(true);
                    } else {
                        List<EmployeeSwitchableCompanyDTO> employeeSwitchableCompanyDTOList =
                                iGroupEmployeeService.listEmployeeSwitchableCompanies(userId, null);
                        if(CollectionUtils.isNotEmpty(employeeSwitchableCompanyDTOList)){
                            for(EmployeeSwitchableCompanyDTO switchableCompanyDTO : employeeSwitchableCompanyDTOList){
                                if(switchableCompanyDTO.getCompanyId().equals(applyOrderExtendList.get(0).getConsumeCompanyId())){
                                    applyV5DetailVO.setConsumer_flag(true);
                                    break;
                                }
                            }
                        }
                    }
                }
            }
            String formId = applyOrder.getFormId();
            if(StringUtils.isNoneBlank(formId)){
                CustformForm custformForm = custformFormMapper.selectByPrimaryKey(formId);
                if(null != custformForm && Objects.equals(custformForm.getGroupType(), 2)){
                    applyV5DetailVO.setIssue(1);
                }
            }
        }

        checkTakeawayAddressVersion(applyV5DetailVO,clientVersion);

        appHomeVO.setApplyDetail(applyV5DetailVO);
        return appHomeVO;
    }

    /**
     * 自定义申请单如果有外卖申请项强升
     * 外卖申请单并且选择的是全场景需要强升
     * @param applyV5DetailVO
     * @param clientVersion
     */
    private void checkTakeawayAddressVersion(ApplyV5DetailVO applyV5DetailVO, String clientVersion) {
        if(VersionUtils.lessThan(clientVersion,"5.2.2")){
            if(Objects.equals(applyV5DetailVO.getType(),ApplyType.CustomFromBeforehand.getValue())) {
                List<ApplyV5TripVO> tripList = applyV5DetailVO.getTripList();
                if (CollectionUtils.isNotEmpty(tripList)) {
                    for (ApplyV5TripVO item : tripList) {
                        if (Objects.equals(item.getType(), BizType.Takeaway.getValue())) {
                            throw new SaasException(GlobalResponseCode.ApplyVersoinERROR);
                        }
                    }
                }
            }else if(Objects.equals(applyV5DetailVO.getType(),ApplyType.TakeAway.getValue())){
                if(Objects.equals(applyV5DetailVO.getScene_id(),-1)){
                    throw new SaasException(GlobalResponseCode.ApplyVersoinERROR);
                }
            }
        }
    }


    @Override
    public List<ApplyV5DetailVO> queryPersonalApplyList(String token, String applyId, String userId, String companyId, UserRole userRole, String clientVersion) {
        return null;
    }

    @Override
    public Apply4BizHomeVO getRecentTrip4Biz(String token, String userId, String companyId, UserRole userRole, String clientVersion, Integer bizType) {
        //log.info("token:{}，userId:{}，companyId:{}，userRole:{}，clientVersion:{}，bizType:{}", token, userId, companyId, userRole, clientVersion, bizType);
        //差旅行程模式
        Apply4BizHomeVO homeVO = new Apply4BizHomeVO();
        Boolean employeeVerifyFlag = iApplyV5Service.getEmployeeVerifyFlag(userId, companyId, bizType);
        homeVO.setNeedApply(employeeVerifyFlag);
        //有权限无需审批
        if (!employeeVerifyFlag) {
            return homeVO;
        }
        //获取所有申请单
        List<ApplyOrder> applyOrders = iApplyV5Service.applyList(0, 4, companyId, userId, 1, Integer.MAX_VALUE, token, clientVersion);
        if (CollectionUtils.isEmpty(applyOrders)) {
            homeVO.setMsg(CoreLanguage.ApplyV5CenterServiceImpl_Value_NoApply.getMessage());
            return homeVO;
        }

        List<String> applyIds = applyOrders.stream().filter(Objects::nonNull).map(ApplyOrder::getId).collect(Collectors.toList());
        //无可用审批单
        if (CollectionUtils.isEmpty(applyIds)) {
            homeVO.setMsg(CoreLanguage.ApplyV5CenterServiceImpl_Value_NoApply.getMessage());
            return homeVO;
        }

        //最终行程模式行程单
        List<ApplyTripInfo> tripList = Lists.newArrayList();

        //非行程模式行程单
        List<ApplyTripInfo> tripFList = Lists.newArrayList();
        //行程模式行程单
        List<ApplyTripInfo> tripCList = Lists.newArrayList();

        ApplyTripInfoExample example = new ApplyTripInfoExample();
        ApplyTripInfoExample.Criteria criteria = example.createCriteria();
        criteria.andApplyOrderIdIn(applyIds);
        example.setOrderByClause("start_time asc");
        //获取当前申请单所有行程
        List<ApplyTripInfo> applyTripInfos = applyTripInfoMapper.selectByExample(example);
        //log.info("applyTripInfos:{}", JsonUtils.toJsonStr(applyTripInfos));

        if (ObjUtils.isEmpty(applyTripInfos)) {
            homeVO.setMsg(CoreLanguage.ApplyV5CenterServiceImpl_Value_NoApply.getMessage());
            return homeVO;
        }

        for (ApplyTripInfo tripInfo : applyTripInfos) {
            if (tripInfo.getType() == null) {
                continue;
            }
            if (tripInfo.getType() == BizType.MULTI_TRIP.getValue()) {
                tripFList.addAll(getMultiTripList(bizType, tripInfo));
            }
            if (Objects.equals(bizType, BizType.Hotel.getValue())) {
                if(Objects.equals(tripInfo.getType(), BizType.Hotel.getValue()) ||
                        Objects.equals(tripInfo.getType(), BizType.IntlHotel.getValue())) {
                    tripCList.add(tripInfo);
                }
            } else {
                if (tripInfo.getType().equals(bizType)) {
                    tripCList.add(tripInfo);
                }
            }
        }

        //log.info("tripCList:{}", JsonUtils.toJsonStr(tripCList));
        //log.info("tripFList:{}", JsonUtils.toJsonStr(tripFList));

        //无可用行程单
        if (CollectionUtils.isEmpty(tripCList) && CollectionUtils.isEmpty(tripFList)) {
            homeVO.setMsg(CoreLanguage.ApplyV5CenterServiceImpl_Value_NoApply.getMessage());
            return homeVO;
        }
        //查询所有审批单行程数据
        for (ApplyTripInfo tripInfo : tripCList) {
            if (tripInfo.getEndTime() == null || tripInfo.getState() != ApplyTripStatus.Available.getValue()) {
                continue;
            }
            Date day = new Date();
            //当天 < 出发日期   然后取出发时间最早的行程
            //log.info("calendar.getTime:{},tripInfo.getEndTime:{}", JsonUtils.toJsonStr(day.getTime()), JsonUtils.toJsonStr(tripInfo.getEndTime().getTime()));
            if (day.before(tripInfo.getEndTime())) {
                tripList.add(tripInfo);
            }
        }


        if (CollectionUtils.isNotEmpty(tripList)) {
            addCity2TripInfo(tripList);
            List<ApplyV5TripVO> tripVOList = Lists.newArrayList();
            Map<String, ApplyOrder> applyOrderMap = Maps.newHashMap();
            applyOrders.forEach(e -> applyOrderMap.put(e.getId(), e));
            for (ApplyTripInfo tripInfo : tripList) {
                ApplyTripBuild applyTripBuild = ApplyStrategyContext.getApplyTripBuild(bizType);
                ApplyV5TripVO tripVO = applyTripBuild.buildTripVO(tripInfo, null);
                ApplyOrder applyOrder = applyOrderMap.get(tripInfo.getApplyOrderId());
                if (applyOrder != null) {
                    tripVO.setCostAttributionId(applyOrder.getCostAttributionId());
                    tripVO.setCostAttributionName(applyOrder.getCostAttributionName());
                }
                tripVOList.add(tripVO);
            }
            addCityICAOCode2TripInfo(token, tripVOList, clientVersion);
            homeVO.setTripList(tripVOList);
        }
        return homeVO;
    }

    @Override
    public ApplyV6AppHomeVO getRecentApplyV6(String token, String userId, String companyId, UserRole userRole, String clientVersion) {
        //log.info("[ getRecentApplyV6 ] token:{}，userId:{}，companyId:{}，userRole:{}，clientVersion:{}", token, userId, companyId, userRole, clientVersion);
        //创建返回结果集
        ApplyV6AppHomeVO v6AppHomeVO = new ApplyV6AppHomeVO();

        //创建待提交,待提交,审批中,已审核,被驳回 状态集合
        List<Integer> applyStatus = Lists.newArrayList(ApplyStatus.Draft.getValue(), ApplyStatus.PendingAudit.getValue(), ApplyStatus.Approved.getValue());
        //获取首页分组后各审批单数量
        List<ApplyOrderStateGroup> applyGroupDataList = iApplyV5Service.getApplyGroupData(companyId, userId, applyStatus, clientVersion, true);
//        log.info("getRecentOneApply.applyGroupDataList:{}", JsonUtils.toJsonStr(applyGroupDataList));

        v6AppHomeVO.setApplyGroupData(ApplyV6StatisticsVO.buildStatisticsVOS(applyGroupDataList));
        log.info("分组情况:{},userId:{}",JsonUtils.toJsonStr(v6AppHomeVO),userId);
        if (CollectionUtils.isEmpty(applyGroupDataList)) {
            return v6AppHomeVO;
        }
        //判断是否有审批单
        boolean hasApply = false;
        for (ApplyOrderStateGroup applyGroupData : applyGroupDataList) {
            if (applyGroupData != null && applyGroupData.getCount() != null && applyGroupData.getCount() > 0) {
                hasApply = true;
                break;
            }
        }
        //如果没有有效审批单直接返回
        if (!hasApply) {
            return v6AppHomeVO;
        }

        ApplyV6StatisticsVO draftVO = null;
        ApplyV6StatisticsVO pendingAuditVO = null;
        ApplyV6StatisticsVO approvedVO = null;

        for (ApplyV6StatisticsVO applyGroupDatum : v6AppHomeVO.getApplyGroupData()) {
            if (Objects.equals(ApplyStatus.Draft.getValue(), applyGroupDatum.getState())) {
                draftVO = applyGroupDatum;
            } else if (Objects.equals(ApplyStatus.PendingAudit.getValue(), applyGroupDatum.getState())) {
                pendingAuditVO = applyGroupDatum;
            } else if (Objects.equals(ApplyStatus.Approved.getValue(), applyGroupDatum.getState())) {
                approvedVO = applyGroupDatum;
            }
        }

        // 处理 6.0 待提交列表
        if (draftVO !=null){
            // 组装待提交数据
            paddingV6DetailByApplyState(ApplyStatus.Draft, draftVO, companyId, userId, token, clientVersion);
        }

        // 处理 6.0 审批中列表
        if (pendingAuditVO !=null){
            // 组装审批中数据
            paddingV6DetailByApplyState(ApplyStatus.PendingAudit, pendingAuditVO, companyId, userId, token, clientVersion);
        }

        // 处理 6.0 已同意列表
        if (approvedVO !=null){
            // 组装已同意数据
            paddingV6DetailByApplyState(ApplyStatus.Approved, approvedVO, companyId, userId, token, clientVersion);
        }

        return v6AppHomeVO;
    }

    private void addCity2TripInfo(List<ApplyTripInfo> tripInfoList) {
        if (ObjUtils.isEmpty(tripInfoList)) {
            return;
        }
        Map<String, String> cityMap = getCityMap(tripInfoList);
        tripInfoList.forEach(tripInfo -> {
            if (ObjUtils.isBlank(tripInfo.getStartCityName())) {
                if (ObjUtils.isNotEmpty(tripInfo.getStartCityId())) {
                    List<String> startCiytName = new ArrayList<>();
                    String[] startcitys = tripInfo.getStartCityId().split(",");
                    for (String startcity : startcitys) {
                        startCiytName.add(cityMap.get(startcity));
                    }
                    tripInfo.setStartCityName(String.join(",", startCiytName));
                }
            }
            if (ObjUtils.isBlank(tripInfo.getArrivalCityName())) {
                tripInfo.setArrivalCityName(cityMap.get(tripInfo.getArrivalCityId()));
                if (ObjUtils.isNotEmpty(tripInfo.getArrivalCityId())) {
                    List<String> endCiytName = new ArrayList<>();
                    String[] endcitys = tripInfo.getArrivalCityId().split(",");
                    for (String endcity : endcitys) {
                        endCiytName.add(cityMap.get(endcity));
                    }
                    tripInfo.setArrivalCityName(String.join(",", endCiytName));
                }
            }
        });
    }

    private List<ApplyTripInfo> getMultiTripList(Integer categoryType, ApplyTripInfo tripInfo) {
        List<ApplyTripInfo> multiTripList = Lists.newArrayList();
        //获取场景ids 把不同场景拆分成多个行程
        String[] contentIds = tripInfo.getContent().split(",");
        for (String content : contentIds) {
            ApplyTripInfo trip = new ApplyTripInfo();
            BeanUtils.copyProperties(tripInfo, trip);
            trip.setType(Integer.parseInt(content));
            if (content.equals(categoryType.toString())) {
                multiTripList.add(trip);
            }
        }
        return multiTripList;
    }

    private Map<String, String> getCityMap(List<ApplyTripInfo> applyTripInfoList) {
        Set<String> cityIds = Sets.newHashSet();
        for (ApplyTripInfo tripInfo : applyTripInfoList) {
            if (ObjUtils.isNotEmpty(tripInfo.getStartCityId())) {
                String[] startcitys = tripInfo.getStartCityId().split(",");
                for (String startcity : startcitys) {
                    cityIds.add(startcity);
                }
            }
            if (ObjUtils.isNotEmpty(tripInfo.getArrivalCityId())) {
                String[] endcitys = tripInfo.getArrivalCityId().split(",");
                for (String endcity : endcitys) {
                    cityIds.add(endcity);
                }
            }
        }

        Map<String, String> cityMap = Maps.newHashMap();
        try {
            List<IdNameContract> cityNamesByIds = iApplyV5Service.getCityNamesByIds(new ArrayList<>(cityIds));
            if (ObjUtils.isNotEmpty(cityNamesByIds)) {
                cityNamesByIds.forEach(e -> {
                    cityMap.put(e.getId(), e.getName());
                });
            }
        } catch (Exception e) {
            log.error("查询城市信息异常", e);
        }
        return cityMap;
    }

    private void addCityICAOCode2TripInfo(String token, List<ApplyV5TripVO> tripVOList, String clientVersion) {
        if (ObjUtils.isEmpty(tripVOList)) {
            return;
        }
        List<String> cityIds = Lists.newArrayList();
        for (ApplyV5TripVO tripVO : tripVOList) {
            if (tripVO == null || tripVO.getType() == null) {
                continue;
            }
            TravelNodeVO startNode = tripVO.getStartNode();
            TravelNodeVO arrivalNode = tripVO.getArrivalNode();
            String startCityName = startNode == null ? null : startNode.getFbCityName();
            String arrivalCityName = arrivalNode == null ? null : arrivalNode.getFbCityName();
            if (tripVO.getType() == BizType.Air.getValue() || tripVO.getType() == BizType.IntlAir.getValue()) {
                String startCityId = startNode == null ? "" : ObjUtils.toString(startNode.getFbCityId(), "");
                String arrivalCityId = arrivalNode == null ? "" : ObjUtils.toString(arrivalNode.getFbCityId(), "");
                KeyValueVO cityName = new KeyValueVO();
                cityName.setName(startCityName);
                cityName.setCode(tripVO.getStartNode().getFbCityId());
                cityIds.addAll(Arrays.stream(startCityId.split(",")).filter(ObjUtils::isNotBlank).collect(Collectors.toList()));
                cityName = new KeyValueVO();
                cityName.setName(arrivalCityName);
                cityName.setCode(tripVO.getArrivalNode().getFbCityId());
                cityIds.addAll(Arrays.stream(arrivalCityId.split(",")).filter(ObjUtils::isNotBlank).collect(Collectors.toList()));
            }
            if ("-1".equals(tripVO.getTripTitle()) || ObjUtils.isEmpty(tripVO.getTripTitle())) {
                tripVO.setTripTitle(BizType.valueOf(tripVO.getType()).getDesc());
            }

        }
        Map<String, AirportCityContract> airportCityMap = harmonyAirCityService.queryAirCityInfo(cityIds);
        for (ApplyV5TripVO tripInfo : tripVOList) {
            if (tripInfo.getType() == null) {
                continue;
            }
            if (tripInfo.getType() == BizType.Air.getValue()) {
                TravelNodeVO startNode = tripInfo.getStartNode();
                AirportCityContract startAirportVO = airportCityMap.get(startNode.getFbCityId());
                if (null != startAirportVO) {
                    startNode.setCityICAOCode(startAirportVO.getCityCode());
                    startNode.setCode(startAirportVO.getCityCode());
                    startNode.setCrCode(startAirportVO.getCrCode());
                    startNode.setCrName(startAirportVO.getCrName());
                    tripInfo.setStartNode(startNode);
                }
                TravelNodeVO arrivalNode = tripInfo.getArrivalNode();
                AirportCityContract arrivalAirportVO = airportCityMap.get(arrivalNode.getFbCityId());
                if (null != arrivalAirportVO) {
                    arrivalNode.setCityICAOCode(arrivalAirportVO.getCityCode());
                    arrivalNode.setCode(arrivalAirportVO.getCityCode());
                    arrivalNode.setCrCode(arrivalAirportVO.getCrCode());
                    arrivalNode.setCrName(arrivalAirportVO.getCrName());
                    tripInfo.setArrivalNode(arrivalNode);
                }
            }
            if (tripInfo.getType() == BizType.IntlAir.getValue()) {
                TravelNodeVO startNode = tripInfo.getStartNode();
                AirportCityContract startAirportVO = airportCityMap.get(startNode.getFbCityId());
                if (startAirportVO != null) {
                    startNode.setCityICAOCode(startAirportVO.getIntlCityCode());
                    startNode.setCode(startAirportVO.getIntlCityCode());
                    startNode.setCrCode(startAirportVO.getCrCode());
                    startNode.setCrName(startAirportVO.getCrName());
                    tripInfo.setStartNode(startNode);
                }
                TravelNodeVO arrivalNode = tripInfo.getArrivalNode();
                AirportCityContract arrivalAirportVO = airportCityMap.get(arrivalNode.getFbCityId());
                if (arrivalAirportVO != null) {
                    arrivalNode.setCityICAOCode(arrivalAirportVO.getIntlCityCode());
                    arrivalNode.setCode(arrivalAirportVO.getIntlCityCode());
                    arrivalNode.setCrCode(arrivalAirportVO.getCrCode());
                    arrivalNode.setCrName(arrivalAirportVO.getCrName());
                    tripInfo.setArrivalNode(arrivalNode);
                }
            }
        }

    }

    /**
     * 查询自定义表单名称
     *
     * @param applyOrder
     * @return 若为自定义表单，返回表单名称；
     * 若为非自定义申请单，返回null
     */
    private String fetchCustomFormName(ApplyOrder applyOrder) {
        if (!Objects.equals(applyOrder.getType(), ApplyType.CustomFromBeforehand.getValue())) {
            CustformForm custformForm = custformFormMapper.selectByPrimaryKey(applyOrder.getFormId());
            if (custformForm != null) {
                return custformForm.getFormName();
            }
        }
        return null;
    }

    /**
     * 按申请单状态填充v6申请单详情
     */
    private void paddingV6DetailByApplyState(ApplyStatus applyStatus, ApplyV6StatisticsVO bePaddingStatisticsVO, String companyId, String userId, String token, String clientVersion) {
        // 处理 6.0 待提交列表
        if (bePaddingStatisticsVO == null) {
            return;
        }
        // 组装待提交数据
        int count = 0;
        //查询当前用户所有审批单列表，指定的状态最多查3个
        List<ApplyOrder> applyOrders = iApplyV5Service.applyList(0, applyStatus.getValue(), companyId, userId, 1, 3, "", "");
//        log.info("[ paddingV6DetailByApplyState ] applyOrders:{}", JsonUtils.toJsonStr(applyOrders));
        if (org.springframework.util.CollectionUtils.isEmpty(applyOrders)) {
            return;
        }
        for (ApplyOrder applyOrder : applyOrders) {
            if (count < 3) {
                if (Objects.equals(applyStatus.getValue(), applyOrder.getState())) {
                    if (bePaddingStatisticsVO.getApplyDetailList() == null) {
                        bePaddingStatisticsVO.setApplyDetailList(new ArrayList<>());
                    }
                    ApplyV5DetailVO v6DetailVO = assembleV6RecentApplyDetail(applyOrder, companyId, userId, token, clientVersion);
                    bePaddingStatisticsVO.getApplyDetailList().add(v6DetailVO);
                    count++;
                }
            }
        }

    }

    /**
     * 组装首页v6最近的申请单详情
     *
     * @param applyOrder
     * @param companyId
     * @param userId
     * @param token
     * @param clientVersion
     * @return
     */
    private ApplyV5DetailVO assembleV6RecentApplyDetail(ApplyOrder applyOrder, String companyId, String userId, String token, String clientVersion) {
        List<ApplyTripInfo> tripInfoList = iApplyV5Service.getApplyTripInfo(applyOrder);
        log.info("assembleV6RecentApplyDetail.tripInfoList:{}", JsonUtils.toJsonStr(tripInfoList));
        //设置出发城市名称
        addCity2TripInfo(tripInfoList);
        log.info("assembleV6RecentApplyDetail.tripInfoList:{}", JsonUtils.toJsonStr(tripInfoList));
        List<SaturnEsOrderDetailsDTO> orderList = null;
        Integer type = applyOrder.getType();
        Integer state = applyOrder.getState();

        List<SaturnEsOrderDetailsDTO> distinctOrderList = Lists.newArrayList();
        //如果是差旅行程单  并且是已审核 去查询订单信息
//        if (type == ApplyType.ChaiLv.getValue() && state != null && ApplyStatus.Approved.getValue() == state) {
//            orderList = iApplyV5Service.getOrderList(applyOrder.getId());
//            log.info("assembleV6RecentApplyDetail.orderList:{}", JsonUtils.toJsonStr(orderList));
//            // 根据 tripApplyId 分组，转为 map
//            HashMap<String, List<SaturnEsOrderDetailsDTO>> map = orderList.stream()
//                    .collect(Collectors.groupingBy(SaturnEsOrderDetailsDTO::getTripApplyId, HashMap::new, Collectors.toList()));
//            // 每个 tripApplyId 相同的分组根据时间排序，取第一个
//            map.forEach((k, v) -> distinctOrderList.add(v.stream()
//                    .sorted(Comparator.comparing(SaturnEsOrderDetailsDTO::getCreateTime).reversed())
//                    .collect(Collectors.toList())
//                    .get(0)));
//        }

        log.info("assembleV6RecentApplyDetail.distinctOrderList:{}", JsonUtils.toJsonStr(distinctOrderList));
        //组装订单,行程,审批单
        ApplyV5DetailVO applyV5DetailVO = new ApplyV5DetailVO(applyOrder, tripInfoList, distinctOrderList, true);





        // 补充用餐外卖场景信息
        SceneInfo sceneInfo = applySceneInfoService.getSceneInfo(companyId, applyOrder);
        applyV5DetailVO.setScene_id(sceneInfo.getSceneId());
        applyV5DetailVO.setScene_name(sceneInfo.getSceneName());
        applyV5DetailVO.setView_name(sceneInfo.getViewName());

//        List<ApplyV5TripVO> tripList = applyV5DetailVO.getTripList();
//        addCityICAOCode2TripInfo(token, tripList, clientVersion);
//        log.info("getRecentOneApply.tripList:{}", JsonUtils.toJsonStr(tripList));
//        applyV5DetailVO.setTripList(tripList);
        //外卖需要给前端传费用归属相关信息
        if (type == ApplyType.TakeAway.getValue()) {
            int bringIn = iApplyV5Service.getBringIn(companyId, type);
            log.info("assembleV6RecentApplyDetail.bringIn:{}", JsonUtils.toJsonStr(bringIn));
            applyV5DetailVO.setBringIn(bringIn);
            if (bringIn == 1) {
                CostInfoContract costAttribution = iApplyV5Service.getApplyCostAttribution(applyOrder.getId(), companyId);
                log.info("assembleV6RecentApplyDetail.costAttribution:{}", JsonUtils.toJsonStr(costAttribution));
                applyV5DetailVO.setCostInfoContract(new CostInfoContractVO(costAttribution));
            }
        }

        // 自定义申请单 标题处理
        if (applyOrder.getType() == ApplyType.CustomFromBeforehand.getValue()) {

            //查询标题数据
            CustformForm custformForm = custformFormMapper.selectByPrimaryKey(applyOrder.getFormId());
            applyV5DetailVO.setMainTitle(StringUtils.isNotBlank(applyOrder.getOrderName())?applyOrder.getOrderName():custformForm.getFormName());

            Boolean isShowReimbursementBtn = true;
            //处理是否展示去报销按钮
            //获取申请单报销单配置
            CustformReimbursementConfigExample exampleCustomFormApplyConfigExample = new CustformReimbursementConfigExample();
            exampleCustomFormApplyConfigExample.createCriteria().andFormIdEqualTo(applyOrder.getFormId());
            List<CustformReimbursementConfig> list = custformReimbursementConfigMapper.selectByExample(exampleCustomFormApplyConfigExample);
            log.info("申请单报销单配置:{}", com.luastar.swift.base.json.JsonUtils.toJson(list));
            //增加申请单报销单配置
            if (!org.springframework.util.CollectionUtils.isEmpty(list)) {
                CustformReimbursementConfig custformReimbursementConfig = list.get(0);

                //1. 开启手动发起报销配置的，展示「去报销」按钮
                if(custformReimbursementConfig.getIsAllowedReimbursement().equals(1)){
                    Date tripEndDate = getTripEndTime(applyOrder.getId());
                    log.info("申请单结束日期：{}：{}",applyOrder.getId(),tripEndDate);
                    //2. 若配置了限制行程未结束的，行程未结束的不展示
                    //获取申请单的行程结束时间
                    if(ObjUtils.isNotEmpty(tripEndDate)){
                        if(custformReimbursementConfig.getIsTripEndReimbursement().equals(0)){
                            //比较申请单结束日期 和 今天相比
                            // 获取今天的日期
                            LocalDate today = LocalDate.now();
                            LocalDate dateToCompareLocal = tripEndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                            // 说明结束了 可以展示报销单按钮
                            if (!dateToCompareLocal.isBefore(today)) {
                                isShowReimbursementBtn = false;
                            }
                        }

                        //超期不允许报销的，行程结束超期后，不展示「去报销」按钮
                        if(custformReimbursementConfig.getIsTripEndOverReimbursement().equals(0)){
                            // 获取今天的日期
                            Calendar todayCalendar = Calendar.getInstance();
                            Date today1 = todayCalendar.getTime();
                            int daysToAdd = custformReimbursementConfig.getTripEndOverReimbursementDay();
                            Calendar modifiedCalendar = Calendar.getInstance();
                            modifiedCalendar.setTime(tripEndDate);
                            modifiedCalendar.add(Calendar.DAY_OF_MONTH, daysToAdd);
                            Date modifiedDate = modifiedCalendar.getTime();
                            // 比较日期  说明已经超期
                            if (modifiedDate.before(today1)) {
                                isShowReimbursementBtn = false;
                            }
                        }
                    }else{
                        isShowReimbursementBtn = false;
                    }
                }else{
                    isShowReimbursementBtn = false;
                }

            }else{
                isShowReimbursementBtn = false;
            }
            if(isShowReimbursementBtn){
                if(applyOrder.getState().equals(4)){
                    applyV5DetailVO.setIsShowGoReimbursement(isShowReimbursementBtn);
                }
            }
        }
        // 设置跨主体消费权限
        Boolean openConsumer = iCommonSettingService.openCrossEnterpriseConsumption(companyId);
        applyV5DetailVO.setOpen_consumer(openConsumer);
        if (null == applyV5DetailVO.getIssue()) {
            applyV5DetailVO.setIssue(0);
        }
        applyV5DetailVO.setControlUpgradeFlag(0);
        // 自定义申请单，消费主体
        if (applyOrder.getType() == ApplyType.CustomFromBeforehand.getValue()) {
            ApplyOrderExtendExample example = new ApplyOrderExtendExample();
            example.createCriteria().andApplyOrderIdEqualTo(applyOrder.getId());
            List<ApplyOrderExtend> applyOrderExtendList = applyOrderExtendMapper.selectByExample(example);
            if (CollectionUtils.isNotEmpty(applyOrderExtendList)) {
                ApplyOrderExtend applyOrderExtend = applyOrderExtendList.get(0);
                applyV5DetailVO.setConsumer_company_id(applyOrderExtend.getConsumeCompanyId());
                applyV5DetailVO.setConsumer_company_name(applyOrderExtend.getConsumeCompanyName());
                // 组件化升级标识
                if (null != applyOrderExtend.getControlUpgradeFlag()) {
                    applyV5DetailVO.setControlUpgradeFlag(applyOrderExtend.getControlUpgradeFlag());
                }
                if (openConsumer) {
                    if (companyId.equals(applyOrderExtendList.get(0).getConsumeCompanyId())) {
                        applyV5DetailVO.setConsumer_flag(true);
                    } else {
                        List<EmployeeSwitchableCompanyDTO> employeeSwitchableCompanyDTOList =
                                iGroupEmployeeService.listEmployeeSwitchableCompanies(userId, null);
                        if (CollectionUtils.isNotEmpty(employeeSwitchableCompanyDTOList)) {
                            for (EmployeeSwitchableCompanyDTO switchableCompanyDTO : employeeSwitchableCompanyDTOList) {
                                if (switchableCompanyDTO.getCompanyId().equals(applyOrderExtendList.get(0).getConsumeCompanyId())) {
                                    applyV5DetailVO.setConsumer_flag(true);
                                    break;
                                }
                            }
                        }
                    }
                }
            }
            String formId = applyOrder.getFormId();
            if (StringUtils.isNoneBlank(formId)) {
                CustformForm custformForm = custformFormMapper.selectByPrimaryKey(formId);
                if (null != custformForm && Objects.equals(custformForm.getGroupType(), 2)) {
                    applyV5DetailVO.setIssue(1);
                }
            }
        }

        return applyV5DetailVO;
    }
    public Date getTripEndTime(String applyId) {
        ApplyTripInfoExample example = new ApplyTripInfoExample();
        example.createCriteria().andApplyOrderIdEqualTo(applyId);
        List<ApplyTripInfo> tripList = applyTripInfoMapper.selectByExample(example);
        Date tripEndTime = getTripEndTime(tripList);
        log.info("行程结束时间, applyId:{}, tripEndTime:{}", applyId, tripEndTime);
        return tripEndTime;
    }

    /**
     * 获取行程结束时间
     */
    public static Date getTripEndTime(List<ApplyTripInfo> tripList) {
        if (ObjUtils.isEmpty(tripList)) {
            return null;
        }
        List<Date> dateList = Lists.newArrayList();
        for (ApplyTripInfo trip : tripList) {
            if (null == trip.getType()) {
                log.warn("行程数据异常：{}", com.luastar.swift.base.json.JsonUtils.toJson(trip));
                continue;
            }
            CategoryTypeEnum tripType = CategoryTypeEnum.valueOf(trip.getType());
            if (tripType == null) {
                log.warn("获取行程结束时间异常 未知的行程类型, tripType:{}", trip.getType());
                continue;
            }
            if (tripType == CategoryTypeEnum.REIMBURSE) {
                log.warn("纯报销不参与计算 tripType:{}", trip.getType());
                continue;
            }
            // 只统计差旅和自定义包含的行程
            if (tripType != CategoryTypeEnum.Air
                    && tripType != CategoryTypeEnum.IntlAir
                    && tripType != CategoryTypeEnum.Train
                    && tripType != CategoryTypeEnum.Hotel
                    && tripType != CategoryTypeEnum.IntelHotel
                    && tripType != CategoryTypeEnum.Taxi
                    && tripType != CategoryTypeEnum.MeiShi
                    && tripType != CategoryTypeEnum.Takeaway
                    && tripType != CategoryTypeEnum.BUS
                    && tripType != CategoryTypeEnum.SELF_DRIVER
                    && tripType != CategoryTypeEnum.MULTI_TRIP) {
                log.warn("非差旅/自定义审批的行程 不参与计算 tripType:{}", trip.getType());
                continue;
            }
            // 不再根据行程类型判断日期 添加全部日期然后判空
            dateList.add(trip.getStartTime());
            dateList.add(trip.getFloatStartTime());
            dateList.add(trip.getEndTime());
            dateList.add(trip.getFloatEndTime());
            dateList.add(trip.getBackStartTime());
            dateList.add(trip.getFloatBackStartTime());
            dateList.add(trip.getBackEndTime());
            dateList.add(trip.getFloatBackEndTime());
        }
        return dateList.stream().filter(ObjUtils::isNotEmpty).max(Comparator.naturalOrder()).orElse(null);
    }
}
