package com.fenbeitong.saas.core.contract.messagesettings;

import com.fenbeitong.saas.core.contract.messagesettings.inner.CommonNoticeContract;

/**
 * Created by zhaohaichao on 2019/12/14.
 */
public class MessageSetupUserCommonContract {

    private Integer apply_message_setting;

    private Integer customer_service_notice;

    private CommonNoticeContract notice;

    public CommonNoticeContract getNotice() {
        return notice;
    }

    public void setNotice(CommonNoticeContract notice) {
        this.notice = notice;
    }

    public Integer getApply_message_setting() {
        return apply_message_setting;
    }

    public void setApply_message_setting(Integer apply_message_setting) {
        this.apply_message_setting = apply_message_setting;
    }

    public Integer getCustomer_service_notice() {
        return customer_service_notice;
    }

    public void setCustomer_service_notice(Integer customer_service_notice) {
        this.customer_service_notice = customer_service_notice;
    }
}
