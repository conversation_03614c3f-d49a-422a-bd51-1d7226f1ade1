package com.fenbeitong.saas.core.contract.messagesettings.inner;

import com.fenbeitong.saas.core.contract.user.EmployeeInfoContract;
import com.fenbeitong.saas.core.model.saas.MessageSetup;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 额度退还
 * @create 2022/6/25 4:57 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreditRefundRemind {
    /**
     * 是否启用提醒
     */
    private Integer is_check;
    /**
     * 是否发送APP通知
     */
    private Integer app_notice;
    /**
     * 是否发送邮件通知
     */
    private Integer mail_notice;
    /**
     * 是否发送电话短信通知
     */
    private Integer phone_notice;

    /**
     * 接收人ID
     */
    private List<EmployeeInfoContract> receiver_list = new ArrayList<>();


    /**
     * 员工退还
     */
    private Integer employee_refund;
    /**
     * 企业回收
     */
    private Integer company_refund;
    /**
     * 系统退还
     */
    private Integer system_refund;

    public static String getRemindType() {
        return "credit_refund_remind";
    }

    public void build(MessageSetup setup) {
        this.setIs_check(setup.getIsChecked());
        this.setApp_notice(setup.getIntVal1());
        this.setMail_notice(setup.getIntVal2());
        this.setPhone_notice(setup.getIntVal3());
        this.setEmployee_refund(Integer.parseInt(setup.getStrVal1()));
        this.setCompany_refund(Integer.parseInt(setup.getStrVal2()));
        this.setSystem_refund(Integer.parseInt(setup.getStrVal3()));
    }
}
