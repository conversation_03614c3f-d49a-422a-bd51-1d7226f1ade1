package com.fenbeitong.saas.core.model.saasplus;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TbBankOrderExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table tb_bank_order
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table tb_bank_order
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table tb_bank_order
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tb_bank_order
     *
     * @mbg.generated
     */
    public TbBankOrderExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tb_bank_order
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tb_bank_order
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tb_bank_order
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tb_bank_order
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tb_bank_order
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tb_bank_order
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tb_bank_order
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tb_bank_order
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tb_bank_order
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tb_bank_order
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table tb_bank_order
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(String value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(String value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(String value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(String value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(String value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLike(String value) {
            addCriterion("order_id like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotLike(String value) {
            addCriterion("order_id not like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<String> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<String> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(String value1, String value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(String value1, String value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andShopNameIsNull() {
            addCriterion("shop_name is null");
            return (Criteria) this;
        }

        public Criteria andShopNameIsNotNull() {
            addCriterion("shop_name is not null");
            return (Criteria) this;
        }

        public Criteria andShopNameEqualTo(String value) {
            addCriterion("shop_name =", value, "shopName");
            return (Criteria) this;
        }

        public Criteria andShopNameNotEqualTo(String value) {
            addCriterion("shop_name <>", value, "shopName");
            return (Criteria) this;
        }

        public Criteria andShopNameGreaterThan(String value) {
            addCriterion("shop_name >", value, "shopName");
            return (Criteria) this;
        }

        public Criteria andShopNameGreaterThanOrEqualTo(String value) {
            addCriterion("shop_name >=", value, "shopName");
            return (Criteria) this;
        }

        public Criteria andShopNameLessThan(String value) {
            addCriterion("shop_name <", value, "shopName");
            return (Criteria) this;
        }

        public Criteria andShopNameLessThanOrEqualTo(String value) {
            addCriterion("shop_name <=", value, "shopName");
            return (Criteria) this;
        }

        public Criteria andShopNameLike(String value) {
            addCriterion("shop_name like", value, "shopName");
            return (Criteria) this;
        }

        public Criteria andShopNameNotLike(String value) {
            addCriterion("shop_name not like", value, "shopName");
            return (Criteria) this;
        }

        public Criteria andShopNameIn(List<String> values) {
            addCriterion("shop_name in", values, "shopName");
            return (Criteria) this;
        }

        public Criteria andShopNameNotIn(List<String> values) {
            addCriterion("shop_name not in", values, "shopName");
            return (Criteria) this;
        }

        public Criteria andShopNameBetween(String value1, String value2) {
            addCriterion("shop_name between", value1, value2, "shopName");
            return (Criteria) this;
        }

        public Criteria andShopNameNotBetween(String value1, String value2) {
            addCriterion("shop_name not between", value1, value2, "shopName");
            return (Criteria) this;
        }

        public Criteria andBankAccountNoIsNull() {
            addCriterion("bank_account_no is null");
            return (Criteria) this;
        }

        public Criteria andBankAccountNoIsNotNull() {
            addCriterion("bank_account_no is not null");
            return (Criteria) this;
        }

        public Criteria andBankAccountNoEqualTo(String value) {
            addCriterion("bank_account_no =", value, "bankAccountNo");
            return (Criteria) this;
        }

        public Criteria andBankAccountNoNotEqualTo(String value) {
            addCriterion("bank_account_no <>", value, "bankAccountNo");
            return (Criteria) this;
        }

        public Criteria andBankAccountNoGreaterThan(String value) {
            addCriterion("bank_account_no >", value, "bankAccountNo");
            return (Criteria) this;
        }

        public Criteria andBankAccountNoGreaterThanOrEqualTo(String value) {
            addCriterion("bank_account_no >=", value, "bankAccountNo");
            return (Criteria) this;
        }

        public Criteria andBankAccountNoLessThan(String value) {
            addCriterion("bank_account_no <", value, "bankAccountNo");
            return (Criteria) this;
        }

        public Criteria andBankAccountNoLessThanOrEqualTo(String value) {
            addCriterion("bank_account_no <=", value, "bankAccountNo");
            return (Criteria) this;
        }

        public Criteria andBankAccountNoLike(String value) {
            addCriterion("bank_account_no like", value, "bankAccountNo");
            return (Criteria) this;
        }

        public Criteria andBankAccountNoNotLike(String value) {
            addCriterion("bank_account_no not like", value, "bankAccountNo");
            return (Criteria) this;
        }

        public Criteria andBankAccountNoIn(List<String> values) {
            addCriterion("bank_account_no in", values, "bankAccountNo");
            return (Criteria) this;
        }

        public Criteria andBankAccountNoNotIn(List<String> values) {
            addCriterion("bank_account_no not in", values, "bankAccountNo");
            return (Criteria) this;
        }

        public Criteria andBankAccountNoBetween(String value1, String value2) {
            addCriterion("bank_account_no between", value1, value2, "bankAccountNo");
            return (Criteria) this;
        }

        public Criteria andBankAccountNoNotBetween(String value1, String value2) {
            addCriterion("bank_account_no not between", value1, value2, "bankAccountNo");
            return (Criteria) this;
        }

        public Criteria andTotalPriceIsNull() {
            addCriterion("total_price is null");
            return (Criteria) this;
        }

        public Criteria andTotalPriceIsNotNull() {
            addCriterion("total_price is not null");
            return (Criteria) this;
        }

        public Criteria andTotalPriceEqualTo(BigDecimal value) {
            addCriterion("total_price =", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotEqualTo(BigDecimal value) {
            addCriterion("total_price <>", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceGreaterThan(BigDecimal value) {
            addCriterion("total_price >", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_price >=", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceLessThan(BigDecimal value) {
            addCriterion("total_price <", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_price <=", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceIn(List<BigDecimal> values) {
            addCriterion("total_price in", values, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotIn(List<BigDecimal> values) {
            addCriterion("total_price not in", values, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_price between", value1, value2, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_price not between", value1, value2, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andCostPriceIsNull() {
            addCriterion("cost_price is null");
            return (Criteria) this;
        }

        public Criteria andCostPriceIsNotNull() {
            addCriterion("cost_price is not null");
            return (Criteria) this;
        }

        public Criteria andCostPriceEqualTo(BigDecimal value) {
            addCriterion("cost_price =", value, "costPrice");
            return (Criteria) this;
        }

        public Criteria andCostPriceNotEqualTo(BigDecimal value) {
            addCriterion("cost_price <>", value, "costPrice");
            return (Criteria) this;
        }

        public Criteria andCostPriceGreaterThan(BigDecimal value) {
            addCriterion("cost_price >", value, "costPrice");
            return (Criteria) this;
        }

        public Criteria andCostPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("cost_price >=", value, "costPrice");
            return (Criteria) this;
        }

        public Criteria andCostPriceLessThan(BigDecimal value) {
            addCriterion("cost_price <", value, "costPrice");
            return (Criteria) this;
        }

        public Criteria andCostPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("cost_price <=", value, "costPrice");
            return (Criteria) this;
        }

        public Criteria andCostPriceIn(List<BigDecimal> values) {
            addCriterion("cost_price in", values, "costPrice");
            return (Criteria) this;
        }

        public Criteria andCostPriceNotIn(List<BigDecimal> values) {
            addCriterion("cost_price not in", values, "costPrice");
            return (Criteria) this;
        }

        public Criteria andCostPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cost_price between", value1, value2, "costPrice");
            return (Criteria) this;
        }

        public Criteria andCostPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("cost_price not between", value1, value2, "costPrice");
            return (Criteria) this;
        }

        public Criteria andCompanyTotalPayIsNull() {
            addCriterion("company_total_pay is null");
            return (Criteria) this;
        }

        public Criteria andCompanyTotalPayIsNotNull() {
            addCriterion("company_total_pay is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyTotalPayEqualTo(BigDecimal value) {
            addCriterion("company_total_pay =", value, "companyTotalPay");
            return (Criteria) this;
        }

        public Criteria andCompanyTotalPayNotEqualTo(BigDecimal value) {
            addCriterion("company_total_pay <>", value, "companyTotalPay");
            return (Criteria) this;
        }

        public Criteria andCompanyTotalPayGreaterThan(BigDecimal value) {
            addCriterion("company_total_pay >", value, "companyTotalPay");
            return (Criteria) this;
        }

        public Criteria andCompanyTotalPayGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("company_total_pay >=", value, "companyTotalPay");
            return (Criteria) this;
        }

        public Criteria andCompanyTotalPayLessThan(BigDecimal value) {
            addCriterion("company_total_pay <", value, "companyTotalPay");
            return (Criteria) this;
        }

        public Criteria andCompanyTotalPayLessThanOrEqualTo(BigDecimal value) {
            addCriterion("company_total_pay <=", value, "companyTotalPay");
            return (Criteria) this;
        }

        public Criteria andCompanyTotalPayIn(List<BigDecimal> values) {
            addCriterion("company_total_pay in", values, "companyTotalPay");
            return (Criteria) this;
        }

        public Criteria andCompanyTotalPayNotIn(List<BigDecimal> values) {
            addCriterion("company_total_pay not in", values, "companyTotalPay");
            return (Criteria) this;
        }

        public Criteria andCompanyTotalPayBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("company_total_pay between", value1, value2, "companyTotalPay");
            return (Criteria) this;
        }

        public Criteria andCompanyTotalPayNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("company_total_pay not between", value1, value2, "companyTotalPay");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIsNull() {
            addCriterion("order_status is null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIsNotNull() {
            addCriterion("order_status is not null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusEqualTo(Integer value) {
            addCriterion("order_status =", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotEqualTo(Integer value) {
            addCriterion("order_status <>", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThan(Integer value) {
            addCriterion("order_status >", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_status >=", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThan(Integer value) {
            addCriterion("order_status <", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThanOrEqualTo(Integer value) {
            addCriterion("order_status <=", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIn(List<Integer> values) {
            addCriterion("order_status in", values, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotIn(List<Integer> values) {
            addCriterion("order_status not in", values, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusBetween(Integer value1, Integer value2) {
            addCriterion("order_status between", value1, value2, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("order_status not between", value1, value2, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andSupplierIdIsNull() {
            addCriterion("supplier_id is null");
            return (Criteria) this;
        }

        public Criteria andSupplierIdIsNotNull() {
            addCriterion("supplier_id is not null");
            return (Criteria) this;
        }

        public Criteria andSupplierIdEqualTo(Integer value) {
            addCriterion("supplier_id =", value, "supplierId");
            return (Criteria) this;
        }

        public Criteria andSupplierIdNotEqualTo(Integer value) {
            addCriterion("supplier_id <>", value, "supplierId");
            return (Criteria) this;
        }

        public Criteria andSupplierIdGreaterThan(Integer value) {
            addCriterion("supplier_id >", value, "supplierId");
            return (Criteria) this;
        }

        public Criteria andSupplierIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("supplier_id >=", value, "supplierId");
            return (Criteria) this;
        }

        public Criteria andSupplierIdLessThan(Integer value) {
            addCriterion("supplier_id <", value, "supplierId");
            return (Criteria) this;
        }

        public Criteria andSupplierIdLessThanOrEqualTo(Integer value) {
            addCriterion("supplier_id <=", value, "supplierId");
            return (Criteria) this;
        }

        public Criteria andSupplierIdIn(List<Integer> values) {
            addCriterion("supplier_id in", values, "supplierId");
            return (Criteria) this;
        }

        public Criteria andSupplierIdNotIn(List<Integer> values) {
            addCriterion("supplier_id not in", values, "supplierId");
            return (Criteria) this;
        }

        public Criteria andSupplierIdBetween(Integer value1, Integer value2) {
            addCriterion("supplier_id between", value1, value2, "supplierId");
            return (Criteria) this;
        }

        public Criteria andSupplierIdNotBetween(Integer value1, Integer value2) {
            addCriterion("supplier_id not between", value1, value2, "supplierId");
            return (Criteria) this;
        }

        public Criteria andUserMarkIsNull() {
            addCriterion("user_mark is null");
            return (Criteria) this;
        }

        public Criteria andUserMarkIsNotNull() {
            addCriterion("user_mark is not null");
            return (Criteria) this;
        }

        public Criteria andUserMarkEqualTo(Integer value) {
            addCriterion("user_mark =", value, "userMark");
            return (Criteria) this;
        }

        public Criteria andUserMarkNotEqualTo(Integer value) {
            addCriterion("user_mark <>", value, "userMark");
            return (Criteria) this;
        }

        public Criteria andUserMarkGreaterThan(Integer value) {
            addCriterion("user_mark >", value, "userMark");
            return (Criteria) this;
        }

        public Criteria andUserMarkGreaterThanOrEqualTo(Integer value) {
            addCriterion("user_mark >=", value, "userMark");
            return (Criteria) this;
        }

        public Criteria andUserMarkLessThan(Integer value) {
            addCriterion("user_mark <", value, "userMark");
            return (Criteria) this;
        }

        public Criteria andUserMarkLessThanOrEqualTo(Integer value) {
            addCriterion("user_mark <=", value, "userMark");
            return (Criteria) this;
        }

        public Criteria andUserMarkIn(List<Integer> values) {
            addCriterion("user_mark in", values, "userMark");
            return (Criteria) this;
        }

        public Criteria andUserMarkNotIn(List<Integer> values) {
            addCriterion("user_mark not in", values, "userMark");
            return (Criteria) this;
        }

        public Criteria andUserMarkBetween(Integer value1, Integer value2) {
            addCriterion("user_mark between", value1, value2, "userMark");
            return (Criteria) this;
        }

        public Criteria andUserMarkNotBetween(Integer value1, Integer value2) {
            addCriterion("user_mark not between", value1, value2, "userMark");
            return (Criteria) this;
        }

        public Criteria andFbOrderIdIsNull() {
            addCriterion("fb_order_id is null");
            return (Criteria) this;
        }

        public Criteria andFbOrderIdIsNotNull() {
            addCriterion("fb_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andFbOrderIdEqualTo(String value) {
            addCriterion("fb_order_id =", value, "fbOrderId");
            return (Criteria) this;
        }

        public Criteria andFbOrderIdNotEqualTo(String value) {
            addCriterion("fb_order_id <>", value, "fbOrderId");
            return (Criteria) this;
        }

        public Criteria andFbOrderIdGreaterThan(String value) {
            addCriterion("fb_order_id >", value, "fbOrderId");
            return (Criteria) this;
        }

        public Criteria andFbOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("fb_order_id >=", value, "fbOrderId");
            return (Criteria) this;
        }

        public Criteria andFbOrderIdLessThan(String value) {
            addCriterion("fb_order_id <", value, "fbOrderId");
            return (Criteria) this;
        }

        public Criteria andFbOrderIdLessThanOrEqualTo(String value) {
            addCriterion("fb_order_id <=", value, "fbOrderId");
            return (Criteria) this;
        }

        public Criteria andFbOrderIdLike(String value) {
            addCriterion("fb_order_id like", value, "fbOrderId");
            return (Criteria) this;
        }

        public Criteria andFbOrderIdNotLike(String value) {
            addCriterion("fb_order_id not like", value, "fbOrderId");
            return (Criteria) this;
        }

        public Criteria andFbOrderIdIn(List<String> values) {
            addCriterion("fb_order_id in", values, "fbOrderId");
            return (Criteria) this;
        }

        public Criteria andFbOrderIdNotIn(List<String> values) {
            addCriterion("fb_order_id not in", values, "fbOrderId");
            return (Criteria) this;
        }

        public Criteria andFbOrderIdBetween(String value1, String value2) {
            addCriterion("fb_order_id between", value1, value2, "fbOrderId");
            return (Criteria) this;
        }

        public Criteria andFbOrderIdNotBetween(String value1, String value2) {
            addCriterion("fb_order_id not between", value1, value2, "fbOrderId");
            return (Criteria) this;
        }

        public Criteria andTransactionTypeIsNull() {
            addCriterion("transaction_type is null");
            return (Criteria) this;
        }

        public Criteria andTransactionTypeIsNotNull() {
            addCriterion("transaction_type is not null");
            return (Criteria) this;
        }

        public Criteria andTransactionTypeEqualTo(Integer value) {
            addCriterion("transaction_type =", value, "transactionType");
            return (Criteria) this;
        }

        public Criteria andTransactionTypeNotEqualTo(Integer value) {
            addCriterion("transaction_type <>", value, "transactionType");
            return (Criteria) this;
        }

        public Criteria andTransactionTypeGreaterThan(Integer value) {
            addCriterion("transaction_type >", value, "transactionType");
            return (Criteria) this;
        }

        public Criteria andTransactionTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("transaction_type >=", value, "transactionType");
            return (Criteria) this;
        }

        public Criteria andTransactionTypeLessThan(Integer value) {
            addCriterion("transaction_type <", value, "transactionType");
            return (Criteria) this;
        }

        public Criteria andTransactionTypeLessThanOrEqualTo(Integer value) {
            addCriterion("transaction_type <=", value, "transactionType");
            return (Criteria) this;
        }

        public Criteria andTransactionTypeIn(List<Integer> values) {
            addCriterion("transaction_type in", values, "transactionType");
            return (Criteria) this;
        }

        public Criteria andTransactionTypeNotIn(List<Integer> values) {
            addCriterion("transaction_type not in", values, "transactionType");
            return (Criteria) this;
        }

        public Criteria andTransactionTypeBetween(Integer value1, Integer value2) {
            addCriterion("transaction_type between", value1, value2, "transactionType");
            return (Criteria) this;
        }

        public Criteria andTransactionTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("transaction_type not between", value1, value2, "transactionType");
            return (Criteria) this;
        }

        public Criteria andOrderChannelIsNull() {
            addCriterion("order_channel is null");
            return (Criteria) this;
        }

        public Criteria andOrderChannelIsNotNull() {
            addCriterion("order_channel is not null");
            return (Criteria) this;
        }

        public Criteria andOrderChannelEqualTo(Integer value) {
            addCriterion("order_channel =", value, "orderChannel");
            return (Criteria) this;
        }

        public Criteria andOrderChannelNotEqualTo(Integer value) {
            addCriterion("order_channel <>", value, "orderChannel");
            return (Criteria) this;
        }

        public Criteria andOrderChannelGreaterThan(Integer value) {
            addCriterion("order_channel >", value, "orderChannel");
            return (Criteria) this;
        }

        public Criteria andOrderChannelGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_channel >=", value, "orderChannel");
            return (Criteria) this;
        }

        public Criteria andOrderChannelLessThan(Integer value) {
            addCriterion("order_channel <", value, "orderChannel");
            return (Criteria) this;
        }

        public Criteria andOrderChannelLessThanOrEqualTo(Integer value) {
            addCriterion("order_channel <=", value, "orderChannel");
            return (Criteria) this;
        }

        public Criteria andOrderChannelIn(List<Integer> values) {
            addCriterion("order_channel in", values, "orderChannel");
            return (Criteria) this;
        }

        public Criteria andOrderChannelNotIn(List<Integer> values) {
            addCriterion("order_channel not in", values, "orderChannel");
            return (Criteria) this;
        }

        public Criteria andOrderChannelBetween(Integer value1, Integer value2) {
            addCriterion("order_channel between", value1, value2, "orderChannel");
            return (Criteria) this;
        }

        public Criteria andOrderChannelNotBetween(Integer value1, Integer value2) {
            addCriterion("order_channel not between", value1, value2, "orderChannel");
            return (Criteria) this;
        }

        public Criteria andCategoryTypeIsNull() {
            addCriterion("category_type is null");
            return (Criteria) this;
        }

        public Criteria andCategoryTypeIsNotNull() {
            addCriterion("category_type is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryTypeEqualTo(Integer value) {
            addCriterion("category_type =", value, "categoryType");
            return (Criteria) this;
        }

        public Criteria andCategoryTypeNotEqualTo(Integer value) {
            addCriterion("category_type <>", value, "categoryType");
            return (Criteria) this;
        }

        public Criteria andCategoryTypeGreaterThan(Integer value) {
            addCriterion("category_type >", value, "categoryType");
            return (Criteria) this;
        }

        public Criteria andCategoryTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("category_type >=", value, "categoryType");
            return (Criteria) this;
        }

        public Criteria andCategoryTypeLessThan(Integer value) {
            addCriterion("category_type <", value, "categoryType");
            return (Criteria) this;
        }

        public Criteria andCategoryTypeLessThanOrEqualTo(Integer value) {
            addCriterion("category_type <=", value, "categoryType");
            return (Criteria) this;
        }

        public Criteria andCategoryTypeIn(List<Integer> values) {
            addCriterion("category_type in", values, "categoryType");
            return (Criteria) this;
        }

        public Criteria andCategoryTypeNotIn(List<Integer> values) {
            addCriterion("category_type not in", values, "categoryType");
            return (Criteria) this;
        }

        public Criteria andCategoryTypeBetween(Integer value1, Integer value2) {
            addCriterion("category_type between", value1, value2, "categoryType");
            return (Criteria) this;
        }

        public Criteria andCategoryTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("category_type not between", value1, value2, "categoryType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeIsNull() {
            addCriterion("account_type is null");
            return (Criteria) this;
        }

        public Criteria andAccountTypeIsNotNull() {
            addCriterion("account_type is not null");
            return (Criteria) this;
        }

        public Criteria andAccountTypeEqualTo(Integer value) {
            addCriterion("account_type =", value, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeNotEqualTo(Integer value) {
            addCriterion("account_type <>", value, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeGreaterThan(Integer value) {
            addCriterion("account_type >", value, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_type >=", value, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeLessThan(Integer value) {
            addCriterion("account_type <", value, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeLessThanOrEqualTo(Integer value) {
            addCriterion("account_type <=", value, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeIn(List<Integer> values) {
            addCriterion("account_type in", values, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeNotIn(List<Integer> values) {
            addCriterion("account_type not in", values, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeBetween(Integer value1, Integer value2) {
            addCriterion("account_type between", value1, value2, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("account_type not between", value1, value2, "accountType");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeIsNull() {
            addCriterion("pay_time is null");
            return (Criteria) this;
        }

        public Criteria andPayTimeIsNotNull() {
            addCriterion("pay_time is not null");
            return (Criteria) this;
        }

        public Criteria andPayTimeEqualTo(Date value) {
            addCriterion("pay_time =", value, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeNotEqualTo(Date value) {
            addCriterion("pay_time <>", value, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeGreaterThan(Date value) {
            addCriterion("pay_time >", value, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("pay_time >=", value, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeLessThan(Date value) {
            addCriterion("pay_time <", value, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeLessThanOrEqualTo(Date value) {
            addCriterion("pay_time <=", value, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeIn(List<Date> values) {
            addCriterion("pay_time in", values, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeNotIn(List<Date> values) {
            addCriterion("pay_time not in", values, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeBetween(Date value1, Date value2) {
            addCriterion("pay_time between", value1, value2, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeNotBetween(Date value1, Date value2) {
            addCriterion("pay_time not between", value1, value2, "payTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeIsNull() {
            addCriterion("complete_time is null");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeIsNotNull() {
            addCriterion("complete_time is not null");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeEqualTo(Date value) {
            addCriterion("complete_time =", value, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeNotEqualTo(Date value) {
            addCriterion("complete_time <>", value, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeGreaterThan(Date value) {
            addCriterion("complete_time >", value, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("complete_time >=", value, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeLessThan(Date value) {
            addCriterion("complete_time <", value, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeLessThanOrEqualTo(Date value) {
            addCriterion("complete_time <=", value, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeIn(List<Date> values) {
            addCriterion("complete_time in", values, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeNotIn(List<Date> values) {
            addCriterion("complete_time not in", values, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeBetween(Date value1, Date value2) {
            addCriterion("complete_time between", value1, value2, "completeTime");
            return (Criteria) this;
        }

        public Criteria andCompleteTimeNotBetween(Date value1, Date value2) {
            addCriterion("complete_time not between", value1, value2, "completeTime");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(String value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(String value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(String value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(String value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(String value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLike(String value) {
            addCriterion("user_id like", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotLike(String value) {
            addCriterion("user_id not like", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<String> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<String> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(String value1, String value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(String value1, String value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserNameIsNull() {
            addCriterion("user_name is null");
            return (Criteria) this;
        }

        public Criteria andUserNameIsNotNull() {
            addCriterion("user_name is not null");
            return (Criteria) this;
        }

        public Criteria andUserNameEqualTo(String value) {
            addCriterion("user_name =", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotEqualTo(String value) {
            addCriterion("user_name <>", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameGreaterThan(String value) {
            addCriterion("user_name >", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("user_name >=", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameLessThan(String value) {
            addCriterion("user_name <", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameLessThanOrEqualTo(String value) {
            addCriterion("user_name <=", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameLike(String value) {
            addCriterion("user_name like", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotLike(String value) {
            addCriterion("user_name not like", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameIn(List<String> values) {
            addCriterion("user_name in", values, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotIn(List<String> values) {
            addCriterion("user_name not in", values, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameBetween(String value1, String value2) {
            addCriterion("user_name between", value1, value2, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotBetween(String value1, String value2) {
            addCriterion("user_name not between", value1, value2, "userName");
            return (Criteria) this;
        }

        public Criteria andUserPhoneIsNull() {
            addCriterion("user_phone is null");
            return (Criteria) this;
        }

        public Criteria andUserPhoneIsNotNull() {
            addCriterion("user_phone is not null");
            return (Criteria) this;
        }

        public Criteria andUserPhoneEqualTo(String value) {
            addCriterion("user_phone =", value, "userPhone");
            return (Criteria) this;
        }

        public Criteria andUserPhoneNotEqualTo(String value) {
            addCriterion("user_phone <>", value, "userPhone");
            return (Criteria) this;
        }

        public Criteria andUserPhoneGreaterThan(String value) {
            addCriterion("user_phone >", value, "userPhone");
            return (Criteria) this;
        }

        public Criteria andUserPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("user_phone >=", value, "userPhone");
            return (Criteria) this;
        }

        public Criteria andUserPhoneLessThan(String value) {
            addCriterion("user_phone <", value, "userPhone");
            return (Criteria) this;
        }

        public Criteria andUserPhoneLessThanOrEqualTo(String value) {
            addCriterion("user_phone <=", value, "userPhone");
            return (Criteria) this;
        }

        public Criteria andUserPhoneLike(String value) {
            addCriterion("user_phone like", value, "userPhone");
            return (Criteria) this;
        }

        public Criteria andUserPhoneNotLike(String value) {
            addCriterion("user_phone not like", value, "userPhone");
            return (Criteria) this;
        }

        public Criteria andUserPhoneIn(List<String> values) {
            addCriterion("user_phone in", values, "userPhone");
            return (Criteria) this;
        }

        public Criteria andUserPhoneNotIn(List<String> values) {
            addCriterion("user_phone not in", values, "userPhone");
            return (Criteria) this;
        }

        public Criteria andUserPhoneBetween(String value1, String value2) {
            addCriterion("user_phone between", value1, value2, "userPhone");
            return (Criteria) this;
        }

        public Criteria andUserPhoneNotBetween(String value1, String value2) {
            addCriterion("user_phone not between", value1, value2, "userPhone");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(String value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(String value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(String value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(String value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(String value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(String value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLike(String value) {
            addCriterion("company_id like", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotLike(String value) {
            addCriterion("company_id not like", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<String> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<String> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(String value1, String value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(String value1, String value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andBankTransTimeIsNull() {
            addCriterion("bank_trans_time is null");
            return (Criteria) this;
        }

        public Criteria andBankTransTimeIsNotNull() {
            addCriterion("bank_trans_time is not null");
            return (Criteria) this;
        }

        public Criteria andBankTransTimeEqualTo(Date value) {
            addCriterion("bank_trans_time =", value, "bankTransTime");
            return (Criteria) this;
        }

        public Criteria andBankTransTimeNotEqualTo(Date value) {
            addCriterion("bank_trans_time <>", value, "bankTransTime");
            return (Criteria) this;
        }

        public Criteria andBankTransTimeGreaterThan(Date value) {
            addCriterion("bank_trans_time >", value, "bankTransTime");
            return (Criteria) this;
        }

        public Criteria andBankTransTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("bank_trans_time >=", value, "bankTransTime");
            return (Criteria) this;
        }

        public Criteria andBankTransTimeLessThan(Date value) {
            addCriterion("bank_trans_time <", value, "bankTransTime");
            return (Criteria) this;
        }

        public Criteria andBankTransTimeLessThanOrEqualTo(Date value) {
            addCriterion("bank_trans_time <=", value, "bankTransTime");
            return (Criteria) this;
        }

        public Criteria andBankTransTimeIn(List<Date> values) {
            addCriterion("bank_trans_time in", values, "bankTransTime");
            return (Criteria) this;
        }

        public Criteria andBankTransTimeNotIn(List<Date> values) {
            addCriterion("bank_trans_time not in", values, "bankTransTime");
            return (Criteria) this;
        }

        public Criteria andBankTransTimeBetween(Date value1, Date value2) {
            addCriterion("bank_trans_time between", value1, value2, "bankTransTime");
            return (Criteria) this;
        }

        public Criteria andBankTransTimeNotBetween(Date value1, Date value2) {
            addCriterion("bank_trans_time not between", value1, value2, "bankTransTime");
            return (Criteria) this;
        }

        public Criteria andBankTransNoIsNull() {
            addCriterion("bank_trans_no is null");
            return (Criteria) this;
        }

        public Criteria andBankTransNoIsNotNull() {
            addCriterion("bank_trans_no is not null");
            return (Criteria) this;
        }

        public Criteria andBankTransNoEqualTo(String value) {
            addCriterion("bank_trans_no =", value, "bankTransNo");
            return (Criteria) this;
        }

        public Criteria andBankTransNoNotEqualTo(String value) {
            addCriterion("bank_trans_no <>", value, "bankTransNo");
            return (Criteria) this;
        }

        public Criteria andBankTransNoGreaterThan(String value) {
            addCriterion("bank_trans_no >", value, "bankTransNo");
            return (Criteria) this;
        }

        public Criteria andBankTransNoGreaterThanOrEqualTo(String value) {
            addCriterion("bank_trans_no >=", value, "bankTransNo");
            return (Criteria) this;
        }

        public Criteria andBankTransNoLessThan(String value) {
            addCriterion("bank_trans_no <", value, "bankTransNo");
            return (Criteria) this;
        }

        public Criteria andBankTransNoLessThanOrEqualTo(String value) {
            addCriterion("bank_trans_no <=", value, "bankTransNo");
            return (Criteria) this;
        }

        public Criteria andBankTransNoLike(String value) {
            addCriterion("bank_trans_no like", value, "bankTransNo");
            return (Criteria) this;
        }

        public Criteria andBankTransNoNotLike(String value) {
            addCriterion("bank_trans_no not like", value, "bankTransNo");
            return (Criteria) this;
        }

        public Criteria andBankTransNoIn(List<String> values) {
            addCriterion("bank_trans_no in", values, "bankTransNo");
            return (Criteria) this;
        }

        public Criteria andBankTransNoNotIn(List<String> values) {
            addCriterion("bank_trans_no not in", values, "bankTransNo");
            return (Criteria) this;
        }

        public Criteria andBankTransNoBetween(String value1, String value2) {
            addCriterion("bank_trans_no between", value1, value2, "bankTransNo");
            return (Criteria) this;
        }

        public Criteria andBankTransNoNotBetween(String value1, String value2) {
            addCriterion("bank_trans_no not between", value1, value2, "bankTransNo");
            return (Criteria) this;
        }

        public Criteria andFbCashierTxnIdIsNull() {
            addCriterion("fb_cashier_txn_id is null");
            return (Criteria) this;
        }

        public Criteria andFbCashierTxnIdIsNotNull() {
            addCriterion("fb_cashier_txn_id is not null");
            return (Criteria) this;
        }

        public Criteria andFbCashierTxnIdEqualTo(String value) {
            addCriterion("fb_cashier_txn_id =", value, "fbCashierTxnId");
            return (Criteria) this;
        }

        public Criteria andFbCashierTxnIdNotEqualTo(String value) {
            addCriterion("fb_cashier_txn_id <>", value, "fbCashierTxnId");
            return (Criteria) this;
        }

        public Criteria andFbCashierTxnIdGreaterThan(String value) {
            addCriterion("fb_cashier_txn_id >", value, "fbCashierTxnId");
            return (Criteria) this;
        }

        public Criteria andFbCashierTxnIdGreaterThanOrEqualTo(String value) {
            addCriterion("fb_cashier_txn_id >=", value, "fbCashierTxnId");
            return (Criteria) this;
        }

        public Criteria andFbCashierTxnIdLessThan(String value) {
            addCriterion("fb_cashier_txn_id <", value, "fbCashierTxnId");
            return (Criteria) this;
        }

        public Criteria andFbCashierTxnIdLessThanOrEqualTo(String value) {
            addCriterion("fb_cashier_txn_id <=", value, "fbCashierTxnId");
            return (Criteria) this;
        }

        public Criteria andFbCashierTxnIdLike(String value) {
            addCriterion("fb_cashier_txn_id like", value, "fbCashierTxnId");
            return (Criteria) this;
        }

        public Criteria andFbCashierTxnIdNotLike(String value) {
            addCriterion("fb_cashier_txn_id not like", value, "fbCashierTxnId");
            return (Criteria) this;
        }

        public Criteria andFbCashierTxnIdIn(List<String> values) {
            addCriterion("fb_cashier_txn_id in", values, "fbCashierTxnId");
            return (Criteria) this;
        }

        public Criteria andFbCashierTxnIdNotIn(List<String> values) {
            addCriterion("fb_cashier_txn_id not in", values, "fbCashierTxnId");
            return (Criteria) this;
        }

        public Criteria andFbCashierTxnIdBetween(String value1, String value2) {
            addCriterion("fb_cashier_txn_id between", value1, value2, "fbCashierTxnId");
            return (Criteria) this;
        }

        public Criteria andFbCashierTxnIdNotBetween(String value1, String value2) {
            addCriterion("fb_cashier_txn_id not between", value1, value2, "fbCashierTxnId");
            return (Criteria) this;
        }

        public Criteria andFbPayNotifyUrlIsNull() {
            addCriterion("fb_pay_notify_url is null");
            return (Criteria) this;
        }

        public Criteria andFbPayNotifyUrlIsNotNull() {
            addCriterion("fb_pay_notify_url is not null");
            return (Criteria) this;
        }

        public Criteria andFbPayNotifyUrlEqualTo(String value) {
            addCriterion("fb_pay_notify_url =", value, "fbPayNotifyUrl");
            return (Criteria) this;
        }

        public Criteria andFbPayNotifyUrlNotEqualTo(String value) {
            addCriterion("fb_pay_notify_url <>", value, "fbPayNotifyUrl");
            return (Criteria) this;
        }

        public Criteria andFbPayNotifyUrlGreaterThan(String value) {
            addCriterion("fb_pay_notify_url >", value, "fbPayNotifyUrl");
            return (Criteria) this;
        }

        public Criteria andFbPayNotifyUrlGreaterThanOrEqualTo(String value) {
            addCriterion("fb_pay_notify_url >=", value, "fbPayNotifyUrl");
            return (Criteria) this;
        }

        public Criteria andFbPayNotifyUrlLessThan(String value) {
            addCriterion("fb_pay_notify_url <", value, "fbPayNotifyUrl");
            return (Criteria) this;
        }

        public Criteria andFbPayNotifyUrlLessThanOrEqualTo(String value) {
            addCriterion("fb_pay_notify_url <=", value, "fbPayNotifyUrl");
            return (Criteria) this;
        }

        public Criteria andFbPayNotifyUrlLike(String value) {
            addCriterion("fb_pay_notify_url like", value, "fbPayNotifyUrl");
            return (Criteria) this;
        }

        public Criteria andFbPayNotifyUrlNotLike(String value) {
            addCriterion("fb_pay_notify_url not like", value, "fbPayNotifyUrl");
            return (Criteria) this;
        }

        public Criteria andFbPayNotifyUrlIn(List<String> values) {
            addCriterion("fb_pay_notify_url in", values, "fbPayNotifyUrl");
            return (Criteria) this;
        }

        public Criteria andFbPayNotifyUrlNotIn(List<String> values) {
            addCriterion("fb_pay_notify_url not in", values, "fbPayNotifyUrl");
            return (Criteria) this;
        }

        public Criteria andFbPayNotifyUrlBetween(String value1, String value2) {
            addCriterion("fb_pay_notify_url between", value1, value2, "fbPayNotifyUrl");
            return (Criteria) this;
        }

        public Criteria andFbPayNotifyUrlNotBetween(String value1, String value2) {
            addCriterion("fb_pay_notify_url not between", value1, value2, "fbPayNotifyUrl");
            return (Criteria) this;
        }

        public Criteria andAccountSubTypeIsNull() {
            addCriterion("account_sub_type is null");
            return (Criteria) this;
        }

        public Criteria andAccountSubTypeIsNotNull() {
            addCriterion("account_sub_type is not null");
            return (Criteria) this;
        }

        public Criteria andAccountSubTypeEqualTo(Integer value) {
            addCriterion("account_sub_type =", value, "accountSubType");
            return (Criteria) this;
        }

        public Criteria andAccountSubTypeNotEqualTo(Integer value) {
            addCriterion("account_sub_type <>", value, "accountSubType");
            return (Criteria) this;
        }

        public Criteria andAccountSubTypeGreaterThan(Integer value) {
            addCriterion("account_sub_type >", value, "accountSubType");
            return (Criteria) this;
        }

        public Criteria andAccountSubTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_sub_type >=", value, "accountSubType");
            return (Criteria) this;
        }

        public Criteria andAccountSubTypeLessThan(Integer value) {
            addCriterion("account_sub_type <", value, "accountSubType");
            return (Criteria) this;
        }

        public Criteria andAccountSubTypeLessThanOrEqualTo(Integer value) {
            addCriterion("account_sub_type <=", value, "accountSubType");
            return (Criteria) this;
        }

        public Criteria andAccountSubTypeIn(List<Integer> values) {
            addCriterion("account_sub_type in", values, "accountSubType");
            return (Criteria) this;
        }

        public Criteria andAccountSubTypeNotIn(List<Integer> values) {
            addCriterion("account_sub_type not in", values, "accountSubType");
            return (Criteria) this;
        }

        public Criteria andAccountSubTypeBetween(Integer value1, Integer value2) {
            addCriterion("account_sub_type between", value1, value2, "accountSubType");
            return (Criteria) this;
        }

        public Criteria andAccountSubTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("account_sub_type not between", value1, value2, "accountSubType");
            return (Criteria) this;
        }

        public Criteria andCostImageUrlIsNull() {
            addCriterion("cost_image_url is null");
            return (Criteria) this;
        }

        public Criteria andCostImageUrlIsNotNull() {
            addCriterion("cost_image_url is not null");
            return (Criteria) this;
        }

        public Criteria andCostImageUrlEqualTo(String value) {
            addCriterion("cost_image_url =", value, "costImageUrl");
            return (Criteria) this;
        }

        public Criteria andCostImageUrlNotEqualTo(String value) {
            addCriterion("cost_image_url <>", value, "costImageUrl");
            return (Criteria) this;
        }

        public Criteria andCostImageUrlGreaterThan(String value) {
            addCriterion("cost_image_url >", value, "costImageUrl");
            return (Criteria) this;
        }

        public Criteria andCostImageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("cost_image_url >=", value, "costImageUrl");
            return (Criteria) this;
        }

        public Criteria andCostImageUrlLessThan(String value) {
            addCriterion("cost_image_url <", value, "costImageUrl");
            return (Criteria) this;
        }

        public Criteria andCostImageUrlLessThanOrEqualTo(String value) {
            addCriterion("cost_image_url <=", value, "costImageUrl");
            return (Criteria) this;
        }

        public Criteria andCostImageUrlLike(String value) {
            addCriterion("cost_image_url like", value, "costImageUrl");
            return (Criteria) this;
        }

        public Criteria andCostImageUrlNotLike(String value) {
            addCriterion("cost_image_url not like", value, "costImageUrl");
            return (Criteria) this;
        }

        public Criteria andCostImageUrlIn(List<String> values) {
            addCriterion("cost_image_url in", values, "costImageUrl");
            return (Criteria) this;
        }

        public Criteria andCostImageUrlNotIn(List<String> values) {
            addCriterion("cost_image_url not in", values, "costImageUrl");
            return (Criteria) this;
        }

        public Criteria andCostImageUrlBetween(String value1, String value2) {
            addCriterion("cost_image_url between", value1, value2, "costImageUrl");
            return (Criteria) this;
        }

        public Criteria andCostImageUrlNotBetween(String value1, String value2) {
            addCriterion("cost_image_url not between", value1, value2, "costImageUrl");
            return (Criteria) this;
        }

        public Criteria andBankNameIsNull() {
            addCriterion("bank_name is null");
            return (Criteria) this;
        }

        public Criteria andBankNameIsNotNull() {
            addCriterion("bank_name is not null");
            return (Criteria) this;
        }

        public Criteria andBankNameEqualTo(String value) {
            addCriterion("bank_name =", value, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameNotEqualTo(String value) {
            addCriterion("bank_name <>", value, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameGreaterThan(String value) {
            addCriterion("bank_name >", value, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameGreaterThanOrEqualTo(String value) {
            addCriterion("bank_name >=", value, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameLessThan(String value) {
            addCriterion("bank_name <", value, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameLessThanOrEqualTo(String value) {
            addCriterion("bank_name <=", value, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameLike(String value) {
            addCriterion("bank_name like", value, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameNotLike(String value) {
            addCriterion("bank_name not like", value, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameIn(List<String> values) {
            addCriterion("bank_name in", values, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameNotIn(List<String> values) {
            addCriterion("bank_name not in", values, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameBetween(String value1, String value2) {
            addCriterion("bank_name between", value1, value2, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameNotBetween(String value1, String value2) {
            addCriterion("bank_name not between", value1, value2, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankHupoTransTypeIsNull() {
            addCriterion("bank_hupo_trans_type is null");
            return (Criteria) this;
        }

        public Criteria andBankHupoTransTypeIsNotNull() {
            addCriterion("bank_hupo_trans_type is not null");
            return (Criteria) this;
        }

        public Criteria andBankHupoTransTypeEqualTo(Integer value) {
            addCriterion("bank_hupo_trans_type =", value, "bankHupoTransType");
            return (Criteria) this;
        }

        public Criteria andBankHupoTransTypeNotEqualTo(Integer value) {
            addCriterion("bank_hupo_trans_type <>", value, "bankHupoTransType");
            return (Criteria) this;
        }

        public Criteria andBankHupoTransTypeGreaterThan(Integer value) {
            addCriterion("bank_hupo_trans_type >", value, "bankHupoTransType");
            return (Criteria) this;
        }

        public Criteria andBankHupoTransTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("bank_hupo_trans_type >=", value, "bankHupoTransType");
            return (Criteria) this;
        }

        public Criteria andBankHupoTransTypeLessThan(Integer value) {
            addCriterion("bank_hupo_trans_type <", value, "bankHupoTransType");
            return (Criteria) this;
        }

        public Criteria andBankHupoTransTypeLessThanOrEqualTo(Integer value) {
            addCriterion("bank_hupo_trans_type <=", value, "bankHupoTransType");
            return (Criteria) this;
        }

        public Criteria andBankHupoTransTypeIn(List<Integer> values) {
            addCriterion("bank_hupo_trans_type in", values, "bankHupoTransType");
            return (Criteria) this;
        }

        public Criteria andBankHupoTransTypeNotIn(List<Integer> values) {
            addCriterion("bank_hupo_trans_type not in", values, "bankHupoTransType");
            return (Criteria) this;
        }

        public Criteria andBankHupoTransTypeBetween(Integer value1, Integer value2) {
            addCriterion("bank_hupo_trans_type between", value1, value2, "bankHupoTransType");
            return (Criteria) this;
        }

        public Criteria andBankHupoTransTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("bank_hupo_trans_type not between", value1, value2, "bankHupoTransType");
            return (Criteria) this;
        }

        public Criteria andBankOriTransNoIsNull() {
            addCriterion("bank_ori_trans_no is null");
            return (Criteria) this;
        }

        public Criteria andBankOriTransNoIsNotNull() {
            addCriterion("bank_ori_trans_no is not null");
            return (Criteria) this;
        }

        public Criteria andBankOriTransNoEqualTo(String value) {
            addCriterion("bank_ori_trans_no =", value, "bankOriTransNo");
            return (Criteria) this;
        }

        public Criteria andBankOriTransNoNotEqualTo(String value) {
            addCriterion("bank_ori_trans_no <>", value, "bankOriTransNo");
            return (Criteria) this;
        }

        public Criteria andBankOriTransNoGreaterThan(String value) {
            addCriterion("bank_ori_trans_no >", value, "bankOriTransNo");
            return (Criteria) this;
        }

        public Criteria andBankOriTransNoGreaterThanOrEqualTo(String value) {
            addCriterion("bank_ori_trans_no >=", value, "bankOriTransNo");
            return (Criteria) this;
        }

        public Criteria andBankOriTransNoLessThan(String value) {
            addCriterion("bank_ori_trans_no <", value, "bankOriTransNo");
            return (Criteria) this;
        }

        public Criteria andBankOriTransNoLessThanOrEqualTo(String value) {
            addCriterion("bank_ori_trans_no <=", value, "bankOriTransNo");
            return (Criteria) this;
        }

        public Criteria andBankOriTransNoLike(String value) {
            addCriterion("bank_ori_trans_no like", value, "bankOriTransNo");
            return (Criteria) this;
        }

        public Criteria andBankOriTransNoNotLike(String value) {
            addCriterion("bank_ori_trans_no not like", value, "bankOriTransNo");
            return (Criteria) this;
        }

        public Criteria andBankOriTransNoIn(List<String> values) {
            addCriterion("bank_ori_trans_no in", values, "bankOriTransNo");
            return (Criteria) this;
        }

        public Criteria andBankOriTransNoNotIn(List<String> values) {
            addCriterion("bank_ori_trans_no not in", values, "bankOriTransNo");
            return (Criteria) this;
        }

        public Criteria andBankOriTransNoBetween(String value1, String value2) {
            addCriterion("bank_ori_trans_no between", value1, value2, "bankOriTransNo");
            return (Criteria) this;
        }

        public Criteria andBankOriTransNoNotBetween(String value1, String value2) {
            addCriterion("bank_ori_trans_no not between", value1, value2, "bankOriTransNo");
            return (Criteria) this;
        }

        public Criteria andMonthTypeIsNull() {
            addCriterion("month_type is null");
            return (Criteria) this;
        }

        public Criteria andMonthTypeIsNotNull() {
            addCriterion("month_type is not null");
            return (Criteria) this;
        }

        public Criteria andMonthTypeEqualTo(String value) {
            addCriterion("month_type =", value, "monthType");
            return (Criteria) this;
        }

        public Criteria andMonthTypeNotEqualTo(String value) {
            addCriterion("month_type <>", value, "monthType");
            return (Criteria) this;
        }

        public Criteria andMonthTypeGreaterThan(String value) {
            addCriterion("month_type >", value, "monthType");
            return (Criteria) this;
        }

        public Criteria andMonthTypeGreaterThanOrEqualTo(String value) {
            addCriterion("month_type >=", value, "monthType");
            return (Criteria) this;
        }

        public Criteria andMonthTypeLessThan(String value) {
            addCriterion("month_type <", value, "monthType");
            return (Criteria) this;
        }

        public Criteria andMonthTypeLessThanOrEqualTo(String value) {
            addCriterion("month_type <=", value, "monthType");
            return (Criteria) this;
        }

        public Criteria andMonthTypeLike(String value) {
            addCriterion("month_type like", value, "monthType");
            return (Criteria) this;
        }

        public Criteria andMonthTypeNotLike(String value) {
            addCriterion("month_type not like", value, "monthType");
            return (Criteria) this;
        }

        public Criteria andMonthTypeIn(List<String> values) {
            addCriterion("month_type in", values, "monthType");
            return (Criteria) this;
        }

        public Criteria andMonthTypeNotIn(List<String> values) {
            addCriterion("month_type not in", values, "monthType");
            return (Criteria) this;
        }

        public Criteria andMonthTypeBetween(String value1, String value2) {
            addCriterion("month_type between", value1, value2, "monthType");
            return (Criteria) this;
        }

        public Criteria andMonthTypeNotBetween(String value1, String value2) {
            addCriterion("month_type not between", value1, value2, "monthType");
            return (Criteria) this;
        }

        public Criteria andSysExt1IsNull() {
            addCriterion("sys_ext_1 is null");
            return (Criteria) this;
        }

        public Criteria andSysExt1IsNotNull() {
            addCriterion("sys_ext_1 is not null");
            return (Criteria) this;
        }

        public Criteria andSysExt1EqualTo(String value) {
            addCriterion("sys_ext_1 =", value, "sysExt1");
            return (Criteria) this;
        }

        public Criteria andSysExt1NotEqualTo(String value) {
            addCriterion("sys_ext_1 <>", value, "sysExt1");
            return (Criteria) this;
        }

        public Criteria andSysExt1GreaterThan(String value) {
            addCriterion("sys_ext_1 >", value, "sysExt1");
            return (Criteria) this;
        }

        public Criteria andSysExt1GreaterThanOrEqualTo(String value) {
            addCriterion("sys_ext_1 >=", value, "sysExt1");
            return (Criteria) this;
        }

        public Criteria andSysExt1LessThan(String value) {
            addCriterion("sys_ext_1 <", value, "sysExt1");
            return (Criteria) this;
        }

        public Criteria andSysExt1LessThanOrEqualTo(String value) {
            addCriterion("sys_ext_1 <=", value, "sysExt1");
            return (Criteria) this;
        }

        public Criteria andSysExt1Like(String value) {
            addCriterion("sys_ext_1 like", value, "sysExt1");
            return (Criteria) this;
        }

        public Criteria andSysExt1NotLike(String value) {
            addCriterion("sys_ext_1 not like", value, "sysExt1");
            return (Criteria) this;
        }

        public Criteria andSysExt1In(List<String> values) {
            addCriterion("sys_ext_1 in", values, "sysExt1");
            return (Criteria) this;
        }

        public Criteria andSysExt1NotIn(List<String> values) {
            addCriterion("sys_ext_1 not in", values, "sysExt1");
            return (Criteria) this;
        }

        public Criteria andSysExt1Between(String value1, String value2) {
            addCriterion("sys_ext_1 between", value1, value2, "sysExt1");
            return (Criteria) this;
        }

        public Criteria andSysExt1NotBetween(String value1, String value2) {
            addCriterion("sys_ext_1 not between", value1, value2, "sysExt1");
            return (Criteria) this;
        }

        public Criteria andSysExt2IsNull() {
            addCriterion("sys_ext_2 is null");
            return (Criteria) this;
        }

        public Criteria andSysExt2IsNotNull() {
            addCriterion("sys_ext_2 is not null");
            return (Criteria) this;
        }

        public Criteria andSysExt2EqualTo(String value) {
            addCriterion("sys_ext_2 =", value, "sysExt2");
            return (Criteria) this;
        }

        public Criteria andSysExt2NotEqualTo(String value) {
            addCriterion("sys_ext_2 <>", value, "sysExt2");
            return (Criteria) this;
        }

        public Criteria andSysExt2GreaterThan(String value) {
            addCriterion("sys_ext_2 >", value, "sysExt2");
            return (Criteria) this;
        }

        public Criteria andSysExt2GreaterThanOrEqualTo(String value) {
            addCriterion("sys_ext_2 >=", value, "sysExt2");
            return (Criteria) this;
        }

        public Criteria andSysExt2LessThan(String value) {
            addCriterion("sys_ext_2 <", value, "sysExt2");
            return (Criteria) this;
        }

        public Criteria andSysExt2LessThanOrEqualTo(String value) {
            addCriterion("sys_ext_2 <=", value, "sysExt2");
            return (Criteria) this;
        }

        public Criteria andSysExt2Like(String value) {
            addCriterion("sys_ext_2 like", value, "sysExt2");
            return (Criteria) this;
        }

        public Criteria andSysExt2NotLike(String value) {
            addCriterion("sys_ext_2 not like", value, "sysExt2");
            return (Criteria) this;
        }

        public Criteria andSysExt2In(List<String> values) {
            addCriterion("sys_ext_2 in", values, "sysExt2");
            return (Criteria) this;
        }

        public Criteria andSysExt2NotIn(List<String> values) {
            addCriterion("sys_ext_2 not in", values, "sysExt2");
            return (Criteria) this;
        }

        public Criteria andSysExt2Between(String value1, String value2) {
            addCriterion("sys_ext_2 between", value1, value2, "sysExt2");
            return (Criteria) this;
        }

        public Criteria andSysExt2NotBetween(String value1, String value2) {
            addCriterion("sys_ext_2 not between", value1, value2, "sysExt2");
            return (Criteria) this;
        }

        public Criteria andSysExt3IsNull() {
            addCriterion("sys_ext_3 is null");
            return (Criteria) this;
        }

        public Criteria andSysExt3IsNotNull() {
            addCriterion("sys_ext_3 is not null");
            return (Criteria) this;
        }

        public Criteria andSysExt3EqualTo(String value) {
            addCriterion("sys_ext_3 =", value, "sysExt3");
            return (Criteria) this;
        }

        public Criteria andSysExt3NotEqualTo(String value) {
            addCriterion("sys_ext_3 <>", value, "sysExt3");
            return (Criteria) this;
        }

        public Criteria andSysExt3GreaterThan(String value) {
            addCriterion("sys_ext_3 >", value, "sysExt3");
            return (Criteria) this;
        }

        public Criteria andSysExt3GreaterThanOrEqualTo(String value) {
            addCriterion("sys_ext_3 >=", value, "sysExt3");
            return (Criteria) this;
        }

        public Criteria andSysExt3LessThan(String value) {
            addCriterion("sys_ext_3 <", value, "sysExt3");
            return (Criteria) this;
        }

        public Criteria andSysExt3LessThanOrEqualTo(String value) {
            addCriterion("sys_ext_3 <=", value, "sysExt3");
            return (Criteria) this;
        }

        public Criteria andSysExt3Like(String value) {
            addCriterion("sys_ext_3 like", value, "sysExt3");
            return (Criteria) this;
        }

        public Criteria andSysExt3NotLike(String value) {
            addCriterion("sys_ext_3 not like", value, "sysExt3");
            return (Criteria) this;
        }

        public Criteria andSysExt3In(List<String> values) {
            addCriterion("sys_ext_3 in", values, "sysExt3");
            return (Criteria) this;
        }

        public Criteria andSysExt3NotIn(List<String> values) {
            addCriterion("sys_ext_3 not in", values, "sysExt3");
            return (Criteria) this;
        }

        public Criteria andSysExt3Between(String value1, String value2) {
            addCriterion("sys_ext_3 between", value1, value2, "sysExt3");
            return (Criteria) this;
        }

        public Criteria andSysExt3NotBetween(String value1, String value2) {
            addCriterion("sys_ext_3 not between", value1, value2, "sysExt3");
            return (Criteria) this;
        }

        public Criteria andSysExt4IsNull() {
            addCriterion("sys_ext_4 is null");
            return (Criteria) this;
        }

        public Criteria andSysExt4IsNotNull() {
            addCriterion("sys_ext_4 is not null");
            return (Criteria) this;
        }

        public Criteria andSysExt4EqualTo(String value) {
            addCriterion("sys_ext_4 =", value, "sysExt4");
            return (Criteria) this;
        }

        public Criteria andSysExt4NotEqualTo(String value) {
            addCriterion("sys_ext_4 <>", value, "sysExt4");
            return (Criteria) this;
        }

        public Criteria andSysExt4GreaterThan(String value) {
            addCriterion("sys_ext_4 >", value, "sysExt4");
            return (Criteria) this;
        }

        public Criteria andSysExt4GreaterThanOrEqualTo(String value) {
            addCriterion("sys_ext_4 >=", value, "sysExt4");
            return (Criteria) this;
        }

        public Criteria andSysExt4LessThan(String value) {
            addCriterion("sys_ext_4 <", value, "sysExt4");
            return (Criteria) this;
        }

        public Criteria andSysExt4LessThanOrEqualTo(String value) {
            addCriterion("sys_ext_4 <=", value, "sysExt4");
            return (Criteria) this;
        }

        public Criteria andSysExt4Like(String value) {
            addCriterion("sys_ext_4 like", value, "sysExt4");
            return (Criteria) this;
        }

        public Criteria andSysExt4NotLike(String value) {
            addCriterion("sys_ext_4 not like", value, "sysExt4");
            return (Criteria) this;
        }

        public Criteria andSysExt4In(List<String> values) {
            addCriterion("sys_ext_4 in", values, "sysExt4");
            return (Criteria) this;
        }

        public Criteria andSysExt4NotIn(List<String> values) {
            addCriterion("sys_ext_4 not in", values, "sysExt4");
            return (Criteria) this;
        }

        public Criteria andSysExt4Between(String value1, String value2) {
            addCriterion("sys_ext_4 between", value1, value2, "sysExt4");
            return (Criteria) this;
        }

        public Criteria andSysExt4NotBetween(String value1, String value2) {
            addCriterion("sys_ext_4 not between", value1, value2, "sysExt4");
            return (Criteria) this;
        }

        public Criteria andShowTypeIsNull() {
            addCriterion("show_type is null");
            return (Criteria) this;
        }

        public Criteria andShowTypeIsNotNull() {
            addCriterion("show_type is not null");
            return (Criteria) this;
        }

        public Criteria andShowTypeEqualTo(Integer value) {
            addCriterion("show_type =", value, "showType");
            return (Criteria) this;
        }

        public Criteria andShowTypeNotEqualTo(Integer value) {
            addCriterion("show_type <>", value, "showType");
            return (Criteria) this;
        }

        public Criteria andShowTypeGreaterThan(Integer value) {
            addCriterion("show_type >", value, "showType");
            return (Criteria) this;
        }

        public Criteria andShowTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("show_type >=", value, "showType");
            return (Criteria) this;
        }

        public Criteria andShowTypeLessThan(Integer value) {
            addCriterion("show_type <", value, "showType");
            return (Criteria) this;
        }

        public Criteria andShowTypeLessThanOrEqualTo(Integer value) {
            addCriterion("show_type <=", value, "showType");
            return (Criteria) this;
        }

        public Criteria andShowTypeIn(List<Integer> values) {
            addCriterion("show_type in", values, "showType");
            return (Criteria) this;
        }

        public Criteria andShowTypeNotIn(List<Integer> values) {
            addCriterion("show_type not in", values, "showType");
            return (Criteria) this;
        }

        public Criteria andShowTypeBetween(Integer value1, Integer value2) {
            addCriterion("show_type between", value1, value2, "showType");
            return (Criteria) this;
        }

        public Criteria andShowTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("show_type not between", value1, value2, "showType");
            return (Criteria) this;
        }

        public Criteria andCheckStatusIsNull() {
            addCriterion("check_status is null");
            return (Criteria) this;
        }

        public Criteria andCheckStatusIsNotNull() {
            addCriterion("check_status is not null");
            return (Criteria) this;
        }

        public Criteria andCheckStatusEqualTo(Integer value) {
            addCriterion("check_status =", value, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusNotEqualTo(Integer value) {
            addCriterion("check_status <>", value, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusGreaterThan(Integer value) {
            addCriterion("check_status >", value, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("check_status >=", value, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusLessThan(Integer value) {
            addCriterion("check_status <", value, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusLessThanOrEqualTo(Integer value) {
            addCriterion("check_status <=", value, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusIn(List<Integer> values) {
            addCriterion("check_status in", values, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusNotIn(List<Integer> values) {
            addCriterion("check_status not in", values, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusBetween(Integer value1, Integer value2) {
            addCriterion("check_status between", value1, value2, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("check_status not between", value1, value2, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckTimeIsNull() {
            addCriterion("check_time is null");
            return (Criteria) this;
        }

        public Criteria andCheckTimeIsNotNull() {
            addCriterion("check_time is not null");
            return (Criteria) this;
        }

        public Criteria andCheckTimeEqualTo(Date value) {
            addCriterion("check_time =", value, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeNotEqualTo(Date value) {
            addCriterion("check_time <>", value, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeGreaterThan(Date value) {
            addCriterion("check_time >", value, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("check_time >=", value, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeLessThan(Date value) {
            addCriterion("check_time <", value, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeLessThanOrEqualTo(Date value) {
            addCriterion("check_time <=", value, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeIn(List<Date> values) {
            addCriterion("check_time in", values, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeNotIn(List<Date> values) {
            addCriterion("check_time not in", values, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeBetween(Date value1, Date value2) {
            addCriterion("check_time between", value1, value2, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeNotBetween(Date value1, Date value2) {
            addCriterion("check_time not between", value1, value2, "checkTime");
            return (Criteria) this;
        }

        public Criteria andReplaceTagIsNull() {
            addCriterion("replace_tag is null");
            return (Criteria) this;
        }

        public Criteria andReplaceTagIsNotNull() {
            addCriterion("replace_tag is not null");
            return (Criteria) this;
        }

        public Criteria andReplaceTagEqualTo(Integer value) {
            addCriterion("replace_tag =", value, "replaceTag");
            return (Criteria) this;
        }

        public Criteria andReplaceTagNotEqualTo(Integer value) {
            addCriterion("replace_tag <>", value, "replaceTag");
            return (Criteria) this;
        }

        public Criteria andReplaceTagGreaterThan(Integer value) {
            addCriterion("replace_tag >", value, "replaceTag");
            return (Criteria) this;
        }

        public Criteria andReplaceTagGreaterThanOrEqualTo(Integer value) {
            addCriterion("replace_tag >=", value, "replaceTag");
            return (Criteria) this;
        }

        public Criteria andReplaceTagLessThan(Integer value) {
            addCriterion("replace_tag <", value, "replaceTag");
            return (Criteria) this;
        }

        public Criteria andReplaceTagLessThanOrEqualTo(Integer value) {
            addCriterion("replace_tag <=", value, "replaceTag");
            return (Criteria) this;
        }

        public Criteria andReplaceTagIn(List<Integer> values) {
            addCriterion("replace_tag in", values, "replaceTag");
            return (Criteria) this;
        }

        public Criteria andReplaceTagNotIn(List<Integer> values) {
            addCriterion("replace_tag not in", values, "replaceTag");
            return (Criteria) this;
        }

        public Criteria andReplaceTagBetween(Integer value1, Integer value2) {
            addCriterion("replace_tag between", value1, value2, "replaceTag");
            return (Criteria) this;
        }

        public Criteria andReplaceTagNotBetween(Integer value1, Integer value2) {
            addCriterion("replace_tag not between", value1, value2, "replaceTag");
            return (Criteria) this;
        }

        public Criteria andUserMarkTimeIsNull() {
            addCriterion("user_mark_time is null");
            return (Criteria) this;
        }

        public Criteria andUserMarkTimeIsNotNull() {
            addCriterion("user_mark_time is not null");
            return (Criteria) this;
        }

        public Criteria andUserMarkTimeEqualTo(Date value) {
            addCriterion("user_mark_time =", value, "userMarkTime");
            return (Criteria) this;
        }

        public Criteria andUserMarkTimeNotEqualTo(Date value) {
            addCriterion("user_mark_time <>", value, "userMarkTime");
            return (Criteria) this;
        }

        public Criteria andUserMarkTimeGreaterThan(Date value) {
            addCriterion("user_mark_time >", value, "userMarkTime");
            return (Criteria) this;
        }

        public Criteria andUserMarkTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("user_mark_time >=", value, "userMarkTime");
            return (Criteria) this;
        }

        public Criteria andUserMarkTimeLessThan(Date value) {
            addCriterion("user_mark_time <", value, "userMarkTime");
            return (Criteria) this;
        }

        public Criteria andUserMarkTimeLessThanOrEqualTo(Date value) {
            addCriterion("user_mark_time <=", value, "userMarkTime");
            return (Criteria) this;
        }

        public Criteria andUserMarkTimeIn(List<Date> values) {
            addCriterion("user_mark_time in", values, "userMarkTime");
            return (Criteria) this;
        }

        public Criteria andUserMarkTimeNotIn(List<Date> values) {
            addCriterion("user_mark_time not in", values, "userMarkTime");
            return (Criteria) this;
        }

        public Criteria andUserMarkTimeBetween(Date value1, Date value2) {
            addCriterion("user_mark_time between", value1, value2, "userMarkTime");
            return (Criteria) this;
        }

        public Criteria andUserMarkTimeNotBetween(Date value1, Date value2) {
            addCriterion("user_mark_time not between", value1, value2, "userMarkTime");
            return (Criteria) this;
        }

        public Criteria andRefundStatusIsNull() {
            addCriterion("refund_status is null");
            return (Criteria) this;
        }

        public Criteria andRefundStatusIsNotNull() {
            addCriterion("refund_status is not null");
            return (Criteria) this;
        }

        public Criteria andRefundStatusEqualTo(Integer value) {
            addCriterion("refund_status =", value, "refundStatus");
            return (Criteria) this;
        }

        public Criteria andRefundStatusNotEqualTo(Integer value) {
            addCriterion("refund_status <>", value, "refundStatus");
            return (Criteria) this;
        }

        public Criteria andRefundStatusGreaterThan(Integer value) {
            addCriterion("refund_status >", value, "refundStatus");
            return (Criteria) this;
        }

        public Criteria andRefundStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("refund_status >=", value, "refundStatus");
            return (Criteria) this;
        }

        public Criteria andRefundStatusLessThan(Integer value) {
            addCriterion("refund_status <", value, "refundStatus");
            return (Criteria) this;
        }

        public Criteria andRefundStatusLessThanOrEqualTo(Integer value) {
            addCriterion("refund_status <=", value, "refundStatus");
            return (Criteria) this;
        }

        public Criteria andRefundStatusIn(List<Integer> values) {
            addCriterion("refund_status in", values, "refundStatus");
            return (Criteria) this;
        }

        public Criteria andRefundStatusNotIn(List<Integer> values) {
            addCriterion("refund_status not in", values, "refundStatus");
            return (Criteria) this;
        }

        public Criteria andRefundStatusBetween(Integer value1, Integer value2) {
            addCriterion("refund_status between", value1, value2, "refundStatus");
            return (Criteria) this;
        }

        public Criteria andRefundStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("refund_status not between", value1, value2, "refundStatus");
            return (Criteria) this;
        }

        public Criteria andTotalInvoicePriceIsNull() {
            addCriterion("total_invoice_price is null");
            return (Criteria) this;
        }

        public Criteria andTotalInvoicePriceIsNotNull() {
            addCriterion("total_invoice_price is not null");
            return (Criteria) this;
        }

        public Criteria andTotalInvoicePriceEqualTo(BigDecimal value) {
            addCriterion("total_invoice_price =", value, "totalInvoicePrice");
            return (Criteria) this;
        }

        public Criteria andTotalInvoicePriceNotEqualTo(BigDecimal value) {
            addCriterion("total_invoice_price <>", value, "totalInvoicePrice");
            return (Criteria) this;
        }

        public Criteria andTotalInvoicePriceGreaterThan(BigDecimal value) {
            addCriterion("total_invoice_price >", value, "totalInvoicePrice");
            return (Criteria) this;
        }

        public Criteria andTotalInvoicePriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_invoice_price >=", value, "totalInvoicePrice");
            return (Criteria) this;
        }

        public Criteria andTotalInvoicePriceLessThan(BigDecimal value) {
            addCriterion("total_invoice_price <", value, "totalInvoicePrice");
            return (Criteria) this;
        }

        public Criteria andTotalInvoicePriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_invoice_price <=", value, "totalInvoicePrice");
            return (Criteria) this;
        }

        public Criteria andTotalInvoicePriceIn(List<BigDecimal> values) {
            addCriterion("total_invoice_price in", values, "totalInvoicePrice");
            return (Criteria) this;
        }

        public Criteria andTotalInvoicePriceNotIn(List<BigDecimal> values) {
            addCriterion("total_invoice_price not in", values, "totalInvoicePrice");
            return (Criteria) this;
        }

        public Criteria andTotalInvoicePriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_invoice_price between", value1, value2, "totalInvoicePrice");
            return (Criteria) this;
        }

        public Criteria andTotalInvoicePriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_invoice_price not between", value1, value2, "totalInvoicePrice");
            return (Criteria) this;
        }

        public Criteria andPaymentIdIsNull() {
            addCriterion("payment_id is null");
            return (Criteria) this;
        }

        public Criteria andPaymentIdIsNotNull() {
            addCriterion("payment_id is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentIdEqualTo(String value) {
            addCriterion("payment_id =", value, "paymentId");
            return (Criteria) this;
        }

        public Criteria andPaymentIdNotEqualTo(String value) {
            addCriterion("payment_id <>", value, "paymentId");
            return (Criteria) this;
        }

        public Criteria andPaymentIdGreaterThan(String value) {
            addCriterion("payment_id >", value, "paymentId");
            return (Criteria) this;
        }

        public Criteria andPaymentIdGreaterThanOrEqualTo(String value) {
            addCriterion("payment_id >=", value, "paymentId");
            return (Criteria) this;
        }

        public Criteria andPaymentIdLessThan(String value) {
            addCriterion("payment_id <", value, "paymentId");
            return (Criteria) this;
        }

        public Criteria andPaymentIdLessThanOrEqualTo(String value) {
            addCriterion("payment_id <=", value, "paymentId");
            return (Criteria) this;
        }

        public Criteria andPaymentIdLike(String value) {
            addCriterion("payment_id like", value, "paymentId");
            return (Criteria) this;
        }

        public Criteria andPaymentIdNotLike(String value) {
            addCriterion("payment_id not like", value, "paymentId");
            return (Criteria) this;
        }

        public Criteria andPaymentIdIn(List<String> values) {
            addCriterion("payment_id in", values, "paymentId");
            return (Criteria) this;
        }

        public Criteria andPaymentIdNotIn(List<String> values) {
            addCriterion("payment_id not in", values, "paymentId");
            return (Criteria) this;
        }

        public Criteria andPaymentIdBetween(String value1, String value2) {
            addCriterion("payment_id between", value1, value2, "paymentId");
            return (Criteria) this;
        }

        public Criteria andPaymentIdNotBetween(String value1, String value2) {
            addCriterion("payment_id not between", value1, value2, "paymentId");
            return (Criteria) this;
        }

        public Criteria andElectronicStatusIsNull() {
            addCriterion("electronic_status is null");
            return (Criteria) this;
        }

        public Criteria andElectronicStatusIsNotNull() {
            addCriterion("electronic_status is not null");
            return (Criteria) this;
        }

        public Criteria andElectronicStatusEqualTo(Integer value) {
            addCriterion("electronic_status =", value, "electronicStatus");
            return (Criteria) this;
        }

        public Criteria andElectronicStatusNotEqualTo(Integer value) {
            addCriterion("electronic_status <>", value, "electronicStatus");
            return (Criteria) this;
        }

        public Criteria andElectronicStatusGreaterThan(Integer value) {
            addCriterion("electronic_status >", value, "electronicStatus");
            return (Criteria) this;
        }

        public Criteria andElectronicStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("electronic_status >=", value, "electronicStatus");
            return (Criteria) this;
        }

        public Criteria andElectronicStatusLessThan(Integer value) {
            addCriterion("electronic_status <", value, "electronicStatus");
            return (Criteria) this;
        }

        public Criteria andElectronicStatusLessThanOrEqualTo(Integer value) {
            addCriterion("electronic_status <=", value, "electronicStatus");
            return (Criteria) this;
        }

        public Criteria andElectronicStatusIn(List<Integer> values) {
            addCriterion("electronic_status in", values, "electronicStatus");
            return (Criteria) this;
        }

        public Criteria andElectronicStatusNotIn(List<Integer> values) {
            addCriterion("electronic_status not in", values, "electronicStatus");
            return (Criteria) this;
        }

        public Criteria andElectronicStatusBetween(Integer value1, Integer value2) {
            addCriterion("electronic_status between", value1, value2, "electronicStatus");
            return (Criteria) this;
        }

        public Criteria andElectronicStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("electronic_status not between", value1, value2, "electronicStatus");
            return (Criteria) this;
        }

        public Criteria andFailDescIsNull() {
            addCriterion("fail_desc is null");
            return (Criteria) this;
        }

        public Criteria andFailDescIsNotNull() {
            addCriterion("fail_desc is not null");
            return (Criteria) this;
        }

        public Criteria andFailDescEqualTo(String value) {
            addCriterion("fail_desc =", value, "failDesc");
            return (Criteria) this;
        }

        public Criteria andFailDescNotEqualTo(String value) {
            addCriterion("fail_desc <>", value, "failDesc");
            return (Criteria) this;
        }

        public Criteria andFailDescGreaterThan(String value) {
            addCriterion("fail_desc >", value, "failDesc");
            return (Criteria) this;
        }

        public Criteria andFailDescGreaterThanOrEqualTo(String value) {
            addCriterion("fail_desc >=", value, "failDesc");
            return (Criteria) this;
        }

        public Criteria andFailDescLessThan(String value) {
            addCriterion("fail_desc <", value, "failDesc");
            return (Criteria) this;
        }

        public Criteria andFailDescLessThanOrEqualTo(String value) {
            addCriterion("fail_desc <=", value, "failDesc");
            return (Criteria) this;
        }

        public Criteria andFailDescLike(String value) {
            addCriterion("fail_desc like", value, "failDesc");
            return (Criteria) this;
        }

        public Criteria andFailDescNotLike(String value) {
            addCriterion("fail_desc not like", value, "failDesc");
            return (Criteria) this;
        }

        public Criteria andFailDescIn(List<String> values) {
            addCriterion("fail_desc in", values, "failDesc");
            return (Criteria) this;
        }

        public Criteria andFailDescNotIn(List<String> values) {
            addCriterion("fail_desc not in", values, "failDesc");
            return (Criteria) this;
        }

        public Criteria andFailDescBetween(String value1, String value2) {
            addCriterion("fail_desc between", value1, value2, "failDesc");
            return (Criteria) this;
        }

        public Criteria andFailDescNotBetween(String value1, String value2) {
            addCriterion("fail_desc not between", value1, value2, "failDesc");
            return (Criteria) this;
        }

        public Criteria andReceiverNameIsNull() {
            addCriterion("receiver_name is null");
            return (Criteria) this;
        }

        public Criteria andReceiverNameIsNotNull() {
            addCriterion("receiver_name is not null");
            return (Criteria) this;
        }

        public Criteria andReceiverNameEqualTo(String value) {
            addCriterion("receiver_name =", value, "receiverName");
            return (Criteria) this;
        }

        public Criteria andReceiverNameNotEqualTo(String value) {
            addCriterion("receiver_name <>", value, "receiverName");
            return (Criteria) this;
        }

        public Criteria andReceiverNameGreaterThan(String value) {
            addCriterion("receiver_name >", value, "receiverName");
            return (Criteria) this;
        }

        public Criteria andReceiverNameGreaterThanOrEqualTo(String value) {
            addCriterion("receiver_name >=", value, "receiverName");
            return (Criteria) this;
        }

        public Criteria andReceiverNameLessThan(String value) {
            addCriterion("receiver_name <", value, "receiverName");
            return (Criteria) this;
        }

        public Criteria andReceiverNameLessThanOrEqualTo(String value) {
            addCriterion("receiver_name <=", value, "receiverName");
            return (Criteria) this;
        }

        public Criteria andReceiverNameLike(String value) {
            addCriterion("receiver_name like", value, "receiverName");
            return (Criteria) this;
        }

        public Criteria andReceiverNameNotLike(String value) {
            addCriterion("receiver_name not like", value, "receiverName");
            return (Criteria) this;
        }

        public Criteria andReceiverNameIn(List<String> values) {
            addCriterion("receiver_name in", values, "receiverName");
            return (Criteria) this;
        }

        public Criteria andReceiverNameNotIn(List<String> values) {
            addCriterion("receiver_name not in", values, "receiverName");
            return (Criteria) this;
        }

        public Criteria andReceiverNameBetween(String value1, String value2) {
            addCriterion("receiver_name between", value1, value2, "receiverName");
            return (Criteria) this;
        }

        public Criteria andReceiverNameNotBetween(String value1, String value2) {
            addCriterion("receiver_name not between", value1, value2, "receiverName");
            return (Criteria) this;
        }

        public Criteria andReceiverBankIsNull() {
            addCriterion("receiver_bank is null");
            return (Criteria) this;
        }

        public Criteria andReceiverBankIsNotNull() {
            addCriterion("receiver_bank is not null");
            return (Criteria) this;
        }

        public Criteria andReceiverBankEqualTo(String value) {
            addCriterion("receiver_bank =", value, "receiverBank");
            return (Criteria) this;
        }

        public Criteria andReceiverBankNotEqualTo(String value) {
            addCriterion("receiver_bank <>", value, "receiverBank");
            return (Criteria) this;
        }

        public Criteria andReceiverBankGreaterThan(String value) {
            addCriterion("receiver_bank >", value, "receiverBank");
            return (Criteria) this;
        }

        public Criteria andReceiverBankGreaterThanOrEqualTo(String value) {
            addCriterion("receiver_bank >=", value, "receiverBank");
            return (Criteria) this;
        }

        public Criteria andReceiverBankLessThan(String value) {
            addCriterion("receiver_bank <", value, "receiverBank");
            return (Criteria) this;
        }

        public Criteria andReceiverBankLessThanOrEqualTo(String value) {
            addCriterion("receiver_bank <=", value, "receiverBank");
            return (Criteria) this;
        }

        public Criteria andReceiverBankLike(String value) {
            addCriterion("receiver_bank like", value, "receiverBank");
            return (Criteria) this;
        }

        public Criteria andReceiverBankNotLike(String value) {
            addCriterion("receiver_bank not like", value, "receiverBank");
            return (Criteria) this;
        }

        public Criteria andReceiverBankIn(List<String> values) {
            addCriterion("receiver_bank in", values, "receiverBank");
            return (Criteria) this;
        }

        public Criteria andReceiverBankNotIn(List<String> values) {
            addCriterion("receiver_bank not in", values, "receiverBank");
            return (Criteria) this;
        }

        public Criteria andReceiverBankBetween(String value1, String value2) {
            addCriterion("receiver_bank between", value1, value2, "receiverBank");
            return (Criteria) this;
        }

        public Criteria andReceiverBankNotBetween(String value1, String value2) {
            addCriterion("receiver_bank not between", value1, value2, "receiverBank");
            return (Criteria) this;
        }

        public Criteria andReceiverAccountIsNull() {
            addCriterion("receiver_account is null");
            return (Criteria) this;
        }

        public Criteria andReceiverAccountIsNotNull() {
            addCriterion("receiver_account is not null");
            return (Criteria) this;
        }

        public Criteria andReceiverAccountEqualTo(String value) {
            addCriterion("receiver_account =", value, "receiverAccount");
            return (Criteria) this;
        }

        public Criteria andReceiverAccountNotEqualTo(String value) {
            addCriterion("receiver_account <>", value, "receiverAccount");
            return (Criteria) this;
        }

        public Criteria andReceiverAccountGreaterThan(String value) {
            addCriterion("receiver_account >", value, "receiverAccount");
            return (Criteria) this;
        }

        public Criteria andReceiverAccountGreaterThanOrEqualTo(String value) {
            addCriterion("receiver_account >=", value, "receiverAccount");
            return (Criteria) this;
        }

        public Criteria andReceiverAccountLessThan(String value) {
            addCriterion("receiver_account <", value, "receiverAccount");
            return (Criteria) this;
        }

        public Criteria andReceiverAccountLessThanOrEqualTo(String value) {
            addCriterion("receiver_account <=", value, "receiverAccount");
            return (Criteria) this;
        }

        public Criteria andReceiverAccountLike(String value) {
            addCriterion("receiver_account like", value, "receiverAccount");
            return (Criteria) this;
        }

        public Criteria andReceiverAccountNotLike(String value) {
            addCriterion("receiver_account not like", value, "receiverAccount");
            return (Criteria) this;
        }

        public Criteria andReceiverAccountIn(List<String> values) {
            addCriterion("receiver_account in", values, "receiverAccount");
            return (Criteria) this;
        }

        public Criteria andReceiverAccountNotIn(List<String> values) {
            addCriterion("receiver_account not in", values, "receiverAccount");
            return (Criteria) this;
        }

        public Criteria andReceiverAccountBetween(String value1, String value2) {
            addCriterion("receiver_account between", value1, value2, "receiverAccount");
            return (Criteria) this;
        }

        public Criteria andReceiverAccountNotBetween(String value1, String value2) {
            addCriterion("receiver_account not between", value1, value2, "receiverAccount");
            return (Criteria) this;
        }

        public Criteria andPublicModelIsNull() {
            addCriterion("public_model is null");
            return (Criteria) this;
        }

        public Criteria andPublicModelIsNotNull() {
            addCriterion("public_model is not null");
            return (Criteria) this;
        }

        public Criteria andPublicModelEqualTo(Integer value) {
            addCriterion("public_model =", value, "publicModel");
            return (Criteria) this;
        }

        public Criteria andPublicModelNotEqualTo(Integer value) {
            addCriterion("public_model <>", value, "publicModel");
            return (Criteria) this;
        }

        public Criteria andPublicModelGreaterThan(Integer value) {
            addCriterion("public_model >", value, "publicModel");
            return (Criteria) this;
        }

        public Criteria andPublicModelGreaterThanOrEqualTo(Integer value) {
            addCriterion("public_model >=", value, "publicModel");
            return (Criteria) this;
        }

        public Criteria andPublicModelLessThan(Integer value) {
            addCriterion("public_model <", value, "publicModel");
            return (Criteria) this;
        }

        public Criteria andPublicModelLessThanOrEqualTo(Integer value) {
            addCriterion("public_model <=", value, "publicModel");
            return (Criteria) this;
        }

        public Criteria andPublicModelIn(List<Integer> values) {
            addCriterion("public_model in", values, "publicModel");
            return (Criteria) this;
        }

        public Criteria andPublicModelNotIn(List<Integer> values) {
            addCriterion("public_model not in", values, "publicModel");
            return (Criteria) this;
        }

        public Criteria andPublicModelBetween(Integer value1, Integer value2) {
            addCriterion("public_model between", value1, value2, "publicModel");
            return (Criteria) this;
        }

        public Criteria andPublicModelNotBetween(Integer value1, Integer value2) {
            addCriterion("public_model not between", value1, value2, "publicModel");
            return (Criteria) this;
        }

        public Criteria andReceiverAccountNameIsNull() {
            addCriterion("receiver_account_name is null");
            return (Criteria) this;
        }

        public Criteria andReceiverAccountNameIsNotNull() {
            addCriterion("receiver_account_name is not null");
            return (Criteria) this;
        }

        public Criteria andReceiverAccountNameEqualTo(String value) {
            addCriterion("receiver_account_name =", value, "receiverAccountName");
            return (Criteria) this;
        }

        public Criteria andReceiverAccountNameNotEqualTo(String value) {
            addCriterion("receiver_account_name <>", value, "receiverAccountName");
            return (Criteria) this;
        }

        public Criteria andReceiverAccountNameGreaterThan(String value) {
            addCriterion("receiver_account_name >", value, "receiverAccountName");
            return (Criteria) this;
        }

        public Criteria andReceiverAccountNameGreaterThanOrEqualTo(String value) {
            addCriterion("receiver_account_name >=", value, "receiverAccountName");
            return (Criteria) this;
        }

        public Criteria andReceiverAccountNameLessThan(String value) {
            addCriterion("receiver_account_name <", value, "receiverAccountName");
            return (Criteria) this;
        }

        public Criteria andReceiverAccountNameLessThanOrEqualTo(String value) {
            addCriterion("receiver_account_name <=", value, "receiverAccountName");
            return (Criteria) this;
        }

        public Criteria andReceiverAccountNameLike(String value) {
            addCriterion("receiver_account_name like", value, "receiverAccountName");
            return (Criteria) this;
        }

        public Criteria andReceiverAccountNameNotLike(String value) {
            addCriterion("receiver_account_name not like", value, "receiverAccountName");
            return (Criteria) this;
        }

        public Criteria andReceiverAccountNameIn(List<String> values) {
            addCriterion("receiver_account_name in", values, "receiverAccountName");
            return (Criteria) this;
        }

        public Criteria andReceiverAccountNameNotIn(List<String> values) {
            addCriterion("receiver_account_name not in", values, "receiverAccountName");
            return (Criteria) this;
        }

        public Criteria andReceiverAccountNameBetween(String value1, String value2) {
            addCriterion("receiver_account_name between", value1, value2, "receiverAccountName");
            return (Criteria) this;
        }

        public Criteria andReceiverAccountNameNotBetween(String value1, String value2) {
            addCriterion("receiver_account_name not between", value1, value2, "receiverAccountName");
            return (Criteria) this;
        }

        public Criteria andBusinessModeIsNull() {
            addCriterion("business_mode is null");
            return (Criteria) this;
        }

        public Criteria andBusinessModeIsNotNull() {
            addCriterion("business_mode is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessModeEqualTo(Integer value) {
            addCriterion("business_mode =", value, "businessMode");
            return (Criteria) this;
        }

        public Criteria andBusinessModeNotEqualTo(Integer value) {
            addCriterion("business_mode <>", value, "businessMode");
            return (Criteria) this;
        }

        public Criteria andBusinessModeGreaterThan(Integer value) {
            addCriterion("business_mode >", value, "businessMode");
            return (Criteria) this;
        }

        public Criteria andBusinessModeGreaterThanOrEqualTo(Integer value) {
            addCriterion("business_mode >=", value, "businessMode");
            return (Criteria) this;
        }

        public Criteria andBusinessModeLessThan(Integer value) {
            addCriterion("business_mode <", value, "businessMode");
            return (Criteria) this;
        }

        public Criteria andBusinessModeLessThanOrEqualTo(Integer value) {
            addCriterion("business_mode <=", value, "businessMode");
            return (Criteria) this;
        }

        public Criteria andBusinessModeIn(List<Integer> values) {
            addCriterion("business_mode in", values, "businessMode");
            return (Criteria) this;
        }

        public Criteria andBusinessModeNotIn(List<Integer> values) {
            addCriterion("business_mode not in", values, "businessMode");
            return (Criteria) this;
        }

        public Criteria andBusinessModeBetween(Integer value1, Integer value2) {
            addCriterion("business_mode between", value1, value2, "businessMode");
            return (Criteria) this;
        }

        public Criteria andBusinessModeNotBetween(Integer value1, Integer value2) {
            addCriterion("business_mode not between", value1, value2, "businessMode");
            return (Criteria) this;
        }

        public Criteria andRemindStatusIsNull() {
            addCriterion("remind_status is null");
            return (Criteria) this;
        }

        public Criteria andRemindStatusIsNotNull() {
            addCriterion("remind_status is not null");
            return (Criteria) this;
        }

        public Criteria andRemindStatusEqualTo(Integer value) {
            addCriterion("remind_status =", value, "remindStatus");
            return (Criteria) this;
        }

        public Criteria andRemindStatusNotEqualTo(Integer value) {
            addCriterion("remind_status <>", value, "remindStatus");
            return (Criteria) this;
        }

        public Criteria andRemindStatusGreaterThan(Integer value) {
            addCriterion("remind_status >", value, "remindStatus");
            return (Criteria) this;
        }

        public Criteria andRemindStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("remind_status >=", value, "remindStatus");
            return (Criteria) this;
        }

        public Criteria andRemindStatusLessThan(Integer value) {
            addCriterion("remind_status <", value, "remindStatus");
            return (Criteria) this;
        }

        public Criteria andRemindStatusLessThanOrEqualTo(Integer value) {
            addCriterion("remind_status <=", value, "remindStatus");
            return (Criteria) this;
        }

        public Criteria andRemindStatusIn(List<Integer> values) {
            addCriterion("remind_status in", values, "remindStatus");
            return (Criteria) this;
        }

        public Criteria andRemindStatusNotIn(List<Integer> values) {
            addCriterion("remind_status not in", values, "remindStatus");
            return (Criteria) this;
        }

        public Criteria andRemindStatusBetween(Integer value1, Integer value2) {
            addCriterion("remind_status between", value1, value2, "remindStatus");
            return (Criteria) this;
        }

        public Criteria andRemindStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("remind_status not between", value1, value2, "remindStatus");
            return (Criteria) this;
        }

        public Criteria andRemindTimeIsNull() {
            addCriterion("remind_time is null");
            return (Criteria) this;
        }

        public Criteria andRemindTimeIsNotNull() {
            addCriterion("remind_time is not null");
            return (Criteria) this;
        }

        public Criteria andRemindTimeEqualTo(Date value) {
            addCriterion("remind_time =", value, "remindTime");
            return (Criteria) this;
        }

        public Criteria andRemindTimeNotEqualTo(Date value) {
            addCriterion("remind_time <>", value, "remindTime");
            return (Criteria) this;
        }

        public Criteria andRemindTimeGreaterThan(Date value) {
            addCriterion("remind_time >", value, "remindTime");
            return (Criteria) this;
        }

        public Criteria andRemindTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("remind_time >=", value, "remindTime");
            return (Criteria) this;
        }

        public Criteria andRemindTimeLessThan(Date value) {
            addCriterion("remind_time <", value, "remindTime");
            return (Criteria) this;
        }

        public Criteria andRemindTimeLessThanOrEqualTo(Date value) {
            addCriterion("remind_time <=", value, "remindTime");
            return (Criteria) this;
        }

        public Criteria andRemindTimeIn(List<Date> values) {
            addCriterion("remind_time in", values, "remindTime");
            return (Criteria) this;
        }

        public Criteria andRemindTimeNotIn(List<Date> values) {
            addCriterion("remind_time not in", values, "remindTime");
            return (Criteria) this;
        }

        public Criteria andRemindTimeBetween(Date value1, Date value2) {
            addCriterion("remind_time between", value1, value2, "remindTime");
            return (Criteria) this;
        }

        public Criteria andRemindTimeNotBetween(Date value1, Date value2) {
            addCriterion("remind_time not between", value1, value2, "remindTime");
            return (Criteria) this;
        }

        public Criteria andRemarksIsNull() {
            addCriterion("remarks is null");
            return (Criteria) this;
        }

        public Criteria andRemarksIsNotNull() {
            addCriterion("remarks is not null");
            return (Criteria) this;
        }

        public Criteria andRemarksEqualTo(String value) {
            addCriterion("remarks =", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksNotEqualTo(String value) {
            addCriterion("remarks <>", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksGreaterThan(String value) {
            addCriterion("remarks >", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksGreaterThanOrEqualTo(String value) {
            addCriterion("remarks >=", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksLessThan(String value) {
            addCriterion("remarks <", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksLessThanOrEqualTo(String value) {
            addCriterion("remarks <=", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksLike(String value) {
            addCriterion("remarks like", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksNotLike(String value) {
            addCriterion("remarks not like", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksIn(List<String> values) {
            addCriterion("remarks in", values, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksNotIn(List<String> values) {
            addCriterion("remarks not in", values, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksBetween(String value1, String value2) {
            addCriterion("remarks between", value1, value2, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksNotBetween(String value1, String value2) {
            addCriterion("remarks not between", value1, value2, "remarks");
            return (Criteria) this;
        }

        public Criteria andApplyBindIsNull() {
            addCriterion("apply_bind is null");
            return (Criteria) this;
        }

        public Criteria andApplyBindIsNotNull() {
            addCriterion("apply_bind is not null");
            return (Criteria) this;
        }

        public Criteria andApplyBindEqualTo(Integer value) {
            addCriterion("apply_bind =", value, "applyBind");
            return (Criteria) this;
        }

        public Criteria andApplyBindNotEqualTo(Integer value) {
            addCriterion("apply_bind <>", value, "applyBind");
            return (Criteria) this;
        }

        public Criteria andApplyBindGreaterThan(Integer value) {
            addCriterion("apply_bind >", value, "applyBind");
            return (Criteria) this;
        }

        public Criteria andApplyBindGreaterThanOrEqualTo(Integer value) {
            addCriterion("apply_bind >=", value, "applyBind");
            return (Criteria) this;
        }

        public Criteria andApplyBindLessThan(Integer value) {
            addCriterion("apply_bind <", value, "applyBind");
            return (Criteria) this;
        }

        public Criteria andApplyBindLessThanOrEqualTo(Integer value) {
            addCriterion("apply_bind <=", value, "applyBind");
            return (Criteria) this;
        }

        public Criteria andApplyBindIn(List<Integer> values) {
            addCriterion("apply_bind in", values, "applyBind");
            return (Criteria) this;
        }

        public Criteria andApplyBindNotIn(List<Integer> values) {
            addCriterion("apply_bind not in", values, "applyBind");
            return (Criteria) this;
        }

        public Criteria andApplyBindBetween(Integer value1, Integer value2) {
            addCriterion("apply_bind between", value1, value2, "applyBind");
            return (Criteria) this;
        }

        public Criteria andApplyBindNotBetween(Integer value1, Integer value2) {
            addCriterion("apply_bind not between", value1, value2, "applyBind");
            return (Criteria) this;
        }

        public Criteria andPettyIdIsNull() {
            addCriterion("petty_id is null");
            return (Criteria) this;
        }

        public Criteria andPettyIdIsNotNull() {
            addCriterion("petty_id is not null");
            return (Criteria) this;
        }

        public Criteria andPettyIdEqualTo(String value) {
            addCriterion("petty_id =", value, "pettyId");
            return (Criteria) this;
        }

        public Criteria andPettyIdNotEqualTo(String value) {
            addCriterion("petty_id <>", value, "pettyId");
            return (Criteria) this;
        }

        public Criteria andPettyIdGreaterThan(String value) {
            addCriterion("petty_id >", value, "pettyId");
            return (Criteria) this;
        }

        public Criteria andPettyIdGreaterThanOrEqualTo(String value) {
            addCriterion("petty_id >=", value, "pettyId");
            return (Criteria) this;
        }

        public Criteria andPettyIdLessThan(String value) {
            addCriterion("petty_id <", value, "pettyId");
            return (Criteria) this;
        }

        public Criteria andPettyIdLessThanOrEqualTo(String value) {
            addCriterion("petty_id <=", value, "pettyId");
            return (Criteria) this;
        }

        public Criteria andPettyIdLike(String value) {
            addCriterion("petty_id like", value, "pettyId");
            return (Criteria) this;
        }

        public Criteria andPettyIdNotLike(String value) {
            addCriterion("petty_id not like", value, "pettyId");
            return (Criteria) this;
        }

        public Criteria andPettyIdIn(List<String> values) {
            addCriterion("petty_id in", values, "pettyId");
            return (Criteria) this;
        }

        public Criteria andPettyIdNotIn(List<String> values) {
            addCriterion("petty_id not in", values, "pettyId");
            return (Criteria) this;
        }

        public Criteria andPettyIdBetween(String value1, String value2) {
            addCriterion("petty_id between", value1, value2, "pettyId");
            return (Criteria) this;
        }

        public Criteria andPettyIdNotBetween(String value1, String value2) {
            addCriterion("petty_id not between", value1, value2, "pettyId");
            return (Criteria) this;
        }

        public Criteria andPaymentPurposeIsNull() {
            addCriterion("payment_purpose is null");
            return (Criteria) this;
        }

        public Criteria andPaymentPurposeIsNotNull() {
            addCriterion("payment_purpose is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentPurposeEqualTo(String value) {
            addCriterion("payment_purpose =", value, "paymentPurpose");
            return (Criteria) this;
        }

        public Criteria andPaymentPurposeNotEqualTo(String value) {
            addCriterion("payment_purpose <>", value, "paymentPurpose");
            return (Criteria) this;
        }

        public Criteria andPaymentPurposeGreaterThan(String value) {
            addCriterion("payment_purpose >", value, "paymentPurpose");
            return (Criteria) this;
        }

        public Criteria andPaymentPurposeGreaterThanOrEqualTo(String value) {
            addCriterion("payment_purpose >=", value, "paymentPurpose");
            return (Criteria) this;
        }

        public Criteria andPaymentPurposeLessThan(String value) {
            addCriterion("payment_purpose <", value, "paymentPurpose");
            return (Criteria) this;
        }

        public Criteria andPaymentPurposeLessThanOrEqualTo(String value) {
            addCriterion("payment_purpose <=", value, "paymentPurpose");
            return (Criteria) this;
        }

        public Criteria andPaymentPurposeLike(String value) {
            addCriterion("payment_purpose like", value, "paymentPurpose");
            return (Criteria) this;
        }

        public Criteria andPaymentPurposeNotLike(String value) {
            addCriterion("payment_purpose not like", value, "paymentPurpose");
            return (Criteria) this;
        }

        public Criteria andPaymentPurposeIn(List<String> values) {
            addCriterion("payment_purpose in", values, "paymentPurpose");
            return (Criteria) this;
        }

        public Criteria andPaymentPurposeNotIn(List<String> values) {
            addCriterion("payment_purpose not in", values, "paymentPurpose");
            return (Criteria) this;
        }

        public Criteria andPaymentPurposeBetween(String value1, String value2) {
            addCriterion("payment_purpose between", value1, value2, "paymentPurpose");
            return (Criteria) this;
        }

        public Criteria andPaymentPurposeNotBetween(String value1, String value2) {
            addCriterion("payment_purpose not between", value1, value2, "paymentPurpose");
            return (Criteria) this;
        }

        public Criteria andBankAccountAcctNameIsNull() {
            addCriterion("bank_account_acct_name is null");
            return (Criteria) this;
        }

        public Criteria andBankAccountAcctNameIsNotNull() {
            addCriterion("bank_account_acct_name is not null");
            return (Criteria) this;
        }

        public Criteria andBankAccountAcctNameEqualTo(String value) {
            addCriterion("bank_account_acct_name =", value, "bankAccountAcctName");
            return (Criteria) this;
        }

        public Criteria andBankAccountAcctNameNotEqualTo(String value) {
            addCriterion("bank_account_acct_name <>", value, "bankAccountAcctName");
            return (Criteria) this;
        }

        public Criteria andBankAccountAcctNameGreaterThan(String value) {
            addCriterion("bank_account_acct_name >", value, "bankAccountAcctName");
            return (Criteria) this;
        }

        public Criteria andBankAccountAcctNameGreaterThanOrEqualTo(String value) {
            addCriterion("bank_account_acct_name >=", value, "bankAccountAcctName");
            return (Criteria) this;
        }

        public Criteria andBankAccountAcctNameLessThan(String value) {
            addCriterion("bank_account_acct_name <", value, "bankAccountAcctName");
            return (Criteria) this;
        }

        public Criteria andBankAccountAcctNameLessThanOrEqualTo(String value) {
            addCriterion("bank_account_acct_name <=", value, "bankAccountAcctName");
            return (Criteria) this;
        }

        public Criteria andBankAccountAcctNameLike(String value) {
            addCriterion("bank_account_acct_name like", value, "bankAccountAcctName");
            return (Criteria) this;
        }

        public Criteria andBankAccountAcctNameNotLike(String value) {
            addCriterion("bank_account_acct_name not like", value, "bankAccountAcctName");
            return (Criteria) this;
        }

        public Criteria andBankAccountAcctNameIn(List<String> values) {
            addCriterion("bank_account_acct_name in", values, "bankAccountAcctName");
            return (Criteria) this;
        }

        public Criteria andBankAccountAcctNameNotIn(List<String> values) {
            addCriterion("bank_account_acct_name not in", values, "bankAccountAcctName");
            return (Criteria) this;
        }

        public Criteria andBankAccountAcctNameBetween(String value1, String value2) {
            addCriterion("bank_account_acct_name between", value1, value2, "bankAccountAcctName");
            return (Criteria) this;
        }

        public Criteria andBankAccountAcctNameNotBetween(String value1, String value2) {
            addCriterion("bank_account_acct_name not between", value1, value2, "bankAccountAcctName");
            return (Criteria) this;
        }

        public Criteria andCompanyAccountIdIsNull() {
            addCriterion("company_account_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyAccountIdIsNotNull() {
            addCriterion("company_account_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyAccountIdEqualTo(String value) {
            addCriterion("company_account_id =", value, "companyAccountId");
            return (Criteria) this;
        }

        public Criteria andCompanyAccountIdNotEqualTo(String value) {
            addCriterion("company_account_id <>", value, "companyAccountId");
            return (Criteria) this;
        }

        public Criteria andCompanyAccountIdGreaterThan(String value) {
            addCriterion("company_account_id >", value, "companyAccountId");
            return (Criteria) this;
        }

        public Criteria andCompanyAccountIdGreaterThanOrEqualTo(String value) {
            addCriterion("company_account_id >=", value, "companyAccountId");
            return (Criteria) this;
        }

        public Criteria andCompanyAccountIdLessThan(String value) {
            addCriterion("company_account_id <", value, "companyAccountId");
            return (Criteria) this;
        }

        public Criteria andCompanyAccountIdLessThanOrEqualTo(String value) {
            addCriterion("company_account_id <=", value, "companyAccountId");
            return (Criteria) this;
        }

        public Criteria andCompanyAccountIdLike(String value) {
            addCriterion("company_account_id like", value, "companyAccountId");
            return (Criteria) this;
        }

        public Criteria andCompanyAccountIdNotLike(String value) {
            addCriterion("company_account_id not like", value, "companyAccountId");
            return (Criteria) this;
        }

        public Criteria andCompanyAccountIdIn(List<String> values) {
            addCriterion("company_account_id in", values, "companyAccountId");
            return (Criteria) this;
        }

        public Criteria andCompanyAccountIdNotIn(List<String> values) {
            addCriterion("company_account_id not in", values, "companyAccountId");
            return (Criteria) this;
        }

        public Criteria andCompanyAccountIdBetween(String value1, String value2) {
            addCriterion("company_account_id between", value1, value2, "companyAccountId");
            return (Criteria) this;
        }

        public Criteria andCompanyAccountIdNotBetween(String value1, String value2) {
            addCriterion("company_account_id not between", value1, value2, "companyAccountId");
            return (Criteria) this;
        }

        public Criteria andRootOrderIdIsNull() {
            addCriterion("root_order_id is null");
            return (Criteria) this;
        }

        public Criteria andRootOrderIdIsNotNull() {
            addCriterion("root_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andRootOrderIdEqualTo(String value) {
            addCriterion("root_order_id =", value, "rootOrderId");
            return (Criteria) this;
        }

        public Criteria andRootOrderIdNotEqualTo(String value) {
            addCriterion("root_order_id <>", value, "rootOrderId");
            return (Criteria) this;
        }

        public Criteria andRootOrderIdGreaterThan(String value) {
            addCriterion("root_order_id >", value, "rootOrderId");
            return (Criteria) this;
        }

        public Criteria andRootOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("root_order_id >=", value, "rootOrderId");
            return (Criteria) this;
        }

        public Criteria andRootOrderIdLessThan(String value) {
            addCriterion("root_order_id <", value, "rootOrderId");
            return (Criteria) this;
        }

        public Criteria andRootOrderIdLessThanOrEqualTo(String value) {
            addCriterion("root_order_id <=", value, "rootOrderId");
            return (Criteria) this;
        }

        public Criteria andRootOrderIdLike(String value) {
            addCriterion("root_order_id like", value, "rootOrderId");
            return (Criteria) this;
        }

        public Criteria andRootOrderIdNotLike(String value) {
            addCriterion("root_order_id not like", value, "rootOrderId");
            return (Criteria) this;
        }

        public Criteria andRootOrderIdIn(List<String> values) {
            addCriterion("root_order_id in", values, "rootOrderId");
            return (Criteria) this;
        }

        public Criteria andRootOrderIdNotIn(List<String> values) {
            addCriterion("root_order_id not in", values, "rootOrderId");
            return (Criteria) this;
        }

        public Criteria andRootOrderIdBetween(String value1, String value2) {
            addCriterion("root_order_id between", value1, value2, "rootOrderId");
            return (Criteria) this;
        }

        public Criteria andRootOrderIdNotBetween(String value1, String value2) {
            addCriterion("root_order_id not between", value1, value2, "rootOrderId");
            return (Criteria) this;
        }

        public Criteria andPayBackStatusIsNull() {
            addCriterion("pay_back_status is null");
            return (Criteria) this;
        }

        public Criteria andPayBackStatusIsNotNull() {
            addCriterion("pay_back_status is not null");
            return (Criteria) this;
        }

        public Criteria andPayBackStatusEqualTo(Integer value) {
            addCriterion("pay_back_status =", value, "payBackStatus");
            return (Criteria) this;
        }

        public Criteria andPayBackStatusNotEqualTo(Integer value) {
            addCriterion("pay_back_status <>", value, "payBackStatus");
            return (Criteria) this;
        }

        public Criteria andPayBackStatusGreaterThan(Integer value) {
            addCriterion("pay_back_status >", value, "payBackStatus");
            return (Criteria) this;
        }

        public Criteria andPayBackStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("pay_back_status >=", value, "payBackStatus");
            return (Criteria) this;
        }

        public Criteria andPayBackStatusLessThan(Integer value) {
            addCriterion("pay_back_status <", value, "payBackStatus");
            return (Criteria) this;
        }

        public Criteria andPayBackStatusLessThanOrEqualTo(Integer value) {
            addCriterion("pay_back_status <=", value, "payBackStatus");
            return (Criteria) this;
        }

        public Criteria andPayBackStatusIn(List<Integer> values) {
            addCriterion("pay_back_status in", values, "payBackStatus");
            return (Criteria) this;
        }

        public Criteria andPayBackStatusNotIn(List<Integer> values) {
            addCriterion("pay_back_status not in", values, "payBackStatus");
            return (Criteria) this;
        }

        public Criteria andPayBackStatusBetween(Integer value1, Integer value2) {
            addCriterion("pay_back_status between", value1, value2, "payBackStatus");
            return (Criteria) this;
        }

        public Criteria andPayBackStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("pay_back_status not between", value1, value2, "payBackStatus");
            return (Criteria) this;
        }

        public Criteria andPersonalTotalPayIsNull() {
            addCriterion("personal_total_pay is null");
            return (Criteria) this;
        }

        public Criteria andPersonalTotalPayIsNotNull() {
            addCriterion("personal_total_pay is not null");
            return (Criteria) this;
        }

        public Criteria andPersonalTotalPayEqualTo(BigDecimal value) {
            addCriterion("personal_total_pay =", value, "personalTotalPay");
            return (Criteria) this;
        }

        public Criteria andPersonalTotalPayNotEqualTo(BigDecimal value) {
            addCriterion("personal_total_pay <>", value, "personalTotalPay");
            return (Criteria) this;
        }

        public Criteria andPersonalTotalPayGreaterThan(BigDecimal value) {
            addCriterion("personal_total_pay >", value, "personalTotalPay");
            return (Criteria) this;
        }

        public Criteria andPersonalTotalPayGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("personal_total_pay >=", value, "personalTotalPay");
            return (Criteria) this;
        }

        public Criteria andPersonalTotalPayLessThan(BigDecimal value) {
            addCriterion("personal_total_pay <", value, "personalTotalPay");
            return (Criteria) this;
        }

        public Criteria andPersonalTotalPayLessThanOrEqualTo(BigDecimal value) {
            addCriterion("personal_total_pay <=", value, "personalTotalPay");
            return (Criteria) this;
        }

        public Criteria andPersonalTotalPayIn(List<BigDecimal> values) {
            addCriterion("personal_total_pay in", values, "personalTotalPay");
            return (Criteria) this;
        }

        public Criteria andPersonalTotalPayNotIn(List<BigDecimal> values) {
            addCriterion("personal_total_pay not in", values, "personalTotalPay");
            return (Criteria) this;
        }

        public Criteria andPersonalTotalPayBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("personal_total_pay between", value1, value2, "personalTotalPay");
            return (Criteria) this;
        }

        public Criteria andPersonalTotalPayNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("personal_total_pay not between", value1, value2, "personalTotalPay");
            return (Criteria) this;
        }

        public Criteria andThirdPaymentPriceIsNull() {
            addCriterion("third_payment_price is null");
            return (Criteria) this;
        }

        public Criteria andThirdPaymentPriceIsNotNull() {
            addCriterion("third_payment_price is not null");
            return (Criteria) this;
        }

        public Criteria andThirdPaymentPriceEqualTo(BigDecimal value) {
            addCriterion("third_payment_price =", value, "thirdPaymentPrice");
            return (Criteria) this;
        }

        public Criteria andThirdPaymentPriceNotEqualTo(BigDecimal value) {
            addCriterion("third_payment_price <>", value, "thirdPaymentPrice");
            return (Criteria) this;
        }

        public Criteria andThirdPaymentPriceGreaterThan(BigDecimal value) {
            addCriterion("third_payment_price >", value, "thirdPaymentPrice");
            return (Criteria) this;
        }

        public Criteria andThirdPaymentPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("third_payment_price >=", value, "thirdPaymentPrice");
            return (Criteria) this;
        }

        public Criteria andThirdPaymentPriceLessThan(BigDecimal value) {
            addCriterion("third_payment_price <", value, "thirdPaymentPrice");
            return (Criteria) this;
        }

        public Criteria andThirdPaymentPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("third_payment_price <=", value, "thirdPaymentPrice");
            return (Criteria) this;
        }

        public Criteria andThirdPaymentPriceIn(List<BigDecimal> values) {
            addCriterion("third_payment_price in", values, "thirdPaymentPrice");
            return (Criteria) this;
        }

        public Criteria andThirdPaymentPriceNotIn(List<BigDecimal> values) {
            addCriterion("third_payment_price not in", values, "thirdPaymentPrice");
            return (Criteria) this;
        }

        public Criteria andThirdPaymentPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("third_payment_price between", value1, value2, "thirdPaymentPrice");
            return (Criteria) this;
        }

        public Criteria andThirdPaymentPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("third_payment_price not between", value1, value2, "thirdPaymentPrice");
            return (Criteria) this;
        }

        public Criteria andThirdPaymentChannelIsNull() {
            addCriterion("third_payment_channel is null");
            return (Criteria) this;
        }

        public Criteria andThirdPaymentChannelIsNotNull() {
            addCriterion("third_payment_channel is not null");
            return (Criteria) this;
        }

        public Criteria andThirdPaymentChannelEqualTo(String value) {
            addCriterion("third_payment_channel =", value, "thirdPaymentChannel");
            return (Criteria) this;
        }

        public Criteria andThirdPaymentChannelNotEqualTo(String value) {
            addCriterion("third_payment_channel <>", value, "thirdPaymentChannel");
            return (Criteria) this;
        }

        public Criteria andThirdPaymentChannelGreaterThan(String value) {
            addCriterion("third_payment_channel >", value, "thirdPaymentChannel");
            return (Criteria) this;
        }

        public Criteria andThirdPaymentChannelGreaterThanOrEqualTo(String value) {
            addCriterion("third_payment_channel >=", value, "thirdPaymentChannel");
            return (Criteria) this;
        }

        public Criteria andThirdPaymentChannelLessThan(String value) {
            addCriterion("third_payment_channel <", value, "thirdPaymentChannel");
            return (Criteria) this;
        }

        public Criteria andThirdPaymentChannelLessThanOrEqualTo(String value) {
            addCriterion("third_payment_channel <=", value, "thirdPaymentChannel");
            return (Criteria) this;
        }

        public Criteria andThirdPaymentChannelLike(String value) {
            addCriterion("third_payment_channel like", value, "thirdPaymentChannel");
            return (Criteria) this;
        }

        public Criteria andThirdPaymentChannelNotLike(String value) {
            addCriterion("third_payment_channel not like", value, "thirdPaymentChannel");
            return (Criteria) this;
        }

        public Criteria andThirdPaymentChannelIn(List<String> values) {
            addCriterion("third_payment_channel in", values, "thirdPaymentChannel");
            return (Criteria) this;
        }

        public Criteria andThirdPaymentChannelNotIn(List<String> values) {
            addCriterion("third_payment_channel not in", values, "thirdPaymentChannel");
            return (Criteria) this;
        }

        public Criteria andThirdPaymentChannelBetween(String value1, String value2) {
            addCriterion("third_payment_channel between", value1, value2, "thirdPaymentChannel");
            return (Criteria) this;
        }

        public Criteria andThirdPaymentChannelNotBetween(String value1, String value2) {
            addCriterion("third_payment_channel not between", value1, value2, "thirdPaymentChannel");
            return (Criteria) this;
        }

        public Criteria andExpiredTimeIsNull() {
            addCriterion("expired_time is null");
            return (Criteria) this;
        }

        public Criteria andExpiredTimeIsNotNull() {
            addCriterion("expired_time is not null");
            return (Criteria) this;
        }

        public Criteria andExpiredTimeEqualTo(Date value) {
            addCriterion("expired_time =", value, "expiredTime");
            return (Criteria) this;
        }

        public Criteria andExpiredTimeNotEqualTo(Date value) {
            addCriterion("expired_time <>", value, "expiredTime");
            return (Criteria) this;
        }

        public Criteria andExpiredTimeGreaterThan(Date value) {
            addCriterion("expired_time >", value, "expiredTime");
            return (Criteria) this;
        }

        public Criteria andExpiredTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("expired_time >=", value, "expiredTime");
            return (Criteria) this;
        }

        public Criteria andExpiredTimeLessThan(Date value) {
            addCriterion("expired_time <", value, "expiredTime");
            return (Criteria) this;
        }

        public Criteria andExpiredTimeLessThanOrEqualTo(Date value) {
            addCriterion("expired_time <=", value, "expiredTime");
            return (Criteria) this;
        }

        public Criteria andExpiredTimeIn(List<Date> values) {
            addCriterion("expired_time in", values, "expiredTime");
            return (Criteria) this;
        }

        public Criteria andExpiredTimeNotIn(List<Date> values) {
            addCriterion("expired_time not in", values, "expiredTime");
            return (Criteria) this;
        }

        public Criteria andExpiredTimeBetween(Date value1, Date value2) {
            addCriterion("expired_time between", value1, value2, "expiredTime");
            return (Criteria) this;
        }

        public Criteria andExpiredTimeNotBetween(Date value1, Date value2) {
            addCriterion("expired_time not between", value1, value2, "expiredTime");
            return (Criteria) this;
        }

        public Criteria andReserveTimeIsNull() {
            addCriterion("reserve_time is null");
            return (Criteria) this;
        }

        public Criteria andReserveTimeIsNotNull() {
            addCriterion("reserve_time is not null");
            return (Criteria) this;
        }

        public Criteria andReserveTimeEqualTo(Date value) {
            addCriterion("reserve_time =", value, "reserveTime");
            return (Criteria) this;
        }

        public Criteria andReserveTimeNotEqualTo(Date value) {
            addCriterion("reserve_time <>", value, "reserveTime");
            return (Criteria) this;
        }

        public Criteria andReserveTimeGreaterThan(Date value) {
            addCriterion("reserve_time >", value, "reserveTime");
            return (Criteria) this;
        }

        public Criteria andReserveTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("reserve_time >=", value, "reserveTime");
            return (Criteria) this;
        }

        public Criteria andReserveTimeLessThan(Date value) {
            addCriterion("reserve_time <", value, "reserveTime");
            return (Criteria) this;
        }

        public Criteria andReserveTimeLessThanOrEqualTo(Date value) {
            addCriterion("reserve_time <=", value, "reserveTime");
            return (Criteria) this;
        }

        public Criteria andReserveTimeIn(List<Date> values) {
            addCriterion("reserve_time in", values, "reserveTime");
            return (Criteria) this;
        }

        public Criteria andReserveTimeNotIn(List<Date> values) {
            addCriterion("reserve_time not in", values, "reserveTime");
            return (Criteria) this;
        }

        public Criteria andReserveTimeBetween(Date value1, Date value2) {
            addCriterion("reserve_time between", value1, value2, "reserveTime");
            return (Criteria) this;
        }

        public Criteria andReserveTimeNotBetween(Date value1, Date value2) {
            addCriterion("reserve_time not between", value1, value2, "reserveTime");
            return (Criteria) this;
        }

        public Criteria andInvoiceBackStatusIsNull() {
            addCriterion("invoice_back_status is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceBackStatusIsNotNull() {
            addCriterion("invoice_back_status is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceBackStatusEqualTo(Integer value) {
            addCriterion("invoice_back_status =", value, "invoiceBackStatus");
            return (Criteria) this;
        }

        public Criteria andInvoiceBackStatusNotEqualTo(Integer value) {
            addCriterion("invoice_back_status <>", value, "invoiceBackStatus");
            return (Criteria) this;
        }

        public Criteria andInvoiceBackStatusGreaterThan(Integer value) {
            addCriterion("invoice_back_status >", value, "invoiceBackStatus");
            return (Criteria) this;
        }

        public Criteria andInvoiceBackStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("invoice_back_status >=", value, "invoiceBackStatus");
            return (Criteria) this;
        }

        public Criteria andInvoiceBackStatusLessThan(Integer value) {
            addCriterion("invoice_back_status <", value, "invoiceBackStatus");
            return (Criteria) this;
        }

        public Criteria andInvoiceBackStatusLessThanOrEqualTo(Integer value) {
            addCriterion("invoice_back_status <=", value, "invoiceBackStatus");
            return (Criteria) this;
        }

        public Criteria andInvoiceBackStatusIn(List<Integer> values) {
            addCriterion("invoice_back_status in", values, "invoiceBackStatus");
            return (Criteria) this;
        }

        public Criteria andInvoiceBackStatusNotIn(List<Integer> values) {
            addCriterion("invoice_back_status not in", values, "invoiceBackStatus");
            return (Criteria) this;
        }

        public Criteria andInvoiceBackStatusBetween(Integer value1, Integer value2) {
            addCriterion("invoice_back_status between", value1, value2, "invoiceBackStatus");
            return (Criteria) this;
        }

        public Criteria andInvoiceBackStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("invoice_back_status not between", value1, value2, "invoiceBackStatus");
            return (Criteria) this;
        }

        public Criteria andInvoiceBackTimeIsNull() {
            addCriterion("invoice_back_time is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceBackTimeIsNotNull() {
            addCriterion("invoice_back_time is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceBackTimeEqualTo(Date value) {
            addCriterion("invoice_back_time =", value, "invoiceBackTime");
            return (Criteria) this;
        }

        public Criteria andInvoiceBackTimeNotEqualTo(Date value) {
            addCriterion("invoice_back_time <>", value, "invoiceBackTime");
            return (Criteria) this;
        }

        public Criteria andInvoiceBackTimeGreaterThan(Date value) {
            addCriterion("invoice_back_time >", value, "invoiceBackTime");
            return (Criteria) this;
        }

        public Criteria andInvoiceBackTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("invoice_back_time >=", value, "invoiceBackTime");
            return (Criteria) this;
        }

        public Criteria andInvoiceBackTimeLessThan(Date value) {
            addCriterion("invoice_back_time <", value, "invoiceBackTime");
            return (Criteria) this;
        }

        public Criteria andInvoiceBackTimeLessThanOrEqualTo(Date value) {
            addCriterion("invoice_back_time <=", value, "invoiceBackTime");
            return (Criteria) this;
        }

        public Criteria andInvoiceBackTimeIn(List<Date> values) {
            addCriterion("invoice_back_time in", values, "invoiceBackTime");
            return (Criteria) this;
        }

        public Criteria andInvoiceBackTimeNotIn(List<Date> values) {
            addCriterion("invoice_back_time not in", values, "invoiceBackTime");
            return (Criteria) this;
        }

        public Criteria andInvoiceBackTimeBetween(Date value1, Date value2) {
            addCriterion("invoice_back_time between", value1, value2, "invoiceBackTime");
            return (Criteria) this;
        }

        public Criteria andInvoiceBackTimeNotBetween(Date value1, Date value2) {
            addCriterion("invoice_back_time not between", value1, value2, "invoiceBackTime");
            return (Criteria) this;
        }

        public Criteria andDirectAcctTypeIsNull() {
            addCriterion("direct_acct_type is null");
            return (Criteria) this;
        }

        public Criteria andDirectAcctTypeIsNotNull() {
            addCriterion("direct_acct_type is not null");
            return (Criteria) this;
        }

        public Criteria andDirectAcctTypeEqualTo(Integer value) {
            addCriterion("direct_acct_type =", value, "directAcctType");
            return (Criteria) this;
        }

        public Criteria andDirectAcctTypeNotEqualTo(Integer value) {
            addCriterion("direct_acct_type <>", value, "directAcctType");
            return (Criteria) this;
        }

        public Criteria andDirectAcctTypeGreaterThan(Integer value) {
            addCriterion("direct_acct_type >", value, "directAcctType");
            return (Criteria) this;
        }

        public Criteria andDirectAcctTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("direct_acct_type >=", value, "directAcctType");
            return (Criteria) this;
        }

        public Criteria andDirectAcctTypeLessThan(Integer value) {
            addCriterion("direct_acct_type <", value, "directAcctType");
            return (Criteria) this;
        }

        public Criteria andDirectAcctTypeLessThanOrEqualTo(Integer value) {
            addCriterion("direct_acct_type <=", value, "directAcctType");
            return (Criteria) this;
        }

        public Criteria andDirectAcctTypeIn(List<Integer> values) {
            addCriterion("direct_acct_type in", values, "directAcctType");
            return (Criteria) this;
        }

        public Criteria andDirectAcctTypeNotIn(List<Integer> values) {
            addCriterion("direct_acct_type not in", values, "directAcctType");
            return (Criteria) this;
        }

        public Criteria andDirectAcctTypeBetween(Integer value1, Integer value2) {
            addCriterion("direct_acct_type between", value1, value2, "directAcctType");
            return (Criteria) this;
        }

        public Criteria andDirectAcctTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("direct_acct_type not between", value1, value2, "directAcctType");
            return (Criteria) this;
        }

        public Criteria andBankPayTypeIsNull() {
            addCriterion("bank_pay_type is null");
            return (Criteria) this;
        }

        public Criteria andBankPayTypeIsNotNull() {
            addCriterion("bank_pay_type is not null");
            return (Criteria) this;
        }

        public Criteria andBankPayTypeEqualTo(String value) {
            addCriterion("bank_pay_type =", value, "bankPayType");
            return (Criteria) this;
        }

        public Criteria andBankPayTypeNotEqualTo(String value) {
            addCriterion("bank_pay_type <>", value, "bankPayType");
            return (Criteria) this;
        }

        public Criteria andBankPayTypeGreaterThan(String value) {
            addCriterion("bank_pay_type >", value, "bankPayType");
            return (Criteria) this;
        }

        public Criteria andBankPayTypeGreaterThanOrEqualTo(String value) {
            addCriterion("bank_pay_type >=", value, "bankPayType");
            return (Criteria) this;
        }

        public Criteria andBankPayTypeLessThan(String value) {
            addCriterion("bank_pay_type <", value, "bankPayType");
            return (Criteria) this;
        }

        public Criteria andBankPayTypeLessThanOrEqualTo(String value) {
            addCriterion("bank_pay_type <=", value, "bankPayType");
            return (Criteria) this;
        }

        public Criteria andBankPayTypeLike(String value) {
            addCriterion("bank_pay_type like", value, "bankPayType");
            return (Criteria) this;
        }

        public Criteria andBankPayTypeNotLike(String value) {
            addCriterion("bank_pay_type not like", value, "bankPayType");
            return (Criteria) this;
        }

        public Criteria andBankPayTypeIn(List<String> values) {
            addCriterion("bank_pay_type in", values, "bankPayType");
            return (Criteria) this;
        }

        public Criteria andBankPayTypeNotIn(List<String> values) {
            addCriterion("bank_pay_type not in", values, "bankPayType");
            return (Criteria) this;
        }

        public Criteria andBankPayTypeBetween(String value1, String value2) {
            addCriterion("bank_pay_type between", value1, value2, "bankPayType");
            return (Criteria) this;
        }

        public Criteria andBankPayTypeNotBetween(String value1, String value2) {
            addCriterion("bank_pay_type not between", value1, value2, "bankPayType");
            return (Criteria) this;
        }

        public Criteria andBankPayPriceIsNull() {
            addCriterion("bank_pay_price is null");
            return (Criteria) this;
        }

        public Criteria andBankPayPriceIsNotNull() {
            addCriterion("bank_pay_price is not null");
            return (Criteria) this;
        }

        public Criteria andBankPayPriceEqualTo(BigDecimal value) {
            addCriterion("bank_pay_price =", value, "bankPayPrice");
            return (Criteria) this;
        }

        public Criteria andBankPayPriceNotEqualTo(BigDecimal value) {
            addCriterion("bank_pay_price <>", value, "bankPayPrice");
            return (Criteria) this;
        }

        public Criteria andBankPayPriceGreaterThan(BigDecimal value) {
            addCriterion("bank_pay_price >", value, "bankPayPrice");
            return (Criteria) this;
        }

        public Criteria andBankPayPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("bank_pay_price >=", value, "bankPayPrice");
            return (Criteria) this;
        }

        public Criteria andBankPayPriceLessThan(BigDecimal value) {
            addCriterion("bank_pay_price <", value, "bankPayPrice");
            return (Criteria) this;
        }

        public Criteria andBankPayPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("bank_pay_price <=", value, "bankPayPrice");
            return (Criteria) this;
        }

        public Criteria andBankPayPriceIn(List<BigDecimal> values) {
            addCriterion("bank_pay_price in", values, "bankPayPrice");
            return (Criteria) this;
        }

        public Criteria andBankPayPriceNotIn(List<BigDecimal> values) {
            addCriterion("bank_pay_price not in", values, "bankPayPrice");
            return (Criteria) this;
        }

        public Criteria andBankPayPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("bank_pay_price between", value1, value2, "bankPayPrice");
            return (Criteria) this;
        }

        public Criteria andBankPayPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("bank_pay_price not between", value1, value2, "bankPayPrice");
            return (Criteria) this;
        }

        public Criteria andSupplierOrderNoIsNull() {
            addCriterion("supplier_order_no is null");
            return (Criteria) this;
        }

        public Criteria andSupplierOrderNoIsNotNull() {
            addCriterion("supplier_order_no is not null");
            return (Criteria) this;
        }

        public Criteria andSupplierOrderNoEqualTo(String value) {
            addCriterion("supplier_order_no =", value, "supplierOrderNo");
            return (Criteria) this;
        }

        public Criteria andSupplierOrderNoNotEqualTo(String value) {
            addCriterion("supplier_order_no <>", value, "supplierOrderNo");
            return (Criteria) this;
        }

        public Criteria andSupplierOrderNoGreaterThan(String value) {
            addCriterion("supplier_order_no >", value, "supplierOrderNo");
            return (Criteria) this;
        }

        public Criteria andSupplierOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("supplier_order_no >=", value, "supplierOrderNo");
            return (Criteria) this;
        }

        public Criteria andSupplierOrderNoLessThan(String value) {
            addCriterion("supplier_order_no <", value, "supplierOrderNo");
            return (Criteria) this;
        }

        public Criteria andSupplierOrderNoLessThanOrEqualTo(String value) {
            addCriterion("supplier_order_no <=", value, "supplierOrderNo");
            return (Criteria) this;
        }

        public Criteria andSupplierOrderNoLike(String value) {
            addCriterion("supplier_order_no like", value, "supplierOrderNo");
            return (Criteria) this;
        }

        public Criteria andSupplierOrderNoNotLike(String value) {
            addCriterion("supplier_order_no not like", value, "supplierOrderNo");
            return (Criteria) this;
        }

        public Criteria andSupplierOrderNoIn(List<String> values) {
            addCriterion("supplier_order_no in", values, "supplierOrderNo");
            return (Criteria) this;
        }

        public Criteria andSupplierOrderNoNotIn(List<String> values) {
            addCriterion("supplier_order_no not in", values, "supplierOrderNo");
            return (Criteria) this;
        }

        public Criteria andSupplierOrderNoBetween(String value1, String value2) {
            addCriterion("supplier_order_no between", value1, value2, "supplierOrderNo");
            return (Criteria) this;
        }

        public Criteria andSupplierOrderNoNotBetween(String value1, String value2) {
            addCriterion("supplier_order_no not between", value1, value2, "supplierOrderNo");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table tb_bank_order
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table tb_bank_order
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}