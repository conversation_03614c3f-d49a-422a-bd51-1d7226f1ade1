package com.fenbeitong.saas.core.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.fenbeitong.common.utils.basis.StringUtil;
import com.fenbeitong.common.utils.basis.VCUtil;
import com.fenbeitong.common.utils.json.JsonUtils;
import com.fenbeitong.finhub.common.constant.CategoryTypeEnum;
import com.fenbeitong.finhub.common.constant.LogOperateActionEnum;
import com.fenbeitong.finhub.common.constant.LogOperateObjectEnum;
import com.fenbeitong.finhub.common.constant.LogOperatePageEnum;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.kafka.msg.uc.KafkaCompanyLogMsg;
import com.fenbeitong.saas.api.model.dto.order.check.TicketOpType;
import com.fenbeitong.saas.api.model.dto.template.rule.ControlOrderRuleDto;
import com.fenbeitong.saas.core.common.SaasHttpContext;
import com.fenbeitong.saas.core.common.base.SaasException;
import com.fenbeitong.saas.core.common.constant.AirRuleConstant;
import com.fenbeitong.saas.core.common.constant.CategorySubType;
import com.fenbeitong.saas.core.common.constant.GlobalResponseCode;
import com.fenbeitong.saas.core.common.constant.NumberConstant;
import com.fenbeitong.saas.core.contract.applyflow.KvContract;
import com.fenbeitong.saas.core.contract.biz.train.TrainInfo;
import com.fenbeitong.saas.core.contract.customrole.EmployeeListContract;
import com.fenbeitong.saas.core.contract.order.check.AirInterceptRecordContract;
import com.fenbeitong.saas.core.contract.order.check.AirOrderCheckReqContract;
import com.fenbeitong.saas.core.contract.order.check.TravelOnBusiOrderCheckReqContract;
import com.fenbeitong.saas.core.contract.organization.AllEmployeeOfCompanyContract;
import com.fenbeitong.saas.core.contract.rule.*;
import com.fenbeitong.saas.core.contract.setup.MessageSetupVO;
import com.fenbeitong.saas.core.contract.user.EmployeeInfoContract;
import com.fenbeitong.saas.core.dao.fenbeitong.AirRuleMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.AirTimeRangeExtMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.AirTimeRangeMapper;
import com.fenbeitong.saas.core.language.CoreLanguage;
import com.fenbeitong.saas.core.model.enums.apply.BizType;
import com.fenbeitong.saas.core.model.enums.booking.BookingConfigEnum;
import com.fenbeitong.saas.core.model.enums.booking.BookingTypeEnum;
import com.fenbeitong.saas.core.model.enums.rule.AirPortCityEnum;
import com.fenbeitong.saas.core.model.enums.rule.*;
import com.fenbeitong.saas.core.model.fenbeitong.AirRule;
import com.fenbeitong.saas.core.model.fenbeitong.AirRuleExample;
import com.fenbeitong.saas.core.model.fenbeitong.AirTimeRange;
import com.fenbeitong.saas.core.model.fenbeitong.AirTimeRangeExample;
import com.fenbeitong.saas.core.model.saas.OperateLogRule;
import com.fenbeitong.saas.core.service.*;
import com.fenbeitong.saas.core.service.kafka.CompanyLogKafkaProducerService;
import com.fenbeitong.saas.core.service.rule.air.AirRuleMsg;
import com.fenbeitong.saas.core.service.rule.air.flight.AirFlightRuleCheckReq;
import com.fenbeitong.saas.core.service.rule.air.flight.AirFlightRuleCheckRes;
import com.fenbeitong.saas.core.service.rule.air.flight.book.AirBookCheckChainOrderEnum;
import com.fenbeitong.saas.core.service.rule.air.flight.book.AirFlightBookRuleCheckChainHandler;
import com.fenbeitong.saas.core.service.rule.air.flight.city.Air1ScreenPageConstant;
import com.fenbeitong.saas.core.service.rule.air.flight.city.AirCityCheckChainOrderEnum;
import com.fenbeitong.saas.core.service.rule.air.flight.city.AirFlightCityRuleCheckChainHandler;
import com.fenbeitong.saas.core.service.rule.multi.ConsumeTemplateRuleService;
import com.fenbeitong.saas.core.service.setup.SetupService;
import com.fenbeitong.saas.core.utils.air.AirRuleUtils;
import com.fenbeitong.saas.core.utils.tools.*;
import com.fenbeitong.saasplus.api.model.po.fenbeitong.rulenew.*;
import com.fenbeitong.saasplus.api.service.rule.IRuleV2Service;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.fenbeitong.usercenter.api.model.dto.rule.EmployeeAirRuleDto;
import com.fenbeitong.usercenter.api.model.po.company.Company;
import com.fenbeitong.usercenter.api.model.po.rule.EmployeeAirRule;
import com.fenbeitong.usercenter.api.service.company.ICompanyService;
import com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeAirRuleExtService;
import com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeAirRuleService;
import com.fenbeitong.usercenter.api.service.rule.ICommonEmployeeRuleService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.DateUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.StrUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;


/**
 * Created by xuzn on 17/12/18.
 */
@Slf4j
@Service
public class AirRuleService implements IAirRuleService {

    private static Logger logger = LoggerFactory.getLogger(AirRuleService.class);

    @Autowired
    private AirRuleMapper airRuleMapper;

    @Autowired
    private AirTimeRangeMapper airTimeRangeMapper;

    @Autowired
    private AirTimeRangeExtMapper airTimeRangeExtMapper;

    @Autowired
    IOrganizationService organizationService;

    @Autowired
    private IBaseEmployeeAirRuleExtService iBaseEmployeeAirRuleExtService;

    @Autowired
    private IBaseEmployeeAirRuleService iBaseEmployeeAirRuleService;

    @Autowired
    private IBaseOrganizationService iBaseOrganizationService;

    @Autowired
    private ICommonEmployeeRuleService iCommonEmployeeRuleService;

    @Autowired
    private IOperateLogRuleService operateLogRuleService;

    @Autowired
    private CompanyLogKafkaProducerService companyLogKafkaProducerService;

    @Autowired
    private ICompanyService iCompanyService;

    @Autowired
    private IAirCheckService airCheckService;

    @Autowired
    private IRuleService ruleService;

    @Autowired
    private IAirCheckV2Service airCheckV2Service;

    @Autowired
    private IRuleV2Service iRuleV2Service;

    @Autowired
    private SetupService setupService;

    @Autowired
    private com.fenbeitong.saasplus.api.service.messageSetup.IMessageSetupService iMessageSetupRpcService;

    @Autowired
    private AirFlightBookRuleCheckChainHandler airFlightBookRuleCheckChainHandler;

    @Autowired
    private AirFlightCityRuleCheckChainHandler airFlightCityRuleCheckChainHandler;

    @Autowired
    private ConsumeTemplateRuleService consumeTemplateRuleService;

    @Override
    public String addAirRule(AirRuleContract airRuleContract, String companyId) throws SaasException {
        AirRuleExample airRuleExample = new AirRuleExample();
        airRuleExample.createCriteria().andNameEqualTo(airRuleContract.getName()).andCompanyIdEqualTo(companyId);
        List<AirRule> airRuleList = airRuleMapper.selectByExample(airRuleExample);
        if (airRuleList.size() > 0) {
            throw new SaasException(GlobalResponseCode.SameRuleName);
        }
        //机票允许订的舱位类型, 1:"商务/头等舱",3:"经济舱"
        List<Integer> airCabinTypes = airRuleContract.getAirCabinTypes();
        if (CollectionUtils.isEmpty(airCabinTypes)) {
            throw new SaasException(GlobalResponseCode.TravelSelectError);
        }
        String airCabinTypeString = StringUtils.join(airCabinTypes, ",");
        String id = IDTool.CreateUniqueID();
        AirRule airRule = new AirRule();
        airRule.setId(id);
        airRule.setCompanyId(companyId);
        airRule.setName(airRuleContract.getName());
        airRule.setAirCabinFlag(airRuleContract.getAirCabinFlag());
        airRule.setAirCabinType(airCabinTypeString);
        airRule.setAirDiscount(airRuleContract.getAirDiscount());
        airRule.setAirDiscountFlag(airRuleContract.getAirDiscountFlag());
        airRule.setAirPriceFlag(airRuleContract.getAirPriceFlag());
        airRule.setAirUnitPrice(airRuleContract.getAirUnitPrice());
        airRule.setLowPriceFlag(null==airRuleContract.getLowPriceFlag()?0:airRuleContract.getLowPriceFlag());
        airRule.setFilterStopoverFlightFlag(airRuleContract.getFilterStopoverFlightFlag());
        airRule.setTimeConsumeFlag(null == airRuleContract.getTimeConsumeFlag() ? false : airRuleContract.getTimeConsumeFlag());
        if (airRule.getTimeConsumeFlag()) {
            if (ObjectUtil.isNull(airRuleContract.getTimeConsumeValue()) || airRuleContract.getTimeConsumeValue() < NumberConstant.ZERO
                    || airRuleContract.getTimeConsumeValue() > NumberConstant.ONE_BILLION) {
                logger.warn("param time consume value: {}", airRuleContract.getTimeConsumeValue());
                throw new FinhubException(GlobalResponseCode.AirRuleParamTimeError.getCode(),
                        GlobalResponseCode.AirRuleParamTimeError.getMsg());
            }
            airRule.setTimeConsumeValue(airRuleContract.getTimeConsumeValue());
        }
        airRule.setDistanceLimitFlag(null == airRuleContract.getDistanceLimitFlag() ? false : airRuleContract.getDistanceLimitFlag());
        if (airRule.getDistanceLimitFlag()) {
            if (ObjectUtil.isNull(airRuleContract.getDistanceLimitValue()) || airRuleContract.getDistanceLimitValue() < NumberConstant.ZERO
                    || airRuleContract.getDistanceLimitValue() > NumberConstant.ONE_BILLION) {
                logger.warn("param distance limit value: {}", airRuleContract.getDistanceLimitValue());
                throw new FinhubException(GlobalResponseCode.AirRuleParamDistanceError.getCode(),
                        GlobalResponseCode.AirRuleParamDistanceError.getMsg());
            }
            airRule.setDistanceLimitValue(airRuleContract.getDistanceLimitValue());
        }
        if(airRule.getLowPriceFlag()>0){
            airRule.setLowPriceTime(airRuleContract.getLowPriceTime());
        }else {
            airRule.setLowPriceTime(0);
        }
        if (airRuleContract.getPrivDayMin() != null) {
            airRule.setPrivDayMin(airRuleContract.getPrivDayMin());
            if (airRuleContract.getPrivDayMax()!=null) {
                if (airRuleContract.getPrivDayMin() > airRuleContract.getPrivDayMax()) {
                    throw new SaasException(GlobalResponseCode.RulePrivDayIsNull);
                } else {
                    airRule.setPrivDayMax(airRuleContract.getPrivDayMax());
                }
            }
        }
        airRule.setModifyTime(new Date());
        //调用插入方法
        airRuleMapper.insertSelective(airRule);
        rangeList(airRuleContract, id);
        //添加规则日志
        addOperateLogRule(companyId,id, SaasHttpContext.getUserId() , JSON.toJSONString(airRuleContract));
        return id;
    }

    private void addOperateLogRule(String companyId,String ruleId,String userId,String operateContent){
        OperateLogRule operateLogRule=new OperateLogRule();
        operateLogRule.setCompanyId(companyId);
        operateLogRule.setBusinessType(CategoryTypeEnum.Air.getCode());
        operateLogRule.setRuleId(ruleId);
        operateLogRule.setOperateType(1);
        operateLogRule.setCreateUser(userId);
        operateLogRule.setOperateContent(operateContent);
        operateLogRuleService.addOperateLogRule(operateLogRule);
    }

    //添加修改公共方法

    public void rangeList(AirRuleContract airRuleContract, String ruleId) {

        List<List<AirRuleContract.AirTimeRanges>> timeRangeList = airRuleContract.getTimeRangeList();

        if (CollectionUtils.isNotEmpty(timeRangeList)) {
            //遍历外层
            for (List<AirRuleContract.AirTimeRanges> timeRanges : timeRangeList) {
                Long batchId = System.currentTimeMillis();
                //遍历内层
                for (AirRuleContract.AirTimeRanges airTimeRange : timeRanges) {
                    AirTimeRange airTime = new AirTimeRange();
                    airTime.setRuleId(ruleId);
                    airTime.setDayType(airTimeRange.getDayType());
                    airTime.setIsOvernight(airTimeRange.getIsOvernight());
                    airTime.setBeginTime(DateUtils.parse(airTimeRange.getBeginTime(), "HH:mm"));
                    airTime.setEndTime(DateUtils.parse(airTimeRange.getEndTime(), "HH:mm"));
                    airTime.setBatchId(batchId);
                    airTimeRangeMapper.insertSelective(airTime);
                }
            }
        }
    }

    @Override
    public String updateAirRule(AirRuleContract airRuleContract, String companyId) throws SaasException {
        List<Integer> airCabinTypes = airRuleContract.getAirCabinTypes();
        if (CollectionUtils.isEmpty(airCabinTypes)) {
            throw new SaasException(GlobalResponseCode.TravelSelectError);
        }
        AirRuleExample airRuleExample = new AirRuleExample();
        airRuleExample.createCriteria().andNameEqualTo(airRuleContract.getName()).andIdNotEqualTo(airRuleContract.getAirRuleId()).andCompanyIdEqualTo(companyId);
        List<AirRule> airRuleList = airRuleMapper.selectByExample(airRuleExample);
        if (airRuleList.size() > 0) {
            throw new SaasException(GlobalResponseCode.SameRuleName);
        }
        String ruleId = airRuleContract.getAirRuleId();
        AirRule airRule = airRuleMapper.selectByPrimaryKey(ruleId);
        airRule.setCompanyId(companyId);
        airRule.setName(airRuleContract.getName());
        String airCabinTypeString = StringUtils.join(airCabinTypes, ",");
        airRule.setAirCabinFlag(airRuleContract.getAirCabinFlag());
        airRule.setAirCabinType(airCabinTypeString);
        airRule.setAirDiscount(airRuleContract.getAirDiscount());
        airRule.setAirDiscountFlag(airRuleContract.getAirDiscountFlag());
        airRule.setAirPriceFlag(airRuleContract.getAirPriceFlag());
        airRule.setAirUnitPrice(airRuleContract.getAirUnitPrice());
        airRule.setLowPriceFlag(null==airRuleContract.getLowPriceFlag()?0:airRuleContract.getLowPriceFlag());
        airRule.setFilterStopoverFlightFlag(airRuleContract.getFilterStopoverFlightFlag());
        airRule.setTimeConsumeFlag(null == airRuleContract.getTimeConsumeFlag() ? false : airRuleContract.getTimeConsumeFlag());
        if (airRule.getTimeConsumeFlag()) {
            if (ObjectUtil.isNull(airRuleContract.getTimeConsumeValue()) || airRuleContract.getTimeConsumeValue() < NumberConstant.ZERO
                    || airRuleContract.getTimeConsumeValue() > NumberConstant.ONE_BILLION) {
                logger.warn("param time consume value: {}", airRuleContract.getTimeConsumeValue());
                throw new FinhubException(GlobalResponseCode.AirRuleParamTimeError.getCode(),
                        GlobalResponseCode.AirRuleParamTimeError.getMsg());
            }
            airRule.setTimeConsumeValue(airRuleContract.getTimeConsumeValue());
        }
        airRule.setDistanceLimitFlag(null == airRuleContract.getDistanceLimitFlag() ? false : airRuleContract.getDistanceLimitFlag());
        if (airRule.getDistanceLimitFlag()) {
            if (ObjectUtil.isNull(airRuleContract.getDistanceLimitValue()) || airRuleContract.getDistanceLimitValue() < NumberConstant.ZERO
                    || airRuleContract.getDistanceLimitValue() > NumberConstant.ONE_BILLION) {
                logger.warn("param distance limit value: {}", airRuleContract.getDistanceLimitValue());
                throw new FinhubException(GlobalResponseCode.AirRuleParamDistanceError.getCode(),
                        GlobalResponseCode.AirRuleParamDistanceError.getMsg());
            }
            airRule.setDistanceLimitValue(airRuleContract.getDistanceLimitValue());
        }
        if(airRule.getLowPriceFlag()>0){
            airRule.setLowPriceTime(airRuleContract.getLowPriceTime());
        }else {
            airRule.setLowPriceTime(0);
        }

        if (airRuleContract.getPrivDayMin() != null) {
            airRule.setPrivDayMin(airRuleContract.getPrivDayMin());
        } else {
            airRule.setPrivDayMin(null);
        }
        if (airRuleContract.getPrivDayMax() != null) {
            if (airRuleContract.getPrivDayMin() != null) {
                if (airRuleContract.getPrivDayMin() > airRuleContract.getPrivDayMax()) {
                    throw new SaasException(GlobalResponseCode.RulePrivDayIsNull);
                } else {
                    airRule.setPrivDayMax(airRuleContract.getPrivDayMax());
                }
            } else {
                airRule.setPrivDayMax(airRuleContract.getPrivDayMax());
            }
        } else {
            airRule.setPrivDayMax(null);
        }
        airRule.setModifyTime(new Date());
        airRuleMapper.updateByPrimaryKey(airRule);

        //删除airTimeRange表中的数据
        AirTimeRangeExample example = new AirTimeRangeExample();
        example.createCriteria().andRuleIdEqualTo(ruleId);
        airTimeRangeMapper.deleteByExample(example);
        //调用插入方法
        rangeList(airRuleContract, ruleId);
        return ruleId;
    }


    /**
     * 删除规则
     *
     * @param deleteContract
     * @param companyId
     * @param clientVersion
     * @throws SaasException
     */
    @Override
    public void deleteAirRule(RuleDeleteContract deleteContract, String companyId, String userId, String clientVersion) throws SaasException {
        // 查询机票是否开启超规个人付
        AirManagerSetting airManagerSetting = iMessageSetupRpcService.queryAirRuleExceedConfigMessage(companyId);
        if (airManagerSetting != null && Objects.equals(airManagerSetting.getExceedConfigLever(), 2)) {
            if (StringUtils.isNotBlank(clientVersion) && VersionTool.lessThan(clientVersion, "5.1.10")) {
                throw new FinhubException(GlobalResponseCode.ApplyCenterAlert.getCode(),
                        GlobalResponseCode.UpdateAirRuleVersionError.getType(),
                        GlobalResponseCode.UpdateAirRuleVersionError.getMsg(),
                        GlobalResponseCode.UpdateAirRuleVersionError.getTitle());
            }
        }
        List<String> ruleList = deleteContract.getRule_ids();

        //查询规则ID是否存在
        AirRuleExample airRuleExample = new AirRuleExample();
        airRuleExample.createCriteria().andCompanyIdEqualTo(companyId)
                .andIdIn(ruleList);
        if (deleteContract.getRule_ids().size() != airRuleMapper.countByExample(airRuleExample)) {
            throw new SaasException(GlobalResponseCode.RuleIdNotExistInCompany);
        }

        for (String ruleId : ruleList) {
            iBaseEmployeeAirRuleExtService.updateManualRuleIdByRuleId(companyId,userId,ruleId);
        }
        // 删除之前先查询，为了推送操作日志消息拿数据
        AirRule airRule = airRuleMapper.selectByPrimaryKey(ruleList.get(0));
        AirRuleExample airRuleDeleteExample = new AirRuleExample();
        airRuleDeleteExample.createCriteria().andCompanyIdEqualTo(companyId).andIdIn(ruleList);
        airRuleMapper.deleteByExample(airRuleDeleteExample);
        if (ruleList.size() == 1) {
            companyLogKafkaProducerService.sendCompanyLogMsg(getKafkaCompanyLogMsg(LogOperateActionEnum.DELETE, airRule.getName()));
        } else {
            companyLogKafkaProducerService.sendCompanyLogMsg(getKafkaCompanyLogMsg(LogOperateActionEnum.BATCH_DELETE, ""));
        }
    }

    /**
     * 查询机票规则详情
     *
     * @param companyId
     * @param id
     * @return
     * @throws SaasException
     */
    @Override
    public AirRuleListContract queryAirRuleDetail(String companyId, String id) throws SaasException {


        if (StringUtils.isBlank(companyId) || id == null) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        AirRuleExample airRuleExample = new AirRuleExample();
        airRuleExample.createCriteria().andCompanyIdEqualTo(companyId).andIdEqualTo(id);
        List<AirRule> airRuleList = airRuleMapper.selectByExample(airRuleExample);
        // 处理NPE
        AirRule airRule = airRuleList.stream().findFirst().orElseThrow(() -> new SaasException(GlobalResponseCode.RuleIdNotExistInCompany));

        AirRuleListContract airRuleListContract = new AirRuleListContract();
        airRuleListContract.setAirRuleId(id);
        airRuleListContract.setAirCabinFlag(airRule.getAirCabinFlag());
        airRuleListContract.setName(airRule.getName());
        airRuleListContract.setAirDiscount(airRule.getAirDiscount());
        airRuleListContract.setAirDiscountFlag(airRule.getAirDiscountFlag());
        airRuleListContract.setAirUnitPrice(airRule.getAirUnitPrice());
        airRuleListContract.setAirPriceFlag(airRule.getAirPriceFlag());
        airRuleListContract.setPrivDayMin(airRule.getPrivDayMin());
        airRuleListContract.setPrivDayMax(airRule.getPrivDayMax());
        airRuleListContract.setModifyTime(0L);
        airRuleListContract.setLowPriceFlag(airRule.getLowPriceFlag());
        airRuleListContract.setFilterStopoverFlightFlag(airRule.getFilterStopoverFlightFlag());
        airRuleListContract.setTimeConsumeFlag(airRule.getTimeConsumeFlag());
        if (airRuleListContract.getTimeConsumeFlag()) {
            airRuleListContract.setTimeConsumeValue(airRule.getTimeConsumeValue());
        }
        airRuleListContract.setDistanceLimitFlag(airRule.getDistanceLimitFlag());
        if (airRuleListContract.getDistanceLimitFlag()) {
            airRuleListContract.setDistanceLimitValue(airRule.getDistanceLimitValue());
        }
        if(airRule.getLowPriceFlag() > 0){
            airRuleListContract.setLowPriceTime(airRule.getLowPriceTime());
        }else {
            airRuleListContract.setLowPriceTime(0);
        }
        //ji型
        if (airRule.getAirCabinFlag()) {

            List<KvContract> airCabinTypes = Lists.newArrayList();
            String[] allowAirCabinTypes = airRule.getAirCabinType().split(",");
            for (String airType : allowAirCabinTypes) {
                airCabinTypes.add(new KvContract(ObjUtils.toInteger(airType), AirCabinType.getName(ObjUtils.toInteger(airType))));
            }
            airRuleListContract.setAirCabinTypes(airCabinTypes);
        }


        AirTimeRangeExample airTimeRangeExample = new AirTimeRangeExample();
        //id是规则id
        airTimeRangeExample.createCriteria().andRuleIdEqualTo(id);

        //获取机票时间列表
        List<AirTimeRangeContract> airRangeList = airTimeRangeExtMapper.airRangeList(id);

        List<List<AirTimeRangeContract>> result = new ArrayList<List<AirTimeRangeContract>>();
        Map<Long, List<AirTimeRangeContract>> map = new TreeMap<Long, List<AirTimeRangeContract>>();

        for (AirTimeRangeContract bean : airRangeList) {
            if (map.containsKey(bean.getBatchId())) {
                List<AirTimeRangeContract> t = map.get(bean.getBatchId());
                t.add(new AirTimeRangeContract(bean.getDayType(), bean.getBeginTime(), bean.getEndTime(), bean.getIsOvernight(), bean.getBatchId()));
                new ArrayList<AirTimeRangeContract>().add(new AirTimeRangeContract(bean.getDayType(), bean.getBeginTime(), bean.getEndTime(), bean.getIsOvernight(), bean.getBatchId()));
                map.put(bean.getBatchId(), t);
            } else {
                List<AirTimeRangeContract> t = new ArrayList<AirTimeRangeContract>();
                t.add(new AirTimeRangeContract(bean.getDayType(), bean.getBeginTime(), bean.getEndTime(), bean.getIsOvernight(), bean.getBatchId()));
                map.put(bean.getBatchId(), t);
            }
        }
        for (Map.Entry<Long, List<AirTimeRangeContract>> entry : map.entrySet()) {
            result.add(entry.getValue());
        }

        airRuleListContract.setTimeRangeList(result);

        Integer companyEmployeeCount = iBaseEmployeeAirRuleExtService.queryEmployeeAirRuleCount(airRule.getId());
        airRuleListContract.setEmployeeCount(companyEmployeeCount);
        Company company = iCompanyService.queryCompanyById(companyId);
        AirRuleListContract.CompanyInfo companyInfo = new AirRuleListContract.CompanyInfo(company.getId(), company.getName());

        airRuleListContract.setCompanyInfo(companyInfo);
        return airRuleListContract;
    }


    /**
     * 查询机票退票规则详情
     *
     * @param companyId
     * @param ruleId
     * @return
     * @throws SaasException
     */
    @Override
    public AirRefundRuleVo queryAirRefundRuleDetail(String companyId, String ruleId) {
        AirRefundRuleVo res = new AirRefundRuleVo();
        logger.info("ruleId={}, companyId={}", ruleId, companyId);
        if (StringUtils.isBlank(companyId) || StringUtils.isBlank(ruleId)) {
            logger.info("params are null");
            return res;
        }

        AirRuleV2 airRuleV2 = iRuleV2Service.getAirRuleV2ById(ruleId, companyId);
        logger.info("airRuleV2:{}", JsonUtils.toJsonStr(airRuleV2));

        if (Objects.isNull(airRuleV2)
                || Objects.isNull(airRuleV2.getAirBookConditionGroup())
                || Objects.isNull(airRuleV2.getAirBookConditionGroup().getAirRuleRefundGroup())) {
            logger.info("No air refund rule found.");
            return res;
        }

        AirRuleRefundGroup airRuleRefundGroup = airRuleV2.getAirBookConditionGroup().getAirRuleRefundGroup();
        BeanUtils.copyProperties(airRuleRefundGroup, res);
        return res;
    }

    @Override
    public List<AirRuleListContract> queryAirRuleList(String companyId, String ruleName, Boolean simpleQuery) throws SaasException {


        List<AirRuleListContract> airRuleListContractList = Lists.newArrayList();
        AirRuleExample airRuleExample = new AirRuleExample();
        if (StringUtils.isNotBlank(ruleName)) {
            airRuleExample.createCriteria().andCompanyIdEqualTo(companyId).andNameLike("%" + ruleName + "%");
        } else {
            airRuleExample.createCriteria().andCompanyIdEqualTo(companyId);
        }
        airRuleExample.setOrderByClause("modify_time desc");


        List<AirRule> airRuleList = airRuleMapper.selectByExample(airRuleExample);

        if (CollectionUtils.isEmpty(airRuleList)) {
            return Lists.newArrayList();
        }
        if (simpleQuery) {
            for (AirRule airRule : airRuleList) {
                AirRuleListContract airRuleListContract = new AirRuleListContract();
                airRuleListContract.setAirRuleId(airRule.getId());
                airRuleListContract.setName(airRule.getName());
                airRuleListContractList.add(airRuleListContract);
            }
            return airRuleListContractList;
        }
        Company company = iCompanyService.queryCompanyById(companyId);
        AirRuleListContract.CompanyInfo companyInfo = new AirRuleListContract.CompanyInfo(company.getId(),company.getName());

        List<String> ruleIds = airRuleList.stream().map(airRule -> airRule.getId()).collect(Collectors.toList());
        List<AirTimeRangeContract> allAirRangeList = airTimeRangeExtMapper.airRangeListByIds(ruleIds);
        Map<String, List<AirTimeRangeContract>> airRangeMap = Maps.newHashMap();
        for (AirTimeRangeContract airTimeRangeContract : allAirRangeList) {
            String ruleId = airTimeRangeContract.getRuleId();
            List<AirTimeRangeContract> airRangeList;
            if (airRangeMap.containsKey(ruleId)) {
                airRangeList = airRangeMap.get(ruleId);
            } else {
                airRangeList = Lists.newArrayList();
                airRangeMap.put(ruleId, airRangeList);
            }
            airRangeList.add(airTimeRangeContract);
        }
        Map<String, Integer> countMap = iCommonEmployeeRuleService.queryRuleConfigEmployeeCount(companyId, BizType.Air.getValue(), CategorySubType.Air.getSubtype(), ruleIds);
        for (AirRule airRule : airRuleList) {
            AirRuleListContract airRuleListContract = new AirRuleListContract();
            airRuleListContract.setAirRuleId(airRule.getId());
            airRuleListContract.setAirCabinFlag(airRule.getAirCabinFlag());
            airRuleListContract.setName(airRule.getName());
            airRuleListContract.setAirDiscount(airRule.getAirDiscount());
            airRuleListContract.setAirDiscountFlag(airRule.getAirDiscountFlag());
            airRuleListContract.setAirUnitPrice(airRule.getAirUnitPrice());
            airRuleListContract.setAirPriceFlag(airRule.getAirPriceFlag());
            airRuleListContract.setPrivDayMin(airRule.getPrivDayMin());
            airRuleListContract.setPrivDayMax(airRule.getPrivDayMax());
            airRuleListContract.setModifyTime(0L);
            airRuleListContract.setLowPriceFlag(airRule.getLowPriceFlag());
            airRuleListContract.setFilterStopoverFlightFlag(airRule.getFilterStopoverFlightFlag());
            airRuleListContract.setTimeConsumeFlag(airRule.getTimeConsumeFlag());
            if (airRuleListContract.getTimeConsumeFlag()) {
                airRuleListContract.setTimeConsumeValue(airRule.getTimeConsumeValue());
            }
            airRuleListContract.setDistanceLimitFlag(airRule.getDistanceLimitFlag());
            if (airRuleListContract.getDistanceLimitFlag()) {
                airRuleListContract.setDistanceLimitValue(airRule.getDistanceLimitValue());
            }
            if(airRule.getLowPriceFlag() >0 ){
                airRuleListContract.setLowPriceTime(airRule.getLowPriceTime());
            }else {
                airRuleListContract.setLowPriceTime(0);
            }
            //ji型
            if (airRule.getAirCabinFlag()) {

                List<KvContract> airCabinTypes = Lists.newArrayList();
                String[] allowAirCabinTypes = airRule.getAirCabinType().split(",");
                for (String airType : allowAirCabinTypes) {
                    airCabinTypes.add(new KvContract(ObjUtils.toInteger(airType), AirCabinType.getName(ObjUtils.toInteger(airType))));
                }
                airRuleListContract.setAirCabinTypes(airCabinTypes);
            }

            //获取机票时间列表
            List<AirTimeRangeContract> airRangeList = Lists.newArrayList();
            if (airRangeMap.containsKey(airRule.getId())) {
                airRangeList = airRangeMap.get(airRule.getId());
            }

            List<List<AirTimeRangeContract>> result = new ArrayList<List<AirTimeRangeContract>>();
            Map<Long, List<AirTimeRangeContract>> map = new TreeMap<Long, List<AirTimeRangeContract>>();

            for (AirTimeRangeContract bean : airRangeList) {
                if (map.containsKey(bean.getBatchId())) {
                    List<AirTimeRangeContract> t = map.get(bean.getBatchId());
                    t.add(new AirTimeRangeContract(bean.getDayType(), bean.getBeginTime(), bean.getEndTime(), bean.getIsOvernight(), bean.getBatchId()));
                    new ArrayList<AirTimeRangeContract>().add(new AirTimeRangeContract(bean.getDayType(), bean.getBeginTime(), bean.getEndTime(), bean.getIsOvernight(), bean.getBatchId()));
                    map.put(bean.getBatchId(), t);
                } else {
                    List<AirTimeRangeContract> t = new ArrayList<AirTimeRangeContract>();
                    t.add(new AirTimeRangeContract(bean.getDayType(), bean.getBeginTime(), bean.getEndTime(), bean.getIsOvernight(), bean.getBatchId()));
                    map.put(bean.getBatchId(), t);
                }
            }
            for (Map.Entry<Long, List<AirTimeRangeContract>> entry : map.entrySet()) {
                result.add(entry.getValue());
            }

            airRuleListContract.setTimeRangeList(result);
            Integer companyEmployeeCount = ObjUtils.toInteger(countMap.get(airRule.getId()), 0);
            airRuleListContract.setEmployeeCount(companyEmployeeCount);

            airRuleListContract.setCompanyInfo(companyInfo);
            airRuleListContractList.add(airRuleListContract);
        }
        return airRuleListContractList;
    }

    @Override
    public EmployeeListContract matchAirRuleEmployeeWithRuleId(String companyId, String ruleId, Integer pageIndex, Integer pageSize) {
        return genAirRuleEmployeeInfoContracts(companyId, ruleId, pageIndex, pageSize);
    }


    /**
     * 获取用餐规则关联的员工信息
     */
    private EmployeeListContract genAirRuleEmployeeInfoContracts(String companyId, String airRuleId, Integer pageIndex, Integer pageSize) {
        EmployeeListContract employeeListContract = new EmployeeListContract();
        employeeListContract.setPageIndex(pageIndex);
        employeeListContract.setPageSize(pageSize);
        EmployeeAirRuleDto employeeAirRuleDto = iBaseEmployeeAirRuleExtService.selectEmployeeAirRuleList(companyId, airRuleId, pageIndex, pageSize);
        employeeListContract.setTotalCount(employeeAirRuleDto.getCount());
        List<EmployeeInfoContract> employees = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(employeeAirRuleDto.getEmployeeAirRuleList())) {
            List<EmployeeAirRule> employeeAirRuleList = employeeAirRuleDto.getEmployeeAirRuleList();
            List<String> employeeIds = employeeAirRuleList.stream().map(
                    employeeMallRule -> employeeMallRule.getEmployee_id()
            ).distinct().collect(Collectors.toList());
            List<EmployeeContract> employeeList = iBaseOrganizationService.getEmployee(employeeIds, companyId);
            employees = employeeList.stream().map(employee ->
                    new EmployeeInfoContract(
                            employee.getId(),
                            employee.getName(),
                            employee.getPhone_num(),
                            employee.getThird_employee_id(),
                            Lists.newArrayList(new AllEmployeeOfCompanyContract.EmployeeListBean.OrgUnitListBean(employee.getOrg_id(), employee.getOrg_name(), employee.getThird_org_id()))
                    )
            ).collect(Collectors.toList());//从公司的所有员工中获取应用改规则的员工信息
        }
        employeeListContract.setEmployees(employees);
        return employeeListContract;
    }

    @Override
    public void fullUpdateEmployee(String companyId, DinnerRuleFullContract dinnerRuleFullContract ,String userId) throws SaasException {
        if (dinnerRuleFullContract == null || StringUtils.isBlank(dinnerRuleFullContract.getId())) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        List<String> fullEmployeeIds = dinnerRuleFullContract.getFullEmployeeIds();
        //查询规则ID是否存在
        AirRuleExample airRuleExample = new AirRuleExample();
        airRuleExample.createCriteria().andCompanyIdEqualTo(companyId).andIdEqualTo(dinnerRuleFullContract.getId());
        if (airRuleMapper.countByExample(airRuleExample) < 1) {
            throw new SaasException(GlobalResponseCode.RuleIdNotExistInCompany);
        }
        iBaseEmployeeAirRuleExtService.updateEmployeeApplyRuleId(companyId,userId,fullEmployeeIds, CategorySubType.Air.getSubtype(),dinnerRuleFullContract.getId());
//            EmployeeAirRuleExample employeeAirRuleExample = new EmployeeAirRuleExample();
//            employeeAirRuleExample.createCriteria().andEmployee_idIn(fullEmployeeIds).andCompany_idEqualTo(companyId);
//            EmployeeAirRule employeeAirRule = new EmployeeAirRule();
//            employeeAirRule.setManual_air_rule_id(dinnerRuleFullContract.getId());
//            employeeAirRule.setAir_rule_flag(true);
//            iBaseEmployeeAirRuleService.updateByExampleSelective(employeeAirRule, employeeAirRuleExample);

    }

    @Override
    public AirRuleAdvanceCheckResultDTO checkAdvanceBookRule(String companyId, String userId, String travelDate) {
        AirRuleAdvanceCheckResultDTO result = new AirRuleAdvanceCheckResultDTO();
        result.setResult(Boolean.TRUE);
        EmployeeAirRule employeeAirRule = iBaseEmployeeAirRuleService.queryEmployeeAirRuleByPrimaryKey(userId, companyId);
        if (Objects.isNull(employeeAirRule)) {
            return result;
        }
        logger.info("员工飞机权限:{}", JsonUtils.toJsonStr(employeeAirRule));
        String ruleId = ObjUtils.ifNull(employeeAirRule.getManual_air_rule_id(), employeeAirRule.getDefault_air_rule_id());
        AirRule airRule = airRuleMapper.selectByPrimaryKey(ruleId);
        if (Objects.isNull(airRule)) {
            return result;
        }
        logger.info("员工机票规则: {}", JsonUtils.toJsonStr(airRule));
        // 旅行时间与预定时间相差天数
        LocalDate currentTime = LocalDate.now();
        LocalDate travelTime = LocalDateTime.ofInstant(DateTimeTool.fromStringToDate(travelDate).toInstant(), ZoneId.systemDefault()).toLocalDate();
        long diffDays = ChronoUnit.DAYS.between(currentTime, travelTime);
        Integer dayMin = airRule.getPrivDayMin();
        if (Objects.nonNull(dayMin) && !Objects.equals(dayMin, 0) && diffDays < dayMin) {
            logger.info(StrUtils.formatString("不满足「最少提前{0}天预订」的限制", dayMin));
            result.setResult(Boolean.FALSE);
            result.setMsg(StrUtils.formatString(CoreLanguage.TrainCheckServiceImpl_Value_MinDay.getMessage(), dayMin));
        }
        Integer dayMax = airRule.getPrivDayMax();
        if (Objects.nonNull(dayMax) && !Objects.equals(dayMax, 0) && diffDays > dayMax) {
            logger.info(StrUtils.formatString("不满足「最多提前{0}天预订」的限制", dayMax));
            result.setResult(Boolean.FALSE);
            result.setMsg(StrUtils.formatString(CoreLanguage.TrainCheckServiceImpl_Value_MaxDay.getMessage(), dayMax));
        }
        return result;
    }

    @Override
    public AirRuleAdvanceCheckResultDTO checkAdvanceBookRuleV2(String companyId, String userId, String travelDate) {
        AirRuleAdvanceCheckResultDTO result = new AirRuleAdvanceCheckResultDTO();
        result.setResult(Boolean.TRUE);
        EmployeeAirRule employeeAirRule = iBaseEmployeeAirRuleService.queryEmployeeAirRuleByPrimaryKey(userId, companyId);
        if (Objects.isNull(employeeAirRule)) {
            return result;
        }
        logger.info("员工飞机权限:{}", JsonUtils.toJsonStr(employeeAirRule));
        String ruleId = ObjUtils.ifNull(employeeAirRule.getManual_air_rule_id(), employeeAirRule.getDefault_air_rule_id());
        AirRuleV2 airRuleV2 = iRuleV2Service.getAirRuleV2ById(ruleId, companyId);
        logger.info("员工机票规则v2: {}", JsonUtils.toJsonStr(airRuleV2));

        if (Objects.isNull(airRuleV2) || Objects.isNull(airRuleV2.getAirBookConditionGroup())
                || Objects.isNull(airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup())) {
            log.info("Objects.isNull(airRuleV2) || Objects.isNull(airRuleV2.getAirBookConditionGroup())  || Objects.isNull(airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup())");
            return result;
        }
        if (!Objects.equals(true, airRuleV2.getAirBookConditionGroup().getAirBookConditionGroupFlag())
                || !Objects.equals(true, airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup().getAirRuleBookGroupFlag())) {
            log.info("组限制没开");
            return result;
        }
        if (Objects.equals(airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup().getPrivDayFlag(), Boolean.TRUE)) {
            // 旅行时间与预定时间相差天数
            LocalDate currentTime = LocalDate.now();
            LocalDate travelTime = LocalDateTime.ofInstant(DateTimeTool.fromStringToDate(travelDate).toInstant(), ZoneId.systemDefault()).toLocalDate();
            long diffDays = ChronoUnit.DAYS.between(currentTime, travelTime);
            Integer dayMin = airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup().getPrivDayMin();
            if (Objects.nonNull(dayMin) && !Objects.equals(dayMin, 0) && diffDays < dayMin) {
                logger.info(StrUtils.formatString("不满足「最少提前{0}天预订」的限制", dayMin));
                result.setResult(Boolean.FALSE);
                result.setMsg(StrUtils.formatString("最少提前{0}天预订", dayMin));
            }
            Integer dayMax = airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup().getPrivDayMax();
            if (Objects.nonNull(dayMax) && !Objects.equals(dayMax, 0) && diffDays > dayMax) {
                logger.info(StrUtils.formatString("不满足「最多提前{0}天预订」的限制", dayMax));
                result.setResult(Boolean.FALSE);
                result.setMsg(StrUtils.formatString("最多提前{0}天预订", dayMax));
            }
            return result;
        }
        return result;

    }

    @Override
    public List<AirRuleCheckRes> checkAirRule(String companyId, String userId, String token, List<AirRuleCheckReq> reqList) {
        // 旧版二屏幕校验是预定的
        return checkAirRule(companyId, userId, token, reqList, TicketOpType.BOOK.getCode(), null);
    }

    @Override
    public List<AirRuleCheckRes> checkAirRule(String companyId, String userId, String token, List<AirRuleCheckReq> reqList, Integer ticketOpType, String clientVersion) {
        logger.info("companyId={},iAir2ScreenCheckV2Service.checkAirRule.reqlist={}, ticketOpType={}, clientVersion={}", companyId, JsonUtils.toJsonStr(reqList), ticketOpType, clientVersion);
        if (TicketOpType.BOOK.equalsTo(ticketOpType)) {
            MessageSetupVO setupVO = setupService.queryBookingConfig(companyId, BookingConfigEnum.AIR);
            if (null != setupVO && BookingTypeEnum.TRAVELER.getType().equals(setupVO.getBookingType())){
                // 出行人模式，直接放行
                logger.info("出行人模式，直接放行");
                return new ArrayList<>(0);
            }

            // 如果开启了个人付且是旧版本，则提示升级
            // 如果是新版app，公司没有升级配置，则前端兼容之前旧版
            AirManagerSetting airManagerSetting = iMessageSetupRpcService.queryAirRuleExceedConfigMessage(companyId);
            Boolean isOpenExceedConfig = airManagerSetting.getIsOpenExceedConfig();
            Integer exceedConfigLever = airManagerSetting.getExceedConfigLever();
            logger.info("机票超规个人付:{}, exceedConfigLever:{}", isOpenExceedConfig, exceedConfigLever);
            if (isOpenExceedConfig && Objects.equals(exceedConfigLever, 2)) { // 机票超规个人付开始
                if (VersionTool.compare(clientVersion, AirRuleConstant.AIR_EXCEED_PERSONAL_PAY_CLIENT_VERSION) < 0) {
                    throw new SaasException(GlobalResponseCode.AirTrainCenterAlert);
                }
                // 超规个人付新版
                return checkAir2ScreenBookRuleV2(companyId, userId, token, reqList, clientVersion);
            }

            // 旧版 2屏校验
            List<AirRuleCheckRes> airRuleCheckRes = checkAir2ScreenBookRule(companyId, userId, token, reqList, clientVersion);
            if (CollectionUtils.isNotEmpty(airRuleCheckRes)) {
                airRuleCheckRes.forEach(airRule -> airRule.setUseVersion(AirRule2ScreenUseVersion.OLD.getCode()));
            }
            return airRuleCheckRes;
        }

        // 本期超规个人付不支持
        // if (TicketOpType.CHANGE.equalsTo(ticketOpType)) {
        //     return checkAir2ScreenChangeRule(companyId, userId, token, reqList);
        // }

        logger.info("非 预定 和改期 二屏幕校验");
        return new ArrayList<>(0);
    }

    /**
     * @MethodName checkAir2ScreenBookRule
     * @Description 预定机票 二屏
     * @param: companyId
     * @param: userId
     * @param: token
     * @param: reqList
     * @param: clientVersion
     * @return: java.util.List<com.fenbeitong.saas.core.contract.rule.AirRuleCheckRes>
     * <AUTHOR> Yunpeng
     * @Date 2022/11/18 11:49
     */
    private List<AirRuleCheckRes> checkAir2ScreenBookRule(String companyId, String userId, String token, List<AirRuleCheckReq> reqList, String clientVersion) {

        List<AirRuleCheckRes> results = Lists.newLinkedList();
        EmployeeAirRule employeeAirRule = iBaseEmployeeAirRuleService.queryEmployeeAirRuleByPrimaryKey(userId, companyId);
        if (Objects.isNull(employeeAirRule)) return results;
        logger.info("员工机票规则: {}", JsonUtils.toJsonStr(employeeAirRule));
        String ruleId = ObjUtils.ifNull(employeeAirRule.getManual_air_rule_id(), employeeAirRule.getDefault_air_rule_id());
        if (StringUtils.isBlank(ruleId)) {
            logger.info("员工机票规则中规则ID为空");
            return null;
        }

        // v1版本机票规则
        AirRule airRule = airRuleMapper.selectByPrimaryKey(ruleId);
        logger.info("v1机票规则: {}", JsonUtils.toJsonStr(airRule));
        if (Objects.isNull(airRule)) {
            logger.info("员工机票规则为空");
            return null;
        }

        reqList = reqList.stream().sorted(Comparator.comparing(AirRuleCheckReq::getPrice)).collect(Collectors.toList());
        // 减少最低价请求次数
        AirRuleCheckReq var = reqList.get(0);
        BigDecimal minPrice = var.getPrice();
        AirRuleCheckResultDetail lowPriceResult = null;
        // 查询火车信息,用于行程距离/耗时中的低价判断
        String trainDate = DateUtils.format(DateUtils.parse(var.getDepTime(), "yyyy-MM-dd HH:mm:ss"), "yyyy-MM-dd");
        TrainInfo trainInfo = airCheckV2Service.getTrainInfo(trainDate, var.getDepCityId(), var.getArrCityId(), token);
        // 适用于同一航班所有舱位的校验规则: 提前预定、起飞时段、行程距离、行程耗时
        AirOrderCheckReqContract commonReqContract = this.buildCommonAirRuleContract(var);
        List<AirRuleCheckResultDetail> commonRuleCheckResults = airCheckService.checkAirCommonRule(airRule, commonReqContract);
        AirRuleCheckResultDetail distanceResult = commonRuleCheckResults.stream().filter(o -> Objects.equals(CoreLanguage.Rule_Value_TripDistance.getMessage(), o.getRuleDesc())).findFirst().orElse(null);
        AirRuleCheckResultDetail timeResult = commonRuleCheckResults.stream().filter(o -> Objects.equals(CoreLanguage.Rule_Value_TripTime.getMessage(), o.getRuleDesc())).findFirst().orElse(null);
        // 对每个舱位进行的规则校验
        for (AirRuleCheckReq req : reqList) {
            AirOrderCheckReqContract reqContract = this.buildAirRuleCheckContract(req, companyId);
            AirRuleCheckRes checkRes = new AirRuleCheckRes();
            checkRes.setCabin(req.getCabin());
            List<AirRuleCheckResultDetail> checkResults = Lists.newArrayList();
            checkResults.add(airCheckService.checkAirCabinRule(airRule, reqContract)); // 舱等
            checkResults.add(airCheckService.checkAirPriceRule(airRule, reqContract)); // 票价
            // 开启低价限制，仅需要拿最低价校验一次，其他价格直接置为超规
            if (minPrice.compareTo(req.getPrice()) == 0) {
                if (Objects.isNull(lowPriceResult)) {
                    if(!Objects.equals(req.getMinPriceFlag(),1)) { //如果前端传递过来选择的的不是最低价，则走最低价校验
                        lowPriceResult = airCheckService.checkAirLowPrice(airRule, reqContract); // 最低价
                    }else{
                        lowPriceResult = new AirRuleCheckResultDetail();
                        lowPriceResult.setRuleDesc(CoreLanguage.Rule_Value_MinPriceLimit.getMessage());
                        lowPriceResult.setRuleResult(Boolean.TRUE);
                    }
                }
                checkResults.add(lowPriceResult);
            } else {
                AirRuleCheckResultDetail defaultLowPriceResult = new AirRuleCheckResultDetail();
                defaultLowPriceResult.setRuleDesc(lowPriceResult.getRuleDesc());
                defaultLowPriceResult.setRuleLimit(lowPriceResult.getRuleLimit());
                if (!Objects.equals(CoreLanguage.Rule_Value_NotLimit.getMessage(), lowPriceResult.getRuleLimit())) {
                    defaultLowPriceResult.setRuleResult(Boolean.FALSE);
                    defaultLowPriceResult.setRuleTag("未预订最低价");
                }
                checkResults.add(defaultLowPriceResult);
            }

            commonRuleCheckResults.forEach(item -> {
                AirRuleCheckResultDetail tmp = new AirRuleCheckResultDetail();
                tmp.setRuleDesc(item.getRuleDesc());
                tmp.setRuleResult(item.getRuleResult());
                tmp.setRuleLimit(item.getRuleLimit());
                tmp.setRuleTag(item.getRuleTag());
                checkResults.add(tmp);
            });

            if (Objects.nonNull(trainInfo) && trainInfo.getMinSeatPrice().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal minSeatPrice = trainInfo.getMinSeatPrice();
                // 行程距离规则中的低价判别
                if (Objects.nonNull(distanceResult) && StringUtils.isNotBlank(distanceResult.getRuleLimit()) && distanceResult.getRuleLimit().contains("含税价")
                        && !Objects.equals(Boolean.TRUE, distanceResult.getRuleResult())) {
                    Integer distanceLowerPrice = airRule.getDistanceLowerPrice();
                    AirRuleCheckResultDetail cabinDistanceResult = checkResults.stream().filter(o -> Objects.equals(CoreLanguage.Rule_Value_TripDistance.getMessage(), o.getRuleDesc())).findFirst().orElse(null);
                    if (minSeatPrice.subtract(BigDecimal.valueOf(distanceLowerPrice)).compareTo(req.getPrice()) > 0) {
                        if (Objects.nonNull(cabinDistanceResult)) {
                            cabinDistanceResult.setRuleResult(Boolean.TRUE);
                            cabinDistanceResult.setRuleTag(null);
                        }
                    } else {
                        if (Objects.nonNull(cabinDistanceResult)) {
                            cabinDistanceResult.setRuleResult(Boolean.FALSE);
                            cabinDistanceResult.setRuleTag("行程距离不符合规则");
                        }
                    }
                }

                // fix bug: https://jira.fenbeijinfu.com/browse/WTPC-1791
                // 行程耗时规则中的低价判断
                // "含税价" 是指配置了价格
                if (Objects.nonNull(timeResult) && StringUtils.isNotBlank(timeResult.getRuleLimit()) && timeResult.getRuleLimit().contains("含税价")
                        && !Objects.equals(Boolean.TRUE, timeResult.getRuleResult())) {
                    // 行程耗时里边如果（耗时配置了，配置了价格）耗时不符合，或者（耗时没配置，配置了价格）RuleResult 为空
                    Integer timeLowerPrice = airRule.getTimeLowerPrice();
                    AirRuleCheckResultDetail cabinTimeResult = checkResults.stream().filter(o -> Objects.equals(CoreLanguage.Rule_Value_TripTime.getMessage(), o.getRuleDesc())).findFirst().orElse(null);
                    boolean isNotPriceExceed = minSeatPrice.subtract(BigDecimal.valueOf(timeLowerPrice)).compareTo(req.getPrice()) > 0;
                    if (Objects.nonNull(cabinTimeResult)) {
                        if (isNotPriceExceed) {
                            // 如果比价不超规
                            cabinTimeResult.setRuleResult(Boolean.TRUE);
                            cabinTimeResult.setRuleTag(null);
                        } else {
                            // 如果比价超规
                            log.info("比价超规");
                            String ruleLimit = StrUtils.formatString("含税价需比火车(高铁/动车)低￥{0}", BigDecimalTool.formatMoney(BigDecimal.valueOf(airRule.getTimeLowerPrice())));
                            AirRuleCheckResultDetail ruleDetailContainConsumeTimeDetail = checkResults.stream()
                                    .filter(o -> Objects.equals(AirRuleConstant.TRAVEL_TIME_RULE, o.getRuleDesc())
                                        && o.getRuleLimit().contains("最短耗时需超过")).findFirst().orElse(null);
                            if (Objects.nonNull(ruleDetailContainConsumeTimeDetail)) {
                                // 如果存在"最短耗时"配置项，那么肯定是不符合的，因为外层判断了!Objects.equals(Boolean.TRUE, timeResult.getRuleResult())
                                cabinTimeResult.setRuleResult(Boolean.FALSE);
                                if (StringUtils.isNotBlank(ruleDetailContainConsumeTimeDetail.getRuleTag())) {
                                    cabinTimeResult.setRuleTag(ruleDetailContainConsumeTimeDetail.getRuleTag()
                                            + "或" + ruleLimit);
                                }
                            } else {
                                cabinTimeResult.setRuleResult(Boolean.FALSE);
                                cabinTimeResult.setRuleTag(ruleLimit);
                            }
                        }
                    }
                    //
                    //
                    // if (minSeatPrice.subtract(BigDecimal.valueOf(timeLowerPrice)).compareTo(req.getPrice()) > 0) {
                    //     if (Objects.nonNull(cabinTimeResult)) {
                    //         cabinTimeResult.setRuleResult(Boolean.TRUE);
                    //         cabinTimeResult.setRuleTag(null);
                    //     }
                    // } else {
                    //     if (Objects.nonNull(cabinTimeResult)) {
                    //         cabinTimeResult.setRuleResult(Boolean.FALSE);
                    //         cabinTimeResult.setRuleTag("行程耗时不符合规则");
                    //     }
                    // }
                }
            }
            checkRes.setAirRuleCheckResults(checkResults);
            results.add(checkRes);
        }
        return results;
    }

    /**
     * @MethodName checkAir2ScreenBookRuleV2
     * @Description 新版（超规个人付）机票二屏预定相关规则校验
     * @param: companyId
     * @param: userId
     * @param: token
     * @param: reqList
     * @return: java.util.List<com.fenbeitong.saas.core.contract.rule.AirRuleCheckRes>
     * <AUTHOR> Yunpeng
     * @Date 2022/11/27 15:55
     */
    private List<AirRuleCheckRes> checkAir2ScreenBookRuleV2(String companyId, String userId, String token, List<AirRuleCheckReq> reqList, String clientVersion){
        EmployeeAirRule employeeAirRule = iBaseEmployeeAirRuleService.queryEmployeeAirRuleByPrimaryKey(userId, companyId);
        if (Objects.isNull(employeeAirRule)) {
            return new ArrayList<>(0);
        }
        logger.info("员工机票规则: {}", JsonUtils.toJsonStr(employeeAirRule));
        String ruleId = ObjUtils.ifNull(employeeAirRule.getManual_air_rule_id(), employeeAirRule.getDefault_air_rule_id());

        // 开启多规则  按照默认员工规则查询二屏
        if (employeeAirRule.getMulti_rule_switch()) {
            ControlOrderRuleDto consumeRule = consumeTemplateRuleService.getConsumeRule("", employeeAirRule.getTemplate_id(), companyId,
                    null, BizType.Air, ConsumeTemplateRuleService.MULTI_RULE_VERSION);
            employeeAirRule.setAir_verify_flag(consumeRule.getIsEnable());
            if (employeeAirRule.getAir_rule_flag()) {
                ruleId = consumeRule.getRuleId();
            }
        }

        if (StringUtils.isBlank(ruleId)) {
            log.info("员工机票规则id为空");
            return new ArrayList<>(0);
        }
        // v2超规个人付新版机票规则查询
        logger.info("v2机票规则req: ruleId={}, companyId={}", ruleId, companyId);
        AirRuleV2 airRuleV2 = iRuleV2Service.getAirRuleV2ById(ruleId, companyId);
        logger.info("v2机票规则rep: {}", JsonUtils.toJsonStr(airRuleV2));
        if (Objects.isNull(airRuleV2)
                || Objects.isNull(airRuleV2.getAirBookConditionGroup())
                || Objects.isNull(airRuleV2.getAirBookConditionGroup().getAirBookConditionGroupFlag())
                || Objects.isNull(airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup())) {
            logger.info("没有配置机票预定相关规则");
            return new ArrayList<>(0);
        }
        if (CollectionUtils.isEmpty(reqList)) {
            logger.info("没有舱位信息");
            return new ArrayList<>(0);
        }

        // 校验结果集
        List<AirRuleCheckRes> res = new ArrayList<>(reqList.size());

        AirRuleUtils.SeatMaxPriceDTO seatMaxPriceDTO = null;
        TrainInfo trainInfo = null;
        // 航班预定相关
        // 预定相关，需要遍历每个航班
        for (AirRuleCheckReq req : reqList) {
            // 城市相关，需要校验多遍：需要校验：同行程下机票机建燃油比火车低xx元
            // 所有规则都需要校验是否符合
            // 若符合则显示符合，不符合显示不符合，没有规则则不显示
            AirFlightRuleCheckReq airCityCheckReq = new AirFlightRuleCheckReq();
            AirFlightRuleCheckReq.FlightInfo flightCityInfo = getFlightInfo(companyId, token, req, airRuleV2);
            airCityCheckReq.setRuleInfo(airRuleV2);
            airCityCheckReq.setRuleId(ruleId);
            airCityCheckReq.setClientVersion(clientVersion);
            airCityCheckReq.setFlightInfo(flightCityInfo);
            if (seatMaxPriceDTO != null) { // 如果 seatMaxPriceDTO 不为空，则加入到城市req里边
                airCityCheckReq.setSeatMaxPriceDTO(seatMaxPriceDTO);
            }
            if (trainInfo != null) {
                airCityCheckReq.setGoTrainInfo(trainInfo);
            }
            AirFlightRuleCheckRes airCityCheckRes = new AirFlightRuleCheckRes();
            logger.info("城市请求参数airFlightRuleCheckReq={}", JsonUtils.toJsonStr(airCityCheckReq));
            airFlightCityRuleCheckChainHandler.executionChain(airCityCheckReq, airCityCheckRes);
            logger.info("城市airFlightRuleCheckRes:{}", JsonUtils.toJsonStr(airCityCheckRes));
            seatMaxPriceDTO = airCityCheckReq.getSeatMaxPriceDTO();
            trainInfo = airCityCheckReq.getGoTrainInfo();
            AirRuleCheckRes airCityRuleCheckRes = new AirRuleCheckRes();
            airCityRuleCheckRes.setAir2ScreenCheckGroups(new ArrayList<>());
            // 城市（起降城市，火车机票联合管控，距离）规则任意一项不超规
            Boolean cityRuleCheckRes = AirRuleUtils.getCityRuleCheck(airCityCheckRes.getPerRuleCheckResList());
            logger.info("cityRuleCheckRes:{}", cityRuleCheckRes);


            boolean isAllConfigCityRuleExceed = !cityRuleCheckRes;

            // 城市相关的规则信息，通过标题映射。eg. <"起降城市", ruleDetailList>
            HashMap<String, List<RuleDetail>> ruleDetailMapByTiTle = new HashMap<>();
            // 遍历城市存在的规则
            // 1 城市条件限制
            // 1.1 起降城市
            // [1] set 规则 到 map
            setTakeoffLandCityRuleDetail(airRuleV2, airCityCheckRes, ruleDetailMapByTiTle);
            String cityGreyTip;
            if (Objects.nonNull(airRuleV2.getCityConditionGroup())
                    && Objects.equals(airRuleV2.getCityConditionGroup().getCityConditionConfig(), CityConditionExceedEnum.EXCEED_NOT_ALLOW.getCode())) {
                cityGreyTip = Air1ScreenPageConstant.TIP_2_CANNOT_ORDER;
            } else {
                cityGreyTip = Air1ScreenPageConstant.TIP_2_NEED_PERSON_PAY;
            }

            // 【2】添加到结果集
            setAirRuleCheckResultDetailV2(airCityRuleCheckRes, ruleDetailMapByTiTle, Air1ScreenPageConstant.RULE_TITLE_TAKEOFF_LAND_CITY,
                    "追加 起降城市 规则", Air1ScreenPageConstant.TITLE_2_CITY_LIMIT, cityGreyTip, "新增 航程距离 规则");

            // 1.2 航程距离
            // 是否配置
            if (Objects.isNull(airRuleV2.getCityConditionGroup())
                    || !Objects.equals(airRuleV2.getCityConditionGroup().getCityConditionGroupFlag(), Boolean.TRUE)
                    || !Objects.equals(airRuleV2.getCityConditionGroup().getDistanceLimitGroup().getDistanceLimitFlag(), Boolean.TRUE)){
                // 没有配置或者不限制
                log.info("航程距离相关规则不限制");
            } else {
                // 配置了规则
                if (CollectionUtils.isNotEmpty(airCityCheckRes.getPerRuleCheckResList())) { // 是否有超规， 有超规则拼装所有规则信息，并且弹窗
                    int errCode = AirRuleMsg.AIR_DISTANCE.getCode();
                    // 是否有距离超规项
                    boolean isExceed = airCityCheckRes.getPerRuleCheckResList().stream()
                            .anyMatch(a -> Objects.equals(a.getErrCode(), errCode)
                                    && Objects.equals(a.getIsExceed(), Boolean.TRUE));
                    boolean isNotExceed = getIsNotExceed(airCityCheckRes, AirCityCheckChainOrderEnum.DISTANCE.getOrder());
                    RuleDetail ruleDetail = new RuleDetail();
                    setRuleResult(isExceed, isNotExceed, ruleDetail);
                    String ruleLimitDesc = String.format(Air1ScreenPageConstant.RULE_DESC_DISTANCE, airRuleV2.getCityConditionGroup().getDistanceLimitGroup().getDistanceLimitValue());
                    ruleDetail.setRuleLimit(ruleLimitDesc);
                    ruleDetailMapByTiTle.put(Air1ScreenPageConstant.RULE_TITLE_FLIGHT_DISTANCE, Lists.newArrayList(ruleDetail));
                }
            }

            // 如果所有的城市条件规则不符合，则二屏幕底部提示：不符合城市条件限制
            // 七个规则：通过距离的规则项放入底部提示文案
            if (isAllConfigCityRuleExceed) {
                if (CollectionUtils.isEmpty(ruleDetailMapByTiTle.get(Air1ScreenPageConstant.RULE_TITLE_FLIGHT_DISTANCE))) {
                    RuleDetail ruleDetail = new RuleDetail();
                    ruleDetail.setRuleLimit(AirRuleConstant.NO_LIMIT_MSG);
                    ruleDetail.setRuleTag(Air1ScreenPageConstant.NOT_MEET_CITY_COND_LIMIT);
                    ruleDetailMapByTiTle.put(Air1ScreenPageConstant.RULE_TITLE_FLIGHT_DISTANCE, Lists.newArrayList(ruleDetail));
                } else {
                    ruleDetailMapByTiTle.get(Air1ScreenPageConstant.RULE_TITLE_FLIGHT_DISTANCE).get(0).setRuleTag(Air1ScreenPageConstant.NOT_MEET_CITY_COND_LIMIT);
                }
            }

            setAirRuleCheckResultDetailV2(airCityRuleCheckRes, ruleDetailMapByTiTle,  Air1ScreenPageConstant.RULE_TITLE_FLIGHT_DISTANCE,
                    "追加 航程距离 规则", Air1ScreenPageConstant.TITLE_2_CITY_LIMIT, cityGreyTip, "新增 航程距离 规则");

            // 1.3 飞机火车联合管控
            if (Objects.isNull(airRuleV2.getCityConditionGroup())
                    || !Objects.equals(airRuleV2.getCityConditionGroup().getCityConditionGroupFlag(), Boolean.TRUE)
                    || !Objects.equals(airRuleV2.getCityConditionGroup().getAirTrainCombinationGroup().getTimeConsumeFlag(), Boolean.TRUE)){
                log.info("飞机火车联合管控规则不限制");
            } else {
                if (CollectionUtils.isNotEmpty(airCityCheckRes.getPerRuleCheckResList())) {
                    ArrayList<RuleDetail> ruleDetailList = new ArrayList<>();
                    if (Objects.nonNull(airRuleV2.getCityConditionGroup().getAirTrainCombinationGroup().getTimeConsumeValue())) {
                        boolean isExceed = airCityCheckRes.getPerRuleCheckResList().stream()
                                .anyMatch(a -> Objects.equals(a.getErrCode(), AirRuleMsg.AIR_TRAIN_TIME.getCode())
                                        && Objects.equals(a.getIsExceed(), Boolean.TRUE));
                        boolean isNotExceed = airCityCheckRes.getPerRuleCheckResList().stream()
                                .anyMatch(a -> Objects.equals(a.getErrCode(), AirRuleMsg.AIR_TRAIN_TIME.getCode()) // 还得靠AirRuleMsg区分，handlerOrder区分不了
                                        && Objects.equals(a.getIsCheckOpen(), Boolean.TRUE)
                                        && Objects.equals(a.getIsExceed(), Boolean.FALSE));
                        RuleDetail ruleDetail = new RuleDetail();
                        setRuleResult(isExceed, isNotExceed, ruleDetail);
                        String ruleLimitDesc = StrUtils.formatString(AirRuleMsg.AIR_TRAIN_TIME.getMsg(), airRuleV2.getCityConditionGroup().getAirTrainCombinationGroup().getTimeConsumeValue());
                        ruleDetail.setRuleLimit(ruleLimitDesc);
                        ruleDetailList.add(ruleDetail);
                    }
                    if (Objects.nonNull(airRuleV2.getCityConditionGroup().getAirTrainCombinationGroup().getTimeLowerPrice())
                            && airRuleV2.getCityConditionGroup().getAirTrainCombinationGroup().getTimeLowerPrice() != 0) {
                        boolean isExceed = airCityCheckRes.getPerRuleCheckResList().stream()
                                .anyMatch(a -> Objects.equals(a.getErrCode(), AirRuleMsg.AIR_TRAIN_PRICE.getCode())
                                        && Objects.equals(a.getIsExceed(), Boolean.TRUE));
                        boolean isNotExceed = airCityCheckRes.getPerRuleCheckResList().stream()
                                .anyMatch(a -> Objects.equals(a.getErrCode(), AirRuleMsg.AIR_TRAIN_PRICE.getCode()) // 还得靠AirRuleMsg区分，handlerOrder区分不了
                                        && Objects.equals(a.getIsCheckOpen(), Boolean.TRUE)
                                        && Objects.equals(a.getIsExceed(), Boolean.FALSE));
                        RuleDetail ruleDetail = new RuleDetail();
                        setRuleResult(isExceed, isNotExceed, ruleDetail);
                        String ruleLimitDesc = StrUtils.formatString(AirRuleMsg.AIR_TRAIN_PRICE.getMsg(), AirRuleUtils.formatMoney(airRuleV2.getCityConditionGroup().getAirTrainCombinationGroup().getTimeLowerPrice()));
                        ruleDetail.setRuleLimit(ruleLimitDesc);
                        ruleDetailList.add(ruleDetail);
                    }

                    log.info("ruleDetailList={}", JsonUtils.toJsonStr(ruleDetailList));
                    if (CollectionUtils.isNotEmpty(ruleDetailList)) {
                        ruleDetailMapByTiTle.put(Air1ScreenPageConstant.RULE_TITLE_AIR_TRAIN_COMBINE, ruleDetailList);
                    }
                }
            }
            setAirRuleCheckResultDetailV2(airCityRuleCheckRes, ruleDetailMapByTiTle,  Air1ScreenPageConstant.RULE_TITLE_AIR_TRAIN_COMBINE,
                    "追加 航程距离 规则", Air1ScreenPageConstant.TITLE_2_CITY_LIMIT, cityGreyTip, "新增 航程距离 规则");

            log.info("ruleDetailMapByTiTle={}", JsonUtils.toJsonStr(ruleDetailMapByTiTle));

            logger.info("航班信息AirRuleCheckReq={}", JsonUtils.toJsonStr(req));

            AirFlightRuleCheckReq airFlightRuleCheckReq = new AirFlightRuleCheckReq();
            // 构造航班信息
            AirFlightRuleCheckReq.FlightInfo flightInfo = getFlightInfo(companyId, token, req, airRuleV2);

            airFlightRuleCheckReq.setFlightInfo(flightInfo);
            airFlightRuleCheckReq.setRuleInfo(airRuleV2);
            airFlightRuleCheckReq.setClientVersion(clientVersion);
            airFlightRuleCheckReq.setRuleId(ruleId);
            if (seatMaxPriceDTO != null) { // 如果 seatMaxPriceDTO 不为空，则加入到城市req里边
                airFlightRuleCheckReq.setSeatMaxPriceDTO(seatMaxPriceDTO);
            }
            if (trainInfo != null) {
                airFlightRuleCheckReq.setGoTrainInfo(trainInfo);
            }
            logger.info("预定请求参数airFlightRuleCheckReq.getFlightInfo={}", JsonUtils.toJsonStr(airFlightRuleCheckReq.getFlightInfo()));
            AirFlightRuleCheckRes airFlightRuleCheckRes = new AirFlightRuleCheckRes();
            airFlightRuleCheckRes.setSkipSetting(airCityCheckRes.getSkipSetting());
            airFlightBookRuleCheckChainHandler.executionChain(airFlightRuleCheckReq, airFlightRuleCheckRes);
            logger.info("预定airFlightRuleCheckRes:{}", JsonUtils.toJsonStr(airFlightRuleCheckRes));


            // 本舱位预定相关的规则信息，通过标题映射。eg. <"可乘坐舱位", ruleDetailList>
            HashMap<String, List<RuleDetail>> bookRuleDetailMapByTiTle = new HashMap<>();
            AirRuleCheckRes airBookRuleCheckRes = new AirRuleCheckRes();
            boolean isNotOpenBookAirRule = Objects.isNull(airRuleV2.getAirBookConditionGroup())
                    || Objects.isNull(airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup())
                    || !Objects.equals(airRuleV2.getAirBookConditionGroup().getAirBookConditionGroupFlag(), Boolean.TRUE)
                    || !Objects.equals(airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup().getAirRuleBookGroupFlag(), Boolean.TRUE);
            // 2.1 可乘坐舱位
            if (isNotOpenBookAirRule
                    || !Objects.equals(airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup().getAirCabinFlag(), Boolean.TRUE)){
                log.info("可乘坐舱位 相关规则不限制");
            } else {
                if (CollectionUtils.isNotEmpty(airFlightRuleCheckRes.getPerRuleCheckResList())) {
                    boolean isExceed = getIsExceed(airFlightRuleCheckRes, AirBookCheckChainOrderEnum.CABIN);
                    boolean isNotExceed = getIsNotExceed(airFlightRuleCheckRes, AirBookCheckChainOrderEnum.CABIN.getOrder());
                    RuleDetail ruleDetail = new RuleDetail();
                    AirRuleBookGroup ruleBookGroup = airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup();
                    String airCabinType = ruleBookGroup.getAirCabinType();
                    String cabinNamesByCodes = AirCabinType.getNamesByCodes(airCabinType);
                    //限制条件下的商务舱拼接内容介绍
                    if (cabinNamesByCodes.contains(AirCabinType.BUSSINESS.getName()) && (VCUtil.getOrElse(ruleBookGroup.getLimitCabinDirectDuration(), 0) > 0 || VCUtil.getOrElse(ruleBookGroup.getLimitCabinStopoverDuration(), 0) > 0)) {
                        List<String> collect = Arrays.asList(cabinNamesByCodes.split(",")).stream().map(cabinName -> {
                            if (ObjectUtil.equals(cabinName, AirCabinType.BUSSINESS.getName())) {
                                cabinName += "(";
                                if (VCUtil.getOrElse(ruleBookGroup.getLimitCabinDirectDuration(), 0) > 0) {
                                    cabinName += "航班时长" + ruleBookGroup.getLimitCabinDirectDuration() + "小时以上可订";
                                }
                                if (VCUtil.getOrElse(ruleBookGroup.getLimitCabinDirectDuration(), 0) > 0 && VCUtil.getOrElse(ruleBookGroup.getLimitCabinStopoverDuration(), 0) > 0) {
                                    cabinName += "、";
                                }
                                if (VCUtil.getOrElse(ruleBookGroup.getLimitCabinStopoverDuration(), 0) > 0) {
                                    cabinName += "经停航班" + ruleBookGroup.getLimitCabinStopoverDuration() + "小时以上可订";
                                }
                                cabinName += ")";
                            }
                            return cabinName;
                        }).collect(Collectors.toList());
                        cabinNamesByCodes = String.join(",", collect);
                    }
                    String ruleLimitDesc = StrUtils.formatString(AirRuleMsg.AIR_CABIN.getMsg(), cabinNamesByCodes);
                    if (isExceed) {
                        AirFlightRuleCheckRes.PerRuleCheckRes perRuleCheckRes = airFlightRuleCheckRes.getPerRuleCheckResList().stream()
                                .filter(a -> Objects.equals(a.getErrCode(), AirRuleMsg.AIR_CABIN.getCode())
                                        && Objects.equals(a.getIsExceed(), Boolean.TRUE)).findFirst().orElse(null);
                        // 底部提示文案
                        Integer config = ruleBookGroup.getAirCabinConfig();
                        setBottomTip(ruleDetail, cabinNamesByCodes, perRuleCheckRes, config, Air1ScreenPageConstant.TIP_TAG_CABIN);
                    }
                    setRuleResult(isExceed, isNotExceed, ruleDetail);
                    ruleDetail.setRuleLimit(ruleLimitDesc);
                    bookRuleDetailMapByTiTle.put(Air1ScreenPageConstant.RULE_TITLE_CABIN, Lists.newArrayList(ruleDetail));
                }
            }
            setAirRuleCheckResultDetailV2(airBookRuleCheckRes, bookRuleDetailMapByTiTle, Air1ScreenPageConstant.RULE_TITLE_CABIN,
                    "追加 可乘坐舱位 规则", Air1ScreenPageConstant.TITLE_2_FLIGHT_BOOK_LIMIT, Air1ScreenPageConstant.TIP_2_NEED_MEET_ALL_COND, "新增 可乘坐舱位 规则");

            // 2.2 价格
            if (isNotOpenBookAirRule
                    || !Objects.equals(airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup().getAirPriceFlag(), Boolean.TRUE)){
                log.info("价格 相关规则不限制");
            } else {
                if (CollectionUtils.isNotEmpty(airFlightRuleCheckRes.getPerRuleCheckResList())) {
                    boolean isExceed = getIsExceed(airFlightRuleCheckRes, AirBookCheckChainOrderEnum.FLIGHT_PRICE);
                    boolean isNotExceed = getIsNotExceed(airFlightRuleCheckRes, AirBookCheckChainOrderEnum.FLIGHT_PRICE.getOrder());
                    RuleDetail ruleDetail = new RuleDetail();

                    BigDecimal airUnitPrice = airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup().getAirUnitPrice();
                    String ruleLimitDesc = StrUtils.formatString(AirRuleMsg.AIR_PRICE.getMsg(),
                            AirRuleUtils.formatMoney(airUnitPrice));
                    if (isExceed) {
                        AirFlightRuleCheckRes.PerRuleCheckRes perRuleCheckRes = airFlightRuleCheckRes.getPerRuleCheckResList().stream()
                                .filter(a -> Objects.equals(a.getErrCode(), AirRuleMsg.AIR_PRICE.getCode())
                                && Objects.equals(a.getIsExceed(), Boolean.TRUE)).findFirst().orElse(null);
                        // 底部提示文案
                        Integer config = airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup().getAirPriceConfig();
                        setBottomTip(ruleDetail, BigDecimalTool.formatMoney(airUnitPrice), perRuleCheckRes, config, Air1ScreenPageConstant.TIP_TAG_PRICE_NEED_LOWER);
                    }
                    setRuleResult(isExceed, isNotExceed, ruleDetail);
                    ruleDetail.setRuleLimit(ruleLimitDesc);
                    bookRuleDetailMapByTiTle.put(Air1ScreenPageConstant.RULE_TITLE_FLIGHT_PRICE, Lists.newArrayList(ruleDetail));
                }
            }
            setAirRuleCheckResultDetailV2(airBookRuleCheckRes, bookRuleDetailMapByTiTle, Air1ScreenPageConstant.RULE_TITLE_FLIGHT_PRICE,
                    "追加 价格 规则", Air1ScreenPageConstant.TITLE_2_FLIGHT_BOOK_LIMIT, Air1ScreenPageConstant.TIP_2_NEED_MEET_ALL_COND, "新增 价格 规则");

            // 2.3 折扣
            if (isNotOpenBookAirRule
                    || !Objects.equals(airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup().getAirDiscountFlag(), Boolean.TRUE)){
                log.info("折扣 相关规则不限制");
            } else {
                if (CollectionUtils.isNotEmpty(airFlightRuleCheckRes.getPerRuleCheckResList())) {
                    boolean isExceed = getIsExceed(airFlightRuleCheckRes, AirBookCheckChainOrderEnum.FLIGHT_DISCOUNT);
                    boolean isNotExceed = getIsNotExceed(airFlightRuleCheckRes, AirBookCheckChainOrderEnum.FLIGHT_DISCOUNT.getOrder());
                    RuleDetail ruleDetail = new RuleDetail();
                    AirRuleBookGroup airRuleBookGroup = airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup();
                    BigDecimal airDiscount = airRuleBookGroup.getAirDiscount();
                    String ruleLimitDesc;
                    String formatDiscount = AirRuleUtils.formatDiscount(airDiscount);
                    // 折扣类型 1 低于、2 不高于
                    String airDiscountTypeName = Objects.equals(airRuleBookGroup.getAirDiscountType(), 2) ? "不高于" : "低于";
                    Integer airType = Integer.valueOf(req.getAirType());
                    if (Objects.equals(airRuleBookGroup.getAirCabinFlag(), true)) {
                        String airCabinType = airRuleBookGroup.getAirCabinType();
                        String[] cabinTypeList = airCabinType.split(",");
                        List<Integer> cabinCodeList = Arrays.stream(cabinTypeList)
                            .map(Integer::parseInt).collect(Collectors.toList());
                        StringBuilder builder = new StringBuilder();
                        builder.append("经济舱价格折扣需").append(airDiscountTypeName)
                            .append(ObjUtils.toString(airRuleBookGroup.getAirDiscount()
                                .multiply(new BigDecimal(10)).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue()))
                            .append("折");
                        if (CollectionUtils.isNotEmpty(cabinCodeList) && cabinCodeList.contains(AirCabinType.BUSSINESS.getCode())) {
                            builder.append("，商务/头等舱折扣需").append(airDiscountTypeName).append(ObjUtils.toString(airRuleBookGroup.getAirBusinessClassDiscount()
                                .multiply(new BigDecimal(10)).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue()))
                                .append("折");
                            if (Objects.equals(airType, AirCabinType.BUSSINESS.getCode())) {
                                formatDiscount =
                                    AirRuleUtils.formatDiscount(airRuleBookGroup.getAirBusinessClassDiscount());
                            }
                        }
                        ruleLimitDesc = builder.toString();
                    } else {
                        ruleLimitDesc = StrUtils.formatString(AirRuleMsg.AIR_DISCOUNT.getMsg(), airDiscountTypeName, formatDiscount);
                    }
                    if (isExceed) {
                        AirFlightRuleCheckRes.PerRuleCheckRes perRuleCheckRes = airFlightRuleCheckRes.getPerRuleCheckResList().stream()
                                .filter(a -> Objects.equals(a.getErrCode(), AirRuleMsg.AIR_DISCOUNT.getCode())
                                        && Objects.equals(a.getIsExceed(), Boolean.TRUE)).findFirst().orElse(null);
                        // 底部提示文案
                        Integer config = airRuleBookGroup.getAirDiscountConfig();
                        setBottomTip(ruleDetail, airDiscountTypeName + formatDiscount, perRuleCheckRes, config, Air1ScreenPageConstant.TIP_TAG_DISCOUNT);
                    }
                    setRuleResult(isExceed, isNotExceed, ruleDetail);
                    ruleDetail.setRuleLimit(ruleLimitDesc);
                    bookRuleDetailMapByTiTle.put(Air1ScreenPageConstant.RULE_TITLE_FLIGHT_DISCOUNT, Lists.newArrayList(ruleDetail));
                }
            }
            setAirRuleCheckResultDetailV2(airBookRuleCheckRes, bookRuleDetailMapByTiTle,Air1ScreenPageConstant.RULE_TITLE_FLIGHT_DISCOUNT,
                    "追加 折扣 规则", Air1ScreenPageConstant.TITLE_2_FLIGHT_BOOK_LIMIT, Air1ScreenPageConstant.TIP_2_NEED_MEET_ALL_COND, "新增 折扣 规则");

            // 2.4 最低价
            if (isNotOpenBookAirRule
                    || !Objects.equals(airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup().getLowPriceFlag(), 1) || Objects.equals(req.getMinPriceFlag(),1)){
                log.info("最低价 相关规则不限制");
            } else {
                AirRuleBookGroup airRuleBookGroup = airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup();
                if (CollectionUtils.isNotEmpty(airFlightRuleCheckRes.getPerRuleCheckResList())) {
                    boolean isExceed = getIsExceed(airFlightRuleCheckRes, AirBookCheckChainOrderEnum.LOWEST_PRICE_FLIGHT);
                    boolean isNotExceed = getIsNotExceed(airFlightRuleCheckRes, AirBookCheckChainOrderEnum.LOWEST_PRICE_FLIGHT.getOrder());
                    RuleDetail ruleDetail = new RuleDetail();
                    String stopoverFlightMsg = FilterStopoverFlightFlag.ALL.equalsTo(airRuleBookGroup.getFilterStopoverFlightFlag())
                            ? "" : FilterStopoverFlightFlag.valueOf(airRuleBookGroup.getFilterStopoverFlightFlag()).getDesc();
                    String airline = StringUtil.isTrimBlank(airRuleBookGroup.getShieldAirline()) ? "" : "部分航司";
                    String lowPriceUnit = AirLowPriceUnitEnum.getDescByKey(airRuleBookGroup.getLowPriceUnit());
                    String ruleLimitDesc = StrUtils.formatString(AirRuleMsg.AIR_LOWEST_PRICE.getMsg(),
                            AirPortCityEnum.getDescribe(airRuleBookGroup.getAirPortCityFlag()),
                            airRuleBookGroup.getLowPriceTime(),
                            stopoverFlightMsg, airline, lowPriceUnit);

                    //如果开启低价上浮 则重新计算最低价金额
                    if(Objects.equals(airRuleBookGroup.getIsFloat(),1)){
                        ruleLimitDesc += ",允许比最低价向上浮动"+airRuleBookGroup.getFloatVal()+ (airRuleBookGroup.getFloatType() == 0?"元":"%");
                    }

                    if (isExceed) {
                        AirFlightRuleCheckRes.PerRuleCheckRes perRuleCheckRes = airFlightRuleCheckRes.getPerRuleCheckResList().stream()
                                .filter(a -> Objects.equals(a.getErrCode(), AirRuleMsg.AIR_LOWEST_PRICE.getCode())
                                        && Objects.equals(a.getIsExceed(), Boolean.TRUE)).findFirst().orElse(null);
                        // 底部提示文案
                        Integer config = airRuleBookGroup.getLowPriceConfig();
                        setBottomTip(ruleDetail, airRuleBookGroup.getLowPriceTime()+lowPriceUnit, perRuleCheckRes, config, Air1ScreenPageConstant.TIP_TAG_LOWEST_PRICE);
                    }
                    setRuleResult(isExceed, isNotExceed, ruleDetail);
                    ruleDetail.setRuleLimit(ruleLimitDesc);
                    bookRuleDetailMapByTiTle.put(Air1ScreenPageConstant.RULE_TITLE_LOWEST_PRICE, Lists.newArrayList(ruleDetail));
                }
            }
            setAirRuleCheckResultDetailV2(airBookRuleCheckRes, bookRuleDetailMapByTiTle, Air1ScreenPageConstant.RULE_TITLE_LOWEST_PRICE,
                    "追加 最低价 规则", Air1ScreenPageConstant.TITLE_2_FLIGHT_BOOK_LIMIT, Air1ScreenPageConstant.TIP_2_NEED_MEET_ALL_COND, "新增 最低价 规则");

            // 2.5 起飞时段
            if (isNotOpenBookAirRule
                    || !Objects.equals(airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup().getAirTimeRangeFlag(), Boolean.TRUE)){
                log.info("起飞时段 相关规则不限制");
            } else {
                if (CollectionUtils.isNotEmpty(airFlightRuleCheckRes.getPerRuleCheckResList())) {
                    boolean isExceed = getIsExceed(airFlightRuleCheckRes, AirBookCheckChainOrderEnum.FLIGHT_TIME_RANGE);
                    boolean isNotExceed = getIsNotExceed(airFlightRuleCheckRes, AirBookCheckChainOrderEnum.FLIGHT_TIME_RANGE.getOrder());
                    RuleDetail ruleDetail = new RuleDetail();
                    List<AirTimeRangeDTO> timeRangeList = airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup().getTimeRangeList();
                    String ruleLimit = AirRuleUtils.getTimeRangeText(timeRangeList);    // 起飞时段
                    if (isExceed) {
                        AirFlightRuleCheckRes.PerRuleCheckRes perRuleCheckRes = airFlightRuleCheckRes.getPerRuleCheckResList().stream()
                                .filter(a -> Objects.equals(a.getErrCode(), AirRuleMsg.AIR_RANGE.getCode())
                                        && Objects.equals(a.getIsExceed(), Boolean.TRUE)).findFirst().orElse(null);
                        // 底部提示文案
                        Integer config = 1;
                        Boolean airTimeRangeFlag = airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup().getAirTimeRangeFlag();
                        if(airTimeRangeFlag == null || Objects.equals(airTimeRangeFlag,false)){
                            config = null;
                        }
                        setBottomTip(ruleDetail, Air1ScreenPageConstant.TIP_TIME_RANGE_NOT_MEET, perRuleCheckRes, config, Air1ScreenPageConstant.TIP_TAG_TIME_RANGE);
                    }

                    setRuleResult(isExceed, isNotExceed, ruleDetail);
                    ruleDetail.setRuleLimit(ruleLimit);
                    bookRuleDetailMapByTiTle.put(Air1ScreenPageConstant.RULE_TITLE_TIME_RANGE, Lists.newArrayList(ruleDetail));
                }
            }
            setAirRuleCheckResultDetailV2(airBookRuleCheckRes, bookRuleDetailMapByTiTle,  Air1ScreenPageConstant.RULE_TITLE_TIME_RANGE,
                    "追加 起飞时段 规则", Air1ScreenPageConstant.TITLE_2_FLIGHT_BOOK_LIMIT, Air1ScreenPageConstant.TIP_2_NEED_MEET_ALL_COND, "新增 起飞时段 规则");

            // 2.6 提前预定天数
            if (isNotOpenBookAirRule
                    || !Objects.equals(airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup().getPrivDayFlag(), Boolean.TRUE)){
                log.info("提前预定天数 相关规则不限制");
            } else {
                if (CollectionUtils.isNotEmpty(airFlightRuleCheckRes.getPerRuleCheckResList())) {
                    boolean isExceed = getIsExceed(airFlightRuleCheckRes, AirBookCheckChainOrderEnum.BOOK_IN_ADVANCE);
                    boolean isNotExceed = getIsNotExceed(airFlightRuleCheckRes, AirBookCheckChainOrderEnum.BOOK_IN_ADVANCE.getOrder());
                    RuleDetail ruleDetail = new RuleDetail();
                    Integer privDayMin = airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup().getPrivDayMin();
                    Integer privDayMax = airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup().getPrivDayMax();
                    String ruleLimitDesc = StrUtils.formatString(AirRuleMsg.AIR_BOOK_ADVANCE.getMsg(), privDayMin, privDayMax);
                    if (isExceed) {
                        AirFlightRuleCheckRes.PerRuleCheckRes perRuleCheckRes = airFlightRuleCheckRes.getPerRuleCheckResList().stream()
                                .filter(a -> Objects.equals(a.getHandlerOrder(), AirBookCheckChainOrderEnum.BOOK_IN_ADVANCE.getOrder())
                                        && Objects.equals(a.getIsExceed(), Boolean.TRUE)).findFirst().orElse(null);
                        // 底部提示文案
                        Integer config = airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup().getPrivDayConfig();
                        setBottomTip(ruleDetail, privDayMin.toString(), privDayMax.toString(), perRuleCheckRes, config, Air1ScreenPageConstant.TIP_TAG_ADVANCED_BOOK);
                    }
                    setRuleResult(isExceed, isNotExceed, ruleDetail);
                    ruleDetail.setRuleLimit(ruleLimitDesc);
                    bookRuleDetailMapByTiTle.put(Air1ScreenPageConstant.RULE_TITLE_ADVANCE_BOOK_DAY, Lists.newArrayList(ruleDetail));
                }
            }
            setAirRuleCheckResultDetailV2(airBookRuleCheckRes, bookRuleDetailMapByTiTle,  Air1ScreenPageConstant.RULE_TITLE_ADVANCE_BOOK_DAY,
                    "追加 提前预定天数 规则", Air1ScreenPageConstant.TITLE_2_FLIGHT_BOOK_LIMIT, Air1ScreenPageConstant.TIP_2_NEED_MEET_ALL_COND, "新增 提前预定天数 规则");

            logger.info("bookRuleDetailMapByTiTle={}", JsonUtils.toJsonStr(bookRuleDetailMapByTiTle));

            removeGroupDesc(airCityRuleCheckRes);

            removeGroupDesc(airBookRuleCheckRes);

            // 汇总城市和预定航班校验
            AirRuleCheckRes airCityAndBookRuleCheckRes = new AirRuleCheckRes();
            airCityAndBookRuleCheckRes.setAir2ScreenCheckGroups(new ArrayList<>());
            airCityAndBookRuleCheckRes.getAir2ScreenCheckGroups().addAll(airCityRuleCheckRes.getAir2ScreenCheckGroups());
            airCityAndBookRuleCheckRes.getAir2ScreenCheckGroups().addAll(airBookRuleCheckRes.getAir2ScreenCheckGroups());
            airCityAndBookRuleCheckRes.setCabin(req.getCabin());
            airCityAndBookRuleCheckRes.setUseVersion(AirRule2ScreenUseVersion.EXCEED_PERSONAL_PAY.getCode());
            res.add(airCityAndBookRuleCheckRes);
            logger.info("航班信息结束cabin={}", req.getCabin());
        }

        return res;
    }

    /**
     * @MethodName removeGroupDesc
     * @Description 当不限制是去除组标题
     * @param: airCityRuleCheckRes
     * <AUTHOR> Yunpeng
     * @Date 2023/1/5 10:34
     */
    private void removeGroupDesc(AirRuleCheckRes airCityRuleCheckRes) {
        boolean needRemoveCityGroupDesc = isNeedRemoveGroupDesc(airCityRuleCheckRes);
        if (needRemoveCityGroupDesc) {
            if (CollectionUtils.isNotEmpty(airCityRuleCheckRes.getAir2ScreenCheckGroups())) {
                for (Air2ScreenCheckGroup air2ScreenCheckGroup : airCityRuleCheckRes.getAir2ScreenCheckGroups()) {
                    air2ScreenCheckGroup.setGroupDesc(null);
                }
            }
        }
    }

    private boolean isNeedRemoveGroupDesc(AirRuleCheckRes airCityRuleCheckRes) {
        boolean needRemoveGroupDesc = true;
        if (CollectionUtils.isNotEmpty(airCityRuleCheckRes.getAir2ScreenCheckGroups())) {
            for (Air2ScreenCheckGroup air2ScreenCheckGroup : airCityRuleCheckRes.getAir2ScreenCheckGroups()) {
                List<AirRuleCheckResultDetailV2> airRuleCheckResultDetailV2List = air2ScreenCheckGroup.getAirRuleCheckResultDetailV2List();

                if (CollectionUtils.isNotEmpty(airRuleCheckResultDetailV2List)) {
                    for (AirRuleCheckResultDetailV2 airRuleCheckResultDetailV2 : airRuleCheckResultDetailV2List) {
                        if (CollectionUtils.isNotEmpty(airRuleCheckResultDetailV2.getRuleDetailList())) {
                            boolean allMatch = airRuleCheckResultDetailV2.getRuleDetailList().stream()
                                    .allMatch(a -> a.getRuleLimit().equals(AirRuleConstant.NO_LIMIT_MSG));
                            if (!allMatch) {
                                needRemoveGroupDesc = false;
                                break;
                            }
                        }
                    }
                }
            }
        }
        return needRemoveGroupDesc;
    }

    private boolean getIsExceed(AirFlightRuleCheckRes airFlightRuleCheckRes, AirBookCheckChainOrderEnum airBookCheckChainOrderEnum) {
        return airFlightRuleCheckRes.getPerRuleCheckResList().stream()
                .anyMatch(a -> Objects.equals(a.getHandlerOrder(), airBookCheckChainOrderEnum.getOrder())
                        && Objects.equals(a.getIsExceed(), Boolean.TRUE));
    }

    private boolean getIsNotExceed(AirFlightRuleCheckRes airCityCheckRes, int order) {
        return airCityCheckRes.getPerRuleCheckResList().stream()
                .anyMatch(a -> Objects.equals(a.getHandlerOrder(), order)
                        && Objects.equals(a.getIsCheckOpen(), Boolean.TRUE)
                        && Objects.equals(a.getIsExceed(), Boolean.FALSE));
    }

    private void setRuleResult(boolean isExceed, boolean isNotExceed, RuleDetail ruleDetail) {
        if (isExceed) {
            ruleDetail.setRuleResult(false);
        } else if (isNotExceed) {
            ruleDetail.setRuleResult(true);
        }
    }

    /**
     * @MethodName setBottomTip
     * @Description 添加底部提示文案
     * @param: ruleDetail
     * @param: cabinNamesByCodes
     * @param: perRuleCheckRes
     * @param: config
     * @param: tipStr
     * <AUTHOR> Yunpeng
     * @Date 2022/12/20 10:12
     */
    private void setBottomTip(RuleDetail ruleDetail, String firstStr, AirFlightRuleCheckRes.PerRuleCheckRes perRuleCheckRes, Integer config, String tipStr) {
        if (null == config) {
            return;
        }
        TravelExceedType travelExceedType = TravelExceedType.getEnumByCompareOrder(config);
        if (perRuleCheckRes != null && Objects.nonNull(travelExceedType)) {
            String tag = String.format(tipStr, firstStr, travelExceedType.getExceedPersonalPayStr());
            ruleDetail.setRuleTag(tag);
        }
    }

    /**
     * @MethodName setBottomTip
     * @Description 添加两个参数的底部提示文案：提前预定天数
     * @param: ruleDetail
     * @param: cabinNamesByCodes
     * @param: perRuleCheckRes
     * @param: config
     * @param: tipStr
     * <AUTHOR> Yunpeng
     * @Date 2022/12/20 10:12
     */
    private void setBottomTip(RuleDetail ruleDetail, String firstStr, String secondStr, AirFlightRuleCheckRes.PerRuleCheckRes perRuleCheckRes, Integer config, String tipStr) {
        TravelExceedType travelExceedType = TravelExceedType.getEnumByCompareOrder(config);
        if (perRuleCheckRes != null && Objects.nonNull(travelExceedType)) {
            String tag = String.format(tipStr, firstStr, secondStr, travelExceedType.getExceedPersonalPayStr());
            ruleDetail.setRuleTag(tag);
        }
    }

    private ExceedConfigEnum getExceedConfigEnum(Integer airPriceConfig) {
        ExceedConfigEnum priceExceedConfigEnum = null;
        for (ExceedConfigEnum exceedConfigEnum : ExceedConfigEnum.values()) {
            if (Objects.equals(exceedConfigEnum.getCompareOrder(), airPriceConfig)) {
                priceExceedConfigEnum = exceedConfigEnum;
            }
        }
        return priceExceedConfigEnum;
    }

    /**
     * @MethodName setAirRuleCheckResultDetailV2
     * @Description 构造 AirRuleCheckResultDetailV2
     * @param: airRuleCheckRes 预定航班或者城市 校验结果
     * @param: ruleDetailMapByTiTle
     * @param: ruleTitle
     * @param: appendLogMsg 追加到 detailV2的 打印日志log
     * @param: ruleLimit
     * @param: tip
     * @param: newLogMsg 新增到 detailV2的 打印日志log
     * <AUTHOR> Yunpeng
     * @Date 2022/11/28 10:08
     */
    private void setAirRuleCheckResultDetailV2(AirRuleCheckRes airRuleCheckRes, HashMap<String, List<RuleDetail>> ruleDetailMapByTiTle,
                                               String ruleTitle, String appendLogMsg, String ruleLimit, String tip, String newLogMsg) {
        setRuleGroup(ruleLimit, tip, ruleTitle, airRuleCheckRes, ruleDetailMapByTiTle);
        log.info(newLogMsg);
    }

    private void setRuleGroup(String groupTitle, String groupDesc, String ruleTitle, AirRuleCheckRes airRuleCheckRes, HashMap<String, List<RuleDetail>> ruleDetailMapByTiTle) {
        // 如果还没分组，直接创建返回
        if (CollectionUtils.isEmpty(airRuleCheckRes.getAir2ScreenCheckGroups())) {
            Air2ScreenCheckGroup air2ScreenCheckGroup = new Air2ScreenCheckGroup();
            air2ScreenCheckGroup.setGroupTitle(groupTitle);
            air2ScreenCheckGroup.setGroupDesc(groupDesc);

            AirRuleCheckResultDetailV2 airRuleCheckResultDetailV2 = new AirRuleCheckResultDetailV2();
            airRuleCheckResultDetailV2.setRuleDesc(ruleTitle);
            if (CollectionUtils.isEmpty(ruleDetailMapByTiTle.get(ruleTitle))) {
                RuleDetail ruleDetail = new RuleDetail();
                ruleDetail.setRuleLimit(AirRuleConstant.NO_LIMIT_MSG);
                airRuleCheckResultDetailV2.setRuleDetailList(Lists.newArrayList(ruleDetail));
            } else {
                airRuleCheckResultDetailV2.setRuleDetailList(ruleDetailMapByTiTle.get(ruleTitle));
            }
            air2ScreenCheckGroup.setAirRuleCheckResultDetailV2List(Lists.newArrayList(airRuleCheckResultDetailV2));
            airRuleCheckRes.setAir2ScreenCheckGroups(Lists.newArrayList(air2ScreenCheckGroup));
            return;
        }

        // 有分组
        // 同一大组的规则
        boolean isExistedGroup = airRuleCheckRes.getAir2ScreenCheckGroups().stream().anyMatch(a -> a.getGroupTitle().equals(groupTitle));
        if (isExistedGroup) {
            // 同一大组
            Air2ScreenCheckGroup existedGroup = airRuleCheckRes.getAir2ScreenCheckGroups().stream().filter(a -> a.getGroupTitle().equals(groupTitle)).findFirst().orElse(null);
            if (Objects.isNull(existedGroup)) {
                logger.info("null");
                return;
            }
            //是否同一小组
            boolean isExistedSmallGroup = existedGroup.getAirRuleCheckResultDetailV2List().stream().anyMatch(a -> a.getRuleDesc().equals(ruleTitle));
            if (isExistedSmallGroup) {//同一小组
                AirRuleCheckResultDetailV2 airRuleCheckResultDetailV2 = existedGroup.getAirRuleCheckResultDetailV2List().stream().filter(a -> a.getRuleDesc().equals(ruleTitle)).findFirst().orElse(null);
                if (Objects.isNull(airRuleCheckResultDetailV2)) {
                    logger.info("null2");
                    return;
                }
                if (CollectionUtils.isEmpty(ruleDetailMapByTiTle.get(ruleTitle))) {
                    RuleDetail ruleDetail = new RuleDetail();
                    ruleDetail.setRuleLimit(AirRuleConstant.NO_LIMIT_MSG);
                    airRuleCheckResultDetailV2.setRuleDetailList(Lists.newArrayList(ruleDetail));
                } else {
                    airRuleCheckResultDetailV2.getRuleDetailList().addAll(ruleDetailMapByTiTle.get(ruleTitle));
                }
                return;
            }
            // 非同一小组，（肯定存在其他组）
            AirRuleCheckResultDetailV2 airRuleCheckResultDetailV2 = new AirRuleCheckResultDetailV2();
            airRuleCheckResultDetailV2.setRuleDesc(ruleTitle);
            if (CollectionUtils.isEmpty(ruleDetailMapByTiTle.get(ruleTitle))) {
                RuleDetail ruleDetail = new RuleDetail();
                ruleDetail.setRuleLimit(AirRuleConstant.NO_LIMIT_MSG);
                airRuleCheckResultDetailV2.setRuleDetailList(Lists.newArrayList(ruleDetail));
            } else {
                airRuleCheckResultDetailV2.setRuleDetailList(ruleDetailMapByTiTle.get(ruleTitle));
            }
            existedGroup.getAirRuleCheckResultDetailV2List().add(airRuleCheckResultDetailV2);

            return;
        }
        // 非同一大组
        // 创建一个大组
        Air2ScreenCheckGroup newCreateGroup = new Air2ScreenCheckGroup();
        newCreateGroup.setGroupTitle(groupTitle);
        newCreateGroup.setGroupDesc(groupDesc);
        AirRuleCheckResultDetailV2 detailV2 = new AirRuleCheckResultDetailV2();
        detailV2.setRuleDesc(ruleTitle);
        if (CollectionUtils.isEmpty(ruleDetailMapByTiTle.get(ruleTitle))) {
            RuleDetail ruleDetail = new RuleDetail();
            ruleDetail.setRuleLimit(AirRuleConstant.NO_LIMIT_MSG);
            detailV2.setRuleDetailList(Lists.newArrayList(ruleDetail));
        } else {
            detailV2.setRuleDetailList(ruleDetailMapByTiTle.get(ruleTitle));
        }
        detailV2.setRuleDetailList(ruleDetailMapByTiTle.get(ruleTitle));
        newCreateGroup.setAirRuleCheckResultDetailV2List(Lists.newArrayList(detailV2));

        airRuleCheckRes.getAir2ScreenCheckGroups().add(newCreateGroup);
    }


    private void setTakeoffLandCityRuleDetail(AirRuleV2 airRuleV2, AirFlightRuleCheckRes airCityCheckRes, HashMap<String, List<RuleDetail>> ruleDetailMapByTiTle) {
        if (Objects.isNull(airRuleV2.getCityConditionGroup())
                || !Objects.equals(airRuleV2.getCityConditionGroup().getCityConditionGroupFlag(), Boolean.TRUE)
                || !Objects.equals(airRuleV2.getCityConditionGroup().getTakeOffLandingCityGroup().getTakeOffLandingCityGroupFlag(), Boolean.TRUE)){
            log.info("起降城市相关规则不限制");
        } else {
            // 根据超规则信息拼装
            if (Objects.isNull(airCityCheckRes) || CollectionUtils.isEmpty(airCityCheckRes.getPerRuleCheckResList())) {
                logger.info("无校验规则信息");
                return;
            }

            ArrayList<RuleDetail> ruleDetailList = new ArrayList<>();

            boolean hasOpenedTakeoffCityGroup = Objects.nonNull(airRuleV2.getCityConditionGroup().getTakeOffLandingCityGroup().getTakeOffLandingCityGroupFlag())
                    && Objects.equals(airRuleV2.getCityConditionGroup().getTakeOffLandingCityGroup().getTakeOffLandingCityGroupFlag(), Boolean.TRUE);

            // (1) 起飞城市到任意
            addRuleDetail(airRuleV2, airCityCheckRes, ruleDetailList, hasOpenedTakeoffCityGroup, AirRuleMsg.AIR_TAKEOFF);
            // (2) 任意到起飞城市
            addRuleDetail(airRuleV2, airCityCheckRes, ruleDetailList, hasOpenedTakeoffCityGroup, AirRuleMsg.AIR_LANDING);
            // (3) 从xx起飞并降落
            addRuleDetail(airRuleV2, airCityCheckRes, ruleDetailList, hasOpenedTakeoffCityGroup, AirRuleMsg.AIR_TAKEOFF_LANDING);
            // (4) 指定航线
            addRuleDetail(airRuleV2, airCityCheckRes, ruleDetailList, hasOpenedTakeoffCityGroup, AirRuleMsg.AIR_FLIGHT);

            log.info("ruleDetailList={}", JsonUtils.toJsonStr(ruleDetailList));
            if (CollectionUtils.isNotEmpty(ruleDetailList)) {
                ruleDetailMapByTiTle.put(Air1ScreenPageConstant.RULE_TITLE_TAKEOFF_LAND_CITY, ruleDetailList);
            }

        }
    }

    /**
     * @MethodName addRuleDetail
     * @Description 机票二屏幕：添加起降城市的所有列表信息
     * @param: airRuleV2
     * @param: airCityCheckRes
     * @param: ruleDetailList
     * @param: hasOpenedTakeoffCityGroup
     * @param: airRuleMsg
     * <AUTHOR> Yunpeng
     * @Date 2022/12/26 20:03
     */
    private void addRuleDetail(AirRuleV2 airRuleV2, AirFlightRuleCheckRes airCityCheckRes, ArrayList<RuleDetail> ruleDetailList,
                               boolean hasOpenedTakeoffCityGroup, AirRuleMsg airRuleMsg) {
        if (hasOpenedTakeoffCityGroup && CollectionUtils.isNotEmpty(airRuleV2.getCityConditionGroup().getTakeOffLandingCityGroup().getAirTakeoffLandingTypes())) {
            Optional<AirFlightRuleCheckRes.PerRuleCheckRes> checkRes = airCityCheckRes.getPerRuleCheckResList().stream()
                    .filter(a -> Objects.equals(a.getErrCode(), airRuleMsg.getCode())).findFirst();
            // 是否超规
            boolean isExceed = airCityCheckRes.getPerRuleCheckResList().stream()
                    .anyMatch(a -> Objects.equals(a.getErrCode(), airRuleMsg.getCode())
                            && Objects.equals(a.getIsExceed(), Boolean.TRUE));
            // 起降城市添加了无论是否超规则都加了ErrCode和 规则描述。eg.从{0}起飞到任意城市降落的航班
            boolean isNotExceed = airCityCheckRes.getPerRuleCheckResList().stream()
                    .anyMatch(a -> Objects.equals(a.getErrCode(), airRuleMsg.getCode())
                            && Objects.equals(a.getIsCheckOpen(), Boolean.TRUE)
                            && Objects.equals(a.getIsExceed(), Boolean.FALSE));

            if (checkRes.isPresent() && (isExceed || isNotExceed)) {
                RuleDetail ruleDetail = new RuleDetail();
                setRuleResult(isExceed, isNotExceed, ruleDetail);
                String ruleLimitDesc = checkRes.get().getErrMsg();
                ruleDetail.setRuleLimit(ruleLimitDesc);
                ruleDetailList.add(ruleDetail);
            }
        }
    }

    private void setCityRuleDetail(AirFlightRuleCheckRes airCityCheckRes, HashMap<String, List<RuleDetail>> ruleDetailMapByTiTle,
                                   List<AirTakeoffLandingType> airTakeoffLandingTypeList, AirRuleMsg airRuleMsg, String title, String ruleDesc) {
        RuleDetail ruleDetail = new RuleDetail();
        boolean isCityExceed = airCityCheckRes.getPerRuleCheckResList()
                .stream().anyMatch(a -> Objects.equals(a.getErrCode(), airRuleMsg.getCode())
                        && Objects.equals(a.getIsExceed(), Boolean.TRUE));
        ruleDetail.setRuleResult(!isCityExceed);
        // 城市名称
        ArrayList<String> cityNameList = new ArrayList<>();
        for (AirTakeoffLandingType landingType : airTakeoffLandingTypeList) {
            List<String> names = landingType.getAirTakeoffLandingCities().stream().map(AirTakeoffLandingCity::getCityName).collect(Collectors.toList());
            cityNameList.addAll(names);
        }
        String allCityName = String.join("、", cityNameList);
        String ruleLimit = String.format(ruleDesc, allCityName);
        ruleDetail.setRuleLimit(ruleLimit); // 规则描述
        if (CollectionUtils.isEmpty(ruleDetailMapByTiTle.get(title))) {
            ruleDetailMapByTiTle.put(title, new ArrayList<>());
        }
        ruleDetailMapByTiTle.get(title).add(ruleDetail);
    }

    private AirFlightRuleCheckReq.FlightInfo getFlightInfo(String companyId, String token, AirRuleCheckReq req, AirRuleV2 airRuleV2) {
        AirFlightRuleCheckReq.FlightInfo flightInfo = new AirFlightRuleCheckReq.FlightInfo();
        flightInfo.setIsAir2stScreenCheck(true); // 二屏校验
        flightInfo.setToken(token);
        flightInfo.setCompanyId(companyId);
        int flightType = req.getFlightType() == null ? 1 : req.getFlightType();
        flightInfo.setIsGoBack(flightType == 2);
        flightInfo.setCouponInfo(req.getCouponInfo());
        flightInfo.setDiscount(req.getDiscount());
        flightInfo.setCabinFullPrice(req.getCabinFullPrice());
        flightInfo.setGoStartDateStr(req.getDepTime());
        flightInfo.setGoEndDateStr(req.getArrTime());
        flightInfo.setCabin(req.getAirType());
//        flightInfo.setPerTicketPrice(req.getPrice());

        // 最低价校验参数
        AirInterceptRecordContract airInterceptRecordContract = new AirInterceptRecordContract();
        airInterceptRecordContract.setDestination_code(req.getArrCode());
        airInterceptRecordContract.setStarting_code(req.getDepCode());
        airInterceptRecordContract.setSale_price(req.getPrice());
        airInterceptRecordContract.setIs_middle_stop(req.getIsMiddleStop());
        airInterceptRecordContract.setTrip_type(1);
        airInterceptRecordContract.setCabin_type(Integer.valueOf(req.getAirType()));
        airInterceptRecordContract.setDiscount(flightInfo.getDiscount());
        airInterceptRecordContract.setCabin_full_price(flightInfo.getCabinFullPrice());

        // 中转设置每程的信息
        if (ObjUtils.isNotEmpty(req.getSegmentList())) {
            List<AirInterceptRecordContract> airParams = req.getSegmentList().stream().map(segment -> {
                AirInterceptRecordContract airParam = new AirInterceptRecordContract();
                airParam.setDestination_code(segment.getArrCode());
                airParam.setStarting_code(segment.getDepCode());
                airParam.setSale_price(segment.getPrice());
                airParam.setIs_middle_stop(segment.getIsMiddleStop());
                airParam.setTrip_type(1);
                airParam.setCabin_type(Integer.valueOf(segment.getAirType()));
                airParam.setDiscount(segment.getDiscount());
                airParam.setCabin_full_price(segment.getCabinFullPrice());
                return airParam;
            }).collect(Collectors.toList());
            flightInfo.setOrderParameterJsonList(airParams);
        }
        // 非中转
        else {
            flightInfo.setOrderParameterJsonList(Lists.newArrayList(airInterceptRecordContract));
        }

        flightInfo.setOrderParameterJson(airInterceptRecordContract);
        flightInfo.setStartCityId(req.getDepCityId());
        flightInfo.setArrivalCityId(req.getArrCityId());
        flightInfo.setStartCityIds(Lists.newArrayList(req.getDepCityId()));
        flightInfo.setArrivalCityIds(Lists.newArrayList(req.getArrCityId()));
        // flightInfo.setStartCityIds(reqContract.getTravel_on_busi_parameter_req_contract().getStart_city_ids());
        // flightInfo.setArrivalCityIds(reqContract.getTravel_on_busi_parameter_req_contract().getArrival_city_ids());
        flightInfo.setOrderPrice(req.getPrice());
        flightInfo.setPersonCount(NumberUtils.INTEGER_ONE); // 计算折扣需要人数

        boolean isOpenLowestPriceLimit = Objects.nonNull(airRuleV2) && Objects.nonNull(airRuleV2.getAirBookConditionGroup())
                && Objects.equals(airRuleV2.getAirBookConditionGroup().getAirBookConditionGroupFlag(), true)
                && Objects.nonNull(airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup())
                && Objects.equals(airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup().getLowPriceFlag(), 1);
        flightInfo.setLowPriceRecommend(!isOpenLowestPriceLimit);
//
//        BigDecimal fuelTax = req.getFuelTax()==null ? BigDecimal.ZERO : req.getFuelTax();
//        BigDecimal airportTax = req.getAirportTax()==null ? BigDecimal.ZERO : req.getAirportTax();

//        flightInfo.setPerTicketPriceAll(req.getPrice().add(fuelTax).add(airportTax).setScale(2, BigDecimal.ROUND_HALF_UP));
        return flightInfo;
    }

    /**
     * @MethodName checkAir2ScreenChangeRule
     * @Description 改期 机票二屏幕
     * @param: companyId
     * @param: userId
     * @param: token
     * @param: reqList
     * @return: java.util.List<com.fenbeitong.saas.core.contract.rule.AirRuleCheckRes>
     * <AUTHOR> Yunpeng
     * @Date 2022/11/18 11:50
     */
    private List<AirRuleCheckRes> checkAir2ScreenChangeRule(String companyId, String userId, String token, List<AirRuleCheckReq> reqList) {
        List<AirRuleCheckRes> results = Lists.newLinkedList();
        EmployeeAirRule employeeAirRule = iBaseEmployeeAirRuleService.queryEmployeeAirRuleByPrimaryKey(userId, companyId);
        if (Objects.isNull(employeeAirRule)) return results;
        logger.info("员工机票规则: {}", JsonUtils.toJsonStr(employeeAirRule));
        String ruleId = ObjUtils.ifNull(employeeAirRule.getManual_air_rule_id(), employeeAirRule.getDefault_air_rule_id());
        // v1版本机票规则
        // AirRule airRule = airRuleMapper.selectByPrimaryKey(ruleId);
        // logger.info("v1机票规则: {}", JsonUtils.toJsonStr(airRule));

        // v2超规个人付新版机票规则查询
        logger.info("v2机票规则req: ruleId={}, companyId={}", ruleId, companyId);
        AirRuleV2 airRuleV2 = iRuleV2Service.getAirRuleV2ById(ruleId, companyId);
        logger.info("v2机票规则rep: {}", JsonUtils.toJsonStr(airRuleV2));
        if (Objects.isNull(airRuleV2)
                || Objects.isNull(airRuleV2.getAirBookConditionGroup())
                || Objects.isNull(airRuleV2.getAirBookConditionGroup().getChangeTicketGroup())) {
            logger.info("Objects.isNull(airRulev2)");
            return results;
        }

        reqList = reqList.stream().sorted(Comparator.comparing(AirRuleCheckReq::getPrice)).collect(Collectors.toList());
        // 改期的校验：可改期舱位、改期航班价格，航班改期费用、改期原因
        // 对每个舱位进行的规则校验
        for (AirRuleCheckReq req : reqList) {
            AirOrderCheckReqContract reqContract = this.buildAirRuleCheckContract(req, companyId);
            AirRuleCheckRes checkRes = new AirRuleCheckRes();
            checkRes.setCabin(req.getCabin());
            List<AirRuleCheckResultDetail> checkResults = Lists.newArrayList();
            checkResults.add(airCheckService.checkAirChangeCabinRule(airRuleV2, reqContract)); // 舱等
            checkResults.add(airCheckService.checkAirChangePriceRule(airRuleV2, reqContract)); // 票价
            checkResults.add(airCheckService.checkAirChangeChangeCostRule(airRuleV2, reqContract)); // 改期手续费
            checkResults.add(airCheckService.checkAirChangeReasonRule(airRuleV2, reqContract)); // 改期理由
            checkRes.setAirRuleCheckResults(checkResults);
            results.add(checkRes);
        }
        return results;
    }

    private AirOrderCheckReqContract buildCommonAirRuleContract(AirRuleCheckReq req) {
        AirOrderCheckReqContract reqContract = new AirOrderCheckReqContract();
        reqContract.setAir_type(req.getAirType());
        reqContract.setPrice(req.getPrice());
        reqContract.setFlight_type(req.getFlightType());
        reqContract.setDiscount(req.getDiscount());
        TravelOnBusiOrderCheckReqContract.TravelOnBusiParameterReqContract bizReqContract = new TravelOnBusiOrderCheckReqContract.TravelOnBusiParameterReqContract();
        bizReqContract.setStart_city_ids(Collections.singletonList(req.getDepCityId()));
        bizReqContract.setArrival_city_ids(Collections.singletonList(req.getArrCityId()));
        bizReqContract.setStart_city_id(req.getDepCityId());
        bizReqContract.setArrival_city_id(req.getArrCityId());
        bizReqContract.setStart_time(req.getDepTime());
        bizReqContract.setEnd_time(req.getArrTime());
        reqContract.setTravel_on_busi_parameter_req_contract(bizReqContract);
        return reqContract;
    }

    private AirOrderCheckReqContract buildAirRuleCheckContract(AirRuleCheckReq req, String companyId) {
        AirOrderCheckReqContract reqContract = new AirOrderCheckReqContract();
        reqContract.setAir_type(req.getAirType());
        reqContract.setPrice(req.getPrice());
        reqContract.setFlight_type(req.getFlightType());
        reqContract.setDiscount(req.getDiscount());
        TravelOnBusiOrderCheckReqContract.TravelOnBusiParameterReqContract bizReqContract = new TravelOnBusiOrderCheckReqContract.TravelOnBusiParameterReqContract();
        bizReqContract.setStart_city_id(req.getDepCityId());
        bizReqContract.setArrival_city_id(req.getArrCityId());
        bizReqContract.setStart_time(req.getDepTime());
        bizReqContract.setEnd_time(req.getArrTime());
        reqContract.setTravel_on_busi_parameter_req_contract(bizReqContract);
        AirInterceptRecordContract orderInfoParam = new AirInterceptRecordContract();
        orderInfoParam.setStarting_code(req.getDepCode());
        orderInfoParam.setDestination_code(req.getArrCode());
        reqContract.setOrder_parameter_json(orderInfoParam);
        TravelOnBusiOrderCheckReqContract.TravelOnBusiCommonReqContract commonReqContract = new TravelOnBusiOrderCheckReqContract.TravelOnBusiCommonReqContract();
        commonReqContract.setCompany_id(companyId);
        reqContract.setTravel_on_busi_common_req_contract(commonReqContract);
        // 优惠券信息（企业/个人）
        reqContract.setCouponInfo(req.getCouponInfo());
        return reqContract;
    }

    private KafkaCompanyLogMsg getKafkaCompanyLogMsg(LogOperateActionEnum logOperateActionEnum, String content) {
        return CompanyLogMsgUtil.getKafkaCompanyLogMsg(LogOperatePageEnum.Air_Rule, logOperateActionEnum, LogOperateObjectEnum.AIR_RULE, content);
    }

}
