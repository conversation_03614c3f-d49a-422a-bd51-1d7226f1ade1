package com.fenbeitong.saas.core.contract.organization;

import com.fenbeitong.saas.core.contract.organization.inner.*;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/5/20.
 */
public class SetRulesReqContract {

    private String client_type;

    private String client_version;
    /**
     * 差旅权限规则-机、酒、火
     */
    private BizTripPolicyBean biz_trip_policy;

    private AirPolicyBean air_policy;

    private HotelPolicyBean hotel_policy;

    private TrainPolicyBean train_policy;
    /**
     * 用车权限规则
     */
    private CarPolicyBean car_policy;
    /**
     * 采购权限规则
     */
    private MallPolicyBean mall_policy;
    /**
     * 用餐权限规则
     */
    private DinnerPolicyBean dinner_policy;
    /**
     * 规则应用到员工
     */
    private List<String> employee_ids;

    private IntlAirPolicyBean intl_air_policy;

    public BizTripPolicyBean getBiz_trip_policy() {
        return biz_trip_policy;
    }

    public void setBiz_trip_policy(BizTripPolicyBean biz_trip_policy) {
        this.biz_trip_policy = biz_trip_policy;
    }

    public CarPolicyBean getCar_policy() {
        return car_policy;
    }

    public void setCar_policy(CarPolicyBean car_policy) {
        this.car_policy = car_policy;
    }

    public MallPolicyBean getMall_policy() {
        return mall_policy;
    }

    public void setMall_policy(MallPolicyBean mall_policy) {
        this.mall_policy = mall_policy;
    }

    public List<String> getEmployee_ids() {
        return employee_ids;
    }

    public void setEmployee_ids(List<String> employee_ids) {
        this.employee_ids = employee_ids;
    }

    public DinnerPolicyBean getDinner_policy() {
        return dinner_policy;
    }

    public void setDinner_policy(DinnerPolicyBean dinner_policy) {
        this.dinner_policy = dinner_policy;
    }

    public AirPolicyBean getAir_policy() {
        return air_policy;
    }

    public void setAir_policy(AirPolicyBean air_policy) {
        this.air_policy = air_policy;
    }

    public HotelPolicyBean getHotel_policy() {
        return hotel_policy;
    }

    public void setHotel_policy(HotelPolicyBean hotel_policy) {
        this.hotel_policy = hotel_policy;
    }

    public TrainPolicyBean getTrain_policy() {
        return train_policy;
    }

    public void setTrain_policy(TrainPolicyBean train_policy) {
        this.train_policy = train_policy;
    }

    public String getClient_type() {
        return client_type;
    }

    public void setClient_type(String client_type) {
        this.client_type = client_type;
    }

    public String getClient_version() {
        return client_version;
    }

    public void setClient_version(String client_version) {
        this.client_version = client_version;
    }

    public IntlAirPolicyBean getIntl_air_policy() {
        return intl_air_policy;
    }

    public void setIntl_air_policy(IntlAirPolicyBean intl_air_policy) {
        this.intl_air_policy = intl_air_policy;
    }
}
