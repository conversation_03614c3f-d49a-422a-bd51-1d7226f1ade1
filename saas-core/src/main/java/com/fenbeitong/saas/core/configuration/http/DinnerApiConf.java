//package com.fenbeitong.saas.core.configuration.http;
//
//import com.fenbeitong.dinner.api.client.MultiSceneHttpClient;
//import com.fenbeitong.dinner.api.http.HttpClientConfig;
//import com.fenbeitong.dinner.api.http.HttpClientFactory;
//import com.fenbeitong.saas.core.utils.tools.GlobalPropertyTool;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//import java.util.Properties;
//
///**
// * 临时读取权限配置。后期须改为uc控制。
// * @Description
// * <AUTHOR>
// * @Date: 2022/7/19
// */
//@Configuration
//public class DinnerApiConf {
//    private static final Properties CONFIG = GlobalPropertyTool.genPropertyContainer("conf/hosts.properties");
//
//    @Bean
//    public HttpClientFactory getDinnerApiFactory() {
//        String hostName = CONFIG.getProperty("host.dinner.bass");
//        HttpClientConfig httpConfig = new HttpClientConfig();
//        httpConfig.setHostName(hostName);
//        HttpClientFactory dinnerApiFactory = new HttpClientFactory();
//        dinnerApiFactory.setHttpConfig(httpConfig);
//        return dinnerApiFactory;
//    }
//
//    @Bean
//    public MultiSceneHttpClient getMultiSceneHttpClient(HttpClientFactory clientFactory) {
//        return clientFactory.createHttpClient(MultiSceneHttpClient.class);
//    }
//}
