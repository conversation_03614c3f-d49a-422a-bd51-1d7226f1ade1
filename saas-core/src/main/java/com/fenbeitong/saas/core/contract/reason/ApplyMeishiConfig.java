package com.fenbeitong.saas.core.contract.reason;

/**
 * <AUTHOR>
 * @date 2020/07/02
 */
public class ApplyMeishiConfig {
    /**
     * 费用归属  0：不展示，1：展示选填，2：展示必填
     */
    private Integer apply_attribution_category;
    /**
     * 申请单费用归属代入订单且不可修改
     * 是否勾选 0：未勾选，1：勾选
     */
    private Integer apply_attribution_category_modifiable;

    public Integer getApply_attribution_category() {
        return apply_attribution_category;
    }

    public void setApply_attribution_category(Integer apply_attribution_category) {
        this.apply_attribution_category = apply_attribution_category;
    }

    public Integer getApply_attribution_category_modifiable() {
        return apply_attribution_category_modifiable;
    }

    public void setApply_attribution_category_modifiable(Integer apply_attribution_category_modifiable) {
        this.apply_attribution_category_modifiable = apply_attribution_category_modifiable;
    }

    @Override
    public String toString() {
        return "ApplyMeishiConfig{" +
                "apply_attribution_category=" + apply_attribution_category +
                ", apply_attribution_category_modifiable=" + apply_attribution_category_modifiable +
                '}';
    }
}
