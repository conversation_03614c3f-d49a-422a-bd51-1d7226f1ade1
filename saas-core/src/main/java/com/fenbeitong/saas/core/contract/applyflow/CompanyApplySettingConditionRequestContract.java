package com.fenbeitong.saas.core.contract.applyflow;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by <PERSON>iabin on 2017/4/21.
 */
public class CompanyApplySettingConditionRequestContract {
    List<CompanyApplyFlowItemSetV2RequestContract> flow;
    private BigDecimal condition_min;
    private BigDecimal condition_max;

    public BigDecimal getCondition_min() {
        return condition_min;
    }

    public void setCondition_min(BigDecimal condition_min) {
        this.condition_min = condition_min;
    }

    public BigDecimal getCondition_max() {
        return condition_max;
    }

    public void setCondition_max(BigDecimal condition_max) {
        this.condition_max = condition_max;
    }

    public List<CompanyApplyFlowItemSetV2RequestContract> getFlow() {
        return flow;
    }

    public void setFlow(List<CompanyApplyFlowItemSetV2RequestContract> flow) {
        this.flow = flow;
    }
}
