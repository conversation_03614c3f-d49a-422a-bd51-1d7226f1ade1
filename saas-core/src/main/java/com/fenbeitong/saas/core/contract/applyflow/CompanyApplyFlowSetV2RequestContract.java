package com.fenbeitong.saas.core.contract.applyflow;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by xiabin on 2017/4/21.
 */
public class CompanyApplyFlowSetV2RequestContract {
    /**
     * 主键id
     */
    private String id;
    /**
     * 自定义审批了流名称
     */
    private String flow_name;
    /**
     * 审批流类型
     */
    private Integer company_apply_type;

    /**
     * 业务类型
     */
    private Integer apply_type;

    /**
     * 固定审批流ID和分条件审批流ID
     */
    private String apply_flow_id;

    /**
     * 是否应用至全公司
     */
    private boolean apply_for_company;

    private Integer cost_attribution_category;

    public boolean isApply_for_company() {
        return apply_for_company;
    }

    public void setApply_for_company(boolean apply_for_company) {
        this.apply_for_company = apply_for_company;
    }

    /**
     * company_apply_type = 固定审批流时的审批流节点
     */
    private List<CompanyApplyFlowItemSetV2RequestContract> fixation_flow_list;
    /**
     * company_apply_type = 分条件审批流时的审批流节点
     */
    private List<CompanyApplySettingConditionRequestContract> conditional_flow_list;

    /**
     * 抄送人列表
     */
    private List<CompanyApplyFlowItemSetV2RequestContract> cc_list;

    /**
     * 抄送通知
     */
    private Integer cc_notice_type;

    private int company_apply_setting_use_count;

    private KvContract cc_notice_type_enums;

    private BigDecimal budget;

    private Integer company_setting_type;

    private Integer can_delete;

    private int company_apply_setting_use_project_count;

    private Integer cost_center_status;

    /**
     * 使用超规流程 0.不使用 1.使用
     */
    private Integer use_beyond_rule_flow;

    /**
     * 订单审批普通审批流类型
     */
    private Integer normal_company_apply_type;

    /**
     * normal_company_apply_type = 固定审批流时的审批流节点
     */
    private List<CompanyApplyFlowItemSetV2RequestContract> normal_fixation_flow_list;
    /**
     * normal_company_apply_type = 分条件审批流时的审批流节点
     */
    private List<CompanyApplySettingConditionRequestContract> normal_conditional_flow_list;

    /**
     * 订单审批普通审批流抄送人列表
     */
    private List<CompanyApplyFlowItemSetV2RequestContract> normal_cc_list;

    /**
     * 订单审批普通审批流抄送通知
     */
    private Integer normal_cc_notice_type;

    private KvContract normal_cc_notice_type_enums;

    private Integer role_approve_type;

    /**
     * 使用超规流程 0.不使用 1.使用
     */
    private Integer use_change_rule_flow;

    /**
     * 订单审批改期固定审批流列表
     */
    private List<CompanyApplyFlowItemSetV2RequestContract> change_fixation_flow_list;

    /**
     * 订单审批改期分条件审批流列表
     */
    private List<CompanyApplySettingConditionRequestContract> change_conditional_flow_list;

    /**
     * 订单审批改期审批流抄送人列表
     */
    private List<CompanyApplyFlowItemSetV2RequestContract> change_cc_list;

    /**
     * 订单审批改期审批流类型
     */
    private Integer change_company_apply_type;

    /**
     * 使用超规流程 0.不使用 1.使用
     */
    private Integer use_refund_rule_flow;

    /**
     * 订单审批退订固定审批流列表
     */
    private List<CompanyApplyFlowItemSetV2RequestContract> refund_fixation_flow_list;

    /**
     * 订单审批退订分条件审批流列表
     */
    private List<CompanyApplySettingConditionRequestContract> refund_conditional_flow_list;

    /**
     * 订单审批退订审批流抄送人列表
     */
    private List<CompanyApplyFlowItemSetV2RequestContract> refund_cc_list;

    /**
     * 订单审批退订审批流类型
     */
    private Integer refund_company_apply_type;

    /**
     *  是否异常审批
     */
    private Boolean abnormalApproval;

    private String coupon_id;

    private String flow_hint = "";

    public CompanyApplyFlowSetV2RequestContract() {
    }

    public CompanyApplyFlowSetV2RequestContract(String flow_name, Integer company_apply_type, Integer apply_type, String apply_flow_id, List<CompanyApplyFlowItemSetV2RequestContract> fixation_flow_list,List<CompanyApplySettingConditionRequestContract> conditional_flow_list) {
        this.flow_name = flow_name;
        this.company_apply_type = company_apply_type;
        this.apply_type = apply_type;
        this.apply_flow_id = apply_flow_id;
        this.fixation_flow_list = fixation_flow_list;
        this.conditional_flow_list = conditional_flow_list;
    }

    public String getFlow_name() {
        return flow_name;
    }

    public void setFlow_name(String flow_name) {
        this.flow_name = flow_name;
    }

    public Integer getCompany_apply_type() {
        return company_apply_type;
    }

    public void setCompany_apply_type(Integer company_apply_type) {
        this.company_apply_type = company_apply_type;
    }

    public Integer getApply_type() {
        return apply_type;
    }

    public void setApply_type(Integer apply_type) {
        this.apply_type = apply_type;
    }

    public String getApply_flow_id() {
        return apply_flow_id;
    }

    public void setApply_flow_id(String apply_flow_id) {
        this.apply_flow_id = apply_flow_id;
    }

    public List<CompanyApplyFlowItemSetV2RequestContract> getFixation_flow_list() {
        return fixation_flow_list;
    }

    public void setFixation_flow_list(List<CompanyApplyFlowItemSetV2RequestContract> fixation_flow_list) {
        this.fixation_flow_list = fixation_flow_list;
    }

    public List<CompanyApplySettingConditionRequestContract> getConditional_flow_list() {
        return conditional_flow_list;
    }

    public void setConditional_flow_list(List<CompanyApplySettingConditionRequestContract> conditional_flow_list) {
        this.conditional_flow_list = conditional_flow_list;
    }

    public List<CompanyApplyFlowItemSetV2RequestContract> getCc_list() {
        return cc_list;
    }

    public void setCc_list(List<CompanyApplyFlowItemSetV2RequestContract> cc_list) {
        this.cc_list = cc_list;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getCc_notice_type() {
        return cc_notice_type;
    }

    public void setCc_notice_type(Integer cc_notice_type) {
        this.cc_notice_type = cc_notice_type;
    }

    public int getCompany_apply_setting_use_count() {
        return company_apply_setting_use_count;
    }

    public void setCompany_apply_setting_use_count(int company_apply_setting_use_count) {
        this.company_apply_setting_use_count = company_apply_setting_use_count;
    }

    public KvContract getCc_notice_type_enums() {
        return cc_notice_type_enums;
    }

    public void setCc_notice_type_enums(KvContract cc_notice_type_enums) {
        this.cc_notice_type_enums = cc_notice_type_enums;
    }

    public BigDecimal getBudget() {
        return budget;
    }

    public void setBudget(BigDecimal budget) {
        this.budget = budget;
    }

    public Integer getCompany_setting_type() {
        return company_setting_type;
    }

    public void setCompany_setting_type(Integer company_setting_type) {
        this.company_setting_type = company_setting_type;
    }

    public Integer getCan_delete() {
        return can_delete;
    }

    public void setCan_delete(Integer can_delete) {
        this.can_delete = can_delete;
    }

    public int getCompany_apply_setting_use_project_count() {
        return company_apply_setting_use_project_count;
    }

    public void setCompany_apply_setting_use_project_count(int company_apply_setting_use_project_count) {
        this.company_apply_setting_use_project_count = company_apply_setting_use_project_count;
    }

    public Integer getCost_attribution_category() {
        return cost_attribution_category;
    }

    public void setCost_attribution_category(Integer cost_attribution_category) {
        this.cost_attribution_category = cost_attribution_category;
    }

    public Integer getCost_center_status() {
        return cost_center_status;
    }

    public void setCost_center_status(Integer cost_center_status) {
        this.cost_center_status = cost_center_status;
    }

    public Integer getUse_beyond_rule_flow() {
        return use_beyond_rule_flow;
    }

    public void setUse_beyond_rule_flow(Integer use_beyond_rule_flow) {
        this.use_beyond_rule_flow = use_beyond_rule_flow;
    }

    public Integer getNormal_company_apply_type() {
        return normal_company_apply_type;
    }

    public void setNormal_company_apply_type(Integer normal_company_apply_type) {
        this.normal_company_apply_type = normal_company_apply_type;
    }

    public List<CompanyApplyFlowItemSetV2RequestContract> getNormal_fixation_flow_list() {
        return normal_fixation_flow_list;
    }

    public void setNormal_fixation_flow_list(List<CompanyApplyFlowItemSetV2RequestContract> normal_fixation_flow_list) {
        this.normal_fixation_flow_list = normal_fixation_flow_list;
    }

    public List<CompanyApplySettingConditionRequestContract> getNormal_conditional_flow_list() {
        return normal_conditional_flow_list;
    }

    public void setNormal_conditional_flow_list(List<CompanyApplySettingConditionRequestContract> normal_conditional_flow_list) {
        this.normal_conditional_flow_list = normal_conditional_flow_list;
    }

    public List<CompanyApplyFlowItemSetV2RequestContract> getNormal_cc_list() {
        return normal_cc_list;
    }

    public void setNormal_cc_list(List<CompanyApplyFlowItemSetV2RequestContract> normal_cc_list) {
        this.normal_cc_list = normal_cc_list;
    }

    public Integer getNormal_cc_notice_type() {
        return normal_cc_notice_type;
    }

    public void setNormal_cc_notice_type(Integer normal_cc_notice_type) {
        this.normal_cc_notice_type = normal_cc_notice_type;
    }

    public KvContract getNormal_cc_notice_type_enums() {
        return normal_cc_notice_type_enums;
    }

    public void setNormal_cc_notice_type_enums(KvContract normal_cc_notice_type_enums) {
        this.normal_cc_notice_type_enums = normal_cc_notice_type_enums;
    }

    public Integer getRole_approve_type() {
        return role_approve_type;
    }

    public void setRole_approve_type(Integer role_approve_type) {
        this.role_approve_type = role_approve_type;
    }

    public Boolean getAbnormalApproval() {
        return abnormalApproval;
    }

    public void setAbnormalApproval(Boolean abnormalApproval) {
        this.abnormalApproval = abnormalApproval;
    }

    public Integer getUse_change_rule_flow() {
        return use_change_rule_flow;
    }

    public void setUse_change_rule_flow(Integer use_change_rule_flow) {
        this.use_change_rule_flow = use_change_rule_flow;
    }

    public List<CompanyApplyFlowItemSetV2RequestContract> getChange_fixation_flow_list() {
        return change_fixation_flow_list;
    }

    public void setChange_fixation_flow_list(List<CompanyApplyFlowItemSetV2RequestContract> change_fixation_flow_list) {
        this.change_fixation_flow_list = change_fixation_flow_list;
    }

    public List<CompanyApplySettingConditionRequestContract> getChange_conditional_flow_list() {
        return change_conditional_flow_list;
    }

    public void setChange_conditional_flow_list(List<CompanyApplySettingConditionRequestContract> change_conditional_flow_list) {
        this.change_conditional_flow_list = change_conditional_flow_list;
    }

    public List<CompanyApplyFlowItemSetV2RequestContract> getChange_cc_list() {
        return change_cc_list;
    }

    public void setChange_cc_list(List<CompanyApplyFlowItemSetV2RequestContract> change_cc_list) {
        this.change_cc_list = change_cc_list;
    }

    public Integer getChange_company_apply_type() {
        return change_company_apply_type;
    }

    public void setChange_company_apply_type(Integer change_company_apply_type) {
        this.change_company_apply_type = change_company_apply_type;
    }

    public Integer getUse_refund_rule_flow() {
        return use_refund_rule_flow;
    }

    public void setUse_refund_rule_flow(Integer use_refund_rule_flow) {
        this.use_refund_rule_flow = use_refund_rule_flow;
    }

    public List<CompanyApplyFlowItemSetV2RequestContract> getRefund_fixation_flow_list() {
        return refund_fixation_flow_list;
    }

    public void setRefund_fixation_flow_list(List<CompanyApplyFlowItemSetV2RequestContract> refund_fixation_flow_list) {
        this.refund_fixation_flow_list = refund_fixation_flow_list;
    }

    public List<CompanyApplySettingConditionRequestContract> getRefund_conditional_flow_list() {
        return refund_conditional_flow_list;
    }

    public void setRefund_conditional_flow_list(List<CompanyApplySettingConditionRequestContract> refund_conditional_flow_list) {
        this.refund_conditional_flow_list = refund_conditional_flow_list;
    }

    public List<CompanyApplyFlowItemSetV2RequestContract> getRefund_cc_list() {
        return refund_cc_list;
    }

    public void setRefund_cc_list(List<CompanyApplyFlowItemSetV2RequestContract> refund_cc_list) {
        this.refund_cc_list = refund_cc_list;
    }

    public Integer getRefund_company_apply_type() {
        return refund_company_apply_type;
    }

    public void setRefund_company_apply_type(Integer refund_company_apply_type) {
        this.refund_company_apply_type = refund_company_apply_type;
    }

    public String getCoupon_id() {
        return coupon_id;
    }

    public void setCoupon_id(String coupon_id) {
        this.coupon_id = coupon_id;
    }

    public String getFlow_hint() {
        return flow_hint;
    }

    public void setFlow_hint(String flow_hint) {
        this.flow_hint = flow_hint;
    }
}
