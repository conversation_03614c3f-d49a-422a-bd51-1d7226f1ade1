package com.fenbeitong.saas.core.contract.organization;

import com.fenbeitong.saas.core.contract.organization.inner.*;

/**
 * Created by wa<PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/8/29.
 */
public class RuleContract {

    private String client_type;

    private String client_version;

    private BizTripPolicyBean bizTripPolicyBean;

    private TrainPolicyBean trainPolicyBean;

    private HotelPolicyBean hotelPolicyBean;

    private AirPolicyBean airPolicyBean;
    private CarPolicyBean carPolicyBean;

    private MallPolicyBean mallPolicyBean;

    private String userId;

    private String companyId;

    private String orgUnitId;

    public BizTripPolicyBean getBizTripPolicyBean() {
        return bizTripPolicyBean;
    }

    public void setBizTripPolicyBean(BizTripPolicyBean bizTripPolicyBean) {
        this.bizTripPolicyBean = bizTripPolicyBean;
    }

    public CarPolicyBean getCarPolicyBean() {
        return carPolicyBean;
    }

    public void setCarPolicyBean(CarPolicyBean carPolicyBean) {
        this.carPolicyBean = carPolicyBean;
    }

    public MallPolicyBean getMallPolicyBean() {
        return mallPolicyBean;
    }

    public void setMallPolicyBean(MallPolicyBean mallPolicyBean) {
        this.mallPolicyBean = mallPolicyBean;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public String getOrgUnitId() {
        return orgUnitId;
    }

    public void setOrgUnitId(String orgUnitId) {
        this.orgUnitId = orgUnitId;
    }

    public TrainPolicyBean getTrainPolicyBean() {
        return trainPolicyBean;
    }

    public void setTrainPolicyBean(TrainPolicyBean trainPolicyBean) {
        this.trainPolicyBean = trainPolicyBean;
    }

    public HotelPolicyBean getHotelPolicyBean() {
        return hotelPolicyBean;
    }

    public void setHotelPolicyBean(HotelPolicyBean hotelPolicyBean) {
        this.hotelPolicyBean = hotelPolicyBean;
    }

    public AirPolicyBean getAirPolicyBean() {
        return airPolicyBean;
    }

    public void setAirPolicyBean(AirPolicyBean airPolicyBean) {
        this.airPolicyBean = airPolicyBean;
    }

    public String getClient_type() {
        return client_type;
    }

    public void setClient_type(String client_type) {
        this.client_type = client_type;
    }

    public String getClient_version() {
        return client_version;
    }

    public void setClient_version(String client_version) {
        this.client_version = client_version;
    }
}
