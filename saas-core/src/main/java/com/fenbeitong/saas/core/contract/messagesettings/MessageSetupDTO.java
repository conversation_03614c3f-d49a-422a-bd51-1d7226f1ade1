package com.fenbeitong.saas.core.contract.messagesettings;

import com.fenbeitong.saas.core.model.saas.MessageSetup;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> yanzeyu
 * @program: saas
 * @description:
 * @create: 2023/3/3 11:40
 */
@Data
public class MessageSetupDTO{
    /**
     * 主键
     */
    private Integer id;
    /**
     * 公司id
     */
    private String companyId;
    /**
     * 项目id
     */
    private String itemCode;
    /**
     * 用户id
     */
    private String userId;
    private Integer isChecked;
    /**
     * 是否区分场景
     * 1：区分场景
     * 2： 不区分场景
     */
    private Integer distinguishCategoryType;

    private String categoryTypeAmount;

    private Integer intVal1;

    private Integer intVal2;

    private Integer intVal3;

    private String strVal1;

    private String strVal2;

    private String strVal3;

    /**
     * 直接上级标识
     */
    private Integer directLeader;

    /**
     * 部门主管标识
     */
    private Integer departLeader;
}
