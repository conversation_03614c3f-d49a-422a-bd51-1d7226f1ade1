package com.fenbeitong.saas.core.contract.message.inner;

import com.alibaba.fastjson.JSONObject;
import io.swagger.models.auth.In;

/**
 * 审批通知附加信息
 */
public class ApplyInfo{
    /**
     * 51 差旅申请 52 用车申请 53 采购申请
     */
    private Integer apply_type;

    private String apply_time;

    private String apply_msg;

    private String applier_msg;

    private String applier_department;

    private String travel_msg;

    private boolean myself;

    //1.我申请的 2.提交到我的 3.抄送到我的
    private Integer view_type;

    private Integer log_id;

    private Integer setting_type;

    private String order_id;

    private String order_type;

    public Integer getView_type() {
        return view_type;
    }

    public void setView_type(Integer view_type) {
        this.view_type = view_type;
    }

    public boolean isMyself() {
        return myself;
    }

    public void setMyself(boolean myself) {
        this.myself = myself;
    }

    public Integer getApply_type() {
        return apply_type;
    }

    public void setApply_type(Integer apply_type) {
        this.apply_type = apply_type;
    }

    public String getApply_time() {
        return apply_time;
    }

    public void setApply_time(String apply_time) {
        this.apply_time = apply_time;
    }

    public String getApply_msg() {
        return apply_msg;
    }

    public void setApply_msg(String apply_msg) {
        this.apply_msg = apply_msg;
    }

    public String getApplier_msg() {
        return applier_msg;
    }

    public void setApplier_msg(String applier_msg) {
        this.applier_msg = applier_msg;
    }

    public String getApplier_department() {
        return applier_department;
    }

    public void setApplier_department(String applier_department) {
        this.applier_department = applier_department;
    }

    public String getTravel_msg() {
        return travel_msg;
    }

    public void setTravel_msg(String travel_msg) {
        this.travel_msg = travel_msg;
    }

    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }

    public Integer getLog_id() {
        return log_id;
    }

    public void setLog_id(Integer log_id) {
        this.log_id = log_id;
    }

    public Integer getSetting_type() {
        return setting_type;
    }

    public void setSetting_type(Integer setting_type) {
        this.setting_type = setting_type;
    }

    public String getOrder_id() {
        return order_id;
    }

    public void setOrder_id(String order_id) {
        this.order_id = order_id;
    }

    public String getOrder_type() {
        return order_type;
    }

    public void setOrder_type(String order_type) {
        this.order_type = order_type;
    }
}
