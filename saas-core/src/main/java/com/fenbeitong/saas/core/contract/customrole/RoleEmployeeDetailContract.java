package com.fenbeitong.saas.core.contract.customrole;

import java.util.List;

/**
 * Created by chenshang on 2017/4/24.
 */
public class RoleEmployeeDetailContract {
    long count;
    List<RoleEmployeeDetail> employees;

    public long getCount() {
        return count;
    }

    public void setCount(long count) {
        this.count = count;
    }

    public List<RoleEmployeeDetail> getEmployees() {
        return employees;
    }

    public void setEmployees(List<RoleEmployeeDetail> employees) {
        this.employees = employees;
    }

    public RoleEmployeeDetailContract(long count, List<RoleEmployeeDetail> employees) {
        this.count = count;
        this.employees = employees;
    }
}

