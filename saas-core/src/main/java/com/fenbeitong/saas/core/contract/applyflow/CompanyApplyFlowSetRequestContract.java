package com.fenbeitong.saas.core.contract.applyflow;

import java.util.List;

/**
 * Created by xiabin on 2017/4/21.
 */
public class CompanyApplyFlowSetRequestContract {
    /**
     * 自定义审批了流名称
     */
    private String flow_name;
    /**
     * 审批流类型
     */
    private Integer company_apply_type;

    /**
     * 业务类型
     */
    private Integer apply_type;

    /**
     * 固定审批流ID
     */
    private String apply_flow_id;
    /**
     * company_apply_type = 固定审批流时的审批流节点
     */
    private List<CompanyApplyFlowItemSetRequestContract> flow_list;

    public CompanyApplyFlowSetRequestContract() {
    }

    public CompanyApplyFlowSetRequestContract(String flow_name, Integer company_apply_type, Integer apply_type, String apply_flow_id, List<CompanyApplyFlowItemSetRequestContract> flow_list) {
        this.flow_name = flow_name;
        this.company_apply_type = company_apply_type;
        this.apply_type = apply_type;
        this.apply_flow_id = apply_flow_id;
        this.flow_list = flow_list;
    }

    public String getFlow_name() {
        return flow_name;
    }

    public void setFlow_name(String flow_name) {
        this.flow_name = flow_name;
    }

    public Integer getCompany_apply_type() {
        return company_apply_type;
    }

    public void setCompany_apply_type(Integer company_apply_type) {
        this.company_apply_type = company_apply_type;
    }

    public Integer getApply_type() {
        return apply_type;
    }

    public void setApply_type(Integer apply_type) {
        this.apply_type = apply_type;
    }

    public String getApply_flow_id() {
        return apply_flow_id;
    }

    public void setApply_flow_id(String apply_flow_id) {
        this.apply_flow_id = apply_flow_id;
    }

    public List<CompanyApplyFlowItemSetRequestContract> getFlow_list() {
        return flow_list;
    }

    public void setFlow_list(List<CompanyApplyFlowItemSetRequestContract> flow_list) {
        this.flow_list = flow_list;
    }
}
