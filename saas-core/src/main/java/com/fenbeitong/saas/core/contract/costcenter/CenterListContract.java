package com.fenbeitong.saas.core.contract.costcenter;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * Created by xuzn on 18/3/30.
 */
public class CenterListContract {
    private String id;
    private String code;
    private String name;
    @JsonIgnore
    private Integer state_type;
    private CostCenterListContract.StateTypeBean state;
    private String creator;
    private Long member_count;
    private boolean all_selected;
    private String member_name;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getState_type() {
        return state_type;
    }

    public void setState_type(Integer state_type) {
        this.state_type = state_type;
    }

    public CostCenterListContract.StateTypeBean getState() {
        return state;
    }

    public void setState(CostCenterListContract.StateTypeBean state) {
        this.state = state;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Long getMember_count() {
        return member_count;
    }

    public void setMember_count(Long member_count) {
        this.member_count = member_count;
    }

    public boolean isAll_selected() {
        return all_selected;
    }

    public void setAll_selected(boolean all_selected) {
        this.all_selected = all_selected;
    }

    public String getMember_name() {
        return member_name;
    }

    public void setMember_name(String member_name) {
        this.member_name = member_name;
    }
}
