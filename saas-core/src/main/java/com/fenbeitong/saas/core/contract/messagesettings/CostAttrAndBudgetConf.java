package com.fenbeitong.saas.core.contract.messagesettings;

import com.fenbeitong.saasplus.api.model.dto.setting.CostAttributionSettingApiVO;

/**
 * <AUTHOR>
 * @date 2020/7/10
 */
public class CostAttrAndBudgetConf {

    /**
     * 费用归属可选范围 1.部门 2.项目 3.部门或项目 4.部门和项目
     * @see com.fenbeitong.finhub.common.constant.CostAttributionScopeEnum
     */
    private Integer cost_attribution_scope;

    /**
     * 预算扣减类型(仅费用归属为部门和项目有效) 1.部门和项目 2.部门 3.项目
     * @see com.fenbeitong.finhub.common.constant.BudgetCostAttrTypeEnum
     */
    private Integer budget_cost_attr_type;

    private CostAttributionSettingApiVO costAttributionSettingApiVO;

    public Integer getCost_attribution_scope() {
        return cost_attribution_scope;
    }

    public void setCost_attribution_scope(Integer cost_attribution_scope) {
        this.cost_attribution_scope = cost_attribution_scope;
    }

    public Integer getBudget_cost_attr_type() {
        return budget_cost_attr_type;
    }

    public void setBudget_cost_attr_type(Integer budget_cost_attr_type) {
        this.budget_cost_attr_type = budget_cost_attr_type;
    }

    public CostAttributionSettingApiVO getCostAttributionSettingApiVO() {
        return costAttributionSettingApiVO;
    }

    public void setCostAttributionSettingApiVO(CostAttributionSettingApiVO costAttributionSettingApiVO) {
        this.costAttributionSettingApiVO = costAttributionSettingApiVO;
    }
}
