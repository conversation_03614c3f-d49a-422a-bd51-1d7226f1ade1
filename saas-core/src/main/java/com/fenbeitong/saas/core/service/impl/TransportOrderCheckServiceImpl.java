package com.fenbeitong.saas.core.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.finhub.common.constant.BudgetCategoryTypeEnum;
import com.fenbeitong.finhub.common.constant.CategoryTypeEnum;
import com.fenbeitong.finhub.common.constant.CostAttributionScopeEnum;
import com.fenbeitong.finhub.common.constant.PayModelEnum;
import com.fenbeitong.finhub.common.utils.EventParams;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.saas.core.common.DynamicDataSourceDecision;
import com.fenbeitong.saas.core.common.base.SaasException;
import com.fenbeitong.saas.core.common.constant.CommonSwitchConstant;
import com.fenbeitong.saas.core.common.constant.GlobalResponseCode;
import com.fenbeitong.saas.core.common.constant.SaasContentConstant;
import com.fenbeitong.saas.core.contract.cost.CostCheckVO;
import com.fenbeitong.saas.core.contract.messagesettings.CostAttrAndBudgetConf;
import com.fenbeitong.saas.core.contract.order.check.*;
import com.fenbeitong.saas.core.model.enums.EmployeeStatusEnum;
import com.fenbeitong.saas.core.model.enums.OrderCategory;
import com.fenbeitong.saas.core.service.IBaseOrganizationService;
import com.fenbeitong.saas.core.service.IMessageSetupService;
import com.fenbeitong.saas.core.service.IOrderCheckService;
import com.fenbeitong.saas.core.service.ITransportOrderCheckService;
import com.fenbeitong.saas.core.utils.tools.ConvertUtils;
import com.fenbeitong.saas.core.utils.tools.VersionTool;
import com.fenbeitong.saasplus.api.model.base.ApiResult;
import com.fenbeitong.saasplus.api.model.dto.commonRule.check.CheckRuleQueryDTO;
import com.fenbeitong.saasplus.api.model.dto.commonRule.check.RuleCheckResultDTO;
import com.fenbeitong.saasplus.api.model.dto.commonRule.check.RuleInvalidDTO;
import com.fenbeitong.saasplus.api.model.dto.commonRule.check.TransportParamDTO;
import com.fenbeitong.saasplus.api.model.dto.finance.CustomDimension;
import com.fenbeitong.saasplus.api.model.enums.commonRule.RuleCheckErrorEnum;
import com.fenbeitong.saasplus.api.model.enums.commonRule.RuleSceneEnum;
import com.fenbeitong.saasplus.api.service.commonRule.ICommonRuleRpcService;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyRuleDTO;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.fenbeitong.usercenter.api.model.enums.privilege.CodeTypeEnums;
import com.fenbeitong.usercenter.api.model.enums.privilege.CustomCodeType;
import com.fenbeitong.usercenter.api.model.po.rule.EmployeeBaseRule;
import com.fenbeitong.usercenter.api.service.company.ICompanyRuleService;
import com.fenbeitong.usercenter.api.service.privilege.IPrivilegeService;
import com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeRuleService;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ObjUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 货运订单校验
 *
 * <AUTHOR>
 * @date 2022/11/28
 **/
@Slf4j
@Service
public class TransportOrderCheckServiceImpl implements ITransportOrderCheckService {

    @Autowired
    private IBaseOrganizationService baseOrganizationService;
    @Autowired
    private ICompanyRuleService iCompanyRuleService;
    @Autowired
    private IOrderCheckService iOrderCheckService;
    @Autowired
    private IMessageSetupService iMessageSetupService;
    @Autowired
    private IPrivilegeService iPrivilegeService;
    @Autowired
    private CurrencyCheckServiceImpl currencyCheckService;
    @Autowired
    private com.fenbeitong.saasplus.api.service.messageSetup.IMessageSetupService iMessageSetupRpcService;
    @Autowired
    private IBaseEmployeeRuleService iBaseEmployeeRuleService;
    @Autowired
    ICommonRuleRpcService iCommonRuleRpcService;

    @Override
    public TravelOnBusiOrderCheckResContract orderCheck(TransportOrderCheckReqContract reqContract, String clientVersion) {
        DynamicDataSourceDecision.determineDataSource(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        // 返回结果
        TravelOnBusiOrderCheckResContract resContract = new TravelOnBusiOrderCheckResContract();
        // 校验权限问题
        TravelOnBusiOrderRuleCheckResult ruleCheckResult = transportOrderRuleCheckResult(reqContract, clientVersion);
        resContract.setErr_code(ruleCheckResult.getErrCode());
        resContract.setErr_msg(ruleCheckResult.getErrMsg());
        resContract.setExceed_msg(ruleCheckResult.getExceed_msg());
        resContract.setPersonal_pay(ruleCheckResult.getPersonalPay());
        resContract.setLimit_price(ruleCheckResult.getLimitPrice());
        resContract.setLimit_type(ruleCheckResult.getLimitType());
        resContract.setIs_price_exceed(ruleCheckResult.getIsPriceExceed());
        resContract.setIs_estimated_amt_exceed(ruleCheckResult.getIs_estimated_amt_exceed());
        resContract.setMin_price_limit(ruleCheckResult.getMinPriceLimit());
        resContract.setPrice_exceed_sum(ruleCheckResult.getPriceExceedSum());
        resContract.setCost_attribution_scope(ruleCheckResult.getCost_attribution_scope());
        resContract.setBudget_cost_attr_type(ruleCheckResult.getBudget_cost_attr_type());
        resContract.setPay_model(ruleCheckResult.getPayModel());
        resContract.setCompany_pay_price(ruleCheckResult.getCompanyPayPrice());
        resContract.setPersonal_pay_price(ruleCheckResult.getPersonalPayPrice());
        resContract.setReimbursable_price(ruleCheckResult.getReimbursablePrice());
        resContract.setUnreimbursable_price(ruleCheckResult.getUnreimbursablePrice());
        resContract.setAmount_compliance(ruleCheckResult.getAmountCompliance());
        resContract.setAmount_non_compliance(ruleCheckResult.getAmountNonCompliance());
        resContract.setCoupon_used_amount(ruleCheckResult.getCouponUsedAmount());
        resContract.setFrequent_info(ruleCheckResult.getFrequent_list());
        resContract.setErr_msg_info(ruleCheckResult.getErrMsgInfo());
        if (ruleCheckResult.getErrCode() == GlobalResponseCode.Success.getCode()) {
            // 成功
            resContract.setErr_type(0);
        } else if (RuleCheckErrorEnum.EXCEED_TRANSPORT_TYPES.getErrorCode() == ruleCheckResult.getErrCode()
            || RuleCheckErrorEnum.EXCEED_TRANSPORT_PRICELIMIT.getErrorCode() == ruleCheckResult.getErrCode()) {
            // 弹窗
            resContract.setErr_type(2);
        } else {
            // toast
            resContract.setErr_type(1);
        }

        if (ruleCheckResult.getErrCode() == GlobalResponseCode.Success.getCode()) {
            /**
             * 2022 06 27 占用减去优惠卷
             */
            BigDecimal costAmount = reqContract.getTravel_on_busi_common_req_contract().getOrder_price()
                .add(ObjUtils.toBigDecimal(reqContract.getTravel_on_busi_common_req_contract().getInsurance_price(), BigDecimal.ZERO));

            // 自定义字段转化
            List<CustomDimension> customDimensionList =
                ConvertUtils.convertToCustomDimension(reqContract.getCustomer_field_setting_list());

            TempOrderCheckResContract costResult = iOrderCheckService.saveCost(
                    reqContract.getTravel_on_busi_common_req_contract().getOrder_id(),
                    reqContract.getTravel_on_busi_common_req_contract().getCompany_id(),
                    reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(),
                    BudgetCategoryTypeEnum.FREIGHT_TRANSPORT,
                    costAmount,
                    reqContract.getCost_info(),
                    reqContract.getCostInfoString(),
                    clientVersion, customDimensionList,"");
            if (costResult.getErr_code() != GlobalResponseCode.Success.getCode()) {
                resContract.setErr_code(costResult.getErr_code());
                resContract.setErr_msg(costResult.getErr_msg());
                resContract.setErr_type(1);// toast
            } else {
                resContract.setCost_id(costResult.getCost_id());
            }
        }
        // 记录下单校验日志
        iOrderCheckService.saveOrderCheckLog(
            reqContract.getTravel_on_busi_common_req_contract().getCompany_id(),
            reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(),
            CategoryTypeEnum.FREIGHT_TRANSPORT.getCode(),
            clientVersion,
            JsonUtils.toJson(reqContract),
            JsonUtils.toJson(resContract),
            ObjUtils.toString(resContract.getErr_code()),
            ruleCheckResult.getSnapshotInfo().toJSONString(),
            ruleCheckResult.getExtInfo().toJSONString());
        return resContract;
    }

    private TravelOnBusiOrderRuleCheckResult transportOrderRuleCheckResult(TransportOrderCheckReqContract
                                                                               reqContract, String clientVersion) throws SaasException {
        // 默认初始化正常
        TravelOnBusiOrderRuleCheckResult checkResult = new TravelOnBusiOrderRuleCheckResult();
        checkResult.setResCode(GlobalResponseCode.Success);
        checkResult.setPersonalPay(false);
        checkResult.setIsPriceExceed(false);
        // 快照信息
        JSONObject snapshotInfo = new JSONObject();
        checkResult.setSnapshotInfo(snapshotInfo);
        // 查询是否个人垫付模式
        Map<String, Boolean> funcMap = iPrivilegeService.queryFunctionMoudle(CodeTypeEnums.CustomAuth.getKey(), reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(), CustomCodeType.M_ADVANCE_PAYMENT.getKey());
        Boolean advancePayment = funcMap.get(CustomCodeType.M_ADVANCE_PAYMENT.getKey());
        log.info("[货运下单校验]，是否个人垫付模式:{}", advancePayment);
        PayModelEnum payModel = advancePayment ? PayModelEnum.PERSONAL_PREPAY : PayModelEnum.COMPANY_PAY;
        checkResult.setPayModel(payModel.getCode());
        snapshotInfo.put("payModel", payModel.getCode());
        //货运总价
        BigDecimal totalPrice = reqContract.getTravel_on_busi_common_req_contract().getTotal_price();
        //保险价格
        BigDecimal insurancePrice = ObjUtils.toBigDecimal(reqContract.getTravel_on_busi_common_req_contract().getInsurance_price(), BigDecimal.ZERO);
        //优惠卷金额
        BigDecimal couponAmount = ObjUtils.toBigDecimal(reqContract.getTravel_on_busi_common_req_contract().getCoupon_amount(), BigDecimal.ZERO);
        //订单金额
        BigDecimal orderAmount = ObjUtils.toBigDecimal(reqContract.getTravel_on_busi_common_req_contract().getOrder_price(), BigDecimal.ZERO);

        log.info("totalPrice:{},insurancePrice:{},couponAmount:{},orderAmount:{}", totalPrice, insurancePrice, couponAmount, orderAmount);

        //个人垫付模式
        if (advancePayment) {
            //设置公司支付金额0 设置个人支付金额为总金额 设置可报销金额为总金额 设置不可报销金额0
            checkResult.setCompanyPayPrice(BigDecimal.ZERO);
            checkResult.setPersonalPayPrice(totalPrice);
            checkResult.setReimbursablePrice(totalPrice);
            checkResult.setUnreimbursablePrice(BigDecimal.ZERO);
        }
        //企业支付模式
        else {
            //设置公司支付金额为总金额 设置个人支付金额为0 设置可报销金额为0 设置不可报销金额0
            checkResult.setCompanyPayPrice(totalPrice);
            checkResult.setPersonalPayPrice(BigDecimal.ZERO);
            checkResult.setReimbursablePrice(BigDecimal.ZERO);
            checkResult.setUnreimbursablePrice(BigDecimal.ZERO);
        }
        //设置合规金额为订单总价
        checkResult.setAmountCompliance(totalPrice);
        //设置超规金额
        checkResult.setAmountNonCompliance(BigDecimal.ZERO);
        // 预订人状态
        EmployeeContract orderEmployee = baseOrganizationService.getEmployee(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(), reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        log.info("[预订人状态]:{},员工编号：{},公司ID：{}", JsonUtils.toJson(orderEmployee), reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(), reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        if (orderEmployee == null || orderEmployee.getStatus() != EmployeeStatusEnum.ACTIVE.getValue()) {
            //设置状态码：100001 您的状态未启用,不允许进行消费交易
            checkResult.setResCode(GlobalResponseCode.OrderCheckEmployeeNotActive);
            return checkResult;
        }
        // 企业货运权限
        CompanyRuleDTO companyRule = iCompanyRuleService.queryByCompanyId(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        log.info("[企业货运权限]:{},公司ID：{}", JsonUtils.toJson(companyRule), reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        if (companyRule == null || companyRule.getFreightRule() != 1) {
            //设置状态码：100002 您所在的公司不允许订购货运,具体情况请联系管理员
            checkResult.setResCode(GlobalResponseCode.OrderCheckCompanyNoAuth.getCode(), SaasContentConstant.TRANSPORT_COMPANY_NO_AUTH_MSG);
            return checkResult;
        }
        // 员工货运权限
        List<EmployeeBaseRule> employeeBaseRules = iBaseEmployeeRuleService.queryRuleListByCompanyIdAndEmployeeIds(reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), Arrays.asList(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id()), CategoryTypeEnum.FREIGHT_TRANSPORT.getCode(), null);
        log.info("[员工货运权限]:{}", JsonUtils.toJson(employeeBaseRules));
        if (CollectionUtils.isNotEmpty(employeeBaseRules)) {
            EmployeeBaseRule employeeBaseRule = employeeBaseRules.get(0);
            if (!employeeBaseRule.getRuleSwitch()) {
                //设置状态码：100003 您尚未开通货运权限，请联系管理员为您开通

                checkResult.setResCode(GlobalResponseCode.OrderCheckEmployeeNoAuth.getCode(), SaasContentConstant.TRANSPORT_EMPLOYEE_NO_AUTH_MSG);
                return checkResult;
            }
            // 校验规则（申请单不校验配置的规则）
            if (!reqContract.isApply_check()
                && employeeBaseRule.getRuleFlag()
                && StringUtils.isNotBlank(employeeBaseRule.getRuleId())) {

                CheckRuleQueryDTO checkRuleQueryDTO = new CheckRuleQueryDTO();
                checkRuleQueryDTO.setGroupId(employeeBaseRule.getRuleId());
                checkRuleQueryDTO.setScene(RuleSceneEnum.TRANSPORT.name());
                checkRuleQueryDTO.setCheckAllParamAnyway(true);
                checkRuleQueryDTO.setTransportParamDTO(TransportParamDTO.builder().price(orderAmount).vehicleId(reqContract.getVehicle_id()).build());
                log.info("transport-checkRule-request={}", JsonUtils.toJson(checkRuleQueryDTO));
                ApiResult<RuleCheckResultDTO> ruleCheckResult = iCommonRuleRpcService.checkRule(checkRuleQueryDTO);
                log.info("transport-checkRule-result={}", JsonUtils.toJson(ruleCheckResult));
                // 参数问题
                if (!ruleCheckResult.isSuccess()) {
                    checkResult.setResCode(GlobalResponseCode.ParameterError.getCode(), ruleCheckResult.getMsg());
                    return checkResult;
                }
                // 校验没通过
                if (ruleCheckResult.getData().invalid()) {
                    return handleErrorInfo(checkResult, ruleCheckResult.getData().getRuleInvalids());
                }
            }
            //设置员工货运权限
            checkResult.setEmployeeBaseRule(employeeBaseRule);
            snapshotInfo.put("authInfo", employeeBaseRule);
        }

        //企业余额校验(余额不足或者账户被锁定)  未仔细阅读
        TravelOnBusiOrderRuleCheckResult checkCompanyAccountResult = iOrderCheckService.checkCompanyAccount(reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), orderAmount, OrderCategory.Transport.getKey(), advancePayment);
        log.info("[企业余额校验]:{}", JsonUtils.toJson(checkCompanyAccountResult));
        if (GlobalResponseCode.Success.getCode() != checkCompanyAccountResult.getErrCode()) {
            FinhubLogger.event(reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), EventParams.build(**********, false).put("account_sub_type", 2));
            return checkCompanyAccountResult;
        }
        //费用归属及预算扣减配置查询 未仔细阅读
        CostAttrAndBudgetConf costAttrAndBudgetConf = iMessageSetupService.queryCostAttrAndBudgetConf(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        log.info("[费用归属及预算扣减配置查询]:{}", JsonUtils.toJson(costAttrAndBudgetConf));
        // 老版本配置"部门和项目"兼容为"部门或项目"
        if (costAttrAndBudgetConf.getCost_attribution_scope() == CostAttributionScopeEnum.DEPT_AND_PROJ.getCode()
            && ObjUtils.isNotBlank(clientVersion) && VersionTool.lessThan(clientVersion, "4.2.0")) {
            costAttrAndBudgetConf.setCost_attribution_scope(CostAttributionScopeEnum.DEPT_OR_PROJ.getCode());
        }
        //设置费用归属可选范围 1.部门 2.项目 3.部门或项目 4.部门和项目
        checkResult.setCost_attribution_scope(costAttrAndBudgetConf.getCost_attribution_scope());
        //设置预算扣减类型(仅费用归属为部门和项目有效) 1.部门和项目 2.部门 3.项目
        checkResult.setBudget_cost_attr_type(costAttrAndBudgetConf.getBudget_cost_attr_type());

        // 检查费用归属灰度开关 进行灰度校验
        Integer costAttributionNewSwitch = iMessageSetupRpcService.queryCostAttributionNewSwitch(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        log.info("costAttributionNewSwitch:{}", costAttributionNewSwitch);
        if (CommonSwitchConstant.OPEN.equals(costAttributionNewSwitch)) {
            CostCheckVO costCheckVO = new CostCheckVO();
            costCheckVO.setCostInfoType(reqContract.getCost_info_type());
            costCheckVO.setCostInfoTicketList(reqContract.getCost_info_ticket_list());
            costCheckVO.setCostInfo(reqContract.getCost_info());
            costCheckVO.setCostInfoString(reqContract.getCostInfoString());
            costCheckVO.setUserIdList(reqContract.getUser_id_list());
            costCheckVO.setCategory(reqContract.getCategory());
            costCheckVO.setCompanyId(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
            currencyCheckService.newCheckCostAttribution(costCheckVO);
        } else {
            //检查公司成本归属 未仔细阅读
            TravelOnBusiOrderRuleCheckResult checkCompanyCostAttributionResult = iOrderCheckService.checkCostInfo(reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), reqContract.getCost_info(), costAttrAndBudgetConf, clientVersion);
            log.info("[公司成本归属]:{}", JsonUtils.toJson(checkCompanyCostAttributionResult));
            if (GlobalResponseCode.Success.getCode() != checkCompanyCostAttributionResult.getErrCode()) {
                return checkCompanyCostAttributionResult;
            }
        }

        //预算校验  未仔细阅读
        TravelOnBusiOrderRuleCheckResult travelOrderBudgetCheckResult = iOrderCheckService.categoryOrderBudgetCheckV2(reqContract, costAttrAndBudgetConf, OrderCategory.Transport, clientVersion);
        log.info("[预算校验]:{}", JsonUtils.toJson(travelOrderBudgetCheckResult));
        //快照设置 预算配置及使用
        snapshotInfo.put("budgetList", travelOrderBudgetCheckResult.getBudgetSettingAndUseList());
        if (GlobalResponseCode.Success.getCode() != travelOrderBudgetCheckResult.getErrCode()) {
            //预算校验失败返回预算校验错误码
            return travelOrderBudgetCheckResult;
        }
        //获取预算合规金额
        BigDecimal budgetAmountCompliance = travelOrderBudgetCheckResult.getAmountCompliance();
        //获取预算超规金额
        BigDecimal budgetAmountNonCompliance = travelOrderBudgetCheckResult.getAmountNonCompliance();
        //设置预算合规金额
        checkResult.setAmountCompliance(budgetAmountCompliance);
        //设置预算超规金额
        checkResult.setAmountNonCompliance(budgetAmountNonCompliance);
        return checkResult;
    }

    private TravelOnBusiOrderRuleCheckResult handleErrorInfo(TravelOnBusiOrderRuleCheckResult
                                                                 checkResult, List<RuleInvalidDTO> ruleInvalidDTOs) {
        if (CollectionUtils.isEmpty(ruleInvalidDTOs)) {
            return checkResult;
        }
        RuleInvalidDTO firstInvalidRule = ruleInvalidDTOs.stream().sorted(Comparator.comparing(RuleInvalidDTO::getErrorCode)).findFirst().get();
        checkResult.setResCode(firstInvalidRule.getErrorCode(), firstInvalidRule.getMessage());
        ErrMsgInfo errMsgInfo = new ErrMsgInfo();
        errMsgInfo.setTitle(firstInvalidRule.getMessage());
        List<ResponseCodeContract> responseCodeContracts = ruleInvalidDTOs.stream().map(ruleInvalidDTO -> {
            ResponseCodeContract responseCodeContract = new ResponseCodeContract();
            responseCodeContract.setCode(ruleInvalidDTO.getErrorCode());
            responseCodeContract.setContent(ruleInvalidDTO.getMessage());
            return responseCodeContract;
        }).collect(Collectors.toList());
        errMsgInfo.setErr_code_list(responseCodeContracts);
        checkResult.setErrMsgInfo(errMsgInfo);
        return checkResult;
    }
}
