package com.fenbeitong.saas.core.contract.selfauthorize;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Title:
 * Desc:
 * PACKAGENAME:com.fenbeitong.saas.core.contract.selfauthorize
 *
 * @author: linyongchao
 * Date: 2022/9/22
 */
@Data
@NoArgsConstructor
public class AuthQRCodeDTO {
    /**
     * 授权场景
     */
    private List<Integer> apply_auth_scene_list;

    /**
     * 授权开始时间yyyy/MM/dd
     */
    private String auth_start_time;

    /**
     * 授权有效期：单位为天
     */
    private Integer validity_period;

    /**
     * 是否为永久授权：1为永久，0为非永久
     */
    private Integer is_forever;

    //前端无需传参，用于生成二维码参数
    /**
     * 自主授权类型
     *
     * @see com.fenbeitong.saas.core.model.enums.selfauthorize.SelfAuthorizationTypeEnum
     */
    private Integer selfAuthorizationType;

    /**
     * 员工id
     */
    private String employee_id;

    /**
     * 员工姓名
     */
    private String employee_name;

    /**
     * 公司id
     */
    private String company_id;

    /**
     * 授权权限类型,默认1
     *
     * @see com.fenbeitong.saas.core.model.enums.selfauthorize.AuthPermissionTypeEnum
     */
    private Integer auth_permission_type;

    /**
     * 二维码有效期截止时间
     */
    private String expire_time;
}
