package com.fenbeitong.saas.core.contract.messagesettings.inner;

import com.fenbeitong.saas.api.model.dto.user.EmployeeEmailContract;
import com.fenbeitong.saas.core.contract.user.EmployeeInfoContract;

import java.util.ArrayList;
import java.util.List;

public class BalanceRemind {

    private BalanceRemindNotice balance_remind_notice;
    private BalanceRemindNotice card_balance_remind_notice;
    private BalanceRemindNotice acct_individual_remind_notice;
    private BalanceRemindNotice acct_public_remind_notice;
    private BalanceRemindNotice acct_oversea_remind_notice;
    private List<EmployeeInfoContract> receiver_list = new ArrayList<>();
    private List<String> email_list = new ArrayList<>();

    private Boolean hasUnbind = false;
    /**
     *  新版邮箱绑定人员列表
     **/
    private List<EmployeeEmailContract>  emails = new ArrayList<>();
    public BalanceRemind() {
    }

    public BalanceRemind(BalanceRemindNotice balance_remind_notice, List<EmployeeInfoContract> receiver_list, List<String> email_list) {
        this.balance_remind_notice = balance_remind_notice;
        this.receiver_list = receiver_list;
        this.email_list = email_list;
    }

    public BalanceRemindNotice getBalance_remind_notice() {
        return balance_remind_notice;
    }

    public void setBalance_remind_notice(BalanceRemindNotice balance_remind_notice) {
        this.balance_remind_notice = balance_remind_notice;
    }

    public List<EmployeeInfoContract> getReceiver_list() {
        return receiver_list;
    }

    public void setReceiver_list(List<EmployeeInfoContract> receiver_list) {
        this.receiver_list = receiver_list;
    }

    public List<String> getEmail_list() {
        return email_list;
    }

    public void setEmail_list(List<String> email_list) {
        this.email_list = email_list;
    }

    public BalanceRemindNotice getCard_balance_remind_notice() {
        return card_balance_remind_notice;
    }

    public void setCard_balance_remind_notice(BalanceRemindNotice card_balance_remind_notice) {
        this.card_balance_remind_notice = card_balance_remind_notice;
    }

    public BalanceRemindNotice getAcct_individual_remind_notice() {
        return acct_individual_remind_notice;
    }

    public void setAcct_individual_remind_notice(BalanceRemindNotice acct_individual_remind_notice) {
        this.acct_individual_remind_notice = acct_individual_remind_notice;
    }

    public BalanceRemindNotice getAcct_public_remind_notice() {
        return acct_public_remind_notice;
    }

    public void setAcct_public_remind_notice(BalanceRemindNotice acct_public_remind_notice) {
        this.acct_public_remind_notice = acct_public_remind_notice;
    }

    public BalanceRemindNotice getAcct_oversea_remind_notice() {
        return acct_oversea_remind_notice;
    }

    public void setAcct_oversea_remind_notice(BalanceRemindNotice acct_oversea_remind_notice) {
        this.acct_oversea_remind_notice = acct_oversea_remind_notice;
    }

    public Boolean getHasUnbind() {
        return hasUnbind;
    }

    public void setHasUnbind(Boolean hasUnbind) {
        this.hasUnbind = hasUnbind;
    }

    public List<EmployeeEmailContract> getEmails() {
        return emails;
    }

    public void setEmails(List<EmployeeEmailContract> emails) {
        this.emails = emails;
    }

    public static class BalanceRemindNotice {
        private Integer is_check;
        private Integer amount;

        public BalanceRemindNotice() {
        }

        public BalanceRemindNotice(Integer is_check, Integer amount) {
            this.is_check = is_check;
            this.amount = amount;
        }

        public Integer getIs_check() {
            return is_check;
        }

        public void setIs_check(Integer is_check) {
            this.is_check = is_check;
        }

        public Integer getAmount() {
            return amount;
        }

        public void setAmount(Integer amount) {
            this.amount = amount;
        }
    }

}
