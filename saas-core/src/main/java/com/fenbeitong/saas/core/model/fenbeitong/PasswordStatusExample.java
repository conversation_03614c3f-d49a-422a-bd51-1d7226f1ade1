package com.fenbeitong.saas.core.model.fenbeitong;

import java.util.ArrayList;
import java.util.List;

public class PasswordStatusExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table password_status
     *
     * @mbg.generated Wed Jul 26 11:17:29 CST 2017
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table password_status
     *
     * @mbg.generated Wed Jul 26 11:17:29 CST 2017
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table password_status
     *
     * @mbg.generated Wed Jul 26 11:17:29 CST 2017
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table password_status
     *
     * @mbg.generated Wed Jul 26 11:17:29 CST 2017
     */
    public PasswordStatusExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table password_status
     *
     * @mbg.generated Wed Jul 26 11:17:29 CST 2017
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table password_status
     *
     * @mbg.generated Wed Jul 26 11:17:29 CST 2017
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table password_status
     *
     * @mbg.generated Wed Jul 26 11:17:29 CST 2017
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table password_status
     *
     * @mbg.generated Wed Jul 26 11:17:29 CST 2017
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table password_status
     *
     * @mbg.generated Wed Jul 26 11:17:29 CST 2017
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table password_status
     *
     * @mbg.generated Wed Jul 26 11:17:29 CST 2017
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table password_status
     *
     * @mbg.generated Wed Jul 26 11:17:29 CST 2017
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table password_status
     *
     * @mbg.generated Wed Jul 26 11:17:29 CST 2017
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table password_status
     *
     * @mbg.generated Wed Jul 26 11:17:29 CST 2017
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table password_status
     *
     * @mbg.generated Wed Jul 26 11:17:29 CST 2017
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table password_status
     *
     * @mbg.generated Wed Jul 26 11:17:29 CST 2017
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andLoginInfoIdIsNull() {
            addCriterion("login_info_id is null");
            return (Criteria) this;
        }

        public Criteria andLoginInfoIdIsNotNull() {
            addCriterion("login_info_id is not null");
            return (Criteria) this;
        }

        public Criteria andLoginInfoIdEqualTo(Long value) {
            addCriterion("login_info_id =", value, "loginInfoId");
            return (Criteria) this;
        }

        public Criteria andLoginInfoIdNotEqualTo(Long value) {
            addCriterion("login_info_id <>", value, "loginInfoId");
            return (Criteria) this;
        }

        public Criteria andLoginInfoIdGreaterThan(Long value) {
            addCriterion("login_info_id >", value, "loginInfoId");
            return (Criteria) this;
        }

        public Criteria andLoginInfoIdGreaterThanOrEqualTo(Long value) {
            addCriterion("login_info_id >=", value, "loginInfoId");
            return (Criteria) this;
        }

        public Criteria andLoginInfoIdLessThan(Long value) {
            addCriterion("login_info_id <", value, "loginInfoId");
            return (Criteria) this;
        }

        public Criteria andLoginInfoIdLessThanOrEqualTo(Long value) {
            addCriterion("login_info_id <=", value, "loginInfoId");
            return (Criteria) this;
        }

        public Criteria andLoginInfoIdIn(List<Long> values) {
            addCriterion("login_info_id in", values, "loginInfoId");
            return (Criteria) this;
        }

        public Criteria andLoginInfoIdNotIn(List<Long> values) {
            addCriterion("login_info_id not in", values, "loginInfoId");
            return (Criteria) this;
        }

        public Criteria andLoginInfoIdBetween(Long value1, Long value2) {
            addCriterion("login_info_id between", value1, value2, "loginInfoId");
            return (Criteria) this;
        }

        public Criteria andLoginInfoIdNotBetween(Long value1, Long value2) {
            addCriterion("login_info_id not between", value1, value2, "loginInfoId");
            return (Criteria) this;
        }

        public Criteria andIsDefaultIsNull() {
            addCriterion("is_default is null");
            return (Criteria) this;
        }

        public Criteria andIsDefaultIsNotNull() {
            addCriterion("is_default is not null");
            return (Criteria) this;
        }

        public Criteria andIsDefaultEqualTo(Boolean value) {
            addCriterion("is_default =", value, "isDefault");
            return (Criteria) this;
        }

        public Criteria andIsDefaultNotEqualTo(Boolean value) {
            addCriterion("is_default <>", value, "isDefault");
            return (Criteria) this;
        }

        public Criteria andIsDefaultGreaterThan(Boolean value) {
            addCriterion("is_default >", value, "isDefault");
            return (Criteria) this;
        }

        public Criteria andIsDefaultGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_default >=", value, "isDefault");
            return (Criteria) this;
        }

        public Criteria andIsDefaultLessThan(Boolean value) {
            addCriterion("is_default <", value, "isDefault");
            return (Criteria) this;
        }

        public Criteria andIsDefaultLessThanOrEqualTo(Boolean value) {
            addCriterion("is_default <=", value, "isDefault");
            return (Criteria) this;
        }

        public Criteria andIsDefaultIn(List<Boolean> values) {
            addCriterion("is_default in", values, "isDefault");
            return (Criteria) this;
        }

        public Criteria andIsDefaultNotIn(List<Boolean> values) {
            addCriterion("is_default not in", values, "isDefault");
            return (Criteria) this;
        }

        public Criteria andIsDefaultBetween(Boolean value1, Boolean value2) {
            addCriterion("is_default between", value1, value2, "isDefault");
            return (Criteria) this;
        }

        public Criteria andIsDefaultNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_default not between", value1, value2, "isDefault");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table password_status
     *
     * @mbg.generated do_not_delete_during_merge Wed Jul 26 11:17:29 CST 2017
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table password_status
     *
     * @mbg.generated Wed Jul 26 11:17:29 CST 2017
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}