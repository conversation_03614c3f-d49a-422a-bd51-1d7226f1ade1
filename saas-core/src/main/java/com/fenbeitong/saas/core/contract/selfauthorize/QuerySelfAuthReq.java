package com.fenbeitong.saas.core.contract.selfauthorize;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @ClassName QuerySelfAuthReq
 * @Description 查询条件
 * <AUTHOR>
 * @Date 2022/9/27 15:02
 * @Version 1.0
 */
@Data
public class QuerySelfAuthReq extends PagerReq{
	/**
	 * 当前时间
	 */
	private Date currentDate;

	/**
	 * 需要变更的状态
	 */
	private List<Integer> states;
	
	/**
	 * 是否永久的: 0-非永久 ｜ 1-永久
	 */
	private Integer isForever;
}
