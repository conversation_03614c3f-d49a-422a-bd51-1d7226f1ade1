package com.fenbeitong.saas.core.contract.organization;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.saas.core.contract.organization.inner.BizTripPolicyBean;
import com.fenbeitong.saas.core.contract.organization.inner.CarPolicyBean;
import com.fenbeitong.saas.core.contract.organization.inner.MallPolicyBean;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/5/22.
 */
public class EmployeeOperateContract {

    /**
     * operate_type : 2
     * employee_id : 12312rfsaf
     * employee : {"phone":"13988987443","name":"hello","gender":1,"birth_date":"yyyymmdd","status":1}
     * org_unit_ids : ["abc","def"]
     * cert_list : [{"cert_type":1,"cert_no":"2211239012r28351"}]
     * role : 1
     * employee_log_id : 101
     * org_unit_log_id : 102
     * biz_trip_policy : {"air_priv_flag":false,"air_verify_flag":true,"hotel_priv_flag":true,"hotel_verify_flag":false,"train_priv_flag":true,"train_verify_flag":false,"rule_limit_flag":true,"rule_id":"575263e982f880a6d686ce11","exceed_buy_flag":false}
     * car_policy : {"car_priv_flag":true,"rule_limit_flag":true,"rule_id":2,"exceed_buy_flag":false}
     * mall_policy : {"mall_priv_flag":true}
     */

    private int operate_type;
    private String employee_id;
    private EmployeeBean employee;
    private Integer role;
    private Long employee_log_id;
    private Long org_unit_log_id;
    private BizTripPolicyBean biz_trip_policy;
    private CarPolicyBean car_policy;
    private MallPolicyBean mall_policy;
    private List<String> org_unit_ids;
    private List<CertListBean> cert_list;

    public int getOperate_type() {
        return operate_type;
    }

    public void setOperate_type(int operate_type) {
        this.operate_type = operate_type;
    }

    public String getEmployee_id() {
        return employee_id;
    }

    public void setEmployee_id(String employee_id) {
        this.employee_id = employee_id;
    }

    public EmployeeBean getEmployee() {
        return employee;
    }

    public void setEmployee(EmployeeBean employee) {
        this.employee = employee;
    }

    public Integer getRole() {
        return role;
    }

    public void setRole(Integer role) {
        this.role = role;
    }

    public Long getEmployee_log_id() {
        return employee_log_id;
    }

    public void setEmployee_log_id(Long employee_log_id) {
        this.employee_log_id = employee_log_id;
    }

    public Long getOrg_unit_log_id() {
        return org_unit_log_id;
    }

    public void setOrg_unit_log_id(Long org_unit_log_id) {
        this.org_unit_log_id = org_unit_log_id;
    }

    public BizTripPolicyBean getBiz_trip_policy() {
        return biz_trip_policy;
    }

    public void setBiz_trip_policy(BizTripPolicyBean biz_trip_policy) {
        this.biz_trip_policy = biz_trip_policy;
    }

    public CarPolicyBean getCar_policy() {
        return car_policy;
    }

    public void setCar_policy(CarPolicyBean car_policy) {
        this.car_policy = car_policy;
    }

    public MallPolicyBean getMall_policy() {
        return mall_policy;
    }

    public void setMall_policy(MallPolicyBean mall_policy) {
        this.mall_policy = mall_policy;
    }

    public List<String> getOrg_unit_ids() {
        return org_unit_ids;
    }

    public void setOrg_unit_ids(List<String> org_unit_ids) {
        this.org_unit_ids = org_unit_ids;
    }

    public List<CertListBean> getCert_list() {
        return cert_list;
    }

    public void setCert_list(List<CertListBean> cert_list) {
        this.cert_list = cert_list;
    }

    public EmployeeOperateContract() {
    }

    public EmployeeOperateContract(int operate_type, String employee_id, EmployeeBean employee, Integer role, Long employee_log_id, Long org_unit_log_id, BizTripPolicyBean biz_trip_policy, CarPolicyBean car_policy, MallPolicyBean mall_policy, List<String> org_unit_ids, List<CertListBean> cert_list) {
        this.operate_type = operate_type;
        this.employee_id = employee_id;
        this.employee = employee;
        this.role = role;
        this.employee_log_id = employee_log_id;
        this.org_unit_log_id = org_unit_log_id;
        this.biz_trip_policy = biz_trip_policy;
        this.car_policy = car_policy;
        this.mall_policy = mall_policy;
        this.org_unit_ids = org_unit_ids;
        this.cert_list = cert_list;
    }

    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }

    public static class EmployeeBean {
        /**
         * phone : 13988987443
         * name : hello
         * gender : 1
         * birth_date : yyyymmdd
         * status : 1
         */

        private String phone;
        private String name;
        private Integer gender;
        private String birth_date;
        private Integer status;

        public EmployeeBean() {
        }

        public EmployeeBean(String phone, String name, Integer gender, String birth_date, Integer status) {
            this.phone = phone;
            this.name = name;
            this.gender = gender;
            this.birth_date = birth_date;
            this.status = status;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
        public String getBirth_date() {
            return birth_date;
        }

        public void setBirth_date(String birth_date) {
            this.birth_date = birth_date;
        }

        public Integer getGender() {
            return gender;
        }

        public void setGender(Integer gender) {
            this.gender = gender;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

    }

    public static class CertListBean {
        public CertListBean(Integer cert_type, String cert_no) {
            this.cert_type = cert_type;
            this.cert_no = cert_no;
        }

        public CertListBean() {
        }

        /**
         * cert_type : 1
         * cert_no : 2211239012r28351
         */

        private Integer cert_type;
        private String cert_no;

        public Integer getCert_type() {
            return cert_type;
        }

        public void setCert_type(Integer cert_type) {
            this.cert_type = cert_type;
        }

        public String getCert_no() {
            return cert_no;
        }

        public void setCert_no(String cert_no) {
            this.cert_no = cert_no;
        }
    }
}
