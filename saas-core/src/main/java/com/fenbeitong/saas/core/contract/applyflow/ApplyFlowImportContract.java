package com.fenbeitong.saas.core.contract.applyflow;

import java.util.List;

/**
 * Created by zhaohaichao on 2019/11/23.
 */
public class ApplyFlowImportContract {
    private String taskId;
    private List<ApplyFlowContract> applySettingList;

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public List<ApplyFlowContract> getApplySettingList() {
        return applySettingList;
    }

    public void setApplySettingList(List<ApplyFlowContract> applySettingList) {
        this.applySettingList = applySettingList;
    }
}
