package com.fenbeitong.saas.core.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.finhub.common.constant.BudgetCategoryTypeEnum;
import com.fenbeitong.finhub.common.constant.CategoryTypeEnum;
import com.fenbeitong.finhub.common.constant.CostAttributionScopeEnum;
import com.fenbeitong.finhub.common.constant.PayModelEnum;
import com.fenbeitong.finhub.common.utils.CheckUtils;
import com.fenbeitong.finhub.common.utils.DistanceUtils;
import com.fenbeitong.finhub.common.utils.EventParams;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.harmony.city.contrail.dto.city.FullPathAreaDTO;
import com.fenbeitong.noc.api.service.common.OrderSaasResDTO;
import com.fenbeitong.noc.api.service.meishi.model.MeishiApplyManageReqDTO;
import com.fenbeitong.noc.api.service.meishi.model.SaasCountReqDTO;
import com.fenbeitong.noc.api.service.meishi.model.SaasCountResDTO;
import com.fenbeitong.noc.api.service.meishi.service.IMeishiOrderSearchService;
import com.fenbeitong.saas.api.model.dto.apply.amount.AmountExceedCheckRes;
import com.fenbeitong.saas.api.model.dto.apply.amount.ApplyAmountReqDTO;
import com.fenbeitong.saas.api.model.dto.apply.amount.TotalEstimatedCheckDto;
import com.fenbeitong.saas.api.model.dto.reason.ReasonConfig;
import com.fenbeitong.saas.core.common.DynamicDataSourceDecision;
import com.fenbeitong.saas.core.common.base.SaasException;
import com.fenbeitong.saas.core.common.constant.*;
import com.fenbeitong.saas.core.common.constant.apply.ApplyRequireEnum;
import com.fenbeitong.saas.core.contract.apply.CheckApplyEstimatedAmountReq;
import com.fenbeitong.saas.core.contract.cost.CostCheckVO;
import com.fenbeitong.saas.core.contract.messagesettings.ApplySetupContract;
import com.fenbeitong.saas.core.contract.messagesettings.CostAttrAndBudgetConf;
import com.fenbeitong.saas.core.contract.order.check.*;
import com.fenbeitong.saas.core.contract.rule.LimitPayTipContract;
import com.fenbeitong.saas.core.contract.setup.MessageSetupVO;
import com.fenbeitong.saas.core.dao.fenbeitong.DinnerRuleMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.DinnerTimeRangeMapper;
import com.fenbeitong.saas.core.dao.saasplus.CustomFormApplyConfigMapper;
import com.fenbeitong.saas.core.language.CoreLanguage;
import com.fenbeitong.saas.core.model.enums.CompanySettingType;
import com.fenbeitong.saas.core.model.enums.EmployeeStatusEnum;
import com.fenbeitong.saas.core.model.enums.OrderCategory;
import com.fenbeitong.saas.core.model.enums.apply.ApplyStatus;
import com.fenbeitong.saas.core.model.enums.apply.ApplyType;
import com.fenbeitong.saas.core.model.enums.apply.TotalEstimatedLimitType;
import com.fenbeitong.saas.core.model.enums.booking.BookingConfigEnum;
import com.fenbeitong.saas.core.model.enums.booking.DinnerTakeawayBookingTypeEnum;
import com.fenbeitong.saas.core.model.enums.message.BizType;
import com.fenbeitong.saas.core.model.enums.reason.ReasonType;
import com.fenbeitong.saas.core.model.enums.rule.DinnerVendor;
import com.fenbeitong.saas.core.model.enums.rule.TravelExceedType;
import com.fenbeitong.saas.core.model.fenbeitong.DinnerRule;
import com.fenbeitong.saas.core.model.fenbeitong.DinnerTimeRange;
import com.fenbeitong.saas.core.model.fenbeitong.DinnerTimeRangeExample;
import com.fenbeitong.saas.core.model.saas.ApplyOrder;
import com.fenbeitong.saas.core.model.saas.ApplyTripApplicateExample;
import com.fenbeitong.saas.core.model.saas.ApplyTripInfo;
import com.fenbeitong.saas.core.model.saas.ApplyTripInfoExample;
import com.fenbeitong.saas.core.model.saas.MessageSetup;
import com.fenbeitong.saas.core.service.*;
import com.fenbeitong.saas.core.service.apply.TripEstimateService;
import com.fenbeitong.saas.core.service.apply.amount.ApplyAmountService;
import com.fenbeitong.saas.core.service.apply.amount.ApplyTripAmountService;
import com.fenbeitong.saas.core.service.order.MeishiOrderService;
import com.fenbeitong.saas.core.service.setup.SetupService;
import com.fenbeitong.saas.core.utils.tools.ConvertUtils;
import com.fenbeitong.saas.core.utils.tools.DateTimeTool;
import com.fenbeitong.saas.core.utils.tools.HttpTool;
import com.fenbeitong.saas.core.utils.tools.VersionTool;
import com.fenbeitong.saasplus.api.model.dto.custform.CustomFormApplyControlItemDTO;
import com.fenbeitong.saasplus.api.model.dto.custform.CustomFormTotalEstimatedOptionDTO;
import com.fenbeitong.saasplus.api.model.dto.finance.CustomDimension;
import com.fenbeitong.saasplus.api.model.enums.custform.SwitchTypeEnum;
import com.fenbeitong.saasplus.api.service.custform.ICustomFormService;
import com.fenbeitong.usercenter.api.model.dto.calendar.CalendarRpcVO;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyAdminInfoVo;
import com.fenbeitong.usercenter.api.model.dto.company.MeishiAuthDto;
import com.fenbeitong.usercenter.api.model.dto.company.SceneAuthDto;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.fenbeitong.usercenter.api.model.dto.rule.EmployeeMeishiRuleDto;
import com.fenbeitong.usercenter.api.model.enums.privilege.CodeTypeEnums;
import com.fenbeitong.usercenter.api.model.enums.privilege.CustomCodeType;
import com.fenbeitong.usercenter.api.model.po.rule.EmployeeBaseRule;
import com.fenbeitong.usercenter.api.service.calendar.RpcCompanyCalendarService;
import com.fenbeitong.usercenter.api.service.company.ICompanyService;
import com.fenbeitong.usercenter.api.service.privilege.IPrivilegeService;
import com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeMeishiRuleExtService;
import com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeRuleService;
import com.google.common.collect.Lists;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.DateUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.StrUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * Created by zhaohaichao on 2019/4/9.
 */
@Slf4j
@Service
public class DeliciousCheckV3ServiceImpl implements IDeliciousCheckV3Service {

    private static final Logger logger = LoggerFactory.getLogger(DeliciousCheckV3ServiceImpl.class);

    @Autowired
    private IOrderCheckService iOrderCheckService;
    @Autowired
    private DinnerRuleMapper dinnerRuleMapper;
    @Autowired
    private IBaseOrganizationService baseOrganizationService;
    @Autowired
    private DinnerTimeRangeMapper dinnerTimeRangeMapper;
    @Autowired
    private IBaseEmployeeMeishiRuleExtService iBaseEmployeeMeishiRuleExtService;
    @Autowired
    private IMeishiOrderSearchService iMeishiOrderSearchService;
    @Autowired
    private ICompanyService iCompanyService;
    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyOrderMapper applyOrderMapper;
    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyTripInfoMapper applyTripInfoMapper;
    @Autowired
    private IMessageSetupService iMessageSetupService;
    @Autowired
    private ICustomReasonService customReasonService;
    @Autowired
    private IBaseEmployeeRuleService iBaseEmployeeRuleService;
    @Autowired
    private IPrivilegeService iPrivilegeService;
    @Autowired
    private IMessageSetupService messageSetupService;
    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyTripApplicateMapper applyTripApplicateMapper;
    @Autowired
    private RpcCompanyCalendarService rpcCompanyCalendarService;
    @Autowired
    private ICustomFieldsService iCustomFieldsService;
    @Autowired
    private com.fenbeitong.saasplus.api.service.messageSetup.IMessageSetupService iMessageSetupRpcService;
    @Autowired
    private CurrencyCheckServiceImpl currencyCheckService;
    @Autowired
    private ApplyAmountService applyAmountService;
    @Autowired
    private SetupService setupService;


    @Autowired
    private CustomFormApplyConfigMapper customFormApplyConfigMapper;
    @Autowired
    private ICustomFormService iCustomFormService;

    @Autowired
    private IDeliciousCheckV2Service iDeliciousCheckV2Service;

    @Autowired
    private ApplyTripAmountService applyTripAmountService;

    @Autowired
    private TripEstimateService tripEstimateService;

    @Autowired
    private MeishiOrderService meishiOrderService;

    @Override
    public DinnerOrderCheckResContract deliciousOrderCheck(DinnerOrderCheckReqV2Contract reqContract, String clientVersion, String clientType) {

        String costCompanyId = StringUtils.isNotBlank(reqContract.getCost_company_id()) ? reqContract.getCost_company_id() : reqContract.getCompany_id();

        DynamicDataSourceDecision.determineDataSource(reqContract.getCompany_id());
        // 返回结果
        DinnerOrderCheckResContract resContract = new DinnerOrderCheckResContract();

        // 强制版本升级
        boolean needUpdate = iCustomFieldsService.checkBudget(reqContract.getCompany_id(), ReasonType.ORDER_MEISHI, clientVersion);
        if (needUpdate) {
            resContract.setErr_code(GlobalResponseCode.ApplyCenterAlert.getCode());
            resContract.setErr_msg(GlobalResponseCode.ApplyCenterAlert.getMsg());
            resContract.setErr_type(1);
            return resContract;
        }
        // 检查费用归属灰度开关及客户端版本号
        Integer costAttributionNewSwitch = iMessageSetupRpcService.queryCostAttributionNewSwitch(reqContract.getCompany_id());
        log.info("[ deliciousOrderCheck ] costAttributionNewSwitch={}", costAttributionNewSwitch);
        if (CommonSwitchConstant.OPEN.equals(costAttributionNewSwitch)
                && VersionTool.lessThan(clientVersion, ClientVersionConstant.COST_ATTRIBUTION_UPDATE_CLIENT_VERSION)) {
            resContract.setErr_code(GlobalResponseCode.OrderCheckUpdateTost.getCode());
            resContract.setErr_msg(GlobalResponseCode.OrderCheckUpdateTost.getMsg());
            resContract.setErr_type(1);
            return resContract;
        }
        // 校验规则
        DinnerOrderRuleCheckResult ruleCheckResult = deliciousOrderRuleCheck(reqContract, clientVersion, clientType);
        logger.info("ruleCheckResult:{}",JsonUtils.toJson(ruleCheckResult));
        resContract.setErr_code(ruleCheckResult.getErrCode());
        resContract.setErr_msg(ruleCheckResult.getErrMsg());
        resContract.setIs_exceed(ruleCheckResult.getExceed());
        resContract.setExceed_msg(ruleCheckResult.getExceedMsg());
        resContract.setSingle_limit_amount(ruleCheckResult.getSingleLimitAmount());
        resContract.setDay_limit_amount(ruleCheckResult.getDayLimitAmount());
        resContract.setCompany_pay_amount(ruleCheckResult.getCompanyPayAmount());
        resContract.setEmployee_pay_amount(ruleCheckResult.getEmployeePayAmount());
        resContract.setCompany_pay_price(ruleCheckResult.getCompanyPayAmount());
        resContract.setPersonal_pay_price(ruleCheckResult.getEmployeePayAmount());
        resContract.setPay_model(ruleCheckResult.getPayModel());
        resContract.setReimbursable_price(ruleCheckResult.getReimbursablePrice());
        resContract.setUnreimbursable_price(ruleCheckResult.getUnreimbursablePrice());
        resContract.setAmount_compliance(ruleCheckResult.getAmountCompliance());
        resContract.setAmount_non_compliance(ruleCheckResult.getAmountNonCompliance());
        resContract.setErr_msg_info(ruleCheckResult.getErrMsgInfo());
        resContract.setRule_info(ruleCheckResult.getRule_info());
        resContract.setCost_attribution_scope(ruleCheckResult.getCost_attribution_scope());
        resContract.setBudget_cost_attr_type(ruleCheckResult.getBudget_cost_attr_type());
        resContract.setCoupon_used_amount(ruleCheckResult.getCoupon_used_amount());
        resContract.setDinnerRule(ruleCheckResult.getDinnerRule());
        if (resContract.getIs_exceed() || (reqContract.getExceed_submit() != null && reqContract.getExceed_submit())) {
            resContract.setIs_exceed(true);
        }
        if (ruleCheckResult.getErrCode() == GlobalResponseCode.Success.getCode()
                && !iOrderCheckService.isNewBudgetCompany(costCompanyId)) {
            CostAttrAndBudgetConf costAttrAndBudgetConf = iMessageSetupService.queryCostAttrAndBudgetConf(costCompanyId);
            logger.info("costAttrAndBudgetConf:{}",JsonUtils.toJson(costAttrAndBudgetConf));
            // 老版本配置"部门和项目"兼容为"部门或项目"
            if (costAttrAndBudgetConf.getCost_attribution_scope() == CostAttributionScopeEnum.DEPT_AND_PROJ.getCode()
                    && ObjUtils.isNotBlank(clientVersion) && VersionTool.lessThan(clientVersion, "4.2.0")) {
                costAttrAndBudgetConf.setCost_attribution_scope(CostAttributionScopeEnum.DEPT_OR_PROJ.getCode());
            }
            // 校验预算
            DinnerOrderBudgetCheckResult budgetCheckResult = deliciousOrderBudgetCheck(reqContract, ruleCheckResult, costAttrAndBudgetConf, clientVersion);
            resContract.setErr_code(budgetCheckResult.getErrCode());
            resContract.setErr_msg(budgetCheckResult.getErrMsg());
            resContract.setIs_over_budget(budgetCheckResult.getOverBudget());
            resContract.setOver_budget_msg(budgetCheckResult.getOverBudgetMsg());
            resContract.setAmount_compliance(resContract.getAmount_compliance().min(budgetCheckResult.getBudgetAmountCompliance()));
            resContract.setAmount_non_compliance(reqContract.getTotal_price().subtract(resContract.getAmount_compliance()));
            try {
                JSONObject snapshotInfo = ruleCheckResult.getSnapshotInfo();
                if (ObjUtils.isBlank(snapshotInfo)) {
                    snapshotInfo = new JSONObject();
                }
                snapshotInfo.put("budgetList", budgetCheckResult.getBudgetSettingAndUseList());
                ruleCheckResult.setSnapshotInfo(snapshotInfo);
            } catch (Exception e) {
                logger.error("处理下单校验日志快照信息异常", e);
            }
        }
        if (resContract.getErr_code() == GlobalResponseCode.Success.getCode()) {
            logger.info("saveCost");
            // 自定义字段转化
            List<CustomDimension> customDimensionList =
                ConvertUtils.convertToCustomDimension(reqContract.getCustomer_field_setting_list());


            Date date = new Date();
            SimpleDateFormat dateFormat= new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");

            TempOrderCheckResContract costResult = iOrderCheckService.saveCost(reqContract.getOrder_id(),
                    costCompanyId,
                    reqContract.getEmployee_id(),
                    reqContract.getApply_id(),
                    reqContract.getTrip_id(),
                    BudgetCategoryTypeEnum.MeiShi,
                    reqContract.getTotal_price(),
                    reqContract.getCost_info(),
                    reqContract.getCostInfoString(),
                    clientVersion, customDimensionList,dateFormat.format(date));
            logger.info("costResult:{}",JsonUtils.toJson(costResult));
            if (costResult.getErr_code() != GlobalResponseCode.Success.getCode()) {
                resContract.setErr_code(costResult.getErr_code());
                resContract.setErr_msg(costResult.getErr_msg());
            } else {
                resContract.setCost_id(costResult.getCost_id());
            }
        }
        // 错误信息类型
        if (resContract.getErr_code() == GlobalResponseCode.Success.getCode()) {
            // 成功
            resContract.setErr_type(0);
        } else if (resContract.getErr_code() == GlobalResponseCode.OrderCheckExceedNeedReason.getCode()
                || resContract.getErr_code() == GlobalResponseCode.OrderCheckOverBudgetCanSubmit.getCode()
                || resContract.getErr_code() == GlobalResponseCode.OrderCheckOverBudgetCanNotSubmit.getCode()
                || resContract.getErr_code() == GlobalResponseCode.OrderCheckNotAllowedOrder.getCode()
                || resContract.getErr_code() == GlobalResponseCode.OrderCheckApplyExceedMore.getCode()
                || resContract.getErr_code() == GlobalResponseCode.OrderCheckApplyExceedLess.getCode()
                || resContract.getErr_code() == TemporaryResponseCode.OrderCheckPersonalPayTip.getCode()
                || resContract.getErr_code() == GlobalResponseCode.ApplyPersonalPay.getCode()
                ) {
            // 提示(弹窗)
            resContract.setErr_type(2);
            ErrMsgInfo eminfo = ruleCheckResult.getErrMsgInfo();
            if (resContract.getErr_code() == GlobalResponseCode.OrderCheckExceedNeedReason.getCode()) {
                if (eminfo != null) {
                    ErrMsgInfo errMsgInfo = new ErrMsgInfo();
                    errMsgInfo.setTitle(SaasOrderConstant.ORDER_OVERRULE_REASON);
                    errMsgInfo.setErr_code_list(eminfo.getErr_code_list());
                    resContract.setErr_msg_info(errMsgInfo);
                }
            } else if (resContract.getErr_code() == GlobalResponseCode.OrderCheckNotAllowedOrder.getCode()) {
                if (eminfo != null) {
                    ErrMsgInfo errMsgInfo = new ErrMsgInfo();
                    errMsgInfo.setTitle(SaasOrderConstant.ORDER_PROHIBIT);
                    errMsgInfo.setErr_code_list(ruleCheckResult.getErrMsgInfo().getErr_code_list());
                    resContract.setErr_msg_info(errMsgInfo);
                }
            } else if (resContract.getErr_code() == GlobalResponseCode.OrderCheckApplyExceedMore.getCode()) {
                resContract.setErr_msg_info(eminfo);
            } else {
                ErrMsgInfo errMsgInfo = new ErrMsgInfo();
                errMsgInfo.setTitle(resContract.getErr_msg());
                errMsgInfo.setErr_code_list(new ArrayList<>());
                resContract.setErr_msg_info(errMsgInfo);
            }
        } else {
            // 异常(toast)
            resContract.setErr_type(1);
            ErrMsgInfo errMsgInfo = new ErrMsgInfo();
            errMsgInfo.setTitle(resContract.getErr_msg());
            errMsgInfo.setErr_code_list(new ArrayList<>());
            resContract.setErr_msg_info(errMsgInfo);
        }
        // 记录下单校验日志
        iOrderCheckService.saveOrderCheckLog(
                reqContract.getCompany_id(),
                reqContract.getEmployee_id(),
                CategoryTypeEnum.MeiShi.getCode(),
                clientVersion,
                JsonUtils.toJson(reqContract),
                JsonUtils.toJson(resContract),
                ObjUtils.toString(resContract.getErr_code()),
                ruleCheckResult.getSnapshotInfo().toJSONString(),
                ruleCheckResult.getExtInfo().toJSONString());
        return resContract;
    }


    /**
     * 校验美食规则
     *
     * @param reqContract
     * @return
     */
    private DinnerOrderRuleCheckResult deliciousOrderRuleCheck(DinnerOrderCheckReqV2Contract reqContract, String clientVersion, String clientType) {
        //查询优惠券配置 0.未开启 1.开启
        BigDecimal totalPrice = ObjUtils.ifNull(reqContract.getTotal_price(), BigDecimal.ZERO);
        BigDecimal couponAmount = ObjUtils.ifNull(reqContract.getCoupon_amount(), BigDecimal.ZERO);

        /** 费用主体 */
        String costCompanyId = StringUtils.isNotBlank(reqContract.getCost_company_id())? reqContract.getCost_company_id() : reqContract.getCompany_id();
        /** 消费主体 */
        String consumerCompanyId = StringUtils.isNotBlank(reqContract.getConsumer_company_id())? reqContract.getConsumer_company_id() : reqContract.getCompany_id();
        log.info("美食预定人(n)模式, 消费主体: {}, 费用主体: {}", consumerCompanyId, costCompanyId);

        Integer couponExceedPriceSetting = iOrderCheckService.queryCouponExceedPriceSetting(costCompanyId);
        logger.info("优惠券抵扣配置：couponExceedPriceSetting:{}",JsonUtils.toJson(couponExceedPriceSetting));
        if (couponExceedPriceSetting == 1) {
            totalPrice = totalPrice.subtract(couponAmount).max(BigDecimal.ZERO);
        }

        Integer subSceneId = reqContract.getSub_scene_id();
        Integer vendor_code = reqContract.getVendor_code();

        // 初始化默认结果
        DinnerOrderRuleCheckResult checkResult = new DinnerOrderRuleCheckResult();

        //企业码用餐特殊验证：
        if(DinnerVendor.ALI_ENT_MS.getCode().equals(vendor_code)){
            //支付宝企业码验证
            //不能使用优惠券
            if(couponAmount.compareTo(BigDecimal.ZERO) > 0){
                checkResult.setResCode(GlobalResponseCode.OrderCheckCouponForbid);
                return checkResult;
            }
        }

        // 校验订单事由及自定义字段
        TempOrderRuleCheckResult result = iOrderCheckService.checkOrderReasonAndCustomFields(costCompanyId, CategoryTypeEnum.Dinner, reqContract.getOrder_reason(), reqContract.getOrder_reason_comment(), reqContract.getOrder_custom_fields(), clientVersion, clientType);
        logger.info("result:{}",JsonUtils.toJson(result));
        if (result.getErrCode() != TemporaryResponseCode.Success.getCode()) {
            checkResult.setResCode(result.getErrCode(), result.getErrMsg());
            return checkResult;
        }
        // 快照信息
        JSONObject snapshotInfo = new JSONObject();
        checkResult.setSnapshotInfo(snapshotInfo);
        // 查询是否个人垫付模式
        Map<String, Boolean> funcMap = iPrivilegeService.queryFunctionMoudle(CodeTypeEnums.CustomAuth.getKey(), consumerCompanyId, reqContract.getEmployee_id(), CustomCodeType.M_ADVANCE_PAYMENT.getKey());
        logger.info("funcMap:{}",JsonUtils.toJson(funcMap));
        Boolean advancePayment = funcMap.get(CustomCodeType.M_ADVANCE_PAYMENT.getKey());
        logger.info("[美食下单校验]，是否个人垫付模式:{}", advancePayment);
        PayModelEnum payModel = advancePayment ? PayModelEnum.PERSONAL_PREPAY : PayModelEnum.COMPANY_PAY;
        checkResult.setPayModel(payModel.getCode());
        snapshotInfo.put("payModel", payModel.getCode());
        if (advancePayment) {
            checkResult.setCompanyPayAmount(BigDecimal.ZERO);
            checkResult.setEmployeePayAmount(totalPrice);
            checkResult.setReimbursablePrice(totalPrice);
            checkResult.setUnreimbursablePrice(BigDecimal.ZERO);
        } else {
            checkResult.setCompanyPayAmount(totalPrice);
            checkResult.setEmployeePayAmount(BigDecimal.ZERO);
            checkResult.setReimbursablePrice(BigDecimal.ZERO);
            checkResult.setUnreimbursablePrice(BigDecimal.ZERO);
        }
        checkResult.setAmountCompliance(totalPrice);
        checkResult.setAmountNonCompliance(BigDecimal.ZERO);
        // 预订人状态
        EmployeeContract orderEmployee = baseOrganizationService.getEmployee(reqContract.getEmployee_id(), consumerCompanyId);
        logger.info("orderEmployee:{}",JsonUtils.toJson(orderEmployee));
        if (orderEmployee == null || orderEmployee.getStatus() != EmployeeStatusEnum.ACTIVE.getValue()) {
            checkResult.setResCode(GlobalResponseCode.OrderCheckEmployeeNotActive);
            return checkResult;
        }
        //公司状态
        CompanyAdminInfoVo companyAdminInfoVo = iCompanyService.queryCompanyInfo(consumerCompanyId);
        logger.info("companyAdminInfoVo:{}",JsonUtils.toJson(companyAdminInfoVo));
        if (ObjUtils.isEmpty(companyAdminInfoVo)||companyAdminInfoVo.getStatus()!=1){
            checkResult.setResCode(GlobalResponseCode.OrderCheckCompanyNoAuth);
            return checkResult;
        }
        //公司用餐权限
        SceneAuthDto sceneAuthDto = iCompanyService.querySecenAuth(consumerCompanyId, reqContract.getEmployee_id());
        logger.info("sceneAuthDto:{}",JsonUtils.toJson(sceneAuthDto));
        if (ObjUtils.isEmpty(sceneAuthDto)||ObjUtils.isEmpty(sceneAuthDto.getMeishiAuth())) {
            checkResult.setResCode(GlobalResponseCode.NotFound);
            return checkResult;
        }
        MeishiAuthDto meishiAuthDto= sceneAuthDto.getMeishiAuth();
        if (meishiAuthDto.getPublicMeishi()!=1){
            checkResult.setResCode(GlobalResponseCode.OrderCheckCompanyNoAuth);
            return checkResult;
        }
        //企业余额校验(余额不足或者账户被锁定)
        TravelOnBusiOrderRuleCheckResult checkCompanyAccountResult = iOrderCheckService.checkCompanyAccount(costCompanyId, reqContract.getTotal_price(), OrderCategory.MeiShi.getKey(), advancePayment);
        logger.info("checkCompanyAccountResult:{}",JsonUtils.toJson(checkCompanyAccountResult));
        if (GlobalResponseCode.Success.getCode() != checkCompanyAccountResult.getErrCode()) {
            FinhubLogger.event(consumerCompanyId, EventParams.build(**********, false).put("account_sub_type", 2));
            checkResult.setResCode(GlobalResponseCode.OrderCheckCompanyAccountNoAuth);
            return checkResult;
        }
        CostAttrAndBudgetConf costAttrAndBudgetConf = iMessageSetupService.queryCostAttrAndBudgetConf(costCompanyId);
        logger.info("costAttrAndBudgetConf:{}",JsonUtils.toJson(costAttrAndBudgetConf));
        // 老版本配置"部门和项目"兼容为"部门或项目"
        if (costAttrAndBudgetConf.getCost_attribution_scope() == CostAttributionScopeEnum.DEPT_AND_PROJ.getCode()
                && ObjUtils.isNotBlank(clientVersion) && VersionTool.lessThan(clientVersion, "4.2.0")) {
            costAttrAndBudgetConf.setCost_attribution_scope(CostAttributionScopeEnum.DEPT_OR_PROJ.getCode());
        }
        checkResult.setCost_attribution_scope(costAttrAndBudgetConf.getCost_attribution_scope());
        checkResult.setBudget_cost_attr_type(costAttrAndBudgetConf.getBudget_cost_attr_type());

        // 检查费用归属灰度开关 进行灰度校验
        Integer costAttributionNewSwitch = iMessageSetupRpcService.queryCostAttributionNewSwitch(costCompanyId);
        log.info("costAttributionNewSwitch:{}",costAttributionNewSwitch);
        if (CommonSwitchConstant.OPEN.equals(costAttributionNewSwitch)){
            CostCheckVO costCheckVO = new CostCheckVO();
            costCheckVO.setCostInfo(reqContract.getCost_info());
            costCheckVO.setCostInfoString(reqContract.getCostInfoString());
            costCheckVO.setUserIdList(reqContract.getUser_id_list());
            costCheckVO.setCategory(reqContract.getCategory());
            costCheckVO.setCompanyId(costCompanyId);
            costCheckVO.setApplyId(reqContract.getApply_id());
            costCheckVO.setSceneCode(reqContract.getSub_scene_id());
            currencyCheckService.newCheckCostAttribution(costCheckVO);
        }else{
            TravelOnBusiOrderRuleCheckResult checkCompanyCostAttributionResult = iOrderCheckService.checkCostInfo(costCompanyId, reqContract.getCost_info(), costAttrAndBudgetConf, clientVersion);
            if (GlobalResponseCode.Success.getCode() != checkCompanyCostAttributionResult.getErrCode()) {
                checkResult.setResCode(checkCompanyCostAttributionResult.getErrCode(), checkCompanyCostAttributionResult.getErrMsg());
                return checkResult;
            }
        }

        //员工用餐规则
        EmployeeBaseRule employeeMeishiRuleDto = queryEmployeeMeishiRuleList(reqContract.getEmployee_id(), consumerCompanyId, subSceneId);
        logger.info("employeeMeishiRuleDto:{}",JsonUtils.toJson(employeeMeishiRuleDto));
        snapshotInfo.put("authInfo", employeeMeishiRuleDto);
        if (employeeMeishiRuleDto == null || !employeeMeishiRuleDto.getRuleSwitch()) {
            checkResult.setResCode(GlobalResponseCode.OrderCheckEmployeeNoAuth);
            return checkResult;
        }
        // 员工用餐规则
        if (!employeeMeishiRuleDto.getRuleFlag()) {
            logger.info("该员工没有配置用餐规则");
            // 总预估费
            log.info("11applyId={}", reqContract.getApply_id());
            TotalEstimatedCheckDto totalEstimatedCheckDto = applyAmountService.isEstimatedAmountExcessive(reqContract.getApply_id(), totalPrice, new Date(),BizType.MeiShi.getCode());
            log.info("************************** = {}", JsonUtils.toJson(totalEstimatedCheckDto));
            // 如果有总预估费
            // 自定义申请单存在 & 总预估费开关打开
            if (totalEstimatedCheckDto.getHasApplyOrder()
                    && totalEstimatedCheckDto.getIsCustformApplyOrder()
                    && Objects.equals(CommonSwitchConstant.OPEN, totalEstimatedCheckDto.getCheckTotalEstimatedFlag())) {
                log.info("advancePayment={},ReqContract.getApply_personalpay_sumbit()={}", advancePayment, reqContract.getApply_personalpay_sumbit());
                if (totalEstimatedCheckDto.getIsExceed() // 预估费超规，
                        && Objects.equals(advancePayment, Boolean.FALSE) // 没有开启个人支付
                        && Objects.equals(reqContract.getApply_personalpay_sumbit(), Boolean.FALSE)) { // 超出部分不是个人支付
                    if (TotalEstimatedLimitType.DAILY_LIMIT.equalsTo(totalEstimatedCheckDto.getTotalEstimatedCostType())) {
                        checkResult.setResCode(GlobalResponseCode.OrderCheckMeiShiTotalEstimateCostExceedDailyLimit);
                    } else if (TotalEstimatedLimitType.DAILY_AVERAGE.equalsTo(totalEstimatedCheckDto.getTotalEstimatedCostType())) {
                        checkResult.setResCode(GlobalResponseCode.OrderCheckMeiShiTotalEstimateCostExceedDailyAverage);
                    } else {
                        checkResult.setResCode(GlobalResponseCode.OrderCheckMeiShiTotalEstimateCostExceedEmployee);
                    }
                }
            }
            
            BigDecimal couponValue = couponAmount;
            if (couponExceedPriceSetting == 0) {
                BigDecimal companyActPayPrice = checkResult.getCompanyPayAmount();
                logger.info("companyActPayPrice:{}",JsonUtils.toJson(companyActPayPrice));
                if (advancePayment) {
                    companyActPayPrice = BigDecimal.ZERO;
                    checkResult.setEmployeePayAmount(reqContract.getTotal_price().subtract(couponValue).setScale(2, RoundingMode.HALF_UP));
                    checkResult.setReimbursablePrice(reqContract.getTotal_price().subtract(couponValue).setScale(2, RoundingMode.HALF_UP));
                    checkResult.setUnreimbursablePrice(BigDecimal.ZERO);
                } else {
                    //公司支付金额
                    if (companyActPayPrice.compareTo(couponValue) >= 0) {
                        companyActPayPrice = companyActPayPrice.subtract(couponValue).setScale(2, RoundingMode.HALF_UP);
                    } else {
                        couponValue = companyActPayPrice;
                        companyActPayPrice = BigDecimal.ZERO;
                    }
                }
                checkResult.setCompanyPayAmount(companyActPayPrice);

            }
            checkResult.setCoupon_used_amount(couponValue);
            logger.info("checkResult:{}",JsonUtils.toJson(checkResult));
            return checkResult;
        }

        if(employeeMeishiRuleDto.getRuleFlag() && ObjUtils.isEmpty(employeeMeishiRuleDto.getRuleIds())){
            checkResult.setResCode(GlobalResponseCode.OrderCheckRuleNotExist);
            return checkResult;
        }

        //指定子场景时，用户每个子场景，仅能有一个规则id：
        String ruleId = employeeMeishiRuleDto.getRuleIds().get(0);
//        String ruleId = ObjUtils.ifNull(employeeMeishiRuleDto.getManualMeishiRuleId(), employeeMeishiRuleDto.getDefaultMeishiRuleId());
        DinnerRule dinnerRule = dinnerRuleMapper.selectByPrimaryKey(ruleId);
        logger.info("dinnerRule:{}",JsonUtils.toJson(dinnerRule));
        if (dinnerRule == null) {
            checkResult.setResCode(GlobalResponseCode.OrderCheckRuleNotExist);
            return checkResult;
        }
        checkResult.setDinnerRule(dinnerRule);
        snapshotInfo.put("ruleInfo", dinnerRule);

        if(ApplyRequireEnum.APPLY_ORDER_REQUIRE.getType().equals(dinnerRule.getApplyIdRequire()) && ObjUtils.isBlank(reqContract.getApply_id())){
            checkResult.setResCode(GlobalResponseCode.OrderTakeawayCheckOnlyApplyOrder);
            return checkResult;
        }

        //审批单
        String applyId = reqContract.getApply_id();
        Boolean checkRule = dinnerRule.getCheckRule();
        boolean hasApply = false;
        // 校验订单用餐人数和申请单用餐人数是否一致
        boolean dinerCountExceed = false;
        ResponseCodeContract dinerCountRcc = new ResponseCodeContract();

        Integer applyTripPersonCount = 1;
        if (ObjUtils.isNotBlank(applyId)) {
            hasApply = true;

            if (Objects.equals(dinnerRule.getCheckDinerNumber(), 1) && ObjUtils.isNotEmpty(reqContract.getTrip_id())) {
                ApplyTripInfo applyTripInfo = applyTripInfoMapper.selectByPrimaryKey(reqContract.getTrip_id());
                //校验申请单用餐人数比较类型 0：需等于 1：需小于 2：必须不大于 3：需大于 4：必须不小于
                Integer compareType = ObjUtils.toInteger(dinnerRule.getCompareType(),0) ;

                if(null == reqContract.getDiner_count()){
                    checkResult.setResCode(GlobalResponseCode.OrderBookingCheckMeiShiDinerCount);
                    return checkResult;
                }

                if (null != applyTripInfo && null != applyTripInfo.getPersonCount() ) {
                    applyTripPersonCount = applyTripInfo.getPersonCount();
                    String exceedMsg ;
                    if(compareType == 0 && !Objects.equals(reqContract.getDiner_count(), applyTripInfo.getPersonCount())){
                        dinerCountExceed  = true;
                        exceedMsg = StrUtils.formatString(TemporaryResponseCode.OrderTakeawayCheckDinerNumberNotEqual.getMsg(), reqContract.getDiner_count(), applyTripInfo.getPersonCount());       dinerCountRcc.setContent(exceedMsg);
                        dinerCountRcc.setCode(GlobalResponseCode.OrderTakeawayCheckDinerNumberNotEqual.getCode());
                    }else if(compareType == 1 && reqContract.getDiner_count() >= applyTripInfo.getPersonCount() ){
                        exceedMsg = StrUtils.formatString(TemporaryResponseCode.OrderTakeawayCheckDinerNumberNotEqual1.getMsg(), reqContract.getDiner_count(), applyTripInfo.getPersonCount());        dinerCountRcc.setContent(exceedMsg);
                        dinerCountRcc.setCode(GlobalResponseCode.OrderTakeawayCheckDinerNumberNotEqual.getCode());
                        dinerCountExceed  = true;
                    }else if( compareType == 2 && reqContract.getDiner_count() > applyTripInfo.getPersonCount()){
                        exceedMsg = StrUtils.formatString(TemporaryResponseCode.OrderTakeawayCheckDinerNumberNotEqual2.getMsg(), reqContract.getDiner_count(), applyTripInfo.getPersonCount());        dinerCountRcc.setContent(exceedMsg);
                        dinerCountRcc.setCode(GlobalResponseCode.OrderTakeawayCheckDinerNumberNotEqual.getCode());
                        dinerCountExceed  = true;
                    }else if( compareType == 3 && reqContract.getDiner_count() <= applyTripInfo.getPersonCount() ){
                        exceedMsg = StrUtils.formatString(TemporaryResponseCode.OrderTakeawayCheckDinerNumberNotEqual3.getMsg(), reqContract.getDiner_count(), applyTripInfo.getPersonCount());                       dinerCountRcc.setContent(exceedMsg);
                        dinerCountRcc.setCode(GlobalResponseCode.OrderTakeawayCheckDinerNumberNotEqual.getCode());
                        dinerCountExceed  = true;
                    }else if( compareType == 4 &&  reqContract.getDiner_count() < applyTripInfo.getPersonCount()  ){
                        exceedMsg = StrUtils.formatString(TemporaryResponseCode.OrderTakeawayCheckDinerNumberNotEqual4.getMsg(), reqContract.getDiner_count(), applyTripInfo.getPersonCount());
                        dinerCountRcc.setContent(exceedMsg);
                        dinerCountRcc.setCode(GlobalResponseCode.OrderTakeawayCheckDinerNumberNotEqual.getCode());
                        dinerCountExceed  = true;
                    }
                }
            }

            applyOrderCheck(checkResult, reqContract, consumerCompanyId, totalPrice, advancePayment, employeeMeishiRuleDto, dinnerRule, couponExceedPriceSetting);

            log.info("apply check result:{}", JsonUtils.toJson(checkResult));

            // 使用申请单 且未开启同时校验规则  按照原逻辑直接返回
            if (!dinerCountExceed && !checkRule) {
                return checkResult;
            }
            // 二次提交 直接返回
            if (ObjUtils.toBoolean(reqContract.getApply_personalpay_sumbit(), false)) {
                return checkResult;
            }

            // 审批校验未通过 或 仅预估费超规 继续走规则校验
            if (checkResult.getErrCode() != TemporaryResponseCode.Success.getCode()
                    && checkResult.getErrCode() != GlobalResponseCode.ApplyPersonalPay.getCode()
                    && checkResult.getErrCode() != GlobalResponseCode.ApplyPersonalPay2.getCode()) {
                return checkResult;
            }
        }

        // 单日限额
        boolean singleExceed = false;
        // 每日限额
        boolean dayExceed = false;
        // 时段限制
        boolean timeExceed = true;
        BigDecimal singleCanUsePrice = totalPrice;
        // 每日限额
        BigDecimal dayCanUsePrice = totalPrice;
        BigDecimal singleLimitPrice = ObjUtils.ifNull(dinnerRule.getDinnerFrequencyLimitPrice(), BigDecimal.valueOf(-1));
        // 没有申请单或有申请单并且校验规则
        if (!hasApply || (hasApply && checkRule)) {
            /** 预定人单次差标乘以人数处理 */
            List<MessageSetupVO> messageSetupVOS = setupService.queryBookingConfig(consumerCompanyId);
            if (CollectionUtils.isNotEmpty(messageSetupVOS)) {
                MessageSetupVO takeawayType = messageSetupVOS.stream().filter(a -> a.getItemCode().equals(BookingConfigEnum.MEISHI.getItemCode())).findAny().orElseGet(() -> new MessageSetupVO());
                if (DinnerTakeawayBookingTypeEnum.N_BOOKER.getType().equals(takeawayType.getBookingType())) {
                    log.info("返回预订人差标乘以用餐人数");
                    if (ObjUtils.isNull(reqContract.getDiner_count()) || reqContract.getDiner_count() <= 0) {
                        checkResult.setResCode(GlobalResponseCode.OrderBookingCheckMeiShiDinerCount);
                        return checkResult;
                    } else {
                        log.info("singleLimitPrice = {}, 人数{}", singleLimitPrice, reqContract.getDiner_count());
                        singleLimitPrice = isNBookingType(singleLimitPrice, reqContract.getDiner_count());
                    }
                }
            }

            checkResult.setSingleLimitAmount(singleLimitPrice);
            if (singleLimitPrice.doubleValue() > 0 && singleLimitPrice.compareTo(totalPrice) < 0) {
                // 单次限额超标
                singleExceed = true;
                singleCanUsePrice = singleLimitPrice;
            }
            // 每日限额
            BigDecimal dayLimitPrice = ObjUtils.ifNull(dinnerRule.getDinnerEverydayLimitPrice(), BigDecimal.valueOf(-1));
            checkResult.setDayLimitAmount(dayLimitPrice);
            /*log.info("applyId={}", applyId);
            TotalEstimatedCheckDto totalEstimatedCheckDto = applyAmountService.isEstimatedAmountExcessive(applyId, totalPrice, new Date(), BizType.MeiShi.getCode());
            log.info("estimatedAmountExcessive = {}", JsonUtils.toJson(totalEstimatedCheckDto));
            // 自定义申请单存在 & 总预估费开关打开
            if (totalEstimatedCheckDto.getHasApplyOrder() && totalEstimatedCheckDto.getIsCustformApplyOrder()
                    && Objects.equals(CommonSwitchConstant.OPEN, totalEstimatedCheckDto.getCheckTotalEstimatedFlag())) {
                if (totalEstimatedCheckDto.getIsExceed()) { // 预估费超规
                    if (TotalEstimatedLimitType.DAILY_LIMIT.equalsTo(totalEstimatedCheckDto.getTotalEstimatedCostType())) {
                        checkResult.setResCode(GlobalResponseCode.OrderCheckMeiShiTotalEstimateCostExceedDailyLimit);
                        return checkResult;
                    } else if (TotalEstimatedLimitType.DAILY_AVERAGE.equalsTo(totalEstimatedCheckDto.getTotalEstimatedCostType())) {
                        checkResult.setResCode(GlobalResponseCode.OrderCheckMeiShiTotalEstimateCostExceedDailyAverage);
                        return checkResult;
                    } else {
                        checkResult.setResCode(GlobalResponseCode.OrderCheckMeiShiTotalEstimateCostExceedEmployee);
                        return checkResult;
                    }
                }
            }*/
            if (dayLimitPrice.doubleValue() >= 0) {
                //远程获取当日已使用
//            BigDecimal dayUsedPrice = iMeishiOrderSearchService.getCompanyPayByUserDaylong(reqContract.getEmployee_id(),new Date());

                SaasCountReqDTO var1 = new SaasCountReqDTO();
                var1.setCompanyId(consumerCompanyId);
                var1.setUserId(reqContract.getEmployee_id());
                var1.setSubSceneId(subSceneId);
                var1.setDate(new Date());
                BigDecimal dayUsedPrice = null;
                try {
                    logger.info("当日已经使用金额, req:{}", JsonUtils.toJson(var1));
                    SaasCountResDTO saasRes = iMeishiOrderSearchService.getSassCountByUserDaylong(var1);
                    logger.info("当日已经使用金额, res:{}", JsonUtils.toJson(saasRes));
                    dayUsedPrice = saasRes.getFee();
                } catch (Exception e) {
                    logger.error("获取当日已使用金额失败, {}", JSONObject.toJSON(var1), e);
                    checkResult.setResCode(GlobalResponseCode.OrderCheckGetDinnerDayUsedAmountError);
                    return checkResult;
                }
                if (dayUsedPrice == null) {
                    logger.info("获取当日已使用金额失败");
                    checkResult.setResCode(GlobalResponseCode.OrderCheckGetDinnerDayUsedAmountError);
                    return checkResult;
                }
                logger.info("当日已经使用金额{}", dayUsedPrice);
                checkResult.setDayUsedAmount(dayUsedPrice);
                JSONObject useInfo = new JSONObject();
                useInfo.put("dayUsedPrice", dayUsedPrice);
                snapshotInfo.put("useInfo", useInfo);
                BigDecimal dayNotUsePrice = dayLimitPrice.subtract(dayUsedPrice);
                if (dayNotUsePrice.doubleValue() < 0) {
                    dayNotUsePrice = BigDecimal.ZERO;
                }
                if (dayNotUsePrice.compareTo(totalPrice) < 0) {
                    dayExceed = true;
                    dayCanUsePrice = dayNotUsePrice;
                }
            }
            // 时段限制
            DinnerTimeRangeExample dinnerTimeRangeInfo = new DinnerTimeRangeExample();
            dinnerTimeRangeInfo.createCriteria().andRuleIdEqualTo(dinnerRule.getId());
            List<DinnerTimeRange> dinnerTimeRangeList = dinnerTimeRangeMapper.selectByExample(dinnerTimeRangeInfo);
            log.info("用餐规则时间范围：{}",JsonUtils.toJson(dinnerTimeRangeList));
            if (CollectionUtils.isEmpty(dinnerTimeRangeList)) {
                timeExceed = false;
            } else {
                DinnerTimeRangeExample dinnerTimeRangeExample = new DinnerTimeRangeExample();
                dinnerTimeRangeExample.createCriteria().andRuleIdEqualTo(dinnerRule.getId());
                List<DinnerTimeRange> dinnerTimeRanges = dinnerTimeRangeMapper.selectByExample(dinnerTimeRangeExample);
                log.info("用餐规则时间范围1：{}",JsonUtils.toJson(dinnerTimeRanges));

                if (CollectionUtils.isNotEmpty(dinnerTimeRanges)) {
                    //当前时间
                    LocalDateTime ldt = LocalDateTime.now();
                    int week = ldt.getDayOfWeek();
                    log.info("week:{}",week);
                    for (DinnerTimeRange dinnerTimeRange : dinnerTimeRanges) {
                        long orderTime = DateUtils.parse(ldt.getHourOfDay() + ":" + ldt.getMinuteOfHour(), "HH:mm").getTime();

                        Boolean isOvernight = dinnerTimeRange.getIsOvernight();
                        if (dinnerTimeRange.getDayType().intValue() == week
                                || ((week == 6 || week == 7) && dinnerTimeRange.getDayType().intValue() == 9)
                                || (week != 6 && week != 7 && dinnerTimeRange.getDayType().intValue() == 8)) {
                            long todayEndTime = DateUtils.parse("24:00", "HH:mm").getTime();
                            long startTime = dinnerTimeRange.getBeginTime().getTime();
                            long endTime = isOvernight ? todayEndTime : dinnerTimeRange.getEndTime().getTime();
                            log.info("orderTime：{},todayEndTime：{},startTime:{},endTime:{}",JsonUtils.toJson(orderTime),JsonUtils.toJson(dinnerTimeRanges),JsonUtils.toJson(startTime),JsonUtils.toJson(endTime));
                            if (orderTime >= startTime && orderTime <= endTime) {
                                log.info("校验通过");
                                timeExceed = false;
                                break;
                            }
                        }

                        LocalDateTime pre_ldt = ldt.plusDays(-1);// 前一天
                        int dayOfWeek = pre_ldt.getDayOfWeek();

                        //查看前一天配置到次日的情况
                        if (isOvernight && (dinnerTimeRange.getDayType().intValue() == dayOfWeek
                                || ((dayOfWeek == 6 || dayOfWeek == 7) && dinnerTimeRange.getDayType().intValue() == 9)
                                || (dayOfWeek != 6 && dayOfWeek != 7 && dinnerTimeRange.getDayType().intValue() == 8))
                        ) {
                            long startTime = DateUtils.parse("00:00", "HH:mm").getTime();
                            long endTime = dinnerTimeRange.getEndTime().getTime();
                            log.info("dayOfWeek：{},startTime:{},endTime:{}",JsonUtils.toJson(dayOfWeek),JsonUtils.toJson(startTime),JsonUtils.toJson(endTime));

                            if (orderTime >= startTime && orderTime <= endTime) {
                                timeExceed = false;
                                break;
                            }
                        }

                        //如果不是1～9的日期类型，那么匹配10～12的类型:
                        int ruleDayType = dinnerTimeRange.getDayType().intValue();
                        if (ruleDayType >= 10 && ruleDayType <= 12) {
                            logger.info("美食 时段验证: reqContract={}, dinnerTimeRanges={}", JsonUtils.toJson(reqContract), JsonUtils.toJson(dinnerTimeRanges));
                            //获取订单日期对应的规则分类:
                            int orderDayType = getDayTypeHolidayWorkday(consumerCompanyId, ldt);
                            if (ruleDayType == orderDayType) {
                                long todayEndTime = DateUtils.parse("24:00", "HH:mm").getTime();
                                long startTime = dinnerTimeRange.getBeginTime().getTime();
                                long endTime = isOvernight ? todayEndTime : dinnerTimeRange.getEndTime().getTime();
                                if (orderTime >= startTime && orderTime <= endTime) {
                                    timeExceed = false;
                                    break;
                                }
                            }

                            int pre_order_ldt_day_type = getDayTypeHolidayWorkday(consumerCompanyId, pre_ldt);
                            if (isOvernight && ruleDayType == pre_order_ldt_day_type) {
                                long startTime = DateUtils.parse("00:00", "HH:mm").getTime();
                                long endTime = dinnerTimeRange.getEndTime().getTime();
                                if (orderTime >= startTime && orderTime <= endTime) {
                                    timeExceed = false;
                                    break;
                                }
                            }

                        }

                    }
                } else {
                    timeExceed = false;
                }
            }
        }else{
            timeExceed = false;
        }
        // 是否超标
        logger.info("singleExceed={}, dayExceed={}, timeExceed={}, dinerCountExceed={}", singleExceed, dayExceed, timeExceed, dinerCountExceed);
        if (singleExceed || dayExceed || timeExceed || dinerCountExceed) {
            checkResult.setExceed(true);
            if (employeeMeishiRuleDto.getExceedBuyType() == TravelExceedType.AllowedNeedReason.getCode()
                    && (!employeeMeishiRuleDto.getPersonalPay() || (employeeMeishiRuleDto.getPersonalPay() && (timeExceed || dinerCountExceed)))) {
                // 可以超标，填写理由
                String exceedMsg = "";
                ErrMsgInfo errMsgInfo = new ErrMsgInfo();
                List<ResponseCodeContract> responseCodeContracts = Lists.newArrayList();
                if (singleExceed && dayExceed) {
                    ResponseCodeContract rcc1 = new ResponseCodeContract();
                    ResponseCodeContract rcc2 = new ResponseCodeContract();
                    exceedMsg = StrUtils.formatString(GlobalResponseCode.OrderMeiShiSingleAndDay.getMsg(), checkResult.getSingleLimitAmount().setScale(2), checkResult.getDayLimitAmount().setScale(2));
                    String singleMsg = StrUtils.formatString(GlobalResponseCode.OrderCheckMeiShiSingleExceed.getMsg(), checkResult.getSingleLimitAmount().setScale(2));
                    String dayMsg = StrUtils.formatString(GlobalResponseCode.OrderCheckMeiShiDayExceed.getMsg(), checkResult.getDayLimitAmount().setScale(2));
                    rcc1.setContent(singleMsg);
                    rcc1.setCode(GlobalResponseCode.OrderCheckMeiShiSingleExceed.getCode());
                    responseCodeContracts.add(rcc1);
                    rcc2.setContent(dayMsg);
                    rcc2.setCode(GlobalResponseCode.OrderCheckMeiShiDayExceed.getCode());
                    responseCodeContracts.add(rcc2);
                } else if (singleExceed) {
                    ResponseCodeContract rcc = new ResponseCodeContract();
                    exceedMsg = StrUtils.formatString(GlobalResponseCode.OrderCheckMeiShiSingleExceed.getMsg(), checkResult.getSingleLimitAmount().setScale(2));
                    rcc.setContent(exceedMsg);
                    rcc.setCode(GlobalResponseCode.OrderCheckMeiShiSingleExceed.getCode());
                    responseCodeContracts.add(rcc);
                } else if (dayExceed) {
                    ResponseCodeContract rcc = new ResponseCodeContract();
                    exceedMsg = StrUtils.formatString(GlobalResponseCode.OrderCheckMeiShiDayExceed.getMsg(), checkResult.getDayLimitAmount().setScale(2));
                    rcc.setContent(exceedMsg);
                    rcc.setCode(GlobalResponseCode.OrderCheckMeiShiDayExceed.getCode());
                    responseCodeContracts.add(rcc);
                }
                //时段超规
                if (timeExceed) {
                    ResponseCodeContract rcc = new ResponseCodeContract();
                    exceedMsg = GlobalResponseCode.OrderDinnerTime.getMsg();
                    rcc.setContent(exceedMsg);
                    rcc.setCode(GlobalResponseCode.OrderDinnerTime.getCode());
                    responseCodeContracts.add(rcc);
                }
                // 校验订单用餐人数和申请单用餐人数是否一致
                if (dinerCountExceed) {

                    responseCodeContracts.add(dinerCountRcc);
                }
                //增加返回字段
                logger.info("超规填写理由，超规规则如下：{}", JsonUtils.toJson(responseCodeContracts));
                errMsgInfo.setTitle(SaasOrderConstant.ORDER_OVERRULE_REASON);
                errMsgInfo.setErr_code_list(responseCodeContracts);
                checkResult.setErrMsgInfo(errMsgInfo);
                checkResult.setExceedMsg(exceedMsg);
                boolean exceedSubmit = ObjUtils.ifNull(reqContract.getExceed_submit(), Boolean.FALSE);
                if (exceedSubmit) {
                    // 超标后强制提交
                    if (StringUtils.isEmpty(reqContract.getExceed_reason())
                            || reqContract.getExceed_reason().length() > 50) {
                        checkResult.setResCode(GlobalResponseCode.OrderCheckExceedReasonInvalid);

                        return checkResult;
                    }
                    if (StringUtils.isNotEmpty(reqContract.getExceed_reason_comment())
                            && reqContract.getExceed_reason_comment().length() > 200) {
                        checkResult.setResCode(GlobalResponseCode.OrderCheckExceedReasonCommentInvalid);
                        return checkResult;
                    }
                    ReasonConfig reasonConfig = customReasonService.queryReasonConfigWithoutItems(consumerCompanyId, ReasonType.EXCEED_ORDER_MEISHI);
                    if (reasonConfig.getReason() == SaasMessageConstant.IS_CHECKED_TRUE) {
                        if (StringUtils.isBlank(reqContract.getExceed_reason())) {
                            checkResult.setResCode(GlobalResponseCode.OrderCheckExceedReasonIsNull);
                            return checkResult;
                        }
                        if (reasonConfig.getReason_desc() == SaasMessageConstant.IS_CHECKED_TRUE) {
                            if (StringUtils.isBlank(reqContract.getExceed_reason_comment())) {
                                checkResult.setResCode(GlobalResponseCode.OrderCheckExceedReasonCommentIsNull);
                                return checkResult;
                            }
                        }
                    }
                } else {
                    checkResult.setErrCode(GlobalResponseCode.OrderCheckExceedNeedReason.getCode());
                    checkResult.setErrMsg(GlobalResponseCode.OrderCheckExceedNeedReason.getMsg());
                    return checkResult;
                }
            }
            if (employeeMeishiRuleDto.getExceedBuyType() == TravelExceedType.NotAllowed.getCode()) {
                List<ResponseCodeContract> responseCodeContracts = Lists.newArrayList();
                JSONObject jo = new JSONObject();
                // 不能超标
                String errMsg = "";
                if (!employeeMeishiRuleDto.getPersonalPay()){
                    if (singleExceed && dayExceed) {
                        ResponseCodeContract singleResponseCodeContract = new ResponseCodeContract();
                        singleResponseCodeContract.setType(2);
                        singleResponseCodeContract.setCode(GlobalResponseCode.OrderCheckMeiShiSingleExceed.getCode());
                        errMsg = StrUtils.formatString(GlobalResponseCode.OrderCheckMeiShiSingleExceed.getMsg(), checkResult.getSingleLimitAmount().setScale(2));
                        singleResponseCodeContract.setContent(errMsg);
                        responseCodeContracts.add(singleResponseCodeContract);
                        ResponseCodeContract dayResponseCodeContract = new ResponseCodeContract();
                        dayResponseCodeContract.setType(2);
                        dayResponseCodeContract.setCode(GlobalResponseCode.OrderCheckMeiShiDayExceed.getCode());
                        errMsg = StrUtils.formatString(GlobalResponseCode.OrderCheckMeiShiDayExceed.getMsg(), checkResult.getDayLimitAmount().setScale(2));
                        dayResponseCodeContract.setContent(errMsg);
                        responseCodeContracts.add(dayResponseCodeContract);
                    } else if (singleExceed) {
                        ResponseCodeContract singleResponseCodeContract = new ResponseCodeContract();
                        singleResponseCodeContract.setType(2);
                        singleResponseCodeContract.setCode(GlobalResponseCode.OrderCheckMeiShiSingleExceed.getCode());
                        errMsg = StrUtils.formatString(GlobalResponseCode.OrderCheckMeiShiSingleExceed.getMsg(), checkResult.getSingleLimitAmount().setScale(2));
                        singleResponseCodeContract.setContent(errMsg);
                        responseCodeContracts.add(singleResponseCodeContract);
                    } else if (dayExceed) {
                        ResponseCodeContract dayResponseCodeContract = new ResponseCodeContract();
                        dayResponseCodeContract.setType(2);
                        dayResponseCodeContract.setCode(GlobalResponseCode.OrderCheckMeiShiDayExceed.getCode());
                        errMsg = StrUtils.formatString(GlobalResponseCode.OrderCheckMeiShiDayExceed.getMsg(), checkResult.getDayLimitAmount().setScale(2));
                        dayResponseCodeContract.setContent(errMsg);
                        responseCodeContracts.add(dayResponseCodeContract);
                    }
                    if (timeExceed) {
                        ResponseCodeContract dayResponseCodeContract = new ResponseCodeContract();
                        dayResponseCodeContract.setType(4);
                        dayResponseCodeContract.setCode(GlobalResponseCode.OrderDinnerTime.getCode());
                        errMsg = GlobalResponseCode.OrderDinnerTime.getMsg();
                        dayResponseCodeContract.setContent(errMsg);
                        responseCodeContracts.add(dayResponseCodeContract);
                    }
                    // 校验订单用餐人数和申请单用餐人数是否一致
                    if (dinerCountExceed) {
                        ResponseCodeContract dayResponseCodeContract = new ResponseCodeContract();
                        dayResponseCodeContract.setType(4);
                        dayResponseCodeContract.setCode(GlobalResponseCode.OrderTakeawayCheckDinerNumberNotEqual.getCode());
                        errMsg = StrUtils.formatString(GlobalResponseCode.OrderTakeawayCheckDinerNumberNotEqual.getMsg(), reqContract.getDiner_count() ,applyTripPersonCount);
                        dayResponseCodeContract.setContent(errMsg);
                        responseCodeContracts.add(dayResponseCodeContract);
                    }
                    jo.put("codeList", responseCodeContracts);
                    logger.info("无法继续下单，超规规则如下：{}", JsonUtils.toJson(responseCodeContracts));
                    ErrMsgInfo errMsgInfo = new ErrMsgInfo();
                    errMsgInfo.setTitle(errMsg);
                    errMsgInfo.setErr_code_list(responseCodeContracts);
                    checkResult.setErrMsgInfo(errMsgInfo);
                    checkResult.setErrCode(GlobalResponseCode.OrderCheckNotAllowedOrder.getCode());
                    checkResult.setErrMsg(jo.toJSONString());
                    return checkResult;
                }else {
                    // 规则和申请单超规取严
                    if (singleExceed) {
                        if (singleCanUsePrice.compareTo(checkResult.getCompanyPayAmount()) < 0) {
                            clearApplyCheckResult(checkResult);
                        }
                    }
                    if (dayExceed) {
                        if (dayCanUsePrice.compareTo(checkResult.getCompanyPayAmount()) < 0) {
                            clearApplyCheckResult(checkResult);
                        }
                    }
                    //开启个人支付 如果时间超规
                    if (timeExceed) {
                        ResponseCodeContract dayResponseCodeContract = new ResponseCodeContract();
                        dayResponseCodeContract.setType(4);
                        dayResponseCodeContract.setCode(GlobalResponseCode.OrderDinnerTime.getCode());
                        errMsg = GlobalResponseCode.OrderDinnerTime.getMsg();
                        dayResponseCodeContract.setContent(errMsg);
                        responseCodeContracts.add(dayResponseCodeContract);
                    }
                    // 校验订单用餐人数和申请单用餐人数是否一致
                    if (dinerCountExceed) {
                        ResponseCodeContract dayResponseCodeContract = new ResponseCodeContract();
                        dayResponseCodeContract.setType(4);
                        dayResponseCodeContract.setCode(GlobalResponseCode.OrderTakeawayCheckDinerNumberNotEqual.getCode());
                        errMsg = StrUtils.formatString(GlobalResponseCode.OrderTakeawayCheckDinerNumberNotEqual.getMsg(), reqContract.getDiner_count() ,applyTripPersonCount);
                        dayResponseCodeContract.setContent(errMsg);
                        responseCodeContracts.add(dayResponseCodeContract);
                    }
                    if (timeExceed || dinerCountExceed) {
                        jo.put("codeList", responseCodeContracts);
                        logger.info("无法继续下单，超规规则如下：{}", JsonUtils.toJson(responseCodeContracts));
                        ErrMsgInfo errMsgInfo = new ErrMsgInfo();
                        errMsgInfo.setTitle(errMsg);
                        errMsgInfo.setErr_code_list(responseCodeContracts);
                        checkResult.setErrMsgInfo(errMsgInfo);
                        checkResult.setErrCode(GlobalResponseCode.OrderCheckNotAllowedOrder.getCode());
                        checkResult.setErrMsg(jo.toJSONString());
                        return checkResult;
                    }
                }
            }

            // 规则和申请单超规取严
            if (singleExceed) {
                if (singleCanUsePrice.compareTo(checkResult.getCompanyPayAmount()) >= 0) {
                    return checkResult;
                }
            }
            if (dayExceed) {
                if (dayCanUsePrice.compareTo(checkResult.getCompanyPayAmount()) >= 0) {
                    return checkResult;
                }
            }
            clearApplyCheckResult(checkResult);
            // 超规/合规金额处理
            if (!timeExceed && !dinerCountExceed) {
                checkResult.setAmountCompliance(checkResult.getAmountCompliance().min(singleCanUsePrice).min(dayCanUsePrice));
                checkResult.setAmountNonCompliance(totalPrice.subtract(checkResult.getAmountCompliance()));
            } else {
                checkResult.setAmountCompliance(BigDecimal.ZERO);
                checkResult.setAmountNonCompliance(totalPrice);
            }
            logger.info("[美食下单校验]，合规金额:{}, 超规金额:{}", checkResult.getAmountCompliance(), checkResult.getAmountNonCompliance());
        }
        //开启个人支付
        if (employeeMeishiRuleDto.getPersonalPay()) {
            //公司支付金额
            BigDecimal companyPayMoney=singleCanUsePrice.compareTo(dayCanUsePrice)>0?dayCanUsePrice:singleCanUsePrice;
            //个人支付金额
            BigDecimal employeePayMoney=totalPrice.compareTo(companyPayMoney)>0?totalPrice.subtract(companyPayMoney):BigDecimal.ZERO;


            if(DinnerVendor.ALI_ENT_MS.getCode().equals(reqContract.getVendor_code()) && employeePayMoney.compareTo(BigDecimal.ZERO) > 0){
                log.info("企业码，不支持超规个人付, {}, {}", companyPayMoney, employeePayMoney);
                //企业码，不支持超规个人付
                String msg = StrUtils.formatString(GlobalResponseCode.OrderCheckExceedForbidQYM.getMsg(), companyPayMoney.setScale(2, BigDecimal.ROUND_HALF_UP));
                checkResult.setResCode(GlobalResponseCode.OrderCheckExceedForbidQYM.getCode(), msg);
                return checkResult;
            }

            if (advancePayment) {
                checkResult.setCompanyPayAmount(BigDecimal.ZERO);
                checkResult.setEmployeePayAmount(totalPrice);
                checkResult.setReimbursablePrice(companyPayMoney);
                checkResult.setUnreimbursablePrice(employeePayMoney);
            } else {
                checkResult.setCompanyPayAmount(companyPayMoney);
                checkResult.setEmployeePayAmount(employeePayMoney);
                checkResult.setReimbursablePrice(BigDecimal.ZERO);
                checkResult.setUnreimbursablePrice(employeePayMoney);
            }
            if (advancePayment && ObjUtils.isNotBlank(clientVersion) && VersionTool.greaterThanOrEqualTo(clientVersion, "4.5.0")) {
                // 个人支付提示
                if (reqContract.getPersonalpay_sumbit() == null || !reqContract.getPersonalpay_sumbit()) {
                    LimitPayTipContract limitPayTipContract = null;
                    if (couponExceedPriceSetting == 1) {
                        limitPayTipContract = priceLimitPayTips(employeeMeishiRuleDto.getPersonalPay(), companyPayMoney, totalPrice, BigDecimal.valueOf(0));
                    } else {
                        limitPayTipContract = priceLimitPayTips(employeeMeishiRuleDto.getPersonalPay(), companyPayMoney, totalPrice, couponAmount);
                    }
                    if (limitPayTipContract.isTip_flag()) {
                        checkResult.setResCode(TemporaryResponseCode.OrderCheckPersonalPayTip.getCode(), SaasOrderConstant.ORDER_OVERRULE_TIP);
                        checkResult.setExceedMsg(limitPayTipContract.getTip().getContent());
                        ErrMsgInfo errMsgInfo = new ErrMsgInfo();
                        errMsgInfo.setTitle(SaasOrderConstant.ORDER_OVERRULE_TIP);
                        checkResult.setErrMsgInfo(errMsgInfo);
                        return checkResult;
                    }
                }
            }
        } else {
            if (advancePayment) {
                checkResult.setCompanyPayAmount(BigDecimal.ZERO);
                checkResult.setEmployeePayAmount(totalPrice);
                checkResult.setReimbursablePrice(totalPrice);
                checkResult.setUnreimbursablePrice(BigDecimal.ZERO);
            } else {
                checkResult.setCompanyPayAmount(totalPrice);
                checkResult.setEmployeePayAmount(BigDecimal.ZERO);
                checkResult.setReimbursablePrice(BigDecimal.ZERO);
                checkResult.setUnreimbursablePrice(BigDecimal.ZERO);
            }
        }

        // 节省规则信息
        CommonSavingRuleInfo ruleInfo = new CommonSavingRuleInfo();
        ruleInfo.setRule_limit(employeeMeishiRuleDto.getRuleFlag());
        ruleInfo.setId(dinnerRule.getId());
        ruleInfo.setName(dinnerRule.getName());
        ruleInfo.setPrice_limit(singleLimitPrice);
        BigDecimal priceLimit = ruleInfo.getPrice_limit();
        if (!ObjUtils.isNull(priceLimit) && priceLimit.compareTo(BigDecimal.valueOf(-1)) != 0) {
            ruleInfo.setPrice_limit_flag(true);
            ruleInfo.setDesc(StrUtils.formatString(CoreLanguage.Dinner_Value_SingleUsedMoney.getMessage(), priceLimit.setScale(2,
                    BigDecimal.ROUND_HALF_UP)));
        } else {
            ruleInfo.setPrice_limit_flag(false);
            ruleInfo.setDesc("");
        }
        ruleInfo.setIs_apply(false);
        checkResult.setRule_info(ruleInfo);
        checkResult.setCoupon_used_amount(couponAmount);
        if (couponExceedPriceSetting == 0) {
            BigDecimal couponValue = couponAmount;
            BigDecimal companyActPayPrice = checkResult.getCompanyPayAmount();
            if (advancePayment) {
                companyActPayPrice = BigDecimal.ZERO;
                BigDecimal reimbursablePrice = Optional.ofNullable(checkResult.getReimbursablePrice()).orElse(BigDecimal.ZERO);
                if (reimbursablePrice.compareTo(couponAmount) < 0) {
                    couponValue = reimbursablePrice;
                }
                checkResult.setEmployeePayAmount(reqContract.getTotal_price().subtract(couponValue).setScale(2, RoundingMode.HALF_UP));
                checkResult.setReimbursablePrice(reqContract.getTotal_price().subtract(couponValue).setScale(2, RoundingMode.HALF_UP));
                checkResult.setUnreimbursablePrice(BigDecimal.ZERO);
            } else {
                //公司支付金额
                if (companyActPayPrice.compareTo(couponValue) >= 0) {
                    companyActPayPrice = companyActPayPrice.subtract(couponValue).setScale(2, RoundingMode.HALF_UP);
                } else {
                    couponValue = companyActPayPrice;
                    companyActPayPrice = BigDecimal.ZERO;
                }
            }
            checkResult.setCompanyPayAmount(companyActPayPrice);
            checkResult.setCoupon_used_amount(couponValue);
        }
        return checkResult;
    }

    private void clearApplyCheckResult(DinnerOrderRuleCheckResult result) {
        result.setResCode(GlobalResponseCode.Success);
    }

    private DinnerOrderRuleCheckResult applyOrderCheck(DinnerOrderRuleCheckResult checkResult,
                                                       DinnerOrderCheckReqV2Contract reqContract,
                                                       String consumerCompanyId, BigDecimal totalPrice,
                                                       Boolean advancePayment, EmployeeBaseRule employeeMeishiRuleDto,
                                                       DinnerRule dinnerRule, Integer couponExceedPriceSetting) {

        String applyId = reqContract.getApply_id();
        String tripId = reqContract.getTrip_id();

        ApplyOrder applyOrder = applyOrderMapper.selectByPrimaryKey(applyId);
        logger.info("applyOrder:{}",JsonUtils.toJson(applyOrder));
        if (ObjUtils.isNull(applyOrder)) {
            checkResult.setResCode(GlobalResponseCode.ApplyNotFound);
            return checkResult;
        }
        String employeeId = applyOrder.getEmployeeId();
        String applyOrderCompanyId = applyOrder.getCompanyId();
        if (!reqContract.getCompany_id().equals(applyOrderCompanyId)) {
            checkResult.setResCode(GlobalResponseCode.ApplyNotFound);
            return checkResult;
        }
        Integer applyOrderType = applyOrder.getApplyOrderType();
        if (applyOrderType != CompanySettingType.DinnerApply.getValue()
                && applyOrderType != CompanySettingType.CustomFormApply.getValue()) {
            checkResult.setResCode(GlobalResponseCode.ApplyTypeNotAvailable);
            return checkResult;
        }
        Integer state = applyOrder.getState();
        if (state != ApplyStatus.Approved.getValue()) {
            checkResult.setResCode(GlobalResponseCode.ApplyStateNotAvailable);
            return checkResult;
        }
        ApplyTripInfo applyTripInfo;
        if (ObjUtils.isBlank(tripId)) {
            checkResult.setResCode(GlobalResponseCode.ApplyTripNotFound.getCode(), GlobalResponseCode.ApplyTripNotFound.getMsg());
            return checkResult;
        }
        if (applyOrderType == CompanySettingType.CustomFormApply.getValue()) {
            applyTripInfo = applyTripInfoMapper.selectByPrimaryKey(tripId);
            logger.info("applyTripInfo:{}",JsonUtils.toJson(applyTripInfo));
            if (ObjUtils.isEmpty(applyTripInfo)) {
                checkResult.setResCode(GlobalResponseCode.ApplyTripNotFound.getCode(), GlobalResponseCode.ApplyTripNotFound.getMsg());
                return checkResult;
            }
        } else {
            applyTripInfo = applyTripInfoMapper.selectByPrimaryKey(tripId);
            if (ObjUtils.isEmpty(applyTripInfo)) {
                checkResult.setResCode(GlobalResponseCode.ApplyNotFound);
                return checkResult;
            }
        }

        Date startTime = ObjUtils.isEmpty(applyTripInfo.getFloatStartTime()) ? applyTripInfo.getStartTime() : applyTripInfo.getFloatStartTime();
        Date endTime = ObjUtils.isEmpty(applyTripInfo.getFloatEndTime()) ? applyTripInfo.getEndTime() : applyTripInfo.getFloatEndTime();

        Date now = new Date();
        if (now.before(startTime) || !now.before(endTime)) {
            checkResult.setResCode(GlobalResponseCode.ApplyNotAvailable);
            return checkResult;
        }
        logger.info("是否范围时间申请单：{}", applyTripInfo.getTripType());
        ApplySetupContract applySetupContract = messageSetupService.queryCompanyApplyConfig(applyOrderCompanyId);
        logger.info("当前配置countLimit: {}, timeLimit: {}", applySetupContract.getApply_meishi_count_limit(), applySetupContract.getApply_meishi_time_limit());
        // 申请单是否限制使用次数
        boolean useCountLimitCheck = false;
        // 申请单是否需要预估费用校验
//            boolean applyEstimatedCheck = false;
//            boolean isEstimatedExceedPersonalPay = false; // 是否开启预估费个人支付
        if (applyOrderType == CompanySettingType.CustomFormApply.getValue()) {
            CustomFormApplyControlItemDTO customFormApplyConfig =
                    iCustomFormService.queryCustomFormApplyControlConfig(applyOrder.getCompanyId(), applyOrder.getFormId(),
                            BizType.MeiShi.getCode(), applyOrder.getId());
            log.info("自定义申请单配置：{}", JsonUtils.toJson(customFormApplyConfig));
            if (customFormApplyConfig != null && Objects.equals(SwitchTypeEnum.OPEN.getSwitchCode(),
                    customFormApplyConfig.getUseCountLimit())) {
                useCountLimitCheck = true;
            }
//                if (customFormApplyConfig != null && Objects.equals(SwitchTypeEnum.OPEN.getSwitchCode(),
//                    customFormApplyConfig.getEstimatedCostCheckSwitch())) {
//                    applyEstimatedCheck = true;
//                }
//                if (customFormApplyConfig != null && Objects.equals(SwitchTypeEnum.OPEN.getSwitchCode(),
//                    customFormApplyConfig.getPersonalPaySwitch())) {
//                    isEstimatedExceedPersonalPay = true;
//                }
        } else {
            useCountLimitCheck = Objects.equals(applySetupContract.getApply_meishi_count_limit(), 1);
//                applyEstimatedCheck = true; // 非自定义申请单默认开启
//                isEstimatedExceedPersonalPay = true;  // 用餐默认开启
        }

        if (useCountLimitCheck) { // 限制使用次数时
            List<OrderSaasResDTO> orderDTOList = meishiOrderService.queryOrderList(applyTripInfo.getId());
            long count = orderDTOList.size();
            if (count > 0) {
                checkResult.setResCode(GlobalResponseCode.ApplyNotAvailable);
                return checkResult;
            }
        }

        // 校验预估费
        String startDateStr = DateTimeTool.fromDateToStringByFormatter(new Date(), DateTimeTool.dateTimeFormator);
        CheckApplyEstimatedAmountReq req = new CheckApplyEstimatedAmountReq();
        req.setApplyId(applyId);
        req.setTripId(tripId);
        req.setCompanyId(consumerCompanyId);
        req.setValidateAmount(totalPrice);
        req.setBizType(BizType.MeiShi.getCode());
        req.setStartDate(startDateStr);
        req.setEndDate(startDateStr);
        AmountExceedCheckRes amountExceedCheckRes = tripEstimateService.checkTripEstimatedAmount(req);
        boolean isEstimatedExceedPersonalPay = amountExceedCheckRes.getIsPersonalPay();
        boolean isEstimatedExceed = GlobalResponseCode.Success.getCode() != amountExceedCheckRes.getErrCode();
//            BigDecimal applyPriceLimit = amountExceedCheckRes.getTotalEstimatedLimitAmount();
        if (isEstimatedExceed) {
            // 如果超过预估费且未开启个人支付的情况 固定单据isEstimatedExceedPersonalPay始终为true
            if (!Objects.equals(amountExceedCheckRes.getApplyType(), ApplyType.CustomFromBeforehand.getValue())) {
                if (!isEstimatedExceedPersonalPay) {
                    String content = String.format("您预订的行程实际费用超过申请单费用上限，请修改申请单或订单信息\n"
                                    + "行程费用上限金额：￥%s\n本次预订金额：￥%s",
                            amountExceedCheckRes.getLeftAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString(),
                            totalPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                    checkResult.setResCode(GlobalResponseCode.ApplyTripEstimatedAmountExceed.getCode(), content);
                    return checkResult;
                }
            } else { // 自定义申请单的情况
                if (amountExceedCheckRes.getIsCheckSumEstimateAmount()) {
                    // 算出单日剩余额度 用餐只有一天  所以加和即可
                    if (TotalEstimatedLimitType.DAILY_LIMIT.equalsTo(amountExceedCheckRes.getTotalEstimatedLimitIntValue())) {
                        BigDecimal left = BigDecimal.ZERO;
                        for (BigDecimal bigDecimal : amountExceedCheckRes.getDailyLeftAmountList()) {
                            left = left.add(bigDecimal);
                        }
                        amountExceedCheckRes.setLeftAmount(left); // 设置值 防止后面判断空指针
                    }
                    if (!ObjUtils.ifNull(reqContract.getApply_personalpay_sumbit(), Boolean.FALSE)) { // 个人支付二次提交
                        if (TotalEstimatedLimitType.DAILY_LIMIT.equalsTo(amountExceedCheckRes.getTotalEstimatedLimitIntValue())) {
                            checkResult.setResCode(GlobalResponseCode.OrderCheckMeiShiTotalEstimateCostExceedDailyLimit);
                        } else if (TotalEstimatedLimitType.DAILY_AVERAGE.equalsTo(amountExceedCheckRes.getTotalEstimatedLimitIntValue())) {
                            checkResult.setResCode(GlobalResponseCode.OrderCheckMeiShiTotalEstimateCostExceedDailyAverage);
                        } else {
                            checkResult.setResCode(GlobalResponseCode.OrderCheckMeiShiTotalEstimateCostExceedEmployee);
                        }
                    }
                } else {
                    if (!isEstimatedExceedPersonalPay) {
                        String content = String.format("您预订的行程实际费用超过申请单费用上限，请修改申请单或订单信息\n"
                                        + "行程费用上限金额：￥%s\n本次预订金额：￥%s",
                                amountExceedCheckRes.getLeftAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString(),
                                totalPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                        checkResult.setResCode(GlobalResponseCode.ApplyTripEstimatedAmountExceed.getCode(), content);
                        return checkResult;
                    }
                }
            }
        }

//            BigDecimal applyPriceLimit = ObjUtils.ifNull(applyTripInfo.getEstimatedAmount(), BigDecimal.ZERO);
//            if (applyOrderType != CompanySettingType.CustomFormApply.getValue()) { // 非自定义申请单
//                if (applyEstimatedCheck) { // 非自定义的默认走预估费用校验
//                    ApplyAmountReqDTO applyAmountReqDTO = new ApplyAmountReqDTO();
//                    applyAmountReqDTO.setApplyId(applyId);
//                    applyAmountReqDTO.setTripId(tripId);
//                    applyAmountReqDTO.setSceneList(Lists.newArrayList(BizType.MeiShi.getCode()));
//                    applyPriceLimit = applyTripAmountService.queryTotalOrderAmount(applyAmountReqDTO);
//                        getApplyEstimatedLeft(applyId, applyTripInfo, applyPriceLimit);
//                    // 如果超过预估费且为开启个人支付的情况 固定单据isEstimatedExceedPersonalPay始终为true
//                    if (totalPrice.compareTo(applyPriceLimit) > 0 && !isEstimatedExceedPersonalPay) {
//                        String content = String.format("您预订的行程实际费用超过申请单费用上限，请修改申请单或订单信息\n"
//                                + "行程费用上限金额：￥%s\n本次预订金额：￥%s",
//                            applyPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString(),
//                            totalPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
//                        checkResult.setResCode(GlobalResponseCode.ApplyTripEstimatedAmountExceed.getCode(), content);
//                        return checkResult;
//                    }
//                }
//            } else {
//                // 总预估费
//                TotalEstimatedCheckDto totalEstimatedCheckDto = applyAmountService.isEstimatedAmountExcessive(applyId, totalPrice, new Date(),BizType.MeiShi.getCode());
//                log.info("estimatedAmountExcessive = {}", JsonUtils.toJson(totalEstimatedCheckDto));
//                // 自定义申请单存在 & 总预估费开关打开
//                if (totalEstimatedCheckDto.getHasApplyOrder()
//                    && totalEstimatedCheckDto.getIsCustformApplyOrder()
//                    && Objects.equals(CommonSwitchConstant.OPEN, totalEstimatedCheckDto.getCheckTotalEstimatedFlag())) {
//                    isEstimatedExceedPersonalPay = true; // 总预估默认开启超预估费用个人支付
//                    applyPriceLimit = totalEstimatedCheckDto.getTotalEstimatedCost().subtract(totalEstimatedCheckDto.getUsedAmount());
//                    if(applyPriceLimit.compareTo(BigDecimal.ZERO) < 0){
//                        log.error("美食剩余预估费小于0异常");
//                    }
//                    log.info("advancePayment={}, apply_personalpay_sumbit={}", advancePayment,
//                        reqContract.getApply_personalpay_sumbit());
//                    if (totalEstimatedCheckDto.getIsExceed() // 预估费超规，
//                        && !ObjUtils.ifNull(reqContract.getApply_personalpay_sumbit(), Boolean.FALSE)) { // 个人支付二次提交
//                        if (TotalEstimatedLimitType.DAILY_LIMIT.equalsTo(totalEstimatedCheckDto.getTotalEstimatedCostType())) {
//                            checkResult.setResCode(GlobalResponseCode.OrderCheckMeiShiTotalEstimateCostExceedDailyLimit);
//                        } else if (TotalEstimatedLimitType.DAILY_AVERAGE.equalsTo(totalEstimatedCheckDto.getTotalEstimatedCostType())) {
//                            checkResult.setResCode(GlobalResponseCode.OrderCheckMeiShiTotalEstimateCostExceedDailyAverage);
//                        } else {
//                            checkResult.setResCode(GlobalResponseCode.OrderCheckMeiShiTotalEstimateCostExceedEmployee);
//                        }
//                    }
//                } else { // 总预估费如果没有开启
//                    if (applyEstimatedCheck) { // 自定义申请单预估费用是否开启
//                        ApplyAmountReqDTO applyAmountReqDTO = new ApplyAmountReqDTO();
//                        applyAmountReqDTO.setApplyId(applyId);
//                        applyAmountReqDTO.setTripId(tripId);
//                        applyAmountReqDTO.setSceneList(Lists.newArrayList(BizType.MeiShi.getCode()));
//                        applyPriceLimit = applyTripAmountService.queryTotalOrderAmount(applyAmountReqDTO);
//                        // 如果超过预估费且为开启超预估费个人支付则提示禁止下单
//                        if (totalPrice.compareTo(applyPriceLimit) > 0 && !isEstimatedExceedPersonalPay) {
//                            String content = String.format("您预订的行程实际费用超过申请单费用上限，请修改申请单或订单信息\n"
//                                    + "行程费用上限金额：￥%s\n本次预订金额：￥%s",
//                                applyPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).toString(),
//                                totalPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
//                            checkResult.setResCode(GlobalResponseCode.ApplyTripEstimatedAmountExceed.getCode(), content);
//                            return checkResult;
//                        }
//                    } else {
//                        applyPriceLimit = BigDecimal.valueOf(Long.MAX_VALUE); // 兼容下面逻辑  给预估费设置超大值
//                    }
//                }
//            }

        //公司支付金额
        BigDecimal companyPayMoney;
        //个人支付金额
        BigDecimal employeePayMoney;

        if (isEstimatedExceed && isEstimatedExceedPersonalPay && totalPrice.compareTo(amountExceedCheckRes.getLeftAmount()) > 0) {
            companyPayMoney = amountExceedCheckRes.getLeftAmount();
            employeePayMoney = totalPrice.subtract(companyPayMoney);

            //企业码不支持超规个人付
            if(DinnerVendor.ALI_ENT_MS.getCode().equals(reqContract.getVendor_code()) && employeePayMoney.compareTo(BigDecimal.ZERO) > 0){
                log.info("企业码，不支持超规个人付, {}, {}", companyPayMoney, employeePayMoney);
                //企业码，不支持超规个人付
                String msg = StrUtils.formatString(GlobalResponseCode.OrderCheckExceedForbidQYM.getMsg(), companyPayMoney.setScale(2, BigDecimal.ROUND_HALF_UP));
                checkResult.setResCode(GlobalResponseCode.OrderCheckExceedForbidQYM.getCode(), msg);
                return checkResult;
            }

        } else {
            companyPayMoney = totalPrice;
            employeePayMoney = BigDecimal.ZERO;
        }
        checkResult.setCompanyPayAmount(companyPayMoney);
        if (reqContract.getApply_personalpay_sumbit() == null) {
            reqContract.setApply_personalpay_sumbit(false);
        }
        if (!reqContract.getApply_personalpay_sumbit()
                && employeePayMoney.compareTo(BigDecimal.ZERO) > 0) {
            GlobalResponseCode tip;
            if (advancePayment) {
                tip = GlobalResponseCode.ApplyPersonalPay2;
            } else {
                tip = GlobalResponseCode.ApplyPersonalPay;
            }
            checkResult.setResCode(tip.getCode(), StrUtils.formatString(tip.getMsg(), amountExceedCheckRes.getLeftAmount()));
            return checkResult;
        }
        if (advancePayment) {
            checkResult.setCompanyPayAmount(BigDecimal.ZERO);
            checkResult.setEmployeePayAmount(totalPrice);
            checkResult.setReimbursablePrice(companyPayMoney);
            checkResult.setUnreimbursablePrice(employeePayMoney);
        } else {
            checkResult.setCompanyPayAmount(companyPayMoney);
            checkResult.setEmployeePayAmount(employeePayMoney);
            checkResult.setReimbursablePrice(BigDecimal.ZERO);
            checkResult.setUnreimbursablePrice(employeePayMoney);
        }
        checkResult.setAmountCompliance(companyPayMoney);
        checkResult.setAmountNonCompliance(employeePayMoney);
        // 节省规则信息
        CommonSavingRuleInfo ruleInfo = new CommonSavingRuleInfo();
        ruleInfo.setRule_limit(true);
        ruleInfo.setDesc("");
        ruleInfo.setPrice_limit_flag(true);
        ruleInfo.setPrice_limit(new BigDecimal(-1));
        if (employeeMeishiRuleDto.getRuleFlag()) {
            if (dinnerRule == null) {
                checkResult.setResCode(GlobalResponseCode.OrderCheckRuleNotExist);
                return checkResult;
            }
            //节省规则信息
            ruleInfo.setId(dinnerRule.getId());
            ruleInfo.setName(dinnerRule.getName());
        }
        BigDecimal couponAmount = ObjUtils.ifNull(reqContract.getCoupon_amount(), BigDecimal.ZERO);
        ruleInfo.setIs_apply(true);
        checkResult.setRule_info(ruleInfo);
        checkResult.setCoupon_used_amount(couponAmount);
        if (couponExceedPriceSetting == 0) {
            BigDecimal couponValue = couponAmount;
            BigDecimal companyActPayPrice = checkResult.getCompanyPayAmount();
            if (advancePayment) {
                companyActPayPrice = BigDecimal.ZERO;
                BigDecimal reimbursablePrice = Optional.ofNullable(checkResult.getReimbursablePrice()).orElse(BigDecimal.ZERO);
                if (reimbursablePrice.compareTo(couponAmount) < 0) {
                    couponValue = reimbursablePrice;
                }
                checkResult.setEmployeePayAmount(reqContract.getTotal_price().subtract(couponValue).setScale(2, RoundingMode.HALF_UP));
                checkResult.setReimbursablePrice(reqContract.getTotal_price().subtract(couponValue).setScale(2, RoundingMode.HALF_UP));
                checkResult.setUnreimbursablePrice(BigDecimal.ZERO);
            } else {
                //公司支付金额
                if (companyActPayPrice.compareTo(couponValue) >= 0) {
                    companyActPayPrice = companyActPayPrice.subtract(couponValue).setScale(2, RoundingMode.HALF_UP);
                } else {
                    couponValue = companyActPayPrice;
                    companyActPayPrice = BigDecimal.ZERO;
                }
            }
            checkResult.setCompanyPayAmount(companyActPayPrice);
            checkResult.setCoupon_used_amount(couponValue);
        }
        return checkResult;
    }



    /**
     * 校验外卖申请单 相关校验
     * @param dinnerOrderCheckReqV2Contract
     */
    @Override
    public ApplyCheckRes checkDinnerByApply(DinnerOrderCheckReqV2Contract dinnerOrderCheckReqV2Contract){

        //申请单id
        String applyId = dinnerOrderCheckReqV2Contract.getApply_id();
        //行程id
        String tripId = dinnerOrderCheckReqV2Contract.getTrip_id();
        //是否有申请单
        boolean hasApply = ObjUtils.isNotBlank(dinnerOrderCheckReqV2Contract.getApply_id()) ? true : false;
        //公司id
        String companyId = dinnerOrderCheckReqV2Contract.getCompany_id();
        //员工id
        String employeeId = dinnerOrderCheckReqV2Contract.getEmployee_id();
        ApplyCheckRes applyCheckRes = new ApplyCheckRes();
        applyCheckRes.setApplyId(applyId);
        applyCheckRes.setTripId(tripId);
        applyCheckRes.setCompanyId(companyId);
        applyCheckRes.setEmployeeId(employeeId);
        applyCheckRes.setHasApply(hasApply);

        if(applyCheckRes.getHasApply()){
            ApplyOrder applyOrder = applyOrderMapper.selectByPrimaryKey(applyId);
            logger.info("申请单信息:{}",  JsonUtils.toJson(applyOrder));

            ApplyTripInfo applyTripInfo =  applyTripInfoMapper.selectByPrimaryKey(tripId);
            logger.info("行程信息:{}",  JsonUtils.toJson(applyTripInfo));
            applyCheckRes.setApplyTripPersonCount(applyTripInfo.getPersonCount());

            // 申请单是否限制使用次数
            boolean useCountLimitCheck = false;

            //自定义申请单
            if(applyOrder.getType().equals(ApplyType.CustomFromBeforehand.getValue())){
                CustomFormApplyControlItemDTO customApply = iCustomFormService.queryCustomFormApplyControlConfig(companyId,applyOrder.getFormId(), BizType.Takeaway.getCode(),applyOrder.getId());
                if (customApply != null && Objects.equals(SwitchTypeEnum.OPEN.getSwitchCode(),customApply.getUseCountLimit())) {
                    useCountLimitCheck = true;
                }
            }else{
                ApplySetupContract applySetupContract = messageSetupService.queryCompanyApplyConfig(companyId);
                logger.info("当前配置countLimit: {}, timeLimit: {}", applySetupContract.getApply_meishi_count_limit(), applySetupContract.getApply_meishi_time_limit());
                useCountLimitCheck = Objects.equals(applySetupContract.getApply_meishi_count_limit(), 1);
            }
            //申请单时段
            Date startTime = ObjUtils.isEmpty(applyTripInfo.getFloatStartTime()) ? applyTripInfo.getStartTime() : applyTripInfo.getFloatStartTime();
            Date endTime = ObjUtils.isEmpty(applyTripInfo.getFloatEndTime()) ? applyTripInfo.getEndTime() : applyTripInfo.getFloatEndTime();
            //订单时间
            Date orderTime = new Date();


            //您用餐时间与所选用的申请单不匹配
            if (orderTime.before(startTime) || !orderTime.before(endTime)) {
                CurrencyMsg currencyMsg = new CurrencyMsg();
                currencyMsg.setErrMsg(GlobalResponseCode.ApplyDinnerNotAvailable.getMsg());
                currencyMsg.setCode(GlobalResponseCode.ApplyDinnerNotAvailable.getCode());
                applyCheckRes.setIsExceed(true);
                applyCheckRes.setIsNoPriceExceed(true);
                applyCheckRes.getCurrencyMsgList().add(currencyMsg);
            }

            // 限制使用次数时
            if (useCountLimitCheck) {
                List<OrderSaasResDTO> orderDTOList = meishiOrderService.queryOrderList(applyTripInfo.getId());
                if (orderDTOList.size() > 0) {
                    //申请单次数限制不通过
                    CurrencyMsg currencyMsg = new CurrencyMsg();
                    currencyMsg.setErrMsg(GlobalResponseCode.ApplyDinnerNotMatchApply.getMsg());
                    currencyMsg.setCode(GlobalResponseCode.ApplyDinnerNotMatchApply.getCode());
                    applyCheckRes.setIsExceed(true);
                    applyCheckRes.setIsNoPriceExceed(true);
                    applyCheckRes.getCurrencyMsgList().add(currencyMsg);
                }
            }
            //订单金额
            BigDecimal totalPrice = ObjUtils.ifNull(dinnerOrderCheckReqV2Contract.getTotal_price(), BigDecimal.ZERO);
            //优惠券金额
            BigDecimal couponAmount = ObjUtils.ifNull(dinnerOrderCheckReqV2Contract.getCoupon_amount(), BigDecimal.ZERO);
            //优惠券抵扣超规
            Integer couponExceedPriceSetting = iOrderCheckService.queryCouponExceedPriceSetting(companyId);
            if (couponExceedPriceSetting == 1) {
                totalPrice = totalPrice.subtract(couponAmount).max(BigDecimal.ZERO);
            }

            // 校验预估费
            String startDateStr = DateTimeTool.fromDateToStringByFormatter(orderTime, DateTimeTool.dateTimeFormator);
            CheckApplyEstimatedAmountReq req = new CheckApplyEstimatedAmountReq();
            req.setApplyId(applyId);
            req.setTripId(tripId);
            req.setCompanyId(companyId);
            req.setValidateAmount(totalPrice);
            req.setBizType(BizType.MeiShi.getCode());
            req.setStartDate(startDateStr);
            req.setEndDate(startDateStr);

            AmountExceedCheckRes amountExceedCheckRes = tripEstimateService.checkTripEstimatedAmount(req);
            boolean isEstimatedExceedPersonalPay = amountExceedCheckRes.getIsPersonalPay();
            boolean isEstimatedExceed = GlobalResponseCode.Success.getCode() != amountExceedCheckRes.getErrCode();
            if (isEstimatedExceed) {
                applyCheckRes.setIsExceed(true);
                applyCheckRes.setIsPriceExceed(true);
                // 如果超过预估费且未开启个人支付的情况 固定单据isEstimatedExceedPersonalPay始终为true
                if (!Objects.equals(amountExceedCheckRes.getApplyType(), ApplyType.CustomFromBeforehand.getValue())) {
                    if (!isEstimatedExceedPersonalPay) {
                        String content = String.format("您预订的行程实际费用超过申请单费用上限，请修改申请单或订单信息\n"
                                        + "行程费用上限金额：￥%s\n本次预订金额：￥%s",
                                amountExceedCheckRes.getLeftAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString(),
                                totalPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                        CurrencyMsg currencyMsg = new CurrencyMsg();
                        currencyMsg.setErrMsg(content);
                        currencyMsg.setCode(GlobalResponseCode.ApplyTripEstimatedAmountExceed.getCode());
                        applyCheckRes.getCurrencyMsgList().add(currencyMsg);
                    }
                } else { // 自定义申请单的情况
                    if (amountExceedCheckRes.getIsCheckSumEstimateAmount()) {
                        if (TotalEstimatedLimitType.DAILY_LIMIT.equalsTo(amountExceedCheckRes.getTotalEstimatedLimitIntValue())) {
                            BigDecimal left = BigDecimal.ZERO;
                            for (BigDecimal bigDecimal : amountExceedCheckRes.getDailyLeftAmountList()) {
                                left = left.add(bigDecimal);
                            }
                            amountExceedCheckRes.setLeftAmount(left); // 设置值 防止后面判断空指针
                        }
                        // 首次提交
                        if (!ObjUtils.ifNull(dinnerOrderCheckReqV2Contract.getApply_personalpay_sumbit(), Boolean.FALSE)) {
                            if (TotalEstimatedLimitType.DAILY_LIMIT.equalsTo(amountExceedCheckRes.getTotalEstimatedLimitIntValue())) {
                                CurrencyMsg currencyMsg = new CurrencyMsg();
                                currencyMsg.setErrMsg(TemporaryResponseCode.OrderTakeawayDailyLimitCostOverLeftEstimatedCost.getMsg());
                                currencyMsg.setCode(TemporaryResponseCode.OrderTakeawayDailyLimitCostOverLeftEstimatedCost.getCode());
                                applyCheckRes.getCurrencyMsgList().add(currencyMsg);
                            } else if (TotalEstimatedLimitType.DAILY_AVERAGE.equalsTo(amountExceedCheckRes.getTotalEstimatedLimitIntValue())) {
                                CurrencyMsg currencyMsg = new CurrencyMsg();
                                currencyMsg.setErrMsg(TemporaryResponseCode.OrderTakeawayDailyAverageCostOverLeftEstimatedCost.getMsg());
                                currencyMsg.setCode(TemporaryResponseCode.OrderTakeawayDailyAverageCostOverLeftEstimatedCost.getCode());
                                applyCheckRes.getCurrencyMsgList().add(currencyMsg);
                            } else {
                                CurrencyMsg currencyMsg = new CurrencyMsg();
                                currencyMsg.setErrMsg(TemporaryResponseCode.OrderTakeawayEmployeeCostOverLeftEstimatedCost.getMsg());
                                currencyMsg.setCode(TemporaryResponseCode.OrderTakeawayEmployeeCostOverLeftEstimatedCost.getCode());
                                applyCheckRes.getCurrencyMsgList().add(currencyMsg);
                            }
                        }
                    } else {
                        if (!isEstimatedExceedPersonalPay) {
                            String content = String.format("您预订的行程实际费用超过申请单费用上限，请修改申请单或订单信息\n"
                                            + "行程费用上限金额：￥%s\n本次预订金额：￥%s",
                                    amountExceedCheckRes.getLeftAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString(),
                                    totalPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                            CurrencyMsg currencyMsg = new CurrencyMsg();
                            currencyMsg.setErrMsg(content);
                            currencyMsg.setCode(GlobalResponseCode.ApplyTripEstimatedAmountExceed.getCode());
                            applyCheckRes.getCurrencyMsgList().add(currencyMsg);
                        }
                    }
                }
            }
            if (isEstimatedExceed && isEstimatedExceedPersonalPay && totalPrice.compareTo(amountExceedCheckRes.getLeftAmount()) > 0) {
                log.info("预估费超规且预估费开启个人支付且订单金额大于剩余金额，申请单多余部分走个人支付，支付金额：{}",amountExceedCheckRes.getLeftAmount());
                applyCheckRes.setCompanyPayPrice(amountExceedCheckRes.getLeftAmount());
                applyCheckRes.setPersonalPay(true);
            } else {
                applyCheckRes.setCompanyPayPrice(totalPrice);
                applyCheckRes.setPersonalPay(false);
            }
            BigDecimal personalPayPrice = totalPrice.subtract(applyCheckRes.getCompanyPayPrice());
            applyCheckRes.setPersonalPayPrice(personalPayPrice);
            //企业码不支持超规个人付
            if(DinnerVendor.ALI_ENT_MS.getCode().equals(dinnerOrderCheckReqV2Contract.getVendor_code()) && personalPayPrice.compareTo(BigDecimal.ZERO) > 0){
                log.info("企业码，不支持超规个人付, {}, {}", applyCheckRes.getCompanyPayPrice(), personalPayPrice);
                //企业码，不支持超规个人付
                String msg = StrUtils.formatString(GlobalResponseCode.OrderCheckExceedForbidQYM.getMsg(),  applyCheckRes.getCompanyPayPrice().setScale(2, BigDecimal.ROUND_HALF_UP));
                CurrencyMsg currencyMsg = new CurrencyMsg();
                currencyMsg.setErrMsg(msg);
                currencyMsg.setCode(GlobalResponseCode.OrderCheckExceedForbidQYM.getCode());
                applyCheckRes.setIsExceed(true);
                applyCheckRes.setIsNoPriceExceed(true);
                applyCheckRes.getCurrencyMsgList().add(currencyMsg);
            }
        }
        log.info("用餐申请单校验结果:{}",JsonUtils.toJson(applyCheckRes));
        return applyCheckRes;
    }


    /** 判定当前多人用餐规则是否为：预订人差标乘以用餐人数。  **/
    public BigDecimal isNBookingType(BigDecimal priceLimit, Integer dinerCount){
        log.info("isNBookingType: {}, {}", priceLimit, dinerCount);
        if(ObjUtils.isNull(priceLimit) || priceLimit.compareTo(BigDecimal.valueOf(-1)) == 0){
            log.info("不处理单次相乘");
            return priceLimit;
        }
        return priceLimit.multiply(new BigDecimal(dinerCount));
    }

    public int getDayTypeHolidayWorkday(String companyId, LocalDateTime ldt){
        String date = DateUtils.format(ldt.toDate(), DateUtils.FORMAT_DATE_WITH_BAR);
        /** 调用接口判定日期对应的类型 */
        List<CalendarRpcVO> calendarRpcVOS = null;
        try{
            calendarRpcVOS = rpcCompanyCalendarService.queryNonWorkDaysInRange(companyId, date, date);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("rpcCompanyCalendarService.queryNonWorkDaysInRange", e);
        }
        logger.info("美食 getDayTypeHolidayWorkday, companyId={}, date={}, calendarRpcVOS={}",
                companyId, date, JsonUtils.toJson(calendarRpcVOS));
        if(ObjectUtils.isEmpty(calendarRpcVOS)){
            return -1;
        }
        // 优先级： 节假日11 > 休息日12 > 工作日10 
        for(CalendarRpcVO vo : calendarRpcVOS){
            CalendarRpcVO.CalendarItems calendarItems = vo.getCalendarItems();
            if(ObjectUtils.isEmpty(calendarItems)){
                continue;
            }
            List<Long> holidays = calendarItems.getHolidays();
            List<Long> restdays = calendarItems.getRestdays();
            List<Long> workDays = calendarItems.getWorkDays();
            
            if(null != holidays && holidays.size() > 0){
                return 11;
            }
            if(null != restdays && restdays.size() > 0){
                return 12;
            }
            if(null != workDays && workDays.size() > 0){
                return 10;
            }
        }
        return -1;
    }


    /**
     * 校验美食预算
     *
     * @param reqContract
     * @param ruleCheckResult
     * @param costAttrAndBudgetConf
     * @return
     */
    private DinnerOrderBudgetCheckResult deliciousOrderBudgetCheck(DinnerOrderCheckReqV2Contract reqContract, DinnerOrderRuleCheckResult ruleCheckResult, CostAttrAndBudgetConf costAttrAndBudgetConf, String clientVersion) throws SaasException {
        String costCompanyId = StringUtils.isNotBlank(reqContract.getCost_company_id()) ? reqContract.getCost_company_id() : reqContract.getCompany_id();
        // 初始化返回值
        DinnerOrderBudgetCheckResult budgetCheckResult = new DinnerOrderBudgetCheckResult();
        OrderBudgetCheckV2Contract orderBudgetCheckContract = new OrderBudgetCheckV2Contract();
        orderBudgetCheckContract.setEmployee_id(reqContract.getEmployee_id());
        orderBudgetCheckContract.setCompany_id(costCompanyId);
        orderBudgetCheckContract.setCost_info(reqContract.getCost_info());
        orderBudgetCheckContract.setPrice(ruleCheckResult.getCompanyPayAmount());
        orderBudgetCheckContract.setForce_sumbit(reqContract.getForce_sumbit());
        TravelOnBusiOrderRuleCheckResult travelOrderBudgetCheckResult = iOrderCheckService.categoryOrderBudgetCheckV2(orderBudgetCheckContract, costAttrAndBudgetConf, OrderCategory.Dinner, clientVersion);
        if (travelOrderBudgetCheckResult != null && travelOrderBudgetCheckResult.getErrCode() != GlobalResponseCode.Success.getCode()) {
            budgetCheckResult.setErrCode(travelOrderBudgetCheckResult.getErrCode());
            budgetCheckResult.setErrMsg(travelOrderBudgetCheckResult.getErrMsg());
        }
        BigDecimal budgetAmountCompliance = travelOrderBudgetCheckResult.getAmountCompliance();
        BigDecimal budgetAmountNonCompliance = travelOrderBudgetCheckResult.getAmountNonCompliance();
        budgetCheckResult.setBudgetAmountCompliance(budgetAmountCompliance);
        budgetCheckResult.setBudgetAmountNonCompliance(budgetAmountNonCompliance);
        budgetCheckResult.setBudgetSettingAndUseList(travelOrderBudgetCheckResult.getBudgetSettingAndUseList());
        logger.info("[美食下单校验]，预算合规金额:{}, 预算超规金额:{}", budgetAmountCompliance, budgetAmountNonCompliance);
        return budgetCheckResult;
    }

    /**
     * 美食限额个人支付提示
     *
     * @param personalPay
     * @param companyPayPrice
     * @param totalPrice
     * @param couponAmount
     * @return
     */
    public LimitPayTipContract priceLimitPayTips(Boolean personalPay, BigDecimal companyPayPrice, BigDecimal totalPrice, BigDecimal couponAmount) {
        LimitPayTipContract limitPayTipContract = new LimitPayTipContract();
        limitPayTipContract.setTip_flag(false);
        // 未开启个人支付
        if (!personalPay) {
            return limitPayTipContract;
        }
        String title = CoreLanguage.TakeawayCheckServiceImpl_Value_OverTitle.getMessage();
        if (totalPrice.compareTo(companyPayPrice) > 0) {
            BigDecimal personalPayPrice = totalPrice.subtract(companyPayPrice);
            companyPayPrice = companyPayPrice.subtract(couponAmount).max(BigDecimal.ZERO);
            limitPayTipContract.setTip_flag(true);
            LimitPayTipContract.Tip tip = new LimitPayTipContract.Tip();
            tip.setTitle(title);
            tip.setContent(StrUtils.formatString(CoreLanguage.TakeawayCheckServiceImpl_Value_OverContent.getMessage(),
                    companyPayPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString(),
                    personalPayPrice.setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
            limitPayTipContract.setTip(tip);
        }
        return limitPayTipContract;
    }

    private BigDecimal getApplyEstimatedLeft(String applyId, ApplyTripInfo applyTripInfo, BigDecimal applyPriceLimit) {
        MeishiApplyManageReqDTO manageReqDTO = new MeishiApplyManageReqDTO();
        manageReqDTO.setDuringApplyIds(Collections.singletonList(applyId));
        BigDecimal applyTotalPrice = iDeliciousCheckV2Service.getOrderUsedTotalPrice(applyId, applyTripInfo.getId());
        logger.info("applyTotalPrice：{}, applyPriceLimit：{}", applyTotalPrice, applyPriceLimit);
        applyTotalPrice = ObjUtils.toBigDecimal(applyTotalPrice, BigDecimal.ZERO);
        applyPriceLimit = applyPriceLimit.subtract(applyTotalPrice).setScale(2, BigDecimal.ROUND_HALF_UP);

        //如果订单总金额超出初始化可用金额则重置成0
        if (applyPriceLimit.compareTo(BigDecimal.ZERO) < 0) {
            applyPriceLimit = BigDecimal.ZERO;
        }
        logger.info("applyPriceLimit left：{}", applyPriceLimit);
        return applyPriceLimit;
    }

    /** 批量查询用餐人美食权限
     */
    private EmployeeBaseRule queryEmployeeMeishiRuleList(String employeeId, String companyId, Integer subSceneId) {
        CheckUtils.create()
                .addCheckBlank(employeeId, CoreLanguage.Common_Exception_EmployeeIdsValueNotEmpty.getMessage())
                .addCheckBlank(companyId, CoreLanguage.Common_Exception_CompanyIdValueNotEmpty.getMessage())
                .check();
        List<EmployeeBaseRule> list = iBaseEmployeeRuleService.queryRuleListByCompanyIdAndEmployeeIds(companyId, Lists.newArrayList(employeeId), CategoryTypeEnum.MeiShi.getCode(), subSceneId);
        logger.info("queryEmployeeMeishiRuleList companyId={}, userId={}, subSceneId={}, list={}", companyId, employeeId, subSceneId, JSONObject.toJSON(list));
        return list.get(0);
    }

}