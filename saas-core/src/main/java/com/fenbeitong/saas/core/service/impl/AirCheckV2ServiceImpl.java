package com.fenbeitong.saas.core.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fenbeitong.config.api.config.SysConfigItemCompanyService;
import com.fenbeitong.fenbeimeta.api.model.enums.mongo.ObjectNameEnum;
import com.fenbeitong.fenbeimeta.api.model.vo.data.CustomizeVO;
import com.fenbeitong.fenbeimeta.api.service.data.IMongoDataService;
import com.fenbeitong.finhub.common.constant.BudgetCategoryTypeEnum;
import com.fenbeitong.finhub.common.constant.CategoryTypeEnum;
import com.fenbeitong.finhub.common.constant.CostAttributionScopeEnum;
import com.fenbeitong.finhub.common.constant.PayModelEnum;
import com.fenbeitong.finhub.common.utils.DistanceUtils;
import com.fenbeitong.finhub.common.utils.EventParams;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.Md5Utils;
import com.fenbeitong.harmony.city.contrail.dto.city.FullPathAreaDTO;
import com.fenbeitong.harmony.city.contrail.service.city.IAreaService;
import com.fenbeitong.saas.api.model.dto.apply.amount.AmountExceedCheckRes;
import com.fenbeitong.saas.api.model.dto.rule.QueryLimitPriceDTO;
import com.fenbeitong.saas.api.model.dto.template.rule.ControlOrderRuleDto;
import com.fenbeitong.saas.core.common.DynamicDataSourceDecision;
import com.fenbeitong.saas.core.common.base.SaasException;
import com.fenbeitong.saas.core.common.constant.AirRuleConstant;
import com.fenbeitong.saas.core.common.constant.CommonSwitchConstant;
import com.fenbeitong.saas.core.common.constant.GlobalResponseCode;
import com.fenbeitong.saas.core.common.constant.SaasApplyConstant;
import com.fenbeitong.saas.core.common.constant.SaasContentConstant;
import com.fenbeitong.saas.core.common.constant.SaasOrderThirdRuleConstant;
import com.fenbeitong.saas.core.contract.apply.ApplyOrderContract;
import com.fenbeitong.saas.core.contract.apply.ApplyThirdContract;
import com.fenbeitong.saas.core.contract.apply.ApplyTripValidRequestContract;
import com.fenbeitong.saas.core.contract.apply.CheckApplyEstimatedAmountAirReq;
import com.fenbeitong.saas.core.contract.biz.train.TrainInfo;
import com.fenbeitong.saas.core.contract.cost.CostCheckVO;
import com.fenbeitong.saas.core.contract.messagesettings.CostAttrAndBudgetConf;
import com.fenbeitong.saas.core.contract.messagesettings.ExceedPersonalPayCompanyConf;
import com.fenbeitong.saas.core.contract.order.check.*;
import com.fenbeitong.saas.core.contract.rule.AirRuleCheckResultDetailV2;
import com.fenbeitong.saas.core.contract.rule.DateInterval;
import com.fenbeitong.saas.core.contract.rule.RuleDetail;
import com.fenbeitong.saas.core.dao.fenbeitong.AirInterceptRecordMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.AirRuleMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.AirTimeRangeMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.IntlAirRuleMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.TrainRuleMapper;
import com.fenbeitong.saas.core.dao.saasplus.AirportAreaCombineMapper;
import com.fenbeitong.saas.core.language.CoreLanguage;
import com.fenbeitong.saas.core.model.enums.EmployeeStatusEnum;
import com.fenbeitong.saas.core.model.enums.OrderCategory;
import com.fenbeitong.saas.core.model.enums.apply.TotalEstimatedLimitType;
import com.fenbeitong.saas.core.model.enums.apply.TripType;
import com.fenbeitong.saas.core.model.enums.booking.BookingConfigEnum;
import com.fenbeitong.saas.core.model.enums.message.BizType;
import com.fenbeitong.saas.core.model.enums.reason.Air1ScreenCheckTitleMapChainOrderEnum;
import com.fenbeitong.saas.core.model.enums.rule.*;
import com.fenbeitong.saas.core.model.enums.rule.AirPortCityEnum;
import com.fenbeitong.saas.core.model.fenbeitong.AirInterceptRecord;
import com.fenbeitong.saas.core.model.fenbeitong.AirRule;
import com.fenbeitong.saas.core.model.fenbeitong.AirTimeRange;
import com.fenbeitong.saas.core.model.fenbeitong.AirTimeRangeExample;
import com.fenbeitong.saas.core.model.fenbeitong.IntlAirRule;
import com.fenbeitong.saas.core.model.fenbeitong.OrderCheckExt;
import com.fenbeitong.saas.core.model.fenbeitong.TrainRule;
import com.fenbeitong.saas.core.model.saas.ApplyOrder;
import com.fenbeitong.saas.core.model.saas.ApplyRuleSetting;
import com.fenbeitong.saas.core.model.saas.MessageSetup;
import com.fenbeitong.saas.core.model.saasplus.AirportAreaCombine;
import com.fenbeitong.saas.core.model.saasplus.AirportAreaCombineExample;
import com.fenbeitong.saas.core.service.IAirCheckV2Service;
import com.fenbeitong.saas.core.service.IApplyV5Service;
import com.fenbeitong.saas.core.service.IBaseOrganizationService;
import com.fenbeitong.saas.core.service.IMessageSetupService;
import com.fenbeitong.saas.core.service.IOrderCheckService;
import com.fenbeitong.saas.core.service.apply.TripEstimateService;
import com.fenbeitong.saas.core.service.apply.amount.ApplyAmountCheckResultService;
import com.fenbeitong.saas.core.service.apply.amount.ApplyAmountService;
import com.fenbeitong.saas.core.service.rule.ExceedConfigTypeEnum;
import com.fenbeitong.saas.core.service.rule.air.AirRuleCheckConstant;
import com.fenbeitong.saas.core.service.rule.air.AirRuleMsg;
import com.fenbeitong.saas.core.service.rule.air.AirRuleV2CheckService;
import com.fenbeitong.saas.core.service.rule.air.flight.AirFlightRuleCheckReq;
import com.fenbeitong.saas.core.service.rule.air.flight.AirFlightRuleCheckRes;
import com.fenbeitong.saas.core.service.rule.air.flight.book.AirBookCheckChainOrderEnum;
import com.fenbeitong.saas.core.service.rule.air.flight.city.Air1ScreenPageConstant;
import com.fenbeitong.saas.core.service.rule.air.flight.city.AirCityCheckChainOrderEnum;
import com.fenbeitong.saas.core.service.rule.air.flight.city.AirFlightCityRuleCheckChainHandler;
import com.fenbeitong.saas.core.service.rule.multi.ConsumeTemplateRuleService;
import com.fenbeitong.saas.core.service.uc.CompanyCalendarService;
import com.fenbeitong.saas.core.utils.air.AirRuleUtils;
import com.fenbeitong.saas.core.utils.cache.ICache;
import com.fenbeitong.saas.core.utils.tools.BigDecimalTool;
import com.fenbeitong.saas.core.utils.tools.ConvertUtils;
import com.fenbeitong.saas.core.utils.tools.DateTimeTool;
import com.fenbeitong.saas.core.utils.tools.HostPropertyConfigTool;
import com.fenbeitong.saas.core.utils.tools.HttpTool;
import com.fenbeitong.saas.core.utils.tools.IDTool;
import com.fenbeitong.saas.core.utils.tools.VersionTool;
import com.fenbeitong.saasplus.api.model.dto.finance.CustomDimension;
import com.fenbeitong.saasplus.api.model.dto.finance.PersonCost;
import com.fenbeitong.saasplus.api.model.po.fenbeitong.rulenew.*;
import com.fenbeitong.saasplus.api.service.custform.ICustomFormService;
import com.fenbeitong.saasplus.api.service.rule.IRuleV2Service;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyRuleDTO;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.fenbeitong.usercenter.api.model.enums.privilege.CodeTypeEnums;
import com.fenbeitong.usercenter.api.model.enums.privilege.CustomCodeType;
import com.fenbeitong.usercenter.api.model.po.rule.EmployeeAirRule;
import com.fenbeitong.usercenter.api.model.po.rule.EmployeeIntlAirRule;
import com.fenbeitong.usercenter.api.model.po.rule.EmployeeTrainRule;
import com.fenbeitong.usercenter.api.service.company.ICompanyRuleService;
import com.fenbeitong.usercenter.api.service.privilege.IPrivilegeService;
import com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeAirRuleService;
import com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeIntlAirRuleService;
import com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeTrainRuleService;
import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.net.HttpClientUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.DateUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.StrUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;


/**
 * Created by xjf on 18/6/21.
 */
@Slf4j
@Service
public class AirCheckV2ServiceImpl implements IAirCheckV2Service {

    private static final Logger logger = LoggerFactory.getLogger(AirCheckV2ServiceImpl.class);

    private static final String URL_GET_AIR_MIN_PRICE = HostPropertyConfigTool.HOST_INTL_AIR_BIZ + "/internal/order/saasMinPrice";

    private static final String URL_GET_AIR_CITY_INFO = HostPropertyConfigTool.HOST_HARMONY + "/city/areas/info";

    private static final String URL_GET_TRAIN_TIME = HostPropertyConfigTool.HOST_TRAIN_BIZ + "/internal/train/order/query_train_time";

    private static final String URL_GET_TRAIN_STATIONS = HostPropertyConfigTool.HOST_HARMONY + "/city/trainStations/getByAreaIds";

    @Autowired
    private ICache cache;
    @Autowired
    private IOrderCheckService iOrderCheckService;
    @Autowired
    private IBaseOrganizationService baseOrganizationService;
    @Autowired
    private ICompanyRuleService iCompanyRuleService;
    @Autowired
    private AirRuleMapper airRuleMapper;
    @Autowired
    private AirInterceptRecordMapper airInterceptRecordMapper;
    @Autowired
    private AirTimeRangeMapper airTimeRangeMapper;
    @Autowired
    private IBaseEmployeeAirRuleService iBaseEmployeeAirRuleService;
    @Autowired
    private IBaseEmployeeTrainRuleService iBaseEmployeeTrainRuleService;
    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyRuleSettingExtMapper applyRuleSettingExtMapper;
    @Autowired
    private IMessageSetupService iMessageSetupService;
    @Autowired
    private IPrivilegeService iPrivilegeService;
    @Autowired
    private SysConfigItemCompanyService sysConfigItemCompanyService;
    @Autowired
    private CurrencyCheckServiceImpl currencyCheckService;
    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyOrderMapper applyOrderMapper;
    @Autowired
    private ICustomFormService iCustomFormService;
    @Autowired
    private com.fenbeitong.saasplus.api.service.messageSetup.IMessageSetupService iMessageSetupRpcService;
    @Autowired
    private ApplyAmountService applyAmountService;
    @Autowired
    private ApplyAmountCheckResultService applyAmountCheckResultService;
    @Autowired
    private IRuleV2Service iRuleV2Service;
    @Autowired
    private TrainRuleMapper trainRuleMapper;
    @Autowired
    private CompanyCalendarService companyCalendarService;

    @Autowired
    private AirFlightCityRuleCheckChainHandler airFlightCityRuleCheckChainHandler;

    @Autowired
    private AirRuleV2CheckService airRuleV2CheckService;

    public static final String REMOVE_CITY_REGEX = "\\(.+?\\)";
    @Autowired
    private IMongoDataService iMongoDataService;

    @Autowired
    private ConsumeTemplateRuleService consumeTemplateRuleService;

    @Autowired
    private ApplyV5ServiceImpl applyV5Service;

    @Autowired
    private TripEstimateService tripEstimateService;

    @Autowired
    private AirportAreaCombineMapper airportAreaCombineMapper;

    @Autowired
    private IAreaService areaService;

    @Autowired
    private IApplyV5Service iApplyV5Service;

    @Autowired
    private IAirCheckV2Service iAirCheckV2Service;

    @Autowired
    private IntlAirRuleMapper intlAirRuleMapper;

    @Autowired
    private IBaseEmployeeIntlAirRuleService iBaseEmployeeIntlAirRuleService;

    /**
     * 飞机订单规则
     *
     * @param reqContract
     * @return
     * @throws SaasException
     */
    @Override
    public TravelOnBusiOrderCheckResContract airOrderCheck(AirOrderCheckReqV2Contract reqContract, String clientVersion, String token) throws SaasException {
        DynamicDataSourceDecision.determineDataSource(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        logger.info("国内机票订单校验参数：{}", JsonUtils.toJson(reqContract));
        // 返回结果
        TravelOnBusiOrderCheckResContract resContract = new TravelOnBusiOrderCheckResContract();

        //申请单不是空 但是 tripid是空 需要兜底补齐tripid  和 业务申请单id
        if(StringUtils.isNotEmpty(reqContract.getTravel_on_busi_common_req_contract().getApply_id()) && StringUtils.isEmpty(reqContract.getTravel_on_busi_common_req_contract().getApply_trip_id())){
            //拼装酒店可用申请单请求数据
            ApplyTripValidRequestContract applyTripValidRequestContract = new ApplyTripValidRequestContract();
            applyTripValidRequestContract.setIsFilter(true);
            applyTripValidRequestContract.setCategory(BizType.AirPlane.getCode());
            applyTripValidRequestContract.setStart_time(reqContract.getTravel_on_busi_parameter_req_contract().getStart_time());
            applyTripValidRequestContract.setEnd_time(reqContract.getTravel_on_busi_parameter_req_contract().getEnd_time());
            applyTripValidRequestContract.setArrival_city_id(reqContract.getTravel_on_busi_parameter_req_contract().getArrival_city_ids().get(0));
            applyTripValidRequestContract.setStart_city_id(reqContract.getTravel_on_busi_parameter_req_contract().getStart_city_ids().get(0));
            applyTripValidRequestContract.setVoyage_type(reqContract.getFlight_type());
            applyTripValidRequestContract.setCompany_id(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
            applyTripValidRequestContract.setUser_id(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id());
            applyTripValidRequestContract.setBack_time(reqContract.getTravel_on_busi_parameter_req_contract().getBack_start_time());
            applyTripValidRequestContract.setSource(1);
            applyTripValidRequestContract.setTrip_type(BizType.AirPlane.getCode());
            List<ApplyOrderContract>  applyOrderList = applyV5Service.queryApplyList(applyTripValidRequestContract);
            List<ApplyOrderContract> applyList = applyOrderList.stream().filter(a->a.getUsable().equals(true) && a.getId().equals(reqContract.getTravel_on_busi_common_req_contract().getApply_id())).collect(Collectors.toList());
            log.info("可用申请单：{}",JsonUtils.toJson(applyList));
            if(ObjUtils.isNotEmpty(applyList)){
                resContract.setTrip_id(applyList.get(0).getApply_trip_id());
                resContract.setBusiness_apply_id(applyOrderList.get(0).getId());
                reqContract.getTravel_on_busi_common_req_contract().setApply_trip_id(applyOrderList.get(0).getApply_trip_id());
            }
        }

        // 校验权限问题
        reqContract.setOriginalPrice(reqContract.getPrice());

        TravelOnBusiOrderRuleCheckResult ruleCheckResult = airOrderRuleCheckResult(reqContract, clientVersion, token);


        if (StringUtils.isNotEmpty(ruleCheckResult.getDuring_reapply_id())) {
            ApplyOrder applyOrder = applyOrderMapper.selectByPrimaryKey(ruleCheckResult.getDuring_reapply_id());
            ruleCheckResult.setDuring_reapply_info(JsonUtils.toJson(applyOrder));
        }
        if (CollectionUtil.isEmpty(ruleCheckResult.getAirLowestPriceMsgList())) {
            String clientStartTime = reqContract.getTravel_on_busi_parameter_req_contract().getStart_time();
            List<String> timeRangeList = Lists.newArrayList();
            //减几小时
            Calendar lowCalendar = Calendar.getInstance();
            lowCalendar.setTime(DateTimeTool.fromStringToDateTime(clientStartTime));
            lowCalendar.add(Calendar.HOUR_OF_DAY, -4);
            String lowDateStr = DateTimeTool.fromDateToShortTimeInfoString(lowCalendar.getTime());
            //加几小时
            Calendar highCalendar = Calendar.getInstance();
            highCalendar.setTime(DateTimeTool.fromStringToDateTime(clientStartTime));
            highCalendar.add(Calendar.HOUR_OF_DAY, 4);
            Long highTime = highCalendar.getTime().getTime();
            //获取当天的最晚时间
            Date startDateInfo = DateUtils.parse(clientStartTime);
            String endDayTime = DateTimeTool.fromDateToString(startDateInfo) + " 23:59:59";
            Long endTime = DateUtils.parse(endDayTime).getTime();
            if (highTime > endTime) {
                highTime = endTime;
            }
            String highDateStr = DateTimeTool.fromDateToShortTimeInfoString(new Date(highTime));
            timeRangeList.add(lowDateStr + "-" + highDateStr);

            //调取最低价接口判断是否超规
            try {
                AirInterceptRecordContract orderParameterJson = reqContract.getOrder_parameter_json();
                Map<String, String> header = Maps.newHashMap();
                header.put("Content-Type", "application/json;charset=utf-8");
                JSONObject param = new JSONObject();
                param.put("start_code", orderParameterJson.getStarting_code());
                param.put("end_code", orderParameterJson.getDestination_code());
                param.put("date", DateTimeTool.fromDateToString(DateUtils.parse(reqContract.getTravel_on_busi_parameter_req_contract().getStart_time())));
                param.put("select_price", reqContract.getOriginalPrice());
                param.put("time_solt", timeRangeList);
                param.put("company_id", reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
                // 经停航班限制
                param.put("flight_filter_type", 0);
                log.info("获取国内机票最低价接口参数:" + JsonUtils.toJson(param));
                String airData = HttpTool.post(URL_GET_AIR_MIN_PRICE, param, header);
                log.info("获取国内机票最低价接口返回结果:" + airData);
                Map<String, Object> jo = JSONObject.parseObject(airData, HashMap.class);
                Map<String, Object> airDataMap = (Map<String, Object>) jo.get("data");
                boolean minPrice = ObjUtils.toboolean(airDataMap.get("min_price"));
                Map<String, Object> minFlight = (Map<String, Object>) airDataMap.get("min_flight");
                // 组装最低价信息
                TravelOnBusiOrderCheckResContract.AirLowestPriceMsg airLowestPriceMsg1 = new TravelOnBusiOrderCheckResContract.AirLowestPriceMsg();
                airLowestPriceMsg1.setMinFlight(minFlight);
                airLowestPriceMsg1.setMinPriceFlag(minPrice);
                airLowestPriceMsg1.setLowPriceFlag(0);
                airLowestPriceMsg1.setLowPriceTime(4);
                airLowestPriceMsg1.setLowPriceUnit(AirLowPriceUnitEnum.HOUR.getKey());
                airLowestPriceMsg1.setFilterStopoverFlightFlag(0);
                List<TravelOnBusiOrderCheckResContract.AirLowestPriceMsg> airLowestPriceMsg = new ArrayList<>();
                airLowestPriceMsg.add(airLowestPriceMsg1);
                ruleCheckResult.setAirLowestPriceMsgList(airLowestPriceMsg);
            } catch (Exception e) {
                log.error(String.format("url:%s,异常", URL_GET_AIR_MIN_PRICE) + e.getMessage());
                throw new SaasException(GlobalResponseCode.PayOrderCancleOrderSystemError);
            }
        }
        //低价上浮描述
        if (StringUtils.isNotEmpty(ruleCheckResult.getAirRuleDesc())) {
            resContract.setAirRuleDesc(ruleCheckResult.getAirRuleDesc());
        }
        resContract.setAirLowestPriceMsgList(ruleCheckResult.getAirLowestPriceMsgList());
        resContract.setErr_code(ruleCheckResult.getErrCode());
        resContract.setErr_msg(ruleCheckResult.getErrMsg());
        resContract.setExceed_msg(ruleCheckResult.getExceed_msg());
        resContract.setCost_attribution_scope(ruleCheckResult.getCost_attribution_scope());
        resContract.setBudget_cost_attr_type(ruleCheckResult.getBudget_cost_attr_type());
        resContract.setPay_model(ruleCheckResult.getPayModel());
        resContract.setCompany_pay_price(ruleCheckResult.getCompanyPayPrice());
        resContract.setPersonal_pay_price(ruleCheckResult.getPersonalPayPrice());
        resContract.setReimbursable_price(ruleCheckResult.getReimbursablePrice());
        resContract.setGo_reimbursable_price(ruleCheckResult.getGoReimbursablePrice());
        resContract.setBack_reimbursable_price(ruleCheckResult.getBackReimbursablePrice());
        resContract.setUnreimbursable_price(ruleCheckResult.getUnreimbursablePrice());
        resContract.setAmount_compliance(ruleCheckResult.getAmountCompliance());
        resContract.setAmount_non_compliance(ruleCheckResult.getAmountNonCompliance());
        resContract.setCurrency_error_message(ruleCheckResult.getCurrencyErrorMessage());
        resContract.setTraveBooking(false);
        resContract.setIsShowRecommend(ruleCheckResult.getIsShowRecommend());
        resContract.setControl_dimension_desc(ruleCheckResult.getControlDimensionDesc());
        resContract.setFlightCheckInfoList(ruleCheckResult.getFlightCheckInfoList());
        Map<String, Object> minFlight = ruleCheckResult.getMin_flight();
        if (ObjUtils.isNotEmpty(minFlight)) {
            resContract.setMin_flight(minFlight);
        }
        Boolean exceedSubmit = ObjUtils.ifNull(reqContract.getTravel_on_busi_parameter_req_contract().getExceed_submit(), false);
        if (ruleCheckResult.getIs_exceed() || exceedSubmit) {
            resContract.setIs_exceed(true);
        }
        resContract.setDuring_reapply_id(ruleCheckResult.getDuring_reapply_id());
        resContract.setDuring_reapply_info(ruleCheckResult.getDuring_reapply_info());
        if (ruleCheckResult.getErrCode() == GlobalResponseCode.Success.getCode()) {
            BigDecimal costAmount = resContract.getCompany_pay_price();
            Integer costInfoType = reqContract.getCost_info_type();
            if (costInfoType != null && costInfoType == 2) {
                // 自定义字段转化
                List<CustomDimension> customDimensionList =
                    ConvertUtils.convertToCustomDimension(reqContract.getCustomer_field_setting_list());

                TempOrderCheckResContract costResult = iOrderCheckService.saveCostTicket(
                        reqContract.getTravel_on_busi_common_req_contract().getOrder_id(),
                        reqContract.getTravel_on_busi_common_req_contract().getCompany_id(),
                        reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(),
                        reqContract.getTravel_on_busi_common_req_contract().getApply_id(),
                        reqContract.getTravel_on_busi_common_req_contract().getApply_trip_id(),
                        BudgetCategoryTypeEnum.Air,
                        costAmount,
                        reqContract.getCost_info_ticket_list(),
                        reqContract.getCostInfoTicketListString(),
                        clientVersion, customDimensionList,reqContract.getTravel_on_busi_parameter_req_contract().getStart_time());
                logger.info("costResult:{}", JsonUtils.toJson(costResult));
                if (costResult.getErr_code() != GlobalResponseCode.Success.getCode()) {
                    resContract.setErr_code(costResult.getErr_code());
                    resContract.setErr_msg(costResult.getErr_msg());
                } else {
                    resContract.setCost_id_ticket_list(costResult.getCost_id_ticket_list());
                }
            } else {
                // 自定义字段转化
                List<CustomDimension> customDimensionList =
                    ConvertUtils.convertToCustomDimension(reqContract.getCustomer_field_setting_list());

                TempOrderCheckResContract costResult = iOrderCheckService.saveCost(
                        reqContract.getTravel_on_busi_common_req_contract().getOrder_id(),
                        reqContract.getTravel_on_busi_common_req_contract().getCompany_id(),
                        reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(),
                        reqContract.getTravel_on_busi_common_req_contract().getApply_id(),
                        reqContract.getTravel_on_busi_common_req_contract().getApply_trip_id(),
                        BudgetCategoryTypeEnum.Air,
                        costAmount,
                        reqContract.getCost_info(),
                        reqContract.getCostInfoString(),
                        clientVersion, customDimensionList,reqContract.getTravel_on_busi_parameter_req_contract().getStart_time());
                logger.info("else costResult:{}", JsonUtils.toJson(costResult));
                if (costResult.getErr_code() != GlobalResponseCode.Success.getCode()) {
                    resContract.setErr_code(costResult.getErr_code());
                    resContract.setErr_msg(costResult.getErr_msg());
                } else {
                    resContract.setCost_id(costResult.getCost_id());
                }
            }
        }

        // errMsgInfo
        AirRuleUtils.setErrMsgInfo(resContract, ruleCheckResult);

        // 错误信息类型
        TravelOnBusiOrderCheckResContract travelOnBusiOrderCheckResContract = iOrderCheckService.travelOnBusiOrderCheckResContractCommon(resContract, ruleCheckResult);

        log.info("PersonnelInfoList:",JsonUtils.toJson(ruleCheckResult.getPersonnelInfoList()));

        //添加订单元数据  一人多规则 需要存储每个人的信息
        List<CustomizeVO> customizeVOs = Lists.newArrayList();
        CustomizeVO customizeVO = new CustomizeVO();
        customizeVO.setDataId("saasControlDimension" + reqContract.getTravel_on_busi_common_req_contract().getOrder_id());
        customizeVO.setDataContent(JsonUtils.toJson(ruleCheckResult.getPersonnelInfoList()));
        customizeVOs.add(customizeVO);
        iMongoDataService.batchSave(customizeVOs, ObjectNameEnum.ORDER_INNER.getKey());

        //处理保险个人付相关信息
        Boolean isInsurancePersonPay = reqContract.getTravel_on_busi_common_req_contract().getInsurance_personal_price().compareTo(BigDecimal.ZERO) > 0;

        //保险个人付 需要处理返回的信息 增加保险相关信息
        if(isInsurancePersonPay){
            //判断是否超规 如果超规 在超规信息里增加保险个人付的提示信息
            if(travelOnBusiOrderCheckResContract.getIs_exceed()){
                if(travelOnBusiOrderCheckResContract.getErr_code()  == GlobalResponseCode.CurrencyFrame.getCode()){
                    if (!Objects.equals(travelOnBusiOrderCheckResContract.getCurrency_error_message().getCode(), GlobalResponseCode.CheckResults.getCode()) ||
                            !Objects.equals(travelOnBusiOrderCheckResContract.getCurrency_error_message().getCode(), GlobalResponseCode.CityRuleForbidden.getCode())) { // 禁止下单
                        CurrencyMsg currencyMsg = new CurrencyMsg();
                        currencyMsg.setCode(GlobalResponseCode.OrderCheckInsurancePrice.getCode());
                        currencyMsg.setErrMsg(StrUtils.formatString(GlobalResponseCode.OrderCheckInsurancePrice.getMsg(),reqContract.getTravel_on_busi_common_req_contract().getInsurance_personal_price()));
                        travelOnBusiOrderCheckResContract.getCurrency_error_message().getCurrencyEmployeeMsgList().get(0).getErrMsgList().add(currencyMsg);
                    }
                }
            }
        }
        //个人支付金额增加保险个人付的金额
        travelOnBusiOrderCheckResContract.setPersonal_pay_price(travelOnBusiOrderCheckResContract.getPersonal_pay_price().add(reqContract.getTravel_on_busi_common_req_contract().getInsurance_personal_price()));

        return travelOnBusiOrderCheckResContract;
    }


    /**
     * 获取机票差标
     * @param queryLimitPriceDTO
     * @return
     * @throws SaasException
     */
    @Override
    public TravelOnBusiOrderCheckReqV2Contract.FrequentInfo queryAirLimitPrice(QueryLimitPriceDTO queryLimitPriceDTO) throws SaasException{
        TravelOnBusiOrderCheckReqV2Contract.FrequentInfo frequentInfo = new TravelOnBusiOrderCheckReqV2Contract.FrequentInfo();
        String companyId = queryLimitPriceDTO.getCompanyId();
        String employeeId = queryLimitPriceDTO.getEmployeeId();
        frequentInfo.setId(employeeId);
        frequentInfo.setLimit_price(BigDecimal.ZERO);
        // 员工飞机权限
        EmployeeAirRule employeeAirRule = iBaseEmployeeAirRuleService.queryEmployeeAirRuleByPrimaryKey(employeeId,companyId);
        log.info("员工飞机权限:{}", JsonUtils.toJson(employeeAirRule));

        String ruleId;
        //如果是多规则 则重新取规则id
        if(employeeAirRule.getMulti_rule_switch()){
            ControlOrderRuleDto controlOrderRuleDto = consumeTemplateRuleService.getConsumeRule("", employeeAirRule.getTemplate_id(),companyId,null, com.fenbeitong.saas.core.model.enums.apply.BizType.Air, "5.2.51");
            employeeAirRule.setAir_rule_flag(controlOrderRuleDto.getIsEnable());
            ruleId = controlOrderRuleDto.getRuleId();
            log.info("用户为多规则:{}",String.format("管控维度-%s,适用范围-%s",controlOrderRuleDto.getName(),controlOrderRuleDto.getRange()));
        }else{
            ruleId = ObjUtils.ifNull(employeeAirRule.getManual_air_rule_id(), employeeAirRule.getDefault_air_rule_id());
        }
        log.info("ruleId:{}",ruleId);
        //校验规则
        AirManagerSetting airManagerSetting = iMessageSetupRpcService.queryAirRuleExceedConfigMessage(companyId);
        Boolean isOpenExceedConfig = airManagerSetting.getIsOpenExceedConfig();
        Integer exceedConfigLever = airManagerSetting.getExceedConfigLever();
        log.info("机票超规个人付:{}, exceedConfigLever:{}", isOpenExceedConfig, exceedConfigLever);

        if (employeeAirRule.getAir_rule_flag()) {
            //获取机票价格
            if (isOpenExceedConfig && Objects.equals(exceedConfigLever, 2)) { // 机票超规个人付开始
                AirRuleV2 airRuleV2 = iRuleV2Service.getAirRuleV2ById(ruleId, companyId);
                log.info("airRuleV2:{}", JsonUtils.toJson(airRuleV2));
                if(airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup().getAirPriceFlag()){
                        frequentInfo.setLimit_price(airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup().getAirUnitPrice());
                }

            }else{
                AirRule airRule = airRuleMapper.selectByPrimaryKey(ruleId);
                log.info("airRule:{}", JsonUtils.toJson(airRule));
                if(airRule.getAirPriceFlag()){
                    frequentInfo.setLimit_price(airRule.getAirUnitPrice());
                }
            }
        }

        return frequentInfo;
    }

    /**
     * 获取国际机票差标
     * @param queryLimitPriceDTO
     * @return
     * @throws SaasException
     */
    @Override
    public TravelOnBusiOrderCheckReqV2Contract.FrequentInfo queryIntlAirLimitPrice(QueryLimitPriceDTO queryLimitPriceDTO) throws SaasException{
        TravelOnBusiOrderCheckReqV2Contract.FrequentInfo frequentInfo = new TravelOnBusiOrderCheckReqV2Contract.FrequentInfo();
        String companyId = queryLimitPriceDTO.getCompanyId();
        String employeeId = queryLimitPriceDTO.getEmployeeId();
        frequentInfo.setId(employeeId);
        frequentInfo.setLimit_price(BigDecimal.ZERO);

        // 员工飞机权限
        EmployeeIntlAirRule employeeIntlAirRule = iBaseEmployeeIntlAirRuleService.queryEmployeeIntlAirRuleByPrimaryKey(employeeId, companyId);
        logger.info("员工飞机权限, employeeIntlAirRule={}", JsonUtils.toJson(employeeIntlAirRule));

        //校验规则
        if (employeeIntlAirRule.getAir_rule_flag()) {
            //消费规则校验
            String ruleId = ObjUtils.ifNull(employeeIntlAirRule.getManual_air_rule_id(), employeeIntlAirRule.getDefault_air_rule_id());
            IntlAirRule intlAirRule = intlAirRuleMapper.selectByPrimaryKey(ruleId);
            log.info("intlAirRule:{}", JsonUtils.toJson(intlAirRule));
            if(intlAirRule.getAirPriceFlag()){
                frequentInfo.setLimit_price(intlAirRule.getAirUnitPrice());
            }
        }

        return frequentInfo;
    }

    /**
     * 获取火车票差标
     * @param queryLimitPriceDTO
     * @return
     * @throws SaasException
     */
    @Override
    public TravelOnBusiOrderCheckReqV2Contract.FrequentInfo queryTrainLimitPrice(QueryLimitPriceDTO queryLimitPriceDTO) throws SaasException{

        TravelOnBusiOrderCheckReqV2Contract.FrequentInfo frequentInfo = new TravelOnBusiOrderCheckReqV2Contract.FrequentInfo();
        String companyId = queryLimitPriceDTO.getCompanyId();
        String employeeId = queryLimitPriceDTO.getEmployeeId();
        frequentInfo.setId(employeeId);
        frequentInfo.setLimit_price(BigDecimal.ZERO);
        // 员工火车权限
        EmployeeTrainRule employeeTrainRule = iBaseEmployeeTrainRuleService.queryEmployeeTrainRuleByPrimaryKey(employeeId, companyId);
        logger.info("员工火车权限, employeeTrainRule:{}", JsonUtils.toJson(employeeTrainRule));
        Boolean isOpenExceedConfig = iMessageSetupRpcService.queryTrainRuleExceedConfigMessage(companyId);
        logger.info("火车超规个人付:{}", isOpenExceedConfig);

        String ruleId = "";
        //如果是多规则 则重新取规则id
        if(employeeTrainRule.getMulti_rule_switch()){
            ControlOrderRuleDto controlOrderRuleDto = consumeTemplateRuleService.getConsumeRule(null, employeeTrainRule.getTemplate_id(),companyId,null, com.fenbeitong.saas.core.model.enums.apply.BizType.Train, "5.2.51");
            employeeTrainRule.setTrain_rule_flag(controlOrderRuleDto.getIsEnable());
            ruleId =controlOrderRuleDto.getRuleId();
            log.info("用户为多规则:{}",String.format("管控维度-%s,适用范围-%s",controlOrderRuleDto.getName(),controlOrderRuleDto.getRange()));
        }else{
            //获取当前预定人规则id
            ruleId = ObjUtils.ifNull(employeeTrainRule.getManual_train_rule_id(), employeeTrainRule.getDefault_train_rule_id());
        }
        log.info("ruleId:{}",ruleId);
        //规则校验
        if (employeeTrainRule.getTrain_rule_flag()) {
            //获取车次信息
            TrainInfo trainInfo = iAirCheckV2Service.getTrainInfo(queryLimitPriceDTO.getToken(),queryLimitPriceDTO.getGoStartTime(),Lists.newArrayList(queryLimitPriceDTO.getArrivalCityId()), Lists.newArrayList(queryLimitPriceDTO.getStartCityId()));
            log.info("trainInfo:",JsonUtils.toJson(trainInfo));
            //火车超规个人付
            if (isOpenExceedConfig) {
                TrainRuleV2 trainRuleV2 = iRuleV2Service.getTrainRuleV2ById(ruleId, companyId);
                logger.info("trainRuleV2:{}", JsonUtils.toJson(trainRuleV2));
                //配置了价格限制：取消费规则的价格
                if(trainRuleV2.getTrainBookCondition().getPriceLimitFlag()){
                    frequentInfo.setLimit_price(trainRuleV2.getTrainBookCondition().getPriceLimit());
                }else{
                    if (trainInfo.getQurydata()) { //查询是有效车次
                        if (trainInfo.getMaxSecondSeatPrice() != null && !trainInfo.getMaxSecondSeatPrice().equals(BigDecimal.ZERO)) {
                            frequentInfo.setLimit_price(trainInfo.getMaxSecondSeatPrice());
                            log.info("没有价格限制，取二等座最高价:{}",trainInfo.getMaxSecondSeatPrice());
                        } else {
                            if (trainInfo.getMaxHardBerthSeatPrice() != null && !trainInfo.getMaxHardBerthSeatPrice().equals(BigDecimal.ZERO)) {
                                frequentInfo.setLimit_price(trainInfo.getMaxHardBerthSeatPrice());
                                log.info("没有价格限制，取硬卧最高价",trainInfo.getMaxHardBerthSeatPrice());
                            }
                        }
                    }
                }
            }else{
                // 超规个人付之前旧版原有规则逻辑
                TrainRule trainRule = trainRuleMapper.selectByPrimaryKey(ruleId);
                logger.info("规则校验, trainRule:{}", JsonUtils.toJson(trainRule));
                if (trainRule.getPriceLimit() != null) {
                    frequentInfo.setLimit_price(trainRule.getPriceLimit());
                }else{
                    if (trainInfo.getQurydata()) { //查询是有效车次
                        if (trainInfo.getMaxSecondSeatPrice() != null && !trainInfo.getMaxSecondSeatPrice().equals(BigDecimal.ZERO)) {
                            frequentInfo.setLimit_price(trainInfo.getMaxSecondSeatPrice());
                            log.info("没有价格限制，取二等座最高价:{}",trainInfo.getMaxSecondSeatPrice());
                        } else {
                            if (trainInfo.getMaxHardBerthSeatPrice() != null && !trainInfo.getMaxHardBerthSeatPrice().equals(BigDecimal.ZERO)) {
                                frequentInfo.setLimit_price(trainInfo.getMaxHardBerthSeatPrice());
                                log.info("没有价格限制，取硬卧最高价",trainInfo.getMaxHardBerthSeatPrice());
                            }
                        }
                    }
                }
            }
        }else{
            frequentInfo.setLimit_price(new BigDecimal(-1));
            log.info("没有规则，不限制差标");
        }
        return frequentInfo;
    }

    @Override
    public TravelOnBusiOrderCheckResContract multiAirOrderCheck(AirOrderCheckReqV2Contract reqContract, String clientVersion, String token) throws SaasException {
        //老代码 需要问问海超这块时兼容什么内容的
        DynamicDataSourceDecision.determineDataSource(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        // 返回结果
        TravelOnBusiOrderCheckResContract resContract = new TravelOnBusiOrderCheckResContract();
        // 校验权限问题
        reqContract.setOriginalPrice(reqContract.getPrice());
        // 执行校验!!!
        TravelOnBusiOrderRuleCheckResult ruleCheckResult = multiAirOrderRuleCheckResult(reqContract, clientVersion, token);
        logger.info("机票订单校验结果：ruleCheckResult: {}", JsonUtils.toJson(ruleCheckResult));

        if (ruleCheckResult.getAirLowestPriceMsgList() == null || ruleCheckResult.getAirLowestPriceMsgList().isEmpty()){
            String clientStartTime = reqContract.getTravel_on_busi_parameter_req_contract().getStart_time();
            List<String> timeRangeList = Lists.newArrayList();
            //减几小时
            Calendar lowCalendar = Calendar.getInstance();
            lowCalendar.setTime(DateTimeTool.fromStringToDateTime(clientStartTime));
            lowCalendar.add(Calendar.HOUR_OF_DAY, - 4);
            String lowDateStr = DateTimeTool.fromDateToShortTimeInfoString(lowCalendar.getTime());
            //加几小时
            Calendar highCalendar = Calendar.getInstance();
            highCalendar.setTime(DateTimeTool.fromStringToDateTime(clientStartTime));
            highCalendar.add(Calendar.HOUR_OF_DAY, 4);
            Long highTime = highCalendar.getTime().getTime();
            //获取当天的最晚时间
            Date startDateInfo = DateUtils.parse(clientStartTime);
            String endDayTime = DateTimeTool.fromDateToString(startDateInfo) + " 23:59:59";
            Long endTime = DateUtils.parse(endDayTime).getTime();
            if (highTime > endTime) {
                highTime = endTime;
            }
            String highDateStr = DateTimeTool.fromDateToShortTimeInfoString(new Date(highTime));
            timeRangeList.add(lowDateStr + "-" + highDateStr);

            //调取最低价接口判断是否超规
            try {
                AirInterceptRecordContract orderParameterJson = reqContract.getOrder_parameter_json();
                Map<String, String> header = Maps.newHashMap();
                header.put("Content-Type", "application/json;charset=utf-8");
                JSONObject param = new JSONObject();
                param.put("start_code", orderParameterJson.getStarting_code());
                param.put("end_code", orderParameterJson.getDestination_code());
                param.put("date", DateTimeTool.fromDateToString(DateUtils.parse(reqContract.getTravel_on_busi_parameter_req_contract().getStart_time())));
                param.put("select_price", reqContract.getOriginalPrice());
                param.put("time_solt", timeRangeList);
                param.put("company_id", reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
                // 经停航班限制
                param.put("flight_filter_type", 0);
                log.info("获取国内机票最低价接口参数:" + JsonUtils.toJson(param));
                String airData = HttpTool.post(URL_GET_AIR_MIN_PRICE, param, header);
                log.info("获取国内机票最低价接口返回结果:" + airData);
                Map<String, Object> jo = JSONObject.parseObject(airData, HashMap.class);
                Map<String, Object> airDataMap = (Map<String, Object>) jo.get("data");
                boolean minPrice = ObjUtils.toboolean(airDataMap.get("min_price"));
                Map<String, Object> minFlight = (Map<String, Object>) airDataMap.get("min_flight");
                // 组装最低价信息
                TravelOnBusiOrderCheckResContract.AirLowestPriceMsg airLowestPriceMsg1 = new TravelOnBusiOrderCheckResContract.AirLowestPriceMsg();
                airLowestPriceMsg1.setMinFlight(minFlight);
                airLowestPriceMsg1.setMinPriceFlag(minPrice);
                airLowestPriceMsg1.setLowPriceFlag(0);
                airLowestPriceMsg1.setLowPriceTime(4);
                airLowestPriceMsg1.setLowPriceUnit(AirLowPriceUnitEnum.HOUR.getKey());
                airLowestPriceMsg1.setFilterStopoverFlightFlag(0);
                List<TravelOnBusiOrderCheckResContract.AirLowestPriceMsg> airLowestPriceMsg = new ArrayList<>();
                airLowestPriceMsg.add(airLowestPriceMsg1);
                ruleCheckResult.setAirLowestPriceMsgList(airLowestPriceMsg);
            }catch (Exception e){
                log.error(String.format("url:%s,异常", URL_GET_AIR_MIN_PRICE) + e.getMessage());
                throw new SaasException(GlobalResponseCode.PayOrderCancleOrderSystemError);
            }
        }

        // 在下单取严
        List<TravelOnBusiOrderCheckResContract.AirLowestPriceMsg> airLowestPriceMsgLists = ruleCheckResult.getAirLowestPriceMsgList();
        Map<Integer, List<TravelOnBusiOrderCheckResContract.AirLowestPriceMsg>> airLowestPriceList = airLowestPriceMsgLists.stream().collect(Collectors.groupingBy(TravelOnBusiOrderCheckResContract.AirLowestPriceMsg::getLowPriceTime));
        //获取最大时间
        Set<Integer> set = airLowestPriceList.keySet();
        Object[] obj = set.toArray();
        Arrays.sort(obj);
        Integer maxKey = Integer.parseInt(obj[obj.length - 1].toString());
        List<TravelOnBusiOrderCheckResContract.AirLowestPriceMsg> airLowestPriceMsgs = airLowestPriceList.get(maxKey);
        Map<Integer, List<TravelOnBusiOrderCheckResContract.AirLowestPriceMsg>> airs = airLowestPriceMsgs.stream().collect(Collectors.groupingBy(TravelOnBusiOrderCheckResContract.AirLowestPriceMsg::getFilterStopoverFlightFlag));
        List<TravelOnBusiOrderCheckResContract.AirLowestPriceMsg> airLowestPriceMsgListEnd = new ArrayList<>();
        if (airs.get(0) != null){
            airLowestPriceMsgListEnd.add(airs.get(0).get(0));
        }else {
            airLowestPriceMsgListEnd.add(airs.get(1).get(0));
        }
        resContract.setAirLowestPriceMsgList(airLowestPriceMsgListEnd);
        resContract.setAirRuleDesc(ruleCheckResult.getAirRuleDesc());
        resContract.setErr_code(ruleCheckResult.getErrCode());
        resContract.setErr_msg(ruleCheckResult.getErrMsg());
        resContract.setExceed_msg(ruleCheckResult.getExceed_msg());
        resContract.setPersonal_pay(ruleCheckResult.getPersonalPay());
        resContract.setLimit_price(ruleCheckResult.getLimitPrice());
        resContract.setLimit_type(ruleCheckResult.getLimitType());
        resContract.setIs_price_exceed(ruleCheckResult.getIsPriceExceed());
        resContract.setIs_estimated_amt_exceed(ruleCheckResult.getIs_estimated_amt_exceed());
        resContract.setMin_price_limit(ruleCheckResult.getMinPriceLimit());
        resContract.setPrice_exceed_sum(ruleCheckResult.getPriceExceedSum());
        resContract.setCost_attribution_scope(ruleCheckResult.getCost_attribution_scope());
        resContract.setBudget_cost_attr_type(ruleCheckResult.getBudget_cost_attr_type());
        resContract.setPay_model(ruleCheckResult.getPayModel());
        resContract.setCompany_pay_price(ruleCheckResult.getCompanyPayPrice());
        resContract.setPersonal_pay_price(ruleCheckResult.getPersonalPayPrice());
        resContract.setReimbursable_price(ruleCheckResult.getReimbursablePrice());
        resContract.setUnreimbursable_price(ruleCheckResult.getUnreimbursablePrice());
        resContract.setAmount_compliance(ruleCheckResult.getAmountCompliance());
        resContract.setAmount_non_compliance(ruleCheckResult.getAmountNonCompliance());
        resContract.setCoupon_used_amount(ruleCheckResult.getCouponUsedAmount());
        resContract.setCurrency_error_message(ruleCheckResult.getCurrencyErrorMessage());
        resContract.setTraveBooking(ruleCheckResult.getIsTraveBooking());
        resContract.setMin_flight(ruleCheckResult.getMin_flight());
        resContract.setIsShowRecommend(ruleCheckResult.getIsShowRecommend());
        if (ruleCheckResult.getIs_exceed() || reqContract.getTravel_on_busi_parameter_req_contract().getExceed_submit()) {
            resContract.setIs_exceed(true);
        }
        resContract.setDuring_reapply_id(ruleCheckResult.getDuring_reapply_id());
        // 校验成功，保存费用信息
        if (ruleCheckResult.getErrCode() == GlobalResponseCode.Success.getCode()) {
            // 机票订单总金额为去掉优惠券后的金额+保险金额
            BigDecimal costAmount = reqContract.getTravel_on_busi_common_req_contract().getTotal_price()
                    .add(ObjUtils.toBigDecimal(reqContract.getTravel_on_busi_common_req_contract().getInsurance_price(), BigDecimal.ZERO));
            Integer costInfoType = reqContract.getCost_info_type();

            // 组装每个出行人的费用信息
            List<PersonCost> personCostList = ruleCheckResult.getCompanyRuleSummary().getEmployeeRuleSummaryList()
                    .stream().filter(a->a.getIsEmployee().equals(true))
                    .map(rule -> {
                        PersonCost personCost = new PersonCost();
                        personCost.setEmployeeId(rule.getEmployeeId());
                        personCost.setAmount(rule.getCompanyPayPrice().subtract(rule.getCouponUsedAmount()));
                        personCost.setApplyId(rule.getApplyId());
                        personCost.setApplyTripId(rule.getTripId());
                        return personCost;
                    }).collect(Collectors.toList());
            if (costInfoType != null && costInfoType == 2) {
                // 自定义字段转化
                List<CustomDimension> customDimensionList =
                    ConvertUtils.convertToCustomDimension(reqContract.getCustomer_field_setting_list());

                TempOrderCheckResContract costResult = iOrderCheckService.saveCostTicket(
                        reqContract.getTravel_on_busi_common_req_contract().getOrder_id(),
                        reqContract.getTravel_on_busi_common_req_contract().getCompany_id(),
                        reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(),
                        reqContract.getTravel_on_busi_common_req_contract().getApply_id(),
                        reqContract.getTravel_on_busi_common_req_contract().getApply_trip_id(),
                        BudgetCategoryTypeEnum.Air,
                        costAmount,
                        reqContract.getCost_info_ticket_list(),
                        reqContract.getCostInfoTicketListString(),
                        clientVersion,
                        personCostList,
                        2, customDimensionList,reqContract.getTravel_on_busi_parameter_req_contract().getStart_time());
                logger.info("costResult:{}", JsonUtils.toJson(costResult));
                if (costResult.getErr_code() != GlobalResponseCode.Success.getCode()) {
                    resContract.setErr_code(costResult.getErr_code());
                    resContract.setErr_msg(costResult.getErr_msg());
                } else {
                    resContract.setCost_id_ticket_list(costResult.getCost_id_ticket_list());
                }
            } else {
                // 自定义字段转化
                List<CustomDimension> customDimensionList =
                    ConvertUtils.convertToCustomDimension(reqContract.getCustomer_field_setting_list());

                TempOrderCheckResContract costResult = iOrderCheckService.saveCost(
                        reqContract.getTravel_on_busi_common_req_contract().getOrder_id(),
                        reqContract.getTravel_on_busi_common_req_contract().getCompany_id(),
                        reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(),
                        reqContract.getTravel_on_busi_common_req_contract().getApply_id(),
                        reqContract.getTravel_on_busi_common_req_contract().getApply_trip_id(),
                        BudgetCategoryTypeEnum.Air,
                        costAmount,
                        reqContract.getCost_info(),
                        reqContract.getCostInfoString(),
                        clientVersion,
                        null,
                        personCostList,
                        2, customDimensionList,reqContract.getTravel_on_busi_parameter_req_contract().getStart_time());
                logger.info("else costResult:{}", JsonUtils.toJson(costResult));
                if (costResult.getErr_code() != GlobalResponseCode.Success.getCode()) {
                    resContract.setErr_code(costResult.getErr_code());
                    resContract.setErr_msg(costResult.getErr_msg());
                } else {
                    resContract.setCost_id(costResult.getCost_id());
                }
            }
        }

        AirRuleUtils.setErrMsgInfo(resContract, ruleCheckResult);

        log.info("PersonnelInfoList:{}",JsonUtils.toJson(ruleCheckResult.getPersonnelInfoList()));

        //添加订单元数据  一人多规则 需要存储每个人的信息
        List<CustomizeVO> customizeVOs = Lists.newArrayList();
        CustomizeVO customizeVO = new CustomizeVO();
        customizeVO.setDataId("saasControlDimension" + reqContract.getTravel_on_busi_common_req_contract().getOrder_id());
        customizeVO.setDataContent(JsonUtils.toJson(ruleCheckResult.getPersonnelInfoList()));
        customizeVOs.add(customizeVO);
        iMongoDataService.batchSave(customizeVOs, ObjectNameEnum.ORDER_INNER.getKey());


        return resContract;
    }

    private TravelOnBusiOrderRuleCheckResult multiAirOrderRuleCheckResult(AirOrderCheckReqV2Contract reqContract,String clientVersion,String token) throws SaasException {
        TravelOnBusiOrderRuleCheckResult checkResult = new TravelOnBusiOrderRuleCheckResult();
        //公司id
        String companyId = reqContract.getTravel_on_busi_common_req_contract().getCompany_id();
        // 预订人状态
        EmployeeContract orderEmployee = baseOrganizationService.getEmployee(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(), companyId);
        log.info("[预订人状态]:{},员工编号：{},公司ID：{}", JsonUtils.toJson(orderEmployee),reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(),companyId);
        if (orderEmployee == null || orderEmployee.getStatus() != EmployeeStatusEnum.ACTIVE.getValue()) {
            //设置状态码：100001 您的状态未启用,不允许进行消费交易
            checkResult.setResCode(GlobalResponseCode.OrderCheckEmployeeNotActive);
            return checkResult;
        }
        // 企业权限
        CompanyRuleDTO companyRule = iCompanyRuleService.queryByCompanyId(companyId);
        log.info("[企业权限]:{},公司ID：{}", JsonUtils.toJson(companyRule), companyId);
        if (companyRule == null || companyRule.getAirRule() != 1) {
            //设置状态码：100002 您所在的公司不允许订购机票,具体情况请联系管理员
            checkResult.setResCode(GlobalResponseCode.OrderCheckCompanyNoAuth.getCode(), CoreLanguage.Common_Exception_AirCompanyNoAuthMsg.getMessage());
            return checkResult;
        }
        // 校验往返机票是否开启
        int companyIntConfig = 0;
        try {
            companyIntConfig = sysConfigItemCompanyService.getCompanyIntConfig(reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), "permissions_air", "whether_go_and_back");
            logger.info("companyIntConfig:{}",companyIntConfig);
        } catch (Exception ex) {
            logger.warn("获取国内机票往返异常：" + ex.getMessage());
        }
        if (ObjectUtil.isNull(reqContract.getFlight_type())) {
            reqContract.setFlight_type(TripType.oneWay.getValue());
        }
        if (reqContract.getFlight_type() == TripType.goAndBack.getValue()) {
            if (companyIntConfig != 1) {
                checkResult.setResCode(GlobalResponseCode.OrderCheckCompanyNoAuth.getCode(), "公司未开启机票往返");
                return checkResult;
            }
        }
        //订单总价
        BigDecimal totalPrice = reqContract.getTravel_on_busi_common_req_contract().getTotal_price();

        BigDecimal insurancePrice = ObjUtils.toBigDecimal(
            reqContract.getTravel_on_busi_common_req_contract().getInsurance_price(), BigDecimal.ZERO);

        //获取乘机人信息
        List<TravelOnBusiOrderCheckReqV2Contract.FrequentInfo> frequentInfoList = reqContract.getTravel_on_busi_common_req_contract().getFrequent_info();



        //初始化权限信息（通用）
        CompanyRuleSummary companyRuleSummary = currencyCheckService.assembleEmployeeRuleSummary(companyId, BizType.AirPlane, frequentInfoList, reqContract.getCost_info(), clientVersion);

        // 检查费用归属灰度开关 进行灰度校验
        Integer costAttributionNewSwitch = iMessageSetupRpcService.queryCostAttributionNewSwitch(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        log.info("costAttributionNewSwitch:{}",costAttributionNewSwitch);
        if (CommonSwitchConstant.OPEN.equals(costAttributionNewSwitch)){
            CostCheckVO costCheckVO = CostCheckVO.from(reqContract);
            costCheckVO.setApplyId(reqContract.getTravel_on_busi_common_req_contract().getApply_id());
            currencyCheckService.newCheckCostAttribution(costCheckVO);
        }else{
            Integer costInfoType = reqContract.getCost_info_type();
            if (costInfoType != null && costInfoType == 2) {
                TravelOnBusiOrderRuleCheckResult checkCompanyCostAttributionResult = iOrderCheckService.checkCostTicketInfo(reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), reqContract.getCost_info_ticket_list(), companyRuleSummary.getCostAttrAndBudgetConf(), clientVersion);
                logger.info("checkCompanyCostAttributionResult:{}", JsonUtils.toJson(checkCompanyCostAttributionResult));
                if (GlobalResponseCode.Success.getCode() != checkCompanyCostAttributionResult.getErrCode()) {
                    return checkCompanyCostAttributionResult;
                }
            } else {
                TravelOnBusiOrderRuleCheckResult checkCompanyCostAttributionResult = iOrderCheckService.checkCostInfo(reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), reqContract.getCost_info(), companyRuleSummary.getCostAttrAndBudgetConf(), clientVersion);
                logger.info("else checkCompanyCostAttributionResult:{}", JsonUtils.toJson(checkCompanyCostAttributionResult));
                if (GlobalResponseCode.Success.getCode() != checkCompanyCostAttributionResult.getErrCode()) {
                    return checkCompanyCostAttributionResult;
                }
            }
        }
        log.info("初始化场景相关信息前companyRuleSummary:{}", JsonUtils.toJson(companyRuleSummary));
        //初始化场景相关信息
        currencyCheckService.assemnleSceneSummary(reqContract,companyRuleSummary,OrderCategory.Airplane,clientVersion,TravelType.Air);

        //设置订单元数据存储信息
        checkResult.setPersonnelInfoList(companyRuleSummary.getPersonnelInfoList());
        //默认返回成功状态码
        checkResult.setResCode(GlobalResponseCode.Success);
        // 出行人模式
        checkResult.setIsTraveBooking(true);
        //默认不需要个人支付
        checkResult.setPersonalPay(false);
        //默认没有金额超规
        checkResult.setIsPriceExceed(false);
        //支付模式
        checkResult.setPayModel(PayModelEnum.COMPANY_PAY.getCode());
        //设置优惠卷使用金额
        checkResult.setCouponUsedAmount(currencyCheckService.getAvgCouponAmountOrValidateAmountOrUseCouponAmount(reqContract,companyRuleSummary.getCouponExceedPriceSetting(),2));
        //设置默认企业支付金额
        checkResult.setCompanyPayPrice(totalPrice.add(insurancePrice));
        //设置默认个人支付金额
        checkResult.setPersonalPayPrice(reqContract.getTravel_on_busi_common_req_contract().getInsurance_personal_price());
        //设置默认可报销金额
        checkResult.setReimbursablePrice(BigDecimal.ZERO);
        //设置默认不可报销金额
        checkResult.setUnreimbursablePrice(BigDecimal.ZERO);
        //设置默认合规金额为订单总价
        checkResult.setAmountCompliance(totalPrice);
        //设置默认超规金额
        checkResult.setAmountNonCompliance(BigDecimal.ZERO);
        //企业统一阻断判断
        //2.企业余额不足
        if(companyRuleSummary.getIsCompanyAccount()){
            checkResult.setResCode(GlobalResponseCode.OrderCheckCompanyAccountNoAuth.getCode(), GlobalResponseCode.OrderCheckCompanyAccountNoAuth.getMsg());
            return checkResult;
        }
        //3.费用扣减配置项异常 直接通过new saasException 返回前端
        //4.公司成本归属异常
        else if(companyRuleSummary.getIsCompanyCostAttributionError()){
            checkResult.setResCode(companyRuleSummary.getCompanyCostAttributionErrorCode(),companyRuleSummary.getCompanyCostAttributionErrorMsg());
            return checkResult;
        }
        //5.统一版本号是否有异常 需要在5.0.0以上版本支持
        else if(companyRuleSummary.getIsClientVersionUpdate()){
            checkResult.setResCode(GlobalResponseCode.ApplyCenterAlert.getCode(),GlobalResponseCode.ApplyCenterAlert.getMsg());
            return checkResult;
        }
        //6.判断入住人是否重复
        else if(companyRuleSummary.getIsFrequentSame()){
            checkResult.setResCode(GlobalResponseCode.OrderCheckHotelFrequentSame.getCode(),GlobalResponseCode.OrderCheckHotelFrequentSame.getMsg());
            return checkResult;
        }
        checkResult.setCost_attribution_scope(companyRuleSummary.getCostAttrAndBudgetConf().getCost_attribution_scope());
        checkResult.setBudget_cost_attr_type(companyRuleSummary.getCostAttrAndBudgetConf().getBudget_cost_attr_type());
        // 预算校验
        TravelOnBusiOrderRuleCheckResult travelOrderBudgetCheckResult = iOrderCheckService.categoryOrderBudgetCheckV2(reqContract, companyRuleSummary.getCostAttrAndBudgetConf(), OrderCategory.Airplane, clientVersion);
        logger.info("travelOrderBudgetCheckResult:{}", JsonUtils.toJson(travelOrderBudgetCheckResult));
        if (GlobalResponseCode.Success.getCode() != travelOrderBudgetCheckResult.getErrCode()) {
            return travelOrderBudgetCheckResult;
        }
        //获取预算合规金额
        BigDecimal budgetAmountCompliance = travelOrderBudgetCheckResult.getAmountCompliance();
        //获取预算超规金额
        BigDecimal budgetAmountNonCompliance = travelOrderBudgetCheckResult.getAmountNonCompliance();
        //设置预算合规金额
        checkResult.setAmountCompliance(budgetAmountCompliance);
        //设置预算超规金额
        checkResult.setAmountNonCompliance(budgetAmountNonCompliance);
        logger.info("[机票下单校验]，预算合规金额:{}, 预算超规金额:{}", budgetAmountCompliance, budgetAmountNonCompliance);
        //设置每个人的超规项 （费用/非费用）
        List<TravelOnBusiOrderCheckResContract.AirLowestPriceMsg> airLowestPriceMsg = new ArrayList<>();

        //执行出行人模式的规则校验
        currencyCheckService.checkMultiAirRule(reqContract, companyRuleSummary, clientVersion, token, airLowestPriceMsg);

        checkResult.setAirLowestPriceMsgList(airLowestPriceMsg);

        if(companyRuleSummary.getEmployeeRuleSummaryList().stream().anyMatch(a->a.getIsFloat()==true)){

            Optional<BigDecimal>  minFloatPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream()
                    .map(EmployeeRuleSummary::getMinFloatPrice)
                    .min(BigDecimal::compareTo);

            String ruleDesc =  "提示：规定员工预定最低价上浮"+ minFloatPrice.get() + "元以内的航班均合规";
            checkResult.setAirRuleDesc(ruleDesc);
        }


        // 设置优惠券
        currencyCheckService.setReSetCouponAmount(reqContract, companyRuleSummary);
        //设置每个人的最终结果集
        currencyCheckService.setEmployeeResultEnum(companyRuleSummary);

        //设置总体返回是否有费用项超规
        checkResult.setIsPriceExceed(companyRuleSummary.getEmployeeRuleSummaryList().stream().anyMatch(a->a.getIsPriceExceed()==true));
        //设置返回设置返回
        checkResult.setIs_exceed(companyRuleSummary.getEmployeeRuleSummaryList().stream().anyMatch(a->a.getIsExceed()==true));
        //说明无法下单 需要给前端相应提示
        //获取多人校验结果 统一返回值处理
        CurrencyErrorMessage currencyErrorMassage = currencyCheckService.getMultiPlayerResults(companyRuleSummary, checkResult,reqContract.getTravel_on_busi_parameter_req_contract().getExceed_submit());
        checkResult.setCompanyRuleSummary(companyRuleSummary);

        //M10V1处理最低价超规推荐逻辑 出行人模式
        if((reqContract.getPickFlightFlag() == null || reqContract.getPickFlightFlag()==0) && VersionTool.greaterThanOrEqualTo(clientVersion,"5.2.11")) {
            processLowPriceRecommed(clientVersion, checkResult, companyRuleSummary, airLowestPriceMsg, currencyErrorMassage);
        }

        //说明无法下单 需要给前端相应提示
        if(currencyErrorMassage.getCode() != GlobalResponseCode.Success.getCode()){
            checkResult.setCurrencyErrorMessage(currencyErrorMassage);
            checkResult.setResCode(GlobalResponseCode.CurrencyFrame);


            //判断一下是否是 订单审批 还是超规审批 查看历史有没有审批通过的审批单，如果有直接返回前端标识， 二次提交放行
            if (GlobalResponseCode.CheckOrderApproval.getCode() == currencyErrorMassage.getCode() ||
                    GlobalResponseCode.CheckExceedApprovalOrPersonalPay.getCode() == currencyErrorMassage.getCode()
            ) {
                OrderApplyMsgReq orderApplyMsgReq = new OrderApplyMsgReq();
                orderApplyMsgReq.setArrivalCityId(reqContract.getTravel_on_busi_parameter_req_contract().getArrival_city_ids().get(0));
                orderApplyMsgReq.setStartCityId(reqContract.getTravel_on_busi_parameter_req_contract().getStart_city_ids().get(0));
                orderApplyMsgReq.setOrderPrice(reqContract.getTravel_on_busi_common_req_contract().getOrder_price());
                orderApplyMsgReq.setTravelList(reqContract.getTravel_on_busi_common_req_contract().getTraveler_list());
                orderApplyMsgReq.setCompanyId(companyId);
                orderApplyMsgReq.setEmployeeId(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id());
                orderApplyMsgReq.setCategoryTypeEnum(CategoryTypeEnum.Air);
                orderApplyMsgReq.setIsExceed(companyRuleSummary.getEmployeeRuleSummaryList().stream().anyMatch(a -> a.getIsExceed() == true));
                orderApplyMsgReq.setStartTime(reqContract.getTravel_on_busi_parameter_req_contract().getStart_time());
                orderApplyMsgReq.setEndTime(reqContract.getTravel_on_busi_parameter_req_contract().getEnd_time());
                String duringApplyId = currencyCheckService.queryOrderApplyMsgByMutil( orderApplyMsgReq);
                logger.info("duringApplyId:{}", duringApplyId);
                if (StringUtils.isNotBlank(duringApplyId)) {
                    //二次订单审批 直接放行
                    if (!reqContract.getTravel_on_busi_parameter_req_contract().getExist_center_apply_submit()) {
                        logger.info("符合条件无需审批，弹窗提示");
                        checkResult.setErrCode(GlobalResponseCode.OrderCheckExceedApplytAirHint.getCode());
                        if(orderApplyMsgReq.getIsExceed()){
                            checkResult.setErrMsg(GlobalResponseCode.OrderCheckExceedApplytAirHint.getMsg());
                        }else{
                            checkResult.setErrMsg(GlobalResponseCode.OrderCheckApplytAirHint.getMsg());
                        }
                        return checkResult;
                    } else {
                        logger.info("符合条件无需审批，返回审批单id和超规标识");
                        checkResult.setDuring_reapply_id(duringApplyId);
                        checkResult.setResCode(GlobalResponseCode.Success);
                        return checkResult;
                    }
                }
            }


            // 填写理由+个人支付
            if (GlobalResponseCode.PartPayPriceDescWithReason.getCode() == currencyErrorMassage.getCode()
                    || GlobalResponseCode.AllPayPriceDescWithReason.getCode() == currencyErrorMassage.getCode()
                    || GlobalResponseCode.ExceedApprovalOrPartPayPriceDesc.getCode() == currencyErrorMassage.getCode()) {
                //设置公司支付金额
                BigDecimal companyPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
                        EmployeeRuleSummary::getCompanyPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal personalPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
                        EmployeeRuleSummary::getPersonalPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                checkResult.setCompanyPayPrice(companyPayPrice);
                checkResult.setPersonalPayPrice(personalPayPrice);
                checkResult.setPersonalPay(true);
            }
            //超规二次提交
            if(reqContract.getTravel_on_busi_parameter_req_contract().getExceed_submit()){

                //超规全额付 无需理由
                if(GlobalResponseCode.AllPayPriceDesc.getCode() == currencyErrorMassage.getCode() ){
                    //设置公司支付金额 0
                    checkResult.setCompanyPayPrice(BigDecimal.ZERO);
                    checkResult.setPersonalPay(true);
                    //个人支付金额为全额
                    BigDecimal personalPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
                        EmployeeRuleSummary::getPersonalPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                    checkResult.setPersonalPayPrice(personalPayPrice);
                }

                //超规部分支付  无需理由
                if(GlobalResponseCode.PartPayPriceDesc.getCode() == currencyErrorMassage.getCode()){
                    //设置公司支付金额
                    BigDecimal companyPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
                        EmployeeRuleSummary::getCompanyPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal personalPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
                        EmployeeRuleSummary::getPersonalPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                    checkResult.setCompanyPayPrice(companyPayPrice);
                    checkResult.setPersonalPayPrice(personalPayPrice);
                    checkResult.setPersonalPay(true);
                }

                //二次提交且标记成全额个人支付后
                if((GlobalResponseCode.PartPayPriceDesc.getCode() == currencyErrorMassage.getCode()
                    ||  GlobalResponseCode.AllPayPriceDesc.getCode() == currencyErrorMassage.getCode()
                    ||  GlobalResponseCode.CheckExceedReasonAndPersonalPay.getCode() == currencyErrorMassage.getCode())
                    && Objects.equals(reqContract.getTravel_on_busi_parameter_req_contract()
                    .getExceed_personalpay_sumbit(), true)){
                    checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                    //设置公司支付金额
                    BigDecimal companyPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
                        EmployeeRuleSummary::getCompanyPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal personalPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
                        EmployeeRuleSummary::getPersonalPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                    checkResult.setCompanyPayPrice(companyPayPrice);
                    checkResult.setPersonalPayPrice(personalPayPrice);
                    checkResult.setPersonalPay(true);
                    checkResult.setResCode(GlobalResponseCode.Success);
                }

                // 填写理由+个人支付
                if (GlobalResponseCode.PartPayPriceDescWithReason.getCode() == currencyErrorMassage.getCode()
                    || GlobalResponseCode.AllPayPriceDescWithReason.getCode() == currencyErrorMassage.getCode()) {
                    //设置公司支付金额
                    BigDecimal companyPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
                        EmployeeRuleSummary::getCompanyPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal personalPayPrice = companyRuleSummary.getEmployeeRuleSummaryList().stream().map(
                        EmployeeRuleSummary::getPersonalPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                    checkResult.setCompanyPayPrice(companyPayPrice);
                    checkResult.setPersonalPayPrice(personalPayPrice);
                    checkResult.setPersonalPay(true);

                    //填写理由校验 填写项是否正常
                    TravelOnBusiOrderRuleCheckResult travelOnBusiOrderRuleCheckResult = iOrderCheckService
                        .checkExceedAuthV2(reqContract, CategoryTypeEnum.Air);
                    if (travelOnBusiOrderRuleCheckResult.getErrCode()!=GlobalResponseCode.Success.getCode()) {
                        checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                        checkResult.setErrCode(travelOnBusiOrderRuleCheckResult.getErrCode());
                        checkResult.setErrMsg(travelOnBusiOrderRuleCheckResult.getErrMsg());
                    } else {
                        // 标记成全额个人支付后
                        if (Objects.equals(reqContract.getTravel_on_busi_parameter_req_contract()
                            .getExceed_personalpay_sumbit(), true)) {
                            checkResult.setResCode(GlobalResponseCode.Success);
                        }
                        checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                    }
                }

                if(GlobalResponseCode.CheckExceedApprovalOrPersonalPay.getCode()== currencyErrorMassage.getCode() ||
                        GlobalResponseCode.CheckPersonalPay.getCode()== currencyErrorMassage.getCode() ||
                        GlobalResponseCode.CheckOrderApproval.getCode()== currencyErrorMassage.getCode()
                ){
                    checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                    checkResult.setResCode(GlobalResponseCode.Success);
                }
                else if (GlobalResponseCode.CheckExceedReasonOrPersonalPay.getCode()== currencyErrorMassage.getCode()
                    || GlobalResponseCode.CheckReason.getCode() == currencyErrorMassage.getCode()
                    || GlobalResponseCode.ExceedAirReason.getCode() == currencyErrorMassage.getCode()) {
                    //填写理由校验 填写项是否正常
                    TravelOnBusiOrderRuleCheckResult travelOnBusiOrderRuleCheckResult = currencyCheckService.checkExceedAuthV2(reqContract, CategoryTypeEnum.Air);
                    if(travelOnBusiOrderRuleCheckResult.getErrCode()!=GlobalResponseCode.Success.getCode()){
                        checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                        checkResult.setErrCode(travelOnBusiOrderRuleCheckResult.getErrCode());
                        checkResult.setErrMsg(travelOnBusiOrderRuleCheckResult.getErrMsg());
                    }else{
                        checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                        checkResult.setResCode(GlobalResponseCode.Success);
                    }
                }
            }
            //如果强制超规个人支付 则直接返回正确
            Boolean exceedPersonalpaySumbit = Objects.equals(reqContract.getTravel_on_busi_parameter_req_contract()
                    .getExceed_personalpay_sumbit(), true);
            if(checkResult.getPersonalPay() && exceedPersonalpaySumbit){
                checkResult.setResCode(GlobalResponseCode.Success);
            }
        }
        //增加快照信息
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("companyRuleSummary",companyRuleSummary);
        checkResult.setSnapshotInfo(jsonObject);
        //完整日志打印
        log.info("companyRuleSummary-{}",JsonUtils.toJson(companyRuleSummary));
        return checkResult;
    }

    private void processLowPriceRecommed(String clientVersion, TravelOnBusiOrderRuleCheckResult checkResult, CompanyRuleSummary companyRuleSummary, List<TravelOnBusiOrderCheckResContract.AirLowestPriceMsg> airLowestPriceMsg, CurrencyErrorMessage currencyErrorMassage) {
        log.info("processLowPriceRecommed companyRuleSummary={},currencyErrorMassage={}",JSON.toJSONString(companyRuleSummary),JSON.toJSONString(currencyErrorMassage));
        List<CurrencyEmployeeMsg> currencyEmployeeMsgList = currencyErrorMassage.getCurrencyEmployeeMsgList(); //各出行人触发的超规规则
        List<EmployeeRuleSummary> employeeRuleSummaryList = companyRuleSummary.getEmployeeRuleSummaryList(); //各出行人的规则
        Map<String,EmployeeRuleSummary> employeeRuleSummaryMap = new HashMap<>();
        employeeRuleSummaryList.stream().forEach(a->{
            employeeRuleSummaryMap.put(a.getEmployeeId(),a);
        });
        if(CollectionUtils.isNotEmpty(currencyEmployeeMsgList)) {
            if (currencyEmployeeMsgList.size() == 1) {// 只有一人超规的情况下，按这个人的配置
                doProcessOnePersonLowPriceExceed(checkResult, companyRuleSummary, airLowestPriceMsg, currencyEmployeeMsgList, employeeRuleSummaryMap);
            } else { //多人超规
                //对比多个人的低价超规限制是否一致
                Set<String> md5Set = new HashSet<>();
                for (EmployeeRuleSummary employeeRuleSummary : employeeRuleSummaryList) {
                    StringBuilder sb = new StringBuilder("");
                    if (companyRuleSummary.getIsOpenExceedConfig()) { //如果是超规个人付版本
                        AirRuleV2 airRuleV2 = employeeRuleSummary.getAirRuleV2();
                        if (airRuleV2 != null && airRuleV2.getAirBookConditionGroup() != null && airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup() != null) {
                            sb.append(airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup().getLowPriceFlag());
                            sb.append(airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup().getLowPriceConfig());
                            sb.append(airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup().getLowPriceTime());
                            sb.append(airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup().getLowPriceUnit());
                            sb.append(airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup().getAirPortCityFlag());
                            sb.append(airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup().getFilterStopoverFlightFlag());
                            sb.append(airRuleV2.getAirBookConditionGroup().getAirRuleBookGroup().getShieldAirline());
                        }
                    } else {
                        AirRule airRule = employeeRuleSummary.getAirRule();
                        if (airRule != null) {
                            sb.append(airRule.getLowPriceFlag());
                            sb.append(airRule.getLowPriceTime());
                            sb.append(airRule.getLowPriceUnit());
                            sb.append(airRule.getAirPortCityFlag());
                            sb.append(airRule.getFilterStopoverFlightFlag());
                        }
                    }
                    log.info("getMd5 str={},employeeRuleSummary.getIsEmployee()={}", sb,employeeRuleSummary.getIsEmployee());
                    if(Objects.equals(employeeRuleSummary.getIsEmployee(),true)) {
                        md5Set.add(Md5Utils.getMd5(sb.toString()));
                    }
                }
                log.info("getMd5 md5Set ={}", JSON.toJSONString(md5Set));
                if (md5Set.size() == 1) { //如果所有人的规则一致才推荐，如果有任何一个人不一致则不推荐
                    //按照第一个人的超规规则提示就可以了
                    doProcessMultiPersonLowPriceExceed(checkResult, companyRuleSummary, airLowestPriceMsg, currencyEmployeeMsgList, employeeRuleSummaryMap);
                }
            }
        }
    }

    private void doProcessMultiPersonLowPriceExceed(TravelOnBusiOrderRuleCheckResult checkResult, CompanyRuleSummary companyRuleSummary, List<TravelOnBusiOrderCheckResContract.AirLowestPriceMsg> airLowestPriceMsg, List<CurrencyEmployeeMsg> currencyEmployeeMsgList, Map<String, EmployeeRuleSummary> employeeRuleSummaryMap) {
        if (companyRuleSummary.getIsOpenExceedConfig()) { //如果是超规个人付版本
            for(CurrencyEmployeeMsg currencyEmployeeMsg: currencyEmployeeMsgList ) {
                for (CurrencyMsg currencyMsg : currencyEmployeeMsg.getErrMsgList()) {
                    if (currencyMsg.getCode() == AirRuleMsg.AIR_LOWEST_PRICE.getCode()) {
                        if (currencyMsg.getBlockPriority() > ExceedConfigTypeEnum.EXCEED_NOT_ALLOW.getCompareOrder()) {
                            checkResult.setIsShowRecommend(true);
                        }
                        checkResult.setMin_flight(airLowestPriceMsg.get(0).getMinFlight());
                    }
                }
            }
        } else { //如果不是超规个人付版本
            boolean hasLowPriceExceed = false;
            boolean notAllowdFlag = false;
            for(CurrencyEmployeeMsg currencyEmployeeMsg: currencyEmployeeMsgList ) {
                for (CurrencyMsg currencyMsg : currencyEmployeeMsg.getErrMsgList()) {
                    //检查超规项里面是不是有最低价超规
                    if (currencyMsg.getCode() == GlobalResponseCode.OrderCheckAirMinPriceNoAuth.getCode()) {
                        hasLowPriceExceed = true;
                        EmployeeRuleSummary employeeRuleSummary = employeeRuleSummaryMap.get(currencyEmployeeMsg.getEmployeeId());
                        if (employeeRuleSummary != null) {
                            if (TravelExceedType.NotAllowed.getCode() == employeeRuleSummary.getExceedBuyType()) {
                                notAllowdFlag = true;
                            }
                        }
                        checkResult.setMin_flight(airLowestPriceMsg.get(0).getMinFlight());
                    }
                }
            }
            if(hasLowPriceExceed && !notAllowdFlag){
                checkResult.setIsShowRecommend(true);
            }
        }

    }

    private void doProcessOnePersonLowPriceExceed(TravelOnBusiOrderRuleCheckResult checkResult, CompanyRuleSummary companyRuleSummary, List<TravelOnBusiOrderCheckResContract.AirLowestPriceMsg> airLowestPriceMsg, List<CurrencyEmployeeMsg> currencyEmployeeMsgList, Map<String, EmployeeRuleSummary> employeeRuleSummaryMap) {
        List<CurrencyMsg> errMsgList = currencyEmployeeMsgList.get(0).getErrMsgList();

        if (companyRuleSummary.getIsOpenExceedConfig()) { //如果是超规个人付版本
            for (CurrencyMsg currencyMsg : errMsgList) {
                if (currencyMsg.getCode() == AirRuleMsg.AIR_LOWEST_PRICE.getCode()) {
                    if (currencyMsg.getBlockPriority() > ExceedConfigTypeEnum.EXCEED_NOT_ALLOW.getCompareOrder()) {
                        checkResult.setIsShowRecommend(true);
                    }
                    checkResult.setMin_flight(airLowestPriceMsg.get(0).getMinFlight());
                }
            }
        } else { //如果不是超规个人付版本
            for (CurrencyMsg currencyMsg : errMsgList) {
                //检查超规项里面是不是有最低价超规
                if (currencyMsg.getCode() == GlobalResponseCode.OrderCheckAirMinPriceNoAuth.getCode()) {
                    EmployeeRuleSummary employeeRuleSummary = employeeRuleSummaryMap.get(currencyEmployeeMsgList.get(0).getEmployeeId());
                    if (employeeRuleSummary != null) {
                        if (TravelExceedType.NotAllowed.getCode() != employeeRuleSummary.getExceedBuyType()) {
                            checkResult.setIsShowRecommend(true);
                        }
                    }
                    checkResult.setMin_flight(airLowestPriceMsg.get(0).getMinFlight());
                }
            }
        }
    }

    /**
     * 校验飞机规则
     *
     * @param reqContract
     * @return
     */
    private TravelOnBusiOrderRuleCheckResult airOrderRuleCheckResult(AirOrderCheckReqV2Contract reqContract, String clientVersion, String token) throws SaasException {
        //人员信息集合
        List<PersonnelInfo> personnelInfoList = new ArrayList<>();
        String companyId = reqContract.getTravel_on_busi_common_req_contract().getCompany_id();
        BigDecimal totalPrice = BigDecimal.ZERO;
        int flightType = reqContract.getFlight_type() == null ? 1 : reqContract.getFlight_type();
        boolean isGoback = flightType == 2;
        logger.info("airOrderRuleCheckResult:isGoback:{}", isGoback);
        //订单金额   是单个还是全部？
        // 全部 往返机票时等于 goTotalAmount + backTotalAmount
        BigDecimal orderPrice = ObjUtils.toBigDecimal(reqContract.getTravel_on_busi_common_req_contract().getOrder_price(), reqContract.getTravel_on_busi_common_req_contract().getTotal_price());
        //优惠券金额   开启所有订单总额的费用上限 优惠券是否可用？
        BigDecimal couponAmount = ObjUtils.toBigDecimal(reqContract.getCouponInfo().getCoupon_amount(), BigDecimal.ZERO);
        // 去程金额
        BigDecimal goTotalAmount = ObjUtils.toBigDecimal(reqContract.getTravel_on_busi_common_req_contract().getGo_total_price(), BigDecimal.ZERO);
        // 返程金额
        BigDecimal backTotalAmount = ObjUtils.toBigDecimal(reqContract.getTravel_on_busi_common_req_contract().getBack_total_price(), BigDecimal.ZERO);
        logger.info("orderPrice:{},couponAmount:{},goTotalAmount:{},backTotalAmount:{}", orderPrice, couponAmount, goTotalAmount, backTotalAmount);
        Integer couponExceedPriceSetting = iOrderCheckService.queryCouponExceedPriceSetting(companyId);
        logger.info("企业优惠券配置, couponExceedPriceSetting:{}", couponExceedPriceSetting);

        BigDecimal insurancePrice = ObjUtils.toBigDecimal(
            reqContract.getTravel_on_busi_common_req_contract().getInsurance_price(), BigDecimal.ZERO);
        // 初始单张机票价格，未减去优惠券
        BigDecimal originPerTicketPrice = reqContract.getPrice();
        // 优惠券是否抵扣超规
        if (reqContract.getCouponInfo().isAllowDeductExceed(couponExceedPriceSetting)) {
            totalPrice = orderPrice.subtract(couponAmount).max(BigDecimal.ZERO);
            List<String> frequentIdList = reqContract.getTravel_on_busi_common_req_contract().getFrequent_id();
            if (couponAmount.compareTo(BigDecimal.ZERO) > 0) {
                reqContract.setPrice(reqContract.getPrice().subtract(couponAmount.divide(BigDecimal.valueOf(frequentIdList.size()), 2, RoundingMode.HALF_UP)).max(BigDecimal.ZERO));
            }
            goTotalAmount = goTotalAmount.subtract(couponAmount).max(BigDecimal.ZERO);
        } else {
            totalPrice = reqContract.getTravel_on_busi_common_req_contract().getTotal_price();
            goTotalAmount = reqContract.getTravel_on_busi_common_req_contract().getGo_total_price();
        }
        if (!isGoback) {
            goTotalAmount = totalPrice;
        }
        // 默认初始化正常
        TravelOnBusiOrderRuleCheckResult checkResult = new TravelOnBusiOrderRuleCheckResult();
        checkResult.setResCode(GlobalResponseCode.Success);
        checkResult.setPersonalPay(false);
//        checkResult.setIsTraveBooking(false); //设置出行模式 预订人模式
        // 快照信息
        JSONObject snapshotInfo = new JSONObject();
        checkResult.setSnapshotInfo(snapshotInfo);
        // 查询是否个人垫付模式
        Map<String, Boolean> funcMap = iPrivilegeService.queryFunctionMoudle(CodeTypeEnums.CustomAuth.getKey(),
            companyId, reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(), CustomCodeType.M_ADVANCE_PAYMENT.getKey());
        Boolean advancePayment = funcMap.get(CustomCodeType.M_ADVANCE_PAYMENT.getKey());
        logger.info("[机票下单校验]，是否个人垫付模式:{}, funcMap:{}", advancePayment, funcMap);



        PayModelEnum payModel = advancePayment ? PayModelEnum.PERSONAL_PREPAY : PayModelEnum.COMPANY_PAY;
        checkResult.setPayModel(payModel.getCode());
        snapshotInfo.put("payModel", payModel.getCode());
        //BigDecimal totalPrice = reqContract.getTravel_on_busi_common_req_contract().getTotal_price();
        //能否垫付
        if (advancePayment) {
            checkResult.setCompanyPayPrice(BigDecimal.ZERO);
            checkResult.setPersonalPayPrice(totalPrice.add(insurancePrice));
            checkResult.setReimbursablePrice(totalPrice.add(insurancePrice));
            if (isGoback) {
                checkResult.setGoReimbursablePrice(goTotalAmount);
                checkResult.setBackReimbursablePrice(backTotalAmount);
            }
            checkResult.setUnreimbursablePrice(BigDecimal.ZERO);
        } else {
            checkResult.setCompanyPayPrice(totalPrice.add(insurancePrice));
            checkResult.setPersonalPayPrice(BigDecimal.ZERO);
            checkResult.setReimbursablePrice(BigDecimal.ZERO);
            if (isGoback) {
                checkResult.setGoReimbursablePrice(BigDecimal.ZERO);
                checkResult.setBackReimbursablePrice(BigDecimal.ZERO);
            }
            checkResult.setUnreimbursablePrice(BigDecimal.ZERO);
        }
        checkResult.setAmountCompliance(totalPrice.add(insurancePrice));
        checkResult.setAmountNonCompliance(BigDecimal.ZERO);
        // 预订人状态
        EmployeeContract orderEmployee = baseOrganizationService.getEmployee(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(),
            companyId);
        logger.info("预订人状态:{}", JsonUtils.toJson(orderEmployee));
        if (orderEmployee == null || orderEmployee.getStatus() != EmployeeStatusEnum.ACTIVE.getValue()) {
            checkResult.setResCode(GlobalResponseCode.OrderCheckEmployeeNotActive);
            return checkResult;
        }
        // 企业飞机权限
        CompanyRuleDTO companyRule = iCompanyRuleService.queryByCompanyId(companyId);
        logger.info("企业飞机权限:{}", JsonUtils.toJson(companyRule));
        if (companyRule == null || companyRule.getAirRule() != 1) {
            checkResult.setResCode(GlobalResponseCode.OrderCheckCompanyNoAuth.getCode(), CoreLanguage.Common_Exception_AirCompanyNoAuthMsg.getMessage());
            return checkResult;
        }
        // 校验往返机票是否开启
        int companyIntConfig = 0;
        try {
            companyIntConfig = sysConfigItemCompanyService.getCompanyIntConfig(companyId, "permissions_air", "whether_go_and_back");
            logger.info("companyIntConfig:{}",companyIntConfig);
        } catch (Exception ex) {
            logger.warn("获取国内机票往返异常：" + ex.getMessage());
        }
        //ApplySetupContract applySetupContract = iMessageSetupService.queryCompanyAirSetupWithDefault(reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), SaasApplyConstant.AIR_GO_THERE_AND_BACK);
        if (ObjectUtil.isNull(reqContract.getFlight_type())) {
            reqContract.setFlight_type(TripType.oneWay.getValue());
        }
        if (reqContract.getFlight_type() == TripType.goAndBack.getValue()) {
            if (companyIntConfig != 1) {
                checkResult.setResCode(GlobalResponseCode.OrderCheckCompanyNoAuth.getCode(), "公司未开启机票往返");
                return checkResult;
            }
        }
        // 员工飞机权限
        EmployeeAirRule employeeAirRule = iBaseEmployeeAirRuleService.queryEmployeeAirRuleByPrimaryKey(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(),
            companyId);
        logger.info("员工飞机权限:{}", JsonUtils.toJson(employeeAirRule));
        checkResult.setEmployeeAirRule(employeeAirRule);
        snapshotInfo.put("authInfo", employeeAirRule);
        if (employeeAirRule == null
                || employeeAirRule.getAir_rule() != AirRuleType.AllowOther.getCode()) {
            checkResult.setResCode(GlobalResponseCode.OrderCheckEmployeeNoAuth.getCode(), CoreLanguage.Common_Exception_AirEmployeeNoAuthMsg.getMessage());
            return checkResult;
        }
        //预定人员信息 (用于一人多规则的订单信息展示)
        PersonnelInfo personnelInfo = new PersonnelInfo();
        //设置预定人员工id
        personnelInfo.setId(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id());
        //设置预定人员工姓名
        personnelInfo.setName(orderEmployee.getName());
        //设置是否是多规则
        personnelInfo.setIs_rules(employeeAirRule.getMulti_rule_switch());


        //企业余额校验(余额不足或者账户被锁定)
        TravelOnBusiOrderRuleCheckResult checkCompanyAccountResult = iOrderCheckService.checkCompanyAccount(companyId, totalPrice, OrderCategory.Airplane.getKey(), advancePayment);
        logger.info("企业余额校验:{}", JsonUtils.toJson(checkCompanyAccountResult));
        if (GlobalResponseCode.Success.getCode() != checkCompanyAccountResult.getErrCode()) {
            FinhubLogger.event(companyId, EventParams.build(**********, false).put("account_sub_type", 2));
            return checkCompanyAccountResult;
        }
        CostAttrAndBudgetConf costAttrAndBudgetConf = iMessageSetupService.queryCostAttrAndBudgetConf(companyId);

        logger.info("CostAttrAndBudgetConf:{}", JsonUtils.toJson(costAttrAndBudgetConf));
        // 老版本配置"部门和项目"兼容为"部门或项目"
        if (costAttrAndBudgetConf.getCost_attribution_scope() == CostAttributionScopeEnum.DEPT_AND_PROJ.getCode()
                && ObjUtils.isNotBlank(clientVersion) && VersionTool.lessThan(clientVersion, "4.2.0")) {
            costAttrAndBudgetConf.setCost_attribution_scope(CostAttributionScopeEnum.DEPT_OR_PROJ.getCode());
        }
        checkResult.setCost_attribution_scope(costAttrAndBudgetConf.getCost_attribution_scope());
        checkResult.setBudget_cost_attr_type(costAttrAndBudgetConf.getBudget_cost_attr_type());

        // 检查费用归属灰度开关 进行灰度校验
        Integer costAttributionNewSwitch = iMessageSetupRpcService.queryCostAttributionNewSwitch(companyId);
        log.info("costAttributionNewSwitch:{}",costAttributionNewSwitch);
        if (CommonSwitchConstant.OPEN.equals(costAttributionNewSwitch)){
            CostCheckVO costCheckVO = CostCheckVO.from(reqContract);
            currencyCheckService.newCheckCostAttribution(costCheckVO);
        }
        else{
            Integer costInfoType = reqContract.getCost_info_type();
            if (costInfoType != null && costInfoType == 2) {
                TravelOnBusiOrderRuleCheckResult checkCompanyCostAttributionResult = iOrderCheckService.checkCostTicketInfo(
                    companyId, reqContract.getCost_info_ticket_list(), costAttrAndBudgetConf, clientVersion);
                logger.info("checkCompanyCostAttributionResult:{}", JsonUtils.toJson(checkCompanyCostAttributionResult));
                if (GlobalResponseCode.Success.getCode() != checkCompanyCostAttributionResult.getErrCode()) {
                    return checkCompanyCostAttributionResult;
                }
            } else {
                TravelOnBusiOrderRuleCheckResult checkCompanyCostAttributionResult = iOrderCheckService.checkCostInfo(
                    companyId, reqContract.getCost_info(), costAttrAndBudgetConf, clientVersion);
                logger.info("else checkCompanyCostAttributionResult:{}", JsonUtils.toJson(checkCompanyCostAttributionResult));
                if (GlobalResponseCode.Success.getCode() != checkCompanyCostAttributionResult.getErrCode()) {
                    return checkCompanyCostAttributionResult;
                }
            }
        }

        // 单程、往返校验审批单
        if (reqContract.getFlight_type()== TripType.oneWay.getValue() || reqContract.getFlight_type()== TripType.goAndBack.getValue()){
            //审批单查询校验
            TravelOnBusiOrderRuleCheckResult travelOnBusiOrderRuleCheckResult = iOrderCheckService.travelOnbusiOrderApplyIdCheckV2(reqContract, employeeAirRule.getAir_verify_flag(), TravelType.Air.getCode());
            logger.info("TravelOnBusiOrderRuleCheckResult:{}", JsonUtils.toJson(travelOnBusiOrderRuleCheckResult));
            if (GlobalResponseCode.Success.getCode() != travelOnBusiOrderRuleCheckResult.getErrCode()) {
                return travelOnBusiOrderRuleCheckResult;
            }
        }
        //预算校验
        TravelOnBusiOrderRuleCheckResult travelOrderBudgetCheckResult = iOrderCheckService.categoryOrderBudgetCheckV2(reqContract, costAttrAndBudgetConf, OrderCategory.Airplane, clientVersion);
        logger.info("travelOrderBudgetCheckResult:{}", JsonUtils.toJson(travelOrderBudgetCheckResult));
        snapshotInfo.put("budgetList", travelOrderBudgetCheckResult.getBudgetSettingAndUseList());
        if (GlobalResponseCode.Success.getCode() != travelOrderBudgetCheckResult.getErrCode()) {
            return travelOrderBudgetCheckResult;
        }
        BigDecimal budgetAmountCompliance = travelOrderBudgetCheckResult.getAmountCompliance();
        BigDecimal budgetAmountNonCompliance = travelOrderBudgetCheckResult.getAmountNonCompliance();
        checkResult.setAmountCompliance(budgetAmountCompliance);
        checkResult.setAmountNonCompliance(budgetAmountNonCompliance);


        logger.info("[机票下单校验]，预算合规金额:{}, 预算超规金额:{}", budgetAmountCompliance, budgetAmountNonCompliance);

        MessageSetup estimatedCheckSetup = iMessageSetupService.queryCompanyMessageSetupWithDefault(companyId, SaasApplyConstant.ITEM_CODE_TRIP_APPLY_BUDGET_CHECK);

        logger.info("estimatedCheckSetup:{}", JsonUtils.toJson(estimatedCheckSetup));

        // 预估费用校验
        CheckApplyEstimatedAmountAirReq req = new CheckApplyEstimatedAmountAirReq();
        req.setApplyId(reqContract.getTravel_on_busi_common_req_contract().getApply_id());
        req.setTripId(reqContract.getTravel_on_busi_common_req_contract().getApply_trip_id());
        req.setCompanyId(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        req.setValidateAmount(goTotalAmount);
        req.setBackValidateAmount(backTotalAmount);
        req.setBizType(BizType.AirPlane.getCode());
        req.setStartDate(reqContract.getTravel_on_busi_parameter_req_contract().getStart_time());
        req.setEndDate(reqContract.getTravel_on_busi_parameter_req_contract().getStart_time());
        req.setBackStartDate(reqContract.getTravel_on_busi_parameter_req_contract().getBack_start_time());
        req.setBackEndDate(reqContract.getTravel_on_busi_parameter_req_contract().getBack_start_time());
        req.setGoBack(isGoback);
        AmountExceedCheckRes amountExceedCheckRes = tripEstimateService.checkTripEstimatedAmountAir(req);

        if (GlobalResponseCode.Success.getCode() != amountExceedCheckRes.getErrCode()) {
            TravelOnBusiOrderRuleCheckResult result = new TravelOnBusiOrderRuleCheckResult();
            result.setErrCode(GlobalResponseCode.ApplyTripEstimatedAmountIsError.getCode());
            String content = "";
            // 单日上限单独message
            if (Objects.equals(amountExceedCheckRes.getTotalEstimatedLimitIntValue(),
                TotalEstimatedLimitType.DAILY_LIMIT.getCode())) {
                result.setErrCode(GlobalResponseCode.CustomApplyTripEstimatedAmountDailyExceed.getCode());
                if (isGoback) {
                    if (Objects.equals(amountExceedCheckRes.getGoOrBackExceed(), 1)) {
                        content = "您预订的去程行程实际费用超过申请单单日费用上限";
                    } else if (Objects.equals(amountExceedCheckRes.getGoOrBackExceed(), 2)) {
                        content = "您预订的回程行程实际费用超过申请单单日费用上限";
                    } else {
                        content = "您预订的行程实际费用超过申请单单日费用上限";
                    }
                } else {
                    content = "您预订的行程实际费用超过申请单单日费用上限";
                }
            } else {
                if (isGoback) {
                    if (Objects.equals(amountExceedCheckRes.getGoOrBackExceed(), 1)) {
                        content = String.format("您预订的去程行程实际费用超过申请单费用上限\n共用一个行程，\n"
                                + "行程费用上限金额：￥%s\n本次预订金额：￥%s",
                            amountExceedCheckRes.getTotalEstimatedLimitAmount().setScale(2,
                                BigDecimal.ROUND_HALF_UP).toString(),
                            amountExceedCheckRes.getSettingAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                    } else if (Objects.equals(amountExceedCheckRes.getGoOrBackExceed(), 2)) {
                        content = String.format("您预订的回程行程实际费用超过申请单费用上限\n共用一个行程，\n"
                                + "行程费用上限金额：￥%s\n本次预订金额：￥%s",
                            amountExceedCheckRes.getTotalEstimatedLimitAmount().setScale(2,
                                BigDecimal.ROUND_HALF_UP).toString(),
                            amountExceedCheckRes.getSettingAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                    } else {
                        content = String.format("您预订的行程实际费用超过申请单费用上限\n共用一个行程，\n"
                                + "行程费用上限金额：￥%s\n本次预订金额：￥%s",
                            amountExceedCheckRes.getTotalEstimatedLimitAmount().setScale(2,
                                BigDecimal.ROUND_HALF_UP).toString(),
                            amountExceedCheckRes.getSettingAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                    }
                } else {
                    content = String.format("您预订的行程实际费用超过申请单费用上限\n共用一个行程，\n"
                            + "行程费用上限金额：￥%s\n本次预订金额：￥%s",
                        amountExceedCheckRes.getTotalEstimatedLimitAmount().setScale(2,
                            BigDecimal.ROUND_HALF_UP).toString(),
                        amountExceedCheckRes.getSettingAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                }
                if (amountExceedCheckRes.getUsedAmount() == null) {
                    result.setEstimatedAmount(amountExceedCheckRes.getTotalEstimatedLimitAmount());
                } else {
                    result.setEstimatedAmount(amountExceedCheckRes.getTotalEstimatedLimitAmount()
                        .subtract(amountExceedCheckRes.getUsedAmount()));
                }
                result.setRealPrice(amountExceedCheckRes.getSettingAmount());
            }
            result.setApplyTripId(reqContract.getTravel_on_busi_common_req_contract().getApply_trip_id());
            result.setErrMsg(content);
            return result;
        }

        logger.info("employeeAirRule.getAir_verify_flag:{}", employeeAirRule.getAir_verify_flag());
        //需要行程审批
        if (employeeAirRule.getAir_verify_flag()) {
            Integer approveType = iOrderCheckService.queryApproveType(companyId);
            logger.info("approveType:{}",approveType);
            //2:审批单中带有规则信息
            if (approveType == 2) {
                String applyId = reqContract.getTravel_on_busi_common_req_contract().getApply_id();
                ApplyRuleSetting applyRuleSetting = applyRuleSettingExtMapper.queryApplyRuleByApplyOrderId(applyId);
                logger.info("applyRuleSetting:{}", JsonUtils.toJson(applyRuleSetting));
                if (applyRuleSetting != null && applyRuleSetting.getAirInfo() != null) {
                    List<ApplyThirdContract.KeyValueItem> airRuleList = JSONArray.parseArray(applyRuleSetting.getAirInfo(), ApplyThirdContract.KeyValueItem.class);
                    if (CollectionUtils.isEmpty(airRuleList)) {
                        return checkResult;
                    }
                    return checkAirThirdRule(airRuleList, checkResult, reqContract);
                }

            }
        }
        //订单审批开关
        Boolean airOrderVerifyFlag = employeeAirRule.getAir_order_verify_flag();
        if (advancePayment) {
            airOrderVerifyFlag = false;
        }
        logger.info("[校验飞机规则]，订单审批开关:{}", airOrderVerifyFlag);
        //处理老板版兼容问题(提示升级)
        String oldVersion = "1.9.96";
        if (VersionTool.compare(clientVersion, oldVersion) < 0 && airOrderVerifyFlag) {
            throw new SaasException(GlobalResponseCode.ApplyCenterAlert);
        }

        String ruleId = "";
        //获取项目id
        List<String> productList = currencyCheckService.getProductIdByCostInfo(reqContract.getCost_info_type(), reqContract.getCost_info_ticket_list(), reqContract.getCost_info(), reqContract.getCostInfoString(), reqContract.getCostInfoTicketListString(),reqContract.getTravel_on_busi_common_req_contract().getCompany_id(),null);
        //如果是多规则 则重新取规则id
        if(employeeAirRule.getMulti_rule_switch()){
            ControlOrderRuleDto controlOrderRuleDto = consumeTemplateRuleService.getConsumeRule(reqContract.getTravel_on_busi_common_req_contract().getReason_id(), employeeAirRule.getTemplate_id(),reqContract.getTravel_on_busi_common_req_contract().getCompany_id(),productList, com.fenbeitong.saas.core.model.enums.apply.BizType.Air, clientVersion);
            employeeAirRule.setAir_rule_flag(controlOrderRuleDto.getIsEnable());
            ruleId = controlOrderRuleDto.getRuleId();
            personnelInfo.setRule_id(ruleId);
            personnelInfo.setIs_rule_flag(controlOrderRuleDto.getIsEnable());
            personnelInfo.setControl_dimension(String.format("管控维度-%s,适用范围-%s",controlOrderRuleDto.getName(),controlOrderRuleDto.getRange()));
            checkResult.setControlDimensionDesc(controlOrderRuleDto.getRangeDesc());
        }
        personnelInfo.setIs_rule_flag(employeeAirRule.getAir_rule_flag());


        //校验规则
        AirManagerSetting airManagerSetting = iMessageSetupRpcService.queryAirRuleExceedConfigMessage(companyId);
        Boolean isOpenExceedConfig = airManagerSetting.getIsOpenExceedConfig();
        Integer exceedConfigLever = airManagerSetting.getExceedConfigLever();
        logger.info("机票超规个人付:{}, exceedConfigLever:{}", isOpenExceedConfig, exceedConfigLever);
        if (employeeAirRule.getAir_rule_flag()) {
            if (isOpenExceedConfig && Objects.equals(exceedConfigLever, 2)) { // 机票超规个人付开始
                if (VersionTool.compare(clientVersion, AirRuleConstant.AIR_EXCEED_PERSONAL_PAY_CLIENT_VERSION) < 0) {
                    throw new SaasException(GlobalResponseCode.AirTrainCenterAlert);
                }

                // 往返的版本升级
                if (Objects.equals(isGoback, true) && VersionTool.compare(clientVersion, "5.2.31") < 0) {
                    throw new SaasException(GlobalResponseCode.ApplyCenterAlert);
                }

                //消费规则校验
                if(!employeeAirRule.getMulti_rule_switch()){
                    //获取当前预定人规则id
                    ruleId = ObjUtils.ifNull(employeeAirRule.getManual_air_rule_id(), employeeAirRule.getDefault_air_rule_id());
                    personnelInfo.setRule_id(ruleId);
                }
                AirRuleV2 airRuleV2 = iRuleV2Service.getAirRuleV2ById(ruleId, companyId);
                personnelInfo.setRule_name(airRuleV2.getName());
                personnelInfoList.add(personnelInfo);
                checkResult.setPersonnelInfoList(personnelInfoList);
                logger.info("airRuleV2:{}", JsonUtils.toJson(airRuleV2));
                checkResult.setAirRuleV2(airRuleV2);
                snapshotInfo.put("ruleInfo", airRuleV2);
                if (airRuleV2 == null && employeeAirRule.getAir_rule_flag()) {
                    checkResult.setResCode(GlobalResponseCode.OrderCheckRuleNotExist);
                    return checkResult;
                }
                // 是否可以抵扣超规
                boolean allowDeductExceed = reqContract.getCouponInfo().isAllowDeductExceed(couponExceedPriceSetting);

                //构造预定机票规则校验航班信息参数
                AirFlightRuleCheckReq.FlightInfo flightInfo = AirRuleUtils.buildBookFlightInfo(reqContract, token,
                    allowDeductExceed, couponAmount, originPerTicketPrice, insurancePrice,reqContract.getTravel_on_busi_common_req_contract().getInsurance_personal_price(),
                    reqContract.getTravel_on_busi_common_req_contract().getFrequent_info().size());
                // 单张票价拿最原始的票价
                AirFlightRuleCheckReq airFlightRuleCheckReq = new AirFlightRuleCheckReq();
                airFlightRuleCheckReq.setFlightInfo(flightInfo);
                airFlightRuleCheckReq.setRuleInfo(airRuleV2);
                airFlightRuleCheckReq.setRuleId(ruleId);
                airFlightRuleCheckReq.setPickFlightFlag(reqContract.getPickFlightFlag());
                airFlightRuleCheckReq.setClientVersion(clientVersion);

                //机票规则校验结果
                AirFlightRuleCheckRes airFlightRuleCheckRes =
                    airRuleV2CheckService.airFlightRuleCheckRes(airFlightRuleCheckReq, employeeAirRule);

                checkResult.setAirLowestPriceMsgList(airFlightRuleCheckRes.getAirLowestPriceMsg());

                if(airFlightRuleCheckRes.getIsFloat()){
                    String ruleDesc =  "提示：贵司规定员工预订最低价上浮"+airFlightRuleCheckRes.getFloatVal()+ (airFlightRuleCheckRes.getFloatType().equals(0)?"元":"%") + "以内的航班均合规";
                    checkResult.setAirRuleDesc(ruleDesc);
                }

                String duringApplyId = iOrderCheckService.queryOrderApplyMsg(reqContract.getTravel_on_busi_parameter_req_contract(), reqContract.getTravel_on_busi_common_req_contract(), CategoryTypeEnum.Air, airFlightRuleCheckRes.getIsExceed(), null);
                logger.info("duringApplyId--:{}", duringApplyId);

                TravelOnBusiOrderCheckReqV2Contract.TravelOnBusiParameterReqContract travelOnBusiParameterReqContract
                    = reqContract.getTravel_on_busi_parameter_req_contract();

                // 超规类别
                Boolean isCityRuleExceed = airFlightRuleCheckRes.getIsCityRuleExceed();
                Boolean isBookRuleExceed = airFlightRuleCheckRes.getIsBookRuleExceed();

                // 获取校验项
                List<AirFlightRuleCheckRes.PerRuleCheckRes> checkOpenList =
                    AirRuleUtils.getCheckOpenList(airFlightRuleCheckRes.getPerRuleCheckResList());
                // 超规项，过滤掉隐藏判断
                List<AirFlightRuleCheckRes.PerRuleCheckRes> exceedList = checkOpenList.stream()
                    .filter(perRuleCheckRes -> perRuleCheckRes.getIsExceed() && !Objects.equals(perRuleCheckRes.getErrCode(),
                        AirRuleMsg.AIR_TRAIN_PRICE_COMPARE.getCode()))
                    .collect(Collectors.toList());

                // 威高逻辑：当企业开启《最低价》消费规则且《最低价》超规措施为禁止下单时。订单提交页校验管控规则时，
                // 《最低价》超规了，提示”改订“最低价航班提示。
                exceedList.stream().filter(perRuleCheckRes ->
                    perRuleCheckRes.getHandlerOrder().equals(AirBookCheckChainOrderEnum.LOWEST_PRICE_FLIGHT.getOrder())
                        && perRuleCheckRes.getBlockPriority().equals(ExceedConfigTypeEnum.EXCEED_NOT_ALLOW.getCompareOrder()))
                    .forEach(perRuleCheckRes -> {
                        if (perRuleCheckRes.getMinFlight() != null) {
                            checkResult.setMin_flight(perRuleCheckRes.getMinFlight());
                        }
                    });
                exceedList.stream().filter(perRuleCheckRes ->
                        perRuleCheckRes.getHandlerOrder().equals(AirBookCheckChainOrderEnum.LOWEST_PRICE_FLIGHT.getOrder())
                                && !perRuleCheckRes.getBlockPriority().equals(ExceedConfigTypeEnum.EXCEED_NOT_ALLOW.getCompareOrder()))
                        .forEach(perRuleCheckRes -> {
                            if (perRuleCheckRes.getMinFlight() != null) {
                                //M10V1 低价超规推荐 升级超规个人付预订人模式
                                if((reqContract.getPickFlightFlag() == null || reqContract.getPickFlightFlag()==0) && VersionTool.greaterThanOrEqualTo(clientVersion,"5.2.11")) {
                                    checkResult.setIsShowRecommend(true);
                                    checkResult.setMin_flight(perRuleCheckRes.getMinFlight());
                                }
                            }
                        });

                if (airFlightRuleCheckRes.getIsExceed()) { // 超规
                    checkResult.setIs_exceed(true);
                    if (isGoback) {
                        if (airFlightRuleCheckRes.getGoFlightCheckInfo().isExceed()) {
                            checkResult.getFlightCheckInfoList().add(airFlightRuleCheckRes.getGoFlightCheckInfo());
                        }
                        if (airFlightRuleCheckRes.getBackFlightCheckInfo().isExceed()) {
                            checkResult.getFlightCheckInfoList().add(airFlightRuleCheckRes.getBackFlightCheckInfo());
                        }
                        AirRuleUtils.setFlightErrMsgInfo(checkResult);
                    }

                    // 获取超规枚举
                    ExceedConfigTypeEnum enumByCompareOrder =
                        ExceedConfigTypeEnum.getEnumByCompareOrder(airFlightRuleCheckRes.getExceedConfigType());

                    checkResult.setAmountCompliance(airFlightRuleCheckRes.getCompanyPayPrice());
                    checkResult.setAmountNonCompliance(airFlightRuleCheckRes.getExceedPrice());
                    // 构造返回结果
                    buildCheckExceedResult(checkResult, duringApplyId,
                        airFlightRuleCheckRes.getExceedPrice(),reqContract.getTravel_on_busi_common_req_contract().getInsurance_personal_price(),
                        airFlightRuleCheckRes.getCompanyPayPrice(), travelOnBusiParameterReqContract,
                        exceedList, enumByCompareOrder, isCityRuleExceed, isBookRuleExceed,
                        reqContract.getTravel_on_busi_parameter_req_contract().getExceed_submit());
                    if(checkResult.getErrCode() != GlobalResponseCode.Success.getCode()) {
                        CurrencyErrorMessage currencyErrorMessage = checkResult.getCurrencyErrorMessage();
                        //判断一下是否是 订单审批 还是超规审批 查看历史有没有审批通过的审批单，如果有直接返回前端标识， 二次提交放行
                        if (GlobalResponseCode.CheckOrderApproval.getCode() == currencyErrorMessage.getCode()
                                || GlobalResponseCode.ExceedApprovalOrPartPayPriceDesc.getCode() == currencyErrorMessage.getCode()) {
                            if (StringUtils.isNotBlank(duringApplyId)) {
                                //二次订单审批 直接放行
                                if (!travelOnBusiParameterReqContract.getExist_center_apply_submit()) {
                                    logger.info("符合条件无需审批，弹窗提示");
                                    checkResult.setErrCode(GlobalResponseCode.OrderCheckApplytAirHint.getCode());
                                    checkResult.setErrMsg(GlobalResponseCode.OrderCheckApplytAirHint.getMsg());
                                    return checkResult;
                                } else {
                                    logger.info("符合条件无需审批，返回审批单id和超规标识");
                                    checkResult.setDuring_reapply_id(duringApplyId);
                                    checkResult.setResCode(GlobalResponseCode.Success);
                                    if (GlobalResponseCode.ExceedApprovalOrPartPayPriceDesc.getCode() == currencyErrorMessage.getCode()) {
                                        checkResult.setCompanyPayPrice(airFlightRuleCheckRes.getCompanyPayPrice().add(airFlightRuleCheckRes.getExceedPrice()));
                                        checkResult.setPersonalPayPrice(BigDecimal.ZERO);
                                        checkResult.setPersonalPay(false);
//                                        for (PerTicketInfoRes perTicketInfoRes : checkResult.getTicket_price_info_list()) {
//                                            // 公司支付金额 票价+保险-优惠券
//                                            perTicketInfoRes.setCompany_pay_price(perTicketInfoRes.getCompany_pay_price().add(perTicketInfoRes.getPersonal_pay_price()));
//                                            perTicketInfoRes.setPersonal_pay_price(BigDecimal.ZERO);
//                                        }
                                    }
                                    return checkResult;
                                }
                            }
                        }

                        //超规二次提交
                        if(reqContract.getTravel_on_busi_parameter_req_contract().getExceed_submit()){
                            //二次提交且标记成全额个人支付后
                            if ((GlobalResponseCode.PartPayPriceDesc.getCode() == currencyErrorMessage.getCode()
                                    || GlobalResponseCode.AllPayPriceDesc.getCode() == currencyErrorMessage.getCode()
                                    || GlobalResponseCode.ExceedApprovalOrPartPayPriceDesc.getCode() == currencyErrorMessage.getCode())
                                && Objects.equals(reqContract.getTravel_on_busi_parameter_req_contract()
                                .getExceed_personalpay_sumbit(), true)) {
                                // 设置成功结果
                                checkResult.setResCode(GlobalResponseCode.Success);
                            } else if (GlobalResponseCode.ExceedApprovalOrPartPayPriceDesc.getCode() == currencyErrorMessage.getCode() && !Objects.equals(reqContract.getTravel_on_busi_parameter_req_contract()
                                    .getExceed_personalpay_sumbit(), true)) {
                                // 设置成功结果
                                checkResult.setResCode(GlobalResponseCode.Success);
                                checkResult.setCompanyPayPrice(airFlightRuleCheckRes.getCompanyPayPrice());
                                checkResult.setPersonalPay(false);
                                checkResult.setPersonalPayPrice(BigDecimal.ZERO);
                                checkResult.setCurrencyErrorMessage(currencyErrorMessage);
                            } else if (GlobalResponseCode.ExceedAirReason.getCode() == currencyErrorMessage.getCode()) {
                                //填写理由校验 填写项是否正常
                                TravelOnBusiOrderRuleCheckResult travelOnBusiOrderRuleCheckResult = iOrderCheckService.checkExceedAuthV2(reqContract, CategoryTypeEnum.Air);
                                if (travelOnBusiOrderRuleCheckResult.getErrCode() != GlobalResponseCode.Success.getCode()) {
                                    checkResult.setCurrencyErrorMessage(currencyErrorMessage);
                                    checkResult.setErrCode(travelOnBusiOrderRuleCheckResult.getErrCode());
                                    checkResult.setErrMsg(travelOnBusiOrderRuleCheckResult.getErrMsg());
                                } else {
                                    checkResult.setCurrencyErrorMessage(currencyErrorMessage);
                                    checkResult.setResCode(GlobalResponseCode.Success);
                                }
                            } else if (GlobalResponseCode.CheckOrderApproval.getCode() == currencyErrorMessage.getCode()) {
                                checkResult.setCurrencyErrorMessage(currencyErrorMessage);
                                checkResult.setResCode(GlobalResponseCode.Success);
                            }
                            // 填写理由+个人支付
                            else if (GlobalResponseCode.PartPayPriceDescWithReason.getCode() == currencyErrorMessage.getCode()
                                    || GlobalResponseCode.AllPayPriceDescWithReason.getCode() == currencyErrorMessage.getCode()) {
                                checkResult.setPersonalPay(true);
                                //填写理由校验 填写项是否正常
                                TravelOnBusiOrderRuleCheckResult travelOnBusiOrderRuleCheckResult = iOrderCheckService
                                    .checkExceedAuthV2(reqContract, CategoryTypeEnum.Air);
                                if (travelOnBusiOrderRuleCheckResult.getErrCode()!=GlobalResponseCode.Success.getCode()) {
                                    checkResult.setCurrencyErrorMessage(currencyErrorMessage);
                                    checkResult.setErrCode(travelOnBusiOrderRuleCheckResult.getErrCode());
                                    checkResult.setErrMsg(travelOnBusiOrderRuleCheckResult.getErrMsg());
                                } else {
                                    // 标记成全额个人支付后
                                    if (Objects.equals(reqContract.getTravel_on_busi_parameter_req_contract()
                                        .getExceed_personalpay_sumbit(), true)) {
                                        checkResult.setResCode(GlobalResponseCode.Success);
                                    }
                                    checkResult.setCurrencyErrorMessage(currencyErrorMessage);
                                }
                            }
                        }
                    }

                } else { // 未超规
                    if (airOrderVerifyFlag) { // 开启订单审批逻辑
                        // 未超规，但是开启了订单审批开关
                        if (StringUtils.isNotBlank(duringApplyId)) {
                            if (!travelOnBusiParameterReqContract.getExist_center_apply_submit()) {
                                logger.info("符合条件无需审批，弹窗提示");
                                checkResult.setErrCode(GlobalResponseCode.OrderCheckApplytAirHint.getCode());
                                checkResult.setErrMsg(GlobalResponseCode.OrderCheckApplytAirHint.getMsg());
                            } else {
                                logger.info("符合条件无需审批，返回审批单id和超规标识");
                                checkResult.setDuring_reapply_id(duringApplyId);
                            }
                            checkResult.setIs_exceed(false);
                            checkResult.setPersonnelInfoList(personnelInfoList);
                            return checkResult;
                        } else {
                            currencyCheckService.setOrderApproval(true, checkResult);
                        }
                    }
                    // 未开启订单审批直接通过
                }
            } else { // 原逻辑
                //消费规则校验
                if(!employeeAirRule.getMulti_rule_switch()){
                    //获取当前预定人规则id
                    ruleId = ObjUtils.ifNull(employeeAirRule.getManual_air_rule_id(), employeeAirRule.getDefault_air_rule_id());
                    personnelInfo.setRule_id(ruleId);
                }
                AirRule airRule = airRuleMapper.selectByPrimaryKey(ruleId);
                personnelInfo.setRule_name(airRule.getName());
                personnelInfoList.add(personnelInfo);
                checkResult.setPersonnelInfoList(personnelInfoList);
                logger.info("airRule:{}", JsonUtils.toJson(airRule));
                checkResult.setAirRule(airRule);
                snapshotInfo.put("ruleInfo", airRule);
                if (airRule == null && employeeAirRule.getAir_rule_flag()) {
                    checkResult.setResCode(GlobalResponseCode.OrderCheckRuleNotExist);
                    return checkResult;
                }
                Integer lowPriceFlag = airRule.getLowPriceFlag();
                //开启最低价限制并且是老版本提示升级
                if (lowPriceFlag == 1 && VersionTool.compare(clientVersion, "3.9.1") < 0) {
                    throw new SaasException(GlobalResponseCode.ApplyCenterAlert);
                }
                //超标规则（1：禁止 2：超规填写理由下单 3：超规需要提交订单审批）
                Integer exceedBuyType = employeeAirRule.getExceed_buy_type();
                // 定义最低价实体
                List<TravelOnBusiOrderCheckResContract.AirLowestPriceMsg> airLowestPriceMsg = new ArrayList<>();

                //机票规则校验主逻辑（未升级超规个人付版本）
                List<TravelOnBusiOrderRuleCheckResult> travelOnBusiOrderRuleCheckResultList = checkAirExceedType(airRule, reqContract, token, airLowestPriceMsg);
                if(ObjUtils.toBoolean(reqContract.getLowPriceRecommend(), Boolean.FALSE) || Objects.equals(reqContract.getPickFlightFlag(),1)){
                    logger.info("已经选择了最低价2");
                    airLowestPriceMsg = new ArrayList<>();
                    TravelOnBusiOrderCheckResContract.AirLowestPriceMsg airLowestPriceMsg1 = new TravelOnBusiOrderCheckResContract.AirLowestPriceMsg();
                    airLowestPriceMsg1.setMinPriceFlag(true);
                    airLowestPriceMsg1.setLowPriceTime(airRule.getLowPriceTime());
                    airLowestPriceMsg1.setLowPriceUnit(airRule.getLowPriceUnit());
                    airLowestPriceMsg1.setLowPriceFlag(airRule.getLowPriceFlag());
                    airLowestPriceMsg1.setFilterStopoverFlightFlag(airRule.getFilterStopoverFlightFlag());
                    AirInterceptRecordContract orderParameterJson = reqContract.getOrder_parameter_json();
                    if (AirPortCityEnum.FIGHT_LIMIT.getCode() == airRule.getAirPortCityFlag()) { //限制相同出发和到达航班
                        airLowestPriceMsg1.setStartPortCode(orderParameterJson.getStarting_code());
                        airLowestPriceMsg1.setEndPortCode(orderParameterJson.getDestination_code());
                    } else if (AirPortCityEnum.TAKEOFF_AND_LANDING_CITY.getCode() == airRule.getAirPortCityFlag()) { //限制到达航班
                        airLowestPriceMsg1.setEndPortCode(orderParameterJson.getDestination_code());
                    } else if (AirPortCityEnum.LANDING_CITY_LIMIT.getCode() == airRule.getAirPortCityFlag()){ //限制出发航班
                        airLowestPriceMsg1.setStartPortCode(orderParameterJson.getStarting_code());
                    }
                    airLowestPriceMsg.add(airLowestPriceMsg1);
                }
                checkResult.setAirLowestPriceMsgList(airLowestPriceMsg);

                logger.info("travelOnBusiOrderRuleCheckResultList:{}", JsonUtils.toJson(travelOnBusiOrderRuleCheckResultList));
                Boolean isPriceExceed = false;
                BigDecimal ruleAmountCompliance = totalPrice;
                boolean isOnlyLowPriceExceed = false;
                if (CollectionUtils.isNotEmpty(travelOnBusiOrderRuleCheckResultList) && travelOnBusiOrderRuleCheckResultList.size() == 1) {
                    if (travelOnBusiOrderRuleCheckResultList.get(0).getErrCode() == GlobalResponseCode.OrderCheckAirMinPriceNoAuth.getCode()) {
                        checkResult.setMin_flight(travelOnBusiOrderRuleCheckResultList.get(0).getMin_flight());
                        isOnlyLowPriceExceed = true;
                    }
                    if (travelOnBusiOrderRuleCheckResultList.get(0).getIsPriceExceed() != null && travelOnBusiOrderRuleCheckResultList.get(0).getIsPriceExceed()) {
                        isPriceExceed = true;
                        ruleAmountCompliance = travelOnBusiOrderRuleCheckResultList.get(0).getAmountCompliance();
                        BigDecimal ruleAmountNonCompliance = travelOnBusiOrderRuleCheckResultList.get(0).getAmountNonCompliance();
                        logger.info("[机票下单校验]，规则合规金额:{}, 规则超规金额:{}", ruleAmountCompliance, ruleAmountNonCompliance);
                    }
                }
                // 超规/合规金额处理
                if (CollectionUtils.isNotEmpty(travelOnBusiOrderRuleCheckResultList)) {
                    if (isPriceExceed) {
                        checkResult.setAmountCompliance(checkResult.getAmountCompliance().min(ruleAmountCompliance));
                        checkResult.setAmountNonCompliance(totalPrice.subtract(checkResult.getAmountCompliance()));
                    } else {
                        checkResult.setAmountCompliance(BigDecimal.ZERO);
                        checkResult.setAmountNonCompliance(totalPrice);
                    }
                    logger.info("[机票下单校验]，合规金额:{}, 超规金额:{}", checkResult.getAmountCompliance(), checkResult.getAmountNonCompliance());
                }
                //超标强制提示
                TravelOnBusiOrderRuleCheckResult checkExceedAuthResult = iOrderCheckService.checkExceedAuthV2(reqContract, CategoryTypeEnum.Air);
                logger.info("checkExceedAuthResult:{}", JsonUtils.toJson(checkExceedAuthResult));
                //判断是否开启订单审批
                TravelOnBusiOrderCheckReqV2Contract.TravelOnBusiParameterReqContract travelOnBusiParameterReqContract = reqContract.getTravel_on_busi_parameter_req_contract();

                logger.info("airOrderVerifyFlag:{}", airOrderVerifyFlag);
                if (airOrderVerifyFlag) {
                    String duringApplyId = iOrderCheckService.queryOrderApplyMsg(reqContract.getTravel_on_busi_parameter_req_contract(), reqContract.getTravel_on_busi_common_req_contract(), CategoryTypeEnum.Air, false, null);
                    logger.info("duringApplyId--1:{},exceedBuyType:{}", duringApplyId,exceedBuyType);
                    //禁止下单（超标需要理由>异常数据情况）
                    if ((TravelExceedType.NotAllowed.getCode() == exceedBuyType || TravelExceedType.AllowedNeedReason.getCode() == exceedBuyType) &&
                        CollectionUtils.isNotEmpty(travelOnBusiOrderRuleCheckResultList)) {
                        //增加返回字段
                        iOrderCheckService.setErrMsgInfoNotAllowed(checkResult, travelOnBusiOrderRuleCheckResultList);
                        return checkResult;

                    } else if (TravelExceedType.AllowedNeedApply.getCode() == exceedBuyType && CollectionUtils.isNotEmpty(travelOnBusiOrderRuleCheckResultList)) {
                        logger.info("国内飞机超规则，走订单审批,reqContract.getFlight_type():{}",reqContract.getFlight_type());

                        if (reqContract.getFlight_type()== TripType.oneWay.getValue() || reqContract.getFlight_type()== TripType.goAndBack.getValue()){
                            duringApplyId = iOrderCheckService.queryOrderApplyMsg(reqContract.getTravel_on_busi_parameter_req_contract(), reqContract.getTravel_on_busi_common_req_contract(), CategoryTypeEnum.Air, true, null);
                            logger.info("duringApplyId--2:{},travelOnBusiParameterReqContract.getExist_center_apply_submit():{}", duringApplyId,travelOnBusiParameterReqContract.getExist_center_apply_submit());
                            if (StringUtils.isNotBlank(duringApplyId) && VersionTool.greaterThanOrEqualTo(clientVersion, "4.9.5")) {
                                if (!travelOnBusiParameterReqContract.getExist_center_apply_submit()) {
                                    logger.info("符合条件无需审批，弹窗提示");
                                    checkResult.setErrCode(GlobalResponseCode.OrderCheckExceedApplytAirHint.getCode());
                                    checkResult.setErrMsg(GlobalResponseCode.OrderCheckExceedApplytAirHint.getMsg());
                                    checkResult.setIs_exceed(true);
                                    return checkResult;
                                } else {
                                    logger.info("符合条件无需审批，返回审批单id和超规标识");
                                    checkResult.setDuring_reapply_id(duringApplyId);
                                    checkResult.setIs_exceed(true);
                                    return checkResult;
                                }
                            } else {
                                //M10V1 未升级超规个人付版本预订人模式 开启订单审批
                                //如果不是超规禁止下单也需要出现推荐页面，所以需要先返回一个特殊状态码，如果前端用户仍然不选择最低价还是选择原单，则判断超规控制是走订单审批还是个人付走原逻辑提示
                                //增加返回
                                if(!isGoback && isOnlyLowPriceExceed && (reqContract.getPickFlightFlag() == null || reqContract.getPickFlightFlag()==0) && VersionTool.greaterThanOrEqualTo(clientVersion,"5.2.11")){
                                    iOrderCheckService.setErrMsgInfoHasRecommend(checkResult, travelOnBusiOrderRuleCheckResultList);
                                    return checkResult;
                                }else {
                                    iOrderCheckService.setErrMsgInfoForCheckApply(checkResult, travelOnBusiOrderRuleCheckResultList);
                                    checkResult.setErrCode(GlobalResponseCode.OrderCheckApplyExceedMore.getCode());
                                    checkResult.setIs_exceed(true);
                                    return checkResult;
                                }
                            }
                        }
                    }
                    //未超规，但是开启了订单审批开关
                    if (reqContract.getFlight_type()== TripType.oneWay.getValue() || reqContract.getFlight_type()== TripType.goAndBack.getValue()){
                        if (StringUtils.isNotBlank(duringApplyId) && VersionTool.greaterThanOrEqualTo(clientVersion, "4.9.5")) {
                            if (!travelOnBusiParameterReqContract.getExist_center_apply_submit()) {
                                logger.info("符合条件无需审批，弹窗提示");
                                checkResult.setErrCode(GlobalResponseCode.OrderCheckApplytAirHint.getCode());
                                checkResult.setErrMsg(GlobalResponseCode.OrderCheckApplytAirHint.getMsg());
                                checkResult.setIs_exceed(false);
                                return checkResult;
                            } else {
                                logger.info("符合条件无需审批，返回审批单id和超规标识");
                                checkResult.setDuring_reapply_id(duringApplyId);
                                checkResult.setIs_exceed(false);
                                return checkResult;
                            }
                        } else {
                            iOrderCheckService.airOrderApplyCheckRes(checkResult, airOrderVerifyFlag, GlobalResponseCode.OrderCheckApplyExceedLess);
                        }
                    }
                }
                //未开启订单审批，执行之前的逻辑（新老版本兼容）
                else {
                    logger.info("else  exceedBuyType :{}，checkExceedAuthResult.getErrCode():{}",exceedBuyType,checkExceedAuthResult.getErrCode());
                    //超标禁止下单
                    if (TravelExceedType.NotAllowed.getCode() == exceedBuyType && CollectionUtils.isNotEmpty(travelOnBusiOrderRuleCheckResultList)) {
                        String version = "1.9.4";
                        if (VersionTool.compare(clientVersion, version) < 0) {
                            return travelOnBusiOrderRuleCheckResultList.get(0);
                        } else {
                            //增加返回字段
                            iOrderCheckService.setErrMsgInfoNotAllowed(checkResult, travelOnBusiOrderRuleCheckResultList);
                            return checkResult;
                        }
                    }
                    //超标需要理由
                    else if (TravelExceedType.AllowedNeedReason.getCode() == exceedBuyType && GlobalResponseCode.Success.getCode() != checkExceedAuthResult.getErrCode() &&
                        CollectionUtils.isNotEmpty(travelOnBusiOrderRuleCheckResultList)) {
                        //M10V1 未升级超规个人付版本预订人模式 没有开启订单审批
                        if(!isGoback && isOnlyLowPriceExceed && (reqContract.getPickFlightFlag() == null || reqContract.getPickFlightFlag()==0) && VersionTool.greaterThanOrEqualTo(clientVersion,"5.2.11")){
                            log.info("pickFlightFlag",reqContract.getPickFlightFlag());
                            iOrderCheckService.setErrMsgInfoHasRecommend(checkResult, travelOnBusiOrderRuleCheckResultList);
                            return checkResult;
                        }else {
                            //增加返回字段
                            iOrderCheckService.setErrMsgInfoForReason(travelOnBusiOrderRuleCheckResultList, checkExceedAuthResult);
                            //checkExceedAuthResult.setErrCode(GlobalResponseCode.OrderCheckExceedNeedReason.getCode());
                            //超规则返回
                            checkExceedAuthResult.setIs_exceed(true);
                            return checkExceedAuthResult;
                        }
                    } else if (!advancePayment && (TravelExceedType.AllowedNeedApply.getCode() == exceedBuyType && CollectionUtils.isNotEmpty(travelOnBusiOrderRuleCheckResultList)) &&
                        !iOrderCheckService.checkExceedApplyAuthV2(reqContract)) {
                        if (reqContract.getFlight_type() == TripType.oneWay.getValue() || reqContract.getFlight_type() == TripType.goAndBack.getValue()) {
                            String duringApplyId = iOrderCheckService.queryOrderApplyMsg(reqContract.getTravel_on_busi_parameter_req_contract(), reqContract.getTravel_on_busi_common_req_contract(), CategoryTypeEnum.Air, true, null);
                            logger.info("duringApplyId--3:{},travelOnBusiParameterReqContract.getExist_center_apply_submit():{}", duringApplyId,travelOnBusiParameterReqContract.getExist_center_apply_submit());

                            if (StringUtils.isNotBlank(duringApplyId) && VersionTool.greaterThanOrEqualTo(clientVersion, "4.9.5")) {
                                if (!travelOnBusiParameterReqContract.getExist_center_apply_submit()) {
                                    logger.info("符合条件无需审批，弹窗提示");
                                    checkResult.setErrCode(GlobalResponseCode.OrderCheckExceedApplytAirHint.getCode());
                                    checkResult.setErrMsg(GlobalResponseCode.OrderCheckExceedApplytAirHint.getMsg());
                                    checkResult.setIs_exceed(true);
                                    return checkResult;
                                } else {
                                    logger.info("符合条件无需审批，返回审批单id和超规标识");
                                    checkResult.setDuring_reapply_id(duringApplyId);
                                    checkResult.setIs_exceed(true);
                                    return checkResult;
                                }
                            } else {
                                logger.info("新版本为开启订单审批国内飞机超规则，走订单审批");
                                //M10V1 未升级超规个人付版本预订人模式 没有开启订单审批
                                if(!isGoback && isOnlyLowPriceExceed && (reqContract.getPickFlightFlag() == null || reqContract.getPickFlightFlag()==0) && VersionTool.greaterThanOrEqualTo(clientVersion,"5.2.11")){
                                    log.info("pickFlightFlag",reqContract.getPickFlightFlag());
                                    iOrderCheckService.setErrMsgInfoHasRecommend(checkResult, travelOnBusiOrderRuleCheckResultList);
                                    return checkResult;
                                }else {
                                    iOrderCheckService.setErrMsgInfoForCheckApply(checkResult, travelOnBusiOrderRuleCheckResultList);
                                    checkResult.setErrCode(GlobalResponseCode.OrderCheckApplyExceedMore.getCode());
                                    checkResult.setIs_exceed(true);
                                    return checkResult;
                                }
                            }
                        }
                    }
                }
            }
        } else {
            if (!advancePayment) {
                checkResult.setPersonnelInfoList(personnelInfoList);
                //没有限制规则，但是开启了订单审批
                logger.info("没有限制规则，但是开启了订单审批 getFlight_type():{}",reqContract.getFlight_type());

                if (reqContract.getFlight_type()== TripType.oneWay.getValue() || reqContract.getFlight_type()== TripType.goAndBack.getValue()){
                    TravelOnBusiOrderCheckReqV2Contract.TravelOnBusiParameterReqContract travelOnBusiParameterReqContract = reqContract.getTravel_on_busi_parameter_req_contract();
                    String duringApplyId = iOrderCheckService.queryOrderApplyMsg(reqContract.getTravel_on_busi_parameter_req_contract(), reqContract.getTravel_on_busi_common_req_contract(), CategoryTypeEnum.Air, false, null);

                    logger.info("duringApplyId--4:{}，travelOnBusiParameterReqContract.getExist_center_apply_submit():{}",duringApplyId,travelOnBusiParameterReqContract.getExist_center_apply_submit());
                    if (StringUtils.isNotBlank(duringApplyId) && VersionTool.greaterThanOrEqualTo(clientVersion, "4.9.5")) {
                        if (!travelOnBusiParameterReqContract.getExist_center_apply_submit()) {
                            logger.info("符合条件无需审批，弹窗提示");
                            checkResult.setErrCode(GlobalResponseCode.OrderCheckApplytAirHint.getCode());
                            checkResult.setErrMsg(GlobalResponseCode.OrderCheckApplytAirHint.getMsg());
                            checkResult.setIs_exceed(false);
                            return checkResult;
                        } else {
                            logger.info("符合条件无需审批，返回审批单id和超规标识");
                            checkResult.setDuring_reapply_id(duringApplyId);
                            checkResult.setIs_exceed(false);
                            return checkResult;
                        }
                    } else {
                        // 开启超规付，并且版本大于等于5.1.10
                        if (isOpenExceedConfig && VersionTool.compare(clientVersion,
                            AirRuleConstant.AIR_EXCEED_PERSONAL_PAY_CLIENT_VERSION) >= 0) { // 超规付新版弹窗
                            currencyCheckService.setOrderApproval(airOrderVerifyFlag, checkResult);
                        } else {
                            iOrderCheckService.airOrderApplyCheckRes(checkResult, airOrderVerifyFlag, GlobalResponseCode.OrderCheckApplyExceedLess);
                        }
                    }
                }
            }
        }

        logger.info("机票订单校验结果：ruleCheckResult: {}", JsonUtils.toJson(checkResult));
        checkResult.setPersonnelInfoList(personnelInfoList);
        log.info("personnelInfoList:{}",JsonUtils.toJson(personnelInfoList));
        return checkResult;
    }

    private void buildCheckExceedResult(TravelOnBusiOrderRuleCheckResult checkResult,
        String duringApplyId, BigDecimal personalPayPrice,BigDecimal insurancePersonalPayPrice, BigDecimal companyPayPrice,
        TravelOnBusiOrderCheckReqV2Contract.TravelOnBusiParameterReqContract travelOnBusiParameterReqContract,
        List<AirFlightRuleCheckRes.PerRuleCheckRes> exceedList,
        ExceedConfigTypeEnum enumByCompareOrder, boolean isCityRuleExceed, boolean isBookRuleExceed,
        boolean exceedSubmit) {

        List<CurrencyMsg> currencyMsgList = new ArrayList<>();
        for (AirFlightRuleCheckRes.PerRuleCheckRes perRuleCheckRes : exceedList) {
            CurrencyMsg currencyMsg = new CurrencyMsg();
            currencyMsg.setCode(perRuleCheckRes.getErrCode());
            currencyMsg.setErrMsg(perRuleCheckRes.getErrMsg());
            currencyMsgList.add(currencyMsg);
        }

        checkResult.setIs_exceed(true); // 默认设置超规
        if (Objects.equals(enumByCompareOrder, ExceedConfigTypeEnum.EXCEED_NOT_ALLOW)) { // 禁止下单
            if (Objects.equals(isCityRuleExceed, true)) {
                iOrderCheckService.setErrMsgInfo(checkResult, currencyMsgList,
                    GlobalResponseCode.CityRuleForbidden.getCode(), null, exceedSubmit);
            }
            if (Objects.equals(isBookRuleExceed, true)) {
                iOrderCheckService.setErrMsgInfo(checkResult, currencyMsgList,
                    GlobalResponseCode.CheckResults.getCode(), null, exceedSubmit);
            }
        } else if (Objects.equals(enumByCompareOrder,
            ExceedConfigTypeEnum.INDIVIDUAL_PAY_DIFFERENCE_AMOUNT)) { // 需个人支付相同行程下飞机票与火车票的差值金额
            checkResult.setCompanyPayPrice(companyPayPrice);
            checkResult.setPersonalPayPrice(personalPayPrice);
            iOrderCheckService.setErrMsgInfo(checkResult, currencyMsgList,
                GlobalResponseCode.CityRulePay.getCode(), String.valueOf(personalPayPrice.add(insurancePersonalPayPrice)), exceedSubmit);
        } else if (Objects.equals(enumByCompareOrder,
            ExceedConfigTypeEnum.COMPANY_PAY_SUBMIT_APPROVAL)) { // 企业支付 - 提交超规订单审批
            checkResult.setCompanyPayPrice(companyPayPrice);
            checkResult.setPersonalPayPrice(personalPayPrice);
            iOrderCheckService.setErrMsgInfo(checkResult, currencyMsgList,
                GlobalResponseCode.CheckOrderApproval.getCode(), null, exceedSubmit);
        } else if (Objects.equals(enumByCompareOrder, ExceedConfigTypeEnum.COMPANY_PAY_BY_REASON)) { // 企业支付 - 填写超规理由报备
            checkResult.setCompanyPayPrice(companyPayPrice);
            checkResult.setPersonalPayPrice(personalPayPrice);
            iOrderCheckService.setErrMsgInfo(checkResult, currencyMsgList,
                GlobalResponseCode.CheckExceedReason.getCode(), String.valueOf(personalPayPrice.add(insurancePersonalPayPrice)), exceedSubmit);
        } else if (Objects.equals(enumByCompareOrder, ExceedConfigTypeEnum.EMPLOYEE_PAY_PART)) { // 员工支付超规部分 - 无需审批、无需报备

            checkResult.setCompanyPayPrice(companyPayPrice);
            checkResult.setPersonalPayPrice(personalPayPrice);
            iOrderCheckService.setErrMsgInfo(checkResult, currencyMsgList,
                GlobalResponseCode.CheckPersonalPay.getCode(), String.valueOf(personalPayPrice.add(insurancePersonalPayPrice)), exceedSubmit);

        } else if (Objects.equals(enumByCompareOrder, ExceedConfigTypeEnum.EMPLOYEE_PAY_PART_BY_REASON)) { // 员工支付超规部分 - 填写超规理由报备
            checkResult.setCompanyPayPrice(companyPayPrice);
            checkResult.setPersonalPayPrice(personalPayPrice);
            iOrderCheckService.setErrMsgInfo(checkResult, currencyMsgList,
                GlobalResponseCode.CheckExceedReasonAndPersonalPayPart.getCode(), String.valueOf(personalPayPrice), exceedSubmit);
        } else if (Objects.equals(enumByCompareOrder, ExceedConfigTypeEnum.EMPLOYEE_PAY_ALL)) { // 员工支付全部费用 - 无需审批、无需报备
            iOrderCheckService.setErrMsgInfo(checkResult, currencyMsgList,
                GlobalResponseCode.CheckPersonalPayAll.getCode(), String.valueOf(personalPayPrice.add(insurancePersonalPayPrice)), exceedSubmit);
            checkResult.setCompanyPayPrice(BigDecimal.ZERO);
            checkResult.setPersonalPayPrice(personalPayPrice);
        } else if (Objects.equals(enumByCompareOrder,
            ExceedConfigTypeEnum.EMPLOYEE_PAY_ALL_BY_REASON)) { // 员工支付全部费用 - 填写超规理由报备
            checkResult.setCompanyPayPrice(BigDecimal.ZERO);
            checkResult.setPersonalPayPrice(personalPayPrice);
            iOrderCheckService.setErrMsgInfo(checkResult, currencyMsgList,
                GlobalResponseCode.CheckExceedReasonAndPersonalPay.getCode(), String.valueOf(personalPayPrice.add(insurancePersonalPayPrice)), exceedSubmit);
        } else if (Objects.equals(enumByCompareOrder, ExceedConfigTypeEnum.EXCEED_SUBMIT_APPROVAL_OR_PERSONAL_PAY)) { // 员工支付超规部分或提交订单审批
            checkResult.setCompanyPayPrice(companyPayPrice);
            checkResult.setPersonalPayPrice(personalPayPrice);
            iOrderCheckService.setErrMsgInfo(checkResult, currencyMsgList,
                    GlobalResponseCode.ExceedApprovalOrPartPayPriceDesc.getCode(), String.valueOf(personalPayPrice.add(insurancePersonalPayPrice)), exceedSubmit);
        } else if (Objects.equals(enumByCompareOrder,
            ExceedConfigTypeEnum.EXCEED_SUBMIT_APPROVAL_OR_PAY_DIFFERENCE_AMOUNT)) { // 员工支付机火差价或提交订单审批
            checkResult.setCompanyPayPrice(companyPayPrice);
            checkResult.setPersonalPayPrice(personalPayPrice);
            iOrderCheckService.setErrMsgInfo(checkResult, currencyMsgList,
                GlobalResponseCode.ExceedApprovalOrPartPayPriceDesc.getCode(), String.valueOf(personalPayPrice.add(insurancePersonalPayPrice)), exceedSubmit);
        } else {
            throw new SaasException(GlobalResponseCode.InnerError.getCode(), "超规措施配置有误", 7);
        }
    }

    /**
     * 设置优惠券金额使用情况
     * @param orderPrice
     * @param couponAmount
     * @param checkResult
     * @param personalPayPrice
     */
    private void setCouponUsed(BigDecimal orderPrice,
        BigDecimal couponAmount, TravelOnBusiOrderRuleCheckResult checkResult, BigDecimal personalPayPrice) {
        if (couponAmount.compareTo(personalPayPrice) > 0) { // 优惠券 > 个人支付
            BigDecimal companyPayPrice = orderPrice.subtract(couponAmount.subtract(personalPayPrice));
            checkResult.setIs_exceed(false);
            checkResult.setCompanyPayPrice(companyPayPrice);
            checkResult.setPersonalPayPrice(BigDecimal.ZERO);
        } else if (couponAmount.compareTo(personalPayPrice) == 0) { // 优惠券 = 个人支付
            checkResult.setIs_exceed(false);
            checkResult.setCompanyPayPrice(orderPrice);
            checkResult.setPersonalPayPrice(BigDecimal.ZERO);
        } else { // 优惠券 < 个人支付
            BigDecimal personalPayPriceSubCoupon = personalPayPrice.subtract(couponAmount); // 个人支付的部分
            BigDecimal companyPayPrice = orderPrice.subtract(personalPayPriceSubCoupon);
            checkResult.setIs_exceed(true);
            checkResult.setCompanyPayPrice(companyPayPrice);
            checkResult.setPersonalPayPrice(personalPayPriceSubCoupon);
        }
        logger.info("setCouponUsed, 是否超规:{}, 公司支付金额:{}, 个人支付金额:{}", checkResult.getIs_exceed(),
            checkResult.getCompanyPayPrice(), checkResult.getPersonalPayPrice());
    }

    /**
     * 校验审批规则信息
     * @param airRuleList
     * @return
     */
    private TravelOnBusiOrderRuleCheckResult checkAirThirdRule(List<ApplyThirdContract.KeyValueItem> airRuleList, TravelOnBusiOrderRuleCheckResult checkResult, AirOrderCheckReqV2Contract reqContract) {
        AirRule airRule = new AirRule();
        airRule.setId("");
        for (ApplyThirdContract.KeyValueItem airInfo : airRuleList) {
            String ruleType = airInfo.getType();
            Object ruleValue = airInfo.getValue();
            if (SaasOrderThirdRuleConstant.AIR_TYPE.equals(ruleType)) {
                airRule.setAirCabinFlag(true);
                String airCabinType = StringUtils.join((List<Integer>) ruleValue, ",");
                airRule.setAirCabinFlag(true);
                airRule.setAirCabinType(airCabinType);
            }
            if (SaasOrderThirdRuleConstant.AIR_PRICE.equals(ruleType)) {
                airRule.setAirPriceFlag(true);
                airRule.setAirUnitPrice(ObjUtils.toBigDecimal(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.AIR_PRIV_DAY_MAX.equals(ruleType)) {
                airRule.setPrivDayMax(ObjUtils.toInteger(ruleValue));
            }
            if (SaasOrderThirdRuleConstant.AIR_PRIV_DAY_MIN.equals(ruleType)) {
                airRule.setPrivDayMin(ObjUtils.toInteger(ruleValue));
            }
        }
        List<TravelOnBusiOrderCheckResContract.AirLowestPriceMsg> airLowestPriceMsg = new ArrayList<>();
        List<TravelOnBusiOrderRuleCheckResult> travelOnBusiOrderRuleCheckResults = checkAirExceedType(airRule, reqContract, null, airLowestPriceMsg);
        if (CollectionUtils.isNotEmpty(travelOnBusiOrderRuleCheckResults)) {
            //增加返回字段
            iOrderCheckService.setErrMsgInfoNotAllowed(checkResult, travelOnBusiOrderRuleCheckResults);
            return checkResult;
        }
        return checkResult;
    }

    //飞机公用规则校验
    private List<TravelOnBusiOrderRuleCheckResult> checkAirExceedType(AirRule airRule, AirOrderCheckReqV2Contract reqContract, String token, List<TravelOnBusiOrderCheckResContract.AirLowestPriceMsg> airLowestPriceMsg) {
        List<TravelOnBusiOrderRuleCheckResult> travelOnBusiOrderRuleCheckResultList = new ArrayList<>();
        int flightType = reqContract.getFlight_type() == null ? 1 : reqContract.getFlight_type();
        boolean isGoAndBack = flightType == 2;
        logger.info("checkAirExceedType:isGoAndBack = {}", isGoAndBack);
        //校验仓位级别
        if (ObjUtils.isNotEmpty(airRule.getAirCabinFlag()) && airRule.getAirCabinFlag() && !airRule.getAirCabinType().contains(reqContract.getAir_type())) {
            TravelOnBusiOrderRuleCheckResult checkResult = new TravelOnBusiOrderRuleCheckResult();
            String codeName = AirCabinType.getName(ObjUtils.toInteger(reqContract.getAir_type()));
            checkResult.setResCode(GlobalResponseCode.OrderCheckAirPositionNoAuth.getCode(), StrUtils.formatString(GlobalResponseCode.OrderCheckAirPositionNoAuth.getMsg(), codeName));
            checkResult.setType(1);
            travelOnBusiOrderRuleCheckResultList.add(checkResult);
        }
        //校验单张机票价格
        Integer couponExceedPriceSetting = iOrderCheckService.queryCouponExceedPriceSetting(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        BigDecimal couponAmount = reqContract.getCouponInfo().getDeductionCouponAmount(couponExceedPriceSetting);
        log.info("couponAmount: {}, price: {}, airUnitPrice:{}", couponAmount, reqContract.getPrice(), airRule.getAirUnitPrice());
        if (!org.springframework.util.StringUtils.isEmpty(airRule.getAirUnitPrice()) && airRule.getAirPriceFlag() && airRule.getAirUnitPrice().compareTo(reqContract.getPrice()) < 0) {
            TravelOnBusiOrderRuleCheckResult checkResult = new TravelOnBusiOrderRuleCheckResult();
            checkResult.setResCode(GlobalResponseCode.OrderCheckAirCabinBelowPriceNoAuth.getCode(),
                    StrUtils.formatString(GlobalResponseCode.OrderCheckAirCabinBelowPriceNoAuth.getMsg(),
                            BigDecimalTool.formatMoney(airRule.getAirUnitPrice())));
            checkResult.setType(2);
            checkResult.setIsPriceExceed(true);
            checkResult.setAmountCompliance(airRule.getAirUnitPrice());
            checkResult.setAmountNonCompliance(reqContract.getPrice().subtract(airRule.getAirUnitPrice()));
            travelOnBusiOrderRuleCheckResultList.add(checkResult);
        }
        //校验折扣
        Boolean airDiscountFlag = airRule.getAirDiscountFlag() == null ? true : airRule.getAirDiscountFlag();
        BigDecimal airDiscount = airRule.getAirDiscount() == null ? BigDecimal.ZERO : airRule.getAirDiscount();
        BigDecimal orderDiscount = reqContract.getDiscount() == null ? BigDecimal.ZERO : reqContract.getDiscount().setScale(2, BigDecimal.ROUND_HALF_UP);
        if (!org.springframework.util.StringUtils.isEmpty(airRule.getAirDiscount()) &&
                (airDiscountFlag) && (airDiscount.compareTo(orderDiscount) < 0)) {
            TravelOnBusiOrderRuleCheckResult checkResult = new TravelOnBusiOrderRuleCheckResult();
            String discountStr = airDiscount == null ? "0.0" : formatDiscount(airDiscount);
            checkResult.setType(2);
            checkResult.setResCode(GlobalResponseCode.OrderCheckAirCabinBelowDiscountNoAuth.getCode(), StrUtils.formatString(GlobalResponseCode.OrderCheckAirCabinBelowDiscountNoAuth.getMsg(), discountStr));
            travelOnBusiOrderRuleCheckResultList.add(checkResult);
        }
        String start_time = reqContract.getTravel_on_busi_parameter_req_contract().getStart_time();
        LocalDate startTime = LocalDateTime.ofInstant(DateTimeTool.fromStringToDateTime(start_time).toInstant(), ZoneId.systemDefault()).toLocalDate();
        LocalDate currentTime = LocalDate.now();
        //下单时间与预定时间相差天数
        long daysDiff = ChronoUnit.DAYS.between(currentTime, startTime);
        Integer privDayMin = airRule.getPrivDayMin();
        if (privDayMin != null && daysDiff < privDayMin && privDayMin != 0) {
            logger.info(StrUtils.formatString("申请人违反了「最少提前{0}天预订」的限制", privDayMin));
            TravelOnBusiOrderRuleCheckResult checkResults = new TravelOnBusiOrderRuleCheckResult();
            String msg = StrUtils.formatString(CoreLanguage.TrainCheckServiceImpl_Value_MinDay.getMessage(), privDayMin);
            checkResults.setResCode(GlobalResponseCode.OrderCheckHotelPerDay.getCode(), msg);
            checkResults.setType(3);
            travelOnBusiOrderRuleCheckResultList.add(checkResults);
        }
        Integer privDayMax = airRule.getPrivDayMax();
        if (privDayMax != null && daysDiff > privDayMax && privDayMax != 0) {
            logger.info(StrUtils.formatString("申请人违反了「最多提前{0}天预订」的限制", privDayMax));
            TravelOnBusiOrderRuleCheckResult checkResults = new TravelOnBusiOrderRuleCheckResult();
            String msg = StrUtils.formatString(CoreLanguage.TrainCheckServiceImpl_Value_MaxDay.getMessage(), privDayMax);
            checkResults.setResCode(GlobalResponseCode.OrderCheckHotelPerDay.getCode(), msg);
            checkResults.setType(3);
            travelOnBusiOrderRuleCheckResultList.add(checkResults);
        }
        // 兼容往返逻辑
        if (isGoAndBack) {
            String back_start_time = reqContract.getTravel_on_busi_parameter_req_contract().getBack_start_time();
            LocalDate backStartTime = LocalDateTime.ofInstant(DateTimeTool.fromStringToDateTime(back_start_time).toInstant(), ZoneId.systemDefault()).toLocalDate();
            long backDaysDiff = ChronoUnit.DAYS.between(currentTime, backStartTime);
            if (privDayMin != null && backDaysDiff < privDayMin) {
                logger.info(StrUtils.formatString("申请人违反了「最少提前{0}天预订」的限制", privDayMin));
                TravelOnBusiOrderRuleCheckResult checkResults = new TravelOnBusiOrderRuleCheckResult();
                String msg = StrUtils.formatString(CoreLanguage.TrainCheckServiceImpl_Value_MinDay.getMessage(), privDayMin);
                checkResults.setResCode(GlobalResponseCode.OrderCheckHotelPerDay.getCode(), msg);
                checkResults.setType(3);
                travelOnBusiOrderRuleCheckResultList.add(checkResults);
            }
            if (privDayMax != null && backDaysDiff > privDayMax) {
                logger.info(StrUtils.formatString("申请人违反了「最多提前{0}天预订」的限制", privDayMax));
                TravelOnBusiOrderRuleCheckResult checkResults = new TravelOnBusiOrderRuleCheckResult();
                String msg = StrUtils.formatString(CoreLanguage.TrainCheckServiceImpl_Value_MaxDay.getMessage(), privDayMax);
                checkResults.setResCode(GlobalResponseCode.OrderCheckHotelPerDay.getCode(), msg);
                checkResults.setType(3);
                travelOnBusiOrderRuleCheckResultList.add(checkResults);
            }
        }
        List<String> startCityIds = reqContract.getTravel_on_busi_parameter_req_contract().getStart_city_ids();
        List<String> arrivalCityIds = reqContract.getTravel_on_busi_parameter_req_contract().getArrival_city_ids();
        if (CollectionUtils.isNotEmpty(startCityIds) && CollectionUtils.isNotEmpty(arrivalCityIds)) {
            // 总金额
            BigDecimal orderPrice = ObjUtils.toBigDecimal(reqContract.getTravel_on_busi_common_req_contract().getOrder_price(),
                    reqContract.getTravel_on_busi_common_req_contract().getTotal_price());
            // 去程金额 不包含优惠券
            BigDecimal goTotalAmount = ObjUtils.toBigDecimal(reqContract.getTravel_on_busi_common_req_contract().getGo_total_price(), BigDecimal.ZERO);
            // 返程金额 不包含优惠券
            BigDecimal backTotalAmount = ObjUtils.toBigDecimal(reqContract.getTravel_on_busi_common_req_contract().getBack_total_price(), BigDecimal.ZERO);
            // 处理一单多人的情况
            int size = reqContract.getTravel_on_busi_common_req_contract().getFrequent_id().size();
            if (size > 1) {
                orderPrice = orderPrice.divide(BigDecimal.valueOf(size), 2, RoundingMode.HALF_UP);
                goTotalAmount = goTotalAmount.divide(BigDecimal.valueOf(size), 2, RoundingMode.HALF_UP);
                backTotalAmount = backTotalAmount.divide(BigDecimal.valueOf(size), 2, RoundingMode.HALF_UP);
            }

            TrainInfo trainInfo = getTrainInfo(token, start_time, startCityIds, arrivalCityIds);

            // 行程距离和低价限制
            checkDistanceAndLowerPriceLimit(travelOnBusiOrderRuleCheckResultList, isGoAndBack, goTotalAmount, backTotalAmount,
                    orderPrice, trainInfo, airRule, startCityIds.get(0), arrivalCityIds.get(0));
            // 行程耗时和低价限制
            checkTimeAndLowerPriceLimit(travelOnBusiOrderRuleCheckResultList, isGoAndBack, goTotalAmount, backTotalAmount,
                    orderPrice, trainInfo, airRule);
        }
        //校验机票时段不符合规则
        String clientStartTime = reqContract.getTravel_on_busi_parameter_req_contract().getStart_time();
        //将客户端传入的时间转换成date类型
        Date startDate = DateUtils.parse(clientStartTime);
        String startDayCount = AirRuleUtils.getWeekOfDate(startDate);
        Integer dayType = DayType.getCode(startDayCount);
        AirTimeRangeExample airTimeRangeExampleAll = new AirTimeRangeExample();
        airTimeRangeExampleAll.createCriteria().andRuleIdEqualTo(airRule.getId());
        List<AirTimeRange> airTimeRangesAll = airTimeRangeMapper.selectByExample(airTimeRangeExampleAll);
        log.info("最低价限制airTimeRangesAll:{}", JsonUtils.toJson(airTimeRangesAll));
        List<String> timeRangeList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(airTimeRangesAll)) {

            // 首先判断是否按照节假日的新类型时间范围校验
            List<Integer> newTimeRangeType = Lists.newArrayList(10, 11, 12);
            boolean isNewTimeRangeType = false;
            for (AirTimeRange range : airTimeRangesAll) {
                if (newTimeRangeType.contains(range.getDayType())) {
                    isNewTimeRangeType = true;
                    break;
                }
            }
            List<AirTimeRange> airTimeRanges = Lists.newArrayList();

            // 根据企业日历校验时间范围
            if (isNewTimeRangeType) {
                String companyId = reqContract.getTravel_on_busi_common_req_contract().getCompany_id();

                dayType = companyCalendarService.getCompanyCalendarDayType(companyId, clientStartTime);
                logger.info("校验时间范围,当前日期类型:{}", dayType);
                boolean result =
                        AirRuleUtils.checkHolidayNotOverNightTimeRange2(airTimeRangesAll, clientStartTime, dayType);
                logger.info("校验时间范围,当天结果:{}", JsonUtils.toJson(result));
                // 往前一天
                Date preDepartureDate = com.fenbeitong.finhub.common.utils.DateUtils.addDay(startDate, -1);
                String preDepartureTime = DateUtils.format(preDepartureDate);
                Integer preDayType = companyCalendarService.getCompanyCalendarDayType(companyId, preDepartureTime);
                boolean preDayResult =
                        AirRuleUtils.checkHolidayOverNightTimeRange2(airTimeRangesAll, preDepartureTime,
                                clientStartTime, preDayType);
                logger.info("校验时间范围,前一天结果:{}", JsonUtils.toJson(preDayResult));
                // 如果当天和往前一天均超规 时间范围项超规
                if (!result && !preDayResult) {
                    TravelOnBusiOrderRuleCheckResult checkResult = new TravelOnBusiOrderRuleCheckResult();
                    checkResult.setResCode(GlobalResponseCode.OrderCheckAirRuleTakeOffTime, GlobalResponseCode.OrderCheckAirRuleTakeOffTime.getMsg());
                    checkResult.setType(4);
                    travelOnBusiOrderRuleCheckResultList.add(checkResult);
                }
                airTimeRanges = airTimeRangesAll;
            }

            Integer backDayType = null;
            if(!isNewTimeRangeType) {
                if (isGoAndBack) {
                    String backClientStartTime = reqContract.getTravel_on_busi_parameter_req_contract().getBack_start_time();
                    Date backStartDate = DateUtils.parse(backClientStartTime);
                    String backStartDayCount = AirRuleUtils.getWeekOfDate(backStartDate);
                    backDayType = DayType.getCode(backStartDayCount);
                }
                AirTimeRangeExample airTimeRangeExample = new AirTimeRangeExample();
                Set<Integer> dayTypeSet = new HashSet<>();
                buildAirTimeRangeExample(dayTypeSet, dayType, airTimeRangeExample, airRule);
                airTimeRanges = airTimeRangeMapper.selectByExample(airTimeRangeExample);
                log.info("去程时间范围:{}", JsonUtils.toJson(airTimeRanges));
                if (CollectionUtils.isEmpty(airTimeRanges) || CollectionUtils.isNotEmpty(airTimeRanges) && !checkoutTimeAuth(dayType, airTimeRanges, reqContract.getTravel_on_busi_parameter_req_contract().getStart_time())) {
                    TravelOnBusiOrderRuleCheckResult checkResult = new TravelOnBusiOrderRuleCheckResult();
                    if (isGoAndBack) {
                        String msg = StrUtils.formatString("去程起飞时段不符合规则");
                        checkResult.setResCode(GlobalResponseCode.OrderCheckAirRuleTakeGoOffTime, msg);
                        checkResult.setType(4);
                        travelOnBusiOrderRuleCheckResultList.add(checkResult);
                    } else {
                        String msg = StrUtils.formatString("起飞时段不符合规则");
                        checkResult.setResCode(GlobalResponseCode.OrderCheckAirRuleTakeOffTime, msg);
                        checkResult.setType(4);
                        travelOnBusiOrderRuleCheckResultList.add(checkResult);
                    }
                }
                if (isGoAndBack) {
                    dayTypeSet.clear();
                    buildAirTimeRangeExample(dayTypeSet, backDayType, airTimeRangeExample, airRule);
                    airTimeRanges = airTimeRangeMapper.selectByExample(airTimeRangeExample);
                    log.info("返程时间范围:{}", JsonUtils.toJson(airTimeRanges));
                    if (CollectionUtils.isEmpty(airTimeRanges) || CollectionUtils.isNotEmpty(airTimeRanges) &&
                            !checkoutTimeAuth(backDayType, airTimeRanges,
                                    reqContract.getTravel_on_busi_parameter_req_contract().getBack_start_time())) {
                        TravelOnBusiOrderRuleCheckResult checkResult = new TravelOnBusiOrderRuleCheckResult();
                        String msg = StrUtils.formatString("返程起飞时段不符合规则");
                        checkResult.setResCode(GlobalResponseCode.OrderCheckAirRuleTakeBackOffTime, msg);
                        checkResult.setType(4);
                        travelOnBusiOrderRuleCheckResultList.add(checkResult);
                    }
                }
            }
            // 最低价限制
            List<String> timeIntervalList = checkLowPriceAuth(dayType, airTimeRanges,
                    reqContract.getTravel_on_busi_parameter_req_contract().getStart_time(), airRule);
            log.info("最低价限制timeIntervalList:{}", JsonUtils.toJson(timeIntervalList));
            if (CollectionUtils.isNotEmpty(timeIntervalList)) {
                List<DateInterval> intervalList = Lists.newArrayList();
                for (String timeIntervalInfo : timeIntervalList) {
                    String[] sectionList = timeIntervalInfo.split("-");
                    intervalList.add(new DateInterval(ObjUtils.toLong(sectionList[0]), ObjUtils.toLong(sectionList[1])));
                }
                List<DateInterval> mergeList = merge(intervalList);
                if (CollectionUtils.isNotEmpty(mergeList)) {
                    for (DateInterval interval : mergeList) {
                        Long lowEndpoint = interval.getStart();
                        Long highEndpoint = interval.getEnd();
                        Date lowDate = new Date(lowEndpoint);
                        Date highDate = new Date(highEndpoint);
                        String lowDateStr = DateTimeTool.fromDateToShortTimeInfoString(lowDate);
                        String highDateStr = DateTimeTool.fromDateToShortTimeInfoString(highDate);
                        timeRangeList.add(lowDateStr + "-" + highDateStr);
                    }
                }
                log.info("最低价限制timeRangeList:{}", JsonUtils.toJson(timeRangeList));
                if (CollectionUtils.isEmpty(timeRangeList)) {
                    return travelOnBusiOrderRuleCheckResultList;
                }
                return queryMinPrice(travelOnBusiOrderRuleCheckResultList, reqContract, timeRangeList, airRule, isGoAndBack, airLowestPriceMsg);
            }
            // todo 代码历史遗留问题，如果需要优化，需跟pm讨论
            // 走不到这块下面往返逻辑。如果是往返，也走上面单程的；本次改动：queryMinPrice()上面的代码改成加入isGoBack参数区分往返和单程，用去程的价格代表往返的校验
            if (isGoAndBack) { // 往返
                List<String> backTimeIntervalList = checkLowPriceAuth(backDayType, airTimeRanges,
                        reqContract.getTravel_on_busi_parameter_req_contract().getBack_start_time(), airRule);
                if (CollectionUtils.isNotEmpty(backTimeIntervalList)) {
                    List<DateInterval> intervalList = Lists.newArrayList();
                    for (String timeIntervalInfo : backTimeIntervalList) {
                        String[] sectionList = timeIntervalInfo.split("-");
                        intervalList.add(new DateInterval(ObjUtils.toLong(sectionList[0]), ObjUtils.toLong(sectionList[1])));
                    }
                    List<DateInterval> mergeList = merge(intervalList);
                    if (CollectionUtils.isNotEmpty(mergeList)) {
                        for (DateInterval interval : mergeList) {
                            Long lowEndpoint = interval.getStart();
                            Long highEndpoint = interval.getEnd();
                            Date lowDate = new Date(lowEndpoint);
                            Date highDate = new Date(highEndpoint);
                            String lowDateStr = DateTimeTool.fromDateToShortTimeInfoString(lowDate);
                            String highDateStr = DateTimeTool.fromDateToShortTimeInfoString(highDate);
                            timeRangeList.add(lowDateStr + "-" + highDateStr);
                        }
                    }
                    log.info("最低价限制timeRangeList:{}", JsonUtils.toJson(timeRangeList));
                    if (CollectionUtils.isEmpty(timeRangeList)) {
                        return travelOnBusiOrderRuleCheckResultList;
                    }
                    return queryGoBackMinPrice(travelOnBusiOrderRuleCheckResultList, reqContract, timeRangeList, airRule);
                }
            }

        } else {
            Integer lowPriceFlag = airRule.getLowPriceFlag();
            if (ObjUtils.isNotEmpty(lowPriceFlag) && lowPriceFlag == 0) {
                return travelOnBusiOrderRuleCheckResultList;
            }
            Integer lowPriceTime = ObjUtils.toInteger(airRule.getLowPriceTime(),0);
            String lowPriceUnit = airRule.getLowPriceUnit();
            //减几小时
            Calendar lowCalendar = Calendar.getInstance();
            lowCalendar.setTime(DateTimeTool.fromStringToDateTime(clientStartTime));
            if(AirLowPriceUnitEnum.MINUTE.getKey().equals(lowPriceUnit)){
                lowCalendar.add(Calendar.MINUTE, -lowPriceTime);
            }else {
                lowCalendar.add(Calendar.HOUR_OF_DAY, -lowPriceTime);
            }
            //加几小时
            Calendar highCalendar = Calendar.getInstance();
            highCalendar.setTime(DateTimeTool.fromStringToDateTime(clientStartTime));
            if(AirLowPriceUnitEnum.MINUTE.getKey().equals(lowPriceUnit)){
                highCalendar.add(Calendar.MINUTE, lowPriceTime);
            }else {
                highCalendar.add(Calendar.HOUR_OF_DAY, lowPriceTime);
            }
            Long highTime = highCalendar.getTime().getTime();
            //获取当天的最晚时间
            Date startDateInfo = DateUtils.parse(clientStartTime);
            String endDayTime = DateTimeTool.fromDateToString(startDateInfo) + " 23:59:59";
            Long endTime = DateUtils.parse(endDayTime).getTime();
            if (highTime > endTime) {
                highTime = endTime;
            }
            String highDateStr = DateTimeTool.fromDateToShortTimeInfoString(new Date(highTime));
            // 获取当天的最早时间
            long lowTime = lowCalendar.getTime().getTime();
            String startDayTime = DateTimeTool.fromDateToString(DateTimeTool.fromStringToDateTime(clientStartTime)) + " 00:00:00";
            long startDateTime = DateUtils.parse(startDayTime).getTime();
            if (lowTime < startDateTime) {
                lowTime = startDateTime;
            }
            String lowDateStr = DateTimeTool.fromDateToShortTimeInfoString(new Date(lowTime));
            timeRangeList.add(lowDateStr + "-" + highDateStr);
            log.info("最低价限制timeRangeList:{}", JsonUtils.toJson(timeRangeList));
            if (CollectionUtils.isEmpty(timeRangeList)) {
                return travelOnBusiOrderRuleCheckResultList;
            }
            return queryMinPrice(travelOnBusiOrderRuleCheckResultList, reqContract, timeRangeList, airRule, isGoAndBack, airLowestPriceMsg);
        }
        return travelOnBusiOrderRuleCheckResultList;
    }

    @Override
    public TrainInfo getTrainInfo(String token, String start_time, List<String> startCityIds,
        List<String> arrivalCityIds) {
        TrainInfo trainInfo = getTrainInfo(start_time.substring(0, 10), startCityIds.get(0), arrivalCityIds.get(0),
            token);
        if (!trainInfo.getQurydata()) {
            if (StringUtils.isEmpty(startCityIds.get(0)) || StringUtils.isEmpty(arrivalCityIds.get(0))) {
                return trainInfo;
            }
            List<AirportAreaCombine> startAirPortList = getAirPortList(startCityIds.get(0));
            List<AirportAreaCombine> arrivalAirPortList = getAirPortList(arrivalCityIds.get(0));

            if (CollectionUtils.isNotEmpty(startAirPortList) && CollectionUtils.isNotEmpty(arrivalAirPortList)) {
                for (AirportAreaCombine start : startAirPortList) {
                    for (AirportAreaCombine arrival : arrivalAirPortList) {
                        trainInfo = getTrainInfo(start_time.substring(0, 10), start.getAreaCode(),
                            arrival.getAreaCode(), token);
                        if (trainInfo.getQurydata()) {
                            break;
                        }
                    }
                    if (trainInfo.getQurydata()) {
                        break;
                    }
                }
            } else if (CollectionUtils.isNotEmpty(startAirPortList)) {
                for (AirportAreaCombine start : startAirPortList) {
                    trainInfo = getTrainInfo(start_time.substring(0, 10), start.getAreaCode(),
                        arrivalCityIds.get(0), token);
                    if (trainInfo.getQurydata()) {
                        break;
                    }
                }
            } else if (CollectionUtils.isNotEmpty(arrivalAirPortList)) {
                for (AirportAreaCombine arrival : arrivalAirPortList) {
                    trainInfo = getTrainInfo(start_time.substring(0, 10), startCityIds.get(0),
                        arrival.getAreaCode(), token);
                    if (trainInfo.getQurydata()) {
                        break;
                    }
                }
            }
        }
        return trainInfo;
    }

    /**
     * 查询中转
     * @param token
     * @param start_time
     * @param startCityIds
     * @param arrivalCityIds
     * @param query_type
     * @return
     */
    @Override
    public TrainInfo getTrainInfo(String token, String start_time, List<String> startCityIds,
                                  List<String> arrivalCityIds,Integer query_type) {
        TrainInfo trainInfo = getTrainInfo(start_time.substring(0, 10), startCityIds.get(0), arrivalCityIds.get(0),
                token,query_type);
        if (!trainInfo.getQurydata()) {
            if (StringUtils.isEmpty(startCityIds.get(0)) || StringUtils.isEmpty(arrivalCityIds.get(0))) {
                return trainInfo;
            }
            List<AirportAreaCombine> startAirPortList = getAirPortList(startCityIds.get(0));
            List<AirportAreaCombine> arrivalAirPortList = getAirPortList(arrivalCityIds.get(0));

            if (CollectionUtils.isNotEmpty(startAirPortList) && CollectionUtils.isNotEmpty(arrivalAirPortList)) {
                for (AirportAreaCombine start : startAirPortList) {
                    for (AirportAreaCombine arrival : arrivalAirPortList) {
                        trainInfo = getTrainInfo(start_time.substring(0, 10), start.getAreaCode(),
                                arrival.getAreaCode(), token,query_type);
                        if (trainInfo.getQurydata()) {
                            break;
                        }
                    }
                    if (trainInfo.getQurydata()) {
                        break;
                    }
                }
            } else if (CollectionUtils.isNotEmpty(startAirPortList)) {
                for (AirportAreaCombine start : startAirPortList) {
                    trainInfo = getTrainInfo(start_time.substring(0, 10), start.getAreaCode(),
                            arrivalCityIds.get(0), token,query_type);
                    if (trainInfo.getQurydata()) {
                        break;
                    }
                }
            } else if (CollectionUtils.isNotEmpty(arrivalAirPortList)) {
                for (AirportAreaCombine arrival : arrivalAirPortList) {
                    trainInfo = getTrainInfo(start_time.substring(0, 10), startCityIds.get(0),
                            arrival.getAreaCode(), token,query_type);
                    if (trainInfo.getQurydata()) {
                        break;
                    }
                }
            }
        }
        return trainInfo;
    }

    @Override
    public Set<String> getStartCityIdSet(AirFlightRuleCheckReq.FlightInfo flightInfo) {
        if (CollectionUtils.isEmpty(flightInfo.getStartProvinceCityIds())) {
            List<String> startCityIds = flightInfo.getStartCityIds();
            List<String> startProvinceIdList = new ArrayList<>();
            for (String startCityId : startCityIds) {
                FullPathAreaDTO startAreaPath = areaService.getFullPathAreaById(startCityId);
                List<String> provinceIdList = iApplyV5Service.setCityList(startAreaPath);
                startProvinceIdList.addAll(provinceIdList);
            }
            flightInfo.setStartProvinceCityIds(startProvinceIdList);
        }
        List<String> startProvinceCityIds = flightInfo.getStartProvinceCityIds();
        if (CollectionUtils.isEmpty(startProvinceCityIds)) {
            logger.info("无城市id");
            return new HashSet<>(0);
        }
        Set<String> cityIdSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(startProvinceCityIds)) {
            cityIdSet.addAll(startProvinceCityIds);
        }
        return cityIdSet;
    }

    @Override
    public Set<String> getArrivalCityIdSet(AirFlightRuleCheckReq.FlightInfo flightInfo) {
        if (CollectionUtils.isEmpty(flightInfo.getArrivalProvinceCityIds())) {
            List<String> arrivalCityIds = flightInfo.getArrivalCityIds();
            List<String> arrivalProvinceIdList = new ArrayList<>();
            for (String arrivalCityId : arrivalCityIds) {
                FullPathAreaDTO startAreaPath = areaService.getFullPathAreaById(arrivalCityId);
                List<String> provinceIdList = iApplyV5Service.setCityList(startAreaPath);
                arrivalProvinceIdList.addAll(provinceIdList);
            }
            flightInfo.setArrivalProvinceCityIds(arrivalProvinceIdList);
        }
        List<String> arrivalProvinceCityIds = flightInfo.getArrivalProvinceCityIds();
        if (CollectionUtils.isEmpty(arrivalProvinceCityIds)) {
            logger.info("无城市id");
            return new HashSet<>(0);
        }
        Set<String> cityIdSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(arrivalProvinceCityIds)) {
            cityIdSet.addAll(arrivalProvinceCityIds);
        }
        return cityIdSet;
    }

    @Override
    public TravelOnBusiOrderCheckResContract intlAirRefundCheck(AirRefundCheckReqV2Contract reqContract,
        String clientVersion, String token) {
        logger.info("国际机票订单校验参数：{}", JsonUtils.toJson(reqContract));
        // 返回结果
        TravelOnBusiOrderCheckResContract resContract = new TravelOnBusiOrderCheckResContract();
        // 校验权限问题
        TravelOnBusiOrderRuleCheckResult ruleCheckResult = intlAirRefundRuleCheckResult(reqContract, clientVersion, token);
        logger.info("机票退款校验结果：ruleCheckResult: {}", JsonUtils.toJson(ruleCheckResult));
        resContract.setErr_code(ruleCheckResult.getErrCode());
        resContract.setErr_msg(ruleCheckResult.getErrMsg());
        resContract.setExceed_msg(ruleCheckResult.getExceed_msg());
        resContract.setIs_price_exceed(ruleCheckResult.getIsPriceExceed());
        resContract.setCurrency_error_message(ruleCheckResult.getCurrencyErrorMessage());
        resContract.setCompany_pay_price(ruleCheckResult.getCompanyPayPrice());
        resContract.setTraveBooking(ruleCheckResult.getIsTraveBooking());
        resContract.setPersonal_pay(ruleCheckResult.getPersonalPay());
        resContract.setPersonal_pay_price(ruleCheckResult.getPersonalPayPrice());
        resContract.setAirPersonalPayModel(ruleCheckResult.getAirPersonalPayModel());
        if (ruleCheckResult.getIs_exceed()) {
            resContract.setIs_exceed(true);
        }

        return resContract;
    }

    private TravelOnBusiOrderRuleCheckResult intlAirRefundRuleCheckResult(AirRefundCheckReqV2Contract reqContract, String clientVersion, String token) {
        // 默认初始化正常
        TravelOnBusiOrderRuleCheckResult checkResult = new TravelOnBusiOrderRuleCheckResult();
        checkResult.setResCode(GlobalResponseCode.Success);
        // 快照信息
        JSONObject snapshotInfo = new JSONObject();
        checkResult.setSnapshotInfo(snapshotInfo);

        // jira 需求：https://jira.fenbeijinfu.com/browse/CHANGJING-7401
        // 本期退票只有一个规则，且是场景通过 /air/rules/refund/detail 查询过了
        // 后续需求新增其他规则时需要校验时，需要校验其他

        // 公司id
        String companyId = reqContract.getTravel_on_busi_common_req_contract().getCompany_id();
        // 预定人id
        String employeeId = reqContract.getTravel_on_busi_common_req_contract().getEmployee_id();
        // 预定人姓名
        String employeeName = reqContract.getTravel_on_busi_common_req_contract().getEmployee_name();
        if (Objects.isNull(employeeId) || Objects.isNull(companyId)) {
            log.info("employeeId={}, companyId={}", employeeId, companyId);
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }

        // 预订人状态
        EmployeeContract orderEmployee = baseOrganizationService.getEmployee(employeeId, companyId);
        logger.info("预订人状态:{}", JsonUtils.toJson(orderEmployee));
        if (orderEmployee == null || orderEmployee.getStatus() != EmployeeStatusEnum.ACTIVE.getValue()) {
            checkResult.setResCode(GlobalResponseCode.OrderCheckEmployeeNotActive);
            return checkResult;
        }

        // 获取退票所有人员信息
        List<TravelOnBusiOrderCheckReqV2Contract.FrequentInfo> frequentInfoList = reqContract.getTravel_on_busi_common_req_contract().getFrequent_info();

        //初始化权限信息
        CompanyRuleSummary companyRuleSummary = currencyCheckService.assembleRefundEmployeeRuleSummary(companyId, employeeId,
            employeeName, BookingConfigEnum.INTL_AIR, frequentInfoList, clientVersion);

        // 是否是出行人模式
        checkResult.setIsTraveBooking(companyRuleSummary.getIsTraveBooking());

        // 企业飞机权限
        CompanyRuleDTO companyRule = iCompanyRuleService.queryByCompanyId(companyId);
        logger.info("企业飞机权限:{}", JsonUtils.toJson(companyRule));
        if (companyRule == null || companyRule.getAirRule() != 1) {
            log.info("企业无权限");
            checkResult.setResCode(GlobalResponseCode.OrderCheckCompanyNoAuth.getCode(), SaasContentConstant.AIR_COMPANY_NO_AUTH_MSG);
            return checkResult;
        }

        log.info("companyRuleSummary={}", JsonUtils.toJson(companyRuleSummary));
        companyRuleSummary.setCompanyRuleDTO(companyRule);
        checkResult.setCompanyRuleSummary(companyRuleSummary);

        // 初始化场景相关信息
        // 权限消费模版相关
        currencyCheckService.assembleIntlAirRefundSceneSummary(reqContract, companyRuleSummary);
        log.info("companyRuleSummary after assembleIntlAirRefundSceneSummary:{}", JsonUtils.toJson(companyRuleSummary));

        // 企业统一阻断判断
        //1.企业火车权限未开启
        if (companyRuleSummary.getIsRule()) {
            //设置状态码：100002 您所在的公司不允许火车,具体情况请联系管理员
            checkResult.setResCode(GlobalResponseCode.OrderCheckCompanyNoAuth.getCode(), SaasContentConstant.TRAIN_EMPLOYEE_NO_AUTH_MSG);
            return checkResult;
        }

        //2.企业余额不足
        else if(companyRuleSummary.getIsCompanyAccount()){
            //设置状态码：100002 您所在的公司不允许订购酒店,具体情况请联系管理员
            checkResult.setResCode(GlobalResponseCode.OrderCheckCompanyAccountNoAuth.getCode(), GlobalResponseCode.OrderCheckCompanyAccountNoAuth.getMsg());
            return checkResult;
        }

        // 设置退票每个人的最终结果集：综合考虑 规则校验 和 权限模版 配置 给出结果
        // 本期退票：如果超规，不会走后续审批。如果不超规则，看消费权限模版
        // 如果是二次强制提交，则看权限模版流程
        currencyCheckService.setEmployeeRefundResultEnum(companyRuleSummary, reqContract.getRefundFeePayerType());
        //获取多人校验结果 统一返回值处理
        CurrencyErrorMessage currencyErrorMassage = currencyCheckService.getRefundPlayerResults(companyRuleSummary, checkResult, reqContract.getRefundFee());
        log.info("currencyErrorMassage:{}",JsonUtils.toJson(currencyErrorMassage));

        // 默认企业支付
        setCompanyPay(reqContract, checkResult);
        //说明无法下单 需要给前端相应提示
        if(currencyErrorMassage.getCode() != GlobalResponseCode.Success.getCode()){
            checkResult.setCurrencyErrorMessage(currencyErrorMassage);
            checkResult.setResCode(GlobalResponseCode.CurrencyFrame);

            // 老版本消费模版填写理由退票需要兼容老版本，不检验
            if (GlobalResponseCode.CheckAndRefundReason.getCode() == currencyErrorMassage.getCode()
                && (VersionTool.compare(clientVersion, AirRuleConstant.AIR_EXCEED_PERSONAL_PAY_CLIENT_VERSION) < 0)) {
                checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                checkResult.setResCode(GlobalResponseCode.Success);
            }

            // 二次提交
            if (Objects.equals(reqContract.getResumbitForce(), Boolean.TRUE)) {
                // 订单审批 || 全额付直接放行
                if(GlobalResponseCode.CheckOrderApproval.getCode()== currencyErrorMassage.getCode() || GlobalResponseCode.AirRefundNeedPersonPay.getCode()== currencyErrorMassage.getCode() ){
                    checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                    checkResult.setResCode(GlobalResponseCode.Success);
                }
                // 退票或超规填写理由
                if(GlobalResponseCode.CheckAndRefundReason.getCode() == currencyErrorMassage.getCode()){
                    // 添加是否是个人理由和企业理由
                    ExceedPersonalPayCompanyConf conf = iMessageSetupService.getPersonalPayReasonConfBy(companyId);
                    checkResult.setExceedPersonalPayCompanyConf(conf);

                    //校验填写项是否正确
                    TravelOnBusiOrderRuleCheckResult travelOnBusiOrderRuleCheckResult = currencyCheckService.checkAirRefundReason(reqContract, currencyErrorMassage);
                    if(travelOnBusiOrderRuleCheckResult.getErrCode()!=GlobalResponseCode.Success.getCode()){
                        checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                        checkResult.setErrCode(travelOnBusiOrderRuleCheckResult.getErrCode());
                        checkResult.setErrMsg(travelOnBusiOrderRuleCheckResult.getErrMsg());
                    }else{
                        checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                        checkResult.setResCode(GlobalResponseCode.Success);
                    }
                }
            } else {
                if (GlobalResponseCode.AirRefundNeedPersonPay.getCode() == currencyErrorMassage.getCode()) {
                    checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                    setPersonalPay(reqContract, checkResult);
                }
            }
        }

        //增加快照信息
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("companyRuleSummary",companyRuleSummary);
        checkResult.setSnapshotInfo(jsonObject);
        //完整日志打印
        log.info("companyRuleSummary-{}",JsonUtils.toJson(companyRuleSummary));
        return checkResult;
    }

    private List<AirportAreaCombine> getAirPortList(String areaCode) {
        AirportAreaCombineExample airportAreaCombineExample = new AirportAreaCombineExample();
        airportAreaCombineExample.createCriteria().andAreaCodeEqualTo(areaCode);
        List<AirportAreaCombine> airportAreaCombineList =
            airportAreaCombineMapper.selectByExample(airportAreaCombineExample);
        List<AirportAreaCombine> airPortList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(airportAreaCombineList)) {
            String airportCode = airportAreaCombineList.get(0).getAirportCode();
            airportAreaCombineExample.clear();
            airportAreaCombineExample.createCriteria().andAirportCodeEqualTo(airportCode);
            airPortList = airportAreaCombineMapper.selectByExample(airportAreaCombineExample);
            airPortList = airPortList.stream().filter(airportAreaCombine -> !Objects.equals(areaCode,
                airportAreaCombine.getAreaCode())).collect(Collectors.toList());
        }
        return airPortList;
    }

    /**
     * 校验行程耗时和低价限制
     */
    private void checkTimeAndLowerPriceLimit(List<TravelOnBusiOrderRuleCheckResult> checkResultList, boolean isGoAndBack,
                                             BigDecimal goTotalAmount, BigDecimal backTotalAmount, BigDecimal orderPrice,
                                             TrainInfo trainInfo, AirRule airRule) {
        BigDecimal minSeatPrice = trainInfo.getMinSeatPrice();
        logger.info("火车票最低价: {}, 去程机票价格: {}, 回程机票价格: {}, 总价: {}, 机票价格需低于火车: {}",
                minSeatPrice, goTotalAmount, backTotalAmount, orderPrice, airRule.getTimeLowerPrice());
        // 未开启耗时限制
        if (!ObjUtils.toBoolean(airRule.getTimeConsumeFlag(), Boolean.FALSE)) {
            return;
        }
        // 行程耗时限制
        Integer timeConsumeValue = airRule.getTimeConsumeValue();
        boolean timeExceed = false;
        if (trainInfo.getQurydata()) {
            BigDecimal minTrainTime = ObjUtils.toBigDecimal(trainInfo.getRuntime().replaceAll(":", "."));
            if (minTrainTime.compareTo(BigDecimal.valueOf(timeConsumeValue)) < 0) {
                timeExceed = true;
            }
        }
        // 低价限制
        boolean priceLowerExceed = false;
        BigDecimal timeLowerPrice = BigDecimal.valueOf(airRule.getTimeLowerPrice());
        if (isGoAndBack) {
            if (trainInfo.getQurydata()) {
                // 往返
                priceLowerExceed = minSeatPrice.subtract(timeLowerPrice).compareTo(goTotalAmount) < 0
                    || minSeatPrice.subtract(timeLowerPrice).compareTo(backTotalAmount) < 0;
            }
        } else {
            if (trainInfo.getQurydata()) {
                // 非往返
                priceLowerExceed = minSeatPrice.subtract(timeLowerPrice).compareTo(orderPrice) < 0;
            }
        }
        // 耗时和低价都不满足，超规
        if (timeExceed && priceLowerExceed) {
            TravelOnBusiOrderRuleCheckResult checkResult = new TravelOnBusiOrderRuleCheckResult();
            checkResult.setResCode(GlobalResponseCode.OrderCheckAirRuleTimeConsume.getCode(),
                    String.format(GlobalResponseCode.OrderCheckAirRuleTimeConsume.getMsg(), timeConsumeValue,
                            timeLowerPrice.compareTo(BigDecimal.ZERO) > 0 ? timeLowerPrice + "元" : ""));
            checkResult.setType(4);
            checkResultList.add(checkResult);
        }
    }

    /**
     * 校验行程距离和低价限制
     */
    private void checkDistanceAndLowerPriceLimit(List<TravelOnBusiOrderRuleCheckResult> checkResultList, boolean isGoAndBack,
                                                 BigDecimal goTotalAmount, BigDecimal backTotalAmount, BigDecimal orderPrice,
                                                 TrainInfo trainInfo, AirRule airRule,
                                                 String startCityId, String arrivalCityId) {
        BigDecimal minSeatPrice = trainInfo.getMinSeatPrice();
        logger.info("火车票最低价: {}, 去程机票价格: {}, 回程机票价格: {}, 总价: {} 机票价格需低于火车: {}",
                minSeatPrice, goTotalAmount, backTotalAmount, orderPrice, airRule.getDistanceLowerPrice());
        // 未开启距离限制
        if (!ObjUtils.toBoolean(airRule.getDistanceLimitFlag(), Boolean.FALSE)) {
            return;
        }
        // 行程距离限制
        Integer distanceLimitValue = airRule.getDistanceLimitValue();
        Double cityDistance = getCityDistance(startCityId, arrivalCityId);
        logger.info("城市之间的距离是 {}, {}, {}", startCityId, arrivalCityId, cityDistance);
        boolean distanceExceed = cityDistance != null && cityDistance / 1000 < airRule.getDistanceLimitValue();
        // 低价限制
        boolean priceLowerExceed = false;
        BigDecimal distanceLowerPrice = BigDecimal.valueOf(airRule.getDistanceLowerPrice());
        if (isGoAndBack) {
            if (trainInfo.getQurydata()) {
                // 往返
                priceLowerExceed = minSeatPrice.subtract(distanceLowerPrice).compareTo(goTotalAmount) < 0
                    || minSeatPrice.subtract(distanceLowerPrice).compareTo(backTotalAmount) < 0;
            }
        } else {
            if (trainInfo.getQurydata()) {
                // 非往返
                priceLowerExceed = minSeatPrice.subtract(distanceLowerPrice).compareTo(orderPrice) < 0;
            }
        }
        // 距离和低价都不满足，超规
        if (distanceExceed && priceLowerExceed) {
            TravelOnBusiOrderRuleCheckResult checkResult = new TravelOnBusiOrderRuleCheckResult();
            checkResult.setResCode(GlobalResponseCode.OrderCheckAirRuleDistanceLimit.getCode(),
                    String.format(GlobalResponseCode.OrderCheckAirRuleDistanceLimit.getMsg(), distanceLimitValue,
                            distanceLowerPrice.compareTo(BigDecimal.ZERO) > 0 ? distanceLowerPrice + "元" : ""));
            checkResult.setType(4);
            checkResultList.add(checkResult);
        }
    }

    private void  buildAirTimeRangeExample(Set<Integer> dayTypeSet, Integer dayType, AirTimeRangeExample airTimeRangeExample, AirRule airRule) {
        dayTypeSet.add(dayType);
        if (dayType >= DayTempType.FIRSTDAY.getCode() && dayType <= DayTempType.FIVEDAY.getCode()) {
            //周一时，默认加入周日的时间规则
            if (dayType == DayTempType.FIRSTDAY.getCode()) {
                dayTypeSet.add(DayTempType.SEVENDAY.getCode());
                dayTypeSet.add(9);
            } else {
                dayTypeSet.add(dayType - 1);
            }
            dayTypeSet.add(8);
        } else {
            if (dayType == DayTempType.SIXDAY.getCode()) {
                dayTypeSet.add(dayType - 1);
                dayTypeSet.add(8);
            } else {
                dayTypeSet.add(dayType - 1);
            }
            dayTypeSet.add(9);
        }
        airTimeRangeExample.clear();
        airTimeRangeExample.createCriteria().andRuleIdEqualTo(airRule.getId()).andDayTypeIn(Lists.newArrayList(dayTypeSet));
    }

    /**
     * 获取与机票同行程的火车信息
     */
    @Override
    public TrainInfo getTrainInfo(String trainDate, String fromStationCityId, String toStationCityId, String token) {
        // 逻辑修改为查询次日火车信息
        // 出发日期等于预定日期时  查询次日  其他情况继续查询当日
        log.info("处理火车日期, before: {}", trainDate);
        Date trainStartDate = DateTimeTool.getDateFromString(trainDate, "yyyy-MM-dd");
        LocalDate today = LocalDate.now();
        Date todayDate = Date.from(today.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        if (trainStartDate.equals(todayDate)) {
            Date nextDay = DateTimeTool.getNextDay(trainStartDate);
            trainDate = DateTimeTool.fromDateToString(nextDay);
        }
        log.info("处理火车日期, after: {}", trainDate);

        Map<String, String> header = Maps.newHashMap();
        header.put("Content-Type", "application/json");
        header.put("X-Auth-Token", token);
        Map<String, String> map = Maps.newHashMap();
        map.put("train_date", trainDate);
        map.put("from_station_city_id", fromStationCityId.replace("2402","2376"));
        map.put("to_station_city_id", toStationCityId.replace("2402","2376"));
        String data;
        try {
            logger.info(String.format("获取火车时长调用参数：%s", JsonUtils.toJson(map)));
            data = HttpClientUtils.postBody(URL_GET_TRAIN_TIME, JSON.toJSONString(map), header);
            logger.info(String.format("获取火车时长返回结果：%s", JsonUtils.toJson(data)));
        } catch (Exception ex) {
            throw new SaasException(GlobalResponseCode.InnerError, CoreLanguage.AirUtils_Exception_TrainTime.getMessage());
        }
        if (StringUtil.isEmpty(data)) {
            throw new SaasException(GlobalResponseCode.InnerError, CoreLanguage.AirUtils_Exception_TrainTime.getMessage());
        }
        Map<String, Object> resultData = JSON.parseObject(data, new TypeReference<Map<String, Object>>() {});
        Integer code = ObjUtils.toInteger(resultData.get("code"), 0);
        if (code != 0) {
            return new TrainInfo();
        }
        TrainInfo trainInfo;
        try {
            trainInfo = JSON.parseObject(JSON.toJSONString(resultData.get("data")), TrainInfo.class);
        } catch (Exception e) {
            trainInfo = new TrainInfo();
            logger.info("解析火车信息异常, e: {}", e.getMessage());
        }
        return trainInfo;
    }


    @Override
    public TrainInfo getTrainInfo(String trainDate, String fromStationCityId, String toStationCityId, String token,Integer query_type) {
        // 逻辑修改为查询次日火车信息
        // 出发日期等于预定日期时  查询次日  其他情况继续查询当日
        log.info("处理火车日期, before: {}", trainDate);
        Date trainStartDate = DateTimeTool.getDateFromString(trainDate, "yyyy-MM-dd");
        LocalDate today = LocalDate.now();
        Date todayDate = Date.from(today.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        if (trainStartDate.equals(todayDate)) {
            Date nextDay = DateTimeTool.getNextDay(trainStartDate);
            trainDate = DateTimeTool.fromDateToString(nextDay);
        }
        log.info("处理火车日期, after: {}", trainDate);

        Map<String, String> header = Maps.newHashMap();
        header.put("Content-Type", "application/json");
        header.put("X-Auth-Token", token);
        Map<String, Object> map = Maps.newHashMap();
        map.put("train_date", trainDate);
        map.put("from_station_city_id", fromStationCityId.replace("2402","2376"));
        map.put("to_station_city_id", toStationCityId.replace("2402","2376"));
        map.put("query_type",query_type);

        String data;
        try {
            String cacheKey = trainDate + fromStationCityId + toStationCityId + query_type;
            log.info("cacheKey:{}",cacheKey);
            data = cache.get(cacheKey, String.class);
            log.info("data:{}",data);
            if(data == null){
                logger.info(String.format("查询机票规则获取当日火车列车最短时长及最低价格调用参数：%s", JsonUtils.toJson(map)));
                data = HttpClientUtils.postBody(URL_GET_TRAIN_TIME, JSON.toJSONString(map), header);
                logger.info(String.format("查询机票规则获取当日火车列车最短时长及最低价格返回结果：%s", JsonUtils.toJson(data)));
                cache.put(cacheKey, data, 60);//缓存1分钟
            }
        } catch (Exception ex) {
            throw new SaasException(GlobalResponseCode.InnerError, CoreLanguage.AirUtils_Exception_TrainTime.getMessage());
        }
        if (StringUtil.isEmpty(data)) {
            throw new SaasException(GlobalResponseCode.InnerError, CoreLanguage.AirUtils_Exception_TrainTime.getMessage());
        }
        Map<String, Object> resultData = JSON.parseObject(data, new TypeReference<Map<String, Object>>() {});
        Integer code = ObjUtils.toInteger(resultData.get("code"), 0);
        if (code != 0) {
            return new TrainInfo();
        }
        TrainInfo trainInfo;
        try {
            trainInfo = JSON.parseObject(JSON.toJSONString(resultData.get("data")), TrainInfo.class);
        } catch (Exception e) {
            trainInfo = new TrainInfo();
            logger.info("解析火车信息异常, e: {}", e.getMessage());
        }
        return trainInfo;
    }

    /**
     * 获取城市距离
     * @param startCityId
     * @param arrivalCityId
     * @return
     */
    private Double getCityDistance(String startCityId, String arrivalCityId) {
        Double startLongitude = null;
        Double startLatitude = null;
        Double arrivalLongitude = null;
        Double arrivalLatitude = null;
        try {
            logger.info(String.format("获取城市信息调用参数：%s,%s", startCityId, arrivalCityId));
            String responseData = HttpTool.get(URL_GET_AIR_CITY_INFO + "?areas_ids=" + startCityId + "," + arrivalCityId);
            logger.info(String.format("获取城市信息调用结果：%s", responseData));
            JSONObject responseDataMap = JSONObject.parseObject(responseData, JSONObject.class);
            List<JSONObject> data = (List<JSONObject>) responseDataMap.get("data");
            if (CollectionUtils.isNotEmpty(data) && data.size() == 2) {
                for (JSONObject cityInfo : data) {
                    String cityId = ObjUtils.toString(cityInfo.get("id"));
                    if (startCityId.equals(cityId)) {
                        startLongitude = cityInfo.getDouble("longitude");
                        startLatitude = cityInfo.getDouble("latitude");
                    } else {
                        arrivalLongitude = cityInfo.getDouble("longitude");
                        arrivalLatitude = cityInfo.getDouble("latitude");
                    }
                }
                logger.info(String.format("计算距离结果参数：%s,%s,%s,%s", startLongitude, startLatitude, arrivalLongitude, arrivalLatitude));
                Double des = DistanceUtils.getDistance(startLongitude, startLatitude, arrivalLongitude, arrivalLatitude);
                logger.info(String.format("计算距离返回结果：%s,%s,%s,%s,%s", startLongitude, startLatitude, arrivalLongitude, arrivalLatitude, des));
                return des;
            }
        } catch (Exception ex) {
            logger.error(String.format("获取城市信息调用失败：%s,%s,%s", startCityId, arrivalCityId, ex.getMessage()));
            throw new SaasException(GlobalResponseCode.InnerError, CoreLanguage.AirUtils_Exception_CityInfo.getMessage());
        }
        return null;
    }

    /**
     * 获取城市车站信息
     * @param startCityId
     * @param arrivalCityId
     * @return
     */
    private List<String> getCityStation(String startCityId, String arrivalCityId) {
        try {
            logger.info(String.format("获取城市车站信息调用参数：%s,%s", startCityId, arrivalCityId));
            String responseData = HttpTool.get(URL_GET_TRAIN_STATIONS + "?areaIds=" + startCityId + "," + arrivalCityId);
            logger.info(String.format("获取城市车站信息返回结果：%s", JsonUtils.toJson(responseData)));
            JSONObject responseDataMap = JSONObject.parseObject(responseData, JSONObject.class);
            JSONObject data = (JSONObject) responseDataMap.get("data");
            List<JSONObject> startCityIdList = (List<JSONObject>) data.get(startCityId);
            List<JSONObject> arrivalCityIdList = (List<JSONObject>) data.get(arrivalCityId);
            if (CollectionUtils.isEmpty(startCityIdList) || CollectionUtils.isEmpty(arrivalCityIdList)) {
                return Lists.newArrayList();
            }
            List<String> cityIdList = Lists.newArrayList();
            cityIdList.add(ObjUtils.toString(startCityIdList.get(0).get("ch_name")));
            cityIdList.add(ObjUtils.toString(arrivalCityIdList.get(0).get("ch_name")));
            return cityIdList;
        } catch (Exception ex) {
            logger.error(String.format("获取城市信息调用失败：%s,%s,%s", startCityId, arrivalCityId, ex.getMessage()));
            throw new SaasException(GlobalResponseCode.InnerError, CoreLanguage.AirUtils_Exception_CityInfo.getMessage());
        }
    }

    private List<TravelOnBusiOrderRuleCheckResult> queryMinPrice(List<TravelOnBusiOrderRuleCheckResult> travelOnBusiOrderRuleCheckResultList, AirOrderCheckReqV2Contract reqContract, List<String> timeRangeList, AirRule airRule, boolean isGoBack, List<TravelOnBusiOrderCheckResContract.AirLowestPriceMsg> airLowestPriceMsg) {
        try {
            //调取最低价接口判断是否超规
            AirInterceptRecordContract orderParameterJson = reqContract.getOrder_parameter_json();
            Map<String, String> header = Maps.newHashMap();
            header.put("Content-Type", "application/json;charset=utf-8");
            JSONObject param = new JSONObject();
            param.put("start_code", orderParameterJson.getStarting_code());
            param.put("end_code", orderParameterJson.getDestination_code());
            param.put("date", DateTimeTool.fromDateToString(DateUtils.parse(reqContract.getTravel_on_busi_parameter_req_contract().getStart_time())));
            if (isGoBack) {
                param.put("select_price", reqContract.getOrder_parameter_json().getSale_price());
            } else {
                param.put("select_price", reqContract.getOriginalPrice());
            }
            param.put("time_solt", timeRangeList);
            param.put("company_id", reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
            // 经停航班限制
            param.put("flight_filter_type", airRule.getFilterStopoverFlightFlag());
            // 机场最低价限制增加机场的范围选择。 不传则所有航班
            Integer airPortCity = airRule.getAirPortCityFlag();
            if (AirPortCityEnum.FIGHT_LIMIT.getCode() == airPortCity) { //限制相同出发和到达航班
                param.put("start_port_code", orderParameterJson.getStarting_code());
                param.put("end_port_code", orderParameterJson.getDestination_code());
            } else if (AirPortCityEnum.TAKEOFF_AND_LANDING_CITY.getCode() == airPortCity) { //限制到达航班
                param.put("end_port_code", orderParameterJson.getDestination_code());
            } else if (AirPortCityEnum.LANDING_CITY_LIMIT.getCode() == airPortCity){ //限制出发航班
                param.put("start_port_code", orderParameterJson.getStarting_code());
            }
            logger.info("获取国内机票最低价接口参数:" + JsonUtils.toJson(param));
            String airData = HttpTool.post(URL_GET_AIR_MIN_PRICE, param, header);
            logger.info("获取国内机票最低价接口返回结果:" + airData);
            Map<String, Object> jo = JSONObject.parseObject(airData, HashMap.class);
            Map<String, Object> airDataMap = (Map<String, Object>) jo.get("data");
            boolean minPrice = ObjUtils.toboolean(airDataMap.get("min_price"));
            Map<String, Object> minFlight = (Map<String, Object>) airDataMap.get("min_flight");
            // 最低价提示信息封装
            TravelOnBusiOrderCheckResContract.AirLowestPriceMsg airLowestPriceMsg1 = new TravelOnBusiOrderCheckResContract.AirLowestPriceMsg();
            airLowestPriceMsg1.setMinPriceFlag(minPrice);
            airLowestPriceMsg1.setMinFlight(minFlight);
            airLowestPriceMsg1.setLowPriceTime(airRule.getLowPriceTime());
            airLowestPriceMsg1.setLowPriceUnit(airRule.getLowPriceUnit());
            airLowestPriceMsg1.setLowPriceFlag(airRule.getLowPriceFlag());
            airLowestPriceMsg1.setFilterStopoverFlightFlag(airRule.getFilterStopoverFlightFlag());
            if (AirPortCityEnum.FIGHT_LIMIT.getCode() == airPortCity) { //限制相同出发和到达航班
                airLowestPriceMsg1.setStartPortCode(orderParameterJson.getStarting_code());
                airLowestPriceMsg1.setEndPortCode(orderParameterJson.getDestination_code());
            } else if (AirPortCityEnum.TAKEOFF_AND_LANDING_CITY.getCode() == airPortCity) { //限制到达航班
                airLowestPriceMsg1.setEndPortCode(orderParameterJson.getDestination_code());
            } else if (AirPortCityEnum.LANDING_CITY_LIMIT.getCode() == airPortCity){ //限制出发航班
                airLowestPriceMsg1.setStartPortCode(orderParameterJson.getStarting_code());
            }
            logger.info("flightMap:{}", JSON.toJSONString(minFlight));
            logger.info("isLowestPrice:{}", minPrice);
            logger.info("lowPriceTime:{}", airRule.getLowPriceTime());
            logger.info("lowPriceFlag:{}", airRule.getLowPriceFlag());
            logger.info("filterStopoverFlightFlag:{}", airRule.getFilterStopoverFlightFlag());
            logger.info("airportCityFlag:{}", airRule.getAirPortCityFlag());
            airLowestPriceMsg.add(airLowestPriceMsg1);
            // 非最低价航班   且用户选择的不是推荐的低价航班
            if (!minPrice && !ObjUtils.toBoolean(reqContract.getLowPriceRecommend(), Boolean.FALSE) && !Objects.equals(reqContract.getPickFlightFlag(),1)) {
                String msg;
                if (FilterStopoverFlightFlag.ALL.equalsTo(airRule.getFilterStopoverFlightFlag())){
                    msg = StrUtils.formatString(GlobalResponseCode.OrderCheckAirMinPriceNoAuth.getMsg(),
                            airRule.getLowPriceTime(),
                            AirPortCityEnum.getDescribe(airRule.getAirPortCityFlag()),
                            "");
                } else {
                    msg = StrUtils.formatString(GlobalResponseCode.OrderCheckAirMinPriceNoAuth.getMsg(),
                            airRule.getLowPriceTime(),
                            AirPortCityEnum.getDescribe(airPortCity),
                            FilterStopoverFlightFlag.valueOf(airRule.getFilterStopoverFlightFlag()).getDesc());
                }
                logger.info(msg);
                TravelOnBusiOrderRuleCheckResult checkResult = new TravelOnBusiOrderRuleCheckResult();
                checkResult.setResCode(GlobalResponseCode.OrderCheckAirMinPriceNoAuth.getCode(), msg);
                checkResult.setType(2);
                checkResult.setMin_flight(minFlight);
                travelOnBusiOrderRuleCheckResultList.add(checkResult);
                return travelOnBusiOrderRuleCheckResultList;
            }
        } catch (Exception e) {
            logger.error(String.format("url:%s,异常", URL_GET_AIR_MIN_PRICE) + e.getMessage());
            throw new SaasException(GlobalResponseCode.PayOrderCancleOrderSystemError);
        }
        return travelOnBusiOrderRuleCheckResultList;
    }

    private List<TravelOnBusiOrderRuleCheckResult> queryGoBackMinPrice(List<TravelOnBusiOrderRuleCheckResult> travelOnBusiOrderRuleCheckResultList, AirOrderCheckReqV2Contract reqContract, List<String> timeRangeList, AirRule airRule) {
        try {
            // 调取最低价接口判断是否超规
            AirInterceptRecordContract orderParameterJson = reqContract.getOrder_parameter_json();
            Map<String, String> header = Maps.newHashMap();
            header.put("Content-Type", "application/json;charset=utf-8");
            JSONObject param = new JSONObject();
            param.put("start_code", orderParameterJson.getStarting_code());
            param.put("end_code", orderParameterJson.getDestination_code());
            param.put("date", DateTimeTool.fromDateToString(DateUtils.parse(reqContract.getTravel_on_busi_parameter_req_contract().getBack_start_time())));
            param.put("select_price", reqContract.getOrder_parameter_json().getSale_price());
            param.put("time_solt", timeRangeList);
            param.put("company_id", reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
            // 经停航班限制
            param.put("flight_filter_type", airRule.getFilterStopoverFlightFlag());
            // 机场最低价限制增加机场的范围选择。 不传则所有航班
            Integer airPortCity = airRule.getAirPortCityFlag();
            if (AirPortCityEnum.FIGHT_LIMIT.getCode() == airPortCity) { //限制相同出发和到达航班
                param.put("start_port_code", orderParameterJson.getStarting_code());
                param.put("end_port_code", orderParameterJson.getDestination_code());
            } else if (AirPortCityEnum.TAKEOFF_AND_LANDING_CITY.getCode() == airPortCity) { //限制到达航班
                param.put("end_port_code", orderParameterJson.getDestination_code());
            } else if (AirPortCityEnum.LANDING_CITY_LIMIT.getCode() == airPortCity){ //限制出发航班
                param.put("start_port_code", orderParameterJson.getStarting_code());
            }
            logger.info("获取国内往返机票最低价接口参数:" + JsonUtils.toJson(param));
            String airData = HttpTool.post(URL_GET_AIR_MIN_PRICE, param, header);
            logger.info("获取国内往返最低价接口返回结果:" + airData);
            Map<String, Object> jo = JSONObject.parseObject(airData, HashMap.class);
            Map<String, Object> airDataMap = (Map<String, Object>) jo.get("data");
            boolean minPrice = ObjUtils.toboolean(airDataMap.get("min_price"));
            Map<String, Object> minFlight = (Map<String, Object>) airDataMap.get("min_flight");
            // 非最低价航班   且用户选择的不是推荐的低价航班
            if (!minPrice && !ObjUtils.toBoolean(reqContract.getLowPriceRecommend(), Boolean.FALSE) && !Objects.equals(reqContract.getPickFlightFlag(),1)) {
                String msg;
                if (FilterStopoverFlightFlag.ALL.equalsTo(airRule.getFilterStopoverFlightFlag())){
                    msg = StrUtils.formatString(GlobalResponseCode.OrderCheckAirMinPriceNoAuth.getMsg(),
                            airRule.getLowPriceTime(),
                            AirPortCityEnum.getDescribe(airPortCity),
                            "");
                } else {
                    msg = StrUtils.formatString(GlobalResponseCode.OrderCheckAirMinPriceNoAuth.getMsg(),
                            airRule.getLowPriceTime(),
                            AirPortCityEnum.getDescribe(airPortCity),
                            FilterStopoverFlightFlag.valueOf(airRule.getFilterStopoverFlightFlag()).getDesc());
                }
                logger.info(msg);
                TravelOnBusiOrderRuleCheckResult checkResult = new TravelOnBusiOrderRuleCheckResult();
                checkResult.setResCode(GlobalResponseCode.OrderCheckAirMinPriceNoAuth.getCode(), msg);
                checkResult.setType(2);
                checkResult.setMin_flight(minFlight);
                travelOnBusiOrderRuleCheckResultList.add(checkResult);
                return travelOnBusiOrderRuleCheckResultList;
            }
        } catch (Exception e) {
            logger.error(String.format("url:%s,异常", URL_GET_AIR_MIN_PRICE) + e.getMessage());
            throw new SaasException(GlobalResponseCode.PayOrderCancleOrderSystemError);
        }
        return travelOnBusiOrderRuleCheckResultList;
    }

    public List<DateInterval> merge(List<DateInterval> intervals) {
        List<DateInterval> intervalList = new ArrayList<>();
        if (intervals.size() == 0) {
            return intervals;
        }
        for (int i = 0; i < intervals.size(); i++) {
            for (int j = i+1; j < intervals.size(); j++) {
                if (ObjUtils.toLong(intervals.get(i).getStart()) > ObjUtils.toLong(intervals.get(j).getStart())) {
                    DateInterval tem = intervals.get(i);
                    intervals.set(i,intervals.get(j));
                    intervals.set(j,tem);
                }
            }
        }
        Long min = intervals.get(0).getStart();
        Long max = intervals.get(0).getEnd();
        for (int i = 1; i < intervals.size(); i++) {
            //重叠即合并区间
            if (ObjUtils.toLong(intervals.get(i).getStart()) <= max) {
                max = ObjUtils.toLong(intervals.get(i).getEnd()) > max ? ObjUtils.toLong(intervals.get(i).getEnd()) : max;
            } else {
                intervalList.add(new DateInterval(min, max));
                min = ObjUtils.toLong(intervals.get(i).getStart());
                max = ObjUtils.toLong(intervals.get(i).getEnd());
            }
        }
        intervalList.add(new DateInterval(min, max));
        return intervalList;
    }

    //折扣字符串格式化
    private String formatDiscount(BigDecimal airDiscount) {
        String discountStr = "0.0";
        try {
            discountStr = airDiscount.multiply(BigDecimal.valueOf(10)).setScale(1, BigDecimal.ROUND_DOWN).toString();
        } catch (Exception e) {
            logger.error("折扣字符串格式化：{}", e.getLocalizedMessage());
        }
        return discountStr;
    }

    /**
     * 校验日期规则问题
     * 注意点：日期出现次日时，天数加一天
     *
     * @param airTimeRanges
     * @param startTime     yyyy-MM-dd HH:mm:ss
     * @return
     */
    private Boolean checkoutTimeAuth(Integer dayType, List<AirTimeRange> airTimeRanges, String startTime) {
        try {
            SimpleDateFormat sdfDateTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat sdfDate = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat sdfTime = new SimpleDateFormat("HH:mm:ss");
            //无时间限制
            if (CollectionUtils.isEmpty(airTimeRanges)) {
                return false;
            }
            //获取起飞日期
            Date airStartDate = null;
            String date = startTime.substring(0, 10);
            airStartDate = sdfDate.parse(startTime);
            //日期加一天
            String dateAdd = AirRuleUtils.dateAdd(airStartDate, 1, sdfDate);
            Boolean isAllowed = false;
            //日期减一天
            String dateReduce = AirRuleUtils.dateAdd(airStartDate, -1, sdfDate);
            Date airDateTime = sdfDateTime.parse(startTime);
            Boolean tag = false;
            for (AirTimeRange airTimeRange : airTimeRanges) {
                String startDate = "";
                String endDate = "";
                //当天日期
                if (airTimeRange.getDayType().intValue() == dayType.intValue()) {
                    startDate = date + " " + sdfTime.format(airTimeRange.getBeginTime());
                    endDate = date + " " + sdfTime.format(airTimeRange.getEndTime());
                    //跨日日期加一天
                    if (airTimeRange.getIsOvernight()) {
                        endDate = dateAdd + " " + sdfTime.format(airTimeRange.getEndTime());
                    }
                }
                //前一天
                else if (airTimeRange.getDayType().intValue() == (dayType.intValue() - 1)
                        || (dayType == DayTempType.FIRSTDAY.getCode() && airTimeRange.getDayType().intValue() == DayTempType.SEVENDAY.getCode())) {
                    if (airTimeRange.getIsOvernight()) {
                        startDate = dateReduce + " " + sdfTime.format(airTimeRange.getBeginTime());
                        endDate = date + " " + sdfTime.format(airTimeRange.getEndTime());
                    } else {
                        continue;
                    }
                }
                //周末或者工作日
                else {
                    //周一特殊处理周末
                    if (dayType == DayTempType.FIRSTDAY.getCode()) {
                        //周末
                        if (airTimeRange.getDayType().intValue() == 9) {
                            if (airTimeRange.getIsOvernight()) {
                                startDate = dateReduce + " " + sdfTime.format(airTimeRange.getBeginTime());
                                endDate = date + " " + sdfTime.format(airTimeRange.getEndTime());
                            } else {
                                continue;
                            }
                        } else {
                            startDate = date + " " + sdfTime.format(airTimeRange.getBeginTime());
                            endDate = date + " " + sdfTime.format(airTimeRange.getEndTime());
                            //跨日日期加一天
                            if (airTimeRange.getIsOvernight()) {
                                endDate = dateAdd + " " + sdfTime.format(airTimeRange.getEndTime());
                            }
                        }

                    }
                    //周六特殊处理工作日
                    else if (dayType == DayTempType.SIXDAY.getCode()) {
                        if (airTimeRange.getDayType().intValue() == 8) {
                            if (airTimeRange.getIsOvernight()) {
                                startDate = dateReduce + " " + sdfTime.format(airTimeRange.getBeginTime());
                                endDate = date + " " + sdfTime.format(airTimeRange.getEndTime());
                            } else {
                                continue;
                            }
                        } else {
                            startDate = date + " " + sdfTime.format(airTimeRange.getBeginTime());
                            endDate = date + " " + sdfTime.format(airTimeRange.getEndTime());
                            //跨日日期加一天
                            if (airTimeRange.getIsOvernight()) {
                                endDate = dateAdd + " " + sdfTime.format(airTimeRange.getEndTime());
                            }
                        }
                    }
                    //非周一和周六
                    else {
                        startDate = date + " " + sdfTime.format(airTimeRange.getBeginTime());
                        endDate = date + " " + sdfTime.format(airTimeRange.getEndTime());
                        //跨日日期加一天
                        if (airTimeRange.getIsOvernight()) {
                            endDate = dateAdd + " " + sdfTime.format(airTimeRange.getEndTime());
                        }
                        if ((dayType > DayTempType.FIRSTDAY.getCode() && dayType <= DayTempType.FIVEDAY.getCode() && airTimeRange.getDayType().intValue() == 8)
                                || (dayType > DayTempType.SIXDAY.getCode() && dayType <= DayTempType.SEVENDAY.getCode() && airTimeRange.getDayType().intValue() == 9)) {
                            isAllowed = true;
                        }
                    }
                }
                //时间比较
                Date startDateTime = sdfDateTime.parse(startDate);
                Date endDateTime = sdfDateTime.parse(endDate);
                if (airDateTime.getTime() >= startDateTime.getTime() && airDateTime.getTime() <= endDateTime.getTime()) {
                    tag = true;
                    break;
                }
                //特殊处理周末，工作日
                if (isAllowed) {
                    if (airTimeRange.getIsOvernight()) {
                        startDate = dateReduce + " " + sdfTime.format(airTimeRange.getBeginTime());
                        endDate = date + " " + sdfTime.format(airTimeRange.getEndTime());
                        Date startDateTimeW = sdfDateTime.parse(startDate);
                        Date endDateTimeW = sdfDateTime.parse(endDate);
                        if (airDateTime.getTime() >= startDateTimeW.getTime() && airDateTime.getTime() <= endDateTimeW.getTime()) {
                            tag = true;
                            break;
                        }
                    } else {
                        continue;
                    }
                }

            }
            return tag;
        } catch (ParseException e) {
            logger.error("打车日期格式化异常" + e.getLocalizedMessage());
        }
        return false;
    }

    /**
     * 校验日期规则问题
     * 注意点：日期出现次日时，天数加一天
     *
     * @param airTimeRanges
     * @param startTime     yyyy-MM-dd HH:mm:ss
     * @return
     */
    private List<String> checkLowPriceAuth(Integer dayType, List<AirTimeRange> airTimeRanges, String startTime, AirRule airRule) {
        try {
            Integer lowPriceFlag = airRule.getLowPriceFlag();
            Integer lowPriceTime = airRule.getLowPriceTime();
            String lowPriceUnit = airRule.getLowPriceUnit();

            if (lowPriceFlag == 0) {
                return Lists.newArrayList();
            }
            //减几小时
            Calendar lowCalendar = Calendar.getInstance();
            lowCalendar.setTime(DateTimeTool.fromStringToDateTime(startTime));
            if(AirLowPriceUnitEnum.MINUTE.getKey().equals(lowPriceUnit)){
                lowCalendar.add(Calendar.MINUTE, -lowPriceTime);
            }else {
                lowCalendar.add(Calendar.HOUR_OF_DAY, -lowPriceTime);
            }

            Long lowTime = lowCalendar.getTime().getTime();
            //加几小时
            Calendar highCalendar = Calendar.getInstance();
            highCalendar.setTime(DateTimeTool.fromStringToDateTime(startTime));
            if(AirLowPriceUnitEnum.MINUTE.getKey().equals(lowPriceUnit)){
                highCalendar.add(Calendar.MINUTE, lowPriceTime);
            }else {
                highCalendar.add(Calendar.HOUR_OF_DAY, lowPriceTime);
            }
            Long highTime = highCalendar.getTime().getTime();
            //获取当天的最晚时间
            Date startDateInfo = DateUtils.parse(startTime);
            String endDayTime = DateTimeTool.fromDateToString(startDateInfo) + " 23:59:59";
            Long endTime = DateUtils.parse(endDayTime).getTime();
            if (highTime > endTime) {
                highTime = endTime;
            }
            SimpleDateFormat sdfDateTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat sdfDate = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat sdfTime = new SimpleDateFormat("HH:mm:ss");
            //无时间限制
            if (CollectionUtils.isEmpty(airTimeRanges)) {
                return Lists.newArrayList();
            }
            //获取起飞日期
            Date airStartDate = null;
            String date = startTime.substring(0, 10);
            airStartDate = sdfDate.parse(startTime);
            //日期加一天
            String dateAdd = AirRuleUtils.dateAdd(airStartDate, 1, sdfDate);
            Boolean isAllowed = false;
            //日期减一天
            String dateReduce = AirRuleUtils.dateAdd(airStartDate, -1, sdfDate);
            List<String> intersectionList = Lists.newArrayList();
            for (AirTimeRange airTimeRange : airTimeRanges) {
                String startDate = "";
                String endDate = "";
                //当天日期
                if (airTimeRange.getDayType().intValue() == dayType.intValue()) {
                    startDate = date + " " + sdfTime.format(airTimeRange.getBeginTime());
                    endDate = date + " " + sdfTime.format(airTimeRange.getEndTime());
                    //跨日日期加一天
                    if (airTimeRange.getIsOvernight()) {
                        endDate = dateAdd + " " + sdfTime.format(airTimeRange.getEndTime());
                    }
                }
                //前一天
                else if (airTimeRange.getDayType().intValue() == (dayType.intValue() - 1)
                    || (dayType == DayTempType.FIRSTDAY.getCode() && airTimeRange.getDayType().intValue() == DayTempType.SEVENDAY.getCode())) {
                    if (airTimeRange.getIsOvernight()) {
                        startDate = dateReduce + " " + sdfTime.format(airTimeRange.getBeginTime());
                        endDate = date + " " + sdfTime.format(airTimeRange.getEndTime());
                    } else {
                        continue;
                    }
                }
                //周末或者工作日
                else {
                    //周一特殊处理周末
                    if (dayType == DayTempType.FIRSTDAY.getCode()) {
                        //周末
                        if (airTimeRange.getDayType().intValue() == 9) {
                            if (airTimeRange.getIsOvernight()) {
                                startDate = dateReduce + " " + sdfTime.format(airTimeRange.getBeginTime());
                                endDate = date + " " + sdfTime.format(airTimeRange.getEndTime());
                            } else {
                                continue;
                            }
                        } else {
                            startDate = date + " " + sdfTime.format(airTimeRange.getBeginTime());
                            endDate = date + " " + sdfTime.format(airTimeRange.getEndTime());
                            //跨日日期加一天
                            if (airTimeRange.getIsOvernight()) {
                                endDate = dateAdd + " " + sdfTime.format(airTimeRange.getEndTime());
                            }
                        }

                    }
                    //周六特殊处理工作日
                    else if (dayType == DayTempType.SIXDAY.getCode()) {
                        if (airTimeRange.getDayType().intValue() == 8) {
                            if (airTimeRange.getIsOvernight()) {
                                startDate = dateReduce + " " + sdfTime.format(airTimeRange.getBeginTime());
                                endDate = date + " " + sdfTime.format(airTimeRange.getEndTime());
                            } else {
                                continue;
                            }
                        } else {
                            startDate = date + " " + sdfTime.format(airTimeRange.getBeginTime());
                            endDate = date + " " + sdfTime.format(airTimeRange.getEndTime());
                            //跨日日期加一天
                            if (airTimeRange.getIsOvernight()) {
                                endDate = dateAdd + " " + sdfTime.format(airTimeRange.getEndTime());
                            }
                        }
                    }
                    //非周一和周六
                    else {
                        startDate = date + " " + sdfTime.format(airTimeRange.getBeginTime());
                        endDate = date + " " + sdfTime.format(airTimeRange.getEndTime());
                        //跨日日期加一天
                        if (airTimeRange.getIsOvernight()) {
                            endDate = dateAdd + " " + sdfTime.format(airTimeRange.getEndTime());
                        }
                        if ((dayType > DayTempType.FIRSTDAY.getCode() && dayType <= DayTempType.FIVEDAY.getCode() && airTimeRange.getDayType().intValue() == 8)
                            || (dayType > DayTempType.SIXDAY.getCode() && dayType <= DayTempType.SEVENDAY.getCode() && airTimeRange.getDayType().intValue() == 9)) {
                            isAllowed = true;
                        }
                    }
                }
                //时间比较
                Date startDateTime = sdfDateTime.parse(startDate);
                Date endDateTime = sdfDateTime.parse(endDate);
                String normalInterval = getInterval(lowTime, highTime, startDateTime.getTime(), endDateTime.getTime());
                if (StringUtils.isNotBlank(normalInterval)) {
                    intersectionList.add(normalInterval);
                }
                //特殊处理周末，工作日
                if (isAllowed) {
                    if (airTimeRange.getIsOvernight()) {
                        startDate = dateReduce + " " + sdfTime.format(airTimeRange.getBeginTime());
                        endDate = date + " " + sdfTime.format(airTimeRange.getEndTime());
                        Date startDateTimeW = sdfDateTime.parse(startDate);
                        Date endDateTimeW = sdfDateTime.parse(endDate);
                        String specialInterval = getInterval(lowTime, highTime, startDateTimeW.getTime(), endDateTimeW.getTime());
                        if (StringUtils.isNotBlank(specialInterval)) {
                            intersectionList.add(specialInterval);
                        }
                    } else {
                        continue;
                    }
                }

            }
            if (CollectionUtils.isEmpty(intersectionList)) {
                return Lists.newArrayList();
            }
            return intersectionList;
        } catch (ParseException e) {
            logger.error("打车日期格式化异常" + e.getLocalizedMessage());
        }
        return Lists.newArrayList();
    }

    public String getInterval(long s1, long e1, long s2, long e2) {
        if (e1 < s2 || e2 < s1) {
            return "";
        } else {
            return Math.max(s1, s2) + "-" + Math.min(e1, e2);
        }
    }

    /**
     * 添加飞机拦截记录信息
     */
    private void initAirInterceptRecord(AirOrderCheckReqV2Contract reqContract, TravelOnBusiOrderRuleCheckResult ruleCheckResult) {
        AirInterceptRecord airInterceptRecord = new AirInterceptRecord();
        //拦截参数
        try {
            //处理规则信息
            EmployeeAirRule employeeAirRule = iBaseEmployeeAirRuleService.queryEmployeeAirRuleByPrimaryKey(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(), reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
            AirRule airRule = null;
            if (employeeAirRule != null) {
                String ruleId = ObjUtils.ifNull(employeeAirRule.getManual_air_rule_id(), employeeAirRule.getDefault_air_rule_id());
                airRule = airRuleMapper.selectByPrimaryKey(ruleId);
            }
            //处理保存参数
            AirInterceptRecordContract airInterceptRecordContract = reqContract.getOrder_parameter_json();
            airInterceptRecord.setId(IDTool.CreateUniqueID());
            airInterceptRecord.setEmployeeId(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id());
            airInterceptRecord.setCompanyId(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
            airInterceptRecord.setCreateTime(new Date());
            airInterceptRecord.setContactName(reqContract.getTravel_on_busi_parameter_req_contract().getContact_name());
            airInterceptRecord.setContactPhone(reqContract.getTravel_on_busi_parameter_req_contract().getContact_phone());
            airInterceptRecord.setChannel(reqContract.getTravel_on_busi_common_req_contract().getChannel());
            airInterceptRecord.setTotalPrice(reqContract.getTravel_on_busi_common_req_contract().getOrder_price());
            airInterceptRecord.setAirRule(employeeAirRule == null ? -1 : employeeAirRule.getAir_rule());
            airInterceptRecord.setAirRuleFlag(employeeAirRule == null ? false : employeeAirRule.getAir_rule_flag());
            airInterceptRecord.setAirVerifyFlag(employeeAirRule == null ? false : employeeAirRule.getAir_verify_flag());
            airInterceptRecord.setExceedBuyType(employeeAirRule == null ? -1 : employeeAirRule.getExceed_buy_type());
            airInterceptRecord.setExceedBuyFlag(reqContract.getTravel_on_busi_parameter_req_contract().getExceed_submit());
            airInterceptRecord.setAirCabinFlag(airRule == null ? false : airRule.getAirCabinFlag());
            airInterceptRecord.setAirCabinType(airRule == null ? "" : airRule.getAirCabinType());
            airInterceptRecord.setAirPriceFlag(airRule == null ? false : airRule.getAirPriceFlag());
            airInterceptRecord.setAirUnitPrice(airRule == null ? BigDecimal.ZERO : airRule.getAirUnitPrice());
            airInterceptRecord.setFlightNo(airInterceptRecordContract.getFlight_no());
            airInterceptRecord.setPlaneType(airInterceptRecordContract.getPlane_type());
            airInterceptRecord.setAirlineName(airInterceptRecordContract.getAirline_name());
            airInterceptRecord.setServiceClass(StringUtils.isEmpty(reqContract.getAir_type()) ? 1 : Integer.valueOf(reqContract.getAir_type()));
            airInterceptRecord.setSeatMsg(airInterceptRecordContract.getSeat_msg());
            airInterceptRecord.setCabin(airInterceptRecordContract.getCabin());
            airInterceptRecord.setStartingCity(airInterceptRecordContract.getStarting_city());
            airInterceptRecord.setStartingCode(airInterceptRecordContract.getStarting_code());
            airInterceptRecord.setDestinationCity(airInterceptRecordContract.getDestination_city());
            airInterceptRecord.setDestinationCode(airInterceptRecordContract.getDestination_code());
            airInterceptRecord.setStartingAirport(airInterceptRecordContract.getStarting_airport());
            airInterceptRecord.setDestinationAirport(airInterceptRecordContract.getDestination_airport());
            airInterceptRecord.setStartingTerminal(airInterceptRecordContract.getStarting_terminal());
            airInterceptRecord.setDestinationTerminal(airInterceptRecordContract.getDestination_terminal());
            airInterceptRecord.setIsMiddleStop(airInterceptRecordContract.getIs_middle_stop());
            airInterceptRecord.setDepartureTimestamp(airInterceptRecordContract.getDeparture_timestamp());
            airInterceptRecord.setArrivedTimestamp(airInterceptRecordContract.getArrived_timestamp());
            airInterceptRecord.setParPrice(airInterceptRecordContract.getPar_price());
            airInterceptRecord.setFuelTax(airInterceptRecordContract.getFuel_tax());
            airInterceptRecord.setAirportTax(airInterceptRecordContract.getAirport_tax());
            airInterceptRecord.setSalePrice(airInterceptRecordContract.getSale_price());
            //需要处理订票人信息
            List<OrderCheckExt> passengerList = iOrderCheckService.getPassengerList(reqContract.getPassengers());
            String passengerInfoList = JsonUtils.toJson(passengerList);
            airInterceptRecord.setPassengerInfoList(passengerInfoList);
            airInterceptRecord.setErrCode(ruleCheckResult.getErrCode());
            String errorMsg = ruleCheckResult.getErrMsg() + (StringUtils.isEmpty(ruleCheckResult.getExceed_msg()) ? "" : "|" + ruleCheckResult.getExceed_msg());
            airInterceptRecord.setErrMsg(errorMsg);
            //airInterceptRecord.setCostCenterId(reqContract.getTravel_on_busi_common_req_contract().getAttribution_id());
            //airInterceptRecord.setCostCenterType(reqContract.getTravel_on_busi_common_req_contract().getAttribution_category());
            airInterceptRecordMapper.insertSelective(airInterceptRecord);
        } catch (Exception e) {
            logger.error("添加飞机拦截记录信息:{},发生异常:{}", JsonUtils.toJson(airInterceptRecord), e.getLocalizedMessage());
        }
    }


    /**
     * @MethodName airRefundCheck
     * @Description 机票退款校验
     * @param: reqContract
     * @param: clientVersion
     * @param: token
     * @return: com.fenbeitong.saas.core.contract.order.check.TravelOnBusiOrderCheckResContract
     * <AUTHOR> Yunpeng
     * @Date 2022/11/7 14:09
     */
    @Override
    public TravelOnBusiOrderCheckResContract airRefundCheck(AirRefundCheckReqV2Contract reqContract, String clientVersion, String token) throws SaasException {
        DynamicDataSourceDecision.determineDataSource(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        logger.info("国内机票订单校验参数：{}", JsonUtils.toJson(reqContract));
        // 返回结果
        TravelOnBusiOrderCheckResContract resContract = new TravelOnBusiOrderCheckResContract();
        // 校验权限问题
        TravelOnBusiOrderRuleCheckResult ruleCheckResult = airRefundRuleCheckResult(reqContract, clientVersion, token);
        logger.info("机票退款校验结果：ruleCheckResult: {}", JsonUtils.toJson(ruleCheckResult));
        resContract.setErr_code(ruleCheckResult.getErrCode());
        resContract.setErr_msg(ruleCheckResult.getErrMsg());
        resContract.setExceed_msg(ruleCheckResult.getExceed_msg());
        resContract.setIs_price_exceed(ruleCheckResult.getIsPriceExceed());
        resContract.setCurrency_error_message(ruleCheckResult.getCurrencyErrorMessage());
        resContract.setCompany_pay_price(ruleCheckResult.getCompanyPayPrice());
        resContract.setTraveBooking(ruleCheckResult.getIsTraveBooking());
        resContract.setPersonal_pay(ruleCheckResult.getPersonalPay());
        resContract.setPersonal_pay_price(ruleCheckResult.getPersonalPayPrice());
        resContract.setAirPersonalPayModel(ruleCheckResult.getAirPersonalPayModel());
        if (ruleCheckResult.getIs_exceed()) {
            resContract.setIs_exceed(true);
        }

        resContract.setIs_change_reason(ruleCheckResult.getIsChangeReason());
        resContract.setIs_change_exceed_reason(ruleCheckResult.getIsExceedReason());
        return resContract;
    }


    /**
     * @MethodName airRefundRuleCheckResult
     * @Description 校验机票退款规则
     * @param: reqContract
     * @param: clientVersion
     * @param: token
     * @return: com.fenbeitong.saas.core.contract.order.check.TravelOnBusiOrderRuleCheckResult
     * <AUTHOR> Yunpeng
     * @Date 2022/11/7 14:22
     */
    private TravelOnBusiOrderRuleCheckResult airRefundRuleCheckResult(AirRefundCheckReqV2Contract reqContract, String clientVersion, String token) throws SaasException {
        // 默认初始化正常
        TravelOnBusiOrderRuleCheckResult checkResult = new TravelOnBusiOrderRuleCheckResult();
        checkResult.setResCode(GlobalResponseCode.Success);
        // 快照信息
        JSONObject snapshotInfo = new JSONObject();
        checkResult.setSnapshotInfo(snapshotInfo);

        // jira 需求：https://jira.fenbeijinfu.com/browse/CHANGJING-7401
        // 本期退票只有一个规则，且是场景通过 /air/rules/refund/detail 查询过了
        // 后续需求新增其他规则时需要校验时，需要校验其他

        // 公司id
        String companyId = reqContract.getTravel_on_busi_common_req_contract().getCompany_id();
        // 预定人id
        String employeeId = reqContract.getTravel_on_busi_common_req_contract().getEmployee_id();
        // 预定人姓名
        String employeeName = reqContract.getTravel_on_busi_common_req_contract().getEmployee_name();
        if (Objects.isNull(employeeId) || Objects.isNull(companyId)) {
            log.info("employeeId={}, companyId={}", employeeId, companyId);
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }

        //校验规则
        AirManagerSetting airManagerSetting = iMessageSetupRpcService.queryAirRuleExceedConfigMessage(companyId);
        Boolean isOpenExceedConfig = airManagerSetting.getIsOpenExceedConfig();
        Integer exceedConfigLever = airManagerSetting.getExceedConfigLever();
        logger.info("机票超规个人付:{}, exceedConfigLever:{}", isOpenExceedConfig, exceedConfigLever);
        if (isOpenExceedConfig && Objects.equals(exceedConfigLever, 2)) { // 机票超规个人付开始
            if (VersionTool.compare(clientVersion, AirRuleConstant.AIR_EXCEED_PERSONAL_PAY_CLIENT_VERSION) < 0) {
                throw new SaasException(GlobalResponseCode.AirTrainCenterAlert);
            }
        }

        // 预订人状态
        EmployeeContract orderEmployee = baseOrganizationService.getEmployee(employeeId, companyId);
        logger.info("预订人状态:{}", JsonUtils.toJson(orderEmployee));
        if (orderEmployee == null || orderEmployee.getStatus() != EmployeeStatusEnum.ACTIVE.getValue()) {
            checkResult.setResCode(GlobalResponseCode.OrderCheckEmployeeNotActive);
            return checkResult;
        }

        // 获取退票所有人员信息
        List<TravelOnBusiOrderCheckReqV2Contract.FrequentInfo> frequentInfoList = reqContract.getTravel_on_busi_common_req_contract().getFrequent_info();

        //初始化权限信息
        CompanyRuleSummary companyRuleSummary = currencyCheckService.assembleRefundEmployeeRuleSummary(companyId, employeeId,
                employeeName, BookingConfigEnum.AIR, frequentInfoList,clientVersion);

        //设置是否是个人原因
        companyRuleSummary.setIsPersonalReasons(Objects.equals(reqContract.getIsPersonalReasons(),1));

        // 是否是出行人模式
        checkResult.setIsTraveBooking(companyRuleSummary.getIsTraveBooking());

        // 企业飞机权限
        CompanyRuleDTO companyRule = iCompanyRuleService.queryByCompanyId(companyId);
        logger.info("企业飞机权限:{}", JsonUtils.toJson(companyRule));
        if (companyRule == null || companyRule.getAirRule() != 1) {
            log.info("企业无权限");
            checkResult.setResCode(GlobalResponseCode.OrderCheckCompanyNoAuth.getCode(), SaasContentConstant.AIR_COMPANY_NO_AUTH_MSG);
            return checkResult;
        }

        log.info("companyRuleSummary={}", JsonUtils.toJson(companyRuleSummary));
        companyRuleSummary.setCompanyRuleDTO(companyRule);
        checkResult.setCompanyRuleSummary(companyRuleSummary);

        // 初始化场景相关信息
        // 权限消费模版相关
        currencyCheckService.assembleAirRefundSceneSummary(reqContract, companyRuleSummary, clientVersion);

        log.info("companyRuleSummary after assembleAirRefund:{}", JsonUtils.toJson(companyRuleSummary));

        // 企业统一阻断判断
        //1.企业火车权限未开启
        if (companyRuleSummary.getIsRule()) {
            //设置状态码：100002 您所在的公司不允许火车,具体情况请联系管理员
            checkResult.setResCode(GlobalResponseCode.OrderCheckCompanyNoAuth.getCode(), SaasContentConstant.TRAIN_EMPLOYEE_NO_AUTH_MSG);
            return checkResult;
        }

        //2.企业余额不足
        else if(companyRuleSummary.getIsCompanyAccount()){
            //设置状态码：100002 您所在的公司不允许订购酒店,具体情况请联系管理员
            checkResult.setResCode(GlobalResponseCode.OrderCheckCompanyAccountNoAuth.getCode(), GlobalResponseCode.OrderCheckCompanyAccountNoAuth.getMsg());
            return checkResult;
        }
        //5.多人切个人原因退票，个人原因规则不一致
        if(companyRuleSummary.getIsPersonalReasonConflict()){
            checkResult.setResCode(GlobalResponseCode.AirTrainCenterAlert.getCode(),GlobalResponseCode.RefundPersonRuleNotMatch.getMsg());
            return checkResult;
        }

        // 5.1 规则不一致 单独改期
        if (companyRuleSummary.getIsRuleConflict()) {
            checkResult.setResCode(GlobalResponseCode.AirTrainCenterAlert.getCode(),GlobalResponseCode.RefundRuleNotSame.getMsg());
            return checkResult;
        }


        // 如果是企业支付
        // 因为退票校验之前查询了退票规则是否个人付，如果是个人付的话不会有企业支付选项，是个悖论，预留后续混合支付。11月24号跟亚男讨论结论，本期直接返回，企业支付
        if (AirRefundPayModel.COMPANY_PAY.equalsTo(reqContract.getRefundFeePayerType())) {
            log.info("公司原因，不校验规则...");
            setCompanyPay(reqContract, checkResult);
        } else {
            // 校验规则
            log.info("个人原因，校验规则...");
            currencyCheckService.checkAirRefundRule(reqContract, companyRuleSummary, clientVersion);
        }

        //6.多人有超规的情况下 规则不一致时 提示只能一个人一个人退票
        if(companyRuleSummary.getIsPersonalReasons() && companyRuleSummary.getEmployeeRuleSummaryList().size() > 1){
            checkResult.setResCode(GlobalResponseCode.AirTrainCenterAlert.getCode(),GlobalResponseCode.RefundPersonRuleNotMatch.getMsg());
            return checkResult;
        }

        // 设置退票每个人的最终结果集：综合考虑 规则校验 和 权限模版 配置 给出结果
        // 本期退票：如果超规，不会走后续审批。如果不超规则，看消费权限模版
        // 如果是二次强制提交，则看权限模版流程
        currencyCheckService.setEmployeeRefundResultEnum(companyRuleSummary, reqContract.getRefundFeePayerType());

        //设置总体返回是否有费用项超规
        checkResult.setIsPriceExceed(companyRuleSummary.getEmployeeRuleSummaryList().stream().anyMatch(a -> Objects.equals(a.getIsPriceExceed(), Boolean.TRUE)));
        //设置返回是否超规
        checkResult.setIs_exceed(companyRuleSummary.getEmployeeRuleSummaryList().stream().anyMatch(a-> Objects.equals(a.getIsExceed(), Boolean.TRUE)));
        //获取多人校验结果 统一返回值处理
        CurrencyErrorMessage currencyErrorMassage = currencyCheckService.getRefundPlayerResults(companyRuleSummary, checkResult, reqContract.getRefundFee());
        log.info("currencyErrorMassage:{}",JsonUtils.toJson(currencyErrorMassage));

        // 默认企业支付
        setCompanyPay(reqContract, checkResult);

        //超规全额付 无需理由
        if(GlobalResponseCode.AirRefundNeedPersonPay.getCode() == currencyErrorMassage.getCode() ){
            //设置公司支付金额 0
            checkResult.setCompanyPayPrice(BigDecimal.ZERO);
            checkResult.setPersonalPay(true);
            //个人支付金额为全额
            checkResult.setPersonalPayPrice(reqContract.getRefundFee());

        }
        //说明无法下单 需要给前端相应提示
        if(currencyErrorMassage.getCode() != GlobalResponseCode.Success.getCode()){
            checkResult.setCurrencyErrorMessage(currencyErrorMassage);
            checkResult.setResCode(GlobalResponseCode.CurrencyFrame);

            // 老版本消费模版填写理由退票需要兼容老版本，不检验
            if (GlobalResponseCode.CheckAndRefundReason.getCode() == currencyErrorMassage.getCode()
                && (VersionTool.compare(clientVersion, AirRuleConstant.AIR_EXCEED_PERSONAL_PAY_CLIENT_VERSION) < 0)) {
                checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                checkResult.setResCode(GlobalResponseCode.Success);
            }

            // 二次提交
            if (Objects.equals(reqContract.getResumbitForce(), Boolean.TRUE)) {
                // 订单审批 || 全额付直接放行
                if(GlobalResponseCode.CheckOrderApproval.getCode()== currencyErrorMassage.getCode() || GlobalResponseCode.AirRefundNeedPersonPay.getCode()== currencyErrorMassage.getCode() ){
                    checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                    checkResult.setResCode(GlobalResponseCode.Success);
                }
                // 退票或超规填写理由
                if(GlobalResponseCode.CheckAndRefundReason.getCode() == currencyErrorMassage.getCode()){
                    // 添加是否是个人理由和企业理由
                    ExceedPersonalPayCompanyConf conf = iMessageSetupService.getPersonalPayReasonConfBy(companyId);
                    checkResult.setExceedPersonalPayCompanyConf(conf);

                    //校验填写项是否正确
                    TravelOnBusiOrderRuleCheckResult travelOnBusiOrderRuleCheckResult = currencyCheckService.checkAirRefundReason(reqContract, currencyErrorMassage);
                    if(travelOnBusiOrderRuleCheckResult.getErrCode()!=GlobalResponseCode.Success.getCode()){
                        checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                        checkResult.setErrCode(travelOnBusiOrderRuleCheckResult.getErrCode());
                        checkResult.setErrMsg(travelOnBusiOrderRuleCheckResult.getErrMsg());
                    }else{
                        checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                        checkResult.setResCode(GlobalResponseCode.Success);
                    }
                }
            } else {
                if (GlobalResponseCode.AirRefundNeedPersonPay.getCode() == currencyErrorMassage.getCode()) {
                    checkResult.setCurrencyErrorMessage(currencyErrorMassage);
                    setPersonalPay(reqContract, checkResult);
                }
            }
        }

        //增加快照信息
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("companyRuleSummary",companyRuleSummary);
        checkResult.setSnapshotInfo(jsonObject);
        //完整日志打印
        log.info("companyRuleSummary-{}",JsonUtils.toJson(companyRuleSummary));
        return checkResult;
    }

    private void setPersonalPay(AirRefundCheckReqV2Contract reqContract, TravelOnBusiOrderRuleCheckResult checkResult) {
        checkResult.setAirPersonalPayModel(AirRefundPayModel.PERSONAL_PAY.getCode());
        checkResult.setCompanyPayPrice(BigDecimal.ZERO);
        checkResult.setPersonalPayPrice(reqContract.getRefundFee());
    }

    private void setCompanyPay(AirRefundCheckReqV2Contract reqContract, TravelOnBusiOrderRuleCheckResult checkResult) {
        checkResult.setAirPersonalPayModel(AirRefundPayModel.COMPANY_PAY.getCode());
        checkResult.setCompanyPayPrice(reqContract.getRefundFee());
        checkResult.setPersonalPayPrice(BigDecimal.ZERO);
    }


    /**
     * @MethodName airCitiesCheck
     * @Description 机票起降城市校验
     * @param: reqContract
     * @param: clientVersion
     * @param: token
     * @return: com.fenbeitong.saas.core.contract.order.check.TravelOnBusiOrderCheckResContract
     * <AUTHOR> Yunpeng
     * @Date 2022/11/17 14:24
     */
    @Override
    public TravelOnBusiOrderRuleCheckResult airCitiesCheck(AirCitiesCheckReqV2Contract reqContract, String clientVersion, String token) throws SaasException {
        log.info("req={},client={}", JsonUtils.toJson(reqContract), clientVersion);
        // 默认初始化正常
        TravelOnBusiOrderRuleCheckResult checkResult = new TravelOnBusiOrderRuleCheckResult();
        checkResult.setResCode(GlobalResponseCode.Success);
        // 快照信息
        JSONObject snapshotInfo = new JSONObject();
        checkResult.setSnapshotInfo(snapshotInfo);
        String employeeId = reqContract.getEmployeeId();
        String companyId = reqContract.getCompanyId();
        log.info("employeeId={}, companyId={}", employeeId, companyId);
        if (Objects.isNull(employeeId) || Objects.isNull(companyId)) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }

        AirManagerSetting airManagerSetting = iMessageSetupRpcService.queryAirRuleExceedConfigMessage(companyId);
        Boolean isOpenExceedConfig = airManagerSetting.getIsOpenExceedConfig();
        Integer exceedConfigLever = airManagerSetting.getExceedConfigLever();
        logger.info("机票超规个人付:{}, exceedConfigLever:{}", isOpenExceedConfig, exceedConfigLever);
        if (!isOpenExceedConfig || Objects.equals(exceedConfigLever, 1)) {
            log.info("企业未开启超规则个人付");
            return checkResult;
        }

        // 预订人状态
        EmployeeContract orderEmployee = baseOrganizationService.getEmployee(employeeId, companyId);
        logger.info("预订人状态:{}", JsonUtils.toJson(orderEmployee));
        if (orderEmployee == null || orderEmployee.getStatus() != EmployeeStatusEnum.ACTIVE.getValue()) {
            checkResult.setResCode(GlobalResponseCode.OrderCheckEmployeeNotActive);
            return checkResult;
        }
        // 企业飞机权限
        CompanyRuleDTO companyRule = iCompanyRuleService.queryByCompanyId(companyId);
        logger.info("企业飞机权限:{}", JsonUtils.toJson(companyRule));
        if (companyRule == null || companyRule.getAirRule() != 1) {
            checkResult.setResCode(GlobalResponseCode.OrderCheckCompanyNoAuth.getCode(), SaasContentConstant.AIR_COMPANY_NO_AUTH_MSG);
            return checkResult;
        }

        // 员工飞机权限
        EmployeeAirRule employeeAirRule = iBaseEmployeeAirRuleService.queryEmployeeAirRuleByPrimaryKey(employeeId, companyId);
        logger.info("员工飞机权限:{}", JsonUtils.toJson(employeeAirRule));
        checkResult.setEmployeeAirRule(employeeAirRule);
        snapshotInfo.put("authInfo", employeeAirRule);
        if (Objects.isNull(employeeAirRule)
                || Objects.isNull(employeeAirRule.getRefund_ticket_type())) {
            checkResult.setResCode(GlobalResponseCode.AirCheckEmployeeNoAuth.getCode(), SaasContentConstant.AIR_EMPLOYEE_NO_AUTH_MSG);
            return checkResult;
        }
        // 消费模版
        // 没有配置消费权限模版
        if (Objects.isNull(employeeAirRule.getAir_rule_flag()) || !employeeAirRule.getAir_rule_flag()) {
            log.info("不限制");
            return checkResult;
        }

        // 员工配置了权限
        //消费规则校验
        String ruleId = ObjUtils.ifNull(employeeAirRule.getManual_air_rule_id(), employeeAirRule.getDefault_air_rule_id());
        log.info("ruleId={},company_id={}", ruleId, companyId);
        AirRuleV2 airRuleV2 = iRuleV2Service.getAirRuleV2ById(ruleId, companyId);
        log.info("airRuleV2={}", JsonUtils.toJson(airRuleV2));

        // 起降城市、城市距离 和 火车联合管控是 "或" 的关系
        // Note1: pm:与火车联合管控计算了金额，但是一屏 根据起降城市查询航班 没有金额，故先判断是否配置了联合管控，如果配置了则直接返回，后续预定时再校验；没有配置则校验其他两项
        if (Objects.nonNull(airRuleV2)
                &&Objects.nonNull(airRuleV2.getCityConditionGroup())
                && Objects.nonNull(airRuleV2.getCityConditionGroup().getAirTrainCombinationGroup())
                && Objects.equals(airRuleV2.getCityConditionGroup().getAirTrainCombinationGroup().getTimeConsumeFlag(), Boolean.TRUE)
        ) {
            // 当配置了 火车联合管控为限制时 放行，后续下单时校验
            log.info("airRuleV2.getCityConditionGroup().getAirTrainCombinationGroup()={}", JsonUtils.toJson(airRuleV2.getCityConditionGroup().getAirTrainCombinationGroup()));
            return checkResult;
        }

        // airFlight1stScreenCityRuleCheckChainHandler.constructChain();
        AirFlightRuleCheckReq airFlightRuleCheckReq = new AirFlightRuleCheckReq();
        AirFlightRuleCheckReq.FlightInfo flightInfo = new AirFlightRuleCheckReq.FlightInfo();
        flightInfo.setToken(token);
        flightInfo.setAirFirstScreenCheck(true); // 是否为机票一屏
        flightInfo.setCompanyId(companyId);
        boolean isGoback = reqContract.getFlightType() == 2;
        flightInfo.setIsGoBack(isGoback);                   // 是否往返
        flightInfo.setCabin(reqContract.getAirType());      // 舱型
        flightInfo.setStartCityId(reqContract.getTakeoffCityCode());   // 出发城市
        flightInfo.setArrivalCityId(reqContract.getLandingCityCode());  // 抵达城市
        flightInfo.setStartCityIds(Lists.newArrayList(reqContract.getTakeoffCityCode()));
        flightInfo.setArrivalCityIds(Lists.newArrayList(reqContract.getLandingCityCode()));
        AirInterceptRecordContract airInterceptRecordContract = new AirInterceptRecordContract();
        airInterceptRecordContract.setTrip_type(1);
        flightInfo.setOrderParameterJson(airInterceptRecordContract);
        flightInfo.setOrderParameterJsonList(Lists.newArrayList(airInterceptRecordContract));
        airFlightRuleCheckReq.setFlightInfo(flightInfo);    // 航班信息汇总
        airFlightRuleCheckReq.setRuleInfo(airRuleV2);
        airFlightRuleCheckReq.setRuleId(ruleId);
        AirFlightRuleCheckRes airFlightRuleCheckRes = new AirFlightRuleCheckRes();

        log.info("before-handler-airFlightRuleCheckReq={},airFlightRuleCheckRes={}", JsonUtils.toJson(airFlightRuleCheckReq), JsonUtils.toJson(airFlightRuleCheckRes));
        airFlightCityRuleCheckChainHandler.executionChain(airFlightRuleCheckReq, airFlightRuleCheckRes);
        log.info("after-handler:airFlightRuleCheckReq={},airFlightRuleCheckRes={}", JsonUtils.toJson(airFlightRuleCheckReq), JsonUtils.toJson(airFlightRuleCheckRes));


        Boolean cityRuleCheckRes = AirRuleUtils.getCityRuleCheck(airFlightRuleCheckRes.getPerRuleCheckResList());
        airFlightRuleCheckRes.setIsExceed(!cityRuleCheckRes);
        log.info("cityRuleCheckRes={}", cityRuleCheckRes);
        // 规则：1 城市超规（没有规定的起降城市或者航线），2 距离超规 3 白名单超规则（城市没在白名单）
        // 如果有任意一个规则不超规：
        if (Objects.equals(cityRuleCheckRes, Boolean.TRUE)) {
            log.info("城市或距离校验通过");
            return checkResult;
        }

        // 责任链获取处理结果
        // 封装msg
        Air1ScreenExceedMsg air1ScreenExceedMsg = new Air1ScreenExceedMsg();
        air1ScreenExceedMsg.setAirRuleCheckResultDetailV2List(new ArrayList<>());
        air1ScreenExceedMsg.setRuleDetailList(new ArrayList<>());
        air1ScreenExceedMsg.setBtnList(new ArrayList<>());

        // 处理超规结果集：三种：1 城市超规（没有规定的起降城市或者航线），2 距离超规 3 白名单超规则（城市没在白名单）
        List<AirFlightRuleCheckRes.PerRuleCheckRes> ruleCheckResList = airFlightRuleCheckRes.getPerRuleCheckResList();
        HashMap<String, List<RuleDetail>> ruleDetailMapByTiTle = new HashMap<>();

        log.info("airRuleV2:{}", JsonUtils.toJson(airRuleV2));
        for (AirFlightRuleCheckRes.PerRuleCheckRes perRuleCheckRes : ruleCheckResList) {
            AirCityCheckChainOrderEnum orderEnum = AirCityCheckChainOrderEnum.valuesOf(perRuleCheckRes.getHandlerOrder());
            Air1ScreenCheckTitleMapChainOrderEnum air1ScreenOrderEnum = Air1ScreenCheckTitleMapChainOrderEnum.getValueBy(orderEnum);
            log.info("orderEnum={},air1ScreenOrderEnum={}", JsonUtils.toJson(orderEnum), air1ScreenOrderEnum);

            if (Objects.isNull(orderEnum) || Objects.isNull(air1ScreenOrderEnum)) {
                continue;
            }

            // 有超规则项
            log.info("城市校验有超规则项");

            // 1 起降城市相关
            if (Objects.equals(orderEnum, AirCityCheckChainOrderEnum.TAKE_OFF_AND_LANDING)
                    && !ruleDetailMapByTiTle.containsKey(Air1ScreenPageConstant.RULE_TITLE_TAKEOFF_LAND_CITY)) {
                if (Objects.isNull(airRuleV2.getCityConditionGroup())
                        || !Objects.equals(airRuleV2.getCityConditionGroup().getCityConditionGroupFlag(), Boolean.TRUE)
                        || !Objects.equals(airRuleV2.getCityConditionGroup().getTakeOffLandingCityGroup().getTakeOffLandingCityGroupFlag(), Boolean.TRUE)){
                    log.info("起降城市相关规则不限制");
                    continue;
                }

                List<AirTakeoffLandingType> airTakeoffLandingTypeList = airRuleV2.getCityConditionGroup().getTakeOffLandingCityGroup().getAirTakeoffLandingTypes().stream().filter(a -> Objects.equals(a.getLimitFlag(), Boolean.TRUE)).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(airTakeoffLandingTypeList)) {
                    log.info("没有需要限制的起降城市");
                    continue;
                }
                setRuleDetailMap(ruleDetailMapByTiTle, airTakeoffLandingTypeList);

                // 添加起降城市相关规则详情
                if (CollectionUtils.isNotEmpty(ruleDetailMapByTiTle.get(Air1ScreenPageConstant.RULE_TITLE_TAKEOFF_LAND_CITY))) {
                    AirRuleCheckResultDetailV2 airRuleCheckResultDetailV2 = new AirRuleCheckResultDetailV2();
                    airRuleCheckResultDetailV2.setRuleDesc(Air1ScreenPageConstant.RULE_TITLE_TAKEOFF_LAND_CITY);
                    airRuleCheckResultDetailV2.setRuleDetailList(ruleDetailMapByTiTle.get(Air1ScreenPageConstant.RULE_TITLE_TAKEOFF_LAND_CITY));
                    air1ScreenExceedMsg.getAirRuleCheckResultDetailV2List().add(airRuleCheckResultDetailV2);
                }
            }


            // 2 航班行程距离
            if (Objects.equals(orderEnum, AirCityCheckChainOrderEnum.DISTANCE)
                    && !ruleDetailMapByTiTle.containsKey(Air1ScreenPageConstant.RULE_TITLE_FLIGHT_DISTANCE)) {
                if (Objects.isNull(airRuleV2.getCityConditionGroup())
                        || !Objects.equals(airRuleV2.getCityConditionGroup().getCityConditionGroupFlag(), Boolean.TRUE)
                        || !Objects.equals(airRuleV2.getCityConditionGroup().getDistanceLimitGroup().getDistanceLimitFlag(), Boolean.TRUE)){
                    log.info("航班行程距离规则不限制");
                    continue;
                }
                Integer distanceLimitValue = airRuleV2.getCityConditionGroup().getDistanceLimitGroup().getDistanceLimitValue();
                AirRuleCheckResultDetailV2 airRuleCheckResultDetailV2 = new AirRuleCheckResultDetailV2();
                RuleDetail ruleDetail = new RuleDetail();
                ruleDetail.setRuleLimit(String.format(Air1ScreenPageConstant.RULE_DESC_DISTANCE, distanceLimitValue));
                airRuleCheckResultDetailV2.setRuleDetailList(Lists.newArrayList(ruleDetail));
                airRuleCheckResultDetailV2.setRuleDesc(Air1ScreenPageConstant.RULE_TITLE_FLIGHT_DISTANCE);
                air1ScreenExceedMsg.getAirRuleCheckResultDetailV2List().add(airRuleCheckResultDetailV2);
                ruleDetailMapByTiTle.put(Air1ScreenPageConstant.RULE_TITLE_FLIGHT_DISTANCE, Lists.newArrayList(ruleDetail));
            }
        }

        // 20221125规则详情列表展示修改，改成不分组
        if (CollectionUtils.isNotEmpty(air1ScreenExceedMsg.getAirRuleCheckResultDetailV2List())) {
            for (AirRuleCheckResultDetailV2 groupDetailV2 : air1ScreenExceedMsg.getAirRuleCheckResultDetailV2List()) {
                if (CollectionUtils.isNotEmpty(groupDetailV2.getRuleDetailList())) {
                    air1ScreenExceedMsg.getRuleDetailList().addAll(groupDetailV2.getRuleDetailList());
                }
            }
        }

        // 如果有超规则项, 则查超规则措施给予按钮
        Integer exceedConfig = null;
        if (CollectionUtils.isNotEmpty(ruleDetailMapByTiTle.keySet())) {
            exceedConfig = airRuleV2.getCityConditionGroup().getCityConditionConfig();
        }
        // 超规则措施：按钮动作
        if (Objects.equals(ExceedConfigTypeEnum.EXCEED_NOT_ALLOW.getCompareOrder(), exceedConfig)) {
            air1ScreenExceedMsg.setTitle(Air1ScreenPageConstant.TITLE_1_ORDER_EXCEED);
            air1ScreenExceedMsg.setMessage(Air1ScreenPageConstant.TIP_2_CITY_CANNOT_ORDER); // 灰字，违反规则，请确认：
            air1ScreenExceedMsg.setDesc(Air1ScreenPageConstant.TIP_1_CITY_CANNOT_ORDER); // 黄字，禁止下单
            ActionButton actionButton = new ActionButton(ActionButtonEnum.IKnowAndToChangeTakeoffAndLandCity);
            air1ScreenExceedMsg.setBtnList(Lists.newArrayList(actionButton));
        } else if (Objects.equals(ExceedConfigTypeEnum.COMPANY_PAY_SUBMIT_APPROVAL.getCompareOrder(), exceedConfig)) {
            air1ScreenExceedMsg.setTitle(Air1ScreenPageConstant.TITLE_1_ORDER_EXCEED);
            air1ScreenExceedMsg.setMessage(Air1ScreenPageConstant.TIP_2_CITY_NEED_APPROVE);
            air1ScreenExceedMsg.setDesc(Air1ScreenPageConstant.TIP_1_CITY_NEED_APPROVE);
            ActionButton actionButton = new ActionButton(ActionButtonEnum.ContinueOrder);
            air1ScreenExceedMsg.setBtnList(Lists.newArrayList(actionButton));
        } else if (Objects.equals(ExceedConfigTypeEnum.EXCEED_SUBMIT_APPROVAL_OR_PERSONAL_PAY.getCompareOrder(), exceedConfig)) {
            air1ScreenExceedMsg.setTitle(Air1ScreenPageConstant.TITLE_1_ORDER_EXCEED);
            air1ScreenExceedMsg.setMessage(Air1ScreenPageConstant.TIP_2_CITY_NEED_APPROVE_OR_PAY);
            air1ScreenExceedMsg.setDesc(Air1ScreenPageConstant.TIP_1_CITY_NEED_APPROVE_OR_PAY);
            ActionButton actionButton = new ActionButton(ActionButtonEnum.ContinueOrder);
            air1ScreenExceedMsg.setBtnList(Lists.newArrayList(actionButton));
        } else if (Objects.equals(ExceedConfigTypeEnum.EXCEED_SUBMIT_APPROVAL_OR_PAY_DIFFERENCE_AMOUNT.getCompareOrder(), exceedConfig)) {
            air1ScreenExceedMsg.setTitle(Air1ScreenPageConstant.TITLE_1_ORDER_EXCEED);
            air1ScreenExceedMsg.setMessage(Air1ScreenPageConstant.TIP_2_CITY_NEED_APPROVE_OR_PAY);
            air1ScreenExceedMsg.setDesc(Air1ScreenPageConstant.TIP_1_CITY_NEED_APPROVE_OR_PAY);
            ActionButton actionButton = new ActionButton(ActionButtonEnum.ContinueOrder);
            air1ScreenExceedMsg.setBtnList(Lists.newArrayList(actionButton));
        } else if (Objects.equals(ExceedConfigTypeEnum.COMPANY_PAY_BY_REASON.getCompareOrder(), exceedConfig)) {
            air1ScreenExceedMsg.setTitle(Air1ScreenPageConstant.TITLE_1_ORDER_EXCEED);
            air1ScreenExceedMsg.setMessage(Air1ScreenPageConstant.TIP_2_CITY_NEED_REASON);
            air1ScreenExceedMsg.setDesc(Air1ScreenPageConstant.TIP_1_CITY_NEED_REASON);
            ActionButton actionButton = new ActionButton(ActionButtonEnum.ContinueOrder);
            air1ScreenExceedMsg.setBtnList(Lists.newArrayList(actionButton));
        } else {
            air1ScreenExceedMsg.setTitle(Air1ScreenPageConstant.TITLE_1_ORDER_EXCEED);
            air1ScreenExceedMsg.setMessage(Air1ScreenPageConstant.TIP_2_CITY_NEED_PERSON_PAY);
            air1ScreenExceedMsg.setDesc(Air1ScreenPageConstant.TIP_1_CITY_NEED_PERSONAL_PAY);
            ActionButton actionButton = new ActionButton(ActionButtonEnum.ContinueOrder);
            air1ScreenExceedMsg.setBtnList(Lists.newArrayList(actionButton));
        }

        checkResult.setAir1ScreenExceedMsg(air1ScreenExceedMsg);
        return checkResult;
    }

    /**
     * @MethodName setRuleDetailMap
     * @Description 设置规则详情map
     * @param: ruleDetailMapByTiTle
     * @param: airTakeoffLandingTypeList
     * <AUTHOR> Yunpeng
     * @Date 2022/11/24 20:08
     */
    private void setRuleDetailMap(HashMap<String, List<RuleDetail>> ruleDetailMapByTiTle, List<AirTakeoffLandingType> airTakeoffLandingTypeList) {
        // 从xx起飞到任意城市降落
        List<AirTakeoffLandingType> takeoffCityLimit = airTakeoffLandingTypeList.stream()
                .filter(a -> Objects.equals(AirCityLimitEnum.takeoff_city_limit.getCode(), a.getType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(takeoffCityLimit)) {
            // 城市名称
            ArrayList<String> cityNameList = new ArrayList<>();
            for (AirTakeoffLandingType landingType : takeoffCityLimit) {
                List<String> names = landingType.getAirTakeoffLandingCities().stream().map(AirTakeoffLandingCity::getCityName).collect(Collectors.toList());
                cityNameList.addAll(names);
            }
            if (CollectionUtils.isEmpty(cityNameList)) {
                log.info("从xx起飞到任意城市降落:城市名称list 为空");
                return;
            }
            String allCityName = String.join("、", cityNameList);
            String ruleLimit = String.format(Air1ScreenPageConstant.RULE_DESC_TAKEOFF_CITY, allCityName);
            RuleDetail ruleDetail = new RuleDetail();
            ruleDetail.setRuleLimit(ruleLimit);
            if (Objects.isNull(ruleDetailMapByTiTle.get(Air1ScreenPageConstant.RULE_TITLE_TAKEOFF_LAND_CITY))) {
                ruleDetailMapByTiTle.put(Air1ScreenPageConstant.RULE_TITLE_TAKEOFF_LAND_CITY, new ArrayList<>());
            }
            ruleDetailMapByTiTle.get(Air1ScreenPageConstant.RULE_TITLE_TAKEOFF_LAND_CITY).add(ruleDetail);
        }
        // 从任意起飞到xx降落
        List<AirTakeoffLandingType> landCityLimit = airTakeoffLandingTypeList.stream()
                .filter(a -> Objects.equals(AirCityLimitEnum.landing_city_limit.getCode(), a.getType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(landCityLimit)) {
            // 城市名称
            ArrayList<String> cityNameList = new ArrayList<>();
            for (AirTakeoffLandingType landingType : landCityLimit) {
                List<String> names = landingType.getAirTakeoffLandingCities().stream().map(AirTakeoffLandingCity::getCityName).collect(Collectors.toList());
                cityNameList.addAll(names);
            }
            if (CollectionUtils.isEmpty(cityNameList)) {
                log.info("从任意起飞到xx降落:城市名称list 为空");
                return;
            }
            String allCityName = String.join("、", cityNameList);
            String ruleLimit = String.format(Air1ScreenPageConstant.RULE_DESC_LAND_CITY, allCityName);
            RuleDetail ruleDetail = new RuleDetail();
            ruleDetail.setRuleLimit(ruleLimit);
            if (Objects.isNull(ruleDetailMapByTiTle.get(Air1ScreenPageConstant.RULE_TITLE_TAKEOFF_LAND_CITY))) {
                ruleDetailMapByTiTle.put(Air1ScreenPageConstant.RULE_TITLE_TAKEOFF_LAND_CITY, new ArrayList<>());
            }
            ruleDetailMapByTiTle.get(Air1ScreenPageConstant.RULE_TITLE_TAKEOFF_LAND_CITY).add(ruleDetail);
        }
        // 从%s起飞并降落的航班
        List<AirTakeoffLandingType> takeoffLandingLimit = airTakeoffLandingTypeList.stream()
                .filter(a -> Objects.equals(AirCityLimitEnum.takeoff_and_landing_city.getCode(), a.getType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(takeoffLandingLimit)) {
            // 城市名称
            ArrayList<String> cityNameList = new ArrayList<>();
            for (AirTakeoffLandingType landingType : takeoffLandingLimit) {
                List<String> names = landingType.getAirTakeoffLandingCities().stream().map(AirTakeoffLandingCity::getCityName).collect(Collectors.toList());
                cityNameList.addAll(names);
            }
            if (CollectionUtils.isEmpty(cityNameList)) {
                log.info("从xxxx起飞降落:城市名称list 为空");
                return;
            }
            String allCityName = String.join("、", cityNameList);
            String ruleLimit = String.format(Air1ScreenPageConstant.RULE_DESC_TAKEOFF_AND_LAND_CITY, allCityName);
            RuleDetail ruleDetail = new RuleDetail();
            ruleDetail.setRuleLimit(ruleLimit);
            if (Objects.isNull(ruleDetailMapByTiTle.get(Air1ScreenPageConstant.RULE_TITLE_TAKEOFF_LAND_CITY))) {
                ruleDetailMapByTiTle.put(Air1ScreenPageConstant.RULE_TITLE_TAKEOFF_LAND_CITY, new ArrayList<>());
            }
            ruleDetailMapByTiTle.get(Air1ScreenPageConstant.RULE_TITLE_TAKEOFF_LAND_CITY).add(ruleDetail);
        }

        // 指定航线
        List<AirTakeoffLandingType> flightLimit = airTakeoffLandingTypeList.stream()
                .filter(a -> Objects.equals(AirCityLimitEnum.fight_limit.getCode(), a.getType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(flightLimit)) {
            // 城市名称
            ArrayList<String> cityNameList = new ArrayList<>();
            for (AirTakeoffLandingType landingType : flightLimit) {
                List<String> names = landingType.getAirTakeoffLandingCities().stream().map(AirTakeoffLandingCity::getCityName).collect(Collectors.toList());
                cityNameList.addAll(names);
            }
            if (CollectionUtils.isEmpty(cityNameList)) {
                log.info("指定航线:城市名称list 为空");
                return;
            }
            StringBuilder builder = new StringBuilder();
            cityNameList.forEach(name -> builder.append("；").append(String.format(Air1ScreenPageConstant.RULE_DESC_FLIGHT_LINE, name)));
            builder.deleteCharAt(0);
            RuleDetail ruleDetail = new RuleDetail();
            ruleDetail.setRuleLimit(builder.toString());
            if (Objects.isNull(ruleDetailMapByTiTle.get(Air1ScreenPageConstant.RULE_TITLE_TAKEOFF_LAND_CITY))) {
                ruleDetailMapByTiTle.put(Air1ScreenPageConstant.RULE_TITLE_TAKEOFF_LAND_CITY, new ArrayList<>());
            }
            ruleDetailMapByTiTle.get(Air1ScreenPageConstant.RULE_TITLE_TAKEOFF_LAND_CITY).add(ruleDetail);
        }
    }

    /**
     * 删除「上海市(虹桥机场、浦东机场)」 中的 (虹桥机场、浦东机场)
     * @param allCityName
     * @return
     */
    private String removeAirports(String allCityName) {
        return allCityName.replaceAll(REMOVE_CITY_REGEX, AirRuleConstant.BLANK_STR);
    }
}
