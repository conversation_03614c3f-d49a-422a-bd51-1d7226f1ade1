package com.fenbeitong.saas.core.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.func.Func;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.bank.api.constant.enums.BankCheckInfoOperationType;
import com.fenbeitong.bank.api.model.BankApplyCreditReqDTO;
import com.fenbeitong.bank.api.model.BankCheckAllInfoReqDTO;
import com.fenbeitong.bank.api.service.IBankHuPoFBTService;
import com.fenbeitong.common.enums.basis.SupplierType;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.constant.FinhubMessageCode;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.saas.entity.CostAttribution;
import com.fenbeitong.finhub.kafka.msg.message.center.KafkaMessageCenterPushMsg;
import com.fenbeitong.finhub.kafka.msg.saas.KafkaPushMsg;
import com.fenbeitong.noc.api.service.bank.service.IBankOrderService;
import com.fenbeitong.saas.api.model.dto.reason.ReasonConfig;
import com.fenbeitong.saas.api.model.enums.TaxiSubType;
import com.fenbeitong.saas.card.api.order.api.channel.saas.IOrderUpdateRpcCallerSaasApi;
import com.fenbeitong.saas.core.common.base.SaasException;
import com.fenbeitong.saas.core.common.base.SaasResponseEntity;
import com.fenbeitong.saas.core.common.constant.CommonSwitchConstant;
import com.fenbeitong.saas.core.common.constant.GlobalResponseCode;
import com.fenbeitong.saas.core.common.constant.SaasAreaConstant;
import com.fenbeitong.saas.core.common.constant.SaasFlowConstant;
import com.fenbeitong.saas.core.common.constant.SaasMessageConstant;
import com.fenbeitong.saas.core.common.constant.SaasOrderThirdRuleConstant;
import com.fenbeitong.saas.core.contract.apply.*;
import com.fenbeitong.saas.core.contract.applyflow.CompanyApplyFlowSetV2RequestContract;
import com.fenbeitong.saas.core.contract.budget.ThirdBudgetCheckReqContract;
import com.fenbeitong.saas.core.contract.budget.ThirdBudgetCheckResult;
import com.fenbeitong.saas.core.contract.city.VenderCityInfoContract;
import com.fenbeitong.saas.core.contract.common.*;
import com.fenbeitong.saas.core.contract.cost.CostCheckVO;
import com.fenbeitong.saas.core.contract.messagesettings.ApplySetupContract;
import com.fenbeitong.saas.core.contract.reason.*;
import com.fenbeitong.saas.core.contract.rule.MyRuleDisployContract;
import com.fenbeitong.saas.core.contract.user.EmployeeNameAndDeptContract;
import com.fenbeitong.saas.core.contract.user.UserBizAuthWithVerify;
import com.fenbeitong.saas.core.contract.user.UserContactContract;
import com.fenbeitong.saas.core.dao.fenbeitong.TaxiApproveRuleMapper;
import com.fenbeitong.saas.core.dao.saasplus.TaxiManageSettingMapper;
import com.fenbeitong.saas.core.language.CoreLanguage;
import com.fenbeitong.saas.core.language.LanguageUtils;
import com.fenbeitong.saas.core.model.enums.CcNoticeType;
import com.fenbeitong.saas.core.model.enums.CompanyApplyType;
import com.fenbeitong.saas.core.model.enums.CostAttributionCategory;
import com.fenbeitong.saas.core.model.enums.OrderCategory;
import com.fenbeitong.saas.core.model.enums.apply.*;
import com.fenbeitong.saas.core.model.enums.reason.ApplyCategoryEnum;
import com.fenbeitong.saas.core.model.enums.reason.ReasonType;
import com.fenbeitong.saas.core.model.enums.rule.TakeawayRuleType;
import com.fenbeitong.saas.core.model.enums.rule.TaxiModelsType;
import com.fenbeitong.saas.core.model.enums.rule.taxi.AllowSameCityLimitType;
import com.fenbeitong.saas.core.model.enums.user.UserRole;
import com.fenbeitong.saas.core.model.fenbeitong.TaxiApproveRule;
import com.fenbeitong.saas.core.model.saas.*;
import com.fenbeitong.saas.core.model.saasplus.TaxiManageSetting;
import com.fenbeitong.saas.core.model.saasplus.TaxiManageSettingExample;
import com.fenbeitong.saas.core.service.*;
import com.fenbeitong.saas.core.service.taxi.inner.TaxiRedisControlService;
import com.fenbeitong.saas.core.utils.constant.UserConstant;
import com.fenbeitong.saas.core.utils.log.LogUtil;
import com.fenbeitong.saas.core.utils.tools.*;
import com.fenbeitong.saasplus.api.model.dto.finance.CostSaveResult;
import com.fenbeitong.saasplus.api.model.dto.finance.OrderCostInfoReq;
import com.fenbeitong.saasplus.api.model.dto.reason.ApplyMallConfigDTO;
import com.fenbeitong.saasplus.api.model.dto.rule.TakeawayAuthContract;
import com.fenbeitong.saasplus.api.model.dto.rule.TakeawayContract;
import com.fenbeitong.saasplus.api.model.dto.rule.TaxiApproveRuleContract;
import com.fenbeitong.saasplus.api.model.dto.rule.TaxiRuleForApproveContract;
import com.fenbeitong.saasplus.api.model.dto.rule.TaxiSceneListVO;
import com.fenbeitong.saasplus.api.model.dto.util.KvStrContract;
import com.fenbeitong.saasplus.api.model.enums.rule.TaxiApproveCityLimitType;
import com.fenbeitong.saasplus.api.model.enums.savings.LimitType;
import com.fenbeitong.saasplus.api.model.enums.savings.TaxiType;
import com.fenbeitong.saasplus.api.model.po.fenbeitong.rulenew.TimesLimitTypeEnum;
import com.fenbeitong.saasplus.api.model.vo.rule.TaxiExceedConfigQueryRes;
import com.fenbeitong.saasplus.api.service.apply.IApplyOccupyBudgetService;
import com.fenbeitong.saasplus.api.service.finance.IOrderCostService;
import com.fenbeitong.saasplus.api.service.reason.ICustomReasonAttrService;
import com.fenbeitong.saasplus.api.service.rule.IBaseTakeawayRuleExtService;
import com.fenbeitong.saasplus.api.service.rule.ITaxiApproveRuleService;
import com.fenbeitong.travel.rule.api.service.apply.ICustomApplyService;
import com.fenbeitong.usercenter.api.model.dto.costcenter.CostInfoContract;
import com.fenbeitong.usercenter.api.model.dto.costcenter.EmployeeCostCenter;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.fenbeitong.usercenter.api.model.dto.meta.FinancialManagementVO;
import com.fenbeitong.usercenter.api.model.dto.rule.CarPolicyBean;
import com.fenbeitong.usercenter.api.model.dto.rule.RuleIdDto;
import com.fenbeitong.usercenter.api.service.meta.IMetaService;
import com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeTaxiRuleExtService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.net.HttpClientUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.DateUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.StrUtils;
import com.luastar.swift.http.constant.HttpConstant;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.joda.time.format.DateTimeFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
@Service
@Transactional(transactionManager = "dynamic", readOnly = false, rollbackFor = Exception.class)
public class ApplyThirdServiceImpl implements IApplyThirdService {
    private static final Logger logger = LoggerFactory.getLogger(ApplyThirdServiceImpl.class);
    private static final String URL_GET_CITY_DATA = HostPropertyConfigTool.HOST_HARMONY + "/city/areas/listStationsByAreaIds";
    private static final String URL_GET_FREQUENT_DATA = HostPropertyConfigTool.HOST_USERCENTER + "/uc/frequent/query_frequent_info";
    private static final String URL_GET_APPROVE_DATA = HostPropertyConfigTool.HOST_OPENAPI + "/open/hgm/company/approve_type";
    private static final String URL_GET_COMPANY_ISDOCKING = HostPropertyConfigTool.HOST_OPENAPI_PLUS + "/openapi/func/company/isDocking/";
    private static final String URL_POST_APPLY_ORDER_CREATE = HostPropertyConfigTool.HOST_OPENAPI_PLUS + "/openapi/func/apply/order/notifyApplyCreated";
    private static final String URL_POST_APPLY_MALL_ORDER_CREATE = HostPropertyConfigTool.HOST_OPENAPI_PLUS + "/openapi/func/apply/mall/notifyApplyCreated";
    private static final String URL_POST_BUDGET_CHECK = HostPropertyConfigTool.HOST_OPENAPI_PLUS + "/openapi/func/company/budget/check";
    private static final String URL_GET_TRAIN_CHANGE_VALID_ORDER_INFO = HostPropertyConfigTool.HOST_HYPERLOOP + "/v2/internal/api/train/order/endorse_check";
    private static final String URL_GET_TRAIN_REFUNG_VALID_ORDER_INFO = HostPropertyConfigTool.HOST_TRAIN_BIZ + "/internal/orders/trains/refund_check";
    private static final String URL_GET_AIR_REFUNGCHANGE_ORDER_INFO = HostPropertyConfigTool.HOST_INTL_AIR_BIZ + "/internal/refundChange/air/order/detail";
    private static final String URL_GET_HOTEL_ORDER_INFO = HostPropertyConfigTool.HOST_HOTEL_BIZ + "/internal/refund/hotel/order/detail";
    private static final String URL_GET_TRAIN_ORDER_INFO = HostPropertyConfigTool.HOST_TRAIN_BIZ + "/internal/orders/trains/detail_apply";
    private static final String URL_POST_APPLY_REFUND_CHANGE_ORDER_CREATE = HostPropertyConfigTool.HOST_OPENAPI_PLUS + "/openapi/func/apply/order/notifyApplyChanged";
    private static final String URL_GET_AIR_REFUNG_ORDER_INFO = HostPropertyConfigTool.HOST_INTL_AIR_BIZ + "/internal/api/air/batchRefund";
    private static final String URL_GET_AIR_CHANGE_ORDER_INFO = HostPropertyConfigTool.HOST_INTL_AIR_BIZ + "/internal/api/air/order/endorse";
    private static final String URL_GET_TRAIN_REFUNG_ORDER_INFO = HostPropertyConfigTool.HOST_HYPERLOOP + "/v2/internal/api/train/order/refund";
    private static final String URL_GET_TRAIN_CHANGE_ORDER_INFO = HostPropertyConfigTool.HOST_HYPERLOOP + "/v2/internal/api/train/order/endorse";
    private static final String URL_GET_HOTEL_REFUNG_ORDER_INFO = HostPropertyConfigTool.HOST_HYPERLOOP + "/internal/hotel/order/refund";
    private static final String URL_POST_APPLY_TRAVEL_ORDER_CREATE = HostPropertyConfigTool.HOST_OPENAPI_PLUS + "/openapi/func/apply/trip/notifyApplyCreated";
    private static final String URL_POST_APPLY_TAXI_ORDER_CREATE = HostPropertyConfigTool.HOST_OPENAPI_PLUS + "/openapi/func/apply/taxi/notifyApplyCreated";
    private static final String URL_POST_APPLY_DINNER_ORDER_CREATE = HostPropertyConfigTool.HOST_OPENAPI_PLUS + "/openapi/func/apply/dinner/notifyApplyCreated";

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyOrderMapper applyMapper;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyTripInfoMapper applyTripMapper;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyTripGuestMapper applyGuestMapper;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyTripGuestExtMapper applyGuestExtMapper;

    @Autowired
    IUserService userService;

    @Autowired
    ICityService cityService;

    @Autowired
    IMessageService messageService;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyOrderExtMapper applyOrderExtMapper;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyTripInfoExtMapper applyTripInfoExtMapper;

    @Autowired
    private IBaseOrganizationService iBaseOrganizationService;

    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyRuleSettingMapper applyRuleSettingMapper;

    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyRuleSettingExtMapper applyRuleSettingExtMapper;

    @Autowired
    private IApplyV2Service applyV2Service;

    @Autowired
    private TaxiApproveRuleMapper taxiApproveRuleMapper;

    @Autowired
    private ITaxiApproveRuleService iTaxiApproveRuleService;

    @Autowired
    IMessageSetupService messageSetupService;

    @Autowired
    IBankHuPoFBTService iBankHuPoFBTService;

    @Autowired
    private ICustomReasonService customReasonService;

    @Autowired
    private com.fenbeitong.usercenter.api.service.costcenter.ICostCenterService costCenterService;

    @Autowired
    private IBaseTakeawayRuleExtService iBaseTakeawayRuleExtService;

    @Autowired
    IMetaService iMetaService;

    @Autowired
    IBankOrderService iBankOrderService;

    @Autowired
    IApplyWriteoffService applyWriteoffService;

    @Autowired
    private IApplyCenterService applyCenterService;

    @Autowired
    private IApplyService applyService;

    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyCostAttributionExtMapper applyCostAttributionExtMapper;

    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyTripApplicateExtMapper applyTripApplicateExtMapper;

    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyTravelTimeMapper applyTravelTimeMapper;

    @Autowired
    IRuleService ruleService;

    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyTripApplicateMapper applyTripApplicateMapper;

    @Autowired
    ICustomReasonAttrService customReasonAttrService;

    @Autowired
    IOrderCostService orderCostService;

    @Autowired
    ICommonValidService commonValidService;

    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyTravelTimeExtMapper applyTravelTimeExtMapper;

    @Autowired
    IPushKafkaProducerService pushKafkaProducerService;

    @Autowired
    private IOrderUpdateRpcCallerSaasApi iOrderUpdateRpcCallerSaasApi;

    @Autowired
    private com.fenbeitong.saasplus.api.service.messageSetup.IMessageSetupService iMessageSetupRpcService;

    @Autowired
    private CurrencyCheckServiceImpl currencyCheckService;

    @Resource
    private TaxiRedisControlService taxiRedisControlService;

    @Resource
    TaxiManageSettingMapper taxiManageSettingMapper;
    @Autowired
    private IBaseEmployeeTaxiRuleExtService iBaseEmployeeTaxiRuleExtService;

    @Autowired
    private IApplyOccupyBudgetService iApplyOccupyBudgetService;

    @Autowired
    private ICustomApplyService iCustomApplyService;

    /**
     * 创建申请单
     *
     * @param applyContract
     * @return
     */
    @Override
    public GlobalResponseCode create(String token, ApplyThirdContract applyContract, String userId, String companyId, String ip, String applyId) throws SaasException {
        //检查数据信息
        GlobalResponseCode code = checkApplyData(applyContract, userId, companyId);
        if (code != GlobalResponseCode.Success) {
            return code;
        }
        ApplyOrderThirdContract applyorderContract = applyContract.getApply();
        //创建申请单
        applyorderContract.setId(applyId);
        applyorderContract.setEmployee_id(userId);
        applyorderContract.setCompany_id(companyId);
        CompanyApplyType companyApplyType = CompanyApplyType.valueOf(applyorderContract.getFlow_type());
        if (companyApplyType == CompanyApplyType.Unknown) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
        applyorderContract.setFlow_type(companyApplyType.getValue());
        //整理数据
        clearApplyData(applyContract);
        //获取联系人数据
        List<UserContactContract> guestList = applyContract.getGuest_list();
        if (!CollectionUtils.isEmpty(guestList)) {
            if (StringUtils.isNotBlank(guestList.get(0).getName())) {
                for (UserContactContract guest : guestList) {
                    guest.setDesc(JSONObject.toJSONString(guest));
                }
            } else {
                List<String> guestIds = new ArrayList<>();
                for (UserContactContract contact : guestList) {
                    guestIds.add(contact.getId());
                }
                List<UserContactContract> contactInfos = userService.getContacts(token, guestIds);
                logger.info("create.contactInfos={}", JsonUtils.toJson(contactInfos));
                if (contactInfos == null) {
                    throw new SaasException(GlobalResponseCode.ApplyContactNotFound);
                }
                for (UserContactContract guest : guestList) {
                    UserContactContract guestInfo = null;
                    for (UserContactContract tmpInfo : contactInfos) {
                        if (tmpInfo.getId().equals(guest.getId())) {
                            guestInfo = tmpInfo;
                            break;
                        }
                    }
                    if (guestInfo == null) {
                        throw new SaasException(GlobalResponseCode.ApplyContactNotFound);
                    }
                    //用desc字段存储序列化后的联系人值
                    guest.setDesc(JSONObject.toJSONString(guestInfo));
                }
            }
        }

        Date now = new Date();
        List<ApplyTripInfoContract> tripList = applyContract.getTrip_list();
        ApplyOrder apply = applyorderContract.ToModel();
        apply.setUpdateTime(now);
        apply.setCreateTime(now);
        apply.setApplyOrderType(SaasFlowConstant.SETTING_TYPE_CENTER);
        //创建申请单
        apply.setId(applyId);
        apply.setState(ApplyStatus.Approved.getValue());
        apply.setFlowCcType(CcNoticeType.Flow.getValue());
        EmployeeContract employee = iBaseOrganizationService.getEmployee(userId, companyId);
        logger.info("create.employee={}", JsonUtils.toJson(employee));
        if (employee != null) {
            apply.setApplicantName(employee.getName());
        }
        //费用归属
        if (applyorderContract.getCost_attribution_category() != null && applyorderContract.getCost_attribution_category() == SettingType.Project.getValue()) {
            if (StringUtils.isBlank(applyorderContract.getCost_attribution_name()) || StringUtils.isBlank(applyorderContract.getCost_attribution_id())) {
                throw new SaasException(GlobalResponseCode.CostAttributionIdIsNull);
            }
            apply.setCostAttributionId(applyorderContract.getCost_attribution_id());
            apply.setCostAttributionName(applyorderContract.getCost_attribution_name());
            apply.setCostAttributionCategory(applyorderContract.getCost_attribution_category());
        } else {
            apply.setCostAttributionCategory(SettingType.Department.getValue());
        }
        //自定义字段
        List<LinkedHashMap> customFields = applyContract.getCustom_fields();
        if (ObjUtils.isNotEmpty(customFields)) {
            apply.setCustomFields(JSONObject.toJSONString(customFields));
        }
        apply.setBillNo(applyV2Service.getBillNo(applyorderContract.getType().intValue(),companyId));
        logger.info("create.apply={}", JsonUtils.toJson(apply));
        applyMapper.insertSelective(apply);
        logger.info("create.tripList={}", JsonUtils.toJson(tripList));
        //保存行程信息
        insertTripContractList(tripList, applyId, now);
        logger.info("create.guestList={}", JsonUtils.toJson(guestList));
        //保存同行人信息
        insertGuestContractList(guestList, applyId, now);
        //保存审批规则数据
        Integer approveType = queryCompanyApplyType(companyId);
        logger.info("create.approveType={}", JsonUtils.toJson(approveType));
        //2:审批单中带有规则信息
        if (approveType == 2) {
            insertApplyRuleContractList(applyId, applyContract);
        }
        //保存审批单中订单的费用归属信息
        sloveApplyCostAttribution(applyorderContract.getCost_attribution_list(), companyId, applyId, applyorderContract.getBring_in());
        //发送消息
        sendKafkaMsg(apply,ApplyStatus.Approved);
        return GlobalResponseCode.Success;
    }

    /**
     * 第三方审批单排重
     * @param companyId
     * @param thirdId
     * @param applyOrderType
     * @param type
     * @return
     */
    private GlobalResponseCode duplicateThirdApply(String companyId, String thirdId, Integer applyOrderType, Integer type) {
        ApplyOrderExample applyOrderExample = new ApplyOrderExample();
        applyOrderExample.createCriteria().andApplyOrderTypeEqualTo(applyOrderType).andTypeEqualTo(type)
                .andCompanyIdEqualTo(companyId)
                .andThirdIdEqualTo(thirdId)
                .andDeleteStatusEqualTo(0)
                .andStateEqualTo(ApplyStatus.Approved.getValue());
        List<ApplyOrder> applyOrderList = applyMapper.selectByExample(applyOrderExample);
        if (ObjUtils.isNotEmpty(applyOrderList)) {
            return GlobalResponseCode.ThirdOrderIdExists;
        }
        return GlobalResponseCode.Success;
    }

    /**
     * 创建申请用车申请单
     *
     * @param applyContract
     * @return
     */
    @Override
    public GlobalResponseCode applyTaxiCreate(String token, ApplyThirdContract applyContract, String userId, String companyId, String ip,String applyId) throws SaasException {
        //检查数据信息
        GlobalResponseCode code = checkApplyData(applyContract, userId, companyId);
        if (code != GlobalResponseCode.Success) {
            return code;
        }
        ApplyOrderThirdContract applyorderContract = applyContract.getApply();
        List<ApplyThirdContract.KeyValueItem> applyTaxiInfoList=Lists.newArrayList();
        //加上排重逻辑
        GlobalResponseCode duplicateCode = duplicateThirdApply(companyId, applyorderContract.getThird_id(), SaasFlowConstant.SETTING_TYPE_CENTER, applyorderContract.getType());
        if (duplicateCode != GlobalResponseCode.Success) {
            return duplicateCode;
        }
        Map<String,Object> ruleMap=Maps.newHashMap();
        Integer sceneType = 2;
        if(ObjectUtil.isNotNull(applyContract.getApply()) && ObjectUtil.isNotNull(applyContract.getApply().getScene_type())){
            sceneType = applyContract.getApply().getScene_type();
        }
        String sceneDesc = querySceneDesc(applyContract.getApply().getCompany_id(),
                applyContract.getApply().getScene_type());
        //传递了规则id
        if (ObjUtils.isNotEmpty(applyorderContract.getRule_id())){
            TaxiApproveRule rule= taxiApproveRuleMapper.selectByPrimaryKey(ObjUtils.toInteger(applyorderContract.getRule_id()));
            logger.info("获取用车规则，规则id={}, 用车规则={}", applyorderContract.getRule_id(), JsonUtils.toJson(rule));
            if (ObjUtils.isEmpty(rule)){
                return GlobalResponseCode.RuleIdNotExistInCompany;
            }
            ruleMap = getRuleMapFromRule(applyContract,rule,applyTaxiInfoList,sceneDesc);

        }else {
            TaxiRuleForApproveContract taxiRuleInfo= iTaxiApproveRuleService.queryTaxiApproveRuleDetail(userId,
                    companyId, sceneType);
            logger.info("获取用车规则，用户id={}, 公司id={} 用车规则={}", userId, companyId, JsonUtils.toJson(taxiRuleInfo));
            if (ObjUtils.isNotEmpty(taxiRuleInfo)){
                ruleMap = getRuleMapFromTaxiRuleInfo(applyContract,taxiRuleInfo,applyTaxiInfoList,sceneDesc);

                applyContract.setApply_taxi_rule_info(applyTaxiInfoList);
            }else {
                //没有传递规则信息
                logger.info("没有传递规则信息");
                if (ObjUtils.isEmpty(applyContract.getApply_taxi_rule_info())){
                    applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("allowed_taxi_type",""));
                    applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("allow_same_city",false));
                    applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem(SaasOrderThirdRuleConstant.TAXI_ALLOW_SAME_CITY_TYPE,0));
                    applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("price_limit_flag",0));
                    applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("price_limit",-1));
                    applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("day_price_limit",-1));
                    applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("taxi_scheduling_fee",20));
                    applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("allow_called_for_other",true));
                    applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("times_limit_flag",0));
                    applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("times_limit",-1));
                    applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("use_personal_budget",1));
                    applyContract.setApply_taxi_rule_info(applyTaxiInfoList);
                }
                ruleMap=applyContract.getApply_taxi_rule_info().stream().collect(Collectors.toMap(ApplyThirdContract.KeyValueItem::getType,ApplyThirdContract.KeyValueItem::getValue));
            }
        }
        GlobalResponseCode responseCode=validateThirdRuleInfo(ruleMap);
        if (!responseCode.equals(GlobalResponseCode.Success)){
            return responseCode;
        }
        List<ApplyTripInfoContract> tripList = applyContract.getTrip_list();
        ruleMap.put("start_time", tripList.get(0).getStart_time());
        ruleMap.put("end_time", tripList.get(0).getEnd_time());
        ruleMap.put("start_city_ids", Lists.newArrayList(tripList.get(0).getStart_city_id().split(",")));

        List<ApplyThirdContract.KeyValueItem> ruleList=ruleMap.entrySet().stream().map(m->new ApplyThirdContract.KeyValueItem(m.getKey(),m.getValue())).collect(Collectors.toList());
        applyContract.setApply_taxi_rule_info(ruleList);
        //创建申请单
        applyorderContract.setId(applyId);
        applyorderContract.setEmployee_id(userId);
        applyorderContract.setCompany_id(companyId);
        CompanyApplyType companyApplyType = CompanyApplyType.valueOf(applyorderContract.getFlow_type());
        if (companyApplyType == CompanyApplyType.Unknown) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
        applyorderContract.setFlow_type(companyApplyType.getValue());
        //整理数据
        clearApplyData(applyContract);
        //获取联系人数据
        List<UserContactContract> guestList = applyContract.getGuest_list();
        if (!CollectionUtils.isEmpty(guestList)) {
            if (StringUtils.isNotBlank(guestList.get(0).getName())) {
                for (UserContactContract guest : guestList) {
                    guest.setDesc(JSONObject.toJSONString(guest));
                }
            } else {
                List<String> guestIds = new ArrayList<>();
                for (UserContactContract contact : guestList) {
                    guestIds.add(contact.getId());
                }
                List<UserContactContract> contactInfos = userService.getContacts(token, guestIds);
                if (contactInfos == null) {
                    throw new SaasException(GlobalResponseCode.ApplyContactNotFound);
                }
                for (UserContactContract guest : guestList) {
                    UserContactContract guestInfo = null;
                    for (UserContactContract tmpInfo : contactInfos) {
                        if (tmpInfo.getId().equals(guest.getId())) {
                            guestInfo = tmpInfo;
                            break;
                        }
                    }
                    if (guestInfo == null) {
                        throw new SaasException(GlobalResponseCode.ApplyContactNotFound);
                    }
                    //用desc字段存储序列化后的联系人值
                    guest.setDesc(JSONObject.toJSONString(guestInfo));
                }
            }
        }

        Date now = new Date();
        Map map= applyContract.getApply_taxi_rule_info().stream().collect(Collectors.toMap(ApplyThirdContract.KeyValueItem::getType,ApplyThirdContract.KeyValueItem::getValue));
        map.put("person_count", map.get("times_limit"));
        tripList.stream().forEach(t->{t.setTrip_content(JsonUtils.toJson(map));
            t.setPerson_count(ObjUtils.toString(map.get("times_limit"),"-1"));
            t.setContent(ObjUtils.toString(map.get("ruleInfos"),null));
            });
        ApplyOrder apply = applyorderContract.ToModel();
        apply.setUpdateTime(now);
        apply.setCreateTime(now);
        apply.setApplyOrderType(SaasFlowConstant.SETTING_TYPE_CENTER);
        //创建申请单
        apply.setId(applyId);
        apply.setMeaningNo(applyId);
        apply.setState(ApplyStatus.Approved.getValue());
        apply.setFlowCcType(CcNoticeType.Flow.getValue());
        EmployeeContract employee = iBaseOrganizationService.getEmployee(userId, companyId);
        if (employee != null) {
            apply.setApplicantName(employee.getName());
        }
        //自定义字段
        List<LinkedHashMap> customFields = applyContract.getCustom_fields();
        if (ObjUtils.isNotEmpty(customFields)) {
            apply.setCustomFields(JSONObject.toJSONString(customFields));
        }
        apply.setBillNo(applyV2Service.getBillNo(apply.getType().intValue(),companyId));
        applyMapper.insertSelective(apply);
        //保存行程信息
        insertTripContractList(tripList, applyId, now);
        //保存同行人信息
        //insertGuestContractList(guestList, applyId, now);
        //2:审批单中带有规则信息
        insertApplyRuleContractList(applyId, applyContract);
        //保存审批单中订单的费用归属信息
        solveApplyCostAttribution(applyorderContract, companyId, applyId, applyorderContract.getBring_in());
        //发送消息
        sendKafkaMsg(apply,ApplyStatus.Approved);
        return GlobalResponseCode.Success;
    }

    @Override
    public GlobalResponseCode applyTaxiCreateV2(String token, ApplyThirdContract applyContract, String userId, String companyId, String ip,String applyId) throws SaasException{
        //检查数据信息
        if(null== applyContract || null==applyContract.getApply()){
            return GlobalResponseCode.ApplyIsNull;
        }
        logger.info("三方创建用车申请单入参：applyContract={}", JsonUtils.toJson(applyContract));
        ApplyOrderThirdContract applyorderContract = applyContract.getApply();
        //校验员工用车场景权限
        Integer sceneType = 2;
        if(ObjectUtil.isNotNull(applyorderContract) && ObjectUtil.isNotNull(applyorderContract.getScene_type())){
            sceneType = applyorderContract.getScene_type();
        }
//        checkPolicy(companyId, userId,
//                sceneType);
        //传递了规则id
        if (ObjUtils.isNotEmpty(applyorderContract.getRule_id())){
            TaxiApproveRule rule= taxiApproveRuleMapper.selectByPrimaryKey(ObjUtils.toInteger(applyorderContract.getRule_id()));
            logger.info("获取用车规则，规则id={}, 用车规则={}", applyorderContract.getRule_id(), JsonUtils.toJson(rule));
            if (ObjUtils.isEmpty(rule)){
                return GlobalResponseCode.RuleIdNotExistInCompany;
            }
            return createApplyTaxi(token, applyContract, userId, companyId, ip, applyId, rule, null);
        }else {
            //申请用车默认场景2
            TaxiRuleForApproveContract taxiRuleInfo= iTaxiApproveRuleService.queryTaxiApproveRuleDetail(userId,
                    companyId, sceneType);
            logger.info("获取用车规则，用户id={}, 公司id={} 用车规则={}", userId, companyId, JsonUtils.toJson(taxiRuleInfo));
            if (ObjUtils.isNotEmpty(taxiRuleInfo)){
                return createApplyTaxi(token, applyContract, userId, companyId, ip, applyId, null, taxiRuleInfo);
            }else {
                logger.info("查不到规则走默认限制城市逻辑");
                return applyTaxiCreate(token, applyContract, userId, companyId, ip, applyId);
            }
        }
    }

    private void checkPolicy(String companyId, String employeeId, Integer sceneType){
        if(ObjectUtil.isNull(companyId) || ObjectUtil.isNull(employeeId)){
            return;
        }
        // 1. 获取用户具有的权限列表
        // 1.1. 从uc获取用户具有的场景权限列表
        logger.info("从uc获取用户具有的场景权限列表,employeeId:{}, companyId:{}", employeeId, companyId);
        CarPolicyBean carPolicyBean = iBaseEmployeeTaxiRuleExtService.getCarPolicyBean(employeeId, companyId);
        logger.info("从uc获取用户具有的场景权限列表,carPolicyBean:{}", JSONUtil.toJsonStr(carPolicyBean));
        if (Objects.isNull(carPolicyBean)) {
            return;
        }
        // 2. 获取列表并处理
        // 2.1.1 获取用户具有权限的场景列表，并转成map
        List<Integer> ruleList = Lists.newArrayList();
        if(ObjUtils.isNotEmpty(carPolicyBean.getRule_infos())){
            // 2.1.2 将uc结果扁平处理，权限type放入集合中
            for (RuleIdDto rule : carPolicyBean.getRule_infos()) {
                ruleList.add(rule.getType().getKey());
            }
            logger.info("从uc中获取用户具有的场景权限，ruleList:{}", JSONUtil.toJsonStr(ruleList));
        }
        if(CollectionUtil.isEmpty(ruleList) || !ruleList.contains(sceneType)){
            throw new SaasException(-2000, "此用车场景不在员工可用范围内。");
        }
    }

    private String querySceneDesc(String companyId, Integer sceneType){
        if(ObjectUtil.isNull(sceneType)){
            return "申请用车";
        }
        // 分贝通用车
        TaxiManageSettingExample taxiManageSettingExample = new TaxiManageSettingExample();
        taxiManageSettingExample.createCriteria()
                .andStateEqualTo(1)
                .andTypeEqualTo(sceneType)
                .andCompanyIdEqualTo(companyId);
        taxiManageSettingExample.setOrderByClause("type asc");
        List<TaxiManageSetting> sceneList = taxiManageSettingMapper.selectByExample(taxiManageSettingExample);
        logger.info("querySceneDesc：{}", JsonUtils.toJson(sceneList));
        if(CollectionUtils.isNotEmpty(sceneList)){
            String result = sceneList.get(0).getViewName();
            if(ObjectUtil.isNotNull(result)){
                return result;
            }else {
                return "申请用车";
            }
        }else {
            throw new SaasException(-1000, "未查询到场景");
        }
    }

    private GlobalResponseCode createApplyTaxi(String token, ApplyThirdContract applyContract, String userId,
                                               String companyId, String ip,String applyId, TaxiApproveRule rule, TaxiRuleForApproveContract taxiRuleInfo){
        if(null==rule && null==taxiRuleInfo){
            return GlobalResponseCode.ApplyIsNull;
        }
        //检查数据信息
        GlobalResponseCode code = checkApplyData(applyContract, userId, companyId);
        logger.info("检查数据信息结果：{}", JsonUtils.toJson(code));
        if (code != GlobalResponseCode.Success) {
            return code;
        }
        ApplyOrderThirdContract applyorderContract = applyContract.getApply();
        List<ApplyThirdContract.KeyValueItem> applyTaxiInfoList=Lists.newArrayList();
        //加上排重逻辑
        GlobalResponseCode duplicateCode = duplicateThirdApply(companyId, applyorderContract.getThird_id(), SaasFlowConstant.SETTING_TYPE_CENTER, applyorderContract.getType());
        if (duplicateCode != GlobalResponseCode.Success) {
            return duplicateCode;
        }
        logger.info("排重逻辑结果：{}", JsonUtils.toJson(code));
        Integer cityLimitFlag = null;
        Map<String,Object> ruleMap = Maps.newHashMap();
        String sceneDesc = querySceneDesc(applyContract.getApply().getCompany_id(),
                applyContract.getApply().getScene_type());
        //传递了规则id
        if (null != rule){
            cityLimitFlag = rule.getCityLimitFlag();

            ruleMap = getRuleMapFromRule(applyContract,rule,applyTaxiInfoList,sceneDesc);

        } else {
            cityLimitFlag = taxiRuleInfo.getCity_limit_flag();

            ruleMap = getRuleMapFromTaxiRuleInfo(applyContract,taxiRuleInfo,applyTaxiInfoList,sceneDesc);

            applyContract.setApply_taxi_rule_info(applyTaxiInfoList);
        }
        logger.info("applyTaxiInfoList：{}", JsonUtils.toJson(applyTaxiInfoList));
        // 如果入参包含不限制，则设置为不限制
        if(ObjUtils.isNotEmpty(ruleMap.get("city_limit")) && Objects.equals(ruleMap.get("city_limit"), 0)){
            cityLimitFlag = 0;
        }
        if(TaxiApproveCityLimitType.NO_LIMIT.getType().equals(cityLimitFlag)){
            // 设置城市不限制
            ruleMap.put("city_limit", 0);
        }
        logger.info("规则检查结果：ruleMap={}", JsonUtils.toJson(code));
        GlobalResponseCode responseCode=validateThirdRuleInfo(ruleMap);
        if (!responseCode.equals(GlobalResponseCode.Success)){
            return responseCode;
        }
        List<ApplyTripInfoContract> tripList = applyContract.getTrip_list();
        if (CollectionUtils.isNotEmpty(tripList)) {
            ruleMap.put("start_time", tripList.get(0).getStart_time());
            ruleMap.put("end_time", tripList.get(0).getEnd_time());
            ruleMap.put("start_city_ids", Lists.newArrayList(tripList.get(0).getStart_city_id().split(",")));
        }
        for(ApplyTripInfoContract applyTripInfoContract : tripList){
            applyTripInfoContract.setSceneType(applyContract.getApply().getScene_type());
        }
        logger.info("处理后行程数据：tripList={}", JsonUtils.toJson(tripList));
        applyContract.setTrip_list(tripList);
        List<ApplyThirdContract.KeyValueItem> ruleList=ruleMap.entrySet().stream().map(m->new ApplyThirdContract.KeyValueItem(m.getKey(),m.getValue())).collect(Collectors.toList());
        applyContract.setApply_taxi_rule_info(ruleList);
        //创建申请单
        applyorderContract.setId(applyId);
        applyorderContract.setEmployee_id(userId);
        applyorderContract.setCompany_id(companyId);
        CompanyApplyType companyApplyType = CompanyApplyType.valueOf(applyorderContract.getFlow_type());
        if (companyApplyType == CompanyApplyType.Unknown) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
        applyorderContract.setFlow_type(companyApplyType.getValue());
        //整理数据
        clearApplyData(applyContract);
        //获取联系人数据
        List<UserContactContract> guestList = applyContract.getGuest_list();
        if (!CollectionUtils.isEmpty(guestList)) {
            if (StringUtils.isNotBlank(guestList.get(0).getName())) {
                for (UserContactContract guest : guestList) {
                    guest.setDesc(JSONObject.toJSONString(guest));
                }
            } else {
                List<String> guestIds = new ArrayList<>();
                for (UserContactContract contact : guestList) {
                    guestIds.add(contact.getId());
                }
                List<UserContactContract> contactInfos = userService.getContacts(token, guestIds);
                if (contactInfos == null) {
                    throw new SaasException(GlobalResponseCode.ApplyContactNotFound);
                }
                for (UserContactContract guest : guestList) {
                    UserContactContract guestInfo = null;
                    for (UserContactContract tmpInfo : contactInfos) {
                        if (tmpInfo.getId().equals(guest.getId())) {
                            guestInfo = tmpInfo;
                            break;
                        }
                    }
                    if (guestInfo == null) {
                        throw new SaasException(GlobalResponseCode.ApplyContactNotFound);
                    }
                    //用desc字段存储序列化后的联系人值
                    guest.setDesc(JSONObject.toJSONString(guestInfo));
                }
            }
        }

        Date now = new Date();
        Map map= applyContract.getApply_taxi_rule_info().stream().collect(Collectors.toMap(ApplyThirdContract.KeyValueItem::getType,ApplyThirdContract.KeyValueItem::getValue));
        map.put("person_count", map.get("times_limit"));
        tripList.stream().forEach(t->{t.setTrip_content(JsonUtils.toJson(map));
            t.setPerson_count(ObjUtils.toString(map.get("times_limit"),"-1"));
            t.setContent(ObjUtils.toString(map.get("ruleInfos"),null));
        });
        ApplyOrder apply = applyorderContract.ToModel();
        apply.setUpdateTime(now);
        apply.setCreateTime(now);
        apply.setApplyOrderType(SaasFlowConstant.SETTING_TYPE_CENTER);
        //创建申请单
        apply.setId(applyId);
        apply.setMeaningNo(applyId);
        apply.setState(ApplyStatus.Approved.getValue());
        apply.setFlowCcType(CcNoticeType.Flow.getValue());
        EmployeeContract employee = iBaseOrganizationService.getEmployee(userId, companyId);
        if (employee != null) {
            apply.setApplicantName(employee.getName());
        }
        //自定义字段
        List<LinkedHashMap> customFields = applyContract.getCustom_fields();
        if (ObjUtils.isNotEmpty(customFields)) {
            apply.setCustomFields(JSONObject.toJSONString(customFields));
        }
        apply.setBillNo(applyV2Service.getBillNo(apply.getType().intValue(),companyId));
        applyMapper.insertSelective(apply);
        //保存行程信息
        insertTripContractList(tripList, applyId, now);
        //保存同行人信息
        //insertGuestContractList(guestList, applyId, now);
        //2:审批单中带有规则信息
        insertApplyRuleContractList(applyId, applyContract);
        //保存审批单中订单的费用归属信息
        solveApplyCostAttribution(applyorderContract, companyId, applyId, applyorderContract.getBring_in());
        //发送消息
        sendKafkaMsg(apply,ApplyStatus.Approved);
        return GlobalResponseCode.Success;
    }

    private Map<String, Object> getRuleMapFromRule(ApplyThirdContract applyContract, TaxiApproveRule rule, List<ApplyThirdContract.KeyValueItem> applyTaxiInfoList,String sceneDesc) {
        Map<String, Object> ruleMap;
        TaxiApproveRuleContract taxiApproveRuleContract = iTaxiApproveRuleService.queryRuleById(ObjUtils.toInteger(applyContract.getApply().getRule_id(), -1), applyContract.getApply().getCompany_id());
        applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("priceAcrossCityFlag", taxiApproveRuleContract.getPriceAcrossCityFlag()));
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(taxiApproveRuleContract.getRuleGroupContractList())) {
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("ruleGroupContractList", taxiApproveRuleContract.getRuleGroupContractList()));
        }

        applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("allowed_taxi_type", ObjUtils.toString(rule.getAllowedTaxiType(), "")));
        applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("allow_same_city", rule.getAllowSameCity()));
        applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("day_price_limit_type", rule.getDayPriceLimitType()));
        applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("use_rule_day_price_limit", rule.getUseRuleDayPriceLimit()));

        applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem(SaasOrderThirdRuleConstant.TAXI_ALLOW_SAME_CITY_TYPE, rule.getAllowSameCityType()));
        if(taxiApproveRuleContract.getTaxiApproveRuleFlag()!=null && taxiApproveRuleContract.getTaxiApproveRuleFlag()==1){//代表是新版规则
            logger.info("使用新版规则。");
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("perDayLimitPrice", taxiApproveRuleContract.getPerDayLimitPrice()));
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("cityLimitType", taxiApproveRuleContract.getCityLimitType()));
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("employeeLimitType", taxiApproveRuleContract.getEmployeeLimitType()));
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("taxiApproveRuleFlag", taxiApproveRuleContract.getTaxiApproveRuleFlag()));
            if(ObjectUtil.isNotNull(taxiApproveRuleContract.getSceneType())){
                applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("sceneType", taxiApproveRuleContract.getSceneType()));
            }else {
                applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("sceneType", 2));
            }
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("extraPerCountLimitPrice", Optional.ofNullable(taxiApproveRuleContract.getExtraPerCountLimitPrice()).orElse(new BigDecimal("-1"))));
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("extraPerDayLimitPrice", Optional.ofNullable(taxiApproveRuleContract.getExtraPerDayLimitPrice()).orElse(new BigDecimal("-1"))));
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("limitPath", rule.getLimitPath()));
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("limitTime", rule.getLimitTime()));

            if(rule.getLimitPath()!=null && rule.getLimitPath() && CollectionUtils.isNotEmpty(taxiApproveRuleContract.getPathLocationInfos())) {
                applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("pathLocationInfos", taxiApproveRuleContract.getPathLocationInfos()));
            }
            if(rule.getLimitTime()!=null && rule.getLimitTime() && CollectionUtils.isNotEmpty(taxiApproveRuleContract.getTimeRangeList())){
                applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("timeRangeList", taxiApproveRuleContract.getTimeRangeList()));
            }

            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("price_limit", Optional.ofNullable(rule.getPriceLimit()).orElse(new BigDecimal("-1"))));
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("day_price_limit", Optional.ofNullable(rule.getDayPriceLimit()).orElse(new BigDecimal("-1"))));

            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("cityRestrictionType", Optional.ofNullable(taxiApproveRuleContract.getCityRestrictionType()).orElse(0)));
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("residentCitySwitch", Optional.ofNullable(taxiApproveRuleContract.getResidentCitySwitch()).orElse(0)));
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("specifyCityType", Optional.ofNullable(taxiApproveRuleContract.getSpecifyCityType()).orElse(0)));

            if(CollectionUtils.isNotEmpty(taxiApproveRuleContract.getSpecifyCityList())) {
                applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("specifyCityList", taxiApproveRuleContract.getSpecifyCityList()));
            }
            if(CollectionUtils.isNotEmpty(taxiApproveRuleContract.getSpecifyCityGroupList())) {
                applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("specifyCityGroupList", taxiApproveRuleContract.getSpecifyCityGroupList()));
            }
        }else {
            if(ObjectUtil.isNotNull(rule.getPriceLimitFlag())){
                applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("price_limit_flag", rule.getPriceLimitFlag()));
            }
            if (rule.getPriceLimitFlag()!=null && rule.getPriceLimitFlag() != 2) {
                applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("price_limit", rule.getPriceLimit()));
                applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("day_price_limit", rule.getDayPriceLimit()));
            }
        }
        applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("taxi_scheduling_fee", rule.getTaxiSchedulingFee()));
        applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("allow_called_for_other", rule.getAllowCalledForother()));
        applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("times_limit_flag", rule.getTimesLimitFlag()));
        applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("times_limit_type", Optional.ofNullable(rule.getTimesLimitType()).orElse(TimesLimitTypeEnum.total_count_limit.getCode())));
        applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("use_personal_budget", rule.getUsePersonalBudget()));
        if (rule.getTimesLimitFlag() != 2) {
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("times_limit", rule.getTimesLimit()));
        }
        if(ObjectUtil.isNotNull(taxiApproveRuleContract.getSceneType())){
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("scene_type", taxiApproveRuleContract.getSceneType()));
        }else {
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("scene_type", 2));
        }
        applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("scene_desc", sceneDesc));
        applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("id", taxiApproveRuleContract.getId()));
        applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("name", taxiApproveRuleContract.getName()));
        // 用车个人付超规控制
        TaxiExceedConfigQueryRes taxiExceedConfigQueryRes = iMessageSetupRpcService.queryTaxiExceedConfigMessage(applyContract.getApply().getCompany_id());
        if (Objects.nonNull(taxiExceedConfigQueryRes) && Objects.equals(true, taxiExceedConfigQueryRes.getIsOpenExceedConfig())) {
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("isOpenExceedConfig", taxiExceedConfigQueryRes.getIsOpenExceedConfig()));
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("exceedConfigInfo", taxiApproveRuleContract.getExceedConfigInfo()));
        } else {
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("isOpenExceedConfig", Boolean.FALSE));
        }
        String ruleInfos = getRuleInfos(rule);
        applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("ruleInfos", ruleInfos));
        logger.info("getRuleMapFromRule applyTaxiInfoList|{}", JsonUtils.toJson(applyTaxiInfoList));

        Map<String, Object> map = new HashMap<>();
        for(ApplyThirdContract.KeyValueItem item: applyTaxiInfoList){
            if(item.getType() !=null && item.getValue()!=null){
                map.put(item.getType(),item.getValue());
            }else{
                logger.warn("getRuleMapFromRule value is null,key={}",item.getType());
            }
        }

        if (ObjUtils.isNotEmpty(applyContract.getApply_taxi_rule_info())) {
            logger.info("getRuleMapFromRule getApply_taxi_rule_info|{}", JsonUtils.toJson(applyContract.getApply_taxi_rule_info()));
            Map<String, Object> reqMap = applyContract.getApply_taxi_rule_info().stream().collect(Collectors.toMap(ApplyThirdContract.KeyValueItem::getType, ApplyThirdContract.KeyValueItem::getValue));
            if (Objects.equals(ObjUtils.toInteger(map.get("taxiApproveRuleFlag"), 0), 1)) {
                List<ApplyThirdContract.KeyValueItem> userRuleConfigList = applyContract.getApply_taxi_rule_info();
                List<ApplyThirdContract.KeyValueItem> totalPriceVal = userRuleConfigList.stream().filter(s -> Objects.equals(s.getType(), "total_price")).collect(Collectors.toList());
                List<ApplyThirdContract.KeyValueItem> priceLimitFlagVal = userRuleConfigList.stream().filter(s -> Objects.equals(s.getType(), "price_limit_flag")).collect(Collectors.toList());
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(priceLimitFlagVal)) {
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(totalPriceVal)) {
                        reqMap.put("employeeLimitType", 1);
                    } else {
                        reqMap.put("employeeLimitType", 0);
                    }
                }
            }
            reqMap.putAll(map);
            ruleMap = reqMap;
        } else {
            ruleMap = map;
        }
        return ruleMap;
    }

    private Map<String, Object> getRuleMapFromTaxiRuleInfo(ApplyThirdContract createContract, TaxiRuleForApproveContract taxiRuleInfo, List<ApplyThirdContract.KeyValueItem> applyTaxiInfoList,String sceneDesc) {
        Map<String, Object> ruleMap;
        applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("priceAcrossCityFlag", taxiRuleInfo.getPriceAcrossCityFlag()));
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(taxiRuleInfo.getRuleGroupContractList())) {
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("ruleGroupContractList", taxiRuleInfo.getRuleGroupContractList()));
        }

        applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("allowed_taxi_type", ObjUtils.toString(taxiRuleInfo.getAllowed_taxi_type(), "")));
        applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem(SaasOrderThirdRuleConstant.TAXI_ALLOW_SAME_CITY_TYPE, taxiRuleInfo.getAllowSameCityType()));
        applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("day_price_limit_type", taxiRuleInfo.getDayPriceLimitType()));
        applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("use_rule_day_price_limit", taxiRuleInfo.getUse_rule_day_price_limit()));

        if(taxiRuleInfo.getTaxiApproveRuleFlag()!=null && taxiRuleInfo.getTaxiApproveRuleFlag()==1){//代表是新版规则
            logger.info("使用新版规则。");
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("perDayLimitPrice", taxiRuleInfo.getPerDayLimitPrice()));
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("cityLimitType", taxiRuleInfo.getCityLimitType()));
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("employeeLimitType", taxiRuleInfo.getEmployeeLimitType()));
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("taxiApproveRuleFlag", taxiRuleInfo.getTaxiApproveRuleFlag()));
            if(ObjectUtil.isNotNull(taxiRuleInfo.getSceneType())){
                applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("sceneType", taxiRuleInfo.getSceneType()));
            }else {
                applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("sceneType", 2));
            }
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("extraPerCountLimitPrice", Optional.ofNullable(taxiRuleInfo.getExtraPerCountLimitPrice()).orElse(new BigDecimal("-1"))));
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("extraPerDayLimitPrice", Optional.ofNullable(taxiRuleInfo.getExtraPerDayLimitPrice()).orElse(new BigDecimal("-1"))));

            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("limitPath", taxiRuleInfo.getLimitPath()));
            if(taxiRuleInfo.getLimitPath()!=null && taxiRuleInfo.getLimitPath() && CollectionUtils.isNotEmpty(taxiRuleInfo.getPathLocationInfos())) {
                applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("pathLocationInfos", taxiRuleInfo.getPathLocationInfos()));
            }
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("limitTime", taxiRuleInfo.getLimitTime()));
            if(taxiRuleInfo.getLimitTime()!=null && taxiRuleInfo.getLimitTime() && CollectionUtils.isNotEmpty(taxiRuleInfo.getTimeRangeList())){
                applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("timeRangeList", taxiRuleInfo.getTimeRangeList()));
            }
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("price_limit", Optional.ofNullable(taxiRuleInfo.getPrice_limit()).orElse(new BigDecimal("-1"))));
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("day_price_limit", Optional.ofNullable(taxiRuleInfo.getDay_price_limit()).orElse(new BigDecimal("-1"))));

            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("cityRestrictionType", Optional.ofNullable(taxiRuleInfo.getCityRestrictionType()).orElse(0)));
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("residentCitySwitch", Optional.ofNullable(taxiRuleInfo.getResidentCitySwitch()).orElse(0)));
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("specifyCityType", Optional.ofNullable(taxiRuleInfo.getSpecifyCityType()).orElse(0)));
            if(CollectionUtils.isNotEmpty(taxiRuleInfo.getSpecifyCityList())) {
                applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("specifyCityList", taxiRuleInfo.getSpecifyCityList()));
            }
            if(CollectionUtils.isNotEmpty(taxiRuleInfo.getSpecifyCityGroupList())) {
                applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("specifyCityGroupList", taxiRuleInfo.getSpecifyCityGroupList()));
            }
        }else {
            if(ObjectUtil.isNotNull(taxiRuleInfo.getPrice_limit_flag())){
                applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("price_limit_flag", taxiRuleInfo.getPrice_limit_flag()));
            }
            if (taxiRuleInfo.getPrice_limit_flag()!=null && taxiRuleInfo.getPrice_limit_flag() != 2) {
                applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("price_limit", taxiRuleInfo.getPrice_limit()));
                applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("day_price_limit", taxiRuleInfo.getDay_price_limit()));
            }
        }

        applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("taxi_scheduling_fee", taxiRuleInfo.getTaxi_scheduling_fee()));
        applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("allow_called_for_other", taxiRuleInfo.getAllow_called_for_other()));
        applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("times_limit_flag", taxiRuleInfo.getTimes_limit_flag()));
        applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("times_limit_type", Optional.ofNullable(taxiRuleInfo.getTimes_limit_type()).orElse(TimesLimitTypeEnum.total_count_limit.getCode())));
        applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("use_personal_budget", taxiRuleInfo.getUse_personal_budget()));
        if (taxiRuleInfo.getTimes_limit_flag() != 2) {
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("times_limit", taxiRuleInfo.getTimes_limit()));
        }
        if(ObjectUtil.isNull(taxiRuleInfo.getSceneType())){
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("scene_type",taxiRuleInfo.getSceneType()));
        }else {
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("scene_type", 2));
        }
        applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("scene_desc", sceneDesc));
        applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("id", taxiRuleInfo.getId()));
        applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("name", taxiRuleInfo.getName()));
        // 用车个人付超规控制
        TaxiExceedConfigQueryRes taxiExceedConfigQueryRes = iMessageSetupRpcService.queryTaxiExceedConfigMessage(taxiRuleInfo.getCompanyId());
        if (Objects.nonNull(taxiExceedConfigQueryRes) && Objects.equals(true, taxiExceedConfigQueryRes.getIsOpenExceedConfig())) {
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("isOpenExceedConfig", taxiExceedConfigQueryRes.getIsOpenExceedConfig()));
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("exceedConfigInfo", taxiRuleInfo.getExceedConfigInfo()));
        } else {
            applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("isOpenExceedConfig", Boolean.FALSE));
        }
        applyTaxiInfoList.add(new ApplyThirdContract.KeyValueItem("ruleInfos", JsonUtils.toJson(taxiRuleInfo.getRuleInfos())));
        logger.info("getRuleMapFromTaxiRuleInfo applyTaxiInfoList|{}", JsonUtils.toJson(applyTaxiInfoList));
        Map<String, Object> map = new HashMap<>();
        for(ApplyThirdContract.KeyValueItem item: applyTaxiInfoList){
            if(item.getType() !=null && item.getValue() !=null){
                map.put(item.getType(),item.getValue());
            }else{
                logger.warn("getRuleMapFromTaxiRuleInfo value is null,key={}",item.getType());
            }
        }
        if (ObjUtils.isNotEmpty(createContract.getApply_taxi_rule_info())) {
            logger.info("getRuleMapFromTaxiRuleInfo getApply_taxi_rule_info|{}", JsonUtils.toJson(createContract.getApply_taxi_rule_info()));
            Map<String, Object> reqMap = createContract.getApply_taxi_rule_info().stream().collect(Collectors.toMap(ApplyThirdContract.KeyValueItem::getType, ApplyThirdContract.KeyValueItem::getValue));
            if (Objects.equals(ObjUtils.toInteger(map.get("taxiApproveRuleFlag"), 0), 1)) {
                List<ApplyThirdContract.KeyValueItem> userRuleConfigList = createContract.getApply_taxi_rule_info();
                List<ApplyThirdContract.KeyValueItem> totalPriceVal = userRuleConfigList.stream().filter(s->Objects.equals(s.getType(), "total_price")).collect(Collectors.toList());
                List<ApplyThirdContract.KeyValueItem> priceLimitFlagVal = userRuleConfigList.stream().filter(s->Objects.equals(s.getType(), "price_limit_flag")).collect(Collectors.toList());
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(priceLimitFlagVal)) {
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(totalPriceVal)) {
                        reqMap.put("employeeLimitType", 1);
                    } else {
                        reqMap.put("employeeLimitType", 0);
                    }
                }
            }
            map.putAll(reqMap);
        }
        ruleMap = map;
        return ruleMap;
    }



    @Transactional(transactionManager = "saas_plus", readOnly = false, rollbackFor = Exception.class)
    protected void solveApplyCostAttribution(ApplyOrderThirdContract applyContract, String companyId, String applyId, Integer bringIn) {
        List<CostAttributionInfo> costAttributionList = applyContract.getCost_attribution_list();
        com.fenbeitong.saas.core.contract.apply.CostInfoContract costInfo = applyContract.getCost_info();
        if (CollectionUtils.isEmpty(costAttributionList) && ObjUtils.isEmpty(costInfo)) {
            return;
        }
        if (bringIn == null) {
            bringIn = 0;
        }
        if (ObjUtils.isNotEmpty(costInfo)) {
            List<com.fenbeitong.saas.core.contract.apply.CostInfoContract.CostAttributionGroup> costAttributionGroupList = costInfo.getCost_attribution_group_list();
            if (CollectionUtils.isEmpty(costAttributionGroupList)) {
                return;
            }
            List<ApplyCostAttribution> applyCostAttributionList = Lists.newArrayList();
            for (com.fenbeitong.saas.core.contract.apply.CostInfoContract.CostAttributionGroup costAttributionGroup : costAttributionGroupList) {
                Integer category = costAttributionGroup.getCategory();
                String recordId = costAttributionGroup.getRecord_id();
                String categoryName = costAttributionGroup.getCategory_name();
                List<CostAttribution> costAttributionListInfos = costAttributionGroup.getCost_attribution_list();
                for (CostAttribution costAttribution : costAttributionListInfos) {
                    ApplyCostAttribution applyCostAttribution = new ApplyCostAttribution();
                    applyCostAttribution.setApplyId(applyId);
                    applyCostAttribution.setCompanyId(companyId);
                    applyCostAttribution.setCostAttributionId(costAttribution.getId());
                    applyCostAttribution.setCostAttributionName(costAttribution.getName());
                    applyCostAttribution.setCostAttributionCategory(category);
                    applyCostAttribution.setBringIn(bringIn);
                    applyCostAttribution.setRecordId(recordId);
                    applyCostAttribution.setCategoryName(categoryName);
                    applyCostAttributionList.add(applyCostAttribution);
                }
            }
            if (CollectionUtils.isNotEmpty(applyCostAttributionList)) {
                applyCostAttributionExtMapper.batchInsertCostAttribution(applyCostAttributionList);
            }
        } else {
            List<ApplyCostAttribution> applyCostAttributionList = Lists.newArrayList();
            for (CostAttributionInfo costAttributionInfo : costAttributionList) {
                ApplyCostAttribution applyCostAttribution = new ApplyCostAttribution();
                applyCostAttribution.setApplyId(applyId);
                applyCostAttribution.setCompanyId(companyId);
                applyCostAttribution.setCostAttributionId(costAttributionInfo.getCost_attribution_id());
                applyCostAttribution.setCostAttributionName(costAttributionInfo.getCost_attribution_name());
                applyCostAttribution.setCostAttributionCategory(costAttributionInfo.getCost_attribution_category());
                applyCostAttribution.setBringIn(bringIn);
                applyCostAttributionList.add(applyCostAttribution);
            }
            if (CollectionUtils.isNotEmpty(applyCostAttributionList)) {
                applyCostAttributionExtMapper.batchInsertCostAttribution(applyCostAttributionList);
            }
        }
    }

    private String getRuleInfos(TaxiApproveRule taxiApproveRule){
        //组装参数
        LinkedList ruleInfos=Lists.newLinkedList();

        if(StringUtils.isNotEmpty(taxiApproveRule.getAllowedTaxiType())){
            StringBuilder str=new StringBuilder();
            String[] allowTaxiTypeList = taxiApproveRule.getAllowedTaxiType().split(",");
            for (String taxiType : allowTaxiTypeList) {
                str.append(TaxiType.getName(ObjUtils.toInteger(taxiType))).append("、") ;
            }
            String taxiLimitType=str.toString();
            ruleInfos.add(new KvStrContract(CoreLanguage.Common_Value_CarTypeLimit.getMessage(),taxiLimitType.substring(0,taxiLimitType.length()-1)));
        }else {
            ruleInfos.add(new KvStrContract(CoreLanguage.Common_Value_CarTypeLimit.getMessage(),CoreLanguage.Rule_Value_NoLimit.getMessage()));
        }

        if(taxiApproveRule.getTaxiSchedulingFee()==-1){
            ruleInfos.add(new KvStrContract(CoreLanguage.Rule_Value_CarDispatchingFee.getMessage(),CoreLanguage.Rule_Value_RejectUsed.getMessage()));
        }else {
            ruleInfos.add(new KvStrContract(CoreLanguage.Rule_Value_CarDispatchingFee.getMessage(),"上限"+taxiApproveRule.getTaxiSchedulingFee()+"元"));
        }
        if (taxiApproveRule.getAllowSameCity()){
            ruleInfos.add(new KvStrContract(CoreLanguage.Rule_Value_CarCityLimit.getMessage(), CoreLanguage.Rule_Value_CarCityStartEnd.getMessage()));
        }else {
            ruleInfos.add(new KvStrContract(CoreLanguage.Rule_Value_CarCityLimit.getMessage(),CoreLanguage.Rule_Value_NoLimit.getMessage()));
        }
        if (taxiApproveRule.getAllowCalledForother()){
            ruleInfos.add(new KvStrContract(CoreLanguage.Rule_Value_CarCallLimit.getMessage(), CoreLanguage.Rule_Value_CarAllowCallOther.getMessage()));
        }else {
            ruleInfos.add(new KvStrContract(CoreLanguage.Rule_Value_CarCallLimit.getMessage(), CoreLanguage.Rule_Value_CarNotAllowCallOther.getMessage()));
        }
        if (taxiApproveRule.getUseRuleDayPriceLimit() == 1){
            ruleInfos.add(new KvStrContract(CoreLanguage.Rule_Value_CarQuotaUnion.getMessage(), CoreLanguage.Rule_Value_CarAllowOtherQuotaUnion.getMessage()));
        }
        return JsonUtils.toJson(ruleInfos);
    }

    private GlobalResponseCode validateThirdRuleInfo(Map<String, Object> map) {
        String taxiTypes = (String) map.get("allowed_taxi_type");
        if (ObjUtils.isNotEmpty(taxiTypes)) {
            String[] ids = taxiTypes.split(",");
            for (String id : ids) {
                if (!TaxiModelsType.containsKey(ObjUtils.toInteger(id))) {
                    return GlobalResponseCode.ApplyTaxiTypeError;
                }
            }
        }
        logger.info("validateThirdRuleInfo入参map={}", JsonUtils.toJson(map));
        // 兼容性处理
        Object allowSameCity = map.get("allow_same_city");
        Object allowSameCityType = map.get(SaasOrderThirdRuleConstant.TAXI_ALLOW_SAME_CITY_TYPE);

        if (ObjUtils.isEmpty(allowSameCityType) && Objects.nonNull(allowSameCity)) {
            logger.info("兼容性处理:allowSameCityType为空，allowSameCity={}", JsonUtils.toJson(allowSameCity));
            allowSameCityType = AllowSameCityLimitType.mapToAllowSameCityLimitTypeBy(ObjUtils.toboolean(allowSameCity));
            map.put(SaasOrderThirdRuleConstant.TAXI_ALLOW_SAME_CITY_TYPE, allowSameCityType);
        }
        if (ObjUtils.isEmpty(map.get("times_limit_flag"))
                || (ObjUtils.isEmpty(map.get(SaasOrderThirdRuleConstant.TAXI_ALLOW_SAME_CITY_TYPE)) && ObjUtils.isEmpty(map.get("allow_same_city")))
                || ObjUtils.isEmpty(map.get("allow_called_for_other")) || ObjUtils.isEmpty(map.get("taxi_scheduling_fee"))) {
            return GlobalResponseCode.ParameterIsNull;
        }
        if (ObjUtils.toInteger(map.get("times_limit_flag"))<0||ObjUtils.toInteger(map.get("times_limit_flag"))>2){
            return GlobalResponseCode.ApplyTaxiTimesLimitTypeError;
        }
        if (ObjUtils.toInteger(map.get("times_limit_flag")) == 2 && (ObjUtils.isEmpty(map.get("times_limit")) || ObjUtils.toInteger(map.get("times_limit")) <= 0)) {
            return GlobalResponseCode.ApplyTaxiTimesLimitIsNull;
        }
        //如果旧版的规则，且配置的是不限制，则设置默认值
        Integer taxiApproveRuleFlag = ObjUtils.toInteger(map.get("taxiApproveRuleFlag"),0);
        if(taxiApproveRuleFlag == 0){
            if (ObjUtils.toInteger(map.get("price_limit_flag"))<0||ObjUtils.toInteger(map.get("price_limit_flag"))>4){
                return GlobalResponseCode.ApplyTaxiPriceLimitTypeError;
            }
            if (ObjUtils.toInteger(map.get("price_limit_flag"))==2&&(ObjUtils.isEmpty(map.get("total_price"))||ObjUtils.toInteger(map.get("total_price"))<=0)){
                return GlobalResponseCode.ApplyTaxiPriceLimitIsNull;
            }
            if (ObjUtils.toInteger(map.get("price_limit_flag"),0) == 0) {
                map.put("price_limit", -1);
                map.put("day_price_limit", -1);
            }
        }else{
            if (1==ObjUtils.toInteger(map.get("employeeLimitType"))&&(ObjUtils.isEmpty(map.get("total_price"))||ObjUtils.toInteger(map.get("total_price"))<=0)){
                return GlobalResponseCode.ApplyTaxiPriceLimitIsNull;
            }
        }
        if (ObjUtils.toInteger(map.get("times_limit_flag"))==0){
            map.put("times_limit", -1);
        }
        if (ObjUtils.isEmpty(map.get("price_limit"))||ObjUtils.toInteger(map.get("price_limit"))<0){
            map.put("price_limit", -1);
        }
        if (ObjUtils.isEmpty(map.get("day_price_limit"))||ObjUtils.toInteger(map.get("day_price_limit"))<0){
            map.put("day_price_limit", -1);
        }
        if (ObjUtils.isEmpty(map.get("city_limit"))){
            //默认限制
            map.put("city_limit", 1);
        }
        return GlobalResponseCode.Success;
    }



    private void insertTripContractList(List<ApplyTripInfoContract> tripList, String applyId, Date now) {
        if (tripList != null) {
            for (ApplyTripInfoContract tripContract : tripList) {
                insertTripContract(tripContract, applyId, now);
            }
        }
    }

    private void insertTripContract(ApplyTripInfoContract tripContract, String applyId, Date now) {
        ApplyTripInfo trip = new ApplyTripInfo();
        String id = IDTool.CreateUniqueID();
        trip.setId(id);
        trip.setApplyOrderId(applyId);
        trip.setCreateTime(now);
        trip.setType(tripContract.getType());
        trip.setArrivalCityId(tripContract.getArrival_city_id());
        trip.setStartCityId(tripContract.getStart_city_id());
        trip.setStartCityName(tripContract.getStart_city_name());
        trip.setArrivalCityName(tripContract.getArrival_city_name());
        trip.setState(ApplyTripStatus.Available.getValue());
        trip.setStartTime(DateTimeTool.fromStringToDate(tripContract.getStart_time()));
        trip.setPersonCount(ObjUtils.toInteger(tripContract.getPerson_count(), -1));
        if(ObjectUtil.isNull(tripContract.getSceneType())){
            trip.setTaxiApplySceneType(2);
        }else {
            trip.setTaxiApplySceneType(tripContract.getSceneType());
        }
        if (tripContract.getEstimated_amount() != null && tripContract.getEstimated_amount().compareTo(BigDecimal.valueOf(0)) == 1) {
            trip.setEstimatedAmount(tripContract.getEstimated_amount().divide(BigDecimal.valueOf(100)));
        }
        if (tripContract.getEnd_time() != null && tripContract.getEnd_time().length() > 0) {
            trip.setEndTime(DateTimeTool.fromStringToDate(tripContract.getEnd_time()));
        }
        trip.setContent(tripContract.getContent());
        if (tripContract.getType().intValue() == BizType.IntlAir.getValue()) {
            trip.setTripType(tripContract.getTrip_type());
            if (tripContract.getTrip_type() == TripType.goAndBack.getValue()){
                trip.setBackStartTime(DateTimeTool.fromStringToDate(tripContract.getBack_start_time()));
                if (ObjUtils.isNotEmpty(tripContract.getBack_end_time())) {
                    trip.setBackEndTime(DateTimeTool.fromStringToDate(tripContract.getBack_end_time()));
                }
            }
        }
        if (tripContract.getType().intValue() == BizType.Meishi.getValue()) {
            if (tripContract.getPerson_count() == null) {
                tripContract.setPerson_count("-1");
            }
            trip.setPersonCount(ObjUtils.toInteger(tripContract.getPerson_count()));
            try {
                trip.setStartTime(DateTimeTool.fromStringToDate(tripContract.getStart_time(), DateTimeFormat.forPattern("yyyy-MM-dd HH:mm")));
                trip.setEndTime(DateTimeTool.fromStringToDate(tripContract.getEnd_time(), DateTimeFormat.forPattern("yyyy-MM-dd HH:mm")));
            } catch (Exception e) {
                e.printStackTrace();
                logger.error("创建用餐审批结束时间格式不正确:{}", tripContract.getEnd_time());
            }
        }
        if (tripContract.getType().intValue() == BizType.Mall.getValue()) {
            trip.setStartTime(now);
            trip.setStartCityId("0");
            trip.setOrderReasonId(tripContract.getOrder_reason_id());
            trip.setOrderReason(tripContract.getOrder_reason());
            trip.setOrderReasonDesc(tripContract.getOrder_reason_desc());
            trip.setPriceStructure(JSON.toJSONString(tripContract.getMall_price_structure()));
            trip.setAddressInfo(JSON.toJSONString(tripContract.getAddress_info()));
            trip.setMallList(JSON.toJSONString(tripContract.getMall_list()));
            if (CollectionUtils.isNotEmpty(tripContract.getCustom_remark())) {
                JSONObject jo = new JSONObject();
                jo.put("custom_remark", tripContract.getCustom_remark());
                trip.setContent(jo.toJSONString());
            }
            if (tripContract.getCost_attribution_name() != null && tripContract.getCost_attribution_name().size() > 0) {
                trip.setCostAttributionName(JSON.toJSONString(tripContract.getCost_attribution_name()));
            }
        }
        if (ObjUtils.isNotEmpty(tripContract.getTrip_content())){
            trip.setTripContent(tripContract.getTrip_content());
        }
        trip.setUpdateTime(now);
        applyTripMapper.insert(trip);
    }

    /**
     * 保存出行人
     *
     * @param guestList
     * @param applyId
     * @param now
     */
    private void insertGuestContractList(List<UserContactContract> guestList, String applyId, Date now) {
        if (guestList != null) {
            for (UserContactContract guestContract : guestList) {
                ApplyTripGuest guest = guestContract.ToModel();
                guest.setId(IDTool.CreateUniqueID());
                guest.setCreateTime(now);
                guest.setContactInfo(guestContract.getDesc());
                guest.setApplyOrderId(applyId);
                applyGuestMapper.insertSelective(guest);
            }
        }
    }

    /**
     * 保存规则信息
     *
     * @param applyId
     * @param applyContract
     */
    private void insertApplyRuleContractList(String applyId, ApplyThirdContract applyContract) {
        List<ApplyThirdContract.KeyValueItem> airRuleInfo = applyContract.getAir_rule_info();
        List<ApplyThirdContract.KeyValueItem> intlAirRuleInfo = applyContract.getIntl_air_rule_info();
        List<ApplyThirdContract.KeyValueItem> hotelRuleInfo = applyContract.getHotel_rule_info();
        List<ApplyThirdContract.KeyValueItem> trainRuleInfo = applyContract.getTrain_rule_info();
        List<ApplyThirdContract.KeyValueItem> applyTaxiRuleInfo = applyContract.getApply_taxi_rule_info();
        ApplyRuleSetting applyRuleSetting = new ApplyRuleSetting();
        applyRuleSetting.setApplyOrderId(applyId);
        if (CollectionUtils.isNotEmpty(airRuleInfo)) {
            applyRuleSetting.setAirInfo(JSONObject.toJSONString(airRuleInfo));
        }
        if (CollectionUtils.isNotEmpty(intlAirRuleInfo)) {
            applyRuleSetting.setIntlAirInfo(JSONObject.toJSONString(intlAirRuleInfo));
        }
        if (CollectionUtils.isNotEmpty(hotelRuleInfo)) {
            applyRuleSetting.setHotelInfo(JSONObject.toJSONString(hotelRuleInfo));
        }
        if (CollectionUtils.isNotEmpty(trainRuleInfo)) {
            applyRuleSetting.setTrainInfo(JSONObject.toJSONString(trainRuleInfo));
        }
        if (CollectionUtils.isNotEmpty(applyTaxiRuleInfo)) {
            applyRuleSetting.setTaxiInfo(JSONObject.toJSONString(applyTaxiRuleInfo));
        }
        applyRuleSettingMapper.insertSelective(applyRuleSetting);
    }

    /**
     * 整理申请单数据
     *
     * @param applyContract
     */
    private void clearApplyData(ApplyThirdContract applyContract) {
        ApplyOrderThirdContract applyorderContract = applyContract.getApply();
        if (applyorderContract.getId() == null || applyorderContract.getId().length() == 0) {
            applyorderContract.setCheck_reason(null);
        }
        if (applyorderContract.getType() == ApplyType.Mall.getValue()) {
            //采购没有行程,也没有同行人
            applyContract.setGuest_list(null);
            applyContract.setTrip_list(null);
        }
        if (applyorderContract.getBudget() == null) {
            applyorderContract.setBudget(0);
        }
        if (applyorderContract.getType() == ApplyType.Taxi.getValue()) {
            //用车没有同行人
            applyContract.setGuest_list(null);
        }
        if (applyContract.getTrip_list() != null && applyContract.getTrip_list().size() == 0) {
            applyContract.setTrip_list(null);
        }
        if (applyContract.getGuest_list() != null && applyContract.getGuest_list().size() == 0) {
            applyContract.setGuest_list(null);
        }
        if (applyorderContract.getType() != ApplyType.ChaiLv.getValue()) {
            applyContract.setGuest_list(null);
        }
        if (applyorderContract.getType() != ApplyType.Mall.getValue()&&applyorderContract.getType() != ApplyType.Meishi.getValue()&&applyorderContract.getType() != ApplyType.TakeAway.getValue()) {
            List<Tuple<Date, String>> cityIds = new ArrayList<>();
            List<Date> timeRange = new ArrayList<>();

            for (ApplyTripInfoContract trip : applyContract.getTrip_list()) {
                logger.info("trip={}", JsonUtils.toJson(trip));
                Date startTime = DateTimeTool.fromStringToDate(trip.getStart_time());
                //这里把时间从String转到Date,再转回String,是需要用Date格式式字段串的显示,如输入为2017-2-27,格式化后变为2017-02-27
                trip.setStart_time(DateTimeTool.fromDateToString(startTime));
                timeRange.add(startTime);
                if (trip.getType() == BizType.Air.getValue() || trip.getType() == BizType.Train.getValue() ||
                        (trip.getType() == BizType.IntlAir.getValue() && trip.getTrip_type() == 1)) {
                    //trip.setEnd_time(null);
                    //trip.setBack_end_time(null);
                    if (trip.getEnd_time() != null) {
                        Date endTime = DateTimeTool.fromStringToDate(trip.getEnd_time());
                        trip.setEnd_time(DateTimeTool.fromDateToString(endTime));
                        if (ObjUtils.isNotBlank(endTime)) {
                            timeRange.add(endTime);
                        }
                    }
                } else {
                    if (trip.getType() == BizType.IntlAir.getValue() && trip.getTrip_type() == 2) {
                        Date backStartTime = DateTimeTool.fromStringToDate(trip.getBack_start_time());
                        trip.setBack_start_time(DateTimeTool.fromDateToString(backStartTime));
                        if (trip.getEnd_time() != null) {
                            Date endTime = DateTimeTool.fromStringToDate(trip.getEnd_time());
                            trip.setEnd_time(DateTimeTool.fromDateToString(endTime));
                            if (ObjUtils.isNotBlank(endTime)) {
                                timeRange.add(endTime);
                            }
                        }
                        timeRange.add(backStartTime);
                        if (trip.getBack_end_time() != null) {
                            Date backEndTime = DateTimeTool.fromStringToDate(trip.getBack_end_time());
                            trip.setBack_end_time(DateTimeTool.fromDateToString(backEndTime));
                            if (ObjUtils.isNotBlank(backEndTime)) {
                                timeRange.add(backEndTime);
                            }
                        }
                    } else {
                        Date endTime = DateTimeTool.fromStringToDate(trip.getEnd_time());
                        trip.setEnd_time(DateTimeTool.fromDateToString(endTime));
                        if (ObjUtils.isNotBlank(endTime)) {
                            timeRange.add(endTime);
                        }
                    }
                }
                if (trip.getType() == BizType.Taxi.getValue() || trip.getType() == BizType.Hotel.getValue()) {
                    trip.setArrival_city_id(null);
                    trip.setArrival_city_name(null);
                }
                if (applyorderContract.getType() == ApplyType.ApplyTaxi.getValue() && trip.getType() == BizType.Taxi.getValue()) {
                    List<String> startCityIds = trip.getStart_city_ids();
                    if (ObjUtils.isNotEmpty(startCityIds)){
                        for (String cityId : startCityIds) {
                            cityIds.add(new Tuple<>(startTime, cityId));
                        }
                    }
                }
                String tripCityId = trip.getStart_city_id();
                if (trip.getArrival_city_id() != null) {
                    tripCityId += "-" + trip.getArrival_city_id();
                }
                cityIds.add(new Tuple<>(startTime, tripCityId));
            }

            //city_range格式:1000001-200002,2000002,300003-400004 意即:北京-上海,上海,天津-重庆
            cityIds.sort((m, n) -> (int) (m.getItem1().getTime() - n.getItem1().getTime()));
            List<String> cityIdArr = new ArrayList<>();
            for (Tuple<Date, String> cityId : cityIds) {
                cityIdArr.add(cityId.getItem2());
            }
            String cityIdVal = String.join(",", cityIdArr);
            applyorderContract.setCity_range(cityIdVal);
            if (ObjUtils.isEmpty(timeRange)||ObjUtils.isEmpty(timeRange.get(0))){
                throw new SaasException(GlobalResponseCode.ParameterError);
            }
            //将所有出现过的时间正序排列,取最小值和最大值,重新写入timeRange字段,如果最小值=最大值,则只取最小值
            logger.info("timeRange={}", JsonUtils.toJson(timeRange));
            timeRange.sort((m, n) -> (int) (m.getTime() - n.getTime()));
            Date minTime = timeRange.get(0);
            Date maxTime = timeRange.get(timeRange.size() - 1);
            String desTimeRange = DateTimeTool.fromDateToString(minTime);
            if (maxTime.getTime() != minTime.getTime()) {
                desTimeRange += "," + DateTimeTool.fromDateToString(maxTime);
            }
            applyorderContract.setTime_range(desTimeRange);
        }
        if (applyorderContract.getApprover_id() == null) {
            applyorderContract.setApprover_id("");
        }

    }

    /**
     * 检查数据有效性
     *
     * @param applyContract
     * @return
     */
    private GlobalResponseCode checkApplyData(ApplyThirdContract applyContract, String employeeId, String companyId) {
        //检查申请单
        ApplyOrderThirdContract apply = applyContract.getApply();
        if (applyContract==null||apply == null) {
            return GlobalResponseCode.ApplyIsNull;
        }
        if (apply.getType() == null
                || (apply.getType() != ApplyType.Mall.getValue()
                && apply.getType() != ApplyType.ChaiLv.getValue()
                && apply.getType() != ApplyType.Taxi.getValue()
                && apply.getType() != ApplyType.ApplyTaxi.getValue())
                && apply.getType() != ApplyType.Meishi.getValue()){
            return GlobalResponseCode.ApplyTypeInvalid;
        }
        if (apply.getBudget() != null && apply.getBudget() < 0) {
            return GlobalResponseCode.ApplyBudgetInvalid;
        }
        //检验差旅和用车的申请事由和事由补充内容和费用归属配置
        ApplySetupContract applySetupContract = messageSetupService.queryCompanyApplyConfig(companyId);
        logger.info("checkApplyData.applySetupContract={}", JsonUtils.toJson(applySetupContract));
        Integer applyDepartureDate = applySetupContract.getApply_departure_date();


            //审批单费用归属是否必填
            Integer applyAttributionCategoryLimit = null;
            if (apply.getType() == ApplyType.ChaiLv.getValue()) {
                applyAttributionCategoryLimit = applySetupContract.getApply_attribution_category_travel();
            }
            if (apply.getType() == ApplyType.Meishi.getValue()) {
                applyAttributionCategoryLimit = applySetupContract.getApply_attribution_category_meishi();
            }
            if (apply.getType() == ApplyType.TakeAway.getValue()) {
                applyAttributionCategoryLimit = applySetupContract.getApply_attribution_category_takeaway();
            }
            if (apply.getType() == ApplyType.ApplyTaxi.getValue()) {
                applyAttributionCategoryLimit = applySetupContract.getApply_attribution_category_taxi();
            }
            if (applyAttributionCategoryLimit == null || applyAttributionCategoryLimit == ApplyCategoryEnum.IS_SHOW_MUST.getValue()) {
                apply.setCost_attribution_list(Lists.newArrayList());
            }
            if (applyAttributionCategoryLimit != null && applyAttributionCategoryLimit == ApplyCategoryEnum.IS_CHECKED_MUST.getValue()) {
            /*ApplySetupContract orderSetupCostAttributionContract = messageSetupService.queryCompanyCostAttributionConfig(companyId);
            //获取订单费用归属范围
            Integer orderCostAttributionCategory = orderSetupCostAttributionContract.getCost_attribution_category();
            List<CostAttributionInfo> costAttributionList = apply.getCost_attribution_list();
            if (CollectionUtils.isEmpty(costAttributionList)) {
                return GlobalResponseCode.ApplyOrderCostAttritutionIsNull;
            }
            if (orderCostAttributionCategory != null && orderCostAttributionCategory != CostAttributionScopeEnum.DEPT_AND_PROJ.getCode()) {
                if (CollectionUtils.isEmpty(costAttributionList) || costAttributionList.size() != 1) {
                    return GlobalResponseCode.ApplyOrderCostAttritutionIsError;
                }
                if (orderCostAttributionCategory == CostAttributionScopeEnum.DEPT.getCode() && costAttributionList.get(0).getCost_attribution_category() != CostAttributionScopeEnum.DEPT.getCode()) {
                    return GlobalResponseCode.CostAttributionCategoryIsDept;
                }
                if (orderCostAttributionCategory == CostAttributionScopeEnum.PROJ.getCode() && costAttributionList.get(0).getCost_attribution_category() != CostAttributionScopeEnum.PROJ.getCode()) {
                    return GlobalResponseCode.CostAttributionCategoryIsCostCenter;
                }
            }
            if (orderCostAttributionCategory != null && orderCostAttributionCategory == CostAttributionScopeEnum.DEPT_AND_PROJ.getCode() && costAttributionList.size() != 2) {
                return GlobalResponseCode.ApplyOrderCostAttritutionIsError;
            }*/

                // 检查费用归属灰度开关 进行灰度校验
                Integer costAttributionNewSwitch = iMessageSetupRpcService.queryCostAttributionNewSwitch(companyId);
                logger.info("costAttributionNewSwitch:{}",costAttributionNewSwitch);
                logger.info("getNeed_check_new_cost_info={}", apply.getNeed_check_new_cost_info());
                if (Objects.nonNull(apply.getNeed_check_new_cost_info())
                        && Objects.equals(apply.getNeed_check_new_cost_info(), NumberUtils.INTEGER_ONE) // 需要校验新费用归属
                        && CommonSwitchConstant.OPEN.equals(costAttributionNewSwitch)){
                    if (StringUtils.isNotBlank(employeeId)) {
                        // 预定人id，三方只有提交人
                        List<String> userIds = Lists.newArrayList(employeeId);
                        com.fenbeitong.saas.core.contract.order.check.CostInfoContract costInfoContract = JSON.parseObject(JSON.toJSONString(apply.getCost_info()), com.fenbeitong.saas.core.contract.order.check.CostInfoContract.class);
                        CostCheckVO costCheckVO = new CostCheckVO();
                        costCheckVO.setCompanyId(companyId);
                        costCheckVO.setUserIdList(userIds);
                        costCheckVO.setCategory(apply.getCost_attribution_category());
                        costCheckVO.setCostInfo(costInfoContract);
                        costCheckVO.setIsThird(ObjUtils.isNotEmpty(apply.getThird_id()));
                        costCheckVO.setIsCheckThird(true);
                        currencyCheckService.newCheckCostAttribution(costCheckVO);
                    }
                } else {
                    GlobalResponseCode code = commonValidService.validThirdCostAttributionSetting(companyId, apply);
                    logger.info("checkApplyData.code={}", JsonUtils.toJson(code));
                    if (code != GlobalResponseCode.Success) {
                        return code;
                    }
                }

                Integer bringIn = 0;
                if (apply.getType() == ApplyType.ChaiLv.getValue()) {
                    bringIn = applySetupContract.getApply_attribution_category_modifiable_travel();
                }
                if (apply.getType() == ApplyType.Meishi.getValue()) {
                    bringIn = applySetupContract.getApply_attribution_category_modifiable_meishi();
                }
                if (apply.getType() == ApplyType.TakeAway.getValue()) {
                    bringIn = applySetupContract.getApply_attribution_category_modifiable_takeaway();
                }
                if (apply.getType() == ApplyType.ApplyTaxi.getValue()) {
                    bringIn = applySetupContract.getApply_attribution_category_modifiable_taxi();
                }
                apply.setBring_in(bringIn);
            }


        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        //检查行程
        if (apply.getType() != ApplyType.Mall.getValue()) {
            List<ApplyTripInfoContract> trips = applyContract.getTrip_list();
            if (trips == null || trips.size() == 0) {
                //差旅和用车必须要有行程
                return GlobalResponseCode.ApplyTripIsNull;
            }
            if (apply.getType() == ApplyType.ApplyTaxi.getValue()){
                if (trips.size() > 1) {
                    //用车数据不超过1个
                    return GlobalResponseCode.ApplyThirdTripGreaterOneIsError;
                }
            }
            if (trips.size() > 100) {
                //行程数据不超过100个
                return GlobalResponseCode.ApplyThirdTripGreaterHundredIsError;
            }
            if (apply.getType() == ApplyType.Meishi.getValue()&&trips.size() > 1){
                return GlobalResponseCode.ApplyThirdTripGreaterOneIsError;
            }
            BigDecimal tripTotalMoney = BigDecimal.valueOf(0);
            for (ApplyTripInfoContract trip : trips) {
                tripTotalMoney = tripTotalMoney.add(trip.getEstimated_amount() == null ? BigDecimal.valueOf(0) : trip.getEstimated_amount());
                if (trip.getType() == null) {
                    return GlobalResponseCode.ApplyTripTypeInvalid;
                }
                //检测行程单类型
                if (apply.getType() == ApplyType.ChaiLv.getValue()) {
                    if (trip.getType() != BizType.Air.getValue()
                            && trip.getType() != BizType.Hotel.getValue()
                            && trip.getType() != BizType.Train.getValue()
                            && trip.getType() != BizType.IntlAir.getValue()) {
                        return GlobalResponseCode.ApplyTripNotTravel;
                    }
                    BigDecimal estimatedAmount = trip.getEstimated_amount();
//                    if (applySetupContract.getWhether_trip_apply_budget() == SaasMessageConstant.IS_CHECKED_TRUE) {
//                        if (ObjUtils.isBlank(estimatedAmount) || estimatedAmount.compareTo(BigDecimal.ZERO) <= 0) {
//                            return GlobalResponseCode.EstimatedAmountIsNull;
//                        }
//                    }
                    //检测地点
                    ApplyTripCityType applyTripCityType = ApplyTripCityType.valueOf(applySetupContract.getApply_trip_city());
                    if (applyTripCityType == ApplyTripCityType.Required) {
                        if (ObjUtils.isBlank(trip.getStart_city_id())) {
                            return GlobalResponseCode.ApplyTripStartCityInvalid;
                        }
                        if (!NumberUtils.isDigits(trip.getStart_city_id())) {
                            return GlobalResponseCode.ApplyTripCodeError;
                        }
                        if (trip.getType() == BizType.Air.getValue() || trip.getType() == BizType.Train.getValue() || trip.getType() == BizType.IntlAir.getValue()) {
                            if (ObjUtils.isBlank(trip.getArrival_city_id())) {
                                return GlobalResponseCode.ApplyTripArrivalCityInvalid;
                            }
                            if (!NumberUtils.isDigits(trip.getArrival_city_id())) {
                                return GlobalResponseCode.ApplyTripCodeError;
                            }
                        }
                    } else if (applyTripCityType == ApplyTripCityType.Hidden) {
                        trip.setStart_city_id(SaasAreaConstant.UNSET_CITY_ID);
                        trip.setStart_city_name(SaasAreaConstant.UNSET_CITY_NAME);
                        if (trip.getType() == BizType.Air.getValue() || trip.getType() == BizType.Train.getValue() || trip.getType() == BizType.IntlAir.getValue()) {
                            trip.setArrival_city_id(SaasAreaConstant.UNSET_CITY_ID);
                            trip.setArrival_city_name(SaasAreaConstant.UNSET_CITY_NAME);
                        }
                    } else {
                        if (ObjUtils.isBlank(trip.getStart_city_id())) {
                            trip.setStart_city_id(SaasAreaConstant.UNSET_CITY_ID);
                            trip.setStart_city_name(SaasAreaConstant.UNSET_CITY_NAME);
                        }
                        if (trip.getType() == BizType.Air.getValue() || trip.getType() == BizType.Train.getValue() || trip.getType() == BizType.IntlAir.getValue()) {
                            if (ObjUtils.isBlank(trip.getArrival_city_id())) {
                                trip.setArrival_city_id(SaasAreaConstant.UNSET_CITY_ID);
                                trip.setArrival_city_name(SaasAreaConstant.UNSET_CITY_NAME);
                            }
                        }
                    }
                } else if (apply.getType() == ApplyType.Taxi.getValue()) {
                    if (trip.getType() != BizType.Taxi.getValue()) {
                        return GlobalResponseCode.ApplyTripNotTaxi;
                    }
                } else if (apply.getType() == ApplyType.ApplyTaxi.getValue()) {
                    if (trip.getType() != BizType.Taxi.getValue()) {
                        return GlobalResponseCode.ApplyTripNotTaxi;
                    }
                }
                Date startTime = null;
                //检测时间
                if (trip.getStart_time() == null) {
                    return GlobalResponseCode.ApplyTripStartDateInvalid;
                } else {
                    try {
                        startTime = sdf.parse(trip.getStart_time());
                    } catch (Exception ex) {
                        return GlobalResponseCode.ApplyTripStartDateInvalid;
                    }
                }
                //校验国内机票、国际机票和火车出发时间为时间段的情况
                if ((trip.getType() == BizType.Air.getValue() || trip.getType() == BizType.Train.getValue() || trip.getType() == BizType.IntlAir.getValue()) && applyDepartureDate == 1) {
                    logger.info("applyDepartureDate{}", applyDepartureDate);
                    if (trip.getEnd_time() == null) {
                        return GlobalResponseCode.ApplyTripEndDateInvalid;
                    } else {
                        Date endTime = null;
                        try {
                            endTime = sdf.parse(trip.getEnd_time());
                        } catch (Exception ex) {
                            return GlobalResponseCode.ApplyTripEndDateInvalid;
                        }
                        if (startTime.getTime() > endTime.getTime()) {
                            return GlobalResponseCode.ApplyTripStartDateCannotBiggerThanEndDate;
                        }
                    }
                }
                //国内机票、国际机票和火车出发时间不为时间段处理数据
                if ((trip.getType() == BizType.Air.getValue() || trip.getType() == BizType.Train.getValue() || trip.getType() == BizType.IntlAir.getValue()) && applyDepartureDate != 1) {
                    logger.info("applyDepartureDate1{}", applyDepartureDate);
                    trip.setEnd_time(null);
                    trip.setBack_end_time(null);
                }
                if (trip.getType() != BizType.Air.getValue() && trip.getType() != BizType.Train.getValue() && trip.getType() != BizType.IntlAir.getValue()) {
                    if (trip.getEnd_time() == null) {
                        //除机票、火车外的其他都有行程结束日期
                        return GlobalResponseCode.ApplyTripEndDateInvalid;
                    } else {
                        Date endTime = null;
                        try {
                            endTime = sdf.parse(trip.getEnd_time());
                        } catch (Exception ex) {
                            return GlobalResponseCode.ApplyTripEndDateInvalid;
                        }
                        if (startTime.getTime() > endTime.getTime()) {
                            return GlobalResponseCode.ApplyTripStartDateCannotBiggerThanEndDate;
                        }
                    }
                }

                if (trip.getType() == BizType.IntlAir.getValue()) {
                    if (ObjUtils.isEmpty(trip.getTrip_type())){
                        return GlobalResponseCode.ApplyTripTypeIsNull;
                    }
                    if (trip.getTrip_type() == TripType.oneWay.getValue() && StringUtils.isBlank(trip.getStart_time())) {
                        return GlobalResponseCode.IntlAirInvalidError;
                    }
                    if (trip.getTrip_type() == TripType.goAndBack.getValue() && (StringUtils.isBlank(trip.getBack_start_time()) || StringUtils.isBlank(trip.getStart_time()))) {
                        return GlobalResponseCode.IntlAirInvalidError;
                    }
                }
            }
            Integer budget = apply.getBudget();
            //之前预算是相等的，第三方没法改
            if (apply.getType() != ApplyType.ApplyTaxi.getValue()&&apply.getType() != ApplyType.Meishi.getValue()){
                if (tripTotalMoney.compareTo(BigDecimal.valueOf(budget)) != 0) {
                    return GlobalResponseCode.ApplyTripBudgetError;
                }
            }
        }
        return GlobalResponseCode.Success;
    }


    private int setStatus(String id, ApplyStatus status, Date time, Integer logId) {
        int count = applyOrderExtMapper.setStatus(id, status.getValue(), time, logId);
        return count;
    }

    public int setStatus(String id, ApplyStatus status, Integer logId) {
        return setStatus(id, status, new Date(), logId);
    }

    private ApplyOrder getApplyOrderModelByIdAndUserIdAndCompanyId(ApplyThirdContract applyThirdContract, String userId, String companyId, UserRole userRole) {
        if (StringTool.isNullOrEmpty(applyThirdContract.getApply_id()) || StringTool.isNullOrEmpty(userId) || StringTool.isNullOrEmpty(companyId)) {
            return null;
        }
        ApplyOrder apply = null;
        if (applyThirdContract.getThird_type() == SaasFlowConstant.FBT_TYPE) {
            apply = applyMapper.selectByPrimaryKey(applyThirdContract.getApply_id());
        } else if (applyThirdContract.getThird_type() == SaasFlowConstant.THIRD_TYPE) {
            ApplyOrderExample applyOrderExample = new ApplyOrderExample();
            applyOrderExample.createCriteria().andThirdIdEqualTo(applyThirdContract.getApply_id()).andCompanyIdEqualTo(companyId).andDeleteStatusEqualTo(0);
            applyOrderExample.setOrderByClause("create_time desc");
            List<ApplyOrder> applyOrderList = applyMapper.selectByExample(applyOrderExample);
            if (CollectionUtils.isEmpty(applyOrderList)) {
                return null;
            }
            apply = applyOrderList.get(0);
        }
        if (apply == null) {
            return null;
        }
        if (apply.getCompanyId() == null) {
            return null;
        }
        if (!apply.getEmployeeId().equals(userId)) {
            //如果不是本人，再判断一下userrole是不是分贝管理员或者企业管理员
            if (userRole == null || (userRole != UserRole.CompanyAdmin && userRole != UserRole.CompanySuperAdmin && userRole != UserRole.FbAdmin)) {
                return null;
            }
        }
        return apply;
    }

    private List<ApplyTripInfoContract> getTripListByApplyOrderId(String applyOrderId) {
        List<ApplyTripInfo> tripList = applyTripInfoExtMapper.selectListByApplyOrderId(applyOrderId);
        if (tripList == null || tripList.size() == 0) {
            return null;
        }
        List<ApplyTripInfoContract> tripContractList = new ArrayList<ApplyTripInfoContract>();
        for (ApplyTripInfo trip : tripList) {
            ApplyTripInfoContract contract = ApplyTripInfoContract.FromModel(trip);
            tripContractList.add(contract);
        }
        return tripContractList;
    }

    private List<UserContactContract> getGuestListByApplyOrderId(String applyOrderId, String token) {
        List<ApplyTripGuest> guestList = applyGuestExtMapper.selectListByApplyOrderId(applyOrderId);
        if (CollectionUtils.isEmpty(guestList)) {
            return null;
        }
        List<UserContactContract> guestContractList = new ArrayList<UserContactContract>();
        for (ApplyTripGuest guest : guestList) {
            UserContactContract contract;
            if (StringUtils.isNotBlank(guest.getContactInfo())) {
                contract = JSONObject.parseObject(guest.getContactInfo(), UserContactContract.class);
                if (StringUtils.isBlank(contract.getName()) && StringUtils.isNotBlank(contract.getId())) {
                    try {
                        Map<String, String> map = Maps.newHashMap();
                        map.put(UserConstant.HEADER_TOKEN_NAME, token);
                        String data = HttpTool.get(URL_GET_FREQUENT_DATA + "?id=" + contract.getId(), map);

                        HLResponseModelBaseContract<UserContactContract> jo = JSONObject.parseObject(data, HLResponseModelBaseContract.class);
                        Object jsonData = jo.getData();
                        if (jsonData != null) {
                            if (jsonData instanceof JSONObject) {
                                UserContactContract contractInfo = JSONObject.parseObject(jsonData.toString(), UserContactContract.class);
                                if (contractInfo != null && StringUtils.isNotBlank(contractInfo.getName())) {
                                    contract = contractInfo;
                                }
                            }
                        }
                    } catch (Exception e) {
                        logger.error("获取同行人信息接口异常：" + URL_GET_FREQUENT_DATA + "?id=" + contract.getId() + e.getMessage(), e);
                        e.printStackTrace();
                    }
                }
                if (StringUtils.isNotBlank(contract.getName())) {
                    guestContractList.add(contract);
                }
            }/* else {
                contract = UserContactContract.FromModel(guest);
            }*/
        }
        for (UserContactContract userContactContract : guestContractList) {
            if (StringUtils.isBlank(userContactContract.getId_number())) {
                userContactContract.setId_number("");
            }
            if (userContactContract.getId_type() == null) {
                KeyValueItem keyValueItem = new KeyValueItem();
                keyValueItem.setKey(1);
                keyValueItem.setValue("");
                userContactContract.setId_type(keyValueItem);
            }
            userContactContract.setGender(null);
        }
        return guestContractList;
    }

    private void appendTripCityNames(List<ApplyTripInfoContract> trips) {
        if (trips == null || trips.size() == 0) {
            return;
        }
        List<String> cityIds = new ArrayList<String>();
        for (ApplyTripInfoContract trip : trips) {
            if (!cityIds.contains(trip.getStart_city_id())) {
                cityIds.add(trip.getStart_city_id());
            }
            if (trip.getArrival_city_id() != null && !cityIds.contains(trip.getArrival_city_id())) {
                cityIds.add(trip.getArrival_city_id());
            }
        }
        List<IdNameContract> cityNames = cityService.getCityNamesByIds(cityIds);
        for (ApplyTripInfoContract trip : trips) {
            IdNameContract cityName = getNameFromListById(cityNames, trip.getStart_city_id());
            if (cityName != null) {
                trip.setStart_city_name(cityName.getName());
            }
            if (trip.getArrival_city_id() != null) {
                cityName = getNameFromListById(cityNames, trip.getArrival_city_id());
                if (cityName != null) {
                    trip.setArrival_city_name(cityName.getName());
                }
            }
        }
    }

    private void resetOrderStateToPendingAuditOrTransfer(ApplyOrderV2Contract order, String userId) {
        boolean isFlow = ValueTool.areEqual(order.getFlow_type(), CompanyApplyType.Flow.getValue()) || ValueTool.areEqual(order.getFlow_type(), CompanyApplyType.CONDITIONAL.getValue());
        if (!isFlow) {
            if (order.getApprover_id() != null) {
                if (order.getState() == ApplyStatus.PendingAudit.getValue() && !userId.equals(order.getApprover_id()) && !order.getEmployee_id().equals(userId)) {
                    order.setState(ApplyStatus.Transfer.getValue());
                }
            }
        }
    }

    private IdNameContract getNameFromListById(List<IdNameContract> list, String id) {
        if (list == null || id == null) {
            return null;
        }
        for (IdNameContract item : list) {
            if (item.getId().equals(id)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 写入员工姓名及所属部门,多个部门以,隔开
     *
     * @param token
     * @param companyId
     * @param data
     */
    private void appendApplyEmployeeNameAndDept(String token, String companyId, List<ApplyOrderV2Contract> data) {
        if (StringTool.isNullOrEmpty(token)) {
            return;
        }
        List<String> employeeIds = new ArrayList<>();
        for (ApplyOrderV2Contract order : data) {
            if (!employeeIds.contains(order.getEmployee_id())) {
                employeeIds.add(order.getEmployee_id());
            }
        }
        List<EmployeeNameAndDeptContract> employeeNameAndDeptList = userService.getNamesAndDeptsByIds(token, companyId, employeeIds);
        if (employeeNameAndDeptList != null && employeeNameAndDeptList.size() > 0) {
            for (ApplyOrderV2Contract order : data) {
                for (EmployeeNameAndDeptContract nditem : employeeNameAndDeptList) {
                    if (nditem.getEmployee_id().equals(order.getEmployee_id())) {
                        order.setUser_name(nditem.getName());
                        if (nditem.getDepts() != null) {
                            order.setUser_dept(String.join(",", nditem.getDepts()));
                        }
                        break;
                    }
                }
            }
        }

        for (ApplyOrderV2Contract order : data) {
            if (!employeeIds.contains(order.getApprover_id())) {
                employeeIds.add(order.getApprover_id());
            }
        }
        List<EmployeeNameAndDeptContract> approverNameAndDeptList = userService.getNamesAndDeptsByIds(token, companyId, employeeIds);
        if (!CollectionUtils.isEmpty(approverNameAndDeptList)) {
            for (ApplyOrderV2Contract order : data) {
                for (EmployeeNameAndDeptContract nditem : approverNameAndDeptList) {
                    if (order.getApprover_id().equals(nditem.getEmployee_id())) {
                        order.setApprover_name(nditem.getName());
                        if (nditem.getDepts() != null) {
                            order.setApprover_dept(String.join(",", nditem.getDepts()));
                        }
                        break;
                    }
                }
            }
        }
    }

    private void appendApplyOrderData(List<ApplyOrderV2Contract> dataList) {
        if (dataList == null || dataList.size() == 0) {
            return;
        }
        //格式化时间显示
        for (ApplyOrderV2Contract data : dataList) {
            String timeRange = data.getTime_range();
            if (timeRange != null && timeRange.length() > 0) {
                String[] timeList = timeRange.split(",");
                for (int i = 0; i < timeList.length; i++) {
                    timeList[i] = DateTimeTool.fromDateToDisplayString(DateTimeTool.fromStringToDate(timeList[i]));
                }
                String timeVal = String.join("-", timeList);
                data.setTime_range(timeVal);
            }
        }
        //找出所有的城市ID
        List<String> cityIds = new ArrayList<>();
        for (ApplyOrderV2Contract data : dataList) {
            String city_range = data.getCity_range();
            if (!StringTool.isNullOrEmpty(city_range)) {
                for (String cityIdArr : city_range.split(",")) {
                    for (String cityId : cityIdArr.split("-")) {
                        if (!cityIds.contains(cityId)) {
                            cityIds.add(cityId);
                        }
                    }
                }
            }
        }
        //用城市ID获取城市名称并设置到结果集中
        if (cityIds.size() > 0) {
            List<IdNameContract> cityNames = cityService.getCityNamesByIds(cityIds);
            for (ApplyOrderV2Contract data : dataList) {
                String city_range = data.getCity_range();
                if (!StringTool.isNullOrEmpty(city_range)) {
                    List<String> city_names = new ArrayList<>();
                    for (String cityIdArr : city_range.split(",")) {
                        List<String> city_names2 = new ArrayList<>();
                        for (String cityId : cityIdArr.split("-")) {
                            IdNameContract model = getModelFromList(cityNames, cityId);
                            if (model != null && !StringTool.isNullOrEmpty(model.getName())) {
                                city_names2.add(model.getName().replace(CoreLanguage.Common_Value_CityText.getMessage(), "").replace(CoreLanguage.Common_Value_CityNextText.getMessage(), ""));
                            }
                        }
                        if (city_names2.size() > 0) {
                            city_names.add(String.join("-", city_names2));
                        }
                    }
                    data.setCity_range(String.join("/", city_names));
                }
            }
        }
    }

    private IdNameContract getModelFromList(List<IdNameContract> list, String id) {
        for (IdNameContract model : list) {
            if (model.getId().equals(id)) {
                return model;
            }
        }
        return null;
    }

    /**
     * 我发起的列表
     *
     * @param token
     * @param userId
     * @param companyId
     * @param page
     * @param pageSize
     * @param status
     * @return
     */
    @Override
    public PageDataBaseContract<List<ApplyOrderV2Contract>> queryStartApplyList(HttpServletRequest request, String token, String userId, String companyId, int page, int pageSize, StartThirdApplyType status) {
        if (pageSize <= 0) {
            pageSize = 50;
        }
        if (page < 1) {
            page = 1;
        }
        int pageStart = (page - 1) * pageSize;
        PageDataBaseContract<List<ApplyOrderV2Contract>> result = new PageDataBaseContract<>();
        result.setPage_index(page);
        result.setPage_size(pageSize);
        String startTime = request.getParameter("start_time");
        if (StringUtils.isBlank(startTime)) {
            startTime = null;
        }
        Date startData = null;
        if (StringUtils.isNotBlank(startTime)) {
            startData = DateTimeTool.fromStringToDateTime(startTime);
        }
        String endTime = request.getParameter("end_time");
        if (StringUtils.isBlank(endTime)) {
            endTime = null;
        }
        Date endData = null;
        if (StringUtils.isNotBlank(endTime)) {
            endData = DateTimeTool.fromStringToDateTime(endTime);
        }
        Integer startApplyTotal = applyOrderExtMapper.queryStartThirdApplyNumList(userId, companyId, status.getValue(), startData, endData);
        result.setTotal(startApplyTotal);
        if (startApplyTotal > 0) {
            List<ApplyOrderV2Contract> data = new ArrayList<>();
            List<ApplyOrder> applyDataList = applyOrderExtMapper.queryStartThirdApplyOrderList(userId, companyId, pageStart, pageSize, status.getValue(), startData, endData);
            for (ApplyOrder apply : applyDataList) {
                List<ApplyTripInfoContract> applyTripInfoContractList = Lists.newArrayList();
                ApplyOrderV2Contract applyOrderV2Contract = ApplyOrderV2Contract.FromExtModel(apply);
                List<ApplyTripInfo> applyTripInfos = applyTripInfoExtMapper.queryAllTripListByApplyOrderId(apply.getId());
                for (ApplyTripInfo applyTripInfo : applyTripInfos) {
                    applyTripInfoContractList.add(ApplyTripInfoContract.FromModel(applyTripInfo));
                }
                appendTripCityNames(applyTripInfoContractList);
                applyOrderV2Contract.setTrip_list(applyTripInfoContractList);
                data.add(applyOrderV2Contract);
            }
            result.setData(data);
            appendApplyOrderData(data);
            appendApplyEmployeeNameAndDept(token, companyId, data);
            for (ApplyOrderV2Contract order : data) {
                resetOrderStateToPendingAuditOrTransfer(order, userId);
                if (StringUtils.isBlank(order.getLog())) {
                    order.setLog("");
                }
            }
            for (ApplyOrderV2Contract dataInfo : data) {
                dataInfo.setEmployee_id(null);
                dataInfo.setCompany_id(null);
                dataInfo.setApprover_id(null);
                dataInfo.setTravel_price_detail(null);
                dataInfo.setState_name(null);
                dataInfo.setType_name(null);
            }
        } else {
            result.setData(Lists.newArrayList());
        }
        return result;
    }

    @Override
    public ApplyTripInfoContract queryApplyTripInfo(String userId, String companyId, String applyId, String id, String token) throws SaasException {
        if (StringUtils.isBlank(companyId) || StringUtils.isBlank(userId)) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
        ApplyTripInfo applyTripInfo = applyTripInfoExtMapper.queryApplyTripInfoByCompanyId(applyId, id, userId, companyId);
        ApplyTripInfoContract applyTripInfoContract = ApplyTripInfoContract.FromModel(applyTripInfo);
        List<ApplyTripInfoContract> applyTripList = Lists.newArrayList();
        applyTripList.add(applyTripInfoContract);
        appendTripCityNames(applyTripList);
        for (ApplyTripInfoContract trip : applyTripList) {
            appendTripCityVenderInfo(token, trip);
        }
        try {
            if (applyTripInfoContract.getType() == 7) {//机票
                String start_city_id = applyTripInfoContract.getStart_city_id();
                String arrival_city_id = applyTripInfoContract.getArrival_city_id();
                logger.info("获取飞机的机场信息:" + URL_GET_CITY_DATA + "?area_ids=" + start_city_id + "," + arrival_city_id + "&category_id=" + 5);
                String data = HttpTool.get(URL_GET_CITY_DATA + "?area_ids=" + start_city_id + "," + arrival_city_id + "&category_id=" + 5);
                logger.info("获取飞机的机场信息返回结果:" + data);
                Map<String, Object> jo = JSONObject.parseObject(data, HashMap.class);
                List<Map<String, Object>> dataMap = (List<Map<String, Object>>) jo.get("data");
                for (Map<String, Object> resultMap : dataMap) {
                    String idInfo = ObjUtils.toString(resultMap.get("id"), "");
                    String description = ObjUtils.toString(resultMap.get("description"), "");
                    if (StringUtils.isBlank(description)) {
                        description = "";
                    }
                    List stationList = JsonUtils.toObj(JsonUtils.toJson(resultMap.get("station_list")), List.class);
                    List allStationList = JsonUtils.toObj(JsonUtils.toJson(resultMap.get("all_station_list")), List.class);
                    if (idInfo.equals(start_city_id)) {
                        applyTripInfoContract.setStart_description(description);
                        applyTripInfoContract.setStart_station_list(stationList);
                        applyTripInfoContract.setStart_all_station_list(allStationList);
                    } else if (idInfo.equals(arrival_city_id)) {
                        applyTripInfoContract.setArrival_description(description);
                        applyTripInfoContract.setArrival_station_list(stationList);
                        applyTripInfoContract.setArrival_all_station_list(stationList);
                    }
                }
            } else if (applyTripInfoContract.getType() == 15) {//火车
                String start_city_id = applyTripInfoContract.getStart_city_id();
                String arrival_city_id = applyTripInfoContract.getArrival_city_id();
                logger.info("获取火车的车站信息:" + URL_GET_CITY_DATA + "?area_ids=" + start_city_id + "," + arrival_city_id + "&category_id=" + 4);
                String data = HttpTool.get(URL_GET_CITY_DATA + "?area_ids=" + start_city_id + "," + arrival_city_id + "&category_id=" + 4);
                logger.info("获取火车的车站信息返回结果:" + data);
                Map<String, Object> jo = JSONObject.parseObject(data, HashMap.class);
                List<Map<String, Object>> dataMap = (List<Map<String, Object>>) jo.get("data");
                for (Map<String, Object> resultMap : dataMap) {
                    String idInfo = ObjUtils.toString(resultMap.get("id"), "");
                    String description = ObjUtils.toString(resultMap.get("description"), "");
                    List stationList = JsonUtils.toObj(JsonUtils.toJson(resultMap.get("station_list")), List.class);
                    List allStationList = JsonUtils.toObj(JsonUtils.toJson(resultMap.get("all_station_list")), List.class);
                    if (idInfo.equals(start_city_id)) {
                        applyTripInfoContract.setStart_description(description);
                        applyTripInfoContract.setStart_station_list(stationList);
                        applyTripInfoContract.setStart_all_station_list(allStationList);
                    } else if (idInfo.equals(arrival_city_id)) {
                        applyTripInfoContract.setArrival_description(description);
                        applyTripInfoContract.setArrival_station_list(stationList);
                        applyTripInfoContract.setArrival_all_station_list(stationList);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(String.format("url:%s,/city/listStationsByAreaIds异常", URL_GET_CITY_DATA));
            throw new SaasException(GlobalResponseCode.QueryCityLISTSTATIONByAREAIDSError);
        }
        return applyTripInfoContract;
    }

    private void appendTripCityVenderInfo(String token, ApplyTripInfoContract trip) {
        List<String> cityIds = new ArrayList<>();
        cityIds.add(trip.getStart_city_id());
        if (trip.getArrival_city_id() != null) {
            cityIds.add(trip.getArrival_city_id());
        }
        List<VenderCityInfoContract> venderCityInfos = cityService.getVenderCityInfoByCityIds(token, cityIds, BizType.valueOf(trip.getType().intValue()), "1.9.3");
        if (venderCityInfos != null && venderCityInfos.size() > 0) {
            for (VenderCityInfoContract cityInfo : venderCityInfos) {
                if (trip.getStart_city_id().equals(cityInfo.getArea_id())) {
                    trip.setStart_city(applyService.handleCityCode(cityInfo.getVendorMsg()));
                    break;
                }
            }
            if (trip.getArrival_city_id() != null) {
                for (VenderCityInfoContract cityInfo : venderCityInfos) {
                    if (trip.getArrival_city_id().equals(cityInfo.getArea_id())) {
                        trip.setArrival_city(applyService.handleCityCode(cityInfo.getVendorMsg()));
                        break;
                    }
                }
            }
        }
    }

    /**
     * 第三方审批单信息
     *
     * @param applyId
     * @param orderId
     * @return
     */
    @Override
    public ApplyOrderV2Contract queryApplyOrderDetail(String applyId, String orderId) {
        ApplyOrder applyOrder = applyOrderExtMapper.queryApplyOrderDetail(applyId, orderId);
        if (applyOrder == null) {
            throw new SaasException(GlobalResponseCode.ApplyIsNull);
        }
        List<ApplyTripInfoContract> applyTripInfoContractList = Lists.newArrayList();
        ApplyOrderV2Contract applyOrderV2Contract = ApplyOrderV2Contract.FromExtModel(applyOrder);
        List<ApplyTripInfo> applyTripInfos = applyTripInfoExtMapper.queryAllTripListByApplyOrderId(applyOrder.getId());
        for (ApplyTripInfo applyTripInfo : applyTripInfos) {
            applyTripInfoContractList.add(ApplyTripInfoContract.FromModel(applyTripInfo));
        }
        appendTripCityNames(applyTripInfoContractList);
        applyOrderV2Contract.setTrip_list(applyTripInfoContractList);
        List<ApplyOrderV2Contract> applyOrderV2ContractList = Lists.newArrayList();
        applyOrderV2ContractList.add(applyOrderV2Contract);
        appendApplyOrderData(applyOrderV2ContractList);
        return applyOrderV2Contract;
    }

    @Override
    @Transactional(value = "fenbeitong")
    public GlobalResponseCode revoke(String userId, String companyId, ApplyThirdContract applyThirdContract, String ip) {
        if (applyThirdContract == null) {
            return GlobalResponseCode.ParametersIsNull;
        }
        ApplyOrder order = getApplyOrderModelByIdAndUserIdAndCompanyId(applyThirdContract, userId, companyId, null);
        if (order == null) {
            return GlobalResponseCode.NotFound;
        }
        Date now = new Date();
        if (applyThirdContract.getThird_type() == SaasFlowConstant.FBT_TYPE) {
            applyOrderExtMapper.setStatus(applyThirdContract.getApply_id(), ApplyStatus.Backout.getValue(), now, null);
        } else if (applyThirdContract.getThird_type() == SaasFlowConstant.THIRD_TYPE) {
            applyOrderExtMapper.setThirdStatus(applyThirdContract.getApply_id(), ApplyStatus.Backout.getValue(), now, companyId);
        }
        // 作废申请单，释放预算
        iApplyOccupyBudgetService.releaseBudget(applyThirdContract.getApply_id(), false);
        return GlobalResponseCode.Success;
    }

    @Override
    public ApplyThirdContract queryApplyRule(String companyId, String applyId) {
        ApplyThirdContract applyThirdContract = new ApplyThirdContract();
        applyThirdContract.setRule_type(1);
        Integer approveType = queryCompanyApplyType(companyId);
        //2:审批单中带有规则信息
        if (approveType == 2) {
            applyThirdContract.setRule_type(2);
        }
        ApplyRuleSetting applyRuleSetting = applyRuleSettingExtMapper.queryApplyRuleByApplyOrderId(applyId);
        if (applyRuleSetting == null) {
            return applyThirdContract;
        }
        List<MyRuleDisployContract> myRuleDisployContracts = applyV2Service.applyRuleInfo(applyRuleSetting);
        if (CollectionUtils.isNotEmpty(myRuleDisployContracts)) {
            applyThirdContract.setRule_info(myRuleDisployContracts);
        }
        return applyThirdContract;
    }

    private Integer queryCompanyApplyType(String companyId) {
        Map<String, String> header = Maps.newHashMap();
        header.put("Content-Type", "application/x-www-form-urlencoded");
        Map<String, String> map = Maps.newHashMap();
        map.put("company_id", companyId);
        logger.info("获取是否是不用审批公司的接口参数:" + "/open/hgm/company/approve_type", map, header);
        String data = HttpClientUtils.post(URL_GET_APPROVE_DATA, map, header);
        logger.info("获取是否是不用审批公司的接口返回结果:" + data);
        Map<String, Object> resultData = JSONObject.parseObject(data, HashMap.class);
        if (resultData == null) {
            throw new SaasException(GlobalResponseCode.OrderApproveCheck);
        }
        Integer resultCode = ObjUtils.toInteger(resultData.get("code"));
        if (resultCode == null || resultCode != 0) {
            throw new SaasException(GlobalResponseCode.OrderApproveCheck);
        }
        Map<String, Object> dataMap = (Map<String, Object>) resultData.get("data");
        return ObjUtils.toInteger(dataMap.get("approve_type"), 0);
    }

    /**
     * 变更申请单
     *
     * @param applyContract
     * @return
     */
    @Override
    public GlobalResponseCode change(String token, ApplyThirdContract applyContract, String userId, String companyId, String ip) throws SaasException {
        //检查数据信息
        GlobalResponseCode code = checkChangeApplyData(applyContract, userId, companyId);
        if (code != GlobalResponseCode.Success) {
            return code;
        }
        ApplyOrder applyOrder = getApplyOrderModelByIdAndUserIdAndCompanyId(applyContract, userId, companyId, null);
        ApplyOrderThirdContract applyOrderContract = applyContract.getApply();
        //创建申请单
        String applyId = IDTool.CreateUniqueID();
        applyOrderContract.setId(applyId);
        applyOrderContract.setEmployee_id(userId);
        applyOrderContract.setCompany_id(companyId);
        CompanyApplyType companyApplyType = CompanyApplyType.valueOf(applyOrderContract.getFlow_type());
        if (companyApplyType == CompanyApplyType.Unknown) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
        applyOrderContract.setFlow_type(companyApplyType.getValue());
        //整理数据
        clearApplyData(applyContract);
        //获取联系人数据
        List<UserContactContract> guestList = applyContract.getGuest_list();
        if (!CollectionUtils.isEmpty(guestList)) {
            if (StringUtils.isNotBlank(guestList.get(0).getName())) {
                for (UserContactContract guest : guestList) {
                    guest.setDesc(JSONObject.toJSONString(guest));
                }
            } else {
                List<String> guestIds = new ArrayList<>();
                for (UserContactContract contact : guestList) {
                    guestIds.add(contact.getId());
                }
                List<UserContactContract> contactInfos = userService.getContacts(token, guestIds);
                if (contactInfos == null) {
                    throw new SaasException(GlobalResponseCode.ApplyContactNotFound);
                }
                for (UserContactContract guest : guestList) {
                    UserContactContract guestInfo = null;
                    for (UserContactContract tmpInfo : contactInfos) {
                        if (tmpInfo.getId().equals(guest.getId())) {
                            guestInfo = tmpInfo;
                            break;
                        }
                    }
                    if (guestInfo == null) {
                        throw new SaasException(GlobalResponseCode.ApplyContactNotFound);
                    }
                    //用desc字段存储序列化后的联系人值
                    guest.setDesc(JSONObject.toJSONString(guestInfo));
                }
            }
        }
        //修改原单信息
        ApplyOrder applyOrderInfo = new ApplyOrder();
        applyOrderInfo.setId(applyOrder.getId());
        applyOrderInfo.setState(ApplyStatus.Changed.getValue());
        applyOrderInfo.setUpdateTime(new Date());
        int updateNum = applyMapper.updateByPrimaryKeySelective(applyOrderInfo);
        if (updateNum == 0) {
            throw new SaasException(GlobalResponseCode.ParentApplyOrderNotFound);
        }
        Date now = new Date();
        List<ApplyTripInfoContract> tripList = applyContract.getTrip_list();
        ApplyOrder apply = applyOrderContract.ToModel();
        //创建申请单
        apply.setId(applyId);
        apply.setState(ApplyStatus.Approved.getValue());
        apply.setFlowCcType(CcNoticeType.Flow.getValue());
        apply.setIsChangeApply(true);
        apply.setRootApplyOrderId(applyOrder.getRootApplyOrderId());
        apply.setParentApplyOrderId(applyOrder.getId());
        apply.setUpdateTime(now);
        apply.setCreateTime(now);
        apply.setApplyOrderType(SaasFlowConstant.SETTING_TYPE_CENTER);
        EmployeeContract employee = iBaseOrganizationService.getEmployee(userId, companyId);
        if (employee != null) {
            apply.setApplicantName(employee.getName());
        }
        //费用归属
        if (applyOrderContract.getCost_attribution_category() != null && applyOrderContract.getCost_attribution_category() == SettingType.Project.getValue()) {
            if (StringUtils.isBlank(applyOrderContract.getCost_attribution_name()) || StringUtils.isBlank(applyOrderContract.getCost_attribution_id())) {
                throw new SaasException(GlobalResponseCode.CostAttributionIdIsNull);
            }
            apply.setCostAttributionId(applyOrderContract.getCost_attribution_id());
            apply.setCostAttributionName(applyOrderContract.getCost_attribution_name());
            apply.setCostAttributionCategory(applyOrderContract.getCost_attribution_category());
        } else {
            apply.setCostAttributionCategory(SettingType.Department.getValue());
        }
        //自定义字段
        List<LinkedHashMap> customFields = applyContract.getCustom_fields();
        if (ObjUtils.isNotEmpty(customFields)) {
            apply.setCustomFields(JSONObject.toJSONString(customFields));
        }
        apply.setBillNo(applyV2Service.getBillNo(applyOrder.getType().intValue(),companyId));
        applyMapper.insertSelective(apply);
        //保存行程信息
        insertTripContractList(tripList, applyId, now);
        //保存同行人信息
        insertGuestContractList(guestList, applyId, now);
        //保存审批规则数据
        Integer approveType = queryCompanyApplyType(companyId);
        //2:审批单中带有规则信息
        if (approveType == 2) {
            insertApplyRuleContractList(applyId, applyContract);
        }
        //保存审批单中订单的费用归属信息
        sloveApplyCostAttribution(applyOrderContract.getCost_attribution_list(), companyId, applyId, applyOrderContract.getBring_in());
        //发送消息
        sendKafkaMsg(apply,ApplyStatus.Approved);
        return GlobalResponseCode.Success;
    }

    @Override
    public GlobalResponseCode dinnerCreate(String token, ApplyThirdContract data, String userId, String companyId, String ip, String applyId) {
        data.getApply().setType(ApplyType.Meishi.getValue());
        data.getApply().setId(applyId);
        //检查数据信息
        GlobalResponseCode code = checkApplyData(data, userId, companyId);
        if (code != GlobalResponseCode.Success) {
            throw new SaasException(code);
        }
        ApplyOrderThirdContract thirdContract= data.getApply();
        //加上排重逻辑
        GlobalResponseCode duplicateCode = duplicateThirdApply(companyId, thirdContract.getThird_id(), SaasFlowConstant.SETTING_TYPE_DINNER, ApplyType.Meishi.getValue());
        if (duplicateCode != GlobalResponseCode.Success) {
            return duplicateCode;
        }
        ApplyOrderV2Contract orderV2Contract=new ApplyOrderV2Contract();
        BeanUtils.copyProperties(thirdContract,orderV2Contract);
        //校验事由
//        GlobalResponseCode responseCode = checkApplyReason(companyId, orderV2Contract,ReasonType.APPLY_VIRTUAL_CARD);
//        if (responseCode != GlobalResponseCode.Success) {
//            return responseCode;
//        }
        ApplyOrderThirdContract applyorderContract = data.getApply();
        applyorderContract.setEmployee_id(userId);
        applyorderContract.setCompany_id(companyId);
        //费用归属
        /*Integer costAttributionCategory = thirdContract.getCost_attribution_category();
        String costAttributionId = thirdContract.getCost_attribution_id();
        String costAttributionName = thirdContract.getCost_attribution_name();
        GlobalResponseCode costCode = checkCostAttribution(thirdContract,companyId,userId,costAttributionId,costAttributionCategory,costAttributionName);
        if (costCode != GlobalResponseCode.Success) {
            return costCode;
        }*/
        //整理数据
        clearApplyData(data);
        Date now = new Date();
        List<ApplyTripInfoContract> tripList = data.getTrip_list();
        //预算
        GlobalResponseCode budgetCode =checkBudget(tripList,applyorderContract.getBudget());
        if (budgetCode != GlobalResponseCode.Success) {
            return budgetCode;
        }
        ApplyOrder apply = applyorderContract.ToModel();
        apply.setUpdateTime(now);
        apply.setCreateTime(now);
        apply.setApplyOrderType(SaasFlowConstant.SETTING_TYPE_DINNER);
        apply.setFlowType(CompanyApplyType.OpenApi.getValue());
        //创建申请单
        apply.setId(applyId);
        apply.setState(ApplyStatus.Approved.getValue());
        apply.setFlowCcType(CcNoticeType.Flow.getValue());
        EmployeeContract employee = iBaseOrganizationService.getEmployee(userId, companyId);
        if (employee != null) {
            apply.setApplicantName(employee.getName());
        }
        apply.setBillNo(applyV2Service.getBillNo(apply.getType().intValue(),companyId));
        applyMapper.insertSelective(apply);
        //保存行程信息
        insertTripContractList(tripList, applyId, now);
        //保存审批单中订单的费用归属信息
        sloveApplyCostAttribution(applyorderContract.getCost_attribution_list(), companyId, applyId, applyorderContract.getBring_in());
        //发送消息
        sendKafkaMsg(apply,ApplyStatus.Approved);
        return GlobalResponseCode.Success;

    }

    private GlobalResponseCode checkBudget(List<ApplyTripInfoContract> tripList, Integer budget) {
        if (ObjUtils.isEmpty(tripList)){
            return GlobalResponseCode.ApplyTripIsNull;
        }
        BigDecimal sum = tripList.stream().map(ApplyTripInfoContract::getEstimated_amount).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (sum.compareTo(new BigDecimal(budget))!=0){
            return GlobalResponseCode.ApplyTripBudgetError;
        }
        return GlobalResponseCode.Success;
    }

    private GlobalResponseCode checkChangeApplyData(ApplyThirdContract changeApplyContract, String userId, String companyId) {
        // 查询原单信息
        String parentApplyId = changeApplyContract.getApply_id();
        if (ObjUtils.isBlank(parentApplyId)) {
            return GlobalResponseCode.ParentApplyOrderNotFound;
        }
        ApplyOrder order = getApplyOrderModelByIdAndUserIdAndCompanyId(changeApplyContract, userId, companyId, null);
        if (ObjUtils.isEmpty(order)) {
            return GlobalResponseCode.ParentApplyOrderNotFound;
        }
        // 非差旅行程不支持变更
        if (order.getType() != ApplyType.ChaiLv.getValue()) {
            return GlobalResponseCode.ApplyTypeNotChaiLv;
        }
        // 非已同意状态不支持变更
        if (order.getState() != ApplyStatus.Approved.getValue()) {
            return GlobalResponseCode.ApplyStatusNotApproved;
        }
        // 已过期不支持变更
        if (order.getPastStatus()) {
            return GlobalResponseCode.ApplyStatusPasted;
        }
        // 变更事由信息
        ApplyOrderThirdContract apply = changeApplyContract.getApply();
        // 申请事由不能超过50字
        if (apply.getChange_reason() != null && apply.getChange_reason().length() > 50) {
            return GlobalResponseCode.ApplyChangeReasonInvalid;
        }
        // 事由补充说明不能超过500字
        if (apply.getChange_reason_desc() != null && apply.getChange_reason_desc().length() > 500) {
            return GlobalResponseCode.ApplyChangeReasonDescInvalid;
        }
        //检验差旅和用车的申请事由和事由补充内容和费用归属配置
        ApplySetupContract applySetupContract = messageSetupService.queryCompanyApplyConfig(companyId);
        //审批单费用归属是否必填
        Integer applyAttributionCategoryLimit = null;
        applyAttributionCategoryLimit = applySetupContract.getApply_attribution_category_travel();
        if (applyAttributionCategoryLimit == null || applyAttributionCategoryLimit == ApplyCategoryEnum.IS_SHOW_MUST.getValue()) {
            apply.setCost_attribution_list(Lists.newArrayList());
        }
        if (applyAttributionCategoryLimit != null && applyAttributionCategoryLimit == ApplyCategoryEnum.IS_CHECKED_MUST.getValue()) {
            /*ApplySetupContract orderSetupCostAttributionContract = messageSetupService.queryCompanyCostAttributionConfig(companyId);
            //获取订单费用归属范围
            Integer orderCostAttributionCategory = orderSetupCostAttributionContract.getCost_attribution_category();
            List<CostAttributionInfo> costAttributionList = apply.getCost_attribution_list();
            if (CollectionUtils.isEmpty(costAttributionList)) {
                return GlobalResponseCode.ApplyOrderCostAttritutionIsNull;
            }
            if (orderCostAttributionCategory != null && orderCostAttributionCategory != CostAttributionScopeEnum.DEPT_AND_PROJ.getCode()) {
                if (CollectionUtils.isEmpty(costAttributionList) || costAttributionList.size() != 1) {
                    return GlobalResponseCode.ApplyOrderCostAttritutionIsError;
                }
                if (orderCostAttributionCategory == CostAttributionScopeEnum.DEPT.getCode() && costAttributionList.get(0).getCost_attribution_category() != CostAttributionScopeEnum.DEPT.getCode()) {
                    return GlobalResponseCode.CostAttributionCategoryIsDept;
                }
                if (orderCostAttributionCategory == CostAttributionScopeEnum.PROJ.getCode() && costAttributionList.get(0).getCost_attribution_category() != CostAttributionScopeEnum.PROJ.getCode()) {
                    return GlobalResponseCode.CostAttributionCategoryIsCostCenter;
                }
            }
            if (orderCostAttributionCategory != null && orderCostAttributionCategory == CostAttributionScopeEnum.DEPT_AND_PROJ.getCode() && costAttributionList.size() != 2) {
                return GlobalResponseCode.ApplyOrderCostAttritutionIsError;
            }*/
            GlobalResponseCode code = commonValidService.validCostAttributiongSetting(companyId, apply.getCost_attribution_list());
            if (code != GlobalResponseCode.Success) {
                return code;
            }
            Integer bringIn = 0;
            if (apply.getType() == ApplyType.ChaiLv.getValue()) {
                bringIn = applySetupContract.getApply_attribution_category_modifiable_travel();
            }
            if (apply.getType() == ApplyType.Meishi.getValue()) {
                bringIn = applySetupContract.getApply_attribution_category_modifiable_meishi();
            }
            if (apply.getType() == ApplyType.TakeAway.getValue()) {
                bringIn = applySetupContract.getApply_attribution_category_modifiable_takeaway();
            }
            if (apply.getType() == ApplyType.ApplyTaxi.getValue()) {
                bringIn = applySetupContract.getApply_attribution_category_modifiable_taxi();
            }
            apply.setBring_in(bringIn);
        }
        List<ApplyTripInfoContract> trips = changeApplyContract.getTrip_list();
        if (trips == null || trips.size() == 0) {
            //差旅和用车必须要有行程
            return GlobalResponseCode.ApplyTripIsNull;
        }
        if (trips.size() > 100) {
            //行程数据不超过30个
            return GlobalResponseCode.ApplyThirdTripGreaterHundredIsError;
        }
        //检测地点
        ApplyTripCityType applyTripCityType = ApplyTripCityType.valueOf(applySetupContract.getApply_trip_city());
        for (ApplyTripInfoContract trip : trips) {
            if (applyTripCityType == ApplyTripCityType.Required) {
                if (ObjUtils.isBlank(trip.getStart_city_id())) {
                    return GlobalResponseCode.ApplyTripStartCityInvalid;
                }
                if (!NumberUtils.isDigits(trip.getStart_city_id())) {
                    return GlobalResponseCode.ApplyTripCodeError;
                }
                if (trip.getType() == BizType.Air.getValue() || trip.getType() == BizType.Train.getValue() || trip.getType() == BizType.IntlAir.getValue()) {
                    if (ObjUtils.isBlank(trip.getArrival_city_id())) {
                        return GlobalResponseCode.ApplyTripArrivalCityInvalid;
                    }
                    if (!NumberUtils.isDigits(trip.getArrival_city_id())) {
                        return GlobalResponseCode.ApplyTripCodeError;
                    }
                }
            } else if (applyTripCityType == ApplyTripCityType.Hidden) {
                trip.setStart_city_id(SaasAreaConstant.UNSET_CITY_ID);
                trip.setStart_city_name(SaasAreaConstant.UNSET_CITY_NAME);
                if (trip.getType() == BizType.Air.getValue() || trip.getType() == BizType.Train.getValue() || trip.getType() == BizType.IntlAir.getValue()) {
                    trip.setArrival_city_id(SaasAreaConstant.UNSET_CITY_ID);
                    trip.setArrival_city_name(SaasAreaConstant.UNSET_CITY_NAME);
                }
            } else {
                if (ObjUtils.isBlank(trip.getStart_city_id())) {
                    trip.setStart_city_id(SaasAreaConstant.UNSET_CITY_ID);
                    trip.setStart_city_name(SaasAreaConstant.UNSET_CITY_NAME);
                }
                if (trip.getType() == BizType.Air.getValue() || trip.getType() == BizType.Train.getValue() || trip.getType() == BizType.IntlAir.getValue()) {
                    if (ObjUtils.isBlank(trip.getArrival_city_id())) {
                        trip.setArrival_city_id(SaasAreaConstant.UNSET_CITY_ID);
                        trip.setArrival_city_name(SaasAreaConstant.UNSET_CITY_NAME);
                    }
                }
            }
        }
        return GlobalResponseCode.Success;
    }

    /**
     * 创建第三方额度申请单
     *
     * @param applyContract
     * @return
     */
    @Override
    public GlobalResponseCode virtualCardCreate(String token, ApplyThirdContract applyContract, String userId, String companyId, String ip, String applyId) throws SaasException {
        String bankName = BankNameEnum.XWBANK.getCode();
        if (applyContract != null && applyContract.getApply() != null && StringUtils.isNotBlank(applyContract.getApply().getBank_name())) {
            bankName = applyContract.getApply().getBank_name();
        }
        //虚拟卡校验
        try {
            BankCheckAllInfoReqDTO bankCheckAllInfoReqDTO = new BankCheckAllInfoReqDTO();
            bankCheckAllInfoReqDTO.setBankName(bankName);
            bankCheckAllInfoReqDTO.setCategoryType(OrderCategory.BankIndividual.getKey());
            bankCheckAllInfoReqDTO.setCompanyId(companyId);
            bankCheckAllInfoReqDTO.setEmployeeId(userId);
            bankCheckAllInfoReqDTO.setOperationSrc(BankCheckInfoOperationType.SAAS.getCode());
            iBankHuPoFBTService.checkCardAllInfo(bankCheckAllInfoReqDTO);
        } catch (FinhubException ex) {
            throw new SaasException(ex.getCode(), ex.getMessage(), ex.getType(), ex.getTitle());
        }
        //检查数据信息
        GlobalResponseCode code = checkVirtualApplyData(applyContract, companyId, userId);
        if (code != GlobalResponseCode.Success) {
            return code;
        }
        ApplyOrderThirdContract applyOrderContract = applyContract.getApply();
        //加上排重逻辑
        GlobalResponseCode duplicateCode = duplicateThirdApply(companyId, applyOrderContract.getThird_id(), SaasFlowConstant.SETTING_TYPE_BANK_INDIVIDUAL, ApplyType.BankIndividual.getValue());
        if (duplicateCode != GlobalResponseCode.Success) {
            return duplicateCode;
        }
        applyOrderContract.setEmployee_id(userId);
        applyOrderContract.setCompany_id(companyId);
        Date now = new Date();
        ApplyOrder apply = applyOrderContract.ToModel();
        EmployeeContract employee = iBaseOrganizationService.getEmployee(userId, companyId);
        if (employee != null) {
            apply.setApplicantName(employee.getName());
        }
        apply.setUpdateTime(now);
        apply.setCreateTime(now);
        //创建申请单
        apply.setId(applyId);
        apply.setRootApplyOrderId(applyId);
        //创建申请单
        apply.setId(applyId);
        apply.setState(ApplyStatus.Approved.getValue());
        apply.setFlowCcType(CcNoticeType.Flow.getValue());
        apply.setFlowType(CompanyApplyType.OpenApi.getValue());
        apply.setType(ApplyType.BankIndividual.getValue());
        apply.setApplyOrderType(SaasFlowConstant.SETTING_TYPE_BANK_INDIVIDUAL);
        apply.setBillNo(applyV2Service.getBillNo(apply.getType().intValue(),companyId));
        applyMapper.insertSelective(apply);
        //保存行程信息
        Integer type = BizType.BankIndividual.getValue();
        List<ApplyTripInfoContract> tripList = applyContract.getTrip_list();
        Map<String, Object> costAttributionMap = tripList.get(0).getCost_attribution_name();
        ApplyTripInfo trip = new ApplyTripInfo();
        String id = IDTool.CreateUniqueID();
        trip.setId(id);
        trip.setApplyOrderId(applyId);
        trip.setCreateTime(now);
        trip.setType(type);
        trip.setStartCityId("0");
        trip.setState(ApplyTripStatus.Available.getValue());
        trip.setStartTime(now);
        trip.setUpdateTime(now);
        trip.setCostAttributionName(JSON.toJSONString(costAttributionMap));
        if (StringUtils.isNotBlank(bankName)) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("bank_name", bankName);
            trip.setTripContent(jsonObject.toJSONString());
        }
        applyTripMapper.insert(trip);
        try {
            String costAttributionId = ObjUtils.toString(costAttributionMap.get("id"),"");
            Integer costAttributionType = ObjUtils.toInteger(costAttributionMap.get("category"));
            String costAttributionName = ObjUtils.toString(costAttributionMap.get("name"), "");
            BankApplyCreditReqDTO bankApplyCreditReqDTO = new BankApplyCreditReqDTO();
            bankApplyCreditReqDTO.setEmployeeId(apply.getEmployeeId());
            bankApplyCreditReqDTO.setCompanyId(companyId);
            bankApplyCreditReqDTO.setApplyCreditAmount(BigDecimal.valueOf(apply.getBudget()));
            bankApplyCreditReqDTO.setSaasApplyNo(applyId);
            bankApplyCreditReqDTO.setBankName(bankName);
            bankApplyCreditReqDTO.setCategoryType(OrderCategory.BankIndividual.getKey());
            bankApplyCreditReqDTO.setCostAttributionId(costAttributionId);
            bankApplyCreditReqDTO.setCostAttributionName(costAttributionName);
            bankApplyCreditReqDTO.setCostAttributionType(costAttributionType);
            bankApplyCreditReqDTO.setApplyReason(apply.getApplyReason());
            bankApplyCreditReqDTO.setApplyReasonDesc(apply.getApplyReasonDesc());
            logger.info("终审开卡调用：" + JsonUtils.toJson(bankApplyCreditReqDTO));
            iBankHuPoFBTService.applyCredit(bankApplyCreditReqDTO);
        } catch (FinhubException ex) {
            logger.info(String.format("终审开卡调用校验结果：%s,%s,%s,%s", ex.getCode(), ex.getMessage(), ex.getType(), ex.getTitle()));
            throw new SaasException(ex.getCode(), ex.getMessage(), ex.getType(), ex.getTitle());
        }
        return GlobalResponseCode.Success;
    }

    /**
     * 检查虚拟卡数据有效性
     *
     * @param applyContract
     * @return
     */
    private GlobalResponseCode checkVirtualApplyData(ApplyThirdContract applyContract, String companyId, String userId) {
        //检查申请单
        ApplyOrderThirdContract apply = applyContract.getApply();
        if (apply == null) {
            return GlobalResponseCode.ApplyIsNull;
        }
        if (StringUtils.isNotBlank(apply.getApply_reason_desc()) && apply.getApply_reason_desc().length() > 500) {
            return GlobalResponseCode.ApplyReasonInvalid;
        }
        Integer costAttributionCategory = apply.getCost_attribution_category();
        String costAttributionId = apply.getCost_attribution_id();
        String costAttributionName = apply.getCost_attribution_name();
        if (costAttributionCategory == null || StringUtils.isBlank(costAttributionId) || StringUtils.isBlank(costAttributionName)) {
            return GlobalResponseCode.CostAttributionNameIsNull;
        }
        GlobalResponseCode code = checkCostAttribution(apply, companyId, userId, costAttributionId, costAttributionCategory, costAttributionName);
        if (code != GlobalResponseCode.Success) {
            return code;
        }
        if (StringUtils.isBlank(apply.getThird_id())) {
            return GlobalResponseCode.ApplyVirtualThirdIdIsNull;
        }
        Integer budget = apply.getBudget();
        if (budget == null || budget <= 0) {
            return GlobalResponseCode.ApplyVirtualBudgetIsNull;
        }
        List<ApplyTripInfoContract> tripList = applyContract.getTrip_list();
        if (CollectionUtils.isEmpty(tripList) || tripList.size() != 1) {
            return GlobalResponseCode.ApplyVirtualTripListIsNull;
        }
        return GlobalResponseCode.Success;
    }

    /**
     * 校验事由
     * @param companyId
     * @param apply
     * @return
     */
    private GlobalResponseCode checkApplyReason(String companyId, ApplyOrderV2Contract apply,ReasonType reasonType) {
        //检验申请事由和事由补充内容
        ReasonConfig reasonConfig = customReasonService.queryReasonConfigWithoutItems(companyId, reasonType);
        if (reasonConfig.getReason() == SaasMessageConstant.IS_CHECKED_TRUE) {
            if (StringUtils.isBlank(apply.getApply_reason())) {
                return GlobalResponseCode.ApplyReasonIsNull;
            }
            if (reasonConfig.getReason_desc() == SaasMessageConstant.IS_CHECKED_TRUE) {
                if (StringUtils.isBlank(apply.getApply_reason_desc())) {
                    return GlobalResponseCode.ApplyReasonDescIsNull;
                }
            }
        }
        return GlobalResponseCode.Success;
    }

    /**
     * 校验用户费用归属
     * @param apply
     * @param companyId
     * @param userId
     * @param costAttributionId
     * @param costAttributionCategory
     * @param costAttributionName
     * @return
     */
    private GlobalResponseCode checkCostAttribution(ApplyOrderThirdContract apply, String companyId, String userId, String costAttributionId, Integer costAttributionCategory, String costAttributionName) {
        if (StringUtils.isBlank(userId) || StringUtils.isBlank(companyId) || StringUtils.isBlank(costAttributionId) || costAttributionCategory == null || StringUtils.isBlank(costAttributionName)) {
            return GlobalResponseCode.ParameterIsNull;
        }
        if (costAttributionCategory != SettingType.Department.getValue() && costAttributionCategory != SettingType.Project.getValue()) {
            return GlobalResponseCode.ParameterError;
        }
        EmployeeCostCenter employeeCostCenter = costCenterService.queryEmployeeCostCenterList(companyId, userId);
        if (employeeCostCenter == null) {
            return GlobalResponseCode.ApplyCostAttributionIsNull;
        }
        //部门
        if (costAttributionCategory == SettingType.Department.getValue()) {
            String orgUnitId = employeeCostCenter.getOrgUnitId();
            if (StringUtils.isBlank(orgUnitId) || !orgUnitId.equals(costAttributionId)) {
                return GlobalResponseCode.ApplyCostAttributionDeptIsError;
            }
            if (ObjUtils.isNotBlank(employeeCostCenter.getOrgUnitName())) {
                apply.setCost_attribution_name(employeeCostCenter.getOrgUnitName());
            }
        }
        //项目
        if (costAttributionCategory == SettingType.Project.getValue()) {
            List<CostInfoContract> costCenterList = employeeCostCenter.getCostCenterList();
            if (CollectionUtils.isEmpty(costCenterList)) {
                return GlobalResponseCode.ApplyCostAttributionIsNull;
            }
            List<String> costCenterIdList = costCenterList.stream().map(costInfoContract -> costInfoContract.getId()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(costCenterIdList)) {
                return GlobalResponseCode.ApplyCostAttributionIsNull;
            }
            if (!costCenterIdList.contains(costAttributionId)) {
                return GlobalResponseCode.ApplyCostAttributionCostCenterIsError;
            }
            Map<String, CostInfoContract> costCenterMap = costCenterList.stream().collect(Collectors.toMap(CostInfoContract::getId, costInfoContract -> costInfoContract));
            if (ObjUtils.isNotBlank(costCenterMap.get(costAttributionId).getName())) {
                apply.setCost_attribution_name(costCenterMap.get(costAttributionId).getName());
            }
        }
        return GlobalResponseCode.Success;
    }

    @Override
    public GlobalResponseCode writeoffCreate(String token, ApplyThirdContract applyContract, String userId, String companyId, String ip, String applyId) throws SaasException {
        //检查数据信息
        GlobalResponseCode code = checkVirtualApplyData(applyContract, companyId, userId);
        List<ApplyTripInfoContract> tripList = applyContract.getTrip_list();
        List<String> orderList = Lists.newArrayList();
        BigDecimal tripTotalMoney = BigDecimal.valueOf(0);
        for (ApplyTripInfoContract applyTripInfoContract : tripList) {
            tripTotalMoney = tripTotalMoney.add(applyTripInfoContract.getEstimated_amount());
            List<Map<String, Object>> tradeInformationList = applyTripInfoContract.getTrade_information();
            for (Map<String, Object> tradeInformation : tradeInformationList) {
                //分贝订单号（交易编号）
                String orderId = ObjUtils.toString(tradeInformation.get("orderId"));
                orderList.add(orderId);
                tradeInformationList = tradeInformationList.stream().sorted(Comparator.comparing(tradeInformationMap -> ObjUtils.toLong(tradeInformationMap.get("costCategoryId")))).collect(Collectors.toList());
                applyTripInfoContract.setTrade_information(tradeInformationList);
                List<FinancialManagementVO> financialManagementVOList = Lists.newArrayList();
                FinancialManagementVO financialManagementVO = new FinancialManagementVO();
                financialManagementVO.setId(ObjUtils.toInteger(tradeInformation.get("costCategoryId")));
                financialManagementVO.setName(ObjUtils.toString(tradeInformation.get("costCategory")));
                financialManagementVOList.add(financialManagementVO);
                Integer replaceCostCategoryId = ObjUtils.toInteger(tradeInformation.get("replaceCostCategoryId"));
                String replaceCostCategory = ObjUtils.toString(tradeInformation.get("replaceCostCategory"));
                if (ObjUtils.isNotEmpty(replaceCostCategoryId) && replaceCostCategoryId != 0) {
                    FinancialManagementVO financialManagementVOInfo = new FinancialManagementVO();
                    financialManagementVOInfo.setId(replaceCostCategoryId);
                    financialManagementVOInfo.setName(replaceCostCategory);
                    financialManagementVOList.add(financialManagementVOInfo);
                }
                String userName = null;
                EmployeeContract employee = iBaseOrganizationService.getEmployee(userId, companyId);
                if (employee != null) {
                    userName = employee.getName();
                }
                List<FinancialManagementVO> financialManagementVOS = iMetaService.queryCategory(token, userId, userName, financialManagementVOList);
                if (financialManagementVOList.size() != financialManagementVOS.size()) {
                    return GlobalResponseCode.ApplyWriteoffCostCategoryIsError;
                }
            }
        }
        Integer budget = applyContract.getApply().getBudget();
        if (tripTotalMoney.setScale(2, BigDecimal.ROUND_HALF_UP).compareTo(new BigDecimal(budget)) != 0) {
            return GlobalResponseCode.ApplyTripBudgetError;
        }
        List<String> notAvailableList = iBankOrderService.checkApplyOrder(orderList);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(notAvailableList)) {
            return GlobalResponseCode.ApplyWriteoffIsResubmit;
        }
        if (code != GlobalResponseCode.Success) {
            return code;
        }
        ApplyOrderThirdContract applyOrderContract = applyContract.getApply();
        //加上排重逻辑
        GlobalResponseCode duplicateCode = duplicateThirdApply(companyId, applyOrderContract.getThird_id(), SaasFlowConstant.SETTING_TYPE_VIRTUAL_CARD, ApplyType.VirtualCardWriteOff.getValue());
        if (duplicateCode != GlobalResponseCode.Success) {
            return duplicateCode;
        }
        applyOrderContract.setEmployee_id(userId);
        applyOrderContract.setCompany_id(companyId);
        Date now = new Date();
        ApplyOrder apply = applyOrderContract.ToModel();
        EmployeeContract employee = iBaseOrganizationService.getEmployee(userId, companyId);
        if (employee != null) {
            apply.setApplicantName(employee.getName());
        }
        apply.setUpdateTime(now);
        apply.setCreateTime(now);
        //创建申请单
        apply.setId(applyId);
        apply.setRootApplyOrderId(applyId);
        //创建申请单
        apply.setId(applyId);
        apply.setState(ApplyStatus.Approved.getValue());
        apply.setFlowCcType(CcNoticeType.Flow.getValue());
        apply.setFlowType(CompanyApplyType.OpenApi.getValue());
        apply.setType(ApplyType.VirtualCardWriteOff.getValue());
        apply.setApplyOrderType(SaasFlowConstant.SETTING_TYPE_VIRTUAL_CARD);
        apply.setBillNo(applyV2Service.getBillNo(apply.getType().intValue(),companyId));
        applyMapper.insertSelective(apply);
        //保存行程信息
        insertWriteoffTripContractList(tripList, applyId, now);
        //修改交易订单状态
        logger.info(String.format("同意时修改交易记录的订单为:%s", JSON.toJSON(orderList)));
        iOrderUpdateRpcCallerSaasApi.applyUpdate(orderList, applyId, com.fenbeitong.noc.api.service.constant.enums.ApplyStatus.DONE.getKey());
        ApplyOrder applyOrder = applyMapper.selectByPrimaryKey(applyId);
        Map<String, Object> writeoffMap = applyWriteoffService.sloveWriteoffTemplate(applyOrder);
        //生成pdf
        new Thread(new Runnable() {
            @Override
            public void run() {
                applyWriteoffService.producePdf(applyOrder, writeoffMap);
            }
        }).start();
        return GlobalResponseCode.Success;
    }

    private void insertWriteoffTripContractList(List<ApplyTripInfoContract> tripList, String applyId, Date now) {
            for (ApplyTripInfoContract applyTripInfo : tripList) {
                ApplyTripInfo trip = new ApplyTripInfo();
                String id = IDTool.CreateUniqueID();
                trip.setId(id);
                trip.setApplyOrderId(applyId);
                trip.setCreateTime(now);
                trip.setType(BizType.BankIndividual.getValue());
                trip.setStartCityId("0");
                trip.setState(ApplyTripStatus.Available.getValue());
                trip.setStartTime(now);
                trip.setUpdateTime(now);
                trip.setEstimatedAmount(applyTripInfo.getEstimated_amount());
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("trade_information_list", applyTripInfo.getTrade_information());
                trip.setTripContent(jsonObject.toJSONString());
                applyTripMapper.insert(trip);
            }
    }

    @Override
    public GlobalResponseCode takeawayCreate(String token, ApplyThirdContract data, String userId, String companyId, String ip, String applyId) throws SaasException {
        ApplyOrderThirdContract thirdContract = data.getApply();
        thirdContract.setType(ApplyType.TakeAway.getValue());
        thirdContract.setId(applyId);
        //加上排重逻辑
        GlobalResponseCode duplicateCode = duplicateThirdApply(companyId, thirdContract.getThird_id(), SaasFlowConstant.SETTING_TYPE_TAKEAWAY, ApplyType.TakeAway.getValue());
        if (duplicateCode != GlobalResponseCode.Success) {
            return duplicateCode;
        }
        //校验数据
        GlobalResponseCode code = checkTakeawayApplyData(data, userId, companyId);
        if (code != GlobalResponseCode.Success) {
            throw new SaasException(code);
        }
        thirdContract.setEmployee_id(userId);
        thirdContract.setCompany_id(companyId);
        /*//费用归属
        Integer costAttributionCategory = thirdContract.getCost_attribution_category();
        String costAttributionId = thirdContract.getCost_attribution_id();
        String costAttributionName = thirdContract.getCost_attribution_name();
        //校验 费用归属
        GlobalResponseCode costCode = checkCostAttribution(thirdContract,companyId,userId,costAttributionId,costAttributionCategory,costAttributionName);
        if (costCode != GlobalResponseCode.Success) {
            return costCode;
        }*/
        thirdContract.setFlow_type(CompanyApplyType.OpenApi.getValue());
        //整理数据,修改组装条件
        clearApplyData(data);
        Date now = new Date();
        ApplyOrder apply = thirdContract.ToModel();
        apply.setUpdateTime(now);
        apply.setCreateTime(now);
        apply.setApplyOrderType(SaasFlowConstant.SETTING_TYPE_TAKEAWAY);
        apply.setId(applyId);
        apply.setState(ApplyStatus.Approved.getValue());
        apply.setFlowCcType(CcNoticeType.Flow.getValue());
        apply.setFlowType(CompanyApplyType.OpenApi.getValue());
        apply.setType(ApplyType.TakeAway.getValue());
        EmployeeContract employee = iBaseOrganizationService.getEmployee(userId,companyId);
        if(null != employee){
            apply.setApplicantName(employee.getName());
        }
        apply.setBillNo(applyV2Service.getBillNo(apply.getType().intValue(),companyId));
        applyMapper.insertSelective(apply);
        //保存行程信息
        ApplyTripInfoContract applyTripInfoContract = data.getTrip_list().get(0);
        ApplyTripInfo trip = new ApplyTripInfo();
        String id = IDTool.CreateUniqueID();
        trip.setId(id);
        trip.setApplyOrderId(applyId);
        trip.setCreateTime(now);
        trip.setType(BizType.Takeaway.getValue());
        trip.setStartTime(DateTimeTool.fromStringToDate(applyTripInfoContract.getStart_time(), DateTimeFormat.forPattern("yyyy-MM-dd HH:mm")));
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd KK:mm");
        try {
            trip.setEndTime(sdf.parse(applyTripInfoContract.getEnd_time()));
        } catch (ParseException e) {
            e.printStackTrace();
            logger.error("创建外卖审批结束时间格式不正确:{}", applyTripInfoContract.getEnd_time());
        }
        trip.setStartCityId(applyTripInfoContract.getStart_city_id());
        trip.setState(ApplyTripStatus.Available.getValue());
        trip.setUpdateTime(now);
        trip.setEstimatedAmount(applyTripInfoContract.getEstimated_amount().divide(new BigDecimal("100").setScale(2, BigDecimal.ROUND_HALF_UP)));
        trip.setContent(applyTripInfoContract.getAddress_name());
        JSONObject jo = new JSONObject();
        jo.put("address_id", applyTripInfoContract.getAddress_id());
        jo.put("address_name", applyTripInfoContract.getAddress_name());
        jo.put("company_address_id", applyTripInfoContract.getCompany_address_id());
        jo.put("address_lat", applyTripInfoContract.getAddress_lat());
        jo.put("address_lng", applyTripInfoContract.getAddress_lng());
        jo.put("address_tag", getTakeawayAddressTag(userId, companyId, applyTripInfoContract.getCompany_address_id()));
        trip.setTripContent(JsonUtils.toJson(jo));
        if (applyTripInfoContract.getCost_attribution_name() != null && applyTripInfoContract.getCost_attribution_name().size() > 0) {
            trip.setCostAttributionName(JSON.toJSONString(applyTripInfoContract.getCost_attribution_name()));
        }
        applyTripMapper.insertSelective(trip);
        //保存审批单中订单的费用归属信息
        sloveApplyCostAttribution(thirdContract.getCost_attribution_list(), companyId, applyId, thirdContract.getBring_in());
        //发送消息
        sendKafkaMsg(apply,ApplyStatus.Approved);
        return GlobalResponseCode.Success;
    }

    private Integer getTakeawayAddressTag(String employeeId, String companyId, String companyAddressId) {
        // 1合规地址 2非外卖规则地址 默认2
        Integer addressTag = 2;
        // 无外卖权限 显示非外卖规则地址
        TakeawayAuthContract takeawayAuthContract = iBaseTakeawayRuleExtService.myTakeawayRule(employeeId, companyId);
        if (ObjUtils.isNull(takeawayAuthContract)
                || takeawayAuthContract.getTakeaway_rule() != TakeawayRuleType.Allowed.getCode()) {
            return addressTag;
        }
        // 不限制规则 显示合规地址
        if (!takeawayAuthContract.getRule_limit_flag()) {
            addressTag = 1;
            return addressTag;
        }
        TakeawayContract takeawayRule = takeawayAuthContract.getRule_info();
        // 限制但无规则信息 显示非外卖规则地址
        if (takeawayRule == null) {
            return addressTag;
        }
        // 不限制地址 显示合规地址
        if (!takeawayRule.getLimitLocation() || ObjUtils.isEmpty(takeawayRule.getTakeawayLocationIds())) {
            addressTag = 1;
            return addressTag;
        }
        // 限制地址并在规则地址中 显示合规地址
        List<String> takeawayLocationIds = takeawayRule.getTakeawayLocationIds();
        if (takeawayLocationIds.contains(companyAddressId)) {
            addressTag = 1;
            return addressTag;
        }
        return addressTag;
    }

    private GlobalResponseCode checkTakeawayApplyData(ApplyThirdContract applyContract,String userId, String companyId){

        //检查申请单
        ApplyOrderThirdContract apply = applyContract.getApply();
        if(null== applyContract || null==apply){
            return GlobalResponseCode.ApplyIsNull;
        }
        if(apply.getType() == null || apply.getType() != ApplyType.TakeAway.getValue()){
            return GlobalResponseCode.ApplyTypeInvalid;
        }

        if (apply.getBudget() != null && apply.getBudget() < 0) {
            return GlobalResponseCode.ApplyBudgetInvalid;
        }

        if (apply.getApply_reason_desc() != null && apply.getApply_reason_desc().length() > 500) {
            //事由补充说明不能超过500字
            return GlobalResponseCode.ApplyReasonDescInvalid;
        }

        List<ApplyTripInfoContract> trips = applyContract.getTrip_list();
        if (CollectionUtils.isEmpty(trips)) {
            return GlobalResponseCode.ApplyTakeawayTripIsNull;
        }
        if (trips.size() > 1) {
            return GlobalResponseCode.ApplyTripTooMuch;
        }
        BigDecimal tripTotalMoney = BigDecimal.valueOf(0);
        ApplyTripInfoContract trip = trips.get(0);
        BigDecimal estimatedAmount = trip.getEstimated_amount();
        if (ObjUtils.isBlank(estimatedAmount) || estimatedAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return GlobalResponseCode.EstimatedAmountIsNull;
        }
        tripTotalMoney = tripTotalMoney.add(estimatedAmount);

        if (trip.getType() == null) {
            return GlobalResponseCode.ApplyTripTypeInvalid;
        }
        //检测行程单类型
        if (trip.getType() != BizType.Takeaway.getValue()) {
            return GlobalResponseCode.ApplyTripNotTakeaway;
        }
        Date startTime;
        SimpleDateFormat dsdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        //检测时间
        if (trip.getStart_time() == null) {
            return GlobalResponseCode.ApplyTripStartDateInvalid;
        } else {
            try {
                startTime = dsdf.parse(trip.getStart_time());
            } catch (Exception ex) {
                return GlobalResponseCode.ApplyTripStartDateInvalid;
            }
        }
        if (trip.getEnd_time() == null) {
            return GlobalResponseCode.ApplyTripEndDateInvalid;
        } else {
            Date endTime;
            try {
                endTime = dsdf.parse(trip.getEnd_time());
            } catch (Exception ex) {
                return GlobalResponseCode.ApplyTripEndDateInvalid;
            }
            if (startTime.getTime() > endTime.getTime()) {
                return GlobalResponseCode.ApplyTripStartDateCannotBiggerThanEndDate;
            }
        }
        //检测地点
        if (trip.getStart_city_id() == null || trip.getStart_city_id().length() == 0) {
            return GlobalResponseCode.ApplyTripStartCityInvalid;
        }
        if (!NumberUtils.isDigits(trip.getStart_city_id())) {
            return GlobalResponseCode.ApplyTripCodeError;
        }
        //送餐地址
        if (ObjUtils.isEmpty(trip.getAddress_id()) || ObjUtils.isEmpty(trip.getAddress_name())) {
            return GlobalResponseCode.ApplyTakeawayAddressInfoIsNull;
        }
        //送餐地址坐标
        if (ObjUtils.isEmpty(trip.getAddress_lat()) || ObjUtils.isEmpty(trip.getAddress_lng())) {
            return GlobalResponseCode.ApplyTakeawayAddressInfoError;
        }
        //校验apply.budget ==estimatedAmount
        BigDecimal b=new BigDecimal(String.valueOf(apply.getBudget()));
        if (tripTotalMoney.compareTo(b) != 0) {
            return GlobalResponseCode.ApplyTripBudgetError;
        }
        ApplySetupContract applySetupContract = messageSetupService.queryCompanyApplyConfig(companyId);
        //审批单费用归属是否必填
        Integer applyAttributionCategoryLimit = null;
        applyAttributionCategoryLimit = applySetupContract.getApply_attribution_category_takeaway();
        if (applyAttributionCategoryLimit == null || applyAttributionCategoryLimit == ApplyCategoryEnum.IS_SHOW_MUST.getValue()) {
            apply.setCost_attribution_list(Lists.newArrayList());
        }
        if (applyAttributionCategoryLimit != null && applyAttributionCategoryLimit == ApplyCategoryEnum.IS_CHECKED_MUST.getValue()) {
            /*ApplySetupContract orderSetupCostAttributionContract = messageSetupService.queryCompanyCostAttributionConfig(companyId);
            //获取订单费用归属范围
            Integer orderCostAttributionCategory = orderSetupCostAttributionContract.getCost_attribution_category();
            List<CostAttributionInfo> costAttributionList = apply.getCost_attribution_list();
            if (CollectionUtils.isEmpty(costAttributionList)) {
                return GlobalResponseCode.ApplyOrderCostAttritutionIsNull;
            }
            if (orderCostAttributionCategory != null && orderCostAttributionCategory != CostAttributionScopeEnum.DEPT_AND_PROJ.getCode()) {
                if (CollectionUtils.isEmpty(costAttributionList) || costAttributionList.size() != 1) {
                    return GlobalResponseCode.ApplyOrderCostAttritutionIsError;
                }
                if (orderCostAttributionCategory == CostAttributionScopeEnum.DEPT.getCode() && costAttributionList.get(0).getCost_attribution_category() != CostAttributionScopeEnum.DEPT.getCode()) {
                    return GlobalResponseCode.CostAttributionCategoryIsDept;
                }
                if (orderCostAttributionCategory == CostAttributionScopeEnum.PROJ.getCode() && costAttributionList.get(0).getCost_attribution_category() != CostAttributionScopeEnum.PROJ.getCode()) {
                    return GlobalResponseCode.CostAttributionCategoryIsCostCenter;
                }
            }
            if (orderCostAttributionCategory != null && orderCostAttributionCategory == CostAttributionScopeEnum.DEPT_AND_PROJ.getCode() && costAttributionList.size() != 2) {
                return GlobalResponseCode.ApplyOrderCostAttritutionIsError;
            }*/
            GlobalResponseCode code = commonValidService.validCostAttributiongSetting(companyId, apply.getCost_attribution_list());
            if (code != GlobalResponseCode.Success) {
                return code;
            }
            Integer bringIn = 0;
            if (apply.getType() == ApplyType.ChaiLv.getValue()) {
                bringIn = applySetupContract.getApply_attribution_category_modifiable_travel();
            }
            if (apply.getType() == ApplyType.Meishi.getValue()) {
                bringIn = applySetupContract.getApply_attribution_category_modifiable_meishi();
            }
            if (apply.getType() == ApplyType.TakeAway.getValue()) {
                bringIn = applySetupContract.getApply_attribution_category_modifiable_takeaway();
            }
            if (apply.getType() == ApplyType.ApplyTaxi.getValue()) {
                bringIn = applySetupContract.getApply_attribution_category_modifiable_taxi();
            }
            apply.setBring_in(bringIn);
        }
        return GlobalResponseCode.Success;
    }

    /**
     * 是否对接公司
     *
     * @param companyId
     * @return
     */
    @Override
    public Map<String, Object> queryCompanyDockingInfo(String companyId) {
        String url = URL_GET_COMPANY_ISDOCKING + companyId;
        String data;
        try {
            data = HttpClientUtils.get(url);
        } catch (Exception e) {
            logger.error("查询是否对接公司接口异常:", e);
            return null;
        }
        logger.info("查询是否对接公司接口返回结果:" + data);
        if (ObjUtils.isBlank(data)) {
            return null;
        }
        Map<String, Object> resultData = JSONObject.parseObject(data);
        if (ObjUtils.isEmpty(resultData)) {
            return null;
        }
        Integer resultCode = ObjUtils.toInteger(resultData.get("code"));
        if (resultCode == null || resultCode != 0) {
            return null;
        }
        return (Map<String, Object>) resultData.get("data");
    }

    /**
     * 是否是对接订单审批公司
     *
     * @param companyId
     * @return
     */
    @Override
    public boolean isApplyOrderDockingCompany(String companyId) {
        Map<String, Object> dataMap = queryCompanyDockingInfo(companyId);
        return ObjUtils.toBoolean(MapUtils.getBoolean(dataMap, "company_apply_order"), false);
    }

    /**
     * 是否是对接预算校验公司
     *
     * @param companyId
     * @return
     */
    @Override
    public boolean isBudgetDockingCompany(String companyId) {
        Map<String, Object> dataMap = queryCompanyDockingInfo(companyId);
        return ObjUtils.toBoolean(MapUtils.getBoolean(dataMap, "company_use_budget"), false);
    }

    /**
     * 创建订单审批
     *
     * @param token
     * @param applyContract
     * @param userId
     * @param companyId
     * @param ip
     * @param applyId
     * @param clientVersion
     * @return
     */
    @Override
    public GlobalResponseCode centerCreate(String token, ApplyCenterContract applyContract, String userId, String companyId, String ip, String applyId, String clientVersion) {
        //检查数据信息
        GlobalResponseCode code = checkCenterApplyData(applyContract, companyId, clientVersion);
        if (code != GlobalResponseCode.Success) {
            return code;
        }
        ApplyOrderV2Contract applyOrderContract = applyContract.getApply();
        applyOrderContract.setEmployee_id(userId);
        applyOrderContract.setCompany_id(companyId);
        CompanyApplyType companyApplyType = CompanyApplyType.valueOf(applyOrderContract.getFlow_type());
        if (companyApplyType == CompanyApplyType.Unknown) {
            companyApplyType = CompanyApplyType.Elastic;
        }
        applyOrderContract.setFlow_type(companyApplyType.getValue());
        //处理超规类型
        Integer exceedBuyType = null;
        if (ExceedBuyType.valueOf(applyOrderContract.getExceed_buy_type()) == ExceedBuyType.Unknown) {
            exceedBuyType = ExceedBuyType.Supernormal.getValue();
        } else {
            exceedBuyType = applyOrderContract.getExceed_buy_type();
        }
        //整理数据
        clearCenterApplyData(applyContract);
        //获取联系人数据
        List<UserContactContract> guestList = applyContract.getGuest_list();
        if (!org.apache.commons.collections.CollectionUtils.isEmpty(guestList)) {
            for (UserContactContract guest : guestList) {
                guest.setDesc(JSONObject.toJSONString(guest));
            }
        }
        //待审核装填
        Date now = new Date();
        List<ApplyTripInfoContract> tripList = applyContract.getTrip_list();
        Integer tripType = tripList.get(0).getType();
        ApplyOrder apply = applyOrderContract.ToModel();
        if (apply.getType() == ApplyType.ChaiLv.getValue()) {
            if (tripType == BizType.IntlAir.getValue()) {
                apply.setType(ApplyType.IntlAir.getValue());
            } else if (tripType == BizType.Air.getValue()) {
                apply.setType(ApplyType.Air.getValue());
            } else if (tripType == BizType.Hotel.getValue()) {
                apply.setType(ApplyType.Hotel.getValue());
            } else if (tripType == BizType.Train.getValue()) {
                apply.setType(ApplyType.Train.getValue());
            }
        }
        //处理超规类型
        apply.setExceedBuyType(exceedBuyType);
        if (applyOrderContract.getCost_attribution_category() != null) {
            if (StringUtils.isBlank(applyOrderContract.getCost_attribution_name()) || StringUtils.isBlank(applyOrderContract.getCost_attribution_id())) {
                return GlobalResponseCode.CostAttributionIdIsNull;
            }
            apply.setCostAttributionId(applyOrderContract.getCost_attribution_id());
            apply.setCostAttributionName(applyOrderContract.getCost_attribution_name());
            apply.setCostAttributionCategory(applyOrderContract.getCost_attribution_category());
        }
        EmployeeContract employee = iBaseOrganizationService.getEmployee(userId, companyId);
        if (employee != null) {
            apply.setApplicantName(employee.getName());
            applyOrderContract.setApplicant_name(employee.getName());
        }
        apply.setUpdateTime(now);
        apply.setCreateTime(now);

        //创建申请单
        apply.setId(applyId);
        apply.setRootApplyOrderId(applyId);
        applyOrderContract.setId(applyId);
        if (apply.getExceedBuyType() == ExceedBuyType.UnSupernormal.getValue()) {
            apply.setExceedBuyDesc("");
        }
        apply.setBillNo(applyV2Service.getBillNo(apply.getType().intValue(),companyId));
        applyMapper.insertSelective(apply);
        //保存行程信息
        applyCenterService.insertTripContractList(tripList, applyId, now, applyOrderContract.getCost_id());
        //保存同行人信息
        applyCenterService.insertGuestContractList(guestList, applyId, now);
        // 同步三方创建订单审批
        syncThirdCenterCreate(applyContract, companyId, userId, now);
        return GlobalResponseCode.Success;
    }

    /**
     * 同步三方创建订单审批
     *
     * @param applyContract
     * @param companyId
     * @param userId
     * @param now
     */
    private void syncThirdCenterCreate(ApplyCenterContract applyContract, String companyId, String userId, Date now) {
        ApplyOrderV2Contract requestApply = applyContract.getApply();
        String applyId = requestApply.getId();
        Integer type = requestApply.getType();

        ApplyV2Contract createThirdParam = new ApplyV2Contract();
        // 审批信息
        ApplyOrderV2Contract apply = new ApplyOrderV2Contract();
        apply.setId(requestApply.getId());
        apply.setApply_order_type(requestApply.getApply_order_type());
        apply.setApply_reason_id(requestApply.getApply_reason_id());
        apply.setApply_reason(requestApply.getApply_reason());
        apply.setApply_reason_desc(requestApply.getApply_reason_desc());
        apply.setBudget(requestApply.getBudget().divide(new BigDecimal(100)));
        apply.setCompany_id(companyId);
        apply.setCreate_time(DateUtils.format(now));
        apply.setEmployee_id(userId);
        apply.setApplicant_name(requestApply.getApplicant_name());
        apply.setState(requestApply.getState());
        apply.setType(requestApply.getType());
        apply.setCost_attribution_category(requestApply.getCost_attribution_category());
        apply.setCost_attribution_id(requestApply.getCost_attribution_id());
        apply.setCost_attribution_name(requestApply.getCost_attribution_name());
        apply.setExceed_buy_type(requestApply.getExceed_buy_type());
        apply.setExceed_buy_desc(requestApply.getExceed_buy_desc());
        apply.setExceed_buy_desc_content(requestApply.getExceed_buy_desc_content());
        apply.setExceed_buy_desc_list(requestApply.getExceed_buy_desc_list());
        createThirdParam.setApply(apply);

        // 出行人信息
        List<ApplyTripInfoContract> applyTripInfoContractList = applyContract.getTrip_list();
        List<UserContactContract> guestList = applyCenterService.composeGuestList(applyId, companyId, type, applyTripInfoContractList, null);
        createThirdParam.setGuest_list(guestList);

        // 订单数据信息
        ApplyTripInfoContract trip = applyContract.getTrip_list().get(0);
        String orderId = trip.getOrder_id();
        Integer orderType = trip.getType();
        createThirdParam.setOrder_info(applyCenterService.disposeOrderInfo(orderId, orderType, userId));
        try {
            String data = HttpClientUtils.postBody(URL_POST_APPLY_ORDER_CREATE, JSON.toJSONString(createThirdParam));
            logger.info("同步三方创建订单审批返回结果:" + data);
        } catch (Exception e) {
            logger.error("同步三方创建订单审批异常:", e);
            throw new SaasException(FinhubMessageCode.EXCEPTION, CoreLanguage.Common_Exception_InternelError.getMessage());
        }
    }

    /**
     * 检查数据有效性
     *
     * @param applyCenterContract
     * @param companyId
     * @param clientVersion
     * @return
     */
    private GlobalResponseCode checkCenterApplyData(ApplyCenterContract applyCenterContract, String companyId, String clientVersion) {
        //检查申请单
        ApplyOrderV2Contract apply = applyCenterContract.getApply();
        if (apply == null) {
            return GlobalResponseCode.ApplyIsNull;
        }
        if (apply.getState() == null) {
            return GlobalResponseCode.ParameterError;
        }
        if (apply.getState().intValue() != ApplyStatus.PendingAudit.getValue()) {
            return GlobalResponseCode.ParameterError;
        }
        if (apply.getType() == null
                || ApplyType.valueOf(apply.getType()) == ApplyType.Unknown) {
            return GlobalResponseCode.ApplyTypeInvalid;
        }
        // 费用归属校验
        if (apply.getCost_attribution_category() != null) {
            if (StringUtils.isBlank(apply.getCost_attribution_name()) || StringUtils.isBlank(apply.getCost_attribution_id())) {
                return GlobalResponseCode.CostAttributionIdIsNull;
            }
        }
        //预计总金额
        if (apply.getBudget() != null && apply.getBudget().compareTo(BigDecimal.valueOf(0)) == -1) {
            return GlobalResponseCode.ApplyBudgetInvalid;
        }
        if (apply.getOvertime() == null || DateTimeTool.fromStringToDateTime(apply.getOvertime()) == null) {
            return GlobalResponseCode.OverTimeInvalidError;
        }
        if (ExceedBuyType.valueOf(apply.getExceed_buy_type()) == ExceedBuyType.Unknown) {
            return GlobalResponseCode.ExceedBuyTypeIsNull;
        }
        if (ExceedBuyType.valueOf(apply.getExceed_buy_type()) == ExceedBuyType.Supernormal) {
            // 版本校验
            if (VersionTool.greaterThanOrEqualTo(clientVersion, "3.8.0")) {
                ReasonType reasonType = null;
                if (ApplyType.valueOf(apply.getType()) == ApplyType.Air) {
                    reasonType = ReasonType.APPLY_EXCEED_ORDER_AIR;
                } else if (ApplyType.valueOf(apply.getType()) == ApplyType.IntlAir) {
                    reasonType = ReasonType.APPLY_EXCEED_ORDER_INTL_AIR;
                } else if (ApplyType.valueOf(apply.getType()) == ApplyType.Hotel) {
                    reasonType = ReasonType.APPLY_EXCEED_ORDER_HOTEL;
                } else if (ApplyType.valueOf(apply.getType()) == ApplyType.Train) {
                    reasonType = ReasonType.APPLY_EXCEED_ORDER_TRAIN;
                }
                ReasonConfig reasonConfig = customReasonService.queryReasonConfigWithoutItems(companyId, reasonType);
                if (reasonConfig.getReason() == SaasMessageConstant.IS_CHECKED_TRUE) {
                    if (StringUtils.isBlank(apply.getApply_reason())) {
                        return GlobalResponseCode.ApplyReasonIsNull;
                    }
                    if (reasonConfig.getReason_desc() == SaasMessageConstant.IS_CHECKED_TRUE) {
                        if (StringUtils.isBlank(apply.getApply_reason_desc())) {
                            return GlobalResponseCode.ApplyReasonDescIsNull;
                        }
                    }
                }
            }
            if (StringUtils.isBlank(apply.getExceed_buy_desc())) {
                return GlobalResponseCode.ExceedBuyDescIsNull;
            }
        }
        if (StringUtils.isBlank(apply.getApply_reason_desc()) && apply.getApply_reason() != null && apply.getApply_reason().length() > 200) {
            //申请事由不能超过50字
            return GlobalResponseCode.ApplyReasonInvalid;
        }
        if (StringUtils.isNotBlank(apply.getApply_reason_desc()) && apply.getApply_reason_desc().length() > 200) {
            return GlobalResponseCode.ApplyReasonInvalid;
        }
        //检查行程
        List<ApplyTripInfoContract> trips = applyCenterContract.getTrip_list();
        if (ObjUtils.isEmpty(trips)) {
            //差旅和用车必须要有行程
            return GlobalResponseCode.ApplyTripIsNull;
        }
        if (trips.size() > 30) {
            //行程数据不超过30个
            return GlobalResponseCode.ApplyTripTooMuch;
        }
        BigDecimal tripTotalMoney = BigDecimal.valueOf(0);
        for (ApplyTripInfoContract trip : trips) {
            tripTotalMoney = tripTotalMoney.add(trip.getEstimated_amount());
            if (trip.getType() == null) {
                return GlobalResponseCode.ApplyTripTypeInvalid;
            }
            //检测行程单类型
            if (apply.getType() == ApplyType.ChaiLv.getValue()) {
                if (trip.getType() != BizType.Air.getValue()
                        && trip.getType() != BizType.Hotel.getValue()
                        && trip.getType() != BizType.Train.getValue()
                        && trip.getType() != BizType.IntlAir.getValue()) {
                    return GlobalResponseCode.ApplyTripNotTravel;
                }
            } else if (apply.getType() == ApplyType.Taxi.getValue()) {
                if (trip.getType() != BizType.Taxi.getValue()) {
                    return GlobalResponseCode.ApplyTripNotTaxi;
                }
            }
            //检测时间
            if (trip.getStart_time() == null) {
                return GlobalResponseCode.ApplyTripStartDateInvalid;
            }
            //检测地点
            if (trip.getStart_city_name() == null || trip.getStart_city_name().length() == 0) {
                return GlobalResponseCode.ApplyTripStartCityInvalid;
            }
            if (trip.getArrival_city_name() == null || trip.getArrival_city_name().length() == 0) {
                if (trip.getType() == BizType.Air.getValue() || trip.getType() == BizType.Train.getValue()) {
                    return GlobalResponseCode.ApplyTripArrivalCityInvalid;
                }
            }
            if (TripType.valueOf(trip.getTrip_type()) == TripType.Unknown) {
                trip.setTrip_type(TripType.oneWay.getValue());
            }
            if (trip.getType() == BizType.Air.getValue() && StringUtils.isBlank(trip.getEnd_time())) {
                return GlobalResponseCode.AirInvalidError;
            }
            if (trip.getType() == BizType.IntlAir.getValue()) {
                if (StringUtils.isBlank(trip.getAir_airline_name()) || StringUtils.isBlank(trip.getAir_flight_no()) || StringUtils.isBlank(trip.getAir_seat_msg())) {
                    return GlobalResponseCode.IntlAirInvalidError;
                }
                if (StringUtils.isBlank(trip.getEnd_time())) {
                    return GlobalResponseCode.IntlAirInvalidError;
                }
                if (trip.getTrip_type() == TripType.goAndBack.getValue()) {
                    if (StringUtils.isBlank(trip.getBack_start_time()) || StringUtils.isBlank(trip.getBack_end_time())) {
                        return GlobalResponseCode.IntlAirInvalidError;
                    }
                }
            }
        }
        BigDecimal budget = apply.getBudget().divide(ObjUtils.toBigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        if (tripTotalMoney.setScale(2, BigDecimal.ROUND_HALF_UP).compareTo(budget) != 0) {
            return GlobalResponseCode.ApplyTripBudgetError;
        }
        /*CostInfo costInfo = apply.getCost_info();
        if (costInfo == null) {
            throw new SaasException(GlobalResponseCode.PaymentOrderCostAttributionIsError);
        }
        CostCategory costCategory = costInfo.getCostCategory();
        MessageSetup messageSetup = null;
        String itemCode = null;
        if (ApplyType.valueOf(apply.getType()) == ApplyType.Air) {
            itemCode = "cost_air";
        } else if (ApplyType.valueOf(apply.getType()) == ApplyType.IntlAir) {
            itemCode = "cost_inter_air";
        } else if (ApplyType.valueOf(apply.getType()) == ApplyType.Hotel) {
            itemCode = "cost_hotel";
        } else if (ApplyType.valueOf(apply.getType()) == ApplyType.Train) {
            itemCode = "cost_train";
        }
        messageSetup = messageSetupService.queryCompanyMessageSetupWithDefault(companyId, itemCode);
        if (messageSetup != null && messageSetup.getIsChecked() == 1) {
            if (costCategory == null || StringUtils.isBlank(costCategory.getId()) || StringUtils.isBlank(costCategory.getName())) {
                throw new SaasException(GlobalResponseCode.CostCategoryIsNull);
            }
        }
        List<CostAttributionGroup> costAttributionGroupList = costInfo.getCostAttributionGroupList();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(costAttributionGroupList) || costAttributionGroupList.size() > 2) {
            throw new SaasException(GlobalResponseCode.PaymentOrderCostAttributionIsError);
        }
        for (CostAttributionGroup costAttributionGroup : costAttributionGroupList) {
            Integer category = costAttributionGroup.getCategory();
            if (category == null || CostAttributionCategory.getByKey(category) == null) {
                throw new SaasException(GlobalResponseCode.PaymentOrderCostAttributionCategoryIsError);
            }
            List<CostAttribution> costAttributionList = costAttributionGroup.getCostAttributionList();
            for (CostAttribution costAttribution : costAttributionList) {
                String id = costAttribution.getId();
                String name = costAttribution.getName();
                BigDecimal weight = costAttribution.getWeight();
                if (StringUtils.isBlank(id) || StringUtils.isBlank(name)) {
                    throw new SaasException(GlobalResponseCode.PaymentOrderCostAttributionCategoryIsError);
                }
                if (weight == null) {
                    costAttribution.setWeight(BigDecimal.valueOf(100));
                }
            }
        }*/
        return GlobalResponseCode.Success;
    }

    /**
     * 整理订单审批数据
     *
     * @param applyContract
     */
    private void clearCenterApplyData(ApplyCenterContract applyContract) {
        ApplyOrderV2Contract applyorderContract = applyContract.getApply();
        if (applyorderContract.getId() == null || applyorderContract.getId().length() == 0) {
            applyorderContract.setCheck_reason(null);
        }
        if (applyorderContract.getBudget() == null) {
            applyorderContract.setBudget(BigDecimal.valueOf(0));
        }
        if (ObjUtils.isEmpty(applyContract.getTrip_list())) {
            applyContract.setTrip_list(null);
        }
        if (ObjUtils.isEmpty(applyContract.getGuest_list())) {
            applyContract.setGuest_list(null);
        }

        List<Tuple<Date, String>> cityIds = new ArrayList<>();
        List<Date> timeRange = new ArrayList<>();

        for (ApplyTripInfoContract trip : applyContract.getTrip_list()) {
            Date startTime = DateTimeTool.fromStringToDateTime(trip.getStart_time());
            //这里把时间从String转到Date,再转回String,是需要用Date格式式字段串的显示,如输入为2017-2-27,格式化后变为2017-02-27
            if (startTime != null) {
                trip.setStart_time(DateTimeTool.fromDateTimeToString(startTime));
                timeRange.add(startTime);
            }
            Date endTime = DateTimeTool.fromStringToDateTime(trip.getEnd_time());
            if (endTime != null) {
                trip.setEnd_time(DateTimeTool.fromDateTimeToString(endTime));
                timeRange.add(endTime);
            }
            if (trip.getType() == BizType.Hotel.getValue()) {
                trip.setArrival_city_id(null);
            }
            String tripCityId = trip.getStart_city_id();
            if (trip.getArrival_city_id() != null) {
                tripCityId += "-" + trip.getArrival_city_id();
            }
            cityIds.add(new Tuple<>(startTime, tripCityId));
        }
        //city_range格式:1000001-200002,2000002,300003-400004 意即:北京-上海,上海,天津-重庆
        cityIds.sort((m, n) -> (int) (m.getItem1().getTime() - n.getItem1().getTime()));
        List<String> cityIdArr = new ArrayList<>();
        for (Tuple<Date, String> cityId : cityIds) {
            cityIdArr.add(cityId.getItem2());
        }
        String cityIdVal = String.join(",", cityIdArr);
        applyorderContract.setCity_range(cityIdVal);

        //将所有出现过的时间正序排列,取最小值和最大值,重新写入timeRange字段,如果最小值=最大值,则只取最小值
        timeRange.sort((m, n) -> (int) (m.getTime() - n.getTime()));
        Date minTime = timeRange.get(0);
        Date maxTime = timeRange.get(timeRange.size() - 1);
        String desTimeRange = DateTimeTool.fromDateToString(minTime);
        if (maxTime.getTime() != minTime.getTime()) {
            desTimeRange += "," + DateTimeTool.fromDateToString(maxTime);
        }
        applyorderContract.setTime_range(desTimeRange);
        if (applyorderContract.getApprover_id() == null) {
            applyorderContract.setApprover_id("");
        }
    }

    /**
     * 订单审批终审同意
     *
     * @param approveModel
     * @param userId
     * @param companyId
     * @param ip
     * @return
     */
    @Override
    public SaasResponseEntity centerApprove(ThirdApplyApproveContract approveModel, String userId, String companyId, String ip) {
        if (approveModel == null) {
            throw new SaasException(GlobalResponseCode.ApplyIsNull);
        }
        if (StringTool.isNullOrEmpty(approveModel.getApply_id())) {
            throw new SaasException(GlobalResponseCode.ApplyIdInvalid);
        }
        if (StringTool.isNullOrEmpty(approveModel.getThird_id())) {
            throw new SaasException(GlobalResponseCode.ApplyVirtualThirdIdIsNull);
        }
        if (approveModel.getComment() != null && approveModel.getComment().length() > 200) {
            throw new SaasException(GlobalResponseCode.ApplyCheckReasonInvalid);
        }
        String applyId = approveModel.getApply_id();
        ApplyOrder apply = applyMapper.selectByPrimaryKey(applyId);
        if (apply == null) {
            throw new SaasException(GlobalResponseCode.NotFound);
        }
        //加上排重逻辑
        GlobalResponseCode duplicateCode = duplicateThirdApply(companyId, approveModel.getThird_id(), apply.getApplyOrderType(), apply.getType());
        if (duplicateCode != GlobalResponseCode.Success) {
            return new SaasResponseEntity(duplicateCode, null);
        }
        if (apply.getState() != null && apply.getState().intValue() == ApplyStatus.Overtime.getValue()) {
            //事中审批已超时
            throw new SaasException(GlobalResponseCode.CenterApplyOverTime);
        }
        if (apply.getState() != null && apply.getState().intValue() == ApplyStatus.Backout.getValue()) {
            //事中审批已撤销
            throw new SaasException(GlobalResponseCode.CenterApplyCancleOrder);
        }
        if (!apply.getCompanyId().equals(companyId)) {
            //申请单和当前人不在一个公司
            throw new SaasException(GlobalResponseCode.ApplyCompanyCannotMatch);
        }
        if (apply.getState() != ApplyStatus.PendingAudit.getValue()) {
            //该单不是待审核,不能审批
            throw new SaasException(GlobalResponseCode.ApplyStatusNotPendingAudit);
        }
        // 更新状态及三方审批id
        ApplyOrder applyOrder = new ApplyOrder();
        applyOrder.setId(applyId);
        applyOrder.setThirdId(approveModel.getThird_id());
        applyOrder.setRealPrice(approveModel.getPrice());
        applyOrder.setState(ApplyStatus.Approved.getValue());
        applyOrder.setUpdateTime(new Date());
        applyMapper.updateByPrimaryKeySelective(applyOrder);
        //终审时审批价格
        BigDecimal price = approveModel.getPrice().setScale(2, BigDecimal.ROUND_HALF_UP);
        //占用金额
        BigDecimal occupyPrice = new BigDecimal(apply.getBudget()).divide(ObjUtils.toBigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        List<ApplyTripInfo> tripList = applyTripInfoExtMapper.selectListByApplyOrderId(applyId);
        String tripContent = tripList.get(0).getTripContent();
        JSONObject jo = JsonUtils.toObj(tripContent, JSONObject.class);
        String costId = jo.getString("costId");
        String orderId = jo.getString("orderId");
        //终审金额大于占用金额 修改占用预算
        if (price.compareTo(occupyPrice) == 1 && StringUtils.isNotBlank(costId)) {
            //修改预算占用
            OrderCostInfoReq costInfo = new OrderCostInfoReq();
            costInfo.setAmount(price);
            costInfo.setOrderId(orderId);
            costInfo.setApplyId(applyId);
            costInfo.setForce(false);
            costInfo.setUserId(userId);
            costInfo.setCompanyId(companyId);
            costInfo.setCategory(tripList.get(0).getType());
            costInfo.setCostId(costId);
            CostSaveResult costSaveResult = orderCostService.updateCost(costInfo);
            //状态 1成功 0失败
            if (costSaveResult == null || costSaveResult.getStatus() == 0) {
                throw new SaasException(GlobalResponseCode.BudgetUpdateIsError.getCode(), costSaveResult.getMessage(), 0, null);
            }
        }
        // 调用订单支付接口
        if (Objects.equals(apply.getType(), ApplyType.Air.getValue())) {
            List<ApplyTripApplicateExt> applyTripApplicates = applyTripApplicateExtMapper.queryApplyTripApplicateList(applyId);
            ApplyTripInfo applyTripInfo = applyTripMapper.selectByPrimaryKey(applyTripApplicates.get(0).getApplyTripId());
            String requestId = LogUtil.getRequestId();
            // 单程
            if (Objects.equals(applyTripInfo.getTripType(), TripType.oneWay.getValue())) {
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        applyCenterService.payAirOrder(applyTripApplicates.get(0).getOrderId(), approveModel.getPrice(), applyTripInfo.getEstimatedAmount(), 2, null, approveModel.getSeat_item());
                    }
                }).start();
            }
            // 往返
            else {
                ThreadPoolUtils.getExecutorService().execute(() -> applyCenterService.payRoundTripAirOrder(requestId,
                        applyTripApplicates.get(0).getOrderId(), approveModel.getPrice(), approveModel.getBack_price(),
                        approveModel.getSeat_item(), approveModel.getBack_seat_item()));
            }
        } else {
            SaasResponseEntity saasResponseEntity = applyCenterService.payOrder(applyId, approveModel.getPrice());
            if (saasResponseEntity.getCode() != GlobalResponseCode.Success.getCode()) {
                throw new SaasException(saasResponseEntity.getCode(), saasResponseEntity.getMsg(), 0, saasResponseEntity.getData());
            }
        }
        //发送消息
        sendKafkaMsg(apply,ApplyStatus.Approved);
        return new SaasResponseEntity(GlobalResponseCode.Success, null);
    }

    /**
     * 查询审批单自定义字段及第三方审批id
     *
     * @param companyId
     * @param applyId
     * @return
     */
    @Override
    public ApplyThirdCustomFieldsContract queryCustomFields(String companyId, String applyId) {
        if (ObjUtils.isBlank(applyId)) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
        if (ObjUtils.isBlank(companyId)) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
        String finalApplyId = iCustomApplyService.queryFinalApply(applyId);
        if (org.springframework.util.StringUtils.isEmpty(finalApplyId)){
            throw new SaasException(GlobalResponseCode.NotFound);
        }
        ApplyOrder applyOrder = applyMapper.selectByPrimaryKey(finalApplyId);
        if (ObjUtils.isEmpty(applyOrder)) {
            throw new SaasException(GlobalResponseCode.NotFound);
        }
        if (!companyId.equals(applyOrder.getCompanyId())) {
            throw new SaasException(GlobalResponseCode.NotFound);
        }
        ApplyThirdCustomFieldsContract applyThirdCustomFieldsContract = new ApplyThirdCustomFieldsContract();
        // 三方审批id
        applyThirdCustomFieldsContract.setThird_apply_id(applyOrder.getThirdId());
        // 自定义字段
        List<ApplyThirdContract.KeyValueItem> customFieldMappings = applyService.getCustomFields(companyId);
        if (ObjUtils.isEmpty(customFieldMappings)) {
            return applyThirdCustomFieldsContract;
        }
        String customFieldsJson = applyOrder.getCustomFields();
        if (ObjUtils.isBlank(customFieldsJson)) {
            return applyThirdCustomFieldsContract;
        }
        List<ApplyThirdContract.KeyValueItem> keyValueItemList = JSON.parseArray(customFieldsJson, ApplyThirdContract.KeyValueItem.class);
        Map<String, String> keyValueItemMap = Maps.newHashMap();
        if (ObjUtils.isNotEmpty(keyValueItemList)) {
            for (ApplyThirdContract.KeyValueItem keyValueItem : keyValueItemList) {
                if (ObjUtils.isNotEmpty(keyValueItem)) {
                    keyValueItemMap.put(keyValueItem.getType(), ObjUtils.toString(keyValueItem.getValue()));
                }
            }
        }
        List<ApplyThirdContract.KeyValueItem> customFields = Lists.newArrayList();
        for (ApplyThirdContract.KeyValueItem customFieldMapping : customFieldMappings) {
            ApplyThirdContract.KeyValueItem customField = new ApplyThirdContract.KeyValueItem();
            customField.setType(customFieldMapping.getValue().toString());
            customField.setValue(ObjUtils.toString(keyValueItemMap.get(customFieldMapping.getType()), ""));
            customFields.add(customField);
        }
        applyThirdCustomFieldsContract.setCustom_fields(customFields);
        return applyThirdCustomFieldsContract;
    }

    /**
     * 是否是对接采购审批公司
     *
     * @param companyId
     * @return
     */
    @Override
    public boolean isApplyMallOrderDockingCompany(String companyId) {
        Map<String, Object> dataMap = queryCompanyDockingInfo(companyId);
        return ObjUtils.toBoolean(MapUtils.getBoolean(dataMap, "company_apply_mall"), false);
    }

    @Override
    public GlobalResponseCode mallApplyCreate(String token, ApplyV2Contract applyContract, String userId, String companyId, String ip, String applyId, String clientVersion) {
        GlobalResponseCode code = checkMallApplyData(applyContract, userId, companyId, clientVersion);
        if (code != GlobalResponseCode.Success) {
            throw new SaasException(code);
        }
        ApplyOrderV2Contract applyorderContract = applyContract.getApply();
        String mallOrderId = null;
        com.fenbeitong.saas.core.contract.apply.CostInfoContract costInfo = applyorderContract.getCost_info();
        //采购审批自动下单开关默认关闭
        Integer automaticOrder = SaasMessageConstant.IS_CHECKED_FALSE;
        if (applyorderContract.getType() == ApplyType.Mall.getValue() && VersionTool.greaterThanOrEqualTo(clientVersion, "4.6.0")) {
            //获取采购是否下单标识
            //0：审批通过后人工下单，1：审批通过后自动下单
            ApplyMallConfigDTO applyMallConfig = customReasonAttrService.getApplyMallConfig(companyId);
            automaticOrder = applyMallConfig == null ? SaasMessageConstant.IS_CHECKED_FALSE : applyMallConfig.getAutomaticOrder();
            //采购下单
            if (automaticOrder == SaasMessageConstant.IS_CHECKED_TRUE) {
                ApplyTripInfoContract applyTripInfoContract = applyContract.getTrip_list().get(0);
                Map<String, Object> tripOrderInfo = applyTripInfoContract.getTrip_order_info();
                tripOrderInfo.put("client_version", clientVersion);
                mallOrderId = mallOrderSubmint(tripOrderInfo, token);
                applyTripInfoContract.setOrder_id(mallOrderId);
            }
        }
        applyorderContract.setEmployee_id(userId);
        applyorderContract.setCompany_id(companyId);
        CompanyApplyType companyApplyType = CompanyApplyType.valueOf(applyorderContract.getFlow_type());
        if (companyApplyType == CompanyApplyType.Unknown) {
            companyApplyType = CompanyApplyType.Elastic;
        }
        applyorderContract.setFlow_type(companyApplyType.getValue());
        Integer settingType = SaasFlowConstant.SETTING_TYPE_ORDER;;
        //默认非变更单
        String rootApplyId = applyId;
        String parentApplyId = null;
        ApplyStatus applyState = applyorderContract.getState() == ApplyStatus.PendingAudit.getValue()
                ? ApplyStatus.PendingAudit : ApplyStatus.Draft;
        Date now = new Date();
        List<ApplyTripInfoContract> tripList = applyContract.getTrip_list();
        ApplyOrder apply = applyorderContract.ToModel();
        if (apply.getType() == ApplyType.Mall.getValue() && StringUtils.isBlank(apply.getApplyReason()) && StringUtils.isNotBlank(apply.getApplyReasonDesc())) {
            apply.setApplyReason(apply.getApplyReasonDesc());
        }
        //处理超规类型
        if (ExceedBuyType.valueOf(apply.getExceedBuyType()) == ExceedBuyType.Unknown) {
            apply.setExceedBuyType(ExceedBuyType.UnSupernormal.getValue());
        }
        if (applyorderContract.getCost_attribution_category() != null && applyorderContract.getCost_attribution_category() == SettingType.Project.getValue()) {
            if (StringUtils.isBlank(applyorderContract.getCost_attribution_name()) || StringUtils.isBlank(applyorderContract.getCost_attribution_id())) {
                throw new SaasException(GlobalResponseCode.CostAttributionIdIsNull);
            }
            apply.setCostAttributionId(applyorderContract.getCost_attribution_id());
            apply.setCostAttributionName(applyorderContract.getCost_attribution_name());
            apply.setCostAttributionCategory(applyorderContract.getCost_attribution_category());
        } else {
            apply.setCostAttributionCategory(SettingType.Department.getValue());
        }
        if (ObjUtils.isNotEmpty(costInfo)) {
            JSONObject jo = new JSONObject();
            jo.put("cost_info", costInfo);
            apply.setSnapContent(jo.toJSONString());
        }
        apply.setApplyOrderType(settingType);
        apply.setUpdateTime(now);
        apply.setCreateTime(now);
        EmployeeContract employee = iBaseOrganizationService.getEmployee(userId, companyId);
        if (employee != null) {
            apply.setApplicantName(employee.getName());
            applyorderContract.setApplicant_name(employee.getName());
            applyorderContract.setId(applyId);
        }
        //创建申请单
        apply.setId(applyId);
        apply.setRootApplyOrderId(rootApplyId);
        apply.setParentApplyOrderId(parentApplyId);
        apply.setIsChangeApply(false);
        apply.setChangeReason(applyorderContract.getChange_reason());
        apply.setChangeReasonDesc(applyorderContract.getChange_reason_desc());
        applyorderContract.setId(applyId);
        apply.setBillNo(applyV2Service.getBillNo(apply.getType().intValue(),companyId));
        applyMapper.insertSelective(apply);
        //保存行程信息
        insertMallTripContractList(tripList, applyId, now, automaticOrder);
        //判断审批单是否是待审核状态
        if (applyState == ApplyStatus.PendingAudit) {
            //保存审批单中订单的费用归属信息
            sloveApplyCostAttribution(applyorderContract.getCost_attribution_list(), companyId, applyId, applyorderContract.getBring_in());
        }
        try {
            String data = HttpClientUtils.postBody(URL_POST_APPLY_MALL_ORDER_CREATE, JSON.toJSONString(applyContract));
            logger.info("同步三方创建采购审批返回结果:" + data);
        } catch (Exception e) {
            logger.error("同步三方创建采购审批异常:", e);
            throw new SaasException(FinhubMessageCode.EXCEPTION, CoreLanguage.Common_Exception_InternelError.getMessage());
        }
        return GlobalResponseCode.Success;
    }

    /**
     * 检查数据有效性
     *
     * @param applyContract
     * @return
     */
    private GlobalResponseCode checkMallApplyData(ApplyV2Contract applyContract, String employeeId, String companyId, String clientVersion) {
        //检查申请单
        ApplyOrderV2Contract apply = applyContract.getApply();
        if (apply.getIs_self_travel() == null) {
            apply.setIs_self_travel(false);
        }
        if (apply.getTravel_day() == null) {
            apply.setTravel_day(BigDecimal.valueOf(0));
        }
        if (apply == null) {
            return GlobalResponseCode.ApplyIsNull;
        }
        if (apply.getType() == null || apply.getType() != ApplyType.Mall.getValue()) {
            return GlobalResponseCode.ApplyTypeInvalid;
        }
        if (apply.getType() == ApplyType.Taxi.getValue()) {
            throw new SaasException(GlobalResponseCode.ApplyCenterAlert);
        }
        String applyId = apply.getId();
        if (applyId != null && applyId.length() > 0 && applyId.length() != 24) {
            return GlobalResponseCode.ApplyIdInvalid;
        }
        Integer state = apply.getState();
        if (state != ApplyStatus.Draft.getValue()) {
            if (apply.getFlow_cc_type() == null || CcNoticeType.valueOf(apply.getFlow_cc_type()) == CcNoticeType.Unknown) {
                return GlobalResponseCode.CCNoticeTypeInvalid;
            }
            ApplySetupContract applySetupCostAttributionContract = messageSetupService.queryCompanyApplyCostAttributionConfig(companyId);
            Integer costAttributionCategory = applySetupCostAttributionContract.getCost_attribution_category();
            Integer applyCostAttributionCategory = apply.getCost_attribution_category();
            if (applyCostAttributionCategory == null) {
                applyCostAttributionCategory = CostAttributionCategory.Dept.getKey();
            }
            if (costAttributionCategory == CostAttributionCategory.Dept.getKey() && applyCostAttributionCategory != CostAttributionCategory.Dept.getKey()) {
                return GlobalResponseCode.CostAttributionCategoryIsDept;
            }
            if (costAttributionCategory == CostAttributionCategory.CostCenter.getKey() && applyCostAttributionCategory != CostAttributionCategory.CostCenter.getKey()) {
                return GlobalResponseCode.CostAttributionCategoryIsCostCenter;
            }
        }
        //检验差旅出差天数
        List<ApplyTravelTimeContract> travelTimeList = apply.getTravel_time_list();
        ApplySetupContract applySetupTravelContract = messageSetupService.queryCompanyApplyTravelConfig(companyId);
        if (apply.getType() == ApplyType.ChaiLv.getValue() && applySetupTravelContract != null && applySetupTravelContract.getWhether_travel_statistics() == SaasMessageConstant.IS_CHECKED_TRUE && applySetupTravelContract.getWhether_required() == SaasMessageConstant.IS_CHECKED_TRUE) {
            if (CollectionUtils.isEmpty(travelTimeList)) {
                return GlobalResponseCode.ApplyTravelDayIsNull;
            }
        }
        apply.setTravel_day(BigDecimal.ZERO);
        apply.setTravel_time_list(null);
        if (apply.getApply_reason() != null && apply.getApply_reason().length() > 100) {
            //采购申请事由不能超过100字
            return GlobalResponseCode.ApplyMallReasonInvalid;
        }
        if (apply.getApply_reason_desc() != null && apply.getApply_reason_desc().length() > 500) {
            //事由补充说明不能超过500字
            return GlobalResponseCode.ApplyReasonDescInvalid;
        }
        if (apply.getBudget() != null && apply.getBudget().compareTo(BigDecimal.valueOf(0)) == -1) {
            return GlobalResponseCode.ApplyBudgetInvalid;
        }
        if (apply.getTravel_price_detail() != null && apply.getTravel_price_detail().length() > 200) {
            //事由补充说明不能超过200字
            return GlobalResponseCode.ApplyTranvelPriceDetailInvalid;
        }
        if (apply.getState() == null) {
            return GlobalResponseCode.ApplyStatusRequired;
        }
        CompanyApplyType applyType = CompanyApplyType.valueOf(apply.getFlow_type());
        if (applyType.getValue() == CompanyApplyType.Unknown.getValue()) {
            return GlobalResponseCode.ApplyFlowTypeError;
        }
        if (applyType.getValue() == CompanyApplyType.Elastic.getValue() && apply.getState() == ApplyStatus.PendingAudit.getValue() && (apply.getApprover_id() == null || apply.getApprover_id().length() == 0)) {
            //申请单必须指定一个审批人
            return GlobalResponseCode.ApplyApproveIdInvalid;
        }
        if (applyType.getValue() == CompanyApplyType.Elastic.getValue() && employeeId.equals(apply.getApprover_id())) {
            //审批人不能是自己
            return GlobalResponseCode.ApplyApproverCannotBeSelf;
        }
        //检查行程
        if (apply.getType() == ApplyType.Mall.getValue()) {
            List<ApplyTripInfoContract> trips = applyContract.getTrip_list();
            if (CollectionUtils.isEmpty(trips)) {
                return GlobalResponseCode.ApplyMallTripIsNull;
            }
            String orderReasonDesc = trips.get(0).getOrder_reason_desc();
            if (StringUtils.isNotBlank(orderReasonDesc) && orderReasonDesc.length() > 200) {
                return GlobalResponseCode.ApplyOrderReasonDescInvalid;
            }
            BigDecimal mallTotalMoney = BigDecimal.valueOf(0);
            for (ApplyTripInfoContract trip : trips) {
                mallTotalMoney = mallTotalMoney.add(trip.getEstimated_amount().setScale(2, BigDecimal.ROUND_HALF_UP));
                if (VersionTool.greaterThanOrEqualTo(clientVersion, "4.2.0")) {
                    List<CostAttributionInfo> costAttributionList = apply.getCost_attribution_list();
                    if (CollectionUtils.isEmpty(costAttributionList) && ObjUtils.isEmpty(apply.getCost_info())) {
                        return GlobalResponseCode.ApplyOrderCostAttritutionIsNull;
                    }
                    Map<String, Object> costAttributionNameMap = Maps.newHashMap();
                    if (CollectionUtils.isNotEmpty(costAttributionList)) {
                        costAttributionNameMap.put("id", costAttributionList.get(0).getCost_attribution_id());
                        costAttributionNameMap.put("category", costAttributionList.get(0).getCost_attribution_category());
                        costAttributionNameMap.put("name", costAttributionList.get(0).getCost_attribution_name());
                    }
                    if (ObjUtils.isNotEmpty(apply.getCost_info())) {
                        com.fenbeitong.saas.core.contract.apply.CostInfoContract costInfo = apply.getCost_info();
                        List<com.fenbeitong.saas.core.contract.apply.CostInfoContract.CostAttributionGroup> costAttributionGroupList = costInfo.getCost_attribution_group_list();
                        if (CollectionUtils.isNotEmpty(costAttributionGroupList)) {
                            Integer category = costAttributionGroupList.get(0).getCategory();
                            String id = costAttributionGroupList.get(0).getCost_attribution_list().get(0).getId();
                            String name = costAttributionGroupList.get(0).getCost_attribution_list().get(0).getName();
                            costAttributionNameMap.put("id", id);
                            costAttributionNameMap.put("category", category);
                            costAttributionNameMap.put("name", name);
                        }
                    }
                }
            }
            BigDecimal budget = apply.getBudget().divide(ObjUtils.toBigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
            if (mallTotalMoney.compareTo(budget) != 0) {
                return GlobalResponseCode.ApplyTripBudgetError;
            }
        }
        return GlobalResponseCode.Success;
    }

    @Override
    public SaasResponseEntity mallApprove(ThirdApplyApproveContract approveModel, String userId, String companyId, String ip) {
        if (approveModel == null) {
            throw new SaasException(GlobalResponseCode.ApplyIsNull);
        }
        String applyId = approveModel.getApply_id();
        if (StringTool.isNullOrEmpty(applyId)) {
            throw new SaasException(GlobalResponseCode.ApplyIdInvalid);
        }
        if (StringTool.isNullOrEmpty(approveModel.getThird_id())) {
            throw new SaasException(GlobalResponseCode.ApplyVirtualThirdIdIsNull);
        }
        if (approveModel.getComment() != null && approveModel.getComment().length() > 200) {
            throw new SaasException(GlobalResponseCode.ApplyCheckReasonInvalid);
        }
        ApplyOrder apply = applyMapper.selectByPrimaryKey(applyId);
        if (apply == null) {
            throw new SaasException(GlobalResponseCode.NotFound);
        }
        //加上排重逻辑
        GlobalResponseCode duplicateCode = duplicateThirdApply(companyId, approveModel.getThird_id(), apply.getApplyOrderType(), apply.getType());
        if (duplicateCode != GlobalResponseCode.Success) {
            return new SaasResponseEntity(duplicateCode, null);
        }
        if (apply.getState() != null && apply.getState().intValue() == ApplyStatus.Overtime.getValue()) {
            //事中审批已超时
            throw new SaasException(GlobalResponseCode.CenterApplyOverTime);
        }
        if (apply.getState() != null && apply.getState().intValue() == ApplyStatus.Backout.getValue()) {
            //事中审批已撤销
            throw new SaasException(GlobalResponseCode.CenterApplyCancleOrder);
        }
        if (!apply.getCompanyId().equals(companyId)) {
            //申请单和当前人不在一个公司
            throw new SaasException(GlobalResponseCode.ApplyCompanyCannotMatch);
        }
        if (apply.getState() != ApplyStatus.PendingAudit.getValue()) {
            //该单不是待审核,不能审批
            throw new SaasException(GlobalResponseCode.ApplyStatusNotPendingAudit);
        }
        Integer automaticOrder = SaasMessageConstant.IS_CHECKED_FALSE;
        if (apply.getType().intValue() == ApplyType.Mall.getValue()) {
            List<ApplyTripInfo> applyTripInfoList = applyTripInfoExtMapper.queryAllTripListByApplyOrderId(applyId);
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(applyTripInfoList)) {
                ApplyTripInfo tripInfo = applyTripInfoList.get(0);
                JSONObject jsonObject = JSON.parseObject(tripInfo.getContent());
                if (jsonObject != null) {
                    automaticOrder = ObjUtils.toInteger(jsonObject.get("automatic_order"));
                    automaticOrder = automaticOrder == null ? SaasMessageConstant.IS_CHECKED_FALSE : automaticOrder;
                }
            }
        }
        // 更新状态及三方审批id
        ApplyOrder applyOrder = new ApplyOrder();
        applyOrder.setId(applyId);
        applyOrder.setThirdId(approveModel.getThird_id());
        applyOrder.setRealPrice(approveModel.getPrice());
        if (automaticOrder == SaasMessageConstant.IS_CHECKED_TRUE) {
            applyOrder.setState(ApplyStatus.Done.getValue());
        } else {
            applyOrder.setState(ApplyStatus.Approved.getValue());
        }
        applyOrder.setUpdateTime(new Date());
        applyMapper.updateByPrimaryKeySelective(applyOrder);
        if (automaticOrder == SaasMessageConstant.IS_CHECKED_TRUE) {
            //采购终审同意自动下单
            sloveMallOrder(applyId);
        }
        return new SaasResponseEntity(GlobalResponseCode.Success, null);
    }

    /**
     * 保存审批单中订单的费用归属
     *
     * @param costAttributionList
     * @param companyId
     * @param applyId
     */
    private void sloveApplyCostAttribution(List<CostAttributionInfo> costAttributionList, String companyId, String applyId, Integer bringIn) {
        if (CollectionUtils.isEmpty(costAttributionList)) {
            return;
        }
        if (bringIn == null) {
            bringIn = 0;
        }
        List<ApplyCostAttribution> applyCostAttributionList = Lists.newArrayList();
        for (CostAttributionInfo costAttributionInfo : costAttributionList) {
            ApplyCostAttribution applyCostAttribution = new ApplyCostAttribution();
            applyCostAttribution.setApplyId(applyId);
            applyCostAttribution.setCompanyId(companyId);
            applyCostAttribution.setCostAttributionId(costAttributionInfo.getCost_attribution_id());
            applyCostAttribution.setCostAttributionName(costAttributionInfo.getCost_attribution_name());
            applyCostAttribution.setCostAttributionCategory(costAttributionInfo.getCost_attribution_category());
            applyCostAttribution.setBringIn(bringIn);
            applyCostAttributionList.add(applyCostAttribution);
        }
        logger.info("sloveApplyCostAttribution.applyCostAttributionList={}", JsonUtils.toJson(applyCostAttributionList));
        applyCostAttributionExtMapper.batchInsertCostAttribution(applyCostAttributionList);
    }

    /**
     * 对接公司预算校验
     *
     * @param req
     * @return
     */
    @Override
    public ThirdBudgetCheckResult budgetCheck(ThirdBudgetCheckReqContract req) {
        String data;
        try {
            data = HttpClientUtils.postBody(URL_POST_BUDGET_CHECK, JSONObject.toJSONString(req));
        } catch (Exception e) {
            logger.error("对接公司预算校验接口异常:", e);
            return null;
        }
        logger.info("对接公司预算校验返回结果:" + data);
        if (ObjUtils.isBlank(data)) {
            return null;
        }
        JSONObject resultData = JSONObject.parseObject(data);
        if (ObjUtils.isEmpty(resultData)) {
            return null;
        }
        Integer resultCode = ObjUtils.toInteger(resultData.get("code"));
        if (resultCode == null || resultCode != 0) {
            return null;
        }
        return resultData.getObject("data", ThirdBudgetCheckResult.class);
    }

    /**
     * 对接行程审批
     * @param token
     * @param applyContract
     * @param userId
     * @param companyId
     * @param ip
     * @param applyId
     * @param clientVersion
     * @return
     */
    @Override
    public GlobalResponseCode travelApplyCreate(String token, ApplyV2Contract applyContract, String userId, String companyId, String ip, String applyId, String clientVersion) {
        GlobalResponseCode code = checkTravelApplyData(applyContract, userId, companyId, clientVersion);
        if (code != GlobalResponseCode.Success) {
            throw new SaasException(code);
        }
        ApplyOrder applyOrder = applyMapper.selectByPrimaryKey(applyId);
        if (applyOrder != null) {
            //行程
            applyTripInfoExtMapper.deleteByApplyOrderId(applyId);
            //出差时间
            applyTravelTimeExtMapper.deleteByApplyOrderId(applyId);
            //出行人
            applyGuestExtMapper.deleteByApplyOrderId(applyId);
        }
        ApplyOrderV2Contract applyorderContract = applyContract.getApply();
        applyorderContract.setEmployee_id(userId);
        applyorderContract.setCompany_id(companyId);
        CompanyApplyType companyApplyType = CompanyApplyType.valueOf(applyorderContract.getFlow_type());
        if (companyApplyType == CompanyApplyType.Unknown) {
            companyApplyType = CompanyApplyType.Elastic;
        }
        applyorderContract.setFlow_type(companyApplyType.getValue());
        Integer settingType = SaasFlowConstant.SETTING_TYPE_CENTER;;
        //默认非变更单
        String rootApplyId = applyId;
        String parentApplyId = null;
        Date now = new Date();
        ApplyOrder apply = applyorderContract.ToModel();
        //处理超规类型
        if (ExceedBuyType.valueOf(apply.getExceedBuyType()) == ExceedBuyType.Unknown) {
            apply.setExceedBuyType(ExceedBuyType.UnSupernormal.getValue());
        }
        if (applyorderContract.getCost_attribution_category() != null && applyorderContract.getCost_attribution_category() == SettingType.Project.getValue()) {
            if (StringUtils.isBlank(applyorderContract.getCost_attribution_name()) || StringUtils.isBlank(applyorderContract.getCost_attribution_id())) {
                throw new SaasException(GlobalResponseCode.CostAttributionIdIsNull);
            }
            apply.setCostAttributionId(applyorderContract.getCost_attribution_id());
            apply.setCostAttributionName(applyorderContract.getCost_attribution_name());
            apply.setCostAttributionCategory(applyorderContract.getCost_attribution_category());
        } else {
            apply.setCostAttributionCategory(SettingType.Department.getValue());
        }
        apply.setApplyOrderType(settingType);
        apply.setUpdateTime(now);
        apply.setCreateTime(now);
        EmployeeContract employee = iBaseOrganizationService.getEmployee(userId, companyId);
        if (employee != null) {
            apply.setApplicantName(employee.getName());
            applyorderContract.setApplicant_name(employee.getName());
            applyorderContract.setId(applyId);
        }
        //创建申请单
        apply.setId(applyId);
        apply.setRootApplyOrderId(rootApplyId);
        apply.setParentApplyOrderId(parentApplyId);
        apply.setIsChangeApply(false);
        apply.setChangeReason(applyorderContract.getChange_reason());
        apply.setChangeReasonDesc(applyorderContract.getChange_reason_desc());
        applyorderContract.setId(applyId);
        if (applyOrder != null) {
            boolean success = applyMapper.updateByPrimaryKeySelective(apply) > 0;
            if (!success) {
                logger.error("未正常更新apply_order id:" + apply.getId());
            }
        } else {
            apply.setBillNo(applyV2Service.getBillNo(apply.getType().intValue(),companyId));
            applyMapper.insertSelective(apply);
        }
        //保存行程信息
        insertTravelTripContract(applyContract.getTrip_list(), applyId, now);
        //获取联系人数据
        List<UserContactContract> guestList = applyContract.getGuest_list();
        if (CollectionUtils.isNotEmpty(guestList)) {
            if (StringUtils.isNotBlank(guestList.get(0).getName())) {
                for (UserContactContract guest : guestList) {
                    guest.setDesc(JSONObject.toJSONString(guest));
                }
            } else {
                List<String> guestIds = new ArrayList<>();
                for (UserContactContract contact : guestList) {
                    guestIds.add(contact.getId());
                }
                List<UserContactContract> contactInfos = userService.getContacts(token, guestIds);
                if (contactInfos == null) {
                    throw new SaasException(GlobalResponseCode.ApplyContactNotFound);
                }
                for (UserContactContract guest : guestList) {
                    UserContactContract guestInfo = null;
                    for (UserContactContract tmpInfo : contactInfos) {
                        if (tmpInfo.getId().equals(guest.getId())) {
                            guestInfo = tmpInfo;
                            break;
                        }
                    }
                    if (guestInfo == null) {
                        throw new SaasException(GlobalResponseCode.ApplyContactNotFound);
                    }
                    //用desc字段存储序列化后的联系人值
                    guest.setDesc(JSONObject.toJSONString(guestInfo));
                }
            }
        }
        //保存同行人信息
        insertGuestContractList(guestList, applyId, now);
        //保存差旅时间信息
        insertTravelTimeList(applyorderContract.getTravel_time_list(), applyId, now, userId, companyId);
        //保存审批单中订单的费用归属信息
        sloveApplyCostAttribution(applyorderContract, companyId, applyId, applyorderContract.getBring_in());
        try {
            logger.info("同步三方行程审批请求参数：{}", JsonUtils.toJson(applyContract));
            String data = HttpClientUtils.postBody(URL_POST_APPLY_TRAVEL_ORDER_CREATE, JSON.toJSONString(applyContract));
            logger.info("同步三方创建行程审批返回结果:" + data);
        } catch (Exception e) {
            logger.error("同步三方创建行程审批异常:", e);
            throw new SaasException(FinhubMessageCode.EXCEPTION, CoreLanguage.Common_Exception_InternelError.getMessage());
        }
        return GlobalResponseCode.Success;
    }

    /**
     * 保存出差记录
     *
     * @param applyTravelTimeList
     * @param applyId
     * @param now
     * @param userId
     * @param companyId
     */
    private void insertTravelTimeList(List<ApplyTravelTimeContract> applyTravelTimeList, String applyId, Date now, String userId, String companyId) {
        if (CollectionUtils.isNotEmpty(applyTravelTimeList)) {
            for (ApplyTravelTimeContract applyTravelTimeInfo : applyTravelTimeList) {
                ApplyTravelTime applyTravelTime = new ApplyTravelTime();
                applyTravelTime.setApplyOrderId(applyId);
                applyTravelTime.setCompanyId(companyId);
                applyTravelTime.setUserId(userId);
                applyTravelTime.setTravelType(applyTravelTimeInfo.getTravel_type());
                applyTravelTime.setTravelTime(ObjUtils.toLong(applyTravelTimeInfo.getTravel_time()));
                applyTravelTime.setCreateTime(now);
                applyTravelTimeMapper.insert(applyTravelTime);
            }
        }
    }

    /**
     * 检查数据有效性
     *
     * @param applyContract
     * @return
     */
    private GlobalResponseCode checkTravelApplyData(ApplyV2Contract applyContract, String employeeId, String companyId, String clientVersion) {
        //检查申请单
        ApplyOrderV2Contract apply = applyContract.getApply();
        if (apply.getIs_self_travel() == null) {
            apply.setIs_self_travel(false);
        }
        if (apply.getTravel_day() == null) {
            apply.setTravel_day(BigDecimal.valueOf(0));
        }
        if (apply == null) {
            return GlobalResponseCode.ApplyIsNull;
        }
        if (apply.getType() == null || apply.getType() != ApplyType.ChaiLv.getValue()) {
            return GlobalResponseCode.ApplyTypeInvalid;
        }
        String applyId = apply.getId();
        if (applyId != null && applyId.length() > 0 && applyId.length() != 24) {
            return GlobalResponseCode.ApplyIdInvalid;
        }
        //检验差旅的申请事由和事由补充内容
        ReasonConfig reasonConfig = customReasonService.queryReasonConfigWithoutItems(companyId, ReasonType.APPLY_CHAILV);
        if (reasonConfig.getReason() == SaasMessageConstant.IS_CHECKED_TRUE) {
            if (StringUtils.isBlank(apply.getApply_reason())) {
                return GlobalResponseCode.ApplyReasonIsNull;
            }
            if (reasonConfig.getReason_desc() == SaasMessageConstant.IS_CHECKED_TRUE) {
                if (StringUtils.isBlank(apply.getApply_reason_desc())) {
                    return GlobalResponseCode.ApplyReasonDescIsNull;
                }
            }
        }
        ApplySetupContract applySetupContract = messageSetupService.queryCompanyApplyConfig(companyId);
        Integer state = apply.getState();
        if (state != ApplyStatus.Draft.getValue()) {
            if (apply.getFlow_cc_type() == null || CcNoticeType.valueOf(apply.getFlow_cc_type()) == CcNoticeType.Unknown) {
                return GlobalResponseCode.CCNoticeTypeInvalid;
            }
            ApplySetupContract applySetupCostAttributionContract = messageSetupService.queryCompanyApplyCostAttributionConfig(companyId);
            Integer costAttributionCategory = applySetupCostAttributionContract.getCost_attribution_category();
            Integer applyCostAttributionCategory = apply.getCost_attribution_category();
            if (applyCostAttributionCategory == null) {
                applyCostAttributionCategory = CostAttributionCategory.Dept.getKey();
            }
            if (costAttributionCategory == CostAttributionCategory.Dept.getKey() && applyCostAttributionCategory != CostAttributionCategory.Dept.getKey()) {
                return GlobalResponseCode.CostAttributionCategoryIsDept;
            }
            if (costAttributionCategory == CostAttributionCategory.CostCenter.getKey() && applyCostAttributionCategory != CostAttributionCategory.CostCenter.getKey()) {
                return GlobalResponseCode.CostAttributionCategoryIsCostCenter;
            }
            //校验费用归属是否与审批一致
            boolean costAttributionCategoryAccordanceFlag = isCostAttributionCategoryAccordance(applyContract, companyId);
            if (costAttributionCategoryAccordanceFlag) {
                //需要一致
                applyContract.getApply().setBring_in(getBringIn(companyId, ApplyType.ChaiLv.getValue()));
                if (!checkCostAttributionCategory(applyContract, companyId).equals(GlobalResponseCode.Success)) {
                    return GlobalResponseCode.CostAttributionCategoryError;
                }
            }
            if (VersionTool.greaterThanOrEqualTo(clientVersion, "4.2.0") && apply.getType() != ApplyType.Mall.getValue()) {
                if (!costAttributionCategoryAccordanceFlag) {
                    //审批单费用归属是否必填
                    Integer applyAttributionCategoryLimit = null;
                    applyAttributionCategoryLimit = applySetupContract.getApply_attribution_category_travel();
                    if (applyAttributionCategoryLimit == null || applyAttributionCategoryLimit == ApplyCategoryEnum.IS_SHOW_MUST.getValue()) {
                        apply.setCost_attribution_list(Lists.newArrayList());
                    }
                    if (applyAttributionCategoryLimit != null && applyAttributionCategoryLimit == ApplyCategoryEnum.IS_CHECKED_MUST.getValue()) {
                        /*ApplySetupContract orderSetupCostAttributionContract = messageSetupService.queryCompanyCostAttributionConfig(companyId);
                        //获取订单费用归属范围
                        Integer orderCostAttributionCategory = orderSetupCostAttributionContract.getCost_attribution_category();
                        List<CostAttributionInfo> costAttributionList = apply.getCost_attribution_list();
                        if (CollectionUtils.isEmpty(costAttributionList)) {
                            return GlobalResponseCode.ApplyOrderCostAttritutionIsNull;
                        }
                        if (orderCostAttributionCategory != null && orderCostAttributionCategory != CostAttributionScopeEnum.DEPT_AND_PROJ.getCode()) {
                            if (CollectionUtils.isEmpty(costAttributionList) || costAttributionList.size() != 1) {
                                return GlobalResponseCode.ApplyOrderCostAttritutionIsError;
                            }
                            if (orderCostAttributionCategory == CostAttributionScopeEnum.DEPT.getCode() && costAttributionList.get(0).getCost_attribution_category() != CostAttributionScopeEnum.DEPT.getCode()) {
                                return GlobalResponseCode.CostAttributionCategoryIsDept;
                            }
                            if (orderCostAttributionCategory == CostAttributionScopeEnum.PROJ.getCode() && costAttributionList.get(0).getCost_attribution_category() != CostAttributionScopeEnum.PROJ.getCode()) {
                                return GlobalResponseCode.CostAttributionCategoryIsCostCenter;
                            }
                        }
                        if (orderCostAttributionCategory != null && orderCostAttributionCategory == CostAttributionScopeEnum.DEPT_AND_PROJ.getCode() && costAttributionList.size() != 2) {
                            return GlobalResponseCode.ApplyOrderCostAttritutionIsError;
                        }*/
                        Integer bringIn = 0;
                        bringIn = applySetupContract.getApply_attribution_category_modifiable_travel();
                        apply.setBring_in(bringIn);
                    }
                } else {
                    //一致校验
                    //需要一致
                    applyContract.getApply().setBring_in(getBringIn(companyId, ApplyType.ChaiLv.getValue()));
                    if (!checkCostAttributionCategory(applyContract, companyId).equals(GlobalResponseCode.Success)) {
                        return GlobalResponseCode.CostAttributionCategoryError;
                    }
                }

            }
        }
        //检验差旅出差天数
        Boolean isSelfTravel = ObjUtils.toBoolean(apply.getIs_self_travel(), false);
        Boolean selfTravel = ObjUtils.toBoolean(apply.getSelf_travel(), false);
        BigDecimal travelDay = apply.getTravel_day();
        List<ApplyTravelTimeContract> travelTimeList = apply.getTravel_time_list();
        BigDecimal traveTotalDay = BigDecimal.valueOf(0);
        ApplySetupContract applySetupTravelContract = messageSetupService.queryCompanyApplyTravelConfig(companyId);
        if (apply.getType() == ApplyType.ChaiLv.getValue() && applySetupTravelContract != null && applySetupTravelContract.getWhether_travel_statistics() == SaasMessageConstant.IS_CHECKED_TRUE && applySetupTravelContract.getWhether_required() == SaasMessageConstant.IS_CHECKED_TRUE) {
            if (CollectionUtils.isEmpty(travelTimeList)) {
                return GlobalResponseCode.ApplyTravelDayIsNull;
            }
        }
        if (isSelfTravel || selfTravel) {
            if (travelDay == null || travelDay.compareTo(BigDecimal.valueOf(0)) != 1 || CollectionUtils.isEmpty(travelTimeList)) {
                return GlobalResponseCode.ApplyTravelDayIsNull;
            }
            // 查询当前登录用户历史选择的天数
            List<ApplyTravelTime> data = selectCurrentUserHistoryTravelTime(employeeId);
            if (checkApplyTravelTime(data, travelTimeList)) {
                return GlobalResponseCode.ApplyTravelTimeError;
            }
            for (ApplyTravelTimeContract applyTravelTimeContract : travelTimeList) {
                Integer travelType = applyTravelTimeContract.getTravel_type();
                if (travelType == 1) {
                    traveTotalDay = traveTotalDay.add(BigDecimal.valueOf(1));
                } else {
                    traveTotalDay = traveTotalDay.add(BigDecimal.valueOf(0.5));
                }
            }
            if (travelDay.compareTo(traveTotalDay) != 0) {
                return GlobalResponseCode.ApplyTravelDayIsError;
            }
            if (applySetupTravelContract == null || applySetupTravelContract.getWhether_travel_statistics() == SaasMessageConstant.IS_CHECKED_FALSE) {
                return GlobalResponseCode.ApplyTravelStatisticsIsError;
            }
        } else {
            apply.setTravel_day(BigDecimal.ZERO);
            apply.setTravel_time_list(null);
        }
        if (apply.getApply_reason() != null && apply.getApply_reason().length() > 200) {
            //申请事由不能超过50字
            return GlobalResponseCode.ApplyReasonInvalid;
        }
        if (apply.getApply_reason_desc() != null && apply.getApply_reason_desc().length() > 500) {
            //事由补充说明不能超过500字
            return GlobalResponseCode.ApplyReasonDescInvalid;
        }
        if (StringUtils.isNotBlank(apply.getApply_reason_desc())) {
            apply.setApply_reason_desc(apply.getApply_reason_desc().replaceAll("\u0000", ""));
        }
        if (apply.getBudget() != null && apply.getBudget().compareTo(BigDecimal.valueOf(0)) == -1) {
            return GlobalResponseCode.ApplyBudgetInvalid;
        }
        if (apply.getTravel_price_detail() != null && apply.getTravel_price_detail().length() > 200) {
            //事由补充说明不能超过200字
            return GlobalResponseCode.ApplyTranvelPriceDetailInvalid;
        }
        if (apply.getState() == null) {
            return GlobalResponseCode.ApplyStatusRequired;
        }
        CompanyApplyType applyType = CompanyApplyType.valueOf(apply.getFlow_type());
        if (applyType.getValue() == CompanyApplyType.Unknown.getValue()) {
            return GlobalResponseCode.ApplyFlowTypeError;
        }
        if (applyType.getValue() == CompanyApplyType.Elastic.getValue() && apply.getState() == ApplyStatus.PendingAudit.getValue() && (apply.getApprover_id() == null || apply.getApprover_id().length() == 0)) {
            //申请单必须指定一个审批人
            return GlobalResponseCode.ApplyApproveIdInvalid;
        }
        if (applyType.getValue() == CompanyApplyType.Elastic.getValue() && employeeId.equals(apply.getApprover_id())) {
            //审批人不能是自己
            return GlobalResponseCode.ApplyApproverCannotBeSelf;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Integer applyDepartureDate = applySetupContract.getApply_departure_date();
        //检查行程
        List<ApplyTripInfoContract> trips = applyContract.getTrip_list();
        if (CollectionUtils.isEmpty(trips)) {
            //差旅和用车必须要有行程
            return GlobalResponseCode.ApplyTripIsNull;
        }
        if (trips.size() > 100) {
            //行程数据不超过100个
            return GlobalResponseCode.ApplyThirdTripGreaterHundredIsError;
        }
        BigDecimal tripTotalMoney = BigDecimal.valueOf(0);
        for (ApplyTripInfoContract trip : trips) {
            BigDecimal estimatedAmount = trip.getEstimated_amount();
            if (applySetupContract.getWhether_trip_apply_budget() == SaasMessageConstant.IS_CHECKED_TRUE) {
                if (ObjUtils.isBlank(estimatedAmount) || estimatedAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    return GlobalResponseCode.EstimatedAmountIsNull;
                }
            }
            tripTotalMoney = tripTotalMoney.add(estimatedAmount);
            if (trip.getType() == null) {
                return GlobalResponseCode.ApplyTripTypeInvalid;
            }
            //检测行程单类型
            if (apply.getType() == ApplyType.ChaiLv.getValue()) {
                if (trip.getType() != BizType.Air.getValue()
                        && trip.getType() != BizType.Hotel.getValue()
                        && trip.getType() != BizType.Train.getValue()
                        && trip.getType() != BizType.IntlAir.getValue()) {
                    return GlobalResponseCode.ApplyTripNotTravel;
                }
            } else if (apply.getType() == ApplyType.Taxi.getValue()) {
                if (trip.getType() != BizType.Taxi.getValue()) {
                    return GlobalResponseCode.ApplyTripNotTaxi;
                }
            }
            Date startTime = null;
            //检测时间
            if (trip.getStart_time() == null) {
                return GlobalResponseCode.ApplyTripStartDateInvalid;
            } else {
                try {
                    startTime = sdf.parse(trip.getStart_time());
                } catch (Exception ex) {
                    return GlobalResponseCode.ApplyTripStartDateInvalid;
                }
            }
            //校验国内机票、国际机票和火车出发时间为时间段的情况
            if (trip.getType() == BizType.Air.getValue() && trip.getType() == BizType.Train.getValue() && trip.getType() == BizType.IntlAir.getValue() && applyDepartureDate == 2) {
                if (trip.getEnd_time() == null) {
                    //除机票、火车外的其他都有行程结束日期
                    return GlobalResponseCode.ApplyTripEndDateInvalid;
                } else {
                    Date endTime = null;
                    try {
                        endTime = sdf.parse(trip.getEnd_time());
                    } catch (Exception ex) {
                        return GlobalResponseCode.ApplyTripEndDateInvalid;
                    }
                    if (startTime.getTime() > endTime.getTime()) {
                        return GlobalResponseCode.ApplyTripStartDateCannotBiggerThanEndDate;
                    }
                }
            }
            //国内机票、国际机票和火车出发时间不为时间段处理数据
            if (trip.getType() == BizType.Air.getValue() && trip.getType() == BizType.Train.getValue() && trip.getType() == BizType.IntlAir.getValue() && applyDepartureDate != 2) {
                trip.setEnd_time(null);
                trip.setBack_end_time(null);
            }
            if (trip.getType() != BizType.Air.getValue() && trip.getType() != BizType.Train.getValue() && trip.getType() != BizType.IntlAir.getValue()) {
                if (trip.getEnd_time() == null) {
                    //除机票、火车外的其他都有行程结束日期
                    return GlobalResponseCode.ApplyTripEndDateInvalid;
                } else {
                    Date endTime = null;
                    try {
                        endTime = sdf.parse(trip.getEnd_time());
                    } catch (Exception ex) {
                        return GlobalResponseCode.ApplyTripEndDateInvalid;
                    }
                    if (startTime.getTime() > endTime.getTime()) {
                        return GlobalResponseCode.ApplyTripStartDateCannotBiggerThanEndDate;
                    }
                }
            }
            //检测地点
            if (trip.getType() == BizType.Dinner.getValue()) {
                trip.setStart_city_id("0");
            }
            ApplyTripCityType applyTripCityType = ApplyTripCityType.valueOf(applySetupContract.getApply_trip_city());
            if (applyTripCityType == ApplyTripCityType.Required) {
                if (ObjUtils.isBlank(trip.getStart_city_id())) {
                    return GlobalResponseCode.ApplyTripStartCityInvalid;
                }
                if (!NumberUtils.isDigits(trip.getStart_city_id())) {
                    return GlobalResponseCode.ApplyTripCodeError;
                }
                if (trip.getType() == BizType.Air.getValue() || trip.getType() == BizType.Train.getValue() || trip.getType() == BizType.IntlAir.getValue()) {
                    if (ObjUtils.isBlank(trip.getArrival_city_id())) {
                        return GlobalResponseCode.ApplyTripArrivalCityInvalid;
                    }
                    if (!NumberUtils.isDigits(trip.getArrival_city_id())) {
                        return GlobalResponseCode.ApplyTripCodeError;
                    }
                }
            } else {
                if (ObjUtils.isBlank(trip.getStart_city_id())) {
                    trip.setStart_city_id(SaasAreaConstant.UNSET_CITY_ID);
                    trip.setStart_city_name(SaasAreaConstant.UNSET_CITY_NAME);
                }
                if (trip.getType() == BizType.Air.getValue() || trip.getType() == BizType.Train.getValue() || trip.getType() == BizType.IntlAir.getValue()) {
                    if (ObjUtils.isBlank(trip.getArrival_city_id())) {
                        trip.setArrival_city_id(SaasAreaConstant.UNSET_CITY_ID);
                        trip.setArrival_city_name(SaasAreaConstant.UNSET_CITY_NAME);
                    }
                }
            }
            // 390版本之后 需要传城市名称
            if (VersionTool.greaterThanOrEqualTo(clientVersion, "3.9.0")) {
                if (ObjUtils.isNotBlank(trip.getStart_city_id()) && ObjUtils.isBlank(trip.getStart_city_name())) {
                    return GlobalResponseCode.ApplyTripStartCityNameInvalid;
                }
                if (ObjUtils.isNotBlank(trip.getArrival_city_id()) && ObjUtils.isBlank(trip.getArrival_city_name())) {
                    return GlobalResponseCode.ApplyTripArrivalCityNameInvalid;
                }
                if (applyTripCityType == ApplyTripCityType.Hidden) {
                    trip.setStart_city_id(SaasAreaConstant.UNSET_CITY_ID);
                    trip.setStart_city_name(SaasAreaConstant.UNSET_CITY_NAME);
                    if (trip.getType() == BizType.Air.getValue() || trip.getType() == BizType.Train.getValue() || trip.getType() == BizType.IntlAir.getValue()) {
                        trip.setArrival_city_id(SaasAreaConstant.UNSET_CITY_ID);
                        trip.setArrival_city_name(SaasAreaConstant.UNSET_CITY_NAME);
                    }
                }
            }
            // 往返
            if (trip.getType() == BizType.IntlAir.getValue()) {
                if (trip.getTrip_type() == TripType.oneWay.getValue() && StringUtils.isBlank(trip.getStart_time())) {
                    return GlobalResponseCode.IntlAirInvalidError;
                }
                if (trip.getTrip_type() == TripType.goAndBack.getValue() && (StringUtils.isBlank(trip.getBack_start_time()) || StringUtils.isBlank(trip.getStart_time()))) {
                    return GlobalResponseCode.IntlAirInvalidError;
                }
            }
        }
        BigDecimal budget = apply.getBudget().divide(ObjUtils.toBigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        if (tripTotalMoney.setScale(2, BigDecimal.ROUND_HALF_UP).compareTo(budget) != 0) {
            return GlobalResponseCode.ApplyTripBudgetError;
        }
        return GlobalResponseCode.Success;
    }

    /**
     * 查询:费用归属与审批流默认一致是否一致
     *
     * @param companyId
     * @return
     */
    private boolean isCostAttributionCategoryAccordance(ApplyV2Contract applyContract, String companyId) {
        ApplySetupContract applySetupContract = messageSetupService.queryCompanyApplyCostAttributionConfig(companyId);
        if (null != applySetupContract && ObjUtils.isNotEmpty(applySetupContract.getApply_attribution_category_flag())
                && applySetupContract.getApply_attribution_category_flag() == 1) {
            return true;
        }
        return false;
    }

    private List<ApplyTravelTime> selectCurrentUserHistoryTravelTime(String userId) {
        ApplyTravelTimeExample applyTravelTimeExample = new ApplyTravelTimeExample();
        applyTravelTimeExample.createCriteria().andUserIdEqualTo(userId);
        List<ApplyTravelTime> returnData =
                applyTravelTimeMapper.selectByExample(applyTravelTimeExample);
        //查看这个时间的申请单是否作废有效
        removeExceedApplyOrder(returnData);
        return null == returnData ? new ArrayList<>() : returnData;
    }

    private static boolean checkApplyTravelTime(List<ApplyTravelTime> data, List<ApplyTravelTimeContract> travelTimeList) {
        // 半天都能选择。全天只能选择全天
        for (ApplyTravelTimeContract travelTimeContract : travelTimeList) {
            for (ApplyTravelTime applyTravelTime : data) {
                // 是同一天
                if (applyTravelTime.getTravelTime().equals(Long.parseLong(travelTimeContract.getTravel_time()))) {
                    // 1是全天
                    Integer travelType = applyTravelTime.getTravelType();
                    if (travelType == 1) {
                        if (travelTimeContract.getTravel_type() != 1) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    private void removeExceedApplyOrder(List<ApplyTravelTime> data) {
        if (null != data) {
            Iterator<ApplyTravelTime> iterator = data.iterator();
            while (iterator.hasNext()) {
                ApplyTravelTime next = iterator.next();
                ApplyOrder applyOrder = applyMapper.selectByPrimaryKey(next.getApplyOrderId());
                if (null != applyOrder) {
                    //超时与作废的不做校验
                    if (applyOrder.getState() == ApplyStatus.Cancelled.getValue() || applyOrder.getState() == ApplyStatus.Overtime.getValue() || applyOrder.getState() == ApplyStatus.Return.getValue()
                            || applyOrder.getState() == ApplyStatus.Draft.getValue() || applyOrder.getState() == ApplyStatus.Backout.getValue() || applyOrder.getState() == ApplyStatus.Changed.getValue()) {
                        iterator.remove();
                    }
                }
            }
        }
    }

    private int getBringIn(String companyId, int value) {
        int configType = 0;
        if (ApplyType.ChaiLv.getValue() == value) {
            configType = ReasonType.APPLY_CHAILV.getValue();
        }
        if (ApplyType.ApplyTaxi.getValue() == value) {
            configType = ReasonType.APPLY_TAXI.getValue();
        }
        if (ApplyType.Meishi.getValue() == value) {
            configType = ReasonType.APPLY_MEISHI.getValue();
        }
        if (ApplyType.TakeAway.getValue() == value) {
            configType = ReasonType.APPLY_TAKEAWAY.getValue();
        }
        ApplyReasonConfigContract applyReasonConfigContract = customReasonService.queryApplyReasonConfigDetail(companyId, configType);
        if (ObjUtils.isNotEmpty(applyReasonConfigContract)) {
            //差旅
            ApplyTripConfig applyTripConfig = applyReasonConfigContract.getApply_trip_config();
            if (ObjUtils.isNotEmpty(applyTripConfig)) {
                logger.info("getBringIn applyTripConfig=:{}", applyTripConfig.getApply_attribution_category_modifiable());
                return applyTripConfig.getApply_attribution_category_modifiable();
            }
            //用车
            ApplyTaxiConfig applyTaxiConfig = applyReasonConfigContract.getApply_taxi_config();
            if (ObjUtils.isNotEmpty(applyTaxiConfig)) {
                logger.info("getBringIn applyTaxiConfig=:{}", applyTaxiConfig.getApply_attribution_category_modifiable());
                return applyTaxiConfig.getApply_attribution_category_modifiable();
            }
            //用餐
            ApplyMeishiConfig applyMeishiConfig = applyReasonConfigContract.getApply_meishi_config();
            if (ObjUtils.isNotEmpty(applyMeishiConfig)) {
                logger.info("getBringIn applyMeishiConfig=:{}", applyMeishiConfig.getApply_attribution_category_modifiable());
                return applyMeishiConfig.getApply_attribution_category_modifiable();
            }
            //外卖
            ApplyTakeawayConfig applyTakeawayConfig = applyReasonConfigContract.getApply_takeaway_config();
            if (ObjUtils.isNotEmpty(applyTakeawayConfig)) {
                logger.info("getBringIn applyTakeawayConfig=:{}", applyTakeawayConfig.getApply_attribution_category_modifiable());
                return applyTakeawayConfig.getApply_attribution_category_modifiable();
            }
        }
        return 0;
    }

    private GlobalResponseCode checkCostAttributionCategory(ApplyV2Contract applyContract, String companyId) {
        logger.info("checkCostAttributionCategory apply=:{}", JSON.toJSONString(applyContract.getApply()));
        ApplyOrderV2Contract applyOrderV2Contract = applyContract.getApply();
        String costAttributionId = applyOrderV2Contract.getCost_attribution_id();
        Integer costAttributionCategory = applyOrderV2Contract.getCost_attribution_category();
        com.fenbeitong.saas.core.contract.apply.CostInfoContract costInfo = applyOrderV2Contract.getCost_info();
        if (costInfo != null) {
            List<com.fenbeitong.saas.core.contract.apply.CostInfoContract.CostAttributionGroup> costAttributionGroupList = costInfo.getCost_attribution_group_list();
            if (CollectionUtils.isEmpty(costAttributionGroupList)) {
                return GlobalResponseCode.CostAttributionCategoryError;
            }
            com.fenbeitong.saas.core.contract.apply.CostInfoContract.CostAttributionGroup costAttributionGroup = costAttributionGroupList.get(0);
            if (ObjUtils.isEmpty(costAttributionGroup)) {
                return GlobalResponseCode.CostAttributionCategoryError;
            }
            Integer category = costAttributionGroup.getCategory();
            List<CostAttribution> costAttributionList = costAttributionGroup.getCost_attribution_list();
            if (CollectionUtils.isEmpty(costAttributionList)) {
                return GlobalResponseCode.CostAttributionCategoryError;
            }
            String categoryId = costAttributionList.get(0).getId();
            if (!costAttributionId.equals(categoryId)) {
                return GlobalResponseCode.CostAttributionCategoryError;
            }
            if (!costAttributionCategory.equals(category)) {
                return GlobalResponseCode.CostAttributionCategoryError;
            }
        } else {
            List<CostAttributionInfo> costAttributionInfoList = applyOrderV2Contract.getCost_attribution_list();
            if (ObjUtils.isEmpty(costAttributionInfoList)) {
                return GlobalResponseCode.CostAttributionCategoryError;
            }
            for (CostAttributionInfo c : costAttributionInfoList) {
                if (!costAttributionId.equals(c.getCost_attribution_id())) {
                    return GlobalResponseCode.CostAttributionCategoryError;
                }
                if (!costAttributionCategory.equals(c.getCost_attribution_category())) {
                    return GlobalResponseCode.CostAttributionCategoryError;
                }
            }
        }
        return GlobalResponseCode.Success;
    }

    private void insertTravelTripContract(List<ApplyTripInfoContract> tripList, String applyId, Date now) {
        if (CollectionUtils.isNotEmpty(tripList)) {
            for (ApplyTripInfoContract tripContract : tripList) {
                ApplyTripInfo trip = new ApplyTripInfo();
                String id = IDTool.CreateUniqueID();
                trip.setId(id);
                trip.setApplyOrderId(applyId);
                trip.setCreateTime(now);
                trip.setType(tripContract.getType());
                trip.setTripType(tripContract.getTrip_type());
                trip.setArrivalCityId(tripContract.getArrival_city_id());
                trip.setStartCityId(tripContract.getStart_city_id());
                trip.setStartCityName(tripContract.getStart_city_name());
                trip.setArrivalCityName(tripContract.getArrival_city_name());
                trip.setState(ApplyTripStatus.Available.getValue());
                trip.setStartTime(DateTimeTool.fromStringToDate(tripContract.getStart_time()));
                if (tripContract.getEstimated_amount() != null && tripContract.getEstimated_amount().compareTo(BigDecimal.valueOf(0)) == 1) {
                    trip.setEstimatedAmount(tripContract.getEstimated_amount());
                }
                if (tripContract.getEnd_time() != null && tripContract.getEnd_time().length() > 0) {
                    trip.setEndTime(DateTimeTool.fromStringToDate(tripContract.getEnd_time()));
                }
                trip.setUpdateTime(now);
                if (tripContract.getCost_attribution_name() != null && tripContract.getCost_attribution_name().size() > 0) {
                    trip.setCostAttributionName(JSON.toJSONString(tripContract.getCost_attribution_name()));
                }
                applyTripMapper.insert(trip);
            }
        }
    }

    /**
     * 是否是对接差旅审批公司
     *
     * @param companyId
     * @return
     */
    @Override
    public boolean isApplyTravelOrderDockingCompany(String companyId) {
        Map<String, Object> dataMap = queryCompanyDockingInfo(companyId);
        return ObjUtils.toBoolean(MapUtils.getBoolean(dataMap, "company_apply_trip"), false);
    }

    /**
     * 是否是对接用车审批公司
     *
     * @param companyId
     * @return
     */
    @Override
    public boolean isApplyTaxiOrderDockingCompany(String companyId) {
        Map<String, Object> dataMap = queryCompanyDockingInfo(companyId);
        return ObjUtils.toBoolean(MapUtils.getBoolean(dataMap, "company_apply_taxi"), false);
    }

    /**
     * 创建用车审批
     * @param token
     * @param applyContract
     * @param userId
     * @param companyId
     * @param ip
     * @param clientVersion
     * @return
     */
    @Override
    public GlobalResponseCode taxiApplyCreate(String token, ApplyV2Contract applyContract, String userId, String companyId, String ip, String clientVersion) {
        // 创建审批
        //检查数据信息
        GlobalResponseCode code = checkTaxiApplyData(applyContract, userId, companyId, clientVersion);
        if (code != GlobalResponseCode.Success) {
            throw new SaasException(code);
        }
        ApplyOrderV2Contract applyorderContract = applyContract.getApply();
        applyorderContract.setEmployee_id(userId);
        applyorderContract.setCompany_id(companyId);
        //检测user是否有审批权限
        checkApplyAuth(applyContract, userId, companyId);
        String applyId = applyorderContract.getId();
        /*if (applyId == null || applyId.length() == 0) {
            //创建申请单
            applyId = IDTool.CreateUniqueID();
        }*/
        CompanyApplyType companyApplyType = CompanyApplyType.valueOf(applyorderContract.getFlow_type());
        if (companyApplyType == CompanyApplyType.Unknown) {
            companyApplyType = CompanyApplyType.Elastic;
        }
        applyorderContract.setFlow_type(companyApplyType.getValue());
        Integer settingType = SaasFlowConstant.SETTING_TYPE_CENTER;
        //默认非变更单
        Boolean isChangeApply = false;
        String rootApplyId = null;
        String parentApplyId = null;
        rootApplyId = applyId;
        //整理数据
        clearTaxiApplyData(applyContract);
        Date now = new Date();
        List<ApplyTripInfoContract> tripList = applyContract.getTrip_list();
        ApplyOrder apply = applyorderContract.ToModel();
        //处理超规类型
        if (ExceedBuyType.valueOf(apply.getExceedBuyType()) == ExceedBuyType.Unknown) {
            apply.setExceedBuyType(ExceedBuyType.UnSupernormal.getValue());
        }
        if (applyorderContract.getCost_attribution_category() != null && applyorderContract.getCost_attribution_category() == SettingType.Project.getValue()) {
            if (StringUtils.isBlank(applyorderContract.getCost_attribution_name()) || StringUtils.isBlank(applyorderContract.getCost_attribution_id())) {
                throw new SaasException(GlobalResponseCode.CostAttributionIdIsNull);
            }
            apply.setCostAttributionId(applyorderContract.getCost_attribution_id());
            apply.setCostAttributionName(applyorderContract.getCost_attribution_name());
            apply.setCostAttributionCategory(applyorderContract.getCost_attribution_category());
        } else {
            apply.setCostAttributionCategory(SettingType.Department.getValue());
        }
        apply.setApplyOrderType(settingType);
        apply.setUpdateTime(now);
        apply.setCreateTime(now);
        EmployeeContract employee = iBaseOrganizationService.getEmployee(userId, companyId);
        if (employee != null) {
            apply.setApplicantName(employee.getName());
        }
        //创建申请单
        apply.setRootApplyOrderId(rootApplyId);
        apply.setParentApplyOrderId(parentApplyId);
        apply.setIsChangeApply(isChangeApply);
        apply.setChangeReason(applyorderContract.getChange_reason());
        apply.setChangeReasonDesc(applyorderContract.getChange_reason_desc());
        applyorderContract.setId(applyId);
        if (ObjUtils.isBlank(applyId)) {
            applyId = IDTool.CreateUniqueID();
            applyorderContract.setId(applyId);
            apply.setId(applyId);
            apply.setRootApplyOrderId(applyId);
            apply.setBillNo(applyV2Service.getBillNo(apply.getType().intValue(),companyId));
            applyMapper.insertSelective(apply);
        } else {
            ApplyTripInfoExample example = new ApplyTripInfoExample();
            example.createCriteria().andApplyOrderIdEqualTo(applyId);
            applyTripMapper.deleteByExample(example);
            ApplyRuleSettingExample ruleExample = new ApplyRuleSettingExample();
            ruleExample.createCriteria().andApplyOrderIdEqualTo(applyId);
            applyRuleSettingMapper.deleteByExample(ruleExample);
            applyMapper.updateByPrimaryKeySelective(apply);
        }
        //保存行程信息
        insertTaxiTripContract(tripList, applyId, now);
        appendTaxiCityNames(tripList);
        //保存审批用车规则信息
        insertApplyTaxiList(applyContract.getTaxi_apply_rule_info(), applyId);
        //保存审批单中订单的费用归属信息
        sloveApplyCostAttribution(applyorderContract.getCost_attribution_list(), companyId, applyId, applyorderContract.getBring_in());
        try {
            logger.info("同步三方用车审批请求参数：{}", JsonUtils.toJson(applyContract));
            String data = HttpClientUtils.postBody(URL_POST_APPLY_TAXI_ORDER_CREATE, JSON.toJSONString(applyContract));
            logger.info("同步三方创建用车审批返回结果:" + data);
        } catch (Exception e) {
            logger.error("同步三方创建行程审批异常:", e);
            throw new SaasException(FinhubMessageCode.EXCEPTION, CoreLanguage.Common_Exception_InternelError.getMessage());
        }
        return GlobalResponseCode.Success;
    }

    /**
     * 检测用户是否有权限和需要审批,如果没有权限直接抛出SaasException异常
     *
     * @param contract
     * @throws SaasException
     */
    private void checkApplyAuth(ApplyV2Contract contract, String userId, String companyId) throws SaasException {
        ApplyOrderV2Contract apply = contract.getApply();
        if (apply.getState() != ApplyStatus.PendingAudit.getValue()) {
            //用户可以任意修改草稿,只要在提交审批的时候才检测权限
            return;
        }
        List<UserBizAuthWithVerify> userBizAuths = ruleService.queryUserRuleAuth(companyId, userId);
        //*******首先检查是否有权限
        List<Integer> chailvBizs = new ArrayList<>();
        if (apply.getType() == ApplyType.ChaiLv.getValue()) {
            //差旅
            for (ApplyTripInfoContract tripContract : contract.getTrip_list()) {
                if (!chailvBizs.contains(tripContract.getType())) {
                    chailvBizs.add(tripContract.getType());
                }
            }
            List<Integer> noAuthBizs = null;
            if (userBizAuths == null || userBizAuths.size() == 0) {
                noAuthBizs = chailvBizs;
            } else {
                noAuthBizs = new ArrayList<>();
                for (Integer biz : chailvBizs) {
                    boolean hasAuth = false;
                    for (UserBizAuthWithVerify bizAuth : userBizAuths) {
                        if (biz.intValue() == bizAuth.getBiz()) {
                            hasAuth = bizAuth.getAuth() == null ? false : bizAuth.getAuth().getHas_auth();
                            break;
                        }
                    }
                    if (!hasAuth) {
                        noAuthBizs.add(biz);
                    }
                }
            }
            if (noAuthBizs.size() > 0) {
                List<String> bizNames = genBizNames(noAuthBizs);
                String msg = StrUtils.formatString(CoreLanguage.Common_Exception_OpenPermission.getMessage(), String.join("、", bizNames));
                throw new SaasException(GlobalResponseCode.ApplyTripDontHaveAuth, msg);
            }
        } else if (apply.getType() == ApplyType.Taxi.getValue() || apply.getType() == ApplyType.Mall.getValue() || apply.getType() == ApplyType.Meishi.getValue() || apply.getType() == ApplyType.TakeAway.getValue()) {
            //用车或采购
            BizType biz;
            if (apply.getType() == ApplyType.ApplyTaxi.getValue()) {
                biz = BizType.Taxi;
            } else if (apply.getType() == ApplyType.Taxi.getValue()) {
                biz = BizType.Taxi;
            } else if (apply.getType() == ApplyType.Meishi.getValue()) {
                biz = BizType.Dinner;
            } else if (apply.getType() == ApplyType.TakeAway.getValue()) {
                biz = BizType.Takeaway;
            } else {
                biz = BizType.Mall;
            }

            boolean hasAuth = false;
            if (userBizAuths != null && userBizAuths.size() > 0) {
                for (UserBizAuthWithVerify bizAuth : userBizAuths) {
                    if (biz.getValue() == bizAuth.getBiz()) {
                        hasAuth = bizAuth.getAuth() == null ? false : bizAuth.getAuth().getHas_auth();
                        break;
                    }
                }
            }
            if (!hasAuth) {
                String msg = StrUtils.formatString(CoreLanguage.Common_Exception_OpenPermission.getMessage(), biz.getDesc());
                throw new SaasException(GlobalResponseCode.ApplyTripDontHaveAuth, msg);
            }
        }
        //*****权限检查结束

        if (contract.getForce_submit() != null && contract.getForce_submit()) {
            return;
        }
        //检查如果有权限但不需要审批,给出提示
        if (apply.getType() == ApplyType.ChaiLv.getValue()) {
            List<Integer> dontNeedApproveIds = new ArrayList<>();
            for (Integer biz : chailvBizs) {
                for (UserBizAuthWithVerify bizAuth : userBizAuths) {
                    if (biz.intValue() == bizAuth.getBiz()) {
                        if (bizAuth.getAuth().getNeed_verify() == null || !bizAuth.getAuth().getNeed_verify()) {
                            dontNeedApproveIds.add(biz);
                        }
                        break;
                    }
                }
            }
            if (dontNeedApproveIds.size() > 0) {
                List<String> bizNames = genBizNames(dontNeedApproveIds);
                String msg = StrUtils.formatString(CoreLanguage.Common_Exception_BusinessNoCheckApply.getMessage(), String.join("、", bizNames));
                throw new SaasException(GlobalResponseCode.ApplyConfirmSubmitWhenSomeTripDontNeedApprove, msg);
            }
        }
    }

    private List<String> genBizNames(List<Integer> bizValues) {
        List<String> bizNames = new ArrayList<>();
        for (Integer biz : bizValues) {
            bizNames.add(BizType.valueOf(biz.intValue()).getDesc());
        }
        return bizNames;
    }

    /**
     * 保存用车行程信息
     * @param tripList
     * @param applyId
     * @param now
     */
    private void insertTaxiTripContract(List<ApplyTripInfoContract> tripList, String applyId, Date now) {
        if (CollectionUtils.isNotEmpty(tripList)) {
            for (ApplyTripInfoContract tripContract : tripList) {
                ApplyTripInfo trip = new ApplyTripInfo();
                String id = IDTool.CreateUniqueID();
                trip.setId(id);
                trip.setApplyOrderId(applyId);
                trip.setCreateTime(now);
                trip.setType(tripContract.getType());
                trip.setTripType(tripContract.getTrip_type());
                trip.setArrivalCityId(tripContract.getArrival_city_id());
                trip.setStartCityId(StringUtils.join(tripContract.getStart_city_ids(), ","));
                trip.setStartCityName(tripContract.getStart_city_name());
                trip.setArrivalCityName(tripContract.getArrival_city_name());
                JSONObject jo = new JSONObject();
                jo.put("price_limit", tripContract.getPrice_limit());
                jo.put("day_price_limit", tripContract.getDay_price_limit());
                jo.put("total_price", tripContract.getEstimated_amount());
                jo.put("person_count", tripContract.getPerson_count());
                jo.put("times_limit_flag", tripContract.getTimes_limit_flag());
                jo.put("price_limit_flag", tripContract.getPrice_limit_flag());
                trip.setTripContent(JsonUtils.toJson(jo));
                trip.setContent(JsonUtils.toJson(tripContract.getRule_info()));
                trip.setState(ApplyTripStatus.Available.getValue());
                trip.setStartTime(DateTimeTool.fromStringToDate(tripContract.getStart_time()));
                if (StringUtils.isBlank(tripContract.getPerson_count())) {
                    tripContract.setPerson_count("-1");
                }
                trip.setPersonCount(ObjUtils.toInteger(tripContract.getPerson_count()));
                if (tripContract.getEstimated_amount() != null && tripContract.getEstimated_amount().compareTo(BigDecimal.valueOf(0)) == 1) {
                    trip.setEstimatedAmount(tripContract.getEstimated_amount());
                }
                if (tripContract.getEnd_time() != null && tripContract.getEnd_time().length() > 0) {
                    trip.setEndTime(DateTimeTool.fromStringToDate(tripContract.getEnd_time()));
                }
                trip.setUpdateTime(now);
                if (tripContract.getCost_attribution_name() != null && tripContract.getCost_attribution_name().size() > 0) {
                    trip.setCostAttributionName(JSON.toJSONString(tripContract.getCost_attribution_name()));
                }
                applyTripMapper.insert(trip);
            }
        }
    }

    /**
     * 保存审批用车规则信息
     *
     * @param taxiApplyRuleInfo
     * @param applyId
     */
    private void insertApplyTaxiList(Map<String, Object> taxiApplyRuleInfo, String applyId) {
        if (ObjUtils.isNotEmpty(taxiApplyRuleInfo)) {
            List<ApplyV2Contract.KeyValueItem> keyValueItemList = Lists.newArrayList();
            for (Map.Entry<String, Object> taxiApplyMap : taxiApplyRuleInfo.entrySet()) {
                ApplyV2Contract.KeyValueItem keyValueItem = new ApplyV2Contract.KeyValueItem();
                keyValueItem.setType(taxiApplyMap.getKey());
                keyValueItem.setValue(taxiApplyMap.getValue());
                keyValueItemList.add(keyValueItem);
            }
            ApplyRuleSetting applyRuleSetting = new ApplyRuleSetting();
            applyRuleSetting.setTaxiInfo(JsonUtils.toJson(keyValueItemList));
            applyRuleSetting.setApplyOrderId(applyId);
            applyRuleSettingMapper.insertSelective(applyRuleSetting);
        }
    }

    /**
     * 检查用车审批数据有效性
     *
     * @param applyContract
     * @return
     */
    private GlobalResponseCode checkTaxiApplyData(ApplyV2Contract applyContract, String employeeId, String companyId, String clientVersion) {
        //检查申请单
        ApplyOrderV2Contract apply = applyContract.getApply();
        if (apply == null) {
            return GlobalResponseCode.ApplyIsNull;
        }
        if (apply.getType() == null
                || apply.getType() != ApplyType.ApplyTaxi.getValue()
                ) {
            return GlobalResponseCode.ApplyTypeInvalid;
        }
        String applyId = apply.getId();
        if (applyId != null && applyId.length() > 0 && applyId.length() != 24) {
            return GlobalResponseCode.ApplyIdInvalid;
        }
        //检验差旅和用车的申请事由和事由补充内容
        ReasonConfig reasonConfig = customReasonService.queryReasonConfigWithoutItems(companyId, ReasonType.APPLY_CHAILV_CHANGE);
        if (apply.getType() == ApplyType.Taxi.getValue()) {
            if (reasonConfig.getReason() == SaasMessageConstant.IS_CHECKED_TRUE) {
                if (StringUtils.isBlank(apply.getApply_reason())) {
                    return GlobalResponseCode.ApplyReasonIsNull;
                }
                if (reasonConfig.getReason_desc() == SaasMessageConstant.IS_CHECKED_TRUE) {
                    if (StringUtils.isBlank(apply.getApply_reason_desc())) {
                        return GlobalResponseCode.ApplyReasonDescIsNull;
                    }
                }
            }
        }
        ApplySetupContract applySetupContract = messageSetupService.queryCompanyApplyConfig(companyId);
        Integer state = apply.getState();
        ApplySetupContract applySetupCostAttributionContract = messageSetupService.queryCompanyApplyCostAttributionConfig(companyId);
        Integer costAttributionCategory = applySetupCostAttributionContract.getCost_attribution_category();
        Integer applyCostAttributionCategory = apply.getCost_attribution_category();
        if (applyCostAttributionCategory == null) {
            applyCostAttributionCategory = CostAttributionCategory.Dept.getKey();
        }
        if (costAttributionCategory == CostAttributionCategory.Dept.getKey() && applyCostAttributionCategory != CostAttributionCategory.Dept.getKey()) {
            return GlobalResponseCode.CostAttributionCategoryIsDept;
        }
        if (costAttributionCategory == CostAttributionCategory.CostCenter.getKey() && applyCostAttributionCategory != CostAttributionCategory.CostCenter.getKey()) {
            return GlobalResponseCode.CostAttributionCategoryIsCostCenter;
        }
        if (apply.getFlow_cc_type() == null || CcNoticeType.valueOf(apply.getFlow_cc_type()) == CcNoticeType.Unknown) {
            return GlobalResponseCode.CCNoticeTypeInvalid;
        }

        //校验:费用归属与审批流默认一致是否一致
        boolean costAttributionCategoryAccordanceFlag = isCostAttributionCategoryAccordance(applyContract, companyId);
        if (costAttributionCategoryAccordanceFlag) {
            //需要一致
            applyContract.getApply().setBring_in(getBringIn(companyId, ApplyType.ApplyTaxi.getValue()));
            if (!checkCostAttributionCategory(applyContract, companyId).equals(GlobalResponseCode.Success)) {
                return GlobalResponseCode.CostAttributionCategoryError;
            }
        }

        if (VersionTool.greaterThanOrEqualTo(clientVersion, "4.2.0")) {
            if (!costAttributionCategoryAccordanceFlag) {
                //审批单费用归属是否必填
                Integer applyAttributionCategoryLimit = applySetupContract.getApply_attribution_category_taxi();
                if (applyAttributionCategoryLimit == null || applyAttributionCategoryLimit == ApplyCategoryEnum.IS_SHOW_MUST.getValue()) {
                    apply.setCost_attribution_list(Lists.newArrayList());
                }
                if (applyAttributionCategoryLimit != null && applyAttributionCategoryLimit == ApplyCategoryEnum.IS_CHECKED_MUST.getValue()) {
                    /*ApplySetupContract orderSetupCostAttributionContract = messageSetupService.queryCompanyCostAttributionConfig(companyId);
                    //获取订单费用归属范围
                    Integer orderCostAttributionCategory = orderSetupCostAttributionContract.getCost_attribution_category();
                    List<CostAttributionInfo> costAttributionList = apply.getCost_attribution_list();
                    if (CollectionUtils.isEmpty(costAttributionList)) {
                        return GlobalResponseCode.ApplyOrderCostAttritutionIsNull;
                    }
                    if (orderCostAttributionCategory != null && orderCostAttributionCategory != CostAttributionScopeEnum.DEPT_AND_PROJ.getCode()) {
                        if (CollectionUtils.isEmpty(costAttributionList) || costAttributionList.size() != 1) {
                            return GlobalResponseCode.ApplyOrderCostAttritutionIsError;
                        }
                        if (orderCostAttributionCategory == CostAttributionScopeEnum.DEPT.getCode() && costAttributionList.get(0).getCost_attribution_category() != CostAttributionScopeEnum.DEPT.getCode()) {
                            return GlobalResponseCode.CostAttributionCategoryIsDept;
                        }
                        if (orderCostAttributionCategory == CostAttributionScopeEnum.PROJ.getCode() && costAttributionList.get(0).getCost_attribution_category() != CostAttributionScopeEnum.PROJ.getCode()) {
                            return GlobalResponseCode.CostAttributionCategoryIsCostCenter;
                        }
                    }
                    if (orderCostAttributionCategory != null && orderCostAttributionCategory == CostAttributionScopeEnum.DEPT_AND_PROJ.getCode() && costAttributionList.size() != 2) {
                        return GlobalResponseCode.ApplyOrderCostAttritutionIsError;
                    }*/
                    Integer bringIn = applySetupContract.getApply_attribution_category_modifiable_taxi();
                    apply.setBring_in(bringIn);
                }
                //一致逻辑校验
            } else {
                //需要一致
                applyContract.getApply().setBring_in(getBringIn(companyId, ApplyType.ApplyTaxi.getValue()));
                if (!checkCostAttributionCategory(applyContract, companyId).equals(GlobalResponseCode.Success)) {
                    return GlobalResponseCode.CostAttributionCategoryError;
                }
            }

        }
        if (apply.getApply_reason() != null && apply.getApply_reason().length() > 200) {
            //申请事由不能超过50字
            return GlobalResponseCode.ApplyReasonInvalid;
        }
        if (apply.getApply_reason_desc() != null && apply.getApply_reason_desc().length() > 500) {
            //事由补充说明不能超过500字
            return GlobalResponseCode.ApplyReasonDescInvalid;
        }
        if (apply.getBudget() != null && apply.getBudget().compareTo(BigDecimal.valueOf(0)) == -1) {
            return GlobalResponseCode.ApplyBudgetInvalid;
        }
        if (apply.getTravel_price_detail() != null && apply.getTravel_price_detail().length() > 200) {
            //事由补充说明不能超过200字
            return GlobalResponseCode.ApplyTranvelPriceDetailInvalid;
        }
        if (apply.getState() == null) {
            return GlobalResponseCode.ApplyStatusRequired;
        }
        CompanyApplyType applyType = CompanyApplyType.valueOf(apply.getFlow_type());
        if (applyType.getValue() == CompanyApplyType.Unknown.getValue()) {
            return GlobalResponseCode.ApplyFlowTypeError;
        }
        if (applyType.getValue() == CompanyApplyType.Elastic.getValue() && apply.getState() == ApplyStatus.PendingAudit.getValue() && (apply.getApprover_id() == null || apply.getApprover_id().length() == 0)) {
            //申请单必须指定一个审批人
            return GlobalResponseCode.ApplyApproveIdInvalid;
        }
        if (applyType.getValue() == CompanyApplyType.Elastic.getValue() && employeeId.equals(apply.getApprover_id())) {
            //审批人不能是自己
            return GlobalResponseCode.ApplyApproverCannotBeSelf;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        //检查行程
        List<ApplyTripInfoContract> trips = applyContract.getTrip_list();
        if (CollectionUtils.isEmpty(trips)) {
            //差旅和用车必须要有行程
            return GlobalResponseCode.ApplyTripIsNull;
        }
        if (trips.size() > 30) {
            //行程数据不超过30个
            return GlobalResponseCode.ApplyTripTooMuch;
        }
        BigDecimal tripTotalMoney = BigDecimal.valueOf(0);
        for (ApplyTripInfoContract trip : trips) {
            BigDecimal estimatedAmount = trip.getEstimated_amount();
            if (applySetupContract.getWhether_trip_apply_budget() == SaasMessageConstant.IS_CHECKED_TRUE) {
                if (ObjUtils.isBlank(estimatedAmount) || estimatedAmount.compareTo(BigDecimal.ZERO) < 0) {
                    return GlobalResponseCode.EstimatedAmountIsNull;
                }
            }
            tripTotalMoney = tripTotalMoney.add(estimatedAmount);
            if (trip.getType() == null) {
                return GlobalResponseCode.ApplyTripTypeInvalid;
            }
            //检测行程单类型
            if (apply.getType() == ApplyType.ApplyTaxi.getValue()) {
                if (trip.getType() != BizType.Taxi.getValue()) {
                    return GlobalResponseCode.ApplyTripNotTaxi;
                }
            }
            Date startTime = null;
            Date endTime = null;
            //检测时间
            if (trip.getStart_time() == null) {
                return GlobalResponseCode.ApplyTripStartDateInvalid;
            } else {
                try {
                    startTime = sdf.parse(trip.getStart_time());
                } catch (Exception ex) {
                    return GlobalResponseCode.ApplyTripStartDateInvalid;
                }
            }

            if (trip.getEnd_time() == null) {
                return GlobalResponseCode.ApplyTripEndDateInvalid;
            } else {
                try {
                    endTime = sdf.parse(trip.getEnd_time());
                } catch (Exception ex) {
                    return GlobalResponseCode.ApplyTripEndDateInvalid;
                }
            }

            List<String> startCityIds = trip.getStart_city_ids();
            if (CollectionUtils.isEmpty(startCityIds)) {
                return GlobalResponseCode.ApplyTripStartCityInvalid;
            }
            for (String startCityId : startCityIds) {
                if (!NumberUtils.isDigits(startCityId)) {
                    return GlobalResponseCode.ApplyTripCodeError;
                }
            }
            if (startCityIds.size() > 15) {
                return GlobalResponseCode.ApplyTaxiCityIsTooLong;
            }
            List<Map<String, String>> ruleInfo = trip.getRule_info();
            if (CollectionUtils.isEmpty(ruleInfo)) {
                return GlobalResponseCode.ApplyTaxiRuleDesxisError;
            }
        }
        BigDecimal budget = apply.getBudget().divide(ObjUtils.toBigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        if (tripTotalMoney.setScale(2, BigDecimal.ROUND_HALF_UP).compareTo(budget) != 0) {
            return GlobalResponseCode.ApplyTripBudgetError;
        }
        TaxiRuleForApproveContract taxiRuleForApproveContract =
            iTaxiApproveRuleService.queryTaxiApproveRuleDetail(employeeId, companyId, apply.getScene_type());
        logger.info("审批用车规则信息：" + JSON.toJSON(taxiRuleForApproveContract));
        Map<String, Object> taxiApplyRuleInfo = applyContract.getTaxi_apply_rule_info();
        if (taxiApplyRuleInfo == null || taxiRuleForApproveContract == null) {
            return GlobalResponseCode.ApplyTaxiIsError;
        }
        Integer idInfo = taxiRuleForApproveContract.getId();
        Integer id = ObjUtils.toInteger(taxiApplyRuleInfo.get("id"));
        if (id == null || !id.equals(idInfo)) {
            return GlobalResponseCode.ApplyTaxiRuleIsUpdate;
        }
        String nameInfo = taxiRuleForApproveContract.getName();
        String name = ObjUtils.toString(taxiApplyRuleInfo.get("name"));
        if (StringUtils.isBlank(name) || !name.equals(nameInfo)) {
            return GlobalResponseCode.ApplyTaxiRuleIsUpdate;
        }
        //限制车型
        String allowedTaxiTypeInfo = taxiRuleForApproveContract.getAllowed_taxi_type();
        String allowedTaxiType = ObjUtils.toString(taxiApplyRuleInfo.get("allowed_taxi_type"));
        if (StringUtils.isNotBlank(allowedTaxiTypeInfo) && StringUtils.isNotBlank(allowedTaxiType)) {
            if (StringUtils.isBlank(allowedTaxiType) || !allowedTaxiType.equals(allowedTaxiTypeInfo)) {
                return GlobalResponseCode.ApplyTaxiRuleIsUpdate;
            }
        }
        //金额限制类型
        Integer priceLimitFlagInfo = taxiRuleForApproveContract.getPrice_limit_flag();
        Integer priceLimitFlag = ObjUtils.toInteger(taxiApplyRuleInfo.get("price_limit_flag"));
        if (priceLimitFlag == null || !priceLimitFlag.equals(priceLimitFlagInfo)) {
            return GlobalResponseCode.ApplyTaxiRuleIsUpdate;
        }
        if (priceLimitFlag.equals(LimitType.EMPLOYEE_WRITE.getCode())) {
            BigDecimal totalPrice = ObjUtils.toBigDecimal(taxiApplyRuleInfo.get("total_price"));
            if (state != ApplyStatus.Draft.getValue() && totalPrice.compareTo(BigDecimal.valueOf(0)) < 1) {
                return GlobalResponseCode.ParameterError;
            }
        }
        //单次限制金额
        BigDecimal priceLimitInfo = taxiRuleForApproveContract.getPrice_limit();
        BigDecimal priceLimit = ObjUtils.toBigDecimal(taxiApplyRuleInfo.get("price_limit"));
        if (priceLimitFlag.equals(LimitType.LIMIT.getCode()) && (priceLimit == null || priceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).compareTo(priceLimitInfo.setScale(2, BigDecimal.ROUND_HALF_UP)) != 0)) {
            return GlobalResponseCode.ApplyTaxiRuleIsUpdate;
        }
        //单日限制金额
        BigDecimal dayPriceLimitInfo = taxiRuleForApproveContract.getDay_price_limit();
        BigDecimal dayPriceLimit = ObjUtils.toBigDecimal(taxiApplyRuleInfo.get("day_price_limit"));
        if (priceLimitFlag.equals(LimitType.LIMIT.getCode()) && (dayPriceLimit == null || dayPriceLimit.setScale(2, BigDecimal.ROUND_HALF_UP).compareTo(dayPriceLimitInfo.setScale(2, BigDecimal.ROUND_HALF_UP)) != 0)) {
            return GlobalResponseCode.ApplyTaxiRuleIsUpdate;
        }
        //出租车调度费
        Integer taxiSchedulingFeeInfo = taxiRuleForApproveContract.getTaxi_scheduling_fee();
        Integer taxiSchedulingFee = ObjUtils.toInteger(taxiApplyRuleInfo.get("taxi_scheduling_fee"));
        if (taxiSchedulingFee == null || !taxiSchedulingFee.equals(taxiSchedulingFeeInfo)) {
            return GlobalResponseCode.ApplyTaxiRuleIsUpdate;
        }
        //同城限制
        if(Objects.nonNull(ObjUtils.toInteger(taxiApplyRuleInfo.get(SaasOrderThirdRuleConstant.TAXI_ALLOW_SAME_CITY_TYPE)))){
            logger.info("走重构后代码。");
            Integer allowSameCityInfo = taxiRuleForApproveContract.getAllowSameCityType();
            Integer allowSameCityType = ObjUtils.toInteger(taxiApplyRuleInfo.get(SaasOrderThirdRuleConstant.TAXI_ALLOW_SAME_CITY_TYPE));
            if (!Objects.equals(allowSameCityType, allowSameCityInfo)) {
                return GlobalResponseCode.ApplyTaxiRuleIsUpdate;
            }
        } else {
            logger.info("走老代码。");
            Boolean allowSameCityInfo = taxiRuleForApproveContract.getAllow_same_city();
            Boolean allowSameCity = ObjUtils.toBoolean(taxiApplyRuleInfo.get("allow_same_city"));
            if (allowSameCity == null || !allowSameCity.equals(allowSameCityInfo)) {
                return GlobalResponseCode.ApplyTaxiRuleIsUpdate;
            }
        }

        //允许为他人叫车
        Boolean allowCalledForOtherInfo = taxiRuleForApproveContract.getAllow_called_for_other();
        Boolean allowCalledForOther = ObjUtils.toBoolean(taxiApplyRuleInfo.get("allow_called_for_other"));
        if (allowCalledForOther == null || !allowCalledForOther.equals(allowCalledForOtherInfo)) {
            return GlobalResponseCode.ApplyTaxiRuleIsUpdate;
        }
        //次数限制类型
        Integer timesLimitFlagInfo = taxiRuleForApproveContract.getTimes_limit_flag();
        Integer timesLimitFlag = ObjUtils.toInteger(taxiApplyRuleInfo.get("times_limit_flag"));
        if (timesLimitFlag == null || !timesLimitFlag.equals(timesLimitFlagInfo)) {
            return GlobalResponseCode.ApplyTaxiRuleIsUpdate;
        }
        //限制次数
        Integer timesLimitInfo = taxiRuleForApproveContract.getTimes_limit();
        Integer timesLimit = ObjUtils.toInteger(taxiApplyRuleInfo.get("times_limit"));
        if (state != ApplyStatus.Draft.getValue() && timesLimitFlag != LimitType.EMPLOYEE_WRITE.getCode() && (timesLimit == null || !timesLimit.equals(timesLimitInfo))) {
            return GlobalResponseCode.ApplyTaxiRuleIsUpdate;
        }
        taxiApplyRuleInfo.put("start_time", trips.get(0).getStart_time());
        taxiApplyRuleInfo.put("end_time", trips.get(0).getEnd_time());
        taxiApplyRuleInfo.put("start_city_ids", trips.get(0).getStart_city_ids());
        //总金额
        BigDecimal totalPrice = ObjUtils.toBigDecimal(taxiApplyRuleInfo.get("total_price"));
        ApplyTripInfoContract applyTripInfoContract = trips.get(0);
        if (timesLimitFlag == LimitType.NO_LIMIT.getCode()) {
            timesLimit = -1;
        }
        applyTripInfoContract.setPerson_count(timesLimit + "");
        applyTripInfoContract.setDay_price_limit(dayPriceLimit);
        applyTripInfoContract.setPrice_limit(priceLimit);
        applyTripInfoContract.setEstimated_amount(totalPrice);
        applyTripInfoContract.setTimes_limit_flag(timesLimitFlag);
        applyTripInfoContract.setPrice_limit_flag(priceLimitFlag);
        return GlobalResponseCode.Success;
    }

    /**
     * 整理申请单数据
     *
     * @param applyContract
     */
    private void clearTaxiApplyData(ApplyV2Contract applyContract) {
        ApplyOrderV2Contract applyorderContract = applyContract.getApply();
        if (applyorderContract.getId() == null || applyorderContract.getId().length() == 0) {
            applyorderContract.setCheck_reason(null);
        }
        if (applyorderContract.getBudget() == null) {
            applyorderContract.setBudget(BigDecimal.valueOf(0));
        }
        //用车没有同行人
        applyContract.setGuest_list(null);
        if (applyContract.getTrip_list() != null && applyContract.getTrip_list().size() == 0) {
            applyContract.setTrip_list(null);
        }
        if (applyContract.getGuest_list() != null && applyContract.getGuest_list().size() == 0) {
            applyContract.setGuest_list(null);
        }
        List<Tuple<Date, String>> cityIds = new ArrayList<>();
        List<Date> timeRange = new ArrayList<>();
        for (ApplyTripInfoContract trip : applyContract.getTrip_list()) {
            Date startTime = DateTimeTool.fromStringToDate(trip.getStart_time());
            //这里把时间从String转到Date,再转回String,是需要用Date格式式字段串的显示,如输入为2017-2-27,格式化后变为2017-02-27
            trip.setStart_time(DateTimeTool.fromDateToString(startTime));
            timeRange.add(startTime);
            Date endTime = DateTimeTool.fromStringToDate(trip.getEnd_time());
            trip.setEnd_time(DateTimeTool.fromDateToString(endTime));
            timeRange.add(endTime);
            if (trip.getType() == BizType.Taxi.getValue() || trip.getType() == BizType.Hotel.getValue()) {
                trip.setArrival_city_id(null);
                trip.setArrival_city_name(null);
            }
            if (applyorderContract.getType() == ApplyType.ApplyTaxi.getValue() && trip.getType() == BizType.Taxi.getValue()) {
                List<String> startCityIds = trip.getStart_city_ids();
                if (ObjUtils.isNotEmpty(startCityIds)) {
                    for (String cityId : startCityIds) {
                        cityIds.add(new Tuple<>(startTime, cityId));
                    }
                }
            }
            String tripCityId = trip.getStart_city_id();
            if (trip.getArrival_city_id() != null) {
                tripCityId += "-" + trip.getArrival_city_id();
            }
            cityIds.add(new Tuple<>(startTime, tripCityId));
        }
        //city_range格式:1000001-200002,2000002,300003-400004 意即:北京-上海,上海,天津-重庆
        cityIds.sort((m, n) -> (int) (m.getItem1().getTime() - n.getItem1().getTime()));
        List<String> cityIdArr = new ArrayList<>();
        for (Tuple<Date, String> cityId : cityIds) {
            cityIdArr.add(cityId.getItem2());
        }
        String cityIdVal = String.join(",", cityIdArr);
        applyorderContract.setCity_range(cityIdVal);
        if (ObjUtils.isEmpty(timeRange) || ObjUtils.isEmpty(timeRange.get(0))) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
        //将所有出现过的时间正序排列,取最小值和最大值,重新写入timeRange字段,如果最小值=最大值,则只取最小值
        timeRange.sort((m, n) -> (int) (m.getTime() - n.getTime()));
        Date minTime = timeRange.get(0);
        Date maxTime = timeRange.get(timeRange.size() - 1);
        String desTimeRange = DateTimeTool.fromDateToString(minTime);
        if (maxTime.getTime() != minTime.getTime()) {
            desTimeRange += "," + DateTimeTool.fromDateToString(maxTime);
        }
        applyorderContract.setTime_range(desTimeRange);
        if (applyorderContract.getApprover_id() == null) {
            applyorderContract.setApprover_id("");
        }
    }

    /**
     * 行程审批
     * @param approveModel
     * @param userId
     * @param companyId
     * @param ip
     * @return
     */
    @Override
    public SaasResponseEntity travelApprove(ThirdApplyApproveContract approveModel, String userId, String companyId, String ip) {
        return sloveThirdApprove(approveModel, userId, companyId, ip);
    }

    /**
     * 用车审批
     * @param approveModel
     * @param userId
     * @param companyId
     * @param ip
     * @return
     */
    @Override
    public SaasResponseEntity taxiApprove(ThirdApplyApproveContract approveModel, String userId, String companyId, String ip) {
        return sloveThirdApprove(approveModel, userId, companyId, ip);
    }

    /**
     * 处理第三方审批同意逻辑
     * @param approveModel
     * @param userId
     * @param companyId
     * @param ip
     * @return
     */
    private SaasResponseEntity sloveThirdApprove(ThirdApplyApproveContract approveModel, String userId, String companyId, String ip) {
        if (approveModel == null) {
            throw new SaasException(GlobalResponseCode.ApplyIsNull);
        }
        String applyId = approveModel.getApply_id();
        if (StringTool.isNullOrEmpty(applyId)) {
            throw new SaasException(GlobalResponseCode.ApplyIdInvalid);
        }
        if (StringTool.isNullOrEmpty(approveModel.getThird_id())) {
            throw new SaasException(GlobalResponseCode.ApplyVirtualThirdIdIsNull);
        }
        if (approveModel.getComment() != null && approveModel.getComment().length() > 200) {
            throw new SaasException(GlobalResponseCode.ApplyCheckReasonInvalid);
        }
        ApplyOrder apply = applyMapper.selectByPrimaryKey(applyId);
        logger.info("变更申请单信息={}", JsonUtils.toJson(apply));
        if (apply == null) {
            throw new SaasException(GlobalResponseCode.NotFound);
        }
        if (apply.getIsChangeApply()) {
            // 是变更申请单
            String parentApplyOrderId = apply.getParentApplyOrderId();
            ApplyOrder parentApplyOrder = applyMapper.selectByPrimaryKey(parentApplyOrderId);
            // 原单不存在
            if (ObjUtils.isEmpty(parentApplyOrder)) {
                throw new SaasException(GlobalResponseCode.ApplyParentOrderNotExist);
            }
            // 原单状态不是已变更
            if (parentApplyOrder.getState() != ApplyStatus.Changing.getValue()) {
                throw new SaasException(GlobalResponseCode.ApplyParentOrderIsNotChanging);
            }
            // 更新原单信息
            parentApplyOrder.setUpdateTime(new Date());
            parentApplyOrder.setState(ApplyStatus.Changed.getValue());
            logger.info("同意变更审批单, 原单信息={}", JsonUtils.toJson(parentApplyOrder));
            applyMapper.updateByPrimaryKeySelective(parentApplyOrder);
        }
        //加上排重逻辑
        GlobalResponseCode duplicateCode = duplicateThirdApply(companyId, approveModel.getThird_id(), apply.getApplyOrderType(), apply.getType());
        if (duplicateCode != GlobalResponseCode.Success) {
            return new SaasResponseEntity(duplicateCode, null);
        }
        if (apply.getState() != null && apply.getState().intValue() == ApplyStatus.Overtime.getValue()) {
            //事中审批已超时
            throw new SaasException(GlobalResponseCode.CenterApplyOverTime);
        }
        if (apply.getState() != null && apply.getState().intValue() == ApplyStatus.Backout.getValue()) {
            //事中审批已撤销
            throw new SaasException(GlobalResponseCode.CenterApplyCancleOrder);
        }
        if (!apply.getCompanyId().equals(companyId)) {
            //申请单和当前人不在一个公司
            throw new SaasException(GlobalResponseCode.ApplyCompanyCannotMatch);
        }
        if (apply.getState() != ApplyStatus.PendingAudit.getValue()) {
            //该单不是待审核,不能审批
            throw new SaasException(GlobalResponseCode.ApplyStatusNotPendingAudit);
        }
        // 更新状态及三方审批id
        ApplyOrder applyOrder = new ApplyOrder();
        applyOrder.setId(applyId);
        applyOrder.setThirdId(approveModel.getThird_id());
        applyOrder.setRealPrice(approveModel.getPrice());
        applyOrder.setState(ApplyStatus.Approved.getValue());
        applyOrder.setUpdateTime(new Date());
        applyMapper.updateByPrimaryKeySelective(applyOrder);
        return new SaasResponseEntity(GlobalResponseCode.Success, null);
    }

    /**
     * 采购预下单操作
     * @param orderInfo
     * @param token
     * @return
     */
    private String mallOrderSubmint(Map<String, Object> orderInfo, String token) {
        Map<String, String> headers = new HashMap<>();
        headers.put("X-AUTH-TOKEN", token);
        String result = null;
        try {
            orderInfo.put("auto_submit_flag", true);
            result = HttpTool.post(HostPropertyConfigTool.HOST_MALL_BIZ + "/mall_biz/order/v2/submit", orderInfo, headers);
        } catch (Exception e) {
            logger.info("获取采购创建订单异常信息：{}", e.getMessage());
            throw new SaasException(GlobalResponseCode.ParameterIsNull, CoreLanguage.Common_Exception_MallOrderServerError.getMessage());
        }
        logger.info("获取采购创建订单返回结果：{}", result);
        if (StringUtils.isBlank(result)) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull, CoreLanguage.Common_Exception_MallOrderResultError.getMessage());
        }
        Map<String, Object> jo = JSONObject.parseObject(result, HashMap.class);
        Map<String, Object> mallDataMap = JSONObject.parseObject(ObjUtils.toString(jo.get("data")), HashMap.class);
        Integer code = ObjUtils.toInteger(jo.get("code"));
        String msg = ObjUtils.toString(jo.get("msg"));
        if (!code.equals(0) && ObjUtils.isEmpty(mallDataMap)) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull, msg);
        }
        String orderId = ObjUtils.toString(mallDataMap.get("order_id"));
        if (StringUtils.isBlank(orderId)) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull, CoreLanguage.Common_Exception_MallOrderIdNotEmpty.getMessage());
        }
        return orderId;

    }

    private void insertMallTripContractList(List<ApplyTripInfoContract> tripList, String applyId, Date now, Integer automaticOrder) {
        if (tripList != null) {
            for (ApplyTripInfoContract tripContract : tripList) {
                String applicateId = IDTool.CreateUniqueID();
                ApplyTripInfo trip = new ApplyTripInfo();
                String id = IDTool.CreateUniqueID();
                trip.setId(id);
                trip.setApplyOrderId(applyId);
                trip.setCreateTime(now);
                trip.setType(tripContract.getType());
                trip.setArrivalCityId(tripContract.getArrival_city_id());
                trip.setStartCityId(tripContract.getStart_city_id());
                trip.setStartCityName(tripContract.getStart_city_name());
                trip.setArrivalCityName(tripContract.getArrival_city_name());
                trip.setState(ApplyTripStatus.Available.getValue());
                trip.setStartTime(DateTimeTool.fromStringToDate(tripContract.getStart_time()));
                trip.setPersonCount(ObjUtils.toInteger(tripContract.getPerson_count(), -1));
                if (tripContract.getEstimated_amount() != null && tripContract.getEstimated_amount().compareTo(BigDecimal.valueOf(0)) == 1) {
                    trip.setEstimatedAmount(tripContract.getEstimated_amount());
                }
                if (tripContract.getEnd_time() != null && tripContract.getEnd_time().length() > 0) {
                    trip.setEndTime(DateTimeTool.fromStringToDate(tripContract.getEnd_time()));
                }
                trip.setStartTime(now);
                trip.setStartCityId("0");
                trip.setOrderReasonId(tripContract.getOrder_reason_id());
                trip.setOrderReason(tripContract.getOrder_reason());
                trip.setOrderReasonDesc(tripContract.getOrder_reason_desc());
                trip.setPriceStructure(JSON.toJSONString(tripContract.getMall_price_structure()));
                trip.setAddressInfo(JSON.toJSONString(tripContract.getAddress_info()));
                trip.setMallList(JSON.toJSONString(tripContract.getMall_list()));
                JSONObject jo = new JSONObject();
                if (CollectionUtils.isNotEmpty(tripContract.getCustom_remark())) {
                    jo.put("custom_remark", tripContract.getCustom_remark());
                }
                if (CollectionUtils.isNotEmpty(tripContract.getNotifier_ids())) {
                    jo.put("notifier_ids", tripContract.getNotifier_ids());
                }
                if (automaticOrder != null) {
                    jo.put("automatic_order", automaticOrder);
                }
                //配送时间
                String deliveryTime = tripContract.getDelivery_time();
                if (StringUtils.isNotBlank(deliveryTime)) {
                    jo.put("delivery_time", deliveryTime);
                }
                if (tripContract.getType() == BizType.Mall.getValue() && automaticOrder == SaasMessageConstant.IS_CHECKED_TRUE && StringUtils.isNotBlank(tripContract.getOrder_id())) {
                    jo.put("orderId", tripContract.getOrder_id());
                }
                if (jo != null) {
                    trip.setContent(jo.toJSONString());
                }
                if (tripContract.getCost_attribution_name() != null && tripContract.getCost_attribution_name().size() > 0) {
                    trip.setCostAttributionName(JSON.toJSONString(tripContract.getCost_attribution_name()));
                }
                if (ObjUtils.isNotEmpty(tripContract.getTrip_content())) {
                    trip.setTripContent(tripContract.getTrip_content());
                }
                trip.setUpdateTime(now);
                trip.setTripApplicateCurrentId(applicateId);
                applyTripMapper.insert(trip);
                if (automaticOrder == SaasMessageConstant.IS_CHECKED_TRUE && StringUtils.isNotBlank(tripContract.getOrder_id())) {
                    ApplyTripApplicate applicateModel = new ApplyTripApplicate();
                    applicateModel.setId(applicateId);
                    applicateModel.setOrderId(tripContract.getOrder_id());
                    applicateModel.setAction(ApplyTripApplicateAction.Applicate.getValue());
                    applicateModel.setApplyId(applyId);
                    applicateModel.setCreateTime(now);
                    applicateModel.setApplyTripId(id);
                    applicateModel.setOrderProductType(1);
                    applicateModel.setComment(null);
                    applicateModel.setUseTime(now);
                    applyTripApplicateMapper.insert(applicateModel);
                }
            }
        }
    }

    /**
     * 终审同意采购自动下单
     * @param applyId
     */
    private void sloveMallOrder(String applyId) {
        List<ApplyTripInfo> applyTripInfoList = applyTripInfoExtMapper.queryAllTripListByApplyOrderId(applyId);
        Integer automaticOrder = SaasMessageConstant.IS_CHECKED_FALSE;
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(applyTripInfoList)) {
            ApplyTripInfo tripInfo = applyTripInfoList.get(0);
            JSONObject jsonObject = JSON.parseObject(tripInfo.getContent());
            if (jsonObject != null) {
                automaticOrder = ObjUtils.toInteger(jsonObject.get("automatic_order"));
                automaticOrder = automaticOrder == null ? SaasMessageConstant.IS_CHECKED_FALSE : automaticOrder;
            }
        }
        if (automaticOrder == SaasMessageConstant.IS_CHECKED_TRUE) {
            ApplyTripApplicateExample applyTripApplicateExample = new ApplyTripApplicateExample();
            applyTripApplicateExample.createCriteria().andApplyIdEqualTo(applyId);
            List<ApplyTripApplicate> applyTripApplicateList = applyTripApplicateMapper.selectByExample(applyTripApplicateExample);
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(applyTripApplicateList)) {
                ThreadPoolUtils.getExecutorService().execute(() -> createMallOrder(applyId, applyTripApplicateList.get(0).getOrderId()));
            }
        }
    }

    /**
     * 采购下单操作
     * @return
     */
    private void createMallOrder(String applyId, String orderId) {
        Map<String, Object> orderInfo = Maps.newHashMap();
        orderInfo.put("order_id", orderId);
        orderInfo.put("apply_id", applyId);
        String result = null;
        try {
            result = HttpTool.post(HostPropertyConfigTool.HOST_MALL_BIZ + "/mall_biz/order/apply/approve/success", orderInfo);
            logger.info("获取采购生成订单返回结果：{}", result);
            if (StringUtils.isBlank(result)) {
                throw new SaasException(GlobalResponseCode.PayOrderCancleOrderSystemError);
            }
        } catch (Exception e) {
            logger.error("获取采购生成订单异常信息：{}", e.getMessage());
            throw new SaasException(GlobalResponseCode.PayOrderCancleOrderSystemError);
        }
    }

    private void appendTaxiCityNames(List<ApplyTripInfoContract> trips) {
        if (CollectionUtils.isEmpty(trips)) {
            return;
        }
        List<String> cityIds = new ArrayList<String>();
        for (ApplyTripInfoContract trip : trips) {
            List<String> startCityIdList = trip.getStart_city_ids();
            List<String> startCityNameList = trip.getStart_city_name_list();
            if (CollectionUtils.isNotEmpty(startCityIdList) && CollectionUtils.isEmpty(startCityNameList)) {
                for (String startCityId : startCityIdList) {
                    if (ObjUtils.isBlank(trip.getStart_city_name()) && CollectionUtils.isEmpty(trip.getStart_city_name_list()) && !cityIds.contains(startCityId)) {
                        cityIds.add(startCityId);
                    }
                }
            }
        }
        List<IdNameContract> cityNames = cityService.getCityNamesByIds(cityIds);
        for (ApplyTripInfoContract trip : trips) {
            List<String> startCityIdList = trip.getStart_city_ids();
            List<String> startCityNameList = trip.getStart_city_name_list();
            if (CollectionUtils.isNotEmpty(startCityIdList) && CollectionUtils.isEmpty(startCityNameList)) {
                List<String> startCityNames = Lists.newArrayList();
                for (String startCityId : startCityIdList) {
                    IdNameContract cityName = getNameFromListById(cityNames, startCityId);
                    if (cityName != null && !startCityNames.contains(cityName)) {
                        startCityNames.add(cityName.getName());
                    }
                }
                trip.setStart_city_name_list(startCityNames);
            }
        }
    }

    /**
     * 是否是对接退改审批公司
     *
     * @param companyId
     * @return
     */
    @Override
    public boolean isApplyRefundChangeDockingCompany(String companyId) {
        Map<String, Object> dataMap = queryCompanyDockingInfo(companyId);
        return ObjUtils.toBoolean(MapUtils.getBoolean(dataMap, "company_apply_order_change"), false);
    }

    @Override
    public GlobalResponseCode refundChangeCreate(String token, ApplyRefundChangeContract applyContract, String userId, String companyId, String ip, String applyId, String clientVersion) {
        //检查数据信息
        GlobalResponseCode code = checkRefundChangeApplyData(applyContract, userId, companyId, clientVersion);
        if (code != GlobalResponseCode.Success) {
            return code;
        }
        ApplyOrderV2Contract applyOrderContract = applyContract.getApply();
        applyOrderContract.setEmployee_id(userId);
        applyOrderContract.setCompany_id(companyId);
        ApplyType applyType = ApplyType.valueOf(applyOrderContract.getType().intValue());
        CompanyApplyType companyApplyType = CompanyApplyType.valueOf(applyOrderContract.getFlow_type());
        if (companyApplyType == CompanyApplyType.Unknown) {
            companyApplyType = CompanyApplyType.Elastic;
        }
        applyOrderContract.setFlow_type(companyApplyType.getValue());
        //如果是提交审批保存审批流设置和审批单日志记录
        CompanyApplyFlowSetV2RequestContract flowRequest = applyOrderContract.getFlow();
        if (flowRequest == null) {
            flowRequest = new CompanyApplyFlowSetV2RequestContract();
        }
        flowRequest.setCompany_apply_type(companyApplyType.getValue());
        flowRequest.setApply_type(applyType.getValue());
        flowRequest.setBudget(applyOrderContract.getBudget());
        //判断审批流的类型
        if (applyOrderContract.getCost_attribution_category() != null) {
            if (StringUtils.isBlank(applyOrderContract.getCost_attribution_name()) || StringUtils.isBlank(applyOrderContract.getCost_attribution_id())) {
                return GlobalResponseCode.CostAttributionIdIsNull;
            }
        }
        //整理数据
        clearRefundChangeApplyData(applyContract);
        //待审核装填
        Date now = new Date();
        List<ApplyTripInfoContract> tripList = applyContract.getTrip_list();
        ApplyOrder apply = applyOrderContract.ToModel();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(applyOrderContract.getExceed_buy_desc_list())) {
            apply.setExceedBuyDesc(String.join(";", applyOrderContract.getExceed_buy_desc_list()));
        }
        if (applyOrderContract.getCost_attribution_category() != null) {
            if (StringUtils.isBlank(applyOrderContract.getCost_attribution_name()) || StringUtils.isBlank(applyOrderContract.getCost_attribution_id())) {
                return GlobalResponseCode.CostAttributionIdIsNull;
            }
            apply.setCostAttributionId(applyOrderContract.getCost_attribution_id());
            apply.setCostAttributionName(applyOrderContract.getCost_attribution_name());
            apply.setCostAttributionCategory(applyOrderContract.getCost_attribution_category());
        }
        EmployeeContract employee = iBaseOrganizationService.getEmployee(userId, companyId);
        if (employee != null) {
            apply.setApplicantName(employee.getName());
        }
        apply.setUpdateTime(now);
        apply.setCreateTime(now);

        String orderId = tripList.get(0).getOrder_id();
        Integer orderType = tripList.get(0).getType();
        String ticketIds = null;
        Integer type = apply.getType();
        Integer subType = applyOrderContract.getSub_type();
        if (type == ApplyType.Air.getValue() && subType == ApplySubType.Refund.getValue()) {
            Map<String, Object> tripOrderInfo = tripList.get(0).getTrip_order_info();
            ticketIds = ObjUtils.toString(tripOrderInfo.get("product_ids"));
        }
        if (type == ApplyType.Air.getValue() && subType == ApplySubType.Change.getValue()) {
            Map<String, Object> tripOrderInfo = tripList.get(0).getTrip_order_info();
            ticketIds = StringUtils.join((List<String>) tripOrderInfo.get("ticket_ids"), ",");
        }
        if (type == ApplyType.Train.getValue() && subType == ApplySubType.Refund.getValue()) {
            Map<String, Object> tripOrderInfo = tripList.get(0).getTrip_order_info();
            List<String> ticketIdList = ((List<String>) tripOrderInfo.get("ticket_ids"));
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(ticketIdList)) {
                ticketIds = ticketIdList.get(0);
            }
        }
        if (type == ApplyType.Train.getValue() && subType == ApplySubType.Change.getValue()) {
            Map<String, Object> tripOrderInfo = tripList.get(0).getTrip_order_info();
            List<String> ticketIdList = ((List<String>) tripOrderInfo.get("product_ids"));
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(ticketIdList)) {
                ticketIds = ticketIdList.get(0);
            }
        }
        // 退改审批事由id
        if (ObjUtils.isNotBlank(applyOrderContract.getApply_reason_id())) {
            tripList.get(0).getTrip_order_info().put("remark_id", applyOrderContract.getApply_reason_id());
        }
        Date overTime = disposeOverTime(orderId, orderType, userId, applyId, ticketIds, clientVersion);
        apply.setOvertime(overTime);
        //创建申请单
        apply.setId(applyId);
        apply.setRootApplyOrderId(applyId);
        applyOrderContract.setId(applyId);
        apply.setBillNo(applyV2Service.getBillNo(apply.getType().intValue(),companyId));
        applyMapper.insertSelective(apply);
        //保存行程信息
        insertRefundChangeTripContractList(tripList, applyId, now);
        applyContract.setOrder_info(disposeOrderInfo(orderId, orderType, userId, applyId, ticketIds, clientVersion));
        // 同步三方创建订单审批
        syncThirdRefundChangeCreate(applyContract, companyId, userId, now);
        return GlobalResponseCode.Success;
    }

    /**
     * 检查数据有效性
     *
     * @param applyContract
     * @param employeeId
     * @param companyId
     * @param clientVersion
     * @return
     */
    private GlobalResponseCode checkRefundChangeApplyData(ApplyRefundChangeContract applyContract, String employeeId, String companyId, String clientVersion) {
        //检查申请单
        ApplyOrderV2Contract apply = applyContract.getApply();
        if (apply == null) {
            return GlobalResponseCode.ApplyIsNull;
        }
        if (apply.getState() == null) {
            return GlobalResponseCode.ParameterError;
        }
        if (apply.getState().intValue() != ApplyStatus.PendingAudit.getValue()) {
            //如果申请单的状态不是草稿,就置为提交
            return GlobalResponseCode.ParameterError;
        }
        if (apply.getType() == null
                || ApplyType.valueOf(apply.getType()) == ApplyType.Unknown) {
            return GlobalResponseCode.ApplyTypeInvalid;
        }
        if (ApplyType.valueOf(apply.getType()) != ApplyType.Air && ApplyType.valueOf(apply.getType()) != ApplyType.Hotel && ApplyType.valueOf(apply.getType()) != ApplyType.Train) {
            return GlobalResponseCode.ApplyTypeInvalid;
        }
        //预计总金额
        if (apply.getBudget() != null && apply.getBudget().compareTo(BigDecimal.valueOf(0)) == -1) {
            return GlobalResponseCode.ApplyBudgetInvalid;
        }
        //校验退改类型
        if (apply.getSub_type() == null || ApplySubType.valueOf(apply.getSub_type()) == ApplySubType.Unknown) {
            return GlobalResponseCode.ApplyRefundChangeIsError;
        }
        ReasonType reasonType = null;
        if (ApplyType.valueOf(apply.getType()) == ApplyType.Air) {
            if (ApplySubType.valueOf(apply.getSub_type()) == ApplySubType.Refund) {
                reasonType = ReasonType.APPLY_REFUND_AIR;
            } else if (ApplySubType.valueOf(apply.getSub_type()) == ApplySubType.Change) {
                reasonType = ReasonType.APPLY_CHANGE_AIR;
            }
        } else if (ApplyType.valueOf(apply.getType()) == ApplyType.Hotel) {
            if (ApplySubType.valueOf(apply.getSub_type()) == ApplySubType.Refund) {
                reasonType = ReasonType.APPLY_REFUND_HOTEL;
            }
        } else if (ApplyType.valueOf(apply.getType()) == ApplyType.Train) {
            if (ApplySubType.valueOf(apply.getSub_type()) == ApplySubType.Refund) {
                reasonType = ReasonType.APPLY_REFUND_TRAIN;
            } else if (ApplySubType.valueOf(apply.getSub_type()) == ApplySubType.Change) {
                reasonType = ReasonType.APPLY_CHANGE_TRAIN;
            }
        }
        ReasonConfig reasonConfig = customReasonService.queryReasonConfigWithoutItems(companyId, reasonType);
        if (reasonConfig.getReason() == SaasMessageConstant.IS_CHECKED_TRUE) {
            if (StringUtils.isBlank(apply.getApply_reason())) {
                return GlobalResponseCode.ApplyReasonIsNull;
            }
            if (reasonConfig.getReason_desc() == SaasMessageConstant.IS_CHECKED_TRUE) {
                if (StringUtils.isBlank(apply.getApply_reason_desc())) {
                    return GlobalResponseCode.ApplyReasonDescIsNull;
                }
            }
        }
        if (apply.getApply_reason() != null && apply.getApply_reason().length() > 200) {
            //申请事由不能超过50字
            return GlobalResponseCode.ApplyReasonInvalid;
        }
        if (StringUtils.isNotBlank(apply.getApply_reason_desc()) && apply.getApply_reason_desc().length() > 200) {
            return GlobalResponseCode.ApplyReasonInvalid;
        }
        //审批类型 1.弹性审批流 2.固定审批流 3.分条件审批流
        CompanyApplyType applyType = CompanyApplyType.valueOf(apply.getFlow_type());
        if (applyType.getValue() == CompanyApplyType.Unknown.getValue()) {
            return GlobalResponseCode.ApplyFlowTypeError;
        }
        if (applyType.getValue() == CompanyApplyType.Elastic.getValue() && apply.getState() == ApplyStatus.PendingAudit.getValue() && StringUtils.isBlank(apply.getApprover_id())) {
            //申请单必须指定一个审批人
            return GlobalResponseCode.ApplyApproveIdInvalid;
        }
        if (applyType.getValue() == CompanyApplyType.Elastic.getValue() && employeeId.equals(apply.getApprover_id())) {
            //审批人不能是自己
            return GlobalResponseCode.ApplyApproverCannotBeSelf;
        }
        //检查行程
        List<ApplyTripInfoContract> trips = applyContract.getTrip_list();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(trips)) {
            //差旅和用车必须要有行程
            return GlobalResponseCode.ApplyTripIsNull;
        }
        if (trips.size() > 30) {
            //行程数据不超过30个
            return GlobalResponseCode.ApplyTripTooMuch;
        }
        for (ApplyTripInfoContract trip : trips) {
            trip.setStart_city_id("0");
            if (trip.getType() == null) {
                return GlobalResponseCode.ApplyTripTypeInvalid;
            }
            //检测行程单类型
            if (apply.getType() == ApplyType.ChaiLv.getValue()) {
                if (trip.getType() != BizType.Air.getValue()
                        && trip.getType() != BizType.Hotel.getValue()
                        && trip.getType() != BizType.Train.getValue()
                        && trip.getType() != BizType.IntlAir.getValue()) {
                    return GlobalResponseCode.ApplyTripNotTravel;
                }
            } else if (apply.getType() == ApplyType.Taxi.getValue()) {
                if (trip.getType() != BizType.Taxi.getValue()) {
                    return GlobalResponseCode.ApplyTripNotTaxi;
                }
            }
            Date startTime = null;
            //检测时间
            if (trip.getStart_time() == null) {
                return GlobalResponseCode.ApplyTripStartDateInvalid;
            }
            //检测地点
            if (trip.getStart_city_name() == null || trip.getStart_city_name().length() == 0) {
                return GlobalResponseCode.ApplyTripStartCityInvalid;
            }
            if (trip.getArrival_city_name() == null || trip.getArrival_city_name().length() == 0) {
                if (trip.getType() == BizType.Air.getValue() || trip.getType() == BizType.Train.getValue()) {
                    return GlobalResponseCode.ApplyTripArrivalCityInvalid;
                }
            }
            if (TripType.valueOf(trip.getTrip_type()) == TripType.Unknown) {
                trip.setTrip_type(TripType.oneWay.getValue());
            }
            if (trip.getType() == BizType.Air.getValue() && StringUtils.isBlank(trip.getEnd_time())) {
                return GlobalResponseCode.AirInvalidError;
            }
            if (trip.getType() == BizType.IntlAir.getValue()) {
                if (StringUtils.isBlank(trip.getAir_airline_name()) || StringUtils.isBlank(trip.getAir_flight_no()) || StringUtils.isBlank(trip.getAir_seat_msg())) {
                    return GlobalResponseCode.IntlAirInvalidError;
                }
                if (StringUtils.isBlank(trip.getEnd_time())) {
                    return GlobalResponseCode.IntlAirInvalidError;
                }
                if (trip.getTrip_type() == TripType.goAndBack.getValue()) {
                    if (StringUtils.isBlank(trip.getBack_start_time()) || StringUtils.isBlank(trip.getBack_end_time())) {
                        return GlobalResponseCode.IntlAirInvalidError;
                    }
                }
            }
        }
        validTrain(applyContract);
        return GlobalResponseCode.Success;
    }

    /**
     * 校验火车退改逻辑
     *
     * @param applyContract
     * @return
     */
    private void validTrain(ApplyRefundChangeContract applyContract) {
        ApplyOrderV2Contract apply = applyContract.getApply();
        Integer applyOrderType = apply.getApply_order_type();
        Integer subType = apply.getSub_type();
        Integer type = apply.getType();
        Map<String, Object> tripOrderInfo = applyContract.getTrip_list().get(0).getTrip_order_info();
        if (applyOrderType.equals(SaasFlowConstant.SETTING_TYPE_REFUND_CHANGE) && type == ApplyType.Train.getValue()) {
            String orderId = applyContract.getTrip_list().get(0).getOrder_id();
            if (subType == ApplySubType.Refund.getValue()) {
                List<String> ticketIdList = ((List<String>) tripOrderInfo.get("ticket_ids"));
                String ticketId = ticketIdList.get(0);
                logger.info("校验火车退订操作内容:" + JSON.toJSON(tripOrderInfo));
                String trainData = null;
                try {
                    trainData = HttpTool.get(URL_GET_TRAIN_REFUNG_VALID_ORDER_INFO + "?order_id=" + orderId + "&ticket_id=" + ticketId);
                } catch (Exception e) {
                    logger.info("火车退订校验报错:" + e.getMessage());
                }
                logger.info("校验火车退订操作结果:" + trainData);
                Map<String, Object> jo = JSONObject.parseObject(trainData, HashMap.class);
                Integer code = ObjUtils.toInteger(jo.get("code"));
                if (code != null && code.intValue() != 0) {
                    String msg = ObjUtils.toString(jo.get("msg"));
                    throw new SaasException(GlobalResponseCode.ApplyOrderIsError, msg);
                }
            } else {
                Map<String, String> header = Maps.newHashMap();
                header.put("Content-Type", "application/json;charset=utf-8");
                logger.info("校验火车改签操作内容:" + JSON.toJSON(tripOrderInfo));
                String trainData = null;
                try {
                    trainData = HttpTool.post(URL_GET_TRAIN_CHANGE_VALID_ORDER_INFO, tripOrderInfo, header);
                } catch (Exception e) {
                    logger.info("火车改签校验报错:" + e.getMessage());
                }
                logger.info("校验火车改签操作结果:" + trainData);
                Map<String, Object> jo = JSONObject.parseObject(trainData, HashMap.class);
                Integer code = ObjUtils.toInteger(jo.get("code"));
                if (code != null && code.intValue() != 0) {
                    String msg = ObjUtils.toString(jo.get("msg"));
                    throw new SaasException(GlobalResponseCode.ApplyOrderIsError, msg);
                }
            }
        }
    }

    /**
     * 整理申请单数据
     *
     * @param applyContract
     */
    private void clearRefundChangeApplyData(ApplyRefundChangeContract applyContract) {
        ApplyOrderV2Contract applyorderContract = applyContract.getApply();
        if (applyorderContract.getId() == null || applyorderContract.getId().length() == 0) {
            applyorderContract.setCheck_reason(null);
        }
        if (applyorderContract.getBudget() == null) {
            applyorderContract.setBudget(BigDecimal.valueOf(0));
        }
        if (org.apache.commons.collections.CollectionUtils.isEmpty(applyContract.getTrip_list())) {
            applyContract.setTrip_list(null);
        }
        List<Tuple<Date, String>> cityIds = new ArrayList<>();
        List<Date> timeRange = new ArrayList<>();
        for (ApplyTripInfoContract trip : applyContract.getTrip_list()) {
            Date startTime = DateTimeTool.fromStringToDateTime(trip.getStart_time());
            //这里把时间从String转到Date,再转回String,是需要用Date格式式字段串的显示,如输入为2017-2-27,格式化后变为2017-02-27
            if (startTime != null) {
                trip.setStart_time(DateTimeTool.fromDateTimeToString(startTime));
                timeRange.add(startTime);
            }
            Date endTime = DateTimeTool.fromStringToDateTime(trip.getEnd_time());
            if (endTime != null) {
                trip.setEnd_time(DateTimeTool.fromDateTimeToString(endTime));
                timeRange.add(endTime);
            }
            if (trip.getType() == BizType.Taxi.getValue() || trip.getType() == BizType.Hotel.getValue()) {
                trip.setArrival_city_id(null);
            }
            String tripCityId = trip.getStart_city_id();
            if (trip.getArrival_city_id() != null) {
                tripCityId += "-" + trip.getArrival_city_id();
            }
            cityIds.add(new Tuple<>(startTime, tripCityId));
        }
        //city_range格式:1000001-200002,2000002,300003-400004 意即:北京-上海,上海,天津-重庆
        cityIds.sort((m, n) -> (int) (m.getItem1().getTime() - n.getItem1().getTime()));
        List<String> cityIdArr = new ArrayList<>();
        for (Tuple<Date, String> cityId : cityIds) {
            cityIdArr.add(cityId.getItem2());
        }
        String cityIdVal = String.join(",", cityIdArr);
        applyorderContract.setCity_range(cityIdVal);
        //将所有出现过的时间正序排列,取最小值和最大值,重新写入timeRange字段,如果最小值=最大值,则只取最小值
        timeRange.sort((m, n) -> (int) (m.getTime() - n.getTime()));
        Date minTime = timeRange.get(0);
        Date maxTime = timeRange.get(timeRange.size() - 1);
        String desTimeRange = DateTimeTool.fromDateToString(minTime);
        if (maxTime.getTime() != minTime.getTime()) {
            desTimeRange += "," + DateTimeTool.fromDateToString(maxTime);
        }
        applyorderContract.setTime_range(desTimeRange);
        if (applyorderContract.getApprover_id() == null) {
            applyorderContract.setApprover_id("");
        }
    }

    /**
     * 处理超时时间
     *
     * @return
     */
    private Date disposeOverTime(String orderId, Integer orderType, String userId, String applyId, String ticketIds,String clientVersion) {
        try {
            if (orderType == BizType.Air.getValue()) {
                logger.info("获取国内机票退改订单详情:" + URL_GET_AIR_REFUNGCHANGE_ORDER_INFO + "?order_id=" + orderId + "&ticket_ids=" + ticketIds + "&apply_id=" + applyId);
                String airData = HttpTool.get(URL_GET_AIR_REFUNGCHANGE_ORDER_INFO + "?order_id=" + orderId + "&ticket_ids=" + ticketIds + "&apply_id=" + applyId);
                logger.info("获取国内机票退改订单详情结果:" + airData);
                Map<String, Object> jo = JSONObject.parseObject(airData, HashMap.class);
                Map<String, Object> dataMap = (Map<String, Object>) jo.get("data");
                Map<String, Object> airDataMap = (Map<String, Object>) dataMap.get("order");
                Map<String, Object> segmentInfo = (Map<String, Object>) airDataMap.get("segment_info");
                Long departureTimestamp = ObjUtils.toLong(segmentInfo.get("departure_timestamp"));
                Date overDate = new Date(departureTimestamp);
                Calendar c = Calendar.getInstance();
                c.setTime(overDate);
                c.add(Calendar.HOUR_OF_DAY, 4);
                Date overTime = c.getTime();
                //老版本判断是否大唐资源
                if (VersionTool.compare(clientVersion, "3.9.1") < 0){
                    if (SupplierType.DaTang.getKey()==airDataMap.get("supplier_id")){
                        throw new SaasException(GlobalResponseCode.APPLYFLOWDETAILERROR);
                    }
                }
                return overTime;
            } else if (orderType == BizType.Hotel.getValue()) {
                logger.info("获取酒店订单详情:" + URL_GET_HOTEL_ORDER_INFO + "?order_id=" + orderId + "&apply_id=" + applyId);
                String hotelData = HttpTool.get(URL_GET_HOTEL_ORDER_INFO + "?order_id=" + orderId + "&apply_id=" + applyId);
                logger.info("获取酒店订单详情结果:" + hotelData);
                Map<String, Object> jo = JSONObject.parseObject(hotelData, HashMap.class);
                Map<String, Object> dataMap = (Map<String, Object>) jo.get("data");
                Map<String, Object> hotelDataMap = (Map<String, Object>) dataMap.get("order");
                String lastCancelTime = ObjUtils.toString(hotelDataMap.get("last_cancel_time"));
                Calendar c = Calendar.getInstance();
                c.setTime(DateTimeTool.fromStringToDateTime(lastCancelTime));
                c.add(Calendar.MINUTE, -3);
                Date overTime = c.getTime();
                return overTime;
            } else if (orderType == BizType.Train.getValue()) {
                logger.info("获取火车订单详情:" + URL_GET_TRAIN_ORDER_INFO + "?order_id=" + orderId + "&apply_id=" + applyId);
                String trainData = HttpTool.get(URL_GET_TRAIN_ORDER_INFO + "?order_id=" + orderId + "&apply_id=" + applyId);
                logger.info("获取火车订单详情结果:" + trainData);
                Map<String, Object> jo = JSONObject.parseObject(trainData, HashMap.class);
                Map<String, Object> dataMap = (Map<String, Object>) jo.get("data");
                Map<String, Object> trainDataMap = (Map<String, Object>) dataMap.get("order");
                Map<String, Object> routeInfo = (Map<String, Object>) trainDataMap.get("routeInfo");
                String startTime = ObjUtils.toString(routeInfo.get("trainStartDate")) + " " + routeInfo.get("startTime") + ":00";
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd HH:mm:ss");
                Calendar c = Calendar.getInstance();
                c.setTime(sdf.parse(startTime));
                c.add(Calendar.MINUTE, -43);
                Date overTime = c.getTime();
                return overTime;
            }
        } catch (Exception e) {
            logger.info("订单审批获取订单信息异常：" + "orderId：" + orderId + " orderType：" + orderType + " 异常提示：" + e.getMessage());
            if (e instanceof SaasException){
                throw new SaasException(GlobalResponseCode.APPLYFLOWDETAILERROR);
            }
        }
        return null;
    }

    /**
     * 同步三方创建订单审批
     *
     * @param applyContract
     * @param companyId
     * @param userId
     * @param now
     */
    private void syncThirdRefundChangeCreate(ApplyRefundChangeContract applyContract, String companyId, String userId, Date now) {
        ApplyOrderV2Contract requestApply = applyContract.getApply();
        ApplyV2Contract createThirdParam = new ApplyV2Contract();
        // 审批信息
        ApplyOrderV2Contract apply = new ApplyOrderV2Contract();
        apply.setId(requestApply.getId());
        apply.setApply_order_type(requestApply.getApply_order_type());
        apply.setApply_reason_id(requestApply.getApply_reason_id());
        apply.setApply_reason(requestApply.getApply_reason());
        apply.setApply_reason_desc(requestApply.getApply_reason_desc());
        apply.setBudget(requestApply.getBudget().divide(new BigDecimal(100)));
        apply.setCompany_id(companyId);
        apply.setCreate_time(DateUtils.format(now));
        apply.setEmployee_id(userId);
        apply.setApplicant_name(requestApply.getApplicant_name());
        apply.setState(requestApply.getState());
        apply.setType(requestApply.getType());
        apply.setCost_attribution_category(requestApply.getCost_attribution_category());
        apply.setCost_attribution_id(requestApply.getCost_attribution_id());
        apply.setCost_attribution_name(requestApply.getCost_attribution_name());
        apply.setExceed_buy_type(requestApply.getExceed_buy_type());
        apply.setExceed_buy_desc(requestApply.getExceed_buy_desc());
        apply.setExceed_buy_desc_content(requestApply.getExceed_buy_desc_content());
        apply.setExceed_buy_desc_list(requestApply.getExceed_buy_desc_list());
        apply.setSub_type(requestApply.getSub_type());
        createThirdParam.setApply(apply);
        // 订单数据信息
        createThirdParam.setTrip_list(applyContract.getTrip_list());
        createThirdParam.setOrder_info(applyContract.getOrder_info());
        try {
            logger.info("同步三方创建退改审批参数:" + JSON.toJSONString(createThirdParam));
            String data = HttpClientUtils.postBody(URL_POST_APPLY_REFUND_CHANGE_ORDER_CREATE, JSON.toJSONString(createThirdParam));
            logger.info("同步三方创建退改审批返回结果:" + data);
        } catch (Exception e) {
            logger.error("同步三方创建退改审批异常:", e);
            throw new SaasException(FinhubMessageCode.EXCEPTION, CoreLanguage.Common_Exception_InternelError.getMessage());
        }
    }

    @Override
    public SaasResponseEntity refundChangeApprove(ThirdApplyApproveContract approveModel, String userId, String companyId, String ip) {
        if (approveModel == null) {
            throw new SaasException(GlobalResponseCode.ApplyIsNull);
        }
        String applyId = approveModel.getApply_id();
        if (StringTool.isNullOrEmpty(applyId)) {
            throw new SaasException(GlobalResponseCode.ApplyIdInvalid);
        }
        if (StringTool.isNullOrEmpty(approveModel.getThird_id())) {
            throw new SaasException(GlobalResponseCode.ApplyVirtualThirdIdIsNull);
        }
        if (approveModel.getComment() != null && approveModel.getComment().length() > 200) {
            throw new SaasException(GlobalResponseCode.ApplyCheckReasonInvalid);
        }
        ApplyOrder apply = applyMapper.selectByPrimaryKey(applyId);
        if (apply == null) {
            throw new SaasException(GlobalResponseCode.ApplyIsNull);
        }
        //加上排重逻辑
        GlobalResponseCode duplicateCode = duplicateThirdApply(companyId, approveModel.getThird_id(), apply.getApplyOrderType(), apply.getType());
        if (duplicateCode != GlobalResponseCode.Success) {
            return new SaasResponseEntity(duplicateCode, null);
        }
        if (apply.getState() != null && apply.getState().intValue() == ApplyStatus.Overtime.getValue()) {
            //事中审批已超时
            throw new SaasException(GlobalResponseCode.CenterApplyOverTime);
        }
        if (apply.getState() != null && apply.getState().intValue() == ApplyStatus.Backout.getValue()) {
            //事中审批已撤销
            throw new SaasException(GlobalResponseCode.CenterApplyCancleOrder);
        }
        if (!apply.getCompanyId().equals(companyId)) {
            //申请单和当前人不在一个公司
            throw new SaasException(GlobalResponseCode.ApplyCompanyCannotMatch);
        }
        if (apply.getState() != ApplyStatus.PendingAudit.getValue()) {
            //该单不是待审核,不能审批
            throw new SaasException(GlobalResponseCode.ApplyStatusNotPendingAudit);
        }
        // 更新状态及三方审批id
        ApplyOrder applyOrder = new ApplyOrder();
        applyOrder.setId(applyId);
        applyOrder.setThirdId(approveModel.getThird_id());
        applyOrder.setRealPrice(approveModel.getPrice());
        applyOrder.setState(ApplyStatus.Approved.getValue());
        applyOrder.setUpdateTime(new Date());
        applyMapper.updateByPrimaryKeySelective(applyOrder);
        apply.setRealPrice(approveModel.getReal_price());
        sloveRefundChange(apply);
        return new SaasResponseEntity(GlobalResponseCode.Success, null);
    }

    /**
     * @param applyOrder
     */
    private void sloveRefundChange(ApplyOrder applyOrder) {
        Integer applyOrderType = applyOrder.getApplyOrderType();
        Integer subType = applyOrder.getSubType();
        Integer type = applyOrder.getType();
        List<ApplyTripInfo> applyTripInfos = applyTripInfoExtMapper.queryAllTripListByApplyOrderId(applyOrder.getId());
        String tripContent = applyTripInfos.get(0).getTripContent();
        Map<String, Object> orderDataMap = JSON.parseObject(tripContent, Map.class);
        Map<String, Object> orderInfo = (Map<String, Object>) orderDataMap.get("order_info");
        try {
            if (applyOrderType == SaasFlowConstant.SETTING_TYPE_REFUND_CHANGE && type == ApplyType.Air.getValue()) {
                if (subType == ApplySubType.Refund.getValue()) {
                    Map<String, String> header = Maps.newHashMap();
                    header.put("Content-Type", "application/json;charset=utf-8");
                    logger.info("国内机票退订操作内容:" + JSON.toJSON(orderInfo));
                    String airData = HttpTool.post(URL_GET_AIR_REFUNG_ORDER_INFO, orderInfo, header);
                    logger.info("国内机票退订操作结果:" + airData);
                } else {
                    if (ObjUtils.isNotEmpty(applyOrder.getRealPrice())){
                        orderInfo.put("real_price",applyOrder.getRealPrice());
                    }
                    Map<String, String> header = Maps.newHashMap();
                    header.put("Content-Type", "application/json;charset=utf-8");
                    logger.info("国内机票改期操作内容:" + JSON.toJSON(orderInfo));
                    String airData = HttpTool.post(URL_GET_AIR_CHANGE_ORDER_INFO, orderInfo, header);
                    logger.info("国内机票改期操作结果:" + airData);
                }
            }
        } catch (Exception ex) {
            logger.info("退改订单异常信息:" + ex.getMessage());
        }
        if (applyOrderType == SaasFlowConstant.SETTING_TYPE_REFUND_CHANGE && type == ApplyType.Train.getValue()) {
            if (subType == ApplySubType.Refund.getValue()) {
                Map<String, String> header = Maps.newHashMap();
                header.put("Content-Type", "application/json;charset=utf-8");
                orderInfo.put("re_apply_id", applyOrder.getId());
                logger.info("火车退订操作内容:" + JSON.toJSON(orderInfo));
                String trainData = null;
                try {
                    trainData = HttpTool.post(URL_GET_TRAIN_REFUNG_ORDER_INFO, orderInfo, header);
                } catch (Exception e) {
                    logger.info("火车退订异常信息:" + e.getMessage());
                }
                logger.info("火车退订操作结果:" + trainData);
                Map<String, Object> jo = JSONObject.parseObject(trainData, HashMap.class);
                Integer code = ObjUtils.toInteger(jo.get("code"));
                if (code != null && code.intValue() != 0) {
                    String msg = ObjUtils.toString(jo.get("msg"));
                    throw new SaasException(GlobalResponseCode.ApplyOrderIsError, msg);
                }
            } else {
                Map<String, String> header = Maps.newHashMap();
                header.put("Content-Type", "application/json;charset=utf-8");
                orderInfo.put("re_apply_id", applyOrder.getId());
                logger.info("火车改签操作内容:" + JSON.toJSON(orderInfo));
                String trainData = null;
                try {
                    trainData = HttpTool.post(URL_GET_TRAIN_CHANGE_ORDER_INFO, orderInfo, header);
                } catch (Exception e) {
                    logger.info("火车改签异常信息:" + e.getMessage());
                }
                logger.info("火车改签操作结果:" + trainData);
                Map<String, Object> jo = JSONObject.parseObject(trainData, HashMap.class);
                Integer code = ObjUtils.toInteger(jo.get("code"));
                if (code != null && code.intValue() != 0) {
                    String msg = ObjUtils.toString(jo.get("msg"));
                    throw new SaasException(GlobalResponseCode.ApplyOrderIsError, msg);
                }
            }
        }
        try {
            if (applyOrderType == SaasFlowConstant.SETTING_TYPE_REFUND_CHANGE && type == ApplyType.Hotel.getValue()) {
                if (subType == ApplySubType.Refund.getValue()) {
                    Map<String, String> header = Maps.newHashMap();
                    header.put("Content-Type", "application/json;charset=utf-8");
                    orderInfo.put("refund_apply_id", applyOrder.getId());
                    orderInfo.put("employee_id", applyOrder.getEmployeeId());
                    orderInfo.put("company_id", applyOrder.getCompanyId());
                    logger.info("酒店退订操作内容:" + JSON.toJSON(orderInfo));
                    String hotelData = HttpTool.post(URL_GET_HOTEL_REFUNG_ORDER_INFO, orderInfo, header);
                    logger.info("酒店退订操作结果:" + hotelData);
                }
            }
        } catch (Exception ex) {
            logger.info("退改订单异常信息:" + ex.getMessage());
        }
    }

    private void insertRefundChangeTripContractList(List<ApplyTripInfoContract> tripList, String applyId, Date now) {
        if (tripList != null) {
            for (ApplyTripInfoContract tripContract : tripList) {
                ApplyTripInfo trip = new ApplyTripInfo();
                String applicateId = IDTool.CreateUniqueID();
                String id = IDTool.CreateUniqueID();
                trip.setId(id);
                trip.setApplyOrderId(applyId);
                trip.setCreateTime(now);
                trip.setType(tripContract.getType());
                trip.setArrivalCityId(tripContract.getArrival_city_id());
                trip.setStartCityId(tripContract.getStart_city_id());
                trip.setState(ApplyTripStatus.Available.getValue());
                trip.setStartTime(DateTimeTool.fromStringToDateTime(tripContract.getStart_time()));
                trip.setEndTime(DateTimeTool.fromStringToDateTime(tripContract.getEnd_time()));
                trip.setBackStartTime(DateTimeTool.fromStringToDateTime(tripContract.getBack_start_time()));
                trip.setBackEndTime(DateTimeTool.fromStringToDateTime(tripContract.getBack_end_time()));
                if (tripContract.getEstimated_amount() != null && tripContract.getEstimated_amount().compareTo(BigDecimal.valueOf(0)) != -1) {
                    trip.setEstimatedAmount(tripContract.getEstimated_amount());
                }
                if (tripContract.getEnd_time() != null && tripContract.getEnd_time().length() > 0) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd KK:mm");
                    try {
                        trip.setEndTime(sdf.parse(tripContract.getEnd_time()));
                    } catch (ParseException e) {
                        logger.error("解析时间报错" + e.getMessage());
                        e.printStackTrace();
                    }
                }
                trip.setUpdateTime(now);
                trip.setStartCityName(tripContract.getStart_city_name());
                trip.setArrivalCityName(tripContract.getArrival_city_name());
                trip.setTitle(tripContract.getTitle());
                trip.setContent(tripContract.getContent());
                trip.setTripApplicateCurrentId(applicateId);
                trip.setTripType(tripContract.getTrip_type());
                JSONObject jo = new JSONObject();
                //机票航空公司名称
                if (StringUtils.isNotBlank(tripContract.getAir_airline_name())) {
                    jo.put("air_airline_name", tripContract.getAir_airline_name());
                }
                //机票航班号
                if (StringUtils.isNotBlank(tripContract.getAir_flight_no())) {
                    jo.put("air_flight_no", tripContract.getAir_flight_no());
                }
                //机票舱位名称
                if (StringUtils.isNotBlank(tripContract.getAir_seat_msg())) {
                    jo.put("air_seat_msg", tripContract.getAir_seat_msg());
                }
                //返程机票航空公司名称
                if (StringUtils.isNotBlank(tripContract.getBack_air_airline_name())) {
                    jo.put("back_air_airline_name", tripContract.getBack_air_airline_name());
                }
                //返程机票航班号
                if (StringUtils.isNotBlank(tripContract.getBack_air_flight_no())) {
                    jo.put("back_air_flight_no", tripContract.getBack_air_flight_no());
                }
                //返程机票舱位名称
                if (StringUtils.isNotBlank(tripContract.getBack_air_seat_msg())) {
                    jo.put("back_air_seat_msg", tripContract.getBack_air_seat_msg());
                }
                //机票折扣信息
                if (tripContract.getAir_discount() != null) {
                    jo.put("air_discount", tripContract.getAir_discount());
                }
                //酒店床型名称
                if (StringUtils.isNotBlank(tripContract.getBed_type())) {
                    jo.put("bed_type", tripContract.getBed_type());
                }
                //酒店预订间数
                if (tripContract.getRoom_count() != null) {
                    jo.put("room_count", tripContract.getRoom_count());
                }
                //乘机人/乘车人/入住人
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(tripContract.getTravel_partner())) {
                    jo.put("travel_partner", tripContract.getTravel_partner());
                }
                jo.put("order_info", tripContract.getTrip_order_info());
                trip.setTripContent(jo.toJSONString());
                applyTripMapper.insert(trip);

                ApplyTripApplicate applicateModel = new ApplyTripApplicate();
                applicateModel.setId(applicateId);
                applicateModel.setOrderId(tripContract.getOrder_id());
                applicateModel.setAction(ApplyTripApplicateAction.Applicate.getValue());
                applicateModel.setApplyId(applyId);
                applicateModel.setCreateTime(now);
                applicateModel.setApplyTripId(id);
                applicateModel.setOrderProductType(1);
                applicateModel.setComment(null);
                applicateModel.setUseTime(now);
                applyTripApplicateMapper.insert(applicateModel);
            }
        }
    }

    /**
     * 整合订单审批的订单信息
     */
    private JSONObject disposeOrderInfo(String orderId, Integer orderType, String userId, String applyId, String ticketIds,String clientHeadVersion) {
        Object contactName = null;
        String contactPhone = null;
        Object orderIdInfo = null;
        Object remarkReason = null;
        Object remarkDetail = null;
        Object orderPerson = null;
        Object orderPersonUserId = null;
        Object costAttributionName = null;
        JSONObject orderInfo = new JSONObject();
        try {
            if (orderType == BizType.Air.getValue()) {
                logger.info("获取国内机票退改订单详情:" + URL_GET_AIR_REFUNGCHANGE_ORDER_INFO + "?order_id=" + orderId + "&ticket_ids=" + ticketIds + "&apply_id=" + applyId);
                String airData = HttpTool.get(URL_GET_AIR_REFUNGCHANGE_ORDER_INFO + "?order_id=" + orderId + "&ticket_ids=" + ticketIds + "&apply_id=" + applyId);
                logger.info("获取国内机票退改订单详情结果:" + airData);
                Map<String, Object> jo = JSONObject.parseObject(airData, HashMap.class);
                Map<String, Object> dataMap = (Map<String, Object>) jo.get("data");
                Map<String, Object> airDataMap = (Map<String, Object>) dataMap.get("order");
                List<Map<String, Object>> newAirDataMap = (List<Map<String, Object>>) dataMap.get("new_orders");
                if (org.apache.commons.collections.CollectionUtils.isEmpty(newAirDataMap)) {
                    newAirDataMap = Lists.newArrayList();
                }
                orderInfo.put("new_order", newAirDataMap);
                contactName = airDataMap.get("contact_name");
                contactPhone = ObjUtils.toString(airDataMap.get("contact_phone"));
                orderIdInfo = airDataMap.get("order_id");
                remarkReason = airDataMap.get("remark_reason");
                remarkDetail = airDataMap.get("remark_detail");
                costAttributionName = airDataMap.get("cost_attribution");
                Map<String, Object> orderPersonInfo = (Map<String, Object>) airDataMap.get("order_owner");
                orderPerson = orderPersonInfo.get("name");
                orderPersonUserId = userId;


                //整合国内机票信息
                JSONObject airInfo = new JSONObject();
                Map<String, Object> segmentInfo = (Map<String, Object>) airDataMap.get("segment_info");
                Map<String, Object> priceInfo = (Map<String, Object>) airDataMap.get("price_info");
                Map<String, Object> stipulateInfo = (Map<String, Object>) airDataMap.get("stipulate_info");
                List<Map<String, Object>> insuranceInfo = (List<Map<String, Object>>) airDataMap.get("insurance_info");
                List<Map<String, Object>> priceDetail = (List<Map<String, Object>>) airDataMap.get("price_detail");
                List<Map<String, Object>> passengerList = (List<Map<String, Object>>) airDataMap.get("passenger_list");
                //行程信息
                airInfo.put("segment_info", segmentInfo);
                airInfo.put("stipulate_info", stipulateInfo);
                airInfo.put("insurance_info", insuranceInfo);
                airInfo.put("price_detail", priceDetail);
                airInfo.put("passenger_list", passengerList);
                //价格信息
                airInfo.put("price_info", priceInfo);
                List<Map<String, Object>> customRemark = (List<Map<String, Object>>) airDataMap.get("custom_remark");
                //如果是大唐资源，那么支持在线改期
                boolean support_online_change=false;
                if (SupplierType.DaTang.getKey()==airDataMap.get("supplier_id")){
                    support_online_change=true;
                }
                if (support_online_change&&ObjUtils.isNotEmpty(clientHeadVersion)){
                    if (VersionTool.compare(clientHeadVersion, "3.9.1") < 0){
                        if (SupplierType.DaTang.getKey()==airDataMap.get("supplier_id")){
                            throw new SaasException(GlobalResponseCode.APPLYFLOWDETAILERROR);
                        }
                    }
                }
                orderInfo.put("support_online_change", support_online_change);
                orderInfo.put("contact_name", contactName);
                orderInfo.put("contact_phone", contactPhone.replace(contactPhone.substring(3, 7), "****"));
                orderInfo.put("order_id", orderIdInfo);
                orderInfo.put("remark_reason", remarkReason);
                orderInfo.put("remark_detail", remarkDetail);
                orderInfo.put("custom_remark", customRemark);
                orderInfo.put("order_person", orderPerson);
                orderInfo.put("order_person_user_id", orderPersonUserId);
                orderInfo.put("cost_attribution_name", costAttributionName);
                orderInfo.put("air_info", airInfo);
            } else if (orderType == BizType.Hotel.getValue()) {
                logger.info("获取酒店订单详情:" + URL_GET_HOTEL_ORDER_INFO + "?order_id=" + orderId + "&apply_id=" + applyId);
                String hotelData = HttpTool.get(URL_GET_HOTEL_ORDER_INFO + "?order_id=" + orderId + "&apply_id=" + applyId);
                logger.info("获取酒店订单详情结果:" + hotelData);
                Map<String, Object> jo = JSONObject.parseObject(hotelData, HashMap.class);
                Map<String, Object> dataMap = (Map<String, Object>) jo.get("data");
                Map<String, Object> hotelDataMap = (Map<String, Object>) dataMap.get("order");
                List<Map<String, Object>> newHotelDataMap = (List<Map<String, Object>>) dataMap.get("new_orders");
                if (org.apache.commons.collections.CollectionUtils.isEmpty(newHotelDataMap)) {
                    newHotelDataMap = Lists.newArrayList();
                }
                orderInfo.put("new_order", newHotelDataMap);
                contactName = hotelDataMap.get("contact_name");
                contactPhone = ObjUtils.toString(hotelDataMap.get("contact_phone_no"));
                orderIdInfo = hotelDataMap.get("order_id");
                remarkReason = hotelDataMap.get("remark");
                remarkDetail = hotelDataMap.get("remark_detail");
                orderPerson = hotelDataMap.get("order_person");
                orderPersonUserId = userId;
                costAttributionName = hotelDataMap.get("cost_attribution");

                //整合酒店信息
                JSONObject hotelInfo = new JSONObject();
                //入店日期
                Object checkinDate = hotelDataMap.get("checkin_date");
                //离店日期
                Object checkoutDate = hotelDataMap.get("checkout_date");
                //城市码
                Object cityCode = hotelDataMap.get("city_code");
                //城市名
                Object cityName = hotelDataMap.get("city_name");
                //酒店地址
                Object hotelAddress = hotelDataMap.get("hotel_address");
                //酒店名称
                Object hotelName = hotelDataMap.get("hotel_name");
                //酒店电话
                Object hotelPhone = hotelDataMap.get("hotel_phone");
                //床型名称
                Object bedType = hotelDataMap.get("bed_type");
                //房型名称
                Object roomName = hotelDataMap.get("room_name");
                //预订间数
                Object roomCount = hotelDataMap.get("room_count");
                //规则名称
                Object priceRuleTag = hotelDataMap.get("price_rule_tag");
                //价格计划名称
                Object planName = hotelDataMap.get("plan_name");
                //规则
                Object priceRule = hotelDataMap.get("price_rule");
                //保险信息
                List<Map<String, Object>> insuranceInfo = (List<Map<String, Object>>) hotelDataMap.get("insurance_info");
                //酒店同行人
                List<Map<String, Object>> guests = (List<Map<String, Object>>) hotelDataMap.get("guests");
                if (ObjUtils.isNotEmpty(guests)){
                    List liveWithList= guests.stream().filter(v->{
                        if (ObjUtils.isNotEmpty(v.get("live_with"))){
                            return true;
                        }
                        return false;
                    }).collect(Collectors.toList());
                    if (ObjUtils.isNotEmpty(clientHeadVersion)&&VersionTool.compare(clientHeadVersion, "3.9.3") < 0&&ObjUtils.isNotEmpty(liveWithList)&&liveWithList.size()>0){
                        throw new SaasException(GlobalResponseCode.ApplyCenterAlert);
                    }
                }
                //早餐信息
                Map<String, Object> breakfastPair = (Map<String, Object>) hotelDataMap.get("breakfast_pair");

                hotelInfo.put("checkin_date", checkinDate);
                hotelInfo.put("checkout_date", checkoutDate);
                hotelInfo.put("city_code", cityCode);
                hotelInfo.put("city_name", cityName);
                hotelInfo.put("hotel_address", hotelAddress);
                hotelInfo.put("hotel_name", hotelName);
                hotelInfo.put("hotel_phone", hotelPhone);
                hotelInfo.put("bed_type", bedType);
                hotelInfo.put("room_name", roomName);
                hotelInfo.put("room_count", roomCount);
                hotelInfo.put("price_rule_tag", priceRuleTag);
                hotelInfo.put("price_rule", priceRule);
                hotelInfo.put("plan_name", planName);
                hotelInfo.put("insurance_info", insuranceInfo);
                hotelInfo.put("guests", guests);
                hotelInfo.put("breakfast_pair", breakfastPair);
                orderInfo.put("hotel_info", hotelInfo);

                List<Map<String, Object>> customRemark = (List<Map<String, Object>>) hotelDataMap.get("custom_remark");
                orderInfo.put("contact_name", contactName);
                orderInfo.put("contact_phone", contactPhone.replace(contactPhone.substring(3, 7), "****"));
                orderInfo.put("order_id", orderIdInfo);
                orderInfo.put("remark_reason", remarkReason);
                orderInfo.put("remark_detail", remarkDetail);
                orderInfo.put("custom_remark", customRemark);
                orderInfo.put("order_person", orderPerson);
                orderInfo.put("order_person_user_id", orderPersonUserId);
                orderInfo.put("cost_attribution_name", costAttributionName);
                orderInfo.put("hotel_info", hotelInfo);
            } else if (orderType == BizType.Train.getValue()) {
                logger.info("获取火车订单详情:" + URL_GET_TRAIN_ORDER_INFO + "?order_id=" + orderId + "&apply_id=" + applyId + "&ticket_id=" + ticketIds);
                String trainData = HttpTool.get(URL_GET_TRAIN_ORDER_INFO + "?order_id=" + orderId + "&apply_id=" + applyId + "&ticket_id=" + ticketIds);
                logger.info("获取火车订单详情结果:" + trainData);
                Map<String, Object> jo = JSONObject.parseObject(trainData, HashMap.class);
                Map<String, Object> dataMap = (Map<String, Object>) jo.get("data");
                Map<String, Object> trainDataMap = (Map<String, Object>) dataMap.get("order");
                List<Map<String, Object>> newTrainDataMap = (List<Map<String, Object>>) dataMap.get("new_orders");
                if (org.apache.commons.collections.CollectionUtils.isEmpty(newTrainDataMap)) {
                    newTrainDataMap = Lists.newArrayList();
                }
                orderInfo.put("new_order", newTrainDataMap);
                List<Map<String, Object>> tickets = (List<Map<String, Object>>) trainDataMap.get("tickets");
                for (Map<String, Object> ticketsMap : tickets) {
                    Map<String, Object> passengerInfo = (Map<String, Object>) ticketsMap.get("passengerInfo");
                    String phoneNum = ObjUtils.toString(passengerInfo.get("phone_num"));
                    if (StringUtils.isNotBlank(phoneNum)) {
                        passengerInfo.put("phone_num", phoneNum.replace(phoneNum.substring(3, 7), "****"));
                    }
                }
                Map<String, Object> costAttribution = (Map<String, Object>) trainDataMap.get("costAttribution");
                costAttributionName = costAttribution.get("name");
                Map<String, Object> contactInfo = (Map<String, Object>) trainDataMap.get("contact");
                contactName = contactInfo.get("name");
                contactPhone = ObjUtils.toString(contactInfo.get("phone"));
                orderIdInfo = trainDataMap.get("orderId");
                Map<String, Object> bookingPerson = (Map<String, Object>) trainDataMap.get("bookingPerson");
                orderPerson = bookingPerson.get("name");
                Object totalPrice = trainDataMap.get("totalPrice");
                orderPersonUserId = userId;
                //整合火车信息
                JSONObject trainInfo = new JSONObject();
                Map<String, Object> routeInfo = (Map<String, Object>) trainDataMap.get("routeInfo");
                //行程信息
                trainInfo.put("routeInfo", routeInfo);
                trainInfo.put("totalPrice", totalPrice);
                trainInfo.put("tickets", tickets);
                orderInfo.put("contact_name", contactName);
                orderInfo.put("contact_phone", contactPhone.replace(contactPhone.substring(3, 7), "****"));
                orderInfo.put("order_id", orderIdInfo);
                orderInfo.put("remark_reason", remarkReason);
                orderInfo.put("remark_detail", remarkDetail);
                orderInfo.put("order_person", orderPerson);
                orderInfo.put("order_person_user_id", orderPersonUserId);
                orderInfo.put("cost_attribution_name", costAttributionName);
                orderInfo.put("train_info", trainInfo);
            }
        } catch (Exception e) {
            logger.info("订单审批获取订单信息异常：" + "orderId：" + orderId + " orderType：" + orderType + " 异常提示：" + e.getMessage());
            if (e instanceof SaasException){
                throw new SaasException(GlobalResponseCode.APPLYFLOWDETAILERROR);
            }
        }
        return orderInfo;
    }

    /**
     * 是否是对接差旅审批公司
     *
     * @param companyId
     * @return
     */
    @Override
    public boolean isApplyDinnerOrderDockingCompany(String companyId) {
        Map<String, Object> dataMap = queryCompanyDockingInfo(companyId);
        return ObjUtils.toBoolean(MapUtils.getBoolean(dataMap, "company_apply_meishi"), false);
    }

    /**
     * 对接用餐审批
     * @param token
     * @param applyContract
     * @param userId
     * @param companyId
     * @param ip
     * @param applyId
     * @param clientVersion
     * @return
     */
    @Override
    public GlobalResponseCode dinnerApplyCreate(String token, ApplyV2Contract applyContract, String userId, String companyId, String ip, String applyId, String clientVersion) {
        GlobalResponseCode code = checkDinnerApplyData(applyContract, userId, companyId, clientVersion);
        if (code != GlobalResponseCode.Success) {
            throw new SaasException(code);
        }
        ApplyOrderV2Contract applyorderContract = applyContract.getApply();
        applyorderContract.setEmployee_id(userId);
        applyorderContract.setCompany_id(companyId);
        CompanyApplyType companyApplyType = CompanyApplyType.valueOf(applyorderContract.getFlow_type());
        if (companyApplyType == CompanyApplyType.Unknown) {
            companyApplyType = CompanyApplyType.Elastic;
        }
        applyorderContract.setFlow_type(companyApplyType.getValue());
        Integer settingType = SaasFlowConstant.SETTING_TYPE_DINNER;
        //默认非变更单
        String rootApplyId = applyId;
        String parentApplyId = null;
        Date now = new Date();
        ApplyOrder apply = applyorderContract.ToModel();
        //处理超规类型
        if (ExceedBuyType.valueOf(apply.getExceedBuyType()) == ExceedBuyType.Unknown) {
            apply.setExceedBuyType(ExceedBuyType.UnSupernormal.getValue());
        }
        if (applyorderContract.getCost_attribution_category() != null && applyorderContract.getCost_attribution_category() == SettingType.Project.getValue()) {
            if (StringUtils.isBlank(applyorderContract.getCost_attribution_name()) || StringUtils.isBlank(applyorderContract.getCost_attribution_id())) {
                throw new SaasException(GlobalResponseCode.CostAttributionIdIsNull);
            }
            apply.setCostAttributionId(applyorderContract.getCost_attribution_id());
            apply.setCostAttributionName(applyorderContract.getCost_attribution_name());
            apply.setCostAttributionCategory(applyorderContract.getCost_attribution_category());
        } else {
            apply.setCostAttributionCategory(SettingType.Department.getValue());
        }
        apply.setApplyOrderType(settingType);
        apply.setUpdateTime(now);
        apply.setCreateTime(now);
        EmployeeContract employee = iBaseOrganizationService.getEmployee(userId, companyId);
        if (employee != null) {
            apply.setApplicantName(employee.getName());
            applyorderContract.setApplicant_name(employee.getName());
            applyorderContract.setId(applyId);
        }
        //创建申请单
        apply.setId(applyId);
        apply.setRootApplyOrderId(rootApplyId);
        apply.setParentApplyOrderId(parentApplyId);
        apply.setIsChangeApply(false);
        apply.setChangeReason(applyorderContract.getChange_reason());
        apply.setChangeReasonDesc(applyorderContract.getChange_reason_desc());
        applyorderContract.setId(applyId);
        apply.setBillNo(applyV2Service.getBillNo(apply.getType().intValue(),companyId));
        applyMapper.insertSelective(apply);
        //保存行程信息
        insertDinnerTripContract(applyContract.getTrip_list(), applyId, now);
        //保存审批单中订单的费用归属信息
        sloveApplyCostAttribution(applyorderContract, companyId, applyId, applyorderContract.getBring_in());
        try {
            logger.info("同步三方用餐审批请求参数：{}", JsonUtils.toJson(applyContract));
            String data = HttpClientUtils.postBody(URL_POST_APPLY_DINNER_ORDER_CREATE, JSON.toJSONString(applyContract));
            logger.info("同步三方创建用餐审批返回结果:" + data);
        } catch (Exception e) {
            logger.error("同步三方创建用餐审批异常:", e);
            throw new SaasException(FinhubMessageCode.EXCEPTION, CoreLanguage.Common_Exception_InternelError.getMessage());
        }
        return GlobalResponseCode.Success;
    }

    /**
     * 检查数据有效性
     *
     * @param applyContract
     * @return
     */
    private GlobalResponseCode checkDinnerApplyData(ApplyV2Contract applyContract, String employeeId, String companyId, String clientVersion) {
        //检查申请单
        ApplyOrderV2Contract apply = applyContract.getApply();
        if (apply.getIs_self_travel() == null) {
            apply.setIs_self_travel(false);
        }
        if (apply.getTravel_day() == null) {
            apply.setTravel_day(BigDecimal.valueOf(0));
        }
        if (apply == null) {
            return GlobalResponseCode.ApplyIsNull;
        }
        if (apply.getType() == null || apply.getType() != ApplyType.Meishi.getValue()) {
            return GlobalResponseCode.ApplyTypeInvalid;
        }
        String applyId = apply.getId();
        if (applyId != null && applyId.length() > 0 && applyId.length() != 24) {
            return GlobalResponseCode.ApplyIdInvalid;
        }
        ReasonConfig reasonConfig = customReasonService.queryReasonConfigWithoutItems(companyId, ReasonType.APPLY_MEISHI);
        if (reasonConfig.getReason() == SaasMessageConstant.IS_CHECKED_TRUE) {
            if (StringUtils.isBlank(apply.getApply_reason())) {
                return GlobalResponseCode.ApplyReasonIsNull;
            }
            if (reasonConfig.getReason_desc() == SaasMessageConstant.IS_CHECKED_TRUE) {
                if (StringUtils.isBlank(apply.getApply_reason_desc())) {
                    return GlobalResponseCode.ApplyReasonDescIsNull;
                }
            }
        }
        ApplySetupContract applySetupContract = messageSetupService.queryCompanyApplyConfig(companyId);
        Integer state = apply.getState();
        if (state != ApplyStatus.Draft.getValue()) {
            if (apply.getFlow_cc_type() == null || CcNoticeType.valueOf(apply.getFlow_cc_type()) == CcNoticeType.Unknown) {
                return GlobalResponseCode.CCNoticeTypeInvalid;
            }
            ApplySetupContract applySetupCostAttributionContract = messageSetupService.queryCompanyApplyCostAttributionConfig(companyId);
            Integer costAttributionCategory = applySetupCostAttributionContract.getCost_attribution_category();
            Integer applyCostAttributionCategory = apply.getCost_attribution_category();
            if (applyCostAttributionCategory == null) {
                applyCostAttributionCategory = CostAttributionCategory.Dept.getKey();
            }
            if (costAttributionCategory == CostAttributionCategory.Dept.getKey() && applyCostAttributionCategory != CostAttributionCategory.Dept.getKey()) {
                return GlobalResponseCode.CostAttributionCategoryIsDept;
            }
            if (costAttributionCategory == CostAttributionCategory.CostCenter.getKey() && applyCostAttributionCategory != CostAttributionCategory.CostCenter.getKey()) {
                return GlobalResponseCode.CostAttributionCategoryIsCostCenter;
            }
            //校验费用归属是否与审批一致
            boolean costAttributionCategoryAccordanceFlag = isCostAttributionCategoryAccordance(applyContract, companyId);
            if (costAttributionCategoryAccordanceFlag) {
                //需要一致
                applyContract.getApply().setBring_in(getBringIn(companyId, apply.getType()));
                logger.info("Bring_in flag==true :{}", getBringIn(companyId, apply.getType()));
                if (!checkCostAttributionCategory(applyContract, companyId).equals(GlobalResponseCode.Success)) {
                    return GlobalResponseCode.CostAttributionCategoryError;
                }
            }
            if (VersionTool.greaterThanOrEqualTo(clientVersion, "4.2.0") && apply.getType() != ApplyType.Mall.getValue()) {
                if (!costAttributionCategoryAccordanceFlag) {
                    //审批单费用归属是否必填
                    Integer applyAttributionCategoryLimit = applySetupContract.getApply_attribution_category_meishi();
                    if (applyAttributionCategoryLimit == null || applyAttributionCategoryLimit == ApplyCategoryEnum.IS_SHOW_MUST.getValue()) {
                        apply.setCost_attribution_list(Lists.newArrayList());
                    }
                    if (applyAttributionCategoryLimit != null && applyAttributionCategoryLimit == ApplyCategoryEnum.IS_CHECKED_MUST.getValue()) {
                        /*ApplySetupContract orderSetupCostAttributionContract = messageSetupService.queryCompanyCostAttributionConfig(companyId);
                        //获取订单费用归属范围
                        Integer orderCostAttributionCategory = orderSetupCostAttributionContract.getCost_attribution_category();
                        List<CostAttributionInfo> costAttributionList = apply.getCost_attribution_list();
                        if (CollectionUtils.isEmpty(costAttributionList)) {
                            return GlobalResponseCode.ApplyOrderCostAttritutionIsNull;
                        }
                        if (orderCostAttributionCategory != null && orderCostAttributionCategory != CostAttributionScopeEnum.DEPT_AND_PROJ.getCode()) {
                            if (CollectionUtils.isEmpty(costAttributionList) || costAttributionList.size() != 1) {
                                return GlobalResponseCode.ApplyOrderCostAttritutionIsError;
                            }
                            if (orderCostAttributionCategory == CostAttributionScopeEnum.DEPT.getCode() && costAttributionList.get(0).getCost_attribution_category() != CostAttributionScopeEnum.DEPT.getCode()) {
                                return GlobalResponseCode.CostAttributionCategoryIsDept;
                            }
                            if (orderCostAttributionCategory == CostAttributionScopeEnum.PROJ.getCode() && costAttributionList.get(0).getCost_attribution_category() != CostAttributionScopeEnum.PROJ.getCode()) {
                                return GlobalResponseCode.CostAttributionCategoryIsCostCenter;
                            }
                        }
                        if (orderCostAttributionCategory != null && orderCostAttributionCategory == CostAttributionScopeEnum.DEPT_AND_PROJ.getCode() && costAttributionList.size() != 2) {
                            return GlobalResponseCode.ApplyOrderCostAttritutionIsError;
                        }*/
                        Integer bringIn = applySetupContract.getApply_attribution_category_modifiable_meishi();
                        logger.info("Bring_in flag==false :{}", bringIn);
                        apply.setBring_in(bringIn);
                    }
                } else {
                    //一致校验
                    //需要一致
                    applyContract.getApply().setBring_in(getBringIn(companyId, apply.getType()));
                    logger.info("Bring_in flag==true 2 :{}", getBringIn(companyId, apply.getType()));
                    if (!checkCostAttributionCategory(applyContract, companyId).equals(GlobalResponseCode.Success)) {
                        return GlobalResponseCode.CostAttributionCategoryError;
                    }
                }
            }
        }
        apply.setTravel_day(BigDecimal.ZERO);
        apply.setTravel_time_list(null);
        if (apply.getApply_reason() != null && apply.getApply_reason().length() > 100) {
            //采购申请事由不能超过100字
            return GlobalResponseCode.ApplyMallReasonInvalid;
        }
        if (apply.getApply_reason_desc() != null && apply.getApply_reason_desc().length() > 500) {
            //事由补充说明不能超过500字
            return GlobalResponseCode.ApplyReasonDescInvalid;
        }
        if (StringUtils.isNotBlank(apply.getApply_reason_desc())) {
            apply.setApply_reason_desc(apply.getApply_reason_desc().replaceAll("\u0000", ""));
        }
        if (apply.getBudget() != null && apply.getBudget().compareTo(BigDecimal.valueOf(0)) == -1) {
            return GlobalResponseCode.ApplyBudgetInvalid;
        }
        if (apply.getTravel_price_detail() != null && apply.getTravel_price_detail().length() > 200) {
            //事由补充说明不能超过200字
            return GlobalResponseCode.ApplyTranvelPriceDetailInvalid;
        }
        if (apply.getState() == null) {
            return GlobalResponseCode.ApplyStatusRequired;
        }
        CompanyApplyType applyType = CompanyApplyType.valueOf(apply.getFlow_type());
        if (applyType.getValue() == CompanyApplyType.Unknown.getValue()) {
            return GlobalResponseCode.ApplyFlowTypeError;
        }
        if (applyType.getValue() == CompanyApplyType.Elastic.getValue() && apply.getState() == ApplyStatus.PendingAudit.getValue() && (apply.getApprover_id() == null || apply.getApprover_id().length() == 0)) {
            //申请单必须指定一个审批人
            return GlobalResponseCode.ApplyApproveIdInvalid;
        }
        if (applyType.getValue() == CompanyApplyType.Elastic.getValue() && employeeId.equals(apply.getApprover_id())) {
            //审批人不能是自己
            return GlobalResponseCode.ApplyApproverCannotBeSelf;
        }
        //检查行程
        List<ApplyTripInfoContract> trips = applyContract.getTrip_list();
        if (CollectionUtils.isEmpty(trips)) {
            return GlobalResponseCode.ApplyDinnerTripIsNull;
        }
        if (trips.size() > 1) {
            return GlobalResponseCode.ApplyTripTooMuch;
        }
        BigDecimal tripTotalMoney = BigDecimal.valueOf(0);
        ApplyTripInfoContract trip = trips.get(0);
        BigDecimal estimatedAmount = trip.getEstimated_amount();
        if (ObjUtils.isBlank(estimatedAmount) || estimatedAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return GlobalResponseCode.EstimatedAmountIsNull;
        }
        tripTotalMoney = tripTotalMoney.add(estimatedAmount);
        if (trip.getType() == null) {
            return GlobalResponseCode.ApplyTripTypeInvalid;
        }
        //检测行程单类型
        if (trip.getType() != BizType.Meishi.getValue()) {
            return GlobalResponseCode.ApplyTripNotDinner;
        }
        Date startTime = null;
        SimpleDateFormat dsdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        //检测时间
        if (trip.getStart_time() == null) {
            return GlobalResponseCode.ApplyTripStartDateInvalid;
        } else {
            try {
                startTime = dsdf.parse(trip.getStart_time());
            } catch (Exception ex) {
                return GlobalResponseCode.ApplyTripStartDateInvalid;
            }
        }
        if (trip.getEnd_time() == null) {
            return GlobalResponseCode.ApplyTripEndDateInvalid;
        } else {
            Date endTime = null;
            try {
                endTime = dsdf.parse(trip.getEnd_time());
            } catch (Exception ex) {
                return GlobalResponseCode.ApplyTripEndDateInvalid;
            }
            if (startTime.getTime() > endTime.getTime()) {
                return GlobalResponseCode.ApplyTripStartDateCannotBiggerThanEndDate;
            }
        }
        //检测地点
        if (trip.getStart_city_id() == null || trip.getStart_city_id().length() == 0) {
            return GlobalResponseCode.ApplyDinnerStartCityInvalid;
        }
        if (!NumberUtils.isDigits(trip.getStart_city_id())) {
            return GlobalResponseCode.ApplyTripCodeError;
        }
        BigDecimal budget = apply.getBudget().divide(ObjUtils.toBigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
        if (tripTotalMoney.setScale(2, BigDecimal.ROUND_HALF_UP).compareTo(budget) != 0) {
            return GlobalResponseCode.ApplyTripBudgetError;
        }
        //用餐人数
        String personCount = trip.getPerson_count();
        if (StringUtils.isBlank(personCount)) {
            return GlobalResponseCode.ApplyDinnerPersonCountIsError;
        }
        if (!NumericUtils.isNumeric(trip.getPerson_count()) || ObjUtils.toInteger(personCount) < 1) {
            return GlobalResponseCode.ApplyDinnerTripPersonCountIsError;
        }
        //费用类型 1.总计 2.人均
        Integer costType = trip.getCost_type();
        if (VersionTool.greaterThanOrEqualTo(clientVersion, "4.5.0") && ApplyCostType.valueOf(costType) == ApplyCostType.Unknown) {
            return GlobalResponseCode.ApplyDinnerCostTypeIsError;
        }
        if (ApplyCostType.valueOf(costType) == ApplyCostType.Unknown) {
            trip.setCost_type(ApplyCostType.TOTAL.getValue());
        }
        //平均费用(单位元)
        BigDecimal averageCost = trip.getAverage_cost();
        if (ApplyCostType.valueOf(costType) == ApplyCostType.AVERAGE) {
            if (averageCost == null || averageCost.compareTo(BigDecimal.valueOf(0)) < 1) {
                return GlobalResponseCode.ApplyDinnerAverageCostIsError;
            }
            if (averageCost.compareTo(BigDecimal.valueOf(9999)) > 0) {
                return GlobalResponseCode.ApplyDinnerAverageCostTooMatch;
            }
            if (ObjUtils.toInteger(personCount) > 999) {
                return GlobalResponseCode.ApplyDinnerPersonCountTooMatch;
            }
        }
        if (ApplyCostType.valueOf(costType) == ApplyCostType.AVERAGE) {
            BigDecimal totalCost = averageCost.multiply(new BigDecimal(personCount));
            if (totalCost.setScale(2, BigDecimal.ROUND_HALF_UP).compareTo(budget) != 0) {
                return GlobalResponseCode.ApplyDinnerTotalCostIsError;
            }
            return GlobalResponseCode.Success;
        }
        return GlobalResponseCode.Success;
    }

    private void insertDinnerTripContract(List<ApplyTripInfoContract> tripList, String applyId, Date now) {
        if (CollectionUtils.isNotEmpty(tripList)) {
            for (ApplyTripInfoContract tripContract : tripList) {
                ApplyTripInfo trip = new ApplyTripInfo();
                String id = IDTool.CreateUniqueID();
                trip.setId(id);
                trip.setApplyOrderId(applyId);
                trip.setCreateTime(now);
                trip.setType(tripContract.getType());
                trip.setTripType(tripContract.getTrip_type());
                trip.setArrivalCityId(tripContract.getArrival_city_id());
                trip.setStartCityId(tripContract.getStart_city_id());
                trip.setStartCityName(tripContract.getStart_city_name());
                trip.setArrivalCityName(tripContract.getArrival_city_name());
                trip.setState(ApplyTripStatus.Available.getValue());
                trip.setStartTime(DateTimeTool.fromStringToDate(tripContract.getStart_time()));
                if (tripContract.getEstimated_amount() != null && tripContract.getEstimated_amount().compareTo(BigDecimal.valueOf(0)) == 1) {
                    trip.setEstimatedAmount(tripContract.getEstimated_amount());
                }
                if (tripContract.getEnd_time() != null && tripContract.getEnd_time().length() > 0) {
                    trip.setEndTime(DateTimeTool.fromStringToDate(tripContract.getEnd_time()));
                }
                if (tripContract.getPerson_count() == null) {
                    tripContract.setPerson_count("-1");
                }
                trip.setPersonCount(ObjUtils.toInteger(tripContract.getPerson_count()));
                trip.setStartTime(DateTimeTool.fromStringToDate(tripContract.getStart_time(), DateTimeFormat.forPattern("yyyy-MM-dd HH:mm")));
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd KK:mm");
                try {
                    trip.setEndTime(sdf.parse(tripContract.getEnd_time()));
                } catch (ParseException e) {
                    e.printStackTrace();
                    logger.error("创建用餐审批结束时间格式不正确:{}", tripContract.getEnd_time());
                }
                JSONObject jo = new JSONObject();
                jo.put("cost_type", tripContract.getCost_type());
                jo.put("average_cost", tripContract.getAverage_cost());
                trip.setTripContent(JsonUtils.toJson(jo));
                trip.setUpdateTime(now);
                if (tripContract.getCost_attribution_name() != null && tripContract.getCost_attribution_name().size() > 0) {
                    trip.setCostAttributionName(JSON.toJSONString(tripContract.getCost_attribution_name()));
                }
                applyTripMapper.insert(trip);
            }
        }
    }

    /**
     * 用餐审批
     * @param approveModel
     * @param userId
     * @param companyId
     * @param ip
     * @return
     */
    @Override
    public SaasResponseEntity dinnerApprove(ThirdApplyApproveContract approveModel, String userId, String companyId, String ip) {
        return sloveThirdApprove(approveModel, userId, companyId, ip);
    }

    /**
     * 保存审批单中订单的费用归属
     *
     * @param applyOrderContract
     * @param companyId
     * @param applyId
     */
    void sloveApplyCostAttribution(ApplyOrderV2Contract applyOrderContract, String companyId, String applyId, Integer bringIn) {
        List<CostAttributionInfo> costAttributionList = applyOrderContract.getCost_attribution_list();
        com.fenbeitong.saas.core.contract.apply.CostInfoContract costInfo = applyOrderContract.getCost_info();
        if (CollectionUtils.isEmpty(costAttributionList) && ObjUtils.isEmpty(costInfo)) {
            return;
        }
        if (bringIn == null) {
            bringIn = 0;
        }
        if (ObjUtils.isNotEmpty(costInfo)) {
            List<com.fenbeitong.saas.core.contract.apply.CostInfoContract.CostAttributionGroup> costAttributionGroupList = costInfo.getCost_attribution_group_list();
            if (CollectionUtils.isEmpty(costAttributionGroupList)) {
                return;
            }
            List<ApplyCostAttribution> applyCostAttributionList = Lists.newArrayList();
            for (com.fenbeitong.saas.core.contract.apply.CostInfoContract.CostAttributionGroup costAttributionGroup : costAttributionGroupList) {
                Integer category = costAttributionGroup.getCategory();
                String recordId = costAttributionGroup.getRecord_id();
                String categoryName = costAttributionGroup.getCategory_name();
                List<CostAttribution> costAttributionListInfos = costAttributionGroup.getCost_attribution_list();
                for (CostAttribution costAttribution : costAttributionListInfos) {
                    ApplyCostAttribution applyCostAttribution = new ApplyCostAttribution();
                    applyCostAttribution.setApplyId(applyId);
                    applyCostAttribution.setCompanyId(companyId);
                    applyCostAttribution.setCostAttributionId(costAttribution.getId());
                    applyCostAttribution.setCostAttributionName(costAttribution.getName());
                    applyCostAttribution.setCostAttributionCategory(category);
                    applyCostAttribution.setBringIn(bringIn);
                    applyCostAttribution.setRecordId(recordId);
                    applyCostAttribution.setCategoryName(categoryName);
                    applyCostAttributionList.add(applyCostAttribution);
                }
            }
            applyCostAttributionExtMapper.batchInsertCostAttribution(applyCostAttributionList);
        } else {
            List<ApplyCostAttribution> applyCostAttributionList = Lists.newArrayList();
            for (CostAttributionInfo costAttributionInfo : costAttributionList) {
                ApplyCostAttribution applyCostAttribution = new ApplyCostAttribution();
                applyCostAttribution.setApplyId(applyId);
                applyCostAttribution.setCompanyId(companyId);
                applyCostAttribution.setCostAttributionId(costAttributionInfo.getCost_attribution_id());
                applyCostAttribution.setCostAttributionName(costAttributionInfo.getCost_attribution_name());
                applyCostAttribution.setCostAttributionCategory(costAttributionInfo.getCost_attribution_category());
                applyCostAttribution.setBringIn(bringIn);
                applyCostAttributionList.add(applyCostAttribution);
            }
            applyCostAttributionExtMapper.batchInsertCostAttribution(applyCostAttributionList);
        }
    }

    /**
     *
     * @param applyOrder 审批单信息
     * @param applyStatus 操作状态
     *
     */
    private void sendKafkaMsg(ApplyOrder applyOrder,ApplyStatus applyStatus) {
        String applyId = applyOrder.getId();
        if(StrUtil.isEmpty(applyId)){
            return;
        }
        ApplyType applyType = ApplyType.valueOf(applyOrder.getType().intValue());
        String pushTitle =
                new StringBuffer().append(StrUtils.formatString(CoreLanguage.Common_Message_AcceptFlowNotice.getMessage(),
                        applyType.getDesc())).toString();
        String pushContent = new StringBuffer().append(StrUtils.formatString(CoreLanguage.Common_Message_FlowNoticeResult.getMessage(), applyType.getDesc(), applyStatus.getDesc())).toString();
        // 跳转信息
        Integer settingType = applyOrder.getApplyOrderType();
        boolean myself = true;
        Map<String, Object> msgData = new HashMap<>();
        msgData.put("myself", myself );
        msgData.put("view_type",  "1");
        msgData.put("id", applyOrder.getId());
        msgData.put("setting_type", String.valueOf(settingType));
        if (applyType == ApplyType.Mall) {
            msgData.put("order_type", "20");
        }
        if (applyType == ApplyType.Meishi) {
            msgData.put("order_type", "60");
        }
        if (applyType == ApplyType.TakeAway) {
            msgData.put("order_type", "50");
        }
        //if (ApplyOrderCategory.ORDER.getValue().equals(settingType)
        //        || ApplyOrderCategory.REFUND_CHANGE.getValue().equals(settingType)) {
        //    List<ApplyTripApplicateExt> applyTripApplicates = applyTripApplicateExtMapper.queryApplyTripApplicateList(applyId);
        //    if (ObjUtils.isNotEmpty(applyTripApplicates)) {
        //        String orderId = applyTripApplicates.get(0).getOrderId();
        //        String orderType = ObjUtils.toString(applyTripApplicates.get(0).getType());
        //        msgData.put("order_id", orderId);
        //        msgData.put("order_type", orderType);
        //    }
        //    if (ObjUtils.isNotEmpty(applyOrder.getSubType())) {
        //        msgData.put("sub_type", applyOrder.getSubType());
        //    }
        //}
        msgData.put("apply_type", applyType.getValue());
        String linkInfo = JSONObject.toJSONString(msgData);
        // push
        KafkaMessageCenterPushMsg kafkaMessageCenterPushMsg = new KafkaMessageCenterPushMsg();
        kafkaMessageCenterPushMsg.setTitle(pushTitle);
        kafkaMessageCenterPushMsg.setContent(pushContent);
        kafkaMessageCenterPushMsg.setUserId(applyOrder.getEmployeeId());
        kafkaMessageCenterPushMsg.setCompanyId(applyOrder.getCompanyId());
        kafkaMessageCenterPushMsg.setMsgType("0");
        kafkaMessageCenterPushMsg.setDesc(pushContent);
        kafkaMessageCenterPushMsg.setAlert(true);
        kafkaMessageCenterPushMsg.setMsg(linkInfo);
        kafkaMessageCenterPushMsg.setTitleMultilingualMap(LanguageUtils.getFullMessage(CoreLanguage.Common_Message_AcceptFlowNotice.getLanguageCode(), applyType.getDesc()));
        kafkaMessageCenterPushMsg.setCommentMultilingualMap(LanguageUtils.getFullMessage(CoreLanguage.Common_Message_FlowNoticeResult.getLanguageCode(), applyType.getDesc(), applyStatus.getDesc()));
        logger.info("sendKafkaMsg.pushData={}", JsonUtils.toJson(kafkaMessageCenterPushMsg));
        pushKafkaProducerService.sendPushKafkaMsg(kafkaMessageCenterPushMsg);
    }

    /**
     * 创建第三方额度申请单
     *
     * @param applyContract
     * @return
     */
    @Override
    public GlobalResponseCode pettyApplyCreate(String token, ApplyThirdContract applyContract, String userId, String companyId, String ip, String applyId) throws SaasException {
        String bankName = BankNameEnum.XWBANK.getCode();
        if (applyContract != null && applyContract.getApply() != null && StringUtils.isNotBlank(applyContract.getApply().getBank_name())) {
            bankName = applyContract.getApply().getBank_name();
        }
        //备用金校验
        try {
            BankCheckAllInfoReqDTO bankCheckAllInfoReqDTO = new BankCheckAllInfoReqDTO();
            bankCheckAllInfoReqDTO.setBankName(bankName);
            bankCheckAllInfoReqDTO.setCategoryType(OrderCategory.BankIndividual.getKey());
            bankCheckAllInfoReqDTO.setCompanyId(companyId);
            bankCheckAllInfoReqDTO.setEmployeeId(userId);
            bankCheckAllInfoReqDTO.setOperationSrc(BankCheckInfoOperationType.SAAS.getCode());
            iBankHuPoFBTService.checkCardAllInfo(bankCheckAllInfoReqDTO);
        } catch (FinhubException ex) {
            throw new SaasException(ex.getCode(), ex.getMessage(), ex.getType(), ex.getTitle());
        }
        //检查数据信息
        GlobalResponseCode code = checkPettyApplyData(applyContract, companyId, userId);
        if (code != GlobalResponseCode.Success) {
            return code;
        }
        ApplyOrderThirdContract applyOrderContract = applyContract.getApply();
        //加上排重逻辑
        GlobalResponseCode duplicateCode = duplicateThirdApply(companyId, applyOrderContract.getThird_id(), SaasFlowConstant.SETTING_TYPE_PETTRY, ApplyType.Petty.getValue());
        if (duplicateCode != GlobalResponseCode.Success) {
            return duplicateCode;
        }
        applyOrderContract.setEmployee_id(userId);
        applyOrderContract.setCompany_id(companyId);
        Date now = new Date();
        ApplyOrder apply = applyOrderContract.ToModel();
        EmployeeContract employee = iBaseOrganizationService.getEmployee(userId, companyId);
        if (employee != null) {
            apply.setApplicantName(employee.getName());
        }
        apply.setUpdateTime(now);
        apply.setCreateTime(now);
        //创建申请单
        apply.setId(applyId);
        apply.setRootApplyOrderId(applyId);
        //创建申请单
        apply.setId(applyId);
        apply.setState(ApplyStatus.Approved.getValue());
        apply.setFlowCcType(CcNoticeType.Flow.getValue());
        apply.setFlowType(CompanyApplyType.OpenApi.getValue());
        apply.setType(ApplyType.Petty.getValue());
        apply.setApplyOrderType(SaasFlowConstant.SETTING_TYPE_PETTRY);
        apply.setBillNo(applyV2Service.getBillNo(apply.getType().intValue(),companyId));
        applyMapper.insertSelective(apply);
        //保存行程信息
        Integer type = BizType.Petty.getValue();
        List<ApplyTripInfoContract> tripList = applyContract.getTrip_list();
        Map<String, Object> costAttributionMap = tripList.get(0).getCost_attribution_name();
        ApplyTripInfo trip = new ApplyTripInfo();
        String id = IDTool.CreateUniqueID();
        trip.setId(id);
        trip.setApplyOrderId(applyId);
        trip.setCreateTime(now);
        trip.setType(type);
        trip.setStartCityId("0");
        trip.setState(ApplyTripStatus.Available.getValue());
        trip.setStartTime(now);
        trip.setUpdateTime(now);
        trip.setCostAttributionName(JSON.toJSONString(costAttributionMap));
        if (StringUtils.isNotBlank(bankName)) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("bank_name", bankName);
            trip.setTripContent(jsonObject.toJSONString());
        }
        applyTripMapper.insert(trip);
        try {
            String costAttributionId = ObjUtils.toString(costAttributionMap.get("id"),"");
            Integer costAttributionType = ObjUtils.toInteger(costAttributionMap.get("category"));
            String costAttributionName = ObjUtils.toString(costAttributionMap.get("name"), "");
            BankApplyCreditReqDTO bankApplyCreditReqDTO = new BankApplyCreditReqDTO();
            bankApplyCreditReqDTO.setEmployeeId(apply.getEmployeeId());
            bankApplyCreditReqDTO.setCompanyId(companyId);
            bankApplyCreditReqDTO.setApplyCreditAmount(BigDecimal.valueOf(apply.getBudget()));
            bankApplyCreditReqDTO.setSaasApplyNo(applyId);
            bankApplyCreditReqDTO.setBankName(bankName);
            bankApplyCreditReqDTO.setCategoryType(OrderCategory.BankIndividual.getKey());
            bankApplyCreditReqDTO.setCostAttributionId(costAttributionId);
            bankApplyCreditReqDTO.setCostAttributionName(costAttributionName);
            bankApplyCreditReqDTO.setCostAttributionType(costAttributionType);
            bankApplyCreditReqDTO.setApplyReason(apply.getApplyReason());
            bankApplyCreditReqDTO.setApplyReasonDesc(apply.getApplyReasonDesc());
            bankApplyCreditReqDTO.setPettyName(apply.getTitle());
            logger.info("备用金终审调用：" + JsonUtils.toJson(bankApplyCreditReqDTO));
            iBankHuPoFBTService.applyCredit(bankApplyCreditReqDTO);
        } catch (FinhubException ex) {
            logger.info(String.format("备用金终审错误：%s,%s,%s,%s", ex.getCode(), ex.getMessage(), ex.getType(), ex.getTitle()));
            throw new SaasException(ex.getCode(), ex.getMessage(), ex.getType(), ex.getTitle());
        }
        return GlobalResponseCode.Success;
    }

    /**
     * 检查虚拟卡备用金数据有效性
     *
     * @param applyContract
     * @return
     */
    private GlobalResponseCode checkPettyApplyData(ApplyThirdContract applyContract, String companyId, String userId) {
        //检查申请单
        ApplyOrderThirdContract apply = applyContract.getApply();
        if (apply == null) {
            return GlobalResponseCode.ApplyIsNull;
        }
        if (StringUtils.isNotBlank(apply.getApply_reason_desc()) && apply.getApply_reason_desc().length() > 500) {
            return GlobalResponseCode.ApplyReasonInvalid;
        }
        Integer costAttributionCategory = apply.getCost_attribution_category();
        String costAttributionId = apply.getCost_attribution_id();
        String costAttributionName = apply.getCost_attribution_name();
        if (costAttributionCategory == null || StringUtils.isBlank(costAttributionId) || StringUtils.isBlank(costAttributionName)) {
            return GlobalResponseCode.CostAttributionNameIsNull;
        }
        GlobalResponseCode code = checkCostAttribution(apply, companyId, userId, costAttributionId, costAttributionCategory, costAttributionName);
        if (code != GlobalResponseCode.Success) {
            return code;
        }
        if (StringUtils.isBlank(apply.getThird_id())) {
            return GlobalResponseCode.ApplyVirtualThirdIdIsNull;
        }
        Integer budget = apply.getBudget();
        if (budget == null || budget <= 0) {
            return GlobalResponseCode.ApplyVirtualBudgetIsNull;
        }
        List<ApplyTripInfoContract> tripList = applyContract.getTrip_list();
        if (CollectionUtils.isEmpty(tripList) || tripList.size() != 1) {
            return GlobalResponseCode.ApplyVirtualTripListIsNull;
        }
        return GlobalResponseCode.Success;
    }
}
