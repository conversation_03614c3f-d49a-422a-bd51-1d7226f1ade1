package com.fenbeitong.saas.core.service.taxi.impl;

import com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountSubFindRespRPCDTO;
import com.fenbeitong.finhub.common.constant.BudgetCategoryTypeEnum;
import com.fenbeitong.finhub.common.constant.PayModelEnum;
import com.fenbeitong.finhub.common.constant.UsePersonalBudgetTypeEnum;
import com.fenbeitong.finhub.common.saas.entity.CostAttribution;
import com.fenbeitong.finhub.common.saas.entity.CostAttributionGroup;
import com.fenbeitong.finhub.common.saas.entity.CostInfo;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import com.fenbeitong.finhub.common.utils.CostInfoUtils;
import com.fenbeitong.saas.api.model.dto.rule.EmployeeTaxiRuleInfo;
import com.fenbeitong.saas.api.model.dto.rule.RuleTimeRange;
import com.fenbeitong.saas.api.model.enums.TaxiCategory;
import com.fenbeitong.saas.api.model.enums.TaxiServerType;
import com.fenbeitong.saas.api.model.enums.TaxiSubType;
import com.fenbeitong.saas.core.common.DynamicDataSourceDecision;
import com.fenbeitong.saas.core.common.base.SaasException;
import com.fenbeitong.saas.core.common.constant.GlobalResponseCode;
import com.fenbeitong.saas.core.common.constant.TemporaryResponseCode;
import com.fenbeitong.saas.core.contract.apply.ApplyOrderContract;
import com.fenbeitong.saas.core.contract.apply.ApplyTripInfoContract;
import com.fenbeitong.saas.core.contract.common.CostAttributionInfo;
import com.fenbeitong.saas.core.contract.messagesettings.CostAttrAndBudgetConf;
import com.fenbeitong.saas.core.contract.order.apply.CustomFormContext;
import com.fenbeitong.saas.core.contract.order.check.CostAttributionContract;
import com.fenbeitong.saas.core.contract.order.check.CostAttributionGroupContract;
import com.fenbeitong.saas.core.contract.order.check.CostInfoContract;
import com.fenbeitong.saas.core.contract.order.check.CouponContract;
import com.fenbeitong.saas.core.contract.order.check.ErrMsgInfo;
import com.fenbeitong.saas.core.contract.order.check.OneKeyTaxiOrderRuleCheckResult;
import com.fenbeitong.saas.core.contract.order.check.ResponseCodeContract;
import com.fenbeitong.saas.core.contract.order.check.TaxiApplyRule;
import com.fenbeitong.saas.core.contract.order.check.TaxiModifyDestinationCheckResContract;
import com.fenbeitong.saas.core.contract.order.check.TaxiOrderCheckReqContract;
import com.fenbeitong.saas.core.contract.order.check.TaxiOrderCheckResV2Contract;
import com.fenbeitong.saas.core.contract.order.check.TaxiOrderCheckRule;
import com.fenbeitong.saas.core.contract.order.check.TaxiOrderRuleCheckV2Result;
import com.fenbeitong.saas.core.contract.order.check.TaxiTypePriceInfoV2;
import com.fenbeitong.saas.core.contract.order.check.TempOrderCheckResContract;
import com.fenbeitong.saas.core.contract.order.check.TempOrderRuleCheckResult;
import com.fenbeitong.saas.core.contract.order.check.TravelOnBusiOrderRuleCheckResult;
import com.fenbeitong.saas.core.contract.rule.PriceLimitPayTips;
import com.fenbeitong.saas.core.contract.rule.TaxiLimitPayTipContract;
import com.fenbeitong.saas.core.contract.rule.TaxiRuleListContract;
import com.fenbeitong.saas.core.dao.saasplus.TaxiManageSettingExtMapper;
import com.fenbeitong.saas.core.language.CoreLanguage;
import com.fenbeitong.saas.core.model.enums.OrderCategory;
import com.fenbeitong.saas.core.model.enums.apply.ApplyStatus;
import com.fenbeitong.saas.core.model.enums.apply.ApplyType;
import com.fenbeitong.saas.core.model.enums.apply.BizType;
import com.fenbeitong.saas.core.model.enums.budget.BudgetType;
import com.fenbeitong.saas.core.model.enums.rule.AllowedTaxiType;
import com.fenbeitong.saas.core.model.enums.rule.ExceedAllowType;
import com.fenbeitong.saas.core.model.enums.rule.TravelExceedType;
import com.fenbeitong.saas.core.model.saas.ApplyOrder;
import com.fenbeitong.saas.core.model.saas.ApplyTripInfo;
import com.fenbeitong.saas.core.model.saasplus.TaxiManageSetting;
import com.fenbeitong.saas.core.service.IAccountService;
import com.fenbeitong.saas.core.service.IApplyThirdService;
import com.fenbeitong.saas.core.service.IApplyV2Service;
import com.fenbeitong.saas.core.service.IBaseOrganizationService;
import com.fenbeitong.saas.core.service.IMessageSetupService;
import com.fenbeitong.saas.core.service.IOrderCheckService;
import com.fenbeitong.saas.core.service.IRuleService;
import com.fenbeitong.saas.core.service.ITaxiCheckV3Service;
import com.fenbeitong.saas.core.service.apply.amount.CustomFormConfigService;
import com.fenbeitong.saas.core.service.impl.CurrencyCheckServiceImpl;
import com.fenbeitong.saas.core.service.taxi.TaxiCheckProcessor;
import com.fenbeitong.saas.core.service.taxi.common.builder.CheckContextBuilder;
import com.fenbeitong.saas.core.service.taxi.common.builder.CommonContextBuilder;
import com.fenbeitong.saas.core.service.taxi.context.TaxiCommonCheckContext;
import com.fenbeitong.saas.core.service.taxi.context.TaxiNotPriceExcludeTypeContext;
import com.fenbeitong.saas.core.service.taxi.exception.SaasTaxiRuleException;
import com.fenbeitong.saas.core.service.taxi.inner.ITaxiOrderCheckService;
import com.fenbeitong.saas.core.service.taxi.manager.TaxiCheckResManager;
import com.fenbeitong.saas.core.service.taxi.manager.TaxiRuleCheckManager;
import com.fenbeitong.saas.core.service.taxi.manager.TaxiRuleCommonManager;
import com.fenbeitong.saas.core.service.taxi.manager.TaxiRuleManager;
import com.fenbeitong.saas.core.service.taxi.manager.TaxiRuleTypeFloatingManager;
import com.fenbeitong.saas.core.service.taxi.manager.TaxiTripManager;
import com.fenbeitong.saas.core.service.taxi.manager.TaxiUserUsedDataManager;
import com.fenbeitong.saas.core.utils.builder.BeanHelpBuilder;
import com.fenbeitong.saas.core.utils.icon.IconChangeUtil;
import com.fenbeitong.saas.core.utils.tools.ConvertUtils;
import com.fenbeitong.saas.core.utils.tools.VersionTool;
import com.fenbeitong.saasplus.api.model.dto.budget.BizBudgetSettingAndUseContract;
import com.fenbeitong.saasplus.api.model.dto.custform.CustomFormTotalEstimatedOptionDTO;
import com.fenbeitong.saasplus.api.model.dto.finance.CustomDimension;
import com.fenbeitong.saasplus.api.model.dto.finance.OrderCostInfoReq;
import com.fenbeitong.saasplus.api.model.dto.rule.TaxiApproveRuleContract;
import com.fenbeitong.saasplus.api.model.enums.rule.TaxiExceedConfigEnum;
import com.fenbeitong.saasplus.api.model.enums.savings.LimitType;
import com.fenbeitong.saasplus.api.model.po.fenbeitong.rulenew.TimesLimitTypeEnum;
import com.fenbeitong.saasplus.api.service.custform.ICustomFormService;
import com.fenbeitong.saasplus.api.service.finance.IOrderCostService;
import com.fenbeitong.saasplus.api.service.rule.ITaxiApproveRuleService;
import com.fenbeitong.saasplus.api.service.setting.IOrderMsgUseService;
import com.fenbeitong.saasplus.api.service.taxi.ICompanyTaxiService;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.fenbeitong.usercenter.api.model.dto.rule.CarPolicyBean;
import com.fenbeitong.usercenter.api.model.dto.rule.RuleIdDto;
import com.fenbeitong.usercenter.api.model.enums.privilege.CodeTypeEnums;
import com.fenbeitong.usercenter.api.model.enums.privilege.CustomCodeType;
import com.fenbeitong.usercenter.api.service.company.ICompanyRuleService;
import com.fenbeitong.usercenter.api.service.privilege.IPrivilegeService;
import com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeTaxiRuleExtService;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.StrUtils;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Service
public class TaxiOrderCheckServiceImpl implements ITaxiOrderCheckService {

    private static final Logger logger = LoggerFactory.getLogger(TaxiOrderCheckServiceImpl.class);

    @Autowired
    private IBaseOrganizationService baseOrganizationService;
    @Autowired
    private ICompanyRuleService iCompanyRuleService;
    @Autowired
    private IOrderCheckService iOrderCheckService;
    @Autowired
    private IRuleService ruleService;
    @Autowired
    private IBaseEmployeeTaxiRuleExtService iBaseEmployeeTaxiRuleExtService;
    @Autowired
    private IAccountService accountService;
    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyOrderMapper applyOrderMapper;
    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyTripInfoMapper applyTripInfoMapper;
    @Autowired
    private ITaxiApproveRuleService iTaxiApproveRuleService;
    @Autowired
    private IMessageSetupService iMessageSetupService;
    @Autowired
    private TaxiRuleCheckManager taxiRuleCheckManager;
    @Autowired
    private IApplyThirdService applyThirdService;
    @Autowired
    private IPrivilegeService iPrivilegeService;
    @Autowired
    private IApplyV2Service iApplyV2Service;
    @Autowired
    private ITaxiCheckV3Service iTaxiCheckV3Service;
    @Autowired
    private TaxiManageSettingExtMapper taxiManageSettingExtMapper;
    @Autowired
    private ICompanyTaxiService iCompanyTaxiService;
    @Autowired
    private com.fenbeitong.saasplus.api.service.messageSetup.IMessageSetupService iMessageSetupRpcService;
    @Autowired
    private CurrencyCheckServiceImpl currencyCheckService;
    @Autowired
    private IOrderMsgUseService iOrderMsgUseService;
    @Autowired
    private TaxiCheckProcessor taxiCheckProcessor;
    @Resource
    ICustomFormService iCustomFormService;
    @Resource
    TaxiTripManager taxiTripManager;
    @Resource
    TaxiRuleManager taxiRuleManager;
    @Resource
    TaxiUserUsedDataManager taxiUserUsedDataManager;
    @Resource
    TaxiRuleCommonManager taxiRuleCommonManager;

    @Autowired
    private CommonContextBuilder commonContextBuilder;

    @Autowired
    private FlowExecutor flowExecutor;

    @Autowired
    private CheckContextBuilder checkContextBuilder;

    @Autowired
    private TaxiCheckResManager taxiCheckResManager;

    @Autowired
    private BeanHelpBuilder beanHelpBuilder;

    @Autowired
    private IOrderCostService iOrderCostService;

    @Autowired
    private CustomFormConfigService customFormConfigService;

    @Autowired
    private TaxiRuleTypeFloatingManager taxiRuleTypeFloatingManager;

    /**
     * 用车规则校验F
     *
     * @param taxiOrderCheckReqContract
     * @param clientVersion
     * @return
     */
    @Override
    public TaxiOrderCheckResV2Contract taxiOrderCheck(TaxiOrderCheckReqContract taxiOrderCheckReqContract, String clientVersion) {
        DynamicDataSourceDecision.determineDataSource(taxiOrderCheckReqContract.getCompany_id());
        logger.info("用车规则校验请求参数：{}", JsonUtils.toJson(taxiOrderCheckReqContract));
        // 返回结果
        TaxiOrderCheckResV2Contract resContract = new TaxiOrderCheckResV2Contract();
        // 校验权限问题
        TaxiOrderRuleCheckV2Result ruleCheckResult = taxiOrderRuleCheckResult(taxiOrderCheckReqContract, clientVersion);
        logger.info("用车校验权限结果：{}", JsonUtils.toJson(ruleCheckResult));
        resContract.setErr_code(ruleCheckResult.getErrCode());
        resContract.setErr_msg(ruleCheckResult.getErrMsg());
        resContract.setCoupon_type(ruleCheckResult.getCoupon_type());
        if (VersionTool.greaterThanOrEqualTo(clientVersion, "4.9.18")
                && Integer.valueOf(TemporaryResponseCode.OrderCanNotSubmit.getCode()).equals(ruleCheckResult.getErrCode())
        ) {
            boolean applyCheck = ruleCheckResult.getTaxiType() != null && ruleCheckResult.getTaxiType().equals(TaxiSubType.TaxiApply.getSubtype());
            boolean applyAble = !applyCheck && (ruleCheckResult.getApply_rule_id() != null || iApplyV2Service.queryValidTaxi(taxiOrderCheckReqContract.getEmployee_id(), taxiOrderCheckReqContract.getCompany_id()));
            if (applyAble) {
                resContract.setErr_sub_msg(CoreLanguage.TaxiCheckServiceImpl_Value_OverUpdateConfigOrApply.getMessage());
                //超标不能提交,但有申请用车权限,版本大于等于4.9.17
                resContract.setErr_code(TemporaryResponseCode.OrderCanNotSubmitGuideApply.getCode());
                resContract.setErr_msg(TemporaryResponseCode.OrderCanNotSubmitGuideApply.getMsg());
            }else{
                resContract.setErr_sub_msg(CoreLanguage.TaxiCheckServiceImpl_Value_OverUpdateConfig.getMessage());
            }
        }
        resContract.setExceed_msg(ruleCheckResult.getExceed_msg());
        resContract.setApply_id(ruleCheckResult.getApplyId());
        resContract.setAll_available(ruleCheckResult.getAllAvailable());
        resContract.setTaxi_type_list(ruleCheckResult.getAvailableTaxiTypeList());
        resContract.setTaxi_type(ruleCheckResult.getTaxiType());
        resContract.setTaxi_type_name(ruleCheckResult.getTaxi_type_name());
        resContract.setRule_id(ruleCheckResult.getRule_id());
        resContract.setRule_name(ruleCheckResult.getRule_name());
        resContract.setEmployee_rule_info(ruleCheckResult.getEmployeeRuleInfo());
        resContract.setLimit_advance(false);
        resContract.setCost_attribution_scope(ruleCheckResult.getCost_attribution_scope());
        resContract.setBudget_cost_attr_type(ruleCheckResult.getBudget_cost_attr_type());
        resContract.setIs_temporary_apply(ruleCheckResult.getIsTemporaryApply());
        resContract.setTaxi_type_limit_flag(false);
        resContract.setPay_model(ruleCheckResult.getPayModel());
        resContract.setCompany_pay_price(ruleCheckResult.getCompanyPayPrice());
        resContract.setApply_amount(ruleCheckResult.getApplyAmount());
        resContract.setRule_amount(ruleCheckResult.getRuleAmount());
        resContract.setRule_exceed_tips(ruleCheckResult.getRuleExceedTips());
        resContract.setTaxi_type_name(ruleCheckResult.getTaxiTypeName());
        resContract.setTaxi_type_view_name(ruleCheckResult.getTaxiTypeViewName());
        if (ObjUtils.isNotEmpty(ruleCheckResult.getEmployeeRuleInfo())) {
            resContract.setLimit_advance(ObjUtils.toBoolean(ruleCheckResult.getEmployeeRuleInfo().getLimitAdvance(), false));
            resContract.setAllowed_taxi_type(ruleCheckResult.getEmployeeRuleInfo().getAllowedTaxiType());
            resContract.setTaxi_type_limit_flag(ruleCheckResult.getEmployeeRuleInfo().getTaxiTypeLimitFlag());
        }
        if (ruleCheckResult.getIs_exceed()
                || (taxiOrderCheckReqContract.getExceed_submit() != null && taxiOrderCheckReqContract.getExceed_submit())) {
            resContract.setIs_exceed(true);
        }
        if (ruleCheckResult.getErrCode() == GlobalResponseCode.Success.getCode()) {
            // 自定义字段转化
            List<CustomDimension> customDimensionList =
                    ConvertUtils.convertToCustomDimension(taxiOrderCheckReqContract.getCustomer_field_setting_list());

            TempOrderCheckResContract costResult = iOrderCheckService.saveCost(taxiOrderCheckReqContract.getOrder_id(),
                    taxiOrderCheckReqContract.getCompany_id(),
                    taxiOrderCheckReqContract.getEmployee_id(),
                    taxiOrderCheckReqContract.getApply_id(),
                    taxiOrderCheckReqContract.getTrip_id(),
                    BudgetCategoryTypeEnum.Taxi,
                    taxiRuleCommonManager.getMaxAndInsurancePrice(taxiOrderCheckReqContract),
                    taxiOrderCheckReqContract.getCost_info(),
                    taxiOrderCheckReqContract.getCostInfoString(),
                    clientVersion,
                    ruleCheckResult.getEmployeeRuleInfo().getUse_personal_budget(), customDimensionList,taxiOrderCheckReqContract.getDeparture_time());
            if (costResult.getErr_code() != GlobalResponseCode.Success.getCode()) {
                //特殊处理
                if (costResult.getErr_code() == GlobalResponseCode.OrderCheckOverBudgetCanSubmit.getCode()) {
                    resContract.setErr_code(TemporaryResponseCode.OrderCheckOverBudgetCanSubmit.getCode());
                } else if (costResult.getErr_code() == GlobalResponseCode.OrderCheckOverBudgetCanNotSubmit.getCode()) {
                    resContract.setErr_code(TemporaryResponseCode.OrderCheckOverBudgetCanNotSubmit.getCode());
                } else {
                    resContract.setErr_code(costResult.getErr_code());
                }
                resContract.setErr_msg(costResult.getErr_msg());
            } else {
                resContract.setCost_id(costResult.getCost_id());
            }
        }

        // 错误信息类型
        taxiRuleCommonManager.taxiCheckResContractCommon(resContract, ruleCheckResult);
//        //通过校验时根据，taxiType查询taxiTypeViewName
//        setTaxiTypeViewName(resContract, taxiOrderCheckReqContract.getCompany_id(),taxiOrderCheckReqContract);

        return resContract;
    }

    /**
     * 用车强制提交提示（超标但需要理由）
     *
     * @param taxiOrderCheckReqContract
     * @return
     */
    private OneKeyTaxiOrderRuleCheckResult checkExceedAuth(TaxiOrderCheckReqContract taxiOrderCheckReqContract) {
        return taxiRuleCheckManager.checkExceedAuth(taxiOrderCheckReqContract.getCompany_id(),
                taxiOrderCheckReqContract.getExceed_submit(),
                taxiOrderCheckReqContract.getExceed_reason(),
                taxiOrderCheckReqContract.getExceed_reason_comment());
    }

//    private void setTaxiTypeViewName(TaxiOrderCheckResV2Contract resContract, String companyId,TaxiOrderCheckReqContract taxiOrderCheckReqContract) {
//        logger.info("resContract:{}",JsonUtils.toJson(resContract));
//        Integer type = taxiOrderCheckReqContract.getType();
//        logger.info("setTaxiTypeViewName:{}",type);
//        //接机
//        if(TaxiServerType.AirportPickup.getCode() == type && taxiOrderCheckReqContract.getScene_category() == 3){
//            resContract.setTaxi_type(998);
//            resContract.setTaxi_type_name(CoreLanguage.TaxiCheckServiceImpl_Value_AirportTransfer.getMessage());
//            resContract.setTaxi_type_view_name(CoreLanguage.TaxiCheckServiceImpl_Value_AirportTransfer.getMessage());
//            return;
//        }
//        //送机
//        else if (TaxiServerType.AirportDropOff.getCode() == type && taxiOrderCheckReqContract.getScene_category() == 3){
//            resContract.setTaxi_type(999);
//            resContract.setTaxi_type_name(CoreLanguage.TaxiCheckServiceImpl_Value_AirportTransfer.getMessage());
//            resContract.setTaxi_type_view_name(CoreLanguage.TaxiCheckServiceImpl_Value_AirportTransfer.getMessage());
//            return;
//        }
//        Integer sceneCode = taxiOrderCheckReqContract.getScene_code();
//        if (ObjUtils.isEmpty(sceneCode)) {
//            return;
//        }
//        if(ObjUtils.isNotEmpty(resContract.getTaxi_type())){
//            TaxiManageSetting companySetting = iTaxiCheckV3Service.getTaxiManagerSetting(companyId, resContract.getTaxi_type());
//            logger.info("companySetting:{}",JsonUtils.toJson(companySetting));
//            if (companySetting != null) {
//                resContract.setTaxi_type_name(ObjUtils.toString(companySetting.getSceneName(),companySetting.getViewName()));
//                if(StringUtils.isNotEmpty(companySetting.getViewName())){
//                    resContract.setTaxi_type_view_name(companySetting.getViewName());
//                }else{
//                    resContract.setTaxi_type_view_name(companySetting.getSceneName());
//                }
//            }
//        }
//    }

    /**
     * 用车规则校验
     *
     * @param taxiOrderCheckReqContract
     * @param clientVersion
     * @return
     */
    private TaxiOrderRuleCheckV2Result taxiOrderRuleCheckResult(TaxiOrderCheckReqContract taxiOrderCheckReqContract, String clientVersion) {
        // 默认初始化正常
        TaxiOrderRuleCheckV2Result checkResult = new TaxiOrderRuleCheckV2Result();
        checkResult.setResCode(TemporaryResponseCode.Success);

        // 通用校验（预估价格信息，订单事由及自定义字段，订单通知人，预订人状态，企业用车权限，员工用车权限，企业账户，费用归属）
        TaxiCommonCheckContext taxiCommonCheckContext =
            commonContextBuilder.buildOrderCheckContext(taxiOrderCheckReqContract);

        logger.info("通用检验开始");
        //初始化
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        LiteflowResponse taxiOrderCommonCheckChain =
            flowExecutor.execute2Resp("taxiOrderCommonCheckChain", null, taxiCommonCheckContext);
        stopWatch.stop();
        logger.info("通用检验用时:{}", stopWatch.getTime());

        if (!taxiOrderCommonCheckChain.isSuccess()) {
            Exception e = taxiOrderCommonCheckChain.getCause();
            logger.error("taxiOrderCommonCheckChain", e);
            throw new SaasException(TemporaryResponseCode.InnerError.getCode(),
                TemporaryResponseCode.InnerError.getMsg());
        }

        checkResult = taxiCommonCheckContext.getTaxiCommonCheckResult();
        if (checkResult.getErrCode() != GlobalResponseCode.Success.getCode()) {
            return checkResult;
        }
        // 通用校验结束

        String companyId = taxiOrderCheckReqContract.getCompany_id();
        String employeeId = taxiOrderCheckReqContract.getEmployee_id();
        Integer type = taxiOrderCheckReqContract.getType();
        String applyId = taxiOrderCheckReqContract.getApply_id();
        String tripId = taxiOrderCheckReqContract.getTrip_id();
        List<TaxiTypePriceInfoV2> taxiTypeList = taxiOrderCheckReqContract.getTaxi_type_list();
        AccountSubFindRespRPCDTO account = taxiCommonCheckContext.getInnerContext().getAccount();
        Integer sceneCode = taxiOrderCheckReqContract.getScene_code();

        // 判断是不是规则升级后  取用车规则的结果
        boolean isOpenExceedConfig = false;

        // 老版本合并额度不弹窗 如开启直接按合并额度逻辑执行
        String version410 = "4.1.0";
        if (VersionTool.lessThan(clientVersion, version410)) {
            taxiOrderCheckReqContract.setRule_day_amount_sumbit(true);
        }
        // 快照信息
        JSONObject snapshotInfo = new JSONObject();
        checkResult.setSnapshotInfo(snapshotInfo);
        // 查询是否个人垫付模式
        Map<String, Boolean> funcMap = iPrivilegeService.queryFunctionMoudle(CodeTypeEnums.CustomAuth.getKey(), companyId, employeeId, CustomCodeType.M_ADVANCE_PAYMENT.getKey());
        logger.info("taxiOrderRuleCheckResult.funcMap：{}", JsonUtils.toJson(funcMap));
        Boolean advancePayment = funcMap.get(CustomCodeType.M_ADVANCE_PAYMENT.getKey());
        logger.info("[用车下单校验]，是否个人垫付模式:{}", advancePayment);
        PayModelEnum payModel = advancePayment ? PayModelEnum.PERSONAL_PREPAY : PayModelEnum.COMPANY_PAY;
        checkResult.setPayModel(payModel.getCode());
        snapshotInfo.put("payModel", payModel.getCode());
        CarPolicyBean carPolicyBean = taxiCommonCheckContext.getInnerContext().getCarPolicyBean();
        snapshotInfo.put("authInfo", carPolicyBean);

        Integer category = null;
        if (ObjUtils.isNotEmpty(applyId)) {
            category = TaxiCategory.TaxiApply.getType();
            sceneCode = ObjUtils.toInteger(sceneCode, TaxiSubType.TaxiApply.getSubtype());
            checkResult.setTaxiType(sceneCode);
            carPolicyBean.setRule_limit_flag(true);
        } else {
            category = TaxiCategory.Taxi.getType();
            sceneCode = ObjUtils.toInteger(sceneCode, TaxiSubType.Taxi.getSubtype());
            checkResult.setTaxiType(sceneCode);
        }
        setSceneAndRuleInfo(checkResult, carPolicyBean, sceneCode);

        CostAttrAndBudgetConf costAttrAndBudgetConf =
            taxiCommonCheckContext.getInnerContext().getCostAttrAndBudgetConf();
        checkResult.setCost_attribution_scope(costAttrAndBudgetConf.getCost_attribution_scope());
        checkResult.setBudget_cost_attr_type(costAttrAndBudgetConf.getBudget_cost_attr_type());

        // 默认全部可用
        checkResult.setAllAvailable(true);

        //获取可用行程信息
        List<ApplyTripInfoContract> validTripList = null;
        // 是否占用个人预算
        Integer usePersonalBudget = null;
        // 查询用户日累计用车消费
        BigDecimal dailyConsumption = BigDecimal.ZERO;
        // 查询用户用车规则
        TaxiOrderCheckRule taxiOrderCheckRule = new TaxiOrderCheckRule();
        taxiOrderCheckRule.setCompanyId(companyId);
        TempOrderRuleCheckResult ruleDayUsedCheckRes = null;
        // 自定义申请单配置
        CustomFormContext customFormContext = new CustomFormContext(BizType.Taxi);
        if (carPolicyBean.getRule_limit_flag()) {
            List<RuleIdDto> ruleInfos = carPolicyBean.getRule_infos();
            logger.info("taxiOrderRuleCheckResult.ruleInfos：{}", JsonUtils.toJson(ruleInfos));
            Integer ruleId = null;
            Integer approveRuleId = null;
            Integer taxiRuleId = null;
            if (ObjUtils.isNotEmpty(ruleInfos)) {
                for (RuleIdDto ruleInfo : ruleInfos) {
                    if (ruleInfo.getType().getKey() == TaxiSubType.TaxiApply.getSubtype()) {
                        if (ObjUtils.isNotEmpty(ruleInfo.getRule_info())) {
                            approveRuleId = ObjUtils.toInteger(ruleInfo.getRule_info().get(0).getRule_id());
                            checkResult.setApply_rule_id(approveRuleId);
                        }
                    } else {
                        if (ruleInfo.getType().getKey().equals(sceneCode) && ObjUtils.isNotEmpty(ruleInfo.getRule_info())) {
                            ruleId = ObjUtils.toInteger(ruleInfo.getRule_info().get(0).getRule_id());
                        }
                        if (ruleInfo.getType().getKey() == TaxiSubType.Taxi.getSubtype()) {
                            if (ObjUtils.isNotEmpty(ruleInfo.getRule_info())) {
                                taxiRuleId = ObjUtils.toInteger(ruleInfo.getRule_info().get(0).getRule_id());
                            }
                        }
                    }

                }
            }
            JSONObject useInfo = new JSONObject();
            snapshotInfo.put("useInfo", useInfo);
            if (ObjUtils.isNotEmpty(applyId)) {
                // 审批用车
                ApplyOrder applyOrder = applyOrderMapper.selectByPrimaryKey(applyId);
                taxiOrderCheckRule.setFormId(applyOrder.getFormId());
                taxiOrderCheckRule.setApplyId(applyId);
                taxiOrderCheckRule.setApplyType(applyOrder.getType());
                logger.info("taxiOrderRuleCheckResult.applyOrder：{}", JsonUtils.toJson(applyOrder));
                if (ObjUtils.isNull(applyOrder)) {
                    checkResult.setResCode(GlobalResponseCode.ApplyIsNull.getCode(), GlobalResponseCode.ApplyIsNull.getMsg());
                    return checkResult;
                }
                if (applyOrder.getType() != ApplyType.ApplyTaxi.getValue()
                        && applyOrder.getType() != ApplyType.CustomFromBeforehand.getValue()) {
                    checkResult.setResCode(GlobalResponseCode.ApplyFlowTypeError.getCode(), GlobalResponseCode.ApplyFlowTypeError.getMsg());
                    return checkResult;
                }
                if (applyOrder.getState() != ApplyStatus.Approved.getValue()) {
                    checkResult.setResCode(GlobalResponseCode.ApplyNotApproved.getCode(), GlobalResponseCode.ApplyNotApproved.getMsg());
                    return checkResult;
                }

                if (ObjUtils.isBlank(tripId)) {
                    throw new SaasException(GlobalResponseCode.ApplyTripNotFound);
                }
                ApplyTripInfo applyTripInfo = applyTripInfoMapper.selectByPrimaryKey(tripId);
                if (ObjUtils.isBlank(applyTripInfo) || !applyTripInfo.getApplyOrderId().equals(applyId)) {
                    throw new SaasException(GlobalResponseCode.ApplyTripNotFound);
                }

                // 总预估费配置
                customFormContext = customFormConfigService.queryTaxiCustomFormContext(applyOrder);

                TaxiApplyRule taxiApplyRule = null;
                try {
                    taxiApplyRule = taxiRuleManager.queryTaxiApplyRule(employeeId, companyId, applyId, taxiOrderCheckReqContract.getDeparture_time(),
                            true, null, taxiRuleId, tripId,
                            taxiOrderCheckReqContract.getStart_city_id(), taxiOrderCheckReqContract.getArrival_city_id());
                    logger.info("taxiOrderRuleCheckResult.taxiApplyRule1：{}", JsonUtils.toJson(taxiApplyRule));
                } catch (SaasException e) {
                    checkResult.setResCode(e.getCode(), e.getMsg());
                    return checkResult;
                }
                // 次数限制
                Boolean timesLimitFlag = ObjUtils.toInteger(taxiApplyRule.getTimesLimitFlag(), 0) == 0 ? false : true;
                Integer timesLimitFlagIntVal = ObjUtils.toInteger(taxiApplyRule.getTimesLimitFlag(), 0);
                Integer timesLimitType = ObjUtils.toInteger(taxiApplyRule.getTimesLimitType(), TimesLimitTypeEnum.total_count_limit.getCode());
                Integer timesLimit = ObjUtils.toInteger(taxiApplyRule.getTimesLimit(), 0);
                Integer timesUsed = ObjUtils.toInteger(taxiApplyRule.getApplyTimesUsed(), 0);
                Integer dayTimesUsed = ObjUtils.toInteger(taxiApplyRule.getSceneDayTimesUsed(), 0);

                // 校验限制次数
                if (timesLimitFlag && timesLimit > 0 ) {
                    //如果是按申请单次数限制或员工填写，则校验申请单次数
                    if((Objects.equals(timesLimitFlagIntVal, LimitType.LIMIT.getCode()) && Objects.equals(timesLimitType,TimesLimitTypeEnum.total_count_limit.getCode()))
                            || Objects.equals(timesLimitFlagIntVal, LimitType.EMPLOYEE_WRITE.getCode())) {
                        if(timesUsed >= timesLimit) {
                            checkResult.setResCode(TemporaryResponseCode.OrderTaxiUseTimesLimit);
                            return checkResult;
                        }
                    }
                    //如果是按单日限制则校验单日次数
                    if(Objects.equals(timesLimitFlagIntVal, LimitType.LIMIT.getCode()) && Objects.equals(timesLimitType,TimesLimitTypeEnum.day_count_limit.getCode())) {
                        if(dayTimesUsed >= timesLimit) {
                            checkResult.setResCode(TemporaryResponseCode.OrderTaxiUseTimesLimit4Rule, StrUtils.formatString(TemporaryResponseCode.OrderTaxiUseTimesLimit4Rule.getMsg(), timesLimit));
                            return checkResult;
                        }
                    }
                }

                if (StringUtils.isEmpty(taxiApplyRule.getTaxiUsedType())) {
                    taxiApplyRule.setTaxiUsedType("1");
                }
                List<Integer> collect = Arrays.stream(taxiApplyRule.getTaxiUsedType().split(",")).map(Integer::parseInt).collect(Collectors.toList());
                if (!collect.contains(taxiOrderCheckReqContract.getTaxi_used_type())) {
                    checkResult.setResCode(TemporaryResponseCode.OrderTaxiUsedTypeLimit);
                    return checkResult;
                }

                // 处理个人支付开关  申请单组件优先级最高 未开启时取消费权限模板或规则配置
                if (customFormContext.getTotalEstimatedOpen() && customFormContext.getPersonalPay()) {
                    // 超规个人付版本 设置预估费超规规则为个人支付
                    if (taxiApplyRule.getOpenExceedConfig()) {
                        taxiApplyRule.getExceedConfigInfo().setTotalEstimatedCostConfig(TaxiExceedConfigEnum.EXCEED_PERSONAL_PAY.getCode());
                    }
                }

                taxiOrderCheckRule.setUseRule(2);
                taxiOrderCheckRule.setTaxiApplyRule(taxiApplyRule);
                isOpenExceedConfig = taxiApplyRule.getOpenExceedConfig();
                checkResult.setApplyId(applyId);
                usePersonalBudget = taxiApplyRule.getUsePersonalBudget();
                useInfo.put("applyUsedInfo", taxiApplyRule);
            } else { // 用车
                if (ObjUtils.isEmpty(ruleInfos)) {
                    checkResult.setResCode(TemporaryResponseCode.OrderTaxiCheckRuleNotExist);
                    return checkResult;
                }
                //获取可用审批单信息
                List<ApplyOrderContract> applyOrderContractList = taxiTripManager.getValidList(employeeId, companyId, taxiOrderCheckReqContract.getStart_city_id(), taxiOrderCheckReqContract.getDeparture_time());
                logger.info("taxiOrderRuleCheckResult.applyOrderContractList：{}", JsonUtils.toJson(applyOrderContractList));
                logger.info("获取用车临时审批单数量：" + applyOrderContractList.size());
                //获取可用行程信息
                validTripList = taxiTripManager.getValidApplyTripInfoContract(applyOrderContractList, taxiOrderCheckReqContract.getDeparture_time(), checkResult);
                logger.info("taxiOrderRuleCheckResult.validTripList：{}", JsonUtils.toJson(validTripList));
                logger.info("获取用车临时审批可用行程数量：" + validTripList.size());
                if (!((TaxiServerType.AirportDropOff.getCode() == type || TaxiServerType.AirportPickup.getCode() == type) && taxiOrderCheckReqContract.getScene_category() == 3)) {
                    if (ObjUtils.isEmpty(ruleId) && ObjUtils.isEmpty(approveRuleId)) {
                        checkResult.setResCode(TemporaryResponseCode.OrderTaxiCheckRuleNotExist);
                        return checkResult;
                    }
                    // 临时用车申请
                    if (ObjUtils.isNotEmpty(validTripList)) {
                        checkResult.setHasTemporaryApply(true);
                        if (ObjUtils.isNotEmpty(approveRuleId)) {
                            // 对接老逻辑 规则升级不接入新规则
                            TaxiApproveRuleContract taxiApproveRuleContract = iTaxiApproveRuleService.queryRuleById(approveRuleId, companyId);
                            logger.info("taxiOrderRuleCheckResult.taxiApproveRuleContract：{}", JsonUtils.toJson(taxiApproveRuleContract));
                            taxiOrderCheckRule.setUseRule(3);
                            taxiOrderCheckRule.setTaxiApproveRuleContract(taxiApproveRuleContract);
                            isOpenExceedConfig = false;
                        } else {
                            TaxiRuleListContract taxiRuleListContract = ruleService.queryTaxiRuleDetailv3(companyId, ruleId);
                            if (taxiRuleListContract.getPathLocationInfos() == null || taxiRuleListContract.getPathLocationInfos().size() < 200) {
                                logger.info("taxiOrderRuleCheckResult.taxiRuleListContract：{}", JsonUtils.toJson(taxiRuleListContract));
                            }
                            if (taxiRuleListContract == null) {
                                checkResult.setResCode(TemporaryResponseCode.OrderTaxiCheckRuleNotExist);
                                return checkResult;
                            }
                            taxiOrderCheckRule.setUseRule(1);
                            taxiOrderCheckRule.setTaxiRuleListContract(taxiRuleListContract);
                            isOpenExceedConfig = taxiRuleListContract.getIsOpenExceedConfig();
                        }
                    } else {
                        // 用车入口 无临时用车审批  无用车规则 提示升级
                        if (ObjUtils.isEmpty(ruleId)) {

                            String version332 = "3.3.2";
                            if (VersionTool.greaterThanOrEqualTo(clientVersion, version332)) {
                                checkResult.setResCode(TemporaryResponseCode.OrderTaxiCheckRuleNotExist);
                                return checkResult;
                            } else {
                                checkResult.setResCode(GlobalResponseCode.ApplyCenterAlert.getCode(), GlobalResponseCode.ApplyCenterAlert.getMsg());
                                return checkResult;
                            }
                        }
                        TaxiRuleListContract taxiRuleListContract = ruleService.queryTaxiRuleDetailv3(companyId, ruleId);
                        //logger.info("taxiOrderRuleCheckResult.taxiRuleListContract1：{}", JsonUtils.toJson(taxiRuleListContract));
                        if (taxiRuleListContract == null) {
                            checkResult.setResCode(TemporaryResponseCode.OrderTaxiCheckRuleNotExist);
                            return checkResult;
                        }
                        taxiOrderCheckRule.setUseRule(1);
                        taxiOrderCheckRule.setTaxiRuleListContract(taxiRuleListContract);
                        isOpenExceedConfig = taxiRuleListContract.getIsOpenExceedConfig();
                        usePersonalBudget = taxiRuleListContract.getUsePersonalBudget();
                    }
                    // CHANGJING-1159新增需求 获取用车规则详细信息
                    Integer periodHour = 0, periodMinute = 0;
                    TaxiRuleListContract taxiRuleListContract =ruleService.queryTaxiRuleDetailv3(companyId, ruleId);
                    if (taxiRuleListContract.getPathLocationInfos() == null || taxiRuleListContract.getPathLocationInfos().size() < 200) {
                        logger.info("TaxiRuleListContract.taxiRuleListContract：{}", JsonUtils.toJson(taxiRuleListContract));
                    }

                    if (StringUtils.isEmpty(taxiRuleListContract.getTaxiUsedType())) {
                        taxiRuleListContract.setTaxiUsedType("1");
                    }
                    List<Integer> collect = Arrays.stream(taxiRuleListContract.getTaxiUsedType().split(",")).map(Integer::parseInt).collect(Collectors.toList());
                    if (!collect.contains(taxiOrderCheckReqContract.getTaxi_used_type())) {
                        checkResult.setResCode(TemporaryResponseCode.OrderTaxiUsedTypeLimit);
                        return checkResult;
                    }

                    if(taxiRuleListContract != null){
                        periodHour = StringUtils.isBlank(taxiRuleListContract.getPeriodHour())? periodHour : Integer.parseInt(taxiRuleListContract.getPeriodHour());
                        periodMinute = StringUtils.isBlank(taxiRuleListContract.getPeriodMinute())? periodMinute : Integer.parseInt(taxiRuleListContract.getPeriodMinute());
                    }
                    // 查询当前城市单日累计消费
                    BigDecimal userCityDailyConsumption = taxiUserUsedDataManager.getUserCityDailyConsumption(employeeId, companyId, null, taxiOrderCheckReqContract.getDeparture_time(),
                            periodHour, periodMinute, sceneCode, taxiOrderCheckReqContract.getStart_city_id(), taxiOrderCheckReqContract.getArrival_city_id(), taxiRuleListContract.getPriceAcrossCityFlag());
                    taxiOrderCheckRule.getTaxiRuleListContract().setCityPriceDayUsed(userCityDailyConsumption);
                    // 查询用户日累计用车消费
                    dailyConsumption = taxiUserUsedDataManager.getUserCustomizeDailyConsumption(employeeId, companyId, null,
                            taxiOrderCheckReqContract.getDeparture_time(), periodHour, periodMinute, sceneCode,
                            null, null);
                    logger.info("taxiOrderRuleCheckResult.查询用户日累计用车消费：{}", JsonUtils.toJson(dailyConsumption));
                    useInfo.put("dayUsed", dailyConsumption);
                }
            }
        }
        checkResult.setTaxiOrderCheckRule(taxiOrderCheckRule);
        //logger.info("员工:{} 用车权限, carPolicyBean:{}, taxiOrderCheckRule:{}", employeeId, JSON.toJSONString(carPolicyBean), JSON.toJSONString(taxiOrderCheckRule));
        // 车型浮动配置
        taxiRuleTypeFloatingManager.initTaxiFloatingConfig(employeeId, taxiOrderCheckRule, taxiTypeList, taxiOrderCheckReqContract.getDistance(), true);
        // 检查提示升级版本
        TaxiOrderRuleCheckV2Result updateResult = checkUpdate(type, carPolicyBean, taxiOrderCheckRule, clientVersion);
        logger.info("taxiOrderRuleCheckResult.updateResult：{}", JsonUtils.toJson(updateResult));
        if (GlobalResponseCode.Success.getCode() != updateResult.getErrCode()) {
            return updateResult;
        }

        List<CostAttributionInfo> costAttributionList = Lists.newArrayList();
        Boolean isCostV3 = taxiCommonCheckContext.getIsCostV3();
        if (isCostV3) {
            CostInfo costInfo = CostInfoUtils.parseFromCostInfoString(taxiCommonCheckContext.getCostInfoString());
            List<CostAttributionGroup> costAttributionGroupList = costInfo.getCostAttributionGroupList();
            logger.info("taxiOrderRuleCheckResult.costAttributionGroupList12：{}", JsonUtils.toJson(costAttributionGroupList));
            for (CostAttributionGroup costAttributionGroup : costAttributionGroupList) {
                Integer groupCategory = costAttributionGroup.getCategory();
                if (groupCategory == 3) {
                    continue;
                }
                for (CostAttribution costAttributionContract : costAttributionGroup.getCostAttributionList()) {
                    CostAttributionInfo costAttributionInfo = new CostAttributionInfo();
                    costAttributionInfo.setCost_attribution_category(groupCategory);
                    costAttributionInfo.setCost_attribution_id(costAttributionContract.getId());
                    costAttributionInfo.setCost_attribution_name(costAttributionContract.getName());
                    costAttributionList.add(costAttributionInfo);
                }
            }
        } else {
            CostInfoContract costInfo = taxiOrderCheckReqContract.getCost_info();
            List<CostAttributionGroupContract> costAttributionGroupList = costInfo.getCost_attribution_group_list();
            logger.info("taxiOrderRuleCheckResult.costAttributionGroupList1：{}", JsonUtils.toJson(costAttributionGroupList));
            for (CostAttributionGroupContract costAttributionGroup : costAttributionGroupList) {
                Integer groupCategory = costAttributionGroup.getCategory();
                if (groupCategory == 3) {
                    continue;
                }
                for (CostAttributionContract costAttributionContract : costAttributionGroup.getCost_attribution_list()) {
                    CostAttributionInfo costAttributionInfo = new CostAttributionInfo();
                    costAttributionInfo.setCost_attribution_category(groupCategory);
                    costAttributionInfo.setCost_attribution_id(costAttributionContract.getId());
                    costAttributionInfo.setCost_attribution_name(costAttributionContract.getName());
                    costAttributionList.add(costAttributionInfo);
                }
            }
        }
        // 查询预算规则及使用
        List<BizBudgetSettingAndUseContract> budgetSettingAndUseList = null;
        // 是否是对接预算校验公司
        boolean isDocking = applyThirdService.isBudgetDockingCompany(companyId);
        // 是否是新预算校验公司
        boolean isNewBudget = iOrderCheckService.isNewBudgetCompany(companyId);
        logger.info("taxiOrderRuleCheckResult isDocking: {}, isNewBudget：{}",  JsonUtils.toJson(isDocking), JsonUtils.toJson(isNewBudget));
        BigDecimal thirdBudgetRestAmount = new BigDecimal(0);
        if (!isDocking && !isNewBudget) {
            budgetSettingAndUseList = iOrderCheckService.queryBudgetSettingAndUseList(companyId, employeeId, costAttributionList, costAttrAndBudgetConf, OrderCategory.Taxi);
            logger.info("taxiOrderRuleCheckResult.budgetSettingAndUseList：{}", JsonUtils.toJson(budgetSettingAndUseList));
            if (ObjUtils.toInteger(usePersonalBudget, UsePersonalBudgetTypeEnum.USE.getCode()) == UsePersonalBudgetTypeEnum.NOT_USE.getCode()) {
                budgetSettingAndUseList = budgetSettingAndUseList.stream()
                        .filter(budget -> budget.getBudgetType() != BudgetType.Employee.getValue()).collect(Collectors.toList());
            }
        } else if (isDocking) {
            thirdBudgetRestAmount = iOrderCheckService.thirdCategoryOrderBudgetQuery(companyId, employeeId, costAttributionList, costAttrAndBudgetConf, OrderCategory.Taxi);
            logger.info("taxiOrderRuleCheckResult.thirdBudgetRestAmount：{}", JsonUtils.toJson(thirdBudgetRestAmount));
        }
        snapshotInfo.put("budgetList", budgetSettingAndUseList);

        // EmployeeTaxiRuleInfo构造
        EmployeeTaxiRuleInfo employeeTaxiRuleInfoContract =
            commonContextBuilder.buildEmployeeTaxiRuleInfo(companyId, type,
                carPolicyBean, taxiOrderCheckRule, clientVersion, checkResult.getIsTemporaryApply(),
                checkResult.getCost_attribution_scope(), checkResult.getBudget_cost_attr_type(), isDocking,
                isNewBudget, payModel, usePersonalBudget, sceneCode, category, tripId, taxiOrderCheckReqContract);
        checkResult.setCoupon_type(employeeTaxiRuleInfoContract.getCouponType());

        boolean personalBudgetPayTipsFlag = false;
        try {
            // 自定义字段转化
            List<CustomDimension> customDimensionList =
                ConvertUtils.convertToCustomDimension(taxiOrderCheckReqContract.getCustomer_field_setting_list());
            OrderCostInfoReq orderCostInfoReq =
                beanHelpBuilder.buildOrderCostInfoReq(taxiOrderCheckReqContract.getOrder_id(),
                    taxiOrderCheckReqContract.getCompany_id(), taxiOrderCheckReqContract.getEmployee_id(),
                    taxiOrderCheckReqContract.getApply_id(), taxiOrderCheckReqContract.getTrip_id(),
                    BudgetCategoryTypeEnum.Taxi, taxiRuleCommonManager.getMaxAndInsurancePrice(taxiOrderCheckReqContract),
                    taxiOrderCheckReqContract.getCostInfoString(), clientVersion,
                    employeeTaxiRuleInfoContract.getUse_personal_budget(), null, 1,
                    customDimensionList,
                    JsonUtils.toObjSnake(JsonUtils.toJson(taxiOrderCheckReqContract.getCost_info()), CostInfo.class));
            logger.info("matchTaxiPersonalPaymentPlan req:{}", JsonUtils.toJson(orderCostInfoReq));
            personalBudgetPayTipsFlag = iOrderCostService.matchTaxiPersonalPaymentPlan(orderCostInfoReq);
            logger.info("matchTaxiPersonalPaymentPlan res:{}", personalBudgetPayTipsFlag);
        } catch (Exception e) {
            logger.warn("调用matchTaxiPersonalPaymentPlan异常, ", e);
        }

        //接机
        if(TaxiServerType.AirportPickup.getCode() == taxiOrderCheckReqContract.getType() && taxiOrderCheckReqContract.getScene_category() == 3){
            checkResult.setTaxiTypeName(CoreLanguage.TaxiCheckServiceImpl_Value_AirportTransfer.getMessage());
            checkResult.setTaxiTypeViewName(CoreLanguage.TaxiCheckServiceImpl_Value_AirportTransfer.getMessage());
        }
        //送机
        else if (TaxiServerType.AirportDropOff.getCode() == taxiOrderCheckReqContract.getType() && taxiOrderCheckReqContract.getScene_category() == 3){
            checkResult.setTaxiTypeName(CoreLanguage.TaxiCheckServiceImpl_Value_AirportTransfer.getMessage());
            checkResult.setTaxiTypeViewName(CoreLanguage.TaxiCheckServiceImpl_Value_AirportTransfer.getMessage());
        }

        if(ObjUtils.isNotEmpty(sceneCode)) {
            TaxiManageSetting companySetting = iTaxiCheckV3Service.getTaxiManagerSetting(companyId, sceneCode);
            if (companySetting != null) {
                checkResult.setTaxiTypeName(ObjUtils.toString(companySetting.getSceneName(),
                    companySetting.getViewName()));
                if(StringUtils.isNotEmpty(companySetting.getViewName())){
                    checkResult.setTaxiTypeViewName(companySetting.getViewName());
                }else{
                    checkResult.setTaxiTypeViewName(companySetting.getSceneName());
                }
            }
        }

        checkResult.setEmployeeRuleInfo(employeeTaxiRuleInfoContract);
        if (taxiOrderCheckRule == null || taxiOrderCheckRule.getTaxiRuleListContract() == null
                || taxiOrderCheckRule.getTaxiRuleListContract().getPathLocationInfos() == null
                || taxiOrderCheckRule.getTaxiRuleListContract().getPathLocationInfos().size() < 200
        ) {
            logger.info("用车规则.taxiOrderCheckRule：{}", JsonUtils.toJson(taxiOrderCheckRule));
        }

        // 非费用
        List<TempOrderRuleCheckResult> commonExceedType = Lists.newArrayList();
        //规则校验
        if (carPolicyBean.getRule_limit_flag()) {
            // 接送机或有临时用车审批单不受规则限制
            if (!((TaxiServerType.AirportDropOff.getCode() == type || TaxiServerType.AirportPickup.getCode() == type) && taxiOrderCheckReqContract.getScene_category() == 3)) {
                if (CollectionUtils.isEmpty(validTripList)) {

                    logger.info("非费用(除车型)校验规则开始...");

                    EmployeeContract orderEmployee = taxiCommonCheckContext.getInnerContext().getOrderEmployee();
                    TaxiNotPriceExcludeTypeContext taxiNotPriceExcludeTypeContext =
                        checkContextBuilder.buildTaxiNotPriceExceedContext(taxiOrderCheckReqContract,
                            employeeTaxiRuleInfoContract, taxiOrderCheckRule, orderEmployee.getPhone_num(),
                            taxiOrderCheckReqContract.getPassengers_phone(), "orderCheck");
                    stopWatch.reset();
                    logger.info("orderCheck, taxiNotPriceExcludeTypeContext:{}",
                        JsonUtils.toJson(taxiNotPriceExcludeTypeContext));
                    stopWatch.start();
                    LiteflowResponse taxiNotPriceExcludeTypeCheckChain =
                        flowExecutor.execute2Resp("TaxiNotPriceExcludeTypeCheckChain", null,
                            taxiNotPriceExcludeTypeContext);
                    stopWatch.stop();
                    logger.info("非车型检验用时:{}", stopWatch.getTime());

                    if (!taxiNotPriceExcludeTypeCheckChain.isSuccess()) {
                        Exception e = taxiNotPriceExcludeTypeCheckChain.getCause();
                        logger.error("taxiNotPriceExcludeTypeCheckChain费用类规则校验失败", e);
                        throw new SaasException(TemporaryResponseCode.InnerError.getCode(),
                            TemporaryResponseCode.InnerError.getMsg());
                    }

                    // 非费用(除车型)规则校验
                    commonExceedType = taxiNotPriceExcludeTypeContext.getCheckResultList();
                }
            }
        }
        //强制提交逻辑
        OneKeyTaxiOrderRuleCheckResult checkExceedAuthResult = checkExceedAuth(taxiOrderCheckReqContract);
        logger.info("taxiOrderRuleCheckResult.checkExceedAuthResult:{}", JsonUtils.toJson(checkExceedAuthResult));
        // 保险金额
        BigDecimal insurancePrice = taxiOrderCheckReqContract.getInsurance_price();
        // 可用车型
        List<TaxiTypePriceInfoV2> availableTaxiTypeList = Lists.newArrayList();
        // 不可用车型
        List<TaxiTypePriceInfoV2> unavailableTaxiTypeList = Lists.newArrayList();
        // 超预算个人支付标识
        Boolean budgetPersonPayFlag = false;
        String budgetPersonPayLimitPriceMsg = "";
        TaxiOrderRuleCheckV2Result tmpBudgetCheckResult = null;
        // 超规项列表
        List<ResponseCodeContract> allOrderListResultContracts = Lists.newArrayList();
        Map<Integer, Object> allOrderListResultMap = Maps.newHashMap();

        logger.info("遍历车型校验规则开始, 数量：{}", taxiTypeList.size());
        stopWatch.reset();
        stopWatch.start();
        for (TaxiTypePriceInfoV2 taxiTypePriceInfo : taxiTypeList) {
            taxiTypePriceInfo.setAvailable(true);
            if (ObjUtils.isNull(taxiTypePriceInfo)) {
                continue;
            }

            //券后预估价
            BigDecimal useCouponAfterPrice = taxiTypePriceInfo.getPrice();
            //预估价
            BigDecimal price = taxiTypePriceInfo.getPrice();
            //优惠券金额
            BigDecimal couponAmount = BigDecimal.ZERO;
            //默认抵扣超规个人支付金额
            Boolean isAllowDeductExceed = false;
            //优惠券处理
            if(CollectionUtils.isNotEmpty(taxiTypePriceInfo.getCoupon_list())){
                //获取当前预估费使用的优惠券
                CouponContract couponContract = taxiTypePriceInfo.getCoupon_list().get(0);
                couponAmount = couponContract.getCoupon_amount();
                isAllowDeductExceed = couponContract.getDeduction_type() == 0 ? false : true;
                if(isAllowDeductExceed){
                    useCouponAfterPrice = useCouponAfterPrice.subtract(couponAmount);
                }
            }

            BigDecimal totalPrice = useCouponAfterPrice.add(insurancePrice);
            if (!advancePayment) {
                //账户信息锁定或者订单金额超过余额
                BigDecimal redcouponBalance = ObjUtils.isBlank(account.getRedcouponBalance()) ? BigDecimal.valueOf(0) : account.getRedcouponBalance();
                BigDecimal balance = ObjUtils.isBlank(account.getBalance()) ? BigDecimal.valueOf(0) : account.getBalance();
                BigDecimal totalAccount = balance.add(redcouponBalance);
                if (BigDecimalUtils.fen2yuan(totalAccount).compareTo(totalPrice) < 0) {
                    checkResult.setResCode(GlobalResponseCode.OrderCheckCompanyAccountNoAuth.getCode(), GlobalResponseCode.OrderCheckCompanyAccountNoAuth.getMsg());
                    return checkResult;
                }
            }
            // 超规填写理由不再校验预算 避免覆盖错误提示信息
            if (!ObjUtils.ifNull(checkResult.getIs_exceed(), false)) {
                //预算校验
                TravelOnBusiOrderRuleCheckResult travelOrderBudgetCheckResult = null;
                if (isDocking) {
                    travelOrderBudgetCheckResult = iOrderCheckService.thirdTaxiBudgetCheck(totalPrice, thirdBudgetRestAmount);
                    logger.info("taxiOrderRuleCheckResult.travelOrderBudgetCheckResult:{}", JsonUtils.toJson(travelOrderBudgetCheckResult));
                } else {
                    travelOrderBudgetCheckResult = iOrderCheckService.taxiOnCategoryOrderBudgetCheck(taxiOrderCheckReqContract, budgetSettingAndUseList, totalPrice, advancePayment);
                    logger.info("taxiOrderRuleCheckResult.travelOrderBudgetCheckResult1:{}", JsonUtils.toJson(travelOrderBudgetCheckResult));
                }
                if (GlobalResponseCode.Success.getCode() != travelOrderBudgetCheckResult.getErrCode()) {
                    //特殊处理
                    if (travelOrderBudgetCheckResult.getErrCode() == GlobalResponseCode.OrderCheckOverBudgetCanSubmit.getCode()) {
                        checkResult.setResCode(TemporaryResponseCode.OrderCheckOverBudgetCanSubmit.getCode(), travelOrderBudgetCheckResult.getErrMsg());
                        if (travelOrderBudgetCheckResult.getPersonalPay()) {
                            budgetPersonPayFlag = true;
                            budgetPersonPayLimitPriceMsg = travelOrderBudgetCheckResult.getExceed_msg();
                        }
                    } else if (travelOrderBudgetCheckResult.getErrCode() == GlobalResponseCode.OrderCheckOverBudgetCanNotSubmit.getCode()) {
                        checkResult.setResCode(TemporaryResponseCode.OrderCheckOverBudgetCanNotSubmit.getCode(), travelOrderBudgetCheckResult.getErrMsg());
                    } else {
                        checkResult.setResCode(travelOrderBudgetCheckResult.getErrCode(), travelOrderBudgetCheckResult.getErrMsg());
                    }
                    checkResult.setErrMsgInfo(travelOrderBudgetCheckResult.getErrMsgInfo());
                    if (VersionTool.lessThan(clientVersion, "3.8.3") || !budgetPersonPayFlag) {
                        return checkResult;
                    } else {
                        tmpBudgetCheckResult = new TaxiOrderRuleCheckV2Result();
                        tmpBudgetCheckResult.setResCode(checkResult.getErrCode(), checkResult.getErrMsg());
                        tmpBudgetCheckResult.setErrMsgInfo(checkResult.getErrMsgInfo());
                    }
                }
            }

            //校验服务类型 0：实时、1：预约、2：接机、3：送机  接送机不受规则限制
            if ((TaxiServerType.AirportDropOff.getCode() == type || TaxiServerType.AirportPickup.getCode() == type) && taxiOrderCheckReqContract.getScene_category() == 3) {
                //禁止接送机
                if (!carPolicyBean.getAllow_shuttle()) {
                    checkResult.setResCode(TemporaryResponseCode.OrderTaxiNotAllowShuttle);
                    return checkResult;
                }
                //优惠券处理
                if(taxiTypePriceInfo!= null){
                    if(CollectionUtils.isNotEmpty(taxiTypePriceInfo.getCoupon_list())){
                        //设置当前优惠券可使用状态
                        taxiTypePriceInfo.getCoupon_list().get(0).setAvailable(true);
                    }
                }
                availableTaxiTypeList.add(taxiTypePriceInfo);
                continue;
            }

            //有临时用车审批单 放行
            if (CollectionUtils.isNotEmpty(validTripList)) {
                availableTaxiTypeList.add(taxiTypePriceInfo);
                //优惠券处理
                if(taxiTypePriceInfo!= null){
                    if(CollectionUtils.isNotEmpty(taxiTypePriceInfo.getCoupon_list())){
                        //设置当前优惠券可使用状态
                        taxiTypePriceInfo.getCoupon_list().get(0).setAvailable(true);
                    }
                }
                continue;
            }

            //规则校验
            if (carPolicyBean.getRule_limit_flag()) {
                // 出租车调度费
                BigDecimal taxiSchedulingFeeLimit = new BigDecimal(-1);
                Integer useRule = taxiOrderCheckRule.getUseRule();
                if (useRule == 1) {
                    TaxiRuleListContract taxiRuleListContract = taxiOrderCheckRule.getTaxiRuleListContract();
                    // 出租车调度费
                    taxiSchedulingFeeLimit = taxiRuleListContract.getTaxiSchedulingFee();
                } else if (useRule == 2) {
                    TaxiApplyRule taxiApplyRule = taxiOrderCheckRule.getTaxiApplyRule();
                    // 出租车调度费
                    taxiSchedulingFeeLimit = new BigDecimal(taxiApplyRule.getTaxiSchedulingFee());
                } else {
                    logger.error("用车规则校验类型错误, useRule:{}", useRule);
                }
                //出租车调度费
                BigDecimal taxiSchedulingFee = taxiTypePriceInfo.getTaxi_scheduling_fee();
                if (taxiSchedulingFee != null && taxiSchedulingFee.compareTo(BigDecimal.ZERO) < 0) {
                    //调度费负值
                    checkResult.setResCode(TemporaryResponseCode.OrderTaxiSchedulingFeeError);
                    return checkResult;
                } else if (ObjUtils.toInteger(taxiTypePriceInfo.getCode()) == AllowedTaxiType.TAXISEAT.getCode()) {
                    //出租车
                    if (taxiSchedulingFeeLimit.compareTo(BigDecimal.valueOf(-1)) == 0) {
                        //-1:禁用调度费
                        if (taxiSchedulingFee != null && taxiSchedulingFee.compareTo(BigDecimal.ZERO) > 0) {
                            checkResult.setResCode(TemporaryResponseCode.OrderTaxiSchedulingFeeError);
                            return checkResult;
                        }
                    } else {
                        if (taxiSchedulingFee != null && taxiSchedulingFee.compareTo(taxiSchedulingFeeLimit) > 0) {
                            checkResult.setResCode(TemporaryResponseCode.OrderTaxiSchedulingFeeLimit.getCode(), StrUtils.formatString(TemporaryResponseCode.OrderTaxiSchedulingFeeLimit.getMsg(), taxiSchedulingFeeLimit));
                            return checkResult;
                        }
                    }
                } else {
                    //非出租车类型传调度费
                    if (taxiSchedulingFee != null && taxiSchedulingFee.compareTo(BigDecimal.ZERO) > 0) {
                        checkResult.setResCode(TemporaryResponseCode.OrderTaxiSchedulingFeeError);
                        return checkResult;
                    }
                }

                //费用
                List<TempOrderRuleCheckResult> priceExceedType = new ArrayList<>();
                priceExceedType = taxiRuleCheckManager.checkTaxiPriceExceedTypeTemp(taxiOrderCheckRule, totalPrice,
                        dailyConsumption, taxiOrderCheckReqContract.getRule_day_amount_sumbit(),
                        taxiOrderCheckReqContract.getStart_city_id(), taxiOrderCheckReqContract.getArrival_city_id(),
                        taxiTypePriceInfo, new TaxiModifyDestinationCheckResContract(), employeeTaxiRuleInfoContract, customFormContext);
                logger.info("taxiOrderRuleCheckResult.priceExceedType:{}", JsonUtils.toJson(priceExceedType));
                if (ruleDayUsedCheckRes != null) {
                    priceExceedType.add(ruleDayUsedCheckRes);
                }
                //非费用(车型)
                List<TempOrderRuleCheckResult> notPriceExceedType = taxiRuleCheckManager.checkTaxiAllowedTaxiType(taxiOrderCheckRule, useCouponAfterPrice, taxiTypePriceInfo.getCode());
                logger.info("taxiOrderRuleCheckResult.notPriceExceedType:{}", JsonUtils.toJson(notPriceExceedType));
                notPriceExceedType.addAll(commonExceedType);

                // 确认用车额度合并
                if (CollectionUtils.isNotEmpty(priceExceedType)) {
                    for (TempOrderRuleCheckResult tempOrderRuleCheckResult : priceExceedType) {
                        if (TemporaryResponseCode.OrderTaxiUseRulePriceLimitTip.getCode() == tempOrderRuleCheckResult.getErrCode()) {
                            checkResult.setResCode(tempOrderRuleCheckResult.getErrCode(), tempOrderRuleCheckResult.getErrMsg());
                            ErrMsgInfo errMsgInfo = new ErrMsgInfo();
                            errMsgInfo.setTitle(CoreLanguage.TaxiCheckServiceImpl_Value_UsedCarMoneyUnion.getMessage());
                            errMsgInfo.setErr_code_list(allOrderListResultContracts);
                            checkResult.setErrMsgInfo(errMsgInfo);
                            return checkResult;
                        }
                    }
                }

                if (Objects.equals(isOpenExceedConfig, true)) {
                    if (taxiCheckResManager.getTaxiTypeCheckRes(checkResult, checkExceedAuthResult,
                        unavailableTaxiTypeList,
                        allOrderListResultContracts,
                        allOrderListResultMap, taxiTypePriceInfo, priceExceedType, notPriceExceedType)) {
                        continue;
                    }
                } else {
                    //是否有规则超规
                    Boolean hasExceedRule = CollectionUtils.isNotEmpty(priceExceedType) || CollectionUtils.isNotEmpty(notPriceExceedType);
                    //是否有非费用规则超规
                    Boolean hasNotPriceExceedRule = CollectionUtils.isNotEmpty(notPriceExceedType);

                    //超标规则（1：禁止 2：超规填写理由下单 3:超额个人支付）
                    Integer exceedBuyType = carPolicyBean.getExceed_buy_type();
                    //个人支付
                    Boolean personalPay = carPolicyBean.getPersonal_pay();
                    if (personalPay && ExceedAllowType.ExceedAllowWithFee.getCode() == exceedBuyType) {
                        checkResult.setResCode(TemporaryResponseCode.OrderTaxiEmployeeRuleDataError.getCode(), TemporaryResponseCode.OrderTaxiEmployeeRuleDataError.getMsg());
                        return checkResult;
                    }
                    if (!personalPay && ExceedAllowType.ExceedAllowWithFee.getCode() == exceedBuyType) {
                        exceedBuyType = ExceedAllowType.ExceedNotAllow.getCode();
                        personalPay = true;
                    }
                    // 个人支付开关优先级  总预估费组件配置的个人支付 高于 消费权限模板配置的个人支付
                    if (customFormContext.getTotalEstimatedOpen() && customFormContext.getPersonalPay()) {
                        personalPay = customFormContext.getPersonalPay();
                        employeeTaxiRuleInfoContract.setPersonalPay(personalPay);
                    }

                    //超标禁止下单
                    if (TravelExceedType.NotAllowed.getCode() == exceedBuyType
                        && ((!personalPay && hasExceedRule) || (personalPay && hasNotPriceExceedRule))) {
                        List<TempOrderRuleCheckResult> newList = new ArrayList<>();
                        if (CollectionUtils.isNotEmpty(notPriceExceedType)) {
                            newList.addAll(notPriceExceedType);
                        }
                        if (CollectionUtils.isNotEmpty(priceExceedType)) {
                            newList.addAll(priceExceedType);
                        }
                        List<ResponseCodeContract> tempOrderListResultContracts = new ArrayList<>();
                        for (TempOrderRuleCheckResult tempOrderRuleCheckResult : newList) {
                            ResponseCodeContract tempOrderListResultContract = new ResponseCodeContract();
                            tempOrderListResultContract.setCode(tempOrderRuleCheckResult.getErrCode());
                            tempOrderListResultContract.setContent(tempOrderRuleCheckResult.getErrMsg());
                            tempOrderListResultContract.setType(IconChangeUtil.getTypeByCode(tempOrderRuleCheckResult.getErrCode()));
                            tempOrderListResultContracts.add(tempOrderListResultContract);
                            if (!allOrderListResultMap.containsKey(tempOrderRuleCheckResult.getErrCode())) {
                                allOrderListResultMap.put(tempOrderRuleCheckResult.getErrCode(), tempOrderRuleCheckResult);
                                allOrderListResultContracts.add(tempOrderListResultContract);
                            }
                        }
                        Map<String, Object> codeListMap = new HashMap<>();
                        codeListMap.put("codeList", tempOrderListResultContracts);
                        logger.info("车型:({})，供应商:{}，无法继续下单，超规规则如下：{}", taxiTypePriceInfo.getCode(), JsonUtils.toJson(taxiTypePriceInfo), JsonUtils.toJson(tempOrderListResultContracts));
                        taxiTypePriceInfo.setReason_code(TemporaryResponseCode.OrderCanNotSubmit.getCode());
                        taxiTypePriceInfo.setReason(JsonUtils.toJson(codeListMap));
                        unavailableTaxiTypeList.add(taxiTypePriceInfo);
                        continue;
                    }
                    //超标需要理由
                    else if (TravelExceedType.AllowedNeedReason.getCode() == exceedBuyType
                        && ((!personalPay && hasExceedRule) || (personalPay && hasNotPriceExceedRule))) {
                        List<TempOrderRuleCheckResult> newList = new ArrayList<>();
                        if (CollectionUtils.isNotEmpty(notPriceExceedType)) {
                            newList.addAll(notPriceExceedType);
                        }
                        if (CollectionUtils.isNotEmpty(priceExceedType)) {
                            newList.addAll(priceExceedType);
                        }
                        List<ResponseCodeContract> tempOrderListResultContracts = new ArrayList<>();
                        for (TempOrderRuleCheckResult tempOrderRuleCheckResult : newList) {
                            ResponseCodeContract tempOrderListResultContract = new ResponseCodeContract();
                            tempOrderListResultContract.setCode(tempOrderRuleCheckResult.getErrCode());
                            tempOrderListResultContract.setContent(tempOrderRuleCheckResult.getErrMsg());
                            tempOrderListResultContract.setType(IconChangeUtil.getTypeByCode(tempOrderRuleCheckResult.getErrCode()));
                            tempOrderListResultContracts.add(tempOrderListResultContract);
                            if (!allOrderListResultMap.containsKey(tempOrderRuleCheckResult.getErrCode())) {
                                allOrderListResultMap.put(tempOrderRuleCheckResult.getErrCode(), tempOrderRuleCheckResult);
                                allOrderListResultContracts.add(tempOrderListResultContract);
                            }
                        }
                        taxiTypePriceInfo.setReason_code(checkExceedAuthResult.getErrCode());
                        taxiTypePriceInfo.setReason(checkExceedAuthResult.getErrMsg());
                        unavailableTaxiTypeList.add(taxiTypePriceInfo);
                        logger.info("车型:({})，供应商:{}，超规填写理由，超规规则如下：{}", taxiTypePriceInfo.getCode(), JsonUtils.toJson(taxiTypePriceInfo), JsonUtils.toJson(tempOrderListResultContracts));
                        //超规则返回
                        if (checkExceedAuthResult.getErrCode() != TemporaryResponseCode.Success.getCode()) {
                            checkResult.setIs_exceed(true);
                            checkResult.setErrMsg(checkExceedAuthResult.getErrMsg());
                            checkResult.setRuleExceedTips("超出您" + checkResult.getTaxiTypeViewName() + "场景规则");
                            checkResult.setErrCode(checkExceedAuthResult.getErrCode());
                            continue;
                        }
                    }
                }

            }else{
                //优惠券处理
                if(taxiTypePriceInfo!= null){
                    if(CollectionUtils.isNotEmpty(taxiTypePriceInfo.getCoupon_list())){
                        //设置当前优惠券可使用状态
                        taxiTypePriceInfo.getCoupon_list().get(0).setAvailable(true);
                    }
                }
            }
            availableTaxiTypeList.add(taxiTypePriceInfo);
        }
        stopWatch.stop();
        logger.info("遍历车型校验规则结束, 用时：{}ms", stopWatch.getTime());

        //按最大预估价计算申请单和用车规则的占用额度
        boolean allowExceedWithNeedReason;
        if (Objects.equals(isOpenExceedConfig, true)) {
            allowExceedWithNeedReason = Objects.equals(taxiOrderCheckReqContract.getExceed_submit(), true) &&
                checkExceedAuthResult.getErrCode() == TemporaryResponseCode.Success.getCode();
        } else {
            allowExceedWithNeedReason = TravelExceedType.AllowedNeedReason.getCode() == carPolicyBean.getExceed_buy_type()
                && checkExceedAuthResult.getErrCode() == TemporaryResponseCode.Success.getCode();
        }

        TaxiCheckProcessor.OrderOccupyAmountInfo orderOccupyAmountInfo =
            taxiCheckProcessor.computeOrderOccupyAmount(availableTaxiTypeList, taxiOrderCheckRule, dailyConsumption,
                taxiOrderCheckReqContract.getStart_city_id(),
                taxiOrderCheckReqContract.getArrival_city_id(), insurancePrice, allowExceedWithNeedReason,
                customFormContext);

        if(!Objects.isNull(orderOccupyAmountInfo)) {
            checkResult.setCompanyPayPrice(orderOccupyAmountInfo.getCompanyPayPrice());
            checkResult.setApplyAmount(orderOccupyAmountInfo.getApplyAmount());
            checkResult.setRuleAmount(orderOccupyAmountInfo.getRuleAmount());
        }

        // 接送机
        if ((TaxiServerType.AirportDropOff.getCode() == type || TaxiServerType.AirportPickup.getCode() == type) && taxiOrderCheckReqContract.getScene_category() == 3) {
            if (budgetPersonPayFlag) {
                return tmpBudgetCheckResult;
            }
            return checkResult;
        }
        // 超规信息不为空
        if (ObjUtils.isNotEmpty(allOrderListResultContracts)) {
            ErrMsgInfo errMsgInfo = new ErrMsgInfo();
            errMsgInfo.setTitle(checkResult.getErrMsg());
            errMsgInfo.setErr_code_list(allOrderListResultContracts);
            checkResult.setErrMsgInfo(errMsgInfo);
        }
        // 超规填写理由
        if (checkResult.getIs_exceed()) {
            ErrMsgInfo errMsgInfo = new ErrMsgInfo();
            errMsgInfo.setTitle(checkResult.getErrMsg());
            errMsgInfo.setErr_code_list(allOrderListResultContracts);
            checkResult.setErrMsgInfo(errMsgInfo);
            return checkResult;
        }
        // 无可用车型 提示超规无法下单
        if (ObjUtils.isEmpty(availableTaxiTypeList)) {
            checkResult.setResCode(TemporaryResponseCode.OrderCanNotSubmit);
            ErrMsgInfo errMsgInfo = new ErrMsgInfo();
            errMsgInfo.setTitle(checkResult.getErrMsg());
            errMsgInfo.setErr_code_list(allOrderListResultContracts);
            checkResult.setErrMsgInfo(errMsgInfo);
            return checkResult;
        }
        // 部分超规
        if (ObjUtils.isNotEmpty(unavailableTaxiTypeList) && checkExceedAuthResult.getErrCode() != TemporaryResponseCode.Success.getCode()) {
            // 部分超规弹框提示
            if (taxiOrderCheckReqContract.getPart_order_sumbit() == null || !taxiOrderCheckReqContract.getPart_order_sumbit()) {
                checkResult.setResCode(TemporaryResponseCode.OrderTaxiPartOrderTip);
                return checkResult;
            }
            // 规则内部分叫车
            checkResult.setAllAvailable(false);
            checkResult.setUnavailableTaxiTypeList(unavailableTaxiTypeList);
        }
        checkResult.setAvailableTaxiTypeList(availableTaxiTypeList);
        // 个人支付提示及预算
        if (VersionTool.greaterThanOrEqualTo(clientVersion, "3.8.3") && budgetPersonPayFlag) {
            if (taxiOrderCheckReqContract.getPersonalpay_sumbit() == null || !taxiOrderCheckReqContract.getPersonalpay_sumbit()) {
                TaxiLimitPayTipContract taxiLimitPayTipContract = taxiRuleCheckManager.priceLimitPayTips(
                    employeeTaxiRuleInfoContract, advancePayment, taxiOrderCheckRule, personalBudgetPayTipsFlag);
                logger.info("1taxiOrderRuleCheckResult.taxiLimitPayTipContract:{}", JsonUtils.toJson(taxiLimitPayTipContract));

                if (taxiLimitPayTipContract.isTip_flag()) {
                    PriceLimitPayTips priceLimitPayTips = taxiRuleCheckManager.budgetPersonPayTips(employeeTaxiRuleInfoContract,
                            budgetPersonPayLimitPriceMsg, advancePayment);
                    checkResult.setResCode(priceLimitPayTips.getCode(), priceLimitPayTips.getMsg());
                    ErrMsgInfo errMsgInfo = new ErrMsgInfo();
                    errMsgInfo.setTitle(priceLimitPayTips.getTitle());
                    checkResult.setErrMsgInfo(errMsgInfo);
                    return checkResult;
                } else {
                    return tmpBudgetCheckResult;
                }
            }
        }
        // 个人支付提示
        if (taxiOrderCheckReqContract.getPersonalpay_sumbit() == null || !taxiOrderCheckReqContract.getPersonalpay_sumbit()) {
            TaxiLimitPayTipContract taxiLimitPayTipContract =
                    taxiRuleCheckManager.priceLimitPayTips(employeeTaxiRuleInfoContract, advancePayment,
                        taxiOrderCheckRule, personalBudgetPayTipsFlag);
            logger.info("2taxiOrderRuleCheckResult.taxiLimitPayTipContract:{}", JsonUtils.toJson(taxiLimitPayTipContract));
            if (taxiLimitPayTipContract.isTip_flag()) {
                TemporaryResponseCode tip;
                if (advancePayment) {
                    tip = TemporaryResponseCode.OrderCheckPersonalPayTip2;
                } else {
                    tip = TemporaryResponseCode.OrderCheckPersonalPayTip;
                }
                checkResult.setResCode(tip, taxiLimitPayTipContract.getTip().getContent());
                return checkResult;
            }
        }
        // 预约用车
        if (TaxiServerType.Order.getCode() == type && ObjUtils.isEmpty(applyId)) {
            TaxiRuleListContract taxiRuleListContract = taxiOrderCheckRule.getTaxiRuleListContract();
            // 提前上车限制提示
            if (carPolicyBean.getRule_limit_flag() && taxiOrderCheckRule.getUseRule() == 1 && !checkResult.getIsTemporaryApply() && taxiRuleListContract.getLimitAdvance() != null && taxiRuleListContract.getLimitAdvance()) {
                // 校验是否配置用车时段限制
                List<RuleTimeRange> timeRange = taxiRuleListContract.getTimeRange();
                // 限制了时段规则
                if (ObjUtils.isNotEmpty(timeRange)) {
                    if (taxiOrderCheckReqContract.getLimit_advance_sumbit() == null || !taxiOrderCheckReqContract.getLimit_advance_sumbit()) {
                        TemporaryResponseCode tip;
                        if (advancePayment) {
                            tip = TemporaryResponseCode.OrderTaxiLimitAdvanceTip2;
                        } else {
                            tip = TemporaryResponseCode.OrderTaxiLimitAdvanceTip;
                        }
                        checkResult.setResCode(tip);
                        return checkResult;
                    }
                }
            }
        }
        return checkResult;
    }

    /**
     * 用车订单检查返回场景码,名称,规则id,名称
     */
    private void setSceneAndRuleInfo(TaxiOrderRuleCheckV2Result checkResult, CarPolicyBean carPolicyBean,
                                     Integer sceneCode) {
        if (checkResult == null || carPolicyBean == null || sceneCode == null) {
            return;
        }
        List<RuleIdDto> rules = carPolicyBean.getRule_infos();
        if (ObjUtils.isEmpty(rules)) {
            return;
        }
        for (RuleIdDto rule : rules) {
            if (rule == null || rule.getType() == null || !sceneCode.equals(rule.getType().getKey())) {
                continue;
            }
            checkResult.setTaxiType(rule.getType().getKey());
            checkResult.setTaxi_type_name(rule.getType().getValue());
            if (rule.getRule_info() == null) {
                return;
            }
            RuleIdDto.RuleInfo r = rule.getRule_info().get(0);
            if (r == null) {
                return;
            }
            checkResult.setRule_id(r.getRule_id());
            checkResult.setRule_name(r.getRule_name());
        }
    }

    /**
     * 检查提示升级版本
     *
     * @param type
     * @param carPolicyBean
     * @param taxiOrderCheckRule
     * @param clientVersion
     */
    private TaxiOrderRuleCheckV2Result checkUpdate(Integer type, CarPolicyBean carPolicyBean, TaxiOrderCheckRule taxiOrderCheckRule, String clientVersion) {
        TaxiOrderRuleCheckV2Result checkResult = new TaxiOrderRuleCheckV2Result();
        checkResult.setResCode(TemporaryResponseCode.Success);
        // 预约用车
        if (TaxiServerType.Order.getCode() == type) {
            TaxiRuleListContract taxiRuleListContract = taxiOrderCheckRule.getTaxiRuleListContract();
            // 提前上车限制提示
            if (carPolicyBean.getRule_limit_flag() && taxiOrderCheckRule.getUseRule() == 1 && taxiRuleListContract.getLimitAdvance() != null && taxiRuleListContract.getLimitAdvance()) {
                // 校验是否配置用车时段限制
                List<RuleTimeRange> timeRange = taxiRuleListContract.getTimeRange();
                // 限制了时段规则
                if (ObjUtils.isNotEmpty(timeRange)) {
                    // 如果是老版本，直接提示用户升级使用
                    String version330 = "3.3.0";
                    if (VersionTool.compare(clientVersion, version330) < 0) {
                        checkResult.setResCode(GlobalResponseCode.ApplyCenterAlert.getCode(), GlobalResponseCode.ApplyCenterAlert.getMsg());
                        return checkResult;
                    }
                }
            }
        }
        return checkResult;
    }

}
