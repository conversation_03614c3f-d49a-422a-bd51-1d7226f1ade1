package com.fenbeitong.saas.core.contract.assistant;

import com.alibaba.fastjson.annotation.JSONField;
import com.fenbeitong.saas.api.model.assistant.AssistantConfigDTO;
import com.fenbeitong.saas.core.model.saasplus.AssistantConfig;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> 2022/4/12
 */
@Data
public class AssistantConfigContract {

    private String id;

    private String companyId;

    private String companyName;

    private Integer status;

    private String operatorId;

    private String operatorName;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    public static AssistantConfigContract fromModel(AssistantConfig assistantConfig) {
        AssistantConfigContract assistantConfigContract = new AssistantConfigContract();
        assistantConfigContract.setId(assistantConfig.getId());
        assistantConfigContract.setCompanyId(assistantConfig.getCompanyId());
        assistantConfigContract.setCompanyName(assistantConfig.getCompanyName());
        assistantConfigContract.setStatus(assistantConfig.getStatus());
        assistantConfigContract.setOperatorId(assistantConfig.getOperatorId());
        assistantConfigContract.setOperatorName(assistantConfig.getOperatorName());
        assistantConfigContract.setCreateTime(assistantConfig.getCreateTime());
        assistantConfigContract.setUpdateTime(assistantConfig.getUpdateTime());
        return assistantConfigContract;
    }

    public static AssistantConfigContract fromModel(AssistantConfigDTO assistantConfig) {
        AssistantConfigContract assistantConfigContract = new AssistantConfigContract();
        assistantConfigContract.setId(assistantConfig.getId());
        assistantConfigContract.setCompanyId(assistantConfig.getCompanyId());
        assistantConfigContract.setCompanyName(assistantConfig.getCompanyName());
        assistantConfigContract.setStatus(assistantConfig.getStatus());
        assistantConfigContract.setOperatorId(assistantConfig.getOperatorId());
        assistantConfigContract.setOperatorName(assistantConfig.getOperatorName());
        assistantConfigContract.setCreateTime(assistantConfig.getCreateTime());
        assistantConfigContract.setUpdateTime(assistantConfig.getUpdateTime());
        return assistantConfigContract;
    }

    public AssistantConfig toModel() {
        AssistantConfig assistantConfig = new AssistantConfig();
        assistantConfig.setId(getId());
        assistantConfig.setCompanyId(getCompanyId());
        assistantConfig.setCompanyName(getCompanyName());
        assistantConfig.setStatus(getStatus());
        assistantConfig.setOperatorId(getOperatorId());
        assistantConfig.setOperatorName(getOperatorName());
        assistantConfig.setCreateTime(getCreateTime());
        assistantConfig.setUpdateTime(getUpdateTime());
        return assistantConfig;
    }

    public AssistantConfigDTO toDTOModel() {
        AssistantConfigDTO assistantConfig = new AssistantConfigDTO();
        assistantConfig.setId(getId());
        assistantConfig.setCompanyId(getCompanyId());
        assistantConfig.setCompanyName(getCompanyName());
        assistantConfig.setStatus(getStatus());
        assistantConfig.setOperatorId(getOperatorId());
        assistantConfig.setOperatorName(getOperatorName());
        assistantConfig.setCreateTime(getCreateTime());
        assistantConfig.setUpdateTime(getUpdateTime());
        return assistantConfig;
    }

}
