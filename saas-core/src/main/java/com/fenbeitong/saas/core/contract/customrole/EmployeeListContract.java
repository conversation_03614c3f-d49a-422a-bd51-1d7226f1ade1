package com.fenbeitong.saas.core.contract.customrole;

import com.fenbeitong.saas.core.contract.user.EmployeeInfoContract;

import java.util.List;


public class EmployeeListContract {
    List<EmployeeInfoContract> employees;

    private Integer taxiRuleId;

    private Integer pageIndex;

    private Integer pageSize;

    private Integer totalCount;

    public List<EmployeeInfoContract> getEmployees() {
        return employees;
    }

    public void setEmployees(List<EmployeeInfoContract> employees) {
        this.employees = employees;
    }

    public Integer getTaxiRuleId() {
        return taxiRuleId;
    }

    public void setTaxiRuleId(Integer taxiRuleId) {
        this.taxiRuleId = taxiRuleId;
    }

    public EmployeeListContract() {
    }

    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public EmployeeListContract(List<EmployeeInfoContract> employees, Integer pageIndex, Integer pageSize) {
        this.employees = employees;
        this.pageIndex = pageIndex;
        this.pageSize = pageSize;
    }

    public EmployeeListContract(List<EmployeeInfoContract> employees, Integer taxiRuleId) {
        this.employees = employees;
        this.taxiRuleId = taxiRuleId;
    }
}
