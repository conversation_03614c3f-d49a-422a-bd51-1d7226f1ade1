package com.fenbeitong.saas.core.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.bank.api.model.CreateAppPaymentReqDto;
import com.fenbeitong.bank.api.model.InvoiceDetailReq;
import com.fenbeitong.bank.api.service.IBankPaymentService;
import com.fenbeitong.fenbeipay.api.model.dto.acctpublic.dech.resp.AcctPublicQueryDetailRespDTO;
import com.fenbeitong.fenbeipay.api.service.acctpublic.IAcctPublicSearchDechService;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.saas.entity.CostAttribution;
import com.fenbeitong.finhub.common.saas.entity.CostAttributionGroup;
import com.fenbeitong.finhub.common.saas.entity.CostCategory;
import com.fenbeitong.finhub.common.saas.entity.CostInfo;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import com.fenbeitong.finhub.common.utils.PdfUtils;
import com.fenbeitong.invoice.api.model.detail.FbtQueryInvoiceDetailVO;
import com.fenbeitong.invoice.api.model.dto.BindTradeAndInvoiceResRPCDTO;
import com.fenbeitong.invoice.api.model.dto.FbtInvoicePublicReqRPCDTO;
import com.fenbeitong.invoice.api.model.dto.InvoicePicResRPCDTO;
import com.fenbeitong.invoice.api.model.dto.TradeRelationInvoiceReqRPCDTO;
import com.fenbeitong.invoice.api.service.IFbtInvoicePublicService;
import com.fenbeitong.invoice.api.service.IFbtInvoiceService;
import com.fenbeitong.invoice.api.service.IInvoiceTradeService;
import com.fenbeitong.saas.api.model.dto.reason.ReasonConfig;
import com.fenbeitong.saas.core.common.SaasHttpContext;
import com.fenbeitong.saas.core.common.base.SaasException;
import com.fenbeitong.saas.core.common.constant.GlobalResponseCode;
import com.fenbeitong.saas.core.common.constant.SaasApplyConstant;
import com.fenbeitong.saas.core.common.constant.SaasFlowConstant;
import com.fenbeitong.saas.core.common.constant.SaasMessageConstant;
import com.fenbeitong.saas.core.contract.apply.*;
import com.fenbeitong.saas.core.contract.applyflow.ApplyFlowApplicateResponseContract;
import com.fenbeitong.saas.core.contract.applyflow.CompanyApplyFlowItemSetV2RequestContract;
import com.fenbeitong.saas.core.contract.applyflow.CompanyApplyFlowSetV2RequestContract;
import com.fenbeitong.saas.core.contract.common.CostAttributionInfo;
import com.fenbeitong.saas.core.contract.common.IdNameContract;
import com.fenbeitong.saas.core.contract.common.PushContract;
import com.fenbeitong.saas.core.contract.message.MessageSaveContract;
import com.fenbeitong.saas.core.contract.message.inner.ApplyInfo;
import com.fenbeitong.saas.core.contract.messagesettings.ApplySetupContract;
import com.fenbeitong.saas.core.contract.reason.ApplyPaymentConfig;
import com.fenbeitong.saas.core.dao.common.adapter.ApplyAdapterMapper;
import com.fenbeitong.saas.core.language.CoreLanguage;
import com.fenbeitong.saas.core.language.LanguageUtils;
import com.fenbeitong.saas.core.language.MessageLanguageEnum;
import com.fenbeitong.saas.core.model.enums.ApplyFlowUserItemStatus;
import com.fenbeitong.saas.core.model.enums.CompanyApplyType;
import com.fenbeitong.saas.core.model.enums.CostAttributionCategory;
import com.fenbeitong.saas.core.model.enums.apply.*;
import com.fenbeitong.saas.core.model.enums.message.MessageType;
import com.fenbeitong.saas.core.model.enums.message.SenderType;
import com.fenbeitong.saas.core.model.enums.reason.ApplyPaymentEnum;
import com.fenbeitong.saas.core.model.enums.reason.ReasonType;
import com.fenbeitong.saas.core.model.enums.user.UserRole;
import com.fenbeitong.saas.core.model.saas.*;
import com.fenbeitong.saas.core.service.*;
import com.fenbeitong.saas.core.utils.flow.FlowCheckUtil;
import com.fenbeitong.saas.core.utils.notice.TemplateConstant;
import com.fenbeitong.saas.core.utils.tools.*;
import com.fenbeitong.usercenter.api.model.dto.costcenter.CostCenterDto;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.fenbeitong.usercenter.api.model.dto.payment.CompanyPaymentCheckDTO;
import com.fenbeitong.usercenter.api.model.dto.payment.CompanyPaymentContractVO;
import com.fenbeitong.usercenter.api.model.dto.payment.CompanyPaymentProofVO;
import com.fenbeitong.usercenter.api.model.dto.payment.CompanyPaymentSupplierVO;
import com.fenbeitong.usercenter.api.model.enums.payment.CompanyPaymentContractSupplierState;
import com.fenbeitong.usercenter.api.service.costcenter.ICostCenterService;
import com.fenbeitong.usercenter.api.service.payment.ICompanyPaymentService;
import com.google.common.collect.Lists;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.net.HttpClientUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.StrUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Transactional(transactionManager = "dynamic", readOnly = false, rollbackFor = Exception.class)
public class ApplyPaymentServiceImpl implements IApplyPaymentService {

    private static final Logger logger = LoggerFactory.getLogger(ApplyPaymentServiceImpl.class);
    private static final String URL_TEMPLATE_DETAIL = HostPropertyConfigTool.HOST_HARMONY + "/harmony/mail/template/name/detail";

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyOrderMapper applyOrderMapper;

    @Autowired
    private IApplyFlowService applyFlowService;

    @Autowired
    IUserService userService;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyApproverMapMapper applyApproverMapMapper;

    @Autowired
    IPushService pushService;

    @Autowired
    IMessageService messageService;

    @Autowired
    private IApplyFlowV2Service applyFlowV2Service;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyOrderLogExtMapper applyOrderLogExtMapper;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyOrderExtMapper applyOrderExtMapper;

    @Autowired
    private ApplyAdapterMapper applyAdapterMapper;

    @Autowired
    private ICustomRoleService customRoleService;

    @Autowired
    private IBaseOrganizationService iBaseOrganizationService;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyOrderLogMapper applyOrderLogMapper;

    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyTripInfoMapper applyTripInfoMapper;

    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyTripInfoExtMapper applyTripInfoExtMapper;

    @Autowired
    IMessageSetupService messageSetupService;

    @Autowired
    IBaseOrganizationService baseOrganizationService;

    @Autowired
    ICustomReasonService customReasonService;

    @Autowired
    ICompanyPaymentService iCompanyPaymentService;

    @Autowired
    IAcctPublicSearchDechService iAcctPublicSearchDechService;

    @Autowired
    IBankPaymentService iBankPaymentService;

    @Autowired
    IFbtInvoiceService iFbtInvoiceService;

    @Autowired
    IInvoiceTradeService iInvoiceTradeService;

    @Autowired
    private IFbtInvoicePublicService iFbtInvoicePublicService;

    @Autowired
    private ICostCenterService iCostCenterService;

    @Autowired
    private IApplyOrderService iApplyOrderService;

    @Autowired
    private IApplyV2Service applyV2Service;

    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyCostAttributionExtMapper applyCostAttributionExtMapper;

    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyCostAttributionMapper applyCostAttributionMapper;

    public void setMessageService(IMessageService messageService) {
        this.messageService = messageService;
    }


    /**
     * 创建申请单
     *
     * @param applyV2Contract
     * @return
     */
    @Override
    //@Transactional(value = "saas")
    public GlobalResponseCode create(String token, ApplyV2Contract applyV2Contract, String userId, String companyId, String ip, String clientHeadVersion, String userName) throws SaasException {
        FlowCheckUtil.check(companyId, clientHeadVersion);
        //检查数据信息
        GlobalResponseCode code = checkApplyData(applyV2Contract, userId, companyId, clientHeadVersion);
        if (code != GlobalResponseCode.Success) {
            return code;
        }
        ApplyOrderV2Contract applyOrderContract = applyV2Contract.getApply();
        String applyId = applyOrderContract.getId();
        if (StringUtils.isBlank(applyId)) {
            applyId = IDTool.CreateUniqueID();
        }
        applyOrderContract.setEmployee_id(userId);
        applyOrderContract.setCompany_id(companyId);
        ApplyOrder record = applyOrderMapper.selectByPrimaryKey(applyId);
        if (ObjUtils.isNotEmpty(record) && record.getState() == 2) {
            return GlobalResponseCode.ApplyOrderIsSumbit;
        }
        Date now = new Date();
        // 保存行程信息
        insertTripContractList(applyV2Contract, applyId, now, clientHeadVersion);
        //草稿
        if (applyOrderContract.getState() == ApplyStatus.Draft.getValue()) {
            ApplyOrderV2Contract applyOrderV2Contract = applyV2Contract.getApply();
            applyOrderV2Contract.setEmployee_id(userId);
            applyOrderV2Contract.setCompany_id(companyId);
            ApplyOrder applyOrder = applyOrderV2Contract.ToModel();
            applyOrder.setUpdateTime(new Date());
            applyOrder.setApplicantName(userName);
            applyOrder.setRootApplyOrderId(applyId);
            if (ObjUtils.isEmpty(applyOrder.getBudget())) {
                applyOrder.setBudget(0);
            }
            if (ObjUtils.isNotEmpty(record)) {
                applyOrderMapper.updateByPrimaryKeySelective(applyOrder);
            } else {
                applyOrder.setCreateTime(new Date());
                applyOrder.setId(applyId);
                applyOrderContract.setId(applyId);
                applyOrderMapper.insertSelective(applyOrder);
            }
            return GlobalResponseCode.Success;
        }
        Integer logId = null;
        ApplyType applyType = ApplyType.valueOf(applyOrderContract.getType().intValue());
        CompanyApplyType companyApplyType = CompanyApplyType.valueOf(applyOrderContract.getFlow_type());
        if (companyApplyType == CompanyApplyType.Unknown) {
            companyApplyType = CompanyApplyType.Elastic;
        }
        applyOrderContract.setFlow_type(companyApplyType.getValue());
        //如果是提交审批保存审批流设置和审批单日志记录
        String approverId = applyOrderContract.getApprover_id();
        CompanyApplyFlowSetV2RequestContract flowRequest = applyOrderContract.getFlow();
        if (flowRequest == null) {
            flowRequest = new CompanyApplyFlowSetV2RequestContract();
        }
        flowRequest.setCompany_apply_type(companyApplyType.getValue());
        flowRequest.setApply_type(applyType.getValue());
        flowRequest.setBudget(applyOrderContract.getBudget());
        ApplyFlowApplicateResponseContract flowResponse = null;
        //判断审批流的类型
        Integer category = applyOrderContract.getCost_attribution_category();
        String costAttributionId = applyOrderContract.getCost_attribution_id();
        //处理超规类型
        Integer bussinessType = applyOrderContract.getType();
        //查询应用审批流
        flowResponse = applyFlowV2Service.applicateFlow(SaasFlowConstant.SETTING_TYPE_PAYMENT, flowRequest, applyId, userId, companyId, ip, category, costAttributionId, ExceedBuyType.UnSupernormal.getValue(), bussinessType, applyId);
        if (flowResponse != null && !StringTool.isNullOrEmpty(flowResponse.getApprover())) {
            approverId = flowResponse.getApprover();
        }
        //设置当前审批人
        logId = flowResponse.getNextLogId();
        if (StringUtils.isNotBlank(approverId)) {
            applyOrderContract.setApprover_id(approverId);
        }
        applyOrderContract.setLog_id(ObjUtils.toLong(flowResponse.getNextLogId()));
        if (companyApplyType == CompanyApplyType.Elastic) {
            if (approverId != null && approverId.length() > 0) {
                int logSort = 100;
                List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectListByCreateTimeDesc(applyId);
                if (!CollectionUtils.isEmpty(applyOrderLogList)) {
                    Integer sort = applyOrderLogList.get(0).getSort();
                    if (sort != null) {
                        logSort = sort + 100;
                    }
                }
                //检测审批人是否属于当前这个公司
                Boolean isApproverInCompany = userService.isUserInCompany(approverId, companyId);
                if (!isApproverInCompany) {
                    return GlobalResponseCode.ApplyApproverNotInCompany;
                }
                ApplyOrderLog log = new ApplyOrderLog();
                log.setApplyOrderId(applyId);
                log.setRootApplyOrderId(applyId);
                log.setCreateTime(now);
                log.setIp(ip);
                log.setSponsorId(userId);
                log.setReceiverId(approverId);
                log.setCheckReason(null);
                log.setAction(ApplyLogAction.Submit.getValue());
                log.setSort(logSort);
                applyOrderLogMapper.insertSelective(log);

                ApplyOrderLog logApprove = new ApplyOrderLog();
                logApprove.setApplyOrderId(applyId);
                logApprove.setRootApplyOrderId(applyId);
                logApprove.setIp(ip);
                logApprove.setSponsorId(approverId);
                logApprove.setReceiverId("");
                logApprove.setCheckReason(null);
                logApprove.setAction(ApplyLogAction.Approval.getValue());
                logApprove.setSort(logSort + 100);
                applyOrderLogMapper.insertSelective(logApprove);
                logId = logApprove.getId();
                applyOrderContract.setLog_id(ObjUtils.toLong(logId));
            }
        }
        //整理数据
        clearApplyData(applyV2Contract);
        //待审核装填
        ApplyStatus applyState = ApplyStatus.PendingAudit;
        ApplyLogAction action = ApplyLogAction.Submit;
        ApplyOrder apply = applyOrderContract.ToModel();
        apply.setApplicantName(userName);
        apply.setUpdateTime(now);
        apply.setCreateTime(now);
        //创建申请单
        apply.setId(applyId);
        apply.setRootApplyOrderId(applyId);
        apply.setState(applyState.getValue());
        if (StringUtils.isBlank(applyOrderContract.getId())) {
            applyOrderContract.setId(applyId);
            //创建申请单
            applyOrderMapper.insertSelective(apply);
        } else {
            applyOrderMapper.updateByPrimaryKeySelective(apply);
        }
        //判断审批单是否是待审核状态
        List<ApplyOrderCopyTo> applyOrderCopyTos = applyAdapterMapper.getApplyOrderCopyToExtMapper(companyId).queryApplyOrderCCByApplyOrderId(applyId);
        if (CollectionUtils.isNotEmpty(applyOrderCopyTos)) {
            applyAdapterMapper.getApplyOrderCopyToExtMapper(companyId).deleteCCByApplyOrderId(applyId);
        }
        //获取抄送人数据
        CompanyApplyFlowSetV2RequestContract flow = applyOrderContract.getFlow();
        if (flow != null) {
            List<CompanyApplyFlowItemSetV2RequestContract> cc_list = flow.getCc_list();
            if (!CollectionUtils.isEmpty(cc_list)) {
                if (cc_list.size() > 5) {
                    throw new SaasException(GlobalResponseCode.FlowItemCCTooMuch);
                }
                int sort = 1;
                for (CompanyApplyFlowItemSetV2RequestContract ccFlowItem : cc_list) {
                    //保存抄送人
                    ApplyOrderCopyTo applyOrderCopyTo = new ApplyOrderCopyTo();
                    applyOrderCopyTo.setId(IDTool.CreateUniqueID());
                    applyOrderCopyTo.setApplyOrderId(applyId);
                    applyOrderCopyTo.setCreateTime(new Date());
                    applyOrderCopyTo.setItemId(ccFlowItem.getItem_id());
                    applyOrderCopyTo.setItemType(ccFlowItem.getItem_type());
                    applyOrderCopyTo.setSort(sort++);
                    applyOrderCopyTo.setRead(false);
                    if (ccFlowItem.getUser() == null || StringUtils.isBlank(ccFlowItem.getUser().getUser_id())) {
                        throw new SaasException(GlobalResponseCode.ApplyFlowItemNeedSetCCUser);
                    }
                    if (ccFlowItem.getUser() != null) {
                        applyOrderCopyTo.setUserId(ccFlowItem.getUser().getUser_id());
                    }
                    //待确认
                    applyOrderCopyTo.setIsDelete(ccFlowItem.getIs_delete());//1:不能删除 2:可以删除
                    applyAdapterMapper.getApplyOrderCopyToMapper(companyId).insert(applyOrderCopyTo);
                }
            }

        }
        if (applyState == ApplyStatus.PendingAudit) {
            insertApproverMap(applyId, applyOrderContract.getApprover_id(), now);
            logger.info(String.format("校验合同和供应商信息调用：%s,%s,%s,%s,%s,%s", companyId, applyOrderContract.getContract_id(), applyOrderContract.getReceiver_id(), 1, userId, userName));
            //校验合同和供应商信息
            // 450兼容老版本
            if (StringUtils.isNotBlank(clientHeadVersion) && VersionTool.greaterThanOrEqualTo(clientHeadVersion, "4.5.0")) {
                CompanyPaymentCheckDTO companyPaymentCheckDTO = new CompanyPaymentCheckDTO();
                companyPaymentCheckDTO.setCompanyId(companyId);
                companyPaymentCheckDTO.setSupplierId(applyOrderContract.getReceiver_id());
                companyPaymentCheckDTO.setOperateType(1);
                companyPaymentCheckDTO.setUserId(userId);
                companyPaymentCheckDTO.setUserName(userName);
                // 付款申请单合同ID(450新增)
                if (applyOrderContract.getContract_id() != null) {
                    companyPaymentCheckDTO.setContractId(applyOrderContract.getContract_id());
                }
                // 付款申请单凭证ID(450新增)
                if (applyOrderContract.getProof_id() != null) {
                    companyPaymentCheckDTO.setProofId(applyOrderContract.getProof_id());
                }
                iCompanyPaymentService.checkContractAndSupplierAndProof(companyPaymentCheckDTO);
            } else {
                // 老版本
                iCompanyPaymentService.checkContractAndSupplier(companyId, applyOrderContract.getContract_id(), applyOrderContract.getReceiver_id(), 1, userId, userName);
            }
            List<String> fbInvIds = Lists.newArrayList();
            List<Map<String, Object>> invoiceList = applyOrderContract.getInvoice_list();
            if (CollectionUtils.isNotEmpty(invoiceList)) {
                for (Map<String, Object> invoiceInfo : invoiceList) {
                    String fbInvId = ObjUtils.toString(invoiceInfo.get("fbInvId"));
                    fbInvIds.add(fbInvId);
                }
                try {
                    logger.info(String.format("校验申请单和发票校验调用：%s,%s", fbInvIds.get(0), applyId));
                    boolean validFbInvIds = iFbtInvoiceService.invoiceUsableCheck(fbInvIds.get(0));
                    logger.info(String.format("校验申请单和发票校验返回结果：%s", validFbInvIds));
                    if (!validFbInvIds) {
                        throw new SaasException(GlobalResponseCode.ApplyPaymentFbInvIdIsError);
                    }
                    TradeRelationInvoiceReqRPCDTO tradeRelationInvoiceReqRPCDTO = new TradeRelationInvoiceReqRPCDTO();
                    tradeRelationInvoiceReqRPCDTO.setCompanyId(companyId);
                    tradeRelationInvoiceReqRPCDTO.setEmployeeId(userId);
                    tradeRelationInvoiceReqRPCDTO.setApplyOrderId(applyId);
                    tradeRelationInvoiceReqRPCDTO.setFbInvIds(fbInvIds);
                    logger.info(String.format("校验申请单和发票绑定调用：%s,%s", JsonUtils.toJson(tradeRelationInvoiceReqRPCDTO), applyId));
                    List<BindTradeAndInvoiceResRPCDTO> bindTradeAndInvoiceResRPCDTOS = iInvoiceTradeService.bindTradeAndInvoice(tradeRelationInvoiceReqRPCDTO);
                    logger.info(String.format("校验申请单和发票绑定返回结果：%s,%s", JsonUtils.toJson(bindTradeAndInvoiceResRPCDTOS), applyId));
                } catch (FinhubException ex) {
                    logger.info(String.format("校验申请单和发票校验和绑定调用异常结果：%s,%s,%s,%s", ex.getCode(), ex.getMessage(), ex.getType(), ex.getTitle()));
                    throw new SaasException(ex.getCode(), ex.getMessage(), ex.getType(), ex.getTitle());
                }
            }
        }
        if (applyState == ApplyStatus.PendingAudit) {
            //push
            postMessage(apply, apply.getEmployeeId(), apply.getApproverId(), action, ApplyStatus.PendingAudit, logId, "");
            //push 抄送人
            pushCCMessage(apply, apply.getEmployeeId(), action, ApplyStatus.PendingAudit);
        }
        return GlobalResponseCode.Success;
    }

    /**
     * 保存审批单中订单的费用归属
     *
     * @param costAttributionList
     * @param companyId
     * @param applyId
     */
    void sloveApplyCostAttribution(List<CostAttributionInfo> costAttributionList, String companyId, String applyId, Integer bringIn) {
        if (CollectionUtils.isEmpty(costAttributionList)) {
            return;
        }
        if (bringIn == null) {
            bringIn = 0;
        }
        List<ApplyCostAttribution> applyCostAttributionList = Lists.newArrayList();
        for (CostAttributionInfo costAttributionInfo : costAttributionList) {
            ApplyCostAttribution applyCostAttribution = new ApplyCostAttribution();
            applyCostAttribution.setApplyId(applyId);
            applyCostAttribution.setCompanyId(companyId);
            applyCostAttribution.setCostAttributionId(costAttributionInfo.getCost_attribution_id());
            applyCostAttribution.setCostAttributionName(costAttributionInfo.getCost_attribution_name());
            applyCostAttribution.setCostAttributionCategory(costAttributionInfo.getCost_attribution_category());
            applyCostAttribution.setBringIn(bringIn);
            applyCostAttributionList.add(applyCostAttribution);
        }
        applyCostAttributionExtMapper.batchInsertCostAttribution(applyCostAttributionList);
    }

    /**
     * 修改供应商状态
     *
     * @param receiverId
     * @param contractId
     * @param companyId
     */
    private void updateContractIdAndReceiverId(Integer receiverId, Integer contractId, Integer state, String companyId, String userId, String userName) {
        try {
            iCompanyPaymentService.updateState(companyId, contractId, receiverId, state, userId, userName);
        } catch (Exception e) {
            logger.error("保存付款审批单更改合同和供应商状态失败" + e);
            throw new SaasException(GlobalResponseCode.InnerError, e.getMessage());
        }
    }

    private void insertTripContractList(ApplyV2Contract applyContract, String applyId, Date now, String clientHeadVersion) {
        if (applyContract != null) {
            List<ApplyTripInfo> oldTripList = applyTripInfoExtMapper.selectListByApplyOrderId(applyId);
            if (CollectionUtils.isNotEmpty(oldTripList)) {
                applyTripInfoExtMapper.deleteByApplyOrderId(applyId);
            }
            ApplyOrderV2Contract apply = applyContract.getApply();
            ApplyTripInfo trip = new ApplyTripInfo();
            String id = IDTool.CreateUniqueID();
            trip.setId(id);
            trip.setApplyOrderId(applyId);
            trip.setCreateTime(now);
            trip.setType(BizType.Payment.getValue());
            trip.setStartCityId("0");
            trip.setState(ApplyTripStatus.Available.getValue());
            trip.setStartTime(now);
            trip.setUpdateTime(now);
            trip.setEstimatedAmount(apply.getBudget().divide(ObjUtils.toBigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP));
            JSONObject jsonObject = new JSONObject();
            //付款id
            jsonObject.put("payer_id", apply.getPayer_id());
            //付款时间
            jsonObject.put("payer_time", apply.getPayer_time());
            //付款名称
            jsonObject.put("payment_name", apply.getPayment_name());
            //付款主体
            jsonObject.put("payer_name", apply.getPayer_name());
            //供应商id
            jsonObject.put("receiver_id", apply.getReceiver_id());
            //合同id
            jsonObject.put("contract_id", apply.getContract_id());
            // 凭证id
            if (StringUtils.isNotBlank(clientHeadVersion) && VersionTool.greaterThanOrEqualTo(clientHeadVersion, "4.5.0")) {
                jsonObject.put("proof_id", apply.getProof_id());
            }
            // 发票选项
            if (StringUtils.isNotBlank(clientHeadVersion) && VersionTool.greaterThanOrEqualTo(clientHeadVersion, "4.7.1")) {
                jsonObject.put("invoice_option", apply.getInvoice_option());
            } else {
                if (CollectionUtils.isNotEmpty(apply.getInvoice_list())) {
                    jsonObject.put("invoice_option", 1);
                } else {
                    jsonObject.put("invoice_option", 0);
                }
            }
            // 发票信息
            jsonObject.put("invoice_list", apply.getInvoice_list());
            // 付款主体id(431新增)
            jsonObject.put("company_account_id", apply.getCompany_account_id());
            // 付款主体名称(431新增)
            jsonObject.put("bank_account_acct_name", apply.getBank_account_acct_name());
            if (StringUtils.isNotBlank(clientHeadVersion) && VersionTool.greaterThanOrEqualTo(clientHeadVersion, "4.3.0")) {
                // 用途
                jsonObject.put("payment_use", apply.getPayment_use());
            }
            jsonObject.put("userDept", SaasHttpContext.getUserInfo().getDepts());

            // 付款主体名称(431新增)
            jsonObject.put("bank_account_acct_name", apply.getBank_account_acct_name());
            //费用信息
            CostInfoContract costInfo = apply.getCost_info();
            CostInfo costInfoDetail = new CostInfo();
            CostCategory costCategory = new CostCategory();
            List<CostAttributionGroup> costAttributionGroupList = Lists.newArrayList();
            if (costInfo != null) {
                Integer costInfoType = ObjUtils.toInteger(apply.getCost_info_type(), 0);
                jsonObject.put("paymentCostInfo", costInfo);
                jsonObject.put("costInfoType", costInfoType);
                CostInfoContract.CostCategory costCategoryInfo = costInfo.getCost_category();
                if (costCategoryInfo != null) {
                    costCategory.setId(costCategoryInfo.getId());
                    costCategory.setName(costCategoryInfo.getName());
                    costInfoDetail.setCostCategory(costCategory);
                }
                List<CostInfoContract.CostAttributionGroup> costAttributionGroupListInfo = costInfo.getCost_attribution_group_list();
                if (CollectionUtils.isNotEmpty(costAttributionGroupListInfo)) {
                    for (CostInfoContract.CostAttributionGroup costAttributionGroup : costAttributionGroupListInfo) {
                        CostAttributionGroup costAttributionGroupInfo = new CostAttributionGroup();
                        List<CostAttribution> costAttributionList = Lists.newArrayList();
                        costAttributionGroupInfo.setCategory(costAttributionGroup.getCategory());
                        costAttributionGroupInfo.setRecordId(costAttributionGroup.getRecord_id());
                        costAttributionGroupInfo.setCategoryName(costAttributionGroup.getCategory_name());
                        List<CostAttribution> costAttributionListInfo = costAttributionGroup.getCost_attribution_list();
                        costAttributionListInfo = CollectionUtils.isEmpty(costAttributionListInfo) ? costAttributionGroup.getCostAttributionList() : costAttributionListInfo;
                        if (CollectionUtils.isNotEmpty(costAttributionListInfo)) {
                            for (CostAttribution costAttribution : costAttributionListInfo) {
                                CostAttribution costAttributionInfo = new CostAttribution();
                                costAttributionInfo.setId(costAttribution.getId());
                                costAttributionInfo.setName(costAttribution.getName());
                                costAttributionInfo.setWeight(costAttribution.getWeight());
                                costAttributionList.add(costAttributionInfo);
                            }
                        }
                        costAttributionGroupInfo.setCostAttributionList(costAttributionList);
                        costAttributionGroupList.add(costAttributionGroupInfo);
                    }
                    costInfoDetail.setCostAttributionGroupList(costAttributionGroupList);
                }
            }
            jsonObject.put("costInfo", costInfoDetail);
            trip.setTripContent(jsonObject.toJSONString());
            applyTripInfoMapper.insert(trip);
        }
    }


    /**
     * 审批抄送人通知
     *
     * @param apply
     * @param senderUserId
     * @param action
     * @param desStatus
     */
    public void pushCCMessage(ApplyOrder apply, String senderUserId, ApplyLogAction action, ApplyStatus desStatus) {
        String applyId = apply.getId();
        ApplyType applyType = ApplyType.valueOf(apply.getType().intValue());
        List<ApplyOrderCopyTo> applyOrderCopyToList = applyAdapterMapper.getApplyOrderCopyToExtMapper(apply.getCompanyId()).queryApplyOrderCCByApplyOrderId(applyId);
        if (CollectionUtils.isEmpty(applyOrderCopyToList)) {
            return;
        }
        if (action.getValue() != ApplyLogAction.Submit.getValue() && desStatus.getValue() != ApplyStatus.Approved.getValue()) {
            return;
        }
        //提交审批时抄送通知类型判断
        if (action.getValue() == ApplyLogAction.Submit.getValue()) {
            Integer flowCcType = apply.getFlowCcType();
            if (flowCcType == null || flowCcType == 1) {
                return;
            }
        }
        //审批终审结束时抄送通知类型判断
        if (desStatus.getValue() == ApplyStatus.Approved.getValue()) {
            Integer flowCcType = apply.getFlowCcType();
            if (flowCcType == null || flowCcType == 2) {
                return;
            }
        }
        //业务类型
        if (CollectionUtils.isEmpty(applyOrderCopyToList)) {
            return;
        }
        List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.queryApprovalInfo(applyId);
        List<String> sponsorIdList = applyOrderLogList.stream().filter(applyOrderLog -> !applyOrderLog.getAction().equals(ApplyLogAction.Submit.getValue())).map(applyOrderLog -> applyOrderLog.getSponsorId()).distinct().collect(Collectors.toList());
        List<String> applyCopyList = applyOrderCopyToList.stream().map(applyOrderCopyTo -> applyOrderCopyTo.getUserId()).distinct().collect(Collectors.toList());
        applyCopyList.removeAll(sponsorIdList);
        applyOrderCopyToList = applyOrderCopyToList.stream().filter(applyOrderCopyTo -> CollectionUtils.isNotEmpty(applyCopyList) && applyCopyList.contains(applyOrderCopyTo.getUserId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(applyOrderCopyToList)) {
            return;
        }
        String name = "";
        EmployeeContract employee = iBaseOrganizationService.getEmployee(senderUserId, apply.getCompanyId());
        if (employee == null || StringUtils.isBlank(employee.getName())) {
            name = apply.getApplicantName();
        } else {
            name = employee.getName();
        }
        Map<String, Map<String, String>> languageMap = Maps.newHashMap();
        String pushContent = "";
        String messageContent = "";
        messageContent = StrUtils.formatString(CoreLanguage.Common_Message_PayApplyNoticeMsg.getMessage(), name);
        pushContent = messageContent;
        languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_PayApplyNoticeMsg.getLanguageCode(), name));
        for (ApplyOrderCopyTo applyOrderCopyTo : applyOrderCopyToList) {
            if (StringUtils.isBlank(applyOrderCopyTo.getUserId())) {
                continue;
            }
            String settingType = "11";
            Map<String, Object> msgInfo = new HashMap<>();
            msgInfo.put("myself", "false");
            msgInfo.put("view_type", "3");
            msgInfo.put("id", applyId);
            msgInfo.put("setting_type", settingType);
            msgInfo.put("apply_type", apply.getType().intValue());
            String linkDetail = JSONObject.toJSONString(msgInfo);
            //存消息
            ApplyLogAction msgAction = action;
            if (msgAction == ApplyLogAction.Approve && desStatus != ApplyStatus.Approved) {
                //固定审批流中间审批完成
                msgAction = ApplyLogAction.Submit;
            }
            if (msgAction == ApplyLogAction.Skip) {
                if (desStatus == ApplyStatus.Approved) {
                    msgAction = ApplyLogAction.Approve;
                } else {
                    msgAction = ApplyLogAction.Submit;
                }
            }
            String messageTitle = genMessageTitle(msgAction, languageMap);
            MessageSaveContract messageSaveContract = new MessageSaveContract();
            messageSaveContract.setMessage_type(MessageType.Apply.getCode());
            messageSaveContract.setTitle(messageTitle);
            messageSaveContract.setContent(messageContent);
            messageSaveContract.setBiz_order(applyId);
            messageSaveContract.setLink(linkDetail);
            messageSaveContract.setSender(senderUserId);
            messageSaveContract.setSender_type(SenderType.Person.getCode());
            messageSaveContract.setReceiver(applyOrderCopyTo.getUserId());
            messageSaveContract.setCompany_id(apply.getCompanyId());

            ApplyInfo messageApplyInfo = new ApplyInfo();
            messageApplyInfo.setApply_type(applyType.getBizType().getCode());
            messageApplyInfo.setApply_time(DateTimeTool.fromDateTimeToString(new Date()));
            messageApplyInfo.setApply_msg(messageContent);
            messageApplyInfo.setMyself(false);
            messageApplyInfo.setView_type(3);
            messageApplyInfo.setSetting_type(11);
            messageSaveContract.setApply_info(messageApplyInfo);
            try {
                messageService.saveMessage(messageSaveContract);
            } catch (SaasException ex) {
                //不处理
            }
            PushContract pushInfo = new PushContract();
            pushInfo.setTitle(messageTitle);
            pushInfo.setContent(pushContent);
            pushInfo.setUser_id(applyOrderCopyTo.getUserId());
            pushInfo.setMsg_type("0");
            pushInfo.setDesc(pushContent);
            pushInfo.setAlert(true);
            pushInfo.setMsg(linkDetail);
            pushInfo.setTitleMultilingualMap(languageMap.get(MessageLanguageEnum.TITLE.getCode()));
            pushInfo.setCommentMultilingualMap(languageMap.get(MessageLanguageEnum.CONTENT.getCode()));
            pushService.push(pushInfo);
        }
    }

    /**
     * 发送差旅push和通知
     *
     * @param apply
     * @param senderUserId
     * @param receiverUserId
     * @param action
     * @param desStatus
     * @param logId
     */
    @Override
    //@Transactional
    public void postMessage(ApplyOrder apply, String senderUserId, String receiverUserId, ApplyLogAction action, ApplyStatus desStatus, Integer logId, String comment) {
        String applyId = apply.getId();
        ApplyType applyType = ApplyType.valueOf(apply.getType().intValue());
        List<String> userIds = new ArrayList<>();
        userIds.add(senderUserId);
        userIds.add(receiverUserId);
        if (!userIds.contains(apply.getEmployeeId())) {
            userIds.add(apply.getEmployeeId());
        }
        List<IdNameContract> userNames = userService.getUserNameByIds(userIds, apply.getCompanyId());
        String employeeName = null;
        String senderUserName = null;
        for (IdNameContract userName : userNames) {
            if (senderUserId.equals(userName.getId())) {
                senderUserName = userName.getName();
                break;
            }
        }
        for (IdNameContract userName : userNames) {
            if (apply.getEmployeeId().equals(userName.getId())) {
                employeeName = userName.getName();
                break;
            }
        }
        if (StringUtils.isBlank(employeeName)) {
            employeeName = apply.getApplicantName();
        }
        ApplyLogAction msgAction = action;
        String msgSender = senderUserName;
        if (StringUtils.isBlank(msgSender) && apply.getEmployeeId().equals(senderUserId)) {
            msgSender = apply.getApplicantName();
        }
        if (msgAction == ApplyLogAction.Approve && desStatus != ApplyStatus.Approved) {
            //固定审批流中间审批完成
            msgSender = employeeName;
            msgAction = ApplyLogAction.Submit;
        }
        if (msgAction == ApplyLogAction.Skip) {
            if (desStatus == ApplyStatus.Approved) {
                msgAction = ApplyLogAction.Approve;
            } else {
                msgAction = ApplyLogAction.Submit;
            }
        }
        Map<String, Map<String, String>> languageMap = Maps.newHashMap();
        String messageContent = "";
        String pushContent = "";
        String messageTitle = genMessageTitle(msgAction, languageMap);
        messageContent = genMessageContent(msgSender, msgAction, applyType);
        pushContent = getPushContent(msgSender, msgAction, applyType, languageMap);

        boolean myself = true;
        if (msgAction == ApplyLogAction.Submit || msgAction == ApplyLogAction.Forward) {
            myself = false;
        }
        String settingType = "11";
        String viewType = myself ? "1" : "2";
        Map<String, Object> msgData = new HashMap<>();
        msgData.put("myself", myself ? "true" : "false");
        msgData.put("view_type", viewType);
        msgData.put("id", applyId);
        msgData.put("setting_type", settingType);
        String linkInfo = JSONObject.toJSONString(msgData); //跳转信息
        //存消息
        MessageSaveContract messageSaveContract = new MessageSaveContract();
        messageSaveContract.setMessage_type(MessageType.Apply.getCode());
        messageSaveContract.setTitle(messageTitle);
        messageSaveContract.setContent(messageContent);
        messageSaveContract.setBiz_order(applyId);
        messageSaveContract.setLink(linkInfo);
        messageSaveContract.setSender(senderUserId);
        messageSaveContract.setSender_type(SenderType.Person.getCode());
        messageSaveContract.setReceiver(receiverUserId);
        messageSaveContract.setCompany_id(apply.getCompanyId());

        ApplyInfo messageApplyInfo = new ApplyInfo();
        messageApplyInfo.setApply_type(applyType.getBizType().getCode());
        messageApplyInfo.setApply_time(DateTimeTool.fromDateTimeToString(new Date()));
        messageApplyInfo.setApply_msg(messageContent);
        messageApplyInfo.setMyself(myself);
        messageApplyInfo.setView_type(myself ? 1 : 2);
        messageApplyInfo.setSetting_type(11);
        if (logId != null && logId != 0) {
            messageApplyInfo.setLog_id(logId);
        }
        messageSaveContract.setApply_info(messageApplyInfo);
        try {
            messageService.saveMessage(messageSaveContract);
        } catch (SaasException ex) {
            //不处理
            logger.info("保存通知异常:" + ex.getMsg());
        }
        PushContract pushData = new PushContract();
        pushData.setTitle(messageTitle);
        pushData.setContent(pushContent);
        pushData.setUser_id(receiverUserId);
        pushData.setCompany_id(apply.getCompanyId());
        pushData.setMsg_type("0");
        pushData.setDesc(pushContent);
        pushData.setAlert(true);
        pushData.setMsg(linkInfo);
        pushData.setTitleMultilingualMap(languageMap.get(MessageLanguageEnum.TITLE.getCode()));
        pushData.setCommentMultilingualMap(languageMap.get(MessageLanguageEnum.CONTENT.getCode()));
        pushService.pushWithBudge(pushData);
    }

    private String genMessageContent(String sendUserName, ApplyLogAction action, ApplyType applyType) {
        String applyTypeDesc = applyType.getDesc();
        switch (action) {
            case Submit:
            case ReSubmit:
                return StrUtils.formatString(CoreLanguage.Common_Message_FlowSubmitMsg.getMessage(), sendUserName, applyTypeDesc);
            case Approve:
                return StrUtils.formatString(CoreLanguage.Common_Message_FlowSubmitAcceptMsg.getMessage(), sendUserName, applyTypeDesc);
            case Forward:
                return StrUtils.formatString(CoreLanguage.Common_Message_FlowForwardMsg.getMessage(), sendUserName, applyTypeDesc);
            case Refuse:
                return StrUtils.formatString(CoreLanguage.Common_Message_FlowRefuse.getMessage(), sendUserName, applyTypeDesc);
            case Revoke:
                return StrUtils.formatString(CoreLanguage.Common_Message_FlowRevokeMsg.getMessage(), sendUserName, applyTypeDesc);
            default:
                logger.error("生成apply push msg时发现没有处理的action:" + action.getValue() + ",系统暂时只返回了actionName:" + action.getDesc());
                return action.getDesc();
        }
    }

    private String genMessageTitle(ApplyLogAction action, Map<String, Map<String, String>> languageMap) {
        switch (action) {
            case Submit:
            case ReSubmit:
                languageMap.put(MessageLanguageEnum.TITLE.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Title_WaitCheck.getLanguageCode()));
                return CoreLanguage.Common_Title_WaitCheck.getMessage();
            case Approve:
                languageMap.put(MessageLanguageEnum.TITLE.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Title_Pass.getLanguageCode()));
                return CoreLanguage.Common_Title_Pass.getMessage();
            case Forward:
                languageMap.put(MessageLanguageEnum.TITLE.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Title_Forward.getLanguageCode()));
                return CoreLanguage.Common_Title_Forward.getMessage();
            case Refuse:
                languageMap.put(MessageLanguageEnum.TITLE.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Title_HasRefuse.getLanguageCode()));
                return CoreLanguage.Common_Title_HasRefuse.getMessage();
            case Revoke:
                languageMap.put(MessageLanguageEnum.TITLE.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Title_HasRevoke.getLanguageCode()));
                return CoreLanguage.Common_Title_HasRevoke.getMessage();
            case Overtime:
                languageMap.put(MessageLanguageEnum.TITLE.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Title_Timeout.getLanguageCode()));
                return CoreLanguage.Common_Title_Timeout.getMessage();
            default:
                logger.error("生成apply push title时发现没有处理的action:" + action.getValue() + ",系统暂时只返回了actionName:" + action.getDesc());
                return action.getDesc();
        }
    }

    private String getPushContent(String sendUserName, ApplyLogAction action, ApplyType applyType, Map<String, Map<String, String>> languageMap) {
        String applyTypeDesc = applyType.getDesc();
        switch (action) {
            case Submit:
            case ReSubmit:
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_FlowSubmitMsg.getLanguageCode(), sendUserName, applyTypeDesc));
                return StrUtils.formatString(CoreLanguage.Common_Message_FlowSubmitMsg.getMessage(), sendUserName, applyTypeDesc);
            case Approve:
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_FlowSubmitAcceptMsg.getLanguageCode(), sendUserName, applyTypeDesc));
                return StrUtils.formatString(CoreLanguage.Common_Message_FlowSubmitAcceptMsg.getMessage(), sendUserName, applyTypeDesc);
            case Forward:
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_FlowForwardMsg.getLanguageCode(), sendUserName, applyTypeDesc));
                return StrUtils.formatString(CoreLanguage.Common_Message_FlowForwardMsg.getMessage(), sendUserName, applyTypeDesc);
            case Refuse:
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_FlowRefuse.getLanguageCode(), sendUserName, applyTypeDesc));
                return StrUtils.formatString(CoreLanguage.Common_Message_FlowRefuse.getMessage(), sendUserName, applyTypeDesc);
            case Revoke:
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_FlowRevokeApplyMsg.getLanguageCode(), sendUserName, applyTypeDesc));
                return StrUtils.formatString(CoreLanguage.Common_Message_FlowRevokeApplyMsg.getMessage(), sendUserName, applyTypeDesc);
            default:
                logger.error("生成apply push msg时发现没有处理的action:" + action.getValue() + ",系统暂时只返回了actionName:" + action.getDesc());
                return action.getDesc();
        }
    }

    //@Transactional(value = "saas")
    private void insertApproverMap(String applyId, String approverId, Date time) {
        ApplyApproverMapExample example = new ApplyApproverMapExample();
        example.createCriteria().andApplyIdEqualTo(applyId).andApproverIdEqualTo(approverId);
        List<ApplyApproverMap> list = applyApproverMapMapper.selectByExample(example);
        if (ObjUtils.isEmpty(list)) {
            ApplyApproverMap approverMap = new ApplyApproverMap();
            approverMap.setApplyId(applyId);
            approverMap.setApproverId(approverId);
            approverMap.setCreateTime(time);
            applyApproverMapMapper.insertSelective(approverMap);
        }
    }

    /**
     * 整理申请单数据
     *
     * @param applyContract
     */
    private void clearApplyData(ApplyV2Contract applyContract) {
        ApplyOrderV2Contract applyorderContract = applyContract.getApply();
        if (applyorderContract.getBudget() == null) {
            applyorderContract.setBudget(BigDecimal.valueOf(0));
        }
        if (CollectionUtils.isEmpty(applyContract.getTrip_list())) {
            applyContract.setTrip_list(null);
        }
        if (applyorderContract.getApprover_id() == null) {
            applyorderContract.setApprover_id("");
        }

    }

    /**
     * 检查数据有效性
     *
     * @param applyContract
     * @return
     */
    private GlobalResponseCode checkApplyData(ApplyV2Contract applyContract, String employeeId, String companyId, String clientHeadVersion) {
        //检查申请单
        ApplyOrderV2Contract apply = applyContract.getApply();
        if (apply == null) {
            return GlobalResponseCode.ApplyIsNull;
        }
        if (apply.getState() == null) {
            return GlobalResponseCode.ParameterError;
        }
        if (apply.getType() == null
                || ApplyType.valueOf(apply.getType()) == ApplyType.Unknown
                || !ApplyType.valueOf(apply.getType()).equals(ApplyType.Payment)) {
            return GlobalResponseCode.ApplyTypeInvalid;
        }
        Integer state = apply.getState();
        //名称
        if (StringUtils.isBlank(apply.getPayment_name())) {
            return GlobalResponseCode.ApplyPaymentNameIsNull;
        }
        //付款主体
        if (StringUtils.isNotBlank(clientHeadVersion) && VersionTool.lessThan(clientHeadVersion, "4.3.1")) {
            if (StringUtils.isBlank(apply.getPayer_name()) || StringUtils.isBlank(apply.getPayer_id())) {
                return GlobalResponseCode.ApplyPaymentPayerIsNull;
            }
        }
        //付款金额
        if (ObjUtils.isEmpty(apply.getBudget()) || apply.getBudget().compareTo(BigDecimal.valueOf(0)) < 1) {
            return GlobalResponseCode.ApplyPaymentPriceIsNull;
        }
        // 付款金额需小于2000w
        if (apply.getBudget().compareTo(BigDecimal.valueOf(2000000000)) > 0) {
            return GlobalResponseCode.ApplyPaymentPriceGt2000w;
        }
        //付款时间
        if (StringUtils.isBlank(apply.getPayer_time()) || DateTimeTool.fromStringToDate(apply.getPayer_time()) == null) {
            return GlobalResponseCode.ApplyPaymentTimeIsNull;
        }
        //供应商信息
        if (apply.getReceiver_id() == null) {
            return GlobalResponseCode.ApplyReceiverIsNull;
        }
        // 431兼容老版本
        if (StringUtils.isNotBlank(clientHeadVersion) && VersionTool.greaterThanOrEqualTo(clientHeadVersion, "4.3.1")) {
            // 付款主体(431新增)
            if (StringUtils.isBlank(apply.getCompany_account_id()) || StringUtils.isBlank(apply.getBank_account_acct_name())) {
                return GlobalResponseCode.ApplyPaymentPayerIsNull;
            }
        }
        if (StringUtils.isNotBlank(clientHeadVersion) && VersionTool.greaterThanOrEqualTo(clientHeadVersion, "4.3.0")) {
            // 用途
            if (StringUtils.isBlank(apply.getPayment_use())) {
                return GlobalResponseCode.ApplyPaymentUseIsNull;
            }
            // 用途大于20字
            if (apply.getPayment_use().length() > 20) {
                return GlobalResponseCode.ApplyPaymentUseTooLong;
            }
        }
        // 450兼容老版本
        ApplyPaymentConfig applyPaymentConfig = getApplyPaymentConfig(companyId);
        if (StringUtils.isNotBlank(clientHeadVersion) && VersionTool.greaterThanOrEqualTo(clientHeadVersion, "4.5.0")) {
            // 付款申请单合同ID(450新增)
            if (ApplyPaymentEnum.IS_CHECKED_MUST.getValue() == applyPaymentConfig.getPayment_contract() && apply.getContract_id() == null) {
                return GlobalResponseCode.ApplyContractCanNotBeNull;
            }
            // 付款申请单凭证ID(450新增)
            if (ApplyPaymentEnum.IS_CHECKED_MUST.getValue() == applyPaymentConfig.getPayment_proof() && apply.getProof_id() == null) {
                return GlobalResponseCode.ApplyProofCanNotBeNull;
            }
        } else {
            // 老版本合同ID必填
            if (apply.getContract_id() == null) {
                return GlobalResponseCode.ApplyContractIsNull;
            }
        }
        // 处理金额分为元
        BigDecimal budgetYuan = BigDecimalUtils.fen2yuan(apply.getBudget());
        // 合同限制金额
        if (apply.getContract_id() != null && SaasMessageConstant.IS_CHECKED_TRUE == applyPaymentConfig.getPayment_contract_limit()) {
            logger.info("合同详情参数：{}, {}", companyId, apply.getContract_id());
            CompanyPaymentContractVO companyPaymentContractVO = iCompanyPaymentService.companyPaymentContract(companyId, apply.getContract_id());
            logger.info("合同详情返回结果：{}", JsonUtils.toJson(companyPaymentContractVO));

            if (companyPaymentContractVO != null && companyPaymentContractVO.getContractPrice().compareTo(budgetYuan) != 0) {
                return GlobalResponseCode.ApplyContractPriceNEqAmt;
            }
        }
        // 凭证限制金额
        if (apply.getProof_id() != null && SaasMessageConstant.IS_CHECKED_TRUE == applyPaymentConfig.getPayment_proof_limit()) {
            logger.info("凭证详情参数：{}, {}", companyId, apply.getProof_id());
            CompanyPaymentProofVO companyPaymentProofVO = iCompanyPaymentService.queryCompanyPaymentProof(companyId, apply.getProof_id());
            logger.info("凭证详情返回结果：{}", JsonUtils.toJson(companyPaymentProofVO));
            if (companyPaymentProofVO != null && companyPaymentProofVO.getProofPrice().compareTo(budgetYuan) != 0) {
                return GlobalResponseCode.ApplyProofPriceNEqAmt;
            }
        }
        // 471兼容老版本
        if (StringUtils.isNotBlank(clientHeadVersion) && VersionTool.greaterThanOrEqualTo(clientHeadVersion, "4.7.1")) {
            // 发票必填
            if (ApplyPaymentEnum.IS_CHECKED_MUST.getValue() == apply.getInvoice_option()
                    && CollectionUtils.isEmpty(apply.getInvoice_list())) {
                return GlobalResponseCode.ApplyInvCanNotBeNull;
            }
            // 发票必填且限制金额
            if (ApplyPaymentEnum.IS_CHECKED_MUST.getValue() == apply.getInvoice_option()
                    && CollectionUtils.isNotEmpty(apply.getInvoice_list())
                    && SaasMessageConstant.IS_CHECKED_TRUE == applyPaymentConfig.getPayment_has_invoice_limit()) {
                BigDecimal invoiceTotalAmt = apply.getInvoice_list().stream().map(e -> ObjUtils.toBigDecimal(e.get("totalPricePlusTax"))).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (invoiceTotalAmt.compareTo(budgetYuan) != 0) {
                    return GlobalResponseCode.ApplyInvPriceNEqAmt;
                }
            }
        }
        ReasonConfig reasonConfig = customReasonService.queryReasonConfigWithoutItems(companyId, ReasonType.APPLY_PAYMENT);
        if (reasonConfig.getReason() == SaasMessageConstant.IS_CHECKED_TRUE) {
            if (StringUtils.isBlank(apply.getApply_reason())) {
                return GlobalResponseCode.ApplyReasonIsNull;
            }
            if (reasonConfig.getReason_desc() == SaasMessageConstant.IS_CHECKED_TRUE) {
                if (StringUtils.isBlank(apply.getApply_reason_desc())) {
                    return GlobalResponseCode.ApplyReasonDescIsNull;
                }
            }
        }
        if (StringUtils.isNotBlank(apply.getApply_reason()) && apply.getApply_reason().length() > 100) {
            return GlobalResponseCode.ApplyMallReasonInvalid;
        }
        if (StringUtils.isNotBlank(apply.getApply_reason_desc()) && apply.getApply_reason_desc().length() > 200) {
            return GlobalResponseCode.ApplyMallReasonInvalid;
        }
        //草稿
        if (state == ApplyStatus.Draft.getValue()) {
            if (ObjUtils.isNotEmpty(apply.getBudget()) && apply.getBudget().compareTo(BigDecimal.ZERO) < 0) {
                return GlobalResponseCode.ApplyTripBudgetError;
            }
        } else {
            //审批类型 1.弹性审批流 2.固定审批流 3.分条件审批流
            CompanyApplyType applyType = CompanyApplyType.valueOf(apply.getFlow_type());
            if (applyType.getValue() == CompanyApplyType.Unknown.getValue()) {
                return GlobalResponseCode.ApplyFlowTypeError;
            }
            if (applyType.getValue() == CompanyApplyType.Elastic.getValue() && apply.getState() == ApplyStatus.PendingAudit.getValue() && StringUtils.isBlank(apply.getApprover_id())) {
                //申请单必须指定一个审批人
                return GlobalResponseCode.ApplyApproveIdInvalid;
            }
            if (applyType.getValue() == CompanyApplyType.Elastic.getValue() && employeeId.equals(apply.getApprover_id())) {
                //审批人不能是自己
                return GlobalResponseCode.ApplyApproverCannotBeSelf;
            }
            Integer applyCostAttributionCategory = apply.getCost_attribution_category();
            String costAttributionId = apply.getCost_attribution_id();
            String costAttributionName = apply.getCost_attribution_name();
            if (applyCostAttributionCategory == null || StringUtils.isBlank(costAttributionId) || StringUtils.isBlank(costAttributionName)) {
                return GlobalResponseCode.CostAttributionNameIsNull;
            }
            ApplySetupContract applySetupCostAttributionContract = messageSetupService.queryCompanyApplyCostAttributionConfig(companyId);
            Integer costAttributionCategory = applySetupCostAttributionContract.getCost_attribution_category();
            if (applyCostAttributionCategory == null) {
                applyCostAttributionCategory = 1;
            }
            if (costAttributionCategory == 1 && applyCostAttributionCategory != 1) {
                return GlobalResponseCode.CostAttributionCategoryIsDept;
            }
            if (costAttributionCategory == 2 && applyCostAttributionCategory != 2) {
                return GlobalResponseCode.CostAttributionCategoryIsCostCenter;
            }
        }
        //对公付款添加费用归属提示升级
        /*if (VersionTool.lessThan(clientHeadVersion, "4.8.3")) {
            throw new SaasException(GlobalResponseCode.APPLYFLOWDETAILERROR);
        }*/
        /*CostInfoContract costInfo = apply.getCost_info();
        if (costInfo == null) {
            throw new SaasException(GlobalResponseCode.PaymentOrderCostAttributionIsError);
        }
        CostInfoContract.CostCategory costCategory = costInfo.getCost_category();
        MessageSetup messageSetup = messageSetupService.queryCompanyMessageSetupWithDefault(companyId, SaasApplyConstant.ITEM_CODE_APPLY_PAYMENT_COST_CATEGORY);
        if (messageSetup != null && messageSetup.getIsChecked() == 1) {
            if (costCategory == null || StringUtils.isBlank(costCategory.getId()) || StringUtils.isBlank(costCategory.getName())) {
                throw new SaasException(GlobalResponseCode.CostCategoryIsNull);
            }
        }
        List<CostInfoContract.CostAttributionGroup> costAttributionGroupList = costInfo.getCost_attribution_group_list();
        if (CollectionUtils.isEmpty(costAttributionGroupList) || costAttributionGroupList.size() > 2) {
            throw new SaasException(GlobalResponseCode.PaymentOrderCostAttributionIsError);
        }
        for (CostInfoContract.CostAttributionGroup costAttributionGroup : costAttributionGroupList) {
            Integer category = costAttributionGroup.getCategory();
            if (category == null || CostAttributionCategory.getByKey(category) == null) {
                throw new SaasException(GlobalResponseCode.PaymentOrderCostAttributionCategoryIsError);
            }
            List<CostAttribution> costAttributionList = costAttributionGroup.getCost_attribution_list();
            for (CostAttribution costAttribution : costAttributionList) {
                String id = costAttribution.getId();
                String name = costAttribution.getName();
                BigDecimal weight = costAttribution.getWeight();
                if (StringUtils.isBlank(id) || StringUtils.isBlank(name)) {
                    throw new SaasException(GlobalResponseCode.PaymentOrderCostAttributionCategoryIsError);
                }
                if (weight == null) {
                    costAttribution.setWeight(BigDecimal.valueOf(100));
                }
            }
        }*/
        return GlobalResponseCode.Success;
    }

    private ApplyV2Contract getApplyByIdAndUserIdAndCompanyId(String applyId, String userId, String companyId, UserRole userRole, String clientVersion) throws SaasException {
        ApplyOrder applyModel = getApplyOrderModelByIdAndUserIdAndCompanyId(applyId, userId, companyId, userRole);
        if (applyModel == null) {
            applyModel = applyOrderExtMapper.getByIdAndApproverIdAndCompanyId(applyId, userId, companyId);
        }
        if (applyModel == null) {
            List<ApplyOrder> ccApplyOrderCountByApplyIdList = applyOrderExtMapper.getCCApplyOrderCountByApplyId(userId, companyId, 0);
            if (CollectionUtils.isEmpty(ccApplyOrderCountByApplyIdList)) {
                return null;
            }
            List<String> cclist = Lists.newArrayList();
            for (ApplyOrder applyOrder : ccApplyOrderCountByApplyIdList) {
                cclist.add(applyOrder.getId());
            }
            if (cclist.contains(applyId)) {
                applyModel = applyOrderMapper.selectByPrimaryKey(applyId);
            } else {
                return null;
            }
        }
        ApplyOrderV2Contract apply = ApplyOrderV2Contract.FromModel(applyModel);
        //返回当前时间戳
        ApplyV2Contract applyContract = new ApplyV2Contract();
        applyContract.setApply(apply);
        getTripListByApplyOrderId(applyId, apply);
        // 处理版本兼容，4.3.0之前的版本默认返回"供应商付款"
        if (StringUtils.isBlank(apply.getPayment_use())) {
            apply.setPayment_use("供应商付款");
        }
        // 4.7.1 配置项目编码显示
        ApplySetupContract applySetupContract = messageSetupService.queryCompanyApplyCostAttributionConfig(companyId);
        if (applySetupContract != null
                && apply.getCost_attribution_category() == CostAttributionCategory.CostCenter.getKey()
                && SaasMessageConstant.IS_CHECKED_TRUE == applySetupContract.getApply_show_project_code()) {
            apply.setCost_attribution_code(applyModel.getCostAttributionCode());
        }
        return applyContract;
    }

    @Override
    public void getTripListByApplyOrderId(String applyOrderId, ApplyOrderV2Contract apply) {
        List<ApplyTripInfo> tripList = applyTripInfoExtMapper.selectListByApplyOrderId(applyOrderId);
        if (ObjUtils.isNotEmpty(tripList)) {
            for (ApplyTripInfo trip : tripList) {
                Map<String, Object> paymentMap = JSON.parseObject(trip.getTripContent(), Map.class);
                //付款名称
                String paymentName = ObjUtils.toString(paymentMap.get("payment_name"));
                //付款主体id
                String payerId = ObjUtils.toString(paymentMap.get("payer_id"));
                //付款主体
                String payerName = ObjUtils.toString(paymentMap.get("payer_name"));
                //付款时间
                String payerTime = ObjUtils.toString(paymentMap.get("payer_time"));
                //供应商id
                Integer receiverId = ObjUtils.toInteger(paymentMap.get("receiver_id"));
                //合同id
                Integer contractId = ObjUtils.toInteger(paymentMap.get("contract_id"));
                // 付款主体id(431新增)
                String companyAccountId = ObjUtils.toString(paymentMap.get("company_account_id"));
                // 付款主体名称(431新增)
                String bankAccountAcctName = ObjUtils.toString(paymentMap.get("bank_account_acct_name"));
                // 用途
                String paymentUse = ObjUtils.toString(paymentMap.get("payment_use"));
                // 凭证id
                Integer proofId = ObjUtils.toInteger(paymentMap.get("proof_id"));
                // 发票选项
                Integer invoiceOption = ObjUtils.toInteger(paymentMap.get("invoice_option"));
                //发票集合
                List<Map<String, Object>> invoiceList = (List<Map<String, Object>>) paymentMap.get("invoice_list");
                apply.setPayment_name(paymentName);
                apply.setPayer_name(payerName);
                apply.setReceiver_id(receiverId);
                apply.setContract_id(contractId);
                apply.setInvoice_list(invoiceList);
                apply.setPayer_time(payerTime);
                apply.setPayer_id(payerId);
                apply.setCompany_account_id(companyAccountId);
                apply.setBank_account_acct_name(bankAccountAcctName);
                apply.setPayment_use(paymentUse);
                apply.setProof_id(proofId);
                apply.setInvoice_option(invoiceOption);
                // 供应商详情，提前给个默认空节点，防止前台崩溃
                apply.setCompany_supplier(new CompanyPaymentSupplierVO());
                if (receiverId != null) {
                    logger.info(String.format("供应商详情参数：%s,%s", apply.getCompany_id(), receiverId));
                    CompanyPaymentSupplierVO companyPaymentSupplierVO = iCompanyPaymentService.companyPaymentSupplier(apply.getCompany_id(), receiverId);
                    logger.info(String.format("供应商详情返回结果：%s", JsonUtils.toJson(companyPaymentSupplierVO)));
                    apply.setCompany_supplier(companyPaymentSupplierVO);
                }
                // 合同详情，提前给个默认空节点，防止前台崩溃
                CompanyPaymentContractVO companyPaymentContractVODefault = new CompanyPaymentContractVO();
//                companyPaymentContractVODefault.setContractBeginDate("");
//                companyPaymentContractVODefault.setContractEndDate("");
                apply.setContract(companyPaymentContractVODefault);
                if (contractId != null) {
                    logger.info(String.format("合同详情参数：%s,%s", apply.getCompany_id(), contractId));
                    CompanyPaymentContractVO companyPaymentContractVO = iCompanyPaymentService.companyPaymentContract(apply.getCompany_id(), contractId);
                    logger.info(String.format("合同详情返回结果：%s", JsonUtils.toJson(companyPaymentContractVO)));
                    apply.setContract(companyPaymentContractVO);
                }
                // 凭证详情，提前给个默认空节点，防止前台崩溃
                apply.setProof(new CompanyPaymentProofVO());
                if (proofId != null) {
                    logger.info("凭证详情参数：{}, {}", apply.getCompany_id(), proofId);
                    CompanyPaymentProofVO companyPaymentProofVO = iCompanyPaymentService.queryCompanyPaymentProof(apply.getCompany_id(), proofId);
                    logger.info("凭证详情返回结果：{}", JsonUtils.toJson(companyPaymentProofVO));
                    apply.setProof(companyPaymentProofVO);
                }
                // 费用信息
                CostInfo costInfo = JSON.parseObject(ObjUtils.toString(paymentMap.get("costInfo")), CostInfo.class);
                if (costInfo != null) {
                    CostCategory costCategory = costInfo.getCostCategory();
                    List<String> costAttributionNameList = Lists.newArrayList();
                    List<CostAttributionGroup> costAttributionGroupList = costInfo.getCostAttributionGroupList();
                    if (CollectionUtils.isNotEmpty(costAttributionGroupList)) {
                        for (CostAttributionGroup costAttributionGroup : costAttributionGroupList) {
                            List<CostAttribution> costAttributionList = costAttributionGroup.getCostAttributionList();
                            if (CollectionUtils.isNotEmpty(costAttributionList)) {
                                for (CostAttribution costAttribution : costAttributionList) {
                                    if (costAttribution != null) {
                                        costAttributionNameList.add(costAttribution.getName());
                                    }
                                }
                            }
                        }
                    }
                    String costAttributionNameStr = StringUtils.join(costAttributionNameList, "；");
                    apply.setOrder_cost_attribution(costAttributionNameStr);
                    if (costCategory != null) {
                        apply.setCost_category_id(costCategory.getId());
                        apply.setCost_category_name(costCategory.getName());
                    }
                }
                CostInfoContract paymentCostInfo = JSON.parseObject(ObjUtils.toString(paymentMap.get("paymentCostInfo")), CostInfoContract.class);
                if (paymentCostInfo != null) {
                    apply.setCost_info(paymentCostInfo);
                }
                Integer costInfoType = ObjUtils.toInteger(paymentMap.get("costInfoType"), 0);
                if (ObjUtils.isNotEmpty(costInfoType)) {
                    apply.setCost_info_type(costInfoType);
                }
            }
        }
    }

    /**
     * 获取审批是否是终审节点
     *
     * @param apply
     * @param applyFlowUserItems
     * @param skippedApprverIds
     * @param couldFinalApprove
     * @return
     * @throws SaasException
     */
    private FindNextApproverIdAndStatusResult findStatusInFlow(ApplyOrder apply, List<ApplyFlowUserItem> applyFlowUserItems, List<String> skippedApprverIds, boolean couldFinalApprove) throws SaasException {
        String approverId = null;
        String receiverId = null;
        ApplyStatus finalStatus;
        Integer applyStatus = null;
        int remainIndex = applyFlowUserItems.size();
        for (int i = 0; i < applyFlowUserItems.size(); i++) {
            if (ValueTool.areEqual(applyFlowUserItems.get(i).getStatus(), ApplyFlowUserItemStatus.Waiting.getValue())) {
                remainIndex = i;
                break;
            }
        }
        //找下一个审批人
        boolean findNextApprover = false;
        // 是否是最后一名审批人
        boolean finnalApprover = true;
        for (int i = remainIndex; i < applyFlowUserItems.size(); i++) {
            ApplyFlowUserItem item = applyFlowUserItems.get(i);
            if (ValueTool.areEqual(item.getStatus(), ApplyFlowUserItemStatus.Waiting.getValue())) {
                finnalApprover = false;
                if (userService.isUserInCompany(item.getUserId(), apply.getCompanyId())) {
                    findNextApprover = true;
                    receiverId = item.getUserId();
                    approverId = receiverId;
                    applyStatus = 1;
                    break;
                } else {
                    skippedApprverIds.add(item.getUserId());
                }
            }
        }
        if (finnalApprover && couldFinalApprove) {
            //当前审批人已经是最后一个审批人
            finalStatus = ApplyStatus.Approved;
            receiverId = apply.getEmployeeId();
            applyStatus = 2;
        } else {
            finalStatus = ApplyStatus.PendingAudit;
            if (!findNextApprover) {
                //后面的审批人都已不存在，直接跳到授权负责人
                List<String> superAdminEmployeeIds = customRoleService.getEmployeeIdByRoleId(Integer.toString(UserRole.CompanySuperAdmin.getValue()), apply.getCompanyId());
                if (superAdminEmployeeIds == null || superAdminEmployeeIds.size() == 0) {
                    throw new SaasException(GlobalResponseCode.ApplyCanotApproveBecauseHasNoSuperAdmin);
                } else {
                    receiverId = superAdminEmployeeIds.get(0);
                    approverId = receiverId;
                    applyStatus = 3;
                }
            }
        }
        FindNextApproverIdAndStatusResult result = new FindNextApproverIdAndStatusResult();
        result.setApproverId(approverId);
        result.setReceiverId(receiverId);
        result.setFinnalStatus(finalStatus);
        result.setApplyStatus(applyStatus);
        return result;
    }

    private ApplyOrder getApplyOrderModelByIdAndUserIdAndCompanyId(String id, String userId, String companyId, UserRole userRole) {
        if (StringTool.isNullOrEmpty(id) || StringTool.isNullOrEmpty(userId) || StringTool.isNullOrEmpty(companyId)) {
            return null;
        }
        ApplyOrder apply = applyOrderMapper.selectByPrimaryKey(id);
        if (apply == null) {
            return null;
        }
        if (apply.getCompanyId() == null || !apply.getCompanyId().equals(companyId)) {
            return null;
        }
        if (!apply.getEmployeeId().equals(userId)) {
            //如果不是本人，再判断一下userrole是不是分贝管理员或者企业管理员
            if (userRole == null || (userRole != UserRole.CompanyAdmin && userRole != UserRole.CompanySuperAdmin && userRole != UserRole.FbAdmin)) {
                return null;
            }
        }
        return apply;
    }

    private List<ApplyOrderLogContract> getLogsByApplyId(String applyId, String companyId) {
        List<ApplyOrderLog> logs = applyOrderLogExtMapper.selectListByApplyId(applyId);
        logs = logs.stream().filter(logInfo -> logInfo.getAction() != ApplyLogAction.CreateDraft.getValue() && logInfo.getAction() != ApplyLogAction.ModifyDraft.getValue()).collect(Collectors.toList());
        //查询是否有已拒绝、已撤销和超时状态的审批日志
        List<ApplyOrderLog> destinationLogList = logs.stream().filter(logInfo -> logInfo.getAction() == ApplyLogAction.Refuse.getValue() || logInfo.getAction() == ApplyLogAction.Revoke.getValue() || logInfo.getAction() == ApplyLogAction.Overtime.getValue()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(destinationLogList)) {
            logs = logs.stream().filter(logInfo -> logInfo.getAction() != ApplyLogAction.Approval.getValue() && logInfo.getAction() != ApplyLogAction.Unknown.getValue()).collect(Collectors.toList());
        }
        List<ApplyOrderLogContract> result = getLogContractList(logs, companyId);
        return result;
    }

    private void appendApplyDetail(String token, ApplyV2Contract detail, String userId) {
        ApplyOrderV2Contract order = detail.getApply();
        //可操作权限
        Integer operateAuth = genApplyOperateAuth(order, userId);
        order.setOperate_auth(operateAuth);
        //处理审批流设置
        appendFlowData(order);
    }

    private void appendFlowData(ApplyOrderV2Contract applyOrderContract) {
        CompanyApplyFlowSetV2RequestContract flowSet = null;
        if (applyOrderContract.getFlow_type() == CompanyApplyType.Flow.getValue() || applyOrderContract.getFlow_type() == CompanyApplyType.CONDITIONAL.getValue()) {
            flowSet = applyFlowV2Service.getFlowByApplyId(applyOrderContract.getId(), applyOrderContract.getCompany_id());
            flowSet = applyFlowV2Service.getFlowCCByApplyId(applyOrderContract.getId(), flowSet, applyOrderContract.getCompany_id());
        }
        if (applyOrderContract.getFlow_type() == CompanyApplyType.Elastic.getValue()) {
            flowSet = applyFlowV2Service.getFlowCCByApplyId(applyOrderContract.getId(), flowSet, applyOrderContract.getCompany_id());
            flowSet.setCompany_apply_type(1);
        }
        if (flowSet != null) {
            applyOrderContract.setFlow_type(flowSet.getCompany_apply_type());
            applyOrderContract.setFlow(flowSet);
        }
    }

    /**
     * 判断操作权限
     *
     * @param order
     * @param userId
     * @return
     */
    private Integer genApplyOperateAuth(ApplyOrderV2Contract order, String userId) {
        ApplyStatus orderStatus = ApplyStatus.valueOf(order.getState());
        if (order.getEmployee_id().equals(userId)) {
            //自己的申请单
            if (orderStatus == ApplyStatus.PendingAudit) {
                if (userId.equals(order.getApprover_id())) {
                    return ApplyLogAction.Approve.getValue() + ApplyLogAction.Forward.getValue() + ApplyLogAction.Refuse.getValue() + ApplyLogAction.Revoke.getValue();
                }
                //状态为待审核时,用户可撤销申请
                return ApplyLogAction.Revoke.getValue();
                //} else if (orderStatus == ApplyStatus.Return || orderStatus == ApplyStatus.Draft) {
            } else if (orderStatus == ApplyStatus.Draft) {
                //状态为被驳回或草稿时,用户可提交申请或者修改
                return ApplyLogAction.Submit.getValue() + ApplyLogAction.ModifyDraft.getValue() + ApplyLogAction.Delete.getValue();
            } else if (orderStatus == ApplyStatus.Backout){
                return ApplyLogAction.Delete.getValue() + ApplyLogAction.ReSubmit.getValue();
            } else if (orderStatus == ApplyStatus.Return){
                return ApplyLogAction.ReSubmit.getValue();
            }
        } else {
            //是需要自己审批,或者自己已经操作过的审批单
            if (orderStatus == ApplyStatus.PendingAudit || orderStatus == ApplyStatus.Transfer) {
                //只有待自己处理的审批单才可操作
                if (userId.equals(order.getApprover_id())) {
                    return ApplyLogAction.Approve.getValue() + ApplyLogAction.Forward.getValue() + ApplyLogAction.Refuse.getValue();
                }
            }
        }
        //不能做任何操作
        return 0;
    }

    private String genApplyLogDisplayString(ApplyOrderLog log, List<IdNameContract> userNames) {
        StringBuilder sb = new StringBuilder();
        IdNameContract sponsor = getNameFromListById(userNames, log.getSponsorId());
        if (sponsor != null) {
            sb.append(sponsor.getName() + " ");
        }
        ApplyLogAction action = ApplyLogAction.valueOf(log.getAction());
        String actionName = "";
        if (action == ApplyLogAction.Unknown) {
            actionName = "";
        } else {
            actionName = action.getDesc();
        }
        sb.append(actionName);
        if (action == ApplyLogAction.Forward
                || action == ApplyLogAction.Submit
                || action == ApplyLogAction.ReSubmit
                ) {
            sb.append(CoreLanguage.Common_Value_Give.getMessage() + " ");
            if (log.getReceiverId() != null) {
                IdNameContract receiver = getNameFromListById(userNames, log.getReceiverId());
                if (receiver != null) {
                    sb.append(receiver.getName());
                }
            }
        }
        return sb.toString();
    }

    private List<ApplyOrderLogContract> getLogContractList(List<ApplyOrderLog> logs, String companyId) {
        if (logs == null || logs.size() == 0) {
            return new ArrayList<>();
        }
        List<ApplyOrderLogContract> result = new ArrayList<>();
        List<String> userIds = new ArrayList<>();
        for (ApplyOrderLog log : logs) {
            if (!userIds.contains(log.getSponsorId())) {
                userIds.add(log.getSponsorId());
            }
            if (log.getReceiverId() != null && !userIds.contains(log.getReceiverId())) {
                userIds.add(log.getReceiverId());
            }
        }
        List<IdNameContract> userNames = userService.getUserNameByIds(userIds, companyId);
        for (ApplyOrderLog log : logs) {
            ApplyOrderLogContract logContract = genLogContract(log, userNames);
            if (logContract != null && logContract.getAction() == ApplyLogAction.Unknown.getValue() && StringUtils.isBlank(logContract.getSponsor()) && StringUtils.isBlank(logContract.getLog())) {
                logContract.setAction(ApplyLogAction.Skip.getValue());
                logContract.setCheck_reason(CoreLanguage.Common_Value_ApprovalPersonDelete.getMessage());
            }
            result.add(logContract);
        }
        return result;
    }

    private ApplyOrderLogContract genLogContract(ApplyOrderLog log, List<IdNameContract> userNames) {
        ApplyOrderLogContract contract = ApplyOrderLogContract.FromModel(log);
        String logContent = genApplyLogDisplayString(log, userNames);
        contract.setLog(logContent);
        if (StringUtils.isNotBlank(log.getSponsorId())) {
            for (IdNameContract idNameContract : userNames) {
                if (idNameContract.getId().equals(log.getSponsorId())) {
                    contract.setSponsor(idNameContract.getName());
                }
            }
        }
        if (StringUtils.isNotBlank(log.getReceiverId())) {
            for (IdNameContract idNameContract : userNames) {
                if (idNameContract.getId().equals(log.getReceiverId())) {
                    contract.setReceiver(idNameContract.getName());
                }
            }
        }
        return contract;
    }

    private IdNameContract getNameFromListById(List<IdNameContract> list, String id) {
        if (list == null || id == null) {
            return null;
        }
        for (IdNameContract item : list) {
            if (item.getId().equals(id)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 审批单详情
     *
     * @param token
     * @param applyId
     * @param userId
     * @param companyId
     * @param userRole
     * @return
     * @throws SaasException
     */
    @Override
    public ApplyV2Contract getDetail(String token, String applyId, String userId, String companyId, UserRole userRole, String clientVersion) throws SaasException {
        FlowCheckUtil.check(companyId, clientVersion);
        ApplyV2Contract apply = getApplyByIdAndUserIdAndCompanyId(applyId, userId, companyId, userRole, clientVersion);
        if (apply == null) {
            return null;
        }
        //1、申请人及审批人的姓名及部门 2、可操作权限
        appendApplyDetail(token, apply, userId);
        List<ApplyOrderLogContract> logs = getLogsByApplyId(applyId, companyId);
        for (ApplyOrderLogContract applyOrderLogContract : logs) {
            if (StringUtils.isBlank(applyOrderLogContract.getSponsor()) && apply.getApply().getEmployee_id().equals(applyOrderLogContract.getSponsor_id())) {
                if (applyOrderLogContract.getAction() != ApplyLogAction.Skip.getValue()) {
                    applyOrderLogContract.setSponsor(apply.getApply().getApplicant_name());
                }
            }
        }
        apply.setLog_list(logs);
        return apply;
    }

    /**
     * 订单审批单（同意）
     *
     * @param approveModel
     * @param userId
     * @param ip
     * @return
     */
    @Override
   // @Transactional(value = "saas")
    public GlobalResponseCode approve(ApplyApproveContract approveModel, String userId, String companyId, String ip, String clientVersion, String source) throws SaasException {
        FlowCheckUtil.check(companyId, clientVersion, source);
        if (approveModel == null) {
            throw new SaasException(GlobalResponseCode.ApplyIsNull);
        }
        if (StringTool.isNullOrEmpty(approveModel.getApply_id())) {
            throw new SaasException(GlobalResponseCode.ApplyIdInvalid);
        }
        if (approveModel.getComment() != null && approveModel.getComment().length() > 200) {
            throw new SaasException(GlobalResponseCode.ApplyCheckReasonInvalid);
        }
        String applyId = approveModel.getApply_id();
        ApplyOrder apply = applyOrderMapper.selectByPrimaryKey(approveModel.getApply_id());
        if (apply == null) {
            throw new SaasException(GlobalResponseCode.NotFound);
        }
        if (apply.getState() != null && apply.getState().intValue() == ApplyStatus.Backout.getValue()) {
            //已撤销
            throw new SaasException(GlobalResponseCode.CenterApplyCancleOrder);
        }
        if (!apply.getCompanyId().equals(companyId)) {
            //申请单和当前人不在一个公司
            throw new SaasException(GlobalResponseCode.ApplyCompanyCannotMatch);
        }
        if (apply.getState() != ApplyStatus.PendingAudit.getValue()) {
            //该单不是待审核,不能审批
            throw new SaasException(GlobalResponseCode.ApplyStatusNotPendingAudit);
        }
        if (!apply.getApproverId().equals(userId)) {
            //不是当前人可以处理的审批单
            throw new SaasException(GlobalResponseCode.NoAuth);
        }
        ApplyStatus finalStatus;
        ApplyLogAction action;
        String receiverId; //下一个审批人/通知人
        String approverId; //审批人
        Integer logId = null;
        List<String> skippedApprverIds = new ArrayList<>();//固定审批流跳过的审批人
        Date now = new Date();
        action = ApplyLogAction.Approve;
        approverId = userId;
        receiverId = null;
        Integer finalJudgmentUser = null;
        boolean isFlow = (ValueTool.areEqual(apply.getFlowType(), CompanyApplyType.Flow.getValue()) || ValueTool.areEqual(apply.getFlowType(), CompanyApplyType.CONDITIONAL.getValue()));
        if (isFlow) {
            //固定审批流和分条件审批流
            List<ApplyFlowUserItem> applyFlowUserItems = applyFlowService.getFlowItemsByApplyId(applyId);
            if (applyFlowUserItems == null ||
                    applyFlowUserItems.size() == 0 ||
                    applyFlowUserItems.stream().allMatch(m ->
                            !ValueTool.areEqual(m.getStatus(), ApplyFlowUserItemStatus.PendingAudit.getValue())
                                    && !ValueTool.areEqual(m.getStatus(), ApplyFlowUserItemStatus.Transfered.getValue())
                    )) {
                //没有待审核记录，也没有转交记录，预示审批流已经审核通过
                finalStatus = ApplyStatus.Approved;
                receiverId = apply.getEmployeeId();
                //处理审批日志和审批单的状态
                logId = sloveApplyLog(applyId, receiverId, approverId, finalStatus, now, ip, userId, action, approveModel);
                finalJudgmentUser = 2;
            } else {
                int pendingAuditItemIndex = -1;
                for (int i = 0; i < applyFlowUserItems.size(); i++) {
                    if (ValueTool.areEqual(applyFlowUserItems.get(i).getStatus(), ApplyFlowUserItemStatus.PendingAudit.getValue())) {
                        pendingAuditItemIndex = i;
                        break;
                    }
                }
                ApplyFlowUserItem pendingAuditItem = null;
                if (pendingAuditItemIndex >= 0) {
                    pendingAuditItem = applyFlowUserItems.get(pendingAuditItemIndex);
                }
                if (pendingAuditItem != null && !pendingAuditItem.getUserId().equals(userId)) {
                    logger.info("当前待审批的item不是userid，userId：" + userId + " itemId:" + pendingAuditItem.getUserId());
                    throw new SaasException(GlobalResponseCode.NoAuth);
                } else {
                    if (pendingAuditItem != null) {
                        //将item status设置为已审核
                        applyFlowService.setApplyFlowUserItemStatus(pendingAuditItem.getId(), ApplyFlowUserItemStatus.Approved);
                    }
                    try {
                        FindNextApproverIdAndStatusResult findNextApproverIdAndStatusResult = findNextApproverIdAndStatusInFlow(apply, applyFlowUserItems, skippedApprverIds, true);
                        finalStatus = findNextApproverIdAndStatusResult.getFinnalStatus();
                        finalJudgmentUser = findNextApproverIdAndStatusResult.getApplyStatus();
                        if (!StringTool.isNullOrEmpty(findNextApproverIdAndStatusResult.getApproverId())) {
                            approverId = findNextApproverIdAndStatusResult.getApproverId();
                        }
                        if (!StringTool.isNullOrEmpty(findNextApproverIdAndStatusResult.getReceiverId())) {
                            receiverId = findNextApproverIdAndStatusResult.getReceiverId();
                        }
                        if (findNextApproverIdAndStatusResult.getApplyStatus() == 1) {
                            //不是最后一个审批人存在下一个审批人
                            //查询审核中的审批日志
                            List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectApproveUser(applyId, ApplyLogAction.Approval.getValue());
                            //有日志数据情况下修改操作无日志数据情况下插入操作
                            if (!CollectionUtils.isEmpty(applyOrderLogList)) {
                                applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Approve.getValue(), applyOrderLogList.get(0).getId(), receiverId, approveModel.getComment(), new Date(), approveModel.getPrice());
                                List<ApplyOrderLog> applyOrderNextUserLogList = applyOrderLogExtMapper.selectNextApproveUser(applyId, ApplyLogAction.Unknown.getValue());
                                if (!CollectionUtils.isEmpty(applyOrderNextUserLogList)) {
                                    for (ApplyOrderLog applyOrderLog : applyOrderNextUserLogList) {
                                        String sponsorId = applyOrderLog.getSponsorId();
                                        EmployeeContract employee = iBaseOrganizationService.getEmployee(sponsorId, companyId);
                                        if (employee == null) {
                                            applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Skip.getValue(), applyOrderLog.getId(), "", CoreLanguage.Common_Value_ApprovalPersonDelete.getMessage(), null, approveModel.getPrice());
                                        } else {
                                            //保存下一个审批人为审批中状态
                                            applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Approval.getValue(), applyOrderLog.getId(), "", null, null, approveModel.getPrice());
                                            logId = applyOrderLog.getId();
                                            break;
                                        }
                                    }
                                }
                                setApproverAndStatus(applyId, approverId, finalStatus, now, logId, approveModel);
                            } else {
                                logId = writeLog(applyId, now, ip, userId, receiverId, approveModel.getComment(), action);
                                setApproverAndStatus(applyId, approverId, finalStatus, now, logId, approveModel);
                            }
                        } else if (findNextApproverIdAndStatusResult.getApplyStatus() == 2) {
                            //最后一个审批人
                            logId = sloveApplyLog(applyId, receiverId, approverId, finalStatus, now, ip, userId, action, approveModel);
                        } else {
                            //后面的审批人都不存在直接跳到授权负责人
                            List<ApplyOrderLog> applyOrderApprovalLogList = applyOrderLogExtMapper.selectApproveUser(applyId, ApplyLogAction.Approval.getValue());
                            applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Approve.getValue(), applyOrderApprovalLogList.get(0).getId(), receiverId, approveModel.getComment(), new Date(), approveModel.getPrice());

                            List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectApproveUser(applyId, ApplyLogAction.Unknown.getValue());
                            if (!CollectionUtils.isEmpty(applyOrderLogList)) {
                                for (ApplyOrderLog applyOrderLog : applyOrderLogList) {
                                    applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Skip.getValue(), applyOrderLog.getId(), "", CoreLanguage.Common_Value_ApprovalPersonDelete.getMessage(), null, approveModel.getPrice());
                                }
                            } else {
                                if (skippedApprverIds != null && skippedApprverIds.size() > 0) {
                                    for (String skippedApproverId : skippedApprverIds) {
                                        writeLog(applyId, now, ip, skippedApproverId, skippedApproverId, CoreLanguage.Common_Value_ApprovalPersonDelete.getMessage(), ApplyLogAction.Skip);
                                    }
                                }
                            }
                            int sort = 0;
                            if (!CollectionUtils.isEmpty(applyOrderLogList)) {
                                sort = applyOrderLogList.get(applyOrderLogList.size() - 1).getSort() + 100;
                            }
                            ApplyOrderLog logApprove = new ApplyOrderLog();
                            logApprove.setApplyOrderId(applyId);
                            logApprove.setIp(ip);
                            logApprove.setSponsorId(approverId);
                            logApprove.setReceiverId("");
                            logApprove.setCheckReason(null);
                            logApprove.setAction(ApplyLogAction.Approval.getValue());
                            logApprove.setSort(sort);
                            logApprove.setRootApplyOrderId(applyId);
                            applyOrderLogMapper.insert(logApprove);
                            logId = logApprove.getId();
                            setApproverAndStatus(applyId, approverId, finalStatus, now, logId, approveModel);
                        }
                    } catch (SaasException ex) {
                        logger.info("审批异常：" + ex.getResponseCode());
                        throw new SaasException(GlobalResponseCode.PayOrderCancleOrderSystemError);
                    }
                }
            }
        } else {
            finalStatus = ApplyStatus.Approved;
            receiverId = apply.getEmployeeId();
            approverId = userId;
            //处理审批日志和审批单的状态
            logId = sloveApplyLog(applyId, receiverId, approverId, finalStatus, now, ip, userId, action, approveModel);
        }
        //最后一个审批节点
        if (apply.getFlowType() == CompanyApplyType.Elastic.getValue() || (finalJudgmentUser != null && finalJudgmentUser == 2)) {
            List<ApplyTripInfo> tripList = applyTripInfoExtMapper.selectListByApplyOrderId(approveModel.getApply_id());
            ApplyTripInfo applyTripInfo = tripList.get(0);
            Map<String, Object> paymentMap = JSON.parseObject(applyTripInfo.getTripContent(), Map.class);
            //付款名称
            String paymentName = ObjUtils.toString(paymentMap.get("payment_name"));
            //付款主体id
            String payerId = ObjUtils.toString(paymentMap.get("payer_id"));
            //付款主体
            String payerName = ObjUtils.toString(paymentMap.get("payer_name"));
            //付款时间
            String payerTime = ObjUtils.toString(paymentMap.get("payer_time"));
            //供应商id
            Integer receiverInfoId = ObjUtils.toInteger(paymentMap.get("receiver_id"));
            //合同id
            Integer contractId = ObjUtils.toInteger(paymentMap.get("contract_id"));
            // 付款主体id(431新增)
            String companyAccountId = ObjUtils.toString(paymentMap.get("company_account_id"));
            // 付款主体名称(431新增)
            String bankAccountAcctName = ObjUtils.toString(paymentMap.get("bank_account_acct_name"));
            // 付款用途
            String paymentPurpose = ObjUtils.toString(paymentMap.get("payment_use"));
            // 凭证id
            Integer proofId = ObjUtils.toInteger(paymentMap.get("proof_id"));
            //发票集合
            List<Map<String, Object>> invoiceList = (List<Map<String, Object>>) paymentMap.get("invoice_list");
            // 发票选项
            Integer invoiceOption;
            if (CollectionUtils.isEmpty(invoiceList)) {
                invoiceOption = ObjUtils.toInteger(paymentMap.get("invoice_option")) == null ? 0 : ObjUtils.toInteger(paymentMap.get("invoice_option"));
            } else {
                invoiceOption = ObjUtils.toInteger(paymentMap.get("invoice_option")) == null ? 1 : ObjUtils.toInteger(paymentMap.get("invoice_option"));
            }
            List<InvoiceDetailReq> invoiceDetailModelList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(invoiceList)) {
                for (Map<String, Object> invoiceMap : invoiceList) {
                    InvoiceDetailReq invoiceDetailModel = new InvoiceDetailReq();
                    invoiceDetailModel.setFbInvId(ObjUtils.toString(invoiceMap.get("fbInvId")));
                    invoiceDetailModel.setInvCode(ObjUtils.toString(invoiceMap.get("invCode")));
                    invoiceDetailModel.setInvNo(ObjUtils.toString(invoiceMap.get("invNo")));
                    invoiceDetailModel.setInvTitle(ObjUtils.toString(invoiceMap.get("invTitle")));
                    invoiceDetailModel.setEinvStatus(ObjUtils.toInteger(invoiceMap.get("einvStatus")));
                    invoiceDetailModel.setIssuedDate(ObjUtils.toString(invoiceMap.get("issuedDate")));
                    invoiceDetailModel.setTotalPricePlusTax(ObjUtils.toBigDecimal(invoiceMap.get("totalPricePlusTax")));
                    invoiceDetailModel.setInvFromSource(ObjUtils.toInteger(invoiceMap.get("invFromSource")));
                    invoiceDetailModelList.add(invoiceDetailModel);
                }
            }
            //供应商详情
            logger.info(String.format("供应商详情调用：%s,%s", companyId, receiverInfoId));
            CompanyPaymentSupplierVO companyPaymentSupplierVO = iCompanyPaymentService.companyPaymentSupplier(companyId, receiverInfoId);
            logger.info(String.format("供应商详情返回结果：%s", JsonUtils.toJson(companyPaymentSupplierVO)));
            //合同详情
            CompanyPaymentContractVO companyPaymentContractVO = new CompanyPaymentContractVO();
            if (contractId != null) {
                logger.info(String.format("合同详情调用：%s,%s", companyId, contractId));
                companyPaymentContractVO = iCompanyPaymentService.companyPaymentContract(companyId, contractId);
                logger.info(String.format("合同详情调用返回结果：%s", JsonUtils.toJson(companyPaymentContractVO)));
            }
            if (CollectionUtils.isNotEmpty(invoiceDetailModelList)) {
                try {
                    List<FbtInvoicePublicReqRPCDTO> list = Lists.newArrayList();
                    for (InvoiceDetailReq invoiceDetailReq : invoiceDetailModelList) {
                        FbtInvoicePublicReqRPCDTO fbtInvoicePublicReqRPCDTO = new FbtInvoicePublicReqRPCDTO();
                        fbtInvoicePublicReqRPCDTO.setCompanyId(companyId);
                        fbtInvoicePublicReqRPCDTO.setEmployeeId(apply.getEmployeeId());
                        fbtInvoicePublicReqRPCDTO.setFbInvId(invoiceDetailReq.getFbInvId());
                        list.add(fbtInvoicePublicReqRPCDTO);
                    }
                    logger.info(String.format("个人发票导出调用：%s,%s,%s", companyId, apply.getEmployeeId(), list));
                    iFbtInvoiceService.impBatchInvoiceToPublicWallet(list);
                } catch (FinhubException ex) {
                    logger.info(String.format("个人发票导出对公发票异常：%s,%s,%s,%s", ex.getCode(), ex.getMessage(), ex.getType(), ex.getTitle()));
                    throw new SaasException(ex.getCode(), ex.getMessage(), ex.getType(), ex.getTitle());
                }
            }
            String bankAccountNo = null;
            BankNameEnum bankAccountName = null;
            AcctPublicQueryDetailRespDTO acctPublicDetailRespRPCDTO = null;
            try {
                logger.info(String.format("公司账号调用：%s,%s", companyId, companyAccountId));
                if (StringUtils.isBlank(companyAccountId)) {
                    throw new SaasException(GlobalResponseCode.ApplyCenterAlert);
                } else {
                    acctPublicDetailRespRPCDTO = iAcctPublicSearchDechService.queryByCompanyAcctId(companyId, companyAccountId);
                }
                logger.info(String.format("公司账号调用返回结果：%s", JsonUtils.toJson(acctPublicDetailRespRPCDTO)));
            } catch (FinhubException ex) {
                logger.info(String.format("公司账号调用异常：%s,%s,%s,%s", ex.getCode(), ex.getMessage(), ex.getType(), ex.getTitle()));
                throw new SaasException(ex.getCode(), ex.getMessage(), ex.getType(), ex.getTitle());
            }
            if (acctPublicDetailRespRPCDTO == null) {
                throw new SaasException(GlobalResponseCode.ApplyCompanyAccountIsNull);
            }
            //公司账号
            bankAccountNo = acctPublicDetailRespRPCDTO.getBankAccountNo();
            //公司开户行
            bankAccountName = acctPublicDetailRespRPCDTO.getBankAccountName();
            CreateAppPaymentReqDto createAppPaymentReqDto = new CreateAppPaymentReqDto();
            createAppPaymentReqDto.setCompanyBank(bankAccountName.getCode());
            createAppPaymentReqDto.setCompanyAccount(bankAccountNo);
            createAppPaymentReqDto.setPaymentName(paymentName);
            createAppPaymentReqDto.setTotalPrice(applyTripInfo.getEstimatedAmount());
            createAppPaymentReqDto.setPayerName(payerName);
            createAppPaymentReqDto.setReceiverName(companyPaymentSupplierVO.getCompanySupplierName());
            createAppPaymentReqDto.setReceiverAccountName(companyPaymentSupplierVO.getBankAccountName());
            createAppPaymentReqDto.setReceiverBank(companyPaymentSupplierVO.getSubbranch());
            createAppPaymentReqDto.setReceiverAccount(companyPaymentSupplierVO.getBankAccount());
            if (ObjectUtil.isNotNull(companyPaymentContractVO)) {
                createAppPaymentReqDto.setContractId(contractId);
                createAppPaymentReqDto.setContractCode(companyPaymentContractVO.getContractCode());
                createAppPaymentReqDto.setContractName(companyPaymentContractVO.getContractName());
            }
            createAppPaymentReqDto.setPayerId(payerId);
            createAppPaymentReqDto.setReceiverId(receiverInfoId);
            createAppPaymentReqDto.setUnionPayAccount(companyPaymentSupplierVO.getUnionPayAccount());
            createAppPaymentReqDto.setDuringApplyId(applyId);
            createAppPaymentReqDto.setDuringApplyUserName(apply.getApplicantName());
            createAppPaymentReqDto.setPayTime(DateTimeTool.fromStringToDate(payerTime));
            createAppPaymentReqDto.setUserId(apply.getEmployeeId());
            createAppPaymentReqDto.setCompanyId(companyId);
            createAppPaymentReqDto.setPaymentPurpose(paymentPurpose);
            createAppPaymentReqDto.setProofId(proofId);
            // 不对标，做一下转换 1-已开 0-待开 2-无
            // 1已开发票 2待开发票 3无发票
            if (invoiceOption == 1) {
                createAppPaymentReqDto.setInvoiceConfigStatus(1);
            } else if (invoiceOption == 0) {
                createAppPaymentReqDto.setInvoiceConfigStatus(2);
            } else if (invoiceOption == 2) {
                createAppPaymentReqDto.setInvoiceConfigStatus(3);
            }
            // 查询项目信息
            if (apply.getCostAttributionCategory() == CostAttributionCategory.CostCenter.getKey()) {
                List<CostCenterDto> costCenterDtos = iCostCenterService.queryCostCenterListByIds(companyId, Lists.newArrayList(apply.getCostAttributionId()));
                if (CollectionUtils.isNotEmpty(costCenterDtos) && costCenterDtos.size() == 1) {
                    CostCenterDto costCenterDto = costCenterDtos.get(0);
                    createAppPaymentReqDto.setProjectId(apply.getCostAttributionId());
                    createAppPaymentReqDto.setProjectName(costCenterDto.getCode() + "-" + apply.getCostAttributionName());
                } else {
                    logger.info("companyId: {} , projectId: {} , doesn't match or more than one", companyId, apply.getCostAttributionId());
                }
            }
            if (CollectionUtils.isNotEmpty(invoiceDetailModelList)) {
                createAppPaymentReqDto.setInvoiceDetailReqList(invoiceDetailModelList);
            }
            // 处理新老版本问题，新字段有值，老版本字段置空
            if (StringUtils.isNotBlank(companyAccountId)) {
                createAppPaymentReqDto.setCompanyAccountId(companyAccountId);
                createAppPaymentReqDto.setPayerId(null);
            }
            if (StringUtils.isNotBlank(bankAccountAcctName)) {
                createAppPaymentReqDto.setBankAccountAcctName(bankAccountAcctName);
                createAppPaymentReqDto.setPayerName(null);
            }
            //费用信息
            CostInfo costAttributionInfo = JSON.parseObject(ObjUtils.toString(paymentMap.get("costInfo")), CostInfo.class);
            if (costAttributionInfo != null) {
                createAppPaymentReqDto.setCostAttributionInfo(costAttributionInfo);
            }
            try {
                logger.info(String.format("生成付款单参数：%s", createAppPaymentReqDto));
                iBankPaymentService.createAppPayment(createAppPaymentReqDto);
            } catch (FinhubException ex) {
                logger.info(String.format("生成付款单异常：%s,%s,%s,%s", ex.getCode(), ex.getMessage(), ex.getType(), ex.getTitle()));
                throw new SaasException(ex.getCode(), ex.getMessage(), ex.getType(), ex.getTitle());
            }
            // 生成pdf
            Map<String, Object> writeoffMap = sloveApplyPaymentTemplate(apply);
            new Thread(()-> producePdf(apply, writeoffMap)).start();
            // 同步日志
            applyV2Service.syncLogOnFinishProcess(applyId, companyId);
        }
        // 生成pdf
//        createElecApplyOrder(apply);
        setApplyApproverAndPushMessage(apply, userId, ip, finalStatus, receiverId, approverId, approveModel.getComment(), action, skippedApprverIds, logId);
        //push 抄送人
        pushCCMessage(apply, apply.getEmployeeId(), action, finalStatus);
        return GlobalResponseCode.Success;
    }

    private void createElecApplyOrder(ApplyOrder applyOrder){
        try{
            // 核销模板
            Map<String, Object> writeoffMap = sloveApplyPaymentTemplate(applyOrder);
            logger.info("applyOrderId=:{},writeoffMap=:{}",applyOrder.getId(),JSON.toJSONString(writeoffMap));
            // 生成pdf
            iApplyOrderService.producePdf(applyOrder,writeoffMap,ApplyType.Payment);
        }catch (Exception e){
            logger.error("生成付款电子单失败："+e);
        }
    }

    /**
     * 审批、拒绝、转交情况下处理审批日志的状态和修改审批单的状态
     *
     * @param applyId
     * @param receiverId
     * @param approverId
     * @param finalStatus
     * @param now
     * @param ip
     * @param userId
     * @param action
     * @param approveModel
     */
    //@Transactional(value = "saas")
    public Integer sloveApplyLog(String applyId, String receiverId, String approverId, ApplyStatus finalStatus, Date now, String ip, String userId, ApplyLogAction action, ApplyApproveContract approveModel) {
        //查询审核中的审批日志
        List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectListByCreateTimeDescAndAction(applyId, ApplyLogAction.Approval.getValue());
        //有审批日志情况下进行修改操作,无审批日志情况下插入操作
        if (!CollectionUtils.isEmpty(applyOrderLogList)) {
            applyOrderLogExtMapper.updateApplyOrderActionById(action.getValue(), applyOrderLogList.get(0).getId(), receiverId, approveModel.getComment(), new Date(), null);
            setApproverAndStatus(applyId, approverId, finalStatus, now, applyOrderLogList.get(0).getId(), approveModel);
            return applyOrderLogList.get(0).getId();
        } else {
            Integer logId = writeLog(applyId, now, ip, userId, receiverId, approveModel.getComment(), action);
            setApproverAndStatus(applyId, approverId, finalStatus, now, logId, approveModel);
            return logId;
        }
    }

   // @Transactional(value = "saas")
    private int setApproverAndStatus(String applyId, String approverId, ApplyStatus status, Date time, Integer logId, ApplyApproveContract approveModel) {
        if (approveModel == null) {
            approveModel.setComment("");
            approveModel.setPrice(null);
        }
        int count = applyOrderExtMapper.updateApproverIdAndStatus(applyId, approverId, status.getValue(), time, logId, approveModel.getComment(), null);
        return count;
    }

   // @Transactional(value = "saas")
    private Integer writeLog(String id, Date createTime, String ip, String sponsorId, String receiverId, String checkReason, ApplyLogAction action) {
        //插入日志
        ApplyOrderLog log = new ApplyOrderLog();
        log.setApplyOrderId(id);
        log.setCreateTime(createTime);
        log.setIp(ip);
        log.setSponsorId(sponsorId);
        if (receiverId == null) {
            receiverId = "";
        }
        log.setReceiverId(receiverId);
        log.setCheckReason(checkReason);
        log.setAction(action.getValue());
        log.setPrice(null);
        log.setRootApplyOrderId(id);
        applyOrderLogMapper.insert(log);
        return log.getId();
    }

    private FindNextApproverIdAndStatusResult findNextApproverIdAndStatusInFlow(ApplyOrder apply, List<ApplyFlowUserItem> applyFlowUserItems, List<String> skippedApprverIds, boolean couldFinalApprove) throws SaasException {
        String approverId = null;
        String receiverId = null;
        ApplyStatus finalStatus;
        Integer applyStatus = null;
        int remainIndex = applyFlowUserItems.size();
        for (int i = 0; i < applyFlowUserItems.size(); i++) {
            if (ValueTool.areEqual(applyFlowUserItems.get(i).getStatus(), ApplyFlowUserItemStatus.Waiting.getValue())) {
                remainIndex = i;
                break;
            }
        }
        //找下一个审批人
        boolean findNextApprover = false;
        // 是否是最后一名审批人
        boolean finnalApprover = true;
        for (int i = remainIndex; i < applyFlowUserItems.size(); i++) {
            ApplyFlowUserItem item = applyFlowUserItems.get(i);
            if (ValueTool.areEqual(item.getStatus(), ApplyFlowUserItemStatus.Waiting.getValue())) {
                finnalApprover = false;
                if (userService.isUserInCompany(item.getUserId(), apply.getCompanyId())) {
                    findNextApprover = true;
                    receiverId = item.getUserId();
                    approverId = receiverId;
                    applyStatus = 1;
                    applyFlowService.setApplyFlowUserItemStatus(item.getId(), ApplyFlowUserItemStatus.PendingAudit);
                    break;
                } else {
                    skippedApprverIds.add(item.getUserId());
                    applyFlowService.setApplyFlowUserItemStatus(item.getId(), ApplyFlowUserItemStatus.Skip);
                }
            }
        }
        if (finnalApprover && couldFinalApprove) {
            //当前审批人已经是最后一个审批人
            finalStatus = ApplyStatus.Approved;
            receiverId = apply.getEmployeeId();
            applyStatus = 2;
        } else {
            finalStatus = ApplyStatus.PendingAudit;
            if (!findNextApprover) {
                //后面的审批人都已不存在，直接跳到授权负责人
                List<String> superAdminEmployeeIds = customRoleService.getEmployeeIdByRoleId(Integer.toString(UserRole.CompanySuperAdmin.getValue()), apply.getCompanyId());
                if (superAdminEmployeeIds == null || superAdminEmployeeIds.size() == 0) {
                    throw new SaasException(GlobalResponseCode.ApplyCanotApproveBecauseHasNoSuperAdmin);
                } else {
                    receiverId = superAdminEmployeeIds.get(0);
                    approverId = receiverId;
                    applyStatus = 3;
                }
            }
        }
        FindNextApproverIdAndStatusResult result = new FindNextApproverIdAndStatusResult();
        result.setApproverId(approverId);
        result.setReceiverId(receiverId);
        result.setFinnalStatus(finalStatus);
        result.setApplyStatus(applyStatus);
        return result;
    }

    @Override
    //@Transactional(value = "saas")
    public void setApplyApproverAndPushMessage(ApplyOrder apply, String userId, String ip, ApplyStatus finalStatus, String receiverId, String approverId, String comment, ApplyLogAction action, List<String> skippedApprverIds, Integer logId) {
        Date now = new Date();
        String applyId = apply.getId();
        if (!StringTool.isNullOrEmpty(approverId) && !approverId.equals(apply.getApproverId())) {
            insertApproverMap(applyId, approverId, now);
        }
        postMessage(apply, userId, receiverId, action, finalStatus, logId, comment);
    }

    /**
     * 驳回
     *
     * @param approveModel
     * @param userId
     * @param ip
     * @return
     */
    @Override
    //@Transactional(value = "saas")
    public GlobalResponseCode repulse(ApplyApproveContract approveModel, String userId, String companyId, String ip, String userName, String clientVersion, String source) throws SaasException {
        FlowCheckUtil.check(companyId, clientVersion, source);
        if (approveModel == null) {
            return GlobalResponseCode.ApplyIsNull;
        }
        if (StringTool.isNullOrEmpty(approveModel.getApply_id())) {
            return GlobalResponseCode.ApplyIdInvalid;
        }
        if (approveModel.getComment() != null && approveModel.getComment().length() > 200) {
            return GlobalResponseCode.ApplyCheckReasonInvalid;
        }

        if (approveModel.getComment() == null || approveModel.getComment().length() == 0) {
            //驳回审批单必须要填写理由
            return GlobalResponseCode.ApplyRefuseMustWriteReason;
        }
        String applyId = approveModel.getApply_id();
        ApplyOrder apply = applyOrderMapper.selectByPrimaryKey(approveModel.getApply_id());
        if (apply == null) {
            return GlobalResponseCode.NotFound;
        }
        if (apply.getState() != null && apply.getState().intValue() == ApplyStatus.Backout.getValue()) {
            //审批已撤销
            return GlobalResponseCode.CenterApplyCancleOrder;
        }
        if (!apply.getCompanyId().equals(companyId)) {
            //申请单和当前人不在一个公司
            return GlobalResponseCode.ApplyCompanyCannotMatch;
        }
        if (apply.getState() != ApplyStatus.PendingAudit.getValue()) {
            //该单不是待审核,不能审批
            return GlobalResponseCode.ApplyStatusNotPendingAudit;
        }
        if (!apply.getApproverId().equals(userId)) {
            //不是当前人可以处理的审批单
            return GlobalResponseCode.NoAuth;
        }
        ApplyStatus finalStatus;
        ApplyLogAction action;
        String receiverId; //下一个审批人/通知人
        String approverId; //审批人
        Integer logId = null;
        List<String> skippedApprverIds = new ArrayList<>();//固定审批流跳过的审批人
        Date now = new Date();
        //拒绝
        List<ApplyFlowUserItem> applyFlowUserItems = applyFlowService.getFlowItemsByApplyId(applyId);
        for (ApplyFlowUserItem applyFlowUserItem : applyFlowUserItems) {
            if (ValueTool.areEqual(applyFlowUserItem.getStatus(), ApplyFlowUserItemStatus.PendingAudit.getValue())
                    && applyFlowUserItem.getUserId().equals(userId)) {
                applyFlowService.setApplyFlowUserItemStatus(applyFlowUserItem.getId(), ApplyFlowUserItemStatus.Refuse);
            }
        }
        finalStatus = ApplyStatus.Return;
        action = ApplyLogAction.Refuse;
        receiverId = apply.getEmployeeId();
        approverId = userId;
        //修改供应商和合同的状态
        List<ApplyTripInfo> tripList = applyTripInfoExtMapper.selectListByApplyOrderId(approveModel.getApply_id());
        String tripContent = tripList.get(0).getTripContent();
        Map<String, Object> paymentMap = JSON.parseObject(tripContent, Map.class);
        Integer receiverInfoId = ObjUtils.toInteger(paymentMap.get("receiver_id"));
        Integer contractId = ObjUtils.toInteger(paymentMap.get("contract_id"));
        logger.info(String.format("修改供应商和合同的参数：%s,%s,%s,%s,%s,%s", receiverInfoId, contractId, CompanyPaymentContractSupplierState.refuse.getCode(), companyId, userId, userName));
        updateContractIdAndReceiverId(receiverInfoId, contractId, CompanyPaymentContractSupplierState.refuse.getCode(), companyId, userId, userName);
        String invoiceListStr = ObjUtils.toString(paymentMap.get("invoice_list"));
        List<Map> invoiceList = JSON.parseArray(invoiceListStr, Map.class);
        if (CollectionUtils.isNotEmpty(invoiceList)) {
            List<String> fbInvIds = Lists.newArrayList();
            for (Map<String, Object> invoiceInfo : invoiceList) {
                String fbInvId = ObjUtils.toString(invoiceInfo.get("fbInvId"));
                fbInvIds.add(fbInvId);
            }
            try {
                TradeRelationInvoiceReqRPCDTO tradeRelationInvoiceReqRPCDTO = new TradeRelationInvoiceReqRPCDTO();
                tradeRelationInvoiceReqRPCDTO.setEmployeeId(apply.getEmployeeId());
                tradeRelationInvoiceReqRPCDTO.setCompanyId(apply.getCompanyId());
                tradeRelationInvoiceReqRPCDTO.setApplyOrderId(applyId);
                tradeRelationInvoiceReqRPCDTO.setFbInvIds(fbInvIds);
                logger.info(String.format("校验申请单和发票解绑调用：%s,%s", JsonUtils.toJson(tradeRelationInvoiceReqRPCDTO), applyId));
                iInvoiceTradeService.unbindTradeAndInvoice(tradeRelationInvoiceReqRPCDTO);
            } catch (FinhubException ex) {
                logger.info(String.format("校验申请单和发票解绑调用异常结果：%s,%s,%s,%s", ex.getCode(), ex.getMessage(), ex.getType(), ex.getTitle()));
                throw new SaasException(ex.getCode(), ex.getMessage(), ex.getType(), ex.getTitle());
            }
        }
        //处理审批日志和审批单的状态
        logId = sloveApplyLog(applyId, receiverId, approverId, finalStatus, now, ip, userId, action, approveModel);
        // 同步日志
        applyV2Service.syncLogOnFinishProcess(applyId, companyId);
        setApplyApproverAndPushMessage(apply, userId, ip, finalStatus, receiverId, approverId, approveModel.getComment(), action, skippedApprverIds, logId);
        return GlobalResponseCode.Success;
    }

    /**
     * 撤销
     *
     * @param userId
     * @param companyId
     * @param ip
     * @return
     */
    @Override
    //@Transactional(value = "fenbeitong")
    public GlobalResponseCode revoke(String userId, String companyId, ApplyRevokeContract data, String ip, String userName, String clientVersion, String source) {
        FlowCheckUtil.check(companyId, clientVersion, source);
        ApplyOrder order = getApplyOrderModelByIdAndUserIdAndCompanyId(data.getId(), userId, companyId, null);
        if (order == null) {
            return GlobalResponseCode.NotFound;
        }
        if (order.getState() != ApplyStatus.PendingAudit.getValue()) {
            return GlobalResponseCode.ApplyStatusNotPendingAudit;
        }
        Date now = new Date();
        ApplyLogAction action = ApplyLogAction.Revoke;
        int logSort = 0;
        List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectListByCreateTimeDesc(data.getId());
        if (!CollectionUtils.isEmpty(applyOrderLogList)) {
            Integer sort = applyOrderLogList.get(0).getSort();
            if (sort != null) {
                logSort = sort + 100;
            }
        }
        //插入日志
        ApplyOrderLog log = new ApplyOrderLog();
        log.setApplyOrderId(data.getId());
        log.setRootApplyOrderId(data.getId());
        log.setCreateTime(now);
        log.setIp(ip);
        log.setSponsorId(userId);
        log.setReceiverId(userId);
        log.setCheckReason(null);
        log.setAction(action.getValue());
        log.setSort(logSort);
        applyOrderLogMapper.insertSelective(log);
        applyOrderExtMapper.setCenterStatus(data.getId(), ApplyStatus.Backout.getValue(), now, log.getId(), SaasFlowConstant.SETTING_TYPE_PAYMENT);
        //修改供应商和合同的状态
        List<ApplyTripInfo> tripList = applyTripInfoExtMapper.selectListByApplyOrderId(data.getId());
        String tripContent = tripList.get(0).getTripContent();
        Map<String, Object> paymentMap = JSON.parseObject(tripContent, Map.class);
        Integer receiverInfoId = ObjUtils.toInteger(paymentMap.get("receiver_id"));
        Integer contractId = ObjUtils.toInteger(paymentMap.get("contract_id"));
        logger.info(String.format("修改供应商和合同的参数：%s,%s,%s,%s,%s,%s", receiverInfoId, contractId, CompanyPaymentContractSupplierState.refuse.getCode(), companyId, userId, userName));
        updateContractIdAndReceiverId(receiverInfoId, contractId, CompanyPaymentContractSupplierState.refuse.getCode(), companyId, userId, userName);
        String invoiceListStr = ObjUtils.toString(paymentMap.get("invoice_list"));
        List<Map> invoiceList = JSON.parseArray(invoiceListStr, Map.class);
        if (CollectionUtils.isNotEmpty(invoiceList)) {
            List<String> fbInvIds = Lists.newArrayList();
            for (Map<String, Object> invoiceInfo : invoiceList) {
                String fbInvId = ObjUtils.toString(invoiceInfo.get("fbInvId"));
                fbInvIds.add(fbInvId);
            }
            try {
                TradeRelationInvoiceReqRPCDTO tradeRelationInvoiceReqRPCDTO = new TradeRelationInvoiceReqRPCDTO();
                tradeRelationInvoiceReqRPCDTO.setEmployeeId(userId);
                tradeRelationInvoiceReqRPCDTO.setCompanyId(companyId);
                tradeRelationInvoiceReqRPCDTO.setApplyOrderId(data.getId());
                tradeRelationInvoiceReqRPCDTO.setFbInvIds(fbInvIds);
                logger.info(String.format("校验申请单和发票解绑调用：%s,%s", JsonUtils.toJson(tradeRelationInvoiceReqRPCDTO), data.getId()));
                iInvoiceTradeService.unbindTradeAndInvoice(tradeRelationInvoiceReqRPCDTO);
            } catch (FinhubException ex) {
                logger.info(String.format("校验申请单和发票解绑调用异常结果：%s,%s,%s,%s", ex.getCode(), ex.getMessage(), ex.getType(), ex.getTitle()));
                throw new SaasException(ex.getCode(), ex.getMessage(), ex.getType(), ex.getTitle());
            }
        }
        // 同步日志
        applyV2Service.syncLogOnFinishProcess(data.getId(), companyId);
        return GlobalResponseCode.Success;
    }

    /**
     * 转交审批单
     *
     * @param approveModel
     * @param userId
     * @param ip
     * @return
     */
    @Override
    //@Transactional(value = "saas")
    public GlobalResponseCode forward(ApplyApproveContract approveModel, String userId, String companyId, String ip, String clientVersion, String source) throws SaasException {
        FlowCheckUtil.check(companyId, clientVersion, source);
        if (approveModel == null) {
            return GlobalResponseCode.ApplyIsNull;
        }
        if (StringTool.isNullOrEmpty(approveModel.getApply_id())) {
            return GlobalResponseCode.ApplyIdInvalid;
        }
        if (approveModel.getComment() != null && approveModel.getComment().length() > 200) {
            return GlobalResponseCode.ApplyCheckReasonInvalid;
        }
        int status = approveModel.getStatus();
        String nextApproverId = approveModel.getApprover_id();
        if (StringTool.isNullOrEmpty(nextApproverId)) {
            //转交需要一个承接人
            return GlobalResponseCode.ApplyApproveIdInvalid;
        } else if (nextApproverId.equals(userId)) {
            return GlobalResponseCode.ApplyTransferNotSelf;
        }
        String applyId = approveModel.getApply_id();
        ApplyOrder apply = applyOrderMapper.selectByPrimaryKey(approveModel.getApply_id());
        if (apply == null) {
            return GlobalResponseCode.NotFound;
        }
        boolean isFlow = (ValueTool.areEqual(apply.getFlowType(), CompanyApplyType.Flow.getValue()) || ValueTool.areEqual(apply.getFlowType(), CompanyApplyType.CONDITIONAL.getValue()));
        if (!apply.getCompanyId().equals(companyId)) {
            //申请单和当前人不在一个公司
            return GlobalResponseCode.ApplyCompanyCannotMatch;
        }
        if (apply.getState() != ApplyStatus.PendingAudit.getValue()) {
            //该单不是待审核或者转交状态,不能审批
            return GlobalResponseCode.ApplyStatusNotPendingAudit;
        }
        if (apply.getEmployeeId().equals(approveModel.getApprover_id())) {
            return GlobalResponseCode.ApplyApproverCannotBeSelf;
        }
        Boolean exist = userService.isUserInCompany(approveModel.getApprover_id(), apply.getCompanyId());
        if (!exist) {
            return GlobalResponseCode.ApplyCompanyCannotMatch;
        }
        if (!apply.getApproverId().equals(userId)) {
            //不是当前人可以处理的审批单
            return GlobalResponseCode.NoAuth;
        }
        ApplyStatus finalStatus;
        ApplyLogAction action;
        String receiverId; //下一个审批人/通知人
        String approverId; //审批人
        Integer logId = null;
        List<String> skippedApprverIds = new ArrayList<>();//固定审批流跳过的审批人
        Date now = new Date();
        //转交
        if (isFlow) {
            List<ApplyFlowUserItem> applyFlowUserItems = applyFlowService.getFlowItemsByApplyId(applyId);
            if (applyFlowUserItems != null && applyFlowUserItems.size() > 0) {
                Optional<ApplyFlowUserItem> optionalApplyFlowUserItem = applyFlowUserItems.stream().filter(m -> m.getUserId().equals(userId) &&
                        ValueTool.areEqual(m.getStatus(), ApplyFlowUserItemStatus.PendingAudit.getValue())).findAny();
                if (optionalApplyFlowUserItem.isPresent()) {
                    ApplyFlowUserItem applyFlowUserItem = optionalApplyFlowUserItem.get();
                    //当前审批人将此单转交给另一人，将此item状态置为Transfered
                    applyFlowService.setApplyFlowUserItemStatus(applyFlowUserItem.getId(), ApplyFlowUserItemStatus.Transfered);
                }
            }
        }
        finalStatus = ApplyStatus.PendingAudit;
        action = ApplyLogAction.Forward;
        receiverId = approveModel.getApprover_id();
        approverId = receiverId;

        //查询审核中的审批日志
        List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectListByCreateTimeDescAndAction(applyId, ApplyLogAction.Approval.getValue());
        //有日志数据情况下修改操作无日志数据情况下插入操作
        if (!CollectionUtils.isEmpty(applyOrderLogList)) {
            applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Forward.getValue(), applyOrderLogList.get(0).getId(), receiverId, approveModel.getComment(), new Date(), approveModel.getPrice());
            int logSort = 0;
            List<ApplyOrderLog> allApplyOrderLogList = applyOrderLogExtMapper.selectListByCreateTimeDesc(applyId);
            Integer sort = allApplyOrderLogList.get(0).getSort();
            if (sort != null) {
                logSort = sort + 100;
            }
            //Integer sort = applyOrderLogList.get(0).getSort() + 1;
            ApplyOrderLog log = new ApplyOrderLog();
            log.setApplyOrderId(applyId);
            log.setIp(ip);
            log.setSponsorId(receiverId);
            log.setReceiverId("");
            log.setCheckReason(null);
            log.setAction(ApplyLogAction.Approval.getValue());
            log.setSort(logSort);
            log.setRootApplyOrderId(applyId);
            applyOrderLogMapper.insertSelective(log);
            logId = log.getId();
            setApproverAndStatus(applyId, approverId, finalStatus, now, log.getId(), approveModel);
        } else {
            writeLog(applyId, now, ip, userId, receiverId, approveModel.getComment(), action);
            ApplyOrderLog logApprove = new ApplyOrderLog();
            logApprove.setApplyOrderId(applyId);
            logApprove.setIp(ip);
            logApprove.setSponsorId(receiverId);
            logApprove.setReceiverId("");
            logApprove.setCheckReason(null);
            logApprove.setAction(ApplyLogAction.Approval.getValue());
            logApprove.setSort(0);
            logApprove.setRootApplyOrderId(applyId);
            applyOrderLogMapper.insertSelective(logApprove);
            logId = logApprove.getId();
            setApproverAndStatus(applyId, approverId, finalStatus, now, logId, approveModel);
        }
        setApplyApproverAndPushMessage(apply, userId, ip, finalStatus, receiverId, approverId, approveModel.getComment(), action, skippedApprverIds, logId);
        return GlobalResponseCode.Success;
    }


    /**
     * 对公付款申请单pdf生成
     */
    @Override
    //@Transactional(value = "saas", rollbackFor = Exception.class)
    public void disposeApplyPaymentPdf() {
        ApplyOrderExample applyOrderExample = new ApplyOrderExample();
        applyOrderExample.createCriteria()
                .andApplyOrderTypeEqualTo(SaasFlowConstant.SETTING_TYPE_PAYMENT)
                .andTypeEqualTo(ApplyType.Payment.getValue())
                .andDownloadLinkIsNull().andStateEqualTo(ApplyStatus.Approved.getValue());
        List<ApplyOrder> applyOrderList = applyOrderMapper.selectByExample(applyOrderExample);
        if (CollectionUtils.isEmpty(applyOrderList)) {
            logger.info("没有待处理对公付款申请单");
            return;
        }
        logger.info("待处理对公付款申请单pdf数量: {}", applyOrderList.size());
        applyOrderList.forEach(e -> {
            Map<String, Object> paramMap = sloveApplyPaymentTemplate(e);
            Boolean generateFlag = producePdf(e, paramMap);
            logger.info("对公付款申请单id: {} ,处理结果: {}", e.getId(), generateFlag);
        });
    }

    /**
     * 对公付款申请单pdf更新
     */
    @Override
   // @Transactional(value = "saas", rollbackFor = Exception.class)
    public void updateApplyPaymentPdf() {
        ApplyOrderExample applyOrderExample = new ApplyOrderExample();
        applyOrderExample.createCriteria()
                .andApplyOrderTypeEqualTo(SaasFlowConstant.SETTING_TYPE_PAYMENT)
                .andTypeEqualTo(ApplyType.Payment.getValue())
                .andStateEqualTo(ApplyStatus.Approved.getValue());
        List<ApplyOrder> applyOrderList = applyOrderMapper.selectByExample(applyOrderExample);
        if (CollectionUtils.isEmpty(applyOrderList)) {
            logger.info("没有待更新对公付款申请单");
            return;
        }
        logger.info("待更新对公付款申请单pdf数量: {}", applyOrderList.size());
        applyOrderList.forEach(e -> {
            Map<String, Object> paramMap = sloveApplyPaymentTemplate(e);
            Boolean generateFlag = producePdf(e, paramMap);
            logger.info("对公付款申请单id: {} ,更新结果: {}", e.getId(), generateFlag);
        });
    }

    /**
     * 对公付款申请单pdf更新
     */
    @Override
    @Transactional(value = "saas", rollbackFor = Exception.class)
    public void updateApplyPaymentPdfById(String id) {
        if (StringUtils.isBlank(id)) {
            return;
        }
        ApplyOrder applyOrder = applyOrderMapper.selectByPrimaryKey(id);
        if (ObjectUtil.isNull(applyOrder)) {
            logger.info("没有待更新对公付款申请单");
            return;
        }
        Map<String, Object> paramMap = sloveApplyPaymentTemplate(applyOrder);
        Boolean generateFlag = producePdf(applyOrder, paramMap);
        logger.info("对公付款申请单id: {} ,更新结果: {}", id, generateFlag);
    }

    /**
     * 对公付款申请单pdf数据提取
     *
     * @param applyOrder
     * @return
     */
    @Override
    public Map<String, Object> sloveApplyPaymentTemplate(ApplyOrder applyOrder) {
        Map<String, Object> templateMap = new HashMap<>(32);
        // 拼装基本信息
        EmployeeContract employee = iBaseOrganizationService.getEmployee(applyOrder.getEmployeeId(), applyOrder.getCompanyId());
        if (employee == null) {
            templateMap.put("userDept", "");
        } else {
            templateMap.put("userDept", employee.getOrg_name());
        }
        templateMap.put("userName", applyOrder.getApplicantName());
        templateMap.put("applyId", applyOrder.getId());
        String reasonDesc = applyOrder.getApplyReason();
        if (ObjectUtil.isNotNull(applyOrder.getApplyReasonDesc())) {
            reasonDesc = reasonDesc + "; " + applyOrder.getApplyReasonDesc();
        }
        templateMap.put("applyReason", reasonDesc);
        templateMap.put("createTime", DateTimeTool.fromDateTimeToString(applyOrder.getCreateTime()));
        templateMap.put("totalPrice", new DecimalFormat("0.00").format(applyOrder.getBudget() / 100D));
        ApplyOrderV2Contract applyOrderV2Contract = new ApplyOrderV2Contract();
        // 不设置公司id会报错
        applyOrderV2Contract.setCompany_id(applyOrder.getCompanyId());
        // 查询数据
        getTripListByApplyOrderId(applyOrder.getId(), applyOrderV2Contract);
        templateMap.put("applyName", applyOrderV2Contract.getPayment_name());
        templateMap.put("paymentId", StringUtils.isBlank(applyOrderV2Contract.getPayer_id()) ? applyOrderV2Contract.getCompany_account_id() : applyOrderV2Contract.getPayer_id());
        templateMap.put("paymentName", StringUtils.isBlank(applyOrderV2Contract.getPayer_name()) ? applyOrderV2Contract.getBank_account_acct_name() : applyOrderV2Contract.getPayer_name());
        templateMap.put("paymentTime", applyOrderV2Contract.getPayer_time());
        // 初始化是否显示项目，0-不显示，1-显示
        templateMap.put("hasPj", 0);
        // 1-部门，2-项目
        if (applyOrder.getCostAttributionCategory() == CostAttributionCategory.CostCenter.getKey()) {
            templateMap.put("hasPj", 1);
            List<CostCenterDto> costCenterDtos = iCostCenterService.queryCostCenterListByIds(applyOrder.getCompanyId(), Lists.newArrayList(applyOrder.getCostAttributionId()));
            if (CollectionUtils.isNotEmpty(costCenterDtos) && costCenterDtos.size() == 1) {
                CostCenterDto costCenterDto = costCenterDtos.get(0);
                templateMap.put("project", costCenterDto.getCode() + "-" + applyOrder.getCostAttributionName());
            } else {
                logger.info("companyId: {} , projectId: {} , doesn't match or more than one", applyOrder.getCompanyId(), applyOrder.getCostAttributionId());
            }
        }
        templateMap.put("paymentUse", StringUtils.isBlank(applyOrderV2Contract.getPayment_use()) ? "供应商付款" : applyOrderV2Contract.getPayment_use());
        // 拼装供应商信息
        CompanyPaymentSupplierVO companyPaymentSupplierVO = applyOrderV2Contract.getCompany_supplier();
        templateMap.put("supplierName", companyPaymentSupplierVO.getCompanySupplierName());
        templateMap.put("gysName", companyPaymentSupplierVO.getBankAccountName());
        templateMap.put("gysAcct", companyPaymentSupplierVO.getBankAccount());
        templateMap.put("gysBankName", companyPaymentSupplierVO.getBankName());
        // 4.5.0新增选填，此处不能查询最新配置，使用该笔申请单已有的数据进行下载，即ID存在，则拼装数据展示
        // 初始化是否显示合同，0-不显示，1-显示
        templateMap.put("hasHt", 0);
        // 拼装合同信息
        CompanyPaymentContractVO companyPaymentContractVO = applyOrderV2Contract.getContract();
        if (ObjectUtil.isNotNull(companyPaymentContractVO)) {
            if (companyPaymentContractVO.getId() != null) {
                templateMap.put("hasHt", 1);
                templateMap.put("htName", companyPaymentContractVO.getContractName());
                templateMap.put("htCode", companyPaymentContractVO.getContractCode());
                templateMap.put("htPrice", companyPaymentContractVO.getContractPrice());
            }
        }
        // 初始化是否显示凭证，0-不显示，1-显示
        templateMap.put("hasPz", 0);
        // 拼装凭证信息
        CompanyPaymentProofVO companyPaymentProofVO = applyOrderV2Contract.getProof();
        if (ObjectUtil.isNotNull(companyPaymentProofVO)) {
            if (companyPaymentProofVO.getId() != null) {
                templateMap.put("hasPz", 1);
                templateMap.put("pzName", companyPaymentProofVO.getProofName());
                templateMap.put("pzDesc", companyPaymentProofVO.getProofDsec());
                templateMap.put("pzPrice", companyPaymentProofVO.getProofPrice());
            }
        }
        // 处理审批流程
        List<Map<String, Object>> applyList = Lists.newArrayList();
        List<ApplyOrderLog> logs = applyOrderLogExtMapper.selectListByApplyId(applyOrder.getId());
        List<ApplyOrderLogContract> applyOrderLogContractList = getLogContractList(logs, applyOrder.getCompanyId());
        applyOrderLogContractList = applyOrderLogContractList.stream().filter(logInfo -> logInfo.getAction() == ApplyLogAction.Approve.getValue()
                || logInfo.getAction() == ApplyLogAction.Forward.getValue()).collect(Collectors.toList());
        for (ApplyOrderLogContract applyOrderLogContract : applyOrderLogContractList) {
            String sponsor = applyOrderLogContract.getSponsor();
            Integer action = applyOrderLogContract.getAction();
            String checkReason = applyOrderLogContract.getCheck_reason();
            Map<String, Object> itemInfo = Maps.newHashMap();
            itemInfo.put("name", sponsor);
            if (action == ApplyLogAction.Forward.getValue()) {
                itemInfo.put("action", "转交 " + applyOrderLogContract.getReceiver());
            } else {
                itemInfo.put("action", "已同意");
            }
            itemInfo.put("time", applyOrderLogContract.getTime());
            if (StringUtils.isNotBlank(checkReason)) {
                itemInfo.put("comment", "评论：" + checkReason);
            }
            applyList.add(itemInfo);
        }
        templateMap.put("applyList", applyList);
        Map<String, Object> paramMap = new HashMap<>(16);
        // 处理发票信息
        List<Map<String, Object>> invoiceList = applyOrderV2Contract.getInvoice_list();
        if (CollectionUtils.isNotEmpty(invoiceList)) {
            List<FbtQueryInvoiceDetailVO> invList = Lists.newArrayList();
            invoiceList.forEach(e -> {
                FbtQueryInvoiceDetailVO invoiceDetailVO = new FbtQueryInvoiceDetailVO();
                String invCode = (String)e.get("invCode");
                String invNo = (String)e.get("invNo");
                invoiceDetailVO.setInvCode(invCode);
                invoiceDetailVO.setInvNo(invNo);
                invList.add(invoiceDetailVO);
            });
            paramMap.put("invList", invList);
        } else {
            paramMap.put("invList", null);
        }
        paramMap.put("templateInfo", templateMap);

        return paramMap;
    }

    /**
     * 对公付款申请单处理pdf
     *
     * @param applyOrder
     * @param writeoffMap
     * @return
     */
    @Override
    public Boolean producePdf(ApplyOrder applyOrder, Map<String, Object> writeoffMap) {
        File file = null;
        try {
            String result = HttpClientUtils.get(URL_TEMPLATE_DETAIL + "?templateName=" + TemplateConstant.EMAIL_TEMP_PAYMENT);
            JSONObject resultObj = JSON.parseObject(result);
            if (ObjUtils.isEmpty(resultObj)) {
                logger.info("未配置模板：{}", TemplateConstant.EMAIL_TEMP_PAYMENT);
                return false;
            }
            logger.info("模板内容：{}", resultObj.toJSONString());
            JSONObject data = resultObj.getJSONObject("data");
            if (data == null || data.isEmpty()) {
                return false;
            }
            String template = ObjUtils.toString(data.get("template"));
            Map<String, Object> templateMap = (Map<String, Object>) writeoffMap.get("templateInfo");
            templateMap.put("imageUrl","data:image/png;base64,"+ Base64.encode(QrCodeUtil.generatePng(applyOrder.getId(), 300,300)));
            List<FbtQueryInvoiceDetailVO> invList = (List<FbtQueryInvoiceDetailVO>) writeoffMap.get("invList");
            String applyOrderHtml = FileUtils.getTempDirectoryPath() + "/对公付款申请单转换.pdf";
            String writeoffPdfPath = applyOrderHtml;
            logger.info("对公付款申请单数据：" + JsonUtils.toJson(templateMap));
            PdfUtils.htmlText2Pdf(template, applyOrderHtml, templateMap);
            logger.info("html转pdf成功");
            // 拼装对公发票地址
            if (CollectionUtils.isNotEmpty(invList)) {
                logger.info("查询发票参数，companyId: {}, invList: {}", applyOrder.getCompanyId(), invList);
                List<InvoicePicResRPCDTO> batchInvoicePicList = iFbtInvoicePublicService.queryPublicBatchInvoicePic(applyOrder.getCompanyId(), invList);
                logger.info("查询发票返回，companyId: {}, batchInvoicePicList: {}", applyOrder.getCompanyId(), batchInvoicePicList);
                if (CollectionUtils.isEmpty(batchInvoicePicList)) {
                    logger.info("未找到发票pic，invList: {}", invList);
                }
                List<String> pdfUrlList = Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(batchInvoicePicList)) {
                    for (InvoicePicResRPCDTO invoicePicResRPCDTO : batchInvoicePicList) {
                        if (StringUtils.isNotBlank(invoicePicResRPCDTO.getInvPdfUrl())) {
                            pdfUrlList.add(invoicePicResRPCDTO.getInvPdfUrl());
                        } else {
                            logger.info("该发票为纸质发票，fbInvId: {}", invoicePicResRPCDTO.getFbInvId());
                        }
                    }
                } else {
                    logger.info("对公付款申请单发票信息未找到,companyId: {},invList: {}", applyOrder.getCompanyId(), invList);
                }
                if (CollectionUtils.isNotEmpty(pdfUrlList)) {
                    logger.info("对公付款申请单发票链接地址：" + pdfUrlList);
                    writeoffPdfPath = FileUtils.getTempDirectoryPath() + "/对公付款申请单合并.pdf";
                    PdfUtils.mergePdfByPathAndUrls(applyOrderHtml, pdfUrlList, writeoffPdfPath);
                    logger.info("对公付款申请单数据与发票合并成功");
                }
            }
            // 文件上传oss
            // 保存到临时文件
            file = new File(writeoffPdfPath);
            file.renameTo(new File(file.getParent() + "/对公付款申请单.pdf"));
            File fileInfo = new File(file.getParent() + "/对公付款申请单.pdf");
            Map<String, Object> paramMap = new HashMap<>(16);
            paramMap.put("busi_code", "saas");
            paramMap.put("user_id", applyOrder.getEmployeeId());
            paramMap.put("file1", fileInfo);
            Map<String, String> fileNameMap = Maps.newHashMap();
            fileNameMap.put("file1", "对公付款申请单.pdf");
            JSONArray jsonArray = UploadOssTool.uploadOss(paramMap, fileNameMap);
            logger.info("上传OSS成功");
            // 完整链接
            String url = MapUtils.getString(jsonArray.getJSONObject(0), "url");
            ApplyOrder applyOrderInfo = new ApplyOrder();
            applyOrderInfo.setId(applyOrder.getId());
            applyOrderInfo.setDownloadLink(url);
            applyOrderMapper.updateByPrimaryKeySelective(applyOrderInfo);
            logger.info("更新数据库成功，pdf完整链接: {}", url);
        } catch (Exception e) {
            logger.info("生成pdf异常：" + e.getMessage());
            return false;
        }  finally {
            // 删除本地临时文件
            FileUtils.deleteQuietly(file);
        }
        return true;
    }

    /**
     * 查询合同和凭证配置信息
     *
     * @param companyId 公司ID
     * @return ApplyPaymentConfig 审批单配置
     */
    @Override
    public ApplyPaymentConfig getApplyPaymentConfig(String companyId) {
        List<String> itemCodeList = Lists.newArrayList();
        itemCodeList.add(SaasApplyConstant.ITEM_CODE_APPLY_PAYMENT_CONTRACT);
        itemCodeList.add(SaasApplyConstant.ITEM_CODE_APPLY_PAYMENT_PROOF);
        itemCodeList.add(SaasApplyConstant.ITEM_CODE_APPLY_PAYMENT_HAS_INVOICE);
        itemCodeList.add(SaasApplyConstant.ITEM_CODE_APPLY_PAYMENT_NEED_INVOICE);
        itemCodeList.add(SaasApplyConstant.ITEM_CODE_APPLY_PAYMENT_NO_INVOICE);
        // 查询设置 带默认值
        List<MessageSetup> messageSetups = messageSetupService.queryCompanyMessageSetupWithDefaultByItemCodeList(companyId, itemCodeList);
        ApplyPaymentConfig applyPaymentConfig = new ApplyPaymentConfig();
        if (ObjUtils.isEmpty(messageSetups)) {
            return applyPaymentConfig;
        }
        Map<String, MessageSetup> messageSetupMap = messageSetups.stream().collect(Collectors.toMap(MessageSetup::getItemCode, messageSetup -> messageSetup));
        applyPaymentConfig.setPayment_contract(messageSetupMap.get(SaasApplyConstant.ITEM_CODE_APPLY_PAYMENT_CONTRACT).getIsChecked());
        applyPaymentConfig.setPayment_contract_limit(messageSetupMap.get(SaasApplyConstant.ITEM_CODE_APPLY_PAYMENT_CONTRACT).getIntVal1());
        applyPaymentConfig.setPayment_proof(messageSetupMap.get(SaasApplyConstant.ITEM_CODE_APPLY_PAYMENT_PROOF).getIsChecked());
        applyPaymentConfig.setPayment_proof_limit(messageSetupMap.get(SaasApplyConstant.ITEM_CODE_APPLY_PAYMENT_PROOF).getIntVal1());
        applyPaymentConfig.setPayment_has_invoice(messageSetupMap.get(SaasApplyConstant.ITEM_CODE_APPLY_PAYMENT_HAS_INVOICE).getIsChecked());
        applyPaymentConfig.setPayment_has_invoice_limit(messageSetupMap.get(SaasApplyConstant.ITEM_CODE_APPLY_PAYMENT_HAS_INVOICE).getIntVal1());
        applyPaymentConfig.setPayment_need_invoice(messageSetupMap.get(SaasApplyConstant.ITEM_CODE_APPLY_PAYMENT_NEED_INVOICE).getIsChecked());
        applyPaymentConfig.setPayment_no_invoice(messageSetupMap.get(SaasApplyConstant.ITEM_CODE_APPLY_PAYMENT_NO_INVOICE).getIsChecked());
        return applyPaymentConfig;
    }
}

