package com.fenbeitong.saas.core.contract.organization.inner;

/**
 * Created by xuzn on 17/12/29.
 */
public class TrainPolicyBean {


    /**
     * 本人火车权限
     */
    private Boolean train_priv_flag;

    /**
     * 为其他员工预定权限
     */
    private Boolean train_other_flag;


    /**
     * 火车是否需要审批

     */
    private Boolean train_verify_flag;
    /**
     * 限制非企业员工预定火车标识
     */
    private Boolean unemployee_train;
    /**
     * 是否限制采购规则
     */
    private Boolean rule_limit_flag;
    /**
     * 差旅规则 id
     */
    private String rule_id;

    private Integer exceed_buy_type;

    private int train_rule;

    private String rule_name;

    private Boolean train_order_verify_flag;



    public Boolean getTrain_other_flag() {
        return train_other_flag;
    }

    public void setTrain_other_flag(Boolean train_other_flag) {
        this.train_other_flag = train_other_flag;
    }
    public Boolean getTrain_priv_flag() {
        return train_priv_flag;
    }

    public void setTrain_priv_flag(Boolean train_priv_flag) {
        this.train_priv_flag = train_priv_flag;
    }

    public Boolean getTrain_verify_flag() {
        return train_verify_flag;
    }

    public void setTrain_verify_flag(Boolean train_verify_flag) {
        this.train_verify_flag = train_verify_flag;
    }

    public Boolean getUnemployee_train() {
        return unemployee_train;
    }

    public void setUnemployee_train(Boolean unemployee_train) {
        this.unemployee_train = unemployee_train;
    }

    public Boolean getRule_limit_flag() {
        return rule_limit_flag;
    }

    public void setRule_limit_flag(Boolean rule_limit_flag) {
        this.rule_limit_flag = rule_limit_flag;
    }

    public String getRule_id() {
        return rule_id;
    }

    public void setRule_id(String rule_id) {
        this.rule_id = rule_id;
    }

    public Integer getExceed_buy_type() {
        return exceed_buy_type;
    }

    public void setExceed_buy_type(Integer exceed_buy_type) {
        this.exceed_buy_type = exceed_buy_type;
    }

    public int getTrain_rule() {
        return train_rule;
    }

    public void setTrain_rule(int train_rule) {
        this.train_rule = train_rule;
    }

    public String getRule_name() {
        return rule_name;
    }

    public void setRule_name(String rule_name) {
        this.rule_name = rule_name;
    }

    public Boolean getTrain_order_verify_flag() {
        return train_order_verify_flag;
    }

    public void setTrain_order_verify_flag(Boolean train_order_verify_flag) {
        this.train_order_verify_flag = train_order_verify_flag;
    }

    public TrainPolicyBean() {
    }

    public TrainPolicyBean(Boolean train_priv_flag, Boolean train_verify_flag, Boolean unemployee_train, Boolean rule_limit_flag, String rule_id, Integer exceed_buy_type, int train_rule, String rule_name, Boolean train_order_verify_flag) {
        this.train_priv_flag = train_priv_flag;
        this.train_verify_flag = train_verify_flag;
        this.unemployee_train = unemployee_train;
        this.rule_limit_flag = rule_limit_flag;
        this.rule_id = rule_id;
        this.exceed_buy_type = exceed_buy_type;
        this.train_rule = train_rule;
        this.rule_name = rule_name;
        this.train_order_verify_flag = train_order_verify_flag;
    }
}
