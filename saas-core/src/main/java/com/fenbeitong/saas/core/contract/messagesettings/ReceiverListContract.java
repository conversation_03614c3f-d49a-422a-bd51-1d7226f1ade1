package com.fenbeitong.saas.core.contract.messagesettings;

import com.fenbeitong.saas.core.contract.user.EmployeeInfoContract;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by zhangkai on 2017/6/3.
 */
public class ReceiverListContract {

    private String item_code;
    private List<EmployeeInfoContract> receiver_list = new ArrayList<>();

    public ReceiverListContract() {
    }

    public ReceiverListContract(String item_code, List<EmployeeInfoContract> receiver_list) {
        this.item_code = item_code;
        this.receiver_list = receiver_list;
    }

    public String getItem_code() {
        return item_code;
    }

    public void setItem_code(String item_code) {
        this.item_code = item_code;
    }

    public List<EmployeeInfoContract> getReceiver_list() {
        return receiver_list;
    }

    public void setReceiver_list(List<EmployeeInfoContract> receiver_list) {
        this.receiver_list = receiver_list;
    }

}
