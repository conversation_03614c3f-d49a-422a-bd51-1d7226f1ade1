package com.fenbeitong.saas.core.language;

/**
 * <AUTHOR>
 * @date 2022/12/13 17:14
 */
public enum MessageLanguageEnum {

    TITLE("title", "标题多语言"),
    CONTENT("content", "内容多语言"),
    ;


    private String code;

    private String name;

    MessageLanguageEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
