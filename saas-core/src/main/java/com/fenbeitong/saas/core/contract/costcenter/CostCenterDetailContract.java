package com.fenbeitong.saas.core.contract.costcenter;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.List;

/**
 * Created by xuzn on 18/3/22.
 */
public class CostCenterDetailContract {
    private String id;
    private String code;
    private String name;
    private String description;
    private String begin_date;
    private String end_date;
    private Integer expired_state_type;
    private ExpiredStateBean expired_state;
    @JsonIgnore
    private Integer state_type;
    private CostCenterListContract.StateTypeBean state;
    @JsonIgnore
    private Integer usable_range_type;
    private CostCenterListContract.UsableRangeBean usable_range;
    private List<CostCenterContract.ManagerBean> manager;
    private List<CostCenterContract.ManagerBean> member;
    private List<CostCenterContract.ManagerBean> member_dept;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getBegin_date() {
        return begin_date;
    }

    public void setBegin_date(String begin_date) {
        this.begin_date = begin_date;
    }

    public String getEnd_date() {
        return end_date;
    }

    public void setEnd_date(String end_date) {
        this.end_date = end_date;
    }

    public Integer getExpired_state_type() {
        return expired_state_type;
    }

    public void setExpired_state_type(Integer expired_state_type) {
        this.expired_state_type = expired_state_type;
    }

    public ExpiredStateBean getExpired_state() {
        return expired_state;
    }

    public void setExpired_state(ExpiredStateBean expired_state) {
        this.expired_state = expired_state;
    }

    public Integer getState_type() {
        return state_type;
    }

    public void setState_type(Integer state_type) {
        this.state_type = state_type;
    }

    public CostCenterListContract.StateTypeBean getState() {
        return state;
    }

    public void setState(CostCenterListContract.StateTypeBean state) {
        this.state = state;
    }

    public Integer getUsable_range_type() {
        return usable_range_type;
    }

    public void setUsable_range_type(Integer usable_range_type) {
        this.usable_range_type = usable_range_type;
    }

    public CostCenterListContract.UsableRangeBean getUsable_range() {
        return usable_range;
    }

    public void setUsable_range(CostCenterListContract.UsableRangeBean usable_range) {
        this.usable_range = usable_range;
    }

    public List<CostCenterContract.ManagerBean> getManager() {
        return manager;
    }

    public void setManager(List<CostCenterContract.ManagerBean> manager) {
        this.manager = manager;
    }

    public List<CostCenterContract.ManagerBean> getMember() {
        return member;
    }

    public void setMember(List<CostCenterContract.ManagerBean> member) {
        this.member = member;
    }

    public List<CostCenterContract.ManagerBean> getMember_dept() {
        return member_dept;
    }

    public void setMember_dept(List<CostCenterContract.ManagerBean> member_dept) {
        this.member_dept = member_dept;
    }

    public static class ExpiredStateBean {
        private Integer key;
        private String value;

        public Integer getKey() {
            return key;
        }

        public void setKey(Integer key) {
            this.key = key;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }
}
