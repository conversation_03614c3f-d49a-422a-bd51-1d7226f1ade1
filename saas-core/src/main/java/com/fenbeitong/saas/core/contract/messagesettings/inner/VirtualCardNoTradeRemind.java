package com.fenbeitong.saas.core.contract.messagesettings.inner;

import com.fenbeitong.saas.core.contract.user.EmployeeInfoContract;
import com.fenbeitong.saas.core.model.saas.MessageSetup;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 备用金设置
 * <AUTHOR>
 * @create 2022/8/03 4:57 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VirtualCardNoTradeRemind {
    /**
     * 是否启用提醒
     */
    private Integer is_check;
    /**
     * 是否发送APP通知
     */
    private Integer app_notice;
    /**
     * 是否发送邮件通知
     */
    private Integer mail_notice;
    /**
     * 是否发送电话短信通知
     */
    private Integer phone_notice;
    /**
     * 接收人邮箱
     */
    private List<String> email_list = new ArrayList<>();
    /**
     * 接收人ID
     */
    private List<EmployeeInfoContract> receiver_list = new ArrayList<>();
    /**
     * 提醒间隔日期
     */
    private String day;
    /**
     * 每次提醒具体时间
     */
    private String time;

    public static String getRemindType() {
        return "virtual_card_no_trade_remind";
    }

    public void build(MessageSetup setup) {
        this.setIs_check(setup.getIsChecked());
        this.setApp_notice(setup.getIntVal1());
        this.setMail_notice(setup.getIntVal2());
        this.setPhone_notice(setup.getIntVal3());
        this.setDay(setup.getStrVal1());
        this.setTime(setup.getStrVal3());
    }
}
