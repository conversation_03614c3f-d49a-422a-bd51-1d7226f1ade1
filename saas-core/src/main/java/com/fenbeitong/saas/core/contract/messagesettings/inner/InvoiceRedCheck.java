package com.fenbeitong.saas.core.contract.messagesettings.inner;

import com.fenbeitong.saas.api.model.dto.user.EmployeeEmailContract;
import com.fenbeitong.saas.core.contract.user.EmployeeInfoContract;

import java.util.ArrayList;
import java.util.List;

public class InvoiceRedCheck {
    private List<EmployeeInfoContract> receiver_list = new ArrayList<>();
    private List<String> email_list = new ArrayList<>();

    private Boolean hasUnbind = false;
    /**
     *  新版邮箱绑定人员列表
     **/
    private List<EmployeeEmailContract>  emails = new ArrayList<>();

    public List<EmployeeInfoContract> getReceiver_list() {
        return receiver_list;
    }

    public void setReceiver_list(List<EmployeeInfoContract> receiver_list) {
        this.receiver_list = receiver_list;
    }

    public List<String> getEmail_list() {
        return email_list;
    }

    public void setEmail_list(List<String> email_list) {
        this.email_list = email_list;
    }

    public InvoiceRedCheck(List<EmployeeInfoContract> receiver_list, List<String> email_list){
        this.receiver_list = receiver_list;
        this.email_list = email_list;
    }

    public Boolean getHasUnbind() {
        return hasUnbind;
    }

    public void setHasUnbind(Boolean hasUnbind) {
        this.hasUnbind = hasUnbind;
    }

    public List<EmployeeEmailContract> getEmails() {
        return emails;
    }

    public void setEmails(List<EmployeeEmailContract> emails) {
        this.emails = emails;
    }

    public InvoiceRedCheck(){
    }

}
