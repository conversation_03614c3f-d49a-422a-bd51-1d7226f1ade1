package com.fenbeitong.saas.core.contract.selfauthorize;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Title:
 * Desc:
 * PACKAGENAME:com.fenbeitong.saas.core.contract.selfauthorize
 *
 * @author: linyongchao
 * Date: 2022/9/22
 */
@Data
@NoArgsConstructor
public class QueryAuthQRcodeReq {
    /**
     * 授权场景
     */
    @NotNull(message = "授权场景不能为空")
    private List<Integer> apply_auth_scene_list;

    /**
     * 授权开始时间yyyy/MM/dd
     */
    @NotBlank(message = "授权开始时间不能为空")
    private String auth_start_time;

    /**
     * 授权有效期：单位为天
     */
    private Integer validity_period;

    /**
     * 是否为永久授权：1为永久，0为非永久
     */
    private Integer is_forever;
}
