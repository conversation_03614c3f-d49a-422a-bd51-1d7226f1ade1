package com.fenbeitong.saas.core.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.finhub.common.utils.EventParams;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.saas.api.model.dto.reason.ReasonConfig;
import com.fenbeitong.saas.core.common.DynamicDataSourceDecision;
import com.fenbeitong.saas.core.common.base.SaasException;
import com.fenbeitong.saas.core.common.constant.*;
import com.fenbeitong.saas.core.contract.cost.CostCheckVO;
import com.fenbeitong.saas.core.contract.messagesettings.CostAttrAndBudgetConf;
import com.fenbeitong.saas.core.contract.order.check.ErrMsgInfo;
import com.fenbeitong.saas.core.contract.order.check.MallOrderCheckReqV2Contract;
import com.fenbeitong.saas.core.contract.order.check.ResponseCodeContract;
import com.fenbeitong.saas.core.contract.order.check.TempOrderCheckResContract;
import com.fenbeitong.saas.core.contract.order.check.TempOrderRuleCheckResult;
import com.fenbeitong.saas.core.contract.order.check.TravelOnBusiOrderRuleCheckResult;
import com.fenbeitong.saas.core.dao.fenbeitong.MallInterceptRecordMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.MallRuleMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.MallRuleSkuMapper;
import com.fenbeitong.saas.core.model.enums.EmployeeStatusEnum;
import com.fenbeitong.saas.core.model.enums.OrderCategory;
import com.fenbeitong.saas.core.model.enums.reason.ReasonType;
import com.fenbeitong.saas.core.model.enums.rule.MallRuleType;
import com.fenbeitong.saas.core.model.fenbeitong.MallInterceptRecord;
import com.fenbeitong.saas.core.model.fenbeitong.MallRule;
import com.fenbeitong.saas.core.model.fenbeitong.MallRuleSku;
import com.fenbeitong.saas.core.model.fenbeitong.MallRuleSkuExample;
import com.fenbeitong.saas.core.model.saas.ApplyOrder;
import com.fenbeitong.saas.core.service.IBaseOrganizationService;
import com.fenbeitong.saas.core.service.ICustomFieldsService;
import com.fenbeitong.saas.core.service.ICustomReasonService;
import com.fenbeitong.saas.core.service.IMallCheckV2Service;
import com.fenbeitong.saas.core.service.IMessageSetupService;
import com.fenbeitong.saas.core.service.IOrderCheckService;
import com.fenbeitong.saas.core.utils.icon.IconChangeUtil;
import com.fenbeitong.saas.core.utils.tools.ConvertUtils;
import com.fenbeitong.saas.core.utils.tools.IDTool;
import com.fenbeitong.saas.core.utils.tools.StringTool;
import com.fenbeitong.saas.core.utils.tools.VersionTool;
import com.fenbeitong.saasplus.api.model.dto.finance.CustomDimension;
import com.fenbeitong.saasplus.api.model.dto.reason.ApplyMallConfigDTO;
import com.fenbeitong.saasplus.api.service.custform.ICustomFormService;
import com.fenbeitong.saasplus.api.service.reason.ICustomReasonAttrService;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyRuleDTO;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.fenbeitong.usercenter.api.model.enums.privilege.CodeTypeEnums;
import com.fenbeitong.usercenter.api.model.enums.privilege.CustomCodeType;
import com.fenbeitong.usercenter.api.model.po.rule.EmployeeMallRule;
import com.fenbeitong.usercenter.api.service.company.ICompanyRuleService;
import com.fenbeitong.usercenter.api.service.privilege.IPrivilegeService;
import com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeMallRuleService;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.StrUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;


/**
 * Created by mac on 17/12/21.
 */
@Slf4j
@Service
public class MallCheckV2ServiceImpl implements IMallCheckV2Service {

    private static final Logger logger = LoggerFactory.getLogger(MallCheckV2ServiceImpl.class);
    @Autowired
    private IBaseOrganizationService baseOrganizationService;
    @Autowired
    private ICompanyRuleService iCompanyRuleService;
    @Autowired
    private IOrderCheckService iOrderCheckService;
    @Autowired
    private MallRuleMapper mallRuleMapper;
    @Autowired
    private MallRuleSkuMapper mallRuleSkuMapper;
    @Autowired
    private MallInterceptRecordMapper mallInterceptRecordMapper;
    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyOrderMapper applyOrderMapper;
    @Autowired
    private IBaseEmployeeMallRuleService iBaseEmployeeMallRuleService;
    @Autowired
    private IMessageSetupService iMessageSetupService;
    @Autowired
    private ICustomReasonService customReasonService;
    @Autowired
    private IPrivilegeService iPrivilegeService;
    @Autowired
    ICustomReasonAttrService customReasonAttrService;

    @Resource
    private ICustomFieldsService iCustomFieldsService;
    @Autowired
    private CurrencyCheckServiceImpl currencyCheckService;
    @Autowired
    private com.fenbeitong.saasplus.api.service.messageSetup.IMessageSetupService iMessageSetupRpcService;

    /**
     * 校验采购订单规则
     *
     * @param mallOrderCheckReqContract
     * @return
     * @throws SaasException
     */
    @Override
    public TempOrderCheckResContract mallOrderCheck(MallOrderCheckReqV2Contract mallOrderCheckReqContract, String clientVersion) throws SaasException {
        String companyId = mallOrderCheckReqContract.getTravel_on_busi_common_req_contract().getCompany_id();
        DynamicDataSourceDecision.determineDataSource(companyId);
        // 返回结果
        TempOrderCheckResContract resContract = new TempOrderCheckResContract();

        // 检查费用归属灰度开关及客户端版本号
        Integer costAttributionNewSwitch = iMessageSetupRpcService.queryCostAttributionNewSwitch(companyId);
        if (CommonSwitchConstant.OPEN.equals(costAttributionNewSwitch)
                && VersionTool.lessThan(clientVersion, ClientVersionConstant.COST_ATTRIBUTION_UPDATE_CLIENT_VERSION)) {
            resContract.setErr_code(GlobalResponseCode.OrderCheckUpdateTost.getCode());
            resContract.setErr_msg(GlobalResponseCode.OrderCheckUpdateTost.getMsg());
            resContract.setErr_type(1);
            return resContract;
        }

        // 校验预算参数，如企业配置开启自定义档案预算，且相关参数未传，提示强制版本升级
        boolean needUpdate = iCustomFieldsService.checkBudget(companyId, ReasonType.ORDER_MALL, clientVersion);
        if (needUpdate) {
            resContract.setErr_code(GlobalResponseCode.ApplyCenterAlert.getCode());
            resContract.setErr_msg(GlobalResponseCode.ApplyCenterAlert.getMsg());
            resContract.setErr_type(1);
            return resContract;
        }

        // 校验权限问题
        TempOrderRuleCheckResult ruleCheckResult = mallOrderRuleCheckResult(mallOrderCheckReqContract, clientVersion);
        resContract.setErr_code(ruleCheckResult.getErrCode());
        resContract.setErr_msg(ruleCheckResult.getErrMsg());
        resContract.setExceed_msg(ruleCheckResult.getExceed_msg());
        resContract.setCost_attribution_scope(ruleCheckResult.getCost_attribution_scope());
        resContract.setBudget_cost_attr_type(ruleCheckResult.getBudget_cost_attr_type());
        resContract.setPay_model(ruleCheckResult.getPayModel());
        resContract.setCompany_pay_price(ruleCheckResult.getCompanyPayPrice());
        resContract.setPersonal_pay_price(ruleCheckResult.getPersonalPayPrice());
        resContract.setReimbursable_price(ruleCheckResult.getReimbursablePrice());
        resContract.setUnreimbursable_price(ruleCheckResult.getUnreimbursablePrice());
        resContract.setAmount_compliance(ruleCheckResult.getAmountCompliance());
        resContract.setAmount_non_compliance(ruleCheckResult.getAmountNonCompliance());
        if (ruleCheckResult.getIs_exceed() || mallOrderCheckReqContract.getTravel_on_busi_parameter_req_contract().getExceed_submit()) {
            resContract.setIs_exceed(true);
        }
        if (ruleCheckResult.getErrCode() == GlobalResponseCode.Success.getCode()) {
            BigDecimal costAmount = mallOrderCheckReqContract.getTravel_on_busi_common_req_contract().getOrder_price()
                    .add(ObjUtils.toBigDecimal(mallOrderCheckReqContract.getTravel_on_busi_common_req_contract().getInsurance_price(), BigDecimal.ZERO));
            // 自定义字段转化
            List<CustomDimension> customDimensionList =
                    ConvertUtils.convertToCustomDimension(mallOrderCheckReqContract.getCustomer_field_setting_list());
            TempOrderCheckResContract costResult = iOrderCheckService.saveCost(mallOrderCheckReqContract.getOrder_id(),
                    mallOrderCheckReqContract.getTravel_on_busi_common_req_contract().getCompany_id(),
                    mallOrderCheckReqContract.getTravel_on_busi_common_req_contract().getEmployee_id(),
                    BudgetCategoryTypeEnum.Mall,
                    costAmount,
                    mallOrderCheckReqContract.getCost_info(),
                    mallOrderCheckReqContract.getCostInfoString(),
                    clientVersion, customDimensionList,"");
            if (costResult.getErr_code() != GlobalResponseCode.Success.getCode()) {
                //特殊处理
                if (costResult.getErr_code() == GlobalResponseCode.OrderCheckOverBudgetCanSubmit.getCode()) {
                    resContract.setErr_code(TemporaryResponseCode.OrderCheckOverBudgetCanSubmit.getCode());
                } else if (costResult.getErr_code() == GlobalResponseCode.OrderCheckOverBudgetCanNotSubmit.getCode()) {
                    resContract.setErr_code(TemporaryResponseCode.OrderCheckOverBudgetCanNotSubmit.getCode());
                } else {
                    resContract.setErr_code(costResult.getErr_code());
                }
                resContract.setErr_msg(costResult.getErr_msg());
            } else {
                resContract.setCost_id(costResult.getCost_id());
            }
        }
        //添加拦截记录
        if (ruleCheckResult.getInterceptFlag() && GlobalResponseCode.Success.getCode() != ruleCheckResult.getErrCode()) {
            initMallInterceptRecord(mallOrderCheckReqContract, ruleCheckResult);
        }
        // 错误信息类型
        TempOrderCheckResContract orderCheckResContract = mallCheckResContractCommon(resContract, ruleCheckResult);
        MallRule mallRule = ruleCheckResult.getMallRule();
        if (Objects.nonNull(mallRule)) {
            if (mallRule.getLimitPriceFlag() && Objects.nonNull(mallRule.getLimitPriceHighest())) {
                orderCheckResContract.setLimit_amount(ruleCheckResult.getMallRule().getLimitPriceHighest());
            }
        }
        // 记录下单校验日志
        iOrderCheckService.saveOrderCheckLog(
                mallOrderCheckReqContract.getTravel_on_busi_common_req_contract().getCompany_id(),
                mallOrderCheckReqContract.getTravel_on_busi_common_req_contract().getEmployee_id(),
                CategoryTypeEnum.Mall.getCode(),
                clientVersion,
                JsonUtils.toJson(mallOrderCheckReqContract),
                JsonUtils.toJson(orderCheckResContract),
                ObjUtils.toString(orderCheckResContract.getErr_code()),
                ruleCheckResult.getSnapshotInfo().toJSONString(),
                ruleCheckResult.getExtInfo().toJSONString());
        return orderCheckResContract;
    }

    //采购返回结果处理
    private TempOrderCheckResContract mallCheckResContractCommon(TempOrderCheckResContract mallOrderCheckResContract, TempOrderRuleCheckResult ruleCheckResult) {
        if (mallOrderCheckResContract.getErr_code() == GlobalResponseCode.Success.getCode()) {
            // 成功
            mallOrderCheckResContract.setErr_type(0);
        } else if (mallOrderCheckResContract.getErr_code() == TemporaryResponseCode.OrderCheckExceedNeedReason.getCode()
                || mallOrderCheckResContract.getErr_code() == TemporaryResponseCode.OrderCheckOverBudgetCanSubmit.getCode()
                || mallOrderCheckResContract.getErr_code() == TemporaryResponseCode.OrderCheckOverBudgetCanNotSubmit.getCode()
                || mallOrderCheckResContract.getErr_code() == TemporaryResponseCode.OrderCanNotSubmit.getCode()
                ) {
            // 提示(弹窗)
            mallOrderCheckResContract.setErr_type(2);
            ErrMsgInfo errMsgInfo1 = ruleCheckResult.getErrMsgInfo();
            if (mallOrderCheckResContract.getErr_code() == TemporaryResponseCode.OrderCheckExceedNeedReason.getCode()) {
                if (errMsgInfo1 != null) {
                    ErrMsgInfo errMsgInfo = new ErrMsgInfo();
                    errMsgInfo.setTitle(SaasOrderConstant.ORDER_OVERRULE_REASON);
                    errMsgInfo.setErr_code_list(errMsgInfo1.getErr_code_list());
                    mallOrderCheckResContract.setErr_msg_info(errMsgInfo);
                }
            } else if (mallOrderCheckResContract.getErr_code() == TemporaryResponseCode.OrderCanNotSubmit.getCode()) {
                if (errMsgInfo1 != null) {
                    ErrMsgInfo errMsgInfo = new ErrMsgInfo();
                    errMsgInfo.setTitle(SaasOrderConstant.ORDER_PROHIBIT);
                    errMsgInfo.setErr_code_list(errMsgInfo1.getErr_code_list());
                    mallOrderCheckResContract.setErr_msg_info(errMsgInfo);
                }
            } else {
                setErrMsgInfo(mallOrderCheckResContract);
            }
        } else {
            // 异常(toast)
            mallOrderCheckResContract.setErr_type(1);
            setErrMsgInfo(mallOrderCheckResContract);
        }
        return mallOrderCheckResContract;
    }

    private void setErrMsgInfo(TempOrderCheckResContract mallOrderCheckResContract) {
        ErrMsgInfo errMsgInfo = new ErrMsgInfo();
        errMsgInfo.setTitle(mallOrderCheckResContract.getErr_msg());
        errMsgInfo.setErr_code_list(new ArrayList<>());
        mallOrderCheckResContract.setErr_msg_info(errMsgInfo);
    }

    /**
     * 校验采购规则
     *
     * @param reqContract
     * @return
     */
    private TempOrderRuleCheckResult mallOrderRuleCheckResult(MallOrderCheckReqV2Contract reqContract, String clientVersion) throws SaasException {
        BigDecimal totalPrice = BigDecimal.ZERO;
        //订单金额
        BigDecimal orderPrice = reqContract.getTravel_on_busi_common_req_contract().getOrder_price();
        //优惠券金额
        BigDecimal couponAmount = ObjUtils.toBigDecimal(reqContract.getTravel_on_busi_common_req_contract().getCoupon_amount(), BigDecimal.ZERO);
        //查询优惠券配置 0.未开启 1.开启
        Integer couponExceedPriceSetting = iOrderCheckService.queryCouponExceedPriceSetting(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        if (couponExceedPriceSetting == 1) {
            totalPrice = orderPrice.subtract(couponAmount).max(BigDecimal.ZERO);
        } else {
            totalPrice = reqContract.getTravel_on_busi_common_req_contract().getTotal_price();
        }
        // 默认初始化正常
        TempOrderRuleCheckResult checkResult = new TempOrderRuleCheckResult();
        checkResult.setResCode(TemporaryResponseCode.Success);

        // 快照信息
        JSONObject snapshotInfo = new JSONObject();
        checkResult.setSnapshotInfo(snapshotInfo);
        // 查询是否个人垫付模式
        Map<String, Boolean> funcMap = iPrivilegeService.queryFunctionMoudle(CodeTypeEnums.CustomAuth.getKey(), reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(), CustomCodeType.M_ADVANCE_PAYMENT.getKey());
        Boolean advancePayment = funcMap.get(CustomCodeType.M_ADVANCE_PAYMENT.getKey());
        logger.info("[采购下单校验]，是否个人垫付模式:{}", advancePayment);
        PayModelEnum payModel = advancePayment ? PayModelEnum.PERSONAL_PREPAY : PayModelEnum.COMPANY_PAY;
        checkResult.setPayModel(payModel.getCode());
        snapshotInfo.put("payModel", payModel.getCode());
        //BigDecimal totalPrice = reqContract.getTravel_on_busi_common_req_contract().getTotal_price();
        if (advancePayment) {
            checkResult.setCompanyPayPrice(BigDecimal.ZERO);
            checkResult.setPersonalPayPrice(totalPrice);
            checkResult.setReimbursablePrice(totalPrice);
            checkResult.setUnreimbursablePrice(BigDecimal.ZERO);
        } else {
            checkResult.setCompanyPayPrice(totalPrice);
            checkResult.setPersonalPayPrice(BigDecimal.ZERO);
            checkResult.setReimbursablePrice(BigDecimal.ZERO);
            checkResult.setUnreimbursablePrice(BigDecimal.ZERO);
        }
        checkResult.setAmountCompliance(totalPrice);
        checkResult.setAmountNonCompliance(BigDecimal.ZERO);
        // 预订人状态
        EmployeeContract orderEmployee = baseOrganizationService.getEmployee(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(), reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        if (orderEmployee == null || orderEmployee.getStatus() != EmployeeStatusEnum.ACTIVE.getValue()) {
            checkResult.setResCode(TemporaryResponseCode.OrderCheckEmployeeNotActive);
            return checkResult;
        }
        // 企业采购权限
        CompanyRuleDTO companyRule = iCompanyRuleService.queryByCompanyId(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        if (companyRule == null || companyRule.getMallRule() != 1) {
            checkResult.setResCode(TemporaryResponseCode.OrderMallCheckCompanyNoAuth);
            return checkResult;
        }
        // 员工采购权限
        EmployeeMallRule employeeMallRule = iBaseEmployeeMallRuleService.queryEmployeeMallRuleByPrimaryKey(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id(), reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        checkResult.setEmployeeMallRule(employeeMallRule);
        snapshotInfo.put("authInfo", employeeMallRule);
        if (employeeMallRule == null
                || employeeMallRule.getMallRule() != MallRuleType.Allowed.getCode()) {
            checkResult.setResCode(TemporaryResponseCode.OrderMallCheckEmployeeNoAuth);
            return checkResult;
        }
        //企业余额校验(余额不足或者账户被锁定)
        TravelOnBusiOrderRuleCheckResult checkCompanyAccountResult = iOrderCheckService.checkCompanyAccount(reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), totalPrice, OrderCategory.Mall.getKey(), advancePayment);
        if (GlobalResponseCode.Success.getCode() != checkCompanyAccountResult.getErrCode()) {
            FinhubLogger.event(reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), EventParams.build(**********, false).put("account_sub_type", 4));
            checkResult.setResCode(checkCompanyAccountResult.getErrCode(), checkCompanyAccountResult.getErrMsg());
            return checkResult;
        }
        CostAttrAndBudgetConf costAttrAndBudgetConf = iMessageSetupService.queryCostAttrAndBudgetConf(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        // 老版本配置"部门和项目"兼容为"部门或项目"
        if (costAttrAndBudgetConf.getCost_attribution_scope() == CostAttributionScopeEnum.DEPT_AND_PROJ.getCode()
                && ObjUtils.isNotBlank(clientVersion) && VersionTool.lessThan(clientVersion, "4.2.0")) {
            costAttrAndBudgetConf.setCost_attribution_scope(CostAttributionScopeEnum.DEPT_OR_PROJ.getCode());
        }
        checkResult.setCost_attribution_scope(costAttrAndBudgetConf.getCost_attribution_scope());
        checkResult.setBudget_cost_attr_type(costAttrAndBudgetConf.getBudget_cost_attr_type());


        // 检查费用归属灰度开关 进行灰度校验
        Integer costAttributionNewSwitch = iMessageSetupRpcService.queryCostAttributionNewSwitch(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        log.info("costAttributionNewSwitch:{}",costAttributionNewSwitch);
        if (CommonSwitchConstant.OPEN.equals(costAttributionNewSwitch)) {
            CostCheckVO costCheckVO = new CostCheckVO();
            costCheckVO.setCostInfoType(reqContract.getCost_info_type());
            costCheckVO.setCostInfoTicketList(reqContract.getCost_info_ticket_list());
            costCheckVO.setCostInfo(reqContract.getCost_info());
            costCheckVO.setCostInfoString(reqContract.getCostInfoString());
            costCheckVO.setUserIdList(com.google.common.collect.Lists.newArrayList(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id()));
            costCheckVO.setCategory(BizType.Mall.getCode());
            costCheckVO.setCompanyId(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
            currencyCheckService.newCheckCostAttribution(costCheckVO);
        } else {
            TravelOnBusiOrderRuleCheckResult checkCompanyCostAttributionResult = iOrderCheckService.checkCostInfo(reqContract.getTravel_on_busi_common_req_contract().getCompany_id(), reqContract.getCost_info(), costAttrAndBudgetConf, clientVersion);
            if (GlobalResponseCode.Success.getCode() != checkCompanyCostAttributionResult.getErrCode()) {
                checkResult.setResCode(checkCompanyCostAttributionResult.getErrCode(), checkCompanyCostAttributionResult.getErrMsg());
                return checkResult;
            }
        }

        //预算校验
        TravelOnBusiOrderRuleCheckResult travelOrderBudgetCheckResult = iOrderCheckService.categoryOrderBudgetCheckV2(reqContract, costAttrAndBudgetConf, OrderCategory.Mall, clientVersion);
        snapshotInfo.put("budgetList", travelOrderBudgetCheckResult.getBudgetSettingAndUseList());
        if (GlobalResponseCode.Success.getCode() != travelOrderBudgetCheckResult.getErrCode()) {
            //特殊处理
            if (travelOrderBudgetCheckResult.getErrCode() == GlobalResponseCode.OrderCheckOverBudgetCanSubmit.getCode()) {
                checkResult.setResCode(TemporaryResponseCode.OrderCheckOverBudgetCanSubmit.getCode(), travelOrderBudgetCheckResult.getErrMsg());
            } else if (travelOrderBudgetCheckResult.getErrCode() == GlobalResponseCode.OrderCheckOverBudgetCanNotSubmit.getCode()) {
                checkResult.setResCode(TemporaryResponseCode.OrderCheckOverBudgetCanNotSubmit.getCode(), travelOrderBudgetCheckResult.getErrMsg());
            } else {
                checkResult.setResCode(travelOrderBudgetCheckResult.getErrCode(), travelOrderBudgetCheckResult.getErrMsg());
            }
            return checkResult;
        }
        BigDecimal budgetAmountCompliance = travelOrderBudgetCheckResult.getAmountCompliance();
        BigDecimal budgetAmountNonCompliance = travelOrderBudgetCheckResult.getAmountNonCompliance();
        checkResult.setAmountCompliance(budgetAmountCompliance);
        checkResult.setAmountNonCompliance(budgetAmountNonCompliance);
        logger.info("[采购下单校验]，预算合规金额:{}, 预算超规金额:{}", budgetAmountCompliance, budgetAmountNonCompliance);
        //规则校验，带申请单号的不走规则校验逻辑
        if (employeeMallRule.getMallRuleFlag() && StringUtils.isBlank(reqContract.getTravel_on_busi_common_req_contract().getOrder_apply_id())) {
            String ruleId = ObjUtils.ifNull(employeeMallRule.getManualMallRuleId(), employeeMallRule.getDefaultMallRuleId());
            MallRule mallRule = mallRuleMapper.selectByPrimaryKey(ruleId);
            checkResult.setMallRule(mallRule);
            snapshotInfo.put("ruleInfo", mallRule);
            if (mallRule == null) {
                checkResult.setResCode(TemporaryResponseCode.OrderMallCheckRuleNotExist);
                return checkResult;
            }
            //超标规则（false：禁止 true：超规填写理由下单）
            Boolean exceedByFlag = employeeMallRule.getExceedBuyFlag();
            //采购规则
            List<TempOrderRuleCheckResult> tempOrderRuleCheckResults = checkMallExceedType(mallRule, reqContract, totalPrice);
            Boolean isPriceExceed = false;
            BigDecimal ruleAmountCompliance = totalPrice;
            if (CollectionUtils.isNotEmpty(tempOrderRuleCheckResults) && tempOrderRuleCheckResults.size() == 1) {
                if (tempOrderRuleCheckResults.get(0).getIsPriceExceed() != null && tempOrderRuleCheckResults.get(0).getIsPriceExceed()) {
                    isPriceExceed = true;
                    ruleAmountCompliance = tempOrderRuleCheckResults.get(0).getAmountCompliance();
                    BigDecimal ruleAmountNonCompliance = tempOrderRuleCheckResults.get(0).getAmountNonCompliance();
                    logger.info("[采购下单校验]，规则合规金额:{}, 规则超规金额:{}", ruleAmountCompliance, ruleAmountNonCompliance);
                }
            }
            // 超规/合规金额处理
            if (CollectionUtils.isNotEmpty(tempOrderRuleCheckResults)) {
                if (isPriceExceed) {
                    checkResult.setAmountCompliance(checkResult.getAmountCompliance().min(ruleAmountCompliance));
                    checkResult.setAmountNonCompliance(totalPrice.subtract(checkResult.getAmountCompliance()));
                } else {
                    checkResult.setAmountCompliance(BigDecimal.ZERO);
                    checkResult.setAmountNonCompliance(totalPrice);
                }
                logger.info("[采购下单校验]，合规金额:{}, 超规金额:{}", checkResult.getAmountCompliance(), checkResult.getAmountNonCompliance());
            }
            //强制提交逻辑
            TempOrderRuleCheckResult checkExceedAuthResult = checkExceedAuth(reqContract);
            //超标禁止下单
            if (!exceedByFlag && CollectionUtils.isNotEmpty(tempOrderRuleCheckResults)) {
                //版本兼容（1.9.4之前）
                String version = "1.9.4";
                if (VersionTool.compare(clientVersion, version) < 0) {
                    return tempOrderRuleCheckResults.get(0);
                } else {
                    List<ResponseCodeContract> tempOrderListResultContracts = new ArrayList<>();
                    for (TempOrderRuleCheckResult tempOrderRuleCheckResult : tempOrderRuleCheckResults) {
                        ResponseCodeContract tempOrderListResultContract = new ResponseCodeContract();
                        tempOrderListResultContract.setCode(tempOrderRuleCheckResult.getErrCode());
                        tempOrderListResultContract.setContent(tempOrderRuleCheckResult.getErrMsg());
                        tempOrderListResultContract.setType(IconChangeUtil.getTypeByCode(tempOrderRuleCheckResult.getErrCode()));
                        tempOrderListResultContract.setBusinessList(tempOrderRuleCheckResult.getBusinessList());
                        tempOrderListResultContracts.add(tempOrderListResultContract);
                    }
                    Map<String, Object> codeListMap = new HashMap<>();
                    codeListMap.put("codeList", tempOrderListResultContracts);
                    checkResult.setResCode(TemporaryResponseCode.OrderCanNotSubmit.getCode(), JsonUtils.toJson(codeListMap));
                    ErrMsgInfo errMsgInfo = new ErrMsgInfo();
                    logger.info("无法继续下单，超规规则如下：{}", JsonUtils.toJson(tempOrderListResultContracts));
                    errMsgInfo.setTitle(checkResult.getErrMsg());
                    errMsgInfo.setErr_code_list(tempOrderListResultContracts);
                    checkResult.setErrMsgInfo(errMsgInfo);
                    return checkResult;
                }
            }
            //超标需要理由
            else if (exceedByFlag && CollectionUtils.isNotEmpty(tempOrderRuleCheckResults) && checkExceedAuthResult.getErrCode() != TemporaryResponseCode.Success.getCode()) {
                //超规则返回
                checkExceedAuthResult.setIs_exceed(true);
                List<ResponseCodeContract> tempOrderListResultContracts = new ArrayList<>();
                for (TempOrderRuleCheckResult tempOrderRuleCheckResult : tempOrderRuleCheckResults) {
                    ResponseCodeContract tempOrderListResultContract = new ResponseCodeContract();
                    tempOrderListResultContract.setCode(tempOrderRuleCheckResult.getErrCode());
                    tempOrderListResultContract.setContent(tempOrderRuleCheckResult.getErrMsg());
                    tempOrderListResultContract.setType(IconChangeUtil.getTypeByCode(tempOrderRuleCheckResult.getErrCode()));
                    tempOrderListResultContract.setBusinessList(tempOrderRuleCheckResult.getBusinessList());
                    tempOrderListResultContracts.add(tempOrderListResultContract);
                }
                ErrMsgInfo errMsgInfo = new ErrMsgInfo();
                logger.info("超规填写理由，超规规则如下：{}", JsonUtils.toJson(tempOrderListResultContracts));
                errMsgInfo.setTitle(checkExceedAuthResult.getErrMsg());
                errMsgInfo.setErr_code_list(tempOrderListResultContracts);
                checkExceedAuthResult.setErrMsgInfo(errMsgInfo);
                checkExceedAuthResult.setMallRule(mallRule);
                return checkExceedAuthResult;
            }
        }
        //是否需要采购审批单
        if (employeeMallRule.getMallVerifyFlag()) {
            String version = "1.9.6";
            if (VersionTool.compare(clientVersion, version) < 0) {
                checkResult.setErrCode(GlobalResponseCode.ApplyCenterAlert.getCode());
                checkResult.setErrMsg(GlobalResponseCode.ApplyCenterAlert.getMsg());
                return checkResult;
            }
            //0：审批通过后人工下单，1：审批通过后自动下单
            ApplyMallConfigDTO applyMallConfig = customReasonAttrService.getApplyMallConfig(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
            Integer automaticOrder = applyMallConfig == null ? SaasMessageConstant.IS_CHECKED_FALSE : applyMallConfig.getAutomaticOrder();
            if (StringUtils.isBlank(reqContract.getTravel_on_busi_common_req_contract().getOrder_apply_id())) {
                checkResult.setErrCode(GlobalResponseCode.OrderMallCheckNeedApply.getCode());
                if (automaticOrder == SaasMessageConstant.IS_CHECKED_TRUE) {
                    checkResult.setErrMsg(GlobalResponseCode.OrderMallCheckNeedAutomaticApply.getMsg());
                } else {
                    checkResult.setErrMsg(GlobalResponseCode.OrderMallCheckNeedApply.getMsg());
                }
                return checkResult;
            } else {
                ApplyOrder applyOrder = applyOrderMapper.selectByPrimaryKey(reqContract.getTravel_on_busi_common_req_contract().getOrder_apply_id());
                if (applyOrder == null || !applyOrder.getEmployeeId().equals(reqContract.getTravel_on_busi_common_req_contract().getEmployee_id())) {
                    checkResult.setErrCode(GlobalResponseCode.OrderMallCheckNeedApply.getCode());
                    if (automaticOrder == SaasMessageConstant.IS_CHECKED_TRUE) {
                        checkResult.setErrMsg(GlobalResponseCode.OrderMallCheckNeedAutomaticApply.getMsg());
                    } else {
                        checkResult.setErrMsg(GlobalResponseCode.OrderMallCheckNeedApply.getMsg());
                    }
                    return checkResult;
                }
            }
        }
        return checkResult;
    }

    /**
     * 强制提交提示（超标但需要理由）
     *
     * @param mallOrderCheckReqContract
     * @return
     */
    private TempOrderRuleCheckResult checkExceedAuth(MallOrderCheckReqV2Contract mallOrderCheckReqContract) {
        //默认初始化
        TempOrderRuleCheckResult result = new TempOrderRuleCheckResult();
        result.setResCode(TemporaryResponseCode.Success);
        boolean exceedSubmit = ObjUtils.ifNull(mallOrderCheckReqContract.getTravel_on_busi_parameter_req_contract().getExceed_submit(), Boolean.FALSE);
        if (exceedSubmit) {
            // 超标后强制提交
            if (StringUtils.isEmpty(mallOrderCheckReqContract.getTravel_on_busi_parameter_req_contract().getExceed_reason())
                    || mallOrderCheckReqContract.getTravel_on_busi_parameter_req_contract().getExceed_reason().length() > 50) {
                result.setResCode(TemporaryResponseCode.OrderCheckExceedReasonError);
                return result;
            }
            if (StringUtils.isNotEmpty(mallOrderCheckReqContract.getTravel_on_busi_parameter_req_contract().getExceed_reason_comment())
                    && mallOrderCheckReqContract.getTravel_on_busi_parameter_req_contract().getExceed_reason_comment().length() > 200) {
                result.setResCode(TemporaryResponseCode.OrderCheckExceedReasonCommentInvalid);
                return result;
            }
            ReasonConfig reasonConfig = customReasonService.queryReasonConfigWithoutItems(mallOrderCheckReqContract.getTravel_on_busi_common_req_contract().getCompany_id(), ReasonType.EXCEED_ORDER_MALL);
            if (reasonConfig.getReason() == SaasMessageConstant.IS_CHECKED_TRUE) {
                if (StringUtils.isBlank(mallOrderCheckReqContract.getTravel_on_busi_parameter_req_contract().getExceed_reason())) {
                    result.setResCode(TemporaryResponseCode.OrderCheckExceedReasonIsNull);
                    return result;
                }
                if (reasonConfig.getReason_desc() == SaasMessageConstant.IS_CHECKED_TRUE) {
                    if (StringUtils.isBlank(mallOrderCheckReqContract.getTravel_on_busi_parameter_req_contract().getExceed_reason_comment())) {
                        result.setResCode(TemporaryResponseCode.OrderCheckExceedReasonCommentIsNull);
                        return result;
                    }
                }
            }
        } else {
            // 超标，提示用户
            result.setResCode(TemporaryResponseCode.OrderCheckExceedNeedReason);
            return result;
        }
        return result;
    }


    /**
     * 校验采购规则信息
     *
     * @param mallRule
     * @param reqContract
     * @return
     */
    private List<TempOrderRuleCheckResult> checkMallExceedType(MallRule mallRule, MallOrderCheckReqV2Contract reqContract, BigDecimal totalPirce) {
        List<TempOrderRuleCheckResult> listMap = new ArrayList<>();
        //校验单笔金额
        if (mallRule.getLimitPriceFlag() && mallRule.getLimitPriceHighest().compareTo(totalPirce) < 0) {
            TempOrderRuleCheckResult checkResult = new TempOrderRuleCheckResult();
            checkResult.setResCode(TemporaryResponseCode.OrderCheckHotelPerNightPrice.getCode(), StrUtils.formatString(TemporaryResponseCode.OrderCheckHotelPerNightPrice.getMsg(), mallRule.getLimitPriceHighest()));
            checkResult.setIsPriceExceed(true);
            checkResult.setAmountCompliance(mallRule.getLimitPriceHighest());
            checkResult.setAmountNonCompliance(totalPirce.subtract(mallRule.getLimitPriceHighest()));
            listMap.add(checkResult);
        }
        List<String> businessList = Lists.newArrayList();
        //校验品类
        if (mallRule.getLimitType() == 1 && CollectionUtils.isNotEmpty(reqContract.getCategory_ids()) && StringUtils.isNotEmpty(mallRule.getLimitCategoryIds())) {
            List<String> ids = reqContract.getCategory_ids();
            List<MallOrderCheckReqV2Contract.SkuInfo> skuList = reqContract.getSku_detail();
            String limitCategoryIds = mallRule.getLimitCategoryIds();
            TempOrderRuleCheckResult checkResult = new TempOrderRuleCheckResult();
            boolean haveExceedSku = false;
            for (String id : ids) {
                if (!StringTool.isContains(id, limitCategoryIds)) {
                    haveExceedSku = true;
                    skuList.forEach(skuInfo -> {
                        if (Objects.equals(id, skuInfo.getCategory_id())) {
                            businessList.add(skuInfo.getSku_id());
                        }
                    });
                }
            }
            //如果存在超规商品
            if (haveExceedSku) {
                checkResult.setResCode(TemporaryResponseCode.OrderCheckRatedCategoryNoAuth);
                checkResult.setBusinessList(businessList);
                listMap.add(checkResult);
            }
        } else if (mallRule.getLimitType() == 2) {
            //校验sku
            MallRuleSkuExample mallRuleSkuExample = new MallRuleSkuExample();
            mallRuleSkuExample.createCriteria().andRuleIdEqualTo(mallRule.getId());
            List<MallRuleSku> mallRuleSkus = mallRuleSkuMapper.selectByExample(mallRuleSkuExample);
            List<MallOrderCheckReqV2Contract.SkuInfo> skuDetailList = reqContract.getSku_detail();
            if (CollectionUtils.isNotEmpty(mallRuleSkus) && CollectionUtils.isNotEmpty(skuDetailList)) {
                List<String> skuCategoryList = new ArrayList<>();
                for (MallRuleSku mallRuleSku : mallRuleSkus) {
                    skuCategoryList.add(mallRuleSku.getSkuId() + mallRuleSku.getSkuCategory());
                }
                for (MallOrderCheckReqV2Contract.SkuInfo skuInfo : skuDetailList) {
                    String skuId = skuInfo.getSku_id();
                    Integer skuCategory = skuInfo.getSku_category();
                    if (!skuCategoryList.contains(skuId + skuCategory)) {
                        businessList.add(skuId);
                    }
                }
                if (CollectionUtils.isNotEmpty(businessList)) {
                    TempOrderRuleCheckResult checkResult = new TempOrderRuleCheckResult();
                    checkResult.setResCode(TemporaryResponseCode.OrderCheckRatedCategoryNoAuth);
                    checkResult.setBusinessList(businessList);
                    listMap.add(checkResult);
                }
            }
            /*if (CollectionUtils.isNotEmpty(mallRuleSkus) && CollectionUtils.isNotEmpty(reqContract.getSku_list())) {
                List<String> skuIdList = mallRuleSkus.stream().map(mallRuleSku -> mallRuleSku.getSkuId()).collect(Collectors.toList());
                String skuIds = StringUtils.join(skuIdList.toArray(), ",");
                for (String id : reqContract.getSku_list()) {
                    if (!StringTool.isContains(id, skuIds)) {
                        TempOrderRuleCheckResult checkResult = new TempOrderRuleCheckResult();
                        checkResult.setResCode(TemporaryResponseCode.OrderCheckRatedCategoryNoAuth);
                        listMap.add(checkResult);
                        break;
                    }
                }
            }*/
        }
        return listMap;
    }

    /**
     * 添加采购拦截记录信息
     */
    private void initMallInterceptRecord(MallOrderCheckReqV2Contract mallOrderCheckReqContract, TempOrderRuleCheckResult ruleCheckResult) {
        MallInterceptRecord mallInterceptRecord = new MallInterceptRecord();
        //拦截参数
        try {
            //处理规则信息
            EmployeeMallRule employeeMallRule = iBaseEmployeeMallRuleService.queryEmployeeMallRuleByPrimaryKey(mallOrderCheckReqContract.getTravel_on_busi_common_req_contract().getEmployee_id(), mallOrderCheckReqContract.getTravel_on_busi_common_req_contract().getCompany_id());
            MallRule mallRule = null;
            if (employeeMallRule != null) {
                String ruleId = ObjUtils.ifNull(employeeMallRule.getManualMallRuleId(), employeeMallRule.getDefaultMallRuleId());
                mallRule = mallRuleMapper.selectByPrimaryKey(ruleId);
            }

            mallInterceptRecord.setId(IDTool.CreateUniqueID());
            mallInterceptRecord.setEmployeeId(mallOrderCheckReqContract.getTravel_on_busi_common_req_contract().getEmployee_id());
            mallInterceptRecord.setCompanyId(mallOrderCheckReqContract.getTravel_on_busi_common_req_contract().getCompany_id());
            mallInterceptRecord.setCreateTime(new Date());
            mallInterceptRecord.setContactName(mallOrderCheckReqContract.getTravel_on_busi_parameter_req_contract().getContact_name());
            mallInterceptRecord.setContactPhone(mallOrderCheckReqContract.getTravel_on_busi_parameter_req_contract().getContact_phone());
            mallInterceptRecord.setChannel(mallOrderCheckReqContract.getTravel_on_busi_common_req_contract().getChannel());
            mallInterceptRecord.setTotalPrice(mallOrderCheckReqContract.getTravel_on_busi_common_req_contract().getOrder_price());
            mallInterceptRecord.setMallRule(employeeMallRule.getMallRule() == null ? -1 : employeeMallRule.getMallRule());
            mallInterceptRecord.setMallRuleFlag(employeeMallRule.getMallRuleFlag() == null ? false : employeeMallRule.getMallRuleFlag());
            mallInterceptRecord.setSkuInfo(mallOrderCheckReqContract.getSku_info());
            mallInterceptRecord.setErrCode(ruleCheckResult.getErrCode());
            String errorMsg = ruleCheckResult.getErrMsg() + (StringUtils.isEmpty(ruleCheckResult.getExceed_msg()) ? "" : "|" + ruleCheckResult.getExceed_msg());
            mallInterceptRecord.setErrMsg(errorMsg);
            //mallInterceptRecord.setCostCenterId(mallOrderCheckReqContract.getTravel_on_busi_common_req_contract().getAttribution_id());
            //mallInterceptRecord.setCostCenterType(mallOrderCheckReqContract.getTravel_on_busi_common_req_contract().getAttribution_category());
            //处理拦截信息
            mallInterceptRecordMapper.insertSelective(mallInterceptRecord);
        } catch (Exception e) {
            logger.error("添加采购拦截记录信息:{},发生异常:{}", JsonUtils.toJson(mallInterceptRecord), e.getLocalizedMessage());
        }
    }

}
