package com.fenbeitong.saas.core.contract.organization.inner;

/**
 * Created by xuzn on 17/12/29.
 */
public class IntlAirPolicyBean {

    /**
     * 本人机票权限
     */
    private Boolean air_priv_flag;

    /**
     * 为其他员工预定权限
     */
    private Boolean air_other_flag;

    /**
     * 机票是否需要审批
     */
    private Boolean air_verify_flag;
    /**
     * 限制非企业员工预定机票标识
     */
    private Boolean unemployee_air;

    /**
     * 是否限制采购规则
     */
    private Boolean rule_limit_flag;
    /**
     * 规则 id
     */
    private String rule_id;

    private Integer exceed_buy_type;

    private int air_rule;

    private String rule_name;

    private Boolean intl_air_order_verify_flag;


    public Boolean getAir_other_flag() {
        return air_other_flag;
    }

    public void setAir_other_flag(Boolean air_other_flag) {
        this.air_other_flag = air_other_flag;
    }
    public Boolean getAir_priv_flag() {
        return air_priv_flag;
    }

    public void setAir_priv_flag(Boolean air_priv_flag) {
        this.air_priv_flag = air_priv_flag;
    }

    public Boolean getAir_verify_flag() {
        return air_verify_flag;
    }

    public void setAir_verify_flag(Boolean air_verify_flag) {
        this.air_verify_flag = air_verify_flag;
    }

    public Boolean getUnemployee_air() {
        return unemployee_air;
    }

    public void setUnemployee_air(Boolean unemployee_air) {
        this.unemployee_air = unemployee_air;
    }

    public Boolean getRule_limit_flag() {
        return rule_limit_flag;
    }

    public void setRule_limit_flag(Boolean rule_limit_flag) {
        this.rule_limit_flag = rule_limit_flag;
    }

    public String getRule_id() {
        return rule_id;
    }

    public void setRule_id(String rule_id) {
        this.rule_id = rule_id;
    }

    public Integer getExceed_buy_type() {
        return exceed_buy_type;
    }

    public void setExceed_buy_type(Integer exceed_buy_type) {
        this.exceed_buy_type = exceed_buy_type;
    }

    public int getAir_rule() {
        return air_rule;
    }

    public void setAir_rule(int air_rule) {
        this.air_rule = air_rule;
    }

    public String getRule_name() {
        return rule_name;
    }

    public void setRule_name(String rule_name) {
        this.rule_name = rule_name;
    }

    public Boolean getIntl_air_order_verify_flag() {
        return intl_air_order_verify_flag;
    }

    public void setIntl_air_order_verify_flag(Boolean intl_air_order_verify_flag) {
        this.intl_air_order_verify_flag = intl_air_order_verify_flag;
    }

    public IntlAirPolicyBean() {
    }

    public IntlAirPolicyBean(Boolean air_priv_flag, Boolean air_verify_flag, Boolean unemployee_air, Boolean rule_limit_flag, String rule_id, Integer exceed_buy_type, int air_rule, String rule_name, Boolean intl_air_order_verify_flag) {
        this.air_priv_flag = air_priv_flag;
        this.air_verify_flag = air_verify_flag;
        this.unemployee_air = unemployee_air;
        this.rule_limit_flag = rule_limit_flag;
        this.rule_id = rule_id;
        this.exceed_buy_type = exceed_buy_type;
        this.air_rule = air_rule;
        this.rule_name = rule_name;
        this.intl_air_order_verify_flag = intl_air_order_verify_flag;
    }
}
