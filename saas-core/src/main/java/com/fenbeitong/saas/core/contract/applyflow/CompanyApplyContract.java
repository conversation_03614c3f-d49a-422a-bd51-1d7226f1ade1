package com.fenbeitong.saas.core.contract.applyflow;

/**
 * Created by zhaohaichao on 2018/10/11.
 */
public class CompanyApplyContract {

    private Boolean abnormalApproval;  //是否存在异常审批
    private Boolean clxcAbnormal;//差旅行程审批流是否异常
    private Boolean lsycAbnormal;//临时用车审批流是否异常
    private Boolean clddAbnormal;//差旅订单审批流是否异常（含超规）
    private Boolean cgycAbnormal;//超规用餐审批流是否异常
    private Boolean ycAbnormal;//用餐审批流是否异常
    private Boolean cgAbnormal;//采购审批流是否异常
    private Boolean wmAbnormal;//采购审批流是否异常
    private Boolean xnkAbnormal;//采购审批流是否异常
    private Boolean xnkshAbnormal;//虚拟卡审核是否异常
    private Boolean ddhxAbnormal;//订单核销审核是否异常
    private Boolean paymentAbnormal;//付款审批是否异常
    private Boolean mileageAbnormal;//里程审批是否异常

    public Boolean getAbnormalApproval() {
        return abnormalApproval;
    }

    public void setAbnormalApproval(Boolean abnormalApproval) {
        this.abnormalApproval = abnormalApproval;
    }

    public Boolean getClxcAbnormal() {
        return clxcAbnormal;
    }

    public void setClxcAbnormal(Boolean clxcAbnormal) {
        this.clxcAbnormal = clxcAbnormal;
    }

    public Boolean getLsycAbnormal() {
        return lsycAbnormal;
    }

    public void setLsycAbnormal(Boolean lsycAbnormal) {
        this.lsycAbnormal = lsycAbnormal;
    }

    public Boolean getClddAbnormal() {
        return clddAbnormal;
    }

    public void setClddAbnormal(Boolean clddAbnormal) {
        this.clddAbnormal = clddAbnormal;
    }

    public Boolean getCgycAbnormal() {
        return cgycAbnormal;
    }

    public void setCgycAbnormal(Boolean cgycAbnormal) {
        this.cgycAbnormal = cgycAbnormal;
    }

    public Boolean getYcAbnormal() {
        return ycAbnormal;
    }

    public void setYcAbnormal(Boolean ycAbnormal) {
        this.ycAbnormal = ycAbnormal;
    }

    public Boolean getCgAbnormal() {
        return cgAbnormal;
    }

    public void setCgAbnormal(Boolean cgAbnormal) {
        this.cgAbnormal = cgAbnormal;
    }

    public Boolean getWmAbnormal() {
        return wmAbnormal;
    }

    public void setWmAbnormal(Boolean wmAbnormal) {
        this.wmAbnormal = wmAbnormal;
    }

    public Boolean getXnkAbnormal() {
        return xnkAbnormal;
    }

    public void setXnkAbnormal(Boolean xnkAbnormal) {
        this.xnkAbnormal = xnkAbnormal;
    }

    public CompanyApplyContract(Boolean abnormalApproval, Boolean clxcAbnormal, Boolean lsycAbnormal, Boolean clddAbnormal, Boolean cgycAbnormal, Boolean ycAbnormal, Boolean cgAbnormal,Boolean wmAbnormal , Boolean xnkAbnormal,Boolean xnkshAbnormal,Boolean ddhxAbnormal, Boolean paymentAbnormal, Boolean mileageAbnormal) {
        this.abnormalApproval = abnormalApproval;
        this.clxcAbnormal = clxcAbnormal;
        this.lsycAbnormal = lsycAbnormal;
        this.clddAbnormal = clddAbnormal;
        this.cgycAbnormal = cgycAbnormal;
        this.ycAbnormal = ycAbnormal;
        this.cgAbnormal = cgAbnormal;
        this.wmAbnormal = wmAbnormal;
        this.xnkAbnormal = xnkAbnormal;
        this.xnkshAbnormal=xnkshAbnormal;
        this.ddhxAbnormal=ddhxAbnormal;
        this.paymentAbnormal = paymentAbnormal;
        this.mileageAbnormal = mileageAbnormal;
    }

    public CompanyApplyContract(Boolean abnormalApproval){
        this.abnormalApproval = abnormalApproval;
    }

    public Boolean getXnkshAbnormal() {
        return xnkshAbnormal;
    }

    public void setXnkshAbnormal(Boolean xnkshAbnormal) {
        this.xnkshAbnormal = xnkshAbnormal;
    }

    public Boolean getDdhxAbnormal() {
        return ddhxAbnormal;
    }

    public void setDdhxAbnormal(Boolean ddhxAbnormal) {
        this.ddhxAbnormal = ddhxAbnormal;
    }

    public Boolean getPaymentAbnormal() {
        return paymentAbnormal;
    }

    public void setPaymentAbnormal(Boolean paymentAbnormal) {
        this.paymentAbnormal = paymentAbnormal;
    }

    public Boolean getMileageAbnormal() {
        return mileageAbnormal;
    }

    public void setMileageAbnormal(Boolean mileageAbnormal) {
        this.mileageAbnormal = mileageAbnormal;
    }
}
