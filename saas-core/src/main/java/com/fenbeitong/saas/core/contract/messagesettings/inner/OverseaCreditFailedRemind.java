package com.fenbeitong.saas.core.contract.messagesettings.inner;

import com.fenbeitong.saas.core.contract.user.EmployeeInfoContract;
import com.fenbeitong.saas.core.model.saas.MessageSetup;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;


/**
 * Web管理端->企业设置->企业配置->通知中心->员工海外卡使用通知
 * 额度下发失败提醒
 * <AUTHOR>
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OverseaCreditFailedRemind {
    /**
     * 是否启用提醒
     */
    private Integer is_check;
    /**
     * 是否发送APP通知
     */
    private Integer app_notice;
    /**
     * 是否发送邮件通知
     */
    private Integer mail_notice;
    /**
     * 是否发送电话短信通知
     */
    private Integer phone_notice;

    /**
     * 接收人ID: 更多人的员工信息
     */
    private List<EmployeeInfoContract> receiver_list = new ArrayList<>();
    /**
     * 持卡人
     */
    private Integer card_holder;


    public static String getRemindType() {
        return "oversea_credit_fail_remind";
    }

    public void build(MessageSetup setup) {
        this.setIs_check(setup.getIsChecked());
        this.setApp_notice(setup.getIntVal1());
        this.setMail_notice(setup.getIntVal2());
        this.setPhone_notice(setup.getIntVal3());
        this.setCard_holder(Integer.parseInt(setup.getStrVal1()));
    }
}
