package com.fenbeitong.saas.core.language;

import com.finhub.framework.i18n.manager.MessageSourceManager;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/11/21 18:56
 */
public enum CoreLanguage {

    Common_Exception_TitleFeeCategory("saas.corelanguage.common_exception_titlefeecategory","请选择正确的费用类别"),
    Common_Message_FlowForwardMsg("saas.corelanguage.common_message_flowforwardmsg","{0}转交{1}申请至您审核"),
    Rule_Value_HotelOrderApply("saas.corelanguage.rule_value_hotelorderapply","酒店订单申请"),
    Common_Message_HotelBackPersonPass("saas.corelanguage.common_message_hotelbackpersonpass","您提交的酒店退订申请已通过，点击查看详情"),
    Common_Value_BudgetQuotaNotEnoughMsg("saas.corelanguage.common_value_budgetquotanotenoughmsg","行程费用超出您的预算余额，可联系企业管理员调整预算或使用个人消费用车"),
    Common_Message_TakeawayNoticeMsg("saas.corelanguage.common_message_takeawaynoticemsg","{0}提交的外卖申请，抄送给您，请知晓"),
    Common_Exception_BusinessTypeNotEmpty("saas.corelanguage.common_exception_businesstypenotempty","业务类型不能为空"),
    Common_Message_MallSubmitDetail("saas.corelanguage.common_message_mallsubmitdetail","【审批抄送】{0}提交了采购申请，请您知晓，点击查看详情"),
    Rule_Value_SingleLimit("saas.corelanguage.rule_value_singlelimit","单笔限额"),
    Auth_Msg_Delay("saas.corelanguage.auth_msg_delay","尊敬的用户，{0}向您延期商务消费场景{1}的授权，延期期限{2}-{3}。"),
    Common_Message_MallFlowPassOrder("saas.corelanguage.common_message_mallflowpassorder","您提交的采购申请已通过，请在24小时内完成下单操作，点击查看详情"),
    Common_Exception_OrderPriceNotEmpty("saas.corelanguage.common_exception_orderpricenotempty","订单价格不能为空"),
    TaxiCacheConstant_Value_WebappNoNotifier("saas.corelanguage.taxicacheconstant_value_webappnonotifier","webapp没有订单通知人"),
    Common_Message_TakeawayCheckPass("saas.corelanguage.common_message_takeawaycheckpass","{0}同意了您的外卖申请。"),
    ApplyV5CenterServiceImpl_Value_NoApply("saas.corelanguage.applyv5centerserviceimpl_value_noapply","你当前没有符合要求的申请单，需先提交申请再预订"),
    Common_Exception_UserIdNotEmpty("saas.corelanguage.common_exception_useridnotempty","用户id不能为空"),
    Rule_Value_TrainNoLimit("saas.corelanguage.rule_value_trainnolimit","车次限制"),
    Common_Value_NotValid("saas.corelanguage.common_value_notvalid","未生效"),
    Common_Value_Project("saas.settingtype.project","项目"),
    Common_Exception_InvalidExtendId("saas.corelanguage.common_exception_invalidextendid","延长授权id不能为空"),
    Common_Message_CarDidiNoticeMsg("saas.corelanguage.common_message_cardidinoticemsg","{0}提交了滴滴企业级用车申请，抄送给您，请知晓"),
    Common_Title_Pass("saas.corelanguage.common_value_pass","已通过"),
    Rule_Value_CarQuota("saas.corelanguage.rule_value_carquota","您的总额上限为{0}元"),
    Common_Value_ApprovalListBusiness("saas.corelanguage.common_value_approvallistbusiness","商务消费报销审批单"),
    Common_Message_DinnerApplyRefuseNotice("saas.corelanguage.common_message_dinnerapplyrefusenotice","【申请被驳回】您提交的用餐费用申请被驳回，点击查看详情"),
    TrainCheckServiceImpl_Value_RebookTicket("saas.corelanguage.traincheckserviceimpl_value_rebookticket","改签票价格需低于{0}元"),
    Common_Exception_TitleCompanyQuota("saas.corelanguage.common_exception_titlecompanyquota","企业额度不足"),
    Common_Message_TravelPassNoticeMsg("saas.corelanguage.common_message_travelpassnoticemsg","{0}提交的出差申请已通过，抄送给您，请知晓"),
    Common_Exception_TrainEmployeeNoAuthMsg("saas.corelanguage.common_exception_trainemployeenoauthmsg","您尚未开通火车票预订权限,请联系管理员为您开通"),
    Common_Message_DinnerFeeForwardNotice("saas.corelanguage.common_message_dinnerfeeforwardnotice","【审批提示】您提交的用餐费用申请已审批至{0}，点击查看详情"),
    Common_Value_TrainApply("saas.applytypewebshowopt.train","火车票申请"),
    Rule_Value_CarBudgetLimit("saas.corelanguage.rule_value_carbudgetlimit","预算限制"),
    Common_Message_MallCheckPassMsg("saas.corelanguage.common_message_mallcheckpassmsg","{0}的采购申请已通过"),
    Rule_Value_ApplicantDepartment("saas.corelanguage.rule_value_applicantdepartment","申请人部门"),
    Rule_Value_DinnerSendTimeRange("saas.corelanguage.rule_value_dinnersendtimerange","用餐/送餐时段"),
    Common_Value_TravelRoadApply("saas.applyflowtype.mileage","里程补贴申请"),
    Rule_Value_ApplyApplicant("saas.corelanguage.rule_value_applyapplicant","申请人"),
    Common_Message_AirChangePersonPass("saas.corelanguage.common_message_airchangepersonpass","您提交的机票改期申请已通过，点击查看详情"),
    Rule_Value_CityLineDistance("saas.corelanguage.rule_value_citylinedistance","城市直线距离大于{0}公里"),
    Custom_Exception_ReasonTypeNotEmpty("saas.corelanguage.custom_exception_reasontypenotempty","事由类型不能为空"),
    Order_Value_DefaultLimitUpdate("saas.corelanguage.order_value_defaultlimitupdate","下单时将默认使用此选项且不可修改，请仔细填写"),
    Common_Exception_MallOrderServerError("saas.corelanguage.common_exception_mallorderservererror","采购创建订单接口异常"),
    Common_Message_TrainOrderTicketErr("saas.corelanguage.common_message_trainorderticketerr","您预订的{0}，{1}，{2}列车，乘车人：{3}，座席：{4}，出票失败，请您重新预订。"),
    Common_Message_RemindSubmitOrderTicket("saas.corelanguage.common_message_remindsubmitorderticket","您有{0}个报销单需要交回单据，快去查看"),
    Common_Value_Close("saas.corelanguage.common_value_close","关闭"),
    TaxiCheckServiceImpl_Value_UsedCarMoneyUnion("saas.corelanguage.taxicheckserviceimpl_value_usedcarmoneyunion","用车金额合并"),
    Common_Value_TravelBackFeeWarn("saas.corelanguage.common_value_travelbackfeewarn","错过最佳退订时间点可能产生较高退订手续费或延误差旅行程，请尽快完成审批"),
    Rule_Value_CarType("saas.corelanguage.rule_value_cartype","乘坐车型"),
    Common_Message_AirSubmitOrderApply("saas.corelanguage.common_message_airsubmitorderapply","{0}提交了机票订单申请，请您知晓，点击查看详情"),
    Rule_Value_Trip("saas.corelanguage.rule_value_trip","行程"),
    Rule_Value_SingleDayLimit("saas.corelanguage.rule_value_singledaylimit","单日限额"),
    Rule_Value_DinnerText("saas.ruletype.diner","用餐"),
    Common_Message_TrainBackPersonPassNotice("saas.corelanguage.common_message_trainbackpersonpassnotice","【申请已通过】您提交的火车票退票申请已通过，点击查看详情"),
    Custom_Exception_TravelAccountRequired("saas.corelanguage.custom_exception_travelaccountrequired","差旅统计必填"),
    Common_Message_FlowFbqPassApply("saas.corelanguage.common_message_flowfbqpassapply","您提交的{0}张{1}分贝券申请已通过，点击查看详情"),
    Rule_Value_CarAllowOtherQuotaUnion("saas.corelanguage.rule_value_carallowotherquotaunion","允许与用车剩余额度合并"),
    Common_Value_TravelChangeApply("saas.corelanguage.common_value_travelchangeapply","差旅变更申请"),
    Common_Exception_OrderMoneyNotEmpty("saas.corelanguage.common_exception_ordermoneynotempty","订单金额不能为空"),
    Common_Title_PleaseCheckFee("saas.corelanguage.common_value_pleasecheckfee","请核实员工消费信息"),
    Custom_Exception_ReasonContentError("saas.corelanguage.custom_exception_reasoncontenterror","事由内容项错误"),
    Common_Value_FieldConfig("saas.corelanguage.common_value_fieldconfig","字段配置"),
    Common_Value_TripHasRepeatOrder("saas.corelanguage.common_value_triphasrepeatorder","所选出行人在该申请单下存在重复订单"),
    Common_Exception_TitleBudgetNot("saas.corelanguage.common_exception_titlebudgetnot","预算不足"),
    Rule_Value_AirIntlTicketApply("saas.applytypewebshowopt.intlair","国际机票订单申请"),
    Common_Value_FbqApply("saas.startapplylisttype.fenbeicouponorder","分贝券申请"),
    Custom_Exception_ReasonRequired("saas.corelanguage.custom_exception_reasonrequired","事由必填"),
    Rule_Value_FeeBelongType("saas.corelanguage.rule_value_feebelongtype","费用归属类型"),
    Common_Exception_CompanyIdNotEmpty("saas.corelanguage.common_exception_companyidnotempty","公司id不能为空"),
    Rule_Value_PredictAmount("saas.corelanguage.rule_value_predictamount","预估/审批金额"),
    Common_Message_FlowRevokeMsg("saas.corelanguage.common_message_flowrevokemsg","{0}撤回了{1}申请"),
    Rule_Value_AllFlightDesc("saas.corelanguage.rule_value_allflightdesc","限起飞前后{0}小时内最低价"),
    Common_Message_FlowSubmitCheckMsg("saas.corelanguage.common_message_flowsubmitcheckmsg","{0}提交了{1}申请，请尽快完成审批"),
    Auth_Msg_Title_DelayApplyAuth("saas.corelanguage.auth_msg_title_delayapplyauth","申请延期授权"),
    Auth_Msg_Title_Reject("saas.corelanguage.auth_msg_title_reject","驳回授权"),
    Common_Message_TaxiOrderDriverArrivalTitle("saas.corelanguage.common_message_taxiorderdriverarrivaltitle","用车-司机已到达"),
    TaxiCacheServiceImpl_Value_FeeType("saas.corelanguage.taxicacheserviceimpl_value_feetype","费用类别"),
    Order_Value_MsgOccupant("saas.corelanguage.order_value_msgoccupant","入住人"),
    Common_Exception_UserRuleIsEmpty("saas.corelanguage.common_exception_userruleisempty","用户规则为空"),
    Common_Exception_ModifyStateError("saas.corelanguage.common_exception_modifystateerror","自主授权状态变更，变更状态参数错误"),
    Common_Message_OrderCarTimeOrPlace("saas.corelanguage.common_message_ordercartimeorplace","{0}预订了{1}至{2}的企业用车"),
    Rule_Value_ApplyOrderType("saas.corelanguage.rule_value_applyordertype","申请单类型"),
    Rule_Value_OverQuotaPersonPay("saas.corelanguage.rule_value_overquotapersonpay","超出部分需您个人自费无法报销，是否继续提交？"),
    Common_Message_AirTicketChangeNotice("saas.corelanguage.common_message_airticketchangenotice","【审批抄送】{0}提交了机票改期申请，请您知晓，点击查看详情"),
    Rule_Value_AllPlaceText("saas.corelanguage.rule_value_allplacetext","全部地区"),
    Common_Message_TrainTicketChange("saas.corelanguage.common_message_trainticketchange","{0}提交了火车票改签申请，请尽快完成审批"),
    Rule_Value_TrainLessTime("saas.corelanguage.rule_value_trainlesstime","火车高铁/动车)最短耗时超过{0}小时"),
    Common_Message_AirChangeFlowNotice("saas.corelanguage.common_message_airchangeflownotice","【审批通知】{0}提交了机票改期申请，错过改期时间点可能延误差旅行程，请尽快完成审批"),
    Order_Value_LimitSubmit("saas.corelanguage.order_value_limitsubmit","您本次提交订单，{0}{1}业务{2}{3}元限额，是否继续提交？"),
    Common_Exception_FeeManagerNotEmpty("saas.corelanguage.common_exception_feemanagernotempty","费用管理配置不能为空"),
    Custom_Exception_TrainPersonNotEmpty("saas.globalresponsecode.ordercheckcostattributionpassengerid","乘机人不能为空"),
    Common_Message_HotelSubmitOrderApply("saas.corelanguage.common_message_hotelsubmitorderapply","{0}提交了酒店订单申请，请您知晓，点击查看详情"),
    Common_Value_Message("saas.corelanguage.common_value_message","提示"),
    Common_Value_BeforeAndAfterDays("saas.corelanguage.common_value_beforeandafterdays","至少提前{0}天,至多提前{1}天"),
    Rule_Value_AirIntlText("saas.traveltype.intlairplane","国际机票"),
    Common_Message_TakeawayNeedCheckNotice("saas.corelanguage.common_message_takeawayneedchecknotice","【审批通知】{0}提交了{1}元的外卖申请，请尽快完成审批。"),
    Common_Message_TrainOrderUnpaid("saas.corelanguage.common_message_trainorderunpaid","车票待支付：您预订的{0}，{1}次，请及时完成订单支付。"),
    TakeawayCheckServiceImpl_Value_OrderFlowChecked("saas.actionbuttionenum.orderapproval","订单审批"),
    Rule_Value_TripTime("saas.corelanguage.rule_value_triptime","行程耗时"),
    ExcelApplyFlowUtil_Value_ApplyItemType("saas.corelanguage.excelapplyflowutil_value_applyitemtype","审批人类型"),
    Common_Message_TrainForwardOrderApprovalNotice("saas.corelanguage.common_message_trainforwardorderapprovalnotice","【审批提示】您提交的火车票订单申请已审批至{0}，点击查看详情"),
    Common_Message_AirTicketChangeInvalid("saas.corelanguage.common_message_airticketchangeinvalid","您提交的机票改期申请航班失效，申请单已关闭，点击查看详情"),
    Common_Value_ZaiText("saas.corelanguage.common_value_zaitext","在"),
    Common_Value_Display("saas.corelanguage.common_value_display","展示"),
    Common_Message_DinnerApplyTimeoutClose("saas.corelanguage.common_message_dinnerapplytimeoutclose","您提交的用餐申请超时未被审批，订单已关闭，点击查看详情"),
    Common_Exception_RequestDataNotEmpty("saas.corelanguage.common_exception_requestdatanotempty","请求参数不能为空"),
    Common_Message_DinnerNeedForwardCheckNotice("saas.corelanguage.common_message_dinnerneedforwardchecknotice","{0}转交用餐申请至您审核。"),
    Order_Value_CheckYear("saas.corelanguage.order_value_checkyear","年度"),
    Rule_Value_HistoryCheckUser("saas.corelanguage.rule_value_historycheckuser","历史审批人"),
    Rule_Value_HotelText("saas.traveltype.hotel","酒店"),
    Common_Message_AirOrderTitle("saas.corelanguage.common_message_airordertitle","机票预订"),
    Common_Message_DinnerFeePassOrderApplyNotice("saas.corelanguage.common_message_dinnerfeepassorderapplynotice","【审批抄送】{0}提交了用餐费用申请已通过，请您知晓，点击查看详情"),
    Common_Exception_RetrySelectApply("saas.corelanguage.common_exception_retryselectapply","请重新选择申请单"),
    Common_Message_TrainOrderApprovalNotice("saas.corelanguage.common_message_trainorderapprovalnotice","【审批通知】{0}提交了火车票订单申请，票量随时变化，请尽快审批"),
    Rule_Value_CopyMoney("saas.corelanguage.rule_value_copymoney","备用金"),
    Common_Exception_FlowUserRepeat("saas.corelanguage.common_exception_flowuserrepeat","审批人重复"),
    Common_Value_TravelDays("saas.corelanguage.common_value_traveldays","出差天数"),
    Rule_Value_PriceDiscountLess("saas.globalresponsecode.ordercheckaircabinbelowdiscountnoauth","折扣需低于{0}折"),
    Common_Exception_RoleApplyTypeError("saas.globalresponsecode.companyroleapprovetypeiserror","角色审批类型不正确"),
    Common_Exception_ApplyIdNotEmpty("saas.corelanguage.common_exception_applyidnotempty","审批单id不能为空"),
    Common_Exception_SelfAuthNotEmpty("saas.corelanguage.common_exception_selfauthnotempty","自主授权状态不能为空"),
    Rule_Value_CarBoardType("saas.corelanguage.rule_value_cartype","乘坐车型"),
    Common_Exception_RoleExistNotRepeat("saas.corelanguage.common_exception_roleexistnotrepeat","公司已有该角色,请勿重复添加"),
    Common_Value_ValidLoading("saas.authstateenum.in_effect","生效中"),
    Common_Message_TrainTimeoutOrderApprovalNotice("saas.corelanguage.common_message_traintimeoutorderapprovalnotice","【申请已超时】您提交的火车票订单申请超时未被审批，订单已关闭，点击查看详情"),
    Common_Message_TrainTimeoutOrderApproval("saas.corelanguage.common_message_traintimeoutorderapproval","您提交的火车票订单申请超时未被审批，订单已关闭，点击查看详情"),
    Common_Value_DayQuota("saas.corelanguage.common_value_dayquota","单日¥{0}"),
    Common_Exception_TrainCompanyNoAuthMsg("saas.corelanguage.common_exception_traincompanynoauthmsg","您所在的公司不允许订购火车票,具体情况请联系管理员"),
    Common_Exception_TripStartAndEndDateNotEmpty("saas.corelanguage.common_exception_tripstartandenddatenotempty","行程开始/结束时间不能为空"),
    Common_Message_HotelBackPass("saas.corelanguage.common_message_hotelbackpass","{0}提交的酒店退订申请已通过，请您知晓，点击查看详情"),
    Common_Exception_TravelApprovalExistSuccessOrFailData("saas.globalresponsecode.applymileageorderdataerror","里程补贴审批单中存在发放失败和发放中数据，请重新勾选"),
    Rule_Value_TakeawayAddress("saas.corelanguage.rule_value_takeawayaddress","送餐地址"),
    Common_Message_FlowRevokeApplyMsg("saas.corelanguage.common_message_flowrevokeapplymsg","{0}撤销了{1}申请，请知晓"),
    Common_Message_BusinessOrderNoticeMsg("saas.corelanguage.common_message_businessordernoticemsg","{0}提交了商务订单报销申请，抄送给您，请知晓"),
    Common_Value_HotelPerson("saas.corelanguage.order_value_msgoccupant","入住人"),
    Common_Value_TimeYesterday("saas.corelanguage.common_value_timeyesterday","昨天"),
    Common_Message_TakeawayPassNoticeMsg("saas.corelanguage.common_message_takeawaypassnoticemsg","{0}提交的外卖申请已通过，抄送给您，请知晓"),
    Common_Exception_IdValueIsEmpty("saas.corelanguage.common_exception_idvalueisempty","id为空"),
    Common_Value_NotSetCity("saas.corelanguage.common_value_notsetcity","未设置城市"),
    Rule_Value_MallText("saas.ruletype.mall","采购"),
    Common_Value_CarFeeNotLimit("saas.corelanguage.common_value_carfeenotlimit","不限制用车费用"),
    Common_Exception_NamesIsLonger("saas.corelanguage.common_exception_namesislonger","名称过长"),
    Common_Value_SingleNunMsg("saas.corelanguage.common_value_singlenunmsg","单次¥{0}"),
    Common_Value_SingleDayMsg("saas.corelanguage.common_value_singlenunmsg","单日¥{0}"),
    ExcelApplyFlowUtil_Value_UsedByProject("saas.corelanguage.excelapplyflowutil_value_usedbyproject","应用项目"),
    Common_Value_ShopNum("saas.corelanguage.common_value_shopnum","共 {0} 件商品"),
    Common_QrcodeFail("saas.corelanguage.common_qrcodefail","条形码内容写入失败"),
    Common_Message_AirTimeoutOrderApprovalNotice("saas.corelanguage.common_message_airtimeoutorderapprovalnotice","【申请已超时】您提交的机票订单申请超时未被审批，订单已关闭，点击查看详情"),
    Common_Exception_BeginTimeValueNotEmpty("saas.corelanguage.common_exception_begintimevaluenotempty","beginTime不能为空"),
    Rule_Value_TripDistance("saas.corelanguage.rule_value_tripdistance","行程距离"),
    TakeawayCheckServiceImpl_Value_OverTitle("saas.corelanguage.takeawaycheckserviceimpl_value_overtitle","超规提示"),
    Rule_Value_CarNotOccupyPerson("saas.corelanguage.rule_value_carnotoccupyperson","不占用个人预算"),
    Common_Message_AcctPublicRemindNoticeTitle("saas.corelanguage.common_message_acctpublicremindnoticetitle","对公付款账户余额不足"),
    Common_Message_AcctOverseaRemindNoticeTitle("saas.corelanguage.common_message_acctoversearemindnoticetitle","海外卡账户余额不足"),
    Common_Message_AcctOverseaLargeOverRemindTitle("saas.corelanguage.common_message_acctoversealargeoverremindtitle","{0}海外卡大额异动提醒"),
    Common_Message_AcctOverseaConsumeRemindTitle("saas.corelanguage.common_message_acctoverseaconsumeremindtitle","{0}海外卡消费通知"),
    Rule_Value_TakeawayOrderTimeRange("saas.corelanguage.rule_value_takeawayordertimerange","下单时段"),
    TaxiCacheConstant_Exception_UserIdIsEmpty("saas.corelanguage.taxicacheconstant_exception_useridisempty","用户id参数为空"),
    MessageKafkaWebCustomerService_Exception_BatchSaveCompanyMessageExistObjectIsEmpty("saas.corelanguage.messagekafkawebcustomerservice_exception_batchsavecompanymessageexistobjectisempty","批量保存企业Web消息存在参数为空"),
    Common_Message_HotelBack("saas.corelanguage.common_message_hotelback","{0}提交了酒店退订申请，请您知晓，点击查看详情"),
    Rule_Value_TakeawayDinnerTimes("saas.corelanguage.rule_value_takeawaydinnertimes","点餐次数"),
    Common_Exception_ReasonNameNotOverTwenty("saas.corelanguage.common_exception_reasonnamenotovertwenty","事由名称不能超过20个字"),
    Common_Message_HotelRefuseOrderApproval("saas.corelanguage.common_message_hotelrefuseorderapproval","您提交的酒店订单申请被驳回，点击查看详情"),
    Common_Message_AirTicketBackForwardNotice("saas.corelanguage.common_message_airticketbackforwardnotice","【审批提示】您提交的机票退改申请已审批至{0}，点击查看详情"),
    Common_Exception_InvalidSource("saas.corelanguage.common_exception_invalidsource","不合法的来源"),
    Common_Message_FlowRefuseMsg("saas.corelanguage.common_message_flowrefusemsg","{0}驳回了您的{1}申请，点击查看详情"),
    Custom_Exception_CustomFieldFlag("saas.corelanguage.custom_exception_customfieldflag","自定义字段应用标识"),
    Common_Value_HotelBackApply("saas.corelanguage.common_value_hotelbackapply","酒店退订申请"),
    Order_Value_MsgPersonDinner("saas.corelanguage.order_value_msgpersondinner","您的用餐订单{0}"),
    Rule_Value_PredictFeeUserAdd("saas.corelanguage.rule_value_predictfeeuseradd","预估费用由申请人填写，仅作为审批参考数据"),
    Common_Message_DinnerCheckPass("saas.corelanguage.common_message_dinnercheckpass","{0}同意了您的用餐申请。"),
    Common_Message_HotelBackTimeoutNotice("saas.corelanguage.common_message_hotelbacktimeoutnotice","【申请已超时】您提交的酒店退订申请超时未被审批，退订申请已关闭，点击查看详情"),
    Rule_Value_CarLimitFee("saas.corelanguage.rule_value_carlimitfee","上限{0}元"),
    Rule_Value_TrainChangeApply("saas.corelanguage.rule_value_trainchangeapply","火车改签申请"),
    Common_Value_CompanyQuotaNotEnoughMsg("saas.corelanguage.common_value_companyquotanotenoughmsg","您所属企业的账户余额不足，请联系企业管理员处理"),
    Auth_Msg_Title_TimeExpired("saas.corelanguage.auth_msg_title_timeexpired","授权失效"),
    Common_Message_AirOrderApprovalNotice("saas.corelanguage.common_message_airorderapprovalnotice","【审批通知】{0}提交了机票订单申请，为避免变价或无票，请尽快审批"),
    Common_Title_Forward("saas.corelanguage.common_value_forward","已转交"),
    Common_Exception_StartDateNotCorrect("saas.corelanguage.common_exception_startdatenotcorrect","起始日期格式不正确"),
    Common_Value_Open("saas.costcenterstateenum.start","开启"),
    Common_Message_TrainTicketBack("saas.corelanguage.common_message_trainticketback","{0}提交了火车票退订申请，请您知晓，点击查看详情"),
    Common_Message_CarSubmitOrderApplyNotice("saas.corelanguage.common_message_carsubmitorderapplynotice","【审批抄送】{0}提交了用车申请，请您知晓，点击查看详情"),
    Order_Value_ApplicateUpperOrderLimit("saas.corelanguage.order_value_applicateupperorderlimit","申请人违反了「最多提前{0}天预订」的限制"),
    Common_Value_UserDeleteForwardLeader("saas.corelanguage.common_value_userdeleteforwardleader","用户被删除，跳转到授权负责人"),
    Order_Value_PredictIsQuotaUpper("saas.corelanguage.order_value_predictisquotaupper","预估费用为本次差旅行程所有订单总额的费用上限"),
    Common_Message_HotelBackPersonRefuse("saas.corelanguage.common_message_hotelbackpersonrefuse","您提交的酒店退订申请被驳回，点击查看详情"),
    Common_Message_TrainTicketUpdatePersonNotice("saas.corelanguage.common_message_trainticketupdatepersonnotice","【申请被驳回】您提交的火车票改签申请被驳回，点击查看详情"),
    Rule_Value_ApplyOrderId("saas.corelanguage.rule_value_applyorderid","申请单号"),
    Common_Exception_TitleFileRequired("saas.corelanguage.common_exception_titlefilerequired","企业已设置附件必填"),
    Common_Exception_FeeBelongIdNotEmpty("saas.corelanguage.common_exception_feebelongidnotempty","费用归属id不能为空"),
    Common_Message_TakeawayCheckPassNotice("saas.corelanguage.common_message_takeawaycheckpassnotice","【审批提示】您提交的{0}元的外卖申请已通过，点击查看详情。"),
    Common_Message_TaxiOrderDriverArrival("saas.corelanguage.common_message_taxiorderdriverarrival","司机已到达，车型：{0}，司机：{1}，{2}，请尽快上车。"),
    Common_Message_FlowSubmitAcceptMsg("saas.corelanguage.common_message_flowsubmitacceptmsg","{0}同意了您的{1}申请"),
    Common_Message_RemoveOrderBack("saas.corelanguage.common_message_removeorderback","您管理员提醒您尽快交回核销单相关单据，点击查看详情"),
    Common_Exception_CreateDateTimeFormatNotCorrect("saas.corelanguage.common_exception_createdatetimeformatnotcorrect","创建时间格式不正确, 请使用'{0}'时间格式"),
    Common_Exception_InternelError("saas.corelanguage.common_exception_internelerror","系统异常，请稍后再试"),
    Rule_Value_CarUsedDayLimit("saas.corelanguage.rule_value_caruseddaylimit","以{0}至次日{1}为一天，管控单日用车金额和单日用车次数"),
    TaxiCacheConstant_Exception_CarInfoIsEmpty("saas.corelanguage.taxicacheconstant_exception_carinfoisempty","用车信息参数为空"),
    Common_Message_TrainPassOrderApproval("saas.corelanguage.common_message_trainpassorderapproval","您提交的火车票订单申请已通过，点击查看详情"),
    Rule_Value_HalfText("saas.corelanguage.rule_value_halftext","半"),
    Common_Value_DinnerApply("saas.startapplylisttype.dinnerorder","用餐申请"),
    Common_Exception_IdListNotEmpty("saas.corelanguage.common_exception_idlistnotempty","idList不能为空"),
    Common_Value_ApplyTotalQuota("saas.corelanguage.common_value_applytotalquota","申请单总额度{0}元，可用{1}元"),
    Rule_Value_PredictFeeTravelUsed("saas.corelanguage.rule_value_predictfeetravelused","预估费用由申请人填写，为本次差旅行程可支付费用上限"),
    Rule_Value_CarSingleMoney("saas.corelanguage.rule_value_carsinglemoney","单次用车上限{0}元"),
    Common_Message_TrainSubmitOrderApply("saas.corelanguage.common_message_trainsubmitorderapply","{0}提交了火车票订单申请，请您知晓，点击查看详情"),
    Common_Exception_TitleAddRemark("saas.corelanguage.common_exception_titleaddremark","请添加描述"),
    TrainCheckServiceImpl_Value_SingleTicket("saas.globalresponsecode.orderchecktrainrulebelowpricelimitmsg","单张票价格需低于{0}元"),
    Common_Message_VirtualCardAccountBalanceWarnContent("saas.corelanguage.common_message_virtualcardaccountbalancewarncontent","尊敬的用户，您好。截止到{0}，您公司虚拟卡场景的生效账户余额为{1}元，已小于您设置的告警阈值，为不影响员工正常使用，建议您及时充值。"),
    Common_Message_HotelBackPersonRefuseNotice("saas.corelanguage.common_message_hotelbackpersonrefusenotice","【申请被驳回】您提交的酒店退订申请被驳回，点击查看详情"),
    Common_Message_MallFlowPass("saas.corelanguage.common_message_mallflowpass","您提交的采购申请已通过，并已提交采购订单，点击查看申请单详情"),
    TaxiCacheConstant_Exception_CarSceneCodeIsEmpty("saas.corelanguage.taxicacheconstant_exception_carscenecodeisempty","用车场景码参数为空"),
    Common_Value_MallApply("saas.startapplylisttype.mallorder","采购申请"),
    Rule_Value_AirTicketApply("saas.applytypewebshowopt.air","国内机票订单申请"),
    TakeawayCheckServiceImpl_Value_OverDinner("saas.corelanguage.takeawaycheckserviceimpl_value_overdinner","用餐人中的{0},{1}人差标超规"),
    Common_Message_AirOrderRefundSuccess("saas.corelanguage.common_message_airorderrefundsuccess","您预订的{0}，{1}，预计{2}起飞，预计{3}到达，{4}票号： {5}，乘机人：{6} ，已退票成功。"),
    Order_Value_CheckQuarter("saas.corelanguage.order_value_checkquarter","季度"),
    Common_Exception_AirEmployeeNoAuthMsg("saas.corelanguage.common_exception_airemployeenoauthmsg","您尚未开通机票权限，请联系管理员为您开通"),
    Common_Message_HotelBackFlowNotice("saas.corelanguage.common_message_hotelbackflownotice","【审批通知】{0}提交了酒店退订申请，酒店退订政策规定在{1}后无法退房，请尽快完成审批"),
    Rule_Value_CarCallLimit("saas.corelanguage.rule_value_carcalllimit","叫车限制"),
    Common_Exception_FlowNameExsits("saas.corelanguage.common_exception_flownameexsits","审批流名称已存在"),
    Custom_Exception_ApplyOrderNumConfig("saas.corelanguage.custom_exception_applyordernumconfig","申请单次数限制配置"),
    Common_Message_TrainOrderBookErr("saas.corelanguage.common_message_trainorderbookerr","很抱歉，您预订的{0}，{1}次，车票占座失败，可登录分贝通重新购票。"),
    TaxiCacheConstant_Exception_CarTypeIsEmpty("saas.corelanguage.taxicacheconstant_exception_cartypeisempty","用车类型参数为空"),
    Common_Exception_CompanyIdValueNotEmpty("saas.corelanguage.common_exception_companyidvaluenotempty","companyId不能为空"),
    Common_Message_TaxiOrderPersonalWaitPayTitle("saas.corelanguage.common_message_taxiorderpersonalwaitpaytitle","用车-订单待支付"),
    Common_Exception_AuthModifySelfRelated("saas.corelanguage.common_exception_authmodifyselfrelated","只可申请更改自己相关的授权记录"),
    Common_Message_MallFlowRejectNotice("saas.corelanguage.common_message_mallflowrejectnotice","【申请被驳回】您提交的采购申请被驳回，点击查看详情"),
    Rule_Value_BusText("saas.traveltype.bus","汽车"),
    Custom_Exception_PredictOrderCheck("saas.corelanguage.custom_exception_predictordercheck","预估费用下单需校验"),
    Common_Exception_InvalidAuthId("saas.corelanguage.common_exception_invalidauthid","授权id不能为空"),
    Rule_Value_ApplyThirdId("saas.corelanguage.rule_value_applythirdid","申请人第三方ID"),
    Common_Message_AirRefuseOrderApprovalNotice("saas.corelanguage.common_message_airrefuseorderapprovalnotice","【申请被驳回】您提交的机票订单申请被驳回，点击查看详情"),
    Rule_Value_HotelBackApply("saas.corelanguage.common_value_hotelbackapply","酒店退订申请"),
    Common_Message_MallFlowReject("saas.corelanguage.common_message_mallflowreject","您提交的采购申请被驳回，点击查看详情"),
    Common_Message_AirBackPassNotice("saas.corelanguage.common_message_airbackpassnotice","【审批抄送】{0}提交的机票退订申请已通过，请您知晓，点击查看详情"),
    Common_Exception_TitleNotRepeatCheck("saas.corelanguage.common_exception_titlenotrepeatcheck","请勿重复提交核销"),
    Common_Message_MileageNoticeMsg("saas.corelanguage.common_message_mileagenoticemsg","{0}提交了里程补贴申请，抄送给您，请知晓"),
    Common_Message_TravelCheckPassMsg("saas.corelanguage.common_message_travelcheckpassmsg","{0}的出差申请已通过"),
    Rule_Value_AirBookableSpace("saas.corelanguage.rule_value_airbookablespace","可订舱位"),
    Rule_Value_TakeawayCheck("saas.companysettingtype.takeawayapply","外卖审批"),
    Common_Exception_TitleVirtualCardValid("saas.corelanguage.common_exception_titlevirtualcardvalid","员工分贝通虚拟卡已被禁用"),
    Common_Value_YearBudget("saas.corelanguage.common_value_yearbudget","年度预算"),
    Common_Message_AirTicketChangePassNotice("saas.corelanguage.common_message_airticketchangepassnotice","【审批抄送】{0}提交的机票改期申请已通过，请您知晓，点击查看详情"),
    Rule_Value_HotelSecondRoomFeeLess("saas.corelanguage.rule_value_hotelsecondroomfeeless","二线城市需低于¥{0}元\n"),
    Common_Exception_FlowUsedByProjectRepeat("saas.corelanguage.common_exception_flowusedbyprojectrepeat","项目应用审批重复:"),
    Common_Value_OnlineChangeTicketFour("saas.corelanguage.common_value_onlinechangeticketfour","在线改签功能将在发车时刻前40分钟关闭，关闭后只能到始发站办理改签手续，请尽快完成审批"),
    Common_Value_AfterNoValid("saas.corelanguage.common_value_afternovalid","后失效"),
    TaxiCacheConstant_Value_ObjectIsEmpty("saas.corelanguage.taxicacheconstant_value_objectisempty","入参该项缓存数据为空"),
    Common_Message_HotelTimeoutOrderApproval("saas.corelanguage.common_message_hoteltimeoutorderapproval","您提交的酒店订单申请超时未被审批，订单已关闭，点击查看详情"),
    Common_Exception_ConfigTypeNotEmpty("saas.corelanguage.common_exception_configtypenotempty","配置类型不能为空"),
    Common_Message_TakeawayRejectNotice("saas.corelanguage.common_message_takeawayrejectnotice","【申请被驳回】您提交的{0}元的外卖申请被驳回，点击查看详情。"),
    Common_Message_MallFlowPassOrderNotice("saas.corelanguage.common_message_mallflowpassordernotice","【申请已通过】您提交的采购申请已通过，请在24小时内完成下单操作，点击查看详情"),
    Common_Exception_BizCategoryValueNotEmpty("saas.corelanguage.common_exception_bizcategoryvaluenotempty","bizCategory不能为空"),
    Common_Value_NumberText("saas.corelanguage.common_value_numbertext","数量"),
    Common_Exception_H5InvalidToAuthState("saas.corelanguage.common_exception_h5invalidtoauthstate","自主授权状态流转没有权限"),
    Common_Message_TrainTicketUpdatePerson("saas.corelanguage.common_message_trainticketupdateperson","您提交的火车票改签申请被驳回，点击查看详情"),
    Common_Message_AirForwardOrderApprovalNotice("saas.corelanguage.common_message_airforwardorderapprovalnotice","【审批提示】您提交的机票订单申请已审批至{0}，点击查看详情"),
    Common_Value_CheckLoading("saas.applylogaction.approval","审批中"),
    Common_Message_OrderScenePlace("saas.corelanguage.common_message_ordersceneplace","{0}预订了{1}的{2}"),
    Rule_Value_ApplyOrderName("saas.corelanguage.rule_value_applyordername","申请单名字"),
    Common_Value_UpgradeVersion("saas.corelanguage.common_value_upgradeversion","请升级至最新版本查看"),
    Rule_Value_DinnerCityAndAddress("saas.corelanguage.rule_value_dinnercityandaddress","用餐城市/送餐地址"),
    Common_Value_CheckDeleteForwardNext("saas.corelanguage.common_value_checkdeleteforwardnext","审批人被删除，跳至下一个审核"),
    Auth_Msg_Title_Confirm("saas.corelanguage.auth_msg_title_confirm","确认授权申请"),
    TaxiCacheConstant_Value_CustomCountIsNotMatch("saas.corelanguage.taxicacheconstant_value_customcountisnotmatch","自定义字段开启数量不匹配"),
    Rule_Value_GoAndBackText("saas.triptype.goandback","往返"),
    Rule_Value_CarUsedFee("saas.corelanguage.rule_value_carusedfee","用车费用"),
    Common_Message_PayApplyNoticeMsg("saas.corelanguage.common_message_payapplynoticemsg","{0}提交了付款申请，抄送给您，请知晓"),
    Common_Message_OrderStatusContent("saas.corelanguage.common_message_orderstatuscontent","您预订的{0}的订单状态更新为：{1}"),
    Hotel_Value_RoomPriceMsg("saas.corelanguage.hotel_value_roompricemsg","房间{0}：间夜价需低于{1}元"),
    Common_Exception_PleaseSelectCopyUser("saas.corelanguage.common_exception_pleaseselectcopyuser","请选择抄送人"),
    Common_Message_FlowFbqTimeoutDetailClosed("saas.corelanguage.common_message_flowfbqtimeoutdetailclosed","您提交的{0}张{1}分贝券申请超时未被审批，申请已关闭，点击查看详情"),
    Common_Message_IntlAirOrderRefundSuccess("saas.corelanguage.common_message_intlairorderrefundsuccess","您预订的{0}，{1}，预计{2}起飞，预计{3}到达，{4}，乘机人：{5} ,已退票成功。"),
    Common_Message_MallStoreChangMsg("saas.corelanguage.common_message_mallstorechangmsg","{0}提交了采购申请已通过，库存随时变化，请尽快审批"),
    Rule_Value_ChangeReason("saas.corelanguage.rule_value_changereason","变更事由"),
    Common_Exception_FlowNameRepeat("saas.corelanguage.common_exception_flownamerepeat","审批流名称重复"),
    TakeawayCheckServiceImpl_Value_OverContent("saas.CoreLanguage.TakeawayCheckServiceImpl_Value_OverContent","当前订单金额超出企业配置的消费金额上限，超出部分将由您自费承担：\n1.报销金额：{0}元\n2.自费金额：{1}元"),
    Rule_Value_CarQuotaUnion("saas.corelanguage.rule_value_carquotaunion","额度合并"),
    Common_Title_HasRevoke("saas.corelanguage.common_value_hasrevoke","已撤回"),
    Common_Exception_AirCompanyNoAuthMsg("saas.corelanguage.common_exception_aircompanynoauthmsg","您所在的公司不允许订购机票,具体情况请联系管理员"),
    Hotel_Value_RoomPriceOtherMsg("saas.corelanguage.hotel_value_roompriceothermsg","间夜价需低于{0}元"),
    Common_Message_AirSubmitOrderApplyNotice("saas.corelanguage.common_message_airsubmitorderapplynotice","【审批抄送】{0}提交了机票订单申请，请您知晓，点击查看详情"),
    Common_Message_ApplyAutoRefuse("saas.corelanguage.common_message_applyautorefuse","您的{0}申请被系统自动驳回"),
    Common_Message_IntlAirOrderChangedSuccess("saas.corelanguage.common_message_intlairorderchangedsuccess","您预订的{0}，{1}航班，已更改为{2}航班，{3}，预计{4}起飞，预计{5}到达，乘机人：{6}。"),
    TakeawayCheckServiceImpl_Value_DinnerSingleLess("saas.corelanguage.takeawaycheckserviceimpl_value_dinnersingleless","单次外卖金额需低于¥{0}"),
    Common_Value_OnlineBackTicketFour("saas.corelanguage.common_value_onlinebackticketfour","在线退票功能将在发车时刻前40分钟关闭，关闭后只能到始发站办理退票手续，请尽快完成审批"),
    Common_Exception_StartOrArriveCityIsEmpty("saas.corelanguage.common_exception_startorarrivecityisempty","行程出发/到达城市名称为空"),
    Common_Value_Other("saas.hotellevel.otherstar","其他"),
    Common_Message_TrainTicketUpdatePersonPass("saas.corelanguage.common_message_trainticketupdatepersonpass","您提交的火车票改签申请已通过，点击查看详情"),
    Common_Message_HotelOrderApprovalNotice("saas.corelanguage.common_message_hotelorderapprovalnotice","【审批通知】{0}提交了酒店订单申请，房量随时变化，请尽快审批"),
    TaxiCheckServiceImpl_Value_OverUpdateConfigOrApply("saas.corelanguage.taxicheckserviceimpl_value_overupdateconfigorapply","超出您{0}场景规则，请更换用车条件或使用申请用车"),
    Common_Message_LargeOverContent("saas.corelanguage.common_message_largeovercontent","大额消费：{0}在分贝通中提交了一笔{1}元的{2}订单，请关注。"),
    Common_Message_HotelTimeoutOrderApprovalNotice("saas.corelanguage.common_message_hoteltimeoutorderapprovalnotice","【申请已超时】您提交的酒店订单申请超时未被审批，订单已关闭，点击查看详情"),
    Common_Message_TrainTicketBackTimout("saas.corelanguage.common_message_trainticketbacktimout","您提交的火车票退票申请超时未被审批，申请单已关闭，点击查看详情"),
    Common_Message_DinnerCheckPassMsg("saas.corelanguage.common_message_dinnercheckpassmsg","{0}的用餐申请已通过"),
    Rule_Value_CarAdvanceBoardLimit("saas.corelanguage.rule_value_caradvanceboardlimit","提前上车限制"),
    Common_Exception_InvalidAuthState("saas.corelanguage.common_exception_invalidauthstate","变更授权状态不能为空"),
    Common_Exception_ApplyIdNotAllowEmpty("saas.corelanguage.common_exception_applyidnotallowempty","申请单id不能为空"),
    Common_Value_TimeCurrent("saas.corelanguage.common_value_timecurrent","刚刚"),
    Rule_Value_CarCityStartEnd("saas.corelanguage.rule_value_carcitystartend","起点和终点必须在同一城市"),
    Common_Exception_OrderDateTimeNotEmpty("saas.corelanguage.common_exception_orderdatetimenotempty","订单时间不能为空"),
    Common_Message_TrainRefuseOrderApprovalNotice("saas.corelanguage.common_message_trainrefuseorderapprovalnotice","【申请被驳回】您提交的火车票订单申请被驳回，点击查看详情"),
    Rule_Value_CarUsedMoney("saas.corelanguage.rule_value_carusedmoney","申请用车总额上限{0}元"),
    Common_Message_MallPassOrderApproval("saas.corelanguage.common_message_mallpassorderapproval","{0}提交了采购申请，库存随时变化，请尽快审批"),
    Common_Exception_FlowConfigError("saas.corelanguage.common_exception_flowconfigerror","审批流流程配置错误"),
    Common_Value_OtherMoneyApply("saas.applytypeappshowopte.petty","备用金申请"),
    Rule_Value_CarWriteApplyOrder("saas.corelanguage.rule_value_carwriteapplyorder","申请单填写"),
    TaxiCacheConstant_Value_SettingIgnored("saas.corelanguage.taxicacheconstant_value_settingignored","配置可忽略"),
    Common_Message_TrainTicketUpdate("saas.corelanguage.common_message_trainticketupdate","{0}提交了火车票改签申请，请您知晓，点击查看详情"),
    Rule_Value_Phone("saas.corelanguage.rule_value_phone","手机号"),
    Rule_Value_PredictFeePayUsed("saas.corelanguage.rule_value_predictfeepayused","预估费用由申请人填写，为本次填写预估费用的行程的可支付费用上限"),
    Rule_Value_PriceNeedLess("saas.corelanguage.rule_value_priceneedless","需低于¥{0}元"),
    Hotel_Value_LevelNotEnoughRule("saas.corelanguage.hotel_value_levelnotenoughrule","酒店类型（{0}星级）不符合规则"),
    Common_Message_TrainOrderTicketed("saas.corelanguage.common_message_trainorderticketed","您预订的{0}，{1}次，发车时间：{2}，乘车人：{3}，{4}，{5}，取票号：{6}。"),
    Rule_Value_ApplyTime("saas.corelanguage.rule_value_applytime","申请时间"),
    TaxiCacheConstant_Value_InternalException("saas.corelanguage.taxicacheconstant_value_internalexception","发生异常"),
    Common_Message_AcctPublicRemindNoticeContent("saas.corelanguage.common_message_acctpublicremindnoticecontent","尊敬的用户，您好。截止到{0}，您公司对公付款场景的{1}尾号为{2}账户余额为{3}元，已小于您设置的告警阈值，为不影响员工正常使用，建议您及时充值。"),
    Common_Message_AcctOverseaRemindNoticeContent("saas.corelanguage.common_message_acctoversearemindnoticecontent","尊敬的用户，您好。截止到{0}，{1}的{2}海外卡账户余额为{3}，已小于您设置的告警阈值，为不影响员工正常使用，建议您及时充值。"),
    Common_Message_AcctOverseaLargeOverRemindContent("saas.corelanguage.common_message_acctoversealargeoverremindcontent","您好，{0}持有的尾号为{1}的海外卡，在{2}发生一笔金额为{3}{4}的交易，已高于您企业设定的大额异动提醒值"),
    Common_Message_AcctOverseaConsumeRemindContent("saas.corelanguage.common_message_acctoverseaconsumeremindcontent","您好，{0}持有的尾号为{1}的海外卡，在{2}发生一笔金额为{3}{4}的交易"),
    Common_Message_LargeOverTitle("saas.corelanguage.common_message_largeovertitle","[大额消费]{0}提交了一笔{1}元的{2}订单。"),
    Common_Message_HotelForwardOrderApprovalNotice("saas.corelanguage.common_message_hotelforwardorderapprovalnotice","【审批提示】您提交的酒店订单申请已审批至{0}，点击查看详情"),
    Common_Message_FlowApproveMsg("saas.corelanguage.common_message_flowapprovemsg","{0}同意了您提交的{1}申请，请尽快导出并打印申请单"),
    Rule_Value_ChineseUnit("saas.corelanguage.rule_value_chineseunit","{0}元"),
    Common_Value_OverPermissionQuota("saas.corelanguage.common_value_overpermissionquota","超出权限额度"),
    Common_Value_TrainBackApply("saas.biztypemessage.trainrefundapply","火车票退票申请"),
    Rule_Value_TimePeriodSetting("saas.corelanguage.rule_value_timeperiodsetting","时间周期设置"),
    Common_Message_MallDetailNotice("saas.corelanguage.common_message_malldetailnotice","【审批抄送】{0}提交的采购申请已通过，请您知晓，点击查看详情"),
    Common_Exception_SearchCityListException("saas.corelanguage.common_exception_searchcitylistexception","查询城市信息列表异常"),
    Rule_Value_OverDinner("saas.biztypemessage.overrulemealapply","超规用餐"),
    Common_Message_DinnerApplyRefuse("saas.corelanguage.common_message_dinnerapplyrefuse","您提交的用餐申请被驳回，点击查看详情"),
    Common_Exception_CarTypeNotEmpty("saas.corelanguage.common_exception_cartypenotempty","用车类型不能为空"),
    Common_Message_AirBackFlow("saas.corelanguage.common_message_airbackflow","{0}提交了机票退票申请，请尽快完成审批"),
    Common_Value_AirChangeApply("saas.biztypemessage.airplanechangeapply","国内机票改期申请"),
    Common_Exception_StateNotSupport("saas.corelanguage.common_exception_statenotsupport","该自主授权状态变更尚未支持"),
    Common_Exception_OtherServerBackResultIsEmpty("saas.corelanguage.common_exception_otherserverbackresultisempty","调用外部接口返回数据为空"),
    Common_Message_DinnerFeeApplyNotice("saas.corelanguage.common_message_dinnerfeeapplynotice","【审批通知】{0}提交了用餐费用申请,请您在{1}前完成审批，点击查看详情"),
    ExcelApplyFlowUtil_Value_CcItemName("saas.corelanguage.excelapplyflowutil_value_ccitemname","抄送人名称"),
    Common_Exception_NameValueNotEmpty("saas.corelanguage.common_exception_namevaluenotempty","name不能为空"),
    TrainCheckServiceImpl_Value_HighTrain("saas.corelanguage.traincheckserviceimpl_value_hightrain","高铁动车"),
    Rule_Value_TravleAndUsed("saas.corelanguage.rule_value_travleandused","出行人/使用人"),
    Rule_Value_AirIntlOrder("saas.reasontype.order_intl_air","国际机票订单"),
    Rule_Value_TrainNormalOrder("saas.corelanguage.rule_value_trainnormalorder","与普通预订时一致"),
    Rule_Value_UsedCall("saas.corelanguage.rule_value_usedcall","可呼叫{0}"),
    Common_Message_CarCheckMsg("saas.corelanguage.common_message_carcheckmsg","{0}的用车申请待审核"),
    Common_Message_HotelPassOrderApplyNotice("saas.corelanguage.common_message_hotelpassorderapplynotice","【审批抄送】{0}提交了酒店订单申请已通过，请您知晓，点击查看详情"),
    Common_Exception_PettyAutoRepulse_msg("saas.corelanguage.common_exception_pettyautorepulse_msg","企业已切换虚拟卡使用模式，请重新提交申请。"),
    Order_Value_SubmitResult("saas.corelanguage.order_value_submitresult","{0}{1}业务{2}限额{3}元"),
    TaxiCacheServiceImpl_Value_CustomArchives("saas.costattributioncategory.record","自定义档案"),
    TrainCheckServiceImpl_Value_OrderNeedCheck("saas.corelanguage.traincheckserviceimpl_value_orderneedcheck","订单需审批"),
    Hotel_Value_RoomText("saas.corelanguage.hotel_value_roomtext","房间"),
    Common_Message_DinnerCheckRefuse("saas.corelanguage.common_message_dinnercheckrefuse","{0}驳回了您的用餐申请。"),
    Order_Value_CheckMonth("saas.corelanguage.order_value_checkmonth","当月"),
    Rule_Value_TripTravelText("saas.corelanguage.rule_value_triptraveltext","差旅行程"),
    Rule_Value_CheckStatus("saas.corelanguage.rule_value_checkstatus","审批状态"),
    Custom_Exception_ReasonMaxTwenty("saas.corelanguage.custom_exception_reasonmaxtwenty","事由内容项最多支持20个"),
    Rule_Value_OverQuotaUserFee("saas.corelanguage.rule_value_overquotauserfee","超额部分需个人自费"),
    Common_Exception_AuthRecordIsEmpty("saas.corelanguage.common_exception_authrecordisempty","查询不到授权记录"),
    Order_Value_MsgPersonOrder("saas.corelanguage.order_value_msgpersonorder","您预订的{0}{1}"),
    Common_Exception_RequestDataIsEmpty("saas.corelanguage.common_exception_requestdataisempty","请求参数为空"),
    Rule_Value_ChineseSignBasic("saas.corelanguage.rule_value_chinesesignbasic","¥{0}"),
    TakeawayCheckServiceImpl_Value_OverDinnerMore("saas.corelanguage.takeawaycheckserviceimpl_value_overdinnermore","用餐人中的{0}等{1}人差标超规"),
    Common_Message_FlowSubmitApplyMsg("saas.corelanguage.common_message_flowsubmitmsg","{0}提交了{1}申请，请审核"),
    Rule_Value_TripRoadChangeText("saas.corelanguage.rule_value_triproadchangetext","差旅行程变更"),
    Common_Value_DinnerNum("saas.corelanguage.common_value_dinnernum","{0}人用餐"),
    Rule_Value_CarUsedPosition("saas.corelanguage.rule_value_carusedposition","用车位置"),
    Common_Message_AcctIndividualRemindNoticeContent("saas.corelanguage.common_message_acctindividualremindnoticecontent","尊敬的用户，您好。截止到{0}，您公司福利补助场景的生效账户余额为{1}元，已小于您设置的告警阈值，为不影响员工正常使用，建议您及时{2}。"),
    AirUtils_Exception_CityInfo("saas.corelanguage.airutils_exception_cityinfo","获取城市信息异常，请稍后"),
    Common_Message_IntlAirOrderRefundFail("saas.corelanguage.common_message_intlairorderrefundfail","您预订的{0}，{1}，预计{2}起飞，预计{3}到达，{4}航班，乘机人：{5}，退票失败。"),
    Common_Message_TrainBackNotice("saas.corelanguage.common_message_trainbacknotice","【审批抄送】{0}提交了机票退订申请，请您知晓，点击查看详情"),
    Order_Value_RejectSubmit("saas.corelanguage.order_value_rejectsubmit","您本次提交订单，{0}{1}业务{2}{3}元限额，不可继续提交"),
    Common_Exception_BusCompanyNoAuthMsg("saas.corelanguage.common_exception_buscompanynoauthmsg","您所在的公司不允许订购汽车票,具体情况请联系管理员"),
    SelfAuthorizeServiceImpl_Value_Dinner("saas.ruletype.diner","用餐"),
    Order_Value_TravelOrder("saas.corelanguage.order_value_travelorder","{0}到{1}"),
    Rule_Value_ChangeRemark("saas.corelanguage.rule_value_changeremark","变更说明"),
    Custom_Exception_ProofConfig("saas.corelanguage.custom_exception_proofconfig","凭证信息配置"),
    Common_Message_TrainTicketBackTimeoutNotice("saas.corelanguage.common_message_trainticketbacktimeoutnotice","【申请已超时】您提交的火车票退票申请超时未被审批，退票申请已关闭，点击查看详情"),
    Common_Message_AirPassOrderApprovalNotice("saas.corelanguage.common_message_airpassorderapprovalnotice","【审批已通过】您提交的机票订单申请已通过，点击查看详情"),
    Rule_Value_CarUsedTimeRange("saas.corelanguage.rule_value_carusedtimerange","用车时段"),
    Common_Exception_OperateApplyListNotEmpty("saas.corelanguage.common_exception_operateapplylistnotempty","操作申请单列表不可为空"),
    Rule_Value_Fbq("saas.biztype.fenbeicoupon","分贝券"),
    Rule_Value_OtherNotSubsidy("saas.corelanguage.rule_value_othernotsubsidy","另有¥{0}超出补贴上限无法补贴"),
    Common_Message_DinnerFeeSubmitOrderApplyNotice("saas.corelanguage.common_message_dinnerfeesubmitorderapplynotice","【审批抄送】{0}提交了用餐费用申请，请您知晓，点击查看详情"),
    Common_Message_MallStoreForwardNotice("saas.corelanguage.common_message_mallstoreforwardnotice","【审批提示】您提交的采购申请已审批至{0}，点击查看详情"),
    Order_Value_MsgAmount("saas.corelanguage.order_value_msgamount","消费金额"),
    Common_Message_HotelSubmitOrderApplyNotice("saas.corelanguage.common_message_hotelsubmitorderapplynotice","【审批抄送】{0}提交了酒店订单申请，请您知晓，点击查看详情"),
    TaxiCacheConstant_Value_CacheIsNotMatch("saas.corelanguage.taxicacheconstant_value_cacheisnotmatch","{0}缓存信息与当前配置不匹配"),
    Common_Exception_TitleFeeBelong("saas.corelanguage.common_exception_titlefeebelong","请修改费用归属"),
    Common_Message_CarCheckPassMsg("saas.corelanguage.common_message_carcheckpassmsg","{0}的用车申请已通过"),
    Rule_Value_HotelSecondRoomFeeAverageLess("saas.corelanguage.rule_value_hotelsecondroomfeeaverageless","二线城市平均每晚房费需低于{0}元\n"),
    Order_Value_MsgBusOrder("saas.corelanguage.order_value_msgbusorder","您预定的{0}前往{1}的{2}"),
    Order_Value_MsgPersonTakeaway("saas.corelanguage.order_value_msgpersontakeaway","您的美食订单{0}"),
    Rule_Value_FeeBelongName("saas.corelanguage.rule_value_feebelongname","费用归属名称"),
    SelfAuthorizeServiceImpl_Value_Permanent("saas.corelanguage.selfauthorizeserviceimpl_value_permanent","永久"),
    Rule_Value_FullText("saas.corelanguage.rule_value_fulltext","全"),
    Common_Exception_MallOrderResultError("saas.corelanguage.common_exception_mallorderresulterror","采购创建订单返回结果异常"),
    Common_Message_CarSubmitOrderApply("saas.corelanguage.common_message_carsubmitorderapply","{0}提交了用车申请，请您知晓，点击查看详情"),
    Common_Value_Allow("saas.corelanguage.common_value_allow","允许"),
    Common_Message_VirtualCardNoticeMsg("saas.corelanguage.common_message_virtualcardnoticemsg","{0}提交的虚拟卡额度申请，抄送给您，请知晓"),
    Common_Value_TripHasRepeatOrderUsed("saas.corelanguage.common_value_triphasrepeatorderused","{0}在该申请单下存在重复的可用订单，可用订单为使用此申请单正在出票和出票成功的订单。"),
    Rule_Value_OverQuotaPay("saas.corelanguage.rule_value_overquotapay","超出部分将需要您进行个人支付。"),
    Rule_Value_HotelOtherRoomFeeAverageLess("saas.corelanguage.rule_value_hotelotherroomfeeaverageless","其他城市平均每晚房费需低于{0}元\n"),
    Common_Exception_UserListIsEmpty("saas.corelanguage.common_exception_userlistisempty","用户列表为空"),
    Common_Message_AirTicketBackPass("saas.corelanguage.common_message_airticketbackpass","{0}提交的机票退订申请已通过，请您知晓，点击查看详情"),
    Order_Message_FbtConsumerReminder("saas.corelanguage.order_message_fbtconsumerreminder","【分贝通】【大额消费提醒】{0}的{1}提交了一笔{2}元的{3}订单。"),
    Common_Message_AirTicketBackPersonRefuseNotice("saas.corelanguage.common_message_airticketbackpersonrefusenotice","【申请被驳回】您提交的机票退订申请被驳回，点击查看详情"),
    Common_Exception_MallEmployeeNoAuthMsg("saas.corelanguage.common_exception_mallemployeenoauthmsg","您尚未开通采购权限,请联系管理员为您开通"),
    Common_Value_AfterDays("saas.corelanguage.common_value_afterdays","至多提前{0}天"),
    Rule_Value_CityLineDistanceGreater("saas.corelanguage.rule_value_citylinedistancegreater","城市直线距离需大于{0}公里"),
    Common_Exception_ValueNotCorrect("saas.corelanguage.common_exception_valuenotcorrect","值不正确"),
    Common_Value_ActionForward("saas.corelanguage.common_value_actionforward","转交"),
    Order_Value_ApplicateDayToNightLimit("saas.globalresponsecode.orderchecktrainruledaylimitmsg","申请人违反了「夕发朝至」的限制"),
    Common_Exception_CirculationEventNotEmpty("saas.corelanguage.common_exception_circulationeventnotempty","流转事件不能为空"),
    Common_Message_AitTimeoutOrderApproval("saas.corelanguage.common_message_aittimeoutorderapproval","您提交的机票订单申请超时未被审批，订单已关闭，点击查看详情"),
    Common_Message_HotelBackTimeout("saas.corelanguage.common_message_hotelbacktimeout","您提交的酒店退订申请超时未被审批，申请单已关闭，点击查看详情"),
    Common_Message_MallStoreChangeNotice("saas.corelanguage.common_message_mallstorechangenotice","【审批通知】{0}提交了采购申请，库存随时变化，请尽快审批"),
    Rule_Value_CarDispatchingFee("saas.corelanguage.rule_value_cardispatchingfee","调度费"),
    ExcelApplyFlowUtil_Value_ApplyItemName("saas.corelanguage.excelapplyflowutil_value_applyitemname","审批人名称"),
    Rule_Value_Desc("saas.globalresponsecode.ordercheckairminpricenoauth","限起飞前后{0}小时内最低价{1}"),
    Rule_Value_SingleTripLess("saas.corelanguage.rule_value_singletripless","单程预估价需低于¥{0}"),
    Common_Message_AirBackFlowNotice("saas.corelanguage.common_message_airbackflownotice","【审批通知】{0}提交了机票退改申请，错过最佳退票时间点可能产生较高退票手续费，请尽快完成审批"),
    Common_Value_HotelRuleNotBack("saas.corelanguage.common_value_hotelrulenotback","超过酒店取消规则时间后无法退房，请尽快完成审批"),
    Custom_Exception_TravelAccount("saas.corelanguage.custom_exception_travelaccount","差旅统计"),
    Common_Exception_EmployeeIdsValueNotEmpty("saas.corelanguage.common_exception_employeeidsvaluenotempty","employeeIds不能为空"),
    Common_Value_TimeSecondBefore("saas.corelanguage.common_value_timesecondbefore","分钟前"),
    Common_Exception_ThirdServer("saas.corelanguage.common_exception_thirdserver","调用第三方接口异常，请稍后重试"),
    Custom_Exception_DepartureDate("saas.corelanguage.custom_exception_departuredate","出发日期"),
    Common_Value_Department("saas.entitytype.department","部门"),
    Common_Value_BudgetQuotaNotEnough("saas.globalresponsecode.orderchecktaxiavailablebudgetexceed","预算余额不足"),
    Rule_Value_ChineseSign("saas.corelanguage.rule_value_chinesesign","¥{0}元"),
    Auth_Msg_TimeExpired("saas.corelanguage.auth_msg_timeexpired","尊敬的用户，您授权给{0}{1}商务消费场景，在{2}-{3}时间内的操作权限，授权时间已过期，授权申请已失效。"),
    Common_Message_FlowRefuse("saas.corelanguage.common_message_flowrefuse","{0}驳回了您的{1}申请"),
    Common_Value_NotSetStartAndEndCity("saas.corelanguage.common_value_notsetstartandendcity","未设置起止城市"),
    Auth_Msg_Title_DelayAuth("saas.corelanguage.auth_msg_title_delayauth","延期授权申请"),
    Rule_Value_TrainOrderApply("saas.corelanguage.rule_value_trainorderapply","火车票订单申请"),
    Common_Message_MallCheckMsg("saas.corelanguage.common_message_mallcheckmsg","{0}的采购申请待审核"),
    Rule_Value_HotelType("saas.corelanguage.rule_value_hoteltype","酒店类型"),
    Rule_Value_DinnerNum("saas.corelanguage.rule_value_dinnernum","用餐人数"),
    Common_Message_IntlAirOrderTicketSuccess("saas.corelanguage.common_message_intlairorderticketsuccess","您预订的{0}，{1}，预计{2}起飞，预计{3}到达，{4}，乘机人：{5}。"),
    Common_Message_TrainOrderApproval("saas.corelanguage.common_message_trainorderapproval","{0}提交了火车票订单申请，票量随时变化，请尽快审批"),
    OrderService_Exception_GetTripUserOrderFail("saas.corelanguage.orderservice_exception_gettripuserorderfail","获取出行人订单信息异常"),
    Common_Exception_TrainOrderNeedBackTicket("saas.corelanguage.common_exception_trainorderneedbackticket","火车订单需要回票"),
    Order_Value_ConfirmSubmit("saas.corelanguage.order_value_confirmsubmit","您本次提交订单，{0}{1}业务{2}{3}元限额，根据本企业设置限额超出部分将由个人自费承担无法报销，是否继续提交？"),
    Common_Exception_OrderIdNotEmpty("saas.corelanguage.common_exception_orderidnotempty","订单id不能为空"),
    Rule_Value_HotelRoomLimit("saas.corelanguage.rule_value_hotelroomlimit","房费限制"),
    Common_Exception_ProjectNoExsits("saas.corelanguage.common_exception_projectnoexsits","项目不存在:"),
    Common_Value_ContactAdmin("saas.corelanguage.common_value_contactadmin","请联系管理员设置"),
    Common_Value_TakeawayApply("saas.startapplylisttype.takeawayorder","外卖申请"),
    TaxiCacheServiceImpl_Value_OrderNotice("saas.reasonordercategory.order_message","订单通知人"),
    Common_Exception_NotEmpty("saas.corelanguage.common_exception_notempty","不能为空"),
    Rule_Value_HotelRoomFeeAverageLess("saas.corelanguage.rule_value_hotelroomfeeaverageless","平均每晚房费需低于{0}元"),
    Common_Message_MallPassOrderApprovalNotice("saas.corelanguage.common_message_mallpassorderapprovalnotice","【申请已通过】您提交的采购申请已通过，并已提交采购订单，点击查看申请单详情"),
    Common_Message_AirRefuseOrderApproval("saas.corelanguage.common_message_airrefuseorderapproval","您提交的机票订单申请被驳回，点击查看详情"),
    TaxiCacheConstant_Value_ContentTypeIsNotMatch("saas.corelanguage.taxicacheconstant_value_contenttypeisnotmatch","自定义文本类型不匹配"),
    Rule_Value_DinnerTimeRange("saas.corelanguage.rule_value_dinnertimerange","用餐时段"),
    Rule_Value_ApplicantLevelDepartment("saas.corelanguage.rule_value_applicantleveldepartment","申请人层级部门"),
    Common_Message_AirPassOrderApproval("saas.corelanguage.common_message_airpassorderapproval","您提交的机票订单申请已通过，点击查看详情"),
    Common_Message_TrainBackFlowNotice("saas.corelanguage.common_message_trainbackflownotice","【审批通知】{0}提交了火车票退票申请，火车票在{1}后无法完成在线退票，请尽快完成审批"),
    Common_Exception_EmployeeIdNotEmpty("saas.corelanguage.common_exception_employeeidnotempty","员工id不能为空"),
    SelfAuthorizeServiceImpl_Value_LongReserve("saas.corelanguage.selfauthorizeserviceimpl_value_longreserve","永久预定权限"),
    Common_Exception_NotSelected("saas.corelanguage.common_exception_notselected","不展示及选填状态下应为置灰，不可勾选"),
    Common_Message_MallDetail("saas.corelanguage.common_message_malldetail","{0}提交了采购申请，请您知晓，点击查看详情"),
    Common_Message_OrderContent("saas.corelanguage.common_message_ordercontent","您预订的{0}"),
    Common_Message_HotelBackForwardNotice("saas.corelanguage.common_message_hotelbackforwardnotice","【审批提示】您提交的酒店退订申请已审批至{0}，点击查看详情"),
    Rule_Value_InvalidReason("saas.corelanguage.rule_value_invalidreason","作废事由"),
    Rule_Value_HotelSecondRoomFeeNotLimit("saas.corelanguage.rule_value_hotelsecondroomfeenotlimit","二线城市无限制\n"),
    Common_Value_OnlineChangeTicket("saas.corelanguage.common_value_onlinechangeticket","在线改签功能将在发车时刻前35分钟关闭，关闭后只能到始发站办理改签手续，请尽快完成审批"),
    Common_Title_Timeout("saas.applylogaction.overtime","超时"),
    Common_Message_TrainOrderEndorseErr("saas.corelanguage.common_message_trainorderendorseerr","您预订的{0}，{1}，{2}列车，乘车人：{3}，座席：{4}，票号：{5}，改签为{6}，{7}，{8}列车，座席：{9}，改签失败。"),
    Common_Exception_ReasonNotExist("saas.corelanguage.common_exception_reasonnotexist","事由不存在"),
    Common_Value_ApprovalListCorporatePayment("saas.corelanguage.common_value_approvallistcorporatepayment","对公付款申请单"),
    Common_Message_TaxiOrderFinishTtile("saas.corelanguage.common_message_taxiorderfinishttile","用车-订单已完成"),
    Common_Message_ByjNoticeMsg("saas.corelanguage.common_message_byjnoticemsg","{0}提交的备用金申请，抄送给您，请知晓"),
    Common_Exception_ConfigTypeError("saas.corelanguage.common_exception_configtypeerror","配置类型错误"),
    Rule_Value_CarApplyAllQuota("saas.corelanguage.rule_value_carapplyallquota","您的申请用车总额上限为{0}元"),
    MessageKafkaWebCustomerService_Exception_SaveCompanyMessageExistObjectIsEmpty("saas.corelanguage.messagekafkawebcustomerservice_exception_savecompanymessageexistobjectisempty","保存企业Web消息存在参数为空"),
    Common_Message_TrainTicketBackNotice("saas.corelanguage.common_message_trainticketbacknotice","【审批抄送】{0}提交了火车票退订申请，请您知晓，点击查看详情"),
    Common_Message_AirOrderApproval("saas.corelanguage.common_message_airorderapproval","{0}提交了机票订单申请，为避免变价或无票，请尽快审批"),
    Order_Value_ProjectCenterBudget("saas.corelanguage.order_value_projectcenterbudget","项目中心预算"),
    Common_Value_NoDepartmentInfo("saas.corelanguage.common_value_nodepartmentinfo","无部门信息"),
    Common_Exception_UsedCarRuleNotEmpty("saas.corelanguage.common_exception_usedcarrulenotempty","用车规则信息不能为空"),
    Auth_Msg_Title_ApplyAuth("saas.corelanguage.auth_msg_title_applyauth","申请授权"),
    Common_Message_TakeawayNeedCheck("saas.corelanguage.common_message_takeawayneedcheck","{0}提交了外卖申请，请审核。"),
    Common_Value_ReasonContent("saas.corelanguage.common_value_reasoncontent","事由内容"),
    Common_Message_HotelBackFlow("saas.corelanguage.common_message_hotelbackflow","{0}提交了酒店退订申请，请尽快完成审批"),
    Common_Exception_TravelCityNotCorrect("saas.corelanguage.common_exception_travelcitynotcorrect","差旅城市值不正确"),
    Rule_Value_SingleTicketPrice("saas.corelanguage.rule_value_singleticketprice","单张票价"),
    Common_Title_WaitCheck("saas.applyflowuseritemstatus.pendingaudit","待审核"),
    Rule_Value_MallSkuLimit("saas.corelanguage.rule_value_mallskulimit","SKU限制"),
    Order_Value_ApplicateToggleLimit("saas.globalresponsecode.ordercheckaircabintypenoauth","申请人违反了「只可预订{0}」的限制"),
    Rule_Exception_CarApplyOrderCategory("saas.corelanguage.rule_exception_carapplyordercategory","用车申请单费用归属"),
    Common_Message_CouponSendFailContent("saas.corelanguage.common_message_couponsendfailcontent","尊敬的用户您好，您的企业{0}分贝券发放失败，您可检查企业个人账户余额是否充足，充值后请进入商户后台重新操作发放。"),
    Common_Message_TrainTicketUpdatePass("saas.corelanguage.common_message_trainticketupdatepass","{0}提交的火车票改签申请已通过，请您知晓，点击查看详情"),
    Common_Value_PublicPayApply("saas.applytypeappshowopte.payment","对公付款申请"),
    Common_Message_HotelPassOrderApproval("saas.corelanguage.common_message_hotelpassorderapproval","您提交的酒店订单申请已通过，点击查看详情"),
    Rule_Value_CarOccupyPerson("saas.corelanguage.rule_value_caroccupyperson","占用个人预算"),
    Custom_Exception_CarPersonNotEmpty("saas.corelanguage.custom_exception_carpersonnotempty","乘车人不能为空"),
    TrainCheckServiceImpl_Value_MaxDay("saas.globalresponsecode.ordercheckmostday","最多提前{0}天预订"),
    Common_Message_TrainTicketBackPass("saas.corelanguage.common_message_trainticketbackpass","{0}提交的火车票退订申请已通过，请您知晓，点击查看详情"),
    Common_Message_TaxiOrderTimeoutTile("saas.corelanguage.common_message_taxiordertimeouttile","用车-订单已关闭"),
    Common_Message_DinnerCheckMsg("saas.corelanguage.common_message_dinnercheckmsg","{0}的用餐申请待审核"),
    Rule_Value_CarDayQuota("saas.corelanguage.rule_value_cardayquota","您的单日用车上限为{0}元"),
    Common_Exception_HotelEmployeeNoAuthMsg("saas.globalresponsecode.hotelcheckemployeenoauth","您尚未开通国内酒店权限，请联系管理员为您开通"),
    Common_Exception_IntlHotelEmployeeNoAuthMsg("saas.globalresponsecode.hotelcheckemployeenoauth","您尚未开通海外酒店权限，请联系管理员为您开通"),
    Rule_Value_ManyFrequency("saas.corelanguage.rule_value_manyfrequency","多次"),
    Order_Value_SubmitOverLimit("saas.corelanguage.order_value_submitoverlimit","下单时所支付费用为此行程支付上限"),
    Common_Value_DepartmentLeader("saas.defaultrole.departmentheads","部门主管"),
    TaxiCacheServiceImpl_Value_ReasonAddConfig("saas.corelanguage.taxicacheserviceimpl_value_reasonaddconfig","事由补充配置"),
    Custom_Exception_PredictCost("saas.corelanguage.custom_exception_predictcost","预估费用"),
    Common_Message_TrainTicketUpdatePassNotice("saas.corelanguage.common_message_trainticketupdatepassnotice","【审批抄送】{0}提交的火车票改签申请已通过，请您知晓，点击查看详情"),
    Common_Message_CouponSendFailTitle("saas.messagewebsubtype.couponsendfail","分贝券发券失败通知"),
    Common_Exception_ThirdBudgetNotEnough("saas.corelanguage.common_exception_thirdbudgetnotenough","三方对接校验预算不足"),
    Order_Value_ApplicateLowerOrderLimit("saas.corelanguage.order_value_applicatelowerorderlimit","申请人违反了「最少提前{0}天预订」的限制"),
    Common_Message_DinnerApplyNotice("saas.corelanguage.common_message_dinnerapplynotice","{0}提交了用餐申请,请您在{1}前完成审批"),
    Common_Exception_ApplySettingTypeError("saas.corelanguage.common_exception_applysettingtypeerror","审批流类型错误"),
    Rule_Value_InvalidRemark("saas.corelanguage.rule_value_invalidremark","作废说明"),
    Common_Message_AirBackTimeout("saas.corelanguage.common_message_airbacktimeout","您提交的机票退票申请超时未被审批，申请单已关闭，点击查看详情"),
    Auth_Msg_Reject("saas.corelanguage.auth_msg_reject","尊敬的用户，您向授权人{0}，申请的{1}商务消费场景，在{2}-{3}时间内的操作权限被授权人{4}驳回了。"),
    Rule_Value_OverQuotaUserPay("saas.corelanguage.rule_value_overquotauserpay","超额部分需个人支付"),
    TaxiCacheConstant_Exception_UserInfoIsEmpty("saas.corelanguage.taxicacheconstant_exception_userinfoisempty","用户信息参数为空"),
    Common_Message_DinnerNeedCheck("saas.corelanguage.common_message_dinnerneedcheck","{0}提交了用餐申请，请审核。"),
    Common_Exception_EmployeeIdValueNotEmpty("saas.corelanguage.common_exception_employeeidvaluenotempty","employeeId不能为空"),
    AirUtils_Exception_TrainTime("saas.corelanguage.airutils_exception_traintime","获取火车时长异常，请稍后"),
    Rule_Value_TrainBackApply("saas.corelanguage.rule_value_trainbackapply","火车退订申请"),
    Rule_Value_PersonPay("saas.actionbuttionenum.personalpay","个人支付"),
    Common_Message_DinnerApplyPass("saas.corelanguage.common_message_dinnerapplypass","您提交的用餐申请已通过，点击查看详情"),
    Common_Value_Budget("saas.corelanguage.common_value_budget","预算"),
    Common_Exception_MallOrderIdNotEmpty("saas.corelanguage.common_exception_mallorderidnotempty","采购订单id为空"),
    Custom_Exception_ReasonAddRequired("saas.corelanguage.custom_exception_reasonaddrequired","事由补充必填"),
    Common_Value_TripHasRepeatOrderHotelUsed("saas.corelanguage.common_value_triphasrepeatorderhotelused","{0}在该申请单下存在重复的可用订单，可用订单为使用此申请单正在订房和订房成功的订单。"),
    Common_Message_TrainTicketBackRefuseNotice("saas.corelanguage.common_message_trainticketbackrefusenotice","【申请被驳回】您提交的火车票退订申请被驳回，点击查看详情"),
    Rule_Value_OtherPlace("saas.corelanguage.rule_value_otherplace","[其他地区，¥{0}]"),
    Common_Value_CityNextText("saas.corelanguage.common_value_citynexttext","县"),
    Common_Message_DinnerApplyTimeoutNotice("saas.corelanguage.common_message_dinnerapplytimeoutnotice","【申请已超时】您提交的用餐费用申请超时未被审批，订单已关闭，点击查看详情"),
    Common_Message_AcctIndividualRemindNoticeTitle("saas.corelanguage.common_message_acctindividualremindnoticetitle","福利补助账户余额不足"),
    Auth_Msg_SubmitDelayApplication("saas.corelanguage.auth_msg_submitdelayapplication","尊敬的用户，{0}向您申请{1}商务消费场景的授权延期，延期期限{2}-{3}，{4}请您及时审批。"),
    Order_Value_MsgPersonApplyOrder("saas.corelanguage.order_value_msgpersonapplyorder","您的{0}订单{1}"),
    Common_Exception_CompanyInfoNotEmpty("saas.corelanguage.common_exception_companyinfonotempty","企业信息不可为空"),
    Rule_Exception_CompanyNotOpenTakeawayServer("saas.globalresponsecode.dinnerjurisdictionerror","您的企业尚未开启用餐业务，如有需要请联系分贝通客服"),
    Common_Message_TrainTicketBackPassNotice("saas.corelanguage.common_message_trainticketbackpassnotice","【审批抄送】{0}提交的火车票退订申请已通过，请您知晓，点击查看详情"),
    Common_Value_AirIntlApply("saas.biztypemessage.intlairplaneapply","国际机票申请"),
    Common_Exception_TakeawayRuleNotFound("saas.corelanguage.common_exception_takeawayrulenotfound","未查到外卖规则"),
    Rule_Value_OverReason("saas.corelanguage.rule_value_overreason","超规理由"),
    Rule_Value_CarCurrentDayMOney("saas.corelanguage.rule_value_carcurrentdaymoney","单日日均用车上限{0}元，申请单可用额度{1}元"),
    Common_Value_VirtualCardApply("saas.applytypeappshowopte.bankindividual","虚拟卡额度申请"),
    AirUtils_Value_CityLineDistance("saas.corelanguage.airutils_value_citylinedistance","城市直线距离在{0}公里内，请乘坐火车"),
    Rule_Value_CarRejectCallOther("saas.corelanguage.rule_value_carrejectcallother","禁止为他人叫车"),
    Common_Message_TakeawayCheckPassMsg("saas.corelanguage.common_message_takeawaycheckpassmsg","{0}的外卖申请已通过"),
    Rule_Value_NoLimit("saas.corelanguage.rule_value_nolimit","不限制"),
    Common_Message_TrainTicketChangeNotice("saas.corelanguage.common_message_trainticketchangenotice","【审批通知】{0}提交了火车票改签申请，火车票在{1}后无法完成在线改签，请尽快完成审批"),
    Common_Exception_RoleNoExsits("saas.corelanguage.common_exception_rolenoexsits","角色不存在:"),
    Common_Message_AirTicketBackPersonRefuse("saas.corelanguage.common_message_airticketbackpersonrefuse","您提交的机票退订申请被驳回，点击查看详情"),
    Common_Value_ManagerText("saas.corelanguage.common_value_managertext","主管"),
    Common_Message_CompanyUsedCarTitle("saas.corelanguage.common_message_companyusedcartitle","企业用车"),
    Order_Value_MsgCompanyNotice("saas.corelanguage.order_value_msgcompanynotice","企业有新的{0}消费"),
    Common_Message_TravelCheckMsg("saas.corelanguage.common_message_travelcheckmsg","{0}的出差申请待审核"),
    TaxiCheckServiceImpl_Value_OverUpdateConfig("saas.corelanguage.taxicheckserviceimpl_value_overupdateconfig","超出您{0}场景规则，请更换用车条件"),
    ExcelApplyFlowUtil_Value_CcItemType("saas.corelanguage.excelapplyflowutil_value_ccitemtype","抄送人类型"),
    Rule_Value_OrderInfo("saas.corelanguage.rule_value_orderinfo","订单信息"),
    Common_Exception_GetAuthFailWithInfo("saas.corelanguage.common_exception_getauthfailwithinfo","获取权限失败（{0})"),
    Common_Message_TakeawayCheckMg("saas.corelanguage.common_message_takeawaycheckmg","{0}的外卖申请待审核"),
    Common_Exception_CcNoticeTypeError("saas.corelanguage.common_exception_ccnoticetypeerror","通知抄送人类型不正确"),
    Common_Message_TrainPassOrderApprovalNotice("saas.corelanguage.common_message_trainpassorderapprovalnotice","【申请已通过】您提交的火车票订单申请已通过，点击查看详情"),
    Common_Value_HotelChangeApply("saas.corelanguage.common_value_hotelchangeapply","酒店改期申请"),
    Custom_Exception_ReasonContentNotEmpty("saas.corelanguage.custom_exception_reasoncontentnotempty","事由内容项不能为空"),
    Common_Exception_TravelApprovalUpdatedFail("saas.corelanguage.common_exception_travelapprovalupdatedfail","里程补贴审批单发放状态更新失败"),
    Auth_Msg_Title_RejectAuth("saas.corelanguage.auth_msg_title_rejectauth","驳回授权申请"),
    Common_Value_OverPermissionServer("saas.corelanguage.common_value_overpermissionserver","超出权限服务"),
    Common_Message_TaxiOrderDriverCancelTitle("saas.corelanguage.common_message_taxiorderdrivercanceltitle","用车-订单取消"),
    Common_Message_CarNoticeMsg("saas.corelanguage.common_message_carnoticemsg","{0}提交的用车申请，抄送给您，请知晓"),
    Common_Message_TrainOrderRefundFailed("saas.corelanguage.common_message_trainorderrefundfailed","您预订的{0}，{1}，{2}列车，乘车人：{3}，座席：{4}，票号：{5}，退票失败。"),
    Common_Exception_FlowUsedByDepartmentRepeat("saas.corelanguage.common_exception_flowusedbydepartmentrepeat","部门应用审批重复:"),
    Common_Message_RemoveOrderBackTitle("saas.corelanguage.common_message_removeorderbacktitle","核销单单据交回提醒"),
    Common_Exception_UsedCarTimeNotEmpty("saas.corelanguage.common_exception_usedcartimenotempty","用车时间不能为空"),
    Common_Message_DinnerPassNoticeMsg("saas.corelanguage.common_message_dinnerpassnoticemsg","{0}提交的用餐申请已通过，抄送给您，请知晓"),
    Common_Value_CheckDeleteForwardAuthUser("saas.corelanguage.common_value_checkdeleteforwardauthuser","审批人被删除，审批单将由授权负责人审核"),
    Common_Message_MallPassNoticeMsg("saas.corelanguage.common_message_mallpassnoticemsg","{0}提交的采购申请已通过，抄送给您，请知晓"),
    Rule_Value_OverRemark("saas.corelanguage.rule_value_overremark","超规说明"),
    Common_Value_DayAverageQuota("saas.corelanguage.common_value_dayaveragequota","单日日均上限{0}元，可用{1}元"),
    Rule_Value_TrainBookableSpace("saas.corelanguage.rule_value_trainbookablespace","可订席别"),
    Common_Exception_ResultValueNotEmpty("saas.corelanguage.common_exception_resultvaluenotempty","result不能为空"),
    Common_Exception_IdValueNotEmpty("saas.corelanguage.common_exception_idvaluenotempty","id不能为空"),
    Common_Value_ApprovalPersonDelete("saas.corelanguage.common_value_approvalpersondelete","审批人被删除"),
    Auth_Msg_Submit("saas.corelanguage.auth_msg_submit","尊敬的用户，{0}向您授权商务消费场景{1}，在{2}-{3}时间内的预订权限，您可在分贝通帮他进行预订。"),
    Common_Message_TrainTicketUpdateNotice("saas.corelanguage.common_message_trainticketupdatenotice","【审批抄送】{0}提交了火车票改签申请，请您知晓，点击查看详情"),
    Common_Exception_TitleCompanyUnopenVirtualCard("saas.corelanguage.common_exception_titlecompanyunopenvirtualcard","企业未开通分贝通虚拟卡功能"),
    Common_Exception_TitlePassAuth("saas.corelanguage.common_exception_titlepassauth","同意授权"),
    Common_Exception_TitlePersonBudget("saas.corelanguage.common_exception_titlepersonbudget","员工个人预算不足"),
    MessageKafkaCustomerService_Exception_BatchSaveObjectIsEmpty("saas.corelanguage.messagekafkacustomerservice_exception_batchsaveobjectisempty","批量保存消息存在参数为空"),
    Common_Value_BusinessFeeBack("saas.applytype.businessorderwriteoff","商务消费报销"),
    Common_Value_MonthBudget("saas.corelanguage.common_value_monthbudget","月度预算"),
    Common_Exception_OrderTypeNotCorrect("saas.corelanguage.common_exception_ordertypenotcorrect","订单类型不正确"),
    Common_Message_TaxiOrderTimeout("saas.corelanguage.common_message_taxiordertimeout","抱歉，您从{0}到{1}的用车订单，超时无供应商接单已经关闭。"),
    Rule_Value_CarUsedUpperMsg("saas.corelanguage.rule_value_caruseduppermsg","您的单次用车上限为{0}元"),
    Common_Message_FlowNoticeResult("saas.corelanguage.common_message_flownoticeresult","你的{0}审批{1}"),
    Common_Exception_BusinessNoCheckApply("saas.corelanguage.common_exception_businessnocheckapply","您的{0}业务无需审批即可预订,确定继续提交审批吗?"),
    Common_Message_AirTicketChangePersonRefuse("saas.corelanguage.common_message_airticketchangepersonrefuse","您提交的机票改期申请被驳回，点击查看详情"),
    Rule_Value_HotelOtherRoomFeeNotLimit("saas.corelanguage.rule_value_hotelotherroomfeenotlimit","其他城市无限制\n"),
    Common_Exception_BankIndividualAutoRepulseWithBalanceMsg("saas.corelanguage.common_exception_bankindividualautorepulsewithbalancemsg","企业已将虚拟卡功能切换至备用金模式，当前额度未退还或用完前无法申请额度"),
    Rule_Value_CarDayMoney("saas.corelanguage.rule_value_cardaymoney","单日用车上限{0}元"),
    Common_Exception_TripConfigNotEmpty("saas.corelanguage.common_exception_tripconfignotempty","行程审批配置不能为空"),
    Common_Exception_OpenPermission("saas.corelanguage.common_exception_openpermission","您尚未开通{0}权限，请联系管理员"),
    Common_Exception_TitleProjectBudgetNot("saas.corelanguage.common_exception_titleprojectbudgetnot","指定项目预算不足"),
    Rule_Value_AirOrder("saas.reasontype.order_air","国内机票订单"),
    Auth_Msg_DueToDeparture("saas.corelanguage.auth_msg_duetodeparture","尊敬的用户，您授权给{0}{1}商务消费场景，在{2}-{3}时间内的操作权限，由于被授权人{4}已离职，授权申请自动失效。"),
    SelfAuthorizeServiceImpl_Value_DateTimeRange("saas.corelanguage.selfauthorizeserviceimpl_value_datetimerange","{0}至{1}"),
    Common_Message_AirTicketChangeInvalidNotice("saas.corelanguage.common_message_airticketchangeinvalidnotice","【申请已失效】您提交的机票改期申请航班时效批，改期申请已关闭，点击查看详情"),
    Order_Value_CheckProjectPeriod("saas.corelanguage.order_value_checkprojectperiod","项目周期"),
    TakeawayCheckServiceImpl_Value_OrderOver("saas.corelanguage.takeawaycheckserviceimpl_value_orderover","订单超规"),
    Hotel_Value_OrderConfig("saas.corelanguage.hotel_value_orderconfig","您预订的行程实际费用超过差旅申请单费用上限，根据企业配置，超出预估金额上限部分将个人支付：\n行程费用上限金额：￥{0}\n本次预订金额：￥{1}\n个人支付金额：￥{2}"),
    Rule_Value_UsedCost("saas.corelanguage.rule_value_usedcost","剩余可用额度¥{0}"),
    Rule_Value_CarUsedNum("saas.corelanguage.rule_value_carusednum","用车次数"),
    Common_Message_AirBackPersonPass("saas.corelanguage.common_message_airbackpersonpass","您提交的机票退票申请已通过，点击查看详情"),
    Rule_Value_TrainOrder("saas.corelanguage.rule_value_trainorder","火车票订单"),
    Common_Message_TrainOrderRefundOK("saas.corelanguage.common_message_trainorderrefundok","您预订的{0}，{1}次列车，发车时间：{2}，乘车人：{3}，退票成功。"),
    Common_Value_UserName("saas.corelanguage.common_value_username","姓名"),
    Order_Value_MsgCarPassengers("saas.corelanguage.order_value_msgcarpassengers","乘车人"),
    Rule_Value_MallShopNum("saas.corelanguage.rule_value_mallshopnum","可采购{0}项商品SKU"),
    Rule_Value_DinnerSendDate("saas.corelanguage.rule_value_dinnersenddate","用餐/送餐日期"),
    Common_Message_MovieTicketTitle("saas.biztypemessage.ticket","电影票"),
    ExcelApplyFlowUtil_Value_CcNoticeType("saas.corelanguage.excelapplyflowutil_value_ccnoticetype","通知抄送人"),
    Common_Message_OrderNoticeMsg("saas.corelanguage.common_message_ordernoticemsg","{0}提交了一笔{1}的{2}订单，已审批预估费用为¥{3}，点击查看详情"),
    TaxiCacheConstant_Value_SettingIsOptional("saas.corelanguage.taxicacheconstant_value_settingisoptional","配置开关选填"),
    Order_Value_MsgUserBusOrderArrive("saas.corelanguage.order_value_msguserbusorderarrive","{0}预定了{1}至{2}的{3}"),
    Common_Message_TakeawayNeedForwardCheck("saas.corelanguage.common_message_takeawayneedforwardcheck","{0}转交外卖申请至您审核。"),
    Common_Value_DayNotLimit("saas.corelanguage.common_value_daynotlimit","单日无限制"),
    Common_Message_TrainTicketUpdateTimeoutNotice("saas.corelanguage.common_message_trainticketupdatetimeoutnotice","【申请已超时】您提交的火车票改签申请超时未被审批，改签申请已关闭，点击查看详情"),
    Common_Message_AirPassOrderApplyNotice("saas.corelanguage.common_message_airpassorderapplynotice","【审批抄送】{0}提交了机票订单申请已通过，请您知晓，点击查看详情"),
    VirtualCardWriteOffRemindServiceImpl_Value_MessageTitle("saas.corelanguage.virtualcardwriteoffremindserviceimpl_value_messagetitle","虚拟卡交易记录核销提醒"),
    TaxiCacheServiceImpl_Value_ReasonConfig("saas.corelanguage.taxicacheserviceimpl_value_reasonconfig","事由配置"),
    Rule_Value_TrainDistanceOther("saas.corelanguage.rule_value_traindistanceother","城市直线距离大于{0}公里，或飞机含税价比火车(高铁/动车)低"),
    Common_Message_RemoveOrderDetail("saas.corelanguage.common_message_removeorderdetail","核销单单据交回提醒\n您管理员提醒您尽快交回核销单相关单据，点击查看详情"),
    Rule_Value_CarUsedAnyPosition("saas.corelanguage.rule_value_carusedanyposition","任意位置"),
    Common_Value_AirInfoInvalid("saas.corelanguage.common_value_airinfoinvalid","航班失效"),
    Common_Message_TaxiOrderDispatchedAndPicking("saas.corelanguage.common_message_taxiorderdispatchedandpicking","您前往{0}的行程，司机已接单，车型：{1}，司机：{2}，{3}。"),
    TaxiCacheConstant_Value_SettingIsOptionalAndObjectIsEmpty("saas.corelanguage.taxicacheconstant_value_settingisoptionalandobjectisempty","配置开关选填并且入参该项缓存数据为空"),
    Rule_Value_CarLimitNum("saas.corelanguage.rule_value_carlimitnum","限{0}次"),
    Order_Value_MsgAirPassengers("saas.corelanguage.order_value_msgairpassengers","乘机人"),
    Rule_Value_CarTempUsed("saas.corelanguage.rule_value_cartempused","临时用车"),
    Common_Exception_DepartmentNoExsits("saas.corelanguage.common_exception_departmentnoexsits","部门不存在:"),
    Rule_Value_TrainMinGreaterHour("saas.corelanguage.rule_value_trainmingreaterhour","火车(高铁/动车)最短耗时需超过{0}小时"),
    Order_Value_MsgReserved("saas.corelanguage.order_value_msgreserved","预订了"),
    TrainCheckServiceImpl_Value_Train("saas.corelanguage.traincheckserviceimpl_value_train","普通列车"),
    Common_Exception_RuleIsEmpty("saas.corelanguage.common_exception_ruleisempty","规则为空"),
    Common_Message_MallNoticeMsg("saas.corelanguage.common_message_mallnoticemsg","{0}提交的采购申请，抄送给您，请知晓"),
    Common_Value_Rebook("saas.orderoperatetype.change","改签"),
    ExcelApplyFlowUtil_Value_flowName("saas.corelanguage.excelapplyflowutil_value_flowname","审批流名称"),
    TaxiCacheConstant_Value_SettingIsOff("saas.corelanguage.taxicacheconstant_value_settingisoff","配置开关未开启"),
    Rule_Value_DinnerDayLimit("saas.corelanguage.rule_value_dinnerdaylimit","每日限额"),
    Common_Value_Give("saas.corelanguage.common_value_give","给"),
    Common_Exception_AuthCompanyIsNotMatched("saas.corelanguage.common_exception_authcompanyisnotmatched","授权记录的公司id不匹配"),
    Common_Message_AirTicketChange("saas.corelanguage.common_message_airticketchange","{0}提交了机票改期申请，请您知晓，点击查看详情"),
    Auth_Msg_Title_Cancel("saas.authactionenum.canceled","取消授权"),
    Common_Message_AirChangeFlow("saas.corelanguage.common_message_airchangeflow","{0}提交了机票改期申请，请尽快完成审批"),
    Rule_Exception_TakeawayApplyOrderCategory("saas.corelanguage.rule_exception_takeawayapplyordercategory","外卖申请单费用归属"),
    Common_Message_TaxiOrderPersonalWaitPay("saas.corelanguage.common_message_taxiorderpersonalwaitpay","您下单前往{0}的用车行程已结束，费用为{1}，已付款{2}，请支付剩余车费"),
    Common_Message_TrainBack("saas.corelanguage.common_message_trainback","{0}提交了机票退订申请，请您知晓，点击查看详情"),
    Common_Value_HasAccept("saas.authextendstateenum.allow","已同意"),
    Common_Message_AccountBalanceWarnNewTitle("saas.messagewebsubtype.accountbalance","商务消费账户余额通知"),
    TaxiCacheConstant_Value_FileTypeIsNotMatch("saas.corelanguage.taxicacheconstant_value_filetypeisnotmatch","自定义档案类型不匹配"),
    Common_Exception_TitleAccountInValid("saas.corelanguage.common_exception_titleaccountinvalid","员工账号被禁用"),
    Common_Message_AirTicketBackFlowNotice("saas.corelanguage.common_message_airticketbackflownotice","【审批通知】{0}提交了机票退票申请，错过最佳退票时间点可能产生较高退票手续费，请尽快完成审批"),
    Common_Exception_ResponseValueNotEmpty("saas.corelanguage.common_exception_responsevaluenotempty","response不能为空"),
    Common_Value_DiscountNum("saas.corelanguage.common_value_discountnum","{0}折"),
    Rule_Value_CarAllowCallOther("saas.corelanguage.rule_value_carallowcallother","允许为他人叫车"),
    Rule_Value_TrainGetTicketType("saas.corelanguage.rule_value_traingettickettype","抢票席别"),
    Common_Value_OnlineBackTicket("saas.corelanguage.common_value_onlinebackticket","在线退票功能将在发车时刻前35分钟关闭，关闭后只能到始发站办理退票手续，请尽快完成审批"),
    Rule_Value_CarText("saas.traveltype.taxi","用车"),
    Common_Message_AirTicketChangePass("saas.corelanguage.common_message_airticketchangepass","{0}提交的机票改期申请已通过，请您知晓，点击查看详情"),
    Common_Message_TrainTicketUpdateTimeout("saas.corelanguage.common_message_trainticketupdatetimeout","您提交的火车票改签申请超时未被审批，申请单已关闭，点击查看详情"),
    Common_Message_HotelPassOrderApprovalNotice("saas.corelanguage.common_message_hotelpassorderapprovalnotice","【申请已通过】您提交的酒店订单申请已通过，点击查看详情"),
    Custom_Exception_OverAndBackNotRequired("saas.corelanguage.custom_exception_overandbacknotrequired","超规和退改事由暂不支持设置为选填"),
    Rule_Exception_DinnerApplyOrderCategory("saas.corelanguage.rule_exception_dinnerapplyordercategory","用餐申请单费用归属"),
    Rule_Value_SomeSubsidy("saas.corelanguage.rule_value_somesubsidy","其余¥{0}超出补贴上限无法补贴"),
    Common_Exception_OperateParamNotEmpty("saas.corelanguage.common_exception_operateparamnotempty","操作参数不能为空"),
    Common_Message_FlowFbqSubmitApply("saas.corelanguage.common_message_flowfbqsubmitapply","{0}提交了分贝券申请，请审核"),
    Common_Value_DidiTaxiApply("saas.biztypemessage.diditaxiapply","滴滴企业级用车申请"),
    TaxiCacheConstant_Value_SettingIsNotShowed("saas.corelanguage.taxicacheconstant_value_settingisnotshowed","配置开关不显示"),
    Common_Message_TrainRefuseOrderApproval("saas.corelanguage.common_message_trainrefuseorderapproval","您提交的火车票订单申请被驳回，点击查看详情"),
    Rule_Value_HotelChangeApply("saas.corelanguage.common_value_hotelchangeapply","酒店改期申请"),
    Common_Value_ApplyCar("saas.corelanguage.common_value_applycar","申请用车"),
    Rule_Value_TripAccountMsg("saas.corelanguage.rule_value_tripaccountmsg","当前申请共{0}条里程信息，行程记录合计{1}km，里程补贴合计¥{2}"),
    Rule_Value_CarNotAllowCallOther("saas.corelanguage.rule_value_carnotallowcallother","不允许为他人叫车"),
    Common_Message_DinnerApplyPassNotice("saas.corelanguage.common_message_dinnerapplypassnotice","【申请已通过】您提交的用餐费用申请已通过，点击查看详情"),
    Common_Message_AccountBalanceWarnOldContent("saas.corelanguage.common_message_accountbalancewarnoldcontent","尊敬的用户，您好。截止到{0}，您公司的可用余额不足{1}元。"),
    Rule_Value_MinPriceLimit("saas.corelanguage.rule_value_minpricelimit","最低价限制"),
    Common_Value_ApprovalListVirtualCard("saas.corelanguage.common_value_approvallistvirtualcard","虚拟卡消费核销审批单"),
    Common_Message_OrderToPlace("saas.corelanguage.common_message_ordertoplace","您预订的{0}前往{1}的{2}"),
    Common_Message_DinnerFlowPass("saas.corelanguage.common_message_dinnerflowpass","【审批提示】您提交的{0}人{1}元的用餐申请已通过，点击查看详情。"),
    Rule_Value_ApplyRemark("saas.corelanguage.rule_value_applyremark","申请说明"),
    Order_Value_MsgUserBusOrder("saas.corelanguage.order_value_msguserbusorder","{0}预定了{1}前往{2}的{3}"),
    ThirdOrderCheckServiceImpl_Value_NoValidApplyOrder("saas.corelanguage.thirdordercheckserviceimpl_value_novalidapplyorder","无可用申请单，请提交申请单，若已提交请耐心等待"),
    Custom_Exception_ContractConfig("saas.corelanguage.custom_exception_contractconfig","合同信息配置"),
    Common_Message_AirTicketChangePersonRefuseNotice("saas.corelanguage.common_message_airticketchangepersonrefusenotice","【申请被驳回】您提交的机票改期申请被驳回，点击查看详情"),
    Common_Value_TravelPerson("saas.bookingtypeenum.traveler","出行人"),
    ExcelApplyFlowUtil_Value_UsedByDepartment("saas.corelanguage.excelapplyflowutil_value_usedbydepartment","应用部门"),
    Common_Message_HotelRefuseOrderApprovalNotice("saas.corelanguage.common_message_hotelrefuseorderapprovalnotice","【申请被驳回】您提交的酒店订单申请被驳回，点击查看详情"),
    Dinner_Value_SingleUsedMoney("saas.corelanguage.dinner_value_singleusedmoney","单次用餐金额需低于¥{0}"),
    Common_Message_HotelBackPersonPassNotice("saas.corelanguage.common_message_hotelbackpersonpassnotice","【申请已通过】您提交的酒店退订申请已通过，点击查看详情"),
    ThirdOrderCheckServiceImpl_Value_NoValidApplyOrderByOther("saas.corelanguage.thirdordercheckserviceimpl_value_novalidapplyorderbyother","无可用申请单，请在对接系统中提交申请单，若已提交请耐心等待"),
    Rule_Value_CopyUser("saas.corelanguage.rule_value_copyuser","抄送人"),
    Rule_Value_FbqValidTime("saas.corelanguage.rule_value_fbqvalidtime","分贝券有效时间"),
    Common_Value_NotAllow("saas.corelanguage.common_value_notallow","不允许"),
    Common_Exception_TakeawayAuthNotFound("saas.corelanguage.common_exception_takeawayauthnotfound","未查到外卖权限"),
    Common_Message_TravelNoticeMsg("saas.corelanguage.common_message_travelnoticemsg","{0}提交的出差申请，抄送给您，请知晓"),
    Rule_Value_AirText("saas.biztype.air","国内机票"),
    Common_Exception_TitleEmployeeRemove("saas.corelanguage.common_exception_titleemployeeremove","员工已被移出企业"),
    Common_Message_OrderTimeOrPlace("saas.corelanguage.common_message_ordertimeorplace","{0}预订了{1}至{2}的{3}"),
    Order_Value_BusOrder("saas.corelanguage.order_value_busorder","汽车预订"),
    Common_Value_ActionComment("saas.corelanguage.common_value_actioncomment","评论"),
    Common_Exception_CreateDateTimeNotEmpty("saas.corelanguage.common_exception_createdatetimenotempty","创建时间不能为空"),
    Common_Message_TrainBackPersonPass("saas.corelanguage.common_message_trainbackpersonpass","您提交的火车票退票申请已通过，点击查看详情"),
    Common_Message_TrainPassOrderApplyNotice("saas.corelanguage.common_message_trainpassorderapplynotice","【审批抄送】{0}提交了火车票订单申请已通过，请您知晓，点击查看详情"),
    Rule_Value_HotelFirstRoomFeeNotLimit("saas.corelanguage.rule_value_hotelfirstroomfeenotlimit","一线城市无限制\n"),
    Common_Message_TrainOrderEndorseOK("saas.corelanguage.common_message_trainorderendorseok","新票信息：{0}，{1}次，发车时间{2}，乘车人：{3}，{4}，{5}，取票号{6}。"),
    Common_Value_HasReject("saas.corelanguage.common_value_hasreject","已拒绝"),
    Rule_Value_TakeawayAllowTimeRange("saas.corelanguage.rule_value_takeawayallowtimerange","{0}-次日{1}"),
    Rule_Value_HotelOrder("saas.reasontype.order_hotel","酒店订单"),
    Common_Value_HotelApply("saas.applytypewebshowopt.hotel","酒店申请"),
    Rule_Value_TakeawayAllPrice("saas.corelanguage.rule_value_takeawayallprice","累计金额"),
    Common_Exception_EndTimeValueNotEmpty("saas.corelanguage.common_exception_endtimevaluenotempty","endTime不能为空"),
    Order_Message_BudgetProgressReminder("saas.corelanguage.order_message_budgetprogressreminder","【分贝通】【预算进度提醒】{0}预算限额已达到{1}，请注意！"),
    Common_Message_TaxiOrderDispatchedAndPickingTitle("saas.corelanguage.common_message_taxiorderdispatchedandpickingtitle","用车-司机已接单"),
    Rule_Value_HotelFirstRoomAverageFeeLess("saas.corelanguage.rule_value_hotelfirstroomaveragefeeless","一线城市平均每晚房费需低于{0}元\n"),
    Common_Message_TrainOrderEndorseBooked("saas.corelanguage.common_message_trainorderendorsebooked","车票待支付：您要改签的{0}，{1}次，请及时完成订单支付"),
    Common_Message_DinnerNeedCheckNotice("saas.corelanguage.common_message_dinnerneedchecknotice","【审批通知】{0}提交了{1}人{2}元的用餐申请，请尽快完成审批。"),
    Common_Message_AccountBalanceWarnNewContent("saas.corelanguage.common_message_accountbalancewarnnewcontent","尊敬的用户，您好。截止到{0}，您公司商务消费场景的生效账户余额为{1}元，已小于您设置的告警阈值，为不影响员工正常使用，建议您及时{2}。"),
    Common_Exception_DinnerTimeNotEmpty("saas.corelanguage.common_exception_dinnertimenotempty","用餐时间不能为空"),
    Common_Exception_InvalidExtend("saas.corelanguage.common_exception_invalidextend","自主授权延长期限无效"),
    Custom_Exception_ReasonContentRepeat("saas.corelanguage.custom_exception_reasoncontentrepeat","事由内容项重复"),
    Common_Message_DinnerCheckRejectNotice("saas.corelanguage.common_message_dinnercheckrejectnotice","【申请被驳回】您提交的{0}人{1}元的用餐申请被驳回，点击查看详情。"),
    Rule_Value_CarQuotaMsg("saas.corelanguage.rule_value_carquotamsg","您的单日日均用车总额度上限为{0}元，申请单可用额度为{1}元"),
    Common_Exception_ThirdServerData("saas.corelanguage.common_exception_thirdserverdata","调用第三方接口返回数据异常，请稍后重试"),
    Common_Value_NotDisplay("saas.corelanguage.common_value_notdisplay","不展示"),
    Rule_Value_RejectUsed("saas.corelanguage.rule_value_rejectused","禁止使用"),
    Common_Exception_MallCompanyNoAuthMsg("saas.temporaryresponsecode.ordermallcheckcompanynoauth","您所在的公司不允许采购物品,具体情况请联系管理员"),
    Custom_Exception_TravelersControl("saas.corelanguage.custom_exception_travelerscontrol","出行人控制"),
    Common_Exception_ApplyFlowNull("saas.corelanguage.common_exception_applyflownull","审批流不能为空"),
    Common_Exception_FeeBelongTypeNotEmpty("saas.corelanguage.common_exception_feebelongtypenotempty","费用归属类型不能为空"),
    Rule_Value_HotelFirstRoomFeeLess("saas.corelanguage.rule_value_hotelfirstroomfeeless","一线城市需低于¥{0}元\n"),
    Common_Value_NoChargePerson("saas.corelanguage.common_value_nochargeperson","暂无负责人"),
    Custom_Exception_NotExistReason("saas.corelanguage.custom_exception_notexistreason","不存在的事由项"),
    Common_Message_TrainTicketBackRefuse("saas.corelanguage.common_message_trainticketbackrefuse","您提交的火车票退订申请被驳回，点击查看详情"),
    Common_Message_AirBackPersonPassNotice("saas.corelanguage.common_message_airbackpersonpassnotice","【申请已通过】您提交的机票退票申请已通过，点击查看详情"),
    Common_Value_VirtualCardRemove("saas.applytype.virtualcardwriteoff","虚拟卡核销"),
    Common_Exception_TitleDepartmentBudgetNot("saas.corelanguage.common_exception_titledepartmentbudgetnot","指定部门预算不足"),
    Common_Exception_RejectReasonLengthNotOverHundred("saas.corelanguage.common_exception_rejectreasonlengthnotoverhundred","拒绝原因不可超过100字"),
    Rule_Value_OtherPlaceText("saas.corelanguage.rule_value_otherplacetext","其他地区"),
    Rule_Value_HotelOtherRoomFeeLess("saas.corelanguage.rule_value_hotelotherroomfeeless","其他城市需低于¥{0}元\n"),
    Common_Exception_UseridValueNotExist("saas.corelanguage.common_exception_useridvaluenotexist","user_id 用户不存在"),
    Common_Exception_RequestValueNotEmpty("saas.corelanguage.common_exception_requestvaluenotempty","request不能为空"),
    Rule_Value_JianText("saas.corelanguage.rule_value_jiantext","间"),
    Common_Message_HotelOrderApproval("saas.corelanguage.common_message_hotelorderapproval","{0}提交了酒店订单申请，房量随时变化，请尽快审批"),
    Common_Value_SingleNoLimit("saas.corelanguage.common_value_singlenolimit","单次无限制"),
    Common_Exception_TitleAcceptAuth("saas.corelanguage.common_exception_titleacceptauth","接受授权"),
    Common_Message_TrainBackFlow("saas.corelanguage.common_message_trainbackflow","{0}提交了火车票退票申请，请尽快完成审批"),
    ExcelApplyFlowUtil_Value_RoleApproveType("saas.corelanguage.excelapplyflowutil_value_roleapprovetype","角色审批时"),
    Common_Message_TaxiOrderDriverCancel("saas.corelanguage.common_message_taxiorderdrivercancel","抱歉，您从{0}至{1}的用车订单已被司机取消。"),
    Rule_Value_AirChangeApply("saas.corelanguage.rule_value_airchangeapply","机票改期申请"),
    Common_Message_TaxiOrderFinish("saas.corelanguage.common_message_taxiorderfinish","您前往{0}的用车行程已结束，费用{1}元。"),
    Common_Value_TimeToday("saas.corelanguage.common_value_timetoday","今天"),
    Common_Message_FlowSubmitMsg("saas.corelanguage.common_message_flowsubmitmsg","{0}提交了{1}申请，请审核"),
    Order_Value_WarnSubmit("saas.corelanguage.order_value_warnsubmit","您本次提交订单，{0}{1}业务{2}{3}元限额，根据本企业设置限额超出部分由个人支付，是否继续提交？"),
    Rule_Value_Frequency("saas.corelanguage.rule_value_frequency","次"),
    Common_Message_AcceptFlowNotice("saas.corelanguage.common_message_acceptflownotice","对接审批通知-{0}审批"),
    Rule_Value_CarGoPosition("saas.corelanguage.rule_value_cargoposition","上车位置"),
    Common_Value_PayOrder("saas.corelanguage.common_value_payorder","买单"),
    AirUtils_Value_TrainNeedTime("saas.corelanguage.airutils_value_trainneedtime","火车（高铁/动车）耗时小于{0}小时，请乘坐火车"),
    Common_Message_AirBackTimeoutNotice("saas.corelanguage.common_message_airbacktimeoutnotice","【申请已超时】您提交的机票退票申请超时未被审批，退票申请已关闭，点击查看详情"),
    TaxiCacheConstant_Value_ChoiceTypeIsNotMatch("saas.corelanguage.taxicacheconstant_value_choicetypeisnotmatch","自定义选项类型不匹配"),
    Rule_Value_TakeawayText("saas.biztype.takeaway","外卖"),
    Order_Value_CheckInnerPeriod("saas.corelanguage.order_value_checkinnerperiod","周期内"),
    ExcelApplyFlowUtil_Value_CompanySettingType("saas.corelanguage.excelapplyflowutil_value_companysettingtype","审批配置类型"),
    Common_Message_AirTicketChangeTimeout("saas.corelanguage.common_message_airticketchangetimeout","您提交的机票改期申请超时未被审批，申请单已关闭，点击查看详情"),
    Common_Exception_PhoneNoExsits("saas.corelanguage.common_exception_phonenoexsits","手机号不存在:"),
    Common_Value_CityText("saas.corelanguage.common_value_citytext","市"),
    TaxiCheckServiceImpl_Value_AirportTransfer("saas.ddscenetypeenum.pick_drop","接送机"),
    Custom_Exception_OccupantNotEmpty("saas.corelanguage.custom_exception_occupantnotempty","入住人不能为空"),
    Hotel_Value_OrderConfigTrip("saas.corelanguage.hotel_value_orderconfigtrip","您预订的行程实际费用超过差旅申请单费用上限，根据企业配置，超出预估金额上限部分将个人支付：\n{0}共用一个行程，\n行程费用上限金额：￥{1}\n本次预订金额：￥{2}\n个人支付金额：￥{3}"),
    Common_Message_TakeawayCheckRefuse("saas.corelanguage.common_message_takeawaycheckrefuse","{0}驳回了您的外卖申请。"),
    Common_Value_TrainChangeApply("saas.biztypemessage.trainchangeapply","火车票改签申请"),
    Common_Value_CarTypeLimit("saas.corelanguage.common_value_cartypelimit","车型限制"),
    Common_Message_DinnerFeeSubmitOrderApply("saas.corelanguage.common_message_dinnerfeesubmitorderapply","{0}提交了用餐申请，请您知晓，点击查看详情"),
    Common_Message_FlowFbqRefuseApply("saas.corelanguage.common_message_flowfbqrefuseapply","您提交的{0}张{1}分贝券申请已被驳回，点击查看详情"),
    Common_Value_CompanyQuotaNotEnough("saas.globalresponsecode.orderchecktaxiavailablecompanybalanceexceed","企业余额不足"),
    Common_Message_HotelBackNotice("saas.corelanguage.common_message_hotelbacknotice","【审批抄送】{0}提交了酒店退订申请，请您知晓，点击查看详情"),
    Common_Value_AirBackApply("saas.biztypemessage.airplanerefundapply","国内机票退票申请"),
    Common_Value_BeforeDays("saas.corelanguage.common_value_beforedays","至少提前{0}天"),
    Common_Message_VirtualCardSendMsg("saas.corelanguage.common_message_virtualcardsendmsg","{0}提交了虚拟卡核销申请，抄送给您，请知晓"),
    Common_Exception_RejectReasonNotEmpty("saas.corelanguage.common_exception_rejectreasonnotempty","拒绝原因不可为空"),
    TaxiCacheConstant_Exception_CarTypeError("saas.corelanguage.taxicacheconstant_exception_cartypeerror","用车类型参数错误"),
    Common_Message_AirOrderChangedSuccess("saas.corelanguage.common_message_airorderchangedsuccess","您预订的{0}，{1}航班，已更改为{2}航班，{3}，预计{4}起飞，预计{5}到达，乘机人：{6}，票号：{7}。"),
    Common_Exception_DefaultFlowConfigGetError("saas.globalresponsecode.defaultapplyordererror","默认审批流获取错误"),
    Common_Message_HotelBackPassNotice("saas.corelanguage.common_message_hotelbackpassnotice","【审批抄送】{0}提交的酒店退订申请已通过，请您知晓，点击查看详情"),
    Auth_Msg_Title_Delay("saas.modifyauthstateenum.extend","延长授权"),
    Common_Message_FlowFbqTimeoutClosed("saas.corelanguage.common_message_flowfbqtimeoutclosed","您提交的分贝券申请超时未被审批，申请已关闭"),
    Rule_Value_ApplyReason("saas.corelanguage.rule_value_applyreason","申请事由"),
    Rule_Value_OverPayTitle("saas.corelanguage.rule_value_overpaytitle","超额个人支付提示"),
    Order_Value_DepartmentBudget("saas.budgettype.department","部门预算"),
    Rule_Value_NotLimit("saas.corelanguage.rule_value_notlimit","无限制"),
    Common_Value_WorkOverTime("saas.ddscenetypeenum.overtime","加班"),
    Common_Message_TrainSubmitOrderApplyNotice("saas.corelanguage.common_message_trainsubmitorderapplynotice","【审批抄送】{0}提交了火车票订单申请，请您知晓，点击查看详情"),
    Common_Message_TrainTicketForwardNotice("saas.corelanguage.common_message_trainticketforwardnotice","【审批提示】您提交的火车票退改申请已审批至{0}，点击查看详情"),
    Common_Message_KoubeiDinnerTitle("saas.corelanguage.common_message_koubeidinnertitle","口碑用餐"),
    Rule_Value_CarUsedCurrentCity("saas.corelanguage.rule_value_carusedcurrentcity","本市范围可用"),
    Rule_Value_AirBackApply("saas.corelanguage.rule_value_airbackapply","机票退订申请"),
    Common_Message_AirOrderRefundFail("saas.corelanguage.common_message_airorderrefundfail","您预订的{0}，{1}，预计{2}起飞，预计{3}到达，{4}航班，乘机人：{5}，票号: {6}，退票失败。"),
    Common_Exception_NotFoundRule("saas.corelanguage.common_exception_notfoundrule","未找到该规则"),
    Common_Value_TravelApply("saas.applytypewebshowopt.chailv","差旅申请"),
    Rule_Value_PersonFee("saas.corelanguage.rule_value_personfee","个人自费"),
    Common_Title_HasRefuse("saas.corelanguage.common_value_hasrefuse","被驳回"),
    Common_Message_AirOrderTicketFail("saas.corelanguage.common_message_airorderticketfail","您预订的{0}，{1}，预计{2}起飞，预计{3}到达，{4}航班，乘机人：{5}，出票失败，请您重新预订。"),
    Order_Value_PersonBudget("saas.budgettype.employee","个人预算"),
    Common_Exception_HotelCompanyNoAuthMsg("saas.globalresponsecode.hotelcheckcompanynoauth","您所在的公司不允许订购酒店,具体情况请联系管理员"),
    Common_Message_HotelOrderTitle("saas.corelanguage.common_message_hotelordertitle","酒店预订"),
    Common_Value_QuarterBudget("saas.corelanguage.common_value_quarterbudget","季度预算"),
    VirtualCardWriteOffRemindServiceImpl_Value_MessageContent("saas.corelanguage.virtualcardwriteoffremindserviceimpl_value_messagecontent","您有虚拟卡的交易记录没有核销，请及时在分贝通系统内核销"),
    Rule_Value_TrainText("saas.traveltype.train","火车"),
    Common_Exception_TravelApprovalExistNotRejectData("saas.corelanguage.common_exception_travelapprovalexistnotrejectdata","里程补贴审批单中存在非拒绝发放数据，请重新勾选"),
    Rule_Value_CarUsedDistanceInner("saas.corelanguage.rule_value_caruseddistanceinner","{0}km内"),
    Common_Exception_ProjectIdNotEmpty("saas.globalresponsecode.costattributionidisnull","项目id不能为空"),
    Common_Message_AirOrderChangedFail("saas.corelanguage.common_message_airorderchangedfail","您预订的{0}，{1}，预计{2}起飞，预计{3}到达，{4}航班，乘机人：{5}，票号：{6}，改签失败。"),
    TrainCheckServiceImpl_Value_MinDay("saas.globalresponsecode.ordercheckleastday","最少提前{0}天预订"),
    Common_Value_OverExpenseRule("saas.globalresponsecode.orderchecktaxiavailableruleexceed","超出消费规则"),
    Rule_Value_TakeawayNotApply("saas.corelanguage.rule_value_takeawaynotapply","不可预订"),
    Common_Exception_InvalidStateTransfer("saas.corelanguage.common_exception_invalidstatetransfer","自主授权状态变更流转非法"),
    Common_Value_AirApply("saas.corelanguage.common_value_airapply","国内机票申请"),
    Common_Value_PersonBudgetOver("saas.corelanguage.common_value_personbudgetover","个人预算超限额"),
    Common_Value_CarApply("saas.applytypewebshowopt.taxi","用车申请"),
    TakeawayCheckServiceImpl_Value_AbandonSubmitApply("saas.corelanguage.takeawaycheckserviceimpl_value_abandonsubmitapply","禁止下单"),
    Common_Message_AirTicketChangeTimeoutNotice("saas.corelanguage.common_message_airticketchangetimeoutnotice","【申请已超时】您提交的机票改期申请超时未被审批，改期申请已关闭，点击查看详情"),
    Common_Exception_BankIndividualAutoRepulseWithoutBalanceMsg("saas.corelanguage.common_exception_bankindividualautorepulsewithoutbalancemsg","企业已将虚拟卡功能切换至备用金模式，请重新提交申请"),
    Common_Message_TrainTicketUpdatePersonPassNotice("saas.corelanguage.common_message_trainticketupdatepersonpassnotice","【申请已通过】您提交的火车票改签申请已通过，点击查看详情"),
    Common_Message_VirtualCardAccountBalanceWarnTitle("saas.corelanguage.common_message_virtualcardaccountbalancewarntitle","虚拟卡账户余额不足"),
    Rule_Value_CarCityLimit("saas.corelanguage.rule_value_carcitylimit","同城限制"),
    Rule_Value_TrainPriceLess("saas.corelanguage.rule_value_trainpriceless","含税价需比火车(高铁/动车)低￥{0}"),
    Rule_Value_TrainLessTimeOther("saas.corelanguage.rule_value_trainlesstimeother","火车(高铁/动车)最短耗时超过{0}小时，或飞机含税价比火车(高铁/动车)低"),
    Common_Exception_ServerErrorRetry("saas.corelanguage.common_exception_servererrorretry","服务异常，请稍后再试"),
    Rule_Value_BeforeOrder("saas.corelanguage.rule_value_beforeorder","提前预订"),
    Common_Message_MallFlowPassNotice("saas.corelanguage.common_message_mallpassorderapprovalnotice","【申请已通过】您提交的采购申请已通过，并已提交采购订单，点击查看申请单详情"),
    Common_Message_AirOrderTicketSuccess("saas.corelanguage.common_message_airorderticketsuccess","您预订的{0}，{1}，预计{2}起飞，预计{3}到达，{4}航班，票号: {5}，乘机人：{6}。请提前两个小时到达机场，祝您旅途愉快！"),
    Common_Exception_AirRuleIdValueNotEmpty("saas.corelanguage.common_exception_airruleidvaluenotempty","airRuleId不能为空"),
    Auth_Msg_SubmitApplication("saas.corelanguage.auth_msg_submitapplication","尊敬的用户，{0}向您申请{1}商务消费场景，在{2}-{3}时间内的操作权限，{4}请您及时审批。"),
    Auth_Msg_Title_Submit("saas.corelanguage.auth_msg_title_submit","提交授权"),
    TaxiCacheConstant_Exception_CompanyIdIsEmpty("saas.corelanguage.taxicacheconstant_exception_companyidisempty","公司id参数为空"),
    Rule_Value_FbtVirtualCard("saas.applytypewebshowopt.bankindividual","分贝通虚拟卡"),
    TrainCheckServiceImpl_Value_NeedDayToNight("saas.corelanguage.traincheckserviceimpl_value_needdaytonight","必选夕发朝至车次"),
    Rule_Value_FbqInfo("saas.corelanguage.rule_value_fbqinfo","分贝券信息"),
    Common_Value_TravelChangeFeeWarn("saas.corelanguage.common_value_travelchangefeewarn","错过最佳改期时间点可能产生较高改期手续费或延误差旅行程，请尽快完成审批"),
    Rule_Value_TakeOffTimeRange("saas.corelanguage.rule_value_takeofftimerange","起飞时段"),
    Common_Message_TrainOrderTitle("saas.corelanguage.common_message_trainordertitle","火车预订"),
    Common_Message_AirChangePersonPassNotice("saas.corelanguage.common_message_airchangepersonpassnotice","【申请已通过】您提交的机票改期申请已通过，点击查看详情"),
    Order_Value_OverBudgetRejectSubmit("saas.corelanguage.order_value_overbudgetrejectsubmit","您本次提交订单，{0}业务超出预算限额，不可继续提交"),
    Common_Message_DinnerNoticeMsg("saas.corelanguage.common_message_dinnernoticemsg","{0}提交的用餐申请，抄送给您，请知晓"),
    Rule_Value_MallShopType("saas.corelanguage.rule_value_mallshoptype","采购品类"),
    Common_Exception_TitleNotPermission("saas.corelanguage.common_exception_titlenotpermission","暂无权限"),
    Common_Value_Reschedule("saas.exceedbuytype.change","改期"),
    Rule_Value_TakeawaySinglePrice("saas.corelanguage.rule_value_takeawaysingleprice","单次金额"),
    Order_Value_MsgBuy("saas.corelanguage.order_value_msgbuy","购买了"),
    Order_Value_AmountOver("saas.globalresponsecode.ordercheckpersonalpayspecialhigh","费用超限"),
    Common_Message_RetryReminderTitle("saas.corelanguage.common_message_retryremindertitle","催办提醒"),
    Auth_Msg_Cancel("saas.corelanguage.auth_msg_cancel","尊敬的用户，{0}取消了向您授权商务消费场景{1}，在{2}-{3}时间内的预订权限。"),
    Common_Message_CarPassNoticeMsg("saas.corelanguage.common_message_carpassnoticemsg","{0}提交的用车申请已通过，抄送给您，请知晓"),
    Rule_Value_AllPlace("saas.corelanguage.rule_value_allplace","[全部地区，¥{0}]"),
    Common_Exception_BusEmployeeNoAuthMsg("saas.corelanguage.common_exception_busemployeenoauthmsg","您尚未开通汽车票预订权限,请联系管理员为您开通"),
    Rule_Value_DinnerNumLimit("saas.corelanguage.rule_value_dinnernumlimit","每餐限额"),
    OrderService_Exception_GetTripUserFail("saas.corelanguage.orderservice_exception_gettripuserfail","获取出行人信息查询订单接口失败,请稍候再试"),
    Common_Value_GroupMall("saas.corelanguage.common_value_groupmall","团购"),
    ;
    private String languageCode;

    private String message;

    private String note;

    private String smsCode;

    CoreLanguage(String languageCode, String message) {
        this.languageCode = languageCode;
        this.message = message;
    }

    CoreLanguage(String languageCode, String message, String note) {
        this.languageCode = languageCode;
        this.message = message;
        this.note = note;
    }

    CoreLanguage(String languageCode, String message, String note, String smsCode) {
        this.languageCode = languageCode;
        this.message = message;
        this.note = note;
        this.smsCode = smsCode;
    }

    public String getLanguageCode() {
        return languageCode;
    }

    public void setLanguageCode(String languageCode) {
        this.languageCode = languageCode;
    }

    public String getMessage() {
        String msg = null;
        if (StringUtils.isNotBlank(languageCode)) {
            try {
                msg = MessageSourceManager.me().getMessage(languageCode);
            } catch (Exception e) {

            }
        }
        return ((StringUtils.isNotBlank(msg) && !msg.equals(languageCode)) ? msg : message);
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getSmsCode() {
        return smsCode;
    }

    public void setSmsCode(String smsCode) {
        this.smsCode = smsCode;
    }
}
