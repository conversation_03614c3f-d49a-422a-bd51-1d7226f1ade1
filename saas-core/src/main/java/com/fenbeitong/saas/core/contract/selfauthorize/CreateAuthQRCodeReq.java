package com.fenbeitong.saas.core.contract.selfauthorize;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * Title:
 * Desc:
 * PACKAGENAME:com.fenbeitong.saas.core.contract.selfauthorize
 *
 * @author: linyongchao
 * Date: 2022/9/22
 */
@Data
public class CreateAuthQRCodeReq {
    /**
     * 二维码扫描出来的参数
     */
    @NotBlank(message = "二维码扫描参数有误，请确认二维码有效后重新扫描！")
    private String string_txt;

    /**
     * 二维码区别类型
     */
    private String type = "authorization";
}
