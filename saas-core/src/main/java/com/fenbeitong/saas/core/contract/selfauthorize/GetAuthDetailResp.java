package com.fenbeitong.saas.core.contract.selfauthorize;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

import java.util.Date;
import java.util.List;

@Data
@Builder
public class GetAuthDetailResp {

    private String auth_employee_id;

    private String auth_employee_name;

    private String be_auth_employee_id;

    private String be_auth_employee_name;

    private String company_id;

    private Integer self_authorization_type;

    private String auth_start_time;

    private String auth_end_time;

    private Integer auth_state;

    private Integer is_forever;

    private List<AuthSceneItem> auth_scene_item_list;

    private Integer operate_state;

    @Tolerate
    public GetAuthDetailResp(){}




}
