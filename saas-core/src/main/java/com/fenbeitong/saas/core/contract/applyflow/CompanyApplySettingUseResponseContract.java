package com.fenbeitong.saas.core.contract.applyflow;

import java.util.List;

public class CompanyApplySettingUseResponseContract {

    private List<CompanyApplySettingUseMessageContract> apply_use_list;

    private boolean apply_for_company;

    public List<CompanyApplySettingUseMessageContract> getApply_use_list() {
        return apply_use_list;
    }

    public void setApply_use_list(List<CompanyApplySettingUseMessageContract> apply_use_list) {
        this.apply_use_list = apply_use_list;
    }

    public boolean isApply_for_company() {
        return apply_for_company;
    }

    public void setApply_for_company(boolean apply_for_company) {
        this.apply_for_company = apply_for_company;
    }
}