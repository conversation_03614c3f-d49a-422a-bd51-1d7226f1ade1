package com.fenbeitong.saas.core.service.rule.multi;

import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.saas.api.model.dto.rule.GrantMultiRuleDetailDTO;
import com.fenbeitong.saas.api.model.dto.rule.GrantMultiRuleReqDTO;
import com.fenbeitong.saas.api.model.dto.template.rule.ConsumeTemplateItemDto;
import com.fenbeitong.saas.api.model.dto.template.rule.ConsumeTemplateRuleDto;
import com.fenbeitong.saas.api.model.dto.template.rule.ControlOrderRuleDto;
import com.fenbeitong.saas.core.common.SaasHttpContext;
import com.fenbeitong.saas.core.common.base.SaasException;
import com.fenbeitong.saas.core.common.constant.GlobalResponseCode;
import com.fenbeitong.saas.core.common.constant.SaasResponseConstant;
import com.fenbeitong.saas.core.contract.rule.ConsumeTemplateItem;
import com.fenbeitong.saas.core.contract.rule.ConsumeTemplateItemInfo;
import com.fenbeitong.saas.core.contract.rule.ConsumeTemplateRule;
import com.fenbeitong.saas.core.contract.rule.ConsumeTemplateRuleInfo;
import com.fenbeitong.saas.core.dao.fenbeitong.AirRuleMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.HotelRuleMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.TrainRuleMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.rule.GrantConsumptionRuleMapper;
import com.fenbeitong.saas.core.dao.rules.multi.ConsumeTemplateRulesExtMapper;
import com.fenbeitong.saas.core.dao.rules.multi.ConsumeTemplateRulesMapper;
import com.fenbeitong.saas.core.dao.rules.multi.ConsumeTemplateUsedRangeExtMapper;
import com.fenbeitong.saas.core.dao.rules.multi.ConsumeTemplateUsedRangeMapper;
import com.fenbeitong.saas.core.dao.saas.CompanyCustomReasonMapper;
import com.fenbeitong.saas.core.model.enums.apply.BizType;
import com.fenbeitong.saas.core.model.enums.rule.group.RuleTypeEnum;
import com.fenbeitong.saas.core.model.enums.rule.multi.ConsumeTypeEnum;
import com.fenbeitong.saas.core.model.enums.rule.multi.PriorityTypeEnum;
import com.fenbeitong.saas.core.model.fenbeitong.AirRule;
import com.fenbeitong.saas.core.model.fenbeitong.HotelRule;
import com.fenbeitong.saas.core.model.fenbeitong.TrainRule;
import com.fenbeitong.saas.core.model.fenbeitong.rule.GrantConsumptionRule;
import com.fenbeitong.saas.core.model.fenbeitong.rule.GrantConsumptionRuleExample;
import com.fenbeitong.saas.core.model.rules.multi.ConsumeTemplateRules;
import com.fenbeitong.saas.core.model.rules.multi.ConsumeTemplateRulesExample;
import com.fenbeitong.saas.core.model.rules.multi.ConsumeTemplateUsedRange;
import com.fenbeitong.saas.core.model.rules.multi.ConsumeTemplateUsedRangeExample;
import com.fenbeitong.saas.core.model.saas.CompanyCustomReason;
import com.fenbeitong.saas.core.model.saas.CompanyCustomReasonExample;
import com.fenbeitong.saas.core.service.uc.CostCenterService;
import com.fenbeitong.saas.core.service.uc.EmployeeTemplateService;
import com.fenbeitong.saas.core.utils.tools.IDTool;
import com.fenbeitong.saas.core.utils.tools.VersionTool;
import com.fenbeitong.saasplus.api.model.dto.grant.GrantRuleDTO;
import com.fenbeitong.saasplus.api.model.dto.grant.GrantRuleReqDTO;
import com.fenbeitong.saasplus.api.model.dto.grant.RuleInfoDTO;
import com.fenbeitong.saasplus.api.model.enums.commonRule.DeleteEnum;
import com.fenbeitong.saasplus.api.service.rule.grant.IGrantRuleService;
import com.fenbeitong.usercenter.api.model.po.rule.EmployeeBaseRule;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023年03月15日 21:04
 * @Desc
 */
@Slf4j
@Service
public class ConsumeTemplateRuleServiceImpl implements ConsumeTemplateRuleService {

    @Resource
    private ConsumeTemplateRulesMapper consumeTemplateRulesMapper;
    @Resource
    private ConsumeTemplateRulesExtMapper consumeTemplateRulesExtMapper;
    @Resource
    private ConsumeTemplateUsedRangeMapper consumeTemplateUsedRangeMapper;
    @Autowired
    private ConsumeTemplateUsedRangeExtMapper consumeTemplateUsedRangeExtMapper;
    @Autowired
    private CostCenterService costCenterService;
    @Autowired
    private CompanyCustomReasonMapper companyCustomReasonMapper;
    @Autowired
    private AirRuleMapper airRuleMapper;
    @Autowired
    private HotelRuleMapper hotelRuleMapper;
    @Autowired
    private TrainRuleMapper trainRuleMapper;
    @Autowired
    private GrantConsumptionRuleMapper grantConsumptionRuleMapper;
    @Autowired
    private EmployeeTemplateService employeeTemplateService;
    @Autowired
    private IGrantRuleService iGrantRuleService;

    /**
     * 添加消费模板规则配置
     *
     * @param consumeTemplateRule consumeTemplateRule
     */
    @Override
    @Transactional(value = "fenbei-rules", rollbackFor = Throwable.class)
    public void addRule(ConsumeTemplateRule consumeTemplateRule, RuleTypeEnum ruleTypeEnum) {
        log.info("ConsumeTemplateRuleServiceImpl#addRule consumeTemplate:{}", JsonUtils.toJson(consumeTemplateRule));
        if (Objects.isNull(consumeTemplateRule)
                || ObjUtils.isEmpty(consumeTemplateRule.getTemplateId())
                || ObjUtils.isEmpty(consumeTemplateRule.getType())
                || ObjUtils.isEmpty(consumeTemplateRule.getBizType())
                || ObjUtils.isEmpty(consumeTemplateRule.getPriorityType())
                || ObjUtils.isEmpty(consumeTemplateRule.getPriorityNo())) {
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        // 非最低优先级 唯一标识消费模板规则配置
        if (!Objects.equals(consumeTemplateRule.getPriorityType(), PriorityTypeEnum.DEFAULT.getType())) {
            if (ObjUtils.isEmpty(consumeTemplateRule.getConsumeTemplateItemInfos())) {
                throw new SaasException(GlobalResponseCode.ParametersIsNull);
            }
            if (consumeTemplateRule.getConsumeTemplateItemInfos().stream()
                    .anyMatch(item -> ObjUtils.isEmpty(item.getItemId()) || ObjUtils.isEmpty(item.getItemName()))) {
                throw new SaasException(GlobalResponseCode.ParametersIsNull);
            }

            ConsumeTemplateRulesExample rulesExample = new ConsumeTemplateRulesExample();
            rulesExample.createCriteria()
                    .andTypeEqualTo(consumeTemplateRule.getType())
                    .andBizTypeEqualTo(consumeTemplateRule.getBizType())
                    .andCompanyIdEqualTo(consumeTemplateRule.getCompanyId())
                    .andTemplateIdEqualTo(consumeTemplateRule.getTemplateId())
                    .andDeleteStatusEqualTo(DeleteEnum.NORMAL.getValue());
            List<ConsumeTemplateRules> configList = consumeTemplateRulesMapper.selectByExample(rulesExample);
            log.info("ConsumeTemplateRuleServiceImpl#addRule configList:{}", JsonUtils.toJson(configList));
            if (configList.size() > 0) {
                List<String> ruleIds = configList.stream().map(ConsumeTemplateRules::getId).collect(Collectors.toList());
                List<ConsumeTemplateItem> consumeTemplateItems = consumeTemplateRule.getConsumeTemplateItemInfos();
                List<String> itemIds =
                        consumeTemplateItems.stream().map(ConsumeTemplateItem::getItemId).collect(Collectors.toList());
                // 相同管控维度+适用范围,只能配置一条
                ConsumeTemplateUsedRangeExample usedRangeExample = new ConsumeTemplateUsedRangeExample();
                usedRangeExample.createCriteria()
                        .andRuleIdIn(ruleIds)
                        .andItemIdIn(itemIds)
                        .andDeleteStatusEqualTo(DeleteEnum.NORMAL.getValue());
                List<ConsumeTemplateUsedRange> consumeTemplateUsedRanges =
                        consumeTemplateUsedRangeMapper.selectByExample(usedRangeExample);
                if (consumeTemplateUsedRanges.size() > 0) {
                    log.info("ConsumeTemplateRuleServiceImpl#addRule consumeTemplateRule is exit:{}",
                            JsonUtils.toJson(consumeTemplateRule));
                    throw new SaasException(GlobalResponseCode.ConsumeTemplateRulesExisted.getCode(),
                            String.format(GlobalResponseCode.ConsumeTemplateRulesExisted.getMsg(),
                                    ConsumeTypeEnum.valueOf(consumeTemplateRule.getType()).getDesc(),
                                    consumeTemplateUsedRanges.stream().map(ConsumeTemplateUsedRange::getItemName).collect(Collectors.toList())),
                            SaasResponseConstant.TOAST_MSG);
                }
            }
        }
        // 插入消费模板数据
        ConsumeTemplateRules consumeTemplateRules = buildConsumeTemplateRules(consumeTemplateRule);
        consumeTemplateRules.setId(IDTool.CreateUniqueID());
        consumeTemplateRulesMapper.insertSelective(consumeTemplateRules);
        String id = consumeTemplateRules.getId();
        if (CollectionUtils.isNotEmpty(consumeTemplateRule.getConsumeTemplateItemInfos())) {
            List<ConsumeTemplateItem> consumeTemplateItems = consumeTemplateRule.getConsumeTemplateItemInfos();
            for (ConsumeTemplateItem consumeTemplateItem : consumeTemplateItems) {
                ConsumeTemplateUsedRange consumeTemplateUsedRange = new ConsumeTemplateUsedRange();
                consumeTemplateUsedRange.setId(IDTool.CreateUniqueID());
                consumeTemplateUsedRange.setRuleId(id);
                consumeTemplateUsedRange.setDeleteStatus(DeleteEnum.NORMAL.getValue());
                consumeTemplateUsedRange.setItemId(consumeTemplateItem.getItemId());
                consumeTemplateUsedRange.setItemName(consumeTemplateItem.getItemName());
                consumeTemplateUsedRangeMapper.insert(consumeTemplateUsedRange);
            }
        }

        // 集团版操作同步子企业
        if (ruleTypeEnum == RuleTypeEnum.GROUP) {
            syncAddRule(consumeTemplateRule);
        }
    }

    /**
     * 同步新增多规则到子企业
     */
    private void syncAddRule(ConsumeTemplateRule consumeTemplateRule) {
        List<EmployeeBaseRule> grantTemplateList = employeeTemplateService.queryTemplateGrantRecord(consumeTemplateRule.getCompanyId(),
                consumeTemplateRule.getTemplateId());
        if (ObjUtils.isEmpty(grantTemplateList)) {
            return;
        }
        grantTemplateList.forEach(template ->
                grantMultiRule(consumeTemplateRule.getCompanyId(), consumeTemplateRule.getTemplateId(),
                        template.getCompanyId(), template.getTemplateId())
        );
    }

    private ConsumeTemplateRules buildConsumeTemplateRules(ConsumeTemplateRule consumeTemplateRule) {
        ConsumeTemplateRules consumeTemplateRules = new ConsumeTemplateRules();
        consumeTemplateRules.setName(consumeTemplateRule.getName());
        consumeTemplateRules.setType(consumeTemplateRule.getType());
        consumeTemplateRules.setPriorityType(consumeTemplateRule.getPriorityType());
        consumeTemplateRules.setPriorityNo(consumeTemplateRule.getPriorityNo());
        consumeTemplateRules.setConsumerRule(consumeTemplateRule.getConsumerRule());
        consumeTemplateRules.setRuleId(consumeTemplateRule.getRuleId());
        consumeTemplateRules.setRuleName(consumeTemplateRule.getRuleName());
        consumeTemplateRules.setBizType(consumeTemplateRule.getBizType());
        consumeTemplateRules.setGroupStatus(consumeTemplateRule.getGroupStatus());
        consumeTemplateRules.setTemplateId(consumeTemplateRule.getTemplateId());
        consumeTemplateRules.setCompanyId(consumeTemplateRule.getCompanyId());
        consumeTemplateRules.setDeleteStatus(DeleteEnum.NORMAL.getValue());
        Integer isAble = ObjUtils.isNotEmpty(consumeTemplateRule.getRuleId())
                && !Objects.equals(consumeTemplateRule.getRuleId(), "0") ? 1 : 0;
        consumeTemplateRules.setIsAble(isAble);
        consumeTemplateRules.setActiveStatus(1);
        consumeTemplateRules.setCreateTime(new Date());
        consumeTemplateRules.setUpdateTime(new Date());
        return consumeTemplateRules;
    }

    @Override
    @Transactional(value = "fenbei-rules", rollbackFor = Throwable.class)
    public void deleteRule(String companyId, String id, RuleTypeEnum ruleTypeEnum) {
        log.info("ConsumeTemplateRuleServiceImpl#deleteRule companyId={}, id={}", companyId, id);
        if (StringUtils.isEmpty(id)) {
            log.info("id is empty:{}", id);
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        // 逻辑删除主表
        ConsumeTemplateRules consumeTemplateRule = new ConsumeTemplateRules();
        consumeTemplateRule.setDeleteStatus(DeleteEnum.DELETED.getValue());
        ConsumeTemplateRulesExample rulesExample = new ConsumeTemplateRulesExample();
        rulesExample.createCriteria().andIdEqualTo(id);
        consumeTemplateRulesMapper.updateByExampleSelective(consumeTemplateRule, rulesExample);
        // 删除子表数据
        ConsumeTemplateUsedRange consumeTemplateUsedRange = new ConsumeTemplateUsedRange();
        consumeTemplateUsedRange.setDeleteStatus(DeleteEnum.DELETED.getValue());
        ConsumeTemplateUsedRangeExample usedRangeExample = new ConsumeTemplateUsedRangeExample();
        usedRangeExample.createCriteria().andRuleIdEqualTo(id);
        consumeTemplateUsedRangeMapper.updateByExampleSelective(consumeTemplateUsedRange, usedRangeExample);
        // 重新排序主表
        ConsumeTemplateRules consumeTemplateRules = consumeTemplateRulesMapper.selectByPrimaryKey(id);
        rulesExample.createCriteria()
                .andTemplateIdEqualTo(consumeTemplateRules.getTemplateId())
                .andBizTypeEqualTo(consumeTemplateRules.getBizType())
                .andDeleteStatusEqualTo(DeleteEnum.NORMAL.getValue())
                .andPriorityTypeEqualTo(consumeTemplateRules.getPriorityType());
        rulesExample.setOrderByClause("priority_no");
        List<ConsumeTemplateRules> templateRulesList = consumeTemplateRulesMapper.selectByExample(rulesExample);
        AtomicInteger priorityNo = new AtomicInteger(1);
        List<ConsumeTemplateRuleInfo> sortedTemplateRulesList = templateRulesList.stream()
                .map(template -> {
                    ConsumeTemplateRuleInfo info = new ConsumeTemplateRuleInfo();
                    info.setId(template.getId());
                    info.setPriorityType(template.getPriorityType());
                    info.setPriorityNo(priorityNo.getAndAdd(1));
                    return info;
                })
                .collect(Collectors.toList());
        sort(sortedTemplateRulesList, ruleTypeEnum, Boolean.FALSE);

        if (ruleTypeEnum == RuleTypeEnum.GROUP) {
            syncDeleteRule(consumeTemplateRules.getCompanyId(), id);
        }
    }

    /**
     * 集团版删除同步子企业
     */
    private void syncDeleteRule(String groupId, String id) {
        // 查询多规则所在模板id
        ConsumeTemplateRules templateRule = consumeTemplateRulesMapper.selectByPrimaryKey(id);
        if (ObjUtils.isEmpty(templateRule)) {
            return;
        }
        List<EmployeeBaseRule> grantTemplateList = employeeTemplateService.queryTemplateGrantRecord(groupId, templateRule.getTemplateId());
        if (ObjUtils.isEmpty(grantTemplateList)) {
            return;
        }
        grantTemplateList.forEach(record ->
                grantMultiRule(groupId, templateRule.getTemplateId(), record.getCompanyId(), record.getTemplateId())
        );
    }

    @Override
    public List<ConsumeTemplateRuleInfo> queryRuleInfo(String ruleId, String groupStatus, String companyId,
                                                       String templateId, RuleTypeEnum ruleTypeEnum) {
        log.info("ConsumeTemplateRuleServiceImpl#queryRuleInfo ruleId={}, groupStatus={}, companyId={}, templateId={}",
                ruleId, groupStatus, companyId, templateId);
        if (ObjUtils.isEmpty(companyId)
                || ObjUtils.isEmpty(templateId)) {
            throw new SaasException(GlobalResponseCode.ParametersIsNull);
        }
        ConsumeTemplateRulesExample example = new ConsumeTemplateRulesExample();
        ConsumeTemplateRulesExample.Criteria criteria = example.createCriteria();
        if (!StringUtils.isEmpty(groupStatus)) {
            criteria.andGroupStatusEqualTo(Integer.valueOf(groupStatus));
        }
        criteria.andCompanyIdEqualTo(companyId);
        criteria.andTemplateIdEqualTo(templateId);
        criteria.andDeleteStatusEqualTo(DeleteEnum.NORMAL.getValue());
        List<ConsumeTemplateRules> consumeTemplateRules = consumeTemplateRulesMapper.selectByExample(example);
        List<ConsumeTemplateRuleInfo> result = new ArrayList<>();
        consumeTemplateRules.forEach(consumeTemplateRule -> {
            ConsumeTemplateUsedRangeExample usedRangeExample = new ConsumeTemplateUsedRangeExample();
            usedRangeExample.createCriteria()
                    .andRuleIdEqualTo(consumeTemplateRule.getId())
                    .andDeleteStatusEqualTo(DeleteEnum.NORMAL.getValue());
            List<ConsumeTemplateUsedRange> consumeTemplateUsedRanges =
                    consumeTemplateUsedRangeMapper.selectByExample(usedRangeExample);
            List<ConsumeTemplateItemInfo> itemInfos = consumeTemplateUsedRanges.stream()
                    .map(item -> new ConsumeTemplateItemInfo(item.getItemId(), item.getItemName()))
                    .collect(Collectors.toList());
            fixRuleName(consumeTemplateRule);
            fixItemName(companyId, ruleTypeEnum,
                    ConsumeTypeEnum.valueOf(consumeTemplateRule.getType()), itemInfos);
            result.add(buildConsumeTemplateRuleInfo(consumeTemplateRule, itemInfos));
        });
        return result.stream()
                .sorted(Comparator.comparing(ConsumeTemplateRuleInfo::getBizType).thenComparing(ConsumeTemplateRuleInfo::getPriorityNo))
                .collect(Collectors.toList());
    }

    private void fixItemName(String companyId, RuleTypeEnum ruleType,
                             ConsumeTypeEnum consumeType, List<ConsumeTemplateItemInfo> itemInfos) {
        if (ObjUtils.isEmpty(itemInfos)) {
            return;
        }
        List<String> itemIdList = itemInfos.stream().map(ConsumeTemplateItemInfo::getItemId).collect(Collectors.toList());
        Map<String, String> itemMap = null;
        switch (consumeType) {
            case PROJECT:
                itemMap = costCenterService.batchQueryCostCenterName(companyId, itemIdList, ruleType);
                break;
            case PROJECT_GROUP:
                itemMap = costCenterService.batchQueryGroupCostCenterName(itemIdList);
                break;
            case ORDER_REASON:
                CompanyCustomReasonExample example = new CompanyCustomReasonExample();
                example.createCriteria().andIdIn(itemIdList.stream().map(Integer::parseInt).collect(Collectors.toList()));
                List<CompanyCustomReason> reasonList = companyCustomReasonMapper.selectByExample(example);
                itemMap = reasonList.stream()
                        .collect(Collectors.toMap(reason -> String.valueOf(reason.getId()), CompanyCustomReason::getName));
            default:
                log.warn("查询更新后的范围名称, type:{}", consumeType);
        }
        if (ObjUtils.isEmpty(itemMap)) {
            return;
        }
        for (ConsumeTemplateItemInfo item : itemInfos) {
            item.setItemName(itemMap.getOrDefault(item.getItemId(), item.getItemName()));
        }
    }

    @Override
    public List<ConsumeTemplateRuleDto> queryTemplateMultiRuleInfo(String companyId, String templateId, Integer bizType) {
        if (ObjUtils.isEmpty(companyId)
                || ObjUtils.isEmpty(templateId)
                || ObjUtils.isEmpty(bizType)) {
            throw new SaasException(GlobalResponseCode.ParametersIsNull);
        }
        ConsumeTemplateRulesExample templateRulesExample = new ConsumeTemplateRulesExample();
        templateRulesExample.createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andTemplateIdEqualTo(templateId)
                .andBizTypeEqualTo(bizType)
                .andDeleteStatusEqualTo(DeleteEnum.NORMAL.getValue());
        templateRulesExample.setOrderByClause("priority_no");
        List<ConsumeTemplateRules> templateRulesList = consumeTemplateRulesMapper.selectByExample(templateRulesExample);
        log.info("查询模板下关联的规则, templateRulesList:{}", JsonUtils.toJson(templateRulesList));
        if (ObjUtils.isEmpty(templateRulesList)) {
            return Lists.newArrayList();
        }
        List<String> idList = templateRulesList.stream().map(ConsumeTemplateRules::getId).collect(Collectors.toList());
        ConsumeTemplateUsedRangeExample usedRangeExample = new ConsumeTemplateUsedRangeExample();
        usedRangeExample.createCriteria()
                .andRuleIdIn(idList)
                .andDeleteStatusEqualTo(DeleteEnum.NORMAL.getValue());
        List<ConsumeTemplateUsedRange> usedRangeList = consumeTemplateUsedRangeMapper.selectByExample(usedRangeExample);
        Map<String, List<ConsumeTemplateUsedRange>> usedRangeMap = usedRangeList.stream()
                .collect(Collectors.groupingBy(ConsumeTemplateUsedRange::getRuleId));
        log.info("查询模板下关联的规则, usedRangeMap:{}", JsonUtils.toJson(usedRangeMap));

        return templateRulesList.stream()
                .map(templateRule -> {
                    List<ConsumeTemplateUsedRange> rangeList = usedRangeMap.getOrDefault(templateRule.getId(), Lists.newArrayList());
                    List<ConsumeTemplateItemInfo> itemInfoList = rangeList.stream()
                            .map(item -> new ConsumeTemplateItemInfo(item.getItemId(), item.getItemName()))
                            .collect(Collectors.toList());
                    ConsumeTemplateRuleInfo templateRuleInfo = new ConsumeTemplateRuleInfo();
                    BeanUtils.copyProperties(templateRule, templateRuleInfo);
                    fixRuleName(templateRuleInfo);
                    fixItemName(companyId, RuleTypeEnum.COMPANY, ConsumeTypeEnum.valueOf(templateRule.getType()), itemInfoList);
                    List<ConsumeTemplateItemDto> itemList = itemInfoList.stream()
                            .map(item -> new ConsumeTemplateItemDto(item.getItemId(), item.getItemName()))
                            .collect(Collectors.toList());
                    return ConsumeTemplateRuleDto.builder()
                            .name(templateRuleInfo.getName())
                            .type(templateRule.getType())
                            .typeName(ConsumeTypeEnum.valueOf(templateRule.getType()).getDesc())
                            .priorityNo(templateRule.getPriorityNo())
                            .priorityType(templateRule.getPriorityType())
                            .priorityTypeName(PriorityTypeEnum.valueOf(templateRule.getPriorityType()).getDesc())
                            .consumerRule(templateRule.getConsumerRule())
                            .isAble(templateRule.getIsAble())
                            .ruleId(templateRule.getRuleId())
                            .ruleName(templateRule.getRuleName())
                            .bizType(templateRule.getBizType())
                            .groupStatus(templateRule.getGroupStatus())
                            .templateId(templateRule.getTemplateId())
                            .companyId(templateRule.getCompanyId())
                            .consumeTemplateItemList(itemList)
                            .build();
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<ConsumeTemplateRuleInfo> queryConsumeTemplateRule(String eventId, String templateId, String companyId,
                                                                  List<String> projectIds, BizType bizType) {
        // 查询主表数据
        ConsumeTemplateRulesExample example = new ConsumeTemplateRulesExample();
        example.createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andTemplateIdEqualTo(templateId)
                .andDeleteStatusEqualTo(DeleteEnum.NORMAL.getValue())
                .andBizTypeEqualTo(bizType.getValue());
        List<ConsumeTemplateRules> consumeTemplateRules = consumeTemplateRulesMapper.selectByExample(example);
        log.info("[一人多规则] 查询多规则配置, consumeTemplateRules:{}", JsonUtils.toJson(consumeTemplateRules));

        List<String> itemIdList = Lists.newArrayList();
        if (ObjUtils.isNotEmpty(eventId)) {
            itemIdList.add(eventId);
        }
        if (ObjUtils.isNotEmpty(projectIds)) {
            itemIdList.addAll(projectIds);
        }
        List<ConsumeTemplateRuleInfo> result = new ArrayList<>();
        // 添加员工维度数据
        ConsumeTemplateRules templateRules = consumeTemplateRules.stream()
                .filter(templateRule -> Objects.equals(templateRule.getPriorityType(), PriorityTypeEnum.DEFAULT.getType()))
                .findFirst().orElse(null);
        if (ObjUtils.isEmpty(templateRules)) {
            log.warn("[一人多规则] 消费规则数据错误 缺失默认员工维度数据, companyId:{}, templateId:{}", companyId, templateId);
            throw new SaasException(GlobalResponseCode.InnerError);
        }
        result.add(buildConsumeTemplateRuleInfo(templateRules, Lists.newArrayList()));
        if (ObjUtils.isNotEmpty(itemIdList)) {
            // 查询项目所在分组
            if (ObjUtils.isNotEmpty(projectIds)) {
                itemIdList.addAll(costCenterService.batchQueryGroupByCostCenterId(companyId, projectIds));
            }
            consumeTemplateRules.stream()
                    .filter(templateRule -> Objects.equals(templateRule.getPriorityType(), PriorityTypeEnum.NORMAL.getType()))
                    .forEach(rule -> {
                        ConsumeTemplateUsedRangeExample usedRangeExample = new ConsumeTemplateUsedRangeExample();
                        usedRangeExample.createCriteria()
                                .andRuleIdEqualTo(rule.getId())
                                .andItemIdIn(itemIdList)
                                .andDeleteStatusEqualTo(DeleteEnum.NORMAL.getValue());
                        // 查询子表-事项表
                        List<ConsumeTemplateUsedRange> consumeTemplateUsedRanges =
                                consumeTemplateUsedRangeMapper.selectByExample(usedRangeExample);
                        log.info("[一人多规则] 查询多规则配置, consumeTemplateUsedRanges:{}", JsonUtils.toJson(consumeTemplateUsedRanges));
                        if (ObjUtils.isNotEmpty(consumeTemplateUsedRanges)) {
                            List<ConsumeTemplateItemInfo> itemInfos = consumeTemplateUsedRanges.stream()
                                    .filter(item -> itemIdList.contains(item.getItemId()))
                                    .map(item -> new ConsumeTemplateItemInfo(item.getItemId(), item.getItemName()))
                                    .collect(Collectors.toList());
                            result.add(buildConsumeTemplateRuleInfo(rule, itemInfos));
                        }
                    });
        }
        log.info("[一人多规则] 查询多规则配置, result:{}", JsonUtils.toJson(result));
        return result;
    }

    @Override
    public ConsumeTemplateRuleInfo getRuleInfo(String companyId, String id, RuleTypeEnum ruleTypeEnum) {
        log.info("ConsumeTemplateRuleServiceImpl#getRuleInfo companyId={}, id={}", companyId, id);
        if (StringUtils.isEmpty(id)) {
            log.info("id is empty:{}", id);
            throw new SaasException(GlobalResponseCode.ParameterIsNull);
        }
        ConsumeTemplateRulesExample example = new ConsumeTemplateRulesExample();
        example.createCriteria()
                .andDeleteStatusEqualTo(DeleteEnum.NORMAL.getValue())
                .andIdEqualTo(id);
        List<ConsumeTemplateRules> consumeTemplateRules = consumeTemplateRulesMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(consumeTemplateRules)) {
            return new ConsumeTemplateRuleInfo();
        }
        ConsumeTemplateRules consumeTemplateRule = consumeTemplateRules.get(0);
        ConsumeTemplateUsedRangeExample usedRangeExample = new ConsumeTemplateUsedRangeExample();
        usedRangeExample.createCriteria()
                .andRuleIdEqualTo(id);
        List<ConsumeTemplateUsedRange> consumeTemplateUsedRanges =
                consumeTemplateUsedRangeMapper.selectByExample(usedRangeExample);
        List<ConsumeTemplateItemInfo> itemInfos = consumeTemplateUsedRanges.stream()
                .map(item -> new ConsumeTemplateItemInfo(item.getItemId(), item.getItemName()))
                .collect(Collectors.toList());
        return buildConsumeTemplateRuleInfo(consumeTemplateRule, itemInfos);
    }

    @Override
    public ControlOrderRuleDto getConsumeRule(String eventId, String templateId, String companyId,
                                              List<String> projectIds, BizType bizType, String clientVersion) {
        log.info("[一人多规则] eventId:{}, templateId:{}, companyId:{}, projectIds:{}, clientVersion:{}",
                eventId, templateId, companyId, JsonUtils.toJson(projectIds), clientVersion);
        // 如果是老版本，强制升级
        if (VersionTool.lessThan(clientVersion, MULTI_RULE_VERSION)) {
            throw new SaasException(GlobalResponseCode.APPLYFLOWDETAILERROR, SaasResponseConstant.TOAST_MSG);
        }

        if (ObjUtils.isEmpty(templateId)) {
            log.warn("[一人多规则] templateId 为空");
            throw new SaasException(GlobalResponseCode.ParametersIsNull);
        }
        // 处理projectId为空的
        if (ObjUtils.isNotEmpty(projectIds)) {
            projectIds = projectIds.stream().filter(projectId -> !StringUtils.isEmpty(projectId)).collect(Collectors.toList());
        }
        // 获取消费规则列表
        List<ConsumeTemplateRuleInfo> consumeTemplateRuleInfos =
                queryConsumeTemplateRule(eventId, templateId, companyId, projectIds, bizType);
        // 按照优先级类型分组并用序号排序
        Map<Integer, List<ConsumeTemplateRuleInfo>> ruleMap = consumeTemplateRuleInfos.stream()
                .collect(Collectors.groupingBy(ConsumeTemplateRuleInfo::getPriorityType));
        // 获取高优先级的消费规则
        // 默认优先级的规则列表
        List<ConsumeTemplateRuleInfo> defaultPriorityRuleList = ruleMap.get(PriorityTypeEnum.DEFAULT.getType());
        // 普通优先级的规则列表
        List<ConsumeTemplateRuleInfo> normalPriorityRuleList = ruleMap.get(PriorityTypeEnum.NORMAL.getType());
        ConsumeTemplateRuleInfo consumeTemplateRuleInfo;
        // 普通优先级  取排序最小的规则
        if (ObjUtils.isNotEmpty(normalPriorityRuleList)) {
            normalPriorityRuleList = normalPriorityRuleList.stream()
                    .sorted(Comparator.comparingInt(ConsumeTemplateRuleInfo::getPriorityNo))
                    .collect(Collectors.toList());
            consumeTemplateRuleInfo = normalPriorityRuleList.get(0);
        }
        // 走默认的员工规则 只有一条数据
        else {
            consumeTemplateRuleInfo = defaultPriorityRuleList.get(0);
        }
        // 组装返回结果(高优先级规则 + 规则列表数据)
        ControlOrderRuleDto controlOrderRuleDto = new ControlOrderRuleDto();
        controlOrderRuleDto.setIsEnable(Objects.equals(consumeTemplateRuleInfo.getIsAble(), 1));
        controlOrderRuleDto.setName(consumeTemplateRuleInfo.getName());

        fixRuleName(consumeTemplateRuleInfo);
        fixItemName(companyId, RuleTypeEnum.COMPANY, ConsumeTypeEnum.valueOf(consumeTemplateRuleInfo.getType()),
                consumeTemplateRuleInfo.getConsumeTemplateItemInfos());
        List<String> itemList = consumeTemplateRuleInfo.getConsumeTemplateItemInfos().stream()
                .map(ConsumeTemplateItemInfo::getItemName)
                .collect(Collectors.toList());
        // 高优先级的适用范围，使用逗号分割
        controlOrderRuleDto.setRange(ObjUtils.isEmpty(itemList) ? "员工" : String.join(",", itemList));
        controlOrderRuleDto.setRuleId(consumeTemplateRuleInfo.getRuleId());
        controlOrderRuleDto.setRangeDesc(String.format("本次订单按照管控维度%s，通过%s获取的消费规则进行管控",
                consumeTemplateRuleInfo.getName(), controlOrderRuleDto.getRange()));
        controlOrderRuleDto.setConsumeTemplateRuleDtoList(consumeTemplateRuleInfos.stream()
                .map(this::buildConsumeTemplateRuleDto).collect(Collectors.toList()));
        log.info("[一人多规则] 管控维度 controlOrderRuleDto:{}", JsonUtils.toJson(controlOrderRuleDto));
        return controlOrderRuleDto;
    }

    /**
     * 获取最新规则名称
     */
    private void fixRuleName(ConsumeTemplateRules templateRule) {
        if (ObjUtils.isEmpty(templateRule)) {
            return;
        }
        String latestRuleName = getLatestRuleName(templateRule.getRuleId(), BizType.valueOf(templateRule.getBizType()));
        String ruleName = ObjUtils.toString(latestRuleName, templateRule.getRuleName());
        templateRule.setRuleName(ruleName);
    }

    private void fixRuleName(ConsumeTemplateRuleInfo templateRule) {
        if (ObjUtils.isEmpty(templateRule)) {
            return;
        }
        String latestRuleName = getLatestRuleName(templateRule.getRuleId(), BizType.valueOf(templateRule.getBizType()));
        String ruleName = ObjUtils.toString(latestRuleName, templateRule.getRuleName());
        templateRule.setRuleName(ruleName);
    }

    private void fixRuleName(ConsumeTemplateRuleDto templateRule) {
        if (ObjUtils.isEmpty(templateRule)) {
            return;
        }
        String latestRuleName = getLatestRuleName(templateRule.getRuleId(), BizType.valueOf(templateRule.getBizType()));
        String ruleName = ObjUtils.toString(latestRuleName, templateRule.getRuleName());
        templateRule.setRuleName(ruleName);
    }

    private String getLatestRuleName(String ruleId, BizType bizType) {
        // 规则不限制的时候  客户端存在传0的情况  需要兼容
        if (ObjUtils.isEmpty(ruleId) || Objects.equals(ruleId, "0")) {
            return null;
        }
        if (bizType == BizType.Air) {
            AirRule rule = airRuleMapper.selectByPrimaryKey(ruleId);
            return Optional.ofNullable(rule).orElse(new AirRule()).getName();
        } else if (bizType == BizType.Hotel) {
            HotelRule rule = hotelRuleMapper.selectByPrimaryKey(ruleId);
            return Optional.ofNullable(rule).orElse(new HotelRule()).getName();
        } else if (bizType == BizType.Train) {
            TrainRule rule = trainRuleMapper.selectByPrimaryKey(ruleId);
            return Optional.ofNullable(rule).orElse(new TrainRule()).getName();
        }
        return null;
    }

    @Override
    public void sort(List<ConsumeTemplateRuleInfo> ruleInfoList, RuleTypeEnum ruleTypeEnum, Boolean isSortOnly) {
        if (ObjUtils.isEmpty(ruleInfoList)) {
            throw new SaasException(GlobalResponseCode.ParametersIsNull);
        }
        // 排序信息必传
        ruleInfoList.forEach(ruleInfo -> {
            // id
            if (ObjUtils.isEmpty(ruleInfo.getId())
                    // 排序
                    || ObjUtils.isEmpty(ruleInfo.getPriorityNo())
                    // 排序类型
                    || ObjUtils.isEmpty(ruleInfo.getPriorityType())) {
                throw new SaasException(GlobalResponseCode.ParametersIsNull);
            }
        });
        // 过滤掉不需要排序的最低优先级
        ruleInfoList = ruleInfoList.stream().filter(info -> Objects.equals(info.getPriorityType(), 1)).collect(Collectors.toList());
        List<String> ruleIdList = ruleInfoList.stream().map(ConsumeTemplateRuleInfo::getId).collect(Collectors.toList());
        ConsumeTemplateRulesExample example = new ConsumeTemplateRulesExample();
        example.createCriteria().andIdIn(ruleIdList);
        List<ConsumeTemplateRules> templateRulesList = consumeTemplateRulesMapper.selectByExample(example);
        if (ObjUtils.isEmpty(templateRulesList)) {
            return;
        }
        // 入参的id和查询出的结果不一致  存在错误数据
        if (templateRulesList.size() != ruleIdList.size()) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
        // 批量更新排序
        List<ConsumeTemplateRules> rulesList = ruleInfoList.stream()
                .map(info -> {
                    ConsumeTemplateRules rule = new ConsumeTemplateRules();
                    rule.setName(info.getName());
                    rule.setId(info.getId());
                    rule.setPriorityNo(info.getPriorityNo());
                    rule.setPriorityType(info.getPriorityType());
                    return rule;
                })
                .collect(Collectors.toList());
        log.info("排序前的数据, rulesList:{}", JsonUtils.toJson(rulesList));
        consumeTemplateRulesExtMapper.batchUpdateSelective(rulesList);

        if (ruleTypeEnum == RuleTypeEnum.GROUP && isSortOnly) {
            ConsumeTemplateRules templateRule = templateRulesList.get(0);
            syncSort(templateRule.getCompanyId(), templateRule.getTemplateId());
        }
    }

    /**
     * 排序下发
     */
    private void syncSort(String groupId, String templateId) {
        List<EmployeeBaseRule> grantTemplateList = employeeTemplateService.queryTemplateGrantRecord(groupId, templateId);
        if (ObjUtils.isEmpty(grantTemplateList)) {
            return;
        }
        grantTemplateList.forEach(template ->
                grantMultiRule(groupId, templateId, template.getCompanyId(), template.getTemplateId())
        );
    }

    @Override
    public Map<String, Map<Integer, String>> bathQueryRuleName(String companyId, List<String> templateIdList) {
        if (ObjUtils.isEmpty(companyId)
                || ObjUtils.isEmpty(templateIdList)) {
            throw new SaasException(GlobalResponseCode.ParametersIsNull);
        }
        ConsumeTemplateRulesExample example = new ConsumeTemplateRulesExample();
        example.createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andTemplateIdIn(templateIdList)
                .andDeleteStatusEqualTo(DeleteEnum.NORMAL.getValue());
        example.setOrderByClause("priority_no");
        List<ConsumeTemplateRules> templateRulesList = consumeTemplateRulesMapper.selectByExample(example);
        Map<String, Map<Integer, String>> result = Maps.newHashMap();
        // 按照 templateId(k), (bizType(k), ruleName(v))(v)  的结构封装规则名称
        // ruleName 是按照逗号分割的该场景下的所有规则名称
        templateRulesList.stream()
                .collect(Collectors.groupingBy(ConsumeTemplateRules::getTemplateId))
                .forEach((templateId, v) -> {
                    Map<Integer, String> ruleNameMap = Maps.newHashMap();
                    Map<Integer, List<ConsumeTemplateRules>> ruleBizMap = v.stream()
                            .collect(Collectors.groupingBy(ConsumeTemplateRules::getBizType));
                    ruleBizMap.forEach((bizType, rule) -> {
                        String ruleName = rule.stream()
                                .map(ConsumeTemplateRules::getRuleName)
                                .collect(Collectors.joining("，"));
                        ruleNameMap.put(bizType, ruleName);
                    });
                    result.put(templateId, ruleNameMap);
                });
        return result;
    }

    @Override
    public void onTemplateSuccess(String companyId, String templateId) {
        if (ObjUtils.isEmpty(companyId)
                || ObjUtils.isEmpty(templateId)) {
            throw new SaasException(GlobalResponseCode.ParametersIsNull);
        }
        ConsumeTemplateRulesExample example = new ConsumeTemplateRulesExample();
        example.createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andTemplateIdEqualTo(templateId)
                .andDeleteStatusEqualTo(DeleteEnum.NORMAL.getValue());
        List<ConsumeTemplateRules> rulesList = consumeTemplateRulesMapper.selectByExample(example);
        log.info("更新模板下的多规则, rulesList:{}", JsonUtils.toJson(rulesList));
        if (ObjUtils.isEmpty(rulesList)) {
            return;
        }
        rulesList.forEach(rule -> rule.setActiveStatus(1));
        log.info("更新模板下的多规则, rulesList:{}", JsonUtils.toJson(rulesList));
        consumeTemplateRulesExtMapper.batchUpdateSelective(rulesList);
    }

    @Override
    @Transactional(transactionManager = "fenbei-rules", rollbackFor = Exception.class)
    public void grantMultiRule(GrantMultiRuleReqDTO req) {
        if (ObjUtils.isEmpty(req)
                || ObjUtils.isEmpty(req.getGroupId())
                || ObjUtils.isEmpty(req.getTemplateId())
                || ObjUtils.isEmpty(req.getDetailList())) {
            throw new SaasException(GlobalResponseCode.ParametersIsNull);
        }

        // 集团多规则信息
        List<GrantMultiRuleDetailDTO> detailList = req.getDetailList();
        detailList.forEach(detail -> {
            String companyId = detail.getCompanyId();
            String templateId = detail.getTemplateId();
            // 下发信息检查
            if (ObjUtils.isEmpty(companyId)
                    || ObjUtils.isEmpty(templateId)) {
                throw new SaasException(GlobalResponseCode.ParametersIsNull);
            }
            // 下发
            grantMultiRule(req.getGroupId(), req.getTemplateId(), companyId, templateId);
        });
    }

    private void grantMultiRule(String groupId, String templateId, String subCompanyId, String subTemplateId) {
        log.info("多规则下发, groupId:{}, templateId:{}, subCompanyId:{}, subTemplateId:{}",
                groupId, templateId, subCompanyId, subTemplateId);
        Date now = new Date();
        ConsumeTemplateRulesExample templateRulesExample = new ConsumeTemplateRulesExample();
        templateRulesExample.createCriteria()
                .andCompanyIdEqualTo(groupId)
                .andTemplateIdEqualTo(templateId)
                .andDeleteStatusEqualTo(DeleteEnum.NORMAL.getValue());
        List<ConsumeTemplateRules> originTemplateRulesList = consumeTemplateRulesMapper.selectByExample(templateRulesExample);
        if (originTemplateRulesList.stream()
                .anyMatch(templateRule -> Objects.equals(templateRule.getType(), ConsumeTypeEnum.ORDER_REASON.getType()))) {
            log.warn("不支持事由多规则下发, groupId:{}, templateId:{}", groupId, templateId);
        }
        List<String> ruleIdList = originTemplateRulesList.stream()
                .map(ConsumeTemplateRules::getRuleId)
                .filter(ObjUtils::isNotEmpty)
                .collect(Collectors.toList());
        // 下发规则
        grantRule(groupId, originTemplateRulesList, subCompanyId);
        // 查询下发后的规则id
        Map<String, String> grantedRuleIdMap = getGrantedRuleId(groupId, ruleIdList, subCompanyId);
        Map<String, ConsumeTemplateRules> templateRuleMap = Maps.newHashMap();
        List<ConsumeTemplateRules> templateRulesList = originTemplateRulesList.stream()
                .map(templateRule -> {
                    ConsumeTemplateRules subTemplateRule = new ConsumeTemplateRules();
                    BeanUtils.copyProperties(templateRule, subTemplateRule);
                    String id = IDTool.CreateUniqueID();
                    subTemplateRule.setId(id);
                    subTemplateRule.setCompanyId(subCompanyId);
                    subTemplateRule.setTemplateId(subTemplateId);
                    subTemplateRule.setRuleId(grantedRuleIdMap.getOrDefault(templateRule.getRuleId(), templateRule.getRuleId()));
                    subTemplateRule.setGroupStatus(0);
                    subTemplateRule.setCreateTime(now);
                    subTemplateRule.setUpdateTime(now);
                    templateRuleMap.put(templateRule.getId(), subTemplateRule);
                    return subTemplateRule;
                })
                .collect(Collectors.toList());
        // 先删除子企业配置
        templateRulesExample.clear();
        templateRulesExample.createCriteria()
                .andCompanyIdEqualTo(subCompanyId)
                .andTemplateIdEqualTo(subTemplateId)
                .andDeleteStatusEqualTo(DeleteEnum.NORMAL.getValue());
        List<ConsumeTemplateRules> subTemplateRulesList = consumeTemplateRulesMapper.selectByExample(templateRulesExample);
        if (ObjUtils.isNotEmpty(subTemplateRulesList)) {
            subTemplateRulesList.forEach(templateRule -> {
                templateRule.setDeleteStatus(DeleteEnum.DELETED.getValue());
                templateRule.setUpdateTime(now);
            });
            consumeTemplateRulesExtMapper.batchUpdateSelective(subTemplateRulesList);
            // 删除使用范围
            List<String> subTemplateRuleIdList = subTemplateRulesList.stream()
                    .map(ConsumeTemplateRules::getId)
                    .collect(Collectors.toList());
            if (ObjUtils.isNotEmpty(subTemplateRuleIdList)) {
                ConsumeTemplateUsedRangeExample rangeExample = new ConsumeTemplateUsedRangeExample();
                rangeExample.createCriteria().andRuleIdIn(subTemplateRuleIdList);
                List<ConsumeTemplateUsedRange> rangeList = consumeTemplateUsedRangeMapper.selectByExample(rangeExample);
                rangeList.forEach(range -> range.setDeleteStatus(DeleteEnum.DELETED.getValue()));
                if (ObjUtils.isNotEmpty(rangeList)) {
                    consumeTemplateUsedRangeExtMapper.batchUpdateSelective(rangeList);
                }
            }
        }
        // 下发
        if (ObjUtils.isEmpty(templateRulesList)) {
            log.warn("查询集团的配置数据为空, groupId:{}, templateId:{}", groupId, templateId);
            return;
        }
        consumeTemplateRulesExtMapper.batchInsert(templateRulesList);

        ConsumeTemplateUsedRangeExample rangeExample = new ConsumeTemplateUsedRangeExample();
        originTemplateRulesList.forEach(templateRule -> {
            rangeExample.clear();
            rangeExample.createCriteria()
                    .andRuleIdEqualTo(templateRule.getId())
                    .andDeleteStatusEqualTo(DeleteEnum.NORMAL.getValue());
            List<ConsumeTemplateUsedRange> originRangeList = consumeTemplateUsedRangeMapper.selectByExample(rangeExample);
            List<String> itemIdList = originRangeList.stream().map(ConsumeTemplateUsedRange::getItemId).collect(Collectors.toList());
            ConsumeTemplateRules subTemplateRule = templateRuleMap.getOrDefault(templateRule.getId(), new ConsumeTemplateRules());
            Map<String, String> subItemMap = getSubItemId(ConsumeTypeEnum.valueOf(templateRule.getType()), groupId, itemIdList, subCompanyId);
            List<ConsumeTemplateUsedRange> rangeList = originRangeList.stream()
                    .map(item -> {
                        ConsumeTemplateUsedRange subItem = new ConsumeTemplateUsedRange();
                        BeanUtils.copyProperties(item, subItem);
                        subItem.setId(IDTool.CreateUniqueID());
                        subItem.setRuleId(subTemplateRule.getId());
                        subItem.setItemId(subItemMap.getOrDefault(item.getItemId(), item.getItemId()));
                        subItem.setDeleteStatus(DeleteEnum.NORMAL.getValue());
                        return subItem;
                    })
                    .collect(Collectors.toList());
            // 下发使用范围
            if (ObjUtils.isNotEmpty(rangeList)) {
                consumeTemplateUsedRangeExtMapper.batchInsert(rangeList);
            }
        });

    }

    /**
     * 下发规则
     */
    private void grantRule(String groupId, List<ConsumeTemplateRules> templateRuleList, String subCompanyId) {
        if (ObjUtils.isEmpty(groupId) || ObjUtils.isEmpty(templateRuleList) || ObjUtils.isEmpty(subCompanyId)) {
            return;
        }
        log.info("下发规则, groupId:{}, templateRuleList:{}, subCompanyId:{}",
                groupId, JsonUtils.toJson(templateRuleList), subCompanyId);
        Map<Integer, List<ConsumeTemplateRules>> sceneList = templateRuleList.stream()
                .collect(Collectors.groupingBy(ConsumeTemplateRules::getBizType));
        sceneList.forEach((bizType, list) -> {
            GrantRuleReqDTO req = new GrantRuleReqDTO();
            req.setGroupId(groupId);
            req.setCompanyId(subCompanyId);
            req.setCurrentCompanyId(subCompanyId);
            req.setSceneType(bizType);
            req.setOperatorName("system");
            req.setOperatorId("system");
            req.setOpCompanyName("system");
            List<RuleInfoDTO> ruleInfoList = list.stream()
                    .filter(rule -> Objects.equals(rule.getIsAble(), 1))
                    .map(rule -> {
                        RuleInfoDTO info = new RuleInfoDTO();
                        info.setRuleId(rule.getRuleId());
                        return info;
                    })
                    .collect(Collectors.toList());
            req.setRuleInfoList(ruleInfoList);
            List<GrantRuleDTO> result = Lists.newArrayList();
            try {
                log.info("下发规则入参, bizType:{}, req:{}", bizType, JsonUtils.toJson(req));
                result = iGrantRuleService.grantRule(req);
                log.info("下发规则返回, bizType:{}, result:{}", bizType, JsonUtils.toJson(result));
            } catch (Exception e) {
                log.warn("下发规则失败, bizType:{}, list:{}", bizType, JsonUtils.toJson(req), e);
            }
            if (result.size() != ruleInfoList.size()) {
                throw new SaasException(GlobalResponseCode.RULE_GRANT_FAILED);
            }
        });
    }

    /**
     * 查询下发后的规则id
     */
    private Map<String, String> getGrantedRuleId(String groupId, List<String> ruleIdList, String subCompanyId) {
        GrantConsumptionRuleExample example = new GrantConsumptionRuleExample();
        example.createCriteria()
                .andGroupIdEqualTo(groupId)
                .andSubCompanyIdEqualTo(subCompanyId)
                .andRuleIdIn(ruleIdList)
                .andRuleTypeEqualTo(0)
                .andGrantStatusEqualTo(2)
                .andIsDelEqualTo(DeleteEnum.NORMAL.getValue());
        List<GrantConsumptionRule> consumptionRuleList = grantConsumptionRuleMapper.selectByExample(example);
        return consumptionRuleList.stream()
                .collect(Collectors.toMap(GrantConsumptionRule::getRuleId, GrantConsumptionRule::getAuthRuleId));
    }

    private Map<String, String> getSubItemId(ConsumeTypeEnum consumeType, String groupId, List<String> itemIdList, String companyId) {
        Map<String, String> grantedItemIdMap = Maps.newHashMap();
        if (ObjUtils.isEmpty(itemIdList)) {
            return grantedItemIdMap;
        }
        if (consumeType == ConsumeTypeEnum.PROJECT) {
            grantedItemIdMap = costCenterService.batchQueryGrantedCostCenterId(groupId, itemIdList, companyId);
        } else if (consumeType == ConsumeTypeEnum.PROJECT_GROUP) {
            grantedItemIdMap = costCenterService.batchQueryGrantedGroupCostCenterId(groupId, itemIdList, companyId);
        } else {
            return grantedItemIdMap;
        }
        // 下发后的数据和入参不相等  存在未下发的项目/项目分组
        if (grantedItemIdMap.size() != itemIdList.size()) {
            throw new FinhubException(GlobalResponseCode.CONSUME_PROJECT_OR_PROJECT_GROUP_NOT_GRANTED.getCode(),
                    GlobalResponseCode.CONSUME_PROJECT_OR_PROJECT_GROUP_NOT_GRANTED.getMsg());
        }
        return grantedItemIdMap;
    }

    @Override
    @Transactional(transactionManager = "fenbei-rules", rollbackFor = Exception.class)
    public void copyMultiRule(String companyId, String srcTemplateId, String targetTemplateId) {
        if (ObjUtils.isEmpty(companyId)
                || ObjUtils.isEmpty(srcTemplateId)
                || ObjUtils.isEmpty(targetTemplateId)) {
            throw new SaasException(GlobalResponseCode.ParametersIsNull);
        }
        ConsumeTemplateRulesExample templateRulesExample = new ConsumeTemplateRulesExample();
        templateRulesExample.createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andTemplateIdEqualTo(srcTemplateId)
                .andDeleteStatusEqualTo(DeleteEnum.NORMAL.getValue());
        List<ConsumeTemplateRules> originTemplateRulesList = consumeTemplateRulesMapper.selectByExample(templateRulesExample);
        if (ObjUtils.isEmpty(originTemplateRulesList)) {
            log.info("规则信息不存在, companyId:{}, srcTemplateId:{}", companyId, srcTemplateId);
            return;
        }
        Date now = new Date();
        // 复制规则信息
        Map<String, String> templateRuleIdMap = Maps.newHashMap();
        List<ConsumeTemplateRules> templateRulesList = originTemplateRulesList.stream()
                .map(templateRule -> {
                    ConsumeTemplateRules subTemplateRule = new ConsumeTemplateRules();
                    BeanUtils.copyProperties(templateRule, subTemplateRule);
                    String id = IDTool.CreateUniqueID();
                    templateRuleIdMap.put(templateRule.getId(), id);
                    subTemplateRule.setId(id);
                    subTemplateRule.setTemplateId(targetTemplateId);
                    subTemplateRule.setCreateTime(now);
                    subTemplateRule.setUpdateTime(now);
                    return subTemplateRule;
                })
                .collect(Collectors.toList());
        consumeTemplateRulesExtMapper.batchInsert(templateRulesList);
        // 复制使用范围信息
        List<String> ruleIdList = originTemplateRulesList.stream()
                .map(ConsumeTemplateRules::getId)
                .collect(Collectors.toList());
        ConsumeTemplateUsedRangeExample rangeExample = new ConsumeTemplateUsedRangeExample();
        rangeExample.createCriteria()
                .andRuleIdIn(ruleIdList)
                .andDeleteStatusEqualTo(DeleteEnum.NORMAL.getValue());
        List<ConsumeTemplateUsedRange> templateUsedRangeList = consumeTemplateUsedRangeMapper.selectByExample(rangeExample);
        templateUsedRangeList = templateUsedRangeList.stream()
                .map(range -> {
                    ConsumeTemplateUsedRange subRange = new ConsumeTemplateUsedRange();
                    BeanUtils.copyProperties(range, subRange);
                    log.info("[一人多规则] 复制使用范围, 原始规则id templateRuleId:{}, 复制后的id:{}",
                            range.getRuleId(), templateRuleIdMap.get(range.getRuleId()));
                    subRange.setId(IDTool.CreateUniqueID());
                    subRange.setRuleId(templateRuleIdMap.get(range.getRuleId()));
                    subRange.setDeleteStatus(DeleteEnum.NORMAL.getValue());
                    return subRange;
                })
                .collect(Collectors.toList());
        if (ObjUtils.isNotEmpty(templateUsedRangeList)) {
            consumeTemplateUsedRangeExtMapper.batchInsert(templateUsedRangeList);
        }
    }

    @Override
    public void deleteMultiRule(String companyId, String templateId) {
        if (ObjUtils.isEmpty(companyId)
                || ObjUtils.isEmpty(templateId)) {
            throw new SaasException(GlobalResponseCode.ParametersIsNull);
        }
        ConsumeTemplateRulesExample templateRulesExample = new ConsumeTemplateRulesExample();
        templateRulesExample.createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andTemplateIdEqualTo(templateId);
        List<ConsumeTemplateRules> templateRulesList = consumeTemplateRulesMapper.selectByExample(templateRulesExample);
        if (ObjUtils.isEmpty(templateRulesList)) {
            return;
        }
        Date now = new Date();
        templateRulesList.forEach(templateRule -> {
            templateRule.setDeleteStatus(DeleteEnum.DELETED.getValue());
            templateRule.setUpdateTime(now);
        });
        consumeTemplateRulesExtMapper.batchUpdateSelective(templateRulesList);
    }

    @Override
    public Map<String, List<String>> queryMultiRuleTemplate(String companyId, Integer category, Integer subType,
                                                            List<String> ruleIdList) {
        if (ObjUtils.isEmpty(companyId)) {
            throw new SaasException(GlobalResponseCode.ParametersIsNull);
        }
        Map<String, List<String>> result = Maps.newHashMap();
        if (ObjUtils.isEmpty(ruleIdList)) {
            return result;
        }
        ConsumeTemplateRulesExample example = new ConsumeTemplateRulesExample();
        example.createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andRuleIdIn(ruleIdList)
                .andIsAbleEqualTo(1)
                .andBizTypeEqualTo(category)
                .andDeleteStatusEqualTo(DeleteEnum.NORMAL.getValue());
        List<ConsumeTemplateRules> templateRulesList = consumeTemplateRulesMapper.selectByExample(example);
        log.info("[一人多规则] templateRulesList:{}", JsonUtils.toJson(templateRulesList));
        // 按照规则id分组组装数据
        Map<String, List<ConsumeTemplateRules>> ruleMap = templateRulesList.stream()
                .collect(Collectors.groupingBy(ConsumeTemplateRules::getRuleId));
        ruleMap.forEach((ruleId, ruleList) -> {
            List<String> templateIdList = ruleList.stream().map(ConsumeTemplateRules::getTemplateId).collect(Collectors.toList());
            result.put(ruleId, templateIdList);
        });
        return result;
    }

    @Override
    public List<String> queryMultiRuleLimit(String companyId, Integer category) {
        if (ObjUtils.isEmpty(companyId)
                || ObjUtils.isEmpty(category)) {
            throw new SaasException(GlobalResponseCode.ParametersIsNull);
        }
        ConsumeTemplateRulesExample example = new ConsumeTemplateRulesExample();
        example.createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andBizTypeEqualTo(category)
                .andDeleteStatusEqualTo(DeleteEnum.NORMAL.getValue())
                .andActiveStatusEqualTo(1)
                .andRuleIdIsNotNull()
                .andRuleIdNotEqualTo("0");
        List<ConsumeTemplateRules> templateRulesList = consumeTemplateRulesMapper.selectByExample(example);
        return templateRulesList.stream()
                .map(ConsumeTemplateRules::getTemplateId)
                .collect(Collectors.toList());
    }

    // 组装数据
    private ConsumeTemplateRuleDto buildConsumeTemplateRuleDto(ConsumeTemplateRuleInfo consumeTemplateRuleInfo) {
        List<ConsumeTemplateItemDto> itemDtoList = consumeTemplateRuleInfo.getConsumeTemplateItemInfos().stream()
                .map(itemDto -> new ConsumeTemplateItemDto(itemDto.getItemId(), itemDto.getItemName()))
                .collect(Collectors.toList());
        return ConsumeTemplateRuleDto.builder()
                .name(consumeTemplateRuleInfo.getName())
                .type(consumeTemplateRuleInfo.getType())
                .priorityNo(consumeTemplateRuleInfo.getPriorityNo())
                .priorityType(consumeTemplateRuleInfo.getPriorityType())
                .consumerRule(consumeTemplateRuleInfo.getConsumerRule())
                .ruleId(consumeTemplateRuleInfo.getRuleId())
                .ruleName(consumeTemplateRuleInfo.getRuleName())
                .bizType(consumeTemplateRuleInfo.getBizType())
                .groupStatus(consumeTemplateRuleInfo.getGroupStatus())
                .templateId(consumeTemplateRuleInfo.getTemplateId())
                .companyId(consumeTemplateRuleInfo.getCompanyId())
                .consumeTemplateItemList(itemDtoList)
                .build();
    }

    private ConsumeTemplateRuleInfo buildConsumeTemplateRuleInfo(ConsumeTemplateRules consumeTemplateRule,
                                                                 List<ConsumeTemplateItemInfo> consumeTemplateItemInfos) {
        return ConsumeTemplateRuleInfo.builder().id(consumeTemplateRule.getId())
                .name(consumeTemplateRule.getName())
                .type(consumeTemplateRule.getType())
                .priorityNo(consumeTemplateRule.getPriorityNo())
                .priorityType(consumeTemplateRule.getPriorityType())
                .consumerRule(consumeTemplateRule.getConsumerRule())
                .ruleId(consumeTemplateRule.getRuleId())
                .ruleName(consumeTemplateRule.getRuleName())
                .bizType(consumeTemplateRule.getBizType())
                .groupStatus(consumeTemplateRule.getGroupStatus())
                .templateId(consumeTemplateRule.getTemplateId())
                .companyId(consumeTemplateRule.getCompanyId())
                .isAble(consumeTemplateRule.getIsAble())
                .consumeTemplateItemInfos(consumeTemplateItemInfos)
                .build();
    }

    /**
     * 修改消费模板的规则
     *
     * @param consumeTemplateRuleInfo consumeTemplateRuleUpdate
     */
    @Override
    @Transactional(value = "fenbei-rules", rollbackFor = Throwable.class)
    public void updateRule(ConsumeTemplateRuleInfo consumeTemplateRuleInfo, RuleTypeEnum ruleTypeEnum) {
        log.info("ConsumeTemplateRuleServiceImpl#updateRule consumeTemplateRuleInfo:{}", JsonUtils.toJson(consumeTemplateRuleInfo));
        if (ObjUtils.isEmpty(consumeTemplateRuleInfo)
                || ObjUtils.isEmpty(consumeTemplateRuleInfo.getId())) {
            throw new SaasException(GlobalResponseCode.ParametersIsNull);
        }
        ConsumeTemplateRulesExample example = new ConsumeTemplateRulesExample();
        example.createCriteria()
                .andDeleteStatusEqualTo(DeleteEnum.NORMAL.getValue())
                .andIdEqualTo(consumeTemplateRuleInfo.getId());
        List<ConsumeTemplateRules> consumeTemplateRules = consumeTemplateRulesMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(consumeTemplateRules)) {
            log.info("ConsumeTemplateRuleServiceImpl#updateRule consumeTemplateRule not exit");
            throw new SaasException(GlobalResponseCode.ConsumeTemplateDataNoExisted);
        }
        if (consumeTemplateRules.size() > 1) {
            log.warn("多规则更新, 错误的数据, consumeTemplateRules:{}", JsonUtils.toJson(consumeTemplateRules));
        }

        // 校验范围重复
        if (!Objects.equals(consumeTemplateRuleInfo.getPriorityType(), PriorityTypeEnum.DEFAULT.getType())
                && ObjUtils.isNotEmpty(consumeTemplateRuleInfo.getConsumeTemplateItemInfos())) {
            if (consumeTemplateRuleInfo.getConsumeTemplateItemInfos().stream()
                    .anyMatch(item -> ObjUtils.isEmpty(item.getItemId()) || ObjUtils.isEmpty(item.getItemName()))) {
                throw new SaasException(GlobalResponseCode.ParametersIsNull);
            }
            ConsumeTemplateRules consumeTemplateRule = consumeTemplateRules.get(0);
            example.clear();
            example.createCriteria()
                    .andTypeEqualTo(consumeTemplateRule.getType())
                    .andBizTypeEqualTo(consumeTemplateRule.getBizType())
                    .andCompanyIdEqualTo(consumeTemplateRule.getCompanyId())
                    .andTemplateIdEqualTo(consumeTemplateRule.getTemplateId())
                    .andDeleteStatusEqualTo(DeleteEnum.NORMAL.getValue());
            List<ConsumeTemplateRules> configList = consumeTemplateRulesMapper.selectByExample(example);
            configList = configList.stream()
                    .filter(config -> !Objects.equals(config.getId(), consumeTemplateRuleInfo.getId()))
                    .collect(Collectors.toList());
            log.info("ConsumeTemplateRuleServiceImpl#addRule configList:{}", JsonUtils.toJson(configList));
            if (configList.size() > 0) {
                List<String> ruleIds = configList.stream().map(ConsumeTemplateRules::getId).collect(Collectors.toList());
                List<ConsumeTemplateItemInfo> consumeTemplateItems = consumeTemplateRuleInfo.getConsumeTemplateItemInfos();
                List<String> itemIds = consumeTemplateItems.stream()
                        .map(ConsumeTemplateItemInfo::getItemId)
                        .collect(Collectors.toList());
                // 相同管控维度+适用范围,只能配置一条
                ConsumeTemplateUsedRangeExample usedRangeExample = new ConsumeTemplateUsedRangeExample();
                usedRangeExample.createCriteria()
                        .andRuleIdIn(ruleIds)
                        .andItemIdIn(itemIds)
                        .andDeleteStatusEqualTo(DeleteEnum.NORMAL.getValue());
                List<ConsumeTemplateUsedRange> consumeTemplateUsedRanges =
                        consumeTemplateUsedRangeMapper.selectByExample(usedRangeExample);
                if (consumeTemplateUsedRanges.size() > 0) {
                    log.info("ConsumeTemplateRuleServiceImpl#addRule consumeTemplateRule is exit:{}",
                            JsonUtils.toJson(consumeTemplateRule));
                    throw new SaasException(GlobalResponseCode.ConsumeTemplateRulesExisted.getCode(),
                            String.format(GlobalResponseCode.ConsumeTemplateRulesExisted.getMsg(),
                                    ConsumeTypeEnum.valueOf(consumeTemplateRule.getType()).getDesc(),
                                    consumeTemplateUsedRanges.stream().map(ConsumeTemplateUsedRange::getItemName).collect(Collectors.toList())),
                            SaasResponseConstant.TOAST_MSG);
                }
            }
        }

        ConsumeTemplateRules consumeTemplateRule = buildConsumeTemplateRuleUpdate(consumeTemplateRuleInfo);
        example.clear();
        example.createCriteria().andIdEqualTo(consumeTemplateRuleInfo.getId());
        consumeTemplateRulesMapper.updateByExampleSelective(consumeTemplateRule, example);
        // 删除并更新
        List<ConsumeTemplateItemInfo> templateItems = consumeTemplateRuleInfo.getConsumeTemplateItemInfos();
        if (CollectionUtils.isNotEmpty(templateItems)) {
            ConsumeTemplateUsedRange templateUsedRange = new ConsumeTemplateUsedRange();
            templateUsedRange.setDeleteStatus(DeleteEnum.DELETED.getValue());
            ConsumeTemplateUsedRangeExample templateUsedRangeExample = new ConsumeTemplateUsedRangeExample();
            templateUsedRangeExample.createCriteria().andRuleIdEqualTo(consumeTemplateRuleInfo.getId());
            consumeTemplateUsedRangeMapper.updateByExampleSelective(templateUsedRange, templateUsedRangeExample);
            for (ConsumeTemplateItemInfo consumeTemplateItem : templateItems) {
                ConsumeTemplateUsedRange consumeTemplateUsedRange = new ConsumeTemplateUsedRange();
                consumeTemplateUsedRange.setId(IDTool.CreateUniqueID());
                consumeTemplateUsedRange.setRuleId(consumeTemplateRuleInfo.getId());
                consumeTemplateUsedRange.setDeleteStatus(DeleteEnum.NORMAL.getValue());
                consumeTemplateUsedRange.setItemId(consumeTemplateItem.getItemId());
                consumeTemplateUsedRange.setItemName(consumeTemplateItem.getItemName());
                consumeTemplateUsedRangeMapper.insert(consumeTemplateUsedRange);
            }
        }

        if (ruleTypeEnum == RuleTypeEnum.GROUP) {
            syncUpdateRule(consumeTemplateRuleInfo);
        }
    }

    /**
     * 同步更新企业规则
     */
    private void syncUpdateRule(ConsumeTemplateRuleInfo consumeTemplateRule) {
        List<EmployeeBaseRule> grantTemplateList = employeeTemplateService.queryTemplateGrantRecord(consumeTemplateRule.getCompanyId(),
                consumeTemplateRule.getTemplateId());
        if (ObjUtils.isEmpty(grantTemplateList)) {
            return;
        }
        grantTemplateList.forEach(template ->
                grantMultiRule(consumeTemplateRule.getCompanyId(), consumeTemplateRule.getTemplateId(),
                        template.getCompanyId(), template.getTemplateId())
        );
    }

    private ConsumeTemplateRules buildConsumeTemplateRuleUpdate(ConsumeTemplateRuleInfo ruleUpdate) {
        ConsumeTemplateRules consumeTemplateRule = new ConsumeTemplateRules();
        if (!StringUtils.isEmpty(ruleUpdate.getName())) {
            consumeTemplateRule.setName(ruleUpdate.getName());
        }
        if (!StringUtils.isEmpty(ruleUpdate.getType())) {
            consumeTemplateRule.setType(ruleUpdate.getType());
        }
        if (!StringUtils.isEmpty(ruleUpdate.getPriorityNo())) {
            consumeTemplateRule.setPriorityNo(ruleUpdate.getPriorityNo());
        }
        if (!StringUtils.isEmpty(ruleUpdate.getPriorityType())) {
            consumeTemplateRule.setPriorityType(ruleUpdate.getPriorityType());
        }
        if (!StringUtils.isEmpty(ruleUpdate.getConsumerRule())) {
            consumeTemplateRule.setConsumerRule(ruleUpdate.getConsumerRule());
        }
        if (ObjUtils.isNotEmpty(ruleUpdate.getRuleId())) {
            consumeTemplateRule.setRuleId(ruleUpdate.getRuleId());
            // 兼容不限制时客户端传0的逻辑
            Integer isAble = !Objects.equals(ruleUpdate.getRuleId(), "0") ? 1 : 0;
            consumeTemplateRule.setIsAble(isAble);
        } else {
            consumeTemplateRule.setIsAble(0);
        }
        if (ObjUtils.isNotEmpty(ruleUpdate.getRuleName())) {
            consumeTemplateRule.setRuleName(ruleUpdate.getRuleName());
        }

        consumeTemplateRule.setUpdateTime(new Date());
        return consumeTemplateRule;
    }

}
