package com.fenbeitong.saas.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.common.enums.basis.SupplierType;
import com.fenbeitong.saas.api.model.dto.reason.ReasonConfig;
import com.fenbeitong.saas.core.common.base.SaasException;
import com.fenbeitong.saas.core.common.constant.GlobalResponseCode;
import com.fenbeitong.saas.core.common.constant.SaasFlowConstant;
import com.fenbeitong.saas.core.common.constant.SaasMessageConstant;
import com.fenbeitong.saas.core.contract.apply.*;
import com.fenbeitong.saas.core.contract.applyflow.ApplyFlowApplicateResponseContract;
import com.fenbeitong.saas.core.contract.applyflow.CompanyApplyFlowItemSetV2RequestContract;
import com.fenbeitong.saas.core.contract.applyflow.CompanyApplyFlowSetV2RequestContract;
import com.fenbeitong.saas.core.contract.common.IdNameContract;
import com.fenbeitong.saas.core.contract.common.KeyValueItem;
import com.fenbeitong.saas.core.contract.common.PushContract;
import com.fenbeitong.saas.core.contract.message.MessageSaveContract;
import com.fenbeitong.saas.core.contract.message.inner.ApplyInfo;
import com.fenbeitong.saas.core.contract.messagesettings.ApplySetupContract;
import com.fenbeitong.saas.core.contract.user.EmployeeNameAndDeptContract;
import com.fenbeitong.saas.core.contract.user.UserContactContract;
import com.fenbeitong.saas.core.dao.common.adapter.ApplyAdapterMapper;
import com.fenbeitong.saas.core.language.CoreLanguage;
import com.fenbeitong.saas.core.language.LanguageUtils;
import com.fenbeitong.saas.core.language.MessageLanguageEnum;
import com.fenbeitong.saas.core.model.enums.ApplyFlowUserItemStatus;
import com.fenbeitong.saas.core.model.enums.CompanyApplyType;
import com.fenbeitong.saas.core.model.enums.CostAttributionCategory;
import com.fenbeitong.saas.core.model.enums.apply.*;
import com.fenbeitong.saas.core.model.enums.message.MessageType;
import com.fenbeitong.saas.core.model.enums.message.SenderType;
import com.fenbeitong.saas.core.model.enums.reason.ReasonType;
import com.fenbeitong.saas.core.model.enums.user.UserRole;
import com.fenbeitong.saas.core.model.saas.*;
import com.fenbeitong.saas.core.model.v5.apply.constant.ApplyConstant;
import com.fenbeitong.saas.core.service.*;
import com.fenbeitong.saas.core.utils.flow.FlowCheckUtil;
import com.fenbeitong.saas.core.utils.notice.NoticeUtils;
import com.fenbeitong.saas.core.utils.notice.SmsContract;
import com.fenbeitong.saas.core.utils.notice.TemplateConstant;
import com.fenbeitong.saas.core.utils.tools.*;
import com.fenbeitong.saas.core.utils.url.ShortUrlUtils;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.fenbeitong.usercenter.api.service.employee.IBaseEmployeeExtService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.StrUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mockito.internal.util.collections.Sets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Transactional(transactionManager = "dynamic", readOnly = false, rollbackFor = Exception.class)
public class ApplyRefundChangeServiceImpl implements IApplyRefundChangeService {

    private static final Logger logger = LoggerFactory.getLogger(ApplyRefundChangeServiceImpl.class);

    private static final String URL_GET_HOTEL_ORDER_INFO = HostPropertyConfigTool.HOST_HOTEL_BIZ + "/internal/refund/hotel/order/detail";

    private static final String URL_GET_TRAIN_ORDER_INFO = HostPropertyConfigTool.HOST_TRAIN_BIZ + "/internal/orders/trains/detail_apply";

    private static final String URL_GET_AIR_REFUNGCHANGE_ORDER_INFO = HostPropertyConfigTool.HOST_INTL_AIR_BIZ + "/internal/refundChange/air/order/detail";

    private static final String URL_GET_AIR_REFUNG_ORDER_INFO = HostPropertyConfigTool.HOST_INTL_AIR_BIZ + "/internal/api/air/batchRefund";

    private static final String URL_GET_AIR_CHANGE_ORDER_INFO = HostPropertyConfigTool.HOST_INTL_AIR_BIZ + "/internal/api/air/order/endorse";

    private static final String URL_GET_TRAIN_REFUNG_ORDER_INFO = HostPropertyConfigTool.HOST_HYPERLOOP + "/v2/internal/api/train/order/refund";

    private static final String URL_GET_TRAIN_CHANGE_ORDER_INFO = HostPropertyConfigTool.HOST_HYPERLOOP + "/v2/internal/api/train/order/endorse";

    private static final String URL_GET_HOTEL_REFUNG_ORDER_INFO = HostPropertyConfigTool.HOST_HYPERLOOP + "/internal/hotel/order/refund";

    private static final String URL_GET_TRAIN_CHANGE_VALID_ORDER_INFO = HostPropertyConfigTool.HOST_HYPERLOOP + "/v2/internal/api/train/order/endorse_check";

    private static final String URL_GET_TRAIN_REFUNG_VALID_ORDER_INFO = HostPropertyConfigTool.HOST_TRAIN_BIZ + "/internal/orders/trains/refund_check";

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyOrderMapper applyOrderMapper;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyTripInfoMapper applyTripMapper;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyTripGuestMapper applyGuestMapper;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyTripGuestExtMapper applyGuestExtMapper;

    @Autowired
    private IApplyFlowService applyFlowService;

    @Autowired
    IUserService userService;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyApproverMapMapper applyApproverMapMapper;

    @Autowired
    IPushService pushService;

    @Autowired
    IMessageService messageService;

    @Autowired
    private IApplyFlowV2Service applyFlowV2Service;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyOrderLogExtMapper applyOrderLogExtMapper;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyOrderExtMapper applyOrderExtMapper;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyTripInfoExtMapper applyTripInfoExtMapper;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyTripApplicateMapper applyTripApplicateMapper;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyTripApplicateExtMapper applyTripApplicateExtMapper;

    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyFlowUserItemMapper applyFlowUserItemMapper;

    @Autowired
    private ApplyAdapterMapper applyAdapterMapper;

    @Autowired
    private ICustomRoleService customRoleService;

    @Autowired
    private IBaseOrganizationService iBaseOrganizationService;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyOrderLogMapper applyOrderLogMapper;

    @Autowired
    private IBaseEmployeeExtService iBaseEmployeeExtService;

    @Autowired
    private ICustomReasonService customReasonService;

    @Autowired
    IMessageSetupService messageSetupService;

    @Autowired
    private IApplyThirdService applyThirdService;

    @Autowired
    private IApplyV2Service applyV2Service;


    public void setMessageService(IMessageService messageService) {
        this.messageService = messageService;
    }


    /**
     * 创建申请单
     *
     * @param applyContract
     * @return
     */
    @Override
    //@Transactional(value = "saas")
    public GlobalResponseCode create(String token, ApplyRefundChangeContract applyContract, String userId, String companyId, String ip, String applyId, String clientHeadVersion, String clientType) throws SaasException {
        FlowCheckUtil.check(companyId, clientHeadVersion);
        // 是否是对接订单审批公司
        boolean isDocking = applyThirdService.isApplyRefundChangeDockingCompany(companyId);
        if (isDocking) {
            return applyThirdService.refundChangeCreate(token, applyContract, userId, companyId, ip, applyId, clientHeadVersion);
        }
        Integer logId = null;
        //检查数据信息
        GlobalResponseCode code = checkApplyData(applyContract, userId, companyId, clientHeadVersion, clientType);
        if (code != GlobalResponseCode.Success) {
            return code;
        }
        ApplyOrderV2Contract applyOrderContract = applyContract.getApply();
        applyOrderContract.setEmployee_id(userId);
        applyOrderContract.setCompany_id(companyId);
        ApplyType applyType = ApplyType.valueOf(applyOrderContract.getType().intValue());
        CompanyApplyType companyApplyType = CompanyApplyType.valueOf(applyOrderContract.getFlow_type());
        if (companyApplyType == CompanyApplyType.Unknown) {
            companyApplyType = CompanyApplyType.Elastic;
        }
        applyOrderContract.setFlow_type(companyApplyType.getValue());

        //如果是提交审批保存审批流设置和审批单日志记录
        String approverId = applyOrderContract.getApprover_id();
        CompanyApplyFlowSetV2RequestContract flowRequest = applyOrderContract.getFlow();
        if (flowRequest == null) {
            flowRequest = new CompanyApplyFlowSetV2RequestContract();
        }
        flowRequest.setCompany_apply_type(companyApplyType.getValue());
        flowRequest.setApply_type(applyType.getValue());
        flowRequest.setBudget(applyOrderContract.getBudget());
        ApplyFlowApplicateResponseContract flowResponse = null;
        //判断审批流的类型
        String costAttributionId = null;
        if (applyOrderContract.getCost_attribution_category() != null) {
            if (StringUtils.isBlank(applyOrderContract.getCost_attribution_name()) || StringUtils.isBlank(applyOrderContract.getCost_attribution_id())) {
                return GlobalResponseCode.CostAttributionIdIsNull;
            }
        }
        Integer category = applyOrderContract.getCost_attribution_category();
        costAttributionId = applyOrderContract.getCost_attribution_id();
        //处理超规类型
        Integer exceedBuyType = applyOrderContract.getExceed_buy_type();
        Integer bussinessType = applyOrderContract.getType();
        Integer subType = applyOrderContract.getSub_type();
        if (subType == 1) {
            exceedBuyType = 3;
        } else if (subType == 2) {
            exceedBuyType = 4;
        }
        //查询应用审批流
        flowResponse = applyFlowV2Service.applicateFlow(SaasFlowConstant.SETTING_TYPE_ROUTE, flowRequest, applyId, userId, companyId, ip, category, costAttributionId, exceedBuyType, bussinessType, applyId);
        if (flowResponse != null && !StringTool.isNullOrEmpty(flowResponse.getApprover())) {
            approverId = flowResponse.getApprover();
        }
        //设置当前审批人
        logId = flowResponse.getNextLogId();
        if (StringUtils.isNotBlank(approverId)) {
            applyOrderContract.setApprover_id(approverId);
        }
        applyOrderContract.setLog_id(ObjUtils.toLong(flowResponse.getNextLogId()));
        if (companyApplyType == CompanyApplyType.Elastic) {
            if (approverId != null && approverId.length() > 0) {
                //检测审批人是否属于当前这个公司
                Boolean isApproverInCompany = userService.isUserInCompany(approverId, companyId);
                if (!isApproverInCompany) {
                    return GlobalResponseCode.ApplyApproverNotInCompany;
                }
                Date now = new Date();
                ApplyOrderLog log = new ApplyOrderLog();
                log.setApplyOrderId(applyId);
                log.setRootApplyOrderId(applyId);
                log.setCreateTime(now);
                log.setIp(ip);
                log.setSponsorId(userId);
                log.setReceiverId(approverId);
                log.setCheckReason(null);
                log.setAction(ApplyLogAction.Submit.getValue());
                log.setSort(100);
                applyOrderLogMapper.insertSelective(log);

                ApplyOrderLog logApprove = new ApplyOrderLog();
                logApprove.setApplyOrderId(applyId);
                logApprove.setRootApplyOrderId(applyId);
                logApprove.setIp(ip);
                logApprove.setSponsorId(approverId);
                logApprove.setReceiverId("");
                logApprove.setCheckReason(null);
                logApprove.setAction(ApplyLogAction.Approval.getValue());
                logApprove.setSort(200);
                applyOrderLogMapper.insertSelective(logApprove);
                logId = logApprove.getId();
                applyOrderContract.setLog_id(ObjUtils.toLong(logId));
            }
        }
        //整理数据
        clearApplyData(applyContract);
        //待审核装填
        ApplyStatus applyState = ApplyStatus.PendingAudit;
        ApplyLogAction action = ApplyLogAction.Submit;
        Date now = new Date();

        List<ApplyTripInfoContract> tripList = applyContract.getTrip_list();
        ApplyOrder apply = applyOrderContract.ToModel();
        if (CollectionUtils.isNotEmpty(applyOrderContract.getExceed_buy_desc_list())) {
            apply.setExceedBuyDesc(String.join(";", applyOrderContract.getExceed_buy_desc_list()));
        }
        if (applyOrderContract.getCost_attribution_category() != null) {
            if (StringUtils.isBlank(applyOrderContract.getCost_attribution_name()) || StringUtils.isBlank(applyOrderContract.getCost_attribution_id())) {
                return GlobalResponseCode.CostAttributionIdIsNull;
            }
            apply.setCostAttributionId(applyOrderContract.getCost_attribution_id());
            apply.setCostAttributionName(applyOrderContract.getCost_attribution_name());
            apply.setCostAttributionCategory(applyOrderContract.getCost_attribution_category());
            apply.setCostAttributionCode(applyOrderContract.getCost_attribution_code());
        }
        EmployeeContract employee = iBaseOrganizationService.getEmployee(userId, companyId);
        if (employee != null) {
            apply.setApplicantName(employee.getName());
        }
        apply.setUpdateTime(now);
        apply.setCreateTime(now);

        String orderId = tripList.get(0).getOrder_id();
        Integer orderType = tripList.get(0).getType();
        String ticketIds = null;
        Integer type = apply.getType();
        if (type == ApplyType.Air.getValue() && subType == ApplySubType.Refund.getValue()) {
            Map<String, Object> tripOrderInfo = tripList.get(0).getTrip_order_info();
            ticketIds = ObjUtils.toString(tripOrderInfo.get("product_ids"));
        }
        if (type == ApplyType.Air.getValue() && subType == ApplySubType.Change.getValue()) {
            Map<String, Object> tripOrderInfo = tripList.get(0).getTrip_order_info();
            ticketIds = StringUtils.join((List<String>) tripOrderInfo.get("ticket_ids"), ",");
        }
        // 退改审批事由id
        if (ObjUtils.isNotBlank(applyOrderContract.getApply_reason_id())) {
            tripList.get(0).getTrip_order_info().put("remark_id", applyOrderContract.getApply_reason_id());
        }
        Date overTime = disposeOverTime(orderId, orderType, userId, applyId, ticketIds, clientHeadVersion, subType);
        if (type == ApplyType.Air.getValue() && subType == ApplySubType.Change.getValue()) {
            Map<String, Object> tripOrderInfo = tripList.get(0).getTrip_order_info();
            Map<String, Object> segmentInfo = (Map<String, Object>) tripOrderInfo.get("segment_info");
            Long departureTimestamp = ObjUtils.toLong(segmentInfo.get("departure_timestamp"));
            Date overDate = new Date(departureTimestamp);
            Calendar c = Calendar.getInstance();
            c.setTime(overDate);
            c.add(Calendar.HOUR_OF_DAY, -4);
            overTime = c.getTime();
        }
        apply.setOvertime(overTime);
        //创建申请单
        apply.setId(applyId);
        apply.setRootApplyOrderId(applyId);
        applyOrderContract.setId(applyId);
        applyOrderMapper.insertSelective(apply);
        //保存行程信息
        insertTripContractList(tripList, applyId, now);
        //判断审批单是否是待审核状态
        List<ApplyOrderCopyTo> applyOrderCopyTos = applyAdapterMapper.getApplyOrderCopyToExtMapper(companyId).queryApplyOrderCCByApplyOrderId(applyId);
        if (!CollectionUtils.isEmpty(applyOrderCopyTos)) {
            applyAdapterMapper.getApplyOrderCopyToExtMapper(companyId).deleteCCByApplyOrderId(applyId);
        }
        //获取抄送人数据
        CompanyApplyFlowSetV2RequestContract flow = applyOrderContract.getFlow();
        if (flow != null) {
            List<CompanyApplyFlowItemSetV2RequestContract> cc_list = flow.getCc_list();
            if (!CollectionUtils.isEmpty(cc_list)) {
                if (cc_list.size() > 5) {
                    throw new SaasException(GlobalResponseCode.FlowItemCCTooMuch);
                }
                int sort = 1;
                for (CompanyApplyFlowItemSetV2RequestContract ccFlowItem : cc_list) {
                    //保存抄送人
                    ApplyOrderCopyTo applyOrderCopyTo = new ApplyOrderCopyTo();
                    applyOrderCopyTo.setId(IDTool.CreateUniqueID());
                    applyOrderCopyTo.setApplyOrderId(applyId);
                    applyOrderCopyTo.setCreateTime(new Date());
                    applyOrderCopyTo.setItemId(ccFlowItem.getItem_id());
                    applyOrderCopyTo.setItemType(ccFlowItem.getItem_type());
                    applyOrderCopyTo.setSort(sort++);
                    applyOrderCopyTo.setRead(false);
                    if (ccFlowItem.getUser() == null || StringUtils.isBlank(ccFlowItem.getUser().getUser_id())) {
                        throw new SaasException(GlobalResponseCode.ApplyFlowItemNeedSetCCUser);
                    }
                    if (ccFlowItem.getUser() != null) {
                        applyOrderCopyTo.setUserId(ccFlowItem.getUser().getUser_id());
                    }
                    //待确认
                    applyOrderCopyTo.setIsDelete(ccFlowItem.getIs_delete());//1:不能删除 2:可以删除
                    applyAdapterMapper.getApplyOrderCopyToMapper(companyId).insert(applyOrderCopyTo);
                }
            }

        }
        if (applyState == ApplyStatus.PendingAudit) {
            insertApproverMap(applyId, applyOrderContract.getApprover_id(), now);
        }
        if (applyState == ApplyStatus.PendingAudit) {
            //push
            postMessage(apply, apply.getEmployeeId(), apply.getApproverId(), action, ApplyStatus.PendingAudit, logId, orderId, orderType, tripList.get(0), "");
            //push 抄送人
            pushCCMessage(apply, apply.getEmployeeId(), action, ApplyStatus.PendingAudit, orderId, orderType);
        }
        return GlobalResponseCode.Success;
    }

    /**
     * 校验火车退改逻辑
     *
     * @param applyContract
     * @return
     */
    private void validTrain(ApplyRefundChangeContract applyContract){
        ApplyOrderV2Contract apply = applyContract.getApply();
        Integer applyOrderType = apply.getApply_order_type();
        Integer subType = apply.getSub_type();
        Integer type = apply.getType();
        Map<String, Object> tripOrderInfo = applyContract.getTrip_list().get(0).getTrip_order_info();
        if (applyOrderType.equals(SaasFlowConstant.SETTING_TYPE_REFUND_CHANGE) && type == ApplyType.Train.getValue()) {
            String orderId = applyContract.getTrip_list().get(0).getOrder_id();
            if (subType == ApplySubType.Refund.getValue()) {
                List<String> ticketIdList = ((List<String>) tripOrderInfo.get("ticket_ids"));
                String ticketId = ticketIdList.get(0);
                logger.info("校验火车退订操作内容:" + JSON.toJSON(tripOrderInfo));
                String trainData = null;
                try {
                    trainData = HttpTool.get(URL_GET_TRAIN_REFUNG_VALID_ORDER_INFO + "?order_id=" + orderId + "&ticket_id=" + ticketId);
                } catch (Exception e) {
                    logger.info("火车退订校验报错:" + e.getMessage());
                }
                logger.info("校验火车退订操作结果:" + trainData);
                Map<String, Object> jo = JSONObject.parseObject(trainData, HashMap.class);
                Integer code = ObjUtils.toInteger(jo.get("code"));
                if (code != null && code.intValue() != 0) {
                    String msg = ObjUtils.toString(jo.get("msg"));
                    throw new SaasException(GlobalResponseCode.ApplyOrderIsError, msg);
                }
            } else {
                Map<String, String> header = Maps.newHashMap();
                header.put("Content-Type", "application/json;charset=utf-8");
                logger.info("校验火车改签操作内容:" + JSON.toJSON(tripOrderInfo));
                String trainData = null;
                try {
                    trainData = HttpTool.post(URL_GET_TRAIN_CHANGE_VALID_ORDER_INFO, tripOrderInfo, header);
                } catch (Exception e) {
                    logger.info("火车改签校验报错:" + e.getMessage());
                }
                logger.info("校验火车改签操作结果:" + trainData);
                Map<String, Object> jo = JSONObject.parseObject(trainData, HashMap.class);
                Integer code = ObjUtils.toInteger(jo.get("code"));
                if (code != null && code.intValue() != 0) {
                    String msg = ObjUtils.toString(jo.get("msg"));
                    throw new SaasException(GlobalResponseCode.ApplyOrderIsError, msg);
                }
            }
        }
    }

    /**
     * 采购和差旅订单审批抄送人通知
     *
     * @param apply
     * @param senderUserId
     * @param action
     * @param desStatus
     */
    public void pushCCMessage(ApplyOrder apply, String senderUserId, ApplyLogAction action, ApplyStatus desStatus, String orderId, Integer orderType) {
        String applyId = apply.getId();
        Integer subType = apply.getSubType();
        ApplyType applyType = ApplyType.valueOf(apply.getType().intValue());
        List<ApplyOrderCopyTo> applyOrderCopyToList = applyAdapterMapper.getApplyOrderCopyToExtMapper(apply.getCompanyId()).queryApplyOrderCCByApplyOrderId(applyId);
        if (CollectionUtils.isEmpty(applyOrderCopyToList)) {
            return;
        }
        if (action.getValue() != ApplyLogAction.Submit.getValue() && desStatus.getValue() != ApplyStatus.Approved.getValue()) {
            return;
        }
        //提交审批时抄送通知类型判断
        if (action == ApplyLogAction.Submit) {
            Integer flowCcType = apply.getFlowCcType();
            if (flowCcType == null || flowCcType == 1) {
                return;
            }
        }
        //审批终审结束时抄送通知类型判断
        if (desStatus == ApplyStatus.Approved) {
            Integer flowCcType = apply.getFlowCcType();
            if (flowCcType == null || flowCcType == 2) {
                return;
            }
        }
        //业务类型
        Integer type = apply.getType();
        if (CollectionUtils.isEmpty(applyOrderCopyToList)) {
            return;
        }
        List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.queryApprovalInfo(applyId);
        List<String> sponsorIdList = applyOrderLogList.stream().filter(applyOrderLog -> !applyOrderLog.getAction().equals(ApplyLogAction.Submit.getValue())).map(applyOrderLog -> applyOrderLog.getSponsorId()).distinct().collect(Collectors.toList());
        List<String> applyCopyList = applyOrderCopyToList.stream().map(applyOrderCopyTo -> applyOrderCopyTo.getUserId()).distinct().collect(Collectors.toList());
        applyCopyList.removeAll(sponsorIdList);
        applyOrderCopyToList = applyOrderCopyToList.stream().filter(applyOrderCopyTo -> CollectionUtils.isNotEmpty(applyCopyList) && applyCopyList.contains(applyOrderCopyTo.getUserId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(applyOrderCopyToList)) {
            return;
        }
        String name = "";
        EmployeeContract employee = iBaseOrganizationService.getEmployee(senderUserId, apply.getCompanyId());
        if (employee == null || StringUtils.isBlank(employee.getName())) {
            name = apply.getApplicantName();
        } else {
            name = employee.getName();
        }
        Map<String, Map<String, String>> languageMap = Maps.newHashMap();
        String pushContent = "";
        String messageContent = "";
        //提交审批时抄送通知类型判断
        if (action == ApplyLogAction.Submit) {
            if (subType == ApplySubType.Refund.getValue()) {
                if (type == ApplyType.IntlAir.getValue() || type == ApplyType.Air.getValue()) {
                    languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_TrainBackNotice.getLanguageCode(), name));
                    pushContent = StrUtils.formatString(CoreLanguage.Common_Message_TrainBackNotice.getMessage(), name);
                    messageContent = StrUtils.formatString(CoreLanguage.Common_Message_TrainBack.getMessage(), name);
                } else if (type == ApplyType.Hotel.getValue()) {
                    languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_HotelBackNotice.getLanguageCode(), name));
                    pushContent = StrUtils.formatString(CoreLanguage.Common_Message_HotelBackNotice.getMessage(), name);
                    messageContent = StrUtils.formatString(CoreLanguage.Common_Message_HotelBack.getMessage(), name);
                } else if (type == ApplyType.Train.getValue()) {
                    languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_TrainTicketBackNotice.getLanguageCode(), name));
                    pushContent = StrUtils.formatString(CoreLanguage.Common_Message_TrainTicketBackNotice.getMessage(), name);
                    messageContent = StrUtils.formatString(CoreLanguage.Common_Message_TrainTicketBack.getMessage(), name);
                }
            } else {
                if (type == ApplyType.IntlAir.getValue() || type == ApplyType.Air.getValue()) {
                    languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_AirTicketChangeNotice.getLanguageCode(), name));
                    pushContent = StrUtils.formatString(CoreLanguage.Common_Message_AirTicketChangeNotice.getMessage(), name);
                    messageContent = StrUtils.formatString(CoreLanguage.Common_Message_AirTicketChange.getMessage(), name);
                } else if (type == ApplyType.Train.getValue()) {
                    languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_TrainTicketUpdateNotice.getLanguageCode(), name));
                    pushContent = StrUtils.formatString(CoreLanguage.Common_Message_TrainTicketUpdateNotice.getMessage(), name);
                    messageContent = StrUtils.formatString(CoreLanguage.Common_Message_TrainTicketUpdate.getMessage(), name);
                }
            }
        }
        //审批终审结束时抄送通知类型判断
        if (desStatus == ApplyStatus.Approved) {
            if (subType == ApplySubType.Refund.getValue()) {
                if (type == ApplyType.IntlAir.getValue() || type == ApplyType.Air.getValue()) {
                    languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_AirBackPassNotice.getLanguageCode(), name));
                    pushContent = StrUtils.formatString(CoreLanguage.Common_Message_AirBackPassNotice.getMessage(), name);
                    messageContent = StrUtils.formatString(CoreLanguage.Common_Message_AirTicketBackPass.getMessage(), name);
                } else if (type == ApplyType.Hotel.getValue()) {
                    languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_HotelBackPassNotice.getLanguageCode(), name));
                    pushContent = StrUtils.formatString(CoreLanguage.Common_Message_HotelBackPassNotice.getMessage(), name);
                    messageContent = StrUtils.formatString(CoreLanguage.Common_Message_HotelBackPass.getMessage(), name);
                } else if (type == ApplyType.Train.getValue()) {
                    languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_TrainTicketBackPassNotice.getLanguageCode(), name));
                    pushContent = StrUtils.formatString(CoreLanguage.Common_Message_TrainTicketBackPassNotice.getMessage(), name);
                    messageContent = StrUtils.formatString(CoreLanguage.Common_Message_TrainTicketBackPass.getMessage(), name);
                }
            } else {
                if (type == ApplyType.IntlAir.getValue() || type == ApplyType.Air.getValue()) {
                    languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_AirTicketChangePassNotice.getLanguageCode(), name));
                    pushContent = StrUtils.formatString(CoreLanguage.Common_Message_AirTicketChangePassNotice.getMessage(), name);
                    messageContent = StrUtils.formatString(CoreLanguage.Common_Message_AirTicketChangePass.getMessage(), name);
                } else if (type == ApplyType.Train.getValue()) {
                    languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_TrainTicketUpdatePassNotice.getLanguageCode(), name));
                    pushContent = StrUtils.formatString(CoreLanguage.Common_Message_TrainTicketUpdatePassNotice.getMessage(), name);
                    messageContent = StrUtils.formatString(CoreLanguage.Common_Message_TrainTicketUpdatePass.getMessage(), name);
                }
            }
        }
        for (ApplyOrderCopyTo applyOrderCopyTo : applyOrderCopyToList) {
            if (StringUtils.isBlank(applyOrderCopyTo.getUserId())) {
                continue;
            }
            String settingType = "6";
            Map<String, Object> msgInfo = new HashMap<>();
            msgInfo.put("myself", "false");
            msgInfo.put("view_type", "3");
            msgInfo.put("id", applyId);
            if (action == ApplyLogAction.Submit && StringUtils.isNotBlank(orderId)) {
                msgInfo.put("order_id", orderId);
                msgInfo.put("order_type", ObjUtils.toString(orderType));
            } else {
                List<ApplyTripApplicateExt> applyTripApplicates = applyTripApplicateExtMapper.queryApplyTripApplicateList(applyId);
                msgInfo.put("order_id", applyTripApplicates.get(0).getOrderId());
                msgInfo.put("order_type", ObjUtils.toString(applyTripApplicates.get(0).getType()));
            }
            msgInfo.put("sub_type", subType);
            msgInfo.put("setting_type", settingType);
            String linkDetail = JSONObject.toJSONString(msgInfo);

            //存消息
            ApplyLogAction msgAction = action;
            if (msgAction == ApplyLogAction.Approve && desStatus != ApplyStatus.Approved) {
                //固定审批流中间审批完成
                msgAction = ApplyLogAction.Submit;
            }
            if (msgAction == ApplyLogAction.Skip) {
                if (desStatus == ApplyStatus.Approved) {
                    msgAction = ApplyLogAction.Approve;
                } else {
                    msgAction = ApplyLogAction.Submit;
                }
            }
            String messageTitle = genMessageTitle(msgAction, languageMap);
            MessageSaveContract messageSaveContract = new MessageSaveContract();
            messageSaveContract.setMessage_type(MessageType.Apply.getCode());
            messageSaveContract.setTitle(messageTitle);
            messageSaveContract.setContent(messageContent);
            messageSaveContract.setBiz_order(applyId);
            messageSaveContract.setLink(linkDetail);
            messageSaveContract.setSender(senderUserId);
            messageSaveContract.setSender_type(SenderType.Person.getCode());
            messageSaveContract.setReceiver(applyOrderCopyTo.getUserId());
            messageSaveContract.setCompany_id(apply.getCompanyId());

            ApplyInfo messageApplyInfo = new ApplyInfo();
            messageApplyInfo.setApply_type(applyType.getBizTypeCode(6, applyType.getValue(), subType).getCode());
            messageApplyInfo.setApply_time(DateTimeTool.fromDateTimeToString(new Date()));
            messageApplyInfo.setApply_msg(messageContent);
            messageApplyInfo.setMyself(false);
            messageApplyInfo.setView_type(3);
            messageApplyInfo.setSetting_type(2);
            if (msgAction == ApplyLogAction.Submit && StringUtils.isNotBlank(orderId)) {
                messageApplyInfo.setOrder_id(orderId);
                messageApplyInfo.setOrder_type(ObjUtils.toString(orderType));
            } else {
                List<ApplyTripApplicateExt> applyTripApplicates = applyTripApplicateExtMapper.queryApplyTripApplicateList(applyId);
                messageApplyInfo.setOrder_id(applyTripApplicates.get(0).getOrderId());
                messageApplyInfo.setOrder_type(ObjUtils.toString(applyTripApplicates.get(0).getType()));
            }
            messageSaveContract.setApply_info(messageApplyInfo);
            try {
                messageService.saveMessage(messageSaveContract);
            } catch (SaasException ex) {
                //不处理
            }
            PushContract pushInfo = new PushContract();
            pushInfo.setTitle(messageTitle);
            pushInfo.setContent(pushContent);
            pushInfo.setUser_id(applyOrderCopyTo.getUserId());
            pushInfo.setMsg_type("0");
            pushInfo.setDesc(pushContent);
            pushInfo.setAlert(true);
            pushInfo.setMsg(linkDetail);
            pushInfo.setTitleMultilingualMap(languageMap.get(MessageLanguageEnum.TITLE.getCode()));
            pushInfo.setCommentMultilingualMap(languageMap.get(MessageLanguageEnum.CONTENT.getCode()));
            pushService.push(pushInfo);
        }
    }

    /**
     * 发送差旅push和通知
     *
     * @param apply
     * @param senderUserId
     * @param receiverUserId
     * @param action
     * @param desStatus
     * @param logId
     */
    @Override
   // @Transactional
    public void postMessage(ApplyOrder apply, String senderUserId, String receiverUserId, ApplyLogAction action, ApplyStatus desStatus, Integer logId, String orderId, Integer orderType, ApplyTripInfoContract applyTripInfo, String comment) {
        String applyId = apply.getId();
        Integer subType = apply.getSubType();
        ApplyType applyType = ApplyType.valueOf(apply.getType().intValue());
        Integer type = applyTripInfo.getType();
        SimpleDateFormat sdf = new SimpleDateFormat(ApplyConstant.FORMAT_DATE_MMDDHHMM_WITH);
        String overtime = sdf.format(apply.getOvertime());
        List<String> userIds = new ArrayList<>();
        userIds.add(senderUserId);
        userIds.add(receiverUserId);
        if (!userIds.contains(apply.getEmployeeId())) {
            userIds.add(apply.getEmployeeId());
        }
        List<IdNameContract> userNames = userService.getUserNameByIds(userIds, apply.getCompanyId());
        String employeeName = null;
        String senderUserName = null;
        String receiverUserName = null;
        for (IdNameContract userName : userNames) {
            if (senderUserId.equals(userName.getId())) {
                senderUserName = userName.getName();
                break;
            }
        }
        for (IdNameContract userName : userNames) {
            if (apply.getEmployeeId().equals(userName.getId())) {
                employeeName = userName.getName();
                break;
            }
        }
        if (StringUtils.isBlank(employeeName)) {
            employeeName = apply.getApplicantName();
        }
        for (IdNameContract userName : userNames) {
            if (receiverUserId.equals(userName.getId())) {
                receiverUserName = userName.getName();
                break;
            }
        }
        ApplyLogAction msgAction = action;
        String msgSender = senderUserName;
        if (StringUtils.isBlank(msgSender) && apply.getEmployeeId().equals(senderUserId)) {
            msgSender = apply.getApplicantName();
        }
        if (msgAction == ApplyLogAction.Approve && desStatus != ApplyStatus.Approved) {
            //固定审批流中间审批完成
            msgSender = employeeName;
            msgAction = ApplyLogAction.Submit;
        }
        if (msgAction == ApplyLogAction.Skip) {
            if (desStatus == ApplyStatus.Approved) {
                msgAction = ApplyLogAction.Approve;
            } else {
                msgAction = ApplyLogAction.Submit;
            }
        }
        //发短信
        EmployeeContract employee = iBaseOrganizationService.getEmployee(receiverUserId, apply.getCompanyId());
        //发短信功能
        if (msgAction == ApplyLogAction.Submit || msgAction == ApplyLogAction.Forward) {
            //发短信
            if (employee != null) {
                String phoneNum = employee.getPhone_num();
                Set<String> phoneSet = Sets.newSet();
                phoneSet.add(phoneNum);
                if (CollectionUtils.isNotEmpty(phoneSet)) {
                    SmsContract smsContract = null;
                    if (type == BizType.IntlAir.getValue() || type == BizType.Air.getValue()) {
                        if (subType == ApplySubType.Refund.getValue()) {
                            smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_AIR_REFUND_SUMMIT_APPLY_CENTER_URL);//机票短信模板
                        } else {
                            smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_AIR_CHANGE_SUMMIT_APPLY_CENTER_URL);//机票短信模板
                        }
                    } else if (type == BizType.Hotel.getValue()) {
                        if (subType == ApplySubType.Refund.getValue()) {
                            smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_HOTEL_REFUND_SUMMIT_APPLY_CENTER_URL);//酒店短信模板
                        }
                    } else if (type == BizType.Train.getValue()) {
                        if (subType == ApplySubType.Refund.getValue()) {
                            smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_TRAIN_REFUND_SUMMIT_APPLY_CENTER_URL);//火车短信模板
                        } else {
                            smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_TRAIN_CHANGE_SUMMIT_APPLY_CENTER_URL);//火车短信模板
                        }
                    }
                    Map<String, Object> param = new LinkedHashMap<>();
                    param.put("var1", employeeName);    //申请人姓名
                    if (type == BizType.IntlAir.getValue() || type == BizType.Air.getValue() || type == BizType.Train.getValue()) {
                        param.put("var2", applyTripInfo.getStart_city_name() + "-" + applyTripInfo.getArrival_city_name());
                    } else if (type == BizType.Hotel.getValue()) {
                        param.put("var2", applyTripInfo.getStart_city_name() + "-" + applyTripInfo.getTitle());
                    }
                    try {
                        logger.info("退改审批id加密前:" + HostPropertyConfigTool.HOST_H5 + "/process/approve/refundChange?apply_id=" + applyId + "&user_id=" + receiverUserId + "秘钥:" + HostPropertyConfigTool.SIGN_KEY);
                        String applySign = EncryptUtil.encryptAES(applyId, HostPropertyConfigTool.SIGN_KEY);
                        String userIdSign = EncryptUtil.encryptAES(receiverUserId, HostPropertyConfigTool.SIGN_KEY);
                        String h5Url = HostPropertyConfigTool.HOST_H5 + "/process/approve/refundChange?apply_id=" + URLEncoder.encode(applySign) + "&user_id=" + URLEncoder.encode(userIdSign);
                        h5Url = h5Url.replaceAll("\r|\n", "");
                        logger.info("退改审批id加密后:" + HostPropertyConfigTool.HOST_H5 + "/process/approve/refundChange?apply_id=" + applySign + "&user_id=" + URLEncoder.encode(userIdSign));
                        String shortUrl = ShortUrlUtils.getShortUrl(h5Url);
                        if (type == BizType.Train.getValue() || type == BizType.Hotel.getValue()) {
                            param.put("var3", overtime);
                            param.put("var4", shortUrl.toString() + " ");
                        } else {
                            param.put("var3", shortUrl.toString() + " ");
                        }
                    } catch (Exception e) {
                        logger.error(String.format("url:%s,异常信息：", "http://harmony.fenbeitong.com/harmony/weixin/shorturl") + e.getMessage());
                        throw new SaasException(GlobalResponseCode.ShortUrlError);
                    }
                    smsContract.setParam(param);
                    NoticeUtils.sendSms(smsContract);
                }
            }
        } else if (msgAction == ApplyLogAction.Refuse) {
            //发短信
            if (employee != null) {
                String phoneNum = employee.getPhone_num();
                Set<String> phoneSet = Sets.newSet();
                phoneSet.add(phoneNum);
                if (CollectionUtils.isNotEmpty(phoneSet)) {
                    SmsContract smsContract = null;
                    if (type == BizType.IntlAir.getValue() || type == BizType.Air.getValue()) {
                        if (subType == ApplySubType.Refund.getValue()) {
                            smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_AIR_REFUND_REPULSE_APPLY_CENTER_URL);//机票短信模板
                        } else {
                            smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_AIR_CHANGE_REPULSE_APPLY_CENTER_URL);//机票短信模板
                        }
                    } else if (type == BizType.Hotel.getValue()) {
                        if (subType == ApplySubType.Refund.getValue()) {
                            smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_HOTEL_REFUND_REPULSE_APPLY_CENTER_URL);//酒店短信模板
                        }
                    } else if (type == BizType.Train.getValue()) {
                        if (subType == ApplySubType.Refund.getValue()) {
                            smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_TRAIN_REFUND_REPULSE_APPLY_CENTER_URL);//火车短信模板
                        } else {
                            smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_TRAIN_CHANGE_REPULSE_APPLY_CENTER_URL);//火车短信模板
                        }
                    }
                    Map<String, Object> param = new LinkedHashMap<>();
                    if (type == BizType.IntlAir.getValue() || type == BizType.Air.getValue() || type == BizType.Train.getValue()) {
                        param.put("var1", applyTripInfo.getStart_city_name() + "-" + applyTripInfo.getArrival_city_name());
                    } else if (type == BizType.Hotel.getValue()) {
                        param.put("var1", applyTripInfo.getStart_city_name() + "-" + applyTripInfo.getTitle());
                    }
                    if (StringUtils.isNotBlank(comment)) {
                        param.put("var2", "（" + comment + "）");
                    } else {
                        param.put("var2", "");
                    }
                    smsContract.setParam(param);
                    NoticeUtils.sendSms(smsContract);
                }
            }
        } else if (msgAction == ApplyLogAction.Approve && desStatus == ApplyStatus.Approved) {
            //发短信
            if (employee != null) {
                String phoneNum = employee.getPhone_num();
                Set<String> phoneSet = Sets.newSet();
                phoneSet.add(phoneNum);
                if (CollectionUtils.isNotEmpty(phoneSet)) {
                    SmsContract smsContract = null;
                    if (type == BizType.IntlAir.getValue() || type == BizType.Air.getValue()) {
                        if (subType == ApplySubType.Refund.getValue()) {
                            smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_AIR_REFUND_APPROVE_APPLY_CENTER_URL);//机票短信模板
                        } else {
                            smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_AIR_CHANGE_APPROVE_APPLY_CENTER_URL);//机票短信模板
                        }
                    } else if (type == BizType.Hotel.getValue()) {
                        if (subType == ApplySubType.Refund.getValue()) {
                            smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_HOTEL_REFUND_APPROVE_APPLY_CENTER_URL);//酒店短信模板
                        }
                    } else if (type == BizType.Train.getValue()) {
                        if (subType == ApplySubType.Refund.getValue()) {
                            smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_TRAIN_REFUND_APPROVE_APPLY_CENTER_URL);//火车短信模板
                        } else {
                            smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_TRAIN_CHANGE_APPROVE_APPLY_CENTER_URL);//火车短信模板
                        }
                    }
                    Map<String, Object> param = new LinkedHashMap<>();
                    if (type == BizType.IntlAir.getValue() || type == BizType.Air.getValue() || type == BizType.Train.getValue()) {
                        param.put("var1", applyTripInfo.getStart_city_name() + "-" + applyTripInfo.getArrival_city_name());
                    } else if (type == BizType.Hotel.getValue()) {
                        param.put("var1", applyTripInfo.getStart_city_name() + "-" + applyTripInfo.getTitle());
                    }
                    smsContract.setParam(param);
                    NoticeUtils.sendSms(smsContract);
                }
            }
        }
        Map<String, Map<String, String>> languageMap = Maps.newHashMap();
        String messageContent = "";
        String pushContent = "";
        String messageTitle = genMessageTitle(msgAction, languageMap);
        if (msgAction == ApplyLogAction.Submit || msgAction == ApplyLogAction.Forward) {
            if (subType == ApplySubType.Refund.getValue()) {
                if (type == BizType.IntlAir.getValue() || type == BizType.Air.getValue()) {
                    languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_AirBackFlowNotice.getLanguageCode(), msgSender));
                    pushContent = StrUtils.formatString(CoreLanguage.Common_Message_AirBackFlowNotice.getMessage(), msgSender);
                    messageContent = StrUtils.formatString(CoreLanguage.Common_Message_AirBackFlow.getMessage(), msgSender);
                } else if (type == BizType.Hotel.getValue()) {
                    if (subType == ApplySubType.Refund.getValue()) {
                        languageMap.put(MessageLanguageEnum.CONTENT.getCode(),
                                LanguageUtils.getFullMessage(CoreLanguage.Common_Message_HotelBackFlowNotice.getLanguageCode(), msgSender, overtime));
                        pushContent = StrUtils.formatString(CoreLanguage.Common_Message_HotelBackFlowNotice.getMessage(), msgSender, overtime);
                        messageContent = StrUtils.formatString(CoreLanguage.Common_Message_HotelBackFlow.getMessage(), msgSender, overtime);
                    }
                } else if (type == BizType.Train.getValue()) {
                    languageMap.put(MessageLanguageEnum.CONTENT.getCode(),
                            LanguageUtils.getFullMessage(CoreLanguage.Common_Message_TrainBackFlowNotice.getLanguageCode(), msgSender, overtime));
                    pushContent = StrUtils.formatString(CoreLanguage.Common_Message_TrainBackFlowNotice.getMessage(), msgSender, overtime);
                    messageContent = StrUtils.formatString(CoreLanguage.Common_Message_TrainBackFlow.getMessage(), msgSender, overtime);
                }
            } else {
                if (type == BizType.IntlAir.getValue() || type == BizType.Air.getValue()) {
                    languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_AirChangeFlowNotice.getLanguageCode(), msgSender));
                    pushContent = StrUtils.formatString(CoreLanguage.Common_Message_AirChangeFlowNotice.getMessage(), msgSender);
                    messageContent = StrUtils.formatString(CoreLanguage.Common_Message_AirChangeFlow.getMessage(), msgSender);
                } else if (type == BizType.Train.getValue()) {
                    languageMap.put(MessageLanguageEnum.CONTENT.getCode(),
                            LanguageUtils.getFullMessage(CoreLanguage.Common_Message_TrainTicketChangeNotice.getLanguageCode(), msgSender, overtime));
                    pushContent = StrUtils.formatString(CoreLanguage.Common_Message_TrainTicketChangeNotice.getMessage(), msgSender, overtime);
                    messageContent = StrUtils.formatString(CoreLanguage.Common_Message_TrainTicketChange.getMessage(), msgSender, overtime);
                }
            }
        } else if (msgAction == ApplyLogAction.Approve) {
            if (desStatus == ApplyStatus.Approved) {
                if (subType == ApplySubType.Refund.getValue()) {
                    if (type == BizType.IntlAir.getValue() || type == BizType.Air.getValue()) {
                        languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_AirBackPersonPassNotice.getLanguageCode()));
                        pushContent = CoreLanguage.Common_Message_AirBackPersonPassNotice.getMessage();
                        messageContent = CoreLanguage.Common_Message_AirBackPersonPass.getMessage();
                    } else if (type == BizType.Hotel.getValue()) {
                        languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_HotelBackPersonPassNotice.getLanguageCode()));
                        pushContent = CoreLanguage.Common_Message_HotelBackPersonPassNotice.getMessage();
                        messageContent = CoreLanguage.Common_Message_HotelBackPersonPass.getMessage();
                    } else if (type == BizType.Train.getValue()) {
                        languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_TrainBackPersonPassNotice.getLanguageCode()));
                        pushContent = CoreLanguage.Common_Message_TrainBackPersonPassNotice.getMessage();
                        messageContent = CoreLanguage.Common_Message_TrainBackPersonPass.getMessage();
                    }
                } else {
                    if (type == BizType.IntlAir.getValue() || type == BizType.Air.getValue()) {
                        languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_AirChangePersonPassNotice.getLanguageCode()));
                        pushContent = CoreLanguage.Common_Message_AirChangePersonPassNotice.getMessage();
                        messageContent = CoreLanguage.Common_Message_AirChangePersonPass.getMessage();
                    } else if (type == BizType.Train.getValue()) {
                        languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_TrainTicketUpdatePersonPassNotice.getLanguageCode()));
                        pushContent = CoreLanguage.Common_Message_TrainTicketUpdatePersonPassNotice.getMessage();
                        messageContent = CoreLanguage.Common_Message_TrainTicketUpdatePersonPass.getMessage();
                    }
                }
            } else {
                if (subType == ApplySubType.Refund.getValue()) {
                    if (type == BizType.IntlAir.getValue() || type == BizType.Air.getValue()) {
                        languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_AirBackFlowNotice.getLanguageCode(), msgSender));
                        pushContent = StrUtils.formatString(CoreLanguage.Common_Message_AirBackFlowNotice.getMessage(), msgSender);
                        messageContent = StrUtils.formatString(CoreLanguage.Common_Message_AirBackFlow.getMessage(), msgSender);
                    } else if (type == BizType.Hotel.getValue()) {
                        languageMap.put(MessageLanguageEnum.CONTENT.getCode(),
                                LanguageUtils.getFullMessage(CoreLanguage.Common_Message_HotelBackFlowNotice.getLanguageCode(), msgSender, overtime));
                        pushContent = StrUtils.formatString(CoreLanguage.Common_Message_HotelBackFlowNotice.getMessage(), msgSender, overtime);
                        messageContent = StrUtils.formatString(CoreLanguage.Common_Message_HotelBackFlow.getMessage(), msgSender, overtime);
                    } else if (type == BizType.Train.getValue()) {
                        languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_TrainBackFlowNotice.getLanguageCode(), msgSender));
                        pushContent = StrUtils.formatString(CoreLanguage.Common_Message_TrainBackFlowNotice.getMessage(), msgSender);
                        messageContent = StrUtils.formatString(CoreLanguage.Common_Message_TrainBackFlow.getMessage(), msgSender, overtime);
                    }
                } else {
                    if (type == BizType.IntlAir.getValue() || type == BizType.Air.getValue()) {
                        languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_AirChangeFlowNotice.getLanguageCode(), msgSender));
                        pushContent = StrUtils.formatString(CoreLanguage.Common_Message_AirChangeFlowNotice.getMessage(), msgSender);
                        messageContent = StrUtils.formatString(CoreLanguage.Common_Message_AirChangeFlow.getMessage(), msgSender);
                    } else if (type == BizType.Train.getValue()) {
                        languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_TrainTicketChangeNotice.getLanguageCode(), msgSender));
                        pushContent = StrUtils.formatString(CoreLanguage.Common_Message_TrainTicketChangeNotice.getMessage(), msgSender);
                        messageContent = StrUtils.formatString(CoreLanguage.Common_Message_TrainTicketChange.getMessage(), msgSender, overtime);
                    }
                }
            }
        } else if (msgAction == ApplyLogAction.Refuse) {
            if (subType == ApplySubType.Refund.getValue()) {
                if (type == BizType.IntlAir.getValue() || type == BizType.Air.getValue()) {
                    languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_AirTicketBackPersonRefuseNotice.getLanguageCode()));
                    pushContent = CoreLanguage.Common_Message_AirTicketBackPersonRefuseNotice.getMessage();
                    messageContent = CoreLanguage.Common_Message_AirTicketBackPersonRefuse.getMessage();
                } else if (type == BizType.Hotel.getValue()) {
                    languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_HotelBackPersonRefuseNotice.getLanguageCode()));
                    pushContent = CoreLanguage.Common_Message_HotelBackPersonRefuseNotice.getMessage();
                    messageContent = CoreLanguage.Common_Message_HotelBackPersonRefuse.getMessage();
                } else if (type == BizType.Train.getValue()) {
                    languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_TrainTicketBackRefuseNotice.getLanguageCode()));
                    pushContent = CoreLanguage.Common_Message_TrainTicketBackRefuseNotice.getMessage();
                    messageContent = CoreLanguage.Common_Message_TrainTicketBackRefuse.getMessage();
                }
            } else {
                if (type == BizType.IntlAir.getValue() || type == BizType.Air.getValue()) {
                    languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_AirTicketChangePersonRefuseNotice.getLanguageCode()));
                    pushContent = CoreLanguage.Common_Message_AirTicketChangePersonRefuseNotice.getMessage();
                    messageContent = CoreLanguage.Common_Message_AirTicketChangePersonRefuse.getMessage();
                } else if (type == BizType.Train.getValue()) {
                    languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_TrainTicketUpdatePersonNotice.getLanguageCode()));
                    pushContent = CoreLanguage.Common_Message_TrainTicketUpdatePersonNotice.getMessage();
                    messageContent = CoreLanguage.Common_Message_TrainTicketUpdatePerson.getMessage();
                }
            }
        } else if (msgAction == ApplyLogAction.Overtime) {
            if (subType == ApplySubType.Refund.getValue()) {
                if (type == BizType.IntlAir.getValue() || type == BizType.Air.getValue()) {
                    languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_AirBackTimeoutNotice.getLanguageCode(), msgSender));
                    pushContent = StrUtils.formatString(CoreLanguage.Common_Message_AirBackTimeoutNotice.getMessage(), msgSender);
                    messageContent = CoreLanguage.Common_Message_AirBackTimeout.getMessage();
                } else if (type == BizType.Hotel.getValue()) {
                    languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_HotelBackTimeoutNotice.getLanguageCode(), msgSender));
                    pushContent = StrUtils.formatString(CoreLanguage.Common_Message_HotelBackTimeoutNotice.getMessage(), msgSender);
                    messageContent = CoreLanguage.Common_Message_HotelBackTimeout.getMessage();
                } else if (type == BizType.Train.getValue()) {
                    languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_TrainTicketBackTimeoutNotice.getLanguageCode(), msgSender));
                    pushContent = StrUtils.formatString(CoreLanguage.Common_Message_TrainTicketBackTimeoutNotice.getMessage(), msgSender);
                    messageContent = CoreLanguage.Common_Message_TrainTicketBackTimout.getMessage();
                }
            } else {
                if (type == BizType.IntlAir.getValue() || type == BizType.Air.getValue()) {
                    languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_AirTicketChangeTimeoutNotice.getLanguageCode(), msgSender));
                    pushContent = StrUtils.formatString(CoreLanguage.Common_Message_AirTicketChangeTimeoutNotice.getMessage(), msgSender);
                    messageContent = CoreLanguage.Common_Message_AirTicketChangeTimeout.getMessage();
                } else if (type == BizType.Train.getValue()) {
                    languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_TrainTicketUpdateTimeoutNotice.getLanguageCode(), msgSender));
                    pushContent = StrUtils.formatString(CoreLanguage.Common_Message_TrainTicketUpdateTimeoutNotice.getMessage(), msgSender);
                    messageContent = CoreLanguage.Common_Message_TrainTicketUpdateTimeout.getMessage();
                }
            }
        } else if (msgAction == ApplyLogAction.Invalid) {
             if (subType==ApplySubType.Change.getValue()){
                if (type == BizType.IntlAir.getValue() || type == BizType.Air.getValue()) {
                    languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_AirTicketChangeInvalidNotice.getLanguageCode(), msgSender));
                    pushContent = StrUtils.formatString(CoreLanguage.Common_Message_AirTicketChangeInvalidNotice.getMessage(), msgSender);
                    messageContent = CoreLanguage.Common_Message_AirTicketChangeInvalid.getMessage();
                }
            }
        }
        boolean myself = true;
        if (msgAction == ApplyLogAction.Submit || msgAction == ApplyLogAction.Forward) {
            myself = false;
        }
        String settingType = "6";
        String viewType = myself ? "1" : "2";
        Map<String, Object> msgData = new HashMap<>();
        msgData.put("myself", myself ? "true" : "false");
        msgData.put("view_type", viewType);
        msgData.put("id", applyId);
        if (msgAction == ApplyLogAction.Submit && StringUtils.isNotBlank(orderId)) {
            msgData.put("order_id", orderId);
            msgData.put("order_type", ObjUtils.toString(orderType));
        } else {
            List<ApplyTripApplicateExt> applyTripApplicates = applyTripApplicateExtMapper.queryApplyTripApplicateList(applyId);
            msgData.put("order_id", applyTripApplicates.get(0).getOrderId());
            msgData.put("order_type", ObjUtils.toString(applyTripApplicates.get(0).getType()));
        }
        msgData.put("setting_type", settingType);
        msgData.put("sub_type", subType);
        String linkInfo = JSONObject.toJSONString(msgData); //跳转信息
        //存消息
        MessageSaveContract messageSaveContract = new MessageSaveContract();
        messageSaveContract.setMessage_type(MessageType.Apply.getCode());
        messageSaveContract.setTitle(messageTitle);
        messageSaveContract.setContent(messageContent);
        messageSaveContract.setBiz_order(applyId);
        messageSaveContract.setLink(linkInfo);
        messageSaveContract.setSender(senderUserId);
        messageSaveContract.setSender_type(SenderType.Person.getCode());
        messageSaveContract.setReceiver(receiverUserId);
        messageSaveContract.setCompany_id(apply.getCompanyId());

        ApplyInfo messageApplyInfo = new ApplyInfo();
        messageApplyInfo.setApply_type(applyType.getBizTypeCode(6, applyType.getValue(), subType).getCode());
        messageApplyInfo.setApply_time(DateTimeTool.fromDateTimeToString(new Date()));
        messageApplyInfo.setApply_msg(messageContent);
        messageApplyInfo.setMyself(myself);
        messageApplyInfo.setView_type(myself ? 1 : 2);
        messageApplyInfo.setSetting_type(6);
        if (msgAction == ApplyLogAction.Submit && StringUtils.isNotBlank(orderId)) {
            messageApplyInfo.setOrder_id(orderId);
            messageApplyInfo.setOrder_type(ObjUtils.toString(orderType));
        } else {
            List<ApplyTripApplicateExt> applyTripApplicates = applyTripApplicateExtMapper.queryApplyTripApplicateList(applyId);
            messageApplyInfo.setOrder_id(applyTripApplicates.get(0).getOrderId());
            messageApplyInfo.setOrder_type(ObjUtils.toString(applyTripApplicates.get(0).getType()));
        }
        if (logId != null && logId != 0) {
            messageApplyInfo.setLog_id(logId);
        }
        messageSaveContract.setApply_info(messageApplyInfo);

        try {
            messageService.saveMessage(messageSaveContract);
        } catch (SaasException ex) {
            //不处理
            logger.info("保存通知异常:" + ex.getMsg());
        }

        PushContract pushData = new PushContract();
        pushData.setTitle(messageTitle);
        pushData.setContent(pushContent);
        pushData.setUser_id(receiverUserId);
        pushData.setCompany_id(apply.getCompanyId());
        pushData.setMsg_type("0");
        pushData.setDesc(pushContent);
        pushData.setAlert(true);
        pushData.setMsg(linkInfo);
        pushData.setTitleMultilingualMap(languageMap.get(MessageLanguageEnum.TITLE.getCode()));
        pushData.setCommentMultilingualMap(languageMap.get(MessageLanguageEnum.CONTENT.getCode()));
        pushService.pushWithBudge(pushData);

        //审批节点（非终审节点）审核完成（通过或转交），单据流转至下一节点审批发送给申请人
        if ((msgAction == ApplyLogAction.Approve && desStatus != ApplyStatus.Approved) || msgAction == ApplyLogAction.Forward) {
            String pushContentInfo = "";
            Map<String, Map<String, String>> languageMapInfo = Maps.newHashMap();
            if (type == BizType.IntlAir.getValue() || type == BizType.Air.getValue()) {
                languageMapInfo.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_AirTicketBackForwardNotice.getLanguageCode(), receiverUserName));
                pushContentInfo = StrUtils.formatString(CoreLanguage.Common_Message_AirTicketBackForwardNotice.getMessage(), receiverUserName);
            } else if (type == BizType.Hotel.getValue()) {
                languageMapInfo.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_HotelBackForwardNotice.getLanguageCode(), receiverUserName));
                pushContentInfo = StrUtils.formatString(CoreLanguage.Common_Message_HotelBackForwardNotice.getMessage(), receiverUserName);
            } else if (type == BizType.Train.getValue()) {
                languageMapInfo.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_TrainTicketForwardNotice.getLanguageCode(), receiverUserName));
                pushContentInfo = StrUtils.formatString(CoreLanguage.Common_Message_TrainTicketForwardNotice.getMessage(), receiverUserName);
            }
            PushContract sendData = new PushContract();
            sendData.setTitle(messageTitle);
            sendData.setContent(pushContentInfo);
            sendData.setUser_id(apply.getEmployeeId());
            sendData.setCompany_id(apply.getCompanyId());
            sendData.setMsg_type("0");
            sendData.setDesc(pushContentInfo);
            sendData.setAlert(true);
            sendData.setMsg(linkInfo);
            sendData.setTitleMultilingualMap(languageMap.get(MessageLanguageEnum.TITLE.getCode()));
            sendData.setCommentMultilingualMap(languageMapInfo.get(MessageLanguageEnum.CONTENT.getCode()));
            pushService.pushWithBudge(sendData);
        }
    }

    private String genMessageTitle(ApplyLogAction action, Map<String, Map<String, String>> languageMap) {
        switch (action) {
            case Submit:
            case ReSubmit:
                languageMap.put(MessageLanguageEnum.TITLE.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Title_WaitCheck.getLanguageCode()));
                return CoreLanguage.Common_Title_WaitCheck.getMessage();
            case Approve:
                languageMap.put(MessageLanguageEnum.TITLE.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Title_Pass.getLanguageCode()));
                return CoreLanguage.Common_Title_Pass.getMessage();
            case Forward:
                languageMap.put(MessageLanguageEnum.TITLE.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Title_Forward.getLanguageCode()));
                return CoreLanguage.Common_Title_Forward.getMessage();
            case Refuse:
                languageMap.put(MessageLanguageEnum.TITLE.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Title_HasRefuse.getLanguageCode()));
                return CoreLanguage.Common_Title_HasRefuse.getMessage();
            case Revoke:
                languageMap.put(MessageLanguageEnum.TITLE.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Title_HasRevoke.getLanguageCode()));
                return CoreLanguage.Common_Title_HasRevoke.getMessage();
            case Overtime:
                languageMap.put(MessageLanguageEnum.TITLE.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Title_Timeout.getLanguageCode()));
                return CoreLanguage.Common_Title_Timeout.getMessage();
            default:
                logger.error("生成apply push title时发现没有处理的action:" + action.getValue() + ",系统暂时只返回了actionName:" + action.getDesc());
                return action.getDesc();
        }
    }

    //@Transactional(value = "saas")
    private void insertTripContractList(List<ApplyTripInfoContract> tripList, String applyId, Date now) {
        if (tripList != null) {
            for (ApplyTripInfoContract tripContract : tripList) {
                insertTripContract(tripContract, applyId, now);
            }
        }
    }

    /**
     * 保存行程信息
     *
     * @param tripContract
     * @param applyId
     * @param now
     */
    //@Transactional(value = "saas")
    private void insertTripContract(ApplyTripInfoContract tripContract, String applyId, Date now) throws SaasException {
        ApplyTripInfo trip = new ApplyTripInfo();
        String applicateId = IDTool.CreateUniqueID();
        String id = IDTool.CreateUniqueID();
        trip.setId(id);
        trip.setApplyOrderId(applyId);
        trip.setCreateTime(now);
        trip.setType(tripContract.getType());
        trip.setArrivalCityId(tripContract.getArrival_city_id());
        trip.setStartCityId(tripContract.getStart_city_id());
        trip.setState(ApplyTripStatus.Available.getValue());
        trip.setStartTime(DateTimeTool.fromStringToDateTime(tripContract.getStart_time()));
        trip.setEndTime(DateTimeTool.fromStringToDateTime(tripContract.getEnd_time()));
        trip.setBackStartTime(DateTimeTool.fromStringToDateTime(tripContract.getBack_start_time()));
        trip.setBackEndTime(DateTimeTool.fromStringToDateTime(tripContract.getBack_end_time()));
        if (tripContract.getEstimated_amount() != null && tripContract.getEstimated_amount().compareTo(BigDecimal.valueOf(0)) != -1) {
            trip.setEstimatedAmount(tripContract.getEstimated_amount());
        }
        if (tripContract.getEnd_time() != null && tripContract.getEnd_time().length() > 0) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd KK:mm");
            try {
                trip.setEndTime(sdf.parse(tripContract.getEnd_time()));
            } catch (ParseException e) {
                logger.error("解析时间报错" + e.getMessage());
                e.printStackTrace();
            }
        }
        trip.setUpdateTime(now);
        trip.setStartCityName(tripContract.getStart_city_name());
        trip.setArrivalCityName(tripContract.getArrival_city_name());
        trip.setTitle(tripContract.getTitle());
        trip.setContent(tripContract.getContent());
        trip.setTripApplicateCurrentId(applicateId);
        trip.setTripType(tripContract.getTrip_type());
        JSONObject jo = new JSONObject();
        //机票航空公司名称
        if (StringUtils.isNotBlank(tripContract.getAir_airline_name())) {
            jo.put("air_airline_name", tripContract.getAir_airline_name());
        }
        //机票航班号
        if (StringUtils.isNotBlank(tripContract.getAir_flight_no())) {
            jo.put("air_flight_no", tripContract.getAir_flight_no());
        }
        //机票舱位名称
        if (StringUtils.isNotBlank(tripContract.getAir_seat_msg())) {
            jo.put("air_seat_msg", tripContract.getAir_seat_msg());
        }
        //返程机票航空公司名称
        if (StringUtils.isNotBlank(tripContract.getBack_air_airline_name())) {
            jo.put("back_air_airline_name", tripContract.getBack_air_airline_name());
        }
        //返程机票航班号
        if (StringUtils.isNotBlank(tripContract.getBack_air_flight_no())) {
            jo.put("back_air_flight_no", tripContract.getBack_air_flight_no());
        }
        //返程机票舱位名称
        if (StringUtils.isNotBlank(tripContract.getBack_air_seat_msg())) {
            jo.put("back_air_seat_msg", tripContract.getBack_air_seat_msg());
        }
        //机票折扣信息
        if (tripContract.getAir_discount() != null) {
            jo.put("air_discount", tripContract.getAir_discount());
        }
        //酒店床型名称
        if (StringUtils.isNotBlank(tripContract.getBed_type())) {
            jo.put("bed_type", tripContract.getBed_type());
        }
        //酒店预订间数
        if (tripContract.getRoom_count() != null) {
            jo.put("room_count", tripContract.getRoom_count());
        }
        //乘机人/乘车人/入住人
        if (CollectionUtils.isNotEmpty(tripContract.getTravel_partner())) {
            jo.put("travel_partner", tripContract.getTravel_partner());
        }
        jo.put("order_info", tripContract.getTrip_order_info());
        trip.setTripContent(jo.toJSONString());
        applyTripMapper.insert(trip);

        ApplyTripApplicate applicateModel = new ApplyTripApplicate();
        applicateModel.setId(applicateId);
        applicateModel.setOrderId(tripContract.getOrder_id());
        applicateModel.setAction(ApplyTripApplicateAction.Applicate.getValue());
        applicateModel.setApplyId(applyId);
        applicateModel.setCreateTime(now);
        applicateModel.setApplyTripId(id);
        applicateModel.setOrderProductType(1);
        applicateModel.setComment(null);
        applicateModel.setUseTime(now);
        applyTripApplicateMapper.insert(applicateModel);
    }

    //@Transactional(value = "saas")
    private void insertApproverMap(String applyId, String approverId, Date time) {
        ApplyApproverMapExample example = new ApplyApproverMapExample();
        example.createCriteria().andApplyIdEqualTo(applyId).andApproverIdEqualTo(approverId);
        List<ApplyApproverMap> list = applyApproverMapMapper.selectByExample(example);
        if (ObjUtils.isEmpty(list)) {
            ApplyApproverMap approverMap = new ApplyApproverMap();
            approverMap.setApplyId(applyId);
            approverMap.setApproverId(approverId);
            approverMap.setCreateTime(time);
            applyApproverMapMapper.insertSelective(approverMap);
        }
    }

    /**
     * 整理申请单数据
     *
     * @param applyContract
     */
    private void clearApplyData(ApplyRefundChangeContract applyContract) {
        ApplyOrderV2Contract applyorderContract = applyContract.getApply();
        if (applyorderContract.getId() == null || applyorderContract.getId().length() == 0) {
            applyorderContract.setCheck_reason(null);
        }
        if (applyorderContract.getBudget() == null) {
            applyorderContract.setBudget(BigDecimal.valueOf(0));
        }
        if (CollectionUtils.isEmpty(applyContract.getTrip_list())) {
            applyContract.setTrip_list(null);
        }
        List<Tuple<Date, String>> cityIds = new ArrayList<>();
        List<Date> timeRange = new ArrayList<>();

        for (ApplyTripInfoContract trip : applyContract.getTrip_list()) {
            Date startTime = DateTimeTool.fromStringToDateTime(trip.getStart_time());
            //这里把时间从String转到Date,再转回String,是需要用Date格式式字段串的显示,如输入为2017-2-27,格式化后变为2017-02-27
            if (startTime != null) {
                trip.setStart_time(DateTimeTool.fromDateTimeToString(startTime));
                timeRange.add(startTime);
            }
            Date endTime = DateTimeTool.fromStringToDateTime(trip.getEnd_time());
            if (endTime != null) {
                trip.setEnd_time(DateTimeTool.fromDateTimeToString(endTime));
                timeRange.add(endTime);
            }
            if (trip.getType() == BizType.Taxi.getValue() || trip.getType() == BizType.Hotel.getValue()) {
                trip.setArrival_city_id(null);
            }
            if (trip.getType() == BizType.Dinner.getValue()) {
                trip.setStart_city_id("0");
                   /*Calendar c = Calendar.getInstance();
                    c.setTime(DateTimeTool.fromStringToDateTime(trip.getStart_time()));
                    c.add(Calendar.MINUTE, 20);
                    trip.setStart_time(DateTimeTool.fromDateTimeToString(c.getTime()));*/
            }
            String tripCityId = trip.getStart_city_id();
            if (trip.getArrival_city_id() != null) {
                tripCityId += "-" + trip.getArrival_city_id();
            }
            cityIds.add(new Tuple<>(startTime, tripCityId));
        }

        //city_range格式:1000001-200002,2000002,300003-400004 意即:北京-上海,上海,天津-重庆
        cityIds.sort((m, n) -> (int) (m.getItem1().getTime() - n.getItem1().getTime()));
        List<String> cityIdArr = new ArrayList<>();
        for (Tuple<Date, String> cityId : cityIds) {
            cityIdArr.add(cityId.getItem2());
        }
        String cityIdVal = String.join(",", cityIdArr);
        applyorderContract.setCity_range(cityIdVal);

        //将所有出现过的时间正序排列,取最小值和最大值,重新写入timeRange字段,如果最小值=最大值,则只取最小值
        timeRange.sort((m, n) -> (int) (m.getTime() - n.getTime()));
        Date minTime = timeRange.get(0);
        Date maxTime = timeRange.get(timeRange.size() - 1);
        String desTimeRange = DateTimeTool.fromDateToString(minTime);
        if (maxTime.getTime() != minTime.getTime()) {
            desTimeRange += "," + DateTimeTool.fromDateToString(maxTime);
        }
        applyorderContract.setTime_range(desTimeRange);
        if (applyorderContract.getApprover_id() == null) {
            applyorderContract.setApprover_id("");
        }

    }

    /**
     * 检查数据有效性
     *
     * @param applyContract
     * @param employeeId
     * @param companyId
     * @param clientVersion
     * @return
     */
    private GlobalResponseCode checkApplyData(ApplyRefundChangeContract applyContract, String employeeId, String companyId, String clientVersion, String clientType) {
        //检查申请单
        ApplyOrderV2Contract apply = applyContract.getApply();
        if (apply == null) {
            return GlobalResponseCode.ApplyIsNull;
        }
        if (apply.getState() == null) {
            return GlobalResponseCode.ParameterError;
        }
        if (apply.getState().intValue() != ApplyStatus.PendingAudit.getValue()) {
            //如果申请单的状态不是草稿,就置为提交
            return GlobalResponseCode.ParameterError;
        }
        if (apply.getType() == null
                || ApplyType.valueOf(apply.getType()) == ApplyType.Unknown) {
            return GlobalResponseCode.ApplyTypeInvalid;
        }
        if (ApplyType.valueOf(apply.getType()) != ApplyType.Air && ApplyType.valueOf(apply.getType()) != ApplyType.Hotel && ApplyType.valueOf(apply.getType()) != ApplyType.Train) {
            return GlobalResponseCode.ApplyTypeInvalid;
        }
        //机票退订审批小于4.9.4提示升级
        if (ApplyType.valueOf(apply.getType()) == ApplyType.Air && ApplySubType.valueOf(apply.getSub_type()) == ApplySubType.Refund && VersionTool.lessThan(clientVersion, "4.9.4") && !"WebApp".equals(clientType)) {
            return GlobalResponseCode.ApplyRefundAirVersion;
        }
        //预计总金额
        if (apply.getBudget() != null && apply.getBudget().compareTo(BigDecimal.valueOf(0)) == -1) {
            return GlobalResponseCode.ApplyBudgetInvalid;
        }
        //校验退改类型
        if (apply.getSub_type() == null || ApplySubType.valueOf(apply.getSub_type()) == ApplySubType.Unknown) {
            return GlobalResponseCode.ApplyRefundChangeIsError;
        }
        ReasonType reasonType = null;
        if (ApplyType.valueOf(apply.getType()) == ApplyType.Air) {
            if (ApplySubType.valueOf(apply.getSub_type()) == ApplySubType.Refund) {
                reasonType = ReasonType.APPLY_REFUND_AIR;
            } else if (ApplySubType.valueOf(apply.getSub_type()) == ApplySubType.Change) {
                reasonType = ReasonType.APPLY_CHANGE_AIR;
            }
        } else if (ApplyType.valueOf(apply.getType()) == ApplyType.Hotel) {
            if (ApplySubType.valueOf(apply.getSub_type()) == ApplySubType.Refund) {
                reasonType = ReasonType.APPLY_REFUND_HOTEL;
            }
        } else if (ApplyType.valueOf(apply.getType()) == ApplyType.Train) {
            if (ApplySubType.valueOf(apply.getSub_type()) == ApplySubType.Refund) {
                reasonType = ReasonType.APPLY_REFUND_TRAIN;
            } else if (ApplySubType.valueOf(apply.getSub_type()) == ApplySubType.Change) {
                reasonType = ReasonType.APPLY_CHANGE_TRAIN;
            }
        }
        ReasonConfig reasonConfig = customReasonService.queryReasonConfigWithoutItems(companyId, reasonType);
        if (reasonConfig.getReason() == SaasMessageConstant.IS_CHECKED_TRUE) {
            if (StringUtils.isBlank(apply.getApply_reason())) {
                return GlobalResponseCode.ApplyReasonIsNull;
            }
            if (reasonConfig.getReason_desc() == SaasMessageConstant.IS_CHECKED_TRUE) {
                if (StringUtils.isBlank(apply.getApply_reason_desc())) {
                    return GlobalResponseCode.ApplyReasonDescIsNull;
                }
            }
        }
        if (apply.getApply_reason() != null && apply.getApply_reason().length() > 200) {
            //申请事由不能超过50字
            return GlobalResponseCode.ApplyReasonInvalid;
        }
        if (StringUtils.isNotBlank(apply.getApply_reason_desc()) && apply.getApply_reason_desc().length() > 200) {
            return GlobalResponseCode.ApplyReasonInvalid;
        }
        //审批类型 1.弹性审批流 2.固定审批流 3.分条件审批流
        CompanyApplyType applyType = CompanyApplyType.valueOf(apply.getFlow_type());
        if (applyType.getValue() == CompanyApplyType.Unknown.getValue()) {
            return GlobalResponseCode.ApplyFlowTypeError;
        }
        if (applyType.getValue() == CompanyApplyType.Elastic.getValue() && apply.getState() == ApplyStatus.PendingAudit.getValue() && StringUtils.isBlank(apply.getApprover_id())) {
            //申请单必须指定一个审批人
            return GlobalResponseCode.ApplyApproveIdInvalid;
        }
        if (applyType.getValue() == CompanyApplyType.Elastic.getValue() && employeeId.equals(apply.getApprover_id())) {
            //审批人不能是自己
            return GlobalResponseCode.ApplyApproverCannotBeSelf;
        }
        //检查行程
        List<ApplyTripInfoContract> trips = applyContract.getTrip_list();
        if (CollectionUtils.isEmpty(trips)) {
            //差旅和用车必须要有行程
            return GlobalResponseCode.ApplyTripIsNull;
        }
        if (trips.size() > 30) {
            //行程数据不超过30个
            return GlobalResponseCode.ApplyTripTooMuch;
        }
        for (ApplyTripInfoContract trip : trips) {
            trip.setStart_city_id("0");
            if (trip.getType() == null) {
                return GlobalResponseCode.ApplyTripTypeInvalid;
            }
            //检测行程单类型
            if (apply.getType() == ApplyType.ChaiLv.getValue()) {
                if (trip.getType() != BizType.Air.getValue()
                        && trip.getType() != BizType.Hotel.getValue()
                        && trip.getType() != BizType.Train.getValue()
                        && trip.getType() != BizType.IntlAir.getValue()) {
                    return GlobalResponseCode.ApplyTripNotTravel;
                }
            } else if (apply.getType() == ApplyType.Taxi.getValue()) {
                if (trip.getType() != BizType.Taxi.getValue()) {
                    return GlobalResponseCode.ApplyTripNotTaxi;
                }
            }
            Date startTime = null;
            //检测时间
            if (trip.getStart_time() == null) {
                return GlobalResponseCode.ApplyTripStartDateInvalid;
            }
            //检测地点
            if (trip.getStart_city_name() == null || trip.getStart_city_name().length() == 0) {
                return GlobalResponseCode.ApplyTripStartCityInvalid;
            }
            if (trip.getArrival_city_name() == null || trip.getArrival_city_name().length() == 0) {
                if (trip.getType() == BizType.Air.getValue() || trip.getType() == BizType.Train.getValue()) {
                    return GlobalResponseCode.ApplyTripArrivalCityInvalid;
                }
            }
            if (TripType.valueOf(trip.getTrip_type()) == TripType.Unknown) {
                trip.setTrip_type(TripType.oneWay.getValue());
            }
            if (trip.getType() == BizType.Air.getValue() && StringUtils.isBlank(trip.getEnd_time())) {
                return GlobalResponseCode.AirInvalidError;
            }
            if (trip.getType() == BizType.IntlAir.getValue()) {
                if (StringUtils.isBlank(trip.getAir_airline_name()) || StringUtils.isBlank(trip.getAir_flight_no()) || StringUtils.isBlank(trip.getAir_seat_msg())) {
                    return GlobalResponseCode.IntlAirInvalidError;
                }
                if (StringUtils.isBlank(trip.getEnd_time())) {
                    return GlobalResponseCode.IntlAirInvalidError;
                }
                if (trip.getTrip_type() == TripType.goAndBack.getValue()) {
                    if (StringUtils.isBlank(trip.getBack_start_time()) || StringUtils.isBlank(trip.getBack_end_time())) {
                        return GlobalResponseCode.IntlAirInvalidError;
                    }
                }
            }
        }
        validTrain(applyContract);
        return GlobalResponseCode.Success;
    }

    private ApplyV2Contract getApplyByIdAndUserIdAndCompanyId(String applyId, String userId, String companyId, UserRole userRole, String token) throws SaasException {
        ApplyOrder applyModel = getApplyOrderModelByIdAndUserIdAndCompanyId(applyId, userId, companyId, userRole);
        if (applyModel == null) {
            applyModel = applyOrderExtMapper.getByIdAndApproverIdAndCompanyId(applyId, userId, companyId);
        }
        if (applyModel == null) {
            List<ApplyOrder> ccApplyOrderCountByApplyIdList = applyOrderExtMapper.getCCApplyOrderCountByApplyId(userId, companyId, 0);
            if (CollectionUtils.isEmpty(ccApplyOrderCountByApplyIdList)) {
                return null;
            }
            List<String> cclist = Lists.newArrayList();
            for (ApplyOrder applyOrder : ccApplyOrderCountByApplyIdList) {
                cclist.add(applyOrder.getId());
            }
            if (cclist.contains(applyId)) {
                applyModel = applyOrderMapper.selectByPrimaryKey(applyId);
            } else {
                return null;
            }
        }
        ApplyOrderV2Contract apply = ApplyOrderV2Contract.FromModel(applyModel);
        // 4.7.1 配置项目编码显示
        ApplySetupContract applySetupContract = messageSetupService.queryCompanyApplyCostAttributionConfig(companyId);
        if (applySetupContract != null
                && apply.getCost_attribution_category() == CostAttributionCategory.CostCenter.getKey()
                && SaasMessageConstant.IS_CHECKED_TRUE == applySetupContract.getApply_show_project_code()) {
            apply.setCost_attribution_code(applyModel.getCostAttributionCode());
        }
        Integer subType = apply.getSub_type();
        Integer type = apply.getType();
        Integer applyOrderType = apply.getApply_order_type();
        if (apply.getState() == 2) {
            if (applyOrderType == SaasFlowConstant.SETTING_TYPE_REFUND_CHANGE && type == ApplyType.Air.getValue() && subType == ApplySubType.Refund.getValue()) {
                apply.setOvertime_desc(CoreLanguage.Common_Value_TravelBackFeeWarn.getMessage());
            }
            if (applyOrderType == SaasFlowConstant.SETTING_TYPE_REFUND_CHANGE && type == ApplyType.Air.getValue() && subType == ApplySubType.Change.getValue()) {
                apply.setOvertime_desc(CoreLanguage.Common_Value_TravelChangeFeeWarn.getMessage());
            }
            if (applyOrderType == SaasFlowConstant.SETTING_TYPE_REFUND_CHANGE && type == ApplyType.Train.getValue() && subType == ApplySubType.Refund.getValue()) {
                apply.setOvertime_desc(CoreLanguage.Common_Value_OnlineBackTicket.getMessage());
            }
            if (applyOrderType == SaasFlowConstant.SETTING_TYPE_REFUND_CHANGE && type == ApplyType.Train.getValue() && subType == ApplySubType.Change.getValue()) {
                apply.setOvertime_desc(CoreLanguage.Common_Value_OnlineChangeTicket.getMessage());
            }
            if (applyOrderType == SaasFlowConstant.SETTING_TYPE_REFUND_CHANGE && type == ApplyType.Hotel.getValue() && subType == ApplySubType.Refund.getValue()) {
                apply.setOvertime_desc(CoreLanguage.Common_Value_HotelRuleNotBack.getMessage());
            }
        }
        if (StringUtils.isNotBlank(apply.getExceed_buy_desc())) {
            String[] exceedBugDescs = apply.getExceed_buy_desc().split(";");
            List<String> exceedBuyDescList = Arrays.asList(exceedBugDescs);
            apply.setExceed_buy_desc_list(exceedBuyDescList);
        }
        List<String> skippedApprverIds = new ArrayList<>();//固定审批流跳过的审批人
        List<ApplyFlowUserItem> applyFlowUserItems = applyFlowService.getFlowItemsByApplyId(applyId);
        if (applyModel.getFlowType() == CompanyApplyType.Elastic.getValue()) {
            apply.setNode_status(1);
        } else {
            FindNextApproverIdAndStatusResult findNextApproverIdAndStatusResult = findStatusInFlow(applyModel, applyFlowUserItems, skippedApprverIds, true);
            //最后审批人
            if (findNextApproverIdAndStatusResult != null && findNextApproverIdAndStatusResult.getApplyStatus() == 2) {
                apply.setNode_status(1);
            } else {
                apply.setNode_status(2);
            }
        }
        //返回当前时间戳
        apply.setCurrent_time(DateTimeTool.fromDateTimeToString(new Date()));
        ApplyV2Contract applyContract = new ApplyV2Contract();
        applyContract.setApply(apply);
        //不是采购申请,读取行程
        List<ApplyTripInfoContract> tripList = getTripListByApplyOrderId(applyId, applyModel);
        applyContract.setTrip_list(tripList);
        return applyContract;
    }

    /**
     * 获取审批是否是终审节点
     *
     * @param apply
     * @param applyFlowUserItems
     * @param skippedApprverIds
     * @param couldFinalApprove
     * @return
     * @throws SaasException
     */
    private FindNextApproverIdAndStatusResult findStatusInFlow(ApplyOrder apply, List<ApplyFlowUserItem> applyFlowUserItems, List<String> skippedApprverIds, boolean couldFinalApprove) throws SaasException {
        String approverId = null;
        String receiverId = null;
        ApplyStatus finalStatus;
        Integer applyStatus = null;
        int remainIndex = applyFlowUserItems.size();
        for (int i = 0; i < applyFlowUserItems.size(); i++) {
            if (ValueTool.areEqual(applyFlowUserItems.get(i).getStatus(), ApplyFlowUserItemStatus.Waiting.getValue())) {
                remainIndex = i;
                break;
            }
        }
        //找下一个审批人
        boolean findNextApprover = false;
        // 是否是最后一名审批人
        boolean finnalApprover = true;
        for (int i = remainIndex; i < applyFlowUserItems.size(); i++) {
            ApplyFlowUserItem item = applyFlowUserItems.get(i);
            if (ValueTool.areEqual(item.getStatus(), ApplyFlowUserItemStatus.Waiting.getValue())) {
                finnalApprover = false;
                if (userService.isUserInCompany(item.getUserId(), apply.getCompanyId())) {
                    findNextApprover = true;
                    receiverId = item.getUserId();
                    approverId = receiverId;
                    applyStatus = 1;
                    break;
                } else {
                    skippedApprverIds.add(item.getUserId());
                }
            }
        }
        if (finnalApprover && couldFinalApprove) {
            //当前审批人已经是最后一个审批人
            finalStatus = ApplyStatus.Approved;
            receiverId = apply.getEmployeeId();
            applyStatus = 2;
        } else {
            finalStatus = ApplyStatus.PendingAudit;
            if (!findNextApprover) {
                //后面的审批人都已不存在，直接跳到授权负责人
                List<String> superAdminEmployeeIds = customRoleService.getEmployeeIdByRoleId(Integer.toString(UserRole.CompanySuperAdmin.getValue()), apply.getCompanyId());
                if (superAdminEmployeeIds == null || superAdminEmployeeIds.size() == 0) {
                    throw new SaasException(GlobalResponseCode.ApplyCanotApproveBecauseHasNoSuperAdmin);
                } else {
                    receiverId = superAdminEmployeeIds.get(0);
                    approverId = receiverId;
                    applyStatus = 3;
                }
            }
        }
        FindNextApproverIdAndStatusResult result = new FindNextApproverIdAndStatusResult();
        result.setApproverId(approverId);
        result.setReceiverId(receiverId);
        result.setFinnalStatus(finalStatus);
        result.setApplyStatus(applyStatus);
        return result;
    }

    private ApplyOrder getApplyOrderModelByIdAndUserIdAndCompanyId(String id, String userId, String companyId, UserRole userRole) {
        if (StringTool.isNullOrEmpty(id) || StringTool.isNullOrEmpty(userId) || StringTool.isNullOrEmpty(companyId)) {
            return null;
        }
        ApplyOrder apply = applyOrderMapper.selectByPrimaryKey(id);
        if (apply == null) {
            return null;
        }
        if (apply.getCompanyId() == null || !apply.getCompanyId().equals(companyId)) {
            return null;
        }
        if (!apply.getEmployeeId().equals(userId)) {
            //如果不是本人，再判断一下userrole是不是分贝管理员或者企业管理员
            if (userRole == null || (userRole != UserRole.CompanyAdmin && userRole != UserRole.CompanySuperAdmin && userRole != UserRole.FbAdmin)) {
                return null;
            }
        }
        return apply;
    }

    private List<ApplyTripInfoContract> getTripListByApplyOrderId(String applyOrderId, ApplyOrder applyOrder) throws SaasException {
        List<ApplyTripInfo> tripList = applyTripInfoExtMapper.selectListByApplyOrderId(applyOrderId);
        if (CollectionUtils.isEmpty(tripList)) {
            return null;
        }
        List<ApplyTripInfoContract> tripContractList = new ArrayList<ApplyTripInfoContract>();
        for (ApplyTripInfo trip : tripList) {
            ApplyTripInfoContract contract = ApplyTripInfoContract.FromCenterModel(trip);
            if (StringUtils.isNotBlank(trip.getCostAttributionName())) {
                contract.setCost_attribution_name((Map<String, Object>) JSON.parse(trip.getCostAttributionName()));
            }
            if (StringUtils.isNotBlank(trip.getTripContent())) {
                Map<String, Object> tripOrderInfo = (Map<String, Object>) JSON.parse(trip.getTripContent());
                Map<String, Object> orderInfo = (Map<String, Object>) tripOrderInfo.get("order_info");
                contract.setTrip_order_info(orderInfo);
            }
            List<ApplyTripApplicateExt> applyTripApplicateExts = applyTripApplicateExtMapper.queryOrderIdByTripId(applyOrderId, trip.getId());
            if (CollectionUtils.isEmpty(applyTripApplicateExts)) {
                throw new SaasException(GlobalResponseCode.QueryOrderIdByTripApplicateCurrentIdError);
            }
            contract.setOrder_id(applyTripApplicateExts.get(0).getOrderId());
            tripContractList.add(contract);
        }
        return tripContractList;
    }

    /**
     * 同行人信息
     *
     * @param applyOrderId
     * @return
     */
    private List<UserContactContract> getGuestListByApplyOrderId(String applyOrderId) {
        List<ApplyTripGuest> guestList = applyGuestExtMapper.selectListByApplyOrderId(applyOrderId);
        if (CollectionUtils.isEmpty(guestList)) {
            return null;
        }
        List<UserContactContract> guestContractList = new ArrayList<UserContactContract>();
        for (ApplyTripGuest guest : guestList) {
            UserContactContract contract;
            if (StringUtils.isNotBlank(guest.getContactInfo())) {
                contract = JSONObject.parseObject(guest.getContactInfo(), UserContactContract.class);
                if (StringUtils.isNotBlank(contract.getPhone())) {
                    contract.setPhone(contract.getPhone().replace(contract.getPhone().substring(3, 7), "****"));
                }
                if (StringUtils.isNotBlank(contract.getName())) {
                    guestContractList.add(contract);
                }
            }
        }
        for (UserContactContract userContactContract : guestContractList) {
            if (StringUtils.isBlank(userContactContract.getId_number())) {
                userContactContract.setId_number("");
            }
            if (userContactContract.getId_type() == null) {
                KeyValueItem keyValueItem = new KeyValueItem();
                keyValueItem.setKey(1);
                keyValueItem.setValue("");
                userContactContract.setId_type(keyValueItem);
            }
            userContactContract.setGender(null);
        }
        return guestContractList;
    }

    /**
     * 整合订单审批的订单信息
     */
    @Override
    public JSONObject disposeOrderInfo(String orderId, Integer orderType, String userId, String applyId, String ticketIds, String clientHeadVersion) {
        Object contactName = null;
        String contactPhone = null;
        Object orderIdInfo = null;
        Object remarkReason = null;
        Object remarkDetail = null;
        Object orderPerson = null;
        Object orderPersonUserId = null;
        Object costAttributionName = null;
        JSONObject orderInfo = new JSONObject();
        try {
            if (orderType == BizType.Air.getValue()) {
                logger.info("获取国内机票退改订单详情:" + URL_GET_AIR_REFUNGCHANGE_ORDER_INFO + "?order_id=" + orderId + "&ticket_ids=" + ticketIds + "&apply_id=" + applyId);
                String airData = HttpTool.get(URL_GET_AIR_REFUNGCHANGE_ORDER_INFO + "?order_id=" + orderId + "&ticket_ids=" + ticketIds + "&apply_id=" + applyId);
                logger.info("获取国内机票退改订单详情结果:" + airData);
                Map<String, Object> jo = JSONObject.parseObject(airData, HashMap.class);
                Map<String, Object> dataMap = (Map<String, Object>) jo.get("data");
                Map<String, Object> airDataMap = (Map<String, Object>) dataMap.get("order");
                List<Map<String, Object>> newAirDataMap = (List<Map<String, Object>>) dataMap.get("new_orders");
                if (CollectionUtils.isEmpty(newAirDataMap)) {
                    newAirDataMap = Lists.newArrayList();
                }
                orderInfo.put("new_order", newAirDataMap);
                contactName = airDataMap.get("contact_name");
                contactPhone = ObjUtils.toString(airDataMap.get("contact_phone"));
                orderIdInfo = airDataMap.get("order_id");
                remarkReason = airDataMap.get("remark_reason");
                remarkDetail = airDataMap.get("remark_detail");
                costAttributionName = airDataMap.get("cost_attribution");
                Map<String, Object> orderPersonInfo = (Map<String, Object>) airDataMap.get("order_owner");
                orderPerson = orderPersonInfo.get("name");
                orderPersonUserId = userId;


                //整合国内机票信息
                JSONObject airInfo = new JSONObject();
                Map<String, Object> segmentInfo = (Map<String, Object>) airDataMap.get("segment_info");
                Map<String, Object> priceInfo = (Map<String, Object>) airDataMap.get("price_info");
                Map<String, Object> stipulateInfo = (Map<String, Object>) airDataMap.get("stipulate_info");
                List<Map<String, Object>> insuranceInfo = (List<Map<String, Object>>) airDataMap.get("insurance_info");
                List<Map<String, Object>> priceDetail = (List<Map<String, Object>>) airDataMap.get("price_detail");
                List<Map<String, Object>> passengerList = (List<Map<String, Object>>) airDataMap.get("passenger_list");
                //行程信息
                airInfo.put("segment_info", segmentInfo);
                airInfo.put("stipulate_info", stipulateInfo);
                airInfo.put("insurance_info", insuranceInfo);
                airInfo.put("price_detail", priceDetail);
                airInfo.put("passenger_list", passengerList);
                //价格信息
                airInfo.put("price_info", priceInfo);
                List<Map<String, Object>> customRemark = (List<Map<String, Object>>) airDataMap.get("custom_remark");
                //如果是大唐资源，那么支持在线改期
                boolean support_online_change=false;
                if (SupplierType.DaTang.getKey()==airDataMap.get("supplier_id")){
                    support_online_change=true;
                }
                if (support_online_change&&ObjUtils.isNotEmpty(clientHeadVersion)){
                    if (VersionTool.compare(clientHeadVersion, "3.9.1") < 0){
                        if (SupplierType.DaTang.getKey()==airDataMap.get("supplier_id")){
                            throw new SaasException(GlobalResponseCode.APPLYFLOWDETAILERROR);
                        }
                    }
                }
                orderInfo.put("support_online_change", support_online_change);
                orderInfo.put("contact_name", contactName);
                orderInfo.put("contact_phone", contactPhone.replace(contactPhone.substring(3, 7), "****"));
                orderInfo.put("order_id", orderIdInfo);
                orderInfo.put("remark_reason", remarkReason);
                orderInfo.put("remark_detail", remarkDetail);
                orderInfo.put("custom_remark", customRemark);
                orderInfo.put("order_person", orderPerson);
                orderInfo.put("order_person_user_id", orderPersonUserId);
                orderInfo.put("cost_attribution_name", costAttributionName);
                orderInfo.put("air_info", airInfo);
            } else if (orderType == BizType.Hotel.getValue()) {
                logger.info("获取酒店订单详情:" + URL_GET_HOTEL_ORDER_INFO + "?order_id=" + orderId + "&apply_id=" + applyId);
                String hotelData = HttpTool.get(URL_GET_HOTEL_ORDER_INFO + "?order_id=" + orderId + "&apply_id=" + applyId);
                logger.info("获取酒店订单详情结果:" + hotelData);
                Map<String, Object> jo = JSONObject.parseObject(hotelData, HashMap.class);
                Map<String, Object> dataMap = (Map<String, Object>) jo.get("data");
                Map<String, Object> hotelDataMap = (Map<String, Object>) dataMap.get("order");
                List<Map<String, Object>> newHotelDataMap = (List<Map<String, Object>>) dataMap.get("new_orders");
                if (CollectionUtils.isEmpty(newHotelDataMap)) {
                    newHotelDataMap = Lists.newArrayList();
                }
                orderInfo.put("new_order", newHotelDataMap);
                contactName = hotelDataMap.get("contact_name");
                contactPhone = ObjUtils.toString(hotelDataMap.get("contact_phone_no"));
                orderIdInfo = hotelDataMap.get("order_id");
                remarkReason = hotelDataMap.get("remark");
                remarkDetail = hotelDataMap.get("remark_detail");
                orderPerson = hotelDataMap.get("order_person");
                orderPersonUserId = userId;
                costAttributionName = hotelDataMap.get("cost_attribution");

                //整合酒店信息
                JSONObject hotelInfo = new JSONObject();
                //入店日期
                Object checkinDate = hotelDataMap.get("checkin_date");
                //离店日期
                Object checkoutDate = hotelDataMap.get("checkout_date");
                //城市码
                Object cityCode = hotelDataMap.get("city_code");
                //城市名
                Object cityName = hotelDataMap.get("city_name");
                //酒店地址
                Object hotelAddress = hotelDataMap.get("hotel_address");
                //酒店名称
                Object hotelName = hotelDataMap.get("hotel_name");
                //酒店电话
                Object hotelPhone = hotelDataMap.get("hotel_phone");
                //床型名称
                Object bedType = hotelDataMap.get("bed_type");
                //房型名称
                Object roomName = hotelDataMap.get("room_name");
                //预订间数
                Object roomCount = hotelDataMap.get("room_count");
                //规则名称
                Object priceRuleTag = hotelDataMap.get("price_rule_tag");
                //价格计划名称
                Object planName = hotelDataMap.get("plan_name");
                //规则
                Object priceRule = hotelDataMap.get("price_rule");
                //保险信息
                List<Map<String, Object>> insuranceInfo = (List<Map<String, Object>>) hotelDataMap.get("insurance_info");
                //酒店同行人
                List<Map<String, Object>> guests = (List<Map<String, Object>>) hotelDataMap.get("guests");
                if (ObjUtils.isNotEmpty(guests)){
                    List liveWithList= guests.stream().filter(v->{
                        if (ObjUtils.isNotEmpty(v.get("live_with"))){
                            return true;
                        }
                        return false;
                    }).collect(Collectors.toList());
                    if (ObjUtils.isNotEmpty(clientHeadVersion)&&VersionTool.compare(clientHeadVersion, "3.9.3") < 0&&ObjUtils.isNotEmpty(liveWithList)&&liveWithList.size()>0){
                        throw new SaasException(GlobalResponseCode.ApplyCenterAlert);
                    }
                }
                //早餐信息
                Map<String, Object> breakfastPair = (Map<String, Object>) hotelDataMap.get("breakfast_pair");

                hotelInfo.put("checkin_date", checkinDate);
                hotelInfo.put("checkout_date", checkoutDate);
                hotelInfo.put("city_code", cityCode);
                hotelInfo.put("city_name", cityName);
                hotelInfo.put("hotel_address", hotelAddress);
                hotelInfo.put("hotel_name", hotelName);
                hotelInfo.put("hotel_phone", hotelPhone);
                hotelInfo.put("bed_type", bedType);
                hotelInfo.put("room_name", roomName);
                hotelInfo.put("room_count", roomCount);
                hotelInfo.put("price_rule_tag", priceRuleTag);
                hotelInfo.put("price_rule", priceRule);
                hotelInfo.put("plan_name", planName);
                hotelInfo.put("insurance_info", insuranceInfo);
                hotelInfo.put("guests", guests);
                hotelInfo.put("breakfast_pair", breakfastPair);
                orderInfo.put("hotel_info", hotelInfo);

                List<Map<String, Object>> customRemark = (List<Map<String, Object>>) hotelDataMap.get("custom_remark");
                orderInfo.put("contact_name", contactName);
                orderInfo.put("contact_phone", contactPhone.replace(contactPhone.substring(3, 7), "****"));
                orderInfo.put("order_id", orderIdInfo);
                orderInfo.put("remark_reason", remarkReason);
                orderInfo.put("remark_detail", remarkDetail);
                orderInfo.put("custom_remark", customRemark);
                orderInfo.put("order_person", orderPerson);
                orderInfo.put("order_person_user_id", orderPersonUserId);
                orderInfo.put("cost_attribution_name", costAttributionName);
                orderInfo.put("hotel_info", hotelInfo);
            } else if (orderType == BizType.Train.getValue()) {
                logger.info("获取火车订单详情:" + URL_GET_TRAIN_ORDER_INFO + "?order_id=" + orderId + "&apply_id=" + applyId + "&ticket_id=" + ticketIds);
                String trainData = HttpTool.get(URL_GET_TRAIN_ORDER_INFO + "?order_id=" + orderId + "&apply_id=" + applyId + "&ticket_id=" + ticketIds);
                logger.info("获取火车订单详情结果:" + trainData);
                Map<String, Object> jo = JSONObject.parseObject(trainData, HashMap.class);
                Map<String, Object> dataMap = (Map<String, Object>) jo.get("data");
                Map<String, Object> trainDataMap = (Map<String, Object>) dataMap.get("order");
                List<Map<String, Object>> newTrainDataMap = (List<Map<String, Object>>) dataMap.get("new_orders");
                if (CollectionUtils.isEmpty(newTrainDataMap)) {
                    newTrainDataMap = Lists.newArrayList();
                }
                orderInfo.put("new_order", newTrainDataMap);
                List<Map<String, Object>> tickets = (List<Map<String, Object>>) trainDataMap.get("tickets");
                for (Map<String, Object> ticketsMap : tickets) {
                    Map<String, Object> passengerInfo = (Map<String, Object>) ticketsMap.get("passengerInfo");
                    String phoneNum = ObjUtils.toString(passengerInfo.get("phone_num"));
                    if (StringUtils.isNotBlank(phoneNum)) {
                        passengerInfo.put("phone_num", phoneNum.replace(phoneNum.substring(3, 7), "****"));
                    }
                }
                Map<String, Object> costAttribution = (Map<String, Object>) trainDataMap.get("costAttribution");
                costAttributionName = costAttribution.get("name");
                Map<String, Object> contactInfo = (Map<String, Object>) trainDataMap.get("contact");
                contactName = contactInfo.get("name");
                contactPhone = ObjUtils.toString(contactInfo.get("phone"));
                orderIdInfo = trainDataMap.get("orderId");
                Map<String, Object> bookingPerson = (Map<String, Object>) trainDataMap.get("bookingPerson");
                orderPerson = bookingPerson.get("name");
                Object totalPrice = trainDataMap.get("totalPrice");
                orderPersonUserId = userId;
                //整合火车信息
                JSONObject trainInfo = new JSONObject();
                Map<String, Object> routeInfo = (Map<String, Object>) trainDataMap.get("routeInfo");
                //行程信息
                trainInfo.put("routeInfo", routeInfo);
                trainInfo.put("totalPrice", totalPrice);
                trainInfo.put("tickets", tickets);
                orderInfo.put("contact_name", contactName);
                orderInfo.put("contact_phone", contactPhone.replace(contactPhone.substring(3, 7), "****"));
                orderInfo.put("order_id", orderIdInfo);
                orderInfo.put("remark_reason", remarkReason);
                orderInfo.put("remark_detail", remarkDetail);
                orderInfo.put("order_person", orderPerson);
                orderInfo.put("order_person_user_id", orderPersonUserId);
                orderInfo.put("cost_attribution_name", costAttributionName);
                orderInfo.put("train_info", trainInfo);
            }
        } catch (Exception e) {
            logger.info("订单审批获取订单信息异常：" + "orderId：" + orderId + " orderType：" + orderType + " 异常提示：" + e.getMessage());
            if (e instanceof SaasException){
                throw new SaasException(GlobalResponseCode.APPLYFLOWDETAILERROR);
            }
        }
        return orderInfo;
    }

    private List<ApplyOrderLogContract> getLogsByApplyId(String applyId, String companyId) {
        List<ApplyOrderLog> logs = applyOrderLogExtMapper.selectListByApplyId(applyId);
        logs = logs.stream().filter(logInfo -> logInfo.getAction() != ApplyLogAction.CreateDraft.getValue() && logInfo.getAction() != ApplyLogAction.ModifyDraft.getValue()).collect(Collectors.toList());
        //查询是否有已拒绝、已撤销和超时状态的审批日志
        List<ApplyOrderLog> destinationLogList = logs.stream().filter(logInfo -> logInfo.getAction() == ApplyLogAction.Refuse.getValue() || logInfo.getAction() == ApplyLogAction.Revoke.getValue() || logInfo.getAction() == ApplyLogAction.Overtime.getValue()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(destinationLogList)) {
            logs = logs.stream().filter(logInfo -> logInfo.getAction() != ApplyLogAction.Approval.getValue() && logInfo.getAction() != ApplyLogAction.Unknown.getValue()).collect(Collectors.toList());
        }
        List<ApplyOrderLogContract> result = getLogContractList(logs, companyId);
        return result;
    }

    private void appendApplyDetail(String token, ApplyV2Contract detail, String userId) {
        ApplyOrderV2Contract order = detail.getApply();

        //申请人及审批人的姓名及部门
        List<String> userIds = new ArrayList<>();
        userIds.add(order.getEmployee_id());
        if (order.getApprover_id() != null && order.getApprover_id().length() > 0 && !userIds.contains(order.getApprover_id())) {
            userIds.add(order.getApprover_id());
        }
        List<EmployeeNameAndDeptContract> approverNameAndDepts = userService.getNamesAndDeptsByIds(token, order.getCompany_id(), userIds);
        if (approverNameAndDepts != null && approverNameAndDepts.size() > 0) {
            for (EmployeeNameAndDeptContract contract : approverNameAndDepts) {
                if (contract.getEmployee_id().equals(order.getEmployee_id())) {
                    order.setUser_name(contract.getName());
                    order.setUser_dept(String.join(",", contract.getDepts()));
                    break;
                }
            }
            if (order.getApprover_id() != null) {
                for (EmployeeNameAndDeptContract contract : approverNameAndDepts) {
                    if (contract.getEmployee_id().equals(order.getApprover_id())) {
                        order.setApprover_dept(String.join(",", contract.getDepts()));
                        order.setApprover_name(contract.getName());
                        break;
                    }
                }
            }
        }
        if (StringUtils.isBlank(order.getUser_name())) {
            order.setUser_name(detail.getApply().getApplicant_name());
        }
        if (StringUtils.isBlank(order.getUser_dept())) {
            order.setUser_dept(CoreLanguage.Common_Value_NoDepartmentInfo.getMessage());
        }
        //可操作权限
        Integer operateAuth = genApplyOperateAuth(order, userId);
        order.setOperate_auth(operateAuth);
        //处理审批流设置
        appendFlowData(order);
    }

    private void appendFlowData(ApplyOrderV2Contract applyOrderContract) {
        CompanyApplyFlowSetV2RequestContract flowSet = null;
        if (applyOrderContract.getFlow_type() == CompanyApplyType.Flow.getValue() || applyOrderContract.getFlow_type() == CompanyApplyType.CONDITIONAL.getValue()) {
            flowSet = applyFlowV2Service.getFlowByApplyId(applyOrderContract.getId(), applyOrderContract.getCompany_id());
            flowSet = applyFlowV2Service.getFlowCCByApplyId(applyOrderContract.getId(), flowSet, applyOrderContract.getCompany_id());
        }
        if (applyOrderContract.getFlow_type() == CompanyApplyType.Elastic.getValue()) {
            flowSet = applyFlowV2Service.getFlowCCByApplyId(applyOrderContract.getId(), flowSet, applyOrderContract.getCompany_id());
            flowSet.setCompany_apply_type(1);
        }
        if (flowSet != null) {
            applyOrderContract.setFlow_type(flowSet.getCompany_apply_type());
            applyOrderContract.setFlow(flowSet);
        }
    }

    /**
     * 判断操作权限
     *
     * @param order
     * @param userId
     * @return
     */
    private Integer genApplyOperateAuth(ApplyOrderV2Contract order, String userId) {
        ApplyStatus orderStatus = ApplyStatus.valueOf(order.getState());
        if (order.getEmployee_id().equals(userId)) {
            //自己的申请单
            if (orderStatus == ApplyStatus.PendingAudit) {
                if (userId.equals(order.getApprover_id())) {
                    return ApplyLogAction.Approve.getValue() + ApplyLogAction.Forward.getValue() + ApplyLogAction.Refuse.getValue() + ApplyLogAction.Revoke.getValue();
                }
                //状态为待审核时,用户可撤销申请
                return ApplyLogAction.Revoke.getValue();
                //} else if (orderStatus == ApplyStatus.Return || orderStatus == ApplyStatus.Draft) {
            } else if (orderStatus == ApplyStatus.Draft) {
                //状态为被驳回或草稿时,用户可提交申请或者修改
                return ApplyLogAction.Submit.getValue() + ApplyLogAction.ModifyDraft.getValue() + ApplyLogAction.Delete.getValue();
            }
        } else {
            //是需要自己审批,或者自己已经操作过的审批单
            if (orderStatus == ApplyStatus.PendingAudit || orderStatus == ApplyStatus.Transfer) {
                //只有待自己处理的审批单才可操作
                if (userId.equals(order.getApprover_id())) {
                    return ApplyLogAction.Approve.getValue() + ApplyLogAction.Forward.getValue() + ApplyLogAction.Refuse.getValue();
                }
            }
        }
        //不能做任何操作
        return 0;
    }

    private String genApplyLogDisplayString(ApplyOrderLog log, List<IdNameContract> userNames) {
        StringBuilder sb = new StringBuilder();
        IdNameContract sponsor = getNameFromListById(userNames, log.getSponsorId());
        if (sponsor != null) {
            sb.append(sponsor.getName() + " ");
        }
        ApplyLogAction action = ApplyLogAction.valueOf(log.getAction());
        String actionName = "";
        if (action == ApplyLogAction.Unknown) {
            actionName = "";
        } else {
            actionName = action.getDesc();
        }
        sb.append(actionName);
        if (action == ApplyLogAction.Forward
                || action == ApplyLogAction.Submit
                || action == ApplyLogAction.ReSubmit
                ) {
            sb.append(CoreLanguage.Common_Value_Give.getMessage() + " ");
            if (log.getReceiverId() != null) {
                IdNameContract receiver = getNameFromListById(userNames, log.getReceiverId());
                if (receiver != null) {
                    sb.append(receiver.getName());
                }
            }
        }
        return sb.toString();
    }

    private List<ApplyOrderLogContract> getLogContractList(List<ApplyOrderLog> logs, String companyId) {
        if (logs == null || logs.size() == 0) {
            return new ArrayList<>();
        }
        List<ApplyOrderLogContract> result = new ArrayList<>();
        List<String> userIds = new ArrayList<>();
        for (ApplyOrderLog log : logs) {
            if (!userIds.contains(log.getSponsorId())) {
                userIds.add(log.getSponsorId());
            }
            if (log.getReceiverId() != null && !userIds.contains(log.getReceiverId())) {
                userIds.add(log.getReceiverId());
            }
        }
        List<IdNameContract> userNames = userService.getUserNameByIds(userIds, companyId);
        for (ApplyOrderLog log : logs) {
            ApplyOrderLogContract logContract = genLogContract(log, userNames);
            if (logContract != null && logContract.getAction() == ApplyLogAction.Unknown.getValue() && StringUtils.isBlank(logContract.getSponsor()) && StringUtils.isBlank(logContract.getLog())) {
                logContract.setAction(ApplyLogAction.Skip.getValue());
                logContract.setCheck_reason(CoreLanguage.Common_Value_ApprovalPersonDelete.getMessage());
            }
            result.add(logContract);
        }
        return result;
    }

    private ApplyOrderLogContract genLogContract(ApplyOrderLog log, List<IdNameContract> userNames) {
        ApplyOrderLogContract contract = ApplyOrderLogContract.FromModel(log);
        String logContent = genApplyLogDisplayString(log, userNames);
        contract.setLog(logContent);
        if (StringUtils.isNotBlank(log.getSponsorId())) {
            for (IdNameContract idNameContract : userNames) {
                if (idNameContract.getId().equals(log.getSponsorId())) {
                    contract.setSponsor(idNameContract.getName());
                }
            }
        }
        if (StringUtils.isNotBlank(log.getReceiverId())) {
            for (IdNameContract idNameContract : userNames) {
                if (idNameContract.getId().equals(log.getReceiverId())) {
                    contract.setReceiver(idNameContract.getName());
                }
            }
        }
        return contract;
    }

    private IdNameContract getNameFromListById(List<IdNameContract> list, String id) {
        if (list == null || id == null) {
            return null;
        }
        for (IdNameContract item : list) {
            if (item.getId().equals(id)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 审批单详情
     *
     * @param token
     * @param applyId
     * @param userId
     * @param companyId
     * @param userRole
     * @return
     * @throws SaasException
     */
    @Override
    public ApplyV2Contract getDetail(String token, String applyId, String userId, String companyId, UserRole userRole,String clientHeadVersion) throws SaasException {
        FlowCheckUtil.check(companyId, clientHeadVersion);
        ApplyV2Contract apply = getApplyByIdAndUserIdAndCompanyId(applyId, userId, companyId, userRole, token);
        if (apply == null) {
            return null;
        }
        //1、申请人及审批人的姓名及部门 2、可操作权限
        appendApplyDetail(token, apply, userId);
        List<ApplyOrderLogContract> logs = getLogsByApplyId(applyId, companyId);
        for (ApplyOrderLogContract applyOrderLogContract : logs) {
            if (StringUtils.isBlank(applyOrderLogContract.getSponsor()) && apply.getApply().getEmployee_id().equals(applyOrderLogContract.getSponsor_id())) {
                if (applyOrderLogContract.getAction() != ApplyLogAction.Skip.getValue()) {
                    applyOrderLogContract.setSponsor(apply.getApply().getApplicant_name());
                }
            }
        }
        apply.setLog_list(logs);
        String orderId = apply.getTrip_list().get(0).getOrder_id();
        Integer orderType = apply.getTrip_list().get(0).getType();
        String ticketIds = "";
        Integer subType = apply.getApply().getSub_type();
        Integer type = apply.getApply().getType();
        //机票退订票号的获取
        if (type == ApplyType.Air.getValue() && subType == ApplySubType.Refund.getValue()) {
            Map<String, Object> tripOrderInfo = apply.getTrip_list().get(0).getTrip_order_info();
            String ticketId = ObjUtils.toString(tripOrderInfo.get("product_ids"));
            if (StringUtils.isNotBlank(ticketId)) {
                ticketIds = ticketId;
            }
        }
        //机票改期票号的获取
        if (type == ApplyType.Air.getValue() && subType == ApplySubType.Change.getValue()) {
            Map<String, Object> tripOrderInfo = apply.getTrip_list().get(0).getTrip_order_info();
            List<String> ticketIdList = (List<String>) tripOrderInfo.get("ticket_ids");
            if (CollectionUtils.isNotEmpty(ticketIdList)) {
                ticketIds = StringUtils.join(ticketIdList, ",");
            }
        }
        //火车退订票号的获取
        if (type == ApplyType.Train.getValue() && subType == ApplySubType.Refund.getValue()) {
            Map<String, Object> tripOrderInfo = apply.getTrip_list().get(0).getTrip_order_info();
            List<String> ticketIdList = ((List<String>) tripOrderInfo.get("ticket_ids"));
            if (CollectionUtils.isNotEmpty(ticketIdList)) {
                ticketIds = ticketIdList.get(0);
            }
        }
        //火车改签票号的获取
        if (type == ApplyType.Train.getValue() && subType == ApplySubType.Change.getValue()) {
            Map<String, Object> tripOrderInfo = apply.getTrip_list().get(0).getTrip_order_info();
            List<String> ticketIdList = ((List<String>) tripOrderInfo.get("product_ids"));
            if (CollectionUtils.isNotEmpty(ticketIdList)) {
                ticketIds = ticketIdList.get(0);
            }
        }
        apply.setOrder_info(disposeOrderInfo(orderId, orderType, apply.getApply().getEmployee_id(), applyId, ticketIds,clientHeadVersion));
        return apply;
    }

    /**
     * 处理超时时间
     *
     * @return
     */
    private Date disposeOverTime(String orderId, Integer orderType, String userId, String applyId, String ticketIds, String clientVersion, Integer subType) {
        try {
            if (orderType == BizType.Air.getValue()) {
                logger.info("获取国内机票退改订单详情:" + URL_GET_AIR_REFUNGCHANGE_ORDER_INFO + "?order_id=" + orderId + "&ticket_ids=" + ticketIds + "&apply_id=" + applyId);
                String airData = HttpTool.get(URL_GET_AIR_REFUNGCHANGE_ORDER_INFO + "?order_id=" + orderId + "&ticket_ids=" + ticketIds + "&apply_id=" + applyId);
                logger.info("获取国内机票退改订单详情结果:" + airData);
                Map<String, Object> jo = JSONObject.parseObject(airData, HashMap.class);
                Map<String, Object> dataMap = (Map<String, Object>) jo.get("data");
                Map<String, Object> airDataMap = (Map<String, Object>) dataMap.get("order");
                Map<String, Object> segmentInfo = (Map<String, Object>) airDataMap.get("segment_info");
                Long departureTimestamp = ObjUtils.toLong(segmentInfo.get("departure_timestamp"));
                Date overDate = new Date(departureTimestamp);
                Calendar c = Calendar.getInstance();
                c.setTime(overDate);
                if (subType == ApplySubType.Refund.getValue()) {
                    c.add(Calendar.DATE, 30);
                } else {
                    c.add(Calendar.HOUR_OF_DAY, 4);
                }
                Date overTime = c.getTime();
                //老版本判断是否大唐资源
                if (VersionTool.compare(clientVersion, "3.9.1") < 0){
                    if (SupplierType.DaTang.getKey()==airDataMap.get("supplier_id")){
                        throw new SaasException(GlobalResponseCode.APPLYFLOWDETAILERROR);
                    }
                }
                return overTime;
            } else if (orderType == BizType.Hotel.getValue()) {
                logger.info("获取酒店订单详情:" + URL_GET_HOTEL_ORDER_INFO + "?order_id=" + orderId + "&apply_id=" + applyId);
                String hotelData = HttpTool.get(URL_GET_HOTEL_ORDER_INFO + "?order_id=" + orderId + "&apply_id=" + applyId);
                logger.info("获取酒店订单详情结果:" + hotelData);
                Map<String, Object> jo = JSONObject.parseObject(hotelData, HashMap.class);
                Map<String, Object> dataMap = (Map<String, Object>) jo.get("data");
                Map<String, Object> hotelDataMap = (Map<String, Object>) dataMap.get("order");
                String lastCancelTime = ObjUtils.toString(hotelDataMap.get("last_cancel_time"));
                Calendar c = Calendar.getInstance();
                c.setTime(DateTimeTool.fromStringToDateTime(lastCancelTime));
                c.add(Calendar.MINUTE, -3);
                Date overTime = c.getTime();
                return overTime;
            } else if (orderType == BizType.Train.getValue()) {
                logger.info("获取火车订单详情:" + URL_GET_TRAIN_ORDER_INFO + "?order_id=" + orderId + "&apply_id=" + applyId);
                String trainData = HttpTool.get(URL_GET_TRAIN_ORDER_INFO + "?order_id=" + orderId + "&apply_id=" + applyId);
                logger.info("获取火车订单详情结果:" + trainData);
                Map<String, Object> jo = JSONObject.parseObject(trainData, HashMap.class);
                Map<String, Object> dataMap = (Map<String, Object>) jo.get("data");
                Map<String, Object> trainDataMap = (Map<String, Object>) dataMap.get("order");
                Map<String, Object> routeInfo = (Map<String, Object>) trainDataMap.get("routeInfo");
                String startTime = ObjUtils.toString(routeInfo.get("trainStartDate")) + " " + routeInfo.get("startTime") + ":00";
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd HH:mm:ss");
                Calendar c = Calendar.getInstance();
                c.setTime(sdf.parse(startTime));
                c.add(Calendar.MINUTE, -43);
                Date overTime = c.getTime();
                return overTime;
            }
        } catch (Exception e) {
            logger.info("订单审批获取订单信息异常：" + "orderId：" + orderId + " orderType：" + orderType + " 异常提示：" + e.getMessage());
            if (e instanceof SaasException){
                throw new SaasException(GlobalResponseCode.APPLYFLOWDETAILERROR);
            }
        }
        return null;
    }

    /**
     * 订单审批单（同意）
     *
     * @param approveModel
     * @param userId
     * @param ip
     * @return
     */
    @Override
    //@Transactional(value = "saas")
    public GlobalResponseCode approve(ApplyApproveContract approveModel, String userId, String companyId, String ip, String clientVersion, String source) throws SaasException {
        FlowCheckUtil.check(companyId, clientVersion, source);
        if (approveModel == null) {
            throw new SaasException(GlobalResponseCode.ApplyIsNull);
        }
        if (StringTool.isNullOrEmpty(approveModel.getApply_id())) {
            throw new SaasException(GlobalResponseCode.ApplyIdInvalid);
        }
        if (approveModel.getComment() != null && approveModel.getComment().length() > 200) {
            throw new SaasException(GlobalResponseCode.ApplyCheckReasonInvalid);
        }
        String applyId = approveModel.getApply_id();

        ApplyOrder apply = applyOrderMapper.selectByPrimaryKey(approveModel.getApply_id());
        if (apply == null) {
            throw new SaasException(GlobalResponseCode.NotFound);
        }
        if (apply.getState() != null && apply.getState().intValue() == ApplyStatus.Overtime.getValue()) {
            //订单审批已超时
            throw new SaasException(GlobalResponseCode.CenterApplyOverTime);
        }
        if (apply.getState() != null && apply.getState().intValue() == ApplyStatus.Backout.getValue()) {
            //事中审批已撤销
            throw new SaasException(GlobalResponseCode.CenterApplyCancleOrder);
        }
        if (!apply.getCompanyId().equals(companyId)) {
            //申请单和当前人不在一个公司
            throw new SaasException(GlobalResponseCode.ApplyCompanyCannotMatch);
        }
        if (apply.getState() != ApplyStatus.PendingAudit.getValue()) {
            //该单不是待审核,不能审批
            throw new SaasException(GlobalResponseCode.ApplyStatusNotPendingAudit);
        }
        if (!apply.getApproverId().equals(userId)) {
            //不是当前人可以处理的审批单
            throw new SaasException(GlobalResponseCode.NoAuth);
        }
        ApplyStatus finalStatus;
        ApplyLogAction action;
        String receiverId; //下一个审批人/通知人
        String approverId; //审批人
        Integer logId = null;
        List<String> skippedApprverIds = new ArrayList<>();//固定审批流跳过的审批人
        Date now = new Date();
        action = ApplyLogAction.Approve;
        approverId = userId;
        receiverId = null;
        Integer finalJudgmentUser = null;
        boolean isFlow = (ValueTool.areEqual(apply.getFlowType(), CompanyApplyType.Flow.getValue()) || ValueTool.areEqual(apply.getFlowType(), CompanyApplyType.CONDITIONAL.getValue()));
        if (isFlow) {
            //固定审批流和分条件审批流
            List<ApplyFlowUserItem> applyFlowUserItems = applyFlowService.getFlowItemsByApplyId(applyId);
            if (applyFlowUserItems == null ||
                    applyFlowUserItems.size() == 0 ||
                    applyFlowUserItems.stream().allMatch(m ->
                            !ValueTool.areEqual(m.getStatus(), ApplyFlowUserItemStatus.PendingAudit.getValue())
                                    && !ValueTool.areEqual(m.getStatus(), ApplyFlowUserItemStatus.Transfered.getValue())
                    )) {
                //没有待审核记录，也没有转交记录，预示审批流已经审核通过
                finalStatus = ApplyStatus.Approved;
                receiverId = apply.getEmployeeId();
                //处理审批日志和审批单的状态
                logId = sloveApplyLog(applyId, receiverId, approverId, finalStatus, now, ip, userId, action, approveModel);
                finalJudgmentUser = 2;
            } else {
                int pendingAuditItemIndex = -1;
                for (int i = 0; i < applyFlowUserItems.size(); i++) {
                    if (ValueTool.areEqual(applyFlowUserItems.get(i).getStatus(), ApplyFlowUserItemStatus.PendingAudit.getValue())) {
                        pendingAuditItemIndex = i;
                        break;
                    }
                }
                ApplyFlowUserItem pendingAuditItem = null;
                if (pendingAuditItemIndex >= 0) {
                    pendingAuditItem = applyFlowUserItems.get(pendingAuditItemIndex);
                }
                if (pendingAuditItem != null && !pendingAuditItem.getUserId().equals(userId)) {
                    logger.info("当前待审批的item不是userid，userId：" + userId + " itemId:" + pendingAuditItem.getUserId());
                    throw new SaasException(GlobalResponseCode.NoAuth);
                } else {
                    if (pendingAuditItem != null) {
                        //将item status设置为已审核
                        applyFlowService.setApplyFlowUserItemStatus(pendingAuditItem.getId(), ApplyFlowUserItemStatus.Approved);
                    }
                    try {
                        FindNextApproverIdAndStatusResult findNextApproverIdAndStatusResult = findNextApproverIdAndStatusInFlow(apply, applyFlowUserItems, skippedApprverIds, true);
                        finalStatus = findNextApproverIdAndStatusResult.getFinnalStatus();
                        finalJudgmentUser = findNextApproverIdAndStatusResult.getApplyStatus();
                        if (!StringTool.isNullOrEmpty(findNextApproverIdAndStatusResult.getApproverId())) {
                            approverId = findNextApproverIdAndStatusResult.getApproverId();
                        }
                        if (!StringTool.isNullOrEmpty(findNextApproverIdAndStatusResult.getReceiverId())) {
                            receiverId = findNextApproverIdAndStatusResult.getReceiverId();
                        }
                        if (findNextApproverIdAndStatusResult.getApplyStatus() == 1) {
                            //不是最后一个审批人存在下一个审批人
                            //查询审核中的审批日志
                            List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectApproveUser(applyId, ApplyLogAction.Approval.getValue());
                            //有日志数据情况下修改操作无日志数据情况下插入操作
                            if (!CollectionUtils.isEmpty(applyOrderLogList)) {
                                applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Approve.getValue(), applyOrderLogList.get(0).getId(), receiverId, approveModel.getComment(), new Date(), approveModel.getPrice());
                                List<ApplyOrderLog> applyOrderNextUserLogList = applyOrderLogExtMapper.selectNextApproveUser(applyId, ApplyLogAction.Unknown.getValue());
                                if (!CollectionUtils.isEmpty(applyOrderNextUserLogList)) {
                                    for (ApplyOrderLog applyOrderLog : applyOrderNextUserLogList) {
                                        String sponsorId = applyOrderLog.getSponsorId();
                                        EmployeeContract employee = iBaseOrganizationService.getEmployee(sponsorId, companyId);
                                        if (employee == null) {
                                            applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Skip.getValue(), applyOrderLog.getId(), "", CoreLanguage.Common_Value_ApprovalPersonDelete.getMessage(), null, approveModel.getPrice());
                                        } else {
                                            //保存下一个审批人为审批中状态
                                            applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Approval.getValue(), applyOrderLog.getId(), "", null, null, approveModel.getPrice());
                                            logId = applyOrderLog.getId();
                                            break;
                                        }
                                    }
                                }
                                setApproverAndStatus(applyId, approverId, finalStatus, now, logId, approveModel);
                            } else {
                                logId = writeLog(applyId, now, ip, userId, receiverId, approveModel.getComment(), action);
                                setApproverAndStatus(applyId, approverId, finalStatus, now, logId, approveModel);
                            }
                        } else if (findNextApproverIdAndStatusResult.getApplyStatus() == 2) {
                            //最后一个审批人
                            logId = sloveApplyLog(applyId, receiverId, approverId, finalStatus, now, ip, userId, action, approveModel);
                        } else {
                            //后面的审批人都不存在直接跳到授权负责人
                            List<ApplyOrderLog> applyOrderApprovalLogList = applyOrderLogExtMapper.selectApproveUser(applyId, ApplyLogAction.Approval.getValue());
                            applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Approve.getValue(), applyOrderApprovalLogList.get(0).getId(), receiverId, approveModel.getComment(), new Date(), approveModel.getPrice());

                            List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectApproveUser(applyId, ApplyLogAction.Unknown.getValue());
                            if (!CollectionUtils.isEmpty(applyOrderLogList)) {
                                for (ApplyOrderLog applyOrderLog : applyOrderLogList) {
                                    applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Skip.getValue(), applyOrderLog.getId(), "", CoreLanguage.Common_Value_ApprovalPersonDelete.getMessage(), null, approveModel.getPrice());
                                }
                            } else {
                                if (skippedApprverIds != null && skippedApprverIds.size() > 0) {
                                    for (String skippedApproverId : skippedApprverIds) {
                                        writeLog(applyId, now, ip, skippedApproverId, skippedApproverId, CoreLanguage.Common_Value_ApprovalPersonDelete.getMessage(), ApplyLogAction.Skip);
                                    }
                                }
                            }
                            int sort = 0;
                            if (!CollectionUtils.isEmpty(applyOrderLogList)) {
                                sort = applyOrderLogList.get(applyOrderLogList.size() - 1).getSort() + 100;
                            }
                            ApplyOrderLog logApprove = new ApplyOrderLog();
                            logApprove.setApplyOrderId(applyId);
                            logApprove.setIp(ip);
                            logApprove.setSponsorId(approverId);
                            logApprove.setReceiverId("");
                            logApprove.setCheckReason(null);
                            logApprove.setAction(ApplyLogAction.Approval.getValue());
                            logApprove.setSort(sort);
                            logApprove.setRootApplyOrderId(applyId);
                            applyOrderLogMapper.insert(logApprove);
                            logId = logApprove.getId();
                            setApproverAndStatus(applyId, approverId, finalStatus, now, logId, approveModel);
                        }
                    } catch (SaasException ex) {
                        logger.info("审批异常：" + ex.getResponseCode());
                        throw new SaasException(GlobalResponseCode.PayOrderCancleOrderSystemError);
                    }
                }
            }
        } else {
            finalStatus = ApplyStatus.Approved;
            receiverId = apply.getEmployeeId();
            approverId = userId;
            //处理审批日志和审批单的状态
            logId = sloveApplyLog(applyId, receiverId, approverId, finalStatus, now, ip, userId, action, approveModel);
        }
        //最后一个审批节点
        if (apply.getFlowType() == CompanyApplyType.Elastic.getValue() || (finalJudgmentUser != null && finalJudgmentUser == 2)) {
            apply.setRealPrice(approveModel.getReal_price());
            sloveRefundChange(apply);
            // 同步日志
            applyV2Service.syncLogOnFinishProcess(applyId, companyId);
        }
        setApplyApproverAndPushMessage(apply, userId, ip, finalStatus, receiverId, approverId, approveModel.getComment(), action, skippedApprverIds, logId);
        //push 抄送人
        pushCCMessage(apply, apply.getEmployeeId(), action, finalStatus, null, null);
        return GlobalResponseCode.Success;

    }

    /**
     * @param applyOrder
     */
    private void sloveRefundChange(ApplyOrder applyOrder) {
        Integer applyOrderType = applyOrder.getApplyOrderType();
        Integer subType = applyOrder.getSubType();
        Integer type = applyOrder.getType();
        List<ApplyTripInfo> applyTripInfos = applyTripInfoExtMapper.queryAllTripListByApplyOrderId(applyOrder.getId());
        String tripContent = applyTripInfos.get(0).getTripContent();
        Map<String, Object> orderDataMap = JSON.parseObject(tripContent, Map.class);
        Map<String, Object> orderInfo = (Map<String, Object>) orderDataMap.get("order_info");
        try {
            if (applyOrderType == SaasFlowConstant.SETTING_TYPE_REFUND_CHANGE && type == ApplyType.Air.getValue()) {
                if (subType == ApplySubType.Refund.getValue()) {
                    Map<String, String> header = Maps.newHashMap();
                    header.put("Content-Type", "application/json;charset=utf-8");
                    logger.info("国内机票退订操作内容:" + JSON.toJSON(orderInfo));
                    String airData = HttpTool.post(URL_GET_AIR_REFUNG_ORDER_INFO, orderInfo, header);
                    logger.info("国内机票退订操作结果:" + airData);
                } else {
                    if (ObjUtils.isNotEmpty(applyOrder.getRealPrice())){
                        orderInfo.put("real_price",applyOrder.getRealPrice());
                    }
                    Map<String, String> header = Maps.newHashMap();
                    header.put("Content-Type", "application/json;charset=utf-8");
                    logger.info("国内机票改期操作内容:" + JSON.toJSON(orderInfo));
                    String airData = HttpTool.post(URL_GET_AIR_CHANGE_ORDER_INFO, orderInfo, header);
                    logger.info("国内机票改期操作结果:" + airData);
                }
            }
        } catch (Exception ex) {
            logger.info("退改订单异常信息:" + ex.getMessage());
        }
        if (applyOrderType == SaasFlowConstant.SETTING_TYPE_REFUND_CHANGE && type == ApplyType.Train.getValue()) {
            if (subType == ApplySubType.Refund.getValue()) {
                Map<String, String> header = Maps.newHashMap();
                header.put("Content-Type", "application/json;charset=utf-8");
                orderInfo.put("re_apply_id", applyOrder.getId());
                logger.info("火车退订操作内容:" + JSON.toJSON(orderInfo));
                String trainData = null;
                try {
                    trainData = HttpTool.post(URL_GET_TRAIN_REFUNG_ORDER_INFO, orderInfo, header);
                } catch (Exception e) {
                    logger.info("火车退订异常信息:" + e.getMessage());
                }
                logger.info("火车退订操作结果:" + trainData);
                Map<String, Object> jo = JSONObject.parseObject(trainData, HashMap.class);
                Integer code = ObjUtils.toInteger(jo.get("code"));
                if (code != null && code.intValue() != 0) {
                    String msg = ObjUtils.toString(jo.get("msg"));
                    throw new SaasException(GlobalResponseCode.ApplyOrderIsError, msg);
                }
            } else {
                Map<String, String> header = Maps.newHashMap();
                header.put("Content-Type", "application/json;charset=utf-8");
                orderInfo.put("re_apply_id", applyOrder.getId());
                logger.info("火车改签操作内容:" + JSON.toJSON(orderInfo));
                String trainData = null;
                try {
                    trainData = HttpTool.post(URL_GET_TRAIN_CHANGE_ORDER_INFO, orderInfo, header);
                } catch (Exception e) {
                    logger.info("火车改签异常信息:" + e.getMessage());
                }
                logger.info("火车改签操作结果:" + trainData);
                Map<String, Object> jo = JSONObject.parseObject(trainData, HashMap.class);
                Integer code = ObjUtils.toInteger(jo.get("code"));
                if (code != null && code.intValue() != 0) {
                    String msg = ObjUtils.toString(jo.get("msg"));
                    throw new SaasException(GlobalResponseCode.ApplyOrderIsError, msg);
                }
            }
        }
        try {
            if (applyOrderType == SaasFlowConstant.SETTING_TYPE_REFUND_CHANGE && type == ApplyType.Hotel.getValue()) {
                if (subType == ApplySubType.Refund.getValue()) {
                    Map<String, String> header = Maps.newHashMap();
                    header.put("Content-Type", "application/json;charset=utf-8");
                    orderInfo.put("refund_apply_id", applyOrder.getId());
                    orderInfo.put("employee_id", applyOrder.getEmployeeId());
                    orderInfo.put("company_id", applyOrder.getCompanyId());
                    logger.info("酒店退订操作内容:" + JSON.toJSON(orderInfo));
                    String hotelData = HttpTool.post(URL_GET_HOTEL_REFUNG_ORDER_INFO, orderInfo, header);
                    logger.info("酒店退订操作结果:" + hotelData);
                }
            }
        } catch (Exception ex) {
            logger.info("退改订单异常信息:" + ex.getMessage());
        }
    }

    /**
     * 审批、拒绝、转交情况下处理审批日志的状态和修改审批单的状态
     *
     * @param applyId
     * @param receiverId
     * @param approverId
     * @param finalStatus
     * @param now
     * @param ip
     * @param userId
     * @param action
     * @param approveModel
     */
    //@Transactional(value = "saas")
    public Integer sloveApplyLog(String applyId, String receiverId, String approverId, ApplyStatus finalStatus, Date now, String ip, String userId, ApplyLogAction action, ApplyApproveContract approveModel) {
        //查询审核中的审批日志
        List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectApproveUser(applyId, ApplyLogAction.Approval.getValue());
        //有审批日志情况下进行修改操作,无审批日志情况下插入操作
        if (!CollectionUtils.isEmpty(applyOrderLogList)) {
            applyOrderLogExtMapper.updateApplyOrderActionById(action.getValue(), applyOrderLogList.get(0).getId(), receiverId, approveModel.getComment(), new Date(), null);
            setApproverAndStatus(applyId, approverId, finalStatus, now, applyOrderLogList.get(0).getId(), approveModel);
            return applyOrderLogList.get(0).getId();
        } else {
            Integer logId = writeLog(applyId, now, ip, userId, receiverId, approveModel.getComment(), action);
            setApproverAndStatus(applyId, approverId, finalStatus, now, logId, approveModel);
            return logId;
        }
    }

   // @Transactional(value = "saas")
    private int setApproverAndStatus(String applyId, String approverId, ApplyStatus status, Date time, Integer logId, ApplyApproveContract approveModel) {
        if (approveModel == null) {
            approveModel.setComment("");
            approveModel.setPrice(null);
        }
        int count = applyOrderExtMapper.updateApproverIdAndStatus(applyId, approverId, status.getValue(), time, logId, approveModel.getComment(), approveModel.getReal_price());
        return count;
    }

    //@Transactional(value = "saas")
    private Integer writeLog(String id, Date createTime, String ip, String sponsorId, String receiverId, String checkReason, ApplyLogAction action) {
        //插入日志
        ApplyOrderLog log = new ApplyOrderLog();
        log.setApplyOrderId(id);
        log.setCreateTime(createTime);
        log.setIp(ip);
        log.setSponsorId(sponsorId);
        if (receiverId == null) {
            receiverId = "";
        }
        log.setReceiverId(receiverId);
        log.setCheckReason(checkReason);
        log.setAction(action.getValue());
        log.setPrice(null);
        log.setRootApplyOrderId(id);
        applyOrderLogMapper.insert(log);
        return log.getId();
    }

    private FindNextApproverIdAndStatusResult findNextApproverIdAndStatusInFlow(ApplyOrder apply, List<ApplyFlowUserItem> applyFlowUserItems, List<String> skippedApprverIds, boolean couldFinalApprove) throws SaasException {
        String approverId = null;
        String receiverId = null;
        ApplyStatus finalStatus;
        Integer applyStatus = null;
        int remainIndex = applyFlowUserItems.size();
        for (int i = 0; i < applyFlowUserItems.size(); i++) {
            if (ValueTool.areEqual(applyFlowUserItems.get(i).getStatus(), ApplyFlowUserItemStatus.Waiting.getValue())) {
                remainIndex = i;
                break;
            }
        }
        //找下一个审批人
        boolean findNextApprover = false;
        // 是否是最后一名审批人
        boolean finnalApprover = true;
        for (int i = remainIndex; i < applyFlowUserItems.size(); i++) {
            ApplyFlowUserItem item = applyFlowUserItems.get(i);
            if (ValueTool.areEqual(item.getStatus(), ApplyFlowUserItemStatus.Waiting.getValue())) {
                finnalApprover = false;
                if (userService.isUserInCompany(item.getUserId(), apply.getCompanyId())) {
                    findNextApprover = true;
                    receiverId = item.getUserId();
                    approverId = receiverId;
                    applyStatus = 1;
                    applyFlowService.setApplyFlowUserItemStatus(item.getId(), ApplyFlowUserItemStatus.PendingAudit);
                    break;
                } else {
                    skippedApprverIds.add(item.getUserId());
                    applyFlowService.setApplyFlowUserItemStatus(item.getId(), ApplyFlowUserItemStatus.Skip);
                }
            }
        }
        if (finnalApprover && couldFinalApprove) {
            //当前审批人已经是最后一个审批人
            finalStatus = ApplyStatus.Approved;
            receiverId = apply.getEmployeeId();
            applyStatus = 2;
        } else {
            finalStatus = ApplyStatus.PendingAudit;
            if (!findNextApprover) {
                //后面的审批人都已不存在，直接跳到授权负责人
                List<String> superAdminEmployeeIds = customRoleService.getEmployeeIdByRoleId(Integer.toString(UserRole.CompanySuperAdmin.getValue()), apply.getCompanyId());
                if (superAdminEmployeeIds == null || superAdminEmployeeIds.size() == 0) {
                    throw new SaasException(GlobalResponseCode.ApplyCanotApproveBecauseHasNoSuperAdmin);
                } else {
                    receiverId = superAdminEmployeeIds.get(0);
                    approverId = receiverId;
                    applyStatus = 3;
                }
            }
        }
        FindNextApproverIdAndStatusResult result = new FindNextApproverIdAndStatusResult();
        result.setApproverId(approverId);
        result.setReceiverId(receiverId);
        result.setFinnalStatus(finalStatus);
        result.setApplyStatus(applyStatus);
        return result;
    }

    @Override
    //@Transactional(value = "saas")
    public void setApplyApproverAndPushMessage(ApplyOrder apply, String userId, String ip, ApplyStatus finalStatus, String receiverId, String approverId, String comment, ApplyLogAction action, List<String> skippedApprverIds, Integer logId) {
        Date now = new Date();
        String applyId = apply.getId();
        if (!StringTool.isNullOrEmpty(approverId) && !approverId.equals(apply.getApproverId())) {
            insertApproverMap(applyId, approverId, now);
        }
        List<ApplyTripInfo> applyTripInfos = applyTripInfoExtMapper.selectListByApplyOrderId(apply.getId());
        postMessage(apply, apply.getEmployeeId(), receiverId, action, finalStatus, logId, null, null, ApplyTripInfoContract.FromCenterModel(applyTripInfos.get(0)), comment);
    }

    /**
     * 驳回
     *
     * @param approveModel
     * @param userId
     * @param ip
     * @return
     */
    @Override
    //@Transactional(value = "saas")
    public GlobalResponseCode repulse(ApplyApproveContract approveModel, String userId, String companyId, String ip, String clientVersion, String source) throws SaasException {
        FlowCheckUtil.check(companyId, clientVersion, source);
        if (approveModel == null) {
            return GlobalResponseCode.ApplyIsNull;
        }
        if (StringTool.isNullOrEmpty(approveModel.getApply_id())) {
            return GlobalResponseCode.ApplyIdInvalid;
        }
        if (approveModel.getComment() != null && approveModel.getComment().length() > 200) {
            return GlobalResponseCode.ApplyCheckReasonInvalid;
        }

        if (approveModel.getComment() == null || approveModel.getComment().length() == 0) {
            //驳回审批单必须要填写理由
            return GlobalResponseCode.ApplyRefuseMustWriteReason;
        }
        String applyId = approveModel.getApply_id();
        ApplyOrder apply = applyOrderMapper.selectByPrimaryKey(approveModel.getApply_id());
        if (apply == null) {
            return GlobalResponseCode.NotFound;
        }
        if (apply.getState() != null && apply.getState().intValue() == ApplyStatus.Overtime.getValue()) {
            //事中审批已超时
            return GlobalResponseCode.CenterApplyOverTime;
        }
        if (apply.getState() != null && apply.getState().intValue() == ApplyStatus.Backout.getValue()) {
            //事中审批已撤销
            return GlobalResponseCode.CenterApplyCancleOrder;
        }
        if (!apply.getCompanyId().equals(companyId)) {
            //申请单和当前人不在一个公司
            return GlobalResponseCode.ApplyCompanyCannotMatch;
        }
        if (apply.getState() != ApplyStatus.PendingAudit.getValue()) {
            //该单不是待审核,不能审批
            return GlobalResponseCode.ApplyStatusNotPendingAudit;
        }
        if (!apply.getApproverId().equals(userId)) {
            //不是当前人可以处理的审批单
            return GlobalResponseCode.NoAuth;
        }
        ApplyStatus finalStatus;
        ApplyLogAction action;
        String receiverId; //下一个审批人/通知人
        String approverId; //审批人
        Integer logId = null;
        List<String> skippedApprverIds = new ArrayList<>();//固定审批流跳过的审批人
        Date now = new Date();
        //拒绝
        List<ApplyFlowUserItem> applyFlowUserItems = applyFlowService.getFlowItemsByApplyId(applyId);
        for (ApplyFlowUserItem applyFlowUserItem : applyFlowUserItems) {
            if (ValueTool.areEqual(applyFlowUserItem.getStatus(), ApplyFlowUserItemStatus.PendingAudit.getValue())
                    && applyFlowUserItem.getUserId().equals(userId)) {
                applyFlowService.setApplyFlowUserItemStatus(applyFlowUserItem.getId(), ApplyFlowUserItemStatus.Refuse);
            }
        }
        finalStatus = ApplyStatus.Return;
        action = ApplyLogAction.Refuse;
        receiverId = apply.getEmployeeId();
        approverId = userId;
        //处理审批日志和审批单的状态
        logId = sloveApplyLog(applyId, receiverId, approverId, finalStatus, now, ip, userId, action, approveModel);
        // 同步日志
        applyV2Service.syncLogOnFinishProcess(applyId, companyId);
        setApplyApproverAndPushMessage(apply, userId, ip, finalStatus, receiverId, approverId, approveModel.getComment(), action, skippedApprverIds, logId);
        return GlobalResponseCode.Success;

    }

    /**
     * 撤销
     *
     * @param userId
     * @param companyId
     * @param ip
     * @return
     */
    @Override
    //@Transactional(value = "fenbeitong")
    public GlobalResponseCode revoke(String userId, String companyId, ApplyRevokeContract data, String ip, String clientVersion, String source) {
        FlowCheckUtil.check(companyId, clientVersion, source);
        if (data == null || StringUtils.isBlank(data.getId())) {
            return GlobalResponseCode.ParameterError;
        }
        ApplyOrder order = getApplyOrderModelByIdAndUserIdAndCompanyId(data.getId(), userId, companyId, null);
        if (order == null) {
            return GlobalResponseCode.NotFound;
        }
        if (order.getState() != ApplyStatus.PendingAudit.getValue()) {
            return GlobalResponseCode.ApplyStatusNotPendingAudit;
        }
        Date now = new Date();
        ApplyLogAction action = ApplyLogAction.Revoke;
        int logSort = 0;
        List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectListByCreateTimeDesc(data.getId());
        if (!CollectionUtils.isEmpty(applyOrderLogList)) {
            Integer sort = applyOrderLogList.get(0).getSort();
            if (sort != null) {
                logSort = sort + 100;
            }
        }
        //插入日志
        ApplyOrderLog log = new ApplyOrderLog();
        log.setApplyOrderId(data.getId());
        log.setRootApplyOrderId(data.getId());
        log.setCreateTime(now);
        log.setIp(ip);
        log.setSponsorId(userId);
        log.setReceiverId(userId);
        log.setCheckReason(null);
        log.setAction(action.getValue());
        log.setSort(logSort);
        applyOrderLogMapper.insertSelective(log);
        applyOrderExtMapper.setCenterStatus(data.getId(), ApplyStatus.Backout.getValue(), now, log.getId(), 6);
        // 同步日志
        applyV2Service.syncLogOnFinishProcess(data.getId(), companyId);
        return GlobalResponseCode.Success;
    }

    /**
     * 通过审批单Web
     *
     * @param approveModel
     * @return
     */
    @Override
    //@Transactional(value = "saas")
    public GlobalResponseCode approveWeb(ApplyApproveContract approveModel) throws SaasException {
        if (approveModel == null) {
            throw new SaasException(GlobalResponseCode.ApplyIsNull);
        }
        if (StringTool.isNullOrEmpty(approveModel.getApply_id())) {
            throw new SaasException(GlobalResponseCode.ApplyIdInvalid);
        }
        if (approveModel.getComment() != null && approveModel.getComment().length() > 200) {
            throw new SaasException(GlobalResponseCode.ApplyCheckReasonInvalid);
        }
        String applyId = null;
        String userId = null;
        try {
            applyId = EncryptUtil.decryptAES(URLDecoder.decode(approveModel.getApply_id()), HostPropertyConfigTool.SIGN_KEY);
            userId = EncryptUtil.decryptAES(URLDecoder.decode(approveModel.getUser_id()), HostPropertyConfigTool.SIGN_KEY);
            logger.info("H5审批的审批单id:" + applyId + " H5审批审批人的用户id:" + userId);
            if (StringUtils.isBlank(applyId)) {
                throw new SaasException(GlobalResponseCode.ApplyIdInvalid);
            }
        } catch (Exception e) {
            logger.error("解析applyId报错" + approveModel.getApply_id() + e.getMessage());
        }
        ApplyOrder apply = applyOrderMapper.selectByPrimaryKey(applyId);
        if (apply == null) {
            throw new SaasException(GlobalResponseCode.ApplyCenterNotFound);
        }
        if (apply.getState() == ApplyStatus.Overtime.getValue()) {
            //事中审批已超时
            throw new SaasException(GlobalResponseCode.CenterApplyOverTime);
        }
        if (apply.getState() == ApplyStatus.Backout.getValue()) {
            //事中审批已撤销
            throw new SaasException(GlobalResponseCode.CenterApplyCancleOrder);
        }
        if (apply.getState() != ApplyStatus.PendingAudit.getValue()) {
            //该单不是待审核,不能审批
            throw new SaasException(GlobalResponseCode.ApplyStatusNotPendingAudit);
        }
        if (!apply.getApproverId().equals(userId)) {
            //不是当前人可以处理的审批单
            throw new SaasException(GlobalResponseCode.NoAuth);
        }
        ApplyStatus finalStatus;
        ApplyLogAction action;
        String receiverId; //下一个审批人/通知人
        String approverId; //审批人
        Integer logId = null;
        List<String> skippedApprverIds = new ArrayList<>();//固定审批流跳过的审批人
        Date now = new Date();
        action = ApplyLogAction.Approve;
        approverId = userId;
        receiverId = null;
        Integer finalJudgmentUser = null;
        boolean isFlow = (ValueTool.areEqual(apply.getFlowType(), CompanyApplyType.Flow.getValue()) || ValueTool.areEqual(apply.getFlowType(), CompanyApplyType.CONDITIONAL.getValue()));
        if (isFlow) {
            //固定审批流和分条件审批流
            List<ApplyFlowUserItem> applyFlowUserItems = applyFlowService.getFlowItemsByApplyId(applyId);
            if (applyFlowUserItems == null ||
                    applyFlowUserItems.size() == 0 ||
                    applyFlowUserItems.stream().allMatch(m ->
                            !ValueTool.areEqual(m.getStatus(), ApplyFlowUserItemStatus.PendingAudit.getValue())
                                    && !ValueTool.areEqual(m.getStatus(), ApplyFlowUserItemStatus.Transfered.getValue())
                    )) {
                //没有待审核记录，也没有转交记录，预示审批流已经审核通过
                finalStatus = ApplyStatus.Approved;
                receiverId = apply.getEmployeeId();
                //处理审批日志和审批单的状态
                logId = sloveApplyLog(applyId, receiverId, approverId, finalStatus, now, "", userId, action, approveModel);
                finalJudgmentUser = 2;
            } else {
                int pendingAuditItemIndex = -1;
                for (int i = 0; i < applyFlowUserItems.size(); i++) {
                    if (ValueTool.areEqual(applyFlowUserItems.get(i).getStatus(), ApplyFlowUserItemStatus.PendingAudit.getValue())) {
                        pendingAuditItemIndex = i;
                        break;
                    }
                }
                ApplyFlowUserItem pendingAuditItem = null;
                if (pendingAuditItemIndex >= 0) {
                    pendingAuditItem = applyFlowUserItems.get(pendingAuditItemIndex);
                }
                if (pendingAuditItem != null && !pendingAuditItem.getUserId().equals(userId)) {
                    logger.info("当前待审批的item不是userid，userId：" + userId + " itemId:" + pendingAuditItem.getUserId());
                    throw new SaasException(GlobalResponseCode.NoAuth);
                } else {
                    if (pendingAuditItem != null) {
                        //将item status设置为已审核
                        applyFlowService.setApplyFlowUserItemStatus(pendingAuditItem.getId(), ApplyFlowUserItemStatus.Approved);
                    }
                    try {
                        FindNextApproverIdAndStatusResult findNextApproverIdAndStatusResult = findNextApproverIdAndStatusInFlow(apply, applyFlowUserItems, skippedApprverIds, true);
                        finalJudgmentUser = findNextApproverIdAndStatusResult.getApplyStatus();
                        finalStatus = findNextApproverIdAndStatusResult.getFinnalStatus();
                        if (!StringTool.isNullOrEmpty(findNextApproverIdAndStatusResult.getApproverId())) {
                            approverId = findNextApproverIdAndStatusResult.getApproverId();
                        }
                        if (!StringTool.isNullOrEmpty(findNextApproverIdAndStatusResult.getReceiverId())) {
                            receiverId = findNextApproverIdAndStatusResult.getReceiverId();
                        }
                        if (findNextApproverIdAndStatusResult.getApplyStatus() == 1) {
                            //不是最后一个审批人存在下一个审批人
                            //查询审核中的审批日志
                            List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectApproveUser(applyId, ApplyLogAction.Approval.getValue());
                            //有日志数据情况下修改操作无日志数据情况下插入操作
                            if (!CollectionUtils.isEmpty(applyOrderLogList)) {
                                applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Approve.getValue(), applyOrderLogList.get(0).getId(), receiverId, approveModel.getComment(), new Date(), approveModel.getPrice());
                                setApproverAndStatus(applyId, approverId, finalStatus, now, applyOrderLogList.get(0).getId(), approveModel);
                                List<ApplyOrderLog> applyOrderNextUserLogList = applyOrderLogExtMapper.selectNextApproveUser(applyId, ApplyLogAction.Unknown.getValue());
                                if (!CollectionUtils.isEmpty(applyOrderNextUserLogList)) {
                                    //保存下一个审批人为审批中状态
                                    applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Approval.getValue(), applyOrderNextUserLogList.get(0).getId(), "", null, null, approveModel.getPrice());
                                    logId = applyOrderNextUserLogList.get(0).getId();
                                }
                            } else {
                                logId = writeLog(applyId, now, "", userId, receiverId, approveModel.getComment(), action);
                                setApproverAndStatus(applyId, approverId, finalStatus, now, logId, approveModel);
                            }
                        } else if (findNextApproverIdAndStatusResult.getApplyStatus() == 2) {
                            //最后一个审批人
                            logId = sloveApplyLog(applyId, receiverId, approverId, finalStatus, now, "", userId, action, approveModel);
                        } else {
                            //后面的审批人都不存在直接跳到授权负责人
                            List<ApplyOrderLog> applyOrderApprovalLogList = applyOrderLogExtMapper.selectApproveUser(applyId, ApplyLogAction.Approval.getValue());
                            applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Approve.getValue(), applyOrderApprovalLogList.get(0).getId(), receiverId, approveModel.getComment(), new Date(), approveModel.getPrice());

                            List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectApproveUser(applyId, ApplyLogAction.Unknown.getValue());
                            if (!CollectionUtils.isEmpty(applyOrderLogList)) {
                                for (ApplyOrderLog applyOrderLog : applyOrderLogList) {
                                    applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Skip.getValue(), applyOrderLog.getId(), "", CoreLanguage.Common_Value_ApprovalPersonDelete.getMessage(), null, approveModel.getPrice());
                                }
                            } else {
                                if (skippedApprverIds != null && skippedApprverIds.size() > 0) {
                                    for (String skippedApproverId : skippedApprverIds) {
                                        writeLog(applyId, now, "", skippedApproverId, skippedApproverId, CoreLanguage.Common_Value_ApprovalPersonDelete.getMessage(), ApplyLogAction.Skip);
                                    }
                                }
                            }
                            int sort = 0;
                            if (!CollectionUtils.isEmpty(applyOrderLogList)) {
                                sort = applyOrderLogList.get(applyOrderLogList.size() - 1).getSort() + 100;
                            }
                            ApplyOrderLog logApprove = new ApplyOrderLog();
                            logApprove.setApplyOrderId(applyId);
                            logApprove.setIp("");
                            logApprove.setSponsorId(approverId);
                            logApprove.setReceiverId("");
                            logApprove.setCheckReason(null);
                            logApprove.setAction(ApplyLogAction.Approval.getValue());
                            logApprove.setSort(sort);
                            applyOrderLogMapper.insert(logApprove);
                            logId = logApprove.getId();
                            setApproverAndStatus(applyId, approverId, finalStatus, now, logId, approveModel);
                        }
                    } catch (SaasException ex) {
                        logger.info("审批异常：" + ex.getResponseCode());
                        throw new SaasException(GlobalResponseCode.PayOrderCancleOrderSystemError);
                    }
                }
            }
        } else {
            finalStatus = ApplyStatus.Approved;
            receiverId = apply.getEmployeeId();
            approverId = userId;
            //处理审批日志和审批单的状态
            logId = sloveApplyLog(applyId, receiverId, approverId, finalStatus, now, "", userId, action, approveModel);
        }
        //最后一个审批节点
        if (apply.getFlowType() == CompanyApplyType.Elastic.getValue() || (finalJudgmentUser != null && finalJudgmentUser == 2)) {
            apply.setRealPrice(approveModel.getReal_price());
            sloveRefundChange(apply);
            // 同步日志
            applyV2Service.syncLogOnFinishProcess(applyId, apply.getCompanyId());
        }
        setApplyApproverAndPushMessage(apply, approverId, "", finalStatus, receiverId, approverId, approveModel.getComment(), action, skippedApprverIds, logId);
        //push 抄送人
        pushCCMessage(apply, apply.getEmployeeId(), action, finalStatus, null, null);
        return GlobalResponseCode.Success;
    }

    /**
     * 驳回Web
     *
     * @param approveModel
     * @return
     */
    @Override
    //@Transactional
    public GlobalResponseCode repulseWeb(ApplyApproveContract approveModel) {
        if (approveModel == null) {
            return GlobalResponseCode.ApplyIsNull;
        }
        if (StringTool.isNullOrEmpty(approveModel.getApply_id())) {
            return GlobalResponseCode.ApplyIdInvalid;
        }
        if (approveModel.getComment() != null && approveModel.getComment().length() > 200) {
            return GlobalResponseCode.ApplyCheckReasonInvalid;
        }

        if (approveModel.getComment() == null || approveModel.getComment().length() == 0) {
            //驳回审批单必须要填写理由
            return GlobalResponseCode.ApplyRefuseMustWriteReason;
        }
        String applyId = null;
        String userId = null;
        try {
            applyId = EncryptUtil.decryptAES(URLDecoder.decode(approveModel.getApply_id()), HostPropertyConfigTool.SIGN_KEY);
            userId = EncryptUtil.decryptAES(URLDecoder.decode(approveModel.getUser_id()), HostPropertyConfigTool.SIGN_KEY);
            logger.info("H5审批驳回的审批单id:" + applyId + " H5审批驳回审批人的用户id:" + userId);
            if (StringUtils.isBlank(applyId)) {
                return GlobalResponseCode.ApplyIdInvalid;
            }
        } catch (Exception e) {
            logger.error("解析applyId报错" + approveModel.getApply_id() + e.getMessage());
        }
        ApplyOrder apply = applyOrderMapper.selectByPrimaryKey(applyId);
        if (apply == null) {
            return GlobalResponseCode.NotFoundData;
        }
        if (apply.getState() == ApplyStatus.Overtime.getValue()) {
            //事中审批已超时
            return GlobalResponseCode.CenterApplyOverTime;
        }
        if (apply.getState() == ApplyStatus.Backout.getValue()) {
            //事中审批已撤销
            return GlobalResponseCode.CenterApplyCancleOrder;
        }
        if (apply.getState() != ApplyStatus.PendingAudit.getValue()) {
            //该单不是待审核,不能审批
            return GlobalResponseCode.ApplyStatusNotPendingAudit;
        }
        if (!apply.getApproverId().equals(userId)) {
            //不是当前人可以处理的审批单
            return GlobalResponseCode.NoAuth;
        }
        ApplyStatus finalStatus;
        ApplyLogAction action;
        String receiverId; //下一个审批人/通知人
        String approverId; //审批人
        Integer logId = null;
        List<String> skippedApprverIds = new ArrayList<>();//固定审批流跳过的审批人
        Date now = new Date();
        //拒绝
        List<ApplyFlowUserItem> applyFlowUserItemList = applyFlowUserItemMapper.selectFlowItemsByApplyId(applyId);
        if (CollectionUtils.isNotEmpty(applyFlowUserItemList)) {
            userId = applyFlowUserItemList.get(0).getUserId();
        }
        for (ApplyFlowUserItem applyFlowUserItem : applyFlowUserItemList) {
            if (ValueTool.areEqual(applyFlowUserItem.getStatus(), ApplyFlowUserItemStatus.PendingAudit.getValue())) {
                applyFlowService.setApplyFlowUserItemStatus(applyFlowUserItem.getId(), ApplyFlowUserItemStatus.Refuse);
                userId = applyFlowUserItem.getUserId();
            }
        }
        finalStatus = ApplyStatus.Return;
        action = ApplyLogAction.Refuse;
        receiverId = apply.getEmployeeId();
        approverId = userId;
        //处理审批日志和审批单的状态
        logId = sloveApplyLog(applyId, receiverId, approverId, finalStatus, now, "", userId, action, approveModel);
        // 同步日志
        applyV2Service.syncLogOnFinishProcess(applyId, apply.getCompanyId());
        setApplyApproverAndPushMessage(apply, userId, "", finalStatus, receiverId, approverId, approveModel.getComment(), action, skippedApprverIds, logId);
        return GlobalResponseCode.Success;
    }

    /**
     * 转发审批单
     *
     * @param approveModel
     * @param userId
     * @param ip
     * @return
     */
    @Override
   // @Transactional(value = "saas")
    public GlobalResponseCode forward(ApplyApproveContract approveModel, String userId, String companyId, String ip, String clientVersion, String source) throws SaasException {
        FlowCheckUtil.check(companyId, clientVersion, source);
        if (approveModel == null) {
            return GlobalResponseCode.ApplyIsNull;
        }
        if (StringTool.isNullOrEmpty(approveModel.getApply_id())) {
            return GlobalResponseCode.ApplyIdInvalid;
        }
        if (approveModel.getComment() != null && approveModel.getComment().length() > 200) {
            return GlobalResponseCode.ApplyCheckReasonInvalid;
        }
        int status = approveModel.getStatus();
        String nextApproverId = approveModel.getApprover_id();
        if (StringTool.isNullOrEmpty(nextApproverId)) {
            //转交需要一个承接人
            return GlobalResponseCode.ApplyApproveIdInvalid;
        } else if (nextApproverId.equals(userId)) {
            return GlobalResponseCode.ApplyTransferNotSelf;
        }
        String applyId = approveModel.getApply_id();
        ApplyOrder apply = applyOrderMapper.selectByPrimaryKey(approveModel.getApply_id());
        if (apply == null) {
            return GlobalResponseCode.NotFound;
        }
        boolean isFlow = (ValueTool.areEqual(apply.getFlowType(), CompanyApplyType.Flow.getValue()) || ValueTool.areEqual(apply.getFlowType(), CompanyApplyType.CONDITIONAL.getValue()));
        if (!apply.getCompanyId().equals(companyId)) {
            //申请单和当前人不在一个公司
            return GlobalResponseCode.ApplyCompanyCannotMatch;
        }
        if (apply.getState() != ApplyStatus.PendingAudit.getValue()) {
            //该单不是待审核或者转交状态,不能审批
            return GlobalResponseCode.ApplyStatusNotPendingAudit;
        }
        if (apply.getEmployeeId().equals(approveModel.getApprover_id())) {
            return GlobalResponseCode.ApplyApproverCannotBeSelf;
        }
        Boolean exist = userService.isUserInCompany(approveModel.getApprover_id(), apply.getCompanyId());
        if (!exist) {
            return GlobalResponseCode.ApplyCompanyCannotMatch;
        }
        if (!apply.getApproverId().equals(userId)) {
            //不是当前人可以处理的审批单
            return GlobalResponseCode.NoAuth;
        }
        ApplyStatus finalStatus;
        ApplyLogAction action;
        String receiverId; //下一个审批人/通知人
        String approverId; //审批人
        Integer logId = null;
        List<String> skippedApprverIds = new ArrayList<>();//固定审批流跳过的审批人
        Date now = new Date();
        //转交
        if (isFlow) {
            List<ApplyFlowUserItem> applyFlowUserItems = applyFlowService.getFlowItemsByApplyId(applyId);
            if (applyFlowUserItems != null && applyFlowUserItems.size() > 0) {
                Optional<ApplyFlowUserItem> optionalApplyFlowUserItem = applyFlowUserItems.stream().filter(m -> m.getUserId().equals(userId) &&
                        ValueTool.areEqual(m.getStatus(), ApplyFlowUserItemStatus.PendingAudit.getValue())).findAny();
                if (optionalApplyFlowUserItem.isPresent()) {
                    ApplyFlowUserItem applyFlowUserItem = optionalApplyFlowUserItem.get();
                    //当前审批人将此单转交给另一人，将此item状态置为Transfered
                    applyFlowService.setApplyFlowUserItemStatus(applyFlowUserItem.getId(), ApplyFlowUserItemStatus.Transfered);
                }
            }
        }
        finalStatus = ApplyStatus.PendingAudit;
        action = ApplyLogAction.Forward;
        receiverId = approveModel.getApprover_id();
        approverId = receiverId;

        //查询审核中的审批日志
        List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectApproveUser(applyId, ApplyLogAction.Approval.getValue());
        //有日志数据情况下修改操作无日志数据情况下插入操作
        if (!CollectionUtils.isEmpty(applyOrderLogList)) {
            applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Forward.getValue(), applyOrderLogList.get(0).getId(), receiverId, approveModel.getComment(), new Date(), approveModel.getPrice());
            Integer sort = applyOrderLogList.get(0).getSort() + 1;
            ApplyOrderLog log = new ApplyOrderLog();
            log.setApplyOrderId(applyId);
            log.setIp(ip);
            log.setSponsorId(receiverId);
            log.setReceiverId("");
            log.setCheckReason(null);
            log.setAction(ApplyLogAction.Approval.getValue());
            log.setSort(sort);
            log.setRootApplyOrderId(applyId);
            applyOrderLogMapper.insertSelective(log);
            logId = log.getId();
            setApproverAndStatus(applyId, approverId, finalStatus, now, log.getId(), approveModel);
        } else {
            writeLog(applyId, now, ip, userId, receiverId, approveModel.getComment(), action);
            ApplyOrderLog logApprove = new ApplyOrderLog();
            logApprove.setApplyOrderId(applyId);
            logApprove.setIp(ip);
            logApprove.setSponsorId(receiverId);
            logApprove.setReceiverId("");
            logApprove.setCheckReason(null);
            logApprove.setAction(ApplyLogAction.Approval.getValue());
            logApprove.setSort(0);
            logApprove.setRootApplyOrderId(applyId);
            applyOrderLogMapper.insertSelective(logApprove);
            logId = logApprove.getId();
            setApproverAndStatus(applyId, approverId, finalStatus, now, logId, approveModel);
        }
        setApplyApproverAndPushMessage(apply, userId, ip, finalStatus, receiverId, approverId, approveModel.getComment(), action, skippedApprverIds, logId);
        return GlobalResponseCode.Success;
    }

    /**
     * 通过审批单id获取审批单详情
     *
     * @param applyId
     * @return
     * @throws SaasException
     */
    @Override
    public ApplyV2Contract getRefundChangeDetail(String applyId, String userId) throws SaasException {
        try {
            applyId = EncryptUtil.decryptAES(URLDecoder.decode(applyId), HostPropertyConfigTool.SIGN_KEY);
            userId = EncryptUtil.decryptAES(URLDecoder.decode(userId), HostPropertyConfigTool.SIGN_KEY);
            logger.info("H5退改审批审批单详情的审批单id:" + applyId + " H5退改审批审批单详情的用户id:" + userId);
            if (StringUtils.isBlank(applyId) || StringUtils.isBlank(userId)) {
                return null;
            }
        } catch (Exception e) {
            logger.error("解析applyId报错" + applyId + e.getMessage());
        }
        ApplyOrder applyOrder = applyOrderMapper.selectByPrimaryKey(applyId);
        if (applyOrder == null) {
            return null;
        }
        ApplyOrderV2Contract apply = ApplyOrderV2Contract.FromModel(applyOrder);
        Integer applyOrderType = apply.getApply_order_type();
        Integer type = apply.getType();
        Integer subType = apply.getSub_type();
        if (apply.getState() == 2) {
            if (applyOrderType == SaasFlowConstant.SETTING_TYPE_REFUND_CHANGE && type == ApplyType.Air.getValue() && subType == ApplySubType.Refund.getValue()) {
                apply.setOvertime_desc(CoreLanguage.Common_Value_TravelBackFeeWarn.getMessage());
            }
            if (applyOrderType == SaasFlowConstant.SETTING_TYPE_REFUND_CHANGE && type == ApplyType.Air.getValue() && subType == ApplySubType.Change.getValue()) {
                apply.setOvertime_desc(CoreLanguage.Common_Value_TravelChangeFeeWarn.getMessage());
            }
            if (applyOrderType == SaasFlowConstant.SETTING_TYPE_REFUND_CHANGE && type == ApplyType.Train.getValue() && subType == ApplySubType.Refund.getValue()) {
                apply.setOvertime_desc(CoreLanguage.Common_Value_OnlineBackTicketFour.getMessage());
            }
            if (applyOrderType == SaasFlowConstant.SETTING_TYPE_REFUND_CHANGE && type == ApplyType.Train.getValue() && subType == ApplySubType.Change.getValue()) {
                apply.setOvertime_desc(CoreLanguage.Common_Value_OnlineChangeTicketFour.getMessage());
            }
            if (applyOrderType == SaasFlowConstant.SETTING_TYPE_REFUND_CHANGE && type == ApplyType.Hotel.getValue() && subType == ApplySubType.Refund.getValue()) {
                apply.setOvertime_desc(CoreLanguage.Common_Value_HotelRuleNotBack.getMessage());
            }
        }
        if (StringUtils.isNotBlank(apply.getExceed_buy_desc())) {
            String[] exceedBugDescs = apply.getExceed_buy_desc().split(";");
            List<String> exceedBuyDescList = Arrays.asList(exceedBugDescs);
            apply.setExceed_buy_desc_list(exceedBuyDescList);
        }
        List<String> skippedApprverIds = new ArrayList<>();//固定审批流跳过的审批人
        List<ApplyFlowUserItem> applyFlowUserItems = applyFlowService.getFlowItemsByApplyId(applyId);
        if (applyOrder.getFlowType() == CompanyApplyType.Elastic.getValue()) {
            apply.setNode_status(1);
        } else {
            FindNextApproverIdAndStatusResult findNextApproverIdAndStatusResult = findStatusInFlow(applyOrder, applyFlowUserItems, skippedApprverIds, true);
            //最后审批人
            if (findNextApproverIdAndStatusResult != null && findNextApproverIdAndStatusResult.getApplyStatus() == 2) {
                apply.setNode_status(1);
            } else {
                apply.setNode_status(2);
            }
        }
        //返回当前时间戳
        apply.setCurrent_time(DateTimeTool.fromDateTimeToString(new Date()));
        ApplyV2Contract applyContract = new ApplyV2Contract();
        applyContract.setApply(apply);
        List<ApplyTripInfoContract> tripList = getTripListByApplyOrderId(applyId, applyOrder);
        applyContract.setTrip_list(tripList);
        if (apply.getType() != ApplyType.Taxi.getValue() || apply.getType() != ApplyType.Mall.getValue() || apply.getType() != ApplyType.Dinner.getValue()) {
            //差旅有同行人信息
            List<UserContactContract> guestList = getGuestListByApplyOrderId(applyId);
            applyContract.setGuest_list(guestList);
        }
        if (applyContract == null) {
            return null;
        }
        //1、申请人及审批人的姓名及部门
        String employeeId = apply.getEmployee_id();
        String companyId = apply.getCompany_id();
        EmployeeContract employeeContract = iBaseEmployeeExtService.queryEmployeeInfo(employeeId, companyId);
        if (employeeContract != null && employeeContract.getId().equals(employeeId)) {
            apply.setUser_name(employeeContract.getName());
            apply.setUser_dept(employeeContract.getOrg_name());
        }
        // 2、可操作权限
        Integer operateAuth = genH5ApplyOperateAuth(apply, userId);
        apply.setOperate_auth(operateAuth);
        //处理审批流设置
        appendFlowData(applyContract.getApply());
        //处理审批流日志显示
        List<ApplyOrderLogContract> logs = getLogsByApplyId(applyId, applyOrder.getCompanyId());
        for (ApplyOrderLogContract applyOrderLogContract : logs) {
            if (StringUtils.isBlank(applyOrderLogContract.getSponsor()) && applyOrder.getEmployeeId().equals(applyOrderLogContract.getSponsor_id())) {
                if (applyOrderLogContract.getAction() != ApplyLogAction.Skip.getValue()) {
                    applyOrderLogContract.setSponsor(applyOrder.getApplicantName());
                }
            }
        }
        applyContract.setLog_list(logs);

        String orderId = applyContract.getTrip_list().get(0).getOrder_id();
        Integer orderType = applyContract.getTrip_list().get(0).getType();
        String ticketIds = null;
        //机票退订票号的获取
        if (type == ApplyType.Air.getValue() && subType == ApplySubType.Refund.getValue()) {
            Map<String, Object> tripOrderInfo = applyContract.getTrip_list().get(0).getTrip_order_info();
            String ticketId = ObjUtils.toString(tripOrderInfo.get("product_ids"));
            if (StringUtils.isNotBlank(ticketId)) {
                ticketIds = ticketId;
            }
        }
        //机票改期票号的获取
        if (type == ApplyType.Air.getValue() && subType == ApplySubType.Change.getValue()) {
            Map<String, Object> tripOrderInfo = applyContract.getTrip_list().get(0).getTrip_order_info();
            List<String> ticketIdList = (List<String>) tripOrderInfo.get("ticket_ids");
            if (CollectionUtils.isNotEmpty(ticketIdList)) {
                ticketIds = StringUtils.join(ticketIdList, ",");
            }
        }
        //火车退订票号的获取
        if (type == ApplyType.Train.getValue() && subType == ApplySubType.Refund.getValue()) {
            Map<String, Object> tripOrderInfo = applyContract.getTrip_list().get(0).getTrip_order_info();
            List<String> ticketIdList = ((List<String>) tripOrderInfo.get("ticket_ids"));
            if (CollectionUtils.isNotEmpty(ticketIdList)) {
                ticketIds = ticketIdList.get(0);
            }
        }
        //火车改签票号的获取
        if (type == ApplyType.Train.getValue() && subType == ApplySubType.Change.getValue()) {
            Map<String, Object> tripOrderInfo = applyContract.getTrip_list().get(0).getTrip_order_info();
            List<String> ticketIdList = ((List<String>) tripOrderInfo.get("product_ids"));
            if (CollectionUtils.isNotEmpty(ticketIdList)) {
                ticketIds = ticketIdList.get(0);
            }
        }
        applyContract.setOrder_info(disposeOrderInfo(orderId, orderType, employeeId, applyId, ticketIds,null));
        return applyContract;
    }

    /**
     * H5判断操作权限
     *
     * @param order
     * @param userId
     * @return
     */
    private Integer genH5ApplyOperateAuth(ApplyOrderV2Contract order, String userId) {
        ApplyStatus orderStatus = ApplyStatus.valueOf(order.getState());
        if (order.getEmployee_id().equals(userId)) {
            //自己的申请单
            if (orderStatus == ApplyStatus.PendingAudit) {
                if (userId.equals(order.getApprover_id())) {
                    return ApplyLogAction.Approve.getValue() + ApplyLogAction.Refuse.getValue();
                }
                //状态为待审核时,用户可撤销申请
                return 0;
            }
        } else {
            //是需要自己审批,或者自己已经操作过的审批单
            if (orderStatus == ApplyStatus.PendingAudit || orderStatus == ApplyStatus.Transfer) {
                //只有待自己处理的审批单才可操作
                if (userId.equals(order.getApprover_id())) {
                    return ApplyLogAction.Approve.getValue() + ApplyLogAction.Refuse.getValue();
                }
            }
        }
        //不能做任何操作
        return 0;
    }

    @Override
    public ApplyV2Contract getRefundChangeOrderDetail(String applyId, String orderId, Integer orderType, List<String> ticket_ids) throws SaasException {
        if ((orderType == BizType.Train.getValue() || orderType == BizType.Hotel.getValue() || orderType == BizType.Air.getValue()) && StringUtils.isBlank(orderId)) {
            throw new SaasException(GlobalResponseCode.ParameterError);
        }
        ApplyOrder applyOrder = null;
        List<ApplyTripApplicateExt> applyTripApplicateExts = applyTripApplicateExtMapper.queryApplyIdByOrderId(orderId);
        if (CollectionUtils.isEmpty(applyTripApplicateExts)) {
            return null;
        }
        applyOrder = applyOrderMapper.selectByPrimaryKey(applyTripApplicateExts.get(0).getApplyId());
        if (applyOrder.getApplyOrderType() != 6) {
            return null;
        }
        ApplyOrderV2Contract apply = ApplyOrderV2Contract.FromModel(applyOrder);
        //返回当前时间戳
        apply.setCurrent_time(DateTimeTool.fromDateTimeToString(new Date()));
        ApplyV2Contract applyContract = new ApplyV2Contract();
        applyContract.setApply(apply);
        return applyContract;
    }

    /**
     * 处理超时审批单定时任务
     */
    @Override
    //@Transactional(value = "saas")
    public void sloveOvertimeApplyOrder(ApplyOrder applyOrder) throws SaasException {
        String applyId = applyOrder.getId();
        if (applyOrder.getState() == ApplyStatus.PendingAudit.getValue()) {
            ApplyStatus finalStatus;
            ApplyLogAction action;
            String receiverId; //下一个审批人/通知人
            String approverId = ""; //审批人
            Integer logId = null;
            List<String> skippedApprverIds = new ArrayList<>();//固定审批流跳过的审批人
            Date now = new Date();
            //超时
            List<ApplyFlowUserItem> applyFlowUserItems = applyFlowService.getFlowItemsByApplyId(applyId);
            boolean isFlow = (ValueTool.areEqual(applyOrder.getFlowType(), CompanyApplyType.Flow.getValue()) || ValueTool.areEqual(applyOrder.getFlowType(), CompanyApplyType.CONDITIONAL.getValue()));
            if (isFlow) {
                for (ApplyFlowUserItem applyFlowUserItem : applyFlowUserItems) {
                    if (ValueTool.areEqual(applyFlowUserItem.getStatus(), ApplyFlowUserItemStatus.PendingAudit.getValue())) {
                        applyFlowService.setApplyFlowUserItemStatus(applyFlowUserItem.getId(), ApplyFlowUserItemStatus.Overtime);
                        approverId = applyFlowUserItem.getUserId();
                        break;
                    }
                }
            }
            finalStatus = ApplyStatus.Overtime;
            action = ApplyLogAction.Overtime;
            receiverId = applyOrder.getEmployeeId();
            //处理审批日志和审批单的状态
            ApplyApproveContract applyApproveContract = new ApplyApproveContract();
            applyApproveContract.setComment("");
            if (applyOrder.getFlowType() == CompanyApplyType.Elastic.getValue() && StringUtils.isBlank(approverId)) {
                approverId = applyOrder.getApproverId();
            }
            logId = sloveApplyLog(applyId, receiverId, approverId, finalStatus, now, "", approverId, action, applyApproveContract);
            setApplyApproverAndPushMessage(applyOrder, "", "", finalStatus, receiverId, approverId, "", action, skippedApprverIds, logId);
        }
    }

    @Override
    //@Transactional(value = "saas")
    public GlobalResponseCode makeApplyOrderInvalid(List<String> applyIds) {
        if (ObjUtils.isEmpty(applyIds)){
            return GlobalResponseCode.ParameterError;
        }
        for (String applyId:applyIds){
            ApplyOrder applyOrder=applyOrderMapper.selectByPrimaryKey(applyId);
            if (ObjUtils.isEmpty(applyOrder)){
                logger.error("当前审批单不存在："+applyId);
                return GlobalResponseCode.ApplyIsNull;
            }
            if (applyOrder.getState() == ApplyStatus.PendingAudit.getValue()) {
                ApplyStatus finalStatus;
                ApplyLogAction action;
                String receiverId; //下一个审批人/通知人
                String approverId = ""; //审批人
                Integer logId = null;
                List<String> skippedApprverIds = new ArrayList<>();//固定审批流跳过的审批人
                Date now = new Date();
                //超时
                List<ApplyFlowUserItem> applyFlowUserItems = applyFlowService.getFlowItemsByApplyId(applyId);
                boolean isFlow = (ValueTool.areEqual(applyOrder.getFlowType(), CompanyApplyType.Flow.getValue()) || ValueTool.areEqual(applyOrder.getFlowType(), CompanyApplyType.CONDITIONAL.getValue()));
                if (isFlow) {
                    for (ApplyFlowUserItem applyFlowUserItem : applyFlowUserItems) {
                        if (ValueTool.areEqual(applyFlowUserItem.getStatus(), ApplyFlowUserItemStatus.PendingAudit.getValue())) {
                            applyFlowService.setApplyFlowUserItemStatus(applyFlowUserItem.getId(), ApplyFlowUserItemStatus.Overtime);
                            approverId = applyFlowUserItem.getUserId();
                            break;
                        }
                    }
                }
                finalStatus = ApplyStatus.Overtime;
                action = ApplyLogAction.Invalid;
                receiverId = applyOrder.getEmployeeId();
                //处理审批日志和审批单的状态
                ApplyApproveContract applyApproveContract = new ApplyApproveContract();
                applyApproveContract.setComment(CoreLanguage.Common_Value_AirInfoInvalid.getMessage());
                if (applyOrder.getFlowType() == CompanyApplyType.Elastic.getValue() && StringUtils.isBlank(approverId)) {
                    approverId = applyOrder.getApproverId();
                }
                logId = sloveApplyLog(applyId, receiverId, approverId, finalStatus, now, "", approverId, action, applyApproveContract);
                setApplyApproverAndPushMessage(applyOrder, "", "", finalStatus, receiverId, approverId, "", action, skippedApprverIds, logId);
            }
        }
        return GlobalResponseCode.Success;
    }

}

