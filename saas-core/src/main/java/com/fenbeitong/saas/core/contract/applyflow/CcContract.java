package com.fenbeitong.saas.core.contract.applyflow;

import com.fenbeitong.saas.core.contract.user.UserInfoContract;
import com.fenbeitong.saas.core.model.saas.ApplyFlowItem;
import com.fenbeitong.saas.core.model.saas.ApplyFlowUserItem;

import java.util.List;

/**
 * Created by xiabin on 2017/4/21.
 */
public class CcContract {
    /**
     * 节点类型 对应枚举ApplyFlowItemType
     */
    private Integer item_type;

    /**
     * 对应item_type 自定义角色->角色ID；指定员工->员工ID；授权负责人 ->null；企业管理员 ->null；主管->主管级别(Int)；
     */
    private String item_id;
    /**
     * 节点名字
     */
    private String item_name;

    private List<UserInfoContract> users;

    /**
     * 提交申请或显示审批单详情时，从users中选择的某一个人作为审批人
     */
    private UserInfoContract user;

    /**
     * 已提交的申请单，提交状态，对应枚举ApplyFlowUserItemStatus
     */
    private Integer status;

    public static CcContract fromModel(ApplyFlowItem applyFlowItem) {
        CcContract itemContract = new CcContract();
        itemContract.setItem_type(applyFlowItem.getItemType());
        itemContract.setItem_id(applyFlowItem.getItemId());
        return itemContract;
    }

    public static CcContract fromModel(ApplyFlowUserItem applyFlowUserItem) {
        CcContract itemContract = new CcContract();
        itemContract.setItem_type(applyFlowUserItem.getItemType());
        itemContract.setItem_id(applyFlowUserItem.getItemId());
        itemContract.setItem_name(applyFlowUserItem.getItemName());
        itemContract.setStatus(applyFlowUserItem.getStatus());
        UserInfoContract user = new UserInfoContract();
        user.setUser_id(applyFlowUserItem.getUserId());
        itemContract.setUser(user);
        return itemContract;
    }

    public Integer getItem_type() {
        return item_type;
    }

    public void setItem_type(Integer item_type) {
        this.item_type = item_type;
    }

    public String getItem_id() {
        return item_id;
    }

    public void setItem_id(String item_id) {
        this.item_id = item_id;
    }

    public String getItem_name() {
        return item_name;
    }

    public void setItem_name(String item_name) {
        this.item_name = item_name;
    }

    public List<UserInfoContract> getUsers() {
        return users;
    }

    public void setUsers(List<UserInfoContract> users) {
        this.users = users;
    }

    public UserInfoContract getUser() {
        return user;
    }

    public void setUser(UserInfoContract user) {
        this.user = user;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
