package com.fenbeitong.saas.core.service.order;

import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.noc.api.car.req.EmployeeConsumeInfoReq;
import com.fenbeitong.noc.api.car.resp.EmployeeConsumeInfoRes;
import com.fenbeitong.noc.api.car.service.ICarSassService;
import com.fenbeitong.noc.api.service.bus.model.dto.req.BusFinalStateQueryReqDTO;
import com.fenbeitong.noc.api.service.bus.model.dto.req.BusSaasTripApplyIdReqDTO;
import com.fenbeitong.noc.api.service.bus.model.dto.resp.BusSaasTripApplyIdResRpcDTO;
import com.fenbeitong.noc.api.service.bus.model.vo.BusFinalQueryVO;
import com.fenbeitong.noc.api.service.bus.service.IBusOrderSearchService;
import com.fenbeitong.noc.api.service.common.OrderSaasReqDTO;
import com.fenbeitong.noc.api.service.common.OrderSaasResDTO;
import com.fenbeitong.noc.api.service.meishi.service.IMeishiOrderSearchService;
import com.fenbeitong.noc.api.service.takeaway.service.ITakeawayOrderSearchService;
import com.fenbeitong.oc.api.model.dto.req.AirFinalStateQueryReqDTO;
import com.fenbeitong.oc.api.model.dto.req.ApplyCostMoneyReqDTO;
import com.fenbeitong.oc.api.model.dto.req.OrderQueryReqDTO;
import com.fenbeitong.oc.api.model.dto.req.hotel.HotelFinalQueryDTO;
import com.fenbeitong.oc.api.model.dto.req.hotel.HotelFinalStateQueryReqDTO;
import com.fenbeitong.oc.api.model.dto.res.ApplyCostMoneyResDTO;
import com.fenbeitong.oc.api.model.dto.res.ApplyOrderFinalStateResDTO;
import com.fenbeitong.oc.api.model.dto.res.OrderQueryResDTO;
import com.fenbeitong.oc.api.model.vo.AirFinalQueryVO;
import com.fenbeitong.oc.api.service.IHotelSaasService;
import com.fenbeitong.oc.api.service.ITrainSaasService;
import com.fenbeitong.oc.api.service.air.IAirSaasService;
import com.fenbeitong.saas.core.common.base.SaasException;
import com.fenbeitong.saas.core.common.constant.GlobalResponseCode;
import com.fenbeitong.saas.core.contract.order.apply.ApplyWithOrderVO;
import com.fenbeitong.saas.core.dao.dynamic.ApplyTripApplicateMapper;
import com.fenbeitong.saas.core.model.enums.apply.ApplyTripApplicateAction;
import com.fenbeitong.saas.core.model.saas.ApplyTripApplicate;
import com.fenbeitong.saas.core.model.saas.ApplyTripApplicateExample;
import com.fenbeitong.saasplus.api.model.dto.finance.FinanceCostInfoRelationApplyVO;
import com.fenbeitong.saasplus.api.service.finance.IFinanceCostInfoRelationApplyService;
import com.google.common.collect.Lists;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ObjUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2022/6/17
 */
@Slf4j
@Service
public class OrderService {

    @Autowired
    private IAirSaasService iAirSaasService;
    @Autowired
    private IHotelSaasService iHotelSaasService;
    @Autowired
    private ITrainSaasService iTrainSaasService;
    @Autowired
    private ICarSassService iCarSassService;
    @Autowired
    private IBusOrderSearchService iBusOrderSearchService;
    @Autowired
    private IMeishiOrderSearchService iMeishiOrderSearchService;
    @Autowired
    private ITakeawayOrderSearchService iTakeawayOrderSearchService;
    @Autowired
    private IFinanceCostInfoRelationApplyService iFinanceCostInfoRelationApplyService;
    @Autowired
    private ApplyTripApplicateMapper applyTripApplicateMapper;

    /**
     * 查询国内/国际机票历史订单金额
     * @param applyId 申请单id
     * @param tripId 行程id
     * @param orderId 订单id，支持多个订单，可以传空串，不能传null
     */
    public BigDecimal queryAirHistoryOrderPrice(String applyId, String tripId, String... orderId) {
        ApplyCostMoneyReqDTO req = new ApplyCostMoneyReqDTO();
        req.setApplyId(applyId);
        req.setTripId(tripId);
        req.setOrderIds(Arrays.stream(orderId).filter(ObjUtils::isNotEmpty).collect(Collectors.toList()));
        ApplyCostMoneyResDTO res;
        try {
            log.info("查询国内/国际机票, req:{}", JsonUtils.toJson(req));
            res = iAirSaasService.queryOrderCostMoney(req);
            log.info("查询国内/国际机票, res:{}", JsonUtils.toJson(res));
        } catch (FinhubException e) {
            log.info("查询国内机票历史订单异常", e);
            throw new SaasException(GlobalResponseCode.ORDER_HISTORY_TOTAL_QUERY_ERROR);
        }
        return res == null ? BigDecimal.ZERO : ObjUtils.toBigDecimal(res.getCostMoney(), BigDecimal.ZERO);
    }

    /**
     * 查询国内/国际机票历史订单金额
     * @param companyId 企业id
     * @param applyIdList 申请单id集合
     * @param createDate 订单创建时间
     * @param category 场景类型 不传时同时查询 国内/国际 机票
     */
    public List<OrderQueryResDTO> queryAirHistoryOrder(String companyId, List<String> applyIdList, Date createDate,
                                                       Integer category) {
        OrderQueryReqDTO req = new OrderQueryReqDTO();
        req.setCompanyId(companyId);
        req.setApplyIdList(applyIdList);
        req.setCreateDate(createDate);
        req.setCategory(category);
        List<OrderQueryResDTO> res;
        try {
            log.info("查询国内/国际机票, req:{}", JsonUtils.toJson(req));
            res = iAirSaasService.queryOrderForSaas(req);
            log.info("查询国内/国际机票, res:{}", JsonUtils.toJson(res));
        } catch (FinhubException e) {
            log.info("查询国内/国际机票历史订单异常", e);
            throw new SaasException(GlobalResponseCode.ORDER_HISTORY_TOTAL_QUERY_ERROR);
        }
        return res == null ? Lists.newArrayList() : res;
    }

    /**
     * 查询酒店历史订单金额
     * @param applyId 申请单id
     * @param tripId 行程id
     * @param orderIds 订单id列表
     */
    public BigDecimal queryHotelHistoryOrderPrice(String applyId, String tripId, List<String> orderIds) {
        // 不传订单id时，酒店历史金额为0
        if (CollectionUtils.isEmpty(orderIds)) {
            return BigDecimal.ZERO;
        }
        ApplyCostMoneyReqDTO req = new ApplyCostMoneyReqDTO();
        req.setApplyId(applyId);
        req.setTripId(tripId);
        req.setOrderIds(orderIds);
        ApplyCostMoneyResDTO res;
        try {
            log.info("查询酒店订单, req:{}", JsonUtils.toJson(req));
            res = iHotelSaasService.queryOrderCostMoneyByTripId(req);
            log.info("查询酒店订单, res:{}", JsonUtils.toJson(res));
        } catch (FinhubException e) {
            log.info("查询酒店历史订单异常", e);
            throw new SaasException(GlobalResponseCode.ORDER_HISTORY_TOTAL_QUERY_ERROR);
        }
        return res == null ? BigDecimal.ZERO : ObjUtils.toBigDecimal(res.getCostMoney(), BigDecimal.ZERO);
    }

    /**
     * 查询酒店历史订单金额
     * @param applyId 申请单id
     * @param tripId 行程id
     * @param orderIds 订单id列表
     */
    public List<OrderQueryResDTO> queryHotelHistoryOrder(String applyId, String tripId, List<String> orderIds) {
        // 不传订单id时，酒店历史金额为0
        if (CollectionUtils.isEmpty(orderIds)) {
            return Lists.newArrayList();
        }
        ApplyCostMoneyReqDTO req = new ApplyCostMoneyReqDTO();
        req.setApplyId(applyId);
        req.setTripId(tripId);
        req.setOrderIds(orderIds);

        List<OrderQueryResDTO> res;
        try {
            log.info("查询酒店订单, req:{}", JsonUtils.toJson(req));
            res = iHotelSaasService.queryOrderCost(req);
            log.info("查询酒店订单, res:{}", JsonUtils.toJson(res));
        } catch (FinhubException e) {
            log.info("查询酒店历史订单异常", e);
            throw new SaasException(GlobalResponseCode.ORDER_HISTORY_TOTAL_QUERY_ERROR);
        }
        return res == null ? Lists.newArrayList() : res;
    }

    /**
     * 查询火车历史订单金额
     * @param applyId 申请单id
     * @param tripId 行程id
     * @param orderId 订单id，支持多个订单，可以传空串，不能传null
     */
    public BigDecimal queryTrainHistoryOrderPrice(String applyId, String tripId, String... orderId) {
        ApplyCostMoneyReqDTO req = new ApplyCostMoneyReqDTO();
        req.setApplyId(applyId);
        req.setTripId(tripId);
        req.setOrderIds(Arrays.stream(orderId).filter(ObjUtils::isNotEmpty).collect(Collectors.toList()));
        ApplyCostMoneyResDTO res;
        try {
            log.info("查询火车订单, req:{}", JsonUtils.toJson(req));
            res = iTrainSaasService.queryOrderCostMoney(req);
            log.info("查询火车订单, res:{}", JsonUtils.toJson(res));
        } catch (FinhubException e) {
            log.info("查询火车历史订单异常", e);
            throw new SaasException(GlobalResponseCode.ORDER_HISTORY_TOTAL_QUERY_ERROR);
        }
        return res == null ? BigDecimal.ZERO : ObjUtils.toBigDecimal(res.getCostMoney(), BigDecimal.ZERO);
    }

    /**
     * 查询火车历史订单信息
     */
    public List<OrderQueryResDTO> queryTrainHistoryOrder(String companyId, List<String> applyIdList, Date createDate) {
        if (CollectionUtils.isEmpty(applyIdList)) {
            return Lists.newArrayList();
        }
        OrderQueryReqDTO req = new OrderQueryReqDTO();
        req.setCompanyId(companyId);
        req.setApplyIdList(applyIdList);
        req.setCreateDate(createDate);

        List<OrderQueryResDTO> res;
        try {
            log.info("查询火车订单, req:{}", JsonUtils.toJson(req));
            res = iTrainSaasService.queryOrderCostDetail(req);
            log.info("查询火车订单, res:{}", JsonUtils.toJson(res));
        } catch (FinhubException e) {
            log.info("查询火车历史订单异常", e);
            throw new SaasException(GlobalResponseCode.ORDER_HISTORY_TOTAL_QUERY_ERROR);
        }
        return res == null ? Lists.newArrayList() : res;
    }

    /**
     * 查询用车历史订单
     * @param companyId 企业id
     * @param userId 用户id
     * @param applyId 申请单id
     */
    public List<EmployeeConsumeInfoRes> queryCarHistoryOrder(String companyId, String userId, String applyId) {
        EmployeeConsumeInfoReq req = new EmployeeConsumeInfoReq();
        req.setCompanyId(companyId);
        req.setEmployeeId(userId);
        req.setApplyId(applyId);
        List<EmployeeConsumeInfoRes> res;
        try {
            log.info("查询用车订单, req:{}", JsonUtils.toJson(req));
            res = iCarSassService.getEmployeeConsumeInfo(req);
            log.info("查询用车订单, res:{}", JsonUtils.toJson(res));
        } catch (FinhubException e) {
            log.info("查询用车历史订单异常", e);
            throw new SaasException(GlobalResponseCode.ORDER_HISTORY_TOTAL_QUERY_ERROR);
        }
        return res == null ? Lists.newArrayList() : res;
    }

    /**
     * 查询用车历史订单
     * @param companyId 企业id
     * @param userId 员工id
     * @param tripId 行程id
     * @param applyId 申请单id
     */
    public List<EmployeeConsumeInfoRes> queryCarHistoryOrder(String companyId, String userId, String applyId,
        String tripId) {
        EmployeeConsumeInfoReq req = new EmployeeConsumeInfoReq();
        req.setCompanyId(companyId);
        req.setEmployeeId(userId);
        req.setTripId(tripId);
        req.setApplyId(applyId);
        List<EmployeeConsumeInfoRes> res;
        try {
            log.info("查询用车订单, req:{}", JsonUtils.toJson(req));
            res = iCarSassService.getEmployeeConsumeInfo(req);
            log.info("查询用车订单, res:{}", JsonUtils.toJson(res));
        } catch (FinhubException e) {
            log.info("查询用车历史订单异常", e);
            throw new SaasException(GlobalResponseCode.ORDER_HISTORY_TOTAL_QUERY_ERROR);
        }
        return res == null ? Lists.newArrayList() : res;
    }

    /**
     * 查询汽车历史订单金额
     * @param companyId 企业id
     * @param tripId 行程id
     */
    public BigDecimal queryBusHistoryOrderPrice(String companyId, String tripId) {
        if (ObjUtils.isEmpty(tripId)) {
            return BigDecimal.ZERO;
        }
        BusSaasTripApplyIdReqDTO req = new BusSaasTripApplyIdReqDTO();
        req.setCompanyId(companyId);
        req.setTripApplyId(tripId);
        BusSaasTripApplyIdResRpcDTO res;
        try {
            log.info("查询汽车订单, req:{}", JsonUtils.toJson(req));
            res = iBusOrderSearchService.saasTripApplyIdOrders(req);
            log.info("查询汽车订单, res:{}", JsonUtils.toJson(res));
        } catch (FinhubException e) {
            log.info("查询汽车历史订单异常", e);
            throw new SaasException(GlobalResponseCode.ORDER_HISTORY_TOTAL_QUERY_ERROR);
        }
        return res == null ? BigDecimal.ZERO : ObjUtils.toBigDecimal(res.getTotalOrderAmount(), BigDecimal.ZERO);
    }

    /**
     * 查询用餐历史订单信息
     */
    public List<OrderSaasResDTO> queryDinnerHistoryOrder(List<String> applyIdList, Date createDate) {
        if (ObjUtils.isEmpty(applyIdList)) {
            return Lists.newArrayList();
        }
        OrderSaasReqDTO req = new OrderSaasReqDTO();
        req.setApplyIdList(applyIdList);
        req.setBeginTime(createDate);

        List<OrderSaasResDTO> res;
        try {
            log.info("查询美食订单, req:{}", JsonUtils.toJson(req));
            res = iMeishiOrderSearchService.searchOrderByApplyIdAndTime(req);
            log.info("查询美食订单, res:{}", JsonUtils.toJson(res));
        } catch (Exception e) {
            log.info("查询美食历史订单异常", e);
            throw new SaasException(GlobalResponseCode.ORDER_HISTORY_TOTAL_QUERY_ERROR);
        }
        return res == null ? Lists.newArrayList() : res;
    }

    /**
     * 查询用餐历史订单信息
     */
    public List<OrderSaasResDTO> queryTakeawayHistoryOrder(List<String> applyIdList, Date createDate) {
        if (ObjUtils.isEmpty(applyIdList)) {
            return Lists.newArrayList();
        }
        OrderSaasReqDTO req = new OrderSaasReqDTO();
        req.setApplyIdList(applyIdList);
        req.setBeginTime(createDate);

        List<OrderSaasResDTO> res;
        try {
            log.info("查询外卖订单, req:{}", JsonUtils.toJson(req));
            res = iTakeawayOrderSearchService.searchOrderByApplyIdAndTime(req);
            log.info("查询外卖订单, res:{}", JsonUtils.toJson(res));
        } catch (Exception e) {
            log.info("查询外卖历史订单异常", e);
            throw new SaasException(GlobalResponseCode.ORDER_HISTORY_TOTAL_QUERY_ERROR);
        }
        return res == null ? Lists.newArrayList() : res;
    }

    /**
     * 查询纯报销历史订单
     */
    public List<FinanceCostInfoRelationApplyVO> queryFinanceCostHistoryOrder(String companyId, List<String> applyIdList) {
        if (ObjUtils.isEmpty(companyId) || ObjUtils.isEmpty(applyIdList)) {
            return Lists.newArrayList();
        }
        List<FinanceCostInfoRelationApplyVO> orderList;
        try {
            log.info("查询纯报销, companyId:{}, applyIdList:{}", companyId, JsonUtils.toJson(applyIdList));
            orderList = iFinanceCostInfoRelationApplyService.queryAmountUsedPriceByApplyIds(companyId, applyIdList);
            log.info("查询纯报销, orderList:{}", JsonUtils.toJson(orderList));
        } catch (Exception e) {
            log.info("查询纯报销异常", e);
            throw new SaasException(GlobalResponseCode.ORDER_HISTORY_TOTAL_QUERY_ERROR);
        }
        return orderList == null ? Lists.newArrayList() : orderList;
    }

    /**
     * 根据申请单批量查询机票关联订单状态
     */
    public List<ApplyOrderFinalStateResDTO> queryAirOrderFinalState(String companyId, List<ApplyWithOrderVO> applyWithOrderList) {
        log.info("查询机票关联订单状态, applyWithOrderList:{}", JsonUtils.toJson(applyWithOrderList));
        if (ObjUtils.isEmpty(companyId) || ObjUtils.isEmpty(applyWithOrderList)) {
            return Lists.newArrayList();
        }
        // 组装入参
        AirFinalStateQueryReqDTO req = new AirFinalStateQueryReqDTO();
        req.setCompanyId(companyId);
        req.setCreateTime(getMinDateFromApplyWithOrder(applyWithOrderList));
        List<AirFinalQueryVO> queryList = applyWithOrderList.stream()
                .map(e -> {
                    AirFinalQueryVO query = new AirFinalQueryVO();
                    query.setApplyId(e.getApplyId());
                    query.setOrderIds(e.getOrderList());
                    return query;
                }).collect(Collectors.toList());
        req.setVos(queryList);

        List<ApplyOrderFinalStateResDTO> res = null;
        try {
            log.info("查询机票关联订单状态, req:{}", JsonUtils.toJson(req));
            res = iAirSaasService.queryAirFinalStateByApply(req);
            log.info("查询机票关联订单状态, res:{}", JsonUtils.toJson(res));
        } catch (FinhubException e) {
            log.info("查询机票关联订单状态异常, req:{}", JsonUtils.toJson(req), e);
        }
        return res == null ? Lists.newArrayList() : res;
    }

    /**
     * 根据申请单批量查询酒店关联订单状态
     */
    public List<ApplyOrderFinalStateResDTO> queryHotelOrderFinalState(String companyId, List<ApplyWithOrderVO> applyWithOrderList) {
        log.info("查询酒店关联订单状态, applyWithOrderList:{}", JsonUtils.toJson(applyWithOrderList));
        if (ObjUtils.isEmpty(companyId) || ObjUtils.isEmpty(applyWithOrderList)) {
            return Lists.newArrayList();
        }
        // 组装入参
        HotelFinalStateQueryReqDTO req = new HotelFinalStateQueryReqDTO();
        req.setCompanyId(companyId);
        req.setCreateTime(getMinDateFromApplyWithOrder(applyWithOrderList));
        List<HotelFinalQueryDTO> queryList = applyWithOrderList.stream()
                .map(e -> {
                    HotelFinalQueryDTO query = new HotelFinalQueryDTO();
                    query.setApplyId(e.getApplyId());
                    query.setOrderIds(e.getOrderList());
                    return query;
                }).collect(Collectors.toList());
        req.setQueryList(queryList);

        List<ApplyOrderFinalStateResDTO> res = null;
        try {
            log.info("查询酒店关联订单状态, req:{}", JsonUtils.toJson(req));
            res = iHotelSaasService.queryHotelFinalStateByApply(req);
            log.info("查询酒店关联订单状态, res:{}", JsonUtils.toJson(res));
        } catch (FinhubException e) {
            log.info("查询酒店关联订单状态异常, req:{}", JsonUtils.toJson(req), e);
        }
        return res == null ? Lists.newArrayList() : res;
    }

    /**
     * 根据申请单批量查询火车关联订单状态
     */
    public List<ApplyOrderFinalStateResDTO> queryTrainOrderFinalState(String companyId, List<ApplyWithOrderVO> applyWithOrderList) {
        log.info("查询火车关联订单状态, applyWithOrderList:{}", JsonUtils.toJson(applyWithOrderList));
        if (ObjUtils.isEmpty(companyId) || ObjUtils.isEmpty(applyWithOrderList)) {
            return Lists.newArrayList();
        }
        // 组装入参
        OrderQueryReqDTO req = new OrderQueryReqDTO();
        req.setCompanyId(companyId);
        req.setCreateDate(getMinDateFromApplyWithOrder(applyWithOrderList));
        List<String> applyIdList = applyWithOrderList.stream()
                .map(ApplyWithOrderVO::getApplyId)
                .collect(Collectors.toList());
        req.setApplyIdList(applyIdList);
        List<ApplyOrderFinalStateResDTO> res = null;
        try {
            log.info("查询火车关联订单状态, req:{}", JsonUtils.toJson(req));
            res = iTrainSaasService.queryOrderFinalStateByApply(req);
            log.info("查询火车关联订单状态, res:{}", JsonUtils.toJson(res));
        } catch (FinhubException e) {
            log.info("查询火车关联订单状态异常, req:{}", JsonUtils.toJson(req), e);
        }
        return res == null ? Lists.newArrayList() : res;
    }

    /**
     * 根据申请单批量查询汽车关联订单状态
     */
    public List<com.fenbeitong.noc.api.service.bus.model.dto.resp.ApplyOrderFinalStateResDTO>
    queryBusOrderFinalState(String companyId, List<ApplyWithOrderVO> applyWithOrderList) {
        log.info("查询汽车关联订单状态, applyWithOrderList:{}", JsonUtils.toJson(applyWithOrderList));
        if (ObjUtils.isEmpty(companyId) || ObjUtils.isEmpty(applyWithOrderList)) {
            return Lists.newArrayList();
        }

        BusFinalStateQueryReqDTO req = new BusFinalStateQueryReqDTO();
        req.setCompanyId(companyId);
        req.setCreateTime(getMinDateFromApplyWithOrder(applyWithOrderList));
        List<BusFinalQueryVO> queryList = applyWithOrderList.stream()
                .map(e -> {
                    BusFinalQueryVO query = new BusFinalQueryVO();
                    query.setApplyId(e.getApplyId());
                    query.setOrderIds(e.getOrderList());
                    return query;
                }).collect(Collectors.toList());
        req.setVos(queryList);

        List<com.fenbeitong.noc.api.service.bus.model.dto.resp.ApplyOrderFinalStateResDTO> res = null;
        try {
            log.info("查询汽车关联订单状态, req:{}", JsonUtils.toJson(req));
            res = iBusOrderSearchService.queryBusFinalStateByApply(req);
            log.info("查询汽车关联订单状态, res:{}", JsonUtils.toJson(res));
        } catch (FinhubException e) {
            log.info("查询汽车关联订单状态异常, req:{}", JsonUtils.toJson(req), e);
        }
        return res == null ? Lists.newArrayList() : res;
    }

    /**
     * 筛选出申请单创建日期中的最小值
     */
    public Date getMinDateFromApplyWithOrder(List<ApplyWithOrderVO> applyWithOrderList) {
        return applyWithOrderList.stream()
                .map(ApplyWithOrderVO::getCreateDate)
                .sorted(Date::compareTo)
                .collect(Collectors.toList())
                .get(0);
    }

    /**
     * 查询非行程历史订单金额
     * @param applyId
     * @param tripId
     * @return
     */
    public BigDecimal queryMultiTripUsedAmount(String applyId, String tripId, String companyId) {
        BigDecimal usedAmount = BigDecimal.ZERO;
        // 国内/国际机票
        BigDecimal airUsedAmount = queryAirHistoryOrderPrice(applyId, tripId, "");
        usedAmount = usedAmount.add(airUsedAmount);
        // 火车
        BigDecimal trainUsedAmount = queryTrainHistoryOrderPrice(applyId, tripId, "");
        usedAmount = usedAmount.add(trainUsedAmount);
        // 酒店
        ApplyTripApplicateExample applicateExample = new ApplyTripApplicateExample();
        applicateExample.createCriteria()
            .andApplyIdEqualTo(applyId)
            .andApplyTripIdEqualTo(tripId)
            .andActionEqualTo(ApplyTripApplicateAction.Applicate.getValue());
        List<ApplyTripApplicate> tripApplicateList = applyTripApplicateMapper.selectByExample(applicateExample);
        List<String> orderList = tripApplicateList.stream()
            .map(ApplyTripApplicate::getOrderId)
            .distinct()
            .collect(Collectors.toList());
        BigDecimal hotelUsedAmount = queryHotelHistoryOrderPrice(applyId, tripId, orderList);
        usedAmount = usedAmount.add(hotelUsedAmount);
        // 汽车票
        BigDecimal busUsedAmount = queryBusHistoryOrderPrice(companyId, tripId);
        usedAmount = usedAmount.add(busUsedAmount);
        return usedAmount;
    }

}
