package com.fenbeitong.saas.core.contract.message.inner;

import com.alibaba.fastjson.JSONObject;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/4/18.
 */
public class SystemInfo {

    private boolean has_link;

    private boolean has_image;

    private String image_url;

    private String redirect_url;

    private String message;

    private String generate_time;

    private Integer biz_type;

    public String getGenerate_time() {
        return generate_time;
    }

    public void setGenerate_time(String generate_time) {
        this.generate_time = generate_time;
    }

    public boolean isHas_link() {
        return has_link;
    }

    public void setHas_link(boolean has_link) {
        this.has_link = has_link;
    }

    public boolean isHas_image() {
        return has_image;
    }

    public void setHas_image(boolean has_image) {
        this.has_image = has_image;
    }

    public String getImage_url() {
        return image_url;
    }

    public void setImage_url(String image_url) {
        this.image_url = image_url;
    }

    public String getRedirect_url() {
        return redirect_url;
    }

    public void setRedirect_url(String redirect_url) {
        this.redirect_url = redirect_url;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }

    public Integer getBiz_type() {
        return biz_type;
    }

    public void setBiz_type(Integer biz_type) {
        this.biz_type = biz_type;
    }
}
