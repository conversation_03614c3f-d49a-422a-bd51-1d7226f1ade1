package com.fenbeitong.saas.core.utils.transfer;

import com.fenbeitong.saas.core.contract.organization.inner.*;
import com.fenbeitong.saas.core.model.enums.rule.*;
import com.fenbeitong.usercenter.api.model.po.rule.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/5/22.
 */
public class RuleTransferUtil {

    private static Logger logger = LoggerFactory.getLogger(RuleTransferUtil.class);

    public static EmployeeMallRule getEmployeeMallRule(String userId, MallPolicyBean mallPolicyBean, String companyId) {
        Integer mallRule = null;
        if (mallPolicyBean == null) {
            mallPolicyBean = new MallPolicyBean();
        } else if (mallPolicyBean.isMall_priv_flag() != null) {
            mallRule = MallRuleType.getBooleanValue(mallPolicyBean.isMall_priv_flag()).getCode();
        }
        return new EmployeeMallRule(
                userId, mallRule, mallPolicyBean.isRule_limit_flag(),
                null, mallPolicyBean.getRule_id(), mallPolicyBean.isExceed_buy_flag(), mallPolicyBean.getMall_verify_flag(), companyId
        );
    }

    public static EmployeeTrainRule getEmployeeTrainRule(String userId, BizTripPolicyBean bizTripPolicyBean, String companyId) {
        Integer trainRule = null;
        Integer trainOtherRule = null;

        if (bizTripPolicyBean == null) {
            bizTripPolicyBean = new BizTripPolicyBean();
        }
        if (bizTripPolicyBean.isHotel_priv_flag() != null) {
            trainRule = TrainRuleType.getBooleanValue(bizTripPolicyBean.isHotel_priv_flag()).getCode();
        }
        if (bizTripPolicyBean.getTrain_other_flag() != null) {
            trainOtherRule =TrainRuleType.getBooleanValue(bizTripPolicyBean.getTrain_other_flag()).getCode();
        }
        return new EmployeeTrainRule(
                userId, trainRule,trainOtherRule, null, null,
                bizTripPolicyBean.isRule_limit_flag(), null, bizTripPolicyBean.getRule_id(),
                bizTripPolicyBean.isTrain_verify_flag(), bizTripPolicyBean.isExceed_buy_flag(), bizTripPolicyBean.getUnemployee_train(), bizTripPolicyBean.getExceed_buy_type(), false, companyId
        );
    }

    public static EmployeeTrainRule getEmployeeTrainRuleV2(String userId, TrainPolicyBean trainPolicyBean, String companyId) {
        Integer trainRule = null;
        Integer trainOtherRule = null;

        if (trainPolicyBean == null) {
            trainPolicyBean = new TrainPolicyBean();
        }
        if (trainPolicyBean.getTrain_priv_flag() != null) {
            trainRule = TrainRuleType.getBooleanValue(trainPolicyBean.getTrain_priv_flag()).getCode();
        }
        if (trainPolicyBean.getTrain_other_flag() != null) {
            trainOtherRule =TrainRuleType.getBooleanValue(trainPolicyBean.getTrain_other_flag()).getCode();
        }


        return new EmployeeTrainRule(
                userId,
                trainRule,
                trainOtherRule,
                null,
                null,

                trainPolicyBean.getRule_limit_flag(),
                null,
                trainPolicyBean.getRule_id(),
                trainPolicyBean.getTrain_verify_flag(),
                false,
                trainPolicyBean.getUnemployee_train(),
                trainPolicyBean.getExceed_buy_type(),
                trainPolicyBean.getTrain_order_verify_flag(),
                companyId
        );
    }

    public static EmployeeHotelRule getEmployeeHotelRule(String userId, BizTripPolicyBean bizTripPolicyBean, String companyId) {
        Integer hotelRule = null;
        Integer hotelOtherRule = null;

        if (bizTripPolicyBean == null) {
            bizTripPolicyBean = new BizTripPolicyBean();
        }

        if (bizTripPolicyBean.isHotel_priv_flag() != null) {
            hotelRule = HotelRuleType.getBooleanValue(bizTripPolicyBean.isHotel_priv_flag()).getCode();
        }
        if (bizTripPolicyBean.getHotel_other_flag() != null) {
            hotelOtherRule =HotelRuleType.getBooleanValue(bizTripPolicyBean.getHotel_other_flag()).getCode();
        }
        return new EmployeeHotelRule(
                userId, hotelRule, hotelOtherRule, null, null,
                bizTripPolicyBean.isRule_limit_flag(), null, bizTripPolicyBean.getRule_id(),
                bizTripPolicyBean.isHotel_verify_flag(), bizTripPolicyBean.isExceed_buy_flag(), bizTripPolicyBean.getUnemployee_hotel(), bizTripPolicyBean.getExceed_buy_type(), false, companyId
        );
    }

    public static EmployeeHotelRule getEmployeeHotelRuleV2(String userId, HotelPolicyBean hotelPolicyBean, String companyId) {
        Integer hotelRule = null;
        Integer hotelOtherRule = null;
        if (hotelPolicyBean == null) {
            hotelPolicyBean = new HotelPolicyBean();
        }
        if (hotelPolicyBean.getHotel_priv_flag() != null) {
            hotelRule = HotelRuleType.getBooleanValue(hotelPolicyBean.getHotel_priv_flag()).getCode();
        }
        if (hotelPolicyBean.getHotel_other_flag() != null) {
            hotelOtherRule =HotelRuleType.getBooleanValue(hotelPolicyBean.getHotel_other_flag()).getCode();
        }

        return new EmployeeHotelRule(
                userId,
                hotelRule,
                hotelOtherRule,
                null,
                null,
                hotelPolicyBean.getRule_limit_flag(),
                null,
                hotelPolicyBean.getRule_id(),
                hotelPolicyBean.getHotel_verify_flag(),
                false,
                hotelPolicyBean.getUnemployee_hotel(),
                hotelPolicyBean.getExceed_buy_type(),
                hotelPolicyBean.getHotel_order_verify_flag(),
                companyId
        );
    }

    public static EmployeeAirRule getEmployeeAirRule(String userId, BizTripPolicyBean bizTripPolicyBean, String companyId) {
        Integer airRule = null;
        Integer airOtherRule = null;

        if (bizTripPolicyBean == null) {
            bizTripPolicyBean = new BizTripPolicyBean();
        }
        if (bizTripPolicyBean.isAir_priv_flag() != null) {
            airRule = AirRuleType.getBooleanValue(bizTripPolicyBean.isAir_priv_flag()).getCode();
        }
        if (bizTripPolicyBean.getAir_other_flag() != null) {
            airOtherRule = AirRuleType.getBooleanValue(bizTripPolicyBean.getAir_other_flag()).getCode();
        }
        return new EmployeeAirRule(
                userId, airRule,airOtherRule, null, null,
                bizTripPolicyBean.isRule_limit_flag(), null, bizTripPolicyBean.getRule_id(),
                bizTripPolicyBean.isAir_verify_flag(), bizTripPolicyBean.isExceed_buy_flag(),
                bizTripPolicyBean.getUnemployee_air(), bizTripPolicyBean.getExceed_buy_type(), false, companyId
        );
    }

    public static EmployeeAirRule getEmployeeAirRuleV2(String userId, AirPolicyBean airPolicyBean, String companyId) {
        Integer airRule = null;
        Integer airOtherRule = null;
        if (airPolicyBean == null) {
            airPolicyBean = new AirPolicyBean();
        }
        if (airPolicyBean.getAir_priv_flag() != null) {
            airRule = AirRuleType.getBooleanValue(airPolicyBean.getAir_priv_flag()).getCode();
        }
        if (airPolicyBean.getAir_other_flag() != null) {
            airOtherRule = AirRuleType.getBooleanValue(airPolicyBean.getAir_other_flag()).getCode();
        }

        return new EmployeeAirRule(
                userId,
                airRule,
                airOtherRule,
                null,
                null,
                airPolicyBean.getRule_limit_flag(),
                null,
                airPolicyBean.getRule_id(),
                airPolicyBean.getAir_verify_flag(),
                false,
                airPolicyBean.getUnemployee_air(),
                airPolicyBean.getExceed_buy_type(),
                airPolicyBean.getAir_order_verify_flag(),
                companyId
        );
    }

    public static EmployeeTaxiRule getEmployeeTaxiRule(String userId, String companyId, CarPolicyBean carPolicyBean) {
        Integer taxiRule = null;
        if (carPolicyBean == null) {
            carPolicyBean = new CarPolicyBean();
        } else if (carPolicyBean.isCar_priv_flag() != null) {
            taxiRule = TaxiRuleType.getBooleanValue(carPolicyBean.isCar_priv_flag()).getCode();
        }
        return new EmployeeTaxiRule(
                companyId, userId, carPolicyBean.getRule_id(), false, null,
                taxiRule, null, null,null,
                carPolicyBean.isRule_limit_flag(), carPolicyBean.isExceed_buy_flag(), carPolicyBean.getExceed_buy_type(), carPolicyBean.getAllow_shuttle()
        );
    }

    public static EmployeeTaxiRule getEmployeeTaxiRuleV2(String userId, String companyId, CarPolicyBean carPolicyBean, String clientVersion, String clientType) {
        Integer taxiRule = null;
        if (carPolicyBean == null) {
            carPolicyBean = new CarPolicyBean();
        } else if (carPolicyBean.isCar_priv_flag() != null) {
            taxiRule = TaxiRuleType.getBooleanValue(carPolicyBean.isCar_priv_flag()).getCode();
        }
        logger.info("版本号是:" + clientVersion);
        logger.info("机型:" + clientType);
        if ("1.9.4".equals(clientVersion) && "iOS".equals(clientType)) {
            EmployeeTaxiRule employeeTaxiRule = new EmployeeTaxiRule();
            employeeTaxiRule.setCompanyId(companyId);
            employeeTaxiRule.setEmployeeId(userId);
            employeeTaxiRule.setManualTaxiRuleId(carPolicyBean.getRule_id());
            employeeTaxiRule.setTaxiAllowOther(false);
            employeeTaxiRule.setTaxiAllowOtype(null);
            employeeTaxiRule.setTaxiRule(taxiRule);
            employeeTaxiRule.setTaxiMonthFlag(null);
            employeeTaxiRule.setTaxiMonthCostLimit(null);
            employeeTaxiRule.setDefaultTaxiRuleId(null);
            employeeTaxiRule.setTaxiRuleFlag(carPolicyBean.isRule_limit_flag());
            employeeTaxiRule.setExceedBuyFlag(carPolicyBean.isExceed_buy_flag());
            Integer exceedBuyFlag = carPolicyBean.getExceed_buy_type();
            if (exceedBuyFlag != null) {
                if (exceedBuyFlag == 0) {
                    employeeTaxiRule.setExceedBuyType(1);
                } else if (exceedBuyFlag == 1) {
                    employeeTaxiRule.setExceedBuyType(2);
                } else if (exceedBuyFlag == 2) {
                    employeeTaxiRule.setExceedBuyType(3);
                }
            }
            employeeTaxiRule.setAllowShuttle(carPolicyBean.getAllow_shuttle());
            return employeeTaxiRule;
        }
        return new EmployeeTaxiRule(
                companyId, userId,
                carPolicyBean.getRule_id(), false, null,
                taxiRule, null, null, null,
                carPolicyBean.isRule_limit_flag(), carPolicyBean.isExceed_buy_flag(),
                carPolicyBean.getExceed_buy_type(), carPolicyBean.getAllow_shuttle()
        );
    }

    public static EmployeeDinnerRule getEmployeeDinerRuleV2(String userId, DinnerPolicyBean dinnerPolicyBean, String clientVersion, String clientType, String companyId) {
        Integer dinnerRule = null;
        if (dinnerPolicyBean == null) {
            dinnerPolicyBean = new DinnerPolicyBean();
        } else if (dinnerPolicyBean.getDinner_priv_flag() != null) {
            dinnerRule = DinnerRuleType.getBooleanValue(dinnerPolicyBean.getDinner_priv_flag()).getCode();
        }
        logger.info("版本号是:" + clientVersion);
        logger.info("机型:" + clientType);
        if ("1.9.4".equals(clientVersion) && "iOS".equals(clientType)) {
            EmployeeDinnerRule employeeDinnerRule = new EmployeeDinnerRule();
            employeeDinnerRule.setEmployee_id(userId);
            employeeDinnerRule.setDinner_rule(dinnerRule);
            employeeDinnerRule.setDinner_rule_flag(dinnerPolicyBean.getRule_limit_flag());
            employeeDinnerRule.setDinner_month_flag(null);
            employeeDinnerRule.setDinner_month_cost_limit(null);
            employeeDinnerRule.setDefault_dinner_rule_id(null);
            employeeDinnerRule.setManual_dinner_rule_id(dinnerPolicyBean.getRule_id());
            Integer exceedBuyFlag = dinnerPolicyBean.getExceed_buy_flag();
            if (exceedBuyFlag != null) {
                if (exceedBuyFlag == 0) {
                    employeeDinnerRule.setExceed_buy_flag(1);
                } else if (exceedBuyFlag == 1) {
                    employeeDinnerRule.setExceed_buy_flag(2);
                } else if (exceedBuyFlag == 2) {
                    employeeDinnerRule.setExceed_buy_flag(3);
                }
            }
            return employeeDinnerRule;
        }
        return new EmployeeDinnerRule(
                userId, dinnerRule, dinnerPolicyBean.getRule_limit_flag(), null, null,
                null, dinnerPolicyBean.getRule_id(), dinnerPolicyBean.getExceed_buy_flag(), companyId
        );
    }

    public static EmployeeDinnerRule getEmployeeDinerRule(String userId, DinnerPolicyBean dinnerPolicyBean, String companyId) {
        Integer dinnerRule = null;
        if (dinnerPolicyBean == null) {
            dinnerPolicyBean = new DinnerPolicyBean();
        } else if (dinnerPolicyBean.getDinner_priv_flag() != null) {
            dinnerRule = DinnerRuleType.getBooleanValue(dinnerPolicyBean.getDinner_priv_flag()).getCode();
        }
        return new EmployeeDinnerRule(
                userId, dinnerRule, dinnerPolicyBean.getRule_limit_flag(), null, null,
                null, dinnerPolicyBean.getRule_id(), dinnerPolicyBean.getExceed_buy_flag(), companyId
        );
    }

    public static EmployeeIntlAirRule getEmployeeIntlAirRuleV2(String userId, IntlAirPolicyBean intlAirPolicyBean, String companyId) {
        Integer airRule = null;
        Integer airOtherRule = null;
        if (intlAirPolicyBean == null) {
            intlAirPolicyBean = new IntlAirPolicyBean();
        }
        if (intlAirPolicyBean.getAir_priv_flag() != null) {
            airRule = AirRuleType.getBooleanValue(intlAirPolicyBean.getAir_priv_flag()).getCode();
        }
        if (intlAirPolicyBean.getAir_other_flag() != null) {
            airOtherRule =AirRuleType.getBooleanValue(intlAirPolicyBean.getAir_other_flag()).getCode();
        }
        return new EmployeeIntlAirRule(
                userId,
                airRule,
                airOtherRule,
                false,
                null,
                intlAirPolicyBean.getRule_limit_flag(),
                null, intlAirPolicyBean.getRule_id(),
                intlAirPolicyBean.getAir_verify_flag(),
                false,
                intlAirPolicyBean.getUnemployee_air(),
                intlAirPolicyBean.getExceed_buy_type(),
                intlAirPolicyBean.getIntl_air_order_verify_flag(),
                companyId
        );
    }

}
