package com.fenbeitong.saas.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.saas.entity.CostAttribution;
import com.fenbeitong.saas.api.model.dto.reason.ReasonConfig;
import com.fenbeitong.saas.core.common.base.SaasException;
import com.fenbeitong.saas.core.common.base.SaasResponseEntity;
import com.fenbeitong.saas.core.common.constant.GlobalResponseCode;
import com.fenbeitong.saas.core.common.constant.SaasFlowConstant;
import com.fenbeitong.saas.core.common.constant.SaasMessageConstant;
import com.fenbeitong.saas.core.contract.apply.*;
import com.fenbeitong.saas.core.contract.applyflow.ApplyFlowApplicateResponseContract;
import com.fenbeitong.saas.core.contract.applyflow.CompanyApplyFlowItemSetV2RequestContract;
import com.fenbeitong.saas.core.contract.applyflow.CompanyApplyFlowSetV2RequestContract;
import com.fenbeitong.saas.core.contract.applyflow.KvContract;
import com.fenbeitong.saas.core.contract.common.IdNameContract;
import com.fenbeitong.saas.core.contract.common.KeyValueItem;
import com.fenbeitong.saas.core.contract.common.PushContract;
import com.fenbeitong.saas.core.contract.message.MessageSaveContract;
import com.fenbeitong.saas.core.contract.message.inner.ApplyInfo;
import com.fenbeitong.saas.core.contract.messagesettings.ApplySetupContract;
import com.fenbeitong.saas.core.contract.user.EmployeeNameAndDeptContract;
import com.fenbeitong.saas.core.contract.user.UserContactContract;
import com.fenbeitong.saas.core.dao.common.adapter.ApplyAdapterMapper;
import com.fenbeitong.saas.core.dao.fenbeitong.HotelRuleMapper;
import com.fenbeitong.saas.core.language.CoreLanguage;
import com.fenbeitong.saas.core.language.LanguageUtils;
import com.fenbeitong.saas.core.language.MessageLanguageEnum;
import com.fenbeitong.saas.core.model.enums.ApplyFlowUserItemStatus;
import com.fenbeitong.saas.core.model.enums.CompanyApplyType;
import com.fenbeitong.saas.core.model.enums.CostAttributionCategory;
import com.fenbeitong.saas.core.model.enums.apply.*;
import com.fenbeitong.saas.core.model.enums.message.MessageType;
import com.fenbeitong.saas.core.model.enums.message.SenderType;
import com.fenbeitong.saas.core.model.enums.reason.ReasonType;
import com.fenbeitong.saas.core.model.enums.rule.HotelMergeType;
import com.fenbeitong.saas.core.model.enums.user.UserRole;
import com.fenbeitong.saas.core.model.fenbeitong.HotelRule;
import com.fenbeitong.saas.core.model.saas.*;
import com.fenbeitong.saas.core.service.*;
import com.fenbeitong.saas.core.utils.flow.FlowCheckUtil;
import com.fenbeitong.saas.core.utils.log.LogUtil;
import com.fenbeitong.saas.core.utils.notice.NoticeUtils;
import com.fenbeitong.saas.core.utils.notice.SmsContract;
import com.fenbeitong.saas.core.utils.notice.TemplateConstant;
import com.fenbeitong.saas.core.utils.tools.*;
import com.fenbeitong.saas.core.utils.url.ShortUrlUtils;
import com.fenbeitong.saasplus.api.model.dto.finance.CostSaveResult;
import com.fenbeitong.saasplus.api.model.dto.finance.OrderCostInfoReq;
import com.fenbeitong.saasplus.api.model.dto.rule.HotelConsumeMergeConfigContract;
import com.fenbeitong.saasplus.api.service.finance.IOrderCostService;
import com.fenbeitong.saasplus.api.service.setting.HotelSettingService;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.fenbeitong.usercenter.api.model.po.rule.EmployeeHotelRule;
import com.fenbeitong.usercenter.api.service.employee.IBaseEmployeeExtService;
import com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeHotelRuleService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.StrUtils;
import com.luastar.swift.http.constant.HttpConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.mockito.internal.util.collections.Sets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * Created by wangguoqinag on 2017/11/15.
 */
@Service
@Transactional(transactionManager = "dynamic", readOnly = false, rollbackFor = Exception.class)
public class ApplyCenterServiceImpl implements IApplyCenterService {

    private static final Logger logger = LoggerFactory.getLogger(ApplyCenterServiceImpl.class);

    private static final String URL_GET_ORDER_PAY = HostPropertyConfigTool.HOST_HYPERLOOP + "/internal/companies/orders/pay";

    /**
     * scala迁移，审批通过支付订单
     */
    private static final String URL_GET_HOTEL_ORDER_PAY = HostPropertyConfigTool.HOST_HOTEL_BIZ + "/internal/companies/orders/pay";

    /**
     * scala迁移，直接访问酒店项目
     */
    private static final String URL_GET_ORDER_STATUS = HostPropertyConfigTool.HOST_HYPERLOOP + "/internal/companies/orders/getstatus";
    private static final String URL_GET_HOTEL_ORDER_STATUS = HostPropertyConfigTool.HOST_HOTEL_BIZ + "/internal/companies/orders/getstatus";

    private static final String URL_GET_ORDER_CANCLE = HostPropertyConfigTool.HOST_HYPERLOOP + "/internal/companies/orders/cancel";

    /**
     * scala迁移，审批拒却取消订单
     */
    private static final String URL_GET_HOTEL_ORDER_CANCLE = HostPropertyConfigTool.HOST_HOTEL_BIZ + "/internal/companies/orders/cancel";

    private static final String URL_GET_AIR_ORDER_INFO = HostPropertyConfigTool.HOST_INTL_AIR_BIZ + "/internal/air/order/detail";

    private static final String URL_GET_INTL_AIR_ORDER_INFO = HostPropertyConfigTool.HOST_INTL_AIR_BIZ + "/internal/airticket/intl/order/detail/";

    private static final String URL_GET_HOTEL_ORDER_INFO = HostPropertyConfigTool.HOST_HOTEL_BIZ + "/internal/hotel/order_detail";

    private static final String URL_GET_TRAIN_ORDER_INFO = HostPropertyConfigTool.HOST_HYPERLOOP + "/internal/train/order/detail";

    private static final String URL_GET_DINNER_ORDER_INFO = HostPropertyConfigTool.HOST_DINNER_BIZ + "/internal/dinner/order/";

    private static final String URL_GET_AIR_MINPRICE_ORDER_INFO = HostPropertyConfigTool.HOST_INTL_AIR_BIZ + "/internal/air/order/min_price";

    private static final String URL_GET_INTL_AIR_MINPRICE_ORDER_INFO = HostPropertyConfigTool.HOST_INTL_AIR_BIZ + "/internal/intl/air/order/min_price";

    private static final String URL_GET_INTL_AIR_PAY = HostPropertyConfigTool.HOST_INTL_AIR_BIZ + "/internal/airticket/intl/order/supplier/create_pay";

    private static final String URL_GET_INTL_AIR_CANCLE = HostPropertyConfigTool.HOST_INTL_AIR_BIZ + "/internal/airticket/intl/order/cancel";

    private static final String URL_GET_AIR_CANCLE = HostPropertyConfigTool.HOST_INTL_AIR_BIZ + "/internal/order/saasCancel";

    //国内机票支付接口
    private static final String URL_GET_AIR_ORDER_PAY = HostPropertyConfigTool.HOST_INTL_AIR_BIZ + "/internal/companies/orders/pay";

    // 国内往返机票支付接口
    private static final String URL_GET_AIR_ROUND_ORDER_PAY = HostPropertyConfigTool.HOST_INTL_AIR_BIZ + "/internal/companies/orders/goBackPay";

    private static final String URL_GET_CITY_FULL_PATH =HostPropertyConfigTool.HOST_HARMONY + "/city/areas/%s/full_path";

    //火车支付接口
    private static final String URL_GET_TRAIN_ORDER_PAY = HostPropertyConfigTool.HOST_TRAIN_BIZ + "/internal/trains/order/during/approval";

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyOrderMapper applyMapper;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyOrderLogMapper applyLogMapper;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyTripInfoMapper applyTripMapper;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyTripGuestMapper applyGuestMapper;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyTripGuestExtMapper applyGuestExtMapper;

    @Autowired
    private IApplyFlowService applyFlowService;

    @Autowired
    IUserService userService;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyApproverMapMapper applyApproverMapMapper;

    @Autowired
    IPushService pushService;

    @Autowired
    IMessageService messageService;

    @Autowired
    private IApplyFlowV2Service applyFlowV2Service;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyOrderLogExtMapper applyOrderLogExtMapper;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyOrderExtMapper applyOrderExtMapper;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyTripInfoExtMapper applyTripInfoExtMapper;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyTripApplicateMapper applyTripApplicateMapper;

    @Autowired
    com.fenbeitong.saas.core.dao.dynamic.ApplyTripApplicateExtMapper applyTripApplicateExtMapper;

    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyFlowUserItemMapper applyFlowUserItemMapper;

    @Autowired
    private ApplyAdapterMapper applyAdapterMapper;

    @Autowired
    private ICustomRoleService customRoleService;

    @Autowired
    private IBaseOrganizationService iBaseOrganizationService;

    @Autowired
    private IApplyRefundChangeService iApplyRefundChangeService;

    @Autowired
    private IBaseEmployeeExtService iBaseEmployeeExtService;

    @Autowired
    private IApplyCouponService iApplyCouponService;

    @Autowired
    private ICustomReasonService customReasonService;

    @Autowired
    private IHotelCheckService iHotelCheckService;

    @Autowired
    private IRuleService ruleService;

    @Autowired
    private IBaseEmployeeHotelRuleService iBaseEmployeeHotelRuleService;

    @Autowired
    private HotelRuleMapper hotelRuleMapper;

    @Autowired
    private IApplyThirdService applyThirdService;

    @Autowired
    private com.fenbeitong.saas.core.dao.dynamic.ApplyCostAttributionMapper applyCostAttributionMapper;

    @Autowired
    private HotelSettingService hotelSettingService;

    @Autowired
    IMessageSetupService messageSetupService;

    @Autowired
    private IApplyV2Service applyV2Service;

    @Autowired
    private IOrderCostService orderCostService;

    public void setMessageService(IMessageService messageService) {
        this.messageService = messageService;
    }


    /**
     * 创建申请单
     *
     * @param applyContract
     * @return
     */
    @Override
    //@Transactional(value = "saas")
    public GlobalResponseCode create(String token, ApplyCenterContract applyContract, String userId, String companyId, String ip, String applyId, String clientHeadVersion) throws SaasException {
        FlowCheckUtil.check(companyId, clientHeadVersion);
        // 是否是对接订单审批公司
        boolean isDocking = applyThirdService.isApplyOrderDockingCompany(companyId);
        if (isDocking) {
            return applyThirdService.centerCreate(token, applyContract, userId, companyId, ip, applyId, clientHeadVersion);
        }
        Integer logId = null;
        //检查数据信息
        GlobalResponseCode code = checkApplyData(applyContract, userId, companyId, clientHeadVersion);
        if (code != GlobalResponseCode.Success) {
            return code;
        }
        ApplyOrderV2Contract applyOrderContract = applyContract.getApply();
        applyOrderContract.setEmployee_id(userId);
        applyOrderContract.setCompany_id(companyId);

        ApplyType applyType = ApplyType.valueOf(applyOrderContract.getType().intValue());

        CompanyApplyType companyApplyType = CompanyApplyType.valueOf(applyOrderContract.getFlow_type());
        if (companyApplyType == CompanyApplyType.Unknown) {
            companyApplyType = CompanyApplyType.Elastic;
        }
        applyOrderContract.setFlow_type(companyApplyType.getValue());

        //如果是提交审批保存审批流设置和审批单日志记录
        String approverId = applyOrderContract.getApprover_id();
        CompanyApplyFlowSetV2RequestContract flowRequest = applyOrderContract.getFlow();
        if (flowRequest == null) {
            flowRequest = new CompanyApplyFlowSetV2RequestContract();
        }
        flowRequest.setCompany_apply_type(companyApplyType.getValue());
        flowRequest.setApply_type(applyType.getValue());
        flowRequest.setBudget(applyOrderContract.getBudget());
        ApplyFlowApplicateResponseContract flowResponse = null;
        //判断审批流的类型
        String costAttributionId = null;
        if (applyOrderContract.getCost_attribution_category() != null) {
            if (StringUtils.isBlank(applyOrderContract.getCost_attribution_name()) || StringUtils.isBlank(applyOrderContract.getCost_attribution_id())) {
                return GlobalResponseCode.CostAttributionIdIsNull;
            }
        }
        Integer category = applyOrderContract.getCost_attribution_category();
        costAttributionId = applyOrderContract.getCost_attribution_id();
        //处理超规类型
        Integer exceedBuyType = null;
        if (ExceedBuyType.valueOf(applyOrderContract.getExceed_buy_type()) == ExceedBuyType.Unknown) {
            exceedBuyType = ExceedBuyType.Supernormal.getValue();
        } else {
            exceedBuyType = applyOrderContract.getExceed_buy_type();
        }
        Integer bussinessType = null;
        Integer tripTypeInfo = applyContract.getTrip_list().get(0).getTrip_type();
        if (applyOrderContract.getType() == ApplyType.ChaiLv.getValue()) {
            if (tripTypeInfo == BizType.IntlAir.getValue()) {
                bussinessType = ApplyType.IntlAir.getValue();
            } else if (tripTypeInfo == BizType.Air.getValue()) {
                bussinessType = ApplyType.Air.getValue();
            } else if (tripTypeInfo == BizType.Hotel.getValue()) {
                bussinessType = ApplyType.Hotel.getValue();
            } else if (tripTypeInfo == BizType.Train.getValue()) {
                bussinessType = ApplyType.Train.getValue();
            }
        } else {
            bussinessType = applyOrderContract.getType();
        }
        //查询应用审批流
        flowResponse = applyFlowV2Service.applicateFlow(SaasFlowConstant.SETTING_TYPE_ROUTE, flowRequest, applyId, userId, companyId, ip, category, costAttributionId, exceedBuyType, bussinessType, applyId);
        if (flowResponse != null && !StringTool.isNullOrEmpty(flowResponse.getApprover())) {
            approverId = flowResponse.getApprover();
        }
        //设置当前审批人
        logId = flowResponse.getNextLogId();
        if (StringUtils.isNotBlank(approverId)) {
            applyOrderContract.setApprover_id(approverId);
        }
        applyOrderContract.setLog_id(ObjUtils.toLong(flowResponse.getNextLogId()));
        if (companyApplyType == CompanyApplyType.Elastic) {
            if (approverId != null && approverId.length() > 0) {
                //检测审批人是否属于当前这个公司
                Boolean isApproverInCompany = userService.isUserInCompany(approverId, companyId);
                if (!isApproverInCompany) {
                    return GlobalResponseCode.ApplyApproverNotInCompany;
                }
                Date now = new Date();
                ApplyOrderLog log = new ApplyOrderLog();
                log.setApplyOrderId(applyId);
                log.setCreateTime(now);
                log.setIp(ip);
                log.setSponsorId(userId);
                log.setReceiverId(approverId);
                log.setCheckReason(null);
                log.setAction(ApplyLogAction.Submit.getValue());
                log.setSort(100);
                log.setRootApplyOrderId(applyId);
                applyLogMapper.insertSelective(log);

                ApplyOrderLog logApprove = new ApplyOrderLog();
                logApprove.setApplyOrderId(applyId);
                logApprove.setIp(ip);
                logApprove.setSponsorId(approverId);
                logApprove.setReceiverId("");
                logApprove.setCheckReason(null);
                logApprove.setAction(ApplyLogAction.Approval.getValue());
                logApprove.setSort(200);
                logApprove.setRootApplyOrderId(applyId);
                applyLogMapper.insertSelective(logApprove);
                logId = logApprove.getId();
                applyOrderContract.setLog_id(ObjUtils.toLong(logId));
            }
        }
        //整理数据
        clearApplyData(applyContract);
        //获取联系人数据
        List<UserContactContract> guestList = applyContract.getGuest_list();
        if (!CollectionUtils.isEmpty(guestList)) {
            for (UserContactContract guest : guestList) {
                guest.setDesc(JSONObject.toJSONString(guest));
            }
        }
        //待审核装填
        ApplyStatus applyState = ApplyStatus.PendingAudit;
        ApplyLogAction action = ApplyLogAction.Submit;
        Date now = new Date();

        List<ApplyTripInfoContract> tripList = applyContract.getTrip_list();
        Integer tripType = tripList.get(0).getType();
        ApplyOrder apply = applyOrderContract.ToModel();
        if (apply.getType() == ApplyType.ChaiLv.getValue()) {
            if (tripType == BizType.IntlAir.getValue()) {
                apply.setType(ApplyType.IntlAir.getValue());
            } else if (tripType == BizType.Air.getValue()) {
                apply.setType(ApplyType.Air.getValue());
            } else if (tripType == BizType.Hotel.getValue()) {
                apply.setType(ApplyType.Hotel.getValue());
            } else if (tripType == BizType.Train.getValue()) {
                apply.setType(ApplyType.Train.getValue());
            }
        }
        //处理超规类型
        apply.setExceedBuyType(exceedBuyType);
        if (applyOrderContract.getCost_attribution_category() != null) {
            if (StringUtils.isBlank(applyOrderContract.getCost_attribution_name()) || StringUtils.isBlank(applyOrderContract.getCost_attribution_id())) {
                return GlobalResponseCode.CostAttributionIdIsNull;
            }
            apply.setCostAttributionId(applyOrderContract.getCost_attribution_id());
            apply.setCostAttributionName(applyOrderContract.getCost_attribution_name());
            apply.setCostAttributionCategory(applyOrderContract.getCost_attribution_category());
        }
        EmployeeContract employee = iBaseOrganizationService.getEmployee(userId, companyId);
        //Employee employee = employeeMapper.selectByPrimaryKey(userId);
        if (employee != null) {
            apply.setApplicantName(employee.getName());
        }
        apply.setUpdateTime(now);
        apply.setCreateTime(now);

        //创建申请单
        apply.setId(applyId);
        apply.setBillNo(applyV2Service.getBillNo(applyOrderContract.getType().intValue(),companyId));
        apply.setRootApplyOrderId(applyId);
        applyOrderContract.setId(applyId);
        if (apply.getExceedBuyType() == ExceedBuyType.UnSupernormal.getValue()) {
            apply.setExceedBuyDesc("");
        }
        applyMapper.insertSelective(apply);
        //保存行程信息
        insertTripContractList(tripList, applyId, now, applyOrderContract.getCost_id());
        //保存同行人信息
        insertGuestContractList(guestList, applyId, now);
        //判断审批单是否是待审核状态
        String version = "1.9.96";
        //老版本toast，新版本弹框
        //if (applyState == ApplyStatus.PendingAudit && VersionTool.compare(clientHeadVersion, version) >= 0) {
        List<ApplyOrderCopyTo> applyOrderCopyTos = applyAdapterMapper.getApplyOrderCopyToExtMapper(companyId).queryApplyOrderCCByApplyOrderId(applyId);
        if (!CollectionUtils.isEmpty(applyOrderCopyTos)) {
            applyAdapterMapper.getApplyOrderCopyToExtMapper(companyId).deleteCCByApplyOrderId(applyId);
        }
        //获取抄送人数据
        CompanyApplyFlowSetV2RequestContract flow = applyOrderContract.getFlow();
        if (flow != null) {
            List<CompanyApplyFlowItemSetV2RequestContract> cc_list = flow.getCc_list();
            if (!CollectionUtils.isEmpty(cc_list)) {
                if (cc_list.size() > 5) {
                    throw new SaasException(GlobalResponseCode.FlowItemCCTooMuch);
                }
                int sort = 1;
                for (CompanyApplyFlowItemSetV2RequestContract ccFlowItem : cc_list) {
                    //保存抄送人
                    ApplyOrderCopyTo applyOrderCopyTo = new ApplyOrderCopyTo();
                    applyOrderCopyTo.setId(IDTool.CreateUniqueID());
                    applyOrderCopyTo.setApplyOrderId(applyId);
                    applyOrderCopyTo.setCreateTime(new Date());
                    applyOrderCopyTo.setItemId(ccFlowItem.getItem_id());
                    applyOrderCopyTo.setItemType(ccFlowItem.getItem_type());
                    applyOrderCopyTo.setSort(sort++);
                    applyOrderCopyTo.setRead(false);
                    if (ccFlowItem.getUser() == null || StringUtils.isBlank(ccFlowItem.getUser().getUser_id())) {
                        throw new SaasException(GlobalResponseCode.ApplyFlowItemNeedSetCCUser);
                    }
                    if (ccFlowItem.getUser() != null) {
                        applyOrderCopyTo.setUserId(ccFlowItem.getUser().getUser_id());
                    }
                    //待确认
                    applyOrderCopyTo.setIsDelete(ccFlowItem.getIs_delete());//1:不能删除 2:可以删除
                    applyAdapterMapper.getApplyOrderCopyToMapper(companyId).insert(applyOrderCopyTo);
                }
            }

        }
        //}
        if (applyState == ApplyStatus.PendingAudit) {
            insertApproverMap(applyId, applyOrderContract.getApprover_id(), now);
        }
        if (applyState == ApplyStatus.PendingAudit) {
            //push
            String orderId = tripList.get(0).getOrder_id();
            Integer orderType = tripList.get(0).getType();
            if (applyOrderContract.getType().intValue() == ApplyType.Dinner.getValue()) {
                postDinnerMessage(tripList.get(0), apply, apply.getEmployeeId(), apply.getApproverId(), action, logId, orderId, orderType, ApplyStatus.PendingAudit, "");
            } else {
                postMessage(apply, apply.getEmployeeId(), apply.getApproverId(), action, ApplyStatus.PendingAudit, logId, orderId, orderType, tripList.get(0), "");
            }
            //push 抄送人
            pushCCMessage(apply, apply.getEmployeeId(), action, ApplyStatus.PendingAudit, orderId, orderType);
        }
        if (apply.getType() == ApplyType.Air.getValue() || apply.getType() == ApplyType.IntlAir.getValue()) {
            AtomicInteger count = new AtomicInteger(1);
            ScheduledExecutorService executorService = new ScheduledThreadPoolExecutor(1, new BasicThreadFactory.Builder().namingPattern("order-apply-retry-schedule-pool-%d").build());
            executorService.scheduleAtFixedRate(() -> {
                int times = count.getAndIncrement();
                boolean isSuccess = false;
                try {
                    isSuccess = appendTripInfo(applyId, tripList.get(0).getOrder_id(), tripList.get(0).getType());
                    logger.info("第{}次获取最低价机票：applyId = {}, isSuccess = {}", times, applyId, isSuccess);
                } finally {
                    if (isSuccess || times >= 3) {
                        executorService.shutdown();
                    }
                }
            }, 0, 6, TimeUnit.SECONDS);
        }
        return GlobalResponseCode.Success;
    }

    /**
     * 获取机票的最低价信息
     *
     * @param applyId
     */
    @Override
    public boolean appendTripInfo(String applyId, String orderId, Integer type) {
        String airData = null;
        try {
            if (type == BizType.Air.getValue()) {
                logger.info("d获取国内机票最低价接口详情:" + URL_GET_AIR_MINPRICE_ORDER_INFO + "?order_id=" + orderId);
                airData = HttpTool.get(URL_GET_AIR_MINPRICE_ORDER_INFO + "?order_id=" + orderId);
                logger.info("获取国内机票最低价接口详情:" + airData);
            } else {
                logger.info("获取国际机票最低价接口详情:" + URL_GET_INTL_AIR_MINPRICE_ORDER_INFO + "?order_id=" + orderId);
                airData = HttpTool.get(URL_GET_INTL_AIR_MINPRICE_ORDER_INFO + "?order_id=" + orderId);
                logger.info("获取国际机票最低价接口详情:" + airData);
            }
            if (StringUtils.isNotBlank(airData)) {
                Map<String, Object> jo = JSONObject.parseObject(airData, HashMap.class);
                Map<String, Object> airDataMap = (Map<String, Object>) jo.get("data");
                //单张最低价
                Object price = airDataMap.get("price");
                //最低总价
                Object totalPrice = airDataMap.get("total_price");
                //折扣
                Object discount = airDataMap.get("discount");
                //航班号
                Object flightNo = airDataMap.get("flight_no");
                //航司
                Object airline = airDataMap.get("airline");
                //航司名称
                Object airlineName = airDataMap.get("airline_name");
                List<ApplyTripInfo> applyTripInfo = applyTripInfoExtMapper.queryAllTripListByApplyOrderId(applyId);
                if (CollectionUtils.isNotEmpty(applyTripInfo)) {
                    String tripContent = applyTripInfo.get(0).getTripContent();
                    JSONObject tripJsonObject = JSONObject.parseObject(tripContent);
                    JSONObject airInfo = new JSONObject();
                    airInfo.put("price", price);
                    airInfo.put("totalPrice", totalPrice);
                    airInfo.put("discount", discount);
                    airInfo.put("flightNo", flightNo);
                    airInfo.put("airline", airline);
                    airInfo.put("airlineName", airlineName);
                    tripJsonObject.put("air_info", airInfo);
                    ApplyTripInfo applyTripDetail = new ApplyTripInfo();
                    applyTripDetail.setId(applyTripInfo.get(0).getId());
                    applyTripDetail.setTripContent(tripJsonObject.toJSONString());
                    applyTripMapper.updateByPrimaryKeySelective(applyTripDetail);
                    return true;
                }
            }
        } catch (Exception e) {
            logger.error("获取机票最低价信息异常：" + e.getMessage());
        }
        return false;
    }

    /**
     * 采购和差旅订单审批抄送人通知
     *
     * @param apply
     * @param senderUserId
     * @param action
     * @param desStatus
     */
    @Override
    public void pushCCMessage(ApplyOrder apply, String senderUserId, ApplyLogAction action, ApplyStatus desStatus, String orderId, Integer orderType) {
        String applyId = apply.getId();
        ApplyType applyType = ApplyType.valueOf(apply.getType().intValue());
        List<ApplyOrderCopyTo> applyOrderCopyToList = applyAdapterMapper.getApplyOrderCopyToExtMapper(apply.getCompanyId()).queryApplyOrderCCByApplyOrderId(applyId);
        if (CollectionUtils.isEmpty(applyOrderCopyToList)) {
            return;
        }
        if (action.getValue() != ApplyLogAction.Submit.getValue() && desStatus.getValue() != ApplyStatus.Approved.getValue()) {
            return;
        }
        //提交审批时抄送通知类型判断
        if (action == ApplyLogAction.Submit) {
            Integer flowCcType = apply.getFlowCcType();
            if (flowCcType == null || flowCcType == 1) {
                return;
            }
        }
        //审批终审结束时抄送通知类型判断
        if (desStatus == ApplyStatus.Approved) {
            Integer flowCcType = apply.getFlowCcType();
            if (flowCcType == null || flowCcType == 2) {
                return;
            }
        }
        //业务类型
        Integer type = apply.getType();
        if (CollectionUtils.isEmpty(applyOrderCopyToList)) {
            return;
        }
        List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.queryApprovalInfo(applyId);
        List<String> sponsorIdList = applyOrderLogList.stream().filter(applyOrderLog -> !applyOrderLog.getAction().equals(ApplyLogAction.Submit.getValue())).map(applyOrderLog -> applyOrderLog.getSponsorId()).distinct().collect(Collectors.toList());
        List<String> applyCopyList = applyOrderCopyToList.stream().map(applyOrderCopyTo -> applyOrderCopyTo.getUserId()).distinct().collect(Collectors.toList());
        applyCopyList.removeAll(sponsorIdList);
        applyOrderCopyToList = applyOrderCopyToList.stream().filter(applyOrderCopyTo -> CollectionUtils.isNotEmpty(applyCopyList) && applyCopyList.contains(applyOrderCopyTo.getUserId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(applyOrderCopyToList)) {
            return;
        }
        String name = "";
        EmployeeContract employee = iBaseOrganizationService.getEmployee(senderUserId, apply.getCompanyId());
        if (employee == null || StringUtils.isBlank(employee.getName())) {
            name = apply.getApplicantName();
        } else {
            name = employee.getName();
        }
        Map<String, Map<String, String>> languageMap = Maps.newHashMap();
        String pushContent = "";
        String messageContent = "";
        //提交审批时抄送通知类型判断
        if (action == ApplyLogAction.Submit) {
            if (type == ApplyType.IntlAir.getValue() || type == ApplyType.Air.getValue()) {
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_AirSubmitOrderApplyNotice.getLanguageCode(), name));
                pushContent = StrUtils.formatString(CoreLanguage.Common_Message_AirSubmitOrderApplyNotice.getMessage(), name);
                messageContent = StrUtils.formatString(CoreLanguage.Common_Message_AirSubmitOrderApply.getMessage(), name);
            } else if (type == ApplyType.Hotel.getValue()) {
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_HotelSubmitOrderApplyNotice.getLanguageCode(), name));
                pushContent = StrUtils.formatString(CoreLanguage.Common_Message_HotelSubmitOrderApplyNotice.getMessage(), name);
                messageContent = StrUtils.formatString(CoreLanguage.Common_Message_HotelSubmitOrderApply.getMessage(), name);
            } else if (type == ApplyType.Train.getValue()) {
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_TrainSubmitOrderApplyNotice.getLanguageCode(), name));
                pushContent = StrUtils.formatString(CoreLanguage.Common_Message_TrainSubmitOrderApplyNotice.getMessage(), name);
                messageContent = StrUtils.formatString(CoreLanguage.Common_Message_TrainSubmitOrderApply.getMessage(), name);
            } else if (type == ApplyType.Dinner.getValue()) {
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_DinnerFeeSubmitOrderApplyNotice.getLanguageCode(), name));
                pushContent = StrUtils.formatString(CoreLanguage.Common_Message_DinnerFeeSubmitOrderApplyNotice.getMessage(), name);
                messageContent = StrUtils.formatString(CoreLanguage.Common_Message_DinnerFeeSubmitOrderApply.getMessage(), name);
            }
        }
        //审批终审结束时抄送通知类型判断
        if (desStatus == ApplyStatus.Approved) {
            if (type == ApplyType.IntlAir.getValue() || type == ApplyType.Air.getValue()) {
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_AirPassOrderApplyNotice.getLanguageCode(), name));
                pushContent = StrUtils.formatString(CoreLanguage.Common_Message_AirPassOrderApplyNotice.getMessage(), name);
                messageContent = StrUtils.formatString(CoreLanguage.Common_Message_AirSubmitOrderApply.getMessage(), name);
            } else if (type == ApplyType.Hotel.getValue()) {
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_HotelPassOrderApplyNotice.getLanguageCode(), name));
                pushContent = StrUtils.formatString(CoreLanguage.Common_Message_HotelPassOrderApplyNotice.getMessage(), name);
                messageContent = StrUtils.formatString(CoreLanguage.Common_Message_HotelSubmitOrderApply.getMessage(), name);
            } else if (type == ApplyType.Train.getValue()) {
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_TrainPassOrderApplyNotice.getLanguageCode(), name));
                pushContent = StrUtils.formatString(CoreLanguage.Common_Message_TrainPassOrderApplyNotice.getMessage(), name);
                messageContent = StrUtils.formatString(CoreLanguage.Common_Message_TrainSubmitOrderApply.getMessage(), name);
            } else if (type == ApplyType.Dinner.getValue()) {
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_DinnerFeePassOrderApplyNotice.getLanguageCode(), name));
                pushContent = StrUtils.formatString(CoreLanguage.Common_Message_DinnerFeePassOrderApplyNotice.getMessage(), name);
                messageContent = StrUtils.formatString(CoreLanguage.Common_Message_DinnerFeeSubmitOrderApply.getMessage(), name);
            } else if (type == ApplyType.Mall.getValue()) {
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_MallDetailNotice.getLanguageCode(), name));
                pushContent = StrUtils.formatString(CoreLanguage.Common_Message_MallDetailNotice.getMessage(), name);
                messageContent = StrUtils.formatString(CoreLanguage.Common_Message_MallDetail.getMessage(), name);
            }
        }
        for (ApplyOrderCopyTo applyOrderCopyTo : applyOrderCopyToList) {
            if (StringUtils.isBlank(applyOrderCopyTo.getUserId())) {
                continue;
            }
            String settingType = "2";
            Map<String, Object> msgInfo = new HashMap<>();
            msgInfo.put("myself", "false");
            msgInfo.put("view_type", "3");
            msgInfo.put("id", applyId);
            if (type == ApplyType.Mall.getValue()) {
                settingType = "3";
                msgInfo.put("order_type", "20");
            } else {
                if (action == ApplyLogAction.Submit && StringUtils.isNotBlank(orderId)) {
                    msgInfo.put("order_id", orderId);
                    msgInfo.put("order_type", ObjUtils.toString(orderType));
                } else {
                    List<ApplyTripApplicateExt> applyTripApplicates = applyTripApplicateExtMapper.queryApplyTripApplicateList(applyId);
                    msgInfo.put("order_id", applyTripApplicates.get(0).getOrderId());
                    msgInfo.put("order_type", ObjUtils.toString(applyTripApplicates.get(0).getType()));
                }
            }
            msgInfo.put("setting_type", settingType);
            String linkDetail = JSONObject.toJSONString(msgInfo);

            //存消息
            ApplyLogAction msgAction = action;
            if (msgAction == ApplyLogAction.Approve && desStatus != ApplyStatus.Approved) {
                //固定审批流中间审批完成
                msgAction = ApplyLogAction.Submit;
            }
            if (msgAction == ApplyLogAction.Skip) {
                if (desStatus == ApplyStatus.Approved) {
                    msgAction = ApplyLogAction.Approve;
                } else {
                    msgAction = ApplyLogAction.Submit;
                }
            }
            String messageTitle = genMessageTitle(msgAction, languageMap);

            MessageSaveContract messageSaveContract = new MessageSaveContract();
            messageSaveContract.setMessage_type(MessageType.Apply.getCode());
            messageSaveContract.setTitle(messageTitle);
            messageSaveContract.setContent(messageContent);
            messageSaveContract.setBiz_order(applyId);
            messageSaveContract.setLink(linkDetail);
            messageSaveContract.setSender(senderUserId);
            messageSaveContract.setSender_type(SenderType.Person.getCode());
            messageSaveContract.setReceiver(applyOrderCopyTo.getUserId());
            messageSaveContract.setCompany_id(apply.getCompanyId());

            ApplyInfo messageApplyInfo = new ApplyInfo();
            messageApplyInfo.setApply_type(applyType.getBizType().getCode());
            messageApplyInfo.setApply_time(DateTimeTool.fromDateTimeToString(new Date()));
            messageApplyInfo.setApply_msg(messageContent);
            messageApplyInfo.setMyself(false);
            messageApplyInfo.setView_type(3);
            if (type == ApplyType.Mall.getValue()) {
                messageApplyInfo.setOrder_id(null);
                messageApplyInfo.setOrder_type(ObjUtils.toString(BizType.Mall.getValue()));
                messageApplyInfo.setSetting_type(3);
            } else {
                messageApplyInfo.setSetting_type(2);
                if (msgAction == ApplyLogAction.Submit && StringUtils.isNotBlank(orderId)) {
                    messageApplyInfo.setOrder_id(orderId);
                    messageApplyInfo.setOrder_type(ObjUtils.toString(orderType));
                } else {
                    List<ApplyTripApplicateExt> applyTripApplicates = applyTripApplicateExtMapper.queryApplyTripApplicateList(applyId);
                    messageApplyInfo.setOrder_id(applyTripApplicates.get(0).getOrderId());
                    messageApplyInfo.setOrder_type(ObjUtils.toString(applyTripApplicates.get(0).getType()));
                }
            }
            messageSaveContract.setApply_info(messageApplyInfo);
            try {
                messageService.saveMessage(messageSaveContract);
            } catch (SaasException ex) {
                //不处理
            }
            PushContract pushInfo = new PushContract();
            pushInfo.setTitle(messageTitle);
            pushInfo.setContent(pushContent);
            pushInfo.setUser_id(applyOrderCopyTo.getUserId());
            pushInfo.setMsg_type("0");
            pushInfo.setDesc(pushContent);
            pushInfo.setAlert(true);
            pushInfo.setMsg(linkDetail);
            pushInfo.setTitleMultilingualMap(languageMap.get(MessageLanguageEnum.TITLE.getCode()));
            pushInfo.setCommentMultilingualMap(languageMap.get(MessageLanguageEnum.CONTENT.getCode()));
            pushService.push(pushInfo);
        }
    }

    private String getCCMessageTitle(String sendUserName, Integer type) {
        switch (type) {
            case 1:
                return StrUtils.formatString(CoreLanguage.Common_Message_TravelCheckMsg.getMessage(), sendUserName);
            case 2:
                return StrUtils.formatString(CoreLanguage.Common_Message_CarCheckMsg.getMessage(), sendUserName);
            case 3:
                return StrUtils.formatString(CoreLanguage.Common_Message_MallCheckMsg.getMessage(), sendUserName);
            case 4:
                return StrUtils.formatString(CoreLanguage.Common_Message_MallCheckMsg.getMessage(), sendUserName);
            case 5:
                return StrUtils.formatString(CoreLanguage.Common_Message_MallCheckMsg.getMessage(), sendUserName);
            default:
                logger.error("生成apply push title时发现没有处理的type:" + type + ",系统暂时只返回了type:" + type);
                return type + "";
        }
    }

    private String getCCMessageContent(String sendUserName, Integer type) {
        switch (type) {
            case 1:
                return StrUtils.formatString(CoreLanguage.Common_Message_DinnerNoticeMsg.getMessage(), sendUserName);
            case 2:
                return StrUtils.formatString(CoreLanguage.Common_Message_CarNoticeMsg.getMessage(), sendUserName);
            case 3:
                return StrUtils.formatString(CoreLanguage.Common_Message_MallNoticeMsg.getMessage(), sendUserName);
            default:
                logger.error("生成apply push title时发现没有处理的type:" + type + ",系统暂时只返回了type:" + type);
                return type + "";
        }
    }

    /**
     * 发送差旅push和通知
     *
     * @param apply
     * @param senderUserId
     * @param receiverUserId
     * @param action
     * @param desStatus
     * @param logId
     */
    //@Transactional
    private void postMessage(ApplyOrder apply, String senderUserId, String receiverUserId, ApplyLogAction action, ApplyStatus desStatus, Integer logId, String orderId, Integer orderType, ApplyTripInfoContract applyTripInfo, String comment) {
        String applyId = apply.getId();
        ApplyType applyType = ApplyType.valueOf(apply.getType().intValue());
        Integer type = applyTripInfo.getType();
        List<String> userIds = new ArrayList<>();
        userIds.add(senderUserId);
        userIds.add(receiverUserId);
        if (!userIds.contains(apply.getEmployeeId())) {
            userIds.add(apply.getEmployeeId());
        }
        List<IdNameContract> userNames = userService.getUserNameByIds(userIds, apply.getCompanyId());
        String employeeName = null;
        String senderUserName = null;
        String receiverUserName = null;
        for (IdNameContract userName : userNames) {
            if (senderUserId.equals(userName.getId())) {
                senderUserName = userName.getName();
                break;
            }
        }
        for (IdNameContract userName : userNames) {
            if (apply.getEmployeeId().equals(userName.getId())) {
                employeeName = userName.getName();
                break;
            }
        }
        if (StringUtils.isBlank(employeeName)) {
            employeeName = apply.getApplicantName();
        }
        for (IdNameContract userName : userNames) {
            if (receiverUserId.equals(userName.getId())) {
                receiverUserName = userName.getName();
                break;
            }
        }
        ApplyLogAction msgAction = action;
        String msgSender = senderUserName;
        if (StringUtils.isBlank(msgSender) && apply.getEmployeeId().equals(senderUserId)) {
            msgSender = apply.getApplicantName();
        }
        if (msgAction == ApplyLogAction.Approve && desStatus != ApplyStatus.Approved) {
            //固定审批流中间审批完成
            msgSender = employeeName;
            msgAction = ApplyLogAction.Submit;
        }
        if (msgAction == ApplyLogAction.Skip) {
            if (desStatus == ApplyStatus.Approved) {
                msgAction = ApplyLogAction.Approve;
            } else {
                msgAction = ApplyLogAction.Submit;
            }
        }
        //发短信
        EmployeeContract employee = iBaseOrganizationService.getEmployee(receiverUserId, apply.getCompanyId());
        //发短信功能
        if (msgAction == ApplyLogAction.Submit || msgAction == ApplyLogAction.Forward) {
            if (type == BizType.Mall.getValue()) {
                //发短信
                //Employee employee = employeeMapper.selectByPrimaryKey(receiverUserId);
                if (employee != null) {
                    String phoneNum = employee.getPhone_num();
                    Set<String> phoneSet = Sets.newSet();
                    phoneSet.add(phoneNum);
                    if (CollectionUtils.isNotEmpty(phoneSet)) {
                        SmsContract smsContract = null;
                        smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_MALL_SUMMIT_APPLY_CENTER_URL);//采购短信模板
                        Map<String, Object> param = new LinkedHashMap<>();
                        param.put("var1", employeeName);    //申请人姓名
                        param.put("var2", ObjUtils.toString(applyTripInfo.getEstimated_amount().setScale(2, BigDecimal.ROUND_HALF_UP)));
                        try {
                            logger.info("采购订单审批id加密前:" + HostPropertyConfigTool.HOST_H5 + "/process/approve/purchase?apply_id=" + applyId + "&user_id=" + receiverUserId + "秘钥:" + HostPropertyConfigTool.SIGN_KEY);
                            String applySign = EncryptUtil.encryptAES(applyId, HostPropertyConfigTool.SIGN_KEY);
                            String userIdSign = EncryptUtil.encryptAES(receiverUserId, HostPropertyConfigTool.SIGN_KEY);
                            String h5Url = HostPropertyConfigTool.HOST_H5 + "/process/approve/purchase?apply_id=" + URLEncoder.encode(applySign) + "&user_id=" + URLEncoder.encode(userIdSign);
                            h5Url = h5Url.replaceAll("\r|\n", "");
                            logger.info("采购订单审批id加密后:" + HostPropertyConfigTool.HOST_H5 + "/process/approve/purchase?apply_id=" + applySign + "&user_id=" + URLEncoder.encode(userIdSign));
                            String shortUrl = ShortUrlUtils.getShortUrl(h5Url);
                            param.put("var3", shortUrl.toString() + " ");
                        } catch (Exception e) {
                            logger.error(String.format("url:%s,异常信息：", "http://harmony.fenbeitong.com/harmony/weixin/shorturl") + e.getMessage());
                            throw new SaasException(GlobalResponseCode.ShortUrlError);
                        }
                        smsContract.setParam(param);
                        NoticeUtils.sendSms(smsContract);
                    }
                }
            } else {
                //发短信
                if (employee != null) {
                    String phoneNum = employee.getPhone_num();
                    Set<String> phoneSet = Sets.newSet();
                    phoneSet.add(phoneNum);
                    if (CollectionUtils.isNotEmpty(phoneSet)) {
                        SmsContract smsContract = null;
                        if (type == BizType.IntlAir.getValue() || type == BizType.Air.getValue()) {
                            smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_AIR_SUMMIT_APPLY_CENTER_URL);//机票短信模板
                        } else if (type == BizType.Hotel.getValue()) {
                            smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_HOTEL_SUMMIT_APPLY_CENTER_URL);//酒店短信模板
                        } else if (type == BizType.Train.getValue()) {
                            smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_TRAIN_SUMMIT_APPLY_CENTER_URL);//火车短信模板
                        }
                        Map<String, Object> param = new LinkedHashMap<>();
                        param.put("var1", employeeName);    //申请人姓名
                        if (type == BizType.IntlAir.getValue() || type == BizType.Air.getValue() || type == BizType.Train.getValue()) {
                            param.put("var2", applyTripInfo.getStart_city_name() + "-" + applyTripInfo.getArrival_city_name());
                        } else if (type == BizType.Hotel.getValue()) {
                            param.put("var2", applyTripInfo.getStart_city_name());
                        }
                        param.put("var3", ObjUtils.toString(applyTripInfo.getEstimated_amount().setScale(2, BigDecimal.ROUND_HALF_UP)));
                        try {
                            logger.info("事中审批id加密前:" + HostPropertyConfigTool.HOST_H5 + "/process/approve?apply_id=" + applyId + "&user_id=" + receiverUserId + "秘钥:" + HostPropertyConfigTool.SIGN_KEY);
                            String applySign = EncryptUtil.encryptAES(applyId, HostPropertyConfigTool.SIGN_KEY);
                            String userIdSign = EncryptUtil.encryptAES(receiverUserId, HostPropertyConfigTool.SIGN_KEY);
                            String h5Url = HostPropertyConfigTool.HOST_H5 + "/process/approve?apply_id=" + URLEncoder.encode(applySign) + "&user_id=" + URLEncoder.encode(userIdSign);
                            h5Url = h5Url.replaceAll("\r|\n", "");
                            logger.info("事中审批id加密后:" + HostPropertyConfigTool.HOST_H5 + "/process/approve?apply_id=" + applySign + "&user_id=" + URLEncoder.encode(userIdSign));
                            String shortUrl = ShortUrlUtils.getShortUrl(h5Url);
                            param.put("var4", shortUrl.toString() + " ");
                        } catch (Exception e) {
                            logger.error(String.format("url:%s,异常信息：", "http://harmony.fenbeitong.com/harmony/weixin/shorturl") + e.getMessage());
                            throw new SaasException(GlobalResponseCode.ShortUrlError);
                        }
                        smsContract.setParam(param);
                        NoticeUtils.sendSms(smsContract);
                    }
                }
            }
        } else if (msgAction == ApplyLogAction.Refuse) {
            //发短信
            if (type == BizType.Mall.getValue()) {
                if (employee != null) {
                    String phoneNum = employee.getPhone_num();
                    Set<String> phoneSet = Sets.newSet();
                    phoneSet.add(phoneNum);
                    if (CollectionUtils.isNotEmpty(phoneSet)) {
                        SmsContract smsContract = null;
                        smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_MALL_REPULSE_APPLY_CENTER_URL);
                        Map<String, Object> param = new LinkedHashMap<>();
                        param.put("var1", ObjUtils.toString(applyTripInfo.getEstimated_amount().setScale(2, BigDecimal.ROUND_HALF_UP)));
                        if (StringUtils.isNotBlank(comment)) {
                            param.put("var2", "（" + comment + "）");
                        } else {
                            param.put("var2", "");
                        }
                        smsContract.setParam(param);
                        NoticeUtils.sendSms(smsContract);
                    }
                }
            } else {
                if (employee != null) {
                    String phoneNum = employee.getPhone_num();
                    Set<String> phoneSet = Sets.newSet();
                    phoneSet.add(phoneNum);
                    if (CollectionUtils.isNotEmpty(phoneSet)) {
                        SmsContract smsContract = null;
                        if (type == BizType.IntlAir.getValue() || type == BizType.Air.getValue()) {
                            smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_AIR_REPULSE_APPLY_CENTER_URL);//机票短信模板
                        } else if (type == BizType.Hotel.getValue()) {
                            smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_HOTEL_REPULSE_APPLY_CENTER_URL);//酒店短信模板
                        } else if (type == BizType.Train.getValue()) {
                            smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_TRAIN_REPULSE_APPLY_CENTER_URL);//火车短信模板
                        }
                        Map<String, Object> param = new LinkedHashMap<>();
                        if (type == BizType.IntlAir.getValue() || type == BizType.Air.getValue() || type == BizType.Train.getValue()) {
                            param.put("var1", applyTripInfo.getStart_city_name() + "-" + applyTripInfo.getArrival_city_name());
                        } else if (type == BizType.Hotel.getValue()) {
                            param.put("var1", applyTripInfo.getStart_city_name());
                        }
                        if (StringUtils.isNotBlank(comment)) {
                            param.put("var2", "（" + comment + "）");
                        } else {
                            param.put("var2", "");
                        }
                        smsContract.setParam(param);
                        NoticeUtils.sendSms(smsContract);
                    }
                }
            }
        } else if (msgAction == ApplyLogAction.Approve && desStatus == ApplyStatus.Approved) {
            //发短信
            if (type == BizType.Mall.getValue()) {
                if (employee != null) {
                    String phoneNum = employee.getPhone_num();
                    Set<String> phoneSet = Sets.newSet();
                    phoneSet.add(phoneNum);
                    if (CollectionUtils.isNotEmpty(phoneSet)) {
                        SmsContract smsContract = null;
                        smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_MALL_APPROVED_APPLY_CENTER_URL);
                        Map<String, Object> param = new LinkedHashMap<>();
                        param.put("var1", ObjUtils.toString(applyTripInfo.getEstimated_amount().setScale(2, BigDecimal.ROUND_HALF_UP)));
                        smsContract.setParam(param);
                        NoticeUtils.sendSms(smsContract);
                    }
                }
            } else {
                if (employee != null) {
                    String phoneNum = employee.getPhone_num();
                    Set<String> phoneSet = Sets.newSet();
                    phoneSet.add(phoneNum);
                    if (CollectionUtils.isNotEmpty(phoneSet)) {
                        SmsContract smsContract = null;
                        if (type == BizType.IntlAir.getValue() || type == BizType.Air.getValue()) {
                            smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_AIR_APPROVED_APPLY_CENTER_URL);//机票短信模板
                        } else if (type == BizType.Hotel.getValue()) {
                            smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_HOTEL_APPROVED_APPLY_CENTER_URL);//酒店短信模板
                        } else if (type == BizType.Train.getValue()) {
                            smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_TRAIN_APPROVED_APPLY_CENTER_URL);//火车短信模板
                        }
                        Map<String, Object> param = new LinkedHashMap<>();
                        if (type == BizType.IntlAir.getValue() || type == BizType.Air.getValue() || type == BizType.Train.getValue()) {
                            param.put("var1", applyTripInfo.getStart_city_name() + "-" + applyTripInfo.getArrival_city_name());
                        } else if (type == BizType.Hotel.getValue()) {
                            param.put("var1", applyTripInfo.getStart_city_name());
                        }
                        smsContract.setParam(param);
                        NoticeUtils.sendSms(smsContract);
                    }
                }
            }
        }
        Map<String, Map<String, String>> languageMap = Maps.newHashMap();
        String messageContent = "";
        String pushContent = "";
        String messageTitle = genMessageTitle(msgAction, languageMap);
        if (msgAction == ApplyLogAction.Submit || msgAction == ApplyLogAction.Forward) {
            if (type == BizType.IntlAir.getValue() || type == BizType.Air.getValue()) {
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_AirOrderApprovalNotice.getLanguageCode(), msgSender));
                pushContent = StrUtils.formatString(CoreLanguage.Common_Message_AirOrderApprovalNotice.getMessage(), msgSender);
                messageContent = StrUtils.formatString(CoreLanguage.Common_Message_AirOrderApproval.getMessage(), msgSender);
            } else if (type == BizType.Hotel.getValue()) {
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_HotelOrderApprovalNotice.getLanguageCode(), msgSender));
                pushContent = StrUtils.formatString(CoreLanguage.Common_Message_HotelOrderApprovalNotice.getMessage(), msgSender);
                messageContent = StrUtils.formatString(CoreLanguage.Common_Message_HotelOrderApproval.getMessage(), msgSender);
            } else if (type == BizType.Train.getValue()) {
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_TrainOrderApprovalNotice.getLanguageCode(), msgSender));
                pushContent = StrUtils.formatString(CoreLanguage.Common_Message_TrainOrderApprovalNotice.getMessage(), msgSender);
                messageContent = StrUtils.formatString(CoreLanguage.Common_Message_TrainOrderApproval.getMessage(), msgSender);
            }
        } else if (msgAction == ApplyLogAction.Approve) {
            if (desStatus == ApplyStatus.Approved) {
                if (type == BizType.IntlAir.getValue() || type == BizType.Air.getValue()) {
                    languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_AirPassOrderApprovalNotice.getLanguageCode()));
                    pushContent = CoreLanguage.Common_Message_AirPassOrderApprovalNotice.getMessage();
                    messageContent = CoreLanguage.Common_Message_AirPassOrderApproval.getMessage();
                } else if (type == BizType.Hotel.getValue()) {
                    languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_HotelPassOrderApprovalNotice.getLanguageCode()));
                    pushContent = CoreLanguage.Common_Message_HotelPassOrderApprovalNotice.getMessage();
                    messageContent = CoreLanguage.Common_Message_HotelPassOrderApproval.getMessage();
                } else if (type == BizType.Train.getValue()) {
                    languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_TrainPassOrderApprovalNotice.getLanguageCode()));
                    pushContent = CoreLanguage.Common_Message_TrainPassOrderApprovalNotice.getMessage();
                    messageContent = CoreLanguage.Common_Message_TrainPassOrderApproval.getMessage();
                } else if (type == BizType.Mall.getValue()) {
                    Integer automaticOrder = SaasMessageConstant.IS_CHECKED_FALSE;
                    JSONObject jsonObject = JSON.parseObject(applyTripInfo.getContent());
                    if (jsonObject != null) {
                        automaticOrder = ObjUtils.toInteger(jsonObject.get("automatic_order"));
                        automaticOrder = automaticOrder == null ? SaasMessageConstant.IS_CHECKED_FALSE : automaticOrder;
                    }
                    if (automaticOrder == SaasMessageConstant.IS_CHECKED_TRUE) {
                        languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_MallPassOrderApprovalNotice.getLanguageCode()));
                        pushContent = CoreLanguage.Common_Message_MallPassOrderApprovalNotice.getMessage();
                        messageContent = CoreLanguage.Common_Message_MallPassOrderApproval.getMessage();
                    } else {
                        languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_MallFlowPassOrderNotice.getLanguageCode()));
                        pushContent = CoreLanguage.Common_Message_MallFlowPassOrderNotice.getMessage();
                        messageContent = CoreLanguage.Common_Message_MallFlowPassOrder.getMessage();
                    }
                }
            } else {
                if (type == BizType.IntlAir.getValue() || type == BizType.Air.getValue()) {
                    languageMap.put(MessageLanguageEnum.CONTENT.getCode(),
                            LanguageUtils.getFullMessage(CoreLanguage.Common_Message_AirOrderApprovalNotice.getLanguageCode(), msgSender));
                    pushContent = StrUtils.formatString(CoreLanguage.Common_Message_AirOrderApprovalNotice.getMessage(), msgSender);
                    messageContent = StrUtils.formatString(CoreLanguage.Common_Message_AirOrderApproval.getMessage(), msgSender);
                } else if (type == BizType.Hotel.getValue()) {
                    languageMap.put(MessageLanguageEnum.CONTENT.getCode(),
                            LanguageUtils.getFullMessage(CoreLanguage.Common_Message_HotelOrderApprovalNotice.getLanguageCode(), msgSender));
                    pushContent = StrUtils.formatString(CoreLanguage.Common_Message_HotelOrderApprovalNotice.getMessage(), msgSender);
                    messageContent = StrUtils.formatString(CoreLanguage.Common_Message_HotelOrderApproval.getMessage(), msgSender);
                } else if (type == BizType.Train.getValue()) {
                    languageMap.put(MessageLanguageEnum.CONTENT.getCode(),
                            LanguageUtils.getFullMessage(CoreLanguage.Common_Message_TrainOrderApprovalNotice.getLanguageCode(), msgSender));
                    pushContent = StrUtils.formatString(CoreLanguage.Common_Message_TrainOrderApprovalNotice.getMessage(), msgSender);
                    messageContent = StrUtils.formatString(CoreLanguage.Common_Message_TrainOrderApproval.getMessage(), msgSender);
                } else if (type == BizType.Mall.getValue()) {
                    languageMap.put(MessageLanguageEnum.CONTENT.getCode(),
                            LanguageUtils.getFullMessage(CoreLanguage.Common_Message_AirRefuseOrderApprovalNotice.getLanguageCode(), msgSender));
                    messageContent = StrUtils.formatString(CoreLanguage.Common_Message_MallPassOrderApproval.getMessage(), msgSender);
                    pushContent = StrUtils.formatString(CoreLanguage.Common_Message_AirRefuseOrderApprovalNotice.getMessage(), msgSender);
                }
            }
        } else if (msgAction == ApplyLogAction.Refuse) {
            if (type == BizType.IntlAir.getValue() || type == BizType.Air.getValue()) {
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_AirRefuseOrderApprovalNotice.getLanguageCode()));
                pushContent = CoreLanguage.Common_Message_AirRefuseOrderApprovalNotice.getMessage();
                messageContent = CoreLanguage.Common_Message_AirRefuseOrderApproval.getMessage();
            } else if (type == BizType.Hotel.getValue()) {
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_HotelRefuseOrderApprovalNotice.getLanguageCode()));
                pushContent = CoreLanguage.Common_Message_HotelRefuseOrderApprovalNotice.getMessage();
                messageContent = CoreLanguage.Common_Message_HotelRefuseOrderApproval.getMessage();
            } else if (type == BizType.Train.getValue()) {
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_TrainRefuseOrderApprovalNotice.getLanguageCode()));
                pushContent = CoreLanguage.Common_Message_TrainRefuseOrderApprovalNotice.getMessage();
                messageContent = CoreLanguage.Common_Message_TrainRefuseOrderApproval.getMessage();
            } else if (type == BizType.Mall.getValue()) {
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_MallFlowRejectNotice.getLanguageCode()));
                pushContent = CoreLanguage.Common_Message_MallFlowRejectNotice.getMessage();
                messageContent = CoreLanguage.Common_Message_MallFlowReject.getMessage();
            }
        } else if (msgAction == ApplyLogAction.Overtime) {
            if (type == BizType.IntlAir.getValue() || type == BizType.Air.getValue()) {
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_AirTimeoutOrderApprovalNotice.getLanguageCode(), msgSender));
                pushContent = StrUtils.formatString(CoreLanguage.Common_Message_AirTimeoutOrderApprovalNotice.getMessage(), msgSender);
                messageContent = CoreLanguage.Common_Message_AitTimeoutOrderApproval.getMessage();
            } else if (type == BizType.Hotel.getValue()) {
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_HotelTimeoutOrderApprovalNotice.getLanguageCode(), msgSender));
                pushContent = StrUtils.formatString(CoreLanguage.Common_Message_HotelTimeoutOrderApprovalNotice.getMessage(), msgSender);
                messageContent = CoreLanguage.Common_Message_HotelTimeoutOrderApproval.getMessage();
            } else if (type == BizType.Train.getValue()) {
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_TrainTimeoutOrderApprovalNotice.getLanguageCode(), msgSender));
                pushContent = StrUtils.formatString(CoreLanguage.Common_Message_TrainTimeoutOrderApprovalNotice.getMessage(), msgSender);
                messageContent = CoreLanguage.Common_Message_TrainTimeoutOrderApproval.getMessage();
            }
        }
        boolean myself = true;
        if (msgAction == ApplyLogAction.Submit || msgAction == ApplyLogAction.Forward) {
            myself = false;
        }
        String settingType = "2";
        String viewType = myself ? "1" : "2";
        Map<String, Object> msgData = new HashMap<>();
        msgData.put("myself", myself ? "true" : "false");
        msgData.put("view_type", viewType);
        msgData.put("id", applyId);
        if (type == BizType.Mall.getValue()) {
            settingType = "3";
            msgData.put("order_type", "20");
            msgData.put("order_id", orderId);
        } else {
            if (msgAction == ApplyLogAction.Submit && StringUtils.isNotBlank(orderId)) {
                msgData.put("order_id", orderId);
                msgData.put("order_type", ObjUtils.toString(orderType));
            } else {
                List<ApplyTripApplicateExt> applyTripApplicates = applyTripApplicateExtMapper.queryApplyTripApplicateList(applyId);
                msgData.put("order_id", applyTripApplicates.get(0).getOrderId());
                msgData.put("order_type", ObjUtils.toString(applyTripApplicates.get(0).getType()));
            }
        }
        msgData.put("setting_type", settingType);
        String linkInfo = JSONObject.toJSONString(msgData); //跳转信息
        //存消息
        MessageSaveContract messageSaveContract = new MessageSaveContract();
        messageSaveContract.setMessage_type(MessageType.Apply.getCode());
        messageSaveContract.setTitle(messageTitle);
        messageSaveContract.setContent(messageContent);
        messageSaveContract.setBiz_order(applyId);
        messageSaveContract.setLink(linkInfo);
        messageSaveContract.setSender(senderUserId);
        messageSaveContract.setSender_type(SenderType.Person.getCode());
        messageSaveContract.setReceiver(receiverUserId);
        messageSaveContract.setCompany_id(apply.getCompanyId());

        ApplyInfo messageApplyInfo = new ApplyInfo();
        messageApplyInfo.setApply_type(applyType.getBizType().getCode());
        messageApplyInfo.setApply_time(DateTimeTool.fromDateTimeToString(new Date()));
        messageApplyInfo.setApply_msg(messageContent);
        messageApplyInfo.setMyself(myself);
        messageApplyInfo.setView_type(myself ? 1 : 2);
        messageApplyInfo.setSetting_type(2);
        if (type == BizType.Mall.getValue()) {
            messageApplyInfo.setOrder_id(null);
            messageApplyInfo.setOrder_type(ObjUtils.toString(BizType.Mall.getValue()));
        } else {
            if (msgAction == ApplyLogAction.Submit && StringUtils.isNotBlank(orderId)) {
                messageApplyInfo.setOrder_id(orderId);
                messageApplyInfo.setOrder_type(ObjUtils.toString(orderType));
            } else {
                List<ApplyTripApplicateExt> applyTripApplicates = applyTripApplicateExtMapper.queryApplyTripApplicateList(applyId);
                messageApplyInfo.setOrder_id(applyTripApplicates.get(0).getOrderId());
                messageApplyInfo.setOrder_type(ObjUtils.toString(applyTripApplicates.get(0).getType()));
            }
        }
        if (logId != null && logId != 0) {
            messageApplyInfo.setLog_id(logId);
        }
        messageSaveContract.setApply_info(messageApplyInfo);

        try {
            messageService.saveMessage(messageSaveContract);
        } catch (SaasException ex) {
            //不处理
            logger.info("保存通知异常:" + ex.getMsg());
        }

        PushContract pushData = new PushContract();
        pushData.setTitle(messageTitle);
        pushData.setContent(pushContent);
        pushData.setUser_id(receiverUserId);
        pushData.setCompany_id(apply.getCompanyId());
        pushData.setMsg_type("0");
        pushData.setDesc(pushContent);
        pushData.setAlert(true);
        pushData.setMsg(linkInfo);
        pushData.setTitleMultilingualMap(languageMap.get(MessageLanguageEnum.TITLE.getCode()));
        pushData.setCommentMultilingualMap(languageMap.get(MessageLanguageEnum.CONTENT.getCode()));
        pushService.pushWithBudge(pushData);

        //审批节点（非终审节点）审核完成（通过或转交），单据流转至下一节点审批发送给申请人
        if ((msgAction == ApplyLogAction.Approve && desStatus != ApplyStatus.Approved) || msgAction == ApplyLogAction.Forward) {
            String pushContentInfo = "";
            Map<String, Map<String, String>> languageMapInfo = Maps.newHashMap();
            if (type == BizType.IntlAir.getValue() || type == BizType.Air.getValue()) {
                languageMapInfo.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_AirForwardOrderApprovalNotice.getLanguageCode(), receiverUserName));
                pushContentInfo = StrUtils.formatString(CoreLanguage.Common_Message_AirForwardOrderApprovalNotice.getMessage(), receiverUserName);
            } else if (type == BizType.Hotel.getValue()) {
                languageMapInfo.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_HotelForwardOrderApprovalNotice.getLanguageCode(), receiverUserName));
                pushContentInfo = StrUtils.formatString(CoreLanguage.Common_Message_HotelForwardOrderApprovalNotice.getMessage(), receiverUserName);
            } else if (type == BizType.Train.getValue()) {
                languageMapInfo.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_TrainForwardOrderApprovalNotice.getLanguageCode(), receiverUserName));
                pushContentInfo = StrUtils.formatString(CoreLanguage.Common_Message_TrainForwardOrderApprovalNotice.getMessage(), receiverUserName);
            } else if (type == BizType.Mall.getValue()) {
                languageMapInfo.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_MallStoreForwardNotice.getLanguageCode(), receiverUserName));
                pushContentInfo = StrUtils.formatString(CoreLanguage.Common_Message_MallStoreForwardNotice.getMessage(), receiverUserName);
            }
            PushContract sendData = new PushContract();
            sendData.setTitle(messageTitle);
            sendData.setContent(pushContentInfo);
            sendData.setUser_id(apply.getEmployeeId());
            sendData.setCompany_id(apply.getCompanyId());
            sendData.setMsg_type("0");
            sendData.setDesc(pushContentInfo);
            sendData.setAlert(true);
            sendData.setMsg(linkInfo);
            sendData.setTitleMultilingualMap(languageMap.get(MessageLanguageEnum.TITLE.getCode()));
            sendData.setCommentMultilingualMap(languageMapInfo.get(MessageLanguageEnum.CONTENT.getCode()));
            pushService.pushWithBudge(sendData);
        }
    }

    private String genMessageTitle(ApplyLogAction action, Map<String, Map<String, String>> languageMap) {
        switch (action) {
            case Submit:
            case ReSubmit:
                languageMap.put(MessageLanguageEnum.TITLE.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Title_WaitCheck.getLanguageCode()));
                return CoreLanguage.Common_Title_WaitCheck.getMessage();
            case Approve:
                languageMap.put(MessageLanguageEnum.TITLE.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Title_Pass.getLanguageCode()));
                return CoreLanguage.Common_Title_Pass.getMessage();
            case Forward:
                languageMap.put(MessageLanguageEnum.TITLE.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Title_WaitCheck.getLanguageCode()));
                return CoreLanguage.Common_Title_Forward.getMessage();
            case Refuse:
                languageMap.put(MessageLanguageEnum.TITLE.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Title_HasRefuse.getLanguageCode()));
                return CoreLanguage.Common_Title_HasRefuse.getMessage();
            case Revoke:
                languageMap.put(MessageLanguageEnum.TITLE.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Title_HasRevoke.getLanguageCode()));
                return CoreLanguage.Common_Title_HasRevoke.getMessage();
            case Overtime:
                languageMap.put(MessageLanguageEnum.TITLE.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Title_Timeout.getLanguageCode()));
                return CoreLanguage.Common_Title_Timeout.getMessage();
            default:
                logger.error("生成apply push title时发现没有处理的action:" + action.getValue() + ",系统暂时只返回了actionName:" + action.getDesc());
                return action.getDesc();
        }
    }

    private List<String> genBizNames(List<Integer> bizValues) {
        List<String> bizNames = new ArrayList<>();
        for (Integer biz : bizValues) {
            bizNames.add(BizType.valueOf(biz.intValue()).getDesc());
        }
        return bizNames;
    }

    /**
     * 保存同行人信息
     *
     * @param guestList
     * @param applyId
     * @param now
     */
    @Override
    public void insertGuestContractList(List<UserContactContract> guestList, String applyId, Date now) {
        if (guestList != null) {
            Set<String> phones=Sets.newSet();
            for (UserContactContract guestContract : guestList) {
                if (phones.contains(guestContract.getPhone())){
                    continue;
                }
                ApplyTripGuest guest = guestContract.ToModel();
                guest.setId(IDTool.CreateUniqueID());
                guest.setCreateTime(now);
                guest.setContactInfo(guestContract.getDesc());
                guest.setApplyOrderId(applyId);
                applyGuestMapper.insertSelective(guest);
                phones.add(guestContract.getPhone());
            }
        }
    }

    //@Transactional(value = "saas")
    @Override
    public void insertTripContractList(List<ApplyTripInfoContract> tripList, String applyId, Date now, String costId) {
        if (tripList != null) {
            for (ApplyTripInfoContract tripContract : tripList) {
                insertTripContract(tripContract, applyId, now, costId);
            }
        }
    }

    /**
     * 保存行程信息
     *
     * @param tripContract
     * @param applyId
     * @param now
     */
    //@Transactional(value = "saas")
    private void insertTripContract(ApplyTripInfoContract tripContract, String applyId, Date now, String costId) throws SaasException {
        ApplyTripInfo trip = new ApplyTripInfo();
        String applicateId = IDTool.CreateUniqueID();
        String id = IDTool.CreateUniqueID();
        trip.setId(id);
        trip.setApplyOrderId(applyId);
        trip.setCreateTime(now);
        trip.setType(tripContract.getType());
        trip.setArrivalCityId(tripContract.getArrival_city_id());
        trip.setStartCityId(tripContract.getStart_city_id());
        trip.setState(ApplyTripStatus.Available.getValue());
        trip.setStartTime(DateTimeTool.fromStringToDateTime(tripContract.getStart_time()));
        trip.setEndTime(DateTimeTool.fromStringToDateTime(tripContract.getEnd_time()));
        trip.setBackStartTime(DateTimeTool.fromStringToDateTime(tripContract.getBack_start_time()));
        trip.setBackEndTime(DateTimeTool.fromStringToDateTime(tripContract.getBack_end_time()));
        if (tripContract.getEstimated_amount() != null && tripContract.getEstimated_amount().compareTo(BigDecimal.valueOf(0)) != -1) {
            trip.setEstimatedAmount(tripContract.getEstimated_amount());
        }
        if (tripContract.getEnd_time() != null && tripContract.getEnd_time().length() > 0) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd KK:mm");
            try {
                trip.setEndTime(sdf.parse(tripContract.getEnd_time()));
            } catch (ParseException e) {
                logger.error("解析时间报错" + e.getMessage());
                e.printStackTrace();
            }
        }
        trip.setUpdateTime(now);
        trip.setStartCityName(tripContract.getStart_city_name());
        trip.setArrivalCityName(tripContract.getArrival_city_name());
        trip.setTitle(tripContract.getTitle());
        trip.setContent(tripContract.getContent());
        trip.setTripApplicateCurrentId(applicateId);
        trip.setTripType(tripContract.getTrip_type());
        if (tripContract != null && tripContract.getType() == 30) {
            if (StringUtils.isBlank(tripContract.getPerson_count()) || ObjUtils.toInteger(tripContract.getPerson_count()) < 1) {
                throw new SaasException(GlobalResponseCode.ParameterError);
            }
            if (StringUtils.isBlank(tripContract.getOrder_time())) {
                throw new SaasException(GlobalResponseCode.ParameterError);
            }
        }
        trip.setPersonCount(ObjUtils.toInteger(tripContract.getPerson_count(), -1));
        trip.setOrderTime(DateTimeTool.fromStringToDateTime(tripContract.getOrder_time()));
        JSONObject jo = new JSONObject();
        //机票航空公司名称
        if (StringUtils.isNotBlank(tripContract.getAir_airline_name())) {
            jo.put("air_airline_name", tripContract.getAir_airline_name());
        }
        //机票航班号
        if (StringUtils.isNotBlank(tripContract.getAir_flight_no())) {
            jo.put("air_flight_no", tripContract.getAir_flight_no());
        }
        //机票舱位名称
        if (StringUtils.isNotBlank(tripContract.getAir_seat_msg())) {
            jo.put("air_seat_msg", tripContract.getAir_seat_msg());
        }
        //返程机票航空公司名称
        if (StringUtils.isNotBlank(tripContract.getBack_air_airline_name())) {
            jo.put("back_air_airline_name", tripContract.getBack_air_airline_name());
        }
        //返程机票航班号
        if (StringUtils.isNotBlank(tripContract.getBack_air_flight_no())) {
            jo.put("back_air_flight_no", tripContract.getBack_air_flight_no());
        }
        //返程机票舱位名称
        if (StringUtils.isNotBlank(tripContract.getBack_air_seat_msg())) {
            jo.put("back_air_seat_msg", tripContract.getBack_air_seat_msg());
        }
        //机票折扣信息
        if (tripContract.getAir_discount() != null) {
            jo.put("air_discount", tripContract.getAir_discount());
        }
        //酒店床型名称
        if (StringUtils.isNotBlank(tripContract.getBed_type())) {
            jo.put("bed_type", tripContract.getBed_type());
        }
        //酒店预订间数
        if (tripContract.getRoom_count() != null) {
            jo.put("room_count", tripContract.getRoom_count());
        }
        if (StringUtils.isNotBlank(costId)) {
            jo.put("costId", costId);
        }
        if (StringUtils.isNotBlank(tripContract.getOrder_id())) {
            jo.put("orderId", tripContract.getOrder_id());
        }
        trip.setTripContent(jo.toJSONString());
        applyTripMapper.insert(trip);

        ApplyTripApplicate applicateModel = new ApplyTripApplicate();
        applicateModel.setId(applicateId);
        applicateModel.setOrderId(tripContract.getOrder_id());
        applicateModel.setAction(ApplyTripApplicateAction.Applicate.getValue());
        applicateModel.setApplyId(applyId);
        applicateModel.setCreateTime(now);
        applicateModel.setApplyTripId(id);
        applicateModel.setOrderProductType(1);
        applicateModel.setComment(null);
        applicateModel.setUseTime(now);
        applyTripApplicateMapper.insert(applicateModel);
    }

    //@Transactional(value = "saas")
    private void insertApproverMap(String applyId, String approverId, Date time) {
        ApplyApproverMapExample example = new ApplyApproverMapExample();
        example.createCriteria().andApplyIdEqualTo(applyId).andApproverIdEqualTo(approverId);
        List<ApplyApproverMap> list = applyApproverMapMapper.selectByExample(example);
        if (ObjUtils.isEmpty(list)) {
            ApplyApproverMap approverMap = new ApplyApproverMap();
            approverMap.setApplyId(applyId);
            approverMap.setApproverId(approverId);
            approverMap.setCreateTime(time);
            applyApproverMapMapper.insertSelective(approverMap);
        }
    }

   // @Transactional(value = "saas")
    private Integer writeLog(String id, Date createTime, String ip, String sponsorId, String receiverId, String checkReason, ApplyLogAction action, BigDecimal price) {
        //插入日志
        ApplyOrderLog log = new ApplyOrderLog();
        log.setApplyOrderId(id);
        log.setCreateTime(createTime);
        log.setIp(ip);
        log.setSponsorId(sponsorId);
        if (receiverId == null) {
            receiverId = "";
        }
        log.setReceiverId(receiverId);
        log.setCheckReason(checkReason);
        log.setAction(action.getValue());
        log.setPrice(price);
        log.setRootApplyOrderId(id);
        applyLogMapper.insert(log);
        return log.getId();
    }

    /**
     * 整理申请单数据
     *
     * @param applyContract
     */
    private void clearApplyData(ApplyCenterContract applyContract) {
        ApplyOrderV2Contract applyorderContract = applyContract.getApply();
        if (applyorderContract.getId() == null || applyorderContract.getId().length() == 0) {
            applyorderContract.setCheck_reason(null);
        }
        if (applyorderContract.getType() == ApplyType.Mall.getValue()) {
            //采购没有行程,也没有同行人
            applyContract.setGuest_list(null);
            applyContract.setTrip_list(null);
        }
        if (applyorderContract.getBudget() == null) {
            applyorderContract.setBudget(BigDecimal.valueOf(0));
        }
        if (applyorderContract.getType() == ApplyType.Taxi.getValue()) {
            //用车没有同行人
            applyContract.setGuest_list(null);
        }
        if (CollectionUtils.isEmpty(applyContract.getTrip_list())) {
            applyContract.setTrip_list(null);
        }
        if (CollectionUtils.isEmpty(applyContract.getGuest_list())) {
            applyContract.setGuest_list(null);
        }
        if (applyorderContract.getType() == ApplyType.Mall.getValue()) {
            applyContract.setTrip_list(null);
        } else {
            List<Tuple<Date, String>> cityIds = new ArrayList<>();
            List<Date> timeRange = new ArrayList<>();

            for (ApplyTripInfoContract trip : applyContract.getTrip_list()) {
                Date startTime = DateTimeTool.fromStringToDateTime(trip.getStart_time());
                //这里把时间从String转到Date,再转回String,是需要用Date格式式字段串的显示,如输入为2017-2-27,格式化后变为2017-02-27
                if (startTime != null) {
                    trip.setStart_time(DateTimeTool.fromDateTimeToString(startTime));
                    timeRange.add(startTime);
                }
                Date endTime = DateTimeTool.fromStringToDateTime(trip.getEnd_time());
                if (endTime != null) {
                    trip.setEnd_time(DateTimeTool.fromDateTimeToString(endTime));
                    timeRange.add(endTime);
                }
                if (trip.getType() == BizType.Taxi.getValue() || trip.getType() == BizType.Hotel.getValue()) {
                    trip.setArrival_city_id(null);
                }
                if (trip.getType() == BizType.Dinner.getValue()) {
                    trip.setStart_city_id("0");
                   /*Calendar c = Calendar.getInstance();
                    c.setTime(DateTimeTool.fromStringToDateTime(trip.getStart_time()));
                    c.add(Calendar.MINUTE, 20);
                    trip.setStart_time(DateTimeTool.fromDateTimeToString(c.getTime()));*/
                }
                String tripCityId = trip.getStart_city_id();
                if (trip.getArrival_city_id() != null) {
                    tripCityId += "-" + trip.getArrival_city_id();
                }
                cityIds.add(new Tuple<>(startTime, tripCityId));
            }

            //city_range格式:1000001-200002,2000002,300003-400004 意即:北京-上海,上海,天津-重庆
            cityIds.sort((m, n) -> (int) (m.getItem1().getTime() - n.getItem1().getTime()));
            List<String> cityIdArr = new ArrayList<>();
            for (Tuple<Date, String> cityId : cityIds) {
                cityIdArr.add(cityId.getItem2());
            }
            String cityIdVal = String.join(",", cityIdArr);
            applyorderContract.setCity_range(cityIdVal);

            //将所有出现过的时间正序排列,取最小值和最大值,重新写入timeRange字段,如果最小值=最大值,则只取最小值
            timeRange.sort((m, n) -> (int) (m.getTime() - n.getTime()));
            Date minTime = timeRange.get(0);
            Date maxTime = timeRange.get(timeRange.size() - 1);
            String desTimeRange = DateTimeTool.fromDateToString(minTime);
            if (maxTime.getTime() != minTime.getTime()) {
                desTimeRange += "," + DateTimeTool.fromDateToString(maxTime);
            }
            applyorderContract.setTime_range(desTimeRange);
        }
        if (applyorderContract.getApprover_id() == null) {
            applyorderContract.setApprover_id("");
        }

    }

    /**
     * 检查数据有效性
     *
     * @param applyCenterContract
     * @param employeeId
     * @param companyId
     * @param clientVersion
     * @return
     */
    private GlobalResponseCode checkApplyData(ApplyCenterContract applyCenterContract, String employeeId, String companyId, String clientVersion) {
        //检查申请单
        ApplyOrderV2Contract apply = applyCenterContract.getApply();
        if (apply == null) {
            return GlobalResponseCode.ApplyIsNull;
        }
        if (apply.getState() == null) {
            return GlobalResponseCode.ParameterError;
        }
        if (apply.getState().intValue() != ApplyStatus.PendingAudit.getValue()) {
            //如果申请单的状态不是草稿,就置为提交
            return GlobalResponseCode.ParameterError;
        }
        if (apply.getType() == null
                || ApplyType.valueOf(apply.getType()) == ApplyType.Unknown) {
            return GlobalResponseCode.ApplyTypeInvalid;
        }
        //预计总金额
        if (apply.getBudget() != null && apply.getBudget().compareTo(BigDecimal.valueOf(0)) == -1) {
            return GlobalResponseCode.ApplyBudgetInvalid;
        }
        //审批类型 1.弹性审批流 2.固定审批流 3.分条件审批流
        CompanyApplyType applyType = CompanyApplyType.valueOf(apply.getFlow_type());
        if (applyType.getValue() == CompanyApplyType.Unknown.getValue()) {
            return GlobalResponseCode.ApplyFlowTypeError;
        }
        if (applyType.getValue() == CompanyApplyType.Elastic.getValue() && apply.getState() == ApplyStatus.PendingAudit.getValue() && StringUtils.isBlank(apply.getApprover_id())) {
            //申请单必须指定一个审批人
            return GlobalResponseCode.ApplyApproveIdInvalid;
        }
        if (applyType.getValue() == CompanyApplyType.Elastic.getValue() && employeeId.equals(apply.getApprover_id())) {
            //审批人不能是自己
            return GlobalResponseCode.ApplyApproverCannotBeSelf;
        }
        if (apply.getOvertime() == null || DateTimeTool.fromStringToDateTime(apply.getOvertime()) == null) {
            return GlobalResponseCode.OverTimeInvalidError;
        }
        if (ExceedBuyType.valueOf(apply.getExceed_buy_type()) == ExceedBuyType.Unknown) {
            return GlobalResponseCode.ExceedBuyTypeIsNull;
        }
        if (ExceedBuyType.valueOf(apply.getExceed_buy_type()) == ExceedBuyType.Supernormal) {
            // 版本校验
            if (VersionTool.greaterThanOrEqualTo(clientVersion, "3.8.0")) {
                ReasonType reasonType = null;
                if (ApplyType.valueOf(apply.getType()) == ApplyType.Air) {
                    reasonType = ReasonType.APPLY_EXCEED_ORDER_AIR;
                } else if (ApplyType.valueOf(apply.getType()) == ApplyType.IntlAir) {
                    reasonType = ReasonType.APPLY_EXCEED_ORDER_INTL_AIR;
                } else if (ApplyType.valueOf(apply.getType()) == ApplyType.Hotel) {
                    reasonType = ReasonType.APPLY_EXCEED_ORDER_HOTEL;
                } else if (ApplyType.valueOf(apply.getType()) == ApplyType.Train) {
                    reasonType = ReasonType.APPLY_EXCEED_ORDER_TRAIN;
                }
                ReasonConfig reasonConfig = customReasonService.queryReasonConfigWithoutItems(companyId, reasonType);
                if (reasonConfig.getReason() == SaasMessageConstant.IS_CHECKED_TRUE) {
                    if (StringUtils.isBlank(apply.getApply_reason())) {
                        return GlobalResponseCode.ApplyReasonIsNull;
                    }
                    if (reasonConfig.getReason_desc() == SaasMessageConstant.IS_CHECKED_TRUE) {
                        if (StringUtils.isBlank(apply.getApply_reason_desc())) {
                            return GlobalResponseCode.ApplyReasonDescIsNull;
                        }
                    }
                }
            }
            if (StringUtils.isBlank(apply.getExceed_buy_desc())) {
                return GlobalResponseCode.ExceedBuyDescIsNull;
            }
        }
        if (StringUtils.isBlank(apply.getApply_reason_desc()) && apply.getApply_reason() != null && apply.getApply_reason().length() > 200) {
            //申请事由不能超过50字
            return GlobalResponseCode.ApplyReasonInvalid;
        }
        if (StringUtils.isNotBlank(apply.getApply_reason_desc()) && apply.getApply_reason_desc().length() > 200) {
            return GlobalResponseCode.ApplyReasonInvalid;
        }
        //检查行程
        if (apply.getType() != ApplyType.Mall.getValue()) {
            List<ApplyTripInfoContract> trips = applyCenterContract.getTrip_list();
            if (CollectionUtils.isEmpty(trips)) {
                //差旅和用车必须要有行程
                return GlobalResponseCode.ApplyTripIsNull;
            }
            if (trips.size() > 30) {
                //行程数据不超过30个
                return GlobalResponseCode.ApplyTripTooMuch;
            }
            BigDecimal tripTotalMoney = BigDecimal.valueOf(0);
            for (ApplyTripInfoContract trip : trips) {
                tripTotalMoney = tripTotalMoney.add(trip.getEstimated_amount());
                if (trip.getType() == null) {
                    return GlobalResponseCode.ApplyTripTypeInvalid;
                }
                //检测行程单类型
                if (apply.getType() == ApplyType.ChaiLv.getValue()) {
                    if (trip.getType() != BizType.Air.getValue()
                            && trip.getType() != BizType.Hotel.getValue()
                            && trip.getType() != BizType.Train.getValue()
                            && trip.getType() != BizType.IntlAir.getValue()) {
                        return GlobalResponseCode.ApplyTripNotTravel;
                    }
                } else if (apply.getType() == ApplyType.Taxi.getValue()) {
                    if (trip.getType() != BizType.Taxi.getValue()) {
                        return GlobalResponseCode.ApplyTripNotTaxi;
                    }
                }
                Date startTime = null;
                //检测时间
                if (trip.getStart_time() == null) {
                    return GlobalResponseCode.ApplyTripStartDateInvalid;
                }
                //检测地点
                if (apply.getType() != ApplyType.Dinner.getValue()) {
                    if (trip.getStart_city_name() == null || trip.getStart_city_name().length() == 0) {
                        return GlobalResponseCode.ApplyTripStartCityInvalid;
                    }
                }
                if (trip.getArrival_city_name() == null || trip.getArrival_city_name().length() == 0) {
                    if (trip.getType() == BizType.Air.getValue() || trip.getType() == BizType.Train.getValue()) {
                        return GlobalResponseCode.ApplyTripArrivalCityInvalid;
                    }
                }
                if (TripType.valueOf(trip.getTrip_type()) == TripType.Unknown) {
                    trip.setTrip_type(TripType.oneWay.getValue());
                }
                if (trip.getType() == BizType.Air.getValue() && StringUtils.isBlank(trip.getEnd_time())) {
                    return GlobalResponseCode.AirInvalidError;
                }
                if (trip.getType() == BizType.IntlAir.getValue()) {
                    if (StringUtils.isBlank(trip.getAir_airline_name()) || StringUtils.isBlank(trip.getAir_flight_no()) || StringUtils.isBlank(trip.getAir_seat_msg())) {
                        return GlobalResponseCode.IntlAirInvalidError;
                    }
                    if (StringUtils.isBlank(trip.getEnd_time())) {
                        return GlobalResponseCode.IntlAirInvalidError;
                    }
                    if (trip.getTrip_type() == TripType.goAndBack.getValue()) {
                        if (StringUtils.isBlank(trip.getBack_start_time()) || StringUtils.isBlank(trip.getBack_end_time())) {
                            return GlobalResponseCode.IntlAirInvalidError;
                        }
                    }
                }
            }
            BigDecimal budget = apply.getBudget().divide(ObjUtils.toBigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
            if (tripTotalMoney.setScale(2, BigDecimal.ROUND_HALF_UP).compareTo(budget) != 0) {
                return GlobalResponseCode.ApplyTripBudgetError;
            }
        }
        return GlobalResponseCode.Success;
    }

    private ApplyV2Contract getApplyByIdAndUserIdAndCompanyId(String applyId, String userId, String companyId, UserRole userRole, String token, String clientVersion) throws SaasException {
        ApplyOrder applyModel = getApplyOrderModelByIdAndUserIdAndCompanyId(applyId, userId, companyId, userRole);
        if (applyModel == null) {
            applyModel = applyOrderExtMapper.getByIdAndApproverIdAndCompanyId(applyId, userId, companyId);
        }
        if (applyModel == null) {
            List<ApplyOrder> ccApplyOrderCountByApplyIdList = applyOrderExtMapper.getCCApplyOrderCountByApplyId(userId, companyId, 0);
            if (CollectionUtils.isEmpty(ccApplyOrderCountByApplyIdList)) {
                return null;
            }
            List<String> cclist = Lists.newArrayList();
            for (ApplyOrder applyOrder : ccApplyOrderCountByApplyIdList) {
                cclist.add(applyOrder.getId());
            }
            if (cclist.contains(applyId)) {
                applyModel = applyMapper.selectByPrimaryKey(applyId);
            } else {
                return null;
            }
        }
        if (ObjUtils.isNotEmpty(applyModel.getDeleteStatus())&&applyModel.getDeleteStatus()==1){
            throw new SaasException(GlobalResponseCode.ApplyOrderIsDelete);
        }
        ApplyOrderV2Contract apply = ApplyOrderV2Contract.FromModel(applyModel);
        // 4.7.1 配置项目编码显示
        ApplySetupContract applySetupContract = messageSetupService.queryCompanyApplyCostAttributionConfig(companyId);
        if (applySetupContract != null
                && apply.getCost_attribution_category() == CostAttributionCategory.CostCenter.getKey()
                && SaasMessageConstant.IS_CHECKED_TRUE == applySetupContract.getApply_show_project_code()) {
            apply.setCost_attribution_code(applyModel.getCostAttributionCode());
        }
        if (StringUtils.isNotBlank(apply.getExceed_buy_desc())) {
            String[] exceedBugDescs = apply.getExceed_buy_desc().split("\n");
            List<String> exceedBuyDescList = Arrays.asList(exceedBugDescs);
            apply.setExceed_buy_desc_list(exceedBuyDescList);
        }
        List<String> skippedApprverIds = new ArrayList<>();//固定审批流跳过的审批人
        List<ApplyFlowUserItem> applyFlowUserItems = applyFlowService.getFlowItemsByApplyId(applyId);
        if (applyModel.getFlowType() == CompanyApplyType.Elastic.getValue()) {
            apply.setNode_status(1);
        } else {
            FindNextApproverIdAndStatusResult findNextApproverIdAndStatusResult = findStatusInFlow(applyModel, applyFlowUserItems, skippedApprverIds, true);
            //最后审批人
            if (findNextApproverIdAndStatusResult != null && findNextApproverIdAndStatusResult.getApplyStatus() == 2) {
                apply.setNode_status(1);
            } else {
                apply.setNode_status(2);
            }
        }
        if (apply.getType().intValue() == ApplyType.Dinner.getValue() && VersionTool.compare(clientVersion, "1.9.4") < 0) {
            throw new SaasException(GlobalResponseCode.ApplyCenterAlert);
        }
        //返回当前时间戳
        apply.setCurrent_time(DateTimeTool.fromDateTimeToString(new Date()));
        ApplyV2Contract applyContract = new ApplyV2Contract();
        applyContract.setApply(apply);
        if (apply.getType() != ApplyType.Mall.getValue()) {
            //不是采购申请,读取行程
            List<ApplyTripInfoContract> tripList = getTripListByApplyOrderId(applyId, applyModel);
            applyContract.setTrip_list(tripList);
        }
        if (apply.getType() != ApplyType.Taxi.getValue() || apply.getType() != ApplyType.Mall.getValue() || apply.getType() != ApplyType.Dinner.getValue()) {
            List<ApplyTripInfoContract> applyTripInfoContractList = applyContract.getTrip_list();
            List<UserContactContract> guestList = composeGuestList(applyId, companyId, apply.getType(), applyTripInfoContractList, clientVersion);
            applyContract.setGuest_list(guestList);
        }
        return applyContract;
    }

    /**
     * 组装出行人信息
     *
     * @param applyId
     * @param companyId
     * @param applyType
     * @param applyTripInfoContractList
     * @param clientVersion
     * @return
     */
    @Override
    public List<UserContactContract> composeGuestList(String applyId, String companyId, Integer applyType, List<ApplyTripInfoContract> applyTripInfoContractList, String clientVersion) {
        //差旅有同行人信息
        List<UserContactContract> guestList = getGuestListByApplyOrderId(applyId);
        //酒店多人入住
        if (applyType==ApplyType.Hotel.getValue()&&ObjUtils.isNotEmpty(guestList)){
            //只有有同住人的才会校验金额
            Set<Integer> groupSet=Sets.newSet();
            guestList.forEach(v->{
                if (ObjUtils.isNotEmpty(v.getOccupier_type())&&v.getOccupier_type()==2){
                    if (ObjUtils.isNotEmpty(v.getRoom_group())){
                        groupSet.add(v.getRoom_group());
                    }
                }
            });
            //小于3.9.2但是包含同住人
            if (ObjUtils.isNotBlank(clientVersion) && VersionTool.compare(clientVersion, "3.9.3") < 0&&groupSet.size()>0){
                throw new SaasException(GlobalResponseCode.ApplyCenterAlert);
            }
            ApplyTripInfoContract applyTripInfoContract = applyTripInfoContractList.get(0);
            Integer city_level = ruleService.getCityLevelByAreaId(companyId,applyTripInfoContract.getStart_city_id());
            logger.info("酒店城市级别：" + city_level);
            String startTime=applyTripInfoContract.getStart_time();
            String cityUrl=String.format(URL_GET_CITY_FULL_PATH,applyTripInfoContract.getStart_city_id());
            String cityList = null;
            try {
                logger.info("获取城市全路径:" + cityUrl);
                cityList = HttpTool.get(cityUrl);
                logger.info("获取城市全路径:" + cityList);
            } catch (Exception e) {
                logger.error("获取城市全路径错误："+e);
                throw new SaasException(GlobalResponseCode.InnerError);
            }
            //不限制组
            Set<Integer> noLimitGroup=Sets.newSet();
            for(UserContactContract userContactContract:guestList){
                //多人分组，需要查询单人酒店规则价格限制
                if (ObjUtils.isNotEmpty(userContactContract.getRoom_group())&&groupSet.contains(userContactContract.getRoom_group())){
                    EmployeeHotelRule employeeHotelRule = iBaseEmployeeHotelRuleService.queryEmployeeHotelRuleByPrimaryKey(userContactContract.getId(), companyId);
                    if (employeeHotelRule == null) {
                        logger.info("未查询到员工酒店规则应用");
                        throw new SaasException(GlobalResponseCode.OrderCheckRuleNotExist);
                    }
                    String hotelRuleId = ObjUtils.ifNull(employeeHotelRule.getManual_hotel_rule_id(), employeeHotelRule.getDefault_hotel_rule_id());
                    HotelRule occupantHotelRule = hotelRuleMapper.selectByPrimaryKey(hotelRuleId);
                    if (occupantHotelRule == null) {
                        noLimitGroup.add(userContactContract.getRoom_group());
                        continue;
                    }
                    Map cityMap=JsonUtils.toObj(cityList,Map.class);
                    List<String> codeList= (List<String>) cityMap.get("data");
                    BigDecimal priceLimit= iHotelCheckService.getHotelRuleLimitPrice(occupantHotelRule,city_level,codeList,startTime);
                    if (priceLimit.compareTo(new BigDecimal(-1))==0){
                        noLimitGroup.add(userContactContract.getRoom_group());
                    }
                    userContactContract.setPrice_limit(priceLimit);
                }
            }
            if (ObjUtils.isNotEmpty(guestList.get(0).getRoom_group())){
                HotelConsumeMergeConfigContract mergeConfigContract= hotelSettingService.queryTravelSetting(companyId);
                Integer consumeMerge= mergeConfigContract.getUsersHotelConsumeMerge();
                Map<Integer, List<UserContactContract>> groupMap = guestList.stream()
                        .collect(Collectors.groupingBy(UserContactContract::getRoom_group));
                groupMap.forEach((k,v)->{
                    if (noLimitGroup.contains(k)||v.size()==1||!groupSet.contains(k)){//不限制组的都不传价格限制,兼容前端错误，存在重复数据
                        v.forEach(noLimitUser->{
                            noLimitUser.setPrice_limit(null);
                        });
                    }else {
                        List<BigDecimal> limitList=Lists.newArrayList();
                        v.forEach(limitUser->{
                            limitList.add(limitUser.getPrice_limit());
                        });
                        BigDecimal limitSum=BigDecimal.ZERO;
                        if (ObjUtils.isNotEmpty(mergeConfigContract)){
                            HotelMergeType hotelMergeType=HotelMergeType.valueOf(mergeConfigContract.getConsumeType());
                            switch (hotelMergeType){
                                case Unknown:
                                    throw new FinhubException(500,"配置类型错误");
                                case TravelMoneySum:
                                    for (BigDecimal priceLimit:limitList){
                                        limitSum=limitSum.add(priceLimit);
                                    }
                                    break;
                                case CityClassFixedAmount:
                                    if (city_level==1){
                                        limitSum=mergeConfigContract.getFirstClassAmount();
                                    }else if (city_level==2){
                                        limitSum=mergeConfigContract.getSecondClassAmount();
                                    }else if (city_level==-1){
                                        limitSum=mergeConfigContract.getOtherClassAmount();
                                    }
                                    break;
                                case HigherTravelMoney:
                                    limitSum=Collections.max(limitList);
                                    break;
                                case HigherTravelMoneyAddFixedAmount:
                                    limitSum=Collections.max(limitList).add(mergeConfigContract.getFixedAmuont());
                                    break;
                                case HigherTravelMoneyFixedRatio:
                                    limitSum=Collections.max(limitList).multiply(new BigDecimal(mergeConfigContract.getPercentage())).divide(new BigDecimal(100));
                                    break;
                            }
                        }
                        for(UserContactContract user:v){
                            if (user.getOccupier_type()==1){
                                user.setPrice_limit(limitSum);
                            }else {
                                user.setPrice_limit(BigDecimal.ZERO);
                            }
                        }
                    }
                });
                List<UserContactContract> result = Lists.newArrayList();
                groupMap.forEach((k,v)->{
                    result.addAll(v);
                });
                return result;
            }

        }
        return guestList;
    }

    /**
     * 获取审批是否是终审节点
     *
     * @param apply
     * @param applyFlowUserItems
     * @param skippedApprverIds
     * @param couldFinalApprove
     * @return
     * @throws SaasException
     */
    private FindNextApproverIdAndStatusResult findStatusInFlow(ApplyOrder apply, List<ApplyFlowUserItem> applyFlowUserItems, List<String> skippedApprverIds, boolean couldFinalApprove) throws SaasException {
        String approverId = null;
        String receiverId = null;
        ApplyStatus finalStatus;
        Integer applyStatus = null;
        int remainIndex = applyFlowUserItems.size();
        for (int i = 0; i < applyFlowUserItems.size(); i++) {
            if (ValueTool.areEqual(applyFlowUserItems.get(i).getStatus(), ApplyFlowUserItemStatus.Waiting.getValue())) {
                remainIndex = i;
                break;
            }
        }
        //找下一个审批人
        boolean findNextApprover = false;
        // 是否是最后一名审批人
        boolean finnalApprover = true;
        for (int i = remainIndex; i < applyFlowUserItems.size(); i++) {
            ApplyFlowUserItem item = applyFlowUserItems.get(i);
            if (ValueTool.areEqual(item.getStatus(), ApplyFlowUserItemStatus.Waiting.getValue())) {
                finnalApprover = false;
                if (userService.isUserInCompany(item.getUserId(), apply.getCompanyId())) {
                    findNextApprover = true;
                    receiverId = item.getUserId();
                    approverId = receiverId;
                    applyStatus = 1;
                    break;
                } else {
                    skippedApprverIds.add(item.getUserId());
                }
            }
        }
        if (finnalApprover && couldFinalApprove) {
            //当前审批人已经是最后一个审批人
            finalStatus = ApplyStatus.Approved;
            receiverId = apply.getEmployeeId();
            applyStatus = 2;
        } else {
            finalStatus = ApplyStatus.PendingAudit;
            if (!findNextApprover) {
                //后面的审批人都已不存在，直接跳到授权负责人
                List<String> superAdminEmployeeIds = customRoleService.getEmployeeIdByRoleId(Integer.toString(UserRole.CompanySuperAdmin.getValue()), apply.getCompanyId());
                if (superAdminEmployeeIds == null || superAdminEmployeeIds.size() == 0) {
                    throw new SaasException(GlobalResponseCode.ApplyCanotApproveBecauseHasNoSuperAdmin);
                } else {
                    receiverId = superAdminEmployeeIds.get(0);
                    approverId = receiverId;
                    applyStatus = 3;
                }
            }
        }
        FindNextApproverIdAndStatusResult result = new FindNextApproverIdAndStatusResult();
        result.setApproverId(approverId);
        result.setReceiverId(receiverId);
        result.setFinnalStatus(finalStatus);
        result.setApplyStatus(applyStatus);
        return result;
    }

    //@Transactional(value = "saas")
    private int setApproverAndStatus(String applyId, String approverId, ApplyStatus status, Date time, Integer logId, ApplyApproveContract approveModel) {
        if (approveModel == null) {
            approveModel.setComment("");
            approveModel.setPrice(null);
        }
        int count = applyOrderExtMapper.updateApproverIdAndStatus(applyId, approverId, status.getValue(), time, logId, approveModel.getComment(), approveModel.getPrice());
        return count;
    }

    private ApplyOrder getApplyOrderModelByIdAndUserIdAndCompanyId(String id, String userId, String companyId, UserRole userRole) {
        if (StringTool.isNullOrEmpty(id) || StringTool.isNullOrEmpty(userId) || StringTool.isNullOrEmpty(companyId)) {
            return null;
        }
        ApplyOrder apply = applyMapper.selectByPrimaryKey(id);
        if (apply == null) {
            return null;
        }
        if (apply.getCompanyId() == null) {
            return null;
        }
        if (!apply.getEmployeeId().equals(userId)) {
            //如果不是本人，再判断一下userrole是不是分贝管理员或者企业管理员
            if (userRole == null || (userRole != UserRole.CompanyAdmin && userRole != UserRole.CompanySuperAdmin && userRole != UserRole.FbAdmin)) {
                return null;
            }
        }
        return apply;
    }

    private List<ApplyTripInfoContract> getTripListByApplyOrderId(String applyOrderId, ApplyOrder applyOrder) throws SaasException {
        List<ApplyTripInfo> tripList = applyTripInfoExtMapper.selectListByApplyOrderId(applyOrderId);
        List<ApplyTripInfoContract> tripContractList = new ArrayList<>();
        if (CollectionUtils.isEmpty(tripList)) {
            return null;
        }
        for (ApplyTripInfo trip : tripList) {
            ApplyTripInfoContract contract = ApplyTripInfoContract.FromCenterModel(trip);
            if (StringUtils.isNotBlank(trip.getCostAttributionName())) {
                contract.setCost_attribution_name((Map<String, Object>) JSON.parse(trip.getCostAttributionName()));
            }
            String orderId = null;
            if (applyOrder.getType() != ApplyType.Mall.getValue() && applyOrder.getType() != ApplyType.Meishi.getValue() && applyOrder.getType() != ApplyType.TakeAway.getValue()) {
                ApplyTripApplicate applyTripApplicate = applyTripApplicateMapper.selectByPrimaryKey(trip.getTripApplicateCurrentId());
                if (applyTripApplicate == null) {
                    throw new SaasException(GlobalResponseCode.QueryOrderIdByTripApplicateCurrentIdError);
                }
                orderId = applyTripApplicate.getOrderId();
                contract.setOrder_id(applyTripApplicate.getOrderId());
            }
            if (applyOrder.getType() == ApplyType.Dinner.getValue()) {
                EmployeeContract employee = iBaseOrganizationService.getEmployee(applyOrder.getEmployeeId(), applyOrder.getCompanyId());
                //Employee employee = employeeMapper.selectByPrimaryKey(applyOrder.getEmployeeId());
                if (employee != null) {
                    contract.setExtra_content(employee.getName() + trip.getPersonCount() + "人用餐");
                    contract.setPerson_content(employee.getName() + "（" + trip.getPersonCount() + "人）");
                }
                SimpleDateFormat myFmt = new SimpleDateFormat("HH:mm");
                String startTime = myFmt.format(trip.getStartTime());
                String endTime = myFmt.format(trip.getEndTime());
                if ("00:00".equals(endTime) || "23:59".equals(endTime)) {
                    endTime = "24:00";
                }
                contract.setTime_range(startTime + "-" + endTime);
                if (applyOrder.getState().intValue() == ApplyStatus.PendingAudit.getValue()) {
                    Calendar c = Calendar.getInstance();
                    c.setTime(trip.getEndTime());
                    c.add(Calendar.MINUTE, -20);
                    SimpleDateFormat sdf = new SimpleDateFormat("MM/dd HH:mm");
                    String dinnerEndDate = sdf.format(c.getTime());
                    contract.setTrip_content("请在" + dinnerEndDate + "前完成审批");
                }
            } else if (applyOrder.getType() == ApplyType.Meishi.getValue()) {
                EmployeeContract employee = iBaseOrganizationService.getEmployee(applyOrder.getEmployeeId(), applyOrder.getCompanyId());
                if (employee != null) {
                    contract.setExtra_content(employee.getName() + trip.getPersonCount() + "人用餐");
                    contract.setPerson_content(employee.getName() + "（" + trip.getPersonCount() + "人）");
                }
                SimpleDateFormat myFmt = new SimpleDateFormat("HH:mm");
                String startTime = myFmt.format(trip.getStartTime());
                String endTime = myFmt.format(trip.getEndTime());
                if ("00:00".equals(endTime) || "23:59".equals(endTime)) {
                    endTime = "24:00";
                }
                SimpleDateFormat sdf = new SimpleDateFormat("MM/dd");
                String startTimeString = sdf.format(trip.getStartTime());
                contract.setTime_range(startTimeString + " " + startTime + "-" + endTime);
                contract.setPerson_count(ObjUtils.toString(trip.getPersonCount()));
            } else if (applyOrder.getType() == ApplyType.TakeAway.getValue()) {
                SimpleDateFormat myFmt = new SimpleDateFormat("HH:mm");
                String startTime = myFmt.format(trip.getStartTime());
                String endTime = myFmt.format(trip.getEndTime());
                if ("00:00".equals(endTime)) {
                    endTime = "24:00";
                }
                SimpleDateFormat sdf = new SimpleDateFormat("MM/dd");
                String startTimeString = sdf.format(trip.getStartTime());
                contract.setTime_range(startTimeString + " " + startTime + "-" + endTime);
                String tripContent = trip.getTripContent();
                if (ObjUtils.isNotEmpty(tripContent)) {
                    JSONObject jo = JsonUtils.toObj(tripContent, JSONObject.class);
                    contract.setAddress_id(jo.getString("address_id"));
                    contract.setAddress_name(jo.getString("address_name"));
                    contract.setCompany_address_id(jo.getString("company_address_id"));
                    contract.setAddress_lat(jo.getBigDecimal("address_lat"));
                    contract.setAddress_lng(jo.getBigDecimal("address_lng"));
                    contract.setAddress_tag(jo.getInteger("address_tag"));
                }
            } else if (applyOrder.getType() == ApplyType.Mall.getValue()) {
                contract.setOrder_reason(trip.getOrderReason());
                contract.setOrder_reason_desc(trip.getOrderReasonDesc());
                contract.setMall_price_structure((Map<String, Object>) JSON.parse(trip.getPriceStructure()));
                contract.setAddress_info((Map<String, Object>) JSON.parse(trip.getAddressInfo()));
                Map<String, Object> addressMap = (Map<String, Object>) JSON.parse(trip.getAddressInfo());
                List<Map<String, Object>> mallList = (List<Map<String, Object>>) JSON.parse(trip.getMallList());
                contract.setMall_list(mallList);
                if (StringUtils.isNotBlank(trip.getContent())) {
                    Map<String, Object> parse = (Map<String, Object>) JSON.parse(trip.getContent());
                    contract.setCustom_remark((List<Map<String, Object>>) parse.get("custom_remark"));
                }
            }
            if (applyOrder.getType() == ApplyType.Air.getValue() || applyOrder.getType() == ApplyType.IntlAir.getValue()) {
                Map<String, Object> airInfoObject = (Map<String, Object>) JSONObject.parseObject(trip.getTripContent());
                if (airInfoObject != null) {
                    Map<String, Object> airInfo = (Map<String, Object>) airInfoObject.get("air_info");
                    if (airInfo != null) {
                        BigDecimal totalPrice = ObjUtils.toBigDecimal(airInfo.get("totalPrice"));
                        Object price = airInfo.get("price");
                        BigDecimal discount = ObjUtils.toBigDecimal(airInfo.get("discount"));
                        Object flightNo = airInfo.get("flightNo");
                        Object airlineName = airInfo.get("airlineName");
                        if (trip.getEstimatedAmount().compareTo(totalPrice) == 0) {
                            contract.setTrip_details("提交申请时，预订人已选最低价航班");
                        } else {
                            if (applyOrder.getType() == ApplyType.Air.getValue()) {
                                if (discount.compareTo(BigDecimal.valueOf(1)) != -1) {
                                    contract.setTrip_details("提交申请时，当日最低" + price + "/张 " + airlineName + flightNo);
                                } else {
                                    contract.setTrip_details("提交申请时，当日最低" + price + "/张 " + airlineName + flightNo + " " + discount.multiply(BigDecimal.valueOf(10)) + "折");
                                }
                            } else {
                                contract.setTrip_details("提交申请时，当日最低" + price + "/张 " + airlineName + flightNo);
                            }
                        }
                    } else {
                        contract.setTrip_details("正在查询当日最低价航班，请稍后重新进入申请单详情查看");
                    }
                }
            }
            Integer busiType = null;
            Integer type = trip.getType();
            if (type != BizType.Dinner.getValue() && type != BizType.Mall.getValue() && type != BizType.Meishi.getValue()) {
                if (type == BizType.Air.getValue()) {
                    busiType = 1;
                } else if (type == BizType.Hotel.getValue()) {
                    busiType = 2;
                } else if (type == BizType.Train.getValue()) {
                    busiType = 3;
                } else if (type == BizType.IntlAir.getValue()) {
                    busiType = 4;
                }
                try {
                    String url = URL_GET_ORDER_STATUS;
                    if(type == BizType.Hotel.getValue()){
                        url = URL_GET_HOTEL_ORDER_STATUS;
                    }
                    logger.info("获取订单最新状态:" + url + "?order_id=" + orderId + "&busi_type=" + busiType);
                    String data = null;
                    data = HttpTool.get(url + "?order_id=" + orderId + "&busi_type=" + busiType);
                    logger.info("获取订单最新状态:" + data);
                    Map<String, Object> jo = JSONObject.parseObject(data, HashMap.class);
                    Map<String, Object> dataMap = (Map<String, Object>) jo.get("data");
                    Object statusName = dataMap.get("statusMsg");
                    Object status = dataMap.get("status");
                    contract.setStatus(ObjUtils.toString(status));
                    contract.setStatus_name(ObjUtils.toString(statusName));
                } catch (Exception e) {
                    logger.error(String.format("url:%s,异常", URL_GET_ORDER_STATUS) + e.getMessage());
                    throw new SaasException(GlobalResponseCode.QueryORDERSTATUSByApplyIdError);
                }
            } else if (type == BizType.Dinner.getValue()) {
                try {
                    logger.info("获取用餐订单最新状态:" + HostPropertyConfigTool.HOST_DINNER_BIZ + "/internal/dinner/during/order/detail?order_id=" + orderId + "&during_apply_id=" + applyOrderId);
                    String dinnerData = HttpTool.get(HostPropertyConfigTool.HOST_DINNER_BIZ + "/internal/dinner/during/order/detail?order_id=" + orderId + "&during_apply_id=" + applyOrderId);
                    logger.info("获取用餐订单最新状态返回结果:" + dinnerData);
                    Map<String, Object> jo = JSONObject.parseObject(dinnerData, HashMap.class);
                    Map<String, Object> dinnerDataMap = (Map<String, Object>) jo.get("data");
                    Map<String, Object> statusData = (Map<String, Object>) dinnerDataMap.get("status");
                    Object key = statusData.get("key");
                    Object value = statusData.get("value");
                    contract.setStatus(ObjUtils.toString(key));
                    contract.setStatus_name(ObjUtils.toString(value));
                } catch (Exception e) {
                    logger.error(String.format("url:%s,异常", HostPropertyConfigTool.HOST_DINNER_BIZ + "/internal/dinner/during/order/detail") + e.getMessage());
                    throw new SaasException(GlobalResponseCode.QueryDinnerOrderStatusByApplyIdError);
                }
            }
            tripContractList.add(contract);
        }
        return tripContractList;
    }

    /**
     * 同行人信息
     *
     * @param applyOrderId
     * @return
     */
    private List<UserContactContract> getGuestListByApplyOrderId(String applyOrderId) {
        List<ApplyTripGuest> guestList = applyGuestExtMapper.selectListByApplyOrderId(applyOrderId);
        if (CollectionUtils.isEmpty(guestList)) {
            return null;
        }
        List<UserContactContract> guestContractList = new ArrayList<UserContactContract>();
        for (ApplyTripGuest guest : guestList) {
            UserContactContract contract;
            if (StringUtils.isNotBlank(guest.getContactInfo())) {
                contract = JSONObject.parseObject(guest.getContactInfo(), UserContactContract.class);
                if (StringUtils.isNotBlank(contract.getPhone())) {
                    contract.setPhone(contract.getPhone().replace(contract.getPhone().substring(3, 7), "****"));
                }
                if (StringUtils.isNotBlank(contract.getName())) {
                    if (StringUtils.isBlank(contract.getId_number())) {
                        contract.setId_number("");
                    }
                    if (contract.getId_type() == null) {
                        KeyValueItem keyValueItem = new KeyValueItem();
                        keyValueItem.setKey(1);
                        keyValueItem.setValue("");
                        contract.setId_type(keyValueItem);
                    }
                    contract.setGender(null);
                    guestContractList.add(contract);
                }
            }
        }
//        for (UserContactContract userContactContract : guestContractList) {
//            if (StringUtils.isBlank(userContactContract.getId_number())) {
//                userContactContract.setId_number("");
//            }
//            if (userContactContract.getId_type() == null) {
//                KeyValueItem keyValueItem = new KeyValueItem();
//                keyValueItem.setKey(1);
//                keyValueItem.setValue("");
//                userContactContract.setId_type(keyValueItem);
//            }
//            userContactContract.setGender(null);
//        }
        return guestContractList;
    }

    private <T> boolean checkCommonListModified(List<T> srcList, List<T> desList) {
        if ((srcList == null || srcList.size() == 0) && (desList != null && desList.size() > 0)) {
            return true;
        }
        if ((desList == null || desList.size() == 0) && (srcList != null && srcList.size() > 0)) {
            return true;
        }
        if (desList != null && desList.size() != srcList.size()) {
            return true;
        }
        return false;
    }

    /**
     * 审批单详情
     *
     * @param token
     * @param applyId
     * @param userId
     * @param companyId
     * @param userRole
     * @return
     * @throws SaasException
     */
    @Override
    public ApplyV2Contract getDetail(String token, String applyId, String userId, String companyId, UserRole userRole, String clientVersion) throws SaasException {
        FlowCheckUtil.check(companyId, clientVersion);
        ApplyV2Contract apply = getApplyByIdAndUserIdAndCompanyId(applyId, userId, companyId, userRole, token, clientVersion);
        if (apply == null) {
            return null;
        }
        if (apply.getApply().getType().intValue() == ApplyType.Mall.getValue() && VersionTool.compare(clientVersion, "1.9.6") < 0) {
            throw new SaasException(GlobalResponseCode.ApplyCenterAlert);
        }
        if (VersionTool.compare(clientVersion, "1.9.96") < 0 && apply.getApply().getApply_order_type() == SaasFlowConstant.SETTING_TYPE_ROUTE) {
            throw new SaasException(GlobalResponseCode.ApplyCenterAlert);
        }
        //1、申请人及审批人的姓名及部门 2、可操作权限
        appendApplyDetail(token, apply, userId);
        List<ApplyOrderLogContract> logs = getLogsByApplyId(applyId, companyId);
        for (ApplyOrderLogContract applyOrderLogContract : logs) {
            if (StringUtils.isBlank(applyOrderLogContract.getSponsor()) && apply.getApply().getEmployee_id().equals(applyOrderLogContract.getSponsor_id())) {
                if (applyOrderLogContract.getAction() != ApplyLogAction.Skip.getValue()) {
                    applyOrderLogContract.setSponsor(apply.getApply().getApplicant_name());
                }
            }
        }
        apply.setLog_list(logs);
        String orderId = apply.getTrip_list().get(0).getOrder_id();
        Integer orderType = apply.getTrip_list().get(0).getType();
        apply.setOrder_info(disposeOrderInfo(orderId, orderType, apply.getApply().getEmployee_id()));
        return apply;
    }

    /**
     * 整合订单审批的订单信息
     */
    @Override
    public JSONObject disposeOrderInfo(String orderId, Integer orderType, String userId) {
        Object contactName = null;
        String contactPhone = null;
        Object orderIdInfo = null;
        Object remarkReason = null;
        Object remarkDetail = null;
        Object orderPerson = null;
        Object orderPersonUserId = null;
        Object costAttributionName = null;
        Object notifiers = null;
        List<Map<String, Object>> costAttributionList = null;
        String costCategoryId = null;
        String costCategoryName = null;
        Integer costInfoType = null;
        List<Map<String, Object>> costInfoTicketList = null;
        Map<String, Object> costInfo = null;
        JSONObject orderInfo = new JSONObject();
        try {
            if (orderType == BizType.Air.getValue()) {
                logger.info("获取国内机票订单详情:" + URL_GET_AIR_ORDER_INFO + "?order_id=" + orderId + "&get_during_apply_info=false");
                String airData = HttpTool.get(URL_GET_AIR_ORDER_INFO + "?order_id=" + orderId + "&get_during_apply_info=false");
                logger.info("获取国内机票订单详情结果:" + airData);
                Map<String, Object> jo = JSONObject.parseObject(airData, HashMap.class);
                Map<String, Object> airDataMap = (Map<String, Object>) jo.get("data");
                contactName = airDataMap.get("contact_name");
                contactPhone = ObjUtils.toString(airDataMap.get("contact_phone"));
                orderIdInfo = airDataMap.get("order_id");
                remarkReason = airDataMap.get("remark_reason");
                remarkDetail = airDataMap.get("remark_detail");
                costAttributionName = airDataMap.get("cost_attribution");
                notifiers = airDataMap.get("notifiers");
                Map<String, Object> orderPersonInfo = (Map<String, Object>) airDataMap.get("order_owner");
                orderPerson = orderPersonInfo.get("name");
                orderPersonUserId = userId;
                costInfoType = ObjUtils.toInteger(airDataMap.get("cost_info_type"), 1);
                List<String> costAttributionNameList = Lists.newArrayList();
                if (costInfoType == 2) {
                    costAttributionName = "";
                    Object costInfoTicket = airDataMap.get("cost_info_ticket_list");
                    if (costInfoTicket != null) {
                        costInfoTicketList = (List<Map<String, Object>>)costInfoTicket;
                    }
                } else {
                    costInfo = (Map<String, Object>) airDataMap.get("cost_detail");
                    if (costInfo != null) {
                        costCategoryId = ObjUtils.toString(costInfo.get("cost_category_id"));
                        costCategoryName = ObjUtils.toString(costInfo.get("cost_category"));
                        List<CostInfoContract.CostAttributionGroup> costAttributionGroupList = JSON.parseArray(ObjUtils.toString(costInfo.get("cost_attribution_group_list")), CostInfoContract.CostAttributionGroup.class);
                        if (CollectionUtils.isNotEmpty(costAttributionGroupList)) {
                            for (CostInfoContract.CostAttributionGroup costAttributionGroup : costAttributionGroupList) {
                                List<CostAttribution> attributionList = costAttributionGroup.getCost_attribution_list();
                                if (CollectionUtils.isNotEmpty(attributionList)) {
                                    for (CostAttribution costAttribution : attributionList) {
                                        if (StringUtils.isNotBlank(costAttribution.getName())) {
                                            costAttributionNameList.add(costAttribution.getName());
                                        }
                                    }
                                }
                            }
                            if (CollectionUtils.isNotEmpty(costAttributionNameList)) {
                                costAttributionName = StringUtils.join(costAttributionNameList, "；");
                            }
                        }
                    } else {
                        Object costAttribution = airDataMap.get("cost_attribution_list");
                        if (costAttribution != null) {
                            costAttributionList = (List<Map<String, Object>>) costAttribution;
                            for (Map<String, Object> costAttributionMap : costAttributionList) {
                                String name = ObjUtils.toString(costAttributionMap.get("name"));
                                if (StringUtils.isNotBlank(name)) {
                                    costAttributionNameList.add(name);
                                }
                            }
                            if (CollectionUtils.isNotEmpty(costAttributionNameList)) {
                                costAttributionName = StringUtils.join(costAttributionNameList, "；");
                            }
                        }
                    }
                }
                //整合国内机票信息
                JSONObject airInfo = new JSONObject();
                Map<String, Object> segmentInfo = (Map<String, Object>) airDataMap.get("segment_info");
                Map<String, Object> priceInfo = (Map<String, Object>) airDataMap.get("price_info");
                Map<String, Object> stipulateInfo = (Map<String, Object>) airDataMap.get("stipulate_info");
                List<Map<String, Object>> insuranceInfo = (List<Map<String, Object>>) airDataMap.get("insurance_info");
                List<Map<String, Object>> priceDetail = (List<Map<String, Object>>) airDataMap.get("price_detail");
                //行程信息
                airInfo.put("segment_info", segmentInfo);
                airInfo.put("stipulate_info", stipulateInfo);
                airInfo.put("insurance_info", insuranceInfo);
                airInfo.put("price_detail", priceDetail);
                //价格信息
                airInfo.put("price_info", priceInfo);
                List<Map<String, Object>> customRemark = (List<Map<String, Object>>) airDataMap.get("custom_remark");
                orderInfo.put("contact_name", contactName);
                orderInfo.put("contact_phone", contactPhone.replace(contactPhone.substring(3, 7), "****"));
                orderInfo.put("order_id", orderIdInfo);
                orderInfo.put("remark_reason", remarkReason);
                orderInfo.put("remark_detail", remarkDetail);
                orderInfo.put("custom_remark", customRemark);
                orderInfo.put("order_person", orderPerson);
                orderInfo.put("order_person_user_id", orderPersonUserId);
                orderInfo.put("cost_attribution_name", costAttributionName);
                orderInfo.put("notifiers", notifiers);
                orderInfo.put("air_info", airInfo);
                orderInfo.put("cost_attribution_list", costAttributionList);
                orderInfo.put("cost_category_id", costCategoryId);
                orderInfo.put("cost_category_name", costCategoryName);
                orderInfo.put("cost_info_type", costInfoType);
                orderInfo.put("cost_info_ticket_list", costInfoTicketList);
                orderInfo.put("cost_detail", costInfo);
            } else if (orderType == BizType.IntlAir.getValue()) {
                logger.info("获取国际机票订单详情:" + URL_GET_INTL_AIR_ORDER_INFO + orderId);
                String intlAirData = HttpTool.get(URL_GET_INTL_AIR_ORDER_INFO + orderId);
                logger.info("获取国际机票订单详情结果:" + intlAirData);
                Map<String, Object> jo = JSONObject.parseObject(intlAirData, HashMap.class);
                Map<String, Object> intlAirDataMap = (Map<String, Object>) jo.get("data");
                contactName = intlAirDataMap.get("contact_name");
                contactPhone = ObjUtils.toString(intlAirDataMap.get("contact_phone"));
                orderIdInfo = intlAirDataMap.get("order_id");
                remarkReason = intlAirDataMap.get("remark_reason");
                remarkDetail = intlAirDataMap.get("remark_detail");
                orderPerson = intlAirDataMap.get("user_name");
                notifiers = intlAirDataMap.get("notifiers");
                costAttributionName = intlAirDataMap.get("cost_attribution_name");
                orderPersonUserId = userId;
                costInfoType = ObjUtils.toInteger(intlAirDataMap.get("cost_info_type"), 1);
                List<String> costAttributionNameList = Lists.newArrayList();
                if (costInfoType == 2) {
                    costAttributionName = "";
                    Object costInfoTicket = intlAirDataMap.get("cost_info_ticket_list");
                    if (costInfoTicket != null) {
                        costInfoTicketList = (List<Map<String, Object>>)costInfoTicket;
                    }
                } else {
                    costInfo = (Map<String, Object>) intlAirDataMap.get("cost_detail");
                    if (costInfo != null) {
                        costCategoryId = ObjUtils.toString(costInfo.get("cost_category_id"));
                        costCategoryName = ObjUtils.toString(costInfo.get("cost_category"));
                        List<CostInfoContract.CostAttributionGroup> costAttributionGroupList = JSON.parseArray(ObjUtils.toString(costInfo.get("cost_attribution_group_list")), CostInfoContract.CostAttributionGroup.class);
                        if (CollectionUtils.isNotEmpty(costAttributionGroupList)) {
                            for (CostInfoContract.CostAttributionGroup costAttributionGroup : costAttributionGroupList) {
                                List<CostAttribution> attributionList = costAttributionGroup.getCost_attribution_list();
                                if (CollectionUtils.isNotEmpty(attributionList)) {
                                    for (CostAttribution costAttribution : attributionList) {
                                        if (StringUtils.isNotBlank(costAttribution.getName())) {
                                            costAttributionNameList.add(costAttribution.getName());
                                        }
                                    }
                                }
                            }
                            if (CollectionUtils.isNotEmpty(costAttributionNameList)) {
                                costAttributionName = StringUtils.join(costAttributionNameList, "；");
                            }
                        }
                    } else {
                        Object costAttribution = intlAirDataMap.get("cost_attribution_list");
                        if (costAttribution != null) {
                            costAttributionList = (List<Map<String, Object>>) costAttribution;
                            for (Map<String, Object> costAttributionMap : costAttributionList) {
                                String name = ObjUtils.toString(costAttributionMap.get("name"));
                                if (StringUtils.isNotBlank(name)) {
                                    costAttributionNameList.add(name);
                                }
                            }
                            if (CollectionUtils.isNotEmpty(costAttributionNameList)) {
                                costAttributionName = StringUtils.join(costAttributionNameList, "；");
                            }
                        }
                    }
                }
                //整合国际机票信息
                JSONObject intlAirInfo = new JSONObject();
                List<Map<String, Object>> segmentInfoList = (List<Map<String, Object>>) intlAirDataMap.get("segment_info_list");
                List<Map<String, Object>> priceDetail = (List<Map<String, Object>>) intlAirDataMap.get("price_detail");
                Map<String, Object> stipulateInfo = (Map<String, Object>) intlAirDataMap.get("stipulate_info");
                //行程信息
                intlAirInfo.put("segment_info_list", segmentInfoList);
                intlAirInfo.put("stipulate_info", stipulateInfo);
                //价格信息
                intlAirInfo.put("price_detail", priceDetail);
                List<Map<String, Object>> customRemark = (List<Map<String, Object>>) intlAirDataMap.get("custom_remark");
                orderInfo.put("contact_name", contactName);
                orderInfo.put("contact_phone", contactPhone.replace(contactPhone.substring(3, 7), "****"));
                orderInfo.put("order_id", orderIdInfo);
                orderInfo.put("remark_reason", remarkReason);
                orderInfo.put("remark_detail", remarkDetail);
                orderInfo.put("custom_remark", customRemark);
                orderInfo.put("order_person", orderPerson);
                orderInfo.put("order_person_user_id", orderPersonUserId);
                orderInfo.put("cost_attribution_name", costAttributionName);
                orderInfo.put("notifiers", notifiers);
                orderInfo.put("intl_air_info", intlAirInfo);
                orderInfo.put("cost_attribution_list", costAttributionList);
                orderInfo.put("cost_category_id", costCategoryId);
                orderInfo.put("cost_category_name", costCategoryName);
                orderInfo.put("cost_info_type", costInfoType);
                orderInfo.put("cost_info_ticket_list", costInfoTicketList);
                orderInfo.put("cost_detail", costInfo);
            } else if (orderType == BizType.Hotel.getValue()) {
                logger.info("获取酒店订单详情:" + URL_GET_HOTEL_ORDER_INFO + "?order_id=" + orderId + "&get_during_apply_info=false");
                String hotelData = HttpTool.get(URL_GET_HOTEL_ORDER_INFO + "?order_id=" + orderId + "&get_during_apply_info=false");
                logger.info("获取酒店订单详情结果:" + hotelData);
                Map<String, Object> jo = JSONObject.parseObject(hotelData, HashMap.class);
                Map<String, Object> hotelDataMap = (Map<String, Object>) jo.get("data");
                contactName = hotelDataMap.get("contact_name");
                contactPhone = ObjUtils.toString(hotelDataMap.get("contact_phone_no"));
                orderIdInfo = hotelDataMap.get("order_id");
                remarkReason = hotelDataMap.get("remark");
                remarkDetail = hotelDataMap.get("remark_detail");
                orderPerson = hotelDataMap.get("order_person");
                orderPersonUserId = userId;
                costInfoType = ObjUtils.toInteger(hotelDataMap.get("cost_info_type"), 1);
                List<String> costAttributionNameList = Lists.newArrayList();
                if (costInfoType == 2) {
                    costAttributionName = "";
                    Object costInfoTicket = hotelDataMap.get("cost_info_ticket_list");
                    if (costInfoTicket != null) {
                        costInfoTicketList = (List<Map<String, Object>>)costInfoTicket;
                    }
                } else {
                    costInfo = (Map<String, Object>) hotelDataMap.get("cost_detail");
                    if (costInfo != null) {
                        costCategoryId = ObjUtils.toString(costInfo.get("cost_category_id"));
                        costCategoryName = ObjUtils.toString(costInfo.get("cost_category"));
                        List<CostInfoContract.CostAttributionGroup> costAttributionGroupList = JSON.parseArray(ObjUtils.toString(costInfo.get("cost_attribution_group_list")), CostInfoContract.CostAttributionGroup.class);
                        if (CollectionUtils.isNotEmpty(costAttributionGroupList)) {
                            for (CostInfoContract.CostAttributionGroup costAttributionGroup : costAttributionGroupList) {
                                List<CostAttribution> attributionList = costAttributionGroup.getCost_attribution_list();
                                if (CollectionUtils.isNotEmpty(attributionList)) {
                                    for (CostAttribution costAttribution : attributionList) {
                                        if (StringUtils.isNotBlank(costAttribution.getName())) {
                                            costAttributionNameList.add(costAttribution.getName());
                                        }
                                    }
                                }
                            }
                            if (CollectionUtils.isNotEmpty(costAttributionNameList)) {
                                costAttributionName = StringUtils.join(costAttributionNameList, "；");
                            }
                        }
                    } else {
                        Object costAttribution = hotelDataMap.get("cost_attribution_list");
                        if (costAttribution != null) {
                            costAttributionList = (List<Map<String, Object>>) costAttribution;
                            for (Map<String, Object> costAttributionMap : costAttributionList) {
                                String name = ObjUtils.toString(costAttributionMap.get("name"));
                                if (StringUtils.isNotBlank(name)) {
                                    costAttributionNameList.add(name);
                                }
                            }
                            if (CollectionUtils.isNotEmpty(costAttributionNameList)) {
                                costAttributionName = StringUtils.join(costAttributionNameList, "；");
                            }
                        }
                    }
                }
                //整合酒店信息
                JSONObject hotelInfo = new JSONObject();
                //入店日期
                Object checkinDate = hotelDataMap.get("checkin_date");
                //离店日期
                Object checkoutDate = hotelDataMap.get("checkout_date");
                //城市码
                Object cityCode = hotelDataMap.get("city_code");
                //城市名
                Object cityName = hotelDataMap.get("city_name");
                //酒店地址
                Object hotelAddress = hotelDataMap.get("hotel_address");
                //酒店名称
                Object hotelName = hotelDataMap.get("hotel_name");
                //酒店电话
                Object hotelPhone = hotelDataMap.get("hotel_phone");
                //床型名称
                Object bedType = hotelDataMap.get("bed_type");
                //房型名称
                Object roomName = hotelDataMap.get("room_name");
                //预订间数
                Object roomCount = hotelDataMap.get("room_count");
                //规则名称
                Object priceRuleTag = hotelDataMap.get("price_rule_tag");
                //价格计划名称
                Object planName = hotelDataMap.get("plan_name");
                //规则
                Object priceRule = hotelDataMap.get("price_rule");
                //保险信息
                List<Map<String, Object>> insuranceInfo = (List<Map<String, Object>>) hotelDataMap.get("insurance_info");
                //订单通知人
                notifiers = hotelDataMap.get("notifiers");

                hotelInfo.put("checkin_date", checkinDate);
                hotelInfo.put("checkout_date", checkoutDate);
                hotelInfo.put("city_code", cityCode);
                hotelInfo.put("city_name", cityName);
                hotelInfo.put("hotel_address", hotelAddress);
                hotelInfo.put("hotel_name", hotelName);
                hotelInfo.put("hotel_phone", hotelPhone);
                hotelInfo.put("bed_type", bedType);
                hotelInfo.put("room_name", roomName);
                hotelInfo.put("room_count", roomCount);
                hotelInfo.put("price_rule_tag", priceRuleTag);
                hotelInfo.put("price_rule", priceRule);
                hotelInfo.put("plan_name", planName);
                hotelInfo.put("insurance_info", insuranceInfo);
                orderInfo.put("hotel_info", hotelInfo);

                List<Map<String, Object>> customRemark = (List<Map<String, Object>>) hotelDataMap.get("custom_remark");
                orderInfo.put("contact_name", contactName);
                orderInfo.put("contact_phone", contactPhone.replace(contactPhone.substring(3, 7), "****"));
                orderInfo.put("order_id", orderIdInfo);
                orderInfo.put("remark_reason", remarkReason);
                orderInfo.put("remark_detail", remarkDetail);
                orderInfo.put("custom_remark", customRemark);
                orderInfo.put("order_person", orderPerson);
                orderInfo.put("order_person_user_id", orderPersonUserId);
                orderInfo.put("cost_attribution_name", costAttributionName);
                orderInfo.put("notifiers", notifiers);
                orderInfo.put("hotel_info", hotelInfo);
                orderInfo.put("cost_attribution_list", costAttributionList);
                orderInfo.put("cost_category_id", costCategoryId);
                orderInfo.put("cost_category_name", costCategoryName);
                orderInfo.put("cost_info_type", costInfoType);
                orderInfo.put("cost_info_ticket_list", costInfoTicketList);
                orderInfo.put("cost_detail", costInfo);
            } else if (orderType == BizType.Train.getValue()) {
                logger.info("获取火车订单详情:" + URL_GET_TRAIN_ORDER_INFO + "?order_id=" + orderId + "&get_during_apply_info=false&get_grab_info_force=true");
                String trainData = HttpTool.get(URL_GET_TRAIN_ORDER_INFO + "?order_id=" + orderId + "&get_during_apply_info=false&get_grab_info_force=true");
                logger.info("获取火车订单详情结果:" + trainData);
                Map<String, Object> jo = JSONObject.parseObject(trainData, HashMap.class);
                Map<String, Object> trainDataMap = (Map<String, Object>) jo.get("data");
                List<Map<String, Object>> passengersInfo = (List<Map<String, Object>>) trainDataMap.get("passengers_info");
                for (Map<String, Object> passengersDetail : passengersInfo) {
                    Map<String, Object> passengerInfo = (Map<String, Object>) passengersDetail.get("passenger_info");
                    String mobileNo = ObjUtils.toString(passengerInfo.get("mobile_no"));
                    if (StringUtils.isNotBlank(mobileNo)) {
                        passengerInfo.put("mobile_no", mobileNo.replace(mobileNo.substring(3, 7), "****"));
                    }
                }
                costInfoType = ObjUtils.toInteger(trainDataMap.get("cost_info_type"), 1);
                List<String> costAttributionNameList = Lists.newArrayList();
                if (costInfoType == 2) {
                    costAttributionName = "";
                    Object costInfoTicket = trainDataMap.get("cost_info_ticket_list");
                    if (costInfoTicket != null) {
                        costInfoTicketList = (List<Map<String, Object>>)costInfoTicket;
                    }
                } else {
                    costInfo = (Map<String, Object>) trainDataMap.get("cost_detail");
                    if (costInfo != null) {
                        costCategoryId = ObjUtils.toString(costInfo.get("cost_category_id"));
                        costCategoryName = ObjUtils.toString(costInfo.get("cost_category"));
                        List<CostInfoContract.CostAttributionGroup> costAttributionGroupList = JSON.parseArray(ObjUtils.toString(costInfo.get("cost_attribution_group_list")), CostInfoContract.CostAttributionGroup.class);
                        if (CollectionUtils.isNotEmpty(costAttributionGroupList)) {
                            for (CostInfoContract.CostAttributionGroup costAttributionGroup : costAttributionGroupList) {
                                List<CostAttribution> attributionList = costAttributionGroup.getCost_attribution_list();
                                if (CollectionUtils.isNotEmpty(attributionList)) {
                                    for (CostAttribution costAttribution : attributionList) {
                                        if (StringUtils.isNotBlank(costAttribution.getName())) {
                                            costAttributionNameList.add(costAttribution.getName());
                                        }
                                    }
                                }
                            }
                            if (CollectionUtils.isNotEmpty(costAttributionNameList)) {
                                costAttributionName = StringUtils.join(costAttributionNameList, "；");
                            }
                        }
                    } else {
                        Object costAttribution = trainDataMap.get("cost_attribution_list");
                        if (costAttribution != null) {
                            costAttributionList = (List<Map<String, Object>>) costAttribution;
                            for (Map<String, Object> costAttributionMap : costAttributionList) {
                                String name = ObjUtils.toString(costAttributionMap.get("name"));
                                if (StringUtils.isNotBlank(name)) {
                                    costAttributionNameList.add(name);
                                }
                            }
                            if (CollectionUtils.isNotEmpty(costAttributionNameList)) {
                                costAttributionName = StringUtils.join(costAttributionNameList, "；");
                            }
                        }
                    }
                }
                Map<String, Object> contactInfo = (Map<String, Object>) trainDataMap.get("contact_info");
                contactName = contactInfo.get("contact_name");
                contactPhone = ObjUtils.toString(contactInfo.get("contact_phone"));
                Map<String, Object> orderBasicInfo = (Map<String, Object>) trainDataMap.get("order_basic_info");
                orderIdInfo = orderBasicInfo.get("order_id");
                orderPerson = orderBasicInfo.get("employee_name");
                Boolean isGrabOrder = ObjUtils.toBoolean(orderBasicInfo.get("is_grab_order"));
                remarkReason = orderBasicInfo.get("remark_reason");
                remarkDetail = orderBasicInfo.get("remark_detail");
                Object totalPrice = orderBasicInfo.get("total_price");
                List<Map<String, Object>> customRemark = (List<Map<String, Object>>) orderBasicInfo.get("custom_remark");
                notifiers = orderBasicInfo.get("notifiers");
                orderPersonUserId = userId;

                //整合火车信息
                JSONObject trainInfo = new JSONObject();
                Map<String, Object> routeInfo = (Map<String, Object>) trainDataMap.get("route_info");
                List<Map<String, Object>> priceDetail = (List<Map<String, Object>>) trainDataMap.get("price_detail");
                List<Map<String, Object>> insuranceInfo = (List<Map<String, Object>>) trainDataMap.get("insurance_info");
                Map<String, Object> grabInfo = (Map<String, Object>) trainDataMap.get("grab_info");
                trainInfo.put("grab_info", grabInfo);
                //行程信息
                if (isGrabOrder) {
                    if (routeInfo == null) {
                        Map<String, Object> routeDetail = Maps.newHashMap();
                        routeDetail.put("train_code", ObjUtils.toString(grabInfo.get("train_code")));
                        routeDetail.put("from_station_name", ObjUtils.toString(grabInfo.get("from_station_name")));
                        routeDetail.put("to_station_name", ObjUtils.toString(grabInfo.get("to_station_name")));
                        routeDetail.put("train_start_date", ObjUtils.toString(grabInfo.get("train_start_date")));
                        trainInfo.put("route_info", routeDetail);
                    } else {
                        routeInfo.put("train_code", ObjUtils.toString(grabInfo.get("train_code")));
                        routeInfo.put("from_station_name", ObjUtils.toString(grabInfo.get("from_station_name")));
                        routeInfo.put("to_station_name", ObjUtils.toString(grabInfo.get("to_station_name")));
                        routeInfo.put("train_start_date", ObjUtils.toString(grabInfo.get("train_start_date")));
                        trainInfo.put("route_info", routeInfo);
                    }
                } else {
                    trainInfo.put("route_info", routeInfo);
                }
                trainInfo.put("route_info", routeInfo);
                trainInfo.put("price_detail", priceDetail);
                trainInfo.put("insurance_info", insuranceInfo);
                trainInfo.put("passengers_info", passengersInfo);
                trainInfo.put("is_grab_order", isGrabOrder);
                trainInfo.put("total_price", totalPrice);
                orderInfo.put("contact_name", contactName);
                orderInfo.put("contact_phone", contactPhone.replace(contactPhone.substring(3, 7), "****"));
                orderInfo.put("order_id", orderIdInfo);
                orderInfo.put("remark_reason", remarkReason);
                orderInfo.put("remark_detail", remarkDetail);
                orderInfo.put("custom_remark", customRemark);
                orderInfo.put("order_person", orderPerson);
                orderInfo.put("order_person_user_id", orderPersonUserId);
                orderInfo.put("cost_attribution_name", costAttributionName);
                orderInfo.put("notifiers", notifiers);
                orderInfo.put("train_info", trainInfo);
                orderInfo.put("cost_attribution_list", costAttributionList);
                orderInfo.put("cost_category_id", costCategoryId);
                orderInfo.put("cost_category_name", costCategoryName);
                orderInfo.put("cost_info_type", costInfoType);
                orderInfo.put("cost_info_ticket_list", costInfoTicketList);
                orderInfo.put("cost_detail", costInfo);
            } else if (orderType == BizType.Dinner.getValue()) {
                logger.info("获取用餐订单详情:" + URL_GET_DINNER_ORDER_INFO + orderId);
                String dinnerData = HttpTool.get(URL_GET_DINNER_ORDER_INFO + orderId);
                logger.info("获取用餐订单详情结果:" + dinnerData);
                Map<String, Object> jo = JSONObject.parseObject(dinnerData, HashMap.class);
                Map<String, Object> dinnerDataMap = (Map<String, Object>) jo.get("data");
                contactName = dinnerDataMap.get("contact_name");
                contactPhone = ObjUtils.toString(dinnerDataMap.get("contact_phone"));
                orderIdInfo = dinnerDataMap.get("order_id");
                Map<String, Object> costAttribution = (Map<String, Object>) dinnerDataMap.get("cost_attribution");
                costAttributionName = costAttribution.get("name");
                List<Map<String, Object>> checkInfo = (List<Map<String, Object>>) dinnerDataMap.get("check_info");
                if (CollectionUtils.isNotEmpty(checkInfo)) {
                    remarkReason = checkInfo.get(0).get("reason");
                    remarkDetail = checkInfo.get(0).get("comment");
                }
                orderPerson = contactName;
                orderPersonUserId = dinnerDataMap.get("employee_id");

                //整合用餐信息
                JSONObject dinnerInfo = new JSONObject();
                Object personCount = dinnerDataMap.get("person_count");
                Object branchShopName = dinnerDataMap.get("branch_shop_name");
                Object shopMobileNo = dinnerDataMap.get("shop_mobile_no");
                Object shopAddr = dinnerDataMap.get("shop_addr");
                List<Map<String, Object>> voucherSuitTime = (List<Map<String, Object>>) dinnerDataMap.get("voucher_suit_time");
                //用餐人数
                dinnerInfo.put("person_count", personCount);
                //门店名称
                dinnerInfo.put("branch_shop_name", branchShopName);
                //门店电话
                dinnerInfo.put("shop_mobile_no", shopMobileNo);
                //地址
                dinnerInfo.put("shop_addr", shopAddr);
                //企业券适用时段
                dinnerInfo.put("voucher_suit_time", voucherSuitTime);
                List<Map<String, Object>> customRemark = (List<Map<String, Object>>) dinnerDataMap.get("custom_remark");
                orderInfo.put("contact_name", contactName);
                orderInfo.put("contact_phone", contactPhone.replace(contactPhone.substring(3, 7), "****"));
                orderInfo.put("order_id", orderIdInfo);
                orderInfo.put("remark_reason", remarkReason);
                orderInfo.put("remark_detail", remarkDetail);
                orderInfo.put("custom_remark", customRemark);
                orderInfo.put("order_person", orderPerson);
                orderInfo.put("order_person_user_id", orderPersonUserId);
                orderInfo.put("cost_attribution_name", costAttributionName);
                orderInfo.put("dinner_info", dinnerInfo);
                orderInfo.put("cost_attribution_list", costAttributionList.add(costAttribution));
            }
        } catch (Exception e) {
            logger.info("订单审批获取订单信息异常：" + "orderId：" + orderId + " orderType：" + orderType + " 异常提示：" + e.getMessage());
        }
        return orderInfo;
    }

    private List<ApplyOrderLogContract> getLogsByApplyId(String applyId, String companyId) {
        List<ApplyOrderLog> logs = applyOrderLogExtMapper.selectListByApplyId(applyId);//1.8.4
        String currentVersion = "1.8.5";
        logs = logs.stream().filter(logInfo -> logInfo.getAction() != ApplyLogAction.CreateDraft.getValue() && logInfo.getAction() != ApplyLogAction.ModifyDraft.getValue()).collect(Collectors.toList());
        //查询是否有已拒绝、已撤销和超时状态的审批日志
        List<ApplyOrderLog> destinationLogList = logs.stream().filter(logInfo -> logInfo.getAction() == ApplyLogAction.Refuse.getValue() || logInfo.getAction() == ApplyLogAction.Revoke.getValue() || logInfo.getAction() == ApplyLogAction.Overtime.getValue()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(destinationLogList)) {
            logs = logs.stream().filter(logInfo -> logInfo.getAction() != ApplyLogAction.Approval.getValue() && logInfo.getAction() != ApplyLogAction.Unknown.getValue()).collect(Collectors.toList());
        }
        List<ApplyOrderLogContract> result = getLogContractList(logs, companyId);
        return result;
    }

    private void appendApplyDetail(String token, ApplyV2Contract detail, String userId) {
        ApplyOrderV2Contract order = detail.getApply();

        //申请人及审批人的姓名及部门
        List<String> userIds = new ArrayList<>();
        userIds.add(order.getEmployee_id());
        if (order.getApprover_id() != null && order.getApprover_id().length() > 0 && !userIds.contains(order.getApprover_id())) {
            userIds.add(order.getApprover_id());
        }
        List<EmployeeNameAndDeptContract> approverNameAndDepts = userService.getNamesAndDeptsByIds(token, order.getCompany_id(), userIds);
        if (approverNameAndDepts != null && approverNameAndDepts.size() > 0) {
            for (EmployeeNameAndDeptContract contract : approverNameAndDepts) {
                if (contract.getEmployee_id().equals(order.getEmployee_id())) {
                    order.setUser_name(contract.getName());
                    order.setUser_dept(String.join(",", contract.getDepts()));
                    break;
                }
            }
            if (order.getApprover_id() != null) {
                for (EmployeeNameAndDeptContract contract : approverNameAndDepts) {
                    if (contract.getEmployee_id().equals(order.getApprover_id())) {
                        order.setApprover_dept(String.join(",", contract.getDepts()));
                        order.setApprover_name(contract.getName());
                        break;
                    }
                }
            }
        }
        if (StringUtils.isBlank(order.getUser_name())) {
            order.setUser_name(detail.getApply().getApplicant_name());
        }
        if (StringUtils.isBlank(order.getUser_dept())) {
            order.setUser_dept(CoreLanguage.Common_Value_NoDepartmentInfo.getMessage());
        }
        //可操作权限
        Integer operateAuth = genApplyOperateAuth(order, userId);
        order.setOperate_auth(operateAuth);
        //处理审批流设置
        appendFlowData(order);
    }

    private void appendFlowData(ApplyOrderV2Contract applyOrderContract) {
        ApplyStatus status = ApplyStatus.valueOf(applyOrderContract.getState());
        ApplyType applyType = ApplyType.valueOf(applyOrderContract.getType().intValue());
        CompanyApplyFlowSetV2RequestContract flowSet = null;
        //草稿状态下获取最新的审批流设置
        if (status == ApplyStatus.Draft) {
            BigDecimal budget = BigDecimal.valueOf(0);
            if (applyOrderContract.getBudget() != null && applyOrderContract.getBudget().compareTo(BigDecimal.valueOf(0)) == 1) {
                budget = applyOrderContract.getBudget().divide(ObjUtils.toBigDecimal(100));
            }
            flowSet = applyFlowV2Service.getCompanySettingWithEmployee(SaasFlowConstant.SETTING_TYPE_CENTER, applyOrderContract.getCompany_id(), applyType, applyOrderContract.getEmployee_id(), budget, ExceedBuyType.UnSupernormal.getValue(), applyOrderContract.getType());
        } else {
            if (applyOrderContract.getFlow_type() == CompanyApplyType.Flow.getValue() || applyOrderContract.getFlow_type() == CompanyApplyType.CONDITIONAL.getValue()) {
                flowSet = applyFlowV2Service.getFlowByApplyId(applyOrderContract.getId(), applyOrderContract.getCompany_id());
                flowSet = applyFlowV2Service.getFlowCCByApplyId(applyOrderContract.getId(), flowSet, applyOrderContract.getCompany_id());
            }
            if (applyOrderContract.getFlow_type() == CompanyApplyType.Elastic.getValue()) {
                flowSet = applyFlowV2Service.getFlowCCByApplyId(applyOrderContract.getId(), flowSet, applyOrderContract.getCompany_id());
                flowSet.setCompany_apply_type(1);
            }
            if (applyOrderContract.getFlow_type() == CompanyApplyType.OpenApi.getValue()) {
                flowSet = new CompanyApplyFlowSetV2RequestContract();
                flowSet.setFixation_flow_list(Lists.newArrayList());
                flowSet.setCc_list(Lists.newArrayList());
                flowSet.setCc_notice_type_enums(new KvContract());
            }
        }
        if (flowSet != null) {
            applyOrderContract.setFlow_type(flowSet.getCompany_apply_type());
            applyOrderContract.setFlow(flowSet);
        }
    }

    /**
     * 判断操作权限
     *
     * @param order
     * @param userId
     * @return
     */
    private Integer genApplyOperateAuth(ApplyOrderV2Contract order, String userId) {
        // 是否是对接订单审批公司
        boolean isDocking = applyThirdService.isApplyOrderDockingCompany(order.getCompany_id());
        // 对接公司直接返回 0-不能做任何操作
        if (isDocking) {
            return 0;
        }
        ApplyStatus orderStatus = ApplyStatus.valueOf(order.getState());
        if (order.getEmployee_id().equals(userId)) {
            //自己的申请单
            if (orderStatus == ApplyStatus.PendingAudit) {
                if (userId.equals(order.getApprover_id())) {
                    return ApplyLogAction.Approve.getValue() + ApplyLogAction.Forward.getValue() + ApplyLogAction.Refuse.getValue() + ApplyLogAction.Revoke.getValue();
                }
                //状态为待审核时,用户可撤销申请
                return ApplyLogAction.Revoke.getValue();
                //} else if (orderStatus == ApplyStatus.Return || orderStatus == ApplyStatus.Draft) {
            } else if (orderStatus == ApplyStatus.Draft) {
                //状态为被驳回或草稿时,用户可提交申请或者修改
                return ApplyLogAction.Submit.getValue() + ApplyLogAction.ModifyDraft.getValue() + ApplyLogAction.Delete.getValue();
            }
        } else {
            //是需要自己审批,或者自己已经操作过的审批单
            if (orderStatus == ApplyStatus.PendingAudit || orderStatus == ApplyStatus.Transfer) {
                //只有待自己处理的审批单才可操作
                if (userId.equals(order.getApprover_id())) {
                    return ApplyLogAction.Approve.getValue() + ApplyLogAction.Forward.getValue() + ApplyLogAction.Refuse.getValue();
                }
            }
        }
        //不能做任何操作
        return 0;
    }

    /**
     * H5判断操作权限
     *
     * @param order
     * @param userId
     * @return
     */
    private Integer genH5ApplyOperateAuth(ApplyOrderV2Contract order, String userId) {
        ApplyStatus orderStatus = ApplyStatus.valueOf(order.getState());
        if (order.getEmployee_id().equals(userId)) {
            //自己的申请单
            if (orderStatus == ApplyStatus.PendingAudit) {
                if (userId.equals(order.getApprover_id())) {
                    return ApplyLogAction.Approve.getValue() + ApplyLogAction.Refuse.getValue();
                }
                //状态为待审核时,用户可撤销申请
                return 0;
            }
        } else {
            //是需要自己审批,或者自己已经操作过的审批单
            if (orderStatus == ApplyStatus.PendingAudit || orderStatus == ApplyStatus.Transfer) {
                //只有待自己处理的审批单才可操作
                if (userId.equals(order.getApprover_id())) {
                    return ApplyLogAction.Approve.getValue() + ApplyLogAction.Refuse.getValue();
                }
            }
        }
        //不能做任何操作
        return 0;
    }

    private String genApplyLogDisplayString(ApplyOrderLog log, List<IdNameContract> userNames) {
        StringBuilder sb = new StringBuilder();
        IdNameContract sponsor = getNameFromListById(userNames, log.getSponsorId());
        if (sponsor != null) {
            sb.append(sponsor.getName() + " ");
        }
        ApplyLogAction action = ApplyLogAction.valueOf(log.getAction());
        String actionName = "";
        if (action == ApplyLogAction.Unknown) {
            actionName = "";
        } else {
            actionName = action.getDesc();
        }
        sb.append(actionName);
        if (action == ApplyLogAction.Forward
                || action == ApplyLogAction.Submit
                || action == ApplyLogAction.ReSubmit
                ) {
            sb.append(CoreLanguage.Common_Value_Give.getMessage() + " ");
            if (log.getReceiverId() != null) {
                IdNameContract receiver = getNameFromListById(userNames, log.getReceiverId());
                if (receiver != null) {
                    sb.append(receiver.getName());
                }
            }
        }
        return sb.toString();
    }

    private List<ApplyOrderLogContract> getLogContractList(List<ApplyOrderLog> logs, String companyId) {
        if (logs == null || logs.size() == 0) {
            return new ArrayList<>();
        }
        List<ApplyOrderLogContract> result = new ArrayList<>();
        List<String> userIds = new ArrayList<>();
        for (ApplyOrderLog log : logs) {
            if (!userIds.contains(log.getSponsorId())) {
                userIds.add(log.getSponsorId());
            }
            if (log.getReceiverId() != null && !userIds.contains(log.getReceiverId())) {
                userIds.add(log.getReceiverId());
            }
        }
        List<IdNameContract> userNames = userService.getUserNameByIds(userIds, companyId);
        for (ApplyOrderLog log : logs) {
            ApplyOrderLogContract logContract = genLogContract(log, userNames);
            if (logContract != null && logContract.getAction() == ApplyLogAction.Unknown.getValue() && StringUtils.isBlank(logContract.getSponsor()) && StringUtils.isBlank(logContract.getLog())) {
                logContract.setAction(ApplyLogAction.Skip.getValue());
                logContract.setCheck_reason(CoreLanguage.Common_Value_ApprovalPersonDelete.getMessage());
            }
            result.add(logContract);
        }
        return result;
    }

    private ApplyOrderLogContract genLogContract(ApplyOrderLog log, List<IdNameContract> userNames) {
        ApplyOrderLogContract contract = ApplyOrderLogContract.FromModel(log);
        String logContent = genApplyLogDisplayString(log, userNames);
        contract.setLog(logContent);
        if (StringUtils.isNotBlank(log.getSponsorId())) {
            for (IdNameContract idNameContract : userNames) {
                if (idNameContract.getId().equals(log.getSponsorId())) {
                    contract.setSponsor(idNameContract.getName());
                }
            }
        }
        if (StringUtils.isNotBlank(log.getReceiverId())) {
            for (IdNameContract idNameContract : userNames) {
                if (idNameContract.getId().equals(log.getReceiverId())) {
                    contract.setReceiver(idNameContract.getName());
                }
            }
        }
        return contract;
    }

    private IdNameContract getNameFromListById(List<IdNameContract> list, String id) {
        if (list == null || id == null) {
            return null;
        }
        for (IdNameContract item : list) {
            if (item.getId().equals(id)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 通过审批单
     *
     * @param approveModel
     * @param userId
     * @param ip
     * @return
     */
    @Override
    //@Transactional(value = "saas")
    public SaasResponseEntity approve(ApplyApproveContract approveModel, String userId, String companyId, String ip, String clientVersion, String source) throws SaasException {
        FlowCheckUtil.check(companyId, clientVersion, source);
        if (approveModel == null) {
            throw new SaasException(GlobalResponseCode.ApplyIsNull);
        }
        if (StringTool.isNullOrEmpty(approveModel.getApply_id())) {
            throw new SaasException(GlobalResponseCode.ApplyIdInvalid);
        }
        if (approveModel.getComment() != null && approveModel.getComment().length() > 200) {
            throw new SaasException(GlobalResponseCode.ApplyCheckReasonInvalid);
        }
        String applyId = approveModel.getApply_id();
        ApplyOrder apply = applyMapper.selectByPrimaryKey(approveModel.getApply_id());
        if (apply == null) {
            throw new SaasException(GlobalResponseCode.NotFound);
        }
        if (apply.getState() != null && apply.getState().intValue() == ApplyStatus.Overtime.getValue()) {
            //事中审批已超时
            throw new SaasException(GlobalResponseCode.CenterApplyOverTime);
        }
        if (apply.getState() != null && apply.getState().intValue() == ApplyStatus.Backout.getValue()) {
            //事中审批已撤销
            throw new SaasException(GlobalResponseCode.CenterApplyCancleOrder);
        }
        if (!apply.getCompanyId().equals(companyId)) {
            //申请单和当前人不在一个公司
            throw new SaasException(GlobalResponseCode.ApplyCompanyCannotMatch);
        }
        if (apply.getState() != ApplyStatus.PendingAudit.getValue()) {
            //该单不是待审核,不能审批
            throw new SaasException(GlobalResponseCode.ApplyStatusNotPendingAudit);
        }

        if (!apply.getApproverId().equals(userId)) {
            //不是当前人可以处理的审批单
            throw new SaasException(GlobalResponseCode.NoAuth);
        }

        ApplyStatus finalStatus;
        ApplyLogAction action;
        String receiverId; //下一个审批人/通知人
        String approverId; //审批人
        Integer logId = null;
        List<String> skippedApprverIds = new ArrayList<>();//固定审批流跳过的审批人
        Date now = new Date();
        action = ApplyLogAction.Approve;
        approverId = userId;
        receiverId = null;
        Integer finalJudgmentUser = null;
        boolean isFlow = (ValueTool.areEqual(apply.getFlowType(), CompanyApplyType.Flow.getValue()) || ValueTool.areEqual(apply.getFlowType(), CompanyApplyType.CONDITIONAL.getValue()));
        if (isFlow) {
            //固定审批流和分条件审批流
            List<ApplyFlowUserItem> applyFlowUserItems = applyFlowService.getFlowItemsByApplyId(applyId);
            if (applyFlowUserItems == null ||
                    applyFlowUserItems.size() == 0 ||
                    applyFlowUserItems.stream().allMatch(m ->
                            !ValueTool.areEqual(m.getStatus(), ApplyFlowUserItemStatus.PendingAudit.getValue())
                                    && !ValueTool.areEqual(m.getStatus(), ApplyFlowUserItemStatus.Transfered.getValue())
                    )) {
                //没有待审核记录，也没有转交记录，预示审批流已经审核通过
                finalStatus = ApplyStatus.Approved;
                receiverId = apply.getEmployeeId();
                //处理审批日志和审批单的状态
                logId = sloveApplyLog(applyId, receiverId, approverId, finalStatus, now, ip, userId, action, approveModel);
                finalJudgmentUser = 2;
            } else {
                int pendingAuditItemIndex = -1;
                for (int i = 0; i < applyFlowUserItems.size(); i++) {
                    if (ValueTool.areEqual(applyFlowUserItems.get(i).getStatus(), ApplyFlowUserItemStatus.PendingAudit.getValue())) {
                        pendingAuditItemIndex = i;
                        break;
                    }
                }
                ApplyFlowUserItem pendingAuditItem = null;
                if (pendingAuditItemIndex >= 0) {
                    pendingAuditItem = applyFlowUserItems.get(pendingAuditItemIndex);
                }
                if (pendingAuditItem != null && !pendingAuditItem.getUserId().equals(userId)) {
                    logger.info("当前待审批的item不是userid，userId：" + userId + " itemId:" + pendingAuditItem.getUserId());
                    throw new SaasException(GlobalResponseCode.NoAuth);
                        /*//当前待审批的item不是userid，所在重新再将审批人设置为此item
                        finalStatus = ApplyStatus.PendingAudit;
                        receiverId = pendingAuditItem.getUserId();
                        approverId = receiverId;

                        //查询审核中的审批日志
                        List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectApproveUser(applyId, ApplyLogAction.Approval.getValue());
                        //有日志数据情况下修改操作无日志数据情况下插入操作
                        if (!CollectionUtils.isEmpty(applyOrderLogList)) {
                            applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Approve.getValue(), applyOrderLogList.get(0).getId(), receiverId, null, new Date(), approveModel.getPrice());
                            setApproverAndStatus(applyId, approverId, finalStatus, now, applyOrderLogList.get(0).getId(), approveModel);
                            List<ApplyOrderLog> applyOrderNextUserLogList = applyOrderLogExtMapper.selectNextApproveUser(applyId, ApplyLogAction.Unknown.getValue());
                            if (!CollectionUtils.isEmpty(applyOrderNextUserLogList)) {
                                //保存下一个审批人为审批中状态
                                applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Approval.getValue(), applyOrderNextUserLogList.get(0).getId(), "", null, null, approveModel.getPrice());
                                logId = applyOrderNextUserLogList.get(0).getId();
                            }
                        } else {
                            logId = writeLog(applyId, now, ip, userId, receiverId, approveModel.getComment(), action, approveModel.getPrice());
                            setApproverAndStatus(applyId, approverId, finalStatus, now, logId, approveModel);
                        }*/
                } else {
                    if (pendingAuditItem != null) {
                        //将item status设置为已审核
                        applyFlowService.setApplyFlowUserItemStatus(pendingAuditItem.getId(), ApplyFlowUserItemStatus.Approved);
                    }
                    try {
                        FindNextApproverIdAndStatusResult findNextApproverIdAndStatusResult = findNextApproverIdAndStatusInFlow(apply, applyFlowUserItems, skippedApprverIds, true);
                        finalJudgmentUser = findNextApproverIdAndStatusResult.getApplyStatus();
                        finalStatus = findNextApproverIdAndStatusResult.getFinnalStatus();
                        if (!StringTool.isNullOrEmpty(findNextApproverIdAndStatusResult.getApproverId())) {
                            approverId = findNextApproverIdAndStatusResult.getApproverId();
                        }
                        if (!StringTool.isNullOrEmpty(findNextApproverIdAndStatusResult.getReceiverId())) {
                            receiverId = findNextApproverIdAndStatusResult.getReceiverId();
                        }
                        if (findNextApproverIdAndStatusResult.getApplyStatus() == 1) {
                            //不是最后一个审批人存在下一个审批人
                            //查询审核中的审批日志
                            List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectApproveUser(applyId, ApplyLogAction.Approval.getValue());
                            //有日志数据情况下修改操作无日志数据情况下插入操作
                            if (!CollectionUtils.isEmpty(applyOrderLogList)) {
                                applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Approve.getValue(), applyOrderLogList.get(0).getId(), receiverId, approveModel.getComment(), new Date(), approveModel.getPrice());
                                List<ApplyOrderLog> applyOrderNextUserLogList = applyOrderLogExtMapper.selectNextApproveUser(applyId, ApplyLogAction.Unknown.getValue());
                                if (!CollectionUtils.isEmpty(applyOrderNextUserLogList)) {
                                    for (ApplyOrderLog applyOrderLog : applyOrderNextUserLogList) {
                                        String sponsorId = applyOrderLog.getSponsorId();
                                        EmployeeContract employee = iBaseOrganizationService.getEmployee(sponsorId, companyId);
                                        if (employee == null) {
                                            applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Skip.getValue(), applyOrderLog.getId(), "", CoreLanguage.Common_Value_ApprovalPersonDelete.getMessage(), null, approveModel.getPrice());
                                        } else {
                                            //保存下一个审批人为审批中状态
                                            applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Approval.getValue(), applyOrderLog.getId(), "", null, null, approveModel.getPrice());
                                            logId = applyOrderLog.getId();
                                            break;
                                        }
                                    }
                                }
                                setApproverAndStatus(applyId, approverId, finalStatus, now, logId, approveModel);
                            } else {
                                logId = writeLog(applyId, now, ip, userId, receiverId, approveModel.getComment(), action, approveModel.getPrice());
                                setApproverAndStatus(applyId, approverId, finalStatus, now, logId, approveModel);
                            }
                        } else if (findNextApproverIdAndStatusResult.getApplyStatus() == 2) {
                            //最后一个审批人
                            logId = sloveApplyLog(applyId, receiverId, approverId, finalStatus, now, ip, userId, action, approveModel);
                        } else {
                            //后面的审批人都不存在直接跳到授权负责人
                            List<ApplyOrderLog> applyOrderApprovalLogList = applyOrderLogExtMapper.selectApproveUser(applyId, ApplyLogAction.Approval.getValue());
                            applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Approve.getValue(), applyOrderApprovalLogList.get(0).getId(), receiverId, approveModel.getComment(), new Date(), approveModel.getPrice());

                            List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectApproveUser(applyId, ApplyLogAction.Unknown.getValue());
                            if (!CollectionUtils.isEmpty(applyOrderLogList)) {
                                for (ApplyOrderLog applyOrderLog : applyOrderLogList) {
                                    applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Skip.getValue(), applyOrderLog.getId(), "", CoreLanguage.Common_Value_ApprovalPersonDelete.getMessage(), null, approveModel.getPrice());
                                }
                            } else {
                                if (skippedApprverIds != null && skippedApprverIds.size() > 0) {
                                    for (String skippedApproverId : skippedApprverIds) {
                                        writeLog(applyId, now, ip, skippedApproverId, skippedApproverId, CoreLanguage.Common_Value_ApprovalPersonDelete.getMessage(), ApplyLogAction.Skip, approveModel.getPrice());
                                    }
                                }
                            }
                            int sort = 0;
                            if (!CollectionUtils.isEmpty(applyOrderLogList)) {
                                sort = applyOrderLogList.get(applyOrderLogList.size() - 1).getSort() + 100;
                            }
                            ApplyOrderLog logApprove = new ApplyOrderLog();
                            logApprove.setApplyOrderId(applyId);
                            logApprove.setIp(ip);
                            logApprove.setSponsorId(approverId);
                            logApprove.setReceiverId("");
                            logApprove.setCheckReason(null);
                            logApprove.setAction(ApplyLogAction.Approval.getValue());
                            logApprove.setSort(sort);
                            logApprove.setRootApplyOrderId(applyId);
                            applyLogMapper.insertSelective(logApprove);
                            logId = logApprove.getId();
                            setApproverAndStatus(applyId, approverId, finalStatus, now, logId, approveModel);
                        }
                    } catch (SaasException ex) {
                        logger.info("审批异常：" + ex.getResponseCode());
                        throw new SaasException(GlobalResponseCode.PayOrderCancleOrderSystemError);
                    }
                }
            }
        } else {
            finalStatus = ApplyStatus.Approved;
            receiverId = apply.getEmployeeId();
            approverId = userId;
            //处理审批日志和审批单的状态
            logId = sloveApplyLog(applyId, receiverId, approverId, finalStatus, now, ip, userId, action, approveModel);
        }
        //最后一个审批节点
        if ((apply.getFlowType() == CompanyApplyType.Elastic.getValue() && apply.getType().intValue() != ApplyType.Mall.getValue()) || (finalJudgmentUser != null && finalJudgmentUser == 2 && apply.getType() != ApplyType.Mall.getValue())) {
            //终审时审批价格
            BigDecimal price = approveModel.getPrice().setScale(2, BigDecimal.ROUND_HALF_UP);
            //占用金额
            BigDecimal occupyPrice = new BigDecimal(apply.getBudget()).divide(ObjUtils.toBigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
            List<ApplyTripInfo> tripList = applyTripInfoExtMapper.selectListByApplyOrderId(applyId);
            String tripContent = tripList.get(0).getTripContent();
            JSONObject jo = JsonUtils.toObj(tripContent, JSONObject.class);
            String costId = jo.getString("costId");
            String orderId = jo.getString("orderId");
            //终审金额大于占用金额 修改占用预算
            if (price.compareTo(occupyPrice) == 1 && StringUtils.isNotBlank(costId)) {
                //修改预算占用
                updateCost(price, orderId, applyId, userId, companyId, costId, tripList.get(0).getType());
            }
            if (apply.getType().intValue() == ApplyType.Dinner.getValue()) {
                sendMealCoupon(applyId);
            } else if (apply.getType().intValue() == ApplyType.Air.getValue()) {
                List<ApplyTripApplicateExt> applyTripApplicates = applyTripApplicateExtMapper.queryApplyTripApplicateList(applyId);
                ApplyTripInfo applyTripInfo = applyTripMapper.selectByPrimaryKey(applyTripApplicates.get(0).getApplyTripId());
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        payAirOrder(applyTripApplicates.get(0).getOrderId(), approveModel.getPrice(), applyTripInfo.getEstimatedAmount(), 1, clientVersion, approveModel.getSeat_item());
                    }
                }).start();
            } else {
                //订单支付接口
                SaasResponseEntity saasResponseEntity = payOrder(applyId, approveModel.getPrice());
                if (saasResponseEntity.getCode() != GlobalResponseCode.Success.getCode()) {
                    throw new SaasException(saasResponseEntity.getCode(), saasResponseEntity.getMsg(), 0, saasResponseEntity.getData());
                }
            }
            // 同步日志
            applyV2Service.syncLogOnFinishProcess(applyId, companyId);
        }
        setApplyApproverAndPushMessage(apply, userId, ip, finalStatus, receiverId, approverId, approveModel.getComment(), action, skippedApprverIds, logId);
        //push 抄送人
        pushCCMessage(apply, apply.getEmployeeId(), action, finalStatus, null, null);
        return new SaasResponseEntity(GlobalResponseCode.Success, null);
    }

    private FindNextApproverIdAndStatusResult findNextApproverIdAndStatusInFlow(ApplyOrder apply, List<ApplyFlowUserItem> applyFlowUserItems, List<String> skippedApprverIds, boolean couldFinalApprove) throws SaasException {
        String approverId = null;
        String receiverId = null;
        ApplyStatus finalStatus;
        Integer applyStatus = null;
        int remainIndex = applyFlowUserItems.size();
        for (int i = 0; i < applyFlowUserItems.size(); i++) {
            if (ValueTool.areEqual(applyFlowUserItems.get(i).getStatus(), ApplyFlowUserItemStatus.Waiting.getValue())) {
                remainIndex = i;
                break;
            }
        }

        //找下一个审批人
        boolean findNextApprover = false;
        // 是否是最后一名审批人
        boolean finnalApprover = true;
        for (int i = remainIndex; i < applyFlowUserItems.size(); i++) {
            ApplyFlowUserItem item = applyFlowUserItems.get(i);
            if (ValueTool.areEqual(item.getStatus(), ApplyFlowUserItemStatus.Waiting.getValue())) {
                finnalApprover = false;
                if (userService.isUserInCompany(item.getUserId(), apply.getCompanyId())) {
                    findNextApprover = true;
                    receiverId = item.getUserId();
                    approverId = receiverId;
                    applyStatus = 1;
                    applyFlowService.setApplyFlowUserItemStatus(item.getId(), ApplyFlowUserItemStatus.PendingAudit);
                    break;
                } else {
                    skippedApprverIds.add(item.getUserId());
                    applyFlowService.setApplyFlowUserItemStatus(item.getId(), ApplyFlowUserItemStatus.Skip);
                }
            }
        }
        if (finnalApprover && couldFinalApprove) {
            //当前审批人已经是最后一个审批人
            finalStatus = ApplyStatus.Approved;
            receiverId = apply.getEmployeeId();
            applyStatus = 2;
        } else {
            finalStatus = ApplyStatus.PendingAudit;
            if (!findNextApprover) {
                //后面的审批人都已不存在，直接跳到授权负责人
                List<String> superAdminEmployeeIds = customRoleService.getEmployeeIdByRoleId(Integer.toString(UserRole.CompanySuperAdmin.getValue()), apply.getCompanyId());
                if (superAdminEmployeeIds == null || superAdminEmployeeIds.size() == 0) {
                    throw new SaasException(GlobalResponseCode.ApplyCanotApproveBecauseHasNoSuperAdmin);
                } else {
                    receiverId = superAdminEmployeeIds.get(0);
                    approverId = receiverId;
                    applyStatus = 3;
                }
            }
        }
        FindNextApproverIdAndStatusResult result = new FindNextApproverIdAndStatusResult();
        result.setApproverId(approverId);
        result.setReceiverId(receiverId);
        result.setFinnalStatus(finalStatus);
        result.setApplyStatus(applyStatus);
        return result;
    }

    /**
     * 审批、拒绝、转交情况下处理审批日志的状态和修改审批单的状态
     *
     * @param applyId
     * @param receiverId
     * @param approverId
     * @param finalStatus
     * @param now
     * @param ip
     * @param userId
     * @param action
     * @param approveModel
     */
    //@Transactional(value = "saas")
    public Integer sloveApplyLog(String applyId, String receiverId, String approverId, ApplyStatus finalStatus, Date now, String ip, String userId, ApplyLogAction action, ApplyApproveContract approveModel) {
        //查询审核中的审批日志
        List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectApproveUser(applyId, ApplyLogAction.Approval.getValue());
        //有审批日志情况下进行修改操作,无审批日志情况下插入操作
        if (!CollectionUtils.isEmpty(applyOrderLogList)) {
            applyOrderLogExtMapper.updateApplyOrderActionById(action.getValue(), applyOrderLogList.get(0).getId(), receiverId, approveModel.getComment(), new Date(), approveModel.getPrice());
            setApproverAndStatus(applyId, approverId, finalStatus, now, applyOrderLogList.get(0).getId(), approveModel);
            return applyOrderLogList.get(0).getId();
        } else {
            Integer logId = writeLog(applyId, now, ip, userId, receiverId, approveModel.getComment(), action, approveModel.getPrice());
            setApproverAndStatus(applyId, approverId, finalStatus, now, logId, approveModel);
            return logId;
        }
    }

    @Override
    //@Transactional(value = "saas")
    public void setApplyApproverAndPushMessage(ApplyOrder apply, String userId, String ip, ApplyStatus finalStatus, String receiverId, String approverId, String comment, ApplyLogAction action, List<String> skippedApprverIds, Integer logId) {
        Date now = new Date();
        String applyId = apply.getId();
        if (!StringTool.isNullOrEmpty(approverId) && !approverId.equals(apply.getApproverId())) {
            insertApproverMap(applyId, approverId, now);
        }
        List<ApplyTripInfo> applyTripInfos = applyTripInfoExtMapper.selectListByApplyOrderId(apply.getId());
        Integer exceedBuyType = apply.getExceedBuyType();
        if (exceedBuyType != ExceedBuyType.Refund.getValue() && exceedBuyType != ExceedBuyType.Change.getValue()) {
            if (apply.getType().intValue() != ApplyType.Dinner.getValue()) {
                postMessage(apply, apply.getEmployeeId(), receiverId, action, finalStatus, logId, null, null, ApplyTripInfoContract.FromCenterModel(applyTripInfos.get(0)), comment);
            } else {
                postDinnerMessage(ApplyTripInfoContract.FromCenterModel(applyTripInfos.get(0)), apply, apply.getEmployeeId(), receiverId, action, logId, null, null, finalStatus, comment);
            }
        } else {
            ApplyTripInfoContract applyTripInfoContract = ApplyTripInfoContract.FromCenterModel(applyTripInfos.get(0));
            iApplyRefundChangeService.postMessage(apply, apply.getEmployeeId(), receiverId, action, finalStatus, logId, applyTripInfoContract.getOrder_id(), applyTripInfoContract.getType(), applyTripInfoContract, comment);
        }
    }

    /**
     * 驳回
     *
     * @param approveModel
     * @param userId
     * @param ip
     * @return
     */
    @Override
    //@Transactional(value = "saas")
    public GlobalResponseCode repulse(ApplyApproveContract approveModel, String userId, String companyId, String ip, String clientVersion, String source) throws SaasException {
        FlowCheckUtil.check(companyId, clientVersion, source);
        if (approveModel == null) {
            return GlobalResponseCode.ApplyIsNull;
        }
        if (StringTool.isNullOrEmpty(approveModel.getApply_id())) {
            return GlobalResponseCode.ApplyIdInvalid;
        }
        if (approveModel.getComment() != null && approveModel.getComment().length() > 200) {
            return GlobalResponseCode.ApplyCheckReasonInvalid;
        }

        if (approveModel.getComment() == null || approveModel.getComment().length() == 0) {
            //驳回审批单必须要填写理由
            return GlobalResponseCode.ApplyRefuseMustWriteReason;
        }
        String applyId = approveModel.getApply_id();
        ApplyOrder apply = applyMapper.selectByPrimaryKey(approveModel.getApply_id());
        if (apply == null) {
            return GlobalResponseCode.NotFound;
        }
        if (apply.getState() != null && apply.getState().intValue() == ApplyStatus.Overtime.getValue()) {
            //事中审批已超时
            return GlobalResponseCode.CenterApplyOverTime;
        }
        if (apply.getState() != null && apply.getState().intValue() == ApplyStatus.Backout.getValue()) {
            //事中审批已撤销
            return GlobalResponseCode.CenterApplyCancleOrder;
        }
        if (!apply.getCompanyId().equals(companyId)) {
            //申请单和当前人不在一个公司
            return GlobalResponseCode.ApplyCompanyCannotMatch;
        }
        if (apply.getState() != ApplyStatus.PendingAudit.getValue()) {
            //该单不是待审核,不能审批
            return GlobalResponseCode.ApplyStatusNotPendingAudit;
        }

        if (!apply.getApproverId().equals(userId)) {
            //不是当前人可以处理的审批单
            return GlobalResponseCode.NoAuth;
        }
        ApplyStatus finalStatus;
        ApplyLogAction action;
        String receiverId; //下一个审批人/通知人
        String approverId; //审批人
        Integer logId = null;
        List<String> skippedApprverIds = new ArrayList<>();//固定审批流跳过的审批人
        Date now = new Date();
        //拒绝
        List<ApplyFlowUserItem> applyFlowUserItems = applyFlowService.getFlowItemsByApplyId(applyId);
        for (ApplyFlowUserItem applyFlowUserItem : applyFlowUserItems) {
            if (ValueTool.areEqual(applyFlowUserItem.getStatus(), ApplyFlowUserItemStatus.PendingAudit.getValue())
                    && applyFlowUserItem.getUserId().equals(userId)) {
                applyFlowService.setApplyFlowUserItemStatus(applyFlowUserItem.getId(), ApplyFlowUserItemStatus.Refuse);
            }
        }
        finalStatus = ApplyStatus.Return;
        action = ApplyLogAction.Refuse;
        receiverId = apply.getEmployeeId();
        approverId = userId;
        //处理审批日志和审批单的状态
        logId = sloveApplyLog(applyId, receiverId, approverId, finalStatus, now, ip, userId, action, approveModel);
        if (apply.getType() != ApplyType.Mall.getValue()) {
            if (apply.getType().intValue() != ApplyType.Dinner.getValue()) {
                //取消订单接口
                cancelOrder(applyId, 1, finalStatus, null);
            } else {
                cancelMealCoupon(applyId, 1);
            }
        }
        // 同步日志
        applyV2Service.syncLogOnFinishProcess(applyId, companyId);
        setApplyApproverAndPushMessage(apply, userId, ip, finalStatus, receiverId, approverId, approveModel.getComment(), action, skippedApprverIds, logId);
        return GlobalResponseCode.Success;
    }


    /**
     * 超时申请单处理
     *
     * @param approveModel
     * @param userId
     * @param ip
     * @return
     */
    @Override
    //@Transactional
    public GlobalResponseCode overtime(ApplyApproveContract approveModel, String userId, String companyId, String ip) {
        if (approveModel == null) {
            return GlobalResponseCode.ApplyIsNull;
        }
        if (StringTool.isNullOrEmpty(approveModel.getApply_id())) {
            return GlobalResponseCode.ApplyIdInvalid;
        }
        String applyId = approveModel.getApply_id();
        ApplyOrder apply = applyMapper.selectByPrimaryKey(approveModel.getApply_id());
        if (apply == null) {
            return GlobalResponseCode.NotFound;
        }
        /*if (!apply.getCompanyId().equals(companyId)) {
            //申请单和当前人不在一个公司
            return GlobalResponseCode.ApplyCompanyCannotMatch;
        }*/
        if (apply.getState() != ApplyStatus.PendingAudit.getValue()) {
            //该单不是待审核,不能审批
            return GlobalResponseCode.ApplyStatusNotPendingAudit;
        }
        /*if (!apply.getApproverId().equals(userId)) {
            //不是当前人可以处理的审批单
            return GlobalResponseCode.NoAuth;
        }*/

        ApplyStatus finalStatus;
        ApplyLogAction action;
        String receiverId; //下一个审批人/通知人
        String approverId; //审批人
        Integer logId = null;
        List<String> skippedApprverIds = new ArrayList<>();//固定审批流跳过的审批人
        Date now = new Date();
        //超时
        List<ApplyFlowUserItem> applyFlowUserItems = applyFlowService.getFlowItemsByApplyId(applyId);
        for (ApplyFlowUserItem applyFlowUserItem : applyFlowUserItems) {
            if (ValueTool.areEqual(applyFlowUserItem.getStatus(), ApplyFlowUserItemStatus.PendingAudit.getValue())
                    && applyFlowUserItem.getUserId().equals(userId)) {
                applyFlowService.setApplyFlowUserItemStatus(applyFlowUserItem.getId(), ApplyFlowUserItemStatus.Overtime);
            }
        }
        finalStatus = ApplyStatus.Overtime;
        action = ApplyLogAction.Overtime;
        receiverId = apply.getEmployeeId();
        approverId = userId;
        //处理审批日志和审批单的状态
        logId = sloveApplyLog(applyId, receiverId, approverId, finalStatus, now, ip, userId, action, approveModel);
        setApplyApproverAndPushMessage(apply, userId, ip, finalStatus, receiverId, approverId, approveModel.getComment(), action, skippedApprverIds, logId);
        return GlobalResponseCode.Success;
    }

    /**
     * 撤销
     *
     * @param userId
     * @param companyId
     * @param ip
     * @return
     */
    @Override
    //@Transactional(value = "fenbeitong")
    public GlobalResponseCode revoke(String userId, String companyId, ApplyRevokeContract data, String ip, String clientVersion, String source) {
        FlowCheckUtil.check(companyId, clientVersion, source);
        if (data == null || StringUtils.isBlank(data.getId())) {
            return GlobalResponseCode.ParameterError;
        }
        ApplyOrder order = getApplyOrderModelByIdAndUserIdAndCompanyId(data.getId(), userId, companyId, null);
        if (order == null) {
            return GlobalResponseCode.NotFound;
        }
        if (order.getState() != ApplyStatus.PendingAudit.getValue()) {
            return GlobalResponseCode.ApplyStatusNotPendingAudit;
        }
        Date now = new Date();
        ApplyLogAction action = ApplyLogAction.Revoke;
        int logSort = 0;
        List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectListByCreateTimeDesc(data.getId());
        if (!CollectionUtils.isEmpty(applyOrderLogList)) {
            Integer sort = applyOrderLogList.get(0).getSort();
            if (sort != null) {
                logSort = sort + 100;
            }
        }
        //插入日志
        ApplyOrderLog log = new ApplyOrderLog();
        log.setApplyOrderId(data.getId());
        log.setCreateTime(now);
        log.setIp(ip);
        log.setSponsorId(userId);
        log.setReceiverId(userId);
        log.setCheckReason(null);
        log.setAction(action.getValue());
        log.setSort(logSort);
        log.setRootApplyOrderId(data.getId());
        applyLogMapper.insertSelective(log);
        applyOrderExtMapper.setCenterStatus(data.getId(), ApplyStatus.Backout.getValue(), now, log.getId(), 2);
        if (data.getType() == null || data.getType() != 1) {
            if (order.getType().intValue() != ApplyType.Dinner.getValue()) {
                //取消订单接口
                cancelOrder(data.getId(), 1, ApplyStatus.Backout, null);
            } else {
                cancelMealCoupon(data.getId(), 2);
            }
        }
        // 同步日志
        applyV2Service.syncLogOnFinishProcess(data.getId(), companyId);
        //postMessage(order, userId, order.getApproverId(), action, ApplyStatus.Backout, log.getId(), null, null);
        return GlobalResponseCode.Success;
    }

    /**
     * 通过审批单Web
     *
     * @param approveModel
     * @return
     */
    @Override
    //@Transactional(value = "saas")
    public SaasResponseEntity approveWeb(ApplyApproveContract approveModel) throws SaasException {
        if (approveModel == null) {
            throw new SaasException(GlobalResponseCode.ApplyIsNull);
        }
        if (StringTool.isNullOrEmpty(approveModel.getApply_id())) {
            throw new SaasException(GlobalResponseCode.ApplyIdInvalid);
        }
        if (approveModel.getComment() != null && approveModel.getComment().length() > 200) {
            throw new SaasException(GlobalResponseCode.ApplyCheckReasonInvalid);
        }
        String applyId = null;
        String userId = null;
        try {
            applyId = EncryptUtil.decryptAES(URLDecoder.decode(approveModel.getApply_id()), HostPropertyConfigTool.SIGN_KEY);
            userId = EncryptUtil.decryptAES(URLDecoder.decode(approveModel.getUser_id()), HostPropertyConfigTool.SIGN_KEY);
            logger.info("H5审批的审批单id:" + applyId + " H5审批审批人的用户id:" + userId);
            if (StringUtils.isBlank(applyId)) {
                throw new SaasException(GlobalResponseCode.ApplyIdInvalid);
            }

        } catch (Exception e) {
            logger.error("解析applyId报错" + approveModel.getApply_id() + e.getMessage());
        }
        ApplyOrder apply = applyMapper.selectByPrimaryKey(applyId);
        if (apply == null) {
            throw new SaasException(GlobalResponseCode.ApplyCenterNotFound);
        }
        if (apply.getState() == ApplyStatus.Overtime.getValue()) {
            //事中审批已超时
            throw new SaasException(GlobalResponseCode.CenterApplyOverTime);
        }
        if (apply.getState() == ApplyStatus.Backout.getValue()) {
            //事中审批已撤销
            throw new SaasException(GlobalResponseCode.CenterApplyCancleOrder);
        }
        if (apply.getState() != ApplyStatus.PendingAudit.getValue()) {
            //该单不是待审核,不能审批
            throw new SaasException(GlobalResponseCode.ApplyStatusNotPendingAudit);
        }
        if (!apply.getApproverId().equals(userId)) {
            //不是当前人可以处理的审批单
            throw new SaasException(GlobalResponseCode.NoAuth);
        }
        Integer automaticOrder = SaasMessageConstant.IS_CHECKED_FALSE;
        if (apply.getType().intValue() == ApplyType.Mall.getValue()) {
            List<ApplyTripInfo> applyTripInfoList = applyTripInfoExtMapper.queryAllTripListByApplyOrderId(applyId);
            if (CollectionUtils.isNotEmpty(applyTripInfoList)) {
                ApplyTripInfo tripInfo = applyTripInfoList.get(0);
                JSONObject jsonObject = JSON.parseObject(tripInfo.getContent());
                if (jsonObject != null) {
                    automaticOrder = ObjUtils.toInteger(jsonObject.get("automatic_order"));
                    automaticOrder = automaticOrder == null ? SaasMessageConstant.IS_CHECKED_FALSE : automaticOrder;
                }
            }
        }
        ApplyStatus finalStatus;
        ApplyLogAction action;
        String receiverId; //下一个审批人/通知人
        String approverId; //审批人
        Integer logId = null;
        List<String> skippedApprverIds = new ArrayList<>();//固定审批流跳过的审批人
        Date now = new Date();
        action = ApplyLogAction.Approve;
        approverId = userId;
        receiverId = null;
        Integer finalJudgmentUser = null;
        boolean isFlow = (ValueTool.areEqual(apply.getFlowType(), CompanyApplyType.Flow.getValue()) || ValueTool.areEqual(apply.getFlowType(), CompanyApplyType.CONDITIONAL.getValue()));
        if (isFlow) {
            //固定审批流和分条件审批流
            List<ApplyFlowUserItem> applyFlowUserItems = applyFlowService.getFlowItemsByApplyId(applyId);
            if (applyFlowUserItems == null ||
                    applyFlowUserItems.size() == 0 ||
                    applyFlowUserItems.stream().allMatch(m ->
                            !ValueTool.areEqual(m.getStatus(), ApplyFlowUserItemStatus.PendingAudit.getValue())
                                    && !ValueTool.areEqual(m.getStatus(), ApplyFlowUserItemStatus.Transfered.getValue())
                    )) {
                //没有待审核记录，也没有转交记录，预示审批流已经审核通过
                finalStatus = ApplyStatus.Approved;
                receiverId = apply.getEmployeeId();
                //处理审批日志和审批单的状态
                logId = sloveApplyLog(applyId, receiverId, approverId, finalStatus, now, "", userId, action, approveModel);
                finalJudgmentUser = 2;
            } else {
                int pendingAuditItemIndex = -1;
                for (int i = 0; i < applyFlowUserItems.size(); i++) {
                    if (ValueTool.areEqual(applyFlowUserItems.get(i).getStatus(), ApplyFlowUserItemStatus.PendingAudit.getValue())) {
                        pendingAuditItemIndex = i;
                        break;
                    }
                }
                ApplyFlowUserItem pendingAuditItem = null;
                if (pendingAuditItemIndex >= 0) {
                    pendingAuditItem = applyFlowUserItems.get(pendingAuditItemIndex);
                }
                if (pendingAuditItem != null && !pendingAuditItem.getUserId().equals(userId)) {
                    logger.info("当前待审批的item不是userid，userId：" + userId + " itemId:" + pendingAuditItem.getUserId());
                    throw new SaasException(GlobalResponseCode.NoAuth);
                        /*//当前待审批的item不是userid，所在重新再将审批人设置为此item
                        finalStatus = ApplyStatus.PendingAudit;
                        receiverId = pendingAuditItem.getUserId();
                        approverId = receiverId;

                        //查询审核中的审批日志
                        List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectApproveUser(applyId, ApplyLogAction.Approval.getValue());
                        //有日志数据情况下修改操作无日志数据情况下插入操作
                        if (!CollectionUtils.isEmpty(applyOrderLogList)) {
                            applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Approve.getValue(), applyOrderLogList.get(0).getId(), receiverId, null, new Date(), approveModel.getPrice());
                            setApproverAndStatus(applyId, approverId, finalStatus, now, applyOrderLogList.get(0).getId(), approveModel);
                            List<ApplyOrderLog> applyOrderNextUserLogList = applyOrderLogExtMapper.selectNextApproveUser(applyId, ApplyLogAction.Unknown.getValue());
                            if (!CollectionUtils.isEmpty(applyOrderNextUserLogList)) {
                                //保存下一个审批人为审批中状态
                                applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Approval.getValue(), applyOrderNextUserLogList.get(0).getId(), "", null, null, approveModel.getPrice());
                                logId = applyOrderNextUserLogList.get(0).getId();
                            }
                        } else {
                            logId = writeLog(applyId, now, "", userId, receiverId, approveModel.getComment(), action, approveModel.getPrice());
                            setApproverAndStatus(applyId, approverId, finalStatus, now, logId, approveModel);
                        }*/
                } else {
                    if (pendingAuditItem != null) {
                        //将item status设置为已审核
                        applyFlowService.setApplyFlowUserItemStatus(pendingAuditItem.getId(), ApplyFlowUserItemStatus.Approved);
                    }
                    try {
                        FindNextApproverIdAndStatusResult findNextApproverIdAndStatusResult = findNextApproverIdAndStatusInFlow(apply, applyFlowUserItems, skippedApprverIds, true);
                        finalJudgmentUser = findNextApproverIdAndStatusResult.getApplyStatus();
                        finalStatus = findNextApproverIdAndStatusResult.getFinnalStatus();
                        if (!StringTool.isNullOrEmpty(findNextApproverIdAndStatusResult.getApproverId())) {
                            approverId = findNextApproverIdAndStatusResult.getApproverId();
                        }
                        if (!StringTool.isNullOrEmpty(findNextApproverIdAndStatusResult.getReceiverId())) {
                            receiverId = findNextApproverIdAndStatusResult.getReceiverId();
                        }
                        if (findNextApproverIdAndStatusResult.getApplyStatus() == 1) {
                            //不是最后一个审批人存在下一个审批人
                            //查询审核中的审批日志
                            List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectApproveUser(applyId, ApplyLogAction.Approval.getValue());
                            //有日志数据情况下修改操作无日志数据情况下插入操作
                            if (!CollectionUtils.isEmpty(applyOrderLogList)) {
                                applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Approve.getValue(), applyOrderLogList.get(0).getId(), receiverId, approveModel.getComment(), new Date(), approveModel.getPrice());
                                setApproverAndStatus(applyId, approverId, finalStatus, now, applyOrderLogList.get(0).getId(), approveModel);
                                List<ApplyOrderLog> applyOrderNextUserLogList = applyOrderLogExtMapper.selectNextApproveUser(applyId, ApplyLogAction.Unknown.getValue());
                                if (!CollectionUtils.isEmpty(applyOrderNextUserLogList)) {
                                    //保存下一个审批人为审批中状态
                                    applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Approval.getValue(), applyOrderNextUserLogList.get(0).getId(), "", null, null, approveModel.getPrice());
                                    logId = applyOrderNextUserLogList.get(0).getId();
                                }
                            } else {
                                logId = writeLog(applyId, now, "", userId, receiverId, approveModel.getComment(), action, approveModel.getPrice());
                                setApproverAndStatus(applyId, approverId, finalStatus, now, logId, approveModel);
                            }
                        } else if (findNextApproverIdAndStatusResult.getApplyStatus() == 2) {
                            //最后一个审批人
                            if (apply.getType() == ApplyType.Mall.getValue() && automaticOrder == SaasMessageConstant.IS_CHECKED_TRUE) {
                                logId = sloveApplyLog(applyId, receiverId, approverId, ApplyStatus.Done, now, "", userId, action, approveModel);
                            } else {
                                logId = sloveApplyLog(applyId, receiverId, approverId, finalStatus, now, "", userId, action, approveModel);
                            }
                        } else {
                            //后面的审批人都不存在直接跳到授权负责人
                            List<ApplyOrderLog> applyOrderApprovalLogList = applyOrderLogExtMapper.selectApproveUser(applyId, ApplyLogAction.Approval.getValue());
                            applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Approve.getValue(), applyOrderApprovalLogList.get(0).getId(), receiverId, approveModel.getComment(), new Date(), approveModel.getPrice());

                            List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectApproveUser(applyId, ApplyLogAction.Unknown.getValue());
                            if (!CollectionUtils.isEmpty(applyOrderLogList)) {
                                for (ApplyOrderLog applyOrderLog : applyOrderLogList) {
                                    applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Skip.getValue(), applyOrderLog.getId(), "", CoreLanguage.Common_Value_ApprovalPersonDelete.getMessage(), null, approveModel.getPrice());
                                }
                            } else {
                                if (skippedApprverIds != null && skippedApprverIds.size() > 0) {
                                    for (String skippedApproverId : skippedApprverIds) {
                                        writeLog(applyId, now, "", skippedApproverId, skippedApproverId, CoreLanguage.Common_Value_ApprovalPersonDelete.getMessage(), ApplyLogAction.Skip, approveModel.getPrice());
                                    }
                                }
                            }
                            int sort = 0;
                            if (!CollectionUtils.isEmpty(applyOrderLogList)) {
                                sort = applyOrderLogList.get(applyOrderLogList.size() - 1).getSort() + 100;
                            }
                            ApplyOrderLog logApprove = new ApplyOrderLog();
                            logApprove.setApplyOrderId(applyId);
                            logApprove.setIp("");
                            logApprove.setSponsorId(approverId);
                            logApprove.setReceiverId("");
                            logApprove.setCheckReason(null);
                            logApprove.setAction(ApplyLogAction.Approval.getValue());
                            logApprove.setSort(sort);
                            logApprove.setRootApplyOrderId(applyId);
                            applyLogMapper.insertSelective(logApprove);
                            logId = logApprove.getId();
                            setApproverAndStatus(applyId, approverId, finalStatus, now, logId, approveModel);
                            //logId = writeLog(applyId, now, ip, userId, receiverId, approveModel.getComment(), action);
                        }
                    } catch (SaasException ex) {
                        logger.info("审批异常：" + ex.getResponseCode());
                        throw new SaasException(GlobalResponseCode.PayOrderCancleOrderSystemError);
                    }
                }
            }
        } else {
            finalStatus = ApplyStatus.Approved;
            receiverId = apply.getEmployeeId();
            approverId = userId;
            //处理审批日志和审批单的状态
            if (apply.getType() == ApplyType.Mall.getValue() && automaticOrder == SaasMessageConstant.IS_CHECKED_TRUE) {
                logId = sloveApplyLog(applyId, receiverId, approverId, ApplyStatus.Done, now, "", userId, action, approveModel);
            } else {
                logId = sloveApplyLog(applyId, receiverId, approverId, finalStatus, now, "", userId, action, approveModel);
            }
        }
        //最后一个审批节点
        if ((apply.getFlowType() == CompanyApplyType.Elastic.getValue() && apply.getType().intValue() != ApplyType.Mall.getValue()) || (finalJudgmentUser != null && finalJudgmentUser == 2 && apply.getType().intValue() != ApplyType.Mall.getValue())) {
            //终审时审批价格
            BigDecimal price = approveModel.getPrice().setScale(2, BigDecimal.ROUND_HALF_UP);
            //占用金额
            BigDecimal occupyPrice = new BigDecimal(apply.getBudget()).divide(ObjUtils.toBigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
            List<ApplyTripInfo> tripList = applyTripInfoExtMapper.selectListByApplyOrderId(applyId);
            String tripContent = tripList.get(0).getTripContent();
            JSONObject jo = JsonUtils.toObj(tripContent, JSONObject.class);
            String costId = jo.getString("costId");
            String orderId = jo.getString("orderId");
            //终审金额大于占用金额 释放并重新占用预算
            if (price.compareTo(occupyPrice) == 1 && StringUtils.isNotBlank(costId)) {
                //修改预算占用
                updateCost(price, orderId, applyId, userId, apply.getCompanyId(), costId, tripList.get(0).getType());
            }
            if (apply.getType().intValue() == ApplyType.Dinner.getValue()) {
                sendMealCoupon(applyId);
            } else if (apply.getType().intValue() == ApplyType.Air.getValue()) {
                List<ApplyTripApplicateExt> applyTripApplicates = applyTripApplicateExtMapper.queryApplyTripApplicateList(applyId);
                ApplyTripInfo applyTripInfo = applyTripMapper.selectByPrimaryKey(applyTripApplicates.get(0).getApplyTripId());
                SaasResponseEntity saasResponseEntity = payAirOrder(applyTripApplicates.get(0).getOrderId(), approveModel.getPrice(), applyTripInfo.getEstimatedAmount(), 2, null, approveModel.getSeat_item());
                if (saasResponseEntity.getCode() != GlobalResponseCode.Success.getCode()) {
                    throw new SaasException(saasResponseEntity.getCode(), saasResponseEntity.getMsg(), 0, saasResponseEntity.getData());
                    //return saasResponseEntity;
                }
            } else {
                //订单支付接口
                SaasResponseEntity saasResponseEntity = payOrder(applyId, approveModel.getPrice());
                if (saasResponseEntity.getCode() != GlobalResponseCode.Success.getCode()) {
                    throw new SaasException(saasResponseEntity.getCode(), saasResponseEntity.getMsg(), 0, saasResponseEntity.getData());
                }
            }
            // 同步日志
            applyV2Service.syncLogOnFinishProcess(applyId, apply.getCompanyId());
        } else if ((apply.getFlowType() == CompanyApplyType.Elastic.getValue() && apply.getType().intValue() == ApplyType.Mall.getValue()) || (finalJudgmentUser != null && finalJudgmentUser == 2 && apply.getType().intValue() == ApplyType.Mall.getValue())) {
            sloveMallOrder(applyId);
            // 同步日志
            applyV2Service.syncLogOnFinishProcess(applyId, apply.getCompanyId());
        }
        setApplyApproverAndPushMessage(apply, approverId, "", finalStatus, receiverId, approverId, approveModel.getComment(), action, skippedApprverIds, logId);
        //push 抄送人
        pushCCMessage(apply, apply.getEmployeeId(), action, finalStatus, null, null);
        return new SaasResponseEntity(GlobalResponseCode.Success, null);
    }

    /**
     * 终审同意采购自动下单
     * @param applyId
     */
    private void sloveMallOrder(String applyId) {
        List<ApplyTripInfo> applyTripInfoList = applyTripInfoExtMapper.queryAllTripListByApplyOrderId(applyId);
        Integer automaticOrder = SaasMessageConstant.IS_CHECKED_FALSE;
        if (CollectionUtils.isNotEmpty(applyTripInfoList)) {
            ApplyTripInfo tripInfo = applyTripInfoList.get(0);
            JSONObject jsonObject = JSON.parseObject(tripInfo.getContent());
            if (jsonObject != null) {
                automaticOrder = ObjUtils.toInteger(jsonObject.get("automatic_order"));
                automaticOrder = automaticOrder == null ? SaasMessageConstant.IS_CHECKED_FALSE : automaticOrder;
            }
        }
        if (automaticOrder == SaasMessageConstant.IS_CHECKED_TRUE) {
            ApplyTripApplicateExample applyTripApplicateExample = new ApplyTripApplicateExample();
            applyTripApplicateExample.createCriteria().andApplyIdEqualTo(applyId);
            List<ApplyTripApplicate> applyTripApplicateList = applyTripApplicateMapper.selectByExample(applyTripApplicateExample);
            if (CollectionUtils.isNotEmpty(applyTripApplicateList)) {
                ThreadPoolUtils.getExecutorService().execute(() -> createMallOrder(applyId, applyTripApplicateList.get(0).getOrderId()));
            }
        }
    }

    /**
     * 采购下单操作
     * @return
     */
    private void createMallOrder(String applyId, String orderId) {
        Map<String, Object> orderInfo = Maps.newHashMap();
        orderInfo.put("order_id", orderId);
        orderInfo.put("apply_id", applyId);
        String result = null;
        try {
            result = HttpTool.post(HostPropertyConfigTool.HOST_MALL_BIZ + "/mall_biz/order/apply/approve/success", orderInfo);
            logger.info("获取采购生成订单返回结果：{}", result);
            if (StringUtils.isBlank(result)) {
                throw new SaasException(GlobalResponseCode.PayOrderCancleOrderSystemError);
            }
        } catch (Exception e) {
            logger.error("获取采购生成订单异常信息：{}", e.getMessage());
            throw new SaasException(GlobalResponseCode.PayOrderCancleOrderSystemError);
        }
    }

    /**
     * 驳回Web
     *
     * @param approveModel
     * @return
     */
    @Override
    //@Transactional
    public GlobalResponseCode repulseWeb(ApplyApproveContract approveModel) {
        if (approveModel == null) {
            return GlobalResponseCode.ApplyIsNull;
        }
        if (StringTool.isNullOrEmpty(approveModel.getApply_id())) {
            return GlobalResponseCode.ApplyIdInvalid;
        }
        if (approveModel.getComment() != null && approveModel.getComment().length() > 200) {
            return GlobalResponseCode.ApplyCheckReasonInvalid;
        }

        if (approveModel.getComment() == null || approveModel.getComment().length() == 0) {
            //驳回审批单必须要填写理由
            return GlobalResponseCode.ApplyRefuseMustWriteReason;
        }
        String applyId = null;
        String userId = null;
        try {
            applyId = EncryptUtil.decryptAES(URLDecoder.decode(approveModel.getApply_id()), HostPropertyConfigTool.SIGN_KEY);
            userId = EncryptUtil.decryptAES(URLDecoder.decode(approveModel.getUser_id()), HostPropertyConfigTool.SIGN_KEY);
            logger.info("H5审批驳回的审批单id:" + applyId + " H5审批驳回审批人的用户id:" + userId);
            if (StringUtils.isBlank(applyId)) {
                return GlobalResponseCode.ApplyIdInvalid;
            }
        } catch (Exception e) {
            logger.error("解析applyId报错" + approveModel.getApply_id() + e.getMessage());
        }
        ApplyOrder apply = applyMapper.selectByPrimaryKey(applyId);
        if (apply == null) {
            return GlobalResponseCode.NotFoundData;
        }
        if (apply.getState() == ApplyStatus.Overtime.getValue()) {
            //事中审批已超时
            return GlobalResponseCode.CenterApplyOverTime;
        }
        if (apply.getState() == ApplyStatus.Backout.getValue()) {
            //事中审批已撤销
            return GlobalResponseCode.CenterApplyCancleOrder;
        }
        if (apply.getState() != ApplyStatus.PendingAudit.getValue()) {
            //该单不是待审核,不能审批
            return GlobalResponseCode.ApplyStatusNotPendingAudit;
        }
        if (!apply.getApproverId().equals(userId)) {
            //不是当前人可以处理的审批单
            return GlobalResponseCode.NoAuth;
        }
        ApplyStatus finalStatus;
        ApplyLogAction action;
        String receiverId; //下一个审批人/通知人
        String approverId; //审批人
        Integer logId = null;
        List<String> skippedApprverIds = new ArrayList<>();//固定审批流跳过的审批人
        Date now = new Date();
        //拒绝
        List<ApplyFlowUserItem> applyFlowUserItemList = applyFlowUserItemMapper.selectFlowItemsByApplyId(applyId);
        if (CollectionUtils.isNotEmpty(applyFlowUserItemList)) {
            userId = applyFlowUserItemList.get(0).getUserId();
        }
        for (ApplyFlowUserItem applyFlowUserItem : applyFlowUserItemList) {
            if (ValueTool.areEqual(applyFlowUserItem.getStatus(), ApplyFlowUserItemStatus.PendingAudit.getValue())) {
                applyFlowService.setApplyFlowUserItemStatus(applyFlowUserItem.getId(), ApplyFlowUserItemStatus.Refuse);
                userId = applyFlowUserItem.getUserId();
            }
        }
        finalStatus = ApplyStatus.Return;
        action = ApplyLogAction.Refuse;
        receiverId = apply.getEmployeeId();
        approverId = userId;
        //处理审批日志和审批单的状态
        logId = sloveApplyLog(applyId, receiverId, approverId, finalStatus, now, "", userId, action, approveModel);
        if (apply.getType() != ApplyType.Dinner.getValue() && apply.getType() != ApplyType.Mall.getValue()) {
            //取消订单接口
            cancelOrder(applyId, 1, finalStatus, null);
        } else if (apply.getType() == ApplyType.Dinner.getValue()) {
            cancelMealCoupon(applyId, 1);
        }
        // 同步日志
        applyV2Service.syncLogOnFinishProcess(applyId, apply.getCompanyId());
        setApplyApproverAndPushMessage(apply, userId, "", finalStatus, receiverId, approverId, approveModel.getComment(), action, skippedApprverIds, logId);
        return GlobalResponseCode.Success;
    }

    /**
     * 处理超时审批单定时任务
     */
    @Override
    //@Transactional(value = "saas")
    public void sloveOvertimeApplyOrder(ApplyOrder applyOrder) throws SaasException {
        String applyId = applyOrder.getId();
        Integer exceedBuyType = applyOrder.getExceedBuyType();
        if (applyOrder.getState() == ApplyStatus.PendingAudit.getValue()) {
            ApplyStatus finalStatus;
            ApplyLogAction action;
            String receiverId; //下一个审批人/通知人
            String approverId = ""; //审批人
            Integer logId = null;
            List<String> skippedApprverIds = new ArrayList<>();//固定审批流跳过的审批人
            Date now = new Date();
            //超时
            List<ApplyFlowUserItem> applyFlowUserItems = applyFlowService.getFlowItemsByApplyId(applyId);
            boolean isFlow = (ValueTool.areEqual(applyOrder.getFlowType(), CompanyApplyType.Flow.getValue()) || ValueTool.areEqual(applyOrder.getFlowType(), CompanyApplyType.CONDITIONAL.getValue()));
            if (isFlow) {
                for (ApplyFlowUserItem applyFlowUserItem : applyFlowUserItems) {
                    if (ValueTool.areEqual(applyFlowUserItem.getStatus(), ApplyFlowUserItemStatus.PendingAudit.getValue())) {
                        applyFlowService.setApplyFlowUserItemStatus(applyFlowUserItem.getId(), ApplyFlowUserItemStatus.Overtime);
                        approverId = applyFlowUserItem.getUserId();
                        break;
                    }
                }
            }
            finalStatus = ApplyStatus.Overtime;
            action = ApplyLogAction.Overtime;
            receiverId = applyOrder.getEmployeeId();
            //处理审批日志和审批单的状态
            ApplyApproveContract applyApproveContract = new ApplyApproveContract();
            applyApproveContract.setComment("");
            if (applyOrder.getFlowType() == CompanyApplyType.Elastic.getValue() && StringUtils.isBlank(approverId)) {
                approverId = applyOrder.getApproverId();
            }
            logId = sloveApplyLog(applyId, receiverId, approverId, finalStatus, now, "", approverId, action, applyApproveContract);
            if (applyOrder.getType().intValue() != ApplyType.FenbeiCoupon.getValue()) {
                if (applyOrder.getType().intValue() != ApplyType.Dinner.getValue()) {
                    //取消订单接口
                    cancelOrder(applyId, 2, finalStatus, applyOrder.getOvertime());
                } else {
                    overTimeMealCoupon(applyId);
                }
                setApplyApproverAndPushMessage(applyOrder, approverId, "", finalStatus, receiverId, approverId, "", action, skippedApprverIds, logId);
            } else {
                iApplyCouponService.setApplyApproverAndPushMessage(applyOrder, finalStatus, receiverId, approverId, action, logId);
            }
        }
    }

    /**
     * 订单支付接口
     *
     * @param applyId
     * @throws SaasException
     */
    public SaasResponseEntity payOrder(String applyId, BigDecimal price) throws SaasException {
        List<ApplyTripApplicateExt> applyTripApplicates = applyTripApplicateExtMapper.queryApplyTripApplicateList(applyId);
        ApplyTripInfo applyTripInfo = applyTripMapper.selectByPrimaryKey(applyTripApplicates.get(0).getApplyTripId());
        Integer type = applyTripApplicates.get(0).getType();
        String data = null;
        try {
            if (price == null || price.compareTo(BigDecimal.valueOf(0)) != 1) {
                price = applyTripInfo.getEstimatedAmount();
            }
            if (type == BizType.IntlAir.getValue()) {
                logger.info("审批通过国际机票订单支付:" + URL_GET_INTL_AIR_PAY + "?order_id=" + applyTripApplicates.get(0).getOrderId() + "&during_apply_id=" + applyId + "&total_price=" + price);
                JSONObject jo = new JSONObject();
                jo.put("order_id", applyTripApplicates.get(0).getOrderId());
                jo.put("during_apply_id", applyId);
                jo.put("total_price", price);
                data = HttpTool.post(URL_GET_INTL_AIR_PAY, jo);
                logger.info("获取国际机票订单支付的返回结果:" + data);
            } else if (type == BizType.Train.getValue()) {
                logger.info("审批通过火车订单支付:" + URL_GET_TRAIN_ORDER_PAY + "?order_id=" + applyTripApplicates.get(0).getOrderId() + "&during_apply_id=" + applyId + "&total_price=" + price);
                JSONObject jo = new JSONObject();
                jo.put("order_id", applyTripApplicates.get(0).getOrderId());
                jo.put("during_apply_id", applyId);
                jo.put("total_price", price);
                data = HttpTool.post(URL_GET_TRAIN_ORDER_PAY, jo);
                logger.info("获取国际机票订单支付的返回结果:" + data);
            } else {
                Integer busiType = null;
                String url = URL_GET_ORDER_PAY;
                if (type == BizType.Air.getValue()) {
                    busiType = 1;
                } else if (type == BizType.Hotel.getValue() || type == BizType.IntlHotel.getValue()) {
                    busiType = 2;
                    url = URL_GET_HOTEL_ORDER_PAY;
                } else if (type == BizType.Train.getValue()) {
                    busiType = 3;
                }
                logger.info("审批通过订单支付:" + url + "?order_id=" + applyTripApplicates.get(0).getOrderId() + "&busi_type=" + busiType + "&totalPrice=" + price);
                data = HttpTool.get(url + "?order_id=" + applyTripApplicates.get(0).getOrderId() + "&busi_type=" + busiType + "&totalPrice=" + price);
                logger.info("获取订单支付的返回结果:" + data);
                if (type == BizType.Hotel.getValue() || type == BizType.IntlHotel.getValue()) {
                    Map<String, Object> jo = JSONObject.parseObject(data, HashMap.class);
                    Integer code = ObjUtils.toInteger(jo.get("code"));
                    if (code.equals(152020)) {
                        SaasResponseEntity saasResponseEntity = new SaasResponseEntity(code, ObjUtils.toString(jo.get("msg")), null);
                        return saasResponseEntity;
                    }
                }
            }
        } catch (Exception e) {
            logger.error(String.format("url:%s,异常", URL_GET_ORDER_PAY) + e.getMessage());
            throw new SaasException(GlobalResponseCode.PayOrderCancleOrderSystemError);
        }
        if (StringUtils.isBlank(data)) {
            throw new SaasException(GlobalResponseCode.PayOrderCancleOrderSystemError);
        }
        return new SaasResponseEntity(GlobalResponseCode.Success, null);
    }

    /**
     * 订单取消接口
     *
     * @param applyId
     * @throws SaasException
     */
    private void cancelOrder(String applyId, Integer sourceReasonType, ApplyStatus applyStatus, Date overTime) throws SaasException {
        List<ApplyTripApplicateExt> applyTripApplicates = applyTripApplicateExtMapper.queryApplyTripApplicateList(applyId);
        Integer type = applyTripApplicates.get(0).getType();
        String data = null;
        try {
            if (type == BizType.IntlAir.getValue()) {
                logger.info("审批订单国际机票取消接口:" + URL_GET_INTL_AIR_CANCLE + "?order_id=" + applyTripApplicates.get(0).getOrderId());
                JSONObject jo = new JSONObject();
                jo.put("order_id", applyTripApplicates.get(0).getOrderId());
                data = HttpTool.post(URL_GET_INTL_AIR_CANCLE, jo);
                logger.info("审批订单国际机票取消接口:" + data);
            } else {
                if (type == BizType.Air.getValue()) {
                    String reason = null;
                    Boolean autoCancel = null;
                    Boolean notCancelApplay = true;
                    if (applyStatus == ApplyStatus.Backout) {
                        reason = "事中审批发起的取消";
                        autoCancel = false;
                        notCancelApplay = true;
                    } else if (applyStatus == ApplyStatus.Return) {
                        reason = "审批人拒绝了您提交的费用申请，订单取消";
                        autoCancel = false;
                        notCancelApplay = true;
                    } else if (applyStatus == ApplyStatus.Overtime) {
                        String overDate = DateTimeTool.fromDateTimeToString(overTime);
                        reason = "审批人未在" + overDate + "前完成审批，订单关闭";
                        autoCancel = true;
                        notCancelApplay = true;
                    }
                    logger.info("审批订单国内机票取消接口:" + URL_GET_AIR_CANCLE + "?order_id=" + applyTripApplicates.get(0).getOrderId() + "&reason=" + reason + "&autoCancel=" + autoCancel + "&notCancelApplay=" + notCancelApplay);
                    JSONObject jo = new JSONObject();
                    jo.put("order_id", applyTripApplicates.get(0).getOrderId());
                    data = HttpTool.get(URL_GET_AIR_CANCLE + "?order_id=" + applyTripApplicates.get(0).getOrderId() + "&reason=" + URLEncoder.encode(reason) + "&autoCancel=" + autoCancel + "&notCancelApplay=" + notCancelApplay);
                    logger.info("审批订单国内机票取消接口返回结果:" + data);
                } else {
                    Integer busiType = null;
                    String url = URL_GET_ORDER_CANCLE;
                    if (type == BizType.Hotel.getValue() || type == BizType.IntlHotel.getValue()) {
                        busiType = 2;
                        url = URL_GET_HOTEL_ORDER_CANCLE;
                    } else if (type == BizType.Train.getValue()) {
                        busiType = 3;
                    }
                    logger.info("审批订单取消接口:" + url + "?order_id=" + applyTripApplicates.get(0).getOrderId() + "&busi_type=" + busiType + "&source_reason_type=" + sourceReasonType);
                    data = HttpTool.get(url + "?order_id=" + applyTripApplicates.get(0).getOrderId() + "&busi_type=" + busiType + "&source_reason_type=" + sourceReasonType);
                    logger.info("获取订单取消的返回结果:" + data);
                }
            }
        } catch (Exception e) {
            logger.error(String.format("url:%s,异常", URL_GET_ORDER_CANCLE) + e.getMessage());
            throw new SaasException(GlobalResponseCode.PayOrderCancleOrderSystemError);
        }
        if (StringUtils.isBlank(data)) {
            throw new SaasException(GlobalResponseCode.PayOrderCancleOrderSystemError);
        }
    }

    /**
     * 通过订单id、审批单id获取审批单详情
     *
     * @param orderId
     * @param applyId
     * @return
     * @throws SaasException
     */
    @Override
    public ApplyV2Contract getCenterInfo(String orderId, String applyId) throws SaasException {
        if (true) {
            throw new SaasException(GlobalResponseCode.APPLYFLOWDETAILERROR);
        }
        List<ApplyOrder> applyOrderList = applyOrderExtMapper.queryCenterApply(applyId, orderId);
        if (CollectionUtils.isEmpty(applyOrderList)) {
            return null;
        }
        ApplyOrderV2Contract apply = ApplyOrderV2Contract.FromModel(applyOrderList.get(0));
        //返回当前时间戳
        apply.setCurrent_time(DateTimeTool.fromDateTimeToString(new Date()));
        ApplyV2Contract applyContract = new ApplyV2Contract();
        applyContract.setApply(apply);
        /*if (apply.getType() != ApplyType.Mall.getValue()) {
            //不是采购申请,读取行程
            List<ApplyTripInfoContract> tripList = getTripListByApplyId(applyId, apply);
            applyContract.setTrip_list(tripList);
        }
        if (apply.getType() == ApplyType.ChaiLv.getValue()) {
            //差旅有同行人信息
            List<UserContactContract> guestList = getGuestListByApplyOrderId(applyId);
            applyContract.setGuest_list(guestList);
        }
        if (applyContract == null) {
            return null;
        }
        //1、申请人及审批人的姓名及部门
        String employeeId = apply.getEmployee_id();
        List<String> idList = Lists.newArrayList();
        idList.add(employeeId);
        List<EmployeeCompanyInfo> ecList = employeeCompanyExtMapper.queryEmployeeCompanyInfoList(idList);
        if (CollectionUtils.isNotEmpty(ecList)) {
            for (EmployeeCompanyInfo employeeCompanyInfo : ecList) {
                if (employeeCompanyInfo.getId().equals(employeeId)) {
                    apply.setUser_name(employeeCompanyInfo.getUserName());
                    apply.setUser_dept(employeeCompanyInfo.getOrgName());
                }
            }
        }
        // 2、可操作权限
        applyContract.getApply().setOperate_auth(0);
        // 2、可操作权限
        //genApplyOperateAuth(apply, userId);
        //处理审批流设置
        appendFlowData(applyContract.getApply());
        //处理审批流设置
        List<ApplyOrderLogContract> logs = getLogsByApplyId(applyId);
        applyContract.setLog_list(logs);
        applyContract.setOrder_info(disposeOrderInfo(applyContract.getTrip_list().get(0).getOrder_id(), applyContract.getTrip_list().get(0).getType(), employeeId));*/
        return applyContract;
    }

    /**
     * 通过审批单id获取审批单详情
     *
     * @param applyId
     * @return
     * @throws SaasException
     */
    @Override
    public ApplyV2Contract getCenterDetail(String applyId, String userId) throws SaasException {
        try {
            applyId = EncryptUtil.decryptAES(URLDecoder.decode(applyId), HostPropertyConfigTool.SIGN_KEY);
            userId = EncryptUtil.decryptAES(URLDecoder.decode(userId), HostPropertyConfigTool.SIGN_KEY);
            logger.info("H5审批审批单详情的审批单id:" + applyId + " H5审批审批单详情的用户id:" + userId);
            if (StringUtils.isBlank(applyId) || StringUtils.isBlank(userId)) {
                return null;
            }
        } catch (Exception e) {
            logger.error("解析applyId报错" + applyId + e.getMessage());
        }
        ApplyOrder applyOrder = applyMapper.selectByPrimaryKey(applyId);
        if (applyOrder == null) {
            return null;
        }
        ApplyOrderV2Contract apply = ApplyOrderV2Contract.FromModel(applyOrder);
        if (StringUtils.isNotBlank(apply.getExceed_buy_desc())) {
            String[] exceedBugDescs = apply.getExceed_buy_desc().split("\n");
            List<String> exceedBuyDescList = Arrays.asList(exceedBugDescs);
            apply.setExceed_buy_desc_list(exceedBuyDescList);
        }
        List<String> skippedApprverIds = new ArrayList<>();//固定审批流跳过的审批人
        List<ApplyFlowUserItem> applyFlowUserItems = applyFlowService.getFlowItemsByApplyId(applyId);
        if (applyOrder.getFlowType() == CompanyApplyType.Elastic.getValue()) {
            apply.setNode_status(1);
        } else {
            FindNextApproverIdAndStatusResult findNextApproverIdAndStatusResult = findStatusInFlow(applyOrder, applyFlowUserItems, skippedApprverIds, true);
            //最后审批人
            if (findNextApproverIdAndStatusResult != null && findNextApproverIdAndStatusResult.getApplyStatus() == 2) {
                apply.setNode_status(1);
            } else {
                apply.setNode_status(2);
            }
        }
        ApplyCostAttributionExample applyCostAttributionExample = new ApplyCostAttributionExample();
        applyCostAttributionExample.createCriteria().andApplyIdEqualTo(applyId).andCompanyIdEqualTo(applyOrder.getCompanyId());
        applyCostAttributionExample.setOrderByClause("id asc");
        List<ApplyCostAttribution> applyCostAttributionList = applyCostAttributionMapper.selectByExample(applyCostAttributionExample);
        if (CollectionUtils.isNotEmpty(applyCostAttributionList)) {
            List<String> costAttributionNameList = applyCostAttributionList.stream().map(applyCostAttribution -> applyCostAttribution.getCostAttributionName()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(costAttributionNameList)) {
                String costAttributionNameStr = StringUtils.join(costAttributionNameList, "；");
                apply.setOrder_cost_attribution(costAttributionNameStr);
            }
        }
        //返回当前时间戳
        apply.setCurrent_time(DateTimeTool.fromDateTimeToString(new Date()));
        ApplyV2Contract applyContract = new ApplyV2Contract();
        applyContract.setApply(apply);
        List<ApplyTripInfoContract> tripList = getTripListByApplyOrderId(applyId, applyOrder);
        applyContract.setTrip_list(tripList);
        if (apply.getType() != ApplyType.Taxi.getValue() && apply.getType() != ApplyType.Mall.getValue() && apply.getType() != ApplyType.Dinner.getValue()
                && apply.getType() != ApplyType.Meishi.getValue() && apply.getType() != ApplyType.TakeAway.getValue()) {
            //差旅有同行人信息
            List<UserContactContract> guestList = getGuestListByApplyOrderId(applyId);
            //酒店多人入住
            if (apply.getType()==ApplyType.Hotel.getValue()&&ObjUtils.isNotEmpty(guestList)){
                //只有有同住人的才会校验金额
                Set<Integer> groupSet=Sets.newSet();
                guestList.forEach(v->{
                    if (ObjUtils.isNotEmpty(v.getOccupier_type())&&v.getOccupier_type()==2){
                        if (ObjUtils.isNotEmpty(v.getRoom_group())){
                            groupSet.add(v.getRoom_group());
                        }
                    }
                });
                //不限制组
                Set<Integer> noLimitGroup=Sets.newSet();
                for(UserContactContract userContactContract:guestList){
                    //多人分组，需要查询单人酒店规则价格限制
                    if (ObjUtils.isNotEmpty(userContactContract.getRoom_group())&&groupSet.contains(userContactContract.getRoom_group())){
                        EmployeeHotelRule employeeHotelRule = iBaseEmployeeHotelRuleService.queryEmployeeHotelRuleByPrimaryKey(userContactContract.getId(), applyOrder.getCompanyId());
                        if (employeeHotelRule == null) {
                            logger.info("未查询到员工酒店规则应用");
                            throw new SaasException(GlobalResponseCode.OrderCheckRuleNotExist);
                        }
                        String hotelRuleId = ObjUtils.ifNull(employeeHotelRule.getManual_hotel_rule_id(), employeeHotelRule.getDefault_hotel_rule_id());
                        HotelRule occupantHotelRule = hotelRuleMapper.selectByPrimaryKey(hotelRuleId);
                        if (occupantHotelRule == null) {
                            noLimitGroup.add(userContactContract.getRoom_group());
                            continue;
                        }
                        ApplyTripInfoContract applyTripInfoContract= applyContract.getTrip_list().get(0);
                        Integer city_level = ruleService.getCityLevelByAreaId(applyOrder.getCompanyId(),applyTripInfoContract.getStart_city_id());
                        logger.info("酒店城市级别：" + city_level);
                        String startTime=applyTripInfoContract.getStart_time();
                        String cityUrl=String.format(URL_GET_CITY_FULL_PATH,applyTripInfoContract.getStart_city_id());
                        String cityList = null;
                        try {
                            logger.info("获取城市全路径:" + cityUrl);
                            cityList = HttpTool.get(cityUrl);
                            logger.info("获取城市全路径:" + cityList);
                        } catch (Exception e) {
                            logger.error("获取城市全路径错误："+e);
                            throw new SaasException(GlobalResponseCode.InnerError);
                        }
                        Map cityMap=JsonUtils.toObj(cityList,Map.class);
                        List<String> codeList= (List<String>) cityMap.get("data");
                        BigDecimal priceLimit= iHotelCheckService.getHotelRuleLimitPrice(occupantHotelRule,city_level,codeList,startTime);
                        if (priceLimit.compareTo(new BigDecimal(-1))==0){
                            noLimitGroup.add(userContactContract.getRoom_group());
                        }
                        userContactContract.setPrice_limit(priceLimit);
                    }
                }
                //不限制组的都不传价格限制
                guestList.forEach(v->{
                    if (noLimitGroup.contains(v.getRoom_group())){
                        v.setPrice_limit(null);
                    }
                });
            }
            applyContract.setGuest_list(guestList);
        }
        if (applyContract == null) {
            return null;
        }
        //1、申请人及审批人的姓名及部门
        String employeeId = apply.getEmployee_id();
        String companyId = apply.getCompany_id();
        EmployeeContract employeeContract = iBaseEmployeeExtService.queryEmployeeInfo(employeeId, companyId);
        if (employeeContract != null && employeeContract.getId().equals(employeeId)) {
            apply.setUser_name(employeeContract.getName());
            apply.setUser_dept(employeeContract.getOrg_name());
        }
        // 2、可操作权限
        Integer operateAuth = genH5ApplyOperateAuth(apply, userId);
        apply.setOperate_auth(operateAuth);
        //处理审批流设置
        appendFlowData(applyContract.getApply());
        //处理审批流日志显示
        List<ApplyOrderLogContract> logs = getLogsByApplyId(applyId, applyOrder.getCompanyId());
        for (ApplyOrderLogContract applyOrderLogContract : logs) {
            if (StringUtils.isBlank(applyOrderLogContract.getSponsor()) && applyOrder.getEmployeeId().equals(applyOrderLogContract.getSponsor_id())) {
                if (applyOrderLogContract.getAction() != ApplyLogAction.Skip.getValue()) {
                    applyOrderLogContract.setSponsor(applyOrder.getApplicantName());
                }
            }
        }
        applyContract.setLog_list(logs);
        applyContract.setOrder_info(disposeOrderInfo(applyContract.getTrip_list().get(0).getOrder_id(), applyContract.getTrip_list().get(0).getType(), employeeId));
        return applyContract;
    }

    /**
     * 发送用餐push和通知
     *
     * @param apply
     * @param senderUserId
     * @param receiverUserId
     * @param action
     * @param logId
     */
   // @Transactional
    private void postDinnerMessage(ApplyTripInfoContract applyTripInfo, ApplyOrder apply, String senderUserId, String receiverUserId, ApplyLogAction action, Integer logId, String orderId, Integer orderType, ApplyStatus finalStatus, String comment) {
        String applyId = apply.getId();
        ApplyType applyType = ApplyType.valueOf(apply.getType().intValue());

        List<String> userIds = new ArrayList<>();
        userIds.add(senderUserId);
        if (!userIds.contains(apply.getEmployeeId())) {
            userIds.add(apply.getEmployeeId());
        }
        List<IdNameContract> userNames = userService.getUserNameByIds(userIds, apply.getCompanyId());
        String employeeName = null;
        String senderUserName = null;
        String receiverUserName = null;
        for (IdNameContract userName : userNames) {
            if (senderUserId.equals(userName.getId())) {
                senderUserName = userName.getName();
                break;
            }
        }
        for (IdNameContract userName : userNames) {
            if (apply.getEmployeeId().equals(userName.getId())) {
                employeeName = userName.getName();
                break;
            }
        }
        if (StringUtils.isBlank(employeeName)) {
            employeeName = apply.getApplicantName();
        }
        for (IdNameContract userName : userNames) {
            if (receiverUserId.equals(userName.getId())) {
                receiverUserName = userName.getName();
                break;
            }
        }
        ApplyLogAction msgAction = action;
        String msgSender = senderUserName;
        if (msgAction == ApplyLogAction.Approve && finalStatus != ApplyStatus.Approved) {
            //固定审批流中间审批完成
            msgSender = employeeName;
            msgAction = ApplyLogAction.Submit;
        }
        if (StringUtils.isBlank(msgSender) && apply.getEmployeeId().equals(senderUserId)) {
            msgSender = apply.getApplicantName();
        }
        if (msgAction == ApplyLogAction.Skip) {
            if (finalStatus == ApplyStatus.Approved) {
                msgAction = ApplyLogAction.Approve;
            } else {
                msgAction = ApplyLogAction.Submit;
            }
        }
        String dinnerDate = null;
        if (applyTripInfo != null) {
            String endTime = applyTripInfo.getEnd_time();
            SimpleDateFormat myFmt = new SimpleDateFormat("yyyy-MM-dd KK:mm");
            try {
                Calendar c = Calendar.getInstance();
                c.setTime(myFmt.parse(endTime));
                c.add(Calendar.MINUTE, -20);
                SimpleDateFormat myFmtString = new SimpleDateFormat("MM月dd日 HH:mm");
                dinnerDate = myFmtString.format(c.getTime());
            } catch (ParseException e) {
                logger.error("解析时间报错" + e.getMessage());
                e.printStackTrace();
            }
        }
        //发短信功能
        EmployeeContract employee = iBaseOrganizationService.getEmployee(receiverUserId, apply.getCompanyId());
        //Employee employee = employeeMapper.selectByPrimaryKey(receiverUserId);
        if (msgAction == ApplyLogAction.Submit || msgAction == ApplyLogAction.Forward) {
            //发短信
            if (employee != null) {
                String phoneNum = employee.getPhone_num();
                Set<String> phoneSet = Sets.newSet();
                phoneSet.add(phoneNum);
                if (CollectionUtils.isNotEmpty(phoneSet)) {
                    SmsContract smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_DINNER_SUMMIT_APPLY_CENTER_URL);//短信模板
                    Map<String, Object> param = new LinkedHashMap<>();
                    param.put("var1", employeeName);    //申请人姓名
                    try {
                        logger.info("事中审批id加密前:" + HostPropertyConfigTool.HOST_H5 + "/process/approve?apply_id=" + applyId + "&user_id=" + receiverUserId + "秘钥:" + HostPropertyConfigTool.SIGN_KEY);
                        String applySign = EncryptUtil.encryptAES(applyId, HostPropertyConfigTool.SIGN_KEY);
                        String userIdSign = EncryptUtil.encryptAES(receiverUserId, HostPropertyConfigTool.SIGN_KEY);
                        String h5Url = HostPropertyConfigTool.HOST_H5 + "/process/approve?apply_id=" + URLEncoder.encode(applySign) + "&user_id=" + URLEncoder.encode(userIdSign);
                        h5Url = h5Url.replaceAll("\r|\n", "");
                        logger.info("事中审批id加密后:" + HostPropertyConfigTool.HOST_H5 + "/process/approve?apply_id=" + applySign + "&user_id=" + userIdSign);
                        String shortUrl = ShortUrlUtils.getShortUrl(h5Url);
                        param.put("var2", ObjUtils.toString(applyTripInfo.getEstimated_amount()));
                        param.put("var3", dinnerDate);
                        param.put("var4", shortUrl.toString() + " ");
                    } catch (Exception e) {
                        logger.error(String.format("url:%s,异常信息：", "http://harmony.fenbeitong.com/harmony/weixin/shorturl") + e.getMessage());
                        throw new SaasException(GlobalResponseCode.ShortUrlError);
                    }
                    smsContract.setParam(param);
                    NoticeUtils.sendSms(smsContract);
                }
            }
        } else if (msgAction == ApplyLogAction.Refuse) {
            //发短信
            if (employee != null) {
                String phoneNum = employee.getPhone_num();
                Set<String> phoneSet = Sets.newSet();
                phoneSet.add(phoneNum);
                if (CollectionUtils.isNotEmpty(phoneSet)) {
                    SmsContract smsContract = null;
                    smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_DINNER_REPULSE_APPLY_CENTER_URL);//用餐短信模板
                    Map<String, Object> param = new LinkedHashMap<>();
                    param.put("var1", ObjUtils.toString(applyTripInfo.getEstimated_amount()));
                    if (StringUtils.isNotBlank(comment)) {
                        param.put("var2", "（" + comment + "）");
                    } else {
                        param.put("var2", "");
                    }
                    smsContract.setParam(param);
                    NoticeUtils.sendSms(smsContract);
                }
            }
        } else if (msgAction == ApplyLogAction.Approve && finalStatus == ApplyStatus.Approved) {
            //发短信
            if (employee != null) {
                String phoneNum = employee.getPhone_num();
                Set<String> phoneSet = Sets.newSet();
                phoneSet.add(phoneNum);
                if (CollectionUtils.isNotEmpty(phoneSet)) {
                    SmsContract smsContract = null;
                    smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_DINNER_APPROVED_APPLY_CENTER_URL);//用餐短信模板
                    Map<String, Object> param = new LinkedHashMap<>();
                    param.put("var1", ObjUtils.toString(applyTripInfo.getEstimated_amount()));
                    smsContract.setParam(param);
                    NoticeUtils.sendSms(smsContract);
                }
            }
        }
        Map<String, Map<String, String>> languageMap = Maps.newHashMap();
        String messageContent = "";
        String pushContent = "";
        String messageTitle = genMessageTitle(msgAction, languageMap);
        if (msgAction == ApplyLogAction.Submit || msgAction == ApplyLogAction.Forward) {
            languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_DinnerFeeApplyNotice.getLanguageCode(), msgSender, dinnerDate));
            messageContent = StrUtils.formatString(CoreLanguage.Common_Message_DinnerApplyNotice.getMessage(), msgSender, dinnerDate);
            pushContent = StrUtils.formatString(CoreLanguage.Common_Message_DinnerFeeApplyNotice.getMessage(), msgSender, dinnerDate);
        } else if (msgAction == ApplyLogAction.Approve) {
            if (finalStatus != ApplyStatus.Approved) {
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_DinnerFeeApplyNotice.getLanguageCode(), msgSender,
                  dinnerDate));
                messageContent = StrUtils.formatString(CoreLanguage.Common_Message_DinnerApplyNotice.getMessage(), msgSender, dinnerDate);
                pushContent = StrUtils.formatString(CoreLanguage.Common_Message_DinnerFeeApplyNotice.getMessage(), msgSender, dinnerDate);
            } else {
                languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_DinnerApplyPassNotice.getLanguageCode()));
                messageContent = CoreLanguage.Common_Message_DinnerApplyPass.getMessage();
                pushContent = CoreLanguage.Common_Message_DinnerApplyPassNotice.getMessage();
            }
        } else if (msgAction == ApplyLogAction.Refuse) {
            languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_DinnerApplyRefuseNotice.getLanguageCode()));
            messageContent = CoreLanguage.Common_Message_DinnerApplyRefuse.getMessage();
            pushContent = CoreLanguage.Common_Message_DinnerApplyRefuseNotice.getMessage();
        } else if (msgAction == ApplyLogAction.Overtime) {
            languageMap.put(MessageLanguageEnum.CONTENT.getCode(), LanguageUtils.getFullMessage(CoreLanguage.Common_Message_DinnerApplyTimeoutNotice.getLanguageCode()));
            pushContent = CoreLanguage.Common_Message_DinnerApplyTimeoutNotice.getMessage();
            messageContent = CoreLanguage.Common_Message_DinnerApplyTimeoutClose.getMessage();
        }
        boolean myself = true;
        if (msgAction == ApplyLogAction.Submit || msgAction == ApplyLogAction.Forward) {
            myself = false;
        }
        String viewType = myself ? "1" : "2";
        Map<String, Object> msgData = new HashMap<>();
        msgData.put("myself", myself ? "true" : "false");
        msgData.put("view_type", viewType);
        msgData.put("id", applyId);
        msgData.put("setting_type", "2");
        if (msgAction == ApplyLogAction.Submit && StringUtils.isNotBlank(orderId)) {
            msgData.put("order_id", orderId);
            msgData.put("order_type", ObjUtils.toString(orderType));
        } else {
            List<ApplyTripApplicateExt> applyTripApplicates = applyTripApplicateExtMapper.queryApplyTripApplicateList(applyId);
            msgData.put("order_id", applyTripApplicates.get(0).getOrderId());
            msgData.put("order_type", ObjUtils.toString(applyTripApplicates.get(0).getType()));
        }
        String linkInfo = JSONObject.toJSONString(msgData); //跳转信息
        //存消息
        MessageSaveContract messageSaveContract = new MessageSaveContract();
        messageSaveContract.setMessage_type(MessageType.Apply.getCode());
        messageSaveContract.setTitle(messageTitle);
        messageSaveContract.setContent(messageContent);
        messageSaveContract.setBiz_order(applyId);
        messageSaveContract.setLink(linkInfo);
        messageSaveContract.setSender(senderUserId);
        messageSaveContract.setSender_type(SenderType.Person.getCode());
        messageSaveContract.setReceiver(receiverUserId);
        messageSaveContract.setCompany_id(apply.getCompanyId());

        ApplyInfo messageApplyInfo = new ApplyInfo();
        messageApplyInfo.setApply_type(applyType.getBizType().getCode());
        messageApplyInfo.setApply_time(DateTimeTool.fromDateTimeToString(new Date()));
        messageApplyInfo.setApply_msg(messageContent);
        messageApplyInfo.setMyself(myself);
        messageApplyInfo.setView_type(myself ? 1 : 2);
        messageApplyInfo.setSetting_type(2);
        if (msgAction == ApplyLogAction.Submit && StringUtils.isNotBlank(orderId)) {
            messageApplyInfo.setOrder_id(orderId);
            messageApplyInfo.setOrder_type(ObjUtils.toString(orderType));
        } else {
            List<ApplyTripApplicateExt> applyTripApplicates = applyTripApplicateExtMapper.queryApplyTripApplicateList(applyId);
            messageApplyInfo.setOrder_id(applyTripApplicates.get(0).getOrderId());
            messageApplyInfo.setOrder_type(ObjUtils.toString(applyTripApplicates.get(0).getType()));
        }
        if (logId != null && logId != 0) {
            messageApplyInfo.setLog_id(logId);
        }
        messageSaveContract.setApply_info(messageApplyInfo);

        try {
            messageService.saveMessage(messageSaveContract);
        } catch (SaasException ex) {
            logger.info("发消息异常：" + ex.getMessage());
            //不处理
        }

        PushContract pushData = new PushContract();
        pushData.setTitle(messageTitle);
        pushData.setContent(pushContent);
        pushData.setUser_id(receiverUserId);
        pushData.setCompany_id(apply.getCompanyId());
        pushData.setMsg_type("0");
        pushData.setDesc(pushContent);
        pushData.setAlert(true);
        pushData.setMsg(linkInfo);
        pushData.setTitleMultilingualMap(languageMap.get(MessageLanguageEnum.TITLE.getCode()));
        pushData.setCommentMultilingualMap(languageMap.get(MessageLanguageEnum.CONTENT.getCode()));
        pushService.pushWithBudge(pushData);

        //审批节点（非终审节点）审核完成（通过或转交），单据流转至下一节点审批发送给申请人
        if ((msgAction == ApplyLogAction.Approve && finalStatus != ApplyStatus.Approved) || msgAction == ApplyLogAction.Forward) {
            String pushContentInfo = "";
            pushContentInfo = StrUtils.formatString(CoreLanguage.Common_Message_DinnerFeeForwardNotice.getMessage(), receiverUserName);
            PushContract sendData = new PushContract();
            sendData.setTitle(messageTitle);
            sendData.setContent(pushContentInfo);
            sendData.setUser_id(apply.getEmployeeId());
            sendData.setCompany_id(apply.getCompanyId());
            sendData.setMsg_type("0");
            sendData.setDesc(pushContentInfo);
            sendData.setAlert(true);
            sendData.setMsg(linkInfo);
            sendData.setTitleMultilingualMap(languageMap.get(MessageLanguageEnum.TITLE.getCode()));
            sendData.setCommentMultilingualMap(LanguageUtils.getFullMessage(CoreLanguage.Common_Message_DinnerFeeForwardNotice.getLanguageCode(), receiverUserName));
            pushService.pushWithBudge(sendData);
        }
    }

    /**
     * 用餐发券接口
     *
     * @param applyId
     * @throws SaasException
     */
    private void sendMealCoupon(String applyId) throws SaasException {
        List<ApplyTripApplicateExt> applyTripApplicates = applyTripApplicateExtMapper.queryApplyTripApplicateList(applyId);
        String data = null;
        try {
            logger.info("用餐发券接口:" + HostPropertyConfigTool.HOST_DINNER_BIZ + "/internal/dinner/during/pass" + "?order:" + applyTripApplicates.get(0).getOrderId() + "during_apply_id:" + applyId);
            JSONObject jo = new JSONObject();
            jo.put("order_id", applyTripApplicates.get(0).getOrderId());
            jo.put("during_apply_id", applyId);
            data = HttpTool.post(HostPropertyConfigTool.HOST_DINNER_BIZ + "/internal/dinner/during/pass", jo);
            logger.info("用餐发券接口结果:" + data);
        } catch (Exception e) {
            logger.error(String.format("url:%s,异常", HostPropertyConfigTool.HOST_DINNER_BIZ + "/internal/dinner/during/pass") + e.getMessage());
            throw new SaasException(GlobalResponseCode.PayOrderCancleOrderSystemError);
        }
        if (StringUtils.isBlank(data)) {
            throw new SaasException(GlobalResponseCode.PayOrderCancleOrderSystemError);
        }
    }

    /**
     * 用餐驳回接口
     *
     * @param applyId
     * @throws SaasException
     */
    private void cancelMealCoupon(String applyId, Integer rejectType) throws SaasException {
        List<ApplyTripApplicateExt> applyTripApplicates = applyTripApplicateExtMapper.queryApplyTripApplicateList(applyId);
        String data = null;
        try {
            logger.info("用餐驳回接口:" + HostPropertyConfigTool.HOST_DINNER_BIZ + "/internal/dinner/during/reject" + "?order:" + applyTripApplicates.get(0).getOrderId() + "during_apply_id:" + applyId);
            JSONObject jo = new JSONObject();
            jo.put("order_id", applyTripApplicates.get(0).getOrderId());
            jo.put("during_apply_id", applyId);
            jo.put("reject_type", rejectType);
            data = HttpTool.post(HostPropertyConfigTool.HOST_DINNER_BIZ + "/internal/dinner/during/reject", jo);
            logger.info("用餐驳回接口结果:" + data);
        } catch (Exception e) {
            logger.error(String.format("url:%s,异常", HostPropertyConfigTool.HOST_DINNER_BIZ + "/internal/dinner/during/reject") + e.getMessage());
            throw new SaasException(GlobalResponseCode.PayOrderCancleOrderSystemError);
        }
        if (StringUtils.isBlank(data)) {
            throw new SaasException(GlobalResponseCode.PayOrderCancleOrderSystemError);
        }
    }

    /**
     * 用餐超时接口
     *
     * @param applyId
     * @throws SaasException
     */
    private void overTimeMealCoupon(String applyId) throws SaasException {
        List<ApplyTripApplicateExt> applyTripApplicates = applyTripApplicateExtMapper.queryApplyTripApplicateList(applyId);
        String data = null;
        try {
            logger.info("用餐超时接口:" + HostPropertyConfigTool.HOST_DINNER_BIZ + "/internal/dinner/during/overtime" + "?order:" + applyTripApplicates.get(0).getOrderId() + "during_apply_id:" + applyId);
            JSONObject jo = new JSONObject();
            jo.put("order_id", applyTripApplicates.get(0).getOrderId());
            jo.put("during_apply_id", applyId);
            data = HttpTool.post(HostPropertyConfigTool.HOST_DINNER_BIZ + "/internal/dinner/during/overtime", jo);
            logger.info("用餐超时接口结果:" + data);
        } catch (Exception e) {
            logger.error(String.format("url:%s,异常", HostPropertyConfigTool.HOST_DINNER_BIZ + "/internal/dinner/during/overtime") + e.getMessage());
            throw new SaasException(GlobalResponseCode.PayOrderCancleOrderSystemError);
        }
        if (StringUtils.isBlank(data)) {
            throw new SaasException(GlobalResponseCode.PayOrderCancleOrderSystemError);
        }
    }


    /**
     * 转发审批单
     *
     * @param approveModel
     * @param userId
     * @param ip
     * @return
     */
    @Override
    //@Transactional(value = "saas")
    public GlobalResponseCode forward(ApplyApproveContract approveModel, String userId, String companyId, String ip, String clientVersion, String source) throws SaasException {
        FlowCheckUtil.check(companyId, clientVersion, source);
        if (approveModel == null) {
            return GlobalResponseCode.ApplyIsNull;
        }
        if (StringTool.isNullOrEmpty(approveModel.getApply_id())) {
            return GlobalResponseCode.ApplyIdInvalid;
        }
        if (approveModel.getComment() != null && approveModel.getComment().length() > 200) {
            return GlobalResponseCode.ApplyCheckReasonInvalid;
        }
        int status = approveModel.getStatus();
        String nextApproverId = approveModel.getApprover_id();
        if (StringTool.isNullOrEmpty(nextApproverId)) {
            //转交需要一个承接人
            return GlobalResponseCode.ApplyApproveIdInvalid;
        } else if (nextApproverId.equals(userId)) {
            return GlobalResponseCode.ApplyTransferNotSelf;
        }
        String applyId = approveModel.getApply_id();
        ApplyOrder apply = applyMapper.selectByPrimaryKey(approveModel.getApply_id());
        if (apply == null) {
            return GlobalResponseCode.NotFound;
        }
        boolean isFlow = (ValueTool.areEqual(apply.getFlowType(), CompanyApplyType.Flow.getValue()) || ValueTool.areEqual(apply.getFlowType(), CompanyApplyType.CONDITIONAL.getValue()));
        if (!apply.getCompanyId().equals(companyId)) {
            //申请单和当前人不在一个公司
            return GlobalResponseCode.ApplyCompanyCannotMatch;
        }
        if (apply.getState() != ApplyStatus.PendingAudit.getValue()) {
            //该单不是待审核或者转交状态,不能审批
            return GlobalResponseCode.ApplyStatusNotPendingAudit;
        }
        if (apply.getEmployeeId().equals(approveModel.getApprover_id())) {
            return GlobalResponseCode.ApplyApproverCannotBeSelf;
        }
        Boolean exist = userService.isUserInCompany(approveModel.getApprover_id(), apply.getCompanyId());
        if (!exist) {
            return GlobalResponseCode.ApplyCompanyCannotMatch;
        }
        if (!apply.getApproverId().equals(userId)) {
            //不是当前人可以处理的审批单
            return GlobalResponseCode.NoAuth;
        }
        ApplyStatus finalStatus;
        ApplyLogAction action;
        String receiverId; //下一个审批人/通知人
        String approverId; //审批人
        Integer logId = null;
        List<String> skippedApprverIds = new ArrayList<>();//固定审批流跳过的审批人
        Date now = new Date();
        //转交
        if (isFlow) {
            List<ApplyFlowUserItem> applyFlowUserItems = applyFlowService.getFlowItemsByApplyId(applyId);
            if (applyFlowUserItems != null && applyFlowUserItems.size() > 0) {
                Optional<ApplyFlowUserItem> optionalApplyFlowUserItem = applyFlowUserItems.stream().filter(m -> m.getUserId().equals(userId) &&
                        ValueTool.areEqual(m.getStatus(), ApplyFlowUserItemStatus.PendingAudit.getValue())).findAny();
                if (optionalApplyFlowUserItem.isPresent()) {
                    ApplyFlowUserItem applyFlowUserItem = optionalApplyFlowUserItem.get();
                    //当前审批人将此单转交给另一人，将此item状态置为Transfered
                    applyFlowService.setApplyFlowUserItemStatus(applyFlowUserItem.getId(), ApplyFlowUserItemStatus.Transfered);
                }
            }
        }
        finalStatus = ApplyStatus.PendingAudit;
        action = ApplyLogAction.Forward;
        receiverId = approveModel.getApprover_id();
        approverId = receiverId;

        //查询审核中的审批日志
        List<ApplyOrderLog> applyOrderLogList = applyOrderLogExtMapper.selectApproveUser(applyId, ApplyLogAction.Approval.getValue());
        //有日志数据情况下修改操作无日志数据情况下插入操作
        if (!CollectionUtils.isEmpty(applyOrderLogList)) {
            applyOrderLogExtMapper.updateApplyOrderActionById(ApplyLogAction.Forward.getValue(), applyOrderLogList.get(0).getId(), receiverId, approveModel.getComment(), new Date(), approveModel.getPrice());
            Integer sort = applyOrderLogList.get(0).getSort() + 1;
            ApplyOrderLog log = new ApplyOrderLog();
            log.setApplyOrderId(applyId);
            log.setIp(ip);
            log.setSponsorId(receiverId);
            log.setReceiverId("");
            log.setCheckReason(null);
            log.setAction(ApplyLogAction.Approval.getValue());
            log.setSort(sort);
            log.setRootApplyOrderId(applyId);
            applyLogMapper.insertSelective(log);
            logId = log.getId();
            setApproverAndStatus(applyId, approverId, finalStatus, now, log.getId(), approveModel);
        } else {
            writeLog(applyId, now, ip, userId, receiverId, approveModel.getComment(), action, approveModel.getPrice());
            ApplyOrderLog logApprove = new ApplyOrderLog();
            logApprove.setApplyOrderId(applyId);
            logApprove.setIp(ip);
            logApprove.setSponsorId(receiverId);
            logApprove.setReceiverId("");
            logApprove.setCheckReason(null);
            logApprove.setAction(ApplyLogAction.Approval.getValue());
            logApprove.setSort(0);
            logApprove.setRootApplyOrderId(applyId);
            applyLogMapper.insert(logApprove);
            logId = logApprove.getId();
            setApproverAndStatus(applyId, approverId, finalStatus, now, logId, approveModel);
        }
        setApplyApproverAndPushMessage(apply, userId, ip, finalStatus, receiverId, approverId, approveModel.getComment(), action, skippedApprverIds, logId);
        return GlobalResponseCode.Success;
    }

    /**
     * 订单支付接口
     *
     * @param orderId
     * @param price
     * @param estimatedAmount
     * @param type
     * @param clientVersion
     * @param seatItem
     * @return
     * @throws SaasException
     */
    @Override
    public SaasResponseEntity payAirOrder(String orderId, BigDecimal price, BigDecimal estimatedAmount, Integer type, String clientVersion, Map<String, Object> seatItem) throws SaasException {
        String data = null;
        try {
            if (price == null || price.compareTo(BigDecimal.valueOf(0)) != 1) {
                price = estimatedAmount;
            }
            if (type == 1 && VersionTool.compare(clientVersion, "2.0.5") < 0) {
                logger.info("审批通过订单支付:" + URL_GET_ORDER_PAY + "?order_id=" + orderId + "&busi_type=1&totalPrice=" + price);
                data = HttpTool.get(URL_GET_ORDER_PAY + "?order_id=" + orderId + "&busi_type=1&totalPrice=" + price);
                logger.info("获取订单支付的返回结果:" + data);
            } else {
                Map<String, String> header = Maps.newHashMap();
                header.put("Content-Type", "application/json;charset=utf-8");
                JSONObject param = new JSONObject();
                param.put("total_price", price);
                param.put("order_id", orderId);
                if (type == 1) {
                    param.put("client_version", clientVersion);
                }
                param.put("seat_item", JSON.toJSON(seatItem));
                logger.info("审批通过订单支付:" + URL_GET_AIR_ORDER_PAY + "?order_id=" + orderId + "&busi_type=1&totalPrice=" + price + "&seat_item=" + JSON.toJSONString(seatItem));
                data = HttpTool.post(URL_GET_AIR_ORDER_PAY, param, header);
                logger.info("获取订单支付的返回结果:" + data);
                Map<String, Object> jo = JSONObject.parseObject(data, HashMap.class);
                Map<String, Object> airDataMap = (Map<String, Object>) jo.get("data");
                Integer code = ObjUtils.toInteger(jo.get("code"));
                if (!code.equals(401005) && !code.equals(401002) && !code.equals(501001) && !code.equals(501002)) {
                    SaasResponseEntity saasResponseEntity = new SaasResponseEntity(code, ObjUtils.toString(jo.get("msg")), airDataMap);
                    return saasResponseEntity;
                }
            }
        } catch (Exception e) {
            logger.error(String.format("url:%s,异常", URL_GET_AIR_ORDER_PAY) + e.getMessage());
            throw new SaasException(GlobalResponseCode.PayOrderCancleOrderSystemError);
        }
        if (StringUtils.isBlank(data)) {
            throw new SaasException(GlobalResponseCode.PayOrderCancleOrderSystemError);
        }
        return new SaasResponseEntity(GlobalResponseCode.Success, null);
    }

    @Override
    public void payRoundTripAirOrder(String requestId, String orderId, BigDecimal goPrice, BigDecimal backPrice,
                                     Map<String, Object> seatItem, Map<String, Object> backSeatItem) {
        try {
            LogUtil.setRequestId(requestId);
            Map<String, String> header = Maps.newHashMap();
            header.put("Content-Type", "application/json;charset=utf-8");
            Map<String, Object> param = Maps.newHashMap();
            param.put("total_price", goPrice);
            param.put("back_total_price", backPrice);
            param.put("order_id", orderId);
            param.put("seatItem", seatItem);
            param.put("backSeatItem", backSeatItem);
            logger.info("[往返机票] 审批通过订单支付 url:{}, param:{}", URL_GET_AIR_ROUND_ORDER_PAY, param);
            String resData = HttpTool.post(URL_GET_AIR_ROUND_ORDER_PAY, param, header);
            logger.info("[往返机票] 获取订单支付的返回结果: {}", resData);
            JSONObject data = JSON.parseObject(resData);
            Integer code = data.getInteger("code");
            if (!Objects.equals(401005, code)
                    && !Objects.equals(401002, code)
                    && !Objects.equals(501001, code)
                    && !Objects.equals(501002, code)) {
                logger.info("[往返机票] 订单出票失败, orderId:{}, msg:{}", orderId, data.getString("msg"));
            }
        } catch (Exception e) {
            logger.info("[往返机票] 订单出票失败, orderId:{}", orderId, e);
        } finally {
            LogUtil.release();
        }
    }

    /**
     * 修改占用
     * @param price
     * @param orderId
     * @param applyId
     * @param userId
     * @param companyId
     */
    private void updateCost(BigDecimal price, String orderId, String applyId, String userId, String companyId, String costId, Integer type) {
        OrderCostInfoReq costInfo = new OrderCostInfoReq();
        costInfo.setAmount(price);
        costInfo.setOrderId(orderId);
        costInfo.setApplyId(applyId);
        costInfo.setForce(false);
        costInfo.setUserId(userId);
        costInfo.setCompanyId(companyId);
        costInfo.setCostId(costId);
        costInfo.setCategory(type);
        CostSaveResult costSaveResult = orderCostService.updateCost(costInfo);
        //状态 1成功 0失败
        if (costSaveResult == null || costSaveResult.getStatus() == 0) {
            throw new SaasException(GlobalResponseCode.BudgetUpdateIsError.getCode(), costSaveResult.getMessage(), 0, null);
        }
    }
}

