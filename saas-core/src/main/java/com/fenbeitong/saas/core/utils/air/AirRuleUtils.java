package com.fenbeitong.saas.core.utils.air;

import com.alibaba.fastjson.JSONObject;

import com.fenbeitong.finhub.common.utils.DistanceUtils;
import com.fenbeitong.saas.api.model.dto.rule.RuleTimeRange;
import com.fenbeitong.saas.core.common.base.SaasException;
import com.fenbeitong.saas.core.common.constant.AirRuleConstant;
import com.fenbeitong.saas.core.common.constant.GlobalResponseCode;
import com.fenbeitong.saas.core.contract.biz.train.TrainInfo;
import com.fenbeitong.saas.core.contract.order.check.AirOrderCheckReqV2Contract;
import com.fenbeitong.saas.core.contract.order.check.CurrencyEmployeeMsg;
import com.fenbeitong.saas.core.contract.order.check.CurrencyMsg;
import com.fenbeitong.saas.core.contract.order.check.ErrMsgInfo;
import com.fenbeitong.saas.core.contract.order.check.ResponseCodeContract;
import com.fenbeitong.saas.core.contract.order.check.TravelOnBusiOrderCheckResContract;
import com.fenbeitong.saas.core.contract.order.check.TravelOnBusiOrderRuleCheckResult;
import com.fenbeitong.saas.core.contract.rule.DateInterval;
import com.fenbeitong.saas.core.model.enums.rule.AirLowPriceUnitEnum;
import com.fenbeitong.saas.core.model.enums.rule.DayTempType;
import com.fenbeitong.saas.core.model.enums.rule.DayType;
import com.fenbeitong.saas.core.model.fenbeitong.AirTimeRange;
import com.fenbeitong.saas.core.service.rule.ExceedConfigTypeEnum;
import com.fenbeitong.saas.core.service.rule.air.flight.AirFlightRuleCheckReq;
import com.fenbeitong.saas.core.service.rule.air.flight.AirFlightRuleCheckRes;
import com.fenbeitong.saas.core.service.rule.air.flight.city.AirCityCheckChainOrderEnum;
import com.fenbeitong.saas.core.utils.tools.DateTimeTool;
import com.fenbeitong.saas.core.utils.tools.HostPropertyConfigTool;
import com.fenbeitong.saas.core.utils.tools.HttpTool;
import com.fenbeitong.saasplus.api.model.po.fenbeitong.rulenew.AirBookConditionGroup;
import com.fenbeitong.saasplus.api.model.po.fenbeitong.rulenew.AirRuleBookGroup;
import com.fenbeitong.saasplus.api.model.po.fenbeitong.rulenew.AirRuleV2;
import com.fenbeitong.saasplus.api.model.po.fenbeitong.rulenew.AirTimeRangeDTO;
import com.fenbeitong.saasplus.api.model.po.fenbeitong.rulenew.CityConditionGroup;

import com.google.common.collect.Lists;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.DateUtils;
import com.luastar.swift.base.utils.MoneyUtils;
import com.luastar.swift.base.utils.ObjUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

public class AirRuleUtils {

    private static final Logger logger = LoggerFactory.getLogger(AirRuleUtils.class);

    public static final Boolean IS_MONEY_GROUP = true; // 钱是否分组表示

    public static final Boolean IS_NEED_RMB = false; // 是否需要¥

    private static final String URL_GET_AIR_CITY_INFO = HostPropertyConfigTool.HOST_HARMONY + "/city/areas/info";

    public static List<AirFlightRuleCheckRes.PerRuleCheckRes> sortFlightRulePriority(
        List<AirFlightRuleCheckRes.PerRuleCheckRes> perRuleCheckResList) {
        // 筛选超规的规则
        List<AirFlightRuleCheckRes.PerRuleCheckRes> collect =
            perRuleCheckResList.stream().filter(AirFlightRuleCheckRes.PerRuleCheckRes::getIsExceed).sorted(
                (o1, o2) -> {
                    int priority = o1.getBlockPriority().compareTo(o2.getBlockPriority());
                    if (priority == 0) {
                        return o1.getSeq().compareTo(o2.getSeq());
                    } else {
                        return priority;
                    }
                }).collect(Collectors.toList());
        logger.info("sortFlightRulePriority res:{}", JsonUtils.toJson(collect));
        return collect;
    }

//    public static List<AirFlightRuleCheckRes.PerRuleCheckRes> sortFlightRulePriorityByType(
//        List<AirFlightRuleCheckRes.PerRuleCheckRes> perRuleCheckResList, Integer exceedType) {
//        // 筛选超规的规则
//        List<AirFlightRuleCheckRes.PerRuleCheckRes> collect =
//            perRuleCheckResList.stream().filter(perRuleCheckRes -> perRuleCheckRes.getIsExceed() && Objects.equals(
//                perRuleCheckRes.getExceedType(), exceedType)).sorted(
//                (o1, o2) -> {
//                    int priority = o1.getBlockPriority().compareTo(o2.getBlockPriority());
//                    if (priority == 0) {
//                        return o1.getSeq().compareTo(o2.getSeq());
//                    } else {
//                        return priority;
//                    }
//                }).collect(Collectors.toList());
//        logger.info("sortFlightRulePriorityByType res:{}", JsonUtils.toJson(collect));
//        return collect;
//    }

    /**
     * 获取校验项，排除掉白名单判断结果
     * @param perRuleCheckResList
     * @return
     */
    public static List<AirFlightRuleCheckRes.PerRuleCheckRes> getCheckOpenList(
        List<AirFlightRuleCheckRes.PerRuleCheckRes> perRuleCheckResList) {

        return perRuleCheckResList.stream().filter(perRuleCheckRes -> perRuleCheckRes.getIsCheckOpen()
                && perRuleCheckRes.getHandlerOrder() != AirCityCheckChainOrderEnum.CITY_WHITE_LIST.getOrder())
            .collect(Collectors.toList());
    }

    /**
     * 获取城市条件规则校验的结果，任意一项不超规，为true
     * 或者只有一项为城市白名单，并且超规的时候，返回true
     * @param perRuleCheckResList
     * @return
     */
    public static Boolean getCityRuleCheck(List<AirFlightRuleCheckRes.PerRuleCheckRes> perRuleCheckResList) {
        List<AirFlightRuleCheckRes.PerRuleCheckRes> checkOpenList = getCheckOpenList(perRuleCheckResList);
        if (checkOpenList.isEmpty()) {
            return true;
        } else {
            if (checkOpenList.size() == 1
                && checkOpenList.get(0).getHandlerOrder() == AirCityCheckChainOrderEnum.CITY_WHITE_LIST.getOrder()) {
                return true;
            }
            return checkOpenList.stream().anyMatch(perRuleCheckRes -> !perRuleCheckRes.getIsExceed());
        }
    }

    /**
     * 设置默认的超规措施，兼容升级企业时未选择超规措施
     * @param airFlightRuleCheckReq
     * @param exceedType //超标规则（1：禁止 2：超规填写理由下单 3：超规需要提交订单审批）
     */
    public static void setExceedConfigType(AirFlightRuleCheckReq airFlightRuleCheckReq, Integer exceedType) {
        AirRuleV2 ruleInfo = airFlightRuleCheckReq.getRuleInfo();
        CityConditionGroup cityConditionGroup = ruleInfo.getCityConditionGroup();
        if (cityConditionGroup.getCityConditionGroupFlag()) {
            int exceedConfigType = ExceedConfigTypeEnum.EXCEED_NOT_ALLOW.getCompareOrder();
            if (cityConditionGroup.getCityConditionConfig() == null) {
                cityConditionGroup.setCityConditionConfig(exceedConfigType); // 默认设置禁止下单
            }
        }
        AirBookConditionGroup airBookConditionGroup = ruleInfo.getAirBookConditionGroup();
        if (airBookConditionGroup.getAirBookConditionGroupFlag()) {
            AirRuleBookGroup airRuleBookGroup = airBookConditionGroup.getAirRuleBookGroup();
            int exceedConfigType;
            if (Objects.equals(exceedType, 1)) {
                exceedConfigType = ExceedConfigTypeEnum.EXCEED_NOT_ALLOW.getCompareOrder();
            } else if (Objects.equals(exceedType, 2)) {
                exceedConfigType = ExceedConfigTypeEnum.COMPANY_PAY_BY_REASON.getCompareOrder();
            } else if (Objects.equals(exceedType, 3)) {
                exceedConfigType = ExceedConfigTypeEnum.COMPANY_PAY_SUBMIT_APPROVAL.getCompareOrder();
            } else {
                exceedConfigType = ExceedConfigTypeEnum.EXCEED_NOT_ALLOW.getCompareOrder();
            }
            if (airRuleBookGroup.getAirCabinFlag()) {
                if (airRuleBookGroup.getAirCabinConfig() == null) {
                    airRuleBookGroup.setAirCabinConfig(exceedConfigType);
                }
            }
            if (airRuleBookGroup.getAirPriceFlag()) {
                if (airRuleBookGroup.getAirPriceConfig() == null) {
                    airRuleBookGroup.setAirPriceConfig(exceedConfigType);
                }
            }
            if (airRuleBookGroup.getAirDiscountFlag()) {
                if (airRuleBookGroup.getAirDiscountConfig() == null) {
                    airRuleBookGroup.setAirDiscountConfig(exceedConfigType);
                }
            }
            if (Objects.equals(airRuleBookGroup.getLowPriceFlag(), 1)) {
                if (airRuleBookGroup.getLowPriceConfig() == null) {
                    airRuleBookGroup.setLowPriceConfig(exceedConfigType);
                }
            }
            if (airRuleBookGroup.getAirTimeRangeFlag()) {
                if (airRuleBookGroup.getTimeRangeConfig() == null) {
                    airRuleBookGroup.setTimeRangeConfig(exceedConfigType);
                }
            }
            if (airRuleBookGroup.getPrivDayFlag()) {
                if (airRuleBookGroup.getPrivDayConfig() == null) {
                    airRuleBookGroup.setPrivDayConfig(exceedConfigType);
                }
            }
        }
    }

    /**
     * 获取最严的规则，值越小越严
     * @param perRuleCheckResList
     * @return
     */
    public static Integer getMaxPriority(List<AirFlightRuleCheckRes.PerRuleCheckRes> perRuleCheckResList) {
        Integer maxPriority = Integer.MAX_VALUE;
        for (AirFlightRuleCheckRes.PerRuleCheckRes perRuleCheckRes: perRuleCheckResList) {
            if (perRuleCheckRes.getBlockPriority() != null) {
                if (perRuleCheckRes.getBlockPriority() < maxPriority) {
                    maxPriority = perRuleCheckRes.getBlockPriority();
                }
            }
        }
        return maxPriority;
    }

    /**
     * 构造预定机票规则校验航班信息参数
     * @param reqContract
     * @return
     */
    public static AirFlightRuleCheckReq.FlightInfo buildBookFlightInfo(AirOrderCheckReqV2Contract reqContract,
        String token, boolean allowDeductExceed, BigDecimal couponAmount, BigDecimal originPerTicketPrice,
        BigDecimal insurancePrice,BigDecimal personInsurancePrice, Integer personCount) {
        AirFlightRuleCheckReq.FlightInfo flightInfo = new AirFlightRuleCheckReq.FlightInfo();
        flightInfo.setToken(token);
        flightInfo.setCompanyId(reqContract.getTravel_on_busi_common_req_contract().getCompany_id());
        flightInfo.setApplyId(reqContract.getTravel_on_busi_common_req_contract().getApply_id());
        int flightType = reqContract.getFlight_type() == null ? 1 : reqContract.getFlight_type();
        flightInfo.setIsGoBack(flightType == 2);
        flightInfo.setCouponInfo(reqContract.getCouponInfo());
        flightInfo.setDiscount(reqContract.getDiscount());
        flightInfo.setCabinFullPrice(reqContract.getCabin_full_price());
        flightInfo.setGoStartDateStr(reqContract.getTravel_on_busi_parameter_req_contract().getStart_time());
        flightInfo.setGoEndDateStr(reqContract.getTravel_on_busi_parameter_req_contract().getEnd_time());
        flightInfo.setBackStartDateStr(reqContract.getTravel_on_busi_parameter_req_contract().getBack_start_time());
        flightInfo.setBackEndDateStr(reqContract.getTravel_on_busi_parameter_req_contract().getBack_end_time());
        flightInfo.setCabin(reqContract.getAir_type());
//        flightInfo.setPerTicketPrice(originPerTicketPrice.setScale(2, BigDecimal.ROUND_HALF_UP));
        flightInfo.setOrderParameterJson(reqContract.getOrder_parameter_json());
        flightInfo.setStartCityId(reqContract.getTravel_on_busi_parameter_req_contract().getStart_city_id());
        flightInfo.setArrivalCityId(reqContract.getTravel_on_busi_parameter_req_contract().getArrival_city_id());
        flightInfo.setStartCityIds(reqContract.getTravel_on_busi_parameter_req_contract().getStart_city_ids());
        flightInfo.setArrivalCityIds(reqContract.getTravel_on_busi_parameter_req_contract().getArrival_city_ids());
        flightInfo.setOrderPrice(reqContract.getTravel_on_busi_common_req_contract().getOrder_price()
            .setScale(2, BigDecimal.ROUND_HALF_UP));
        flightInfo.setGoTotalPrice(reqContract.getTravel_on_busi_common_req_contract().getGo_total_price()
            .setScale(2, BigDecimal.ROUND_HALF_UP));
        flightInfo.setBackTotalPrice(reqContract.getTravel_on_busi_common_req_contract().getBack_total_price()
            .setScale(2, BigDecimal.ROUND_HALF_UP));
        flightInfo.setAllowDeductExceed(allowDeductExceed);
        flightInfo.setCouponAmount(couponAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
        flightInfo.setInsurancePrice(insurancePrice.setScale(2, BigDecimal.ROUND_HALF_UP));
        flightInfo.setPersonInsurancePrice(personInsurancePrice.setScale(2, BigDecimal.ROUND_HALF_UP));
        flightInfo.setPersonCount(personCount);
        flightInfo.setLowPriceRecommend(ObjUtils.toBoolean(reqContract.getLowPriceRecommend(), Boolean.FALSE));
//        flightInfo.setPerTicketPriceAll(reqContract.getOrder_parameter_json().getAirport_tax()
//            .add(reqContract.getOrder_parameter_json().getFuel_tax().add(originPerTicketPrice))
//            .setScale(2, BigDecimal.ROUND_HALF_UP));
        flightInfo.setOrderParameterJsonList(reqContract.getOrder_parameter_jsons());
        return flightInfo;
    }

    /**
     * 校验日期规则问题
     * 注意点：日期出现次日时，天数加一天
     * 方法从AirCheckV2ServiceImpl中抽出来
     * @param airTimeRanges
     * @param startTime     yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static List<String> buildTimeRange(Integer dayType, List<AirTimeRangeDTO> airTimeRanges, String startTime,
        Integer lowPriceTime, String lowPriceUnit) {
        logger.info("dayType:{}, airTimeRanges:{}, startTime:{}, lowPriceTime:{}, lowPriceUnit:{}",
                dayType, JsonUtils.toJson(airTimeRanges), startTime, lowPriceTime, lowPriceUnit);
        try {
            //减几小时
            Calendar lowCalendar = Calendar.getInstance();
            lowCalendar.setTime(DateTimeTool.fromStringToDateTime(startTime));
            if(AirLowPriceUnitEnum.MINUTE.getKey().equals(lowPriceUnit)){
                lowCalendar.add(Calendar.MINUTE, -lowPriceTime);
            }else {
                lowCalendar.add(Calendar.HOUR_OF_DAY, -lowPriceTime);
            }

            long lowTime = lowCalendar.getTime().getTime();
            //加几小时
            Calendar highCalendar = Calendar.getInstance();
            highCalendar.setTime(DateTimeTool.fromStringToDateTime(startTime));
            if(AirLowPriceUnitEnum.MINUTE.getKey().equals(lowPriceUnit)){
                highCalendar.add(Calendar.MINUTE, lowPriceTime);
            }else {
                highCalendar.add(Calendar.HOUR_OF_DAY, lowPriceTime);
            }

            long highTime = highCalendar.getTime().getTime();
            //获取当天的最晚时间
            Date startDateInfo = DateUtils.parse(startTime);
            String endDayTime = DateTimeTool.fromDateToString(startDateInfo) + " 23:59:59";
            long endTime = DateUtils.parse(endDayTime).getTime();
            if (highTime > endTime) {
                highTime = endTime;
            }
            SimpleDateFormat sdfDateTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat sdfDate = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat sdfTime = new SimpleDateFormat("HH:mm:ss");
            //无时间限制
            if (CollectionUtils.isEmpty(airTimeRanges)) {
                //获取当天的最早时间
                String startDayTime = DateTimeTool.fromDateToString(startDateInfo) + " 00:00:00";
                long startDateTime = DateUtils.parse(startDayTime).getTime();
                if (lowTime < startDateTime) {
                    lowTime = startDateTime;
                }
                String lowDateStr = DateTimeTool.fromDateToShortTimeInfoString(new Date(lowTime));
                String highDateStr = DateTimeTool.fromDateToShortTimeInfoString(new Date(highTime));
                List<String> res = new ArrayList<>();
                res.add(lowDateStr + "-" + highDateStr);
                return res;
            }
            //获取起飞日期
            Date airStartDate = null;
            String date = startTime.substring(0, 10);
            airStartDate = sdfDate.parse(startTime);
            //日期加一天
            String dateAdd = dateAdd(airStartDate, 1, sdfDate);
            boolean isAllowed = false;
            //日期减一天
            String dateReduce = dateAdd(airStartDate, -1, sdfDate);
            List<String> intersectionList = Lists.newArrayList();
            for (AirTimeRangeDTO airTimeRange : airTimeRanges) {
                String startDate = "";
                String endDate = "";
                //当天日期
                if (airTimeRange.getDayType().intValue() == dayType.intValue()) {
                    startDate = date + " " + sdfTime.format(airTimeRange.getBeginTime());
                    endDate = date + " " + sdfTime.format(airTimeRange.getEndTime());
                    //跨日日期加一天
                    if (airTimeRange.getIsOvernight()) {
                        endDate = dateAdd + " " + sdfTime.format(airTimeRange.getEndTime());
                    }
                }
                //前一天
                else if (airTimeRange.getDayType() == (dayType - 1) || (dayType == DayTempType.FIRSTDAY.getCode()
                    && airTimeRange.getDayType() == DayTempType.SEVENDAY.getCode())) {
                    if (airTimeRange.getIsOvernight()) {
                        startDate = dateReduce + " " + sdfTime.format(airTimeRange.getBeginTime());
                        endDate = date + " " + sdfTime.format(airTimeRange.getEndTime());
                    } else {
                        continue;
                    }
                }
                //周末或者工作日
                else {
                    //周一特殊处理周末
                    if (dayType == DayTempType.FIRSTDAY.getCode()) {
                        //周末
                        if (airTimeRange.getDayType() == 9) {
                            if (airTimeRange.getIsOvernight()) {
                                startDate = dateReduce + " " + sdfTime.format(airTimeRange.getBeginTime());
                                endDate = date + " " + sdfTime.format(airTimeRange.getEndTime());
                            } else {
                                continue;
                            }
                        } else {
                            startDate = date + " " + sdfTime.format(airTimeRange.getBeginTime());
                            endDate = date + " " + sdfTime.format(airTimeRange.getEndTime());
                            //跨日日期加一天
                            if (airTimeRange.getIsOvernight()) {
                                endDate = dateAdd + " " + sdfTime.format(airTimeRange.getEndTime());
                            }
                        }

                    }
                    //周六特殊处理工作日
                    else if (dayType == DayTempType.SIXDAY.getCode()) {
                        if (airTimeRange.getDayType() == 8) {
                            if (airTimeRange.getIsOvernight()) {
                                startDate = dateReduce + " " + sdfTime.format(airTimeRange.getBeginTime());
                                endDate = date + " " + sdfTime.format(airTimeRange.getEndTime());
                            } else {
                                continue;
                            }
                        } else {
                            startDate = date + " " + sdfTime.format(airTimeRange.getBeginTime());
                            endDate = date + " " + sdfTime.format(airTimeRange.getEndTime());
                            //跨日日期加一天
                            if (airTimeRange.getIsOvernight()) {
                                endDate = dateAdd + " " + sdfTime.format(airTimeRange.getEndTime());
                            }
                        }
                    }
                    //非周一和周六
                    else {
                        startDate = date + " " + sdfTime.format(airTimeRange.getBeginTime());
                        endDate = date + " " + sdfTime.format(airTimeRange.getEndTime());
                        //跨日日期加一天
                        if (airTimeRange.getIsOvernight()) {
                            endDate = dateAdd + " " + sdfTime.format(airTimeRange.getEndTime());
                        }
                        if ((dayType > DayTempType.FIRSTDAY.getCode() && dayType <= DayTempType.FIVEDAY.getCode() &&
                            airTimeRange.getDayType() == 8)
                            || (dayType > DayTempType.SIXDAY.getCode() && dayType <= DayTempType.SEVENDAY.getCode() &&
                            airTimeRange.getDayType() == 9)) {
                            isAllowed = true;
                        }
                    }
                }
                //时间比较
                Date startDateTime = sdfDateTime.parse(startDate);
                Date endDateTime = sdfDateTime.parse(endDate);
                String normalInterval = getInterval(lowTime, highTime, startDateTime.getTime(), endDateTime.getTime());
                if (StringUtils.isNotBlank(normalInterval)) {
                    intersectionList.add(normalInterval);
                }
                //特殊处理周末，工作日
                if (isAllowed) {
                    if (airTimeRange.getIsOvernight()) {
                        startDate = dateReduce + " " + sdfTime.format(airTimeRange.getBeginTime());
                        endDate = date + " " + sdfTime.format(airTimeRange.getEndTime());
                        Date startDateTimeW = sdfDateTime.parse(startDate);
                        Date endDateTimeW = sdfDateTime.parse(endDate);
                        String specialInterval = getInterval(lowTime, highTime, startDateTimeW.getTime(), endDateTimeW.getTime());
                        if (StringUtils.isNotBlank(specialInterval)) {
                            intersectionList.add(specialInterval);
                        }
                    }
                }

            }
            if (CollectionUtils.isEmpty(intersectionList)) {
                return Lists.newArrayList();
            }
            List<String> timeRangeList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(intersectionList)) {
                List<DateInterval> intervalList = Lists.newArrayList();
                for (String timeIntervalInfo : intersectionList) {
                    String[] sectionList = timeIntervalInfo.split("-");
                    intervalList.add(new DateInterval(ObjUtils.toLong(sectionList[0]), ObjUtils.toLong(sectionList[1])));
                }
                List<DateInterval> mergeList = merge(intervalList);
                if (CollectionUtils.isNotEmpty(mergeList)) {
                    for (DateInterval interval : mergeList) {
                        Long lowEndpoint = interval.getStart();
                        Long highEndpoint = interval.getEnd();
                        Date lowDate = new Date(lowEndpoint);
                        Date highDate = new Date(highEndpoint);
                        String lowDateStr = DateTimeTool.fromDateToShortTimeInfoString(lowDate);
                        String highDateStr = DateTimeTool.fromDateToShortTimeInfoString(highDate);
                        timeRangeList.add(lowDateStr + "-" + highDateStr);
                    }
                }
            }
            logger.info("buildTimeRange result:{}:", JsonUtils.toJson(timeRangeList));
            return timeRangeList;
        } catch (ParseException e) {
            logger.error("打车日期格式化异常" + e.getLocalizedMessage());
        }
        return Lists.newArrayList();
    }

    public static String getInterval(long s1, long e1, long s2, long e2) {
        if (e1 < s2 || e2 < s1) {
            return "";
        } else {
            return Math.max(s1, s2) + "-" + Math.min(e1, e2);
        }
    }

    private static List<DateInterval> merge(List<DateInterval> intervals) {
        List<DateInterval> intervalList = new ArrayList<>();
        if (intervals.size() == 0) {
            return intervals;
        }
        for (int i = 0; i < intervals.size(); i++) {
            for (int j = i+1; j < intervals.size(); j++) {
                if (ObjUtils.toLong(intervals.get(i).getStart()) > ObjUtils.toLong(intervals.get(j).getStart())) {
                    DateInterval tem = intervals.get(i);
                    intervals.set(i,intervals.get(j));
                    intervals.set(j,tem);
                }
            }
        }
        Long min = intervals.get(0).getStart();
        Long max = intervals.get(0).getEnd();
        for (int i = 1; i < intervals.size(); i++) {
            //重叠即合并区间
            if (ObjUtils.toLong(intervals.get(i).getStart()) <= max) {
                max = ObjUtils.toLong(intervals.get(i).getEnd()) > max ? ObjUtils.toLong(intervals.get(i).getEnd()) : max;
            } else {
                intervalList.add(new DateInterval(min, max));
                min = ObjUtils.toLong(intervals.get(i).getStart());
                max = ObjUtils.toLong(intervals.get(i).getEnd());
            }
        }
        intervalList.add(new DateInterval(min, max));
        return intervalList;
    }

    /**
     * 获取xx天后日期
     *
     * @param date
     * @param amount
     * @param sdf
     * @return
     */
    public static String dateAdd(Date date, int amount, SimpleDateFormat sdf) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DAY_OF_MONTH, amount);
        Date targetDate = c.getTime();
        return sdf.format(targetDate);
    }

    public static String getWeekOfDate(Date date) {
        String[] weekOfDays = {"周日", "周一", "周二", "周三", "周四", "周五", "周六"};
        Calendar calendar = Calendar.getInstance();
        if (date != null) {
            calendar.setTime(date);
        }
        int w = calendar.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0) {
            w = 0;
        }
        return weekOfDays[w];
    }

    /**
     * 时间段筛选set构造
     * @param dayType
     * @return
     */
    public static Set<Integer> buildTimeRangeDayTypeSet(Integer dayType) {
        Set<Integer> dayTypeSet = new HashSet<>();
        dayTypeSet.add(dayType);
        if (dayType >= DayTempType.FIRSTDAY.getCode() && dayType <= DayTempType.FIVEDAY.getCode()) {
            //周一时，默认加入周日的时间规则
            if (dayType == DayTempType.FIRSTDAY.getCode()) {
                dayTypeSet.add(DayTempType.SEVENDAY.getCode());
                dayTypeSet.add(9);
            } else {
                dayTypeSet.add(dayType - 1);
            }
            dayTypeSet.add(8);
        } else {
            if (dayType == DayTempType.SIXDAY.getCode()) {
                dayTypeSet.add(dayType - 1);
                dayTypeSet.add(8);
            } else {
                dayTypeSet.add(dayType - 1);
            }
            dayTypeSet.add(9);
        }
        logger.info("buildTimeRangeDayTypeSet result:{}", JsonUtils.toJson(dayTypeSet));
        return dayTypeSet;
    }

    public static Boolean checkoutTimeAuth(Integer dayType, List<AirTimeRangeDTO> airTimeRanges, String startTime) {
        try {
            SimpleDateFormat sdfDateTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat sdfDate = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat sdfTime = new SimpleDateFormat("HH:mm:ss");
            //无时间限制
            if (CollectionUtils.isEmpty(airTimeRanges)) {
                return false;
            }
            //获取起飞日期
            Date airStartDate = null;
            String date = startTime.substring(0, 10);
            airStartDate = sdfDate.parse(startTime);
            //日期加一天
            String dateAdd = AirRuleUtils.dateAdd(airStartDate, 1, sdfDate);
            boolean isAllowed = false;
            //日期减一天
            String dateReduce = AirRuleUtils.dateAdd(airStartDate, -1, sdfDate);
            Date airDateTime = sdfDateTime.parse(startTime);
            boolean tag = false;
            for (AirTimeRangeDTO airTimeRange : airTimeRanges) {
                String startDate = "";
                String endDate = "";
                //当天日期
                if (airTimeRange.getDayType().intValue() == dayType.intValue()) {
                    startDate = date + " " + sdfTime.format(airTimeRange.getBeginTime());
                    endDate = date + " " + sdfTime.format(airTimeRange.getEndTime());
                    //跨日日期加一天
                    if (airTimeRange.getIsOvernight()) {
                        endDate = dateAdd + " " + sdfTime.format(airTimeRange.getEndTime());
                    }
                }
                //前一天
                else if (airTimeRange.getDayType() == (dayType - 1)
                    || (dayType == DayTempType.FIRSTDAY.getCode() && airTimeRange.getDayType() == DayTempType.SEVENDAY.getCode())) {
                    if (airTimeRange.getIsOvernight()) {
                        startDate = dateReduce + " " + sdfTime.format(airTimeRange.getBeginTime());
                        endDate = date + " " + sdfTime.format(airTimeRange.getEndTime());
                    } else {
                        continue;
                    }
                }
                //周末或者工作日
                else {
                    //周一特殊处理周末
                    if (dayType == DayTempType.FIRSTDAY.getCode()) {
                        //周末
                        if (airTimeRange.getDayType() == 9) {
                            if (airTimeRange.getIsOvernight()) {
                                startDate = dateReduce + " " + sdfTime.format(airTimeRange.getBeginTime());
                                endDate = date + " " + sdfTime.format(airTimeRange.getEndTime());
                            } else {
                                continue;
                            }
                        } else {
                            startDate = date + " " + sdfTime.format(airTimeRange.getBeginTime());
                            endDate = date + " " + sdfTime.format(airTimeRange.getEndTime());
                            //跨日日期加一天
                            if (airTimeRange.getIsOvernight()) {
                                endDate = dateAdd + " " + sdfTime.format(airTimeRange.getEndTime());
                            }
                        }

                    }
                    //周六特殊处理工作日
                    else if (dayType == DayTempType.SIXDAY.getCode()) {
                        if (airTimeRange.getDayType() == 8) {
                            if (airTimeRange.getIsOvernight()) {
                                startDate = dateReduce + " " + sdfTime.format(airTimeRange.getBeginTime());
                                endDate = date + " " + sdfTime.format(airTimeRange.getEndTime());
                            } else {
                                continue;
                            }
                        } else {
                            startDate = date + " " + sdfTime.format(airTimeRange.getBeginTime());
                            endDate = date + " " + sdfTime.format(airTimeRange.getEndTime());
                            //跨日日期加一天
                            if (airTimeRange.getIsOvernight()) {
                                endDate = dateAdd + " " + sdfTime.format(airTimeRange.getEndTime());
                            }
                        }
                    }
                    //非周一和周六
                    else {
                        startDate = date + " " + sdfTime.format(airTimeRange.getBeginTime());
                        endDate = date + " " + sdfTime.format(airTimeRange.getEndTime());
                        //跨日日期加一天
                        if (airTimeRange.getIsOvernight()) {
                            endDate = dateAdd + " " + sdfTime.format(airTimeRange.getEndTime());
                        }
                        if ((dayType > DayTempType.FIRSTDAY.getCode() && dayType <= DayTempType.FIVEDAY.getCode() &&
                            airTimeRange.getDayType() == 8)
                            || (dayType > DayTempType.SIXDAY.getCode() && dayType <= DayTempType.SEVENDAY.getCode() &&
                            airTimeRange.getDayType() == 9)) {
                            isAllowed = true;
                        }
                    }
                }
                //时间比较
                Date startDateTime = sdfDateTime.parse(startDate);
                Date endDateTime = sdfDateTime.parse(endDate);
                if (airDateTime.getTime() >= startDateTime.getTime() && airDateTime.getTime() <= endDateTime.getTime()) {
                    tag = true;
                    break;
                }
                //特殊处理周末，工作日
                if (isAllowed) {
                    if (airTimeRange.getIsOvernight()) {
                        startDate = dateReduce + " " + sdfTime.format(airTimeRange.getBeginTime());
                        endDate = date + " " + sdfTime.format(airTimeRange.getEndTime());
                        Date startDateTimeW = sdfDateTime.parse(startDate);
                        Date endDateTimeW = sdfDateTime.parse(endDate);
                        if (airDateTime.getTime() >= startDateTimeW.getTime() && airDateTime.getTime() <= endDateTimeW.getTime()) {
                            tag = true;
                            break;
                        }
                    }
                }

            }
            return tag;
        } catch (ParseException e) {
            logger.error("checkoutTimeAuth日期格式化异常" + e.getLocalizedMessage());
        }
        return false;
    }

    /**
     * 火车提供车次列表接口给管控，管控做校验，逻辑为
     *   •  按日期和城市对，调直达站站查询接口：有gdc车次的城市对，取当日gdc二等座最高价；输出车次号和判断依据给用户，文案可以是：二等座最高价
     *   •  没有gdc车次但有其他车次的城市对，取当日硬卧最高价；输出车次号和判断依据，文案可以是：硬卧最高价
     *   •  如当日超出火车发售15天，以第14天价格为准，价格不考虑是否售罄
     * @param airFlightRuleCheckReq
     * @return
     */
    public static SeatMaxPriceDTO getSeatMaxPrice(AirFlightRuleCheckReq airFlightRuleCheckReq, TrainInfo trainInfo,
        BigDecimal multiple) {

        String msg = "获取不到火车票信息，个人支付全部金额";
        SeatMaxPriceDTO seatMaxPriceDTO = new SeatMaxPriceDTO();
        seatMaxPriceDTO.setMaxSecondSeatPrice(trainInfo.getMaxSecondSeatPrice());
        seatMaxPriceDTO.setMaxHardBerthSeatPrice(trainInfo.getMaxHardBerthSeatPrice());

        BigDecimal seatMaxPrice = BigDecimal.ZERO;
        if (trainInfo.getQurydata()) { //查询是有效车次
            if (trainInfo.getMaxSecondSeatPrice() != null
                && !trainInfo.getMaxSecondSeatPrice().equals(BigDecimal.ZERO)) {
                seatMaxPrice = trainInfo.getMaxSecondSeatPrice();
                msg = "二等座最高价";
            } else {
                if (trainInfo.getMaxHardBerthSeatPrice() != null
                    && !trainInfo.getMaxHardBerthSeatPrice().equals(BigDecimal.ZERO)) {
                    seatMaxPrice = trainInfo.getMaxHardBerthSeatPrice().multiply(multiple);
                    msg = "硬卧最高价*倍数";
                }
            }
        }

        seatMaxPriceDTO.setSeatMaxPrice(seatMaxPrice);
        seatMaxPriceDTO.setMsg(msg);
        logger.info("seatMaxPriceDTO:{}", JsonUtils.toJson(seatMaxPriceDTO));
        return seatMaxPriceDTO;
    }

    public static SeatMaxPriceDTO getBackSeatMaxPrice(AirFlightRuleCheckReq airFlightRuleCheckReq,
        TrainInfo trainInfo, BigDecimal multiple) {

        String msg = "获取不到火车票信息，个人支付全部金额";
        SeatMaxPriceDTO seatMaxPriceDTO = new SeatMaxPriceDTO();
        seatMaxPriceDTO.setMaxSecondSeatPrice(trainInfo.getMaxSecondSeatPrice());
        seatMaxPriceDTO.setMaxHardBerthSeatPrice(trainInfo.getMaxHardBerthSeatPrice());

        BigDecimal seatMaxPrice = BigDecimal.ZERO;
        if (trainInfo.getQurydata()) { //查询是有效车次
            if (trainInfo.getMaxSecondSeatPrice() != null
                && !trainInfo.getMaxSecondSeatPrice().equals(BigDecimal.ZERO)) {
                seatMaxPrice = trainInfo.getMaxSecondSeatPrice();
                msg = "二等座最高价";
            } else {
                if (trainInfo.getMaxHardBerthSeatPrice() != null
                    && !trainInfo.getMaxHardBerthSeatPrice().equals(BigDecimal.ZERO)) {
                    seatMaxPrice = trainInfo.getMaxHardBerthSeatPrice().multiply(multiple);
                    msg = "硬卧最高价*倍数";
                }
            }
        }

        seatMaxPriceDTO.setSeatMaxPrice(seatMaxPrice);
        seatMaxPriceDTO.setMsg(msg);
        logger.info("seatMaxPriceDTO:{}", JsonUtils.toJson(seatMaxPriceDTO));
        return seatMaxPriceDTO;
    }

    /**
     * 时间范围不跨天时的校验
     * @return 合规时返回true
     */
    public static boolean checkHolidayNotOverNightTimeRange(List<AirTimeRangeDTO> timeRangeList,
                                                            String departureTime, Integer dayType) {
        String dayEndTime = "24:00:00";
        String departurePrefix = departureTime.substring(0, 11);
        long departureTimestamp = DateUtils.parse(departureTime).getTime();
        for (AirTimeRangeDTO timeRange : timeRangeList) {
            if (!dayType.equals(timeRange.getDayType())) {
                continue;
            }
            String beginTimeStr = DateUtils.format(timeRange.getBeginTime(),"HH:mm:ss");
            String endTimeStr = DateUtils.format(timeRange.getEndTime(),"HH:mm:ss");

            Date beginTime = DateUtils.parse(departurePrefix + beginTimeStr );
            Date endTime = DateUtils.parse(departurePrefix + (timeRange.getIsOvernight() ? dayEndTime : endTimeStr));

            logger.info("checkHolidayNotOverNightTimeRange: departureTimestamp={},beginTimeStr{},endTimeStr={}，beginTime={},endTime={}",departureTimestamp, beginTimeStr,endTimeStr,beginTime,endTime);

            if (departureTimestamp >= beginTime.getTime() && departureTimestamp <= endTime.getTime()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 时间范围跨天时的校验
     * @return 合规时返回true
     */
    public static boolean checkHolidayOverNightTimeRange(List<AirTimeRangeDTO> timeRangeList, String preDepartureTime,
                                                         String departureTime, Integer dayType) {
        String preDeparturePrefix = preDepartureTime.substring(0, 11);
        String departurePrefix = departureTime.substring(0, 11);
        long departureTimestamp = DateUtils.parse(departureTime).getTime();
        for (AirTimeRangeDTO timeRange : timeRangeList) {
            if (!timeRange.getIsOvernight() || !dayType.equals(timeRange.getDayType())) {
                continue;
            }

            String beginTimeStr = DateUtils.format(timeRange.getBeginTime(),"HH:mm:ss");
            String endTimeStr = DateUtils.format(timeRange.getEndTime(),"HH:mm:ss");

            Date beginTime = DateUtils.parse(preDeparturePrefix + beginTimeStr);
            Date endTime = DateUtils.parse(departurePrefix + endTimeStr);

            logger.info("checkHolidayOverNightTimeRange: departureTimestamp={},beginTimeStr{},endTimeStr={}，beginTime={},endTime={}",departureTimestamp, beginTimeStr,endTimeStr,beginTime,endTime);

            if (departureTimestamp >= beginTime.getTime() && departureTimestamp <= endTime.getTime()) {
                return true;
            }
        }
        return false;
    }


    /**
     * 时间范围跨天时的校验
     * @return 合规时返回true
     */
    public static boolean checkHolidayOverNightTimeRange2(List<AirTimeRange> timeRangeList, String preDepartureTime,
                                                         String departureTime, Integer dayType) {
        String preDeparturePrefix = preDepartureTime.substring(0, 11);
        String departurePrefix = departureTime.substring(0, 11);
        long departureTimestamp = DateUtils.parse(departureTime).getTime();
        for (AirTimeRange timeRange : timeRangeList) {
            if (!timeRange.getIsOvernight() || !dayType.equals(timeRange.getDayType())) {
                continue;
            }

            String beginTimeStr = DateUtils.format(timeRange.getBeginTime(),"HH:mm:ss");
            String endTimeStr = DateUtils.format(timeRange.getEndTime(),"HH:mm:ss");

            Date beginTime = DateUtils.parse(preDeparturePrefix + beginTimeStr);
            Date endTime = DateUtils.parse(departurePrefix + endTimeStr);

            logger.info("checkHolidayOverNightTimeRange2: departureTimestamp={},beginTimeStr{},endTimeStr={}，beginTime={},endTime={}",departureTimestamp, beginTimeStr,endTimeStr,beginTime,endTime);

            if (departureTimestamp >= beginTime.getTime() && departureTimestamp <= endTime.getTime()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 时间范围不跨天时的校验
     * @return 合规时返回true
     */
    public static boolean checkHolidayNotOverNightTimeRange2(List<AirTimeRange> timeRangeList,
                                                            String departureTime, Integer dayType) {
        String dayEndTime = "24:00:00";
        String departurePrefix = departureTime.substring(0, 11);
        long departureTimestamp = DateUtils.parse(departureTime).getTime();
        for (AirTimeRange timeRange : timeRangeList) {
            if (!dayType.equals(timeRange.getDayType())) {
                continue;
            }

            String beginTimeStr = DateUtils.format(timeRange.getBeginTime(),"HH:mm:ss");
            String endTimeStr = DateUtils.format(timeRange.getEndTime(),"HH:mm:ss");

            Date beginTime = DateUtils.parse(departurePrefix + beginTimeStr);
            Date endTime = DateUtils.parse(departurePrefix + (timeRange.getIsOvernight() ? dayEndTime : endTimeStr));

            logger.info("checkHolidayNotOverNightTimeRange2: departureTimestamp={},beginTimeStr{},endTimeStr={}，beginTime={},endTime={}",departureTimestamp, beginTimeStr,endTimeStr,beginTime,endTime);

            if (departureTimestamp >= beginTime.getTime() && departureTimestamp <= endTime.getTime()) {
                return true;
            }
        }
        return false;
    }



    /**
     * @MethodName getTimeRangeText
     * @Description 组装-机票-起飞时段-规则文案
     * @param: timeRangeList
     * @return: java.lang.String
     * <AUTHOR> Yunpeng
     * @Date 2022/11/28 14:51
     */
    public static String getTimeRangeText(List<AirTimeRangeDTO> timeRangeList) {
        try {
            Map<Long, List<AirTimeRangeDTO>> batchIdMap =
                    timeRangeList.stream().collect(Collectors.groupingBy(AirTimeRangeDTO::getBatchId));

            List<String> timeTextList = new ArrayList<>();

            SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
            batchIdMap.forEach((k, v) -> {
                v.sort(Comparator.comparingInt(AirTimeRangeDTO::getDayType));

                List<DisplayTimeRange> displayTimeRangeList = new ArrayList<>();
                for (AirTimeRangeDTO airTimeRangeDTO: v) {
                    DisplayTimeRange sameRange = null;
                    for (DisplayTimeRange displayTimeRange : displayTimeRangeList) {
                        AirTimeRangeDTO displayAirTimeRangeDTO = displayTimeRange.getAirTimeRangeDTO();
                        if (Objects.equals(displayAirTimeRangeDTO.getBeginTime(), airTimeRangeDTO.getBeginTime())
                            && Objects.equals(displayAirTimeRangeDTO.getEndTime(), airTimeRangeDTO.getEndTime())
                            && Objects.equals(displayAirTimeRangeDTO.getIsOvernight(),
                            airTimeRangeDTO.getIsOvernight())) {
                            sameRange = displayTimeRange;
                            break;
                        }
                    }
                    if (sameRange != null) {
                        sameRange.getDayTypeList().add(airTimeRangeDTO.getDayType());
                    } else {
                        DisplayTimeRange displayTimeRange = new DisplayTimeRange();
                        displayTimeRange.setAirTimeRangeDTO(airTimeRangeDTO);
                        displayTimeRange.getDayTypeList().add(airTimeRangeDTO.getDayType());
                        displayTimeRangeList.add(displayTimeRange);
                    }
                }

                String text = displayTimeRangeList.stream()
                    .map(displayTimeRange -> {
                        String dayString = displayTimeRange.getDayTypeList().stream().map(DayType::getName)
                            .collect(Collectors.joining("、"));
                        String timeString;
                        AirTimeRangeDTO airTimeRangeDTO = displayTimeRange.getAirTimeRangeDTO();
                        String beginTime = sdf.format(airTimeRangeDTO.getBeginTime());
                        String endTime = sdf.format(airTimeRangeDTO.getEndTime());
                        if (airTimeRangeDTO.getIsOvernight()) {
                            timeString = beginTime + "-" + "次日" + endTime;
                        } else {
                            timeString = beginTime + "-" + endTime;
                        }
                        return dayString + " " + timeString;
                    }).collect(Collectors.joining("；"));

                timeTextList.add(text);
            });

            return String.join("\n", timeTextList);
        } catch (Exception e) {
            logger.info("getTimeRangeText exception, ", e);
        }
        return "";
    }

    /**
     * @MethodName getTimeRangeTextV2
     * @Description 组装-机票-起飞时段-规则文案
     * todo 本地测试ok
     * @param: timeRangeList
     * @return: java.lang.String
     * <AUTHOR> Yunpeng
     * @Date 2022/11/28 14:51
     */
    public static String getTimeRangeTextV2(List<AirTimeRangeDTO> timeRangeList) {
        if (CollectionUtils.isEmpty(timeRangeList)) {
            return "";
        }

        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");

        try {
            Map<Long, List<AirTimeRangeDTO>> dtoListMapByBatchId =
                    timeRangeList.stream().collect(Collectors.groupingBy(AirTimeRangeDTO::getBatchId));
            List<Long> batchIdList = new ArrayList<>(dtoListMapByBatchId.keySet()).stream().sorted().collect(Collectors.toList());
            StringBuilder res = new StringBuilder();
            for (Long batchId : batchIdList) {
                List<AirTimeRangeDTO> dtos = dtoListMapByBatchId.get(batchId);
                List<Integer> remainDayTypeList = dtos.stream().map(AirTimeRangeDTO::getDayType).sorted().collect(Collectors.toList());


                while (CollectionUtils.isNotEmpty(remainDayTypeList)) {
                    Integer currentDayType = remainDayTypeList.get(0);
                    AirTimeRangeDTO currentDayTypeDto = dtos.stream().filter(dto -> Objects.equals(dto.getDayType(), currentDayType)).findFirst().orElse(null);
                    if (Objects.isNull(currentDayTypeDto)) {
                        continue;
                    }
                    List<AirTimeRangeDTO> sameWithCurrentDtoList = dtos.stream().filter(dto -> Objects.equals(dto.getIsOvernight(), currentDayTypeDto.getIsOvernight())
                            && Objects.equals(dto.getBeginTime(), currentDayTypeDto.getBeginTime())
                            && Objects.equals(dto.getEndTime(), currentDayTypeDto.getEndTime())).sorted(Comparator.comparing(AirTimeRangeDTO::getDayType)).collect(Collectors.toList());
                    String dayChina = sameWithCurrentDtoList.stream().map(airTimeRangeDTO -> DayType.getName(airTimeRangeDTO.getDayType())).collect(
                            Collectors.joining("、"));
                    String beginTime = sdf.format(currentDayTypeDto.getBeginTime());
                    String endTime = sdf.format(currentDayTypeDto.getEndTime());

                    String timeText;
                    if (currentDayTypeDto.getIsOvernight()) {
                        timeText = beginTime + AirRuleConstant.SHORT_LINE + "次日" + endTime;
                    } else {
                        timeText = beginTime + "-" + endTime;
                    }
                    res.append(dayChina).append(AirRuleConstant.ONE_BLANK_FILL).append(timeText).append(AirRuleConstant.RETURN);
                    List<Integer> dayTypesNeedRemoveList = sameWithCurrentDtoList.stream().map(AirTimeRangeDTO::getDayType).collect(Collectors.toList());

                    for (Integer dayTypesNeedRemove : dayTypesNeedRemoveList) {
                        remainDayTypeList.remove(dayTypesNeedRemove);
                    }
                }
            }
            String resFinal = res.replace(res.length() - 1, res.length(), "").toString();
            logger.info("res={}", resFinal);
            return resFinal;
        } catch (Exception e) {
            logger.info("getTimeRangeText exception, ", e);
        }
        return "";
    }

    private static class DisplayTimeRange {
        private List<Integer> dayTypeList = new ArrayList<>();
        private AirTimeRangeDTO airTimeRangeDTO = new AirTimeRangeDTO();

        public List<Integer> getDayTypeList() {
            return dayTypeList;
        }

        public void setDayTypeList(List<Integer> dayTypeList) {
            this.dayTypeList = dayTypeList;
        }

        public AirTimeRangeDTO getAirTimeRangeDTO() {
            return airTimeRangeDTO;
        }

        public void setAirTimeRangeDTO(AirTimeRangeDTO airTimeRangeDTO) {
            this.airTimeRangeDTO = airTimeRangeDTO;
        }
    }

    /**
     * @MethodName setErrMsgInfo
     * @Description 封装外层结果集的errMsgInfo
     * @param: resContract
     * @param: ruleCheckResult
     * <AUTHOR> Yunpeng
     * @Date 2022/12/8 11:16
     */
    public static void setErrMsgInfo(TravelOnBusiOrderCheckResContract resContract, TravelOnBusiOrderRuleCheckResult ruleCheckResult) {
        // 封装 errMsgInfo
        if (Objects.nonNull(ruleCheckResult.getCurrencyErrorMessage()) && CollectionUtils.isNotEmpty(ruleCheckResult.getCurrencyErrorMessage().getCurrencyEmployeeMsgList())) {
            ArrayList<ResponseCodeContract> responseCodeContracts = new ArrayList<>();
            for (CurrencyEmployeeMsg employeeMsg : ruleCheckResult.getCurrencyErrorMessage().getCurrencyEmployeeMsgList()) {
                if (CollectionUtils.isNotEmpty(employeeMsg.getErrMsgList())) {
                    for (CurrencyMsg currencyMsg : employeeMsg.getErrMsgList()) {
                        ResponseCodeContract responseCodeContract = new ResponseCodeContract();
                        responseCodeContract.setCode(currencyMsg.getCode());
                        responseCodeContract.setContent(currencyMsg.getErrMsg());
                        responseCodeContract.setUserId(currencyMsg.getEmployeeId());
                        responseCodeContract.setType(1);
                        responseCodeContracts.add(responseCodeContract);
                    }
                }
            }
            ErrMsgInfo errMsgInfo = new ErrMsgInfo();
            errMsgInfo.setTitle(ruleCheckResult.getCurrencyErrorMessage().getTitle());
            errMsgInfo.setErr_code_list(responseCodeContracts);
            resContract.setErr_msg_info(errMsgInfo);
        } else if (Objects.nonNull(ruleCheckResult.getErrMsgInfo()) && CollectionUtils.isNotEmpty(ruleCheckResult.getErrMsgInfo().getErr_code_list())) {
            resContract.setErr_msg_info(ruleCheckResult.getErrMsgInfo());
        }
    }

    public static void setFlightErrMsgInfo(TravelOnBusiOrderRuleCheckResult ruleCheckResult) {
        // 封装 errMsgInfo
        if (CollectionUtils.isNotEmpty(ruleCheckResult.getFlightCheckInfoList())) {
            for (AirFlightRuleCheckRes.flightCheckInfo flightCheckInfo : ruleCheckResult.getFlightCheckInfoList()) {
                ArrayList<ResponseCodeContract> responseCodeContracts = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(flightCheckInfo.getCurrencyMsgList())) {
                    for (CurrencyMsg currencyMsg : flightCheckInfo.getCurrencyMsgList()) {
                        ResponseCodeContract responseCodeContract = new ResponseCodeContract();
                        responseCodeContract.setCode(currencyMsg.getCode());
                        responseCodeContract.setContent(currencyMsg.getErrMsg());
                        responseCodeContract.setUserId(currencyMsg.getEmployeeId());
                        responseCodeContract.setType(1);
                        responseCodeContracts.add(responseCodeContract);
                        flightCheckInfo.setErrCodeList(responseCodeContracts);
                    }
                }
            }
        }
    }

    /***
     * @MethodName formatDiscount
     * @Description 机票折扣展示转换：数据库存储的是0.x，代表x折
     * 转换： 0.x -》 x
     * @param: airDiscount
     * @return: java.lang.String
     * <AUTHOR> Yunpeng
     * @Date 2022/12/21 14:42
     */
    public static String formatDiscount(BigDecimal airDiscount) {
        String discountStr = "0.0";
        try {
            discountStr = airDiscount.multiply(BigDecimal.valueOf(10)).setScale(1, BigDecimal.ROUND_DOWN).toString();
        } catch (Exception e) {
            logger.error("折扣字符串格式化：{}", e.getLocalizedMessage());
        }
        return discountStr;
    }

    /**
     * @MethodName formatMoney
     * @Description 机票场景：钱相关统一format工具类
     * eg.: BigDecimal(10000.13666) => String(10,000.14)
     * @param: money
     * @return: java.lang.String
     * <AUTHOR> Yunpeng
     * @Date 2022/12/22 10:52
     */
    public static String formatMoney(BigDecimal money) {
        return MoneyUtils.formatYuan(money, IS_MONEY_GROUP, IS_NEED_RMB);
    }

    public static String formatMoney(Integer money) {
        if (Objects.isNull(money)) {
            return null;
        }
        return MoneyUtils.formatYuan(BigDecimal.valueOf(money), IS_MONEY_GROUP, IS_NEED_RMB);
    }

    @Data
    public static class SeatMaxPriceDTO {
        private BigDecimal seatMaxPrice;
        private BigDecimal maxSecondSeatPrice;
        private BigDecimal maxHardBerthSeatPrice;
        private String msg;
    }

    public static Double getCityDistance(String startCityId, String arrivalCityId) {
        Double startLongitude = null;
        Double startLatitude = null;
        Double arrivalLongitude = null;
        Double arrivalLatitude = null;
        try {
            logger.info(String.format("获取城市信息调用参数：%s,%s", startCityId, arrivalCityId));
            String responseData = HttpTool.get(URL_GET_AIR_CITY_INFO + "?areas_ids=" + startCityId + "," + arrivalCityId);
            logger.info(String.format("获取城市信息调用结果：%s", responseData));
            JSONObject responseDataMap = JSONObject.parseObject(responseData, JSONObject.class);
            List<JSONObject> data = (List<JSONObject>) responseDataMap.get("data");
            if (CollectionUtils.isNotEmpty(data) && data.size() == 2) {
                for (JSONObject cityInfo : data) {
                    String cityId = ObjUtils.toString(cityInfo.get("id"));
                    if (startCityId.equals(cityId)) {
                        startLongitude = cityInfo.getDouble("longitude");
                        startLatitude = cityInfo.getDouble("latitude");
                    } else {
                        arrivalLongitude = cityInfo.getDouble("longitude");
                        arrivalLatitude = cityInfo.getDouble("latitude");
                    }
                }
                logger.info(String.format("计算距离结果参数：%s,%s,%s,%s", startLongitude, startLatitude, arrivalLongitude, arrivalLatitude));
                Double des = DistanceUtils.getDistance(startLongitude, startLatitude, arrivalLongitude, arrivalLatitude);
                logger.info(String.format("计算距离返回结果：%s,%s,%s,%s,%s", startLongitude, startLatitude, arrivalLongitude, arrivalLatitude, des));
                return des;
            } else {
                return 0d;
            }
        } catch (Exception ex) {
            logger.error(String.format("获取城市信息调用失败：%s,%s,%s", startCityId, arrivalCityId, ex.getMessage()));
            throw new SaasException(GlobalResponseCode.InnerError, "获取城市信息异常，请稍后");
        }
    }
}
