package com.fenbeitong.saas.core.contract.apply;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/** 商务核销列表对象 */
public class ApplyBusinessOrderWriteOffApproveContract implements Serializable {

  private List<ApplyOrderApproveContract> approveContractList;

  // 总金额合计
  private String totalPrice;
  // 回票金额->有发票总金额合计
  private String invoiceReturnTicketPriceTotal;
  // 回票金额->其他总金额合计
  private String otherReturnTicketPriceTotal;
  /// 需回票金额合计
  private String needReturnTicketPriceTotal;

  public List<ApplyOrderApproveContract> getApproveContractList() {
    return approveContractList;
  }

  public void setApproveContractList(List<ApplyOrderApproveContract> approveContractList) {
    this.approveContractList = approveContractList;
  }

  public String getTotalPrice() {
    return totalPrice;
  }

  public void setTotalPrice(String totalPrice) {
    this.totalPrice = totalPrice;
  }

  public String getInvoiceReturnTicketPriceTotal() {
    return invoiceReturnTicketPriceTotal;
  }

  public void setInvoiceReturnTicketPriceTotal(String invoiceReturnTicketPriceTotal) {
    this.invoiceReturnTicketPriceTotal = invoiceReturnTicketPriceTotal;
  }

  public String getOtherReturnTicketPriceTotal() {
    return otherReturnTicketPriceTotal;
  }

  public void setOtherReturnTicketPriceTotal(String otherReturnTicketPriceTotal) {
    this.otherReturnTicketPriceTotal = otherReturnTicketPriceTotal;
  }

  public String getNeedReturnTicketPriceTotal() {
    return needReturnTicketPriceTotal;
  }

  public void setNeedReturnTicketPriceTotal(String needReturnTicketPriceTotal) {
    this.needReturnTicketPriceTotal = needReturnTicketPriceTotal;
  }

  @Override
  public String toString() {
    return "ApplyBusinessOrderWriteOffApproveContract{"
        + "approveContractList="
        + approveContractList
        + ", totalPrice='"
        + totalPrice
        + '\''
        + ", invoiceReturnTicketPriceTotal='"
        + invoiceReturnTicketPriceTotal
        + '\''
        + ", otherReturnTicketPriceTotal='"
        + otherReturnTicketPriceTotal
        + '\''
        + ", needReturnTicketPriceTotal='"
        + needReturnTicketPriceTotal
        + '\''
        + '}';
  }
}
