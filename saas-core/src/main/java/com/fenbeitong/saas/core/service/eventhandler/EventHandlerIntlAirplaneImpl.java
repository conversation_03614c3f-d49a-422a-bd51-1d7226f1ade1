package com.fenbeitong.saas.core.service.eventhandler;

import cn.hutool.core.util.ObjectUtil;
import com.fenbeitong.eventbus.event.order.*;
import com.fenbeitong.eventbus.util.IEventHandler;
import com.fenbeitong.finhub.common.constant.CategoryTypeEnum;
import com.fenbeitong.oc.api.model.dto.res.IntlAirTicketSegment;
import com.fenbeitong.oc.api.service.AirTicketService;
import com.fenbeitong.saas.core.common.ApplicationContextUtils;
import com.fenbeitong.saas.core.common.BizCommonService;
import com.fenbeitong.saas.core.common.base.SaasException;
import com.fenbeitong.saas.core.common.constant.GlobalResponseCode;
import com.fenbeitong.saas.core.common.constant.SaasMessageConstant;
import com.fenbeitong.saas.core.contract.common.CostAttributionInfo;
import com.fenbeitong.saas.core.contract.message.MessageSaveContract;
import com.fenbeitong.saas.core.contract.message.MessageWebContract;
import com.fenbeitong.saas.core.contract.message.inner.ConsumptionInfo;
import com.fenbeitong.saas.core.contract.message.inner.OrderInfo;
import com.fenbeitong.saas.core.language.CoreLanguage;
import com.fenbeitong.saas.core.model.enums.OrderCategory;
import com.fenbeitong.saas.core.model.enums.apply.EvevtOrderStatus;
import com.fenbeitong.saas.core.model.enums.message.*;
import com.fenbeitong.saas.core.model.saas.MessageSetup;
import com.fenbeitong.saas.core.model.saas.MessageSetupEmail;
import com.fenbeitong.saas.core.model.saas.MessageSetupReceiver;
import com.fenbeitong.saas.core.model.v5.apply.constant.ApplyConstant;
import com.fenbeitong.saas.core.service.*;
import com.fenbeitong.saas.core.utils.log.LogUtil;
import com.fenbeitong.saas.core.utils.notice.EmailContract;
import com.fenbeitong.saas.core.utils.notice.NoticeUtils;
import com.fenbeitong.saas.core.utils.notice.SmsContract;
import com.fenbeitong.saas.core.utils.notice.TemplateConstant;
import com.fenbeitong.saas.core.utils.tools.DateTimeTool;
import com.google.common.collect.Lists;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.DateUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.StrUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import scala.collection.JavaConversions;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.text.NumberFormat;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * Created by majinglong on 2018/06/12.
 * 处理出票成功
 */
public class EventHandlerIntlAirplaneImpl implements IEventHandler<IntlAirplaneTicketOrderEvent> {

    private static Logger LOGGER = LoggerFactory.getLogger(EventHandlerIntlAirplaneImpl.class);

    private static final String DATE_MM_DD = "MM-dd";

    private static final String DATE_HH_MM = "HH:mm";

    @Override
    public void handle(IntlAirplaneTicketOrderEvent event) {
        LogUtil.setRequestId(LogUtil.getRequestId());
        LOGGER.info("国际机票Event：" + event.toString());
        sendConsumeInfo(event); //消费通知
        sendOrderInfo(event);   //订单通知
        sendLargeOver(event);   //大额异动
        sendWarnPercentInfo(event);   //预算报警
        sendApplyMessageInfo(event); //审批通知
        sendOrderMessageInfo(event);
    }

    /**
     * 消费通知
     *
     * @param event
     */
    private void sendConsumeInfo(IntlAirplaneTicketOrderEvent event) {
        //只有出票成功的状态才触发
        if (EvevtOrderStatus.AirTicketed.getValue() != event.ticketInfo().status().key()) {
            return;
        }
        IMessageService messageService = ApplicationContextUtils.getBean("messageService", IMessageService.class);
        Set<String> receivers = messageService.getReceivers(event.userInfo().id(), event.userInfo().companyId());
        for (String receiver : receivers) {
            MessageSaveContract messageSaveContract = new MessageSaveContract();
            ConsumptionInfo consumptionInfo = new ConsumptionInfo();
            messageSaveContract.setMessage_type(MessageType.Consume.getCode());
            messageSaveContract.setTitle(MessageType.Consume.getName() + "-" + BizType.InternationalAirPlane.getName());
            StringBuilder content = new StringBuilder()
                    .append(StrUtils.formatString(CoreLanguage.Common_Message_OrderTimeOrPlace.getMessage()
                    , event.userInfo().name()
                    , event.travelInfo().startingCity()
                    , event.travelInfo().arrivedCity()
                    , BizType.InternationalAirPlane.getName()));
            messageSaveContract.setContent(content.toString());
            messageSaveContract.setSender_type(SenderType.Person.getCode());
            messageSaveContract.setSender(event.userInfo().id());
            messageSaveContract.setReceiver(receiver);
            messageSaveContract.setBiz_order(event.orderInfo().id());
            messageSaveContract.setConsumption_info(consumptionInfo);
            messageSaveContract.setCompany_id(event.userInfo().companyId());
            //TODO  messageSaveContract.setLink();
            consumptionInfo.setConsumption_type(BizType.InternationalAirPlane.getCode());
            consumptionInfo.setCreator_msg(event.userInfo().name());
            consumptionInfo.setPrice_msg(StrUtils.formatString(CoreLanguage.Rule_Value_ChineseUnit.getMessage(), event.priceInfo().totalPrice()));
            consumptionInfo.setConsume_time(event.orderInfo().createTime());
            ConsumptionInfo.AirInfo airInfo = new ConsumptionInfo.AirInfo();
            List<IntlAirplaneTicketSegmentDetails> intlAirplaneTicketSegmentDetails = JavaConversions.seqAsJavaList(event.segment().goIntlAirplaneTicketSegmentList());
            if(CollectionUtils.isEmpty(intlAirplaneTicketSegmentDetails)){
                throw new SaasException(GlobalResponseCode.IntlAirInvalidError);
            }
            String flightNo = intlAirplaneTicketSegmentDetails.get(0).flightNo();
            String startingDateTime = intlAirplaneTicketSegmentDetails.get(0).startingDatetime();
            StringBuilder travelMsg = new StringBuilder()
                    .append(StrUtils.formatString(CoreLanguage.Order_Value_TravelOrder.getMessage(), event.travelInfo().startingCity(), event.travelInfo().arrivedCity()))
                    .append(" ")
                    .append(flightNo)
                    .append(" ")
                    .append(startingDateTime);
            airInfo.setTravel_msg(travelMsg.toString());
            airInfo.setPassenger_msg(event.passengerInfo().name());
            consumptionInfo.setAir_info(airInfo);
            if (event.orderInfo().remark().isDefined()) {
                consumptionInfo.setRemark(event.orderInfo().remark().get());
            }
            // 兼容老版本单费用归属
            if (!event.orderInfo().costAttributionList().isEmpty()) {
                List<OrderCostAttribution> orderCostAttributionList = JavaConversions.seqAsJavaList(event.orderInfo().costAttributionList().get());
                List<CostAttributionInfo> costAttributionList = Lists.newArrayList();
                for (OrderCostAttribution orderCostAttribution : orderCostAttributionList) {
                    CostAttributionInfo costAttributionInfo = new CostAttributionInfo();
                    costAttributionInfo.setCost_attribution_category(ObjUtils.toInteger(orderCostAttribution.category().get()));
                    costAttributionInfo.setCost_attribution_id(orderCostAttribution.id());
                    costAttributionInfo.setCost_attribution_name(orderCostAttribution.name());
                    costAttributionList.add(costAttributionInfo);
                }
                consumptionInfo.setCost_attribution_list(costAttributionList);
            } else {
                consumptionInfo.setCost_attribution_name(event.orderInfo().costAttribution().get().name());
            }
            consumptionInfo.setData_type(2);
            try {
                messageService.saveMessage(messageSaveContract);
            } catch (SaasException e) {
                LOGGER.error(e.getMessage(), e);
            }
        }
    }

    /**
     * 订单通知
     *
     * @param event
     */
    private void sendOrderInfo(IntlAirplaneTicketOrderEvent event) {
        if(event.ticketInfo().status().key() != EvevtOrderStatus.AirClosed.getValue()
                && event.ticketInfo().status().key() != EvevtOrderStatus.AirTicketed.getValue()
                && event.ticketInfo().status().key() != EvevtOrderStatus.AirRefundAndChange.getValue()
                && event.ticketInfo().status().key() != EvevtOrderStatus.AirTicketFail.getValue()
                ){
            return;
        }
        List<IntlAirplaneTicketSegmentDetails> goList = JavaConversions.seqAsJavaList(event.segment().goIntlAirplaneTicketSegmentList());
        // 获取订单状态
        int orderStatus = event.orderInfo().status().key();
        // 出发时间
        String startTime = DateUtils.format(DateUtils.parse(event.travelInfo().startingDatetime(), DateUtils.FORMAT_TIME_WITH_BAR), ApplyConstant.FORMAT_DATE_YYMMDD_WITH);
        // 出发机场
        String startAirport = event.travelInfo().simpleStartAirport().isEmpty() ? null : event.travelInfo().simpleStartAirport().get();
        // 出发机场航站楼
        String startTerminal = StringUtils.isEmpty(goList.get(0).startingTerminal()) ? "" : goList.get(0).startingTerminal();
        // 到达机场
        String destinationAirport = event.travelInfo().simpleArrivedAirport().isEmpty() ? null : event.travelInfo().simpleArrivedAirport().get();
        // 到达机场航站楼
        String destinatioTerminal = StringUtils.isEmpty(goList.get(goList.size() - 1).arrivedTerminal()) ? "" : goList.get(goList.size() - 1).arrivedTerminal();
        // 起飞时间
        String departureTime = DateUtils.format(DateUtils.parse(event.travelInfo().startingDatetime(), DateUtils.FORMAT_TIME_WITH_BAR), DATE_HH_MM);
        // 到达时间
        String arrivedTime = DateUtils.format(DateUtils.parse(event.travelInfo().arrivedDatetime(), DateUtils.FORMAT_TIME_WITH_BAR), DATE_HH_MM);
        // 航空公司
        String airLine = goList.get(0).airlineName();
        // 航班号
        String flightNo = goList.get(0).flightNo();
        // 乘机人姓名
        String passengerName = event.passengerInfo().name();
        // 出发机场 航站楼-到达机场 航站楼
        String startAnddestination = startAirport + " " + startTerminal + "-" + destinationAirport + " " + destinatioTerminal;
        // 航空公司 航班号
        String airFlightNo = airLine + " " + flightNo;
        String content = null;
        //校验是否是有退改签
        if (event.ticketInfo().status().key() == EvevtOrderStatus.AirRefundAndChange.getValue()) {
            if (!event.refundOrderInfo().isDefined() && !event.endorseOrderInfo().isDefined()) {
                return;
            }
            String orderStateName = "";
            boolean refundFail = false;
            if (!event.refundSegment().isEmpty()) {
                List<IntlAirplaneTicketOperateSegment> intlAirplaneTicketOperateSegments = JavaConversions.seqAsJavaList(event.refundSegment().toList());
                if (CollectionUtils.isNotEmpty(intlAirplaneTicketOperateSegments)) {
                    for (IntlAirplaneTicketOperateSegment segment : intlAirplaneTicketOperateSegments) {
                        List<IntlAirplaneTicketSegment> intlAirplaneTicketSegments = JavaConversions.seqAsJavaList(segment.failedSegment().toList());
                        if (CollectionUtils.isNotEmpty(intlAirplaneTicketOperateSegments)) {
                            for (IntlAirplaneTicketSegment tsegment : intlAirplaneTicketSegments) {
                                List<IntlAirplaneTicketSegmentDetails> intlAirplaneTicketSegmentDetails = JavaConversions.seqAsJavaList(tsegment.goIntlAirplaneTicketSegmentList().toList());
                                List<IntlAirplaneTicketSegmentDetails> intlAirplaneTicketSegmentDetails1 = JavaConversions.seqAsJavaList(tsegment.backIntlAirplaneTicketSegmentList().toList());
                                if (!CollectionUtils.isEmpty(intlAirplaneTicketSegmentDetails) || !CollectionUtils.isEmpty(intlAirplaneTicketSegmentDetails1)) {
                                    refundFail = true;
                                    break;
                                }
                            }
                        }
                        if (refundFail) {
                            break;
                        }
                    }
                }
            }
            if (refundFail) {
                orderStateName = EvevtOrderStatus.AirRefundFail.getDesc();
                content = MessageFormat.format(CoreLanguage.Common_Message_IntlAirOrderRefundFail.getMessage(), startTime,
                        startAnddestination, departureTime, arrivedTime, airFlightNo, passengerName);
            } else if (event.refundOrderInfo().isDefined()) {
                if (ObjectUtil.equals(EvevtOrderStatus.AirRefund.getValue(), event.refundOrderInfo().get().status().key())) {
                    orderStateName = EvevtOrderStatus.AirRefund.getDesc();
                    content = MessageFormat.format(CoreLanguage.Common_Message_IntlAirOrderRefundSuccess.getMessage(), startTime,
                            startAnddestination, departureTime, arrivedTime, airFlightNo, passengerName);
                }
            } else if (event.endorseOrderInfo().isDefined() && ObjectUtil.equals(EvevtOrderStatus.AirChanged.getValue(), event.endorseOrderInfo().get().status().key())) {
                //改签操作
                orderStateName = EvevtOrderStatus.AirChanged.getDesc();
                String endorseOrderId = event.endorseOrderId().isDefined() ? event.endorseOrderId().get() : null;
                String preOrderId = event.orderInfo().id();
                LOGGER.info("改签成功根据原单查询国际机票订单详情RPC：endorseOrderId={},preOrderId={}", endorseOrderId, preOrderId);
                AirTicketService airTicketService = ApplicationContextUtils.getBean("airTicketService", AirTicketService.class);
                if (ObjUtils.isNotEmpty(endorseOrderId) && ObjUtils.isNotEmpty(preOrderId) && airTicketService != null) {
                    List<IntlAirTicketSegment> endorseIntlAirTicketSegment = null;
                    List<IntlAirTicketSegment> preIntlAirTicketSegment = null;
                    try {
                        endorseIntlAirTicketSegment = airTicketService.getIntlAirTicketSegment(endorseOrderId);
                        preIntlAirTicketSegment = airTicketService.getIntlAirTicketSegment(preOrderId);
                    } catch (Exception e) {
                        LOGGER.error("[调用order-center-api]改签成功根据原单查询国际机票订单详情RPC接口异常");
                        return;
                    }
                    LOGGER.info("改签成功根据原单查询国际机票订单详情RPC：endorseOrderId={}，result={}", endorseOrderId, JsonUtils.toJson(endorseIntlAirTicketSegment));
                    LOGGER.info("改签成功根据原单查询国际机票订单详情RPC：preOrderId={}，result={}", preOrderId, JsonUtils.toJson(preIntlAirTicketSegment));
                    if (CollectionUtils.isNotEmpty(endorseIntlAirTicketSegment) && endorseIntlAirTicketSegment.get(0) != null && CollectionUtils.isNotEmpty(preIntlAirTicketSegment) && preIntlAirTicketSegment.get(0) != null) {

                        IntlAirTicketSegment endorseAirTicketSegment = endorseIntlAirTicketSegment.get(0);
                        // 出发时间戳
                        Date endorseStartingDatetime = endorseAirTicketSegment.getStartingDatetime();
                        String endorseStartTime = DateUtils.format(endorseStartingDatetime, DateUtils.FORMAT_TIME_WITH_MINUTE);
                        String endorseStartingAirport = endorseAirTicketSegment.getStartingAirport();
                        String endorseStartingTerminal = endorseAirTicketSegment.getStartingTerminal();
                        String endorseArrivedAirport = endorseAirTicketSegment.getArrivedAirport();
                        String endorseArrivedTerminal = endorseAirTicketSegment.getArrivedTerminal();
                        String endorseStartInfo = endorseStartTime + "，" + endorseStartingAirport + " " + endorseAirTicketSegment.getFlightNo();
                        String endorseStartAnddestination = endorseStartingAirport + " " + endorseStartingTerminal + "-" + endorseArrivedAirport + " " + endorseArrivedTerminal;
                        String endorseDepartureTime = DateUtils.format(endorseStartingDatetime, DATE_HH_MM);
                        String endorseArrivedTime = DateUtils.format(endorseAirTicketSegment.getArrivedDatetime(), DATE_HH_MM);

                        IntlAirTicketSegment preAirTicketSegment = preIntlAirTicketSegment.get(0);
                        // 出发时间戳
                        Date startingDatetime = preAirTicketSegment.getStartingDatetime();
                        String oldStartTime = DateUtils.format(startingDatetime, DateUtils.FORMAT_TIME_WITH_MINUTE);
                        String oldStartingAirport = preAirTicketSegment.getStartingAirport();
                        String oldStartingTerminal = preAirTicketSegment.getStartingTerminal();
                        String oldArrivedAirport = preAirTicketSegment.getArrivedAirport();
                        String oldArrivedTerminal = preAirTicketSegment.getArrivedTerminal();
                        // 原单：出发日期时间，出发机场，航站楼-到达机场，航站楼
                        String oldStartInfo = oldStartTime + "，" + oldStartingAirport + "，" + oldStartingTerminal + "-" + oldArrivedAirport + "，" + oldArrivedTerminal;
                        // 原单：航空公司 航班号
                        String oldAirlineName = preAirTicketSegment.getAirlineName();
                        String oldFlightNo = preAirTicketSegment.getFlightNo();
                        String oldAirFlightNo = oldAirlineName + oldFlightNo;
                        content = MessageFormat.format(CoreLanguage.Common_Message_IntlAirOrderChangedSuccess.getMessage(),
                                oldStartInfo, oldAirFlightNo, endorseStartInfo, endorseStartAnddestination, endorseDepartureTime, endorseArrivedTime, passengerName);
                    }
                }
            }
            LOGGER.info("国际机票订单通知内容：content={}", content);
            if (ObjUtils.isNotEmpty(content)) {
                IMessageService messageService = ApplicationContextUtils.getBean("messageService", IMessageService.class);
                MessageSaveContract messageSaveContract = new MessageSaveContract();
                OrderInfo orderInfo = new OrderInfo();
                messageSaveContract.setMessage_type(MessageType.Order.getCode());
                messageSaveContract.setTitle(BizType.InternationalAirPlane.getName()+"-" + orderStateName);
                messageSaveContract.setContent(content);
                messageSaveContract.setSender_type(SenderType.HL.getCode());
                messageSaveContract.setSender("");
                messageSaveContract.setReceiver(event.userInfo().id());
                messageSaveContract.setBiz_order(event.orderInfo().id());
                messageSaveContract.setOrder_info(orderInfo);
                messageSaveContract.setCompany_id(event.userInfo().companyId());
                //TODO    messageSaveContract.setLink();
                orderInfo.setOrder_type(BizType.InternationalAirPlane.getCode());
                orderInfo.setCreate_time(event.orderInfo().createTime());
                orderInfo.setOrder_status_msg(orderStateName);
                orderInfo.setOrder_msg(content);
                orderInfo.setCustomer_msg(event.passengerInfo().name());
                orderInfo.setRedirect_order_id(event.orderInfo().id());
                try {
                    messageService.saveMessage(messageSaveContract);
                } catch (SaasException e) {
                    LOGGER.error(e.getMessage(), e);
                }
            }
        }
        // 出票成功
        else if (EventOrderStatusEnum.AirTicketed.getKey() == orderStatus) {
            content = MessageFormat.format(CoreLanguage.Common_Message_IntlAirOrderTicketSuccess.getMessage(), startTime,
                    startAnddestination, departureTime, arrivedTime, airFlightNo, passengerName);
            LOGGER.info("国际机票订单通知内容：content={}", content);
            IMessageService messageService = ApplicationContextUtils.getBean("messageService", IMessageService.class);
            MessageSaveContract messageSaveContract = new MessageSaveContract();
            OrderInfo orderInfo = new OrderInfo();
            messageSaveContract.setMessage_type(MessageType.Order.getCode());
            messageSaveContract.setTitle(BizType.InternationalAirPlane.getName()+"-" + event.ticketInfo().status().value());
            messageSaveContract.setContent(content);
            messageSaveContract.setSender_type(SenderType.HL.getCode());
            messageSaveContract.setSender("");
            messageSaveContract.setReceiver(event.userInfo().id());
            messageSaveContract.setBiz_order(event.orderInfo().id());
            messageSaveContract.setOrder_info(orderInfo);
            messageSaveContract.setCompany_id(event.userInfo().companyId());
            //TODO    messageSaveContract.setLink();
            orderInfo.setOrder_type(BizType.InternationalAirPlane.getCode());
            orderInfo.setCreate_time(event.orderInfo().createTime());
            orderInfo.setOrder_status_msg(event.ticketInfo().status().value());
            orderInfo.setOrder_msg(content);
            orderInfo.setCustomer_msg(event.passengerInfo().name());
            orderInfo.setRedirect_order_id(event.orderInfo().id());
            try {
                messageService.saveMessage(messageSaveContract);
            } catch (SaasException e) {
                LOGGER.error(e.getMessage(), e);
            }
        }
        // 出票失败
        else if (EventOrderStatusEnum.AirTicketFail.getKey() == orderStatus) {
            content = MessageFormat.format(CoreLanguage.Common_Message_AirOrderTicketFail.getMessage(), startTime,
                    startAnddestination, departureTime, arrivedTime, airFlightNo, passengerName);
            LOGGER.info("国际机票订单通知内容：content={}", content);
            IMessageService messageService = ApplicationContextUtils.getBean("messageService", IMessageService.class);
            MessageSaveContract messageSaveContract = new MessageSaveContract();
            OrderInfo orderInfo = new OrderInfo();
            messageSaveContract.setMessage_type(MessageType.Order.getCode());
            messageSaveContract.setTitle(BizType.InternationalAirPlane.getName()+"-" + event.ticketInfo().status().value());
            messageSaveContract.setContent(content);
            messageSaveContract.setSender_type(SenderType.HL.getCode());
            messageSaveContract.setSender("");
            messageSaveContract.setReceiver(event.userInfo().id());
            messageSaveContract.setBiz_order(event.orderInfo().id());
            messageSaveContract.setOrder_info(orderInfo);
            messageSaveContract.setCompany_id(event.userInfo().companyId());
            //TODO    messageSaveContract.setLink();
            orderInfo.setOrder_type(BizType.InternationalAirPlane.getCode());
            orderInfo.setCreate_time(event.orderInfo().createTime());
            orderInfo.setOrder_status_msg(event.ticketInfo().status().value());
            orderInfo.setOrder_msg(content);
            orderInfo.setCustomer_msg(event.passengerInfo().name());
            orderInfo.setRedirect_order_id(event.orderInfo().id());
            try {
                messageService.saveMessage(messageSaveContract);
            } catch (SaasException e) {
                LOGGER.error(e.getMessage(), e);
            }
        }
        else{
            IMessageService messageService = ApplicationContextUtils.getBean("messageService", IMessageService.class);
            MessageSaveContract messageSaveContract = new MessageSaveContract();
            OrderInfo orderInfo = new OrderInfo();
            messageSaveContract.setMessage_type(MessageType.Order.getCode());
            messageSaveContract.setTitle(BizType.InternationalAirPlane.getName()+"-" + event.ticketInfo().status().value());
            StringBuilder contentOld = new StringBuilder()
                    .append(StrUtils.formatString(CoreLanguage.Common_Message_OrderToPlace.getMessage()
                    , event.travelInfo().startingCity()
                    , event.travelInfo().arrivedCity()
                    , BizType.InternationalAirPlane.getName()
                    , event.ticketInfo().status().value()));
            messageSaveContract.setContent(contentOld.toString());
            messageSaveContract.setSender_type(SenderType.HL.getCode());
            messageSaveContract.setSender("");
            messageSaveContract.setReceiver(event.userInfo().id());
            messageSaveContract.setBiz_order(event.orderInfo().id());
            messageSaveContract.setOrder_info(orderInfo);
            messageSaveContract.setCompany_id(event.userInfo().companyId());
            //TODO    messageSaveContract.setLink();
            orderInfo.setOrder_type(BizType.InternationalAirPlane.getCode());
            orderInfo.setCreate_time(event.orderInfo().createTime());
            orderInfo.setOrder_status_msg(event.ticketInfo().status().value());
            StringBuilder orderMsg = new StringBuilder()
                    .append(StrUtils.formatString(CoreLanguage.Common_Message_OrderToPlace.getMessage()
                    , event.travelInfo().startingCity()
                    , event.travelInfo().arrivedCity()
                    , BizType.InternationalAirPlane.getName()
                    , event.ticketInfo().status().value()));
            orderInfo.setOrder_msg(orderMsg.toString());
            orderInfo.setCustomer_msg(event.passengerInfo().name());
            orderInfo.setRedirect_order_id(event.orderInfo().id());
            try {
                messageService.saveMessage(messageSaveContract);
            } catch (SaasException e) {
                LOGGER.error(e.getMessage(), e);
            }
        }
    }

    /**
     * 大额异动
     *
     * @param event
     */
    private void sendLargeOver(IntlAirplaneTicketOrderEvent event) {
        //只有出票成功的状态才触发
        if (OrderCommonStatus.AirTicketed().id() != event.ticketInfo().status().key()
        && OrderCommonStatus.AirChanged().id()!= event.ticketInfo().status().key()) {
            return;
        }
        // 获取大额异动配置
        IMessageSetupService messageSetupService = ApplicationContextUtils.getBean(IMessageSetupService.class);
        LargeOverService largeOverService = ApplicationContextUtils.getBean(LargeOverService.class);
        String companyId = event.userInfo().companyId();
        MessageSetup messageSetup = messageSetupService.queryCompanyMessageSetupWithDefault(companyId, SaasMessageConstant.ITEM_CODE_LARGE_OVER_NOTICE);
        if (messageSetup == null
                || messageSetup.getIsChecked() == SaasMessageConstant.IS_CHECKED_FALSE
                || messageSetup.getIntVal1() <= 0) {
            return;
        }
        // 判断订单是否大额
        BigDecimal orderPrice = new BigDecimal(event.orderInfo().totalPrice().toString());
        LOGGER.info("国际机票大额异动消息处理：订单金额：{}，大额设置：{}", orderPrice, messageSetup.getIntVal1());
        // 订单金额没有超过设置的大额
        if (BizCommonService.priceLowerThanConfig(orderPrice,messageSetup, CategoryTypeEnum.IntlAir.getCode())) {
            LOGGER.info("国际机票大额异动消息处理：订单金额没有超过设置的大额，不提醒。");
            return;
        }
        // 2.保存大额异动通知
        List<MessageSetupReceiver> largeOverReceiverList = messageSetupService.queryMessageReceiverList(companyId, SaasMessageConstant.BUSI_CODE_LARGE_OVER);
        List<String> receiverIdList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(largeOverReceiverList)) {
            receiverIdList = largeOverReceiverList.stream().map(MessageSetupReceiver::getUserId).collect(toList());
        }
        BizCommonService bizCommonService = ApplicationContextUtils.getBean(BizCommonService.class);
        receiverIdList = bizCommonService.getReceiverList(messageSetup,event.userInfo().id(),receiverIdList);
        if(CollectionUtils.isNotEmpty(receiverIdList)){
            LOGGER.info("save intlair largeover message start..........");
            //标题
            String title = StrUtils.formatString(CoreLanguage.Common_Message_LargeOverTitle.getMessage(), event.userInfo().name(), NumberFormat.getCurrencyInstance(Locale.CHINA).format(orderPrice), BizType.InternationalAirPlane.getName());
            String content = StrUtils.formatString(CoreLanguage.Common_Message_LargeOverContent.getMessage(), event.userInfo().name(), NumberFormat.getCurrencyInstance(Locale.CHINA).format(orderPrice), BizType.InternationalAirPlane.getName());
            MessageWebContract largeOverContract = new MessageWebContract();
            largeOverContract.setMessageType(MessageType.Consume.getCode());
            largeOverContract.setMessageSubType(MessageWebSubType.LargeOver.getCode());
            largeOverContract.setCompanyId(companyId);
            largeOverContract.setReceiverIdList(receiverIdList);
            largeOverContract.setTitle(title);
            largeOverContract.setContent(content);
            largeOverContract.setBizeType(BizType.InternationalAirPlane.getCode());
            largeOverContract.setBizOrder(event.orderInfo().id());
            IMessageWebService messageWebService = ApplicationContextUtils.getBean("messageWebService", IMessageWebService.class);
            try {
                messageWebService.saveWebMessage(largeOverContract);
                LOGGER.info("save intlair largeover message end..........");
            } catch (SaasException e) {
                LOGGER.error(e.getMessage(), e);
            }
        }
        // 3.发送短信
        if (CollectionUtils.isNotEmpty(receiverIdList)) {
            // 短信接收者手机号
            Set<String> phoneSet = bizCommonService.getReceiverPhoneList(receiverIdList,companyId);
            if (CollectionUtils.isNotEmpty(phoneSet)) {
                //出票成功 发送短信
                if(OrderCommonStatus.AirTicketed().id() == event.ticketInfo().status().key()){
                    SmsContract smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_LARGE_OVER);
                    Map<String, Object> param = new LinkedHashMap<>();
                    param.put("var1", event.userInfo().name());    //下单人姓名
                    param.put("var2", NumberFormat.getCurrencyInstance(Locale.CHINA).format(orderPrice) + "元"); //订单总价
                    param.put("var3", BizType.InternationalAirPlane.getName()); //业务类型
                    param.put("var4", TemplateConstant.CUSTOMER_SERVICE_PHONE); //客服电话
                    smsContract.setParam(param);
                    NoticeUtils.sendSms(smsContract);
                }
                // 改签成功 发送短信
                if(OrderCommonStatus.AirChanged().id() == event.ticketInfo().status().key()){
                    SmsContract smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_TICKET_MODIFY_LARGE_OVER);
                    Map<String, Object> param = new LinkedHashMap<>();
                    param.put("var1", event.userInfo().deptName());     //部门
                    param.put("var2", event.userInfo().name());  //姓名
                    param.put("var3", NumberFormat.getCurrencyInstance(Locale.CHINA).format(orderPrice) + "元"); //订单总价
                    param.put("var4", BizType.InternationalAirPlane.getName()); //业务类型
                    smsContract.setParam(param);
                    NoticeUtils.sendSms(smsContract);
                }
            }
        }
        // 判断机票是否大额
        BigDecimal ticketPrice = new BigDecimal(event.priceInfo().totalPrice().toString());
        LOGGER.info("国际机票大额异动消息处理：机票金额：{}，大额设置：{}", ticketPrice, messageSetup.getIntVal1());
        // 订单金额没有超过设置的大额
        if (ticketPrice.compareTo(new BigDecimal(messageSetup.getIntVal1())) < 0) {
            LOGGER.info("国际机票大额异动消息处理：机票金额没有超过设置的大额，不提醒。");
            return;
        }
        // 4.发送邮件
        List<MessageSetupEmail> largeOverEmailList = largeOverService.queryMessageEmailList(companyId, SaasMessageConstant.BUSI_CODE_LARGE_OVER);
        if (CollectionUtils.isNotEmpty(largeOverEmailList)) {
            Set<String> emailSet = largeOverEmailList.stream()
                    .map(email -> email.getEmail())
                    .filter(email -> StringUtils.isNotEmpty(email))
                    .collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(emailSet)) {
                EmailContract emailContract = new EmailContract();
                // 收件人
                emailContract.setToList(emailSet);
                // 邮件标题
                String subject = MessageFormat.format(CoreLanguage.Order_Message_FbtConsumerReminder.getMessage(),
                        event.userInfo().deptName(),
                        event.userInfo().name(),
                        NumberFormat.getCurrencyInstance(Locale.CHINA).format(ticketPrice),
                        BizType.InternationalAirPlane.getName());
                emailContract.setSubject(subject);
                // html模板及内容 出票成功
                if(OrderCommonStatus.AirTicketed().id() == event.ticketInfo().status().key()){
                    emailContract.setTemplateId(TemplateConstant.EMAIL_TEMP_LARGE_OVER);
                }
                // html模板及内容 改签成功
                if(OrderCommonStatus.AirChanged().id() == event.ticketInfo().status().key()){
                    emailContract.setTemplateId(TemplateConstant.EMAIL_TEMP_TICKET_MODIFY_LARGE_OVER);
                }

                Map<String, Object> dataMap = new LinkedHashMap<>();
                dataMap.put("orderTime", event.orderInfo().createTime());
                dataMap.put("userName", event.userInfo().name() + " " + event.userInfo().phone());
                dataMap.put("userDept", event.userInfo().deptName());
                dataMap.put("bizType", BizType.InternationalAirPlane.getName());
                dataMap.put("orderPrice", NumberFormat.getCurrencyInstance(Locale.CHINA).format(ticketPrice) + "元");
                List<IntlAirplaneTicketSegmentDetails> segmentDetails = JavaConversions.seqAsJavaList(event.segment().goIntlAirplaneTicketSegmentList());
                String cabin = "";
                String startingDatetime = "";
                String flightNo="";
                for (IntlAirplaneTicketSegmentDetails details : segmentDetails) {
                    cabin = details.cabin();
                    startingDatetime = details.startingDatetime();
                    flightNo=details.flightNo();
                }
                //出票成功
                if(OrderCommonStatus.AirTicketed().id() == event.ticketInfo().status().key()){
                    // yyyy/mm/dd，出发地-目的地， 舱位等级
                    dataMap.put("orderDetail", MessageFormat.format("{0}，{1}-{2}，{3}",
                            DateTimeTool.fromDateToString(DateTimeTool.fromStringToDateTime(startingDatetime)),
                            event.travelInfo().startingCity(),
                            event.travelInfo().arrivedCity(),
                            cabin));
                }
                //改签成功
                if(OrderCommonStatus.AirChanged().id() == event.ticketInfo().status().key()){
                    //行程开始日期：MM月DD日,起止城市,班次号
                    dataMap.put("orderDetail", MessageFormat.format("{0}，{1}-{2}，{3}",
                            DateTimeTool.fromDateToDisplayString(DateTimeTool.fromStringToDateTime(startingDatetime)),
                            event.travelInfo().startingCity(),
                            event.travelInfo().arrivedCity(),
                            flightNo));
                }
                emailContract.setData(dataMap);
                // 发送邮件
                NoticeUtils.sendEmail(emailContract);
            }
        }
    }

    /**
     * 预算报警
     *
     * @param event
     */
    public void sendWarnPercentInfo(IntlAirplaneTicketOrderEvent event) {
        //只有出票成功的状态才触发
        if (OrderCommonStatus.AirTicketed().id() != event.ticketInfo().status().key()) {
            return;
        }
        IBudgetService budgetService = ApplicationContextUtils.getBean(IBudgetService.class);
        String companyAuthEmail = event.userInfo().companyAuthEmail().get();
        String companyName = event.userInfo().companyName();
        String companyId = event.userInfo().companyId();
        String employeeId = event.userInfo().id();
        Integer costAttributionScope = event.orderInfo().costOpt().isEmpty() ? null : ObjUtils.toInteger(event.orderInfo().costOpt().get());
        Integer budgetCostAttrType = event.orderInfo().budgetOpt().isEmpty() ? null : ObjUtils.toInteger(event.orderInfo().budgetOpt().get());
        // 兼容老版本单费用归属
        if (!event.orderInfo().costAttributionList().isEmpty()) {
            List<OrderCostAttribution> orderCostAttributionList = JavaConversions.seqAsJavaList(event.orderInfo().costAttributionList().get());
            List<CostAttributionInfo> costAttributionList = Lists.newArrayList();
            for (OrderCostAttribution orderCostAttribution : orderCostAttributionList) {
                CostAttributionInfo costAttributionInfo = new CostAttributionInfo();
                costAttributionInfo.setCost_attribution_category(ObjUtils.toInteger(orderCostAttribution.category().get()));
                costAttributionInfo.setCost_attribution_id(orderCostAttribution.id());
                costAttributionInfo.setCost_attribution_name(orderCostAttribution.name());
                costAttributionList.add(costAttributionInfo);
            }
            budgetService.sendWarnPercentInfo(companyAuthEmail, companyId, companyName, employeeId, costAttributionList, OrderCategory.IntlAirplane, costAttributionScope, budgetCostAttrType);
        } else {
            String orderId = event.orderInfo().id();
            String costAttributionName = event.orderInfo().costAttribution().get().name();
            budgetService.sendWarnPercentInfo(companyAuthEmail, companyId, companyName, employeeId, orderId, costAttributionName, OrderCategory.IntlAirplane);
        }
    }

    /**
     * 审批通知提醒
     *
     * @param event
     */
    public void sendApplyMessageInfo(IntlAirplaneTicketOrderEvent event) {
        //只有出票成功的状态才触发
        if (EvevtOrderStatus.AirTicketed.getValue() != event.ticketInfo().status().key()) {
            return;
        }
        if (!event.applyId().isDefined()) {
            return;
        }
        String applyId = event.applyId().get();
        if (StringUtils.isBlank(applyId)) {
            return;
        }
        String orderPrice = event.priceInfo().totalPrice().toString();
        IApplyV2Service applyV2Service = ApplicationContextUtils.getBean(IApplyV2Service.class);
        applyV2Service.sloveApplyMessage(applyId, event.userInfo(), orderPrice, BizType.InternationalAirPlane.getName());
    }

    /**
     * 订单通知人
     *
     * @param event
     */
    private void sendOrderMessageInfo(IntlAirplaneTicketOrderEvent event) {
        //只有出票成功的状态才触发
        if (EvevtOrderStatus.AirTicketed.getValue() != event.ticketInfo().status().key()) {
            return;
        }
        IMessageService messageService = ApplicationContextUtils.getBean("messageService", IMessageService.class);
        if (event.orderInfo().notifierInfo().isEmpty()) {
            LOGGER.info("无订单通知人" + event.toString());
            return;
        }
        List<UserInfo> userInfoList = JavaConversions.seqAsJavaList(event.orderInfo().notifierInfo().get());
        for (UserInfo userInfo : userInfoList) {
            MessageSaveContract messageSaveContract = new MessageSaveContract();
            ConsumptionInfo consumptionInfo = new ConsumptionInfo();
            messageSaveContract.setMessage_type(MessageType.System.getCode());
            messageSaveContract.setTitle(MessageType.Consume.getName() + "-" + BizType.InternationalAirPlane.getName());
            StringBuilder content = new StringBuilder()
                    .append(StrUtils.formatString(CoreLanguage.Common_Message_OrderTimeOrPlace.getMessage()
                    , event.userInfo().name()
                    , event.travelInfo().startingCity()
                    , event.travelInfo().arrivedCity()
                    , BizType.InternationalAirPlane.getName()));
            messageSaveContract.setContent(content.toString());
            messageSaveContract.setSender_type(SenderType.Person.getCode());
            messageSaveContract.setSender(event.userInfo().id());
            messageSaveContract.setReceiver(userInfo.id());
            messageSaveContract.setBiz_order(event.orderInfo().id());
            messageSaveContract.setConsumption_info(consumptionInfo);
            messageSaveContract.setCompany_id(event.userInfo().companyId());
            //TODO  messageSaveContract.setLink();
            consumptionInfo.setConsumption_type(BizType.InternationalAirPlane.getCode());
            consumptionInfo.setCreator_msg(event.userInfo().name());
            consumptionInfo.setPrice_msg(StrUtils.formatString(CoreLanguage.Rule_Value_ChineseUnit.getMessage(), event.priceInfo().totalPrice()));
            consumptionInfo.setConsume_time(event.orderInfo().createTime());
            ConsumptionInfo.AirInfo airInfo = new ConsumptionInfo.AirInfo();
            List<IntlAirplaneTicketSegmentDetails> intlAirplaneTicketSegmentDetails = JavaConversions.seqAsJavaList(event.segment().goIntlAirplaneTicketSegmentList());
            if(CollectionUtils.isEmpty(intlAirplaneTicketSegmentDetails)){
                throw new SaasException(GlobalResponseCode.IntlAirInvalidError);
            }
            String flightNo = intlAirplaneTicketSegmentDetails.get(0).flightNo();
            String startingDateTime = intlAirplaneTicketSegmentDetails.get(0).startingDatetime();
            StringBuilder travelMsg = new StringBuilder()
                    .append(StrUtils.formatString(CoreLanguage.Order_Value_TravelOrder.getMessage(), event.travelInfo().startingCity(), event.travelInfo().arrivedCity()))
                    .append(" ")
                    .append(flightNo)
                    .append(" ")
                    .append(startingDateTime);
            airInfo.setTravel_msg(travelMsg.toString());
            airInfo.setPassenger_msg(event.passengerInfo().name());
            consumptionInfo.setAir_info(airInfo);
            if (event.orderInfo().remark().isDefined()) {
                consumptionInfo.setRemark(event.orderInfo().remark().get());
            }
            // 兼容老版本单费用归属
            if (!event.orderInfo().costAttributionList().isEmpty()) {
                List<OrderCostAttribution> orderCostAttributionList = JavaConversions.seqAsJavaList(event.orderInfo().costAttributionList().get());
                List<CostAttributionInfo> costAttributionList = Lists.newArrayList();
                for (OrderCostAttribution orderCostAttribution : orderCostAttributionList) {
                    CostAttributionInfo costAttributionInfo = new CostAttributionInfo();
                    costAttributionInfo.setCost_attribution_category(ObjUtils.toInteger(orderCostAttribution.category().get()));
                    costAttributionInfo.setCost_attribution_id(orderCostAttribution.id());
                    costAttributionInfo.setCost_attribution_name(orderCostAttribution.name());
                    costAttributionList.add(costAttributionInfo);
                }
                consumptionInfo.setCost_attribution_list(costAttributionList);
            } else {
                consumptionInfo.setCost_attribution_name(event.orderInfo().costAttribution().get().name());
            }
            consumptionInfo.setData_type(2);
            try {
                messageService.saveMessage(messageSaveContract);
            } catch (SaasException e) {
                LOGGER.error(e.getMessage(), e);
            }
        }
    }

}

