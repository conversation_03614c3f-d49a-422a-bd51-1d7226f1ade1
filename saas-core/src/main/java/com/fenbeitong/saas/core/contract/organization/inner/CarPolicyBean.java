package com.fenbeitong.saas.core.contract.organization.inner;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/5/22.
 */
public class CarPolicyBean {
    /**
     * car_priv_flag : true
     * rule_limit_flag : true
     * rule_id : 2
     * exceed_buy_flag : false
     */

    private Boolean car_priv_flag;
    private Boolean rule_limit_flag;
    private Integer rule_id;
    private Boolean exceed_buy_flag;
    private Integer exceed_buy_type;
    private Boolean allow_shuttle;
    public Boolean isCar_priv_flag() {
        return car_priv_flag;
    }

    public void setCar_priv_flag(Boolean car_priv_flag) {
        this.car_priv_flag = car_priv_flag;
    }

    public Boolean isRule_limit_flag() {
        return rule_limit_flag;
    }

    public void setRule_limit_flag(Boolean rule_limit_flag) {
        this.rule_limit_flag = rule_limit_flag;
    }

    public Integer getRule_id() {
        return rule_id;
    }

    public void setRule_id(Integer rule_id) {
        this.rule_id = rule_id;
    }

    public Boolean isExceed_buy_flag() {
        return exceed_buy_flag;
    }

    public void setExceed_buy_flag(<PERSON>olean exceed_buy_flag) {
        this.exceed_buy_flag = exceed_buy_flag;
    }

    public Integer getExceed_buy_type() {
        return exceed_buy_type;
    }

    public void setExceed_buy_type(Integer exceed_buy_type) {
        this.exceed_buy_type = exceed_buy_type;
    }

    public Boolean getAllow_shuttle() {
        return allow_shuttle;
    }

    public void setAllow_shuttle(Boolean allow_shuttle) {
        this.allow_shuttle = allow_shuttle;
    }
}
