package com.fenbeitong.saas.core.contract.organization.inner;

/**
 * Created by xuzn on 17/12/29.
 */
public class HotelPolicyBean {


    /**
     * 本人酒店权限
     */
    private Boolean hotel_priv_flag;

    /**
     * 为其他员工预定权限
     */
    private Boolean hotel_other_flag;

    public Boolean getHotel_other_flag() {
        return hotel_other_flag;
    }

    public void setHotel_other_flag(Boolean hotel_other_flag) {
        this.hotel_other_flag = hotel_other_flag;
    }

    /**
     * 酒店是否需要审批
     */
    private Boolean hotel_verify_flag;
    /**
     * 限制非企业员工预定酒店标识
     */
    private Boolean unemployee_hotel;

    /**
     * 是否限制采购规则
     */
    private Boolean rule_limit_flag;
    /**
     * 差旅规则 id
     */
    private String rule_id;

    private Integer exceed_buy_type;

    private int hotel_rule;

    private String rule_name;

    private Boolean hotel_order_verify_flag;

    public Boolean getHotel_priv_flag() {
        return hotel_priv_flag;
    }

    public void setHotel_priv_flag(Boolean hotel_priv_flag) {
        this.hotel_priv_flag = hotel_priv_flag;
    }

    public Boolean getHotel_verify_flag() {
        return hotel_verify_flag;
    }

    public void setHotel_verify_flag(Boolean hotel_verify_flag) {
        this.hotel_verify_flag = hotel_verify_flag;
    }

    public Boolean getUnemployee_hotel() {
        return unemployee_hotel;
    }

    public void setUnemployee_hotel(Boolean unemployee_hotel) {
        this.unemployee_hotel = unemployee_hotel;
    }

    public Boolean getRule_limit_flag() {
        return rule_limit_flag;
    }

    public void setRule_limit_flag(Boolean rule_limit_flag) {
        this.rule_limit_flag = rule_limit_flag;
    }

    public String getRule_id() {
        return rule_id;
    }

    public void setRule_id(String rule_id) {
        this.rule_id = rule_id;
    }

    public Integer getExceed_buy_type() {
        return exceed_buy_type;
    }

    public void setExceed_buy_type(Integer exceed_buy_type) {
        this.exceed_buy_type = exceed_buy_type;
    }

    public int getHotel_rule() {
        return hotel_rule;
    }

    public void setHotel_rule(int hotel_rule) {
        this.hotel_rule = hotel_rule;
    }

    public String getRule_name() {
        return rule_name;
    }

    public void setRule_name(String rule_name) {
        this.rule_name = rule_name;
    }

    public Boolean getHotel_order_verify_flag() {
        return hotel_order_verify_flag;
    }

    public void setHotel_order_verify_flag(Boolean hotel_order_verify_flag) {
        this.hotel_order_verify_flag = hotel_order_verify_flag;
    }

    public HotelPolicyBean(Boolean hotel_priv_flag, Boolean hotel_verify_flag, Boolean unemployee_hotel, Boolean rule_limit_flag, String rule_id,
                           Integer exceed_buy_type, int hotel_rule, String rule_name, Boolean hotel_order_verify_flag) {
        this.hotel_priv_flag = hotel_priv_flag;
        this.hotel_verify_flag = hotel_verify_flag;
        this.unemployee_hotel = unemployee_hotel;
        this.rule_limit_flag = rule_limit_flag;
        this.rule_id = rule_id;
        this.exceed_buy_type = exceed_buy_type;
        this.hotel_rule = hotel_rule;
        this.rule_name = rule_name;
        this.hotel_order_verify_flag = hotel_order_verify_flag;
    }

    public HotelPolicyBean() {
    }
}
