package com.fenbeitong.saas.core.contract.applyflow;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhaohaichao on 2019/11/26.
 */
public class ExcelApplyResultContract implements Serializable {
    private int status;
    private Double schedule;
    private String errorMsg;
    private List<ApplyFlowContract> result;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Double getSchedule() {
        return schedule;
    }

    public void setSchedule(Double schedule) {
        this.schedule = schedule;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public List<ApplyFlowContract> getResult() {
        return result;
    }

    public void setResult(List<ApplyFlowContract> result) {
        this.result = result;
    }
}
