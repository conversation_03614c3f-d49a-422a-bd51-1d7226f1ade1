package com.fenbeitong.saas.core.service.eventhandler;

import com.fenbeitong.eventbus.event.order.OrderCostAttribution;
import com.fenbeitong.eventbus.event.order.TaxiOrderEvent;
import com.fenbeitong.eventbus.event.order.UserInfo;
import com.fenbeitong.eventbus.util.IEventHandler;
import com.fenbeitong.finhub.common.constant.CategoryTypeEnum;
import com.fenbeitong.finhub.common.constant.OrderCommonEventEnum;
import com.fenbeitong.saas.core.common.ApplicationContextUtils;
import com.fenbeitong.saas.core.common.BizCommonService;
import com.fenbeitong.saas.core.common.base.SaasException;
import com.fenbeitong.saas.core.common.constant.SaasMessageConstant;
import com.fenbeitong.saas.core.contract.common.CostAttributionInfo;
import com.fenbeitong.saas.core.contract.message.MessageSaveContract;
import com.fenbeitong.saas.core.contract.message.MessageTemplate;
import com.fenbeitong.saas.core.contract.message.MessageWebContract;
import com.fenbeitong.saas.core.contract.message.MessageWebTemplate;
import com.fenbeitong.saas.core.contract.message.inner.ConsumptionInfo;
import com.fenbeitong.saas.core.contract.message.inner.OrderInfo;
import com.fenbeitong.saas.core.language.CoreLanguage;
import com.fenbeitong.saas.core.model.enums.OrderCategory;
import com.fenbeitong.saas.core.model.enums.message.*;
import com.fenbeitong.saas.core.model.saas.MessageSetup;
import com.fenbeitong.saas.core.model.saas.MessageSetupEmail;
import com.fenbeitong.saas.core.model.saas.MessageSetupReceiver;
import com.fenbeitong.saas.core.service.*;
import com.fenbeitong.saas.core.utils.notice.EmailContract;
import com.fenbeitong.saas.core.utils.notice.NoticeUtils;
import com.fenbeitong.saas.core.utils.notice.SmsContract;
import com.fenbeitong.saas.core.utils.notice.TemplateConstant;
import com.fenbeitong.saas.core.utils.tools.DateTimeTool;
import com.google.common.collect.Lists;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.StrUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import scala.collection.JavaConversions;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.text.NumberFormat;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * Created by chenshang on 2017/4/19.
 * 处理出票成功
 */
public class EventHandlerTaxiImpl implements IEventHandler<TaxiOrderEvent> {

    private static Logger LOGGER = LoggerFactory.getLogger(EventHandlerTaxiImpl.class);


    @Override
    public void handle(TaxiOrderEvent event) {
        LOGGER.info("用车Event：" + event.toString());
        if (event.orderInfo() != null && event.orderInfo().orderType().get().equals(1)) {
            sendConsumeInfo(event);
            sendLargeOver(event);
            sendWarnPercentInfo(event);//预算预警
            sendOrderMessageInfo(event);
            sendSensitiveInfo(event);
        }
    }

    /**
     * 敏感订单消息
     *
     * @param event
     */
    private void sendSensitiveInfo(TaxiOrderEvent event) {
        int smsRecieveType = event.smsRecieveType();
        if (smsRecieveType < -2) {
            LOGGER.info("当前event订单通知由场景订单处理，不操作");
            return;
        }
        // 获取订单状态
        int orderStatus = event.orderInfo().status().key();
        // 上车点
        String departureName = event.departureName();
        // 目的地
        String arrivalName = event.arrivalName();
        // 车型
        String taxiType = event.carInfo().isEmpty() ? null : event.carInfo().get();
        // 司机姓名
        String driverName = event.driverName().isEmpty() ? null : event.driverName().get();
        // 司机电话
        String driverPhone = event.driverPhone().isEmpty() ? null : event.driverPhone().get();

        String content = null;
        String title = null;
        // 订单超时
        if (EventOrderStatusEnum.Timeout.getKey() == orderStatus) {
            title = CoreLanguage.Common_Message_TaxiOrderTimeoutTile.getMessage();
            content = MessageFormat.format(CoreLanguage.Common_Message_TaxiOrderTimeout.getMessage(), departureName, arrivalName);
        }
        // 司机已接单
        else if (EventOrderStatusEnum.TaxiDispatched.getKey() == orderStatus || EventOrderStatusEnum.TaxiPicking.getKey() == orderStatus) {
            title = CoreLanguage.Common_Message_TaxiOrderDispatchedAndPickingTitle.getMessage();
            String companyId = null;
            // 因私订单公司id存用户id
            if (event.orderInfo() != null && event.orderInfo().orderType().get().equals(2)) {
                companyId = event.userInfo().id();
            } else {
                companyId = event.userInfo().companyId();
            }
            // 查询是否已有司机已接单状态数据
            IMessageService messageService = ApplicationContextUtils.getBean("messageService", IMessageService.class);
            int count = messageService.queryUserOrderStatus(companyId, event.userInfo().id(), event.orderInfo().id(), title);
            if (count > 0) {
                return;
            }
            content = MessageFormat.format(CoreLanguage.Common_Message_TaxiOrderDispatchedAndPicking.getMessage(), arrivalName,
                    taxiType, driverName, driverPhone);
        }
        // 司机取消
        else if (EventOrderStatusEnum.TaxiDriverCancel.getKey() == orderStatus) {
            title = CoreLanguage.Common_Message_TaxiOrderDriverCancelTitle.getMessage();
            content = MessageFormat.format(CoreLanguage.Common_Message_TaxiOrderDriverCancel.getMessage(), departureName,
                    arrivalName);
        }
        // 司机已到达
        else if (EventOrderStatusEnum.DriverArrival.getKey() == orderStatus) {
            title = CoreLanguage.Common_Message_TaxiOrderDriverArrivalTitle.getMessage();
            content = MessageFormat.format(CoreLanguage.Common_Message_TaxiOrderDriverArrival.getMessage(), taxiType, driverName,
                    driverPhone);
        }
        // 行程已结束
        else if (EventOrderStatusEnum.TaxiFinish.getKey() == orderStatus || EventOrderStatusEnum.Paid.getKey() == orderStatus) {
            title = CoreLanguage.Common_Message_TaxiOrderFinishTtile.getMessage();
            String companyId = null;
            // 因私订单公司id存用户id
            if (event.orderInfo() != null && event.orderInfo().orderType().get().equals(2)) {
                companyId = event.userInfo().id();
            } else {
                companyId = event.userInfo().companyId();
            }
            // 查询是否已有司机已接单状态数据
            IMessageService messageService = ApplicationContextUtils.getBean("messageService", IMessageService.class);
            int count = messageService.queryUserOrderStatus(companyId, event.userInfo().id(), event.orderInfo().id(), title);
            if (count > 0) {
                return;
            }
            // 费用
            BigDecimal totalPrice = ObjUtils.toBigDecimal(event.orderInfo().totalPrice()).setScale(2, BigDecimal.ROUND_HALF_UP);
            content = MessageFormat.format(CoreLanguage.Common_Message_TaxiOrderFinish.getMessage(), arrivalName, totalPrice);
        }
        // 员工待支付
        else if (EventOrderStatusEnum.PersonalWaitPay.getKey() == orderStatus) {
            title = CoreLanguage.Common_Message_TaxiOrderPersonalWaitPayTitle.getMessage();
            // 费用
            BigDecimal totalPrice = ObjUtils.toBigDecimal(event.totalPayPrice().isEmpty() ? 0 : event.totalPayPrice().get()).setScale(2, BigDecimal.ROUND_HALF_UP);
            // 个人支付费用
            BigDecimal waitPayPrice = ObjUtils.toBigDecimal(event.personalPayPrice().isEmpty() ? 0 : event.personalPayPrice().get()).setScale(2, BigDecimal.ROUND_HALF_UP);
            content = MessageFormat.format(CoreLanguage.Common_Message_TaxiOrderPersonalWaitPay.getMessage(), arrivalName,
                    totalPrice, waitPayPrice);
        }
        LOGGER.info("用车订单通知内容：content={}", content);
        // 用车订单标记为敏感订单，且虚拟子订单订单状态为待支付时
        if ((EventOrderStatusEnum.Timeout.getKey() == orderStatus
                || EventOrderStatusEnum.TaxiDispatched.getKey() == orderStatus
                || EventOrderStatusEnum.TaxiPicking.getKey() == orderStatus
                || EventOrderStatusEnum.TaxiDriverCancel.getKey() == orderStatus
                || EventOrderStatusEnum.DriverArrival.getKey() == orderStatus
                || EventOrderStatusEnum.TaxiFinish.getKey() == orderStatus
                || EventOrderStatusEnum.PersonalWaitPay.getKey() == orderStatus
                || EventOrderStatusEnum.Paid.getKey() == orderStatus) && ObjUtils.isNotEmpty(content)) {
            IMessageService messageService = ApplicationContextUtils.getBean("messageService", IMessageService.class);
            MessageSaveContract messageSaveContract = new MessageSaveContract();
            OrderInfo orderInfo = new OrderInfo();
            messageSaveContract.setMessage_type(MessageType.Order.getCode());
            messageSaveContract.setTitle(title);
            messageSaveContract.setContent(content);
            messageSaveContract.setSender_type(SenderType.HL.getCode());
            messageSaveContract.setSender("");
            messageSaveContract.setReceiver(event.userInfo().id());
            messageSaveContract.setBiz_order(event.orderInfo().id());
            messageSaveContract.setOrder_info(orderInfo);
            //因私订单公司id存用户id
            if (event.orderInfo() != null && event.orderInfo().orderType().get().equals(2)) {
                messageSaveContract.setCompany_id(event.userInfo().id());
            } else {
                messageSaveContract.setCompany_id(event.userInfo().companyId());
            }
            orderInfo.setOrder_type(BizType.Taxi.getCode());
            orderInfo.setCreate_time(event.orderInfo().createTime());
            orderInfo.setOrder_status_msg(event.orderInfo().status().value());
            orderInfo.setOrder_msg("您的用车订单已调整为员工支付，请及时支付，点击查看订单详情");
            orderInfo.setCustomer_msg(event.passengerInfo().name());
            orderInfo.setRedirect_order_id(event.orderInfo().id());
            try {
                messageService.saveMessage(messageSaveContract);
            } catch (SaasException e) {
                LOGGER.error(e.getMessage(), e);
            }
        }
        // 用车敏感订单被重新调整为企业支付时
        if (event.orderInfo().event().isDefined() && OrderCommonEventEnum.CANCELSENSITIVEORDER.getKey().equals(event.orderInfo().event().get())) {
            IMessageService messageService = ApplicationContextUtils.getBean("messageService", IMessageService.class);
            MessageSaveContract messageSaveContract = new MessageSaveContract();
            messageSaveContract.setMessage_type(MessageType.Order.getCode());
            messageSaveContract.setTitle("用车-已完成");
            messageSaveContract.setContent("您的用车订单已调整回企业支付，点击查看订单详情");
            messageSaveContract.setSender_type(SenderType.HL.getCode());
            messageSaveContract.setSender("");
            messageSaveContract.setReceiver(event.userInfo().id());
            messageSaveContract.setBiz_order(event.orderInfo().id());
            // 因私订单公司id存用户id
            if (event.orderInfo() != null && event.orderInfo().orderType().get().equals(2)) {
                messageSaveContract.setCompany_id(event.userInfo().id());
            } else {
                messageSaveContract.setCompany_id(event.userInfo().companyId());
            }
            OrderInfo orderInfo = new OrderInfo();
            orderInfo.setOrder_type(BizType.Taxi.getCode());
            orderInfo.setCreate_time(event.orderInfo().createTime());
            orderInfo.setOrder_status_msg(event.orderInfo().status().value());
            orderInfo.setOrder_msg("您的用车订单已调整回企业支付，点击查看订单详情");
            orderInfo.setCustomer_msg(event.passengerInfo().name());
            orderInfo.setRedirect_order_id(event.orderInfo().id());
            messageSaveContract.setOrder_info(orderInfo);
            try {
                messageService.saveMessage(messageSaveContract);
            } catch (SaasException e) {
                LOGGER.error(e.getMessage(), e);
            }
        }
    }

    private void sendConsumeInfo(TaxiOrderEvent event) {
        double priceConsume = event.personalPayPrice().isEmpty()?0:event.personalPayPrice().get().doubleValue();
        int totalNum = event.orderInfo().totalPrice().compare(scala.math.BigDecimal.valueOf(0));
        if (priceConsume == 0){
            //无员工待支付-700
            if (TaxiScheduleType.TaxiPaid.getCode() != event.orderInfo().status().key()) {
                return;
            }
            IMessageService messageService = ApplicationContextUtils.getBean("messageService", IMessageService.class);
            Set<String> receivers = messageService.getReceivers(event.userInfo().id(), event.userInfo().companyId());
            for (String receiver : receivers) {
                MessageSaveContract messageSaveContract = new MessageSaveContract();
                messageSaveContract.setMessage_type(MessageType.Consume.getCode());
                messageSaveContract.setTitle(CoreLanguage.Common_Message_CompanyUsedCarTitle.getMessage());
                StringBuilder content = new StringBuilder()
                        .append(StrUtils.formatString(CoreLanguage.Common_Message_OrderCarTimeOrPlace.getMessage()
                        , event.userInfo().name()
                        , event.departureName()
                        , event.arrivalName()));
                messageSaveContract.setContent(content.toString());
                messageSaveContract.setSender_type(SenderType.Person.getCode());
                messageSaveContract.setSender(event.userInfo().id());
                messageSaveContract.setReceiver(receiver);
                messageSaveContract.setBiz_order(event.orderInfo().id());
                messageSaveContract.setCompany_id(event.userInfo().companyId());
                ConsumptionInfo consumptionInfo = new ConsumptionInfo();
                consumptionInfo.setConsumption_type(BizType.Taxi.getCode());
                consumptionInfo.setCreator_msg(event.userInfo().name());
                consumptionInfo.setPrice_msg(StrUtils.formatString(CoreLanguage.Rule_Value_ChineseUnit.getMessage(),NumberFormat.getCurrencyInstance(Locale.CHINA).format(event.orderInfo().totalPrice())));
                consumptionInfo.setConsume_time(event.finishChargeTime());
                ConsumptionInfo.TaxiInfo taxiInfo = new ConsumptionInfo.TaxiInfo();
                taxiInfo.setTravel_msg(event.departureName() + "到" + event.arrivalName());
                taxiInfo.setPassenger_msg(event.passengerInfo().name());
                taxiInfo.setSchedule_type(event.serverType().key());
                consumptionInfo.setTaxi_info(taxiInfo);
                messageSaveContract.setConsumption_info(consumptionInfo);
                if (event.orderInfo().remark().isDefined()) {
                    consumptionInfo.setRemark(event.orderInfo().remark().get());
                }
                // 兼容老版本单费用归属
                if (!event.orderInfo().costAttributionList().isEmpty()) {
                    List<OrderCostAttribution> orderCostAttributionList = JavaConversions.seqAsJavaList(event.orderInfo().costAttributionList().get());
                    List<CostAttributionInfo> costAttributionList = Lists.newArrayList();
                    for (OrderCostAttribution orderCostAttribution : orderCostAttributionList) {
                        CostAttributionInfo costAttributionInfo = new CostAttributionInfo();
                        costAttributionInfo.setCost_attribution_category(ObjUtils.toInteger(orderCostAttribution.category().get()));
                        costAttributionInfo.setCost_attribution_id(orderCostAttribution.id());
                        costAttributionInfo.setCost_attribution_name(orderCostAttribution.name());
                        costAttributionList.add(costAttributionInfo);
                    }
                    consumptionInfo.setCost_attribution_list(costAttributionList);
                } else {
                    consumptionInfo.setCost_attribution_name(event.orderInfo().costAttribution().get().name());
                }
                consumptionInfo.setData_type(2);
                try {
                    LOGGER.info("用车Event：messageSaveContract = {}", messageSaveContract);
                    messageService.saveMessage(messageSaveContract);
                } catch (SaasException e) {
                    LOGGER.error(e.getMessage(), e);
                }
            }
        }else if (priceConsume > 0 && totalNum > 0){
            //有员工待支付-690
            if (TaxiScheduleType.TaxiPersonalWaitPay.getCode() != event.orderInfo().status().key()) {
                return;
            }
            IMessageService messageService = ApplicationContextUtils.getBean("messageService", IMessageService.class);
            Set<String> receivers = messageService.getReceivers(event.userInfo().id(), event.userInfo().companyId());
            for (String receiver : receivers) {
                MessageSaveContract messageSaveContract = new MessageSaveContract();
                messageSaveContract.setMessage_type(MessageType.Consume.getCode());
                messageSaveContract.setTitle(CoreLanguage.Common_Message_CompanyUsedCarTitle.getMessage());
                StringBuilder content = new StringBuilder()
                        .append(StrUtils.formatString(CoreLanguage.Common_Message_OrderCarTimeOrPlace.getMessage()
                                , event.userInfo().name()
                                , event.departureName()
                                , event.arrivalName()));
                messageSaveContract.setContent(content.toString());
                messageSaveContract.setSender_type(SenderType.Person.getCode());
                messageSaveContract.setSender(event.userInfo().id());
                messageSaveContract.setReceiver(receiver);
                messageSaveContract.setBiz_order(event.orderInfo().id());
                messageSaveContract.setCompany_id(event.userInfo().companyId());
                ConsumptionInfo consumptionInfo = new ConsumptionInfo();
                consumptionInfo.setConsumption_type(BizType.Taxi.getCode());
                consumptionInfo.setCreator_msg(event.userInfo().name());
                consumptionInfo.setPrice_msg(StrUtils.formatString(CoreLanguage.Rule_Value_ChineseUnit.getMessage(),NumberFormat.getCurrencyInstance(Locale.CHINA).format(event.orderInfo().totalPrice())));
                consumptionInfo.setConsume_time(event.finishChargeTime());
                ConsumptionInfo.TaxiInfo taxiInfo = new ConsumptionInfo.TaxiInfo();
                taxiInfo.setTravel_msg(event.departureName() + "到" + event.arrivalName());
                taxiInfo.setPassenger_msg(event.passengerInfo().name());
                taxiInfo.setSchedule_type(event.serverType().key());
                consumptionInfo.setTaxi_info(taxiInfo);
                messageSaveContract.setConsumption_info(consumptionInfo);
                if (event.orderInfo().remark().isDefined()) {
                    consumptionInfo.setRemark(event.orderInfo().remark().get());
                }
                List<OrderCostAttribution> orderCostAttributionList = JavaConversions.seqAsJavaList(event.orderInfo().costAttributionList().get());
                // 兼容老版本单费用归属
                if (ObjUtils.isNotEmpty(orderCostAttributionList)) {
                    List<CostAttributionInfo> costAttributionList = Lists.newArrayList();
                    for (OrderCostAttribution orderCostAttribution : orderCostAttributionList) {
                        CostAttributionInfo costAttributionInfo = new CostAttributionInfo();
                        costAttributionInfo.setCost_attribution_category(ObjUtils.toInteger(orderCostAttribution.category().get()));
                        costAttributionInfo.setCost_attribution_id(orderCostAttribution.id());
                        costAttributionInfo.setCost_attribution_name(orderCostAttribution.name());
                        costAttributionList.add(costAttributionInfo);
                    }
                    consumptionInfo.setCost_attribution_list(costAttributionList);
                } else {
                    consumptionInfo.setCost_attribution_name(event.orderInfo().costAttribution().get().name());
                }
                consumptionInfo.setData_type(2);
                try {
                    LOGGER.info("用车Event：messageSaveContract = {}", messageSaveContract);
                    messageService.saveMessage(messageSaveContract);
                } catch (SaasException e) {
                    LOGGER.error(e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 大额异动
     *
     * @param event
     */
    private void sendLargeOver(TaxiOrderEvent event) {
        double priceLargeOver = event.personalPayPrice().isEmpty()?0:event.personalPayPrice().get().doubleValue();
        if (priceLargeOver == 0){
            if (TaxiScheduleType.TaxiPaid.getCode() != event.orderInfo().status().key()) {
                return;
            }
            // 获取大额异动配置
            IMessageSetupService messageSetupService = ApplicationContextUtils.getBean(IMessageSetupService.class);
            LargeOverService largeOverService = ApplicationContextUtils.getBean(LargeOverService.class);
            String companyId = event.userInfo().companyId();
            MessageSetup messageSetup = messageSetupService.queryCompanyMessageSetupWithDefault(companyId, SaasMessageConstant.ITEM_CODE_LARGE_OVER_NOTICE);
            if (messageSetup == null
                    || messageSetup.getIsChecked() == SaasMessageConstant.IS_CHECKED_FALSE
                    || messageSetup.getIntVal1() <= 0) {
                return;
            }
            // 判断订单是否大额
            BigDecimal price = new BigDecimal(event.orderInfo().totalPrice().toString());
            LOGGER.info("用车大额异动消息处理：订单金额：{}，大额设置：{}", price, messageSetup.getIntVal1());
            // 订单金额没有超过设置的大额
            if (BizCommonService.priceLowerThanConfig(price,messageSetup, CategoryTypeEnum.Taxi.getCode())) {
                LOGGER.info("用车大额异动消息处理：订单金额没有超过设置的大额，不提醒。");
                return;
            }
            // 2.保存大额异动通知
            List<MessageSetupReceiver> largeOverReceiverList = messageSetupService.queryMessageReceiverList(companyId, SaasMessageConstant.BUSI_CODE_LARGE_OVER);
            List<String> receiverIdList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(largeOverReceiverList)) {
                receiverIdList = largeOverReceiverList.stream().map(MessageSetupReceiver::getUserId).collect(toList());
            }
            BizCommonService bizCommonService = ApplicationContextUtils.getBean(BizCommonService.class);
            receiverIdList = bizCommonService.getReceiverList(messageSetup,event.userInfo().id(),receiverIdList);
            if(CollectionUtils.isNotEmpty(receiverIdList)){
                LOGGER.info("save taxi largeover message start..........");
                //标题
                String title = StrUtils.formatString(CoreLanguage.Common_Message_LargeOverTitle.getMessage(), event.userInfo().name(), NumberFormat.getCurrencyInstance(Locale.CHINA).format(price), BizType.Taxi.getName());
                String content = StrUtils.formatString(CoreLanguage.Common_Message_LargeOverContent.getMessage(), event.userInfo().name(), NumberFormat.getCurrencyInstance(Locale.CHINA).format(price), BizType.Taxi.getName());
                MessageWebContract largeOverContract = new MessageWebContract();
                largeOverContract.setMessageType(MessageType.Consume.getCode());
                largeOverContract.setMessageSubType(MessageWebSubType.LargeOver.getCode());
                largeOverContract.setCompanyId(companyId);
                largeOverContract.setReceiverIdList(receiverIdList);
                largeOverContract.setTitle(title);
                largeOverContract.setContent(content);
                largeOverContract.setBizeType(BizType.Taxi.getCode());
                largeOverContract.setBizOrder(event.orderInfo().id());
                IMessageWebService messageWebService = ApplicationContextUtils.getBean("messageWebService", IMessageWebService.class);
                try {
                    messageWebService.saveWebMessage(largeOverContract);
                    LOGGER.info("save taxi largeover message end..........");
                } catch (SaasException e) {
                    LOGGER.error(e.getMessage(), e);
                }
            }
            // 3.发送短信
            if (CollectionUtils.isNotEmpty(receiverIdList)) {
                // 短信接收者手机号
                Set<String> phoneSet = bizCommonService.getReceiverPhoneList(receiverIdList, companyId);
                if (CollectionUtils.isNotEmpty(phoneSet)) {
                    SmsContract smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_LARGE_OVER);
                    Map<String, Object> param = new LinkedHashMap<>();
                    param.put("var1", event.userInfo().name());    //下单人姓名
                    param.put("var2", NumberFormat.getCurrencyInstance(Locale.CHINA).format(price) + "元"); //订单总价
                    param.put("var3", BizType.Taxi.getName()); //业务类型
                    param.put("var4", TemplateConstant.CUSTOMER_SERVICE_PHONE); //客服电话
                    smsContract.setParam(param);
                    NoticeUtils.sendSms(smsContract);
                }
            }
            // 4.发送邮件
            List<MessageSetupEmail> largeOverEmailList = largeOverService.queryMessageEmailList(companyId, SaasMessageConstant.BUSI_CODE_LARGE_OVER);
            if (CollectionUtils.isNotEmpty(largeOverEmailList)) {
                Set<String> emailSet = largeOverEmailList.stream()
                        .map(email -> email.getEmail())
                        .filter(email -> StringUtils.isNotEmpty(email))
                        .collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(emailSet)) {
                    EmailContract emailContract = new EmailContract();
                    // 收件人
                    emailContract.setToList(emailSet);
                    // 邮件标题
                    String subject = MessageFormat.format(CoreLanguage.Order_Message_FbtConsumerReminder.getMessage(),
                            event.userInfo().deptName(),
                            event.userInfo().name(),
                            NumberFormat.getCurrencyInstance(Locale.CHINA).format(price),
                            BizType.Taxi.getName());
                    emailContract.setSubject(subject);
                    // html模板及内容
                    emailContract.setTemplateId(TemplateConstant.EMAIL_TEMP_LARGE_OVER);
                    Map<String, Object> dataMap = new LinkedHashMap<>();
                    dataMap.put("orderTime", event.orderInfo().createTime());
                    dataMap.put("userName", event.userInfo().name() + " " + event.userInfo().phone());
                    dataMap.put("userDept", event.userInfo().deptName());
                    dataMap.put("bizType", BizType.Taxi.getName());
                    dataMap.put("orderPrice", StrUtils.formatString(CoreLanguage.Rule_Value_ChineseUnit.getMessage(), NumberFormat.getCurrencyInstance(Locale.CHINA).format(price)));
                    // yyyy/mm/dd，车型
                    dataMap.put("orderDetail", MessageFormat.format("{0}，{1}",
                            DateTimeTool.fromDateToString(DateTimeTool.fromStringToDateTime(event.beginChargeTime())),
                            event.taxiType().value()));
                    emailContract.setData(dataMap);
                    // 发送邮件
                    NoticeUtils.sendEmail(emailContract);
                }
            }
        }else if (priceLargeOver > 0){
            if (TaxiScheduleType.TaxiPersonalWaitPay.getCode() != event.orderInfo().status().key()) {
                return;
            }
            // 获取大额异动配置
            IMessageSetupService messageSetupService = ApplicationContextUtils.getBean(IMessageSetupService.class);
            LargeOverService largeOverService = ApplicationContextUtils.getBean(LargeOverService.class);
            String companyId = event.userInfo().companyId();
            MessageSetup messageSetup = messageSetupService.queryCompanyMessageSetupWithDefault(companyId, SaasMessageConstant.ITEM_CODE_LARGE_OVER_NOTICE);
            if (messageSetup == null
                    || messageSetup.getIsChecked() == SaasMessageConstant.IS_CHECKED_FALSE
                    || messageSetup.getIntVal1() <= 0) {
                return;
            }
            // 判断订单是否大额
            BigDecimal price = new BigDecimal(event.orderInfo().totalPrice().toString());
            LOGGER.info("用车大额异动消息处理：订单金额：{}，大额设置：{}", price, messageSetup.getIntVal1());
            // 订单金额没有超过设置的大额
            if (price.compareTo(new BigDecimal(messageSetup.getIntVal1())) < 0) {
                LOGGER.info("用车大额异动消息处理：订单金额没有超过设置的大额，不提醒。");
                return;
            }
            // TODO 1.发送大额异动push
            // TODO 2.保存大额异动通知
            // 3.发送短信
            List<MessageSetupReceiver> largeOverReceiverList = messageSetupService.queryMessageReceiverList(companyId, SaasMessageConstant.BUSI_CODE_LARGE_OVER);
            if (CollectionUtils.isNotEmpty(largeOverReceiverList)) {
                // 短信接收者手机号
                IBaseOrganizationService organizationService = ApplicationContextUtils.getBean(IBaseOrganizationService.class);
                Set<String> phoneSet = largeOverReceiverList.stream()
                        .map(receiver -> organizationService.getEmployee(receiver.getUserId(), companyId))
                        .filter(employee -> employee != null)
                        .map(employee -> employee.getPhone_num())
                        .collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(phoneSet)) {
                    SmsContract smsContract = new SmsContract(phoneSet, TemplateConstant.SMS_TEMP_LARGE_OVER);
                    Map<String, Object> param = new LinkedHashMap<>();
                    param.put("var1", event.userInfo().name());    //下单人姓名
                    param.put("var2", NumberFormat.getCurrencyInstance(Locale.CHINA).format(price) + "元"); //订单总价
                    param.put("var3", BizType.Taxi.getName()); //业务类型
                    param.put("var4", TemplateConstant.CUSTOMER_SERVICE_PHONE); //客服电话
                    smsContract.setParam(param);
                    NoticeUtils.sendSms(smsContract);
                }
            }
            // 4.发送邮件
            List<MessageSetupEmail> largeOverEmailList = largeOverService.queryMessageEmailList(companyId, SaasMessageConstant.BUSI_CODE_LARGE_OVER);
            if (CollectionUtils.isNotEmpty(largeOverEmailList)) {
                Set<String> emailSet = largeOverEmailList.stream()
                        .map(email -> email.getEmail())
                        .filter(email -> StringUtils.isNotEmpty(email))
                        .collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(emailSet)) {
                    EmailContract emailContract = new EmailContract();
                    // 收件人
                    emailContract.setToList(emailSet);
                    // 邮件标题
                    String subject = MessageFormat.format(CoreLanguage.Order_Message_FbtConsumerReminder.getMessage(),
                            event.userInfo().deptName(),
                            event.userInfo().name(),
                            NumberFormat.getCurrencyInstance(Locale.CHINA).format(price),
                            BizType.Taxi.getName());
                    emailContract.setSubject(subject);
                    // html模板及内容
                    emailContract.setTemplateId(TemplateConstant.EMAIL_TEMP_LARGE_OVER);
                    Map<String, Object> dataMap = new LinkedHashMap<>();
                    dataMap.put("orderTime", event.orderInfo().createTime());
                    dataMap.put("userName", event.userInfo().name() + " " + event.userInfo().phone());
                    dataMap.put("userDept", event.userInfo().deptName());
                    dataMap.put("bizType", BizType.Taxi.getName());
                    dataMap.put("orderPrice", StrUtils.formatString(CoreLanguage.Rule_Value_ChineseUnit.getMessage(), NumberFormat.getCurrencyInstance(Locale.CHINA).format(price)));
                    // yyyy/mm/dd，车型
                    dataMap.put("orderDetail", MessageFormat.format("{0}，{1}",
                            DateTimeTool.fromDateToString(DateTimeTool.fromStringToDateTime(event.beginChargeTime())),
                            event.taxiType().value()));
                    emailContract.setData(dataMap);
                    // 发送邮件
                    NoticeUtils.sendEmail(emailContract);
                }
            }
        }
    }

    /**
     * 预算报警
     *
     * @param event
     */
    public void sendWarnPercentInfo(TaxiOrderEvent event) {
        double priceWarnPercent = event.personalPayPrice().isEmpty()?0:event.personalPayPrice().get().doubleValue();
        if (priceWarnPercent == 0) {
            if (TaxiScheduleType.TaxiPaid.getCode() != event.orderInfo().status().key()) {
                return;
            }
            IBudgetService budgetService = ApplicationContextUtils.getBean(IBudgetService.class);
            String companyAuthEmail = event.userInfo().companyAuthEmail().get();
            String companyName = event.userInfo().companyName();
            String companyId = event.userInfo().companyId();
            String employeeId = event.userInfo().id();
            Integer costAttributionScope = event.orderInfo().costOpt().isEmpty() ? null : ObjUtils.toInteger(event.orderInfo().costOpt().get());
            Integer budgetCostAttrType = event.orderInfo().budgetOpt().isEmpty() ? null : ObjUtils.toInteger(event.orderInfo().budgetOpt().get());
            // 兼容老版本单费用归属
            if (!event.orderInfo().costAttributionList().isEmpty()) {
                List<OrderCostAttribution> orderCostAttributionList = JavaConversions.seqAsJavaList(event.orderInfo().costAttributionList().get());
                List<CostAttributionInfo> costAttributionList = Lists.newArrayList();
                for (OrderCostAttribution orderCostAttribution : orderCostAttributionList) {
                    CostAttributionInfo costAttributionInfo = new CostAttributionInfo();
                    costAttributionInfo.setCost_attribution_category(ObjUtils.toInteger(orderCostAttribution.category().get()));
                    costAttributionInfo.setCost_attribution_id(orderCostAttribution.id());
                    costAttributionInfo.setCost_attribution_name(orderCostAttribution.name());
                    costAttributionList.add(costAttributionInfo);
                }
                budgetService.sendWarnPercentInfo(companyAuthEmail, companyId, companyName, employeeId, costAttributionList, OrderCategory.Taxi, costAttributionScope, budgetCostAttrType);
            } else {
                String orderId = event.orderInfo().id();
                String costAttributionName = event.orderInfo().costAttribution().get().name();
                budgetService.sendWarnPercentInfo(companyAuthEmail, companyId, companyName, employeeId, orderId, costAttributionName, OrderCategory.Taxi);
            }
        } else if (priceWarnPercent > 0) {
            if (TaxiScheduleType.TaxiPersonalWaitPay.getCode() != event.orderInfo().status().key()) {
                return;
            }
            IBudgetService budgetService = ApplicationContextUtils.getBean(IBudgetService.class);
            String companyAuthEmail = event.userInfo().companyAuthEmail().get();
            String companyName = event.userInfo().companyName();
            String companyId = event.userInfo().companyId();
            String employeeId = event.userInfo().id();
            String orderId = event.orderInfo().id();
            Integer costAttributionScope = event.orderInfo().costOpt().isEmpty() ? null : ObjUtils.toInteger(event.orderInfo().costOpt().get());
            Integer budgetCostAttrType = event.orderInfo().budgetOpt().isEmpty() ? null : ObjUtils.toInteger(event.orderInfo().budgetOpt().get());
            // 兼容老版本单费用归属
            if (!event.orderInfo().costAttributionList().isEmpty()) {
                List<OrderCostAttribution> orderCostAttributionList = JavaConversions.seqAsJavaList(event.orderInfo().costAttributionList().get());
                List<CostAttributionInfo> costAttributionList = Lists.newArrayList();
                for (OrderCostAttribution orderCostAttribution : orderCostAttributionList) {
                    CostAttributionInfo costAttributionInfo = new CostAttributionInfo();
                    costAttributionInfo.setCost_attribution_category(ObjUtils.toInteger(orderCostAttribution.category().get()));
                    costAttributionInfo.setCost_attribution_id(orderCostAttribution.id());
                    costAttributionInfo.setCost_attribution_name(orderCostAttribution.name());
                    costAttributionList.add(costAttributionInfo);
                }
                budgetService.sendWarnPercentInfo(companyAuthEmail, companyId, companyName, employeeId, costAttributionList, OrderCategory.Taxi, costAttributionScope, budgetCostAttrType);
            } else {
                String costAttributionName = event.orderInfo().costAttribution().get().name();
                budgetService.sendWarnPercentInfo(companyAuthEmail, companyId, companyName, employeeId, orderId, costAttributionName, OrderCategory.Taxi);
            }
        }
    }

    /**
     * 订单通知人
     * @param event
     */
    private void sendOrderMessageInfo(TaxiOrderEvent event) {
        double priceConsume = event.personalPayPrice().isEmpty()?0:event.personalPayPrice().get().doubleValue();
        int totalNum = event.orderInfo().totalPrice().compare(scala.math.BigDecimal.valueOf(0));
        if (event.orderInfo().notifierInfo().isEmpty()) {
            LOGGER.info("无订单通知人" + event.toString());
            return;
        }
        if (priceConsume == 0){
            //无员工待支付-700
            if (TaxiScheduleType.TaxiPaid.getCode() != event.orderInfo().status().key()) {
                return;
            }
            IMessageService messageService = ApplicationContextUtils.getBean("messageService", IMessageService.class);
            List<UserInfo> userInfoList = JavaConversions.seqAsJavaList(event.orderInfo().notifierInfo().get());
            for (UserInfo userInfo : userInfoList) {
                MessageSaveContract messageSaveContract = new MessageSaveContract();
                messageSaveContract.setMessage_type(MessageType.System.getCode());
                messageSaveContract.setTitle(CoreLanguage.Common_Message_CompanyUsedCarTitle.getMessage());
                StringBuilder content = new StringBuilder()
                        .append(StrUtils.formatString(CoreLanguage.Common_Message_OrderCarTimeOrPlace.getMessage()
                                , event.userInfo().name()
                                , event.departureName()
                                , event.arrivalName()));
                messageSaveContract.setContent(content.toString());
                messageSaveContract.setSender_type(SenderType.Person.getCode());
                messageSaveContract.setSender(event.userInfo().id());
                messageSaveContract.setReceiver(userInfo.id());
                messageSaveContract.setBiz_order(event.orderInfo().id());
                messageSaveContract.setCompany_id(event.userInfo().companyId());
                ConsumptionInfo consumptionInfo = new ConsumptionInfo();
                consumptionInfo.setConsumption_type(BizType.Taxi.getCode());
                consumptionInfo.setCreator_msg(event.userInfo().name());
                consumptionInfo.setPrice_msg(StrUtils.formatString(CoreLanguage.Rule_Value_ChineseUnit.getMessage(),NumberFormat.getCurrencyInstance(Locale.CHINA).format(event.orderInfo().totalPrice())));
                consumptionInfo.setConsume_time(event.finishChargeTime());
                ConsumptionInfo.TaxiInfo taxiInfo = new ConsumptionInfo.TaxiInfo();
                taxiInfo.setTravel_msg(event.departureName() + "到" + event.arrivalName());
                taxiInfo.setPassenger_msg(event.passengerInfo().name());
                taxiInfo.setSchedule_type(event.serverType().key());
                consumptionInfo.setTaxi_info(taxiInfo);
                messageSaveContract.setConsumption_info(consumptionInfo);
                if (event.orderInfo().remark().isDefined()) {
                    consumptionInfo.setRemark(event.orderInfo().remark().get());
                }
                // 兼容老版本单费用归属
                if (!event.orderInfo().costAttributionList().isEmpty()) {
                    List<OrderCostAttribution> orderCostAttributionList = JavaConversions.seqAsJavaList(event.orderInfo().costAttributionList().get());
                    List<CostAttributionInfo> costAttributionList = Lists.newArrayList();
                    for (OrderCostAttribution orderCostAttribution : orderCostAttributionList) {
                        CostAttributionInfo costAttributionInfo = new CostAttributionInfo();
                        costAttributionInfo.setCost_attribution_category(ObjUtils.toInteger(orderCostAttribution.category().get()));
                        costAttributionInfo.setCost_attribution_id(orderCostAttribution.id());
                        costAttributionInfo.setCost_attribution_name(orderCostAttribution.name());
                        costAttributionList.add(costAttributionInfo);
                    }
                    consumptionInfo.setCost_attribution_list(costAttributionList);
                } else {
                    consumptionInfo.setCost_attribution_name(event.orderInfo().costAttribution().get().name());
                }
                consumptionInfo.setData_type(2);
                try {
                    LOGGER.info("用车Event：messageSaveContract = {}", messageSaveContract);
                    messageService.saveMessage(messageSaveContract);
                } catch (SaasException e) {
                    LOGGER.error(e.getMessage(), e);
                }
            }
        }else if (priceConsume > 0 && totalNum > 0){
            //有员工待支付-690
            if (TaxiScheduleType.TaxiPersonalWaitPay.getCode() != event.orderInfo().status().key()) {
                return;
            }
            IMessageService messageService = ApplicationContextUtils.getBean("messageService", IMessageService.class);
            List<UserInfo> userInfoList = JavaConversions.seqAsJavaList(event.orderInfo().notifierInfo().get());
            for (UserInfo userInfo : userInfoList) {
                MessageSaveContract messageSaveContract = new MessageSaveContract();
                messageSaveContract.setMessage_type(MessageType.System.getCode());
                messageSaveContract.setTitle(CoreLanguage.Common_Message_CompanyUsedCarTitle.getMessage());
                StringBuilder content = new StringBuilder()
                        .append(StrUtils.formatString(CoreLanguage.Common_Message_OrderCarTimeOrPlace.getMessage()
                                , event.userInfo().name()
                                , event.departureName()
                                , event.arrivalName()));
                messageSaveContract.setContent(content.toString());
                messageSaveContract.setSender_type(SenderType.Person.getCode());
                messageSaveContract.setSender(event.userInfo().id());
                messageSaveContract.setReceiver(userInfo.id());
                messageSaveContract.setBiz_order(event.orderInfo().id());
                messageSaveContract.setCompany_id(event.userInfo().companyId());
                ConsumptionInfo consumptionInfo = new ConsumptionInfo();
                consumptionInfo.setConsumption_type(BizType.Taxi.getCode());
                consumptionInfo.setCreator_msg(event.userInfo().name());
                consumptionInfo.setPrice_msg(StrUtils.formatString(CoreLanguage.Rule_Value_ChineseUnit.getMessage(),NumberFormat.getCurrencyInstance(Locale.CHINA).format(event.orderInfo().totalPrice())));
                consumptionInfo.setConsume_time(event.finishChargeTime());
                ConsumptionInfo.TaxiInfo taxiInfo = new ConsumptionInfo.TaxiInfo();
                taxiInfo.setTravel_msg(event.departureName() + "到" + event.arrivalName());
                taxiInfo.setPassenger_msg(event.passengerInfo().name());
                taxiInfo.setSchedule_type(event.serverType().key());
                consumptionInfo.setTaxi_info(taxiInfo);
                messageSaveContract.setConsumption_info(consumptionInfo);
                if (event.orderInfo().remark().isDefined()) {
                    consumptionInfo.setRemark(event.orderInfo().remark().get());
                }
                List<OrderCostAttribution> orderCostAttributionList = JavaConversions.seqAsJavaList(event.orderInfo().costAttributionList().get());
                // 兼容老版本单费用归属
                if (ObjUtils.isNotEmpty(orderCostAttributionList)) {
                    List<CostAttributionInfo> costAttributionList = Lists.newArrayList();
                    for (OrderCostAttribution orderCostAttribution : orderCostAttributionList) {
                        CostAttributionInfo costAttributionInfo = new CostAttributionInfo();
                        costAttributionInfo.setCost_attribution_category(ObjUtils.toInteger(orderCostAttribution.category().get()));
                        costAttributionInfo.setCost_attribution_id(orderCostAttribution.id());
                        costAttributionInfo.setCost_attribution_name(orderCostAttribution.name());
                        costAttributionList.add(costAttributionInfo);
                    }
                    consumptionInfo.setCost_attribution_list(costAttributionList);
                } else {
                    consumptionInfo.setCost_attribution_name(event.orderInfo().costAttribution().get().name());
                }
                consumptionInfo.setData_type(2);
                try {
                    LOGGER.info("用车Event：messageSaveContract = {}", messageSaveContract);
                    messageService.saveMessage(messageSaveContract);
                } catch (SaasException e) {
                    LOGGER.error(e.getMessage(), e);
                }
            }
        }
    }

}
