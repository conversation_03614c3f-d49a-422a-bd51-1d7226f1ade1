package com.fenbeitong.saas.core.contract.reason;

import java.util.List;

/**
 * 事由配置列表返回结果
 *
 * <AUTHOR>
 * @date 2019/12/10
 */
public class CustomReasonListResult {

    private List<Category> category_list;

    public List<Category> getCategory_list() {
        return category_list;
    }

    public void setCategory_list(List<Category> category_list) {
        this.category_list = category_list;
    }

    public static class Category {
        /**
         * category_type : 1
         * sub_type : 1
         * category_name : 订单管控配置
         * config_list : [{"config_type":101,"config_name":"国内机票订单配置","config_desc":"适用于国内机票订单配置管控信息","reason_title":"事由内容","reason_items":["商务洽谈","开发市场","学习培训","其他"]},{"config_type":102,"config_name":"国际机票订单配置","config_desc":"适用于国际机票订单配置管控信息","reason_title":"事由内容","reason_items":["商务洽谈","开发市场","学习培训","其他"]}]
         */

        private Integer category_type;
        private Integer sub_type;
        private String category_name;
        private List<Config> config_list;

        public Integer getCategory_type() {
            return category_type;
        }

        public void setCategory_type(Integer category_type) {
            this.category_type = category_type;
        }

        public Integer getSub_type() {
            return sub_type;
        }

        public void setSub_type(Integer sub_type) {
            this.sub_type = sub_type;
        }

        public String getCategory_name() {
            return category_name;
        }

        public void setCategory_name(String category_name) {
            this.category_name = category_name;
        }

        public List<Config> getConfig_list() {
            return config_list;
        }

        public void setConfig_list(List<Config> config_list) {
            this.config_list = config_list;
        }
    }

    public static class Config {
        /**
         * config_type : 101
         * config_name : 国内机票订单配置
         * reason_title : 事由内容
         * reason_items : ["商务洽谈","开发市场","学习培训","其他"]
         */

        private Integer config_type;
        private String config_name;
        private String reason_title;
        private List<String> reason_items;
        private String field_title;
        private String field_comment;

        public Integer getConfig_type() {
            return config_type;
        }

        public void setConfig_type(Integer config_type) {
            this.config_type = config_type;
        }

        public String getConfig_name() {
            return config_name;
        }

        public void setConfig_name(String config_name) {
            this.config_name = config_name;
        }

        public String getReason_title() {
            return reason_title;
        }

        public void setReason_title(String reason_title) {
            this.reason_title = reason_title;
        }

        public List<String> getReason_items() {
            return reason_items;
        }

        public void setReason_items(List<String> reason_items) {
            this.reason_items = reason_items;
        }

        public String getField_title() {
            return field_title;
        }

        public void setField_title(String field_title) {
            this.field_title = field_title;
        }

        public String getField_comment() {
            return field_comment;
        }

        public void setField_comment(String field_comment) {
            this.field_comment = field_comment;
        }
    }
}
