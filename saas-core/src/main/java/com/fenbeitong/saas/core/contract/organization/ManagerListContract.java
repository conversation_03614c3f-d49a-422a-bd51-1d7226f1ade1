package com.fenbeitong.saas.core.contract.organization;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/5/9.
 * 存储同部门下的主管列表
 */
public class ManagerListContract {

    private List<ManagerContract> managerContractList;

    public ManagerListContract(List<ManagerContract> managerContractList) {
        this.managerContractList = managerContractList;
    }

    public ManagerListContract() {
    }

    public List<ManagerContract> getManagerContractList() {
        return managerContractList;
    }

    public void setManagerContractList(List<ManagerContract> managerContractList) {
        this.managerContractList = managerContractList;
    }
}
