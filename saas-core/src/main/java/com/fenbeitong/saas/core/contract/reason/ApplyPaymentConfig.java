package com.fenbeitong.saas.core.contract.reason;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/10/10
 */
@Data
public class ApplyPaymentConfig {
    /**
     * 合同信息 0：选填，1：必填，2：不显示
     */
    private Integer payment_contract;
    /**
     * 合同信息-限制付款金额与合同金额一致 0：不限制，1：限制
     */
    private Integer payment_contract_limit;
    /**
     * 凭证信息  0：选填，1：必填，2：不显示
     */
    private Integer payment_proof;
    /**
     * 凭证信息-限制付款金额与凭证金额一致  0：不限制，1：限制
     */
    private Integer payment_proof_limit;
    /**
     * 已开发票  0：未勾选，1：勾选
     */
    private Integer payment_has_invoice;
    /**
     * 已开发票-限制付款金额与凭证金额一致  0：不限制，1：限制
     */
    private Integer payment_has_invoice_limit;
    /**
     * 待开发票  0：未勾选，1：勾选
     */
    private Integer payment_need_invoice;
    /**
     * 无发票  0：未勾选，1：勾选
     */
    private Integer payment_no_invoice;

}
