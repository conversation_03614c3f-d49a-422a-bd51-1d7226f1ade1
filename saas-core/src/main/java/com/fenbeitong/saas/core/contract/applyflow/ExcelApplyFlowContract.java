package com.fenbeitong.saas.core.contract.applyflow;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/11/20.
 */
public class ExcelApplyFlowContract {
    private String companySettingType; //审批流类型
    private String flowName;    //审批流名称
    private Integer applyItemType1; //审批人类型
    private String applyItemName1; //审批人名称（角色/手机号）
    private Integer applyItemType2;
    private String applyItemName2;
    private Integer applyItemType3;
    private String applyItemName3;
    private Integer applyItemType4;
    private String applyItemName4;
    private Integer applyItemType5;
    private String applyItemName5;

    private String usedByDepartment; //应用部门
    private String usedByProject; //应用项目

    private Integer ccItemType1; //抄送人类型
    private String ccItemName1;  //抄送人名称（角色/手机号）
    private Integer ccItemType2;
    private String ccItemName2;
    private Integer ccItemType3;
    private String ccItemName3;
    private Integer ccItemType4;
    private String ccItemName4;
    private Integer ccItemType5;
    private String ccItemName5;

    private String roleApproveType;//角色审批类型
    private String ccNoticeType;//抄送通知类型

    public String getCompanySettingType() {
        return companySettingType;
    }

    public void setCompanySettingType(String companySettingType) {
        this.companySettingType = companySettingType;
    }

    public String getFlowName() {
        return flowName;
    }

    public void setFlowName(String flowName) {
        this.flowName = flowName;
    }

    public Integer getApplyItemType1() {
        return applyItemType1;
    }

    public void setApplyItemType1(Integer applyItemType1) {
        this.applyItemType1 = applyItemType1;
    }

    public String getApplyItemName1() {
        return applyItemName1;
    }

    public void setApplyItemName1(String applyItemName1) {
        this.applyItemName1 = applyItemName1;
    }

    public Integer getApplyItemType2() {
        return applyItemType2;
    }

    public void setApplyItemType2(Integer applyItemType2) {
        this.applyItemType2 = applyItemType2;
    }

    public String getApplyItemName2() {
        return applyItemName2;
    }

    public void setApplyItemName2(String applyItemName2) {
        this.applyItemName2 = applyItemName2;
    }

    public Integer getApplyItemType3() {
        return applyItemType3;
    }

    public void setApplyItemType3(Integer applyItemType3) {
        this.applyItemType3 = applyItemType3;
    }

    public String getApplyItemName3() {
        return applyItemName3;
    }

    public void setApplyItemName3(String applyItemName3) {
        this.applyItemName3 = applyItemName3;
    }

    public Integer getApplyItemType4() {
        return applyItemType4;
    }

    public void setApplyItemType4(Integer applyItemType4) {
        this.applyItemType4 = applyItemType4;
    }

    public String getApplyItemName4() {
        return applyItemName4;
    }

    public void setApplyItemName4(String applyItemName4) {
        this.applyItemName4 = applyItemName4;
    }

    public Integer getApplyItemType5() {
        return applyItemType5;
    }

    public void setApplyItemType5(Integer applyItemType5) {
        this.applyItemType5 = applyItemType5;
    }

    public String getApplyItemName5() {
        return applyItemName5;
    }

    public void setApplyItemName5(String applyItemName5) {
        this.applyItemName5 = applyItemName5;
    }

    public String getUsedByDepartment() {
        return usedByDepartment;
    }

    public void setUsedByDepartment(String usedByDepartment) {
        this.usedByDepartment = usedByDepartment;
    }

    public String getUsedByProject() {
        return usedByProject;
    }

    public void setUsedByProject(String usedByProject) {
        this.usedByProject = usedByProject;
    }

    public Integer getCcItemType1() {
        return ccItemType1;
    }

    public void setCcItemType1(Integer ccItemType1) {
        this.ccItemType1 = ccItemType1;
    }

    public String getCcItemName1() {
        return ccItemName1;
    }

    public void setCcItemName1(String ccItemName1) {
        this.ccItemName1 = ccItemName1;
    }

    public Integer getCcItemType2() {
        return ccItemType2;
    }

    public void setCcItemType2(Integer ccItemType2) {
        this.ccItemType2 = ccItemType2;
    }

    public String getCcItemName2() {
        return ccItemName2;
    }

    public void setCcItemName2(String ccItemName2) {
        this.ccItemName2 = ccItemName2;
    }

    public Integer getCcItemType3() {
        return ccItemType3;
    }

    public void setCcItemType3(Integer ccItemType3) {
        this.ccItemType3 = ccItemType3;
    }

    public String getCcItemName3() {
        return ccItemName3;
    }

    public void setCcItemName3(String ccItemName3) {
        this.ccItemName3 = ccItemName3;
    }

    public Integer getCcItemType4() {
        return ccItemType4;
    }

    public void setCcItemType4(Integer ccItemType4) {
        this.ccItemType4 = ccItemType4;
    }

    public String getCcItemName4() {
        return ccItemName4;
    }

    public void setCcItemName4(String ccItemName4) {
        this.ccItemName4 = ccItemName4;
    }

    public Integer getCcItemType5() {
        return ccItemType5;
    }

    public void setCcItemType5(Integer ccItemType5) {
        this.ccItemType5 = ccItemType5;
    }

    public String getCcItemName5() {
        return ccItemName5;
    }

    public void setCcItemName5(String ccItemName5) {
        this.ccItemName5 = ccItemName5;
    }

    public String getRoleApproveType() {
        return roleApproveType;
    }

    public void setRoleApproveType(String roleApproveType) {
        this.roleApproveType = roleApproveType;
    }

    public String getCcNoticeType() {
        return ccNoticeType;
    }

    public void setCcNoticeType(String ccNoticeType) {
        this.ccNoticeType = ccNoticeType;
    }
}
