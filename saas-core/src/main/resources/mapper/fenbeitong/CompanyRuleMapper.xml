<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.CompanyRuleMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.CompanyRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="company_id" jdbcType="CHAR" property="companyId" />
    <result column="car_rule" jdbcType="INTEGER" property="carRule" />
    <result column="car_money_flag" jdbcType="BIT" property="carMoneyFlag" />
    <result column="air_rule" jdbcType="INTEGER" property="airRule" />
    <result column="air_money_flag" jdbcType="BIT" property="airMoneyFlag" />
    <result column="hotel_rule" jdbcType="INTEGER" property="hotelRule" />
    <result column="hotel_money_flag" jdbcType="BIT" property="hotelMoneyFlag" />
    <result column="train_rule" jdbcType="INTEGER" property="trainRule" />
    <result column="train_money_flag" jdbcType="BIT" property="trainMoneyFlag" />
    <result column="mall_rule" jdbcType="INTEGER" property="mallRule" />
    <result column="mall_money_flag" jdbcType="BIT" property="mallMoneyFlag" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="dinner_rule" jdbcType="INTEGER" property="dinnerRule" />
    <result column="dinner_money_flag" jdbcType="BIT" property="dinnerMoneyFlag" />
    <result column="hotel_rule_price_type" jdbcType="INTEGER" property="hotelRulePriceType" />
    <result column="takeaway_rule" jdbcType="SMALLINT" property="takeawayRule" />
    <result column="takeaway_money_flag" jdbcType="BIT" property="takeawayMoneyFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    company_id, car_rule, car_money_flag, air_rule, air_money_flag, hotel_rule, hotel_money_flag, 
    train_rule, train_money_flag, mall_rule, mall_money_flag, modify_time, dinner_rule, 
    dinner_money_flag, hotel_rule_price_type, takeaway_rule, takeaway_money_flag
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.CompanyRuleExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from company_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${limit} offset ${offset}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from company_rule
    where company_id = #{companyId,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from company_rule
    where company_id = #{companyId,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.CompanyRuleExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from company_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.fenbeitong.CompanyRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into company_rule (company_id, car_rule, car_money_flag, 
      air_rule, air_money_flag, hotel_rule, 
      hotel_money_flag, train_rule, train_money_flag, 
      mall_rule, mall_money_flag, modify_time, 
      dinner_rule, dinner_money_flag, hotel_rule_price_type, 
      takeaway_rule, takeaway_money_flag)
    values (#{companyId,jdbcType=CHAR}, #{carRule,jdbcType=INTEGER}, #{carMoneyFlag,jdbcType=BIT}, 
      #{airRule,jdbcType=INTEGER}, #{airMoneyFlag,jdbcType=BIT}, #{hotelRule,jdbcType=INTEGER}, 
      #{hotelMoneyFlag,jdbcType=BIT}, #{trainRule,jdbcType=INTEGER}, #{trainMoneyFlag,jdbcType=BIT}, 
      #{mallRule,jdbcType=INTEGER}, #{mallMoneyFlag,jdbcType=BIT}, #{modifyTime,jdbcType=TIMESTAMP}, 
      #{dinnerRule,jdbcType=INTEGER}, #{dinnerMoneyFlag,jdbcType=BIT}, #{hotelRulePriceType,jdbcType=INTEGER}, 
      #{takeawayRule,jdbcType=SMALLINT}, #{takeawayMoneyFlag,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.CompanyRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into company_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="carRule != null">
        car_rule,
      </if>
      <if test="carMoneyFlag != null">
        car_money_flag,
      </if>
      <if test="airRule != null">
        air_rule,
      </if>
      <if test="airMoneyFlag != null">
        air_money_flag,
      </if>
      <if test="hotelRule != null">
        hotel_rule,
      </if>
      <if test="hotelMoneyFlag != null">
        hotel_money_flag,
      </if>
      <if test="trainRule != null">
        train_rule,
      </if>
      <if test="trainMoneyFlag != null">
        train_money_flag,
      </if>
      <if test="mallRule != null">
        mall_rule,
      </if>
      <if test="mallMoneyFlag != null">
        mall_money_flag,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="dinnerRule != null">
        dinner_rule,
      </if>
      <if test="dinnerMoneyFlag != null">
        dinner_money_flag,
      </if>
      <if test="hotelRulePriceType != null">
        hotel_rule_price_type,
      </if>
      <if test="takeawayRule != null">
        takeaway_rule,
      </if>
      <if test="takeawayMoneyFlag != null">
        takeaway_money_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=CHAR},
      </if>
      <if test="carRule != null">
        #{carRule,jdbcType=INTEGER},
      </if>
      <if test="carMoneyFlag != null">
        #{carMoneyFlag,jdbcType=BIT},
      </if>
      <if test="airRule != null">
        #{airRule,jdbcType=INTEGER},
      </if>
      <if test="airMoneyFlag != null">
        #{airMoneyFlag,jdbcType=BIT},
      </if>
      <if test="hotelRule != null">
        #{hotelRule,jdbcType=INTEGER},
      </if>
      <if test="hotelMoneyFlag != null">
        #{hotelMoneyFlag,jdbcType=BIT},
      </if>
      <if test="trainRule != null">
        #{trainRule,jdbcType=INTEGER},
      </if>
      <if test="trainMoneyFlag != null">
        #{trainMoneyFlag,jdbcType=BIT},
      </if>
      <if test="mallRule != null">
        #{mallRule,jdbcType=INTEGER},
      </if>
      <if test="mallMoneyFlag != null">
        #{mallMoneyFlag,jdbcType=BIT},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dinnerRule != null">
        #{dinnerRule,jdbcType=INTEGER},
      </if>
      <if test="dinnerMoneyFlag != null">
        #{dinnerMoneyFlag,jdbcType=BIT},
      </if>
      <if test="hotelRulePriceType != null">
        #{hotelRulePriceType,jdbcType=INTEGER},
      </if>
      <if test="takeawayRule != null">
        #{takeawayRule,jdbcType=SMALLINT},
      </if>
      <if test="takeawayMoneyFlag != null">
        #{takeawayMoneyFlag,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.CompanyRuleExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from company_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update company_rule
    <set>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=CHAR},
      </if>
      <if test="record.carRule != null">
        car_rule = #{record.carRule,jdbcType=INTEGER},
      </if>
      <if test="record.carMoneyFlag != null">
        car_money_flag = #{record.carMoneyFlag,jdbcType=BIT},
      </if>
      <if test="record.airRule != null">
        air_rule = #{record.airRule,jdbcType=INTEGER},
      </if>
      <if test="record.airMoneyFlag != null">
        air_money_flag = #{record.airMoneyFlag,jdbcType=BIT},
      </if>
      <if test="record.hotelRule != null">
        hotel_rule = #{record.hotelRule,jdbcType=INTEGER},
      </if>
      <if test="record.hotelMoneyFlag != null">
        hotel_money_flag = #{record.hotelMoneyFlag,jdbcType=BIT},
      </if>
      <if test="record.trainRule != null">
        train_rule = #{record.trainRule,jdbcType=INTEGER},
      </if>
      <if test="record.trainMoneyFlag != null">
        train_money_flag = #{record.trainMoneyFlag,jdbcType=BIT},
      </if>
      <if test="record.mallRule != null">
        mall_rule = #{record.mallRule,jdbcType=INTEGER},
      </if>
      <if test="record.mallMoneyFlag != null">
        mall_money_flag = #{record.mallMoneyFlag,jdbcType=BIT},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dinnerRule != null">
        dinner_rule = #{record.dinnerRule,jdbcType=INTEGER},
      </if>
      <if test="record.dinnerMoneyFlag != null">
        dinner_money_flag = #{record.dinnerMoneyFlag,jdbcType=BIT},
      </if>
      <if test="record.hotelRulePriceType != null">
        hotel_rule_price_type = #{record.hotelRulePriceType,jdbcType=INTEGER},
      </if>
      <if test="record.takeawayRule != null">
        takeaway_rule = #{record.takeawayRule,jdbcType=SMALLINT},
      </if>
      <if test="record.takeawayMoneyFlag != null">
        takeaway_money_flag = #{record.takeawayMoneyFlag,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update company_rule
    set company_id = #{record.companyId,jdbcType=CHAR},
      car_rule = #{record.carRule,jdbcType=INTEGER},
      car_money_flag = #{record.carMoneyFlag,jdbcType=BIT},
      air_rule = #{record.airRule,jdbcType=INTEGER},
      air_money_flag = #{record.airMoneyFlag,jdbcType=BIT},
      hotel_rule = #{record.hotelRule,jdbcType=INTEGER},
      hotel_money_flag = #{record.hotelMoneyFlag,jdbcType=BIT},
      train_rule = #{record.trainRule,jdbcType=INTEGER},
      train_money_flag = #{record.trainMoneyFlag,jdbcType=BIT},
      mall_rule = #{record.mallRule,jdbcType=INTEGER},
      mall_money_flag = #{record.mallMoneyFlag,jdbcType=BIT},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      dinner_rule = #{record.dinnerRule,jdbcType=INTEGER},
      dinner_money_flag = #{record.dinnerMoneyFlag,jdbcType=BIT},
      hotel_rule_price_type = #{record.hotelRulePriceType,jdbcType=INTEGER},
      takeaway_rule = #{record.takeawayRule,jdbcType=SMALLINT},
      takeaway_money_flag = #{record.takeawayMoneyFlag,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.CompanyRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update company_rule
    <set>
      <if test="carRule != null">
        car_rule = #{carRule,jdbcType=INTEGER},
      </if>
      <if test="carMoneyFlag != null">
        car_money_flag = #{carMoneyFlag,jdbcType=BIT},
      </if>
      <if test="airRule != null">
        air_rule = #{airRule,jdbcType=INTEGER},
      </if>
      <if test="airMoneyFlag != null">
        air_money_flag = #{airMoneyFlag,jdbcType=BIT},
      </if>
      <if test="hotelRule != null">
        hotel_rule = #{hotelRule,jdbcType=INTEGER},
      </if>
      <if test="hotelMoneyFlag != null">
        hotel_money_flag = #{hotelMoneyFlag,jdbcType=BIT},
      </if>
      <if test="trainRule != null">
        train_rule = #{trainRule,jdbcType=INTEGER},
      </if>
      <if test="trainMoneyFlag != null">
        train_money_flag = #{trainMoneyFlag,jdbcType=BIT},
      </if>
      <if test="mallRule != null">
        mall_rule = #{mallRule,jdbcType=INTEGER},
      </if>
      <if test="mallMoneyFlag != null">
        mall_money_flag = #{mallMoneyFlag,jdbcType=BIT},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dinnerRule != null">
        dinner_rule = #{dinnerRule,jdbcType=INTEGER},
      </if>
      <if test="dinnerMoneyFlag != null">
        dinner_money_flag = #{dinnerMoneyFlag,jdbcType=BIT},
      </if>
      <if test="hotelRulePriceType != null">
        hotel_rule_price_type = #{hotelRulePriceType,jdbcType=INTEGER},
      </if>
      <if test="takeawayRule != null">
        takeaway_rule = #{takeawayRule,jdbcType=SMALLINT},
      </if>
      <if test="takeawayMoneyFlag != null">
        takeaway_money_flag = #{takeawayMoneyFlag,jdbcType=BIT},
      </if>
    </set>
    where company_id = #{companyId,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.fenbeitong.CompanyRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update company_rule
    set car_rule = #{carRule,jdbcType=INTEGER},
      car_money_flag = #{carMoneyFlag,jdbcType=BIT},
      air_rule = #{airRule,jdbcType=INTEGER},
      air_money_flag = #{airMoneyFlag,jdbcType=BIT},
      hotel_rule = #{hotelRule,jdbcType=INTEGER},
      hotel_money_flag = #{hotelMoneyFlag,jdbcType=BIT},
      train_rule = #{trainRule,jdbcType=INTEGER},
      train_money_flag = #{trainMoneyFlag,jdbcType=BIT},
      mall_rule = #{mallRule,jdbcType=INTEGER},
      mall_money_flag = #{mallMoneyFlag,jdbcType=BIT},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      dinner_rule = #{dinnerRule,jdbcType=INTEGER},
      dinner_money_flag = #{dinnerMoneyFlag,jdbcType=BIT},
      hotel_rule_price_type = #{hotelRulePriceType,jdbcType=INTEGER},
      takeaway_rule = #{takeawayRule,jdbcType=SMALLINT},
      takeaway_money_flag = #{takeawayMoneyFlag,jdbcType=BIT}
    where company_id = #{companyId,jdbcType=CHAR}
  </update>
</mapper>