<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.TrainRuleMapper">
  <resultMap id="BaseResultMapEx" type="com.fenbeitong.saas.core.model.fenbeitong.TrainRule"
             extends="com.fenbeitong.saas.core.dao.fenbeitong.TrainRuleMapper.BaseResultMap">
    </resultMap>


  <update id="updateManualRuleIdByRuleId" parameterType="com.fenbeitong.usercenter.api.model.po.rule.EmployeeTrainRule">
    update employee_train_rule
    set manual_train_rule_id = null,
    air_rule_flag = false
    where manual_train_rule_id = #{manualTrainRuleId,jdbcType=CHAR}
  </update>

  <select id="queryEmployeeTrainRuleCount" resultType="java.lang.Integer">
    select count(*) from employee_train_rule a
    left join employee b on a.employee_id=b.id
    where a.manual_train_rule_id = #{ruleId,jdbcType=INTEGER} and b.status!=4;
  </select>



  <select id="queryEmployeeTrainRuleFields" parameterType="java.util.Map"
          resultType="com.fenbeitong.saas.core.model.fenbeitong.TrainRule">

    SELECT
	tr.id ,
	tr.company_id AS companyId,
	tr.name,
	tr.train_seat_flag AS trainSeatFlag ,
	tr.common_train_seat_type AS  commonTrainSeatType,
	tr.highspeed_train_seat_type AS highspeedTrainSeatType,
    tr.modify_time AS modifyTime,
    tr.grap_common_seat_type AS grapCommonSeatType,
    tr.grap_highspeed_seat_type AS grapHighspeedSeatType,
    tr.priv_day_min AS privDayMin,
    tr.priv_day_max AS privDayMax,
    tr.day_limit AS dayLimit,
    tr.price_limit AS  priceLimit,
    tr.is_same_seat AS  isSameSeat
    FROM
        train_rule tr LEFT JOIN
        employee_train_rule etr
    ON
        etr.manual_train_rule_id = tr."id"
    WHERE etr.employee_id = #{userId}
  </select>
</mapper>