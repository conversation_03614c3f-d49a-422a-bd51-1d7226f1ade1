<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.DinnerTimeRangeExtMapper">
    <resultMap id="BaseResultMapExt" type="com.fenbeitong.saas.core.model.fenbeitong.DinnerTimeRange">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="rule_id" jdbcType="VARCHAR" property="ruleId"/>
        <result column="day_type" jdbcType="INTEGER" property="dayType"/>
        <result column="begin_time" jdbcType="TIME" property="beginTime"/>
        <result column="end_time" jdbcType="TIME" property="endTime"/>
        <result column="is_overnight" jdbcType="BIT" property="isOvernight"/>
        <result column="batch_id" jdbcType="BIGINT" property="batchId"/>
    </resultMap>

    <select id="selectBatchIdByRuleId" resultType="java.lang.Long">
        select batch_id from dinner_time_range
        where rule_id = #{ruleId,jdbcType=VARCHAR}
        group by batch_id
        order by batch_id asc
  </select>

    <select id="queryDinnerTimeByBatchId" resultMap="BaseResultMapExt">
        select * from dinner_time_range
        where rule_id = #{ruleId,jdbcType=VARCHAR}
        and batch_id = #{batchId,jdbcType=BIGINT}
        order by day_type asc
    </select>

    <select id="queryDinnerTimeByRuleIds" resultMap="BaseResultMapExt">
        select * from dinner_time_range
        where rule_id in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        order by rule_id, batch_id asc, day_type asc
    </select>

    <select id="queryDinnerTimeByRuleId" resultMap="BaseResultMapExt">
        select * from dinner_time_range
        where day_type = #{dayType,jdbcType=INTEGER}
        and rule_id = #{ruleId,jdbcType=VARCHAR}
    </select>
</mapper>