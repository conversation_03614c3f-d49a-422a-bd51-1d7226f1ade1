<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.CompanyMapperExt">
    <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.Company">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Tue May 23 21:17:19 CST 2017.
        -->
        <constructor>
            <idArg column="id" javaType="java.lang.String" jdbcType="CHAR"/>
            <arg column="name" javaType="java.lang.String" jdbcType="VARCHAR"/>
            <arg column="certificate_num" javaType="java.lang.String" jdbcType="CHAR"/>
            <arg column="status" javaType="java.lang.Integer" jdbcType="INTEGER"/>
            <arg column="alias" javaType="java.lang.String" jdbcType="VARCHAR"/>
            <arg column="office_address" javaType="java.lang.String" jdbcType="VARCHAR"/>
            <arg column="super_admin_id" javaType="java.lang.String" jdbcType="CHAR"/>
            <arg column="account_manager_id" javaType="java.lang.String" jdbcType="CHAR"/>
            <arg column="remark" javaType="java.lang.String" jdbcType="VARCHAR"/>
        </constructor>
    </resultMap>

    <select id="queryCompanyId" resultType="java.lang.String" parameterType="java.lang.Integer">
    select id
    from company
    order by id desc
    limit #{pageSize,jdbcType=INTEGER} offset #{pageStart,jdbcType=INTEGER}
  </select>
</mapper>