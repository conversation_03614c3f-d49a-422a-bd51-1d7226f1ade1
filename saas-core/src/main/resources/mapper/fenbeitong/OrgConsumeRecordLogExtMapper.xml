<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.OrgConsumeRecordLogExtMapper">
    <resultMap id="BaseResultMap"
               type="com.fenbeitong.saas.core.model.fenbeitong.OrgConsumeRecordLog"
               extends="com.fenbeitong.saas.core.dao.fenbeitong.OrgConsumeRecordLogMapper.BaseResultMap">
    </resultMap>

    <!--批量插入-->
    <insert id="batchInsertOrgConsumeRecordLog">
        INSERT INTO org_consume_record_log(id, consumer_id, consumer_name,
        company_id,order_id,amount,consume_category,consumer_type,create_time,pre_order_id,origin_order_id,is_latest,request_time,param_json)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.consumerId}, #{item.consumerName}, #{item.companyId}, #{item.orderId}, #{item.amount},
            #{item.consumeCategory}
            , #{item.consumerType}, #{item.createTime}, #{item.preOrderId}, #{item.originOrderId}, #{item.isLatest},
            #{item.requestTime}, #{item.paramJson})
        </foreach>
    </insert>
</mapper>