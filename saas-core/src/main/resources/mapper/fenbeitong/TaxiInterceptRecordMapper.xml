<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.TaxiInterceptRecordMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.TaxiInterceptRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="employee_id" jdbcType="CHAR" property="employeeId" />
    <result column="company_id" jdbcType="CHAR" property="companyId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="contact_name" jdbcType="VARCHAR" property="contactName" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="channel" jdbcType="INTEGER" property="channel" />
    <result column="taxi_rule" jdbcType="INTEGER" property="taxiRule" />
    <result column="taxi_rule_flag" jdbcType="BIT" property="taxiRuleFlag" />
    <result column="exceed_buy_flag" jdbcType="BIT" property="exceedBuyFlag" />
    <result column="limit_time" jdbcType="BIT" property="limitTime" />
    <result column="taxi_time_range_info" jdbcType="VARCHAR" property="taxiTimeRangeInfo" />
    <result column="limit_departure" jdbcType="BIT" property="limitDeparture" />
    <result column="departure_location_info" jdbcType="VARCHAR" property="departureLocationInfo" />
    <result column="limit_arrival" jdbcType="BIT" property="limitArrival" />
    <result column="arrival_location_info" jdbcType="VARCHAR" property="arrivalLocationInfo" />
    <result column="limit_taxi_type" jdbcType="BIT" property="limitTaxiType" />
    <result column="allowed_taxi_type" jdbcType="VARCHAR" property="allowedTaxiType" />
    <result column="schedule_type" jdbcType="INTEGER" property="scheduleType" />
    <result column="schedule_time" jdbcType="TIMESTAMP" property="scheduleTime" />
    <result column="rule" jdbcType="INTEGER" property="rule" />
    <result column="require_level" jdbcType="INTEGER" property="requireLevel" />
    <result column="estimate_price" jdbcType="NUMERIC" property="estimatePrice" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="departure_lng" jdbcType="VARCHAR" property="departureLng" />
    <result column="departure_lat" jdbcType="VARCHAR" property="departureLat" />
    <result column="departure_name" jdbcType="VARCHAR" property="departureName" />
    <result column="departure_addr" jdbcType="VARCHAR" property="departureAddr" />
    <result column="departure_time" jdbcType="TIMESTAMP" property="departureTime" />
    <result column="arrival_lng" jdbcType="VARCHAR" property="arrivalLng" />
    <result column="arrival_lat" jdbcType="VARCHAR" property="arrivalLat" />
    <result column="arrival_name" jdbcType="VARCHAR" property="arrivalName" />
    <result column="arrival_addr" jdbcType="VARCHAR" property="arrivalAddr" />
    <result column="client_lng" jdbcType="VARCHAR" property="clientLng" />
    <result column="client_lat" jdbcType="VARCHAR" property="clientLat" />
    <result column="err_msg" jdbcType="VARCHAR" property="errMsg" />
    <result column="err_code" jdbcType="INTEGER" property="errCode" />
    <result column="limit_price" jdbcType="BIT" property="limitPrice" />
    <result column="limit_price_info" jdbcType="NUMERIC" property="limitPriceInfo" />
    <result column="cost_center_id" jdbcType="VARCHAR" property="costCenterId" />
    <result column="cost_center_type" jdbcType="INTEGER" property="costCenterType" />
    <result column="flt" jdbcType="VARCHAR" property="flt" />
    <result column="flight_date" jdbcType="VARCHAR" property="flightDate" />
    <result column="flight_delay_time" jdbcType="INTEGER" property="flightDelayTime" />
    <result column="air_code" jdbcType="VARCHAR" property="airCode" />
    <result column="allow_shuttle" jdbcType="BIT" property="allowShuttle" />
    <result column="exceed_buy_type" jdbcType="INTEGER" property="exceedBuyType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, employee_id, company_id, create_time, contact_name, contact_phone, channel, taxi_rule, 
    taxi_rule_flag, exceed_buy_flag, limit_time, taxi_time_range_info, limit_departure, 
    departure_location_info, limit_arrival, arrival_location_info, limit_taxi_type, allowed_taxi_type, 
    schedule_type, schedule_time, rule, require_level, estimate_price, city, departure_lng, 
    departure_lat, departure_name, departure_addr, departure_time, arrival_lng, arrival_lat, 
    arrival_name, arrival_addr, client_lng, client_lat, err_msg, err_code, limit_price, 
    limit_price_info, cost_center_id, cost_center_type, flt, flight_date, flight_delay_time, 
    air_code, allow_shuttle, exceed_buy_type
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiInterceptRecordExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from taxi_intercept_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from taxi_intercept_record
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from taxi_intercept_record
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiInterceptRecordExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from taxi_intercept_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiInterceptRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into taxi_intercept_record (id, employee_id, company_id, 
      create_time, contact_name, contact_phone, 
      channel, taxi_rule, taxi_rule_flag, 
      exceed_buy_flag, limit_time, taxi_time_range_info, 
      limit_departure, departure_location_info, limit_arrival, 
      arrival_location_info, limit_taxi_type, allowed_taxi_type, 
      schedule_type, schedule_time, rule, 
      require_level, estimate_price, city, 
      departure_lng, departure_lat, departure_name, 
      departure_addr, departure_time, arrival_lng, 
      arrival_lat, arrival_name, arrival_addr, 
      client_lng, client_lat, err_msg, 
      err_code, limit_price, limit_price_info, 
      cost_center_id, cost_center_type, flt, 
      flight_date, flight_delay_time, air_code, 
      allow_shuttle, exceed_buy_type)
    values (#{id,jdbcType=CHAR}, #{employeeId,jdbcType=CHAR}, #{companyId,jdbcType=CHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{contactName,jdbcType=VARCHAR}, #{contactPhone,jdbcType=VARCHAR}, 
      #{channel,jdbcType=INTEGER}, #{taxiRule,jdbcType=INTEGER}, #{taxiRuleFlag,jdbcType=BIT}, 
      #{exceedBuyFlag,jdbcType=BIT}, #{limitTime,jdbcType=BIT}, #{taxiTimeRangeInfo,jdbcType=VARCHAR}, 
      #{limitDeparture,jdbcType=BIT}, #{departureLocationInfo,jdbcType=VARCHAR}, #{limitArrival,jdbcType=BIT}, 
      #{arrivalLocationInfo,jdbcType=VARCHAR}, #{limitTaxiType,jdbcType=BIT}, #{allowedTaxiType,jdbcType=VARCHAR}, 
      #{scheduleType,jdbcType=INTEGER}, #{scheduleTime,jdbcType=TIMESTAMP}, #{rule,jdbcType=INTEGER}, 
      #{requireLevel,jdbcType=INTEGER}, #{estimatePrice,jdbcType=NUMERIC}, #{city,jdbcType=VARCHAR}, 
      #{departureLng,jdbcType=VARCHAR}, #{departureLat,jdbcType=VARCHAR}, #{departureName,jdbcType=VARCHAR}, 
      #{departureAddr,jdbcType=VARCHAR}, #{departureTime,jdbcType=TIMESTAMP}, #{arrivalLng,jdbcType=VARCHAR}, 
      #{arrivalLat,jdbcType=VARCHAR}, #{arrivalName,jdbcType=VARCHAR}, #{arrivalAddr,jdbcType=VARCHAR}, 
      #{clientLng,jdbcType=VARCHAR}, #{clientLat,jdbcType=VARCHAR}, #{errMsg,jdbcType=VARCHAR}, 
      #{errCode,jdbcType=INTEGER}, #{limitPrice,jdbcType=BIT}, #{limitPriceInfo,jdbcType=NUMERIC}, 
      #{costCenterId,jdbcType=VARCHAR}, #{costCenterType,jdbcType=INTEGER}, #{flt,jdbcType=VARCHAR}, 
      #{flightDate,jdbcType=VARCHAR}, #{flightDelayTime,jdbcType=INTEGER}, #{airCode,jdbcType=VARCHAR}, 
      #{allowShuttle,jdbcType=BIT}, #{exceedBuyType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiInterceptRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into taxi_intercept_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="employeeId != null">
        employee_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="contactName != null">
        contact_name,
      </if>
      <if test="contactPhone != null">
        contact_phone,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="taxiRule != null">
        taxi_rule,
      </if>
      <if test="taxiRuleFlag != null">
        taxi_rule_flag,
      </if>
      <if test="exceedBuyFlag != null">
        exceed_buy_flag,
      </if>
      <if test="limitTime != null">
        limit_time,
      </if>
      <if test="taxiTimeRangeInfo != null">
        taxi_time_range_info,
      </if>
      <if test="limitDeparture != null">
        limit_departure,
      </if>
      <if test="departureLocationInfo != null">
        departure_location_info,
      </if>
      <if test="limitArrival != null">
        limit_arrival,
      </if>
      <if test="arrivalLocationInfo != null">
        arrival_location_info,
      </if>
      <if test="limitTaxiType != null">
        limit_taxi_type,
      </if>
      <if test="allowedTaxiType != null">
        allowed_taxi_type,
      </if>
      <if test="scheduleType != null">
        schedule_type,
      </if>
      <if test="scheduleTime != null">
        schedule_time,
      </if>
      <if test="rule != null">
        rule,
      </if>
      <if test="requireLevel != null">
        require_level,
      </if>
      <if test="estimatePrice != null">
        estimate_price,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="departureLng != null">
        departure_lng,
      </if>
      <if test="departureLat != null">
        departure_lat,
      </if>
      <if test="departureName != null">
        departure_name,
      </if>
      <if test="departureAddr != null">
        departure_addr,
      </if>
      <if test="departureTime != null">
        departure_time,
      </if>
      <if test="arrivalLng != null">
        arrival_lng,
      </if>
      <if test="arrivalLat != null">
        arrival_lat,
      </if>
      <if test="arrivalName != null">
        arrival_name,
      </if>
      <if test="arrivalAddr != null">
        arrival_addr,
      </if>
      <if test="clientLng != null">
        client_lng,
      </if>
      <if test="clientLat != null">
        client_lat,
      </if>
      <if test="errMsg != null">
        err_msg,
      </if>
      <if test="errCode != null">
        err_code,
      </if>
      <if test="limitPrice != null">
        limit_price,
      </if>
      <if test="limitPriceInfo != null">
        limit_price_info,
      </if>
      <if test="costCenterId != null">
        cost_center_id,
      </if>
      <if test="costCenterType != null">
        cost_center_type,
      </if>
      <if test="flt != null">
        flt,
      </if>
      <if test="flightDate != null">
        flight_date,
      </if>
      <if test="flightDelayTime != null">
        flight_delay_time,
      </if>
      <if test="airCode != null">
        air_code,
      </if>
      <if test="allowShuttle != null">
        allow_shuttle,
      </if>
      <if test="exceedBuyType != null">
        exceed_buy_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="employeeId != null">
        #{employeeId,jdbcType=CHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contactName != null">
        #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=INTEGER},
      </if>
      <if test="taxiRule != null">
        #{taxiRule,jdbcType=INTEGER},
      </if>
      <if test="taxiRuleFlag != null">
        #{taxiRuleFlag,jdbcType=BIT},
      </if>
      <if test="exceedBuyFlag != null">
        #{exceedBuyFlag,jdbcType=BIT},
      </if>
      <if test="limitTime != null">
        #{limitTime,jdbcType=BIT},
      </if>
      <if test="taxiTimeRangeInfo != null">
        #{taxiTimeRangeInfo,jdbcType=VARCHAR},
      </if>
      <if test="limitDeparture != null">
        #{limitDeparture,jdbcType=BIT},
      </if>
      <if test="departureLocationInfo != null">
        #{departureLocationInfo,jdbcType=VARCHAR},
      </if>
      <if test="limitArrival != null">
        #{limitArrival,jdbcType=BIT},
      </if>
      <if test="arrivalLocationInfo != null">
        #{arrivalLocationInfo,jdbcType=VARCHAR},
      </if>
      <if test="limitTaxiType != null">
        #{limitTaxiType,jdbcType=BIT},
      </if>
      <if test="allowedTaxiType != null">
        #{allowedTaxiType,jdbcType=VARCHAR},
      </if>
      <if test="scheduleType != null">
        #{scheduleType,jdbcType=INTEGER},
      </if>
      <if test="scheduleTime != null">
        #{scheduleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="rule != null">
        #{rule,jdbcType=INTEGER},
      </if>
      <if test="requireLevel != null">
        #{requireLevel,jdbcType=INTEGER},
      </if>
      <if test="estimatePrice != null">
        #{estimatePrice,jdbcType=NUMERIC},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="departureLng != null">
        #{departureLng,jdbcType=VARCHAR},
      </if>
      <if test="departureLat != null">
        #{departureLat,jdbcType=VARCHAR},
      </if>
      <if test="departureName != null">
        #{departureName,jdbcType=VARCHAR},
      </if>
      <if test="departureAddr != null">
        #{departureAddr,jdbcType=VARCHAR},
      </if>
      <if test="departureTime != null">
        #{departureTime,jdbcType=TIMESTAMP},
      </if>
      <if test="arrivalLng != null">
        #{arrivalLng,jdbcType=VARCHAR},
      </if>
      <if test="arrivalLat != null">
        #{arrivalLat,jdbcType=VARCHAR},
      </if>
      <if test="arrivalName != null">
        #{arrivalName,jdbcType=VARCHAR},
      </if>
      <if test="arrivalAddr != null">
        #{arrivalAddr,jdbcType=VARCHAR},
      </if>
      <if test="clientLng != null">
        #{clientLng,jdbcType=VARCHAR},
      </if>
      <if test="clientLat != null">
        #{clientLat,jdbcType=VARCHAR},
      </if>
      <if test="errMsg != null">
        #{errMsg,jdbcType=VARCHAR},
      </if>
      <if test="errCode != null">
        #{errCode,jdbcType=INTEGER},
      </if>
      <if test="limitPrice != null">
        #{limitPrice,jdbcType=BIT},
      </if>
      <if test="limitPriceInfo != null">
        #{limitPriceInfo,jdbcType=NUMERIC},
      </if>
      <if test="costCenterId != null">
        #{costCenterId,jdbcType=VARCHAR},
      </if>
      <if test="costCenterType != null">
        #{costCenterType,jdbcType=INTEGER},
      </if>
      <if test="flt != null">
        #{flt,jdbcType=VARCHAR},
      </if>
      <if test="flightDate != null">
        #{flightDate,jdbcType=VARCHAR},
      </if>
      <if test="flightDelayTime != null">
        #{flightDelayTime,jdbcType=INTEGER},
      </if>
      <if test="airCode != null">
        #{airCode,jdbcType=VARCHAR},
      </if>
      <if test="allowShuttle != null">
        #{allowShuttle,jdbcType=BIT},
      </if>
      <if test="exceedBuyType != null">
        #{exceedBuyType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiInterceptRecordExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from taxi_intercept_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update taxi_intercept_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.employeeId != null">
        employee_id = #{record.employeeId,jdbcType=CHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=CHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.contactName != null">
        contact_name = #{record.contactName,jdbcType=VARCHAR},
      </if>
      <if test="record.contactPhone != null">
        contact_phone = #{record.contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.channel != null">
        channel = #{record.channel,jdbcType=INTEGER},
      </if>
      <if test="record.taxiRule != null">
        taxi_rule = #{record.taxiRule,jdbcType=INTEGER},
      </if>
      <if test="record.taxiRuleFlag != null">
        taxi_rule_flag = #{record.taxiRuleFlag,jdbcType=BIT},
      </if>
      <if test="record.exceedBuyFlag != null">
        exceed_buy_flag = #{record.exceedBuyFlag,jdbcType=BIT},
      </if>
      <if test="record.limitTime != null">
        limit_time = #{record.limitTime,jdbcType=BIT},
      </if>
      <if test="record.taxiTimeRangeInfo != null">
        taxi_time_range_info = #{record.taxiTimeRangeInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.limitDeparture != null">
        limit_departure = #{record.limitDeparture,jdbcType=BIT},
      </if>
      <if test="record.departureLocationInfo != null">
        departure_location_info = #{record.departureLocationInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.limitArrival != null">
        limit_arrival = #{record.limitArrival,jdbcType=BIT},
      </if>
      <if test="record.arrivalLocationInfo != null">
        arrival_location_info = #{record.arrivalLocationInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.limitTaxiType != null">
        limit_taxi_type = #{record.limitTaxiType,jdbcType=BIT},
      </if>
      <if test="record.allowedTaxiType != null">
        allowed_taxi_type = #{record.allowedTaxiType,jdbcType=VARCHAR},
      </if>
      <if test="record.scheduleType != null">
        schedule_type = #{record.scheduleType,jdbcType=INTEGER},
      </if>
      <if test="record.scheduleTime != null">
        schedule_time = #{record.scheduleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.rule != null">
        rule = #{record.rule,jdbcType=INTEGER},
      </if>
      <if test="record.requireLevel != null">
        require_level = #{record.requireLevel,jdbcType=INTEGER},
      </if>
      <if test="record.estimatePrice != null">
        estimate_price = #{record.estimatePrice,jdbcType=NUMERIC},
      </if>
      <if test="record.city != null">
        city = #{record.city,jdbcType=VARCHAR},
      </if>
      <if test="record.departureLng != null">
        departure_lng = #{record.departureLng,jdbcType=VARCHAR},
      </if>
      <if test="record.departureLat != null">
        departure_lat = #{record.departureLat,jdbcType=VARCHAR},
      </if>
      <if test="record.departureName != null">
        departure_name = #{record.departureName,jdbcType=VARCHAR},
      </if>
      <if test="record.departureAddr != null">
        departure_addr = #{record.departureAddr,jdbcType=VARCHAR},
      </if>
      <if test="record.departureTime != null">
        departure_time = #{record.departureTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.arrivalLng != null">
        arrival_lng = #{record.arrivalLng,jdbcType=VARCHAR},
      </if>
      <if test="record.arrivalLat != null">
        arrival_lat = #{record.arrivalLat,jdbcType=VARCHAR},
      </if>
      <if test="record.arrivalName != null">
        arrival_name = #{record.arrivalName,jdbcType=VARCHAR},
      </if>
      <if test="record.arrivalAddr != null">
        arrival_addr = #{record.arrivalAddr,jdbcType=VARCHAR},
      </if>
      <if test="record.clientLng != null">
        client_lng = #{record.clientLng,jdbcType=VARCHAR},
      </if>
      <if test="record.clientLat != null">
        client_lat = #{record.clientLat,jdbcType=VARCHAR},
      </if>
      <if test="record.errMsg != null">
        err_msg = #{record.errMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.errCode != null">
        err_code = #{record.errCode,jdbcType=INTEGER},
      </if>
      <if test="record.limitPrice != null">
        limit_price = #{record.limitPrice,jdbcType=BIT},
      </if>
      <if test="record.limitPriceInfo != null">
        limit_price_info = #{record.limitPriceInfo,jdbcType=NUMERIC},
      </if>
      <if test="record.costCenterId != null">
        cost_center_id = #{record.costCenterId,jdbcType=VARCHAR},
      </if>
      <if test="record.costCenterType != null">
        cost_center_type = #{record.costCenterType,jdbcType=INTEGER},
      </if>
      <if test="record.flt != null">
        flt = #{record.flt,jdbcType=VARCHAR},
      </if>
      <if test="record.flightDate != null">
        flight_date = #{record.flightDate,jdbcType=VARCHAR},
      </if>
      <if test="record.flightDelayTime != null">
        flight_delay_time = #{record.flightDelayTime,jdbcType=INTEGER},
      </if>
      <if test="record.airCode != null">
        air_code = #{record.airCode,jdbcType=VARCHAR},
      </if>
      <if test="record.allowShuttle != null">
        allow_shuttle = #{record.allowShuttle,jdbcType=BIT},
      </if>
      <if test="record.exceedBuyType != null">
        exceed_buy_type = #{record.exceedBuyType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update taxi_intercept_record
    set id = #{record.id,jdbcType=CHAR},
      employee_id = #{record.employeeId,jdbcType=CHAR},
      company_id = #{record.companyId,jdbcType=CHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      contact_name = #{record.contactName,jdbcType=VARCHAR},
      contact_phone = #{record.contactPhone,jdbcType=VARCHAR},
      channel = #{record.channel,jdbcType=INTEGER},
      taxi_rule = #{record.taxiRule,jdbcType=INTEGER},
      taxi_rule_flag = #{record.taxiRuleFlag,jdbcType=BIT},
      exceed_buy_flag = #{record.exceedBuyFlag,jdbcType=BIT},
      limit_time = #{record.limitTime,jdbcType=BIT},
      taxi_time_range_info = #{record.taxiTimeRangeInfo,jdbcType=VARCHAR},
      limit_departure = #{record.limitDeparture,jdbcType=BIT},
      departure_location_info = #{record.departureLocationInfo,jdbcType=VARCHAR},
      limit_arrival = #{record.limitArrival,jdbcType=BIT},
      arrival_location_info = #{record.arrivalLocationInfo,jdbcType=VARCHAR},
      limit_taxi_type = #{record.limitTaxiType,jdbcType=BIT},
      allowed_taxi_type = #{record.allowedTaxiType,jdbcType=VARCHAR},
      schedule_type = #{record.scheduleType,jdbcType=INTEGER},
      schedule_time = #{record.scheduleTime,jdbcType=TIMESTAMP},
      rule = #{record.rule,jdbcType=INTEGER},
      require_level = #{record.requireLevel,jdbcType=INTEGER},
      estimate_price = #{record.estimatePrice,jdbcType=NUMERIC},
      city = #{record.city,jdbcType=VARCHAR},
      departure_lng = #{record.departureLng,jdbcType=VARCHAR},
      departure_lat = #{record.departureLat,jdbcType=VARCHAR},
      departure_name = #{record.departureName,jdbcType=VARCHAR},
      departure_addr = #{record.departureAddr,jdbcType=VARCHAR},
      departure_time = #{record.departureTime,jdbcType=TIMESTAMP},
      arrival_lng = #{record.arrivalLng,jdbcType=VARCHAR},
      arrival_lat = #{record.arrivalLat,jdbcType=VARCHAR},
      arrival_name = #{record.arrivalName,jdbcType=VARCHAR},
      arrival_addr = #{record.arrivalAddr,jdbcType=VARCHAR},
      client_lng = #{record.clientLng,jdbcType=VARCHAR},
      client_lat = #{record.clientLat,jdbcType=VARCHAR},
      err_msg = #{record.errMsg,jdbcType=VARCHAR},
      err_code = #{record.errCode,jdbcType=INTEGER},
      limit_price = #{record.limitPrice,jdbcType=BIT},
      limit_price_info = #{record.limitPriceInfo,jdbcType=NUMERIC},
      cost_center_id = #{record.costCenterId,jdbcType=VARCHAR},
      cost_center_type = #{record.costCenterType,jdbcType=INTEGER},
      flt = #{record.flt,jdbcType=VARCHAR},
      flight_date = #{record.flightDate,jdbcType=VARCHAR},
      flight_delay_time = #{record.flightDelayTime,jdbcType=INTEGER},
      air_code = #{record.airCode,jdbcType=VARCHAR},
      allow_shuttle = #{record.allowShuttle,jdbcType=BIT},
      exceed_buy_type = #{record.exceedBuyType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiInterceptRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update taxi_intercept_record
    <set>
      <if test="employeeId != null">
        employee_id = #{employeeId,jdbcType=CHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contactName != null">
        contact_name = #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        contact_phone = #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=INTEGER},
      </if>
      <if test="taxiRule != null">
        taxi_rule = #{taxiRule,jdbcType=INTEGER},
      </if>
      <if test="taxiRuleFlag != null">
        taxi_rule_flag = #{taxiRuleFlag,jdbcType=BIT},
      </if>
      <if test="exceedBuyFlag != null">
        exceed_buy_flag = #{exceedBuyFlag,jdbcType=BIT},
      </if>
      <if test="limitTime != null">
        limit_time = #{limitTime,jdbcType=BIT},
      </if>
      <if test="taxiTimeRangeInfo != null">
        taxi_time_range_info = #{taxiTimeRangeInfo,jdbcType=VARCHAR},
      </if>
      <if test="limitDeparture != null">
        limit_departure = #{limitDeparture,jdbcType=BIT},
      </if>
      <if test="departureLocationInfo != null">
        departure_location_info = #{departureLocationInfo,jdbcType=VARCHAR},
      </if>
      <if test="limitArrival != null">
        limit_arrival = #{limitArrival,jdbcType=BIT},
      </if>
      <if test="arrivalLocationInfo != null">
        arrival_location_info = #{arrivalLocationInfo,jdbcType=VARCHAR},
      </if>
      <if test="limitTaxiType != null">
        limit_taxi_type = #{limitTaxiType,jdbcType=BIT},
      </if>
      <if test="allowedTaxiType != null">
        allowed_taxi_type = #{allowedTaxiType,jdbcType=VARCHAR},
      </if>
      <if test="scheduleType != null">
        schedule_type = #{scheduleType,jdbcType=INTEGER},
      </if>
      <if test="scheduleTime != null">
        schedule_time = #{scheduleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="rule != null">
        rule = #{rule,jdbcType=INTEGER},
      </if>
      <if test="requireLevel != null">
        require_level = #{requireLevel,jdbcType=INTEGER},
      </if>
      <if test="estimatePrice != null">
        estimate_price = #{estimatePrice,jdbcType=NUMERIC},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="departureLng != null">
        departure_lng = #{departureLng,jdbcType=VARCHAR},
      </if>
      <if test="departureLat != null">
        departure_lat = #{departureLat,jdbcType=VARCHAR},
      </if>
      <if test="departureName != null">
        departure_name = #{departureName,jdbcType=VARCHAR},
      </if>
      <if test="departureAddr != null">
        departure_addr = #{departureAddr,jdbcType=VARCHAR},
      </if>
      <if test="departureTime != null">
        departure_time = #{departureTime,jdbcType=TIMESTAMP},
      </if>
      <if test="arrivalLng != null">
        arrival_lng = #{arrivalLng,jdbcType=VARCHAR},
      </if>
      <if test="arrivalLat != null">
        arrival_lat = #{arrivalLat,jdbcType=VARCHAR},
      </if>
      <if test="arrivalName != null">
        arrival_name = #{arrivalName,jdbcType=VARCHAR},
      </if>
      <if test="arrivalAddr != null">
        arrival_addr = #{arrivalAddr,jdbcType=VARCHAR},
      </if>
      <if test="clientLng != null">
        client_lng = #{clientLng,jdbcType=VARCHAR},
      </if>
      <if test="clientLat != null">
        client_lat = #{clientLat,jdbcType=VARCHAR},
      </if>
      <if test="errMsg != null">
        err_msg = #{errMsg,jdbcType=VARCHAR},
      </if>
      <if test="errCode != null">
        err_code = #{errCode,jdbcType=INTEGER},
      </if>
      <if test="limitPrice != null">
        limit_price = #{limitPrice,jdbcType=BIT},
      </if>
      <if test="limitPriceInfo != null">
        limit_price_info = #{limitPriceInfo,jdbcType=NUMERIC},
      </if>
      <if test="costCenterId != null">
        cost_center_id = #{costCenterId,jdbcType=VARCHAR},
      </if>
      <if test="costCenterType != null">
        cost_center_type = #{costCenterType,jdbcType=INTEGER},
      </if>
      <if test="flt != null">
        flt = #{flt,jdbcType=VARCHAR},
      </if>
      <if test="flightDate != null">
        flight_date = #{flightDate,jdbcType=VARCHAR},
      </if>
      <if test="flightDelayTime != null">
        flight_delay_time = #{flightDelayTime,jdbcType=INTEGER},
      </if>
      <if test="airCode != null">
        air_code = #{airCode,jdbcType=VARCHAR},
      </if>
      <if test="allowShuttle != null">
        allow_shuttle = #{allowShuttle,jdbcType=BIT},
      </if>
      <if test="exceedBuyType != null">
        exceed_buy_type = #{exceedBuyType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiInterceptRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update taxi_intercept_record
    set employee_id = #{employeeId,jdbcType=CHAR},
      company_id = #{companyId,jdbcType=CHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      contact_name = #{contactName,jdbcType=VARCHAR},
      contact_phone = #{contactPhone,jdbcType=VARCHAR},
      channel = #{channel,jdbcType=INTEGER},
      taxi_rule = #{taxiRule,jdbcType=INTEGER},
      taxi_rule_flag = #{taxiRuleFlag,jdbcType=BIT},
      exceed_buy_flag = #{exceedBuyFlag,jdbcType=BIT},
      limit_time = #{limitTime,jdbcType=BIT},
      taxi_time_range_info = #{taxiTimeRangeInfo,jdbcType=VARCHAR},
      limit_departure = #{limitDeparture,jdbcType=BIT},
      departure_location_info = #{departureLocationInfo,jdbcType=VARCHAR},
      limit_arrival = #{limitArrival,jdbcType=BIT},
      arrival_location_info = #{arrivalLocationInfo,jdbcType=VARCHAR},
      limit_taxi_type = #{limitTaxiType,jdbcType=BIT},
      allowed_taxi_type = #{allowedTaxiType,jdbcType=VARCHAR},
      schedule_type = #{scheduleType,jdbcType=INTEGER},
      schedule_time = #{scheduleTime,jdbcType=TIMESTAMP},
      rule = #{rule,jdbcType=INTEGER},
      require_level = #{requireLevel,jdbcType=INTEGER},
      estimate_price = #{estimatePrice,jdbcType=NUMERIC},
      city = #{city,jdbcType=VARCHAR},
      departure_lng = #{departureLng,jdbcType=VARCHAR},
      departure_lat = #{departureLat,jdbcType=VARCHAR},
      departure_name = #{departureName,jdbcType=VARCHAR},
      departure_addr = #{departureAddr,jdbcType=VARCHAR},
      departure_time = #{departureTime,jdbcType=TIMESTAMP},
      arrival_lng = #{arrivalLng,jdbcType=VARCHAR},
      arrival_lat = #{arrivalLat,jdbcType=VARCHAR},
      arrival_name = #{arrivalName,jdbcType=VARCHAR},
      arrival_addr = #{arrivalAddr,jdbcType=VARCHAR},
      client_lng = #{clientLng,jdbcType=VARCHAR},
      client_lat = #{clientLat,jdbcType=VARCHAR},
      err_msg = #{errMsg,jdbcType=VARCHAR},
      err_code = #{errCode,jdbcType=INTEGER},
      limit_price = #{limitPrice,jdbcType=BIT},
      limit_price_info = #{limitPriceInfo,jdbcType=NUMERIC},
      cost_center_id = #{costCenterId,jdbcType=VARCHAR},
      cost_center_type = #{costCenterType,jdbcType=INTEGER},
      flt = #{flt,jdbcType=VARCHAR},
      flight_date = #{flightDate,jdbcType=VARCHAR},
      flight_delay_time = #{flightDelayTime,jdbcType=INTEGER},
      air_code = #{airCode,jdbcType=VARCHAR},
      allow_shuttle = #{allowShuttle,jdbcType=BIT},
      exceed_buy_type = #{exceedBuyType,jdbcType=INTEGER}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>