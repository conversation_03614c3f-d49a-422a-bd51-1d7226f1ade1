<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.EmployeeCustomroleMapper">
    <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.EmployeeCustomrole">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Apr 21 17:16:00 CST 2017.
        -->
        <result column="employee_id" jdbcType="CHAR" property="employeeId"/>
        <result column="customrole_id" jdbcType="CHAR" property="customroleId"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Apr 21 17:16:00 CST 2017.
        -->
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Apr 21 17:16:00 CST 2017.
        -->
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Apr 21 17:16:00 CST 2017.
        -->
        employee_id, customrole_id
    </sql>
    <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.EmployeeCustomroleExample"
            resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Apr 21 17:16:00 CST 2017.
        -->
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from employee_customrole
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.EmployeeCustomroleExample">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Apr 21 17:16:00 CST 2017.
        -->
        delete from employee_customrole
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.EmployeeCustomrole">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Apr 21 17:16:00 CST 2017.
        -->
        insert into employee_customrole
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="employeeId != null">
                employee_id,
            </if>
            <if test="customroleId != null">
                customrole_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="employeeId != null">
                #{employeeId,jdbcType=CHAR},
            </if>
            <if test="customroleId != null">
                #{customroleId,jdbcType=CHAR},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.EmployeeCustomroleExample"
            resultType="java.lang.Long">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Apr 21 17:16:00 CST 2017.
        -->
        select count(*) from employee_customrole
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Apr 21 17:16:00 CST 2017.
        -->
        update employee_customrole
        <set>
            <if test="record.employeeId != null">
                employee_id = #{record.employeeId,jdbcType=CHAR},
            </if>
            <if test="record.customroleId != null">
                customrole_id = #{record.customroleId,jdbcType=CHAR},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Apr 21 17:16:00 CST 2017.
        -->
        update employee_customrole
        set employee_id = #{record.employeeId,jdbcType=CHAR},
        customrole_id = #{record.customroleId,jdbcType=CHAR}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>

    <delete id="deleteByCustomroleId" parameterType="java.lang.String">
        DELETE FROM employee_customrole WHERE customrole_id=#{customroleId,jdbcType=CHAR}
    </delete>

    <select id="selectByExamplePage" parameterType="map" resultType="java.lang.String">
        select employee_id
        from employee_customrole where customrole_id=#{customroleId}
        limit #{pageSize} offset #{index}
    </select>
</mapper>