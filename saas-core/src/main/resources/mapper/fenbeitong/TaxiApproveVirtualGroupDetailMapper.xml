<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.TaxiApproveVirtualGroupDetailMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.TaxiApproveVirtualGroupDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="company_id" jdbcType="VARCHAR" property="companyId" />
    <id column="group_id" jdbcType="VARCHAR" property="groupId" />
    <id column="area_code" jdbcType="VARCHAR" property="areaCode" />
    <result column="taxi_approve_rule_id" jdbcType="VARCHAR" property="taxiApproveRuleId" />
    <result column="area_name" jdbcType="VARCHAR" property="areaName" />
    <result column="area_type" jdbcType="INTEGER" property="areaType" />
    <result column="parent_area_code" jdbcType="VARCHAR" property="parentAreaCode" />
    <result column="parent_area_name" jdbcType="VARCHAR" property="parentAreaName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    company_id, group_id, area_code, taxi_approve_rule_id, area_name, area_type, parent_area_code, 
    parent_area_name, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiApproveVirtualGroupDetailExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from taxi_approve_virtual_group_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${limit} offset ${offset}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from taxi_approve_virtual_group_detail
    where company_id = #{companyId,jdbcType=VARCHAR}
      and group_id = #{groupId,jdbcType=VARCHAR}
      and area_code = #{areaCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from taxi_approve_virtual_group_detail
    where company_id = #{companyId,jdbcType=VARCHAR}
      and group_id = #{groupId,jdbcType=VARCHAR}
      and area_code = #{areaCode,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiApproveVirtualGroupDetailExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from taxi_approve_virtual_group_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiApproveVirtualGroupDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into taxi_approve_virtual_group_detail (company_id, group_id, area_code, 
      taxi_approve_rule_id, area_name, area_type, 
      parent_area_code, parent_area_name, create_time, 
      update_time)
    values (#{companyId,jdbcType=VARCHAR}, #{groupId,jdbcType=VARCHAR}, #{areaCode,jdbcType=VARCHAR}, 
      #{taxiApproveRuleId,jdbcType=VARCHAR}, #{areaName,jdbcType=VARCHAR}, #{areaType,jdbcType=INTEGER}, 
      #{parentAreaCode,jdbcType=VARCHAR}, #{parentAreaName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiApproveVirtualGroupDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into taxi_approve_virtual_group_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="areaCode != null">
        area_code,
      </if>
      <if test="taxiApproveRuleId != null">
        taxi_approve_rule_id,
      </if>
      <if test="areaName != null">
        area_name,
      </if>
      <if test="areaType != null">
        area_type,
      </if>
      <if test="parentAreaCode != null">
        parent_area_code,
      </if>
      <if test="parentAreaName != null">
        parent_area_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="areaCode != null">
        #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="taxiApproveRuleId != null">
        #{taxiApproveRuleId,jdbcType=VARCHAR},
      </if>
      <if test="areaName != null">
        #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="areaType != null">
        #{areaType,jdbcType=INTEGER},
      </if>
      <if test="parentAreaCode != null">
        #{parentAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="parentAreaName != null">
        #{parentAreaName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiApproveVirtualGroupDetailExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from taxi_approve_virtual_group_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update taxi_approve_virtual_group_detail
    <set>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.groupId != null">
        group_id = #{record.groupId,jdbcType=VARCHAR},
      </if>
      <if test="record.areaCode != null">
        area_code = #{record.areaCode,jdbcType=VARCHAR},
      </if>
      <if test="record.taxiApproveRuleId != null">
        taxi_approve_rule_id = #{record.taxiApproveRuleId,jdbcType=VARCHAR},
      </if>
      <if test="record.areaName != null">
        area_name = #{record.areaName,jdbcType=VARCHAR},
      </if>
      <if test="record.areaType != null">
        area_type = #{record.areaType,jdbcType=INTEGER},
      </if>
      <if test="record.parentAreaCode != null">
        parent_area_code = #{record.parentAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="record.parentAreaName != null">
        parent_area_name = #{record.parentAreaName,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update taxi_approve_virtual_group_detail
    set company_id = #{record.companyId,jdbcType=VARCHAR},
      group_id = #{record.groupId,jdbcType=VARCHAR},
      area_code = #{record.areaCode,jdbcType=VARCHAR},
      taxi_approve_rule_id = #{record.taxiApproveRuleId,jdbcType=VARCHAR},
      area_name = #{record.areaName,jdbcType=VARCHAR},
      area_type = #{record.areaType,jdbcType=INTEGER},
      parent_area_code = #{record.parentAreaCode,jdbcType=VARCHAR},
      parent_area_name = #{record.parentAreaName,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiApproveVirtualGroupDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update taxi_approve_virtual_group_detail
    <set>
      <if test="taxiApproveRuleId != null">
        taxi_approve_rule_id = #{taxiApproveRuleId,jdbcType=VARCHAR},
      </if>
      <if test="areaName != null">
        area_name = #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="areaType != null">
        area_type = #{areaType,jdbcType=INTEGER},
      </if>
      <if test="parentAreaCode != null">
        parent_area_code = #{parentAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="parentAreaName != null">
        parent_area_name = #{parentAreaName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where company_id = #{companyId,jdbcType=VARCHAR}
      and group_id = #{groupId,jdbcType=VARCHAR}
      and area_code = #{areaCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiApproveVirtualGroupDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update taxi_approve_virtual_group_detail
    set taxi_approve_rule_id = #{taxiApproveRuleId,jdbcType=VARCHAR},
      area_name = #{areaName,jdbcType=VARCHAR},
      area_type = #{areaType,jdbcType=INTEGER},
      parent_area_code = #{parentAreaCode,jdbcType=VARCHAR},
      parent_area_name = #{parentAreaName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where company_id = #{companyId,jdbcType=VARCHAR}
      and group_id = #{groupId,jdbcType=VARCHAR}
      and area_code = #{areaCode,jdbcType=VARCHAR}
  </update>
</mapper>