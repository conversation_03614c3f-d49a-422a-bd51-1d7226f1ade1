<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.TakeawayInterceptRecordMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.TakeawayInterceptRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="employee_id" jdbcType="CHAR" property="employeeId" />
    <result column="company_id" jdbcType="CHAR" property="companyId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="contact_name" jdbcType="VARCHAR" property="contactName" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="channel" jdbcType="INTEGER" property="channel" />
    <result column="total_price" jdbcType="NUMERIC" property="totalPrice" />
    <result column="takeaway_rule" jdbcType="INTEGER" property="takeawayRule" />
    <result column="takeaway_rule_flag" jdbcType="BIT" property="takeawayRuleFlag" />
    <result column="limit_time" jdbcType="BIT" property="limitTime" />
    <result column="takeaway_time_range_info" jdbcType="VARCHAR" property="takeawayTimeRangeInfo" />
    <result column="limit_location" jdbcType="BIT" property="limitLocation" />
    <result column="takeaway_location_info" jdbcType="VARCHAR" property="takeawayLocationInfo" />
    <result column="err_msg" jdbcType="VARCHAR" property="errMsg" />
    <result column="err_code" jdbcType="INTEGER" property="errCode" />
    <result column="cost_center_id" jdbcType="VARCHAR" property="costCenterId" />
    <result column="cost_center_type" jdbcType="INTEGER" property="costCenterType" />
    <result column="exceed_buy_type" jdbcType="INTEGER" property="exceedBuyType" />
    <result column="personal_pay" jdbcType="BIT" property="personalPay" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, employee_id, company_id, create_time, contact_name, contact_phone, channel, total_price, 
    takeaway_rule, takeaway_rule_flag, limit_time, takeaway_time_range_info, limit_location, 
    takeaway_location_info, err_msg, err_code, cost_center_id, cost_center_type, exceed_buy_type, 
    personal_pay
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TakeawayInterceptRecordExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from takeaway_intercept_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${limit} offset ${offset}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from takeaway_intercept_record
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from takeaway_intercept_record
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TakeawayInterceptRecordExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from takeaway_intercept_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TakeawayInterceptRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into takeaway_intercept_record (id, employee_id, company_id, 
      create_time, contact_name, contact_phone, 
      channel, total_price, takeaway_rule, 
      takeaway_rule_flag, limit_time, takeaway_time_range_info, 
      limit_location, takeaway_location_info, err_msg, 
      err_code, cost_center_id, cost_center_type, 
      exceed_buy_type, personal_pay)
    values (#{id,jdbcType=CHAR}, #{employeeId,jdbcType=CHAR}, #{companyId,jdbcType=CHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{contactName,jdbcType=VARCHAR}, #{contactPhone,jdbcType=VARCHAR}, 
      #{channel,jdbcType=INTEGER}, #{totalPrice,jdbcType=NUMERIC}, #{takeawayRule,jdbcType=INTEGER}, 
      #{takeawayRuleFlag,jdbcType=BIT}, #{limitTime,jdbcType=BIT}, #{takeawayTimeRangeInfo,jdbcType=VARCHAR}, 
      #{limitLocation,jdbcType=BIT}, #{takeawayLocationInfo,jdbcType=VARCHAR}, #{errMsg,jdbcType=VARCHAR}, 
      #{errCode,jdbcType=INTEGER}, #{costCenterId,jdbcType=VARCHAR}, #{costCenterType,jdbcType=INTEGER}, 
      #{exceedBuyType,jdbcType=INTEGER}, #{personalPay,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TakeawayInterceptRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into takeaway_intercept_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="employeeId != null">
        employee_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="contactName != null">
        contact_name,
      </if>
      <if test="contactPhone != null">
        contact_phone,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="totalPrice != null">
        total_price,
      </if>
      <if test="takeawayRule != null">
        takeaway_rule,
      </if>
      <if test="takeawayRuleFlag != null">
        takeaway_rule_flag,
      </if>
      <if test="limitTime != null">
        limit_time,
      </if>
      <if test="takeawayTimeRangeInfo != null">
        takeaway_time_range_info,
      </if>
      <if test="limitLocation != null">
        limit_location,
      </if>
      <if test="takeawayLocationInfo != null">
        takeaway_location_info,
      </if>
      <if test="errMsg != null">
        err_msg,
      </if>
      <if test="errCode != null">
        err_code,
      </if>
      <if test="costCenterId != null">
        cost_center_id,
      </if>
      <if test="costCenterType != null">
        cost_center_type,
      </if>
      <if test="exceedBuyType != null">
        exceed_buy_type,
      </if>
      <if test="personalPay != null">
        personal_pay,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="employeeId != null">
        #{employeeId,jdbcType=CHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contactName != null">
        #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=INTEGER},
      </if>
      <if test="totalPrice != null">
        #{totalPrice,jdbcType=NUMERIC},
      </if>
      <if test="takeawayRule != null">
        #{takeawayRule,jdbcType=INTEGER},
      </if>
      <if test="takeawayRuleFlag != null">
        #{takeawayRuleFlag,jdbcType=BIT},
      </if>
      <if test="limitTime != null">
        #{limitTime,jdbcType=BIT},
      </if>
      <if test="takeawayTimeRangeInfo != null">
        #{takeawayTimeRangeInfo,jdbcType=VARCHAR},
      </if>
      <if test="limitLocation != null">
        #{limitLocation,jdbcType=BIT},
      </if>
      <if test="takeawayLocationInfo != null">
        #{takeawayLocationInfo,jdbcType=VARCHAR},
      </if>
      <if test="errMsg != null">
        #{errMsg,jdbcType=VARCHAR},
      </if>
      <if test="errCode != null">
        #{errCode,jdbcType=INTEGER},
      </if>
      <if test="costCenterId != null">
        #{costCenterId,jdbcType=VARCHAR},
      </if>
      <if test="costCenterType != null">
        #{costCenterType,jdbcType=INTEGER},
      </if>
      <if test="exceedBuyType != null">
        #{exceedBuyType,jdbcType=INTEGER},
      </if>
      <if test="personalPay != null">
        #{personalPay,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TakeawayInterceptRecordExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from takeaway_intercept_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update takeaway_intercept_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.employeeId != null">
        employee_id = #{record.employeeId,jdbcType=CHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=CHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.contactName != null">
        contact_name = #{record.contactName,jdbcType=VARCHAR},
      </if>
      <if test="record.contactPhone != null">
        contact_phone = #{record.contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.channel != null">
        channel = #{record.channel,jdbcType=INTEGER},
      </if>
      <if test="record.totalPrice != null">
        total_price = #{record.totalPrice,jdbcType=NUMERIC},
      </if>
      <if test="record.takeawayRule != null">
        takeaway_rule = #{record.takeawayRule,jdbcType=INTEGER},
      </if>
      <if test="record.takeawayRuleFlag != null">
        takeaway_rule_flag = #{record.takeawayRuleFlag,jdbcType=BIT},
      </if>
      <if test="record.limitTime != null">
        limit_time = #{record.limitTime,jdbcType=BIT},
      </if>
      <if test="record.takeawayTimeRangeInfo != null">
        takeaway_time_range_info = #{record.takeawayTimeRangeInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.limitLocation != null">
        limit_location = #{record.limitLocation,jdbcType=BIT},
      </if>
      <if test="record.takeawayLocationInfo != null">
        takeaway_location_info = #{record.takeawayLocationInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.errMsg != null">
        err_msg = #{record.errMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.errCode != null">
        err_code = #{record.errCode,jdbcType=INTEGER},
      </if>
      <if test="record.costCenterId != null">
        cost_center_id = #{record.costCenterId,jdbcType=VARCHAR},
      </if>
      <if test="record.costCenterType != null">
        cost_center_type = #{record.costCenterType,jdbcType=INTEGER},
      </if>
      <if test="record.exceedBuyType != null">
        exceed_buy_type = #{record.exceedBuyType,jdbcType=INTEGER},
      </if>
      <if test="record.personalPay != null">
        personal_pay = #{record.personalPay,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update takeaway_intercept_record
    set id = #{record.id,jdbcType=CHAR},
      employee_id = #{record.employeeId,jdbcType=CHAR},
      company_id = #{record.companyId,jdbcType=CHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      contact_name = #{record.contactName,jdbcType=VARCHAR},
      contact_phone = #{record.contactPhone,jdbcType=VARCHAR},
      channel = #{record.channel,jdbcType=INTEGER},
      total_price = #{record.totalPrice,jdbcType=NUMERIC},
      takeaway_rule = #{record.takeawayRule,jdbcType=INTEGER},
      takeaway_rule_flag = #{record.takeawayRuleFlag,jdbcType=BIT},
      limit_time = #{record.limitTime,jdbcType=BIT},
      takeaway_time_range_info = #{record.takeawayTimeRangeInfo,jdbcType=VARCHAR},
      limit_location = #{record.limitLocation,jdbcType=BIT},
      takeaway_location_info = #{record.takeawayLocationInfo,jdbcType=VARCHAR},
      err_msg = #{record.errMsg,jdbcType=VARCHAR},
      err_code = #{record.errCode,jdbcType=INTEGER},
      cost_center_id = #{record.costCenterId,jdbcType=VARCHAR},
      cost_center_type = #{record.costCenterType,jdbcType=INTEGER},
      exceed_buy_type = #{record.exceedBuyType,jdbcType=INTEGER},
      personal_pay = #{record.personalPay,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TakeawayInterceptRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update takeaway_intercept_record
    <set>
      <if test="employeeId != null">
        employee_id = #{employeeId,jdbcType=CHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contactName != null">
        contact_name = #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        contact_phone = #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=INTEGER},
      </if>
      <if test="totalPrice != null">
        total_price = #{totalPrice,jdbcType=NUMERIC},
      </if>
      <if test="takeawayRule != null">
        takeaway_rule = #{takeawayRule,jdbcType=INTEGER},
      </if>
      <if test="takeawayRuleFlag != null">
        takeaway_rule_flag = #{takeawayRuleFlag,jdbcType=BIT},
      </if>
      <if test="limitTime != null">
        limit_time = #{limitTime,jdbcType=BIT},
      </if>
      <if test="takeawayTimeRangeInfo != null">
        takeaway_time_range_info = #{takeawayTimeRangeInfo,jdbcType=VARCHAR},
      </if>
      <if test="limitLocation != null">
        limit_location = #{limitLocation,jdbcType=BIT},
      </if>
      <if test="takeawayLocationInfo != null">
        takeaway_location_info = #{takeawayLocationInfo,jdbcType=VARCHAR},
      </if>
      <if test="errMsg != null">
        err_msg = #{errMsg,jdbcType=VARCHAR},
      </if>
      <if test="errCode != null">
        err_code = #{errCode,jdbcType=INTEGER},
      </if>
      <if test="costCenterId != null">
        cost_center_id = #{costCenterId,jdbcType=VARCHAR},
      </if>
      <if test="costCenterType != null">
        cost_center_type = #{costCenterType,jdbcType=INTEGER},
      </if>
      <if test="exceedBuyType != null">
        exceed_buy_type = #{exceedBuyType,jdbcType=INTEGER},
      </if>
      <if test="personalPay != null">
        personal_pay = #{personalPay,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TakeawayInterceptRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update takeaway_intercept_record
    set employee_id = #{employeeId,jdbcType=CHAR},
      company_id = #{companyId,jdbcType=CHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      contact_name = #{contactName,jdbcType=VARCHAR},
      contact_phone = #{contactPhone,jdbcType=VARCHAR},
      channel = #{channel,jdbcType=INTEGER},
      total_price = #{totalPrice,jdbcType=NUMERIC},
      takeaway_rule = #{takeawayRule,jdbcType=INTEGER},
      takeaway_rule_flag = #{takeawayRuleFlag,jdbcType=BIT},
      limit_time = #{limitTime,jdbcType=BIT},
      takeaway_time_range_info = #{takeawayTimeRangeInfo,jdbcType=VARCHAR},
      limit_location = #{limitLocation,jdbcType=BIT},
      takeaway_location_info = #{takeawayLocationInfo,jdbcType=VARCHAR},
      err_msg = #{errMsg,jdbcType=VARCHAR},
      err_code = #{errCode,jdbcType=INTEGER},
      cost_center_id = #{costCenterId,jdbcType=VARCHAR},
      cost_center_type = #{costCenterType,jdbcType=INTEGER},
      exceed_buy_type = #{exceedBuyType,jdbcType=INTEGER},
      personal_pay = #{personalPay,jdbcType=BIT}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>