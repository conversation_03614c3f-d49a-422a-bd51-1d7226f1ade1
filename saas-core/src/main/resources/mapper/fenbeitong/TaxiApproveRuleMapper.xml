<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.TaxiApproveRuleMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.TaxiApproveRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="allowed_taxi_type" jdbcType="VARCHAR" property="allowedTaxiType" />
    <result column="price_limit_flag" jdbcType="INTEGER" property="priceLimitFlag" />
    <result column="price_limit" jdbcType="NUMERIC" property="priceLimit" />
    <result column="day_price_limit" jdbcType="NUMERIC" property="dayPriceLimit" />
    <result column="taxi_scheduling_fee" jdbcType="INTEGER" property="taxiSchedulingFee" />
    <result column="allow_same_city" jdbcType="BIT" property="allowSameCity" />
    <result column="allow_called_forother" jdbcType="BIT" property="allowCalledForother" />
    <result column="times_limit_flag" jdbcType="INTEGER" property="timesLimitFlag" />
    <result column="times_limit" jdbcType="INTEGER" property="timesLimit" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="delete_status" jdbcType="INTEGER" property="deleteStatus" />
    <result column="use_rule_day_price_limit" jdbcType="INTEGER" property="useRuleDayPriceLimit" />
    <result column="use_personal_budget" jdbcType="INTEGER" property="usePersonalBudget" />
    <result column="is_ding" jdbcType="SMALLINT" property="isDing" />
    <result column="price_across_city_flag" jdbcType="INTEGER" property="priceAcrossCityFlag" />
    <result column="applicant_apply_max_times_limit" jdbcType="INTEGER" property="applicantApplyMaxTimesLimit" />
    <result column="day_price_limit_type" jdbcType="INTEGER" property="dayPriceLimitType" />
    <result column="rule_type" jdbcType="INTEGER" property="ruleType" />
    <result column="modify_flag" jdbcType="BIT" property="modifyFlag" />
    <result column="limit_time" jdbcType="BIT" property="limitTime" />
    <result column="limit_path" jdbcType="BIT" property="limitPath" />
    <result column="allow_call_for_other_subsidiary" jdbcType="BIT" property="allowCallForOtherSubsidiary" />
    <result column="city_limit_flag" jdbcType="INTEGER" property="cityLimitFlag" />
    <result column="allow_same_city_type" jdbcType="INTEGER" property="allowSameCityType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, name, allowed_taxi_type, price_limit_flag, price_limit, day_price_limit,
    taxi_scheduling_fee, allow_same_city, allow_called_forother, times_limit_flag, times_limit,
    create_user, create_time, update_user, update_time, delete_status, use_rule_day_price_limit,
    use_personal_budget, is_ding, price_across_city_flag, applicant_apply_max_times_limit,
    day_price_limit_type, rule_type, modify_flag, limit_time, limit_path, allow_call_for_other_subsidiary,
    city_limit_flag, allow_same_city_type
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiApproveRuleExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from taxi_approve_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${limit} offset ${offset}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from taxi_approve_rule
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from taxi_approve_rule
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiApproveRuleExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from taxi_approve_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiApproveRule" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into taxi_approve_rule (company_id, name, allowed_taxi_type,
    price_limit_flag, price_limit, day_price_limit,
    taxi_scheduling_fee, allow_same_city, allow_called_forother,
    times_limit_flag, times_limit, create_user,
    create_time, update_user, update_time,
    delete_status, use_rule_day_price_limit, use_personal_budget,
    is_ding, price_across_city_flag, applicant_apply_max_times_limit,
    day_price_limit_type, rule_type, modify_flag,
    limit_time, limit_path, allow_call_for_other_subsidiary,
    city_limit_flag, allow_same_city_type)
    values (#{companyId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{allowedTaxiType,jdbcType=VARCHAR},
    #{priceLimitFlag,jdbcType=INTEGER}, #{priceLimit,jdbcType=NUMERIC}, #{dayPriceLimit,jdbcType=NUMERIC},
    #{taxiSchedulingFee,jdbcType=INTEGER}, #{allowSameCity,jdbcType=BIT}, #{allowCalledForother,jdbcType=BIT},
    #{timesLimitFlag,jdbcType=INTEGER}, #{timesLimit,jdbcType=INTEGER}, #{createUser,jdbcType=VARCHAR},
    #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
    #{deleteStatus,jdbcType=INTEGER}, #{useRuleDayPriceLimit,jdbcType=INTEGER}, #{usePersonalBudget,jdbcType=INTEGER},
    #{isDing,jdbcType=SMALLINT}, #{priceAcrossCityFlag,jdbcType=INTEGER}, #{applicantApplyMaxTimesLimit,jdbcType=INTEGER},
    #{dayPriceLimitType,jdbcType=INTEGER}, #{ruleType,jdbcType=INTEGER}, #{modifyFlag,jdbcType=BIT},
    #{limitTime,jdbcType=BIT}, #{limitPath,jdbcType=BIT}, #{allowCallForOtherSubsidiary,jdbcType=BIT},
    #{cityLimitFlag,jdbcType=INTEGER}, #{allowSameCityType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiApproveRule" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into taxi_approve_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="allowedTaxiType != null">
        allowed_taxi_type,
      </if>
      <if test="priceLimitFlag != null">
        price_limit_flag,
      </if>
      <if test="priceLimit != null">
        price_limit,
      </if>
      <if test="dayPriceLimit != null">
        day_price_limit,
      </if>
      <if test="taxiSchedulingFee != null">
        taxi_scheduling_fee,
      </if>
      <if test="allowSameCity != null">
        allow_same_city,
      </if>
      <if test="allowCalledForother != null">
        allow_called_forother,
      </if>
      <if test="timesLimitFlag != null">
        times_limit_flag,
      </if>
      <if test="timesLimit != null">
        times_limit,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleteStatus != null">
        delete_status,
      </if>
      <if test="useRuleDayPriceLimit != null">
        use_rule_day_price_limit,
      </if>
      <if test="usePersonalBudget != null">
        use_personal_budget,
      </if>
      <if test="isDing != null">
        is_ding,
      </if>
      <if test="priceAcrossCityFlag != null">
        price_across_city_flag,
      </if>
      <if test="applicantApplyMaxTimesLimit != null">
        applicant_apply_max_times_limit,
      </if>
      <if test="dayPriceLimitType != null">
        day_price_limit_type,
      </if>
      <if test="ruleType != null">
        rule_type,
      </if>
      <if test="modifyFlag != null">
        modify_flag,
      </if>
      <if test="limitTime != null">
        limit_time,
      </if>
      <if test="limitPath != null">
        limit_path,
      </if>
      <if test="allowCallForOtherSubsidiary != null">
        allow_call_for_other_subsidiary,
      </if>
      <if test="cityLimitFlag != null">
        city_limit_flag,
      </if>
      <if test="allowSameCityType != null">
        allow_same_city_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="allowedTaxiType != null">
        #{allowedTaxiType,jdbcType=VARCHAR},
      </if>
      <if test="priceLimitFlag != null">
        #{priceLimitFlag,jdbcType=INTEGER},
      </if>
      <if test="priceLimit != null">
        #{priceLimit,jdbcType=NUMERIC},
      </if>
      <if test="dayPriceLimit != null">
        #{dayPriceLimit,jdbcType=NUMERIC},
      </if>
      <if test="taxiSchedulingFee != null">
        #{taxiSchedulingFee,jdbcType=INTEGER},
      </if>
      <if test="allowSameCity != null">
        #{allowSameCity,jdbcType=BIT},
      </if>
      <if test="allowCalledForother != null">
        #{allowCalledForother,jdbcType=BIT},
      </if>
      <if test="timesLimitFlag != null">
        #{timesLimitFlag,jdbcType=INTEGER},
      </if>
      <if test="timesLimit != null">
        #{timesLimit,jdbcType=INTEGER},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleteStatus != null">
        #{deleteStatus,jdbcType=INTEGER},
      </if>
      <if test="useRuleDayPriceLimit != null">
        #{useRuleDayPriceLimit,jdbcType=INTEGER},
      </if>
      <if test="usePersonalBudget != null">
        #{usePersonalBudget,jdbcType=INTEGER},
      </if>
      <if test="isDing != null">
        #{isDing,jdbcType=SMALLINT},
      </if>
      <if test="priceAcrossCityFlag != null">
        #{priceAcrossCityFlag,jdbcType=INTEGER},
      </if>
      <if test="applicantApplyMaxTimesLimit != null">
        #{applicantApplyMaxTimesLimit,jdbcType=INTEGER},
      </if>
      <if test="dayPriceLimitType != null">
        #{dayPriceLimitType,jdbcType=INTEGER},
      </if>
      <if test="ruleType != null">
        #{ruleType,jdbcType=INTEGER},
      </if>
      <if test="modifyFlag != null">
        #{modifyFlag,jdbcType=BIT},
      </if>
      <if test="limitTime != null">
        #{limitTime,jdbcType=BIT},
      </if>
      <if test="limitPath != null">
        #{limitPath,jdbcType=BIT},
      </if>
      <if test="allowCallForOtherSubsidiary != null">
        #{allowCallForOtherSubsidiary,jdbcType=BIT},
      </if>
      <if test="cityLimitFlag != null">
        #{cityLimitFlag,jdbcType=INTEGER},
      </if>
      <if test="allowSameCityType != null">
        #{allowSameCityType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiApproveRuleExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from taxi_approve_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update taxi_approve_rule
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.allowedTaxiType != null">
        allowed_taxi_type = #{record.allowedTaxiType,jdbcType=VARCHAR},
      </if>
      <if test="record.priceLimitFlag != null">
        price_limit_flag = #{record.priceLimitFlag,jdbcType=INTEGER},
      </if>
      <if test="record.priceLimit != null">
        price_limit = #{record.priceLimit,jdbcType=NUMERIC},
      </if>
      <if test="record.dayPriceLimit != null">
        day_price_limit = #{record.dayPriceLimit,jdbcType=NUMERIC},
      </if>
      <if test="record.taxiSchedulingFee != null">
        taxi_scheduling_fee = #{record.taxiSchedulingFee,jdbcType=INTEGER},
      </if>
      <if test="record.allowSameCity != null">
        allow_same_city = #{record.allowSameCity,jdbcType=BIT},
      </if>
      <if test="record.allowCalledForother != null">
        allow_called_forother = #{record.allowCalledForother,jdbcType=BIT},
      </if>
      <if test="record.timesLimitFlag != null">
        times_limit_flag = #{record.timesLimitFlag,jdbcType=INTEGER},
      </if>
      <if test="record.timesLimit != null">
        times_limit = #{record.timesLimit,jdbcType=INTEGER},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleteStatus != null">
        delete_status = #{record.deleteStatus,jdbcType=INTEGER},
      </if>
      <if test="record.useRuleDayPriceLimit != null">
        use_rule_day_price_limit = #{record.useRuleDayPriceLimit,jdbcType=INTEGER},
      </if>
      <if test="record.usePersonalBudget != null">
        use_personal_budget = #{record.usePersonalBudget,jdbcType=INTEGER},
      </if>
      <if test="record.isDing != null">
        is_ding = #{record.isDing,jdbcType=SMALLINT},
      </if>
      <if test="record.priceAcrossCityFlag != null">
        price_across_city_flag = #{record.priceAcrossCityFlag,jdbcType=INTEGER},
      </if>
      <if test="record.applicantApplyMaxTimesLimit != null">
        applicant_apply_max_times_limit = #{record.applicantApplyMaxTimesLimit,jdbcType=INTEGER},
      </if>
      <if test="record.dayPriceLimitType != null">
        day_price_limit_type = #{record.dayPriceLimitType,jdbcType=INTEGER},
      </if>
      <if test="record.ruleType != null">
        rule_type = #{record.ruleType,jdbcType=INTEGER},
      </if>
      <if test="record.modifyFlag != null">
        modify_flag = #{record.modifyFlag,jdbcType=BIT},
      </if>
      <if test="record.limitTime != null">
        limit_time = #{record.limitTime,jdbcType=BIT},
      </if>
      <if test="record.limitPath != null">
        limit_path = #{record.limitPath,jdbcType=BIT},
      </if>
      <if test="record.allowCallForOtherSubsidiary != null">
        allow_call_for_other_subsidiary = #{record.allowCallForOtherSubsidiary,jdbcType=BIT},
      </if>
      <if test="record.cityLimitFlag != null">
        city_limit_flag = #{record.cityLimitFlag,jdbcType=INTEGER},
      </if>
      <if test="record.allowSameCityType != null">
        allow_same_city_type = #{record.allowSameCityType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update taxi_approve_rule
    set id = #{record.id,jdbcType=INTEGER},
    company_id = #{record.companyId,jdbcType=VARCHAR},
    name = #{record.name,jdbcType=VARCHAR},
    allowed_taxi_type = #{record.allowedTaxiType,jdbcType=VARCHAR},
    price_limit_flag = #{record.priceLimitFlag,jdbcType=INTEGER},
    price_limit = #{record.priceLimit,jdbcType=NUMERIC},
    day_price_limit = #{record.dayPriceLimit,jdbcType=NUMERIC},
    taxi_scheduling_fee = #{record.taxiSchedulingFee,jdbcType=INTEGER},
    allow_same_city = #{record.allowSameCity,jdbcType=BIT},
    allow_called_forother = #{record.allowCalledForother,jdbcType=BIT},
    times_limit_flag = #{record.timesLimitFlag,jdbcType=INTEGER},
    times_limit = #{record.timesLimit,jdbcType=INTEGER},
    create_user = #{record.createUser,jdbcType=VARCHAR},
    create_time = #{record.createTime,jdbcType=TIMESTAMP},
    update_user = #{record.updateUser,jdbcType=VARCHAR},
    update_time = #{record.updateTime,jdbcType=TIMESTAMP},
    delete_status = #{record.deleteStatus,jdbcType=INTEGER},
    use_rule_day_price_limit = #{record.useRuleDayPriceLimit,jdbcType=INTEGER},
    use_personal_budget = #{record.usePersonalBudget,jdbcType=INTEGER},
    is_ding = #{record.isDing,jdbcType=SMALLINT},
    price_across_city_flag = #{record.priceAcrossCityFlag,jdbcType=INTEGER},
    applicant_apply_max_times_limit = #{record.applicantApplyMaxTimesLimit,jdbcType=INTEGER},
    day_price_limit_type = #{record.dayPriceLimitType,jdbcType=INTEGER},
    rule_type = #{record.ruleType,jdbcType=INTEGER},
    modify_flag = #{record.modifyFlag,jdbcType=BIT},
    limit_time = #{record.limitTime,jdbcType=BIT},
    limit_path = #{record.limitPath,jdbcType=BIT},
    allow_call_for_other_subsidiary = #{record.allowCallForOtherSubsidiary,jdbcType=BIT},
    city_limit_flag = #{record.cityLimitFlag,jdbcType=INTEGER},
    allow_same_city_type = #{record.allowSameCityType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiApproveRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update taxi_approve_rule
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="allowedTaxiType != null">
        allowed_taxi_type = #{allowedTaxiType,jdbcType=VARCHAR},
      </if>
      <if test="priceLimitFlag != null">
        price_limit_flag = #{priceLimitFlag,jdbcType=INTEGER},
      </if>
      <if test="priceLimit != null">
        price_limit = #{priceLimit,jdbcType=NUMERIC},
      </if>
      <if test="dayPriceLimit != null">
        day_price_limit = #{dayPriceLimit,jdbcType=NUMERIC},
      </if>
      <if test="taxiSchedulingFee != null">
        taxi_scheduling_fee = #{taxiSchedulingFee,jdbcType=INTEGER},
      </if>
      <if test="allowSameCity != null">
        allow_same_city = #{allowSameCity,jdbcType=BIT},
      </if>
      <if test="allowCalledForother != null">
        allow_called_forother = #{allowCalledForother,jdbcType=BIT},
      </if>
      <if test="timesLimitFlag != null">
        times_limit_flag = #{timesLimitFlag,jdbcType=INTEGER},
      </if>
      <if test="timesLimit != null">
        times_limit = #{timesLimit,jdbcType=INTEGER},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleteStatus != null">
        delete_status = #{deleteStatus,jdbcType=INTEGER},
      </if>
      <if test="useRuleDayPriceLimit != null">
        use_rule_day_price_limit = #{useRuleDayPriceLimit,jdbcType=INTEGER},
      </if>
      <if test="usePersonalBudget != null">
        use_personal_budget = #{usePersonalBudget,jdbcType=INTEGER},
      </if>
      <if test="isDing != null">
        is_ding = #{isDing,jdbcType=SMALLINT},
      </if>
      <if test="priceAcrossCityFlag != null">
        price_across_city_flag = #{priceAcrossCityFlag,jdbcType=INTEGER},
      </if>
      <if test="applicantApplyMaxTimesLimit != null">
        applicant_apply_max_times_limit = #{applicantApplyMaxTimesLimit,jdbcType=INTEGER},
      </if>
      <if test="dayPriceLimitType != null">
        day_price_limit_type = #{dayPriceLimitType,jdbcType=INTEGER},
      </if>
      <if test="ruleType != null">
        rule_type = #{ruleType,jdbcType=INTEGER},
      </if>
      <if test="modifyFlag != null">
        modify_flag = #{modifyFlag,jdbcType=BIT},
      </if>
      <if test="limitTime != null">
        limit_time = #{limitTime,jdbcType=BIT},
      </if>
      <if test="limitPath != null">
        limit_path = #{limitPath,jdbcType=BIT},
      </if>
      <if test="allowCallForOtherSubsidiary != null">
        allow_call_for_other_subsidiary = #{allowCallForOtherSubsidiary,jdbcType=BIT},
      </if>
      <if test="cityLimitFlag != null">
        city_limit_flag = #{cityLimitFlag,jdbcType=INTEGER},
      </if>
      <if test="allowSameCityType != null">
        allow_same_city_type = #{allowSameCityType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiApproveRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update taxi_approve_rule
    set company_id = #{companyId,jdbcType=VARCHAR},
    name = #{name,jdbcType=VARCHAR},
    allowed_taxi_type = #{allowedTaxiType,jdbcType=VARCHAR},
    price_limit_flag = #{priceLimitFlag,jdbcType=INTEGER},
    price_limit = #{priceLimit,jdbcType=NUMERIC},
    day_price_limit = #{dayPriceLimit,jdbcType=NUMERIC},
    taxi_scheduling_fee = #{taxiSchedulingFee,jdbcType=INTEGER},
    allow_same_city = #{allowSameCity,jdbcType=BIT},
    allow_called_forother = #{allowCalledForother,jdbcType=BIT},
    times_limit_flag = #{timesLimitFlag,jdbcType=INTEGER},
    times_limit = #{timesLimit,jdbcType=INTEGER},
    create_user = #{createUser,jdbcType=VARCHAR},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    update_user = #{updateUser,jdbcType=VARCHAR},
    update_time = #{updateTime,jdbcType=TIMESTAMP},
    delete_status = #{deleteStatus,jdbcType=INTEGER},
    use_rule_day_price_limit = #{useRuleDayPriceLimit,jdbcType=INTEGER},
    use_personal_budget = #{usePersonalBudget,jdbcType=INTEGER},
    is_ding = #{isDing,jdbcType=SMALLINT},
    price_across_city_flag = #{priceAcrossCityFlag,jdbcType=INTEGER},
    applicant_apply_max_times_limit = #{applicantApplyMaxTimesLimit,jdbcType=INTEGER},
    day_price_limit_type = #{dayPriceLimitType,jdbcType=INTEGER},
    rule_type = #{ruleType,jdbcType=INTEGER},
    modify_flag = #{modifyFlag,jdbcType=BIT},
    limit_time = #{limitTime,jdbcType=BIT},
    limit_path = #{limitPath,jdbcType=BIT},
    allow_call_for_other_subsidiary = #{allowCallForOtherSubsidiary,jdbcType=BIT},
    city_limit_flag = #{cityLimitFlag,jdbcType=INTEGER},
    allow_same_city_type = #{allowSameCityType,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>
