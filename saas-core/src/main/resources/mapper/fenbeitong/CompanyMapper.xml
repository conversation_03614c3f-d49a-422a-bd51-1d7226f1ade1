<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.CompanyMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.Company">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 23 21:17:19 CST 2017.
    -->
    <constructor>
      <idArg column="id" javaType="java.lang.String" jdbcType="CHAR" />
      <arg column="name" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="certificate_num" javaType="java.lang.String" jdbcType="CHAR" />
      <arg column="status" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="alias" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="office_address" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="super_admin_id" javaType="java.lang.String" jdbcType="CHAR" />
      <arg column="account_manager_id" javaType="java.lang.String" jdbcType="CHAR" />
      <arg column="remark" javaType="java.lang.String" jdbcType="VARCHAR" />
    </constructor>
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 23 21:17:19 CST 2017.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 23 21:17:19 CST 2017.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 23 21:17:19 CST 2017.
    -->
    id, name, certificate_num, status, alias, office_address, super_admin_id, account_manager_id, 
    remark
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.CompanyExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 23 21:17:19 CST 2017.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from company
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.CompanyExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 23 21:17:19 CST 2017.
    -->
    delete from company
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.Company">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 23 21:17:19 CST 2017.
    -->
    insert into company
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="certificateNum != null">
        certificate_num,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="alias != null">
        alias,
      </if>
      <if test="officeAddress != null">
        office_address,
      </if>
      <if test="superAdminId != null">
        super_admin_id,
      </if>
      <if test="accountManagerId != null">
        account_manager_id,
      </if>
      <if test="remark != null">
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="certificateNum != null">
        #{certificateNum,jdbcType=CHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="alias != null">
        #{alias,jdbcType=VARCHAR},
      </if>
      <if test="officeAddress != null">
        #{officeAddress,jdbcType=VARCHAR},
      </if>
      <if test="superAdminId != null">
        #{superAdminId,jdbcType=CHAR},
      </if>
      <if test="accountManagerId != null">
        #{accountManagerId,jdbcType=CHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.CompanyExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 23 21:17:19 CST 2017.
    -->
    select count(*) from company
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 23 21:17:19 CST 2017.
    -->
    update company
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.certificateNum != null">
        certificate_num = #{record.certificateNum,jdbcType=CHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.alias != null">
        alias = #{record.alias,jdbcType=VARCHAR},
      </if>
      <if test="record.officeAddress != null">
        office_address = #{record.officeAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.superAdminId != null">
        super_admin_id = #{record.superAdminId,jdbcType=CHAR},
      </if>
      <if test="record.accountManagerId != null">
        account_manager_id = #{record.accountManagerId,jdbcType=CHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 23 21:17:19 CST 2017.
    -->
    update company
    set id = #{record.id,jdbcType=CHAR},
      name = #{record.name,jdbcType=VARCHAR},
      certificate_num = #{record.certificateNum,jdbcType=CHAR},
      status = #{record.status,jdbcType=INTEGER},
      alias = #{record.alias,jdbcType=VARCHAR},
      office_address = #{record.officeAddress,jdbcType=VARCHAR},
      super_admin_id = #{record.superAdminId,jdbcType=CHAR},
      account_manager_id = #{record.accountManagerId,jdbcType=CHAR},
      remark = #{record.remark,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.Company">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 23 21:17:19 CST 2017.
    -->
    update company
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="certificateNum != null">
        certificate_num = #{certificateNum,jdbcType=CHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="alias != null">
        alias = #{alias,jdbcType=VARCHAR},
      </if>
      <if test="officeAddress != null">
        office_address = #{officeAddress,jdbcType=VARCHAR},
      </if>
      <if test="superAdminId != null">
        super_admin_id = #{superAdminId,jdbcType=CHAR},
      </if>
      <if test="accountManagerId != null">
        account_manager_id = #{accountManagerId,jdbcType=CHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>