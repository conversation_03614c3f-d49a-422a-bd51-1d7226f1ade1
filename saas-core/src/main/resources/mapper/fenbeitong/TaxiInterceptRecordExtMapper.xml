<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.TaxiInterceptRecordExtMapper">

  <insert id="batchInsert" parameterType="java.util.List">
    insert into taxi_intercept_record (id, employee_id, company_id,
    create_time, contact_name, contact_phone,
    channel, taxi_rule, taxi_rule_flag,
    exceed_buy_flag, limit_time, taxi_time_range_info,
    limit_departure, departure_location_info, limit_arrival,
    arrival_location_info, limit_taxi_type, allowed_taxi_type,
    schedule_type, schedule_time, rule,
    require_level, estimate_price, city,
    departure_lng, departure_lat, departure_name,
    departure_addr, departure_time, arrival_lng,
    arrival_lat, arrival_name, arrival_addr,
    client_lng, client_lat, err_msg,
    err_code, limit_price, limit_price_info,
    cost_center_id, cost_center_type, flt,
    flight_date, flight_delay_time, air_code,
    allow_shuttle, exceed_buy_type)
    values
    <foreach collection="list" item="item" index="index" open="" close="" separator=",">
      (#{item.id,jdbcType=CHAR}, #{item.employeeId,jdbcType=CHAR}, #{item.companyId,jdbcType=CHAR},
      #{item.createTime,jdbcType=TIMESTAMP}, #{item.contactName,jdbcType=VARCHAR}, #{item.contactPhone,jdbcType=VARCHAR},
      #{item.channel,jdbcType=INTEGER}, #{item.taxiRule,jdbcType=INTEGER}, #{item.taxiRuleFlag,jdbcType=BIT},
      #{item.exceedBuyFlag,jdbcType=BIT}, #{item.limitTime,jdbcType=BIT}, #{item.taxiTimeRangeInfo,jdbcType=VARCHAR},
      #{item.limitDeparture,jdbcType=BIT}, #{item.departureLocationInfo,jdbcType=VARCHAR}, #{item.limitArrival,jdbcType=BIT},
      #{item.arrivalLocationInfo,jdbcType=VARCHAR}, #{item.limitTaxiType,jdbcType=BIT}, #{item.allowedTaxiType,jdbcType=VARCHAR},
      #{item.scheduleType,jdbcType=INTEGER}, #{item.scheduleTime,jdbcType=TIMESTAMP}, #{item.rule,jdbcType=INTEGER},
      #{item.requireLevel,jdbcType=INTEGER}, #{item.estimatePrice,jdbcType=NUMERIC}, #{item.city,jdbcType=VARCHAR},
      #{item.departureLng,jdbcType=VARCHAR}, #{item.departureLat,jdbcType=VARCHAR}, #{item.departureName,jdbcType=VARCHAR},
      #{item.departureAddr,jdbcType=VARCHAR}, #{item.departureTime,jdbcType=TIMESTAMP}, #{item.arrivalLng,jdbcType=VARCHAR},
      #{item.arrivalLat,jdbcType=VARCHAR}, #{item.arrivalName,jdbcType=VARCHAR}, #{item.arrivalAddr,jdbcType=VARCHAR},
      #{item.clientLng,jdbcType=VARCHAR}, #{item.clientLat,jdbcType=VARCHAR}, #{item.errMsg,jdbcType=VARCHAR},
      #{item.errCode,jdbcType=INTEGER}, #{item.limitPrice,jdbcType=BIT}, #{item.limitPriceInfo,jdbcType=NUMERIC},
      #{item.costCenterId,jdbcType=VARCHAR}, #{item.costCenterType,jdbcType=INTEGER}, #{item.flt,jdbcType=VARCHAR},
      #{item.flightDate,jdbcType=VARCHAR}, #{item.flightDelayTime,jdbcType=INTEGER}, #{item.airCode,jdbcType=VARCHAR},
      #{item.allowShuttle,jdbcType=BIT}, #{item.exceedBuyType,jdbcType=INTEGER})
    </foreach>
  </insert>
</mapper>