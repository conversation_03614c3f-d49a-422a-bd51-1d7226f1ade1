<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.OrgUnitEmployeeMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.OrgUnitEmployee">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 24 14:25:21 CST 2017.
    -->
    <id column="origin_id" jdbcType="VARCHAR" property="originId" />
    <id column="entity_id" jdbcType="VARCHAR" property="entityId" />
    <id column="entity_type" jdbcType="INTEGER" property="entityType" />
    <id column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="ref_count" jdbcType="INTEGER" property="refCount" />
    <result column="is_own" jdbcType="BIT" property="isOwn" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 24 14:25:21 CST 2017.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 24 14:25:21 CST 2017.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 24 14:25:21 CST 2017.
    -->
    origin_id, entity_id, entity_type, company_id, ref_count, is_own
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.OrgUnitEmployeeExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 24 14:25:21 CST 2017.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from org_unit_employee
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.OrgUnitEmployeeExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 24 14:25:21 CST 2017.
    -->
    delete from org_unit_employee
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.OrgUnitEmployee">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 24 14:25:21 CST 2017.
    -->
    insert into org_unit_employee
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="originId != null">
        origin_id,
      </if>
      <if test="entityId != null">
        entity_id,
      </if>
      <if test="entityType != null">
        entity_type,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="refCount != null">
        ref_count,
      </if>
      <if test="isOwn != null">
        is_own,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="originId != null">
        #{originId,jdbcType=VARCHAR},
      </if>
      <if test="entityId != null">
        #{entityId,jdbcType=VARCHAR},
      </if>
      <if test="entityType != null">
        #{entityType,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="refCount != null">
        #{refCount,jdbcType=INTEGER},
      </if>
      <if test="isOwn != null">
        #{isOwn,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.OrgUnitEmployeeExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 24 14:25:21 CST 2017.
    -->
    select count(*) from org_unit_employee
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 24 14:25:21 CST 2017.
    -->
    update org_unit_employee
    <set>
      <if test="record.originId != null">
        origin_id = #{record.originId,jdbcType=VARCHAR},
      </if>
      <if test="record.entityId != null">
        entity_id = #{record.entityId,jdbcType=VARCHAR},
      </if>
      <if test="record.entityType != null">
        entity_type = #{record.entityType,jdbcType=INTEGER},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.refCount != null">
        ref_count = #{record.refCount,jdbcType=INTEGER},
      </if>
      <if test="record.isOwn != null">
        is_own = #{record.isOwn,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 24 14:25:21 CST 2017.
    -->
    update org_unit_employee
    set origin_id = #{record.originId,jdbcType=VARCHAR},
      entity_id = #{record.entityId,jdbcType=VARCHAR},
      entity_type = #{record.entityType,jdbcType=INTEGER},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      ref_count = #{record.refCount,jdbcType=INTEGER},
      is_own = #{record.isOwn,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.OrgUnitEmployee">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Apr 24 14:25:21 CST 2017.
    -->
    update org_unit_employee
    <set>
      <if test="refCount != null">
        ref_count = #{refCount,jdbcType=INTEGER},
      </if>
      <if test="isOwn != null">
        is_own = #{isOwn,jdbcType=BIT},
      </if>
    </set>
    where origin_id = #{originId,jdbcType=VARCHAR}
      and entity_id = #{entityId,jdbcType=VARCHAR}
      and entity_type = #{entityType,jdbcType=INTEGER}
      and company_id = #{companyId,jdbcType=VARCHAR}
  </update>
</mapper>