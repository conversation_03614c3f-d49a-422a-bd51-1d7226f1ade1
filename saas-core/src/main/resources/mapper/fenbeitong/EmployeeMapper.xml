<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.EmployeeMapper">
    <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.Employee">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Sat Apr 22 09:41:15 CST 2017.
        -->
        <id column="id" jdbcType="CHAR" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="first_name" jdbcType="VARCHAR" property="firstName"/>
        <result column="last_name" jdbcType="VARCHAR" property="lastName"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="phone_num" jdbcType="VARCHAR" property="phoneNum"/>
        <result column="id_number" jdbcType="VARCHAR" property="idNumber"/>
        <result column="id_type" jdbcType="INTEGER" property="idType"/>
        <result column="avatar_url" jdbcType="VARCHAR" property="avatarUrl"/>
        <result column="role" jdbcType="INTEGER" property="role"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="fixed_phone" jdbcType="VARCHAR" property="fixedPhone"/>
        <result column="birth_date" jdbcType="TIMESTAMP" property="birthDate"/>
        <result column="gender" jdbcType="INTEGER" property="gender"/>
        <result column="pinyin" jdbcType="VARCHAR" property="pinyin"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Sat Apr 22 09:41:15 CST 2017.
        -->
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Sat Apr 22 09:41:15 CST 2017.
        -->
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Sat Apr 22 09:41:15 CST 2017.
        -->
        id, name, first_name, last_name, email, phone_num, id_number, id_type, avatar_url,
        role, status, modify_time, fixed_phone, birth_date, gender, pinyin
    </sql>
    <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.EmployeeExample"
            resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Sat Apr 22 09:41:15 CST 2017.
        -->
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from employee
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.EmployeeExample">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Sat Apr 22 09:41:15 CST 2017.
        -->
        delete from employee
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.Employee">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Sat Apr 22 09:41:15 CST 2017.
        -->
        insert into employee
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="firstName != null">
                first_name,
            </if>
            <if test="lastName != null">
                last_name,
            </if>
            <if test="email != null">
                email,
            </if>
            <if test="phoneNum != null">
                phone_num,
            </if>
            <if test="idNumber != null">
                id_number,
            </if>
            <if test="idType != null">
                id_type,
            </if>
            <if test="avatarUrl != null">
                avatar_url,
            </if>
            <if test="role != null">
                role,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="fixedPhone != null">
                fixed_phone,
            </if>
            <if test="birthDate != null">
                birth_date,
            </if>
            <if test="gender != null">
                gender,
            </if>
            <if test="pinyin != null">
                pinyin,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=CHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="firstName != null">
                #{firstName,jdbcType=VARCHAR},
            </if>
            <if test="lastName != null">
                #{lastName,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="phoneNum != null">
                #{phoneNum,jdbcType=VARCHAR},
            </if>
            <if test="idNumber != null">
                #{idNumber,jdbcType=VARCHAR},
            </if>
            <if test="idType != null">
                #{idType,jdbcType=INTEGER},
            </if>
            <if test="avatarUrl != null">
                #{avatarUrl,jdbcType=VARCHAR},
            </if>
            <if test="role != null">
                #{role,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="fixedPhone != null">
                #{fixedPhone,jdbcType=VARCHAR},
            </if>
            <if test="birthDate != null">
                #{birthDate,jdbcType=TIMESTAMP},
            </if>
            <if test="gender != null">
                #{gender,jdbcType=INTEGER},
            </if>
            <if test="pinyin != null">
                #{pinyin,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.EmployeeExample"
            resultType="java.lang.Long">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Sat Apr 22 09:41:15 CST 2017.
        -->
        select count(*) from employee
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Sat Apr 22 09:41:15 CST 2017.
        -->
        update employee
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=CHAR},
            </if>
            <if test="record.name != null">
                name = #{record.name,jdbcType=VARCHAR},
            </if>
            <if test="record.firstName != null">
                first_name = #{record.firstName,jdbcType=VARCHAR},
            </if>
            <if test="record.lastName != null">
                last_name = #{record.lastName,jdbcType=VARCHAR},
            </if>
            <if test="record.email != null">
                email = #{record.email,jdbcType=VARCHAR},
            </if>
            <if test="record.phoneNum != null">
                phone_num = #{record.phoneNum,jdbcType=VARCHAR},
            </if>
            <if test="record.idNumber != null">
                id_number = #{record.idNumber,jdbcType=VARCHAR},
            </if>
            <if test="record.idType != null">
                id_type = #{record.idType,jdbcType=INTEGER},
            </if>
            <if test="record.avatarUrl != null">
                avatar_url = #{record.avatarUrl,jdbcType=VARCHAR},
            </if>
            <if test="record.role != null">
                role = #{record.role,jdbcType=INTEGER},
            </if>
            <if test="record.status != null">
                status = #{record.status,jdbcType=INTEGER},
            </if>
            <if test="record.modifyTime != null">
                modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.fixedPhone != null">
                fixed_phone = #{record.fixedPhone,jdbcType=VARCHAR},
            </if>
            <if test="record.birthDate != null">
                birth_date = #{record.birthDate,jdbcType=TIMESTAMP},
            </if>
            <if test="record.gender != null">
                gender = #{record.gender,jdbcType=INTEGER},
            </if>
            <if test="record.pinyin != null">
                pinyin = #{record.pinyin,jdbcType=VARCHAR},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Sat Apr 22 09:41:15 CST 2017.
        -->
        update employee
        set id = #{record.id,jdbcType=CHAR},
        name = #{record.name,jdbcType=VARCHAR},
        first_name = #{record.firstName,jdbcType=VARCHAR},
        last_name = #{record.lastName,jdbcType=VARCHAR},
        email = #{record.email,jdbcType=VARCHAR},
        phone_num = #{record.phoneNum,jdbcType=VARCHAR},
        id_number = #{record.idNumber,jdbcType=VARCHAR},
        id_type = #{record.idType,jdbcType=INTEGER},
        avatar_url = #{record.avatarUrl,jdbcType=VARCHAR},
        role = #{record.role,jdbcType=INTEGER},
        status = #{record.status,jdbcType=INTEGER},
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
        fixed_phone = #{record.fixedPhone,jdbcType=VARCHAR},
        birth_date = #{record.birthDate,jdbcType=TIMESTAMP},
        gender = #{record.gender,jdbcType=INTEGER},
        pinyin = #{record.pinyin,jdbcType=VARCHAR}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.Employee">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Sat Apr 22 09:41:15 CST 2017.
        -->
        update employee
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="firstName != null">
                first_name = #{firstName,jdbcType=VARCHAR},
            </if>
            <if test="lastName != null">
                last_name = #{lastName,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="phoneNum != null">
                phone_num = #{phoneNum,jdbcType=VARCHAR},
            </if>
            <if test="idNumber != null">
                id_number = #{idNumber,jdbcType=VARCHAR},
            </if>
            <if test="idType != null">
                id_type = #{idType,jdbcType=INTEGER},
            </if>
            <if test="avatarUrl != null">
                avatar_url = #{avatarUrl,jdbcType=VARCHAR},
            </if>
            <if test="role != null">
                role = #{role,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="fixedPhone != null">
                fixed_phone = #{fixedPhone,jdbcType=VARCHAR},
            </if>
            <if test="birthDate != null">
                birth_date = #{birthDate,jdbcType=TIMESTAMP},
            </if>
            <if test="gender != null">
                gender = #{gender,jdbcType=INTEGER},
            </if>
            <if test="pinyin != null">
                pinyin = #{pinyin,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=CHAR}
    </update>

    <select id="selectByExamplePage" parameterType="map" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Sat Apr 22 09:41:15 CST 2017.
        -->
        select
        <if test="example.distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from employee
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
        <if test="example.orderByClause != null">
            order by ${orderByClause}
        </if>
        limit #{pageSize} offset #{index}
    </select>
</mapper>