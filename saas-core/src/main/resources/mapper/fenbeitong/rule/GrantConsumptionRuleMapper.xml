<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.rule.GrantConsumptionRuleMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.rule.GrantConsumptionRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="sub_company_id" jdbcType="VARCHAR" property="subCompanyId" />
    <result column="rule_id" jdbcType="VARCHAR" property="ruleId" />
    <result column="auth_rule_id" jdbcType="VARCHAR" property="authRuleId" />
    <result column="rule_int_id" jdbcType="BIGINT" property="ruleIntId" />
    <result column="auth_rule_int_id" jdbcType="BIGINT" property="authRuleIntId" />
    <result column="scene_type" jdbcType="SMALLINT" property="sceneType" />
    <result column="scene_name" jdbcType="VARCHAR" property="sceneName" />
    <result column="setting_id" jdbcType="VARCHAR" property="settingId" />
    <result column="auth_setting_id" jdbcType="VARCHAR" property="authSettingId" />
    <result column="grant_status" jdbcType="SMALLINT" property="grantStatus" />
    <result column="grant_fail_reason_type" jdbcType="SMALLINT" property="grantFailReasonType" />
    <result column="rule_type" jdbcType="SMALLINT" property="ruleType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_del" jdbcType="SMALLINT" property="isDel" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, name, group_id, sub_company_id, rule_id, auth_rule_id, rule_int_id, auth_rule_int_id, 
    scene_type, scene_name, setting_id, auth_setting_id, grant_status, grant_fail_reason_type, 
    rule_type, create_time, update_time, is_del
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.rule.GrantConsumptionRuleExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from grant_consumption_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${limit} offset ${offset}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from grant_consumption_rule
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from grant_consumption_rule
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.rule.GrantConsumptionRuleExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from grant_consumption_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.fenbeitong.rule.GrantConsumptionRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into grant_consumption_rule (id, name, group_id, 
      sub_company_id, rule_id, auth_rule_id, 
      rule_int_id, auth_rule_int_id, scene_type, 
      scene_name, setting_id, auth_setting_id, 
      grant_status, grant_fail_reason_type, rule_type, 
      create_time, update_time, is_del
      )
    values (#{id,jdbcType=CHAR}, #{name,jdbcType=VARCHAR}, #{groupId,jdbcType=VARCHAR}, 
      #{subCompanyId,jdbcType=VARCHAR}, #{ruleId,jdbcType=VARCHAR}, #{authRuleId,jdbcType=VARCHAR}, 
      #{ruleIntId,jdbcType=BIGINT}, #{authRuleIntId,jdbcType=BIGINT}, #{sceneType,jdbcType=SMALLINT}, 
      #{sceneName,jdbcType=VARCHAR}, #{settingId,jdbcType=VARCHAR}, #{authSettingId,jdbcType=VARCHAR}, 
      #{grantStatus,jdbcType=SMALLINT}, #{grantFailReasonType,jdbcType=SMALLINT}, #{ruleType,jdbcType=SMALLINT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{isDel,jdbcType=SMALLINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.rule.GrantConsumptionRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into grant_consumption_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="subCompanyId != null">
        sub_company_id,
      </if>
      <if test="ruleId != null">
        rule_id,
      </if>
      <if test="authRuleId != null">
        auth_rule_id,
      </if>
      <if test="ruleIntId != null">
        rule_int_id,
      </if>
      <if test="authRuleIntId != null">
        auth_rule_int_id,
      </if>
      <if test="sceneType != null">
        scene_type,
      </if>
      <if test="sceneName != null">
        scene_name,
      </if>
      <if test="settingId != null">
        setting_id,
      </if>
      <if test="authSettingId != null">
        auth_setting_id,
      </if>
      <if test="grantStatus != null">
        grant_status,
      </if>
      <if test="grantFailReasonType != null">
        grant_fail_reason_type,
      </if>
      <if test="ruleType != null">
        rule_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDel != null">
        is_del,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="subCompanyId != null">
        #{subCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="ruleId != null">
        #{ruleId,jdbcType=VARCHAR},
      </if>
      <if test="authRuleId != null">
        #{authRuleId,jdbcType=VARCHAR},
      </if>
      <if test="ruleIntId != null">
        #{ruleIntId,jdbcType=BIGINT},
      </if>
      <if test="authRuleIntId != null">
        #{authRuleIntId,jdbcType=BIGINT},
      </if>
      <if test="sceneType != null">
        #{sceneType,jdbcType=SMALLINT},
      </if>
      <if test="sceneName != null">
        #{sceneName,jdbcType=VARCHAR},
      </if>
      <if test="settingId != null">
        #{settingId,jdbcType=VARCHAR},
      </if>
      <if test="authSettingId != null">
        #{authSettingId,jdbcType=VARCHAR},
      </if>
      <if test="grantStatus != null">
        #{grantStatus,jdbcType=SMALLINT},
      </if>
      <if test="grantFailReasonType != null">
        #{grantFailReasonType,jdbcType=SMALLINT},
      </if>
      <if test="ruleType != null">
        #{ruleType,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=SMALLINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.rule.GrantConsumptionRuleExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from grant_consumption_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update grant_consumption_rule
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.groupId != null">
        group_id = #{record.groupId,jdbcType=VARCHAR},
      </if>
      <if test="record.subCompanyId != null">
        sub_company_id = #{record.subCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleId != null">
        rule_id = #{record.ruleId,jdbcType=VARCHAR},
      </if>
      <if test="record.authRuleId != null">
        auth_rule_id = #{record.authRuleId,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleIntId != null">
        rule_int_id = #{record.ruleIntId,jdbcType=BIGINT},
      </if>
      <if test="record.authRuleIntId != null">
        auth_rule_int_id = #{record.authRuleIntId,jdbcType=BIGINT},
      </if>
      <if test="record.sceneType != null">
        scene_type = #{record.sceneType,jdbcType=SMALLINT},
      </if>
      <if test="record.sceneName != null">
        scene_name = #{record.sceneName,jdbcType=VARCHAR},
      </if>
      <if test="record.settingId != null">
        setting_id = #{record.settingId,jdbcType=VARCHAR},
      </if>
      <if test="record.authSettingId != null">
        auth_setting_id = #{record.authSettingId,jdbcType=VARCHAR},
      </if>
      <if test="record.grantStatus != null">
        grant_status = #{record.grantStatus,jdbcType=SMALLINT},
      </if>
      <if test="record.grantFailReasonType != null">
        grant_fail_reason_type = #{record.grantFailReasonType,jdbcType=SMALLINT},
      </if>
      <if test="record.ruleType != null">
        rule_type = #{record.ruleType,jdbcType=SMALLINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDel != null">
        is_del = #{record.isDel,jdbcType=SMALLINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update grant_consumption_rule
    set id = #{record.id,jdbcType=CHAR},
      name = #{record.name,jdbcType=VARCHAR},
      group_id = #{record.groupId,jdbcType=VARCHAR},
      sub_company_id = #{record.subCompanyId,jdbcType=VARCHAR},
      rule_id = #{record.ruleId,jdbcType=VARCHAR},
      auth_rule_id = #{record.authRuleId,jdbcType=VARCHAR},
      rule_int_id = #{record.ruleIntId,jdbcType=BIGINT},
      auth_rule_int_id = #{record.authRuleIntId,jdbcType=BIGINT},
      scene_type = #{record.sceneType,jdbcType=SMALLINT},
      scene_name = #{record.sceneName,jdbcType=VARCHAR},
      setting_id = #{record.settingId,jdbcType=VARCHAR},
      auth_setting_id = #{record.authSettingId,jdbcType=VARCHAR},
      grant_status = #{record.grantStatus,jdbcType=SMALLINT},
      grant_fail_reason_type = #{record.grantFailReasonType,jdbcType=SMALLINT},
      rule_type = #{record.ruleType,jdbcType=SMALLINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      is_del = #{record.isDel,jdbcType=SMALLINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.rule.GrantConsumptionRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update grant_consumption_rule
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="subCompanyId != null">
        sub_company_id = #{subCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="ruleId != null">
        rule_id = #{ruleId,jdbcType=VARCHAR},
      </if>
      <if test="authRuleId != null">
        auth_rule_id = #{authRuleId,jdbcType=VARCHAR},
      </if>
      <if test="ruleIntId != null">
        rule_int_id = #{ruleIntId,jdbcType=BIGINT},
      </if>
      <if test="authRuleIntId != null">
        auth_rule_int_id = #{authRuleIntId,jdbcType=BIGINT},
      </if>
      <if test="sceneType != null">
        scene_type = #{sceneType,jdbcType=SMALLINT},
      </if>
      <if test="sceneName != null">
        scene_name = #{sceneName,jdbcType=VARCHAR},
      </if>
      <if test="settingId != null">
        setting_id = #{settingId,jdbcType=VARCHAR},
      </if>
      <if test="authSettingId != null">
        auth_setting_id = #{authSettingId,jdbcType=VARCHAR},
      </if>
      <if test="grantStatus != null">
        grant_status = #{grantStatus,jdbcType=SMALLINT},
      </if>
      <if test="grantFailReasonType != null">
        grant_fail_reason_type = #{grantFailReasonType,jdbcType=SMALLINT},
      </if>
      <if test="ruleType != null">
        rule_type = #{ruleType,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDel != null">
        is_del = #{isDel,jdbcType=SMALLINT},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.fenbeitong.rule.GrantConsumptionRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update grant_consumption_rule
    set name = #{name,jdbcType=VARCHAR},
      group_id = #{groupId,jdbcType=VARCHAR},
      sub_company_id = #{subCompanyId,jdbcType=VARCHAR},
      rule_id = #{ruleId,jdbcType=VARCHAR},
      auth_rule_id = #{authRuleId,jdbcType=VARCHAR},
      rule_int_id = #{ruleIntId,jdbcType=BIGINT},
      auth_rule_int_id = #{authRuleIntId,jdbcType=BIGINT},
      scene_type = #{sceneType,jdbcType=SMALLINT},
      scene_name = #{sceneName,jdbcType=VARCHAR},
      setting_id = #{settingId,jdbcType=VARCHAR},
      auth_setting_id = #{authSettingId,jdbcType=VARCHAR},
      grant_status = #{grantStatus,jdbcType=SMALLINT},
      grant_fail_reason_type = #{grantFailReasonType,jdbcType=SMALLINT},
      rule_type = #{ruleType,jdbcType=SMALLINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_del = #{isDel,jdbcType=SMALLINT}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>