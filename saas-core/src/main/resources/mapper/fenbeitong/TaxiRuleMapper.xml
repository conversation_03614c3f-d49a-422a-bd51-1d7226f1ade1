<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.TaxiRuleMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.TaxiRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="company_id" jdbcType="CHAR" property="companyId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="limit_level" jdbcType="INTEGER" property="limitLevel" />
    <result column="limit_time" jdbcType="BIT" property="limitTime" />
    <result column="limit_departure" jdbcType="BIT" property="limitDeparture" />
    <result column="departure_location_id" jdbcType="VARCHAR" property="departureLocationId" />
    <result column="limit_arrival" jdbcType="BIT" property="limitArrival" />
    <result column="arrival_location_id" jdbcType="VARCHAR" property="arrivalLocationId" />
    <result column="limit_taxi_type" jdbcType="BIT" property="limitTaxiType" />
    <result column="allowed_taxi_type" jdbcType="VARCHAR" property="allowedTaxiType" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="price_limit_flag" jdbcType="BIT" property="priceLimitFlag" />
    <result column="price_limit" jdbcType="NUMERIC" property="priceLimit" />
    <result column="allow_shuttle" jdbcType="BIT" property="allowShuttle" />
    <result column="allow_same_city" jdbcType="BIT" property="allowSameCity" />
    <result column="allow_called_forother" jdbcType="BIT" property="allowCalledForother" />
    <result column="day_price_limit" jdbcType="NUMERIC" property="dayPriceLimit" />
    <result column="taxi_scheduling_fee" jdbcType="NUMERIC" property="taxiSchedulingFee" />
    <result column="limit_advance" jdbcType="BIT" property="limitAdvance" />
    <result column="limit_path" jdbcType="BIT" property="limitPath" />
    <result column="use_personal_budget" jdbcType="INTEGER" property="usePersonalBudget" />
    <result column="type" jdbcType="BIGINT" property="type" />
    <result column="is_ding" jdbcType="SMALLINT" property="isDing" />
    <result column="times_limit_flag" jdbcType="BIT" property="timesLimitFlag" />
    <result column="day_times_limit" jdbcType="INTEGER" property="dayTimesLimit" />
    <result column="period_hour" jdbcType="VARCHAR" property="periodHour" />
    <result column="period_minute" jdbcType="VARCHAR" property="periodMinute" />
    <result column="rule_type" jdbcType="INTEGER" property="ruleType" />
    <result column="modify_flag" jdbcType="BIT" property="modifyFlag" />
    <result column="allow_call_for_other_subsidiary" jdbcType="BIT" property="allowCallForOtherSubsidiary" />
    <result column="allow_same_city_type" jdbcType="INTEGER" property="allowSameCityType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, name, limit_level, limit_time, limit_departure, departure_location_id,
    limit_arrival, arrival_location_id, limit_taxi_type, allowed_taxi_type, modify_time,
    price_limit_flag, price_limit, allow_shuttle, allow_same_city, allow_called_forother,
    day_price_limit, taxi_scheduling_fee, limit_advance, limit_path, use_personal_budget,
    type, is_ding, times_limit_flag, day_times_limit, period_hour, period_minute, rule_type,
    modify_flag, allow_call_for_other_subsidiary, allow_same_city_type
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiRuleExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from taxi_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${limit} offset ${offset}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from taxi_rule
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from taxi_rule
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiRuleExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from taxi_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiRule" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into taxi_rule (company_id, name, limit_level,
    limit_time, limit_departure, departure_location_id,
    limit_arrival, arrival_location_id, limit_taxi_type,
    allowed_taxi_type, modify_time, price_limit_flag,
    price_limit, allow_shuttle, allow_same_city,
    allow_called_forother, day_price_limit, taxi_scheduling_fee,
    limit_advance, limit_path, use_personal_budget,
    type, is_ding, times_limit_flag,
    day_times_limit, period_hour, period_minute,
    rule_type, modify_flag, allow_call_for_other_subsidiary,
    allow_same_city_type)
    values (#{companyId,jdbcType=CHAR}, #{name,jdbcType=VARCHAR}, #{limitLevel,jdbcType=INTEGER},
    #{limitTime,jdbcType=BIT}, #{limitDeparture,jdbcType=BIT}, #{departureLocationId,jdbcType=VARCHAR},
    #{limitArrival,jdbcType=BIT}, #{arrivalLocationId,jdbcType=VARCHAR}, #{limitTaxiType,jdbcType=BIT},
    #{allowedTaxiType,jdbcType=VARCHAR}, #{modifyTime,jdbcType=TIMESTAMP}, #{priceLimitFlag,jdbcType=BIT},
    #{priceLimit,jdbcType=NUMERIC}, #{allowShuttle,jdbcType=BIT}, #{allowSameCity,jdbcType=BIT},
    #{allowCalledForother,jdbcType=BIT}, #{dayPriceLimit,jdbcType=NUMERIC}, #{taxiSchedulingFee,jdbcType=NUMERIC},
    #{limitAdvance,jdbcType=BIT}, #{limitPath,jdbcType=BIT}, #{usePersonalBudget,jdbcType=INTEGER},
    #{type,jdbcType=BIGINT}, #{isDing,jdbcType=SMALLINT}, #{timesLimitFlag,jdbcType=BIT},
    #{dayTimesLimit,jdbcType=INTEGER}, #{periodHour,jdbcType=VARCHAR}, #{periodMinute,jdbcType=VARCHAR},
    #{ruleType,jdbcType=INTEGER}, #{modifyFlag,jdbcType=BIT}, #{allowCallForOtherSubsidiary,jdbcType=BIT},
    #{allowSameCityType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiRule" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into taxi_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="limitLevel != null">
        limit_level,
      </if>
      <if test="limitTime != null">
        limit_time,
      </if>
      <if test="limitDeparture != null">
        limit_departure,
      </if>
      <if test="departureLocationId != null">
        departure_location_id,
      </if>
      <if test="limitArrival != null">
        limit_arrival,
      </if>
      <if test="arrivalLocationId != null">
        arrival_location_id,
      </if>
      <if test="limitTaxiType != null">
        limit_taxi_type,
      </if>
      <if test="allowedTaxiType != null">
        allowed_taxi_type,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="priceLimitFlag != null">
        price_limit_flag,
      </if>
      <if test="priceLimit != null">
        price_limit,
      </if>
      <if test="allowShuttle != null">
        allow_shuttle,
      </if>
      <if test="allowSameCity != null">
        allow_same_city,
      </if>
      <if test="allowCalledForother != null">
        allow_called_forother,
      </if>
      <if test="dayPriceLimit != null">
        day_price_limit,
      </if>
      <if test="taxiSchedulingFee != null">
        taxi_scheduling_fee,
      </if>
      <if test="limitAdvance != null">
        limit_advance,
      </if>
      <if test="limitPath != null">
        limit_path,
      </if>
      <if test="usePersonalBudget != null">
        use_personal_budget,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="isDing != null">
        is_ding,
      </if>
      <if test="timesLimitFlag != null">
        times_limit_flag,
      </if>
      <if test="dayTimesLimit != null">
        day_times_limit,
      </if>
      <if test="periodHour != null">
        period_hour,
      </if>
      <if test="periodMinute != null">
        period_minute,
      </if>
      <if test="ruleType != null">
        rule_type,
      </if>
      <if test="modifyFlag != null">
        modify_flag,
      </if>
      <if test="allowCallForOtherSubsidiary != null">
        allow_call_for_other_subsidiary,
      </if>
      <if test="allowSameCityType != null">
        allow_same_city_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=CHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="limitLevel != null">
        #{limitLevel,jdbcType=INTEGER},
      </if>
      <if test="limitTime != null">
        #{limitTime,jdbcType=BIT},
      </if>
      <if test="limitDeparture != null">
        #{limitDeparture,jdbcType=BIT},
      </if>
      <if test="departureLocationId != null">
        #{departureLocationId,jdbcType=VARCHAR},
      </if>
      <if test="limitArrival != null">
        #{limitArrival,jdbcType=BIT},
      </if>
      <if test="arrivalLocationId != null">
        #{arrivalLocationId,jdbcType=VARCHAR},
      </if>
      <if test="limitTaxiType != null">
        #{limitTaxiType,jdbcType=BIT},
      </if>
      <if test="allowedTaxiType != null">
        #{allowedTaxiType,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="priceLimitFlag != null">
        #{priceLimitFlag,jdbcType=BIT},
      </if>
      <if test="priceLimit != null">
        #{priceLimit,jdbcType=NUMERIC},
      </if>
      <if test="allowShuttle != null">
        #{allowShuttle,jdbcType=BIT},
      </if>
      <if test="allowSameCity != null">
        #{allowSameCity,jdbcType=BIT},
      </if>
      <if test="allowCalledForother != null">
        #{allowCalledForother,jdbcType=BIT},
      </if>
      <if test="dayPriceLimit != null">
        #{dayPriceLimit,jdbcType=NUMERIC},
      </if>
      <if test="taxiSchedulingFee != null">
        #{taxiSchedulingFee,jdbcType=NUMERIC},
      </if>
      <if test="limitAdvance != null">
        #{limitAdvance,jdbcType=BIT},
      </if>
      <if test="limitPath != null">
        #{limitPath,jdbcType=BIT},
      </if>
      <if test="usePersonalBudget != null">
        #{usePersonalBudget,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=BIGINT},
      </if>
      <if test="isDing != null">
        #{isDing,jdbcType=SMALLINT},
      </if>
      <if test="timesLimitFlag != null">
        #{timesLimitFlag,jdbcType=BIT},
      </if>
      <if test="dayTimesLimit != null">
        #{dayTimesLimit,jdbcType=INTEGER},
      </if>
      <if test="periodHour != null">
        #{periodHour,jdbcType=VARCHAR},
      </if>
      <if test="periodMinute != null">
        #{periodMinute,jdbcType=VARCHAR},
      </if>
      <if test="ruleType != null">
        #{ruleType,jdbcType=INTEGER},
      </if>
      <if test="modifyFlag != null">
        #{modifyFlag,jdbcType=BIT},
      </if>
      <if test="allowCallForOtherSubsidiary != null">
        #{allowCallForOtherSubsidiary,jdbcType=BIT},
      </if>
      <if test="allowSameCityType != null">
        #{allowSameCityType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiRuleExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from taxi_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update taxi_rule
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=CHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.limitLevel != null">
        limit_level = #{record.limitLevel,jdbcType=INTEGER},
      </if>
      <if test="record.limitTime != null">
        limit_time = #{record.limitTime,jdbcType=BIT},
      </if>
      <if test="record.limitDeparture != null">
        limit_departure = #{record.limitDeparture,jdbcType=BIT},
      </if>
      <if test="record.departureLocationId != null">
        departure_location_id = #{record.departureLocationId,jdbcType=VARCHAR},
      </if>
      <if test="record.limitArrival != null">
        limit_arrival = #{record.limitArrival,jdbcType=BIT},
      </if>
      <if test="record.arrivalLocationId != null">
        arrival_location_id = #{record.arrivalLocationId,jdbcType=VARCHAR},
      </if>
      <if test="record.limitTaxiType != null">
        limit_taxi_type = #{record.limitTaxiType,jdbcType=BIT},
      </if>
      <if test="record.allowedTaxiType != null">
        allowed_taxi_type = #{record.allowedTaxiType,jdbcType=VARCHAR},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.priceLimitFlag != null">
        price_limit_flag = #{record.priceLimitFlag,jdbcType=BIT},
      </if>
      <if test="record.priceLimit != null">
        price_limit = #{record.priceLimit,jdbcType=NUMERIC},
      </if>
      <if test="record.allowShuttle != null">
        allow_shuttle = #{record.allowShuttle,jdbcType=BIT},
      </if>
      <if test="record.allowSameCity != null">
        allow_same_city = #{record.allowSameCity,jdbcType=BIT},
      </if>
      <if test="record.allowCalledForother != null">
        allow_called_forother = #{record.allowCalledForother,jdbcType=BIT},
      </if>
      <if test="record.dayPriceLimit != null">
        day_price_limit = #{record.dayPriceLimit,jdbcType=NUMERIC},
      </if>
      <if test="record.taxiSchedulingFee != null">
        taxi_scheduling_fee = #{record.taxiSchedulingFee,jdbcType=NUMERIC},
      </if>
      <if test="record.limitAdvance != null">
        limit_advance = #{record.limitAdvance,jdbcType=BIT},
      </if>
      <if test="record.limitPath != null">
        limit_path = #{record.limitPath,jdbcType=BIT},
      </if>
      <if test="record.usePersonalBudget != null">
        use_personal_budget = #{record.usePersonalBudget,jdbcType=INTEGER},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=BIGINT},
      </if>
      <if test="record.isDing != null">
        is_ding = #{record.isDing,jdbcType=SMALLINT},
      </if>
      <if test="record.timesLimitFlag != null">
        times_limit_flag = #{record.timesLimitFlag,jdbcType=BIT},
      </if>
      <if test="record.dayTimesLimit != null">
        day_times_limit = #{record.dayTimesLimit,jdbcType=INTEGER},
      </if>
      <if test="record.periodHour != null">
        period_hour = #{record.periodHour,jdbcType=VARCHAR},
      </if>
      <if test="record.periodMinute != null">
        period_minute = #{record.periodMinute,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleType != null">
        rule_type = #{record.ruleType,jdbcType=INTEGER},
      </if>
      <if test="record.modifyFlag != null">
        modify_flag = #{record.modifyFlag,jdbcType=BIT},
      </if>
      <if test="record.allowCallForOtherSubsidiary != null">
        allow_call_for_other_subsidiary = #{record.allowCallForOtherSubsidiary,jdbcType=BIT},
      </if>
      <if test="record.allowSameCityType != null">
        allow_same_city_type = #{record.allowSameCityType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update taxi_rule
    set id = #{record.id,jdbcType=INTEGER},
    company_id = #{record.companyId,jdbcType=CHAR},
    name = #{record.name,jdbcType=VARCHAR},
    limit_level = #{record.limitLevel,jdbcType=INTEGER},
    limit_time = #{record.limitTime,jdbcType=BIT},
    limit_departure = #{record.limitDeparture,jdbcType=BIT},
    departure_location_id = #{record.departureLocationId,jdbcType=VARCHAR},
    limit_arrival = #{record.limitArrival,jdbcType=BIT},
    arrival_location_id = #{record.arrivalLocationId,jdbcType=VARCHAR},
    limit_taxi_type = #{record.limitTaxiType,jdbcType=BIT},
    allowed_taxi_type = #{record.allowedTaxiType,jdbcType=VARCHAR},
    modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
    price_limit_flag = #{record.priceLimitFlag,jdbcType=BIT},
    price_limit = #{record.priceLimit,jdbcType=NUMERIC},
    allow_shuttle = #{record.allowShuttle,jdbcType=BIT},
    allow_same_city = #{record.allowSameCity,jdbcType=BIT},
    allow_called_forother = #{record.allowCalledForother,jdbcType=BIT},
    day_price_limit = #{record.dayPriceLimit,jdbcType=NUMERIC},
    taxi_scheduling_fee = #{record.taxiSchedulingFee,jdbcType=NUMERIC},
    limit_advance = #{record.limitAdvance,jdbcType=BIT},
    limit_path = #{record.limitPath,jdbcType=BIT},
    use_personal_budget = #{record.usePersonalBudget,jdbcType=INTEGER},
    type = #{record.type,jdbcType=BIGINT},
    is_ding = #{record.isDing,jdbcType=SMALLINT},
    times_limit_flag = #{record.timesLimitFlag,jdbcType=BIT},
    day_times_limit = #{record.dayTimesLimit,jdbcType=INTEGER},
    period_hour = #{record.periodHour,jdbcType=VARCHAR},
    period_minute = #{record.periodMinute,jdbcType=VARCHAR},
    rule_type = #{record.ruleType,jdbcType=INTEGER},
    modify_flag = #{record.modifyFlag,jdbcType=BIT},
    allow_call_for_other_subsidiary = #{record.allowCallForOtherSubsidiary,jdbcType=BIT},
    allow_same_city_type = #{record.allowSameCityType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update taxi_rule
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=CHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="limitLevel != null">
        limit_level = #{limitLevel,jdbcType=INTEGER},
      </if>
      <if test="limitTime != null">
        limit_time = #{limitTime,jdbcType=BIT},
      </if>
      <if test="limitDeparture != null">
        limit_departure = #{limitDeparture,jdbcType=BIT},
      </if>
      <if test="departureLocationId != null">
        departure_location_id = #{departureLocationId,jdbcType=VARCHAR},
      </if>
      <if test="limitArrival != null">
        limit_arrival = #{limitArrival,jdbcType=BIT},
      </if>
      <if test="arrivalLocationId != null">
        arrival_location_id = #{arrivalLocationId,jdbcType=VARCHAR},
      </if>
      <if test="limitTaxiType != null">
        limit_taxi_type = #{limitTaxiType,jdbcType=BIT},
      </if>
      <if test="allowedTaxiType != null">
        allowed_taxi_type = #{allowedTaxiType,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="priceLimitFlag != null">
        price_limit_flag = #{priceLimitFlag,jdbcType=BIT},
      </if>
      <if test="priceLimit != null">
        price_limit = #{priceLimit,jdbcType=NUMERIC},
      </if>
      <if test="allowShuttle != null">
        allow_shuttle = #{allowShuttle,jdbcType=BIT},
      </if>
      <if test="allowSameCity != null">
        allow_same_city = #{allowSameCity,jdbcType=BIT},
      </if>
      <if test="allowCalledForother != null">
        allow_called_forother = #{allowCalledForother,jdbcType=BIT},
      </if>
      <if test="dayPriceLimit != null">
        day_price_limit = #{dayPriceLimit,jdbcType=NUMERIC},
      </if>
      <if test="taxiSchedulingFee != null">
        taxi_scheduling_fee = #{taxiSchedulingFee,jdbcType=NUMERIC},
      </if>
      <if test="limitAdvance != null">
        limit_advance = #{limitAdvance,jdbcType=BIT},
      </if>
      <if test="limitPath != null">
        limit_path = #{limitPath,jdbcType=BIT},
      </if>
      <if test="usePersonalBudget != null">
        use_personal_budget = #{usePersonalBudget,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=BIGINT},
      </if>
      <if test="isDing != null">
        is_ding = #{isDing,jdbcType=SMALLINT},
      </if>
      <if test="timesLimitFlag != null">
        times_limit_flag = #{timesLimitFlag,jdbcType=BIT},
      </if>
      <if test="dayTimesLimit != null">
        day_times_limit = #{dayTimesLimit,jdbcType=INTEGER},
      </if>
      <if test="periodHour != null">
        period_hour = #{periodHour,jdbcType=VARCHAR},
      </if>
      <if test="periodMinute != null">
        period_minute = #{periodMinute,jdbcType=VARCHAR},
      </if>
      <if test="ruleType != null">
        rule_type = #{ruleType,jdbcType=INTEGER},
      </if>
      <if test="modifyFlag != null">
        modify_flag = #{modifyFlag,jdbcType=BIT},
      </if>
      <if test="allowCallForOtherSubsidiary != null">
        allow_call_for_other_subsidiary = #{allowCallForOtherSubsidiary,jdbcType=BIT},
      </if>
      <if test="allowSameCityType != null">
        allow_same_city_type = #{allowSameCityType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update taxi_rule
    set company_id = #{companyId,jdbcType=CHAR},
    name = #{name,jdbcType=VARCHAR},
    limit_level = #{limitLevel,jdbcType=INTEGER},
    limit_time = #{limitTime,jdbcType=BIT},
    limit_departure = #{limitDeparture,jdbcType=BIT},
    departure_location_id = #{departureLocationId,jdbcType=VARCHAR},
    limit_arrival = #{limitArrival,jdbcType=BIT},
    arrival_location_id = #{arrivalLocationId,jdbcType=VARCHAR},
    limit_taxi_type = #{limitTaxiType,jdbcType=BIT},
    allowed_taxi_type = #{allowedTaxiType,jdbcType=VARCHAR},
    modify_time = #{modifyTime,jdbcType=TIMESTAMP},
    price_limit_flag = #{priceLimitFlag,jdbcType=BIT},
    price_limit = #{priceLimit,jdbcType=NUMERIC},
    allow_shuttle = #{allowShuttle,jdbcType=BIT},
    allow_same_city = #{allowSameCity,jdbcType=BIT},
    allow_called_forother = #{allowCalledForother,jdbcType=BIT},
    day_price_limit = #{dayPriceLimit,jdbcType=NUMERIC},
    taxi_scheduling_fee = #{taxiSchedulingFee,jdbcType=NUMERIC},
    limit_advance = #{limitAdvance,jdbcType=BIT},
    limit_path = #{limitPath,jdbcType=BIT},
    use_personal_budget = #{usePersonalBudget,jdbcType=INTEGER},
    type = #{type,jdbcType=BIGINT},
    is_ding = #{isDing,jdbcType=SMALLINT},
    times_limit_flag = #{timesLimitFlag,jdbcType=BIT},
    day_times_limit = #{dayTimesLimit,jdbcType=INTEGER},
    period_hour = #{periodHour,jdbcType=VARCHAR},
    period_minute = #{periodMinute,jdbcType=VARCHAR},
    rule_type = #{ruleType,jdbcType=INTEGER},
    modify_flag = #{modifyFlag,jdbcType=BIT},
    allow_call_for_other_subsidiary = #{allowCallForOtherSubsidiary,jdbcType=BIT},
    allow_same_city_type = #{allowSameCityType,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>
