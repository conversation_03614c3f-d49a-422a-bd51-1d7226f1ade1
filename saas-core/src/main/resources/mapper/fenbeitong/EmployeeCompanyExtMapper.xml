<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.EmployeeCompanyExtMapper">
    <resultMap id="BaseResultExtMap" type="com.fenbeitong.saas.core.model.fenbeitong.EmployeeCompanyInfo">
        <id column="id" property="id"/>
        <result column="user_name" property="userName"/>
        <result column="user_role"  property="userRole"/>
        <result column="org_name"  property="orgName"/>
        <result column="user_email"  property="userEmail"/>
        <result column="phone_num"  property="userPhone"/>
        <result column="company_name" property="companyName"/>
        <result column="alias" property="alias"/>
        <result column="gender" property="gender"/>
        <result column="officeAddress" property="officeAddress"/>
    </resultMap>

    <select id="queryEmployeeCompanyInfoList" resultMap="BaseResultExtMap">
        select e.id, e.name user_name,e.phone_num,e.email user_email,e."role" user_role,e.gender,
        ou."name" org_name,cy."name" company_name,cy."alias",cy.office_address
        from employee e
        left join org_unit_employee_simple oues on e."id"=oues.employee_id
        left join org_unit ou on ou."id" = oues.org_unit_id
        left join company cy on oues.company_id = cy."id"
        where e.id in
        <foreach item="item" index="index" collection="list"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>