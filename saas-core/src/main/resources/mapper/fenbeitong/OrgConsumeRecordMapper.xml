<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.OrgConsumeRecordMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.OrgConsumeRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="consumer_id" jdbcType="VARCHAR" property="consumerId" />
    <result column="consumer_name" jdbcType="VARCHAR" property="consumerName" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="amount" jdbcType="NUMERIC" property="amount" />
    <result column="consume_category" jdbcType="SMALLINT" property="consumeCategory" />
    <result column="consumer_type" jdbcType="SMALLINT" property="consumerType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="pre_order_id" jdbcType="VARCHAR" property="preOrderId" />
    <result column="origin_order_id" jdbcType="VARCHAR" property="originOrderId" />
    <result column="is_latest" jdbcType="BIT" property="isLatest" />
    <result column="request_time" jdbcType="TIMESTAMP" property="requestTime" />
    <result column="param_json" jdbcType="VARCHAR" property="paramJson" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, consumer_id, consumer_name, company_id, order_id, amount, consume_category, consumer_type, 
    create_time, pre_order_id, origin_order_id, is_latest, request_time, param_json
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.OrgConsumeRecordExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from org_consume_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${limit} offset ${offset}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from org_consume_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from org_consume_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.OrgConsumeRecordExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from org_consume_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.fenbeitong.saas.core.model.fenbeitong.OrgConsumeRecord" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into org_consume_record (consumer_id, consumer_name, company_id, 
      order_id, amount, consume_category, 
      consumer_type, create_time, pre_order_id, 
      origin_order_id, is_latest, request_time, 
      param_json)
    values (#{consumerId,jdbcType=VARCHAR}, #{consumerName,jdbcType=VARCHAR}, #{companyId,jdbcType=VARCHAR}, 
      #{orderId,jdbcType=VARCHAR}, #{amount,jdbcType=NUMERIC}, #{consumeCategory,jdbcType=SMALLINT}, 
      #{consumerType,jdbcType=SMALLINT}, #{createTime,jdbcType=TIMESTAMP}, #{preOrderId,jdbcType=VARCHAR}, 
      #{originOrderId,jdbcType=VARCHAR}, #{isLatest,jdbcType=BIT}, #{requestTime,jdbcType=TIMESTAMP}, 
      #{paramJson,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.fenbeitong.saas.core.model.fenbeitong.OrgConsumeRecord" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into org_consume_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="consumerId != null">
        consumer_id,
      </if>
      <if test="consumerName != null">
        consumer_name,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="consumeCategory != null">
        consume_category,
      </if>
      <if test="consumerType != null">
        consumer_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="preOrderId != null">
        pre_order_id,
      </if>
      <if test="originOrderId != null">
        origin_order_id,
      </if>
      <if test="isLatest != null">
        is_latest,
      </if>
      <if test="requestTime != null">
        request_time,
      </if>
      <if test="paramJson != null">
        param_json,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="consumerId != null">
        #{consumerId,jdbcType=VARCHAR},
      </if>
      <if test="consumerName != null">
        #{consumerName,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=NUMERIC},
      </if>
      <if test="consumeCategory != null">
        #{consumeCategory,jdbcType=SMALLINT},
      </if>
      <if test="consumerType != null">
        #{consumerType,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="preOrderId != null">
        #{preOrderId,jdbcType=VARCHAR},
      </if>
      <if test="originOrderId != null">
        #{originOrderId,jdbcType=VARCHAR},
      </if>
      <if test="isLatest != null">
        #{isLatest,jdbcType=BIT},
      </if>
      <if test="requestTime != null">
        #{requestTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paramJson != null">
        #{paramJson,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.OrgConsumeRecordExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from org_consume_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update org_consume_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.consumerId != null">
        consumer_id = #{record.consumerId,jdbcType=VARCHAR},
      </if>
      <if test="record.consumerName != null">
        consumer_name = #{record.consumerName,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=NUMERIC},
      </if>
      <if test="record.consumeCategory != null">
        consume_category = #{record.consumeCategory,jdbcType=SMALLINT},
      </if>
      <if test="record.consumerType != null">
        consumer_type = #{record.consumerType,jdbcType=SMALLINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.preOrderId != null">
        pre_order_id = #{record.preOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.originOrderId != null">
        origin_order_id = #{record.originOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.isLatest != null">
        is_latest = #{record.isLatest,jdbcType=BIT},
      </if>
      <if test="record.requestTime != null">
        request_time = #{record.requestTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.paramJson != null">
        param_json = #{record.paramJson,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update org_consume_record
    set id = #{record.id,jdbcType=BIGINT},
      consumer_id = #{record.consumerId,jdbcType=VARCHAR},
      consumer_name = #{record.consumerName,jdbcType=VARCHAR},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      amount = #{record.amount,jdbcType=NUMERIC},
      consume_category = #{record.consumeCategory,jdbcType=SMALLINT},
      consumer_type = #{record.consumerType,jdbcType=SMALLINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      pre_order_id = #{record.preOrderId,jdbcType=VARCHAR},
      origin_order_id = #{record.originOrderId,jdbcType=VARCHAR},
      is_latest = #{record.isLatest,jdbcType=BIT},
      request_time = #{record.requestTime,jdbcType=TIMESTAMP},
      param_json = #{record.paramJson,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.OrgConsumeRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update org_consume_record
    <set>
      <if test="consumerId != null">
        consumer_id = #{consumerId,jdbcType=VARCHAR},
      </if>
      <if test="consumerName != null">
        consumer_name = #{consumerName,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=NUMERIC},
      </if>
      <if test="consumeCategory != null">
        consume_category = #{consumeCategory,jdbcType=SMALLINT},
      </if>
      <if test="consumerType != null">
        consumer_type = #{consumerType,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="preOrderId != null">
        pre_order_id = #{preOrderId,jdbcType=VARCHAR},
      </if>
      <if test="originOrderId != null">
        origin_order_id = #{originOrderId,jdbcType=VARCHAR},
      </if>
      <if test="isLatest != null">
        is_latest = #{isLatest,jdbcType=BIT},
      </if>
      <if test="requestTime != null">
        request_time = #{requestTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paramJson != null">
        param_json = #{paramJson,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.fenbeitong.OrgConsumeRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update org_consume_record
    set consumer_id = #{consumerId,jdbcType=VARCHAR},
      consumer_name = #{consumerName,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=NUMERIC},
      consume_category = #{consumeCategory,jdbcType=SMALLINT},
      consumer_type = #{consumerType,jdbcType=SMALLINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      pre_order_id = #{preOrderId,jdbcType=VARCHAR},
      origin_order_id = #{originOrderId,jdbcType=VARCHAR},
      is_latest = #{isLatest,jdbcType=BIT},
      request_time = #{requestTime,jdbcType=TIMESTAMP},
      param_json = #{paramJson,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>