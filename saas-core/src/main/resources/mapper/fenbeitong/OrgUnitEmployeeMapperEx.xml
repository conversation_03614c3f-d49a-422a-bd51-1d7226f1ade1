<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.OrgUnitEmployeeMapperEx">
    <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.OrgUnitEmployee"
               extends="com.fenbeitong.saas.core.dao.fenbeitong.OrgUnitEmployeeMapper.BaseResultMap">
    </resultMap>

    <sql id="Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Apr 24 14:25:21 CST 2017.
        -->
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Apr 24 14:25:21 CST 2017.
        -->
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Apr 24 14:25:21 CST 2017.
        -->
        origin_id, entity_id, entity_type, company_id, ref_count, is_own
    </sql>

    <insert id="insertAddDepartmentOfOrigin" parameterType="map">
    insert into org_unit_employee ( company_id, origin_id, entity_id, entity_type, is_own, ref_count)
    ( select company_id, origin_id as origin_id, #{entityId,jdbcType=VARCHAR} as entity_id, 1 as entity_type, false as is_own, 1 as ref_count
    from org_unit_employee
    where company_id = #{companyId,jdbcType=VARCHAR} and entity_id = #{parentId,jdbcType=VARCHAR} and entity_type = 1 )
  </insert>

    <resultMap id="managersOfEmployee" type="com.fenbeitong.saas.core.model.fenbeitong.custom.ManagersOfEmployee">
        <result column="department_id" property="departmentId" jdbcType="VARCHAR" />
        <result column="employee_id" property="employeeId" jdbcType="VARCHAR" />
        <result column="manager_id" property="managerId" jdbcType="VARCHAR" />
    </resultMap>

    <select id="getManagersOfEmployee" parameterType="map" resultMap="managersOfEmployee">
        select
        	oue.origin_id as department_id,
        	oue.entity_id as employee_id,
        	oues.employee_id as manager_id
        from
        	org_unit_employee oue
        left join org_unit_employee_simple oues on oue.origin_id = oues.org_unit_id
        where
        	oue.entity_id = #{employeeId,jdbcType=VARCHAR}
        and oue.company_id = #{companyId,jdbcType=VARCHAR}
        and oues.is_manager = true;
  </select>

    <update id="relieveEmployeeWithParentIO" parameterType="map" >
        update org_unit_employee set ref_count = ref_count - 1
        where company_id = #{companyId} and entity_type = 2 and entity_id = #{id} and
        origin_id in (select origin_id from org_unit_employee where company_id = #{companyId} and entity_id = #{parentId} union select #{parentId})
    </update>
    <update id="relieveEmpoyeeIsOwnIO" parameterType="map">
        update org_unit_employee set is_own = false where company_id = #{companyId} and
         entity_type = 2 and entity_id = #{id} and origin_id = #{parentId}
    </update>

    <select id="getManagersByEmployee" parameterType="map" resultMap="managersOfEmployee">
        select
        oue.origin_id as department_id,
        oue.entity_id as employee_id,
        oues.employee_id as manager_id
        from
        org_unit_employee oue
        left join org_unit_employee_simple oues on oue.origin_id = oues.org_unit_id
        where
        oue.entity_id = #{employeeId,jdbcType=VARCHAR}
        and oue.company_id = #{companyId,jdbcType=VARCHAR}
        and oues.is_manager = true;
    </select>


    <!--查询用户所属部门-->
    <select id="selectOrgUnitList" parameterType="java.lang.String" resultType="com.fenbeitong.saas.core.contract.user.innerbean.OrgUnitListBean">

        SELECT
	    u."id",u."name",u.parent_org_unit_id AS parent_id,u."rank",u.employee_count,u.company_id
    FROM
        org_unit_employee s,
        org_unit u
    WHERE
	 s.origin_id=u."id" and s.entity_type=2
    AND
        s.entity_id = #{employee_id,jdbcType=VARCHAR}
    AND s.is_own = TRUE

    </select>
</mapper>