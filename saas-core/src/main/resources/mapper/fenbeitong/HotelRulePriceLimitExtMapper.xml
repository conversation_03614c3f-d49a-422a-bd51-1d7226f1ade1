<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.HotelRulePriceLimitExtMapper">
    <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.HotelRulePriceLimit">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="province_limit_id" jdbcType="VARCHAR" property="provinceLimitId"/>
        <result column="price" jdbcType="NUMERIC" property="price"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id, province_limit_id, price, type, create_time
    </sql>
    <insert id="batchInsert" parameterType="java.util.List">
        insert into hotel_rule_price_limit
        VALUES
        <foreach collection="list" item="hotelRulePriceLimit" index="index" separator=",">
            (#{hotelRulePriceLimit.id},#{hotelRulePriceLimit.provinceLimitId},#{hotelRulePriceLimit.price},
            #{hotelRulePriceLimit.type},#{hotelRulePriceLimit.createTime})
        </foreach>
    </insert>

    <select id="queryProvinceList" resultType="java.util.Map">
        select hr.id ,hr.global_price,hrpl.id province_limit_id,hrpl.price province_price,
        hrpl.province_id,hrpl.province_name,hrppl."id" price_limit_id ,hrppl.price ,hrcl.area_id,hrcl.area_name,
        hrdl.start_date,hrdl.end_date,hrppl."type"
        from (select * from hotel_rule_province_limit where rule_id=#{ruleId}
        order by rank
        <if test="pageSize != null">
            limit #{pageSize} OFFSET #{pageStart}
        </if>
        ) hrpl
        left join hotel_rule hr on hrpl.rule_id=hr.id
        left join hotel_rule_price_limit hrppl on hrpl.id=hrppl.province_limit_id
        left join hotel_rule_city_limit hrcl on hrcl.price_limit_id=hrppl.id
        left join hotel_rule_date_limit hrdl on hrdl.price_limit_id=hrppl.id
        where hr.id= #{ruleId} order by hrpl."rank",hrppl."type" desc,hrppl.id,hrcl."rank",hrdl."rank"
    </select>

    <select id="queryPriceLimitList" resultType="java.util.Map">
        select hrpl.id province_limit_id,hrpl.price province_price,
        hrpl.province_id,hrpl.province_name,hrppl."id" price_limit_id ,hrppl.price ,hrcl.area_id,hrcl.area_name,
        hrdl.start_date,hrdl.end_date,hrppl."type"
        from (select * from hotel_rule_province_limit where id=#{id}) hrpl
        left join hotel_rule_price_limit hrppl on hrpl.id=hrppl.province_limit_id
        left join hotel_rule_city_limit hrcl on hrcl.price_limit_id=hrppl.id
        left join hotel_rule_date_limit hrdl on hrdl.price_limit_id=hrppl.id
        order by hrpl."rank",hrppl."type" desc,hrppl.id,hrcl."rank",hrdl."rank"
    </select>


    <select id="queryProviceCityLimitList" resultType="com.fenbeitong.saas.core.contract.rule.ProvinceCityLimitContract">
        select hrpl.id,hrpl.province_id provinceId,hrpl.price provicePrice,hrpll.price areaPrice,hrpll."type",
        hrcl.price_limit_id priceLimitId,hrcl.area_id areaId,hrcl.area_name areaName,
        hrdl.start_date startDate,hrdl.end_date endDate
        from hotel_rule_province_limit hrpl
        left join hotel_rule_price_limit hrpll on hrpl.id=hrpll.province_limit_id
        left join hotel_rule_city_limit hrcl on hrpll."id"=hrcl.price_limit_id
        left join hotel_rule_date_limit hrdl on hrpll."id"=hrdl.price_limit_id
        where hrpl.rule_id=#{ruleId}
        <!--and hrpl.province_id in-->
        <!--<foreach collection="cityCodeList" item="listItem" open="(" separator="," close=")">-->
            <!--#{listItem}-->
        <!--</foreach>-->
        and hrcl.area_id in
        <foreach collection="cityCodeList" item="listItem" open="(" separator="," close=")">
            #{listItem}
        </foreach>
        order by hrpl.rank, hrpll."type" desc,hrcl."rank" ;
    </select>


</mapper>