<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.OperateLogOrgUnitMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.OperateLogOrgUnit">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 05 17:26:45 CST 2017.
    -->
    <constructor>
      <idArg column="id" javaType="java.lang.Long" jdbcType="BIGINT" />
      <arg column="company_id" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="org_unit_id" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="operate_type" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="delete_content" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="operator_id" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="create_time" javaType="java.util.Date" jdbcType="TIMESTAMP" />
    </constructor>
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 05 17:26:45 CST 2017.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 05 17:26:45 CST 2017.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 05 17:26:45 CST 2017.
    -->
    id, company_id, org_unit_id, operate_type, delete_content, operator_id, create_time
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.OperateLogOrgUnitExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 05 17:26:45 CST 2017.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from operate_log_org_unit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.OperateLogOrgUnitExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 05 17:26:45 CST 2017.
    -->
    delete from operate_log_org_unit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.OperateLogOrgUnit">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 05 17:26:45 CST 2017.
    -->
    insert into operate_log_org_unit
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="orgUnitId != null">
        org_unit_id,
      </if>
      <if test="operateType != null">
        operate_type,
      </if>
      <if test="deleteContent != null">
        delete_content,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="orgUnitId != null">
        #{orgUnitId,jdbcType=VARCHAR},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=INTEGER},
      </if>
      <if test="deleteContent != null">
        #{deleteContent,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.OperateLogOrgUnitExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 05 17:26:45 CST 2017.
    -->
    select count(*) from operate_log_org_unit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 05 17:26:45 CST 2017.
    -->
    update operate_log_org_unit
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.orgUnitId != null">
        org_unit_id = #{record.orgUnitId,jdbcType=VARCHAR},
      </if>
      <if test="record.operateType != null">
        operate_type = #{record.operateType,jdbcType=INTEGER},
      </if>
      <if test="record.deleteContent != null">
        delete_content = #{record.deleteContent,jdbcType=VARCHAR},
      </if>
      <if test="record.operatorId != null">
        operator_id = #{record.operatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 05 17:26:45 CST 2017.
    -->
    update operate_log_org_unit
    set id = #{record.id,jdbcType=BIGINT},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      org_unit_id = #{record.orgUnitId,jdbcType=VARCHAR},
      operate_type = #{record.operateType,jdbcType=INTEGER},
      delete_content = #{record.deleteContent,jdbcType=VARCHAR},
      operator_id = #{record.operatorId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.OperateLogOrgUnit">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 05 17:26:45 CST 2017.
    -->
    update operate_log_org_unit
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="orgUnitId != null">
        org_unit_id = #{orgUnitId,jdbcType=VARCHAR},
      </if>
      <if test="operateType != null">
        operate_type = #{operateType,jdbcType=INTEGER},
      </if>
      <if test="deleteContent != null">
        delete_content = #{deleteContent,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>