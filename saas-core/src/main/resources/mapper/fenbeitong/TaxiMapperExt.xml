<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.TaxiMapperExt">

  <select id="getTaxiInfoByOrderId" parameterType="map" resultType="map">
    select t.*
    from fb_order o
    inner join fb_order_product op on op.fb_order_id=o.id
    inner join taxi t on t.id=op.product_id
    where o.id=#{orderId,jdbcType=VARCHAR}
  </select>

</mapper>