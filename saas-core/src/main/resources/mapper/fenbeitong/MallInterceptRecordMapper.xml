<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.MallInterceptRecordMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.MallInterceptRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="employee_id" jdbcType="CHAR" property="employeeId" />
    <result column="company_id" jdbcType="CHAR" property="companyId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="contact_name" jdbcType="VARCHAR" property="contactName" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="channel" jdbcType="INTEGER" property="channel" />
    <result column="total_price" jdbcType="NUMERIC" property="totalPrice" />
    <result column="mall_rule" jdbcType="INTEGER" property="mallRule" />
    <result column="mall_rule_flag" jdbcType="BIT" property="mallRuleFlag" />
    <result column="sku_info" jdbcType="VARCHAR" property="skuInfo" />
    <result column="err_msg" jdbcType="VARCHAR" property="errMsg" />
    <result column="err_code" jdbcType="INTEGER" property="errCode" />
    <result column="cost_center_id" jdbcType="VARCHAR" property="costCenterId" />
    <result column="cost_center_type" jdbcType="INTEGER" property="costCenterType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, employee_id, company_id, create_time, contact_name, contact_phone, channel, total_price, 
    mall_rule, mall_rule_flag, sku_info, err_msg, err_code, cost_center_id, cost_center_type
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.MallInterceptRecordExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mall_intercept_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from mall_intercept_record
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from mall_intercept_record
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.MallInterceptRecordExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from mall_intercept_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.fenbeitong.MallInterceptRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into mall_intercept_record (id, employee_id, company_id, 
      create_time, contact_name, contact_phone, 
      channel, total_price, mall_rule, 
      mall_rule_flag, sku_info, err_msg, 
      err_code, cost_center_id, cost_center_type
      )
    values (#{id,jdbcType=CHAR}, #{employeeId,jdbcType=CHAR}, #{companyId,jdbcType=CHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{contactName,jdbcType=VARCHAR}, #{contactPhone,jdbcType=VARCHAR}, 
      #{channel,jdbcType=INTEGER}, #{totalPrice,jdbcType=NUMERIC}, #{mallRule,jdbcType=INTEGER}, 
      #{mallRuleFlag,jdbcType=BIT}, #{skuInfo,jdbcType=VARCHAR}, #{errMsg,jdbcType=VARCHAR}, 
      #{errCode,jdbcType=INTEGER}, #{costCenterId,jdbcType=VARCHAR}, #{costCenterType,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.MallInterceptRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into mall_intercept_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="employeeId != null">
        employee_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="contactName != null">
        contact_name,
      </if>
      <if test="contactPhone != null">
        contact_phone,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="totalPrice != null">
        total_price,
      </if>
      <if test="mallRule != null">
        mall_rule,
      </if>
      <if test="mallRuleFlag != null">
        mall_rule_flag,
      </if>
      <if test="skuInfo != null">
        sku_info,
      </if>
      <if test="errMsg != null">
        err_msg,
      </if>
      <if test="errCode != null">
        err_code,
      </if>
      <if test="costCenterId != null">
        cost_center_id,
      </if>
      <if test="costCenterType != null">
        cost_center_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="employeeId != null">
        #{employeeId,jdbcType=CHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contactName != null">
        #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=INTEGER},
      </if>
      <if test="totalPrice != null">
        #{totalPrice,jdbcType=NUMERIC},
      </if>
      <if test="mallRule != null">
        #{mallRule,jdbcType=INTEGER},
      </if>
      <if test="mallRuleFlag != null">
        #{mallRuleFlag,jdbcType=BIT},
      </if>
      <if test="skuInfo != null">
        #{skuInfo,jdbcType=VARCHAR},
      </if>
      <if test="errMsg != null">
        #{errMsg,jdbcType=VARCHAR},
      </if>
      <if test="errCode != null">
        #{errCode,jdbcType=INTEGER},
      </if>
      <if test="costCenterId != null">
        #{costCenterId,jdbcType=VARCHAR},
      </if>
      <if test="costCenterType != null">
        #{costCenterType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.MallInterceptRecordExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from mall_intercept_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update mall_intercept_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.employeeId != null">
        employee_id = #{record.employeeId,jdbcType=CHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=CHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.contactName != null">
        contact_name = #{record.contactName,jdbcType=VARCHAR},
      </if>
      <if test="record.contactPhone != null">
        contact_phone = #{record.contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.channel != null">
        channel = #{record.channel,jdbcType=INTEGER},
      </if>
      <if test="record.totalPrice != null">
        total_price = #{record.totalPrice,jdbcType=NUMERIC},
      </if>
      <if test="record.mallRule != null">
        mall_rule = #{record.mallRule,jdbcType=INTEGER},
      </if>
      <if test="record.mallRuleFlag != null">
        mall_rule_flag = #{record.mallRuleFlag,jdbcType=BIT},
      </if>
      <if test="record.skuInfo != null">
        sku_info = #{record.skuInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.errMsg != null">
        err_msg = #{record.errMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.errCode != null">
        err_code = #{record.errCode,jdbcType=INTEGER},
      </if>
      <if test="record.costCenterId != null">
        cost_center_id = #{record.costCenterId,jdbcType=VARCHAR},
      </if>
      <if test="record.costCenterType != null">
        cost_center_type = #{record.costCenterType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update mall_intercept_record
    set id = #{record.id,jdbcType=CHAR},
      employee_id = #{record.employeeId,jdbcType=CHAR},
      company_id = #{record.companyId,jdbcType=CHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      contact_name = #{record.contactName,jdbcType=VARCHAR},
      contact_phone = #{record.contactPhone,jdbcType=VARCHAR},
      channel = #{record.channel,jdbcType=INTEGER},
      total_price = #{record.totalPrice,jdbcType=NUMERIC},
      mall_rule = #{record.mallRule,jdbcType=INTEGER},
      mall_rule_flag = #{record.mallRuleFlag,jdbcType=BIT},
      sku_info = #{record.skuInfo,jdbcType=VARCHAR},
      err_msg = #{record.errMsg,jdbcType=VARCHAR},
      err_code = #{record.errCode,jdbcType=INTEGER},
      cost_center_id = #{record.costCenterId,jdbcType=VARCHAR},
      cost_center_type = #{record.costCenterType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.MallInterceptRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update mall_intercept_record
    <set>
      <if test="employeeId != null">
        employee_id = #{employeeId,jdbcType=CHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contactName != null">
        contact_name = #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        contact_phone = #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=INTEGER},
      </if>
      <if test="totalPrice != null">
        total_price = #{totalPrice,jdbcType=NUMERIC},
      </if>
      <if test="mallRule != null">
        mall_rule = #{mallRule,jdbcType=INTEGER},
      </if>
      <if test="mallRuleFlag != null">
        mall_rule_flag = #{mallRuleFlag,jdbcType=BIT},
      </if>
      <if test="skuInfo != null">
        sku_info = #{skuInfo,jdbcType=VARCHAR},
      </if>
      <if test="errMsg != null">
        err_msg = #{errMsg,jdbcType=VARCHAR},
      </if>
      <if test="errCode != null">
        err_code = #{errCode,jdbcType=INTEGER},
      </if>
      <if test="costCenterId != null">
        cost_center_id = #{costCenterId,jdbcType=VARCHAR},
      </if>
      <if test="costCenterType != null">
        cost_center_type = #{costCenterType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.fenbeitong.MallInterceptRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update mall_intercept_record
    set employee_id = #{employeeId,jdbcType=CHAR},
      company_id = #{companyId,jdbcType=CHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      contact_name = #{contactName,jdbcType=VARCHAR},
      contact_phone = #{contactPhone,jdbcType=VARCHAR},
      channel = #{channel,jdbcType=INTEGER},
      total_price = #{totalPrice,jdbcType=NUMERIC},
      mall_rule = #{mallRule,jdbcType=INTEGER},
      mall_rule_flag = #{mallRuleFlag,jdbcType=BIT},
      sku_info = #{skuInfo,jdbcType=VARCHAR},
      err_msg = #{errMsg,jdbcType=VARCHAR},
      err_code = #{errCode,jdbcType=INTEGER},
      cost_center_id = #{costCenterId,jdbcType=VARCHAR},
      cost_center_type = #{costCenterType,jdbcType=INTEGER}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>