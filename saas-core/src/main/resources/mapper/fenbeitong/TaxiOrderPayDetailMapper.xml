<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.TaxiOrderPayDetailMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.TaxiOrderPayDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="company_id" jdbcType="CHAR" property="companyId" />
    <result column="order_id" jdbcType="CHAR" property="orderId" />
    <result column="order_price" jdbcType="NUMERIC" property="orderPrice" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="is_check_single_order" jdbcType="INTEGER" property="isCheckSingleOrder" />
    <result column="single_order" jdbcType="NUMERIC" property="singleOrder" />
    <result column="is_check_per_day" jdbcType="INTEGER" property="isCheckPerDay" />
    <result column="per_day" jdbcType="NUMERIC" property="perDay" />
    <result column="is_check_budget" jdbcType="INTEGER" property="isCheckBudget" />
    <result column="budget" jdbcType="NUMERIC" property="budget" />
    <result column="is_check_city_single_order" jdbcType="INTEGER" property="isCheckCitySingleOrder" />
    <result column="city_single_order" jdbcType="NUMERIC" property="citySingleOrder" />
    <result column="is_check_city_per_day" jdbcType="INTEGER" property="isCheckCityPerDay" />
    <result column="city_per_day" jdbcType="NUMERIC" property="cityPerDay" />
    <result column="is_check_apply_per_day" jdbcType="INTEGER" property="isCheckApplyPerDay" />
    <result column="apply_per_day" jdbcType="NUMERIC" property="applyPerDay" />
    <result column="apply_per_day_desc" jdbcType="VARCHAR" property="applyPerDayDesc" />
    <result column="is_check_apply_amount" jdbcType="INTEGER" property="isCheckApplyAmount" />
    <result column="apply_amount" jdbcType="NUMERIC" property="applyAmount" />
    <result column="apply_amount_desc" jdbcType="VARCHAR" property="applyAmountDesc" />
    <result column="is_open_exceed_config" jdbcType="INTEGER" property="isOpenExceedConfig" />
    <result column="is_personal_pay" jdbcType="INTEGER" property="isPersonalPay" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="buget_desc" jdbcType="VARCHAR" property="bugetDesc" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, order_id, order_price, type, is_check_single_order, single_order, 
    is_check_per_day, per_day, is_check_budget, budget, is_check_city_single_order, city_single_order, 
    is_check_city_per_day, city_per_day, is_check_apply_per_day, apply_per_day, apply_per_day_desc, 
    is_check_apply_amount, apply_amount, apply_amount_desc, is_open_exceed_config, is_personal_pay, 
    create_time, buget_desc
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiOrderPayDetailExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from taxi_order_pay_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${limit} offset ${offset}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from taxi_order_pay_detail
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from taxi_order_pay_detail
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiOrderPayDetailExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from taxi_order_pay_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiOrderPayDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into taxi_order_pay_detail (id, company_id, order_id, 
      order_price, type, is_check_single_order, 
      single_order, is_check_per_day, per_day, 
      is_check_budget, budget, is_check_city_single_order, 
      city_single_order, is_check_city_per_day, city_per_day, 
      is_check_apply_per_day, apply_per_day, apply_per_day_desc, 
      is_check_apply_amount, apply_amount, apply_amount_desc, 
      is_open_exceed_config, is_personal_pay, create_time, 
      buget_desc)
    values (#{id,jdbcType=VARCHAR}, #{companyId,jdbcType=CHAR}, #{orderId,jdbcType=CHAR}, 
      #{orderPrice,jdbcType=NUMERIC}, #{type,jdbcType=INTEGER}, #{isCheckSingleOrder,jdbcType=INTEGER}, 
      #{singleOrder,jdbcType=NUMERIC}, #{isCheckPerDay,jdbcType=INTEGER}, #{perDay,jdbcType=NUMERIC}, 
      #{isCheckBudget,jdbcType=INTEGER}, #{budget,jdbcType=NUMERIC}, #{isCheckCitySingleOrder,jdbcType=INTEGER}, 
      #{citySingleOrder,jdbcType=NUMERIC}, #{isCheckCityPerDay,jdbcType=INTEGER}, #{cityPerDay,jdbcType=NUMERIC}, 
      #{isCheckApplyPerDay,jdbcType=INTEGER}, #{applyPerDay,jdbcType=NUMERIC}, #{applyPerDayDesc,jdbcType=VARCHAR}, 
      #{isCheckApplyAmount,jdbcType=INTEGER}, #{applyAmount,jdbcType=NUMERIC}, #{applyAmountDesc,jdbcType=VARCHAR}, 
      #{isOpenExceedConfig,jdbcType=INTEGER}, #{isPersonalPay,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{bugetDesc,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiOrderPayDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into taxi_order_pay_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderPrice != null">
        order_price,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="isCheckSingleOrder != null">
        is_check_single_order,
      </if>
      <if test="singleOrder != null">
        single_order,
      </if>
      <if test="isCheckPerDay != null">
        is_check_per_day,
      </if>
      <if test="perDay != null">
        per_day,
      </if>
      <if test="isCheckBudget != null">
        is_check_budget,
      </if>
      <if test="budget != null">
        budget,
      </if>
      <if test="isCheckCitySingleOrder != null">
        is_check_city_single_order,
      </if>
      <if test="citySingleOrder != null">
        city_single_order,
      </if>
      <if test="isCheckCityPerDay != null">
        is_check_city_per_day,
      </if>
      <if test="cityPerDay != null">
        city_per_day,
      </if>
      <if test="isCheckApplyPerDay != null">
        is_check_apply_per_day,
      </if>
      <if test="applyPerDay != null">
        apply_per_day,
      </if>
      <if test="applyPerDayDesc != null">
        apply_per_day_desc,
      </if>
      <if test="isCheckApplyAmount != null">
        is_check_apply_amount,
      </if>
      <if test="applyAmount != null">
        apply_amount,
      </if>
      <if test="applyAmountDesc != null">
        apply_amount_desc,
      </if>
      <if test="isOpenExceedConfig != null">
        is_open_exceed_config,
      </if>
      <if test="isPersonalPay != null">
        is_personal_pay,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="bugetDesc != null">
        buget_desc,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=CHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=CHAR},
      </if>
      <if test="orderPrice != null">
        #{orderPrice,jdbcType=NUMERIC},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="isCheckSingleOrder != null">
        #{isCheckSingleOrder,jdbcType=INTEGER},
      </if>
      <if test="singleOrder != null">
        #{singleOrder,jdbcType=NUMERIC},
      </if>
      <if test="isCheckPerDay != null">
        #{isCheckPerDay,jdbcType=INTEGER},
      </if>
      <if test="perDay != null">
        #{perDay,jdbcType=NUMERIC},
      </if>
      <if test="isCheckBudget != null">
        #{isCheckBudget,jdbcType=INTEGER},
      </if>
      <if test="budget != null">
        #{budget,jdbcType=NUMERIC},
      </if>
      <if test="isCheckCitySingleOrder != null">
        #{isCheckCitySingleOrder,jdbcType=INTEGER},
      </if>
      <if test="citySingleOrder != null">
        #{citySingleOrder,jdbcType=NUMERIC},
      </if>
      <if test="isCheckCityPerDay != null">
        #{isCheckCityPerDay,jdbcType=INTEGER},
      </if>
      <if test="cityPerDay != null">
        #{cityPerDay,jdbcType=NUMERIC},
      </if>
      <if test="isCheckApplyPerDay != null">
        #{isCheckApplyPerDay,jdbcType=INTEGER},
      </if>
      <if test="applyPerDay != null">
        #{applyPerDay,jdbcType=NUMERIC},
      </if>
      <if test="applyPerDayDesc != null">
        #{applyPerDayDesc,jdbcType=VARCHAR},
      </if>
      <if test="isCheckApplyAmount != null">
        #{isCheckApplyAmount,jdbcType=INTEGER},
      </if>
      <if test="applyAmount != null">
        #{applyAmount,jdbcType=NUMERIC},
      </if>
      <if test="applyAmountDesc != null">
        #{applyAmountDesc,jdbcType=VARCHAR},
      </if>
      <if test="isOpenExceedConfig != null">
        #{isOpenExceedConfig,jdbcType=INTEGER},
      </if>
      <if test="isPersonalPay != null">
        #{isPersonalPay,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bugetDesc != null">
        #{bugetDesc,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiOrderPayDetailExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from taxi_order_pay_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update taxi_order_pay_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=CHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=CHAR},
      </if>
      <if test="record.orderPrice != null">
        order_price = #{record.orderPrice,jdbcType=NUMERIC},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.isCheckSingleOrder != null">
        is_check_single_order = #{record.isCheckSingleOrder,jdbcType=INTEGER},
      </if>
      <if test="record.singleOrder != null">
        single_order = #{record.singleOrder,jdbcType=NUMERIC},
      </if>
      <if test="record.isCheckPerDay != null">
        is_check_per_day = #{record.isCheckPerDay,jdbcType=INTEGER},
      </if>
      <if test="record.perDay != null">
        per_day = #{record.perDay,jdbcType=NUMERIC},
      </if>
      <if test="record.isCheckBudget != null">
        is_check_budget = #{record.isCheckBudget,jdbcType=INTEGER},
      </if>
      <if test="record.budget != null">
        budget = #{record.budget,jdbcType=NUMERIC},
      </if>
      <if test="record.isCheckCitySingleOrder != null">
        is_check_city_single_order = #{record.isCheckCitySingleOrder,jdbcType=INTEGER},
      </if>
      <if test="record.citySingleOrder != null">
        city_single_order = #{record.citySingleOrder,jdbcType=NUMERIC},
      </if>
      <if test="record.isCheckCityPerDay != null">
        is_check_city_per_day = #{record.isCheckCityPerDay,jdbcType=INTEGER},
      </if>
      <if test="record.cityPerDay != null">
        city_per_day = #{record.cityPerDay,jdbcType=NUMERIC},
      </if>
      <if test="record.isCheckApplyPerDay != null">
        is_check_apply_per_day = #{record.isCheckApplyPerDay,jdbcType=INTEGER},
      </if>
      <if test="record.applyPerDay != null">
        apply_per_day = #{record.applyPerDay,jdbcType=NUMERIC},
      </if>
      <if test="record.applyPerDayDesc != null">
        apply_per_day_desc = #{record.applyPerDayDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.isCheckApplyAmount != null">
        is_check_apply_amount = #{record.isCheckApplyAmount,jdbcType=INTEGER},
      </if>
      <if test="record.applyAmount != null">
        apply_amount = #{record.applyAmount,jdbcType=NUMERIC},
      </if>
      <if test="record.applyAmountDesc != null">
        apply_amount_desc = #{record.applyAmountDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.isOpenExceedConfig != null">
        is_open_exceed_config = #{record.isOpenExceedConfig,jdbcType=INTEGER},
      </if>
      <if test="record.isPersonalPay != null">
        is_personal_pay = #{record.isPersonalPay,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.bugetDesc != null">
        buget_desc = #{record.bugetDesc,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update taxi_order_pay_detail
    set id = #{record.id,jdbcType=VARCHAR},
      company_id = #{record.companyId,jdbcType=CHAR},
      order_id = #{record.orderId,jdbcType=CHAR},
      order_price = #{record.orderPrice,jdbcType=NUMERIC},
      type = #{record.type,jdbcType=INTEGER},
      is_check_single_order = #{record.isCheckSingleOrder,jdbcType=INTEGER},
      single_order = #{record.singleOrder,jdbcType=NUMERIC},
      is_check_per_day = #{record.isCheckPerDay,jdbcType=INTEGER},
      per_day = #{record.perDay,jdbcType=NUMERIC},
      is_check_budget = #{record.isCheckBudget,jdbcType=INTEGER},
      budget = #{record.budget,jdbcType=NUMERIC},
      is_check_city_single_order = #{record.isCheckCitySingleOrder,jdbcType=INTEGER},
      city_single_order = #{record.citySingleOrder,jdbcType=NUMERIC},
      is_check_city_per_day = #{record.isCheckCityPerDay,jdbcType=INTEGER},
      city_per_day = #{record.cityPerDay,jdbcType=NUMERIC},
      is_check_apply_per_day = #{record.isCheckApplyPerDay,jdbcType=INTEGER},
      apply_per_day = #{record.applyPerDay,jdbcType=NUMERIC},
      apply_per_day_desc = #{record.applyPerDayDesc,jdbcType=VARCHAR},
      is_check_apply_amount = #{record.isCheckApplyAmount,jdbcType=INTEGER},
      apply_amount = #{record.applyAmount,jdbcType=NUMERIC},
      apply_amount_desc = #{record.applyAmountDesc,jdbcType=VARCHAR},
      is_open_exceed_config = #{record.isOpenExceedConfig,jdbcType=INTEGER},
      is_personal_pay = #{record.isPersonalPay,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      buget_desc = #{record.bugetDesc,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiOrderPayDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update taxi_order_pay_detail
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=CHAR},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=CHAR},
      </if>
      <if test="orderPrice != null">
        order_price = #{orderPrice,jdbcType=NUMERIC},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="isCheckSingleOrder != null">
        is_check_single_order = #{isCheckSingleOrder,jdbcType=INTEGER},
      </if>
      <if test="singleOrder != null">
        single_order = #{singleOrder,jdbcType=NUMERIC},
      </if>
      <if test="isCheckPerDay != null">
        is_check_per_day = #{isCheckPerDay,jdbcType=INTEGER},
      </if>
      <if test="perDay != null">
        per_day = #{perDay,jdbcType=NUMERIC},
      </if>
      <if test="isCheckBudget != null">
        is_check_budget = #{isCheckBudget,jdbcType=INTEGER},
      </if>
      <if test="budget != null">
        budget = #{budget,jdbcType=NUMERIC},
      </if>
      <if test="isCheckCitySingleOrder != null">
        is_check_city_single_order = #{isCheckCitySingleOrder,jdbcType=INTEGER},
      </if>
      <if test="citySingleOrder != null">
        city_single_order = #{citySingleOrder,jdbcType=NUMERIC},
      </if>
      <if test="isCheckCityPerDay != null">
        is_check_city_per_day = #{isCheckCityPerDay,jdbcType=INTEGER},
      </if>
      <if test="cityPerDay != null">
        city_per_day = #{cityPerDay,jdbcType=NUMERIC},
      </if>
      <if test="isCheckApplyPerDay != null">
        is_check_apply_per_day = #{isCheckApplyPerDay,jdbcType=INTEGER},
      </if>
      <if test="applyPerDay != null">
        apply_per_day = #{applyPerDay,jdbcType=NUMERIC},
      </if>
      <if test="applyPerDayDesc != null">
        apply_per_day_desc = #{applyPerDayDesc,jdbcType=VARCHAR},
      </if>
      <if test="isCheckApplyAmount != null">
        is_check_apply_amount = #{isCheckApplyAmount,jdbcType=INTEGER},
      </if>
      <if test="applyAmount != null">
        apply_amount = #{applyAmount,jdbcType=NUMERIC},
      </if>
      <if test="applyAmountDesc != null">
        apply_amount_desc = #{applyAmountDesc,jdbcType=VARCHAR},
      </if>
      <if test="isOpenExceedConfig != null">
        is_open_exceed_config = #{isOpenExceedConfig,jdbcType=INTEGER},
      </if>
      <if test="isPersonalPay != null">
        is_personal_pay = #{isPersonalPay,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bugetDesc != null">
        buget_desc = #{bugetDesc,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiOrderPayDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update taxi_order_pay_detail
    set company_id = #{companyId,jdbcType=CHAR},
      order_id = #{orderId,jdbcType=CHAR},
      order_price = #{orderPrice,jdbcType=NUMERIC},
      type = #{type,jdbcType=INTEGER},
      is_check_single_order = #{isCheckSingleOrder,jdbcType=INTEGER},
      single_order = #{singleOrder,jdbcType=NUMERIC},
      is_check_per_day = #{isCheckPerDay,jdbcType=INTEGER},
      per_day = #{perDay,jdbcType=NUMERIC},
      is_check_budget = #{isCheckBudget,jdbcType=INTEGER},
      budget = #{budget,jdbcType=NUMERIC},
      is_check_city_single_order = #{isCheckCitySingleOrder,jdbcType=INTEGER},
      city_single_order = #{citySingleOrder,jdbcType=NUMERIC},
      is_check_city_per_day = #{isCheckCityPerDay,jdbcType=INTEGER},
      city_per_day = #{cityPerDay,jdbcType=NUMERIC},
      is_check_apply_per_day = #{isCheckApplyPerDay,jdbcType=INTEGER},
      apply_per_day = #{applyPerDay,jdbcType=NUMERIC},
      apply_per_day_desc = #{applyPerDayDesc,jdbcType=VARCHAR},
      is_check_apply_amount = #{isCheckApplyAmount,jdbcType=INTEGER},
      apply_amount = #{applyAmount,jdbcType=NUMERIC},
      apply_amount_desc = #{applyAmountDesc,jdbcType=VARCHAR},
      is_open_exceed_config = #{isOpenExceedConfig,jdbcType=INTEGER},
      is_personal_pay = #{isPersonalPay,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      buget_desc = #{bugetDesc,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>