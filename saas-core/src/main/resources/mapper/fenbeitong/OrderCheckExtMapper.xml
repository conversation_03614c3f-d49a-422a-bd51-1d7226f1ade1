<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.OrderCheckExtMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.OrderCheckExt">
    <id column="id" property="id" />
    <result column="name" property="name" />
    <result column="id_number" property="id_number" />
    <result column="id_type" property="id_type" />
    <result column="gender" property="gender" />
    <result column="birth_date" property="birth_date" />
    <result column="phone_num" property="phone_num" />
    <result column="is_employee" property="is_employee" />
    <result column="category" property="category" />
    <result column="descr" property="descr" />
  </resultMap>
  <select id="selectContactsByIds" resultMap="BaseResultMap">
      select fc.id, fc.name,fc.id_number,fc.id_type,fc.gender,fc.birth_date,fc.phone_num,fc.is_employee,
      fc.category,case when (e.status =4 or e.status is null) then '来自手动添加' else  ou."name" end as descr,e.status
      from frequent_contact fc
      left join employee e on e.id=fc.selected_employee_id
      left join org_unit_employee_simple oues on oues.employee_id = e."id"
      left join org_unit ou on ou."id"=oues.org_unit_id
      where fc.id in
      <foreach item="item" index="index" collection="list"
               open="(" separator="," close=")">
        #{item}
      </foreach>
  </select>

</mapper>