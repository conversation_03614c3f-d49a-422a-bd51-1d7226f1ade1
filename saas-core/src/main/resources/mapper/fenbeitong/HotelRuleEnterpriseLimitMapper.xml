<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.HotelRuleEnterpriseLimitMapper">
    <resultMap id="HotelRuleEnterpriseLimitResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.HotelRuleEnterpriseLimit">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="rule_id" property="ruleId" jdbcType="VARCHAR"/>
        <result column="hotel_id" property="hotelId" jdbcType="VARCHAR"/>
        <result column="hotel_name" property="hotelName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="is_ding" property="isDing" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="select_column_list">SELECT id,rule_id,hotel_id,hotel_name,create_time,is_ding</sql>
    <sql id="select_not_del">AND is_del = 0</sql>
    <sql id="order_by_sql">ORDER BY id DESC</sql>
    <sql id="insert_into_sql">INSERT INTO hotel_rule_enterprise_limit (id,rule_id,hotel_id,hotel_name,create_time,is_ding)</sql>
    <sql id="delete_from_sql">UPDATE hotel_rule_enterprise_limit</sql>
    <sql id="update_table_sql">UPDATE hotel_rule_enterprise_limit</sql>
    <sql id="select_count_sql">SELECT COUNT(1) FROM hotel_rule_enterprise_limit</sql>
    <sql id="from_sql">FROM hotel_rule_enterprise_limit</sql>
    <sql id="insert_table_sql">INSERT INTO hotel_rule_enterprise_limit</sql>
    <sql id="limit_1_sql">LIMIT 1</sql>


    <select id="selectByRuleId" resultType="com.fenbeitong.saas.core.model.fenbeitong.HotelRuleEnterpriseLimit" resultMap="HotelRuleEnterpriseLimitResultMap">
        <include refid="select_column_list"></include>
        from hotel_rule_enterprise_limit
        where rule_id = #{ruleId,jdbcType=VARCHAR};
    </select>
</mapper>
