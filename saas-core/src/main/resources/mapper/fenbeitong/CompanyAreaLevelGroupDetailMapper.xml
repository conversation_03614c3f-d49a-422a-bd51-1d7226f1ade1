<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.CompanyAreaLevelGroupDetailMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.CompanyAreaLevelGroupDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="company_id" jdbcType="VARCHAR" property="companyId" />
    <id column="area_code" jdbcType="VARCHAR" property="areaCode" />
    <result column="lev" jdbcType="INTEGER" property="lev" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="is_ding" jdbcType="SMALLINT" property="isDing" />
    <result column="area_group_id" jdbcType="VARCHAR" property="areaGroupId" />
    <result column="area_type" jdbcType="SMALLINT" property="areaType" />
    <result column="area_name" jdbcType="VARCHAR" property="areaName" />
    <result column="parent_area_code" jdbcType="VARCHAR" property="parentAreaCode" />
    <result column="parent_area_name" jdbcType="VARCHAR" property="parentAreaName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    company_id, area_code, lev, create_time, is_ding, area_group_id, area_type, area_name, 
    parent_area_code, parent_area_name
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.CompanyAreaLevelGroupDetailExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from company_area_level_group_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${limit} offset ${offset}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from company_area_level_group_detail
    where company_id = #{companyId,jdbcType=VARCHAR}
      and area_code = #{areaCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from company_area_level_group_detail
    where company_id = #{companyId,jdbcType=VARCHAR}
      and area_code = #{areaCode,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.CompanyAreaLevelGroupDetailExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from company_area_level_group_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.fenbeitong.CompanyAreaLevelGroupDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into company_area_level_group_detail (company_id, area_code, lev, 
      create_time, is_ding, area_group_id, 
      area_type, area_name, parent_area_code, 
      parent_area_name)
    values (#{companyId,jdbcType=VARCHAR}, #{areaCode,jdbcType=VARCHAR}, #{lev,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{isDing,jdbcType=SMALLINT}, #{areaGroupId,jdbcType=VARCHAR}, 
      #{areaType,jdbcType=SMALLINT}, #{areaName,jdbcType=VARCHAR}, #{parentAreaCode,jdbcType=VARCHAR}, 
      #{parentAreaName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.CompanyAreaLevelGroupDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into company_area_level_group_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="areaCode != null">
        area_code,
      </if>
      <if test="lev != null">
        lev,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="isDing != null">
        is_ding,
      </if>
      <if test="areaGroupId != null">
        area_group_id,
      </if>
      <if test="areaType != null">
        area_type,
      </if>
      <if test="areaName != null">
        area_name,
      </if>
      <if test="parentAreaCode != null">
        parent_area_code,
      </if>
      <if test="parentAreaName != null">
        parent_area_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="areaCode != null">
        #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="lev != null">
        #{lev,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDing != null">
        #{isDing,jdbcType=SMALLINT},
      </if>
      <if test="areaGroupId != null">
        #{areaGroupId,jdbcType=VARCHAR},
      </if>
      <if test="areaType != null">
        #{areaType,jdbcType=SMALLINT},
      </if>
      <if test="areaName != null">
        #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="parentAreaCode != null">
        #{parentAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="parentAreaName != null">
        #{parentAreaName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.CompanyAreaLevelGroupDetailExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from company_area_level_group_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update company_area_level_group_detail
    <set>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.areaCode != null">
        area_code = #{record.areaCode,jdbcType=VARCHAR},
      </if>
      <if test="record.lev != null">
        lev = #{record.lev,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDing != null">
        is_ding = #{record.isDing,jdbcType=SMALLINT},
      </if>
      <if test="record.areaGroupId != null">
        area_group_id = #{record.areaGroupId,jdbcType=VARCHAR},
      </if>
      <if test="record.areaType != null">
        area_type = #{record.areaType,jdbcType=SMALLINT},
      </if>
      <if test="record.areaName != null">
        area_name = #{record.areaName,jdbcType=VARCHAR},
      </if>
      <if test="record.parentAreaCode != null">
        parent_area_code = #{record.parentAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="record.parentAreaName != null">
        parent_area_name = #{record.parentAreaName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update company_area_level_group_detail
    set company_id = #{record.companyId,jdbcType=VARCHAR},
      area_code = #{record.areaCode,jdbcType=VARCHAR},
      lev = #{record.lev,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      is_ding = #{record.isDing,jdbcType=SMALLINT},
      area_group_id = #{record.areaGroupId,jdbcType=VARCHAR},
      area_type = #{record.areaType,jdbcType=SMALLINT},
      area_name = #{record.areaName,jdbcType=VARCHAR},
      parent_area_code = #{record.parentAreaCode,jdbcType=VARCHAR},
      parent_area_name = #{record.parentAreaName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.CompanyAreaLevelGroupDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update company_area_level_group_detail
    <set>
      <if test="lev != null">
        lev = #{lev,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDing != null">
        is_ding = #{isDing,jdbcType=SMALLINT},
      </if>
      <if test="areaGroupId != null">
        area_group_id = #{areaGroupId,jdbcType=VARCHAR},
      </if>
      <if test="areaType != null">
        area_type = #{areaType,jdbcType=SMALLINT},
      </if>
      <if test="areaName != null">
        area_name = #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="parentAreaCode != null">
        parent_area_code = #{parentAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="parentAreaName != null">
        parent_area_name = #{parentAreaName,jdbcType=VARCHAR},
      </if>
    </set>
    where company_id = #{companyId,jdbcType=VARCHAR}
      and area_code = #{areaCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.fenbeitong.CompanyAreaLevelGroupDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update company_area_level_group_detail
    set lev = #{lev,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      is_ding = #{isDing,jdbcType=SMALLINT},
      area_group_id = #{areaGroupId,jdbcType=VARCHAR},
      area_type = #{areaType,jdbcType=SMALLINT},
      area_name = #{areaName,jdbcType=VARCHAR},
      parent_area_code = #{parentAreaCode,jdbcType=VARCHAR},
      parent_area_name = #{parentAreaName,jdbcType=VARCHAR}
    where company_id = #{companyId,jdbcType=VARCHAR}
      and area_code = #{areaCode,jdbcType=VARCHAR}
  </update>
</mapper>