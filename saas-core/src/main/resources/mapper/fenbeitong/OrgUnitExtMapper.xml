<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.OrgUnitExtMapper">

  <select id="queryParentOrgUnitList" parameterType="map" resultMap="com.fenbeitong.saas.core.dao.fenbeitong.OrgUnitMapper.BaseResultMap">
    WITH RECURSIVE dept_parent AS (
        SELECT o1.*
        FROM org_unit o1
        WHERE o1.id=#{orgUnitId}
        UNION ALL
        SELECT o2.*
        FROM org_unit o2
        INNER JOIN dept_parent dp ON dp.parent_org_unit_id=o2.id
    )SELECT * FROM dept_parent WHERE company_id=#{companyId}
  </select>

</mapper>