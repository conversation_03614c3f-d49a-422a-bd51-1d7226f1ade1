<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.TakeawayTimeRangeExtMapper">
    <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.TimeRange">
        <constructor>
            <arg column="day_type" javaType="java.lang.Integer" jdbcType="INTEGER"/>
            <arg column="begin_time" javaType="java.util.Date" jdbcType="TIME"/>
            <arg column="end_time" javaType="java.util.Date" jdbcType="TIME"/>
            <arg column="is_overnight" javaType="java.lang.Boolean" jdbcType="BIT"/>
            <arg column="frequency_limit_price" javaType="java.math.BigDecimal" jdbcType="NUMERIC"/>
            <arg column="accumulative_limit_price" javaType="java.math.BigDecimal" jdbcType="NUMERIC"/>
            <arg column="order_limit_num" javaType="java.lang.Integer" jdbcType="INTEGER"/>
        </constructor>
    </resultMap>
    <sql id="Base_Column_List">
        day_type, begin_time, end_time, is_overnight
    </sql>

    <select id="queryEmployeeTakeawayRuleRangeTime" resultMap="BaseResultMap">
        select day_type, begin_time, end_time, is_overnight, frequency_limit_price, accumulative_limit_price, order_limit_num
        from takeaway_time_range
        where rule_id=#{ruleId,jdbcType=INTEGER}
        group by day_type,begin_time,end_time,is_overnight,frequency_limit_price,accumulative_limit_price,order_limit_num
        order by day_type,begin_time
    </select>
</mapper>