<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.HotelRuleGroupDetailMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.HotelRuleGroupDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="hotel_rule_id" jdbcType="CHAR" property="hotelRuleId" />
    <result column="company_id" jdbcType="CHAR" property="companyId" />
    <result column="company_area_level_group_id" jdbcType="VARCHAR" property="companyAreaLevelGroupId" />
    <result column="company_area_level_group_name" jdbcType="VARCHAR" property="companyAreaLevelGroupName" />
    <result column="group_tier_flag" jdbcType="BIT" property="groupTierFlag" />
    <result column="group_tier_price" jdbcType="NUMERIC" property="groupTierPrice" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, hotel_rule_id, company_id, company_area_level_group_id, company_area_level_group_name, 
    group_tier_flag, group_tier_price
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.HotelRuleGroupDetailExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from hotel_rule_group_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${limit} offset ${offset}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from hotel_rule_group_detail
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from hotel_rule_group_detail
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.HotelRuleGroupDetailExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from hotel_rule_group_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.fenbeitong.HotelRuleGroupDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into hotel_rule_group_detail (id, hotel_rule_id, company_id, 
      company_area_level_group_id, company_area_level_group_name, 
      group_tier_flag, group_tier_price)
    values (#{id,jdbcType=CHAR}, #{hotelRuleId,jdbcType=CHAR}, #{companyId,jdbcType=CHAR}, 
      #{companyAreaLevelGroupId,jdbcType=VARCHAR}, #{companyAreaLevelGroupName,jdbcType=VARCHAR}, 
      #{groupTierFlag,jdbcType=BIT}, #{groupTierPrice,jdbcType=NUMERIC})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.HotelRuleGroupDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into hotel_rule_group_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="hotelRuleId != null">
        hotel_rule_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="companyAreaLevelGroupId != null">
        company_area_level_group_id,
      </if>
      <if test="companyAreaLevelGroupName != null">
        company_area_level_group_name,
      </if>
      <if test="groupTierFlag != null">
        group_tier_flag,
      </if>
      <if test="groupTierPrice != null">
        group_tier_price,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="hotelRuleId != null">
        #{hotelRuleId,jdbcType=CHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=CHAR},
      </if>
      <if test="companyAreaLevelGroupId != null">
        #{companyAreaLevelGroupId,jdbcType=VARCHAR},
      </if>
      <if test="companyAreaLevelGroupName != null">
        #{companyAreaLevelGroupName,jdbcType=VARCHAR},
      </if>
      <if test="groupTierFlag != null">
        #{groupTierFlag,jdbcType=BIT},
      </if>
      <if test="groupTierPrice != null">
        #{groupTierPrice,jdbcType=NUMERIC},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.HotelRuleGroupDetailExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from hotel_rule_group_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update hotel_rule_group_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.hotelRuleId != null">
        hotel_rule_id = #{record.hotelRuleId,jdbcType=CHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=CHAR},
      </if>
      <if test="record.companyAreaLevelGroupId != null">
        company_area_level_group_id = #{record.companyAreaLevelGroupId,jdbcType=VARCHAR},
      </if>
      <if test="record.companyAreaLevelGroupName != null">
        company_area_level_group_name = #{record.companyAreaLevelGroupName,jdbcType=VARCHAR},
      </if>
      <if test="record.groupTierFlag != null">
        group_tier_flag = #{record.groupTierFlag,jdbcType=BIT},
      </if>
      <if test="record.groupTierPrice != null">
        group_tier_price = #{record.groupTierPrice,jdbcType=NUMERIC},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update hotel_rule_group_detail
    set id = #{record.id,jdbcType=CHAR},
      hotel_rule_id = #{record.hotelRuleId,jdbcType=CHAR},
      company_id = #{record.companyId,jdbcType=CHAR},
      company_area_level_group_id = #{record.companyAreaLevelGroupId,jdbcType=VARCHAR},
      company_area_level_group_name = #{record.companyAreaLevelGroupName,jdbcType=VARCHAR},
      group_tier_flag = #{record.groupTierFlag,jdbcType=BIT},
      group_tier_price = #{record.groupTierPrice,jdbcType=NUMERIC}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.HotelRuleGroupDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update hotel_rule_group_detail
    <set>
      <if test="hotelRuleId != null">
        hotel_rule_id = #{hotelRuleId,jdbcType=CHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=CHAR},
      </if>
      <if test="companyAreaLevelGroupId != null">
        company_area_level_group_id = #{companyAreaLevelGroupId,jdbcType=VARCHAR},
      </if>
      <if test="companyAreaLevelGroupName != null">
        company_area_level_group_name = #{companyAreaLevelGroupName,jdbcType=VARCHAR},
      </if>
      <if test="groupTierFlag != null">
        group_tier_flag = #{groupTierFlag,jdbcType=BIT},
      </if>
      <if test="groupTierPrice != null">
        group_tier_price = #{groupTierPrice,jdbcType=NUMERIC},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.fenbeitong.HotelRuleGroupDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update hotel_rule_group_detail
    set hotel_rule_id = #{hotelRuleId,jdbcType=CHAR},
      company_id = #{companyId,jdbcType=CHAR},
      company_area_level_group_id = #{companyAreaLevelGroupId,jdbcType=VARCHAR},
      company_area_level_group_name = #{companyAreaLevelGroupName,jdbcType=VARCHAR},
      group_tier_flag = #{groupTierFlag,jdbcType=BIT},
      group_tier_price = #{groupTierPrice,jdbcType=NUMERIC}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>