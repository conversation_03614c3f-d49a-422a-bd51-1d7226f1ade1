<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.TaxiTimeRangeExtMapper">
    <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.TaxiTimeRange">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed Sep 06 18:12:27 CST 2017.
        -->
        <constructor>
            <idArg column="id" javaType="java.lang.Integer" jdbcType="INTEGER"/>
            <arg column="rule_id" javaType="java.lang.Integer" jdbcType="INTEGER"/>
            <arg column="day_type" javaType="java.lang.Integer" jdbcType="INTEGER"/>
            <arg column="begin_time" javaType="java.util.Date" jdbcType="TIME"/>
            <arg column="end_time" javaType="java.util.Date" jdbcType="TIME"/>
            <arg column="is_overnight" javaType="java.lang.Boolean" jdbcType="BIT"/>
            <arg column="batch_id" jdbcType="BIGINT" javaType="java.lang.Long"/>
            <arg column="dingtalk_flag" jdbcType="BIT" javaType="java.lang.Boolean" />
        </constructor>
    </resultMap>
    <sql id="Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed Sep 06 18:12:27 CST 2017.
        -->
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed Sep 06 18:12:27 CST 2017.
        -->
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed Sep 06 18:12:27 CST 2017.
        -->
        id, rule_id, day_type, begin_time, end_time, is_overnight, batch_id, dingtalk_flag
    </sql>

    <select id="queryRuleRangeTimeGroup" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from taxi_time_range
        where rule_id = #{ruleId,jdbcType=INTEGER}
        order by batch_id DESC , day_type asc
    </select>

    <select id="queryRuleRangeTimeGroupByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from taxi_time_range
        where rule_id in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        order by rule_id, batch_id DESC , day_type asc
    </select>

    <!--获取用车最新一条数据-->
    <select id="queryRuleRangeTimeList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from taxi_time_range
        where rule_id= #{ruleId,jdbcType=INTEGER} and
        batch_id=(select batch_id from taxi_time_range
        where rule_id= #{ruleId,jdbcType=INTEGER} group by batch_id
        order by batch_id desc limit 1)
        order by day_type
    </select>

</mapper>