<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.HotelRuleMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.HotelRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="company_id" jdbcType="CHAR" property="companyId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="hotel_price_flag" jdbcType="BIT" property="hotelPriceFlag" />
    <result column="hotel_unit_price" jdbcType="NUMERIC" property="hotelUnitPrice" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="first_tier_flag" jdbcType="BIT" property="firstTierFlag" />
    <result column="first_tier_price" jdbcType="NUMERIC" property="firstTierPrice" />
    <result column="second_tier_flag" jdbcType="BIT" property="secondTierFlag" />
    <result column="second_tier_price" jdbcType="NUMERIC" property="secondTierPrice" />
    <result column="other_tier_flag" jdbcType="BIT" property="otherTierFlag" />
    <result column="other_tier_price" jdbcType="NUMERIC" property="otherTierPrice" />
    <result column="level" jdbcType="VARCHAR" property="level" />
    <result column="level_flag" jdbcType="BIT" property="levelFlag" />
    <result column="priv_day_min" jdbcType="INTEGER" property="privDayMin" />
    <result column="priv_day_max" jdbcType="INTEGER" property="privDayMax" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="global_price" jdbcType="NUMERIC" property="globalPrice" />
    <result column="rule_limit" jdbcType="BIT" property="ruleLimit" />
    <result column="brand_limit" jdbcType="INTEGER" property="brandLimit" />
    <result column="brand_limit_type" jdbcType="INTEGER" property="brandLimitType" />
    <result column="is_ding" jdbcType="SMALLINT" property="isDing" />
    <result column="max_reverse_day_limit_flag" jdbcType="BIT" property="maxReverseDayLimitFlag" />
    <result column="max_reverse_day_limit" jdbcType="INTEGER" property="maxReverseDayLimit" />
    <result column="employee_base_location_control" jdbcType="INTEGER" property="employeeBaseLocationControl" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, name, hotel_price_flag, hotel_unit_price, modify_time, first_tier_flag, 
    first_tier_price, second_tier_flag, second_tier_price, other_tier_flag, other_tier_price, 
    level, level_flag, priv_day_min, priv_day_max, type, global_price, rule_limit, brand_limit, 
    brand_limit_type, is_ding, max_reverse_day_limit_flag, max_reverse_day_limit, employee_base_location_control
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.HotelRuleExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from hotel_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${limit} offset ${offset}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from hotel_rule
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from hotel_rule
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.HotelRuleExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from hotel_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.fenbeitong.HotelRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into hotel_rule (id, company_id, name, 
      hotel_price_flag, hotel_unit_price, modify_time, 
      first_tier_flag, first_tier_price, second_tier_flag, 
      second_tier_price, other_tier_flag, other_tier_price, 
      level, level_flag, priv_day_min, 
      priv_day_max, type, global_price, 
      rule_limit, brand_limit, brand_limit_type,
      is_ding, max_reverse_day_limit_flag, max_reverse_day_limit,
      employee_base_location_control)
    values (#{id,jdbcType=CHAR}, #{companyId,jdbcType=CHAR}, #{name,jdbcType=VARCHAR}, 
      #{hotelPriceFlag,jdbcType=BIT}, #{hotelUnitPrice,jdbcType=NUMERIC}, #{modifyTime,jdbcType=TIMESTAMP}, 
      #{firstTierFlag,jdbcType=BIT}, #{firstTierPrice,jdbcType=NUMERIC}, #{secondTierFlag,jdbcType=BIT}, 
      #{secondTierPrice,jdbcType=NUMERIC}, #{otherTierFlag,jdbcType=BIT}, #{otherTierPrice,jdbcType=NUMERIC}, 
      #{level,jdbcType=VARCHAR}, #{levelFlag,jdbcType=BIT}, #{privDayMin,jdbcType=INTEGER}, 
      #{privDayMax,jdbcType=INTEGER}, #{type,jdbcType=INTEGER}, #{globalPrice,jdbcType=NUMERIC}, 
      #{ruleLimit,jdbcType=BIT}, #{brandLimit,jdbcType=INTEGER}, #{brandLimitType,jdbcType=INTEGER},
      #{isDing,jdbcType=SMALLINT}, #{maxReverseDayLimitFlag,jdbcType=BIT}, #{maxReverseDayLimit,jdbcType=INTEGER},
      #{employeeBaseLocationControl,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.HotelRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into hotel_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="hotelPriceFlag != null">
        hotel_price_flag,
      </if>
      <if test="hotelUnitPrice != null">
        hotel_unit_price,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="firstTierFlag != null">
        first_tier_flag,
      </if>
      <if test="firstTierPrice != null">
        first_tier_price,
      </if>
      <if test="secondTierFlag != null">
        second_tier_flag,
      </if>
      <if test="secondTierPrice != null">
        second_tier_price,
      </if>
      <if test="otherTierFlag != null">
        other_tier_flag,
      </if>
      <if test="otherTierPrice != null">
        other_tier_price,
      </if>
      <if test="level != null">
        level,
      </if>
      <if test="levelFlag != null">
        level_flag,
      </if>
      <if test="privDayMin != null">
        priv_day_min,
      </if>
      <if test="privDayMax != null">
        priv_day_max,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="globalPrice != null">
        global_price,
      </if>
      <if test="ruleLimit != null">
        rule_limit,
      </if>
      <if test="brandLimit != null">
        brand_limit,
      </if>
      <if test="brandLimitType != null">
        brand_limit_type,
      </if>
      <if test="isDing != null">
        is_ding,
      </if>
      <if test="maxReverseDayLimitFlag != null">
        max_reverse_day_limit_flag,
      </if>
      <if test="maxReverseDayLimit != null">
        max_reverse_day_limit,
      </if>
      <if test="employeeBaseLocationControl != null">
        employee_base_location_control,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=CHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="hotelPriceFlag != null">
        #{hotelPriceFlag,jdbcType=BIT},
      </if>
      <if test="hotelUnitPrice != null">
        #{hotelUnitPrice,jdbcType=NUMERIC},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="firstTierFlag != null">
        #{firstTierFlag,jdbcType=BIT},
      </if>
      <if test="firstTierPrice != null">
        #{firstTierPrice,jdbcType=NUMERIC},
      </if>
      <if test="secondTierFlag != null">
        #{secondTierFlag,jdbcType=BIT},
      </if>
      <if test="secondTierPrice != null">
        #{secondTierPrice,jdbcType=NUMERIC},
      </if>
      <if test="otherTierFlag != null">
        #{otherTierFlag,jdbcType=BIT},
      </if>
      <if test="otherTierPrice != null">
        #{otherTierPrice,jdbcType=NUMERIC},
      </if>
      <if test="level != null">
        #{level,jdbcType=VARCHAR},
      </if>
      <if test="levelFlag != null">
        #{levelFlag,jdbcType=BIT},
      </if>
      <if test="privDayMin != null">
        #{privDayMin,jdbcType=INTEGER},
      </if>
      <if test="privDayMax != null">
        #{privDayMax,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="globalPrice != null">
        #{globalPrice,jdbcType=NUMERIC},
      </if>
      <if test="ruleLimit != null">
        #{ruleLimit,jdbcType=BIT},
      </if>
      <if test="brandLimit != null">
        #{brandLimit,jdbcType=INTEGER},
      </if>
      <if test="brandLimitType != null">
        #{brandLimitType,jdbcType=INTEGER},
      </if>
      <if test="isDing != null">
        #{isDing,jdbcType=SMALLINT},
      </if>
      <if test="maxReverseDayLimitFlag != null">
        #{maxReverseDayLimitFlag,jdbcType=BIT},
      </if>
      <if test="maxReverseDayLimit != null">
        #{maxReverseDayLimit,jdbcType=INTEGER},
      </if>
      <if test="employeeBaseLocationControl != null">
        #{employeeBaseLocationControl,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.HotelRuleExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from hotel_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update hotel_rule
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=CHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.hotelPriceFlag != null">
        hotel_price_flag = #{record.hotelPriceFlag,jdbcType=BIT},
      </if>
      <if test="record.hotelUnitPrice != null">
        hotel_unit_price = #{record.hotelUnitPrice,jdbcType=NUMERIC},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.firstTierFlag != null">
        first_tier_flag = #{record.firstTierFlag,jdbcType=BIT},
      </if>
      <if test="record.firstTierPrice != null">
        first_tier_price = #{record.firstTierPrice,jdbcType=NUMERIC},
      </if>
      <if test="record.secondTierFlag != null">
        second_tier_flag = #{record.secondTierFlag,jdbcType=BIT},
      </if>
      <if test="record.secondTierPrice != null">
        second_tier_price = #{record.secondTierPrice,jdbcType=NUMERIC},
      </if>
      <if test="record.otherTierFlag != null">
        other_tier_flag = #{record.otherTierFlag,jdbcType=BIT},
      </if>
      <if test="record.otherTierPrice != null">
        other_tier_price = #{record.otherTierPrice,jdbcType=NUMERIC},
      </if>
      <if test="record.level != null">
        level = #{record.level,jdbcType=VARCHAR},
      </if>
      <if test="record.levelFlag != null">
        level_flag = #{record.levelFlag,jdbcType=BIT},
      </if>
      <if test="record.privDayMin != null">
        priv_day_min = #{record.privDayMin,jdbcType=INTEGER},
      </if>
      <if test="record.privDayMax != null">
        priv_day_max = #{record.privDayMax,jdbcType=INTEGER},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.globalPrice != null">
        global_price = #{record.globalPrice,jdbcType=NUMERIC},
      </if>
      <if test="record.ruleLimit != null">
        rule_limit = #{record.ruleLimit,jdbcType=BIT},
      </if>
      <if test="record.brandLimit != null">
        brand_limit = #{record.brandLimit,jdbcType=INTEGER},
      </if>
      <if test="record.brandLimitType != null">
        brand_limit_type = #{record.brandLimitType,jdbcType=INTEGER},
      </if>
      <if test="record.isDing != null">
        is_ding = #{record.isDing,jdbcType=SMALLINT},
      </if>
      <if test="record.maxReverseDayLimitFlag != null">
        max_reverse_day_limit_flag = #{record.maxReverseDayLimitFlag,jdbcType=BIT},
      </if>
      <if test="record.maxReverseDayLimit != null">
        max_reverse_day_limit = #{record.maxReverseDayLimit,jdbcType=INTEGER},
      </if>
      <if test="record.employeeBaseLocationControl != null">
        employee_base_location_control = #{record.employeeBaseLocationControl,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update hotel_rule
    set id = #{record.id,jdbcType=CHAR},
      company_id = #{record.companyId,jdbcType=CHAR},
      name = #{record.name,jdbcType=VARCHAR},
      hotel_price_flag = #{record.hotelPriceFlag,jdbcType=BIT},
      hotel_unit_price = #{record.hotelUnitPrice,jdbcType=NUMERIC},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      first_tier_flag = #{record.firstTierFlag,jdbcType=BIT},
      first_tier_price = #{record.firstTierPrice,jdbcType=NUMERIC},
      second_tier_flag = #{record.secondTierFlag,jdbcType=BIT},
      second_tier_price = #{record.secondTierPrice,jdbcType=NUMERIC},
      other_tier_flag = #{record.otherTierFlag,jdbcType=BIT},
      other_tier_price = #{record.otherTierPrice,jdbcType=NUMERIC},
      level = #{record.level,jdbcType=VARCHAR},
      level_flag = #{record.levelFlag,jdbcType=BIT},
      priv_day_min = #{record.privDayMin,jdbcType=INTEGER},
      priv_day_max = #{record.privDayMax,jdbcType=INTEGER},
      type = #{record.type,jdbcType=INTEGER},
      global_price = #{record.globalPrice,jdbcType=NUMERIC},
      rule_limit = #{record.ruleLimit,jdbcType=BIT},
      brand_limit = #{record.brandLimit,jdbcType=INTEGER},
      brand_limit_type = #{record.brandLimitType,jdbcType=INTEGER},
      is_ding = #{record.isDing,jdbcType=SMALLINT},
      max_reverse_day_limit_flag = #{record.maxReverseDayLimitFlag,jdbcType=BIT},
      max_reverse_day_limit = #{record.maxReverseDayLimit,jdbcType=INTEGER},
      employee_base_location_control = #{record.employeeBaseLocationControl,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.HotelRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update hotel_rule
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=CHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="hotelPriceFlag != null">
        hotel_price_flag = #{hotelPriceFlag,jdbcType=BIT},
      </if>
      <if test="hotelUnitPrice != null">
        hotel_unit_price = #{hotelUnitPrice,jdbcType=NUMERIC},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="firstTierFlag != null">
        first_tier_flag = #{firstTierFlag,jdbcType=BIT},
      </if>
      <if test="firstTierPrice != null">
        first_tier_price = #{firstTierPrice,jdbcType=NUMERIC},
      </if>
      <if test="secondTierFlag != null">
        second_tier_flag = #{secondTierFlag,jdbcType=BIT},
      </if>
      <if test="secondTierPrice != null">
        second_tier_price = #{secondTierPrice,jdbcType=NUMERIC},
      </if>
      <if test="otherTierFlag != null">
        other_tier_flag = #{otherTierFlag,jdbcType=BIT},
      </if>
      <if test="otherTierPrice != null">
        other_tier_price = #{otherTierPrice,jdbcType=NUMERIC},
      </if>
      <if test="level != null">
        level = #{level,jdbcType=VARCHAR},
      </if>
      <if test="levelFlag != null">
        level_flag = #{levelFlag,jdbcType=BIT},
      </if>
      <if test="privDayMin != null">
        priv_day_min = #{privDayMin,jdbcType=INTEGER},
      </if>
      <if test="privDayMax != null">
        priv_day_max = #{privDayMax,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="globalPrice != null">
        global_price = #{globalPrice,jdbcType=NUMERIC},
      </if>
      <if test="ruleLimit != null">
        rule_limit = #{ruleLimit,jdbcType=BIT},
      </if>
      <if test="brandLimit != null">
        brand_limit = #{brandLimit,jdbcType=INTEGER},
      </if>
      <if test="brandLimitType != null">
        brand_limit_type = #{brandLimitType,jdbcType=INTEGER},
      </if>
      <if test="isDing != null">
        is_ding = #{isDing,jdbcType=SMALLINT},
      </if>
      <if test="maxReverseDayLimitFlag != null">
        max_reverse_day_limit_flag = #{maxReverseDayLimitFlag,jdbcType=BIT},
      </if>
      <if test="maxReverseDayLimit != null">
        max_reverse_day_limit = #{maxReverseDayLimit,jdbcType=INTEGER},
      </if>
      <if test="employeeBaseLocationControl != null">
        employee_base_location_control = #{employeeBaseLocationControl,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.fenbeitong.HotelRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update hotel_rule
    set company_id = #{companyId,jdbcType=CHAR},
      name = #{name,jdbcType=VARCHAR},
      hotel_price_flag = #{hotelPriceFlag,jdbcType=BIT},
      hotel_unit_price = #{hotelUnitPrice,jdbcType=NUMERIC},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      first_tier_flag = #{firstTierFlag,jdbcType=BIT},
      first_tier_price = #{firstTierPrice,jdbcType=NUMERIC},
      second_tier_flag = #{secondTierFlag,jdbcType=BIT},
      second_tier_price = #{secondTierPrice,jdbcType=NUMERIC},
      other_tier_flag = #{otherTierFlag,jdbcType=BIT},
      other_tier_price = #{otherTierPrice,jdbcType=NUMERIC},
      level = #{level,jdbcType=VARCHAR},
      level_flag = #{levelFlag,jdbcType=BIT},
      priv_day_min = #{privDayMin,jdbcType=INTEGER},
      priv_day_max = #{privDayMax,jdbcType=INTEGER},
      type = #{type,jdbcType=INTEGER},
      global_price = #{globalPrice,jdbcType=NUMERIC},
      rule_limit = #{ruleLimit,jdbcType=BIT},
      brand_limit = #{brandLimit,jdbcType=INTEGER},
      brand_limit_type = #{brandLimitType,jdbcType=INTEGER},
      is_ding = #{isDing,jdbcType=SMALLINT},
      max_reverse_day_limit_flag = #{maxReverseDayLimitFlag,jdbcType=BIT},
      max_reverse_day_limit = #{maxReverseDayLimit,jdbcType=INTEGER},
      employee_base_location_control = #{employeeBaseLocationControl,jdbcType=INTEGER}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>