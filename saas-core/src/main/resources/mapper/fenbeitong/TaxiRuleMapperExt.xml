<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.TaxiRuleMapperExt">

    <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.TaxiRule"
               extends="com.fenbeitong.saas.core.dao.fenbeitong.TaxiRuleMapper.BaseResultMap">

    </resultMap>

    <select id="queryTaxiRuleByLocationId" resultMap="BaseResultMap">
        select *
        from taxi_rule tr
        where
        tr.company_id = #{companyId,jdbcType=CHAR} and
        ','||tr.departure_location_id||',' like concat('%,',#{locationId,jdbcType=INTEGER},',%')
    </select>

    <update id="updateTaxiRuleById" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiRule">
        update taxi_rule
        set company_id = #{companyId,jdbcType=CHAR},
            limit_departure = #{limitDeparture,jdbcType=BIT},
            departure_location_id = #{departureLocationId,jdbcType=VARCHAR}
        where id = #{id,jdbcType=INTEGER}
        and company_id = #{companyId,jdbcType=CHAR}
    </update>
</mapper>