<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.DinnerRuleMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.DinnerRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="dinner_frequency_limit_price" jdbcType="NUMERIC" property="dinnerFrequencyLimitPrice" />
    <result column="dinner_everyday_limit_price" jdbcType="NUMERIC" property="dinnerEverydayLimitPrice" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_ding" jdbcType="SMALLINT" property="isDing" />
    <result column="scene_id" jdbcType="SMALLINT" property="sceneId" />
    <result column="rule_type" jdbcType="INTEGER" property="ruleType" />
    <result column="modify_flag" jdbcType="BIT" property="modifyFlag" />
    <result column="apply_id_require" jdbcType="SMALLINT" property="applyIdRequire" />
    <result column="check_rule" jdbcType="BIT" property="checkRule" />
    <result column="check_diner_number" jdbcType="INTEGER" property="checkDinerNumber" />
    <result column="compare_type" jdbcType="SMALLINT" property="compareType" />
    <result column="is_after_control" jdbcType="INTEGER" property="isAfterControl" />
    <result column="is_open_order_price_limit" jdbcType="INTEGER" property="isOpenOrderPriceLimit" />
    <result column="order_price_limit" jdbcType="NUMERIC" property="orderPriceLimit" />
    <result column="order_price_field_config" jdbcType="INTEGER" property="orderPriceFieldConfig" />
    <result column="order_price_field_title" jdbcType="VARCHAR" property="orderPriceFieldTitle" />
    <result column="order_price_field_msg" jdbcType="VARCHAR" property="orderPriceFieldMsg" />
    <result column="is_open_order_exceed_limit" jdbcType="INTEGER" property="isOpenOrderExceedLimit" />
    <result column="order_exceed_field_config" jdbcType="INTEGER" property="orderExceedFieldConfig" />
    <result column="order_exceed_field_title" jdbcType="VARCHAR" property="orderExceedFieldTitle" />
    <result column="order_exceed_field_msg" jdbcType="VARCHAR" property="orderExceedFieldMsg" />
    <result column="is_manage_confirm_order" jdbcType="INTEGER" property="isManageConfirmOrder" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, name, dinner_frequency_limit_price, dinner_everyday_limit_price, 
    create_time, update_time, is_ding, scene_id, rule_type, modify_flag, apply_id_require, 
    check_rule, check_diner_number, compare_type, is_after_control, is_open_order_price_limit, 
    order_price_limit, order_price_field_config, order_price_field_title, order_price_field_msg, 
    is_open_order_exceed_limit, order_exceed_field_config, order_exceed_field_title, 
    order_exceed_field_msg, is_manage_confirm_order
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.DinnerRuleExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from dinner_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${limit} offset ${offset}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from dinner_rule
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from dinner_rule
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.DinnerRuleExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from dinner_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.fenbeitong.DinnerRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into dinner_rule (id, company_id, name, 
      dinner_frequency_limit_price, dinner_everyday_limit_price, 
      create_time, update_time, is_ding, 
      scene_id, rule_type, modify_flag, 
      apply_id_require, check_rule, check_diner_number, 
      compare_type, is_after_control, is_open_order_price_limit, 
      order_price_limit, order_price_field_config, 
      order_price_field_title, order_price_field_msg, 
      is_open_order_exceed_limit, order_exceed_field_config, 
      order_exceed_field_title, order_exceed_field_msg, 
      is_manage_confirm_order)
    values (#{id,jdbcType=CHAR}, #{companyId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{dinnerFrequencyLimitPrice,jdbcType=NUMERIC}, #{dinnerEverydayLimitPrice,jdbcType=NUMERIC}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{isDing,jdbcType=SMALLINT}, 
      #{sceneId,jdbcType=SMALLINT}, #{ruleType,jdbcType=INTEGER}, #{modifyFlag,jdbcType=BIT}, 
      #{applyIdRequire,jdbcType=SMALLINT}, #{checkRule,jdbcType=BIT}, #{checkDinerNumber,jdbcType=INTEGER}, 
      #{compareType,jdbcType=SMALLINT}, #{isAfterControl,jdbcType=INTEGER}, #{isOpenOrderPriceLimit,jdbcType=INTEGER}, 
      #{orderPriceLimit,jdbcType=NUMERIC}, #{orderPriceFieldConfig,jdbcType=INTEGER}, 
      #{orderPriceFieldTitle,jdbcType=VARCHAR}, #{orderPriceFieldMsg,jdbcType=VARCHAR}, 
      #{isOpenOrderExceedLimit,jdbcType=INTEGER}, #{orderExceedFieldConfig,jdbcType=INTEGER}, 
      #{orderExceedFieldTitle,jdbcType=VARCHAR}, #{orderExceedFieldMsg,jdbcType=VARCHAR}, 
      #{isManageConfirmOrder,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.DinnerRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into dinner_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="dinnerFrequencyLimitPrice != null">
        dinner_frequency_limit_price,
      </if>
      <if test="dinnerEverydayLimitPrice != null">
        dinner_everyday_limit_price,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDing != null">
        is_ding,
      </if>
      <if test="sceneId != null">
        scene_id,
      </if>
      <if test="ruleType != null">
        rule_type,
      </if>
      <if test="modifyFlag != null">
        modify_flag,
      </if>
      <if test="applyIdRequire != null">
        apply_id_require,
      </if>
      <if test="checkRule != null">
        check_rule,
      </if>
      <if test="checkDinerNumber != null">
        check_diner_number,
      </if>
      <if test="compareType != null">
        compare_type,
      </if>
      <if test="isAfterControl != null">
        is_after_control,
      </if>
      <if test="isOpenOrderPriceLimit != null">
        is_open_order_price_limit,
      </if>
      <if test="orderPriceLimit != null">
        order_price_limit,
      </if>
      <if test="orderPriceFieldConfig != null">
        order_price_field_config,
      </if>
      <if test="orderPriceFieldTitle != null">
        order_price_field_title,
      </if>
      <if test="orderPriceFieldMsg != null">
        order_price_field_msg,
      </if>
      <if test="isOpenOrderExceedLimit != null">
        is_open_order_exceed_limit,
      </if>
      <if test="orderExceedFieldConfig != null">
        order_exceed_field_config,
      </if>
      <if test="orderExceedFieldTitle != null">
        order_exceed_field_title,
      </if>
      <if test="orderExceedFieldMsg != null">
        order_exceed_field_msg,
      </if>
      <if test="isManageConfirmOrder != null">
        is_manage_confirm_order,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="dinnerFrequencyLimitPrice != null">
        #{dinnerFrequencyLimitPrice,jdbcType=NUMERIC},
      </if>
      <if test="dinnerEverydayLimitPrice != null">
        #{dinnerEverydayLimitPrice,jdbcType=NUMERIC},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDing != null">
        #{isDing,jdbcType=SMALLINT},
      </if>
      <if test="sceneId != null">
        #{sceneId,jdbcType=SMALLINT},
      </if>
      <if test="ruleType != null">
        #{ruleType,jdbcType=INTEGER},
      </if>
      <if test="modifyFlag != null">
        #{modifyFlag,jdbcType=BIT},
      </if>
      <if test="applyIdRequire != null">
        #{applyIdRequire,jdbcType=SMALLINT},
      </if>
      <if test="checkRule != null">
        #{checkRule,jdbcType=BIT},
      </if>
      <if test="checkDinerNumber != null">
        #{checkDinerNumber,jdbcType=INTEGER},
      </if>
      <if test="compareType != null">
        #{compareType,jdbcType=SMALLINT},
      </if>
      <if test="isAfterControl != null">
        #{isAfterControl,jdbcType=INTEGER},
      </if>
      <if test="isOpenOrderPriceLimit != null">
        #{isOpenOrderPriceLimit,jdbcType=INTEGER},
      </if>
      <if test="orderPriceLimit != null">
        #{orderPriceLimit,jdbcType=NUMERIC},
      </if>
      <if test="orderPriceFieldConfig != null">
        #{orderPriceFieldConfig,jdbcType=INTEGER},
      </if>
      <if test="orderPriceFieldTitle != null">
        #{orderPriceFieldTitle,jdbcType=VARCHAR},
      </if>
      <if test="orderPriceFieldMsg != null">
        #{orderPriceFieldMsg,jdbcType=VARCHAR},
      </if>
      <if test="isOpenOrderExceedLimit != null">
        #{isOpenOrderExceedLimit,jdbcType=INTEGER},
      </if>
      <if test="orderExceedFieldConfig != null">
        #{orderExceedFieldConfig,jdbcType=INTEGER},
      </if>
      <if test="orderExceedFieldTitle != null">
        #{orderExceedFieldTitle,jdbcType=VARCHAR},
      </if>
      <if test="orderExceedFieldMsg != null">
        #{orderExceedFieldMsg,jdbcType=VARCHAR},
      </if>
      <if test="isManageConfirmOrder != null">
        #{isManageConfirmOrder,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.DinnerRuleExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from dinner_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update dinner_rule
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.dinnerFrequencyLimitPrice != null">
        dinner_frequency_limit_price = #{record.dinnerFrequencyLimitPrice,jdbcType=NUMERIC},
      </if>
      <if test="record.dinnerEverydayLimitPrice != null">
        dinner_everyday_limit_price = #{record.dinnerEverydayLimitPrice,jdbcType=NUMERIC},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDing != null">
        is_ding = #{record.isDing,jdbcType=SMALLINT},
      </if>
      <if test="record.sceneId != null">
        scene_id = #{record.sceneId,jdbcType=SMALLINT},
      </if>
      <if test="record.ruleType != null">
        rule_type = #{record.ruleType,jdbcType=INTEGER},
      </if>
      <if test="record.modifyFlag != null">
        modify_flag = #{record.modifyFlag,jdbcType=BIT},
      </if>
      <if test="record.applyIdRequire != null">
        apply_id_require = #{record.applyIdRequire,jdbcType=SMALLINT},
      </if>
      <if test="record.checkRule != null">
        check_rule = #{record.checkRule,jdbcType=BIT},
      </if>
      <if test="record.checkDinerNumber != null">
        check_diner_number = #{record.checkDinerNumber,jdbcType=INTEGER},
      </if>
      <if test="record.compareType != null">
        compare_type = #{record.compareType,jdbcType=SMALLINT},
      </if>
      <if test="record.isAfterControl != null">
        is_after_control = #{record.isAfterControl,jdbcType=INTEGER},
      </if>
      <if test="record.isOpenOrderPriceLimit != null">
        is_open_order_price_limit = #{record.isOpenOrderPriceLimit,jdbcType=INTEGER},
      </if>
      <if test="record.orderPriceLimit != null">
        order_price_limit = #{record.orderPriceLimit,jdbcType=NUMERIC},
      </if>
      <if test="record.orderPriceFieldConfig != null">
        order_price_field_config = #{record.orderPriceFieldConfig,jdbcType=INTEGER},
      </if>
      <if test="record.orderPriceFieldTitle != null">
        order_price_field_title = #{record.orderPriceFieldTitle,jdbcType=VARCHAR},
      </if>
      <if test="record.orderPriceFieldMsg != null">
        order_price_field_msg = #{record.orderPriceFieldMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.isOpenOrderExceedLimit != null">
        is_open_order_exceed_limit = #{record.isOpenOrderExceedLimit,jdbcType=INTEGER},
      </if>
      <if test="record.orderExceedFieldConfig != null">
        order_exceed_field_config = #{record.orderExceedFieldConfig,jdbcType=INTEGER},
      </if>
      <if test="record.orderExceedFieldTitle != null">
        order_exceed_field_title = #{record.orderExceedFieldTitle,jdbcType=VARCHAR},
      </if>
      <if test="record.orderExceedFieldMsg != null">
        order_exceed_field_msg = #{record.orderExceedFieldMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.isManageConfirmOrder != null">
        is_manage_confirm_order = #{record.isManageConfirmOrder,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update dinner_rule
    set id = #{record.id,jdbcType=CHAR},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      dinner_frequency_limit_price = #{record.dinnerFrequencyLimitPrice,jdbcType=NUMERIC},
      dinner_everyday_limit_price = #{record.dinnerEverydayLimitPrice,jdbcType=NUMERIC},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      is_ding = #{record.isDing,jdbcType=SMALLINT},
      scene_id = #{record.sceneId,jdbcType=SMALLINT},
      rule_type = #{record.ruleType,jdbcType=INTEGER},
      modify_flag = #{record.modifyFlag,jdbcType=BIT},
      apply_id_require = #{record.applyIdRequire,jdbcType=SMALLINT},
      check_rule = #{record.checkRule,jdbcType=BIT},
      check_diner_number = #{record.checkDinerNumber,jdbcType=INTEGER},
      compare_type = #{record.compareType,jdbcType=SMALLINT},
      is_after_control = #{record.isAfterControl,jdbcType=INTEGER},
      is_open_order_price_limit = #{record.isOpenOrderPriceLimit,jdbcType=INTEGER},
      order_price_limit = #{record.orderPriceLimit,jdbcType=NUMERIC},
      order_price_field_config = #{record.orderPriceFieldConfig,jdbcType=INTEGER},
      order_price_field_title = #{record.orderPriceFieldTitle,jdbcType=VARCHAR},
      order_price_field_msg = #{record.orderPriceFieldMsg,jdbcType=VARCHAR},
      is_open_order_exceed_limit = #{record.isOpenOrderExceedLimit,jdbcType=INTEGER},
      order_exceed_field_config = #{record.orderExceedFieldConfig,jdbcType=INTEGER},
      order_exceed_field_title = #{record.orderExceedFieldTitle,jdbcType=VARCHAR},
      order_exceed_field_msg = #{record.orderExceedFieldMsg,jdbcType=VARCHAR},
      is_manage_confirm_order = #{record.isManageConfirmOrder,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.DinnerRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update dinner_rule
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="dinnerFrequencyLimitPrice != null">
        dinner_frequency_limit_price = #{dinnerFrequencyLimitPrice,jdbcType=NUMERIC},
      </if>
      <if test="dinnerEverydayLimitPrice != null">
        dinner_everyday_limit_price = #{dinnerEverydayLimitPrice,jdbcType=NUMERIC},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDing != null">
        is_ding = #{isDing,jdbcType=SMALLINT},
      </if>
      <if test="sceneId != null">
        scene_id = #{sceneId,jdbcType=SMALLINT},
      </if>
      <if test="ruleType != null">
        rule_type = #{ruleType,jdbcType=INTEGER},
      </if>
      <if test="modifyFlag != null">
        modify_flag = #{modifyFlag,jdbcType=BIT},
      </if>
      <if test="applyIdRequire != null">
        apply_id_require = #{applyIdRequire,jdbcType=SMALLINT},
      </if>
      <if test="checkRule != null">
        check_rule = #{checkRule,jdbcType=BIT},
      </if>
      <if test="checkDinerNumber != null">
        check_diner_number = #{checkDinerNumber,jdbcType=INTEGER},
      </if>
      <if test="compareType != null">
        compare_type = #{compareType,jdbcType=SMALLINT},
      </if>
      <if test="isAfterControl != null">
        is_after_control = #{isAfterControl,jdbcType=INTEGER},
      </if>
      <if test="isOpenOrderPriceLimit != null">
        is_open_order_price_limit = #{isOpenOrderPriceLimit,jdbcType=INTEGER},
      </if>
      <if test="orderPriceLimit != null">
        order_price_limit = #{orderPriceLimit,jdbcType=NUMERIC},
      </if>
      <if test="orderPriceFieldConfig != null">
        order_price_field_config = #{orderPriceFieldConfig,jdbcType=INTEGER},
      </if>
      <if test="orderPriceFieldTitle != null">
        order_price_field_title = #{orderPriceFieldTitle,jdbcType=VARCHAR},
      </if>
      <if test="orderPriceFieldMsg != null">
        order_price_field_msg = #{orderPriceFieldMsg,jdbcType=VARCHAR},
      </if>
      <if test="isOpenOrderExceedLimit != null">
        is_open_order_exceed_limit = #{isOpenOrderExceedLimit,jdbcType=INTEGER},
      </if>
      <if test="orderExceedFieldConfig != null">
        order_exceed_field_config = #{orderExceedFieldConfig,jdbcType=INTEGER},
      </if>
      <if test="orderExceedFieldTitle != null">
        order_exceed_field_title = #{orderExceedFieldTitle,jdbcType=VARCHAR},
      </if>
      <if test="orderExceedFieldMsg != null">
        order_exceed_field_msg = #{orderExceedFieldMsg,jdbcType=VARCHAR},
      </if>
      <if test="isManageConfirmOrder != null">
        is_manage_confirm_order = #{isManageConfirmOrder,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.fenbeitong.DinnerRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update dinner_rule
    set company_id = #{companyId,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      dinner_frequency_limit_price = #{dinnerFrequencyLimitPrice,jdbcType=NUMERIC},
      dinner_everyday_limit_price = #{dinnerEverydayLimitPrice,jdbcType=NUMERIC},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_ding = #{isDing,jdbcType=SMALLINT},
      scene_id = #{sceneId,jdbcType=SMALLINT},
      rule_type = #{ruleType,jdbcType=INTEGER},
      modify_flag = #{modifyFlag,jdbcType=BIT},
      apply_id_require = #{applyIdRequire,jdbcType=SMALLINT},
      check_rule = #{checkRule,jdbcType=BIT},
      check_diner_number = #{checkDinerNumber,jdbcType=INTEGER},
      compare_type = #{compareType,jdbcType=SMALLINT},
      is_after_control = #{isAfterControl,jdbcType=INTEGER},
      is_open_order_price_limit = #{isOpenOrderPriceLimit,jdbcType=INTEGER},
      order_price_limit = #{orderPriceLimit,jdbcType=NUMERIC},
      order_price_field_config = #{orderPriceFieldConfig,jdbcType=INTEGER},
      order_price_field_title = #{orderPriceFieldTitle,jdbcType=VARCHAR},
      order_price_field_msg = #{orderPriceFieldMsg,jdbcType=VARCHAR},
      is_open_order_exceed_limit = #{isOpenOrderExceedLimit,jdbcType=INTEGER},
      order_exceed_field_config = #{orderExceedFieldConfig,jdbcType=INTEGER},
      order_exceed_field_title = #{orderExceedFieldTitle,jdbcType=VARCHAR},
      order_exceed_field_msg = #{orderExceedFieldMsg,jdbcType=VARCHAR},
      is_manage_confirm_order = #{isManageConfirmOrder,jdbcType=INTEGER}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>