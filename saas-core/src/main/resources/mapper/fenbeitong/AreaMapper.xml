<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.AreaMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.Area">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 25 14:42:00 CST 2017.
    -->
    <constructor>
      <idArg column="id" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="name" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="parent_id" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="level" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="area_type" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="air_flag" javaType="java.lang.Boolean" jdbcType="BIT" />
      <arg column="hotel_flag" javaType="java.lang.Boolean" jdbcType="BIT" />
      <arg column="taxi_flag" javaType="java.lang.Boolean" jdbcType="BIT" />
      <arg column="train_flag" javaType="java.lang.Boolean" jdbcType="BIT" />
      <arg column="pinyin" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="hot_value" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="longitude" javaType="java.lang.Double" jdbcType="DOUBLE" />
      <arg column="latitude" javaType="java.lang.Double" jdbcType="DOUBLE" />
    </constructor>
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 25 14:42:00 CST 2017.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 25 14:42:00 CST 2017.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 25 14:42:00 CST 2017.
    -->
    id, name, parent_id, level, area_type, air_flag, hotel_flag, taxi_flag, train_flag, 
    pinyin, hot_value, longitude, latitude
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.AreaExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 25 14:42:00 CST 2017.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from area
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.AreaExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 25 14:42:00 CST 2017.
    -->
    delete from area
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.Area">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 25 14:42:00 CST 2017.
    -->
    insert into area
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="level != null">
        level,
      </if>
      <if test="areaType != null">
        area_type,
      </if>
      <if test="airFlag != null">
        air_flag,
      </if>
      <if test="hotelFlag != null">
        hotel_flag,
      </if>
      <if test="taxiFlag != null">
        taxi_flag,
      </if>
      <if test="trainFlag != null">
        train_flag,
      </if>
      <if test="pinyin != null">
        pinyin,
      </if>
      <if test="hotValue != null">
        hot_value,
      </if>
      <if test="longitude != null">
        longitude,
      </if>
      <if test="latitude != null">
        latitude,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        #{level,jdbcType=INTEGER},
      </if>
      <if test="areaType != null">
        #{areaType,jdbcType=INTEGER},
      </if>
      <if test="airFlag != null">
        #{airFlag,jdbcType=BIT},
      </if>
      <if test="hotelFlag != null">
        #{hotelFlag,jdbcType=BIT},
      </if>
      <if test="taxiFlag != null">
        #{taxiFlag,jdbcType=BIT},
      </if>
      <if test="trainFlag != null">
        #{trainFlag,jdbcType=BIT},
      </if>
      <if test="pinyin != null">
        #{pinyin,jdbcType=VARCHAR},
      </if>
      <if test="hotValue != null">
        #{hotValue,jdbcType=INTEGER},
      </if>
      <if test="longitude != null">
        #{longitude,jdbcType=DOUBLE},
      </if>
      <if test="latitude != null">
        #{latitude,jdbcType=DOUBLE},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.AreaExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 25 14:42:00 CST 2017.
    -->
    select count(*) from area
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 25 14:42:00 CST 2017.
    -->
    update area
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.parentId != null">
        parent_id = #{record.parentId,jdbcType=VARCHAR},
      </if>
      <if test="record.level != null">
        level = #{record.level,jdbcType=INTEGER},
      </if>
      <if test="record.areaType != null">
        area_type = #{record.areaType,jdbcType=INTEGER},
      </if>
      <if test="record.airFlag != null">
        air_flag = #{record.airFlag,jdbcType=BIT},
      </if>
      <if test="record.hotelFlag != null">
        hotel_flag = #{record.hotelFlag,jdbcType=BIT},
      </if>
      <if test="record.taxiFlag != null">
        taxi_flag = #{record.taxiFlag,jdbcType=BIT},
      </if>
      <if test="record.trainFlag != null">
        train_flag = #{record.trainFlag,jdbcType=BIT},
      </if>
      <if test="record.pinyin != null">
        pinyin = #{record.pinyin,jdbcType=VARCHAR},
      </if>
      <if test="record.hotValue != null">
        hot_value = #{record.hotValue,jdbcType=INTEGER},
      </if>
      <if test="record.longitude != null">
        longitude = #{record.longitude,jdbcType=DOUBLE},
      </if>
      <if test="record.latitude != null">
        latitude = #{record.latitude,jdbcType=DOUBLE},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 25 14:42:00 CST 2017.
    -->
    update area
    set id = #{record.id,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      parent_id = #{record.parentId,jdbcType=VARCHAR},
      level = #{record.level,jdbcType=INTEGER},
      area_type = #{record.areaType,jdbcType=INTEGER},
      air_flag = #{record.airFlag,jdbcType=BIT},
      hotel_flag = #{record.hotelFlag,jdbcType=BIT},
      taxi_flag = #{record.taxiFlag,jdbcType=BIT},
      train_flag = #{record.trainFlag,jdbcType=BIT},
      pinyin = #{record.pinyin,jdbcType=VARCHAR},
      hot_value = #{record.hotValue,jdbcType=INTEGER},
      longitude = #{record.longitude,jdbcType=DOUBLE},
      latitude = #{record.latitude,jdbcType=DOUBLE}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.Area">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jul 25 14:42:00 CST 2017.
    -->
    update area
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        level = #{level,jdbcType=INTEGER},
      </if>
      <if test="areaType != null">
        area_type = #{areaType,jdbcType=INTEGER},
      </if>
      <if test="airFlag != null">
        air_flag = #{airFlag,jdbcType=BIT},
      </if>
      <if test="hotelFlag != null">
        hotel_flag = #{hotelFlag,jdbcType=BIT},
      </if>
      <if test="taxiFlag != null">
        taxi_flag = #{taxiFlag,jdbcType=BIT},
      </if>
      <if test="trainFlag != null">
        train_flag = #{trainFlag,jdbcType=BIT},
      </if>
      <if test="pinyin != null">
        pinyin = #{pinyin,jdbcType=VARCHAR},
      </if>
      <if test="hotValue != null">
        hot_value = #{hotValue,jdbcType=INTEGER},
      </if>
      <if test="longitude != null">
        longitude = #{longitude,jdbcType=DOUBLE},
      </if>
      <if test="latitude != null">
        latitude = #{latitude,jdbcType=DOUBLE},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>