<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.OrgConsumeRecordExtMapper">

  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.OrgConsumeRecordTotal">
    <result column="consumer_type" jdbcType="SMALLINT" property="consumerType" />
    <result column="consumer_id" jdbcType="VARCHAR" property="consumerId" />
    <result column="amount" jdbcType="NUMERIC" property="totalAmount" />
  </resultMap>

  <resultMap id="BaseResultMapExt"
             type="com.fenbeitong.saas.core.model.fenbeitong.OrgConsumeRecord"
             extends="com.fenbeitong.saas.core.dao.fenbeitong.OrgConsumeRecordMapper.BaseResultMap">
  </resultMap>

  <select id="queryOrgConsumeRecordTotal" parameterType="map" resultMap="BaseResultMap">
    select consumer_type, consumer_id, sum(amount) as amount
    from org_consume_record
    where company_id=#{companyId,jdbcType=VARCHAR}
    and create_time between #{beginTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
    <if test="consumerIdList != null">
      and consumer_id in
      <foreach collection="consumerIdList" item="listItem" open="(" separator="," close=")">
        #{listItem}
      </foreach>
    </if>
    <if test="consumeCategoryList != null">
      and consume_category in
      <foreach collection="consumeCategoryList" item="listItem" open="(" separator="," close=")">
        #{listItem}
      </foreach>
    </if>
    group by consumer_type, consumer_id
  </select>

  <select id="queryOrgConsumeRecordNum" resultMap="BaseResultMapExt">
    select * from org_consume_record
    where
    <![CDATA[create_time < #{overtime,jdbcType=TIMESTAMP}]]>
    and consumer_type!=3
    order by create_time asc limit 1000
  </select>

</mapper>