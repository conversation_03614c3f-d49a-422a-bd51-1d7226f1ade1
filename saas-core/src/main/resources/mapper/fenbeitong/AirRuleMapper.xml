<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.AirRuleMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.AirRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="company_id" jdbcType="CHAR" property="companyId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="air_cabin_flag" jdbcType="BIT" property="airCabinFlag" />
    <result column="air_cabin_type" jdbcType="VARCHAR" property="airCabinType" />
    <result column="air_price_flag" jdbcType="BIT" property="airPriceFlag" />
    <result column="air_unit_price" jdbcType="NUMERIC" property="airUnitPrice" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="air_discount_flag" jdbcType="BIT" property="airDiscountFlag" />
    <result column="air_discount" jdbcType="NUMERIC" property="airDiscount" />
    <result column="priv_day_min" jdbcType="INTEGER" property="privDayMin" />
    <result column="priv_day_max" jdbcType="INTEGER" property="privDayMax" />
    <result column="low_price_flag" jdbcType="INTEGER" property="lowPriceFlag" />
    <result column="low_price_time" jdbcType="INTEGER" property="lowPriceTime" />
    <result column="time_consume_flag" jdbcType="BIT" property="timeConsumeFlag" />
    <result column="time_consume_value" jdbcType="INTEGER" property="timeConsumeValue" />
    <result column="distance_limit_flag" jdbcType="BIT" property="distanceLimitFlag" />
    <result column="distance_limit_value" jdbcType="INTEGER" property="distanceLimitValue" />
    <result column="is_ding" jdbcType="SMALLINT" property="isDing" />
    <result column="time_lower_price" jdbcType="INTEGER" property="timeLowerPrice" />
    <result column="distance_lower_price" jdbcType="INTEGER" property="distanceLowerPrice" />
    <result column="filter_stopover_flight_flag" jdbcType="INTEGER" property="filterStopoverFlightFlag" />
    <result column="air_port_city_flag" jdbcType="INTEGER" property="airPortCityFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, name, air_cabin_flag, air_cabin_type, air_price_flag, air_unit_price, 
    modify_time, air_discount_flag, air_discount, priv_day_min, priv_day_max, low_price_flag, 
    low_price_time, time_consume_flag, time_consume_value, distance_limit_flag, distance_limit_value, 
    is_ding, time_lower_price, distance_lower_price, filter_stopover_flight_flag, air_port_city_flag
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.AirRuleExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from air_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${limit} offset ${offset}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from air_rule
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from air_rule
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.AirRuleExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from air_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.fenbeitong.AirRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into air_rule (id, company_id, name, 
      air_cabin_flag, air_cabin_type, air_price_flag, 
      air_unit_price, modify_time, air_discount_flag, 
      air_discount, priv_day_min, priv_day_max, 
      low_price_flag, low_price_time, time_consume_flag, 
      time_consume_value, distance_limit_flag, distance_limit_value, 
      is_ding, time_lower_price, distance_lower_price, 
      filter_stopover_flight_flag, air_port_city_flag)
    values (#{id,jdbcType=CHAR}, #{companyId,jdbcType=CHAR}, #{name,jdbcType=VARCHAR}, 
      #{airCabinFlag,jdbcType=BIT}, #{airCabinType,jdbcType=VARCHAR}, #{airPriceFlag,jdbcType=BIT}, 
      #{airUnitPrice,jdbcType=NUMERIC}, #{modifyTime,jdbcType=TIMESTAMP}, #{airDiscountFlag,jdbcType=BIT}, 
      #{airDiscount,jdbcType=NUMERIC}, #{privDayMin,jdbcType=INTEGER}, #{privDayMax,jdbcType=INTEGER}, 
      #{lowPriceFlag,jdbcType=INTEGER}, #{lowPriceTime,jdbcType=INTEGER}, #{timeConsumeFlag,jdbcType=BIT}, 
      #{timeConsumeValue,jdbcType=INTEGER}, #{distanceLimitFlag,jdbcType=BIT}, #{distanceLimitValue,jdbcType=INTEGER}, 
      #{isDing,jdbcType=SMALLINT}, #{timeLowerPrice,jdbcType=INTEGER}, #{distanceLowerPrice,jdbcType=INTEGER}, 
      #{filterStopoverFlightFlag,jdbcType=INTEGER}, #{airPortCityFlag,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.AirRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into air_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="airCabinFlag != null">
        air_cabin_flag,
      </if>
      <if test="airCabinType != null">
        air_cabin_type,
      </if>
      <if test="airPriceFlag != null">
        air_price_flag,
      </if>
      <if test="airUnitPrice != null">
        air_unit_price,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="airDiscountFlag != null">
        air_discount_flag,
      </if>
      <if test="airDiscount != null">
        air_discount,
      </if>
      <if test="privDayMin != null">
        priv_day_min,
      </if>
      <if test="privDayMax != null">
        priv_day_max,
      </if>
      <if test="lowPriceFlag != null">
        low_price_flag,
      </if>
      <if test="lowPriceTime != null">
        low_price_time,
      </if>
      <if test="timeConsumeFlag != null">
        time_consume_flag,
      </if>
      <if test="timeConsumeValue != null">
        time_consume_value,
      </if>
      <if test="distanceLimitFlag != null">
        distance_limit_flag,
      </if>
      <if test="distanceLimitValue != null">
        distance_limit_value,
      </if>
      <if test="isDing != null">
        is_ding,
      </if>
      <if test="timeLowerPrice != null">
        time_lower_price,
      </if>
      <if test="distanceLowerPrice != null">
        distance_lower_price,
      </if>
      <if test="filterStopoverFlightFlag != null">
        filter_stopover_flight_flag,
      </if>
      <if test="airPortCityFlag != null">
        air_port_city_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=CHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="airCabinFlag != null">
        #{airCabinFlag,jdbcType=BIT},
      </if>
      <if test="airCabinType != null">
        #{airCabinType,jdbcType=VARCHAR},
      </if>
      <if test="airPriceFlag != null">
        #{airPriceFlag,jdbcType=BIT},
      </if>
      <if test="airUnitPrice != null">
        #{airUnitPrice,jdbcType=NUMERIC},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="airDiscountFlag != null">
        #{airDiscountFlag,jdbcType=BIT},
      </if>
      <if test="airDiscount != null">
        #{airDiscount,jdbcType=NUMERIC},
      </if>
      <if test="privDayMin != null">
        #{privDayMin,jdbcType=INTEGER},
      </if>
      <if test="privDayMax != null">
        #{privDayMax,jdbcType=INTEGER},
      </if>
      <if test="lowPriceFlag != null">
        #{lowPriceFlag,jdbcType=INTEGER},
      </if>
      <if test="lowPriceTime != null">
        #{lowPriceTime,jdbcType=INTEGER},
      </if>
      <if test="timeConsumeFlag != null">
        #{timeConsumeFlag,jdbcType=BIT},
      </if>
      <if test="timeConsumeValue != null">
        #{timeConsumeValue,jdbcType=INTEGER},
      </if>
      <if test="distanceLimitFlag != null">
        #{distanceLimitFlag,jdbcType=BIT},
      </if>
      <if test="distanceLimitValue != null">
        #{distanceLimitValue,jdbcType=INTEGER},
      </if>
      <if test="isDing != null">
        #{isDing,jdbcType=SMALLINT},
      </if>
      <if test="timeLowerPrice != null">
        #{timeLowerPrice,jdbcType=INTEGER},
      </if>
      <if test="distanceLowerPrice != null">
        #{distanceLowerPrice,jdbcType=INTEGER},
      </if>
      <if test="filterStopoverFlightFlag != null">
        #{filterStopoverFlightFlag,jdbcType=INTEGER},
      </if>
      <if test="airPortCityFlag != null">
        #{airPortCityFlag,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.AirRuleExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from air_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update air_rule
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=CHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.airCabinFlag != null">
        air_cabin_flag = #{record.airCabinFlag,jdbcType=BIT},
      </if>
      <if test="record.airCabinType != null">
        air_cabin_type = #{record.airCabinType,jdbcType=VARCHAR},
      </if>
      <if test="record.airPriceFlag != null">
        air_price_flag = #{record.airPriceFlag,jdbcType=BIT},
      </if>
      <if test="record.airUnitPrice != null">
        air_unit_price = #{record.airUnitPrice,jdbcType=NUMERIC},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.airDiscountFlag != null">
        air_discount_flag = #{record.airDiscountFlag,jdbcType=BIT},
      </if>
      <if test="record.airDiscount != null">
        air_discount = #{record.airDiscount,jdbcType=NUMERIC},
      </if>
      <if test="record.privDayMin != null">
        priv_day_min = #{record.privDayMin,jdbcType=INTEGER},
      </if>
      <if test="record.privDayMax != null">
        priv_day_max = #{record.privDayMax,jdbcType=INTEGER},
      </if>
      <if test="record.lowPriceFlag != null">
        low_price_flag = #{record.lowPriceFlag,jdbcType=INTEGER},
      </if>
      <if test="record.lowPriceTime != null">
        low_price_time = #{record.lowPriceTime,jdbcType=INTEGER},
      </if>
      <if test="record.timeConsumeFlag != null">
        time_consume_flag = #{record.timeConsumeFlag,jdbcType=BIT},
      </if>
      <if test="record.timeConsumeValue != null">
        time_consume_value = #{record.timeConsumeValue,jdbcType=INTEGER},
      </if>
      <if test="record.distanceLimitFlag != null">
        distance_limit_flag = #{record.distanceLimitFlag,jdbcType=BIT},
      </if>
      <if test="record.distanceLimitValue != null">
        distance_limit_value = #{record.distanceLimitValue,jdbcType=INTEGER},
      </if>
      <if test="record.isDing != null">
        is_ding = #{record.isDing,jdbcType=SMALLINT},
      </if>
      <if test="record.timeLowerPrice != null">
        time_lower_price = #{record.timeLowerPrice,jdbcType=INTEGER},
      </if>
      <if test="record.distanceLowerPrice != null">
        distance_lower_price = #{record.distanceLowerPrice,jdbcType=INTEGER},
      </if>
      <if test="record.filterStopoverFlightFlag != null">
        filter_stopover_flight_flag = #{record.filterStopoverFlightFlag,jdbcType=INTEGER},
      </if>
      <if test="record.airPortCityFlag != null">
        air_port_city_flag = #{record.airPortCityFlag,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update air_rule
    set id = #{record.id,jdbcType=CHAR},
      company_id = #{record.companyId,jdbcType=CHAR},
      name = #{record.name,jdbcType=VARCHAR},
      air_cabin_flag = #{record.airCabinFlag,jdbcType=BIT},
      air_cabin_type = #{record.airCabinType,jdbcType=VARCHAR},
      air_price_flag = #{record.airPriceFlag,jdbcType=BIT},
      air_unit_price = #{record.airUnitPrice,jdbcType=NUMERIC},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      air_discount_flag = #{record.airDiscountFlag,jdbcType=BIT},
      air_discount = #{record.airDiscount,jdbcType=NUMERIC},
      priv_day_min = #{record.privDayMin,jdbcType=INTEGER},
      priv_day_max = #{record.privDayMax,jdbcType=INTEGER},
      low_price_flag = #{record.lowPriceFlag,jdbcType=INTEGER},
      low_price_time = #{record.lowPriceTime,jdbcType=INTEGER},
      time_consume_flag = #{record.timeConsumeFlag,jdbcType=BIT},
      time_consume_value = #{record.timeConsumeValue,jdbcType=INTEGER},
      distance_limit_flag = #{record.distanceLimitFlag,jdbcType=BIT},
      distance_limit_value = #{record.distanceLimitValue,jdbcType=INTEGER},
      is_ding = #{record.isDing,jdbcType=SMALLINT},
      time_lower_price = #{record.timeLowerPrice,jdbcType=INTEGER},
      distance_lower_price = #{record.distanceLowerPrice,jdbcType=INTEGER},
      filter_stopover_flight_flag = #{record.filterStopoverFlightFlag,jdbcType=INTEGER},
    air_port_city_flag = #{record.airPortCityFlag,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.AirRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update air_rule
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=CHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="airCabinFlag != null">
        air_cabin_flag = #{airCabinFlag,jdbcType=BIT},
      </if>
      <if test="airCabinType != null">
        air_cabin_type = #{airCabinType,jdbcType=VARCHAR},
      </if>
      <if test="airPriceFlag != null">
        air_price_flag = #{airPriceFlag,jdbcType=BIT},
      </if>
      <if test="airUnitPrice != null">
        air_unit_price = #{airUnitPrice,jdbcType=NUMERIC},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="airDiscountFlag != null">
        air_discount_flag = #{airDiscountFlag,jdbcType=BIT},
      </if>
      <if test="airDiscount != null">
        air_discount = #{airDiscount,jdbcType=NUMERIC},
      </if>
      <if test="privDayMin != null">
        priv_day_min = #{privDayMin,jdbcType=INTEGER},
      </if>
      <if test="privDayMax != null">
        priv_day_max = #{privDayMax,jdbcType=INTEGER},
      </if>
      <if test="lowPriceFlag != null">
        low_price_flag = #{lowPriceFlag,jdbcType=INTEGER},
      </if>
      <if test="lowPriceTime != null">
        low_price_time = #{lowPriceTime,jdbcType=INTEGER},
      </if>
      <if test="timeConsumeFlag != null">
        time_consume_flag = #{timeConsumeFlag,jdbcType=BIT},
      </if>
      <if test="timeConsumeValue != null">
        time_consume_value = #{timeConsumeValue,jdbcType=INTEGER},
      </if>
      <if test="distanceLimitFlag != null">
        distance_limit_flag = #{distanceLimitFlag,jdbcType=BIT},
      </if>
      <if test="distanceLimitValue != null">
        distance_limit_value = #{distanceLimitValue,jdbcType=INTEGER},
      </if>
      <if test="isDing != null">
        is_ding = #{isDing,jdbcType=SMALLINT},
      </if>
      <if test="timeLowerPrice != null">
        time_lower_price = #{timeLowerPrice,jdbcType=INTEGER},
      </if>
      <if test="distanceLowerPrice != null">
        distance_lower_price = #{distanceLowerPrice,jdbcType=INTEGER},
      </if>
      <if test="filterStopoverFlightFlag != null">
        filter_stopover_flight_flag = #{filterStopoverFlightFlag,jdbcType=INTEGER},
      </if>
      <if test="airPortCityFlag != null">
        air_port_city_flag = #{airPortCityFlag,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.fenbeitong.AirRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update air_rule
    set company_id = #{companyId,jdbcType=CHAR},
      name = #{name,jdbcType=VARCHAR},
      air_cabin_flag = #{airCabinFlag,jdbcType=BIT},
      air_cabin_type = #{airCabinType,jdbcType=VARCHAR},
      air_price_flag = #{airPriceFlag,jdbcType=BIT},
      air_unit_price = #{airUnitPrice,jdbcType=NUMERIC},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      air_discount_flag = #{airDiscountFlag,jdbcType=BIT},
      air_discount = #{airDiscount,jdbcType=NUMERIC},
      priv_day_min = #{privDayMin,jdbcType=INTEGER},
      priv_day_max = #{privDayMax,jdbcType=INTEGER},
      low_price_flag = #{lowPriceFlag,jdbcType=INTEGER},
      low_price_time = #{lowPriceTime,jdbcType=INTEGER},
      time_consume_flag = #{timeConsumeFlag,jdbcType=BIT},
      time_consume_value = #{timeConsumeValue,jdbcType=INTEGER},
      distance_limit_flag = #{distanceLimitFlag,jdbcType=BIT},
      distance_limit_value = #{distanceLimitValue,jdbcType=INTEGER},
      is_ding = #{isDing,jdbcType=SMALLINT},
      time_lower_price = #{timeLowerPrice,jdbcType=INTEGER},
      distance_lower_price = #{distanceLowerPrice,jdbcType=INTEGER},
      filter_stopover_flight_flag = #{filterStopoverFlightFlag,jdbcType=INTEGER},
      air_port_city_flag = #{airPortCityFlag,jdbcType=INTEGER}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>