<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.IntlAirRuleMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.IntlAirRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="company_id" jdbcType="CHAR" property="companyId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="air_cabin_flag" jdbcType="BIT" property="airCabinFlag" />
    <result column="air_cabin_type" jdbcType="VARCHAR" property="airCabinType" />
    <result column="air_price_flag" jdbcType="BIT" property="airPriceFlag" />
    <result column="air_unit_price" jdbcType="NUMERIC" property="airUnitPrice" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="air_discount_flag" jdbcType="BIT" property="airDiscountFlag" />
    <result column="air_discount" jdbcType="NUMERIC" property="airDiscount" />
    <result column="priv_day_min" jdbcType="INTEGER" property="privDayMin" />
    <result column="priv_day_max" jdbcType="INTEGER" property="privDayMax" />
    <result column="is_ding" jdbcType="SMALLINT" property="isDing" />
    <result column="rule_type" jdbcType="INTEGER" property="ruleType" />
    <result column="modify_flag" jdbcType="BIT" property="modifyFlag" />
    <result column="low_price_flag" jdbcType="INTEGER" property="lowPriceFlag" />
    <result column="low_price_time" jdbcType="INTEGER" property="lowPriceTime" />
    <result column="low_price_unit" jdbcType="VARCHAR" property="lowPriceUnit" />
    <result column="filter_stopover_flight_flag" jdbcType="INTEGER" property="filterStopoverFlightFlag" />
    <result column="is_check_flight_time" jdbcType="INTEGER" property="isCheckFlightTime" />
    <result column="check_flight_time_type" jdbcType="INTEGER" property="checkFlightTimeType" />
    <result column="flight_time" jdbcType="INTEGER" property="flightTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, name, air_cabin_flag, air_cabin_type, air_price_flag, air_unit_price, 
    modify_time, air_discount_flag, air_discount, priv_day_min, priv_day_max, is_ding, 
    rule_type, modify_flag, low_price_flag, low_price_time, low_price_unit, filter_stopover_flight_flag, 
    is_check_flight_time, check_flight_time_type, flight_time
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.IntlAirRuleExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from intl_air_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${limit} offset ${offset}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from intl_air_rule
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from intl_air_rule
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.IntlAirRuleExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from intl_air_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.fenbeitong.IntlAirRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into intl_air_rule (id, company_id, name, 
      air_cabin_flag, air_cabin_type, air_price_flag, 
      air_unit_price, modify_time, air_discount_flag, 
      air_discount, priv_day_min, priv_day_max, 
      is_ding, rule_type, modify_flag, 
      low_price_flag, low_price_time, low_price_unit, 
      filter_stopover_flight_flag, is_check_flight_time, 
      check_flight_time_type, flight_time)
    values (#{id,jdbcType=CHAR}, #{companyId,jdbcType=CHAR}, #{name,jdbcType=VARCHAR}, 
      #{airCabinFlag,jdbcType=BIT}, #{airCabinType,jdbcType=VARCHAR}, #{airPriceFlag,jdbcType=BIT}, 
      #{airUnitPrice,jdbcType=NUMERIC}, #{modifyTime,jdbcType=TIMESTAMP}, #{airDiscountFlag,jdbcType=BIT}, 
      #{airDiscount,jdbcType=NUMERIC}, #{privDayMin,jdbcType=INTEGER}, #{privDayMax,jdbcType=INTEGER}, 
      #{isDing,jdbcType=SMALLINT}, #{ruleType,jdbcType=INTEGER}, #{modifyFlag,jdbcType=BIT}, 
      #{lowPriceFlag,jdbcType=INTEGER}, #{lowPriceTime,jdbcType=INTEGER}, #{lowPriceUnit,jdbcType=VARCHAR}, 
      #{filterStopoverFlightFlag,jdbcType=INTEGER}, #{isCheckFlightTime,jdbcType=INTEGER}, 
      #{checkFlightTimeType,jdbcType=INTEGER}, #{flightTime,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.IntlAirRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into intl_air_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="airCabinFlag != null">
        air_cabin_flag,
      </if>
      <if test="airCabinType != null">
        air_cabin_type,
      </if>
      <if test="airPriceFlag != null">
        air_price_flag,
      </if>
      <if test="airUnitPrice != null">
        air_unit_price,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="airDiscountFlag != null">
        air_discount_flag,
      </if>
      <if test="airDiscount != null">
        air_discount,
      </if>
      <if test="privDayMin != null">
        priv_day_min,
      </if>
      <if test="privDayMax != null">
        priv_day_max,
      </if>
      <if test="isDing != null">
        is_ding,
      </if>
      <if test="ruleType != null">
        rule_type,
      </if>
      <if test="modifyFlag != null">
        modify_flag,
      </if>
      <if test="lowPriceFlag != null">
        low_price_flag,
      </if>
      <if test="lowPriceTime != null">
        low_price_time,
      </if>
      <if test="lowPriceUnit != null">
        low_price_unit,
      </if>
      <if test="filterStopoverFlightFlag != null">
        filter_stopover_flight_flag,
      </if>
      <if test="isCheckFlightTime != null">
        is_check_flight_time,
      </if>
      <if test="checkFlightTimeType != null">
        check_flight_time_type,
      </if>
      <if test="flightTime != null">
        flight_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=CHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="airCabinFlag != null">
        #{airCabinFlag,jdbcType=BIT},
      </if>
      <if test="airCabinType != null">
        #{airCabinType,jdbcType=VARCHAR},
      </if>
      <if test="airPriceFlag != null">
        #{airPriceFlag,jdbcType=BIT},
      </if>
      <if test="airUnitPrice != null">
        #{airUnitPrice,jdbcType=NUMERIC},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="airDiscountFlag != null">
        #{airDiscountFlag,jdbcType=BIT},
      </if>
      <if test="airDiscount != null">
        #{airDiscount,jdbcType=NUMERIC},
      </if>
      <if test="privDayMin != null">
        #{privDayMin,jdbcType=INTEGER},
      </if>
      <if test="privDayMax != null">
        #{privDayMax,jdbcType=INTEGER},
      </if>
      <if test="isDing != null">
        #{isDing,jdbcType=SMALLINT},
      </if>
      <if test="ruleType != null">
        #{ruleType,jdbcType=INTEGER},
      </if>
      <if test="modifyFlag != null">
        #{modifyFlag,jdbcType=BIT},
      </if>
      <if test="lowPriceFlag != null">
        #{lowPriceFlag,jdbcType=INTEGER},
      </if>
      <if test="lowPriceTime != null">
        #{lowPriceTime,jdbcType=INTEGER},
      </if>
      <if test="lowPriceUnit != null">
        #{lowPriceUnit,jdbcType=VARCHAR},
      </if>
      <if test="filterStopoverFlightFlag != null">
        #{filterStopoverFlightFlag,jdbcType=INTEGER},
      </if>
      <if test="isCheckFlightTime != null">
        #{isCheckFlightTime,jdbcType=INTEGER},
      </if>
      <if test="checkFlightTimeType != null">
        #{checkFlightTimeType,jdbcType=INTEGER},
      </if>
      <if test="flightTime != null">
        #{flightTime,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.IntlAirRuleExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from intl_air_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update intl_air_rule
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=CHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.airCabinFlag != null">
        air_cabin_flag = #{record.airCabinFlag,jdbcType=BIT},
      </if>
      <if test="record.airCabinType != null">
        air_cabin_type = #{record.airCabinType,jdbcType=VARCHAR},
      </if>
      <if test="record.airPriceFlag != null">
        air_price_flag = #{record.airPriceFlag,jdbcType=BIT},
      </if>
      <if test="record.airUnitPrice != null">
        air_unit_price = #{record.airUnitPrice,jdbcType=NUMERIC},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.airDiscountFlag != null">
        air_discount_flag = #{record.airDiscountFlag,jdbcType=BIT},
      </if>
      <if test="record.airDiscount != null">
        air_discount = #{record.airDiscount,jdbcType=NUMERIC},
      </if>
      <if test="record.privDayMin != null">
        priv_day_min = #{record.privDayMin,jdbcType=INTEGER},
      </if>
      <if test="record.privDayMax != null">
        priv_day_max = #{record.privDayMax,jdbcType=INTEGER},
      </if>
      <if test="record.isDing != null">
        is_ding = #{record.isDing,jdbcType=SMALLINT},
      </if>
      <if test="record.ruleType != null">
        rule_type = #{record.ruleType,jdbcType=INTEGER},
      </if>
      <if test="record.modifyFlag != null">
        modify_flag = #{record.modifyFlag,jdbcType=BIT},
      </if>
      <if test="record.lowPriceFlag != null">
        low_price_flag = #{record.lowPriceFlag,jdbcType=INTEGER},
      </if>
      <if test="record.lowPriceTime != null">
        low_price_time = #{record.lowPriceTime,jdbcType=INTEGER},
      </if>
      <if test="record.lowPriceUnit != null">
        low_price_unit = #{record.lowPriceUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.filterStopoverFlightFlag != null">
        filter_stopover_flight_flag = #{record.filterStopoverFlightFlag,jdbcType=INTEGER},
      </if>
      <if test="record.isCheckFlightTime != null">
        is_check_flight_time = #{record.isCheckFlightTime,jdbcType=INTEGER},
      </if>
      <if test="record.checkFlightTimeType != null">
        check_flight_time_type = #{record.checkFlightTimeType,jdbcType=INTEGER},
      </if>
      <if test="record.flightTime != null">
        flight_time = #{record.flightTime,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update intl_air_rule
    set id = #{record.id,jdbcType=CHAR},
      company_id = #{record.companyId,jdbcType=CHAR},
      name = #{record.name,jdbcType=VARCHAR},
      air_cabin_flag = #{record.airCabinFlag,jdbcType=BIT},
      air_cabin_type = #{record.airCabinType,jdbcType=VARCHAR},
      air_price_flag = #{record.airPriceFlag,jdbcType=BIT},
      air_unit_price = #{record.airUnitPrice,jdbcType=NUMERIC},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      air_discount_flag = #{record.airDiscountFlag,jdbcType=BIT},
      air_discount = #{record.airDiscount,jdbcType=NUMERIC},
      priv_day_min = #{record.privDayMin,jdbcType=INTEGER},
      priv_day_max = #{record.privDayMax,jdbcType=INTEGER},
      is_ding = #{record.isDing,jdbcType=SMALLINT},
      rule_type = #{record.ruleType,jdbcType=INTEGER},
      modify_flag = #{record.modifyFlag,jdbcType=BIT},
      low_price_flag = #{record.lowPriceFlag,jdbcType=INTEGER},
      low_price_time = #{record.lowPriceTime,jdbcType=INTEGER},
      low_price_unit = #{record.lowPriceUnit,jdbcType=VARCHAR},
      filter_stopover_flight_flag = #{record.filterStopoverFlightFlag,jdbcType=INTEGER},
      is_check_flight_time = #{record.isCheckFlightTime,jdbcType=INTEGER},
      check_flight_time_type = #{record.checkFlightTimeType,jdbcType=INTEGER},
      flight_time = #{record.flightTime,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.IntlAirRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update intl_air_rule
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=CHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="airCabinFlag != null">
        air_cabin_flag = #{airCabinFlag,jdbcType=BIT},
      </if>
      <if test="airCabinType != null">
        air_cabin_type = #{airCabinType,jdbcType=VARCHAR},
      </if>
      <if test="airPriceFlag != null">
        air_price_flag = #{airPriceFlag,jdbcType=BIT},
      </if>
      <if test="airUnitPrice != null">
        air_unit_price = #{airUnitPrice,jdbcType=NUMERIC},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="airDiscountFlag != null">
        air_discount_flag = #{airDiscountFlag,jdbcType=BIT},
      </if>
      <if test="airDiscount != null">
        air_discount = #{airDiscount,jdbcType=NUMERIC},
      </if>
      <if test="privDayMin != null">
        priv_day_min = #{privDayMin,jdbcType=INTEGER},
      </if>
      <if test="privDayMax != null">
        priv_day_max = #{privDayMax,jdbcType=INTEGER},
      </if>
      <if test="isDing != null">
        is_ding = #{isDing,jdbcType=SMALLINT},
      </if>
      <if test="ruleType != null">
        rule_type = #{ruleType,jdbcType=INTEGER},
      </if>
      <if test="modifyFlag != null">
        modify_flag = #{modifyFlag,jdbcType=BIT},
      </if>
      <if test="lowPriceFlag != null">
        low_price_flag = #{lowPriceFlag,jdbcType=INTEGER},
      </if>
      <if test="lowPriceTime != null">
        low_price_time = #{lowPriceTime,jdbcType=INTEGER},
      </if>
      <if test="lowPriceUnit != null">
        low_price_unit = #{lowPriceUnit,jdbcType=VARCHAR},
      </if>
      <if test="filterStopoverFlightFlag != null">
        filter_stopover_flight_flag = #{filterStopoverFlightFlag,jdbcType=INTEGER},
      </if>
      <if test="isCheckFlightTime != null">
        is_check_flight_time = #{isCheckFlightTime,jdbcType=INTEGER},
      </if>
      <if test="checkFlightTimeType != null">
        check_flight_time_type = #{checkFlightTimeType,jdbcType=INTEGER},
      </if>
      <if test="flightTime != null">
        flight_time = #{flightTime,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.fenbeitong.IntlAirRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update intl_air_rule
    set company_id = #{companyId,jdbcType=CHAR},
      name = #{name,jdbcType=VARCHAR},
      air_cabin_flag = #{airCabinFlag,jdbcType=BIT},
      air_cabin_type = #{airCabinType,jdbcType=VARCHAR},
      air_price_flag = #{airPriceFlag,jdbcType=BIT},
      air_unit_price = #{airUnitPrice,jdbcType=NUMERIC},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      air_discount_flag = #{airDiscountFlag,jdbcType=BIT},
      air_discount = #{airDiscount,jdbcType=NUMERIC},
      priv_day_min = #{privDayMin,jdbcType=INTEGER},
      priv_day_max = #{privDayMax,jdbcType=INTEGER},
      is_ding = #{isDing,jdbcType=SMALLINT},
      rule_type = #{ruleType,jdbcType=INTEGER},
      modify_flag = #{modifyFlag,jdbcType=BIT},
      low_price_flag = #{lowPriceFlag,jdbcType=INTEGER},
      low_price_time = #{lowPriceTime,jdbcType=INTEGER},
      low_price_unit = #{lowPriceUnit,jdbcType=VARCHAR},
      filter_stopover_flight_flag = #{filterStopoverFlightFlag,jdbcType=INTEGER},
      is_check_flight_time = #{isCheckFlightTime,jdbcType=INTEGER},
      check_flight_time_type = #{checkFlightTimeType,jdbcType=INTEGER},
      flight_time = #{flightTime,jdbcType=INTEGER}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>