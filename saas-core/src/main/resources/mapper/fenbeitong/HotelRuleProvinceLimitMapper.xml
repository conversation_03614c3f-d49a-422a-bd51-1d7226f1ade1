<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.HotelRuleProvinceLimitMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.HotelRuleProvinceLimit">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="rule_id" jdbcType="VARCHAR" property="ruleId" />
    <id column="province_id" jdbcType="VARCHAR" property="provinceId" />
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="price" jdbcType="NUMERIC" property="price" />
    <result column="rank" jdbcType="BIGINT" property="rank" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    rule_id, province_id, id, province_name, price, rank, create_time
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.HotelRuleProvinceLimitExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from hotel_rule_province_limit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from hotel_rule_province_limit
    where rule_id = #{ruleId,jdbcType=VARCHAR}
      and province_id = #{provinceId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from hotel_rule_province_limit
    where rule_id = #{ruleId,jdbcType=VARCHAR}
      and province_id = #{provinceId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.HotelRuleProvinceLimitExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from hotel_rule_province_limit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.fenbeitong.HotelRuleProvinceLimit">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into hotel_rule_province_limit (rule_id, province_id, id, 
      province_name, price, rank, 
      create_time)
    values (#{ruleId,jdbcType=VARCHAR}, #{provinceId,jdbcType=VARCHAR}, #{id,jdbcType=VARCHAR}, 
      #{provinceName,jdbcType=VARCHAR}, #{price,jdbcType=NUMERIC}, #{rank,jdbcType=BIGINT}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.HotelRuleProvinceLimit">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into hotel_rule_province_limit
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ruleId != null">
        rule_id,
      </if>
      <if test="provinceId != null">
        province_id,
      </if>
      <if test="id != null">
        id,
      </if>
      <if test="provinceName != null">
        province_name,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="rank != null">
        rank,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ruleId != null">
        #{ruleId,jdbcType=VARCHAR},
      </if>
      <if test="provinceId != null">
        #{provinceId,jdbcType=VARCHAR},
      </if>
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=NUMERIC},
      </if>
      <if test="rank != null">
        #{rank,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.HotelRuleProvinceLimitExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from hotel_rule_province_limit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update hotel_rule_province_limit
    <set>
      <if test="record.ruleId != null">
        rule_id = #{record.ruleId,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceId != null">
        province_id = #{record.provinceId,jdbcType=VARCHAR},
      </if>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceName != null">
        province_name = #{record.provinceName,jdbcType=VARCHAR},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=NUMERIC},
      </if>
      <if test="record.rank != null">
        rank = #{record.rank,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update hotel_rule_province_limit
    set rule_id = #{record.ruleId,jdbcType=VARCHAR},
      province_id = #{record.provinceId,jdbcType=VARCHAR},
      id = #{record.id,jdbcType=VARCHAR},
      province_name = #{record.provinceName,jdbcType=VARCHAR},
      price = #{record.price,jdbcType=NUMERIC},
      rank = #{record.rank,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.HotelRuleProvinceLimit">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update hotel_rule_province_limit
    <set>
      <if test="id != null">
        id = #{id,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        province_name = #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=NUMERIC},
      </if>
      <if test="rank != null">
        rank = #{rank,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where rule_id = #{ruleId,jdbcType=VARCHAR}
      and province_id = #{provinceId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.fenbeitong.HotelRuleProvinceLimit">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update hotel_rule_province_limit
    set id = #{id,jdbcType=VARCHAR},
      province_name = #{provinceName,jdbcType=VARCHAR},
      price = #{price,jdbcType=NUMERIC},
      rank = #{rank,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where rule_id = #{ruleId,jdbcType=VARCHAR}
      and province_id = #{provinceId,jdbcType=VARCHAR}
  </update>
</mapper>