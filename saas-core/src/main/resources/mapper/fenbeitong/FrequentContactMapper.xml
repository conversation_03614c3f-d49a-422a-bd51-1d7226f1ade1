<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.FrequentContactMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.FrequentContact">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 12 14:42:21 CST 2017.
    -->
    <constructor>
      <idArg column="id" javaType="java.lang.String" jdbcType="CHAR" />
      <arg column="name" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="email" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="phone_num" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="id_number" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="id_type" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="gender" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="birth_date" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="category" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="modify_time" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="last_used" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="owner_id" javaType="java.lang.String" jdbcType="CHAR" />
      <arg column="is_employee" javaType="java.lang.Boolean" jdbcType="BIT" />
      <arg column="selected_employee_id" javaType="java.lang.String" jdbcType="CHAR" />
      <arg column="company_id" javaType="java.lang.String" jdbcType="VARCHAR" />
    </constructor>
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 12 14:42:21 CST 2017.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 12 14:42:21 CST 2017.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 12 14:42:21 CST 2017.
    -->
    id, name, email, phone_num, id_number, id_type, gender, birth_date, category, modify_time, 
    last_used, owner_id, is_employee, selected_employee_id, company_id
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.FrequentContactExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 12 14:42:21 CST 2017.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from frequent_contact
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.FrequentContactExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 12 14:42:21 CST 2017.
    -->
    delete from frequent_contact
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.FrequentContact">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 12 14:42:21 CST 2017.
    -->
    insert into frequent_contact
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="phoneNum != null">
        phone_num,
      </if>
      <if test="idNumber != null">
        id_number,
      </if>
      <if test="idType != null">
        id_type,
      </if>
      <if test="gender != null">
        gender,
      </if>
      <if test="birthDate != null">
        birth_date,
      </if>
      <if test="category != null">
        category,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="lastUsed != null">
        last_used,
      </if>
      <if test="ownerId != null">
        owner_id,
      </if>
      <if test="isEmployee != null">
        is_employee,
      </if>
      <if test="selectedEmployeeId != null">
        selected_employee_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="phoneNum != null">
        #{phoneNum,jdbcType=VARCHAR},
      </if>
      <if test="idNumber != null">
        #{idNumber,jdbcType=VARCHAR},
      </if>
      <if test="idType != null">
        #{idType,jdbcType=INTEGER},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=INTEGER},
      </if>
      <if test="birthDate != null">
        #{birthDate,jdbcType=TIMESTAMP},
      </if>
      <if test="category != null">
        #{category,jdbcType=INTEGER},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUsed != null">
        #{lastUsed,jdbcType=TIMESTAMP},
      </if>
      <if test="ownerId != null">
        #{ownerId,jdbcType=CHAR},
      </if>
      <if test="isEmployee != null">
        #{isEmployee,jdbcType=BIT},
      </if>
      <if test="selectedEmployeeId != null">
        #{selectedEmployeeId,jdbcType=CHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.FrequentContactExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 12 14:42:21 CST 2017.
    -->
    select count(*) from frequent_contact
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 12 14:42:21 CST 2017.
    -->
    update frequent_contact
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.email != null">
        email = #{record.email,jdbcType=VARCHAR},
      </if>
      <if test="record.phoneNum != null">
        phone_num = #{record.phoneNum,jdbcType=VARCHAR},
      </if>
      <if test="record.idNumber != null">
        id_number = #{record.idNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.idType != null">
        id_type = #{record.idType,jdbcType=INTEGER},
      </if>
      <if test="record.gender != null">
        gender = #{record.gender,jdbcType=INTEGER},
      </if>
      <if test="record.birthDate != null">
        birth_date = #{record.birthDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.category != null">
        category = #{record.category,jdbcType=INTEGER},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUsed != null">
        last_used = #{record.lastUsed,jdbcType=TIMESTAMP},
      </if>
      <if test="record.ownerId != null">
        owner_id = #{record.ownerId,jdbcType=CHAR},
      </if>
      <if test="record.isEmployee != null">
        is_employee = #{record.isEmployee,jdbcType=BIT},
      </if>
      <if test="record.selectedEmployeeId != null">
        selected_employee_id = #{record.selectedEmployeeId,jdbcType=CHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 12 14:42:21 CST 2017.
    -->
    update frequent_contact
    set id = #{record.id,jdbcType=CHAR},
      name = #{record.name,jdbcType=VARCHAR},
      email = #{record.email,jdbcType=VARCHAR},
      phone_num = #{record.phoneNum,jdbcType=VARCHAR},
      id_number = #{record.idNumber,jdbcType=VARCHAR},
      id_type = #{record.idType,jdbcType=INTEGER},
      gender = #{record.gender,jdbcType=INTEGER},
      birth_date = #{record.birthDate,jdbcType=TIMESTAMP},
      category = #{record.category,jdbcType=INTEGER},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      last_used = #{record.lastUsed,jdbcType=TIMESTAMP},
      owner_id = #{record.ownerId,jdbcType=CHAR},
      is_employee = #{record.isEmployee,jdbcType=BIT},
      selected_employee_id = #{record.selectedEmployeeId,jdbcType=CHAR},
      company_id = #{record.companyId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.FrequentContact">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 12 14:42:21 CST 2017.
    -->
    update frequent_contact
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="phoneNum != null">
        phone_num = #{phoneNum,jdbcType=VARCHAR},
      </if>
      <if test="idNumber != null">
        id_number = #{idNumber,jdbcType=VARCHAR},
      </if>
      <if test="idType != null">
        id_type = #{idType,jdbcType=INTEGER},
      </if>
      <if test="gender != null">
        gender = #{gender,jdbcType=INTEGER},
      </if>
      <if test="birthDate != null">
        birth_date = #{birthDate,jdbcType=TIMESTAMP},
      </if>
      <if test="category != null">
        category = #{category,jdbcType=INTEGER},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUsed != null">
        last_used = #{lastUsed,jdbcType=TIMESTAMP},
      </if>
      <if test="ownerId != null">
        owner_id = #{ownerId,jdbcType=CHAR},
      </if>
      <if test="isEmployee != null">
        is_employee = #{isEmployee,jdbcType=BIT},
      </if>
      <if test="selectedEmployeeId != null">
        selected_employee_id = #{selectedEmployeeId,jdbcType=CHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>