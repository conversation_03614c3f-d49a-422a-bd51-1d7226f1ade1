<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.TaxiPathLocationMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.TaxiPathLocation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="taxi_rule_id" jdbcType="INTEGER" property="taxiRuleId" />
    <result column="limit_departure" jdbcType="BIT" property="limitDeparture" />
    <result column="departure_location_id" jdbcType="INTEGER" property="departureLocationId" />
    <result column="limit_arrival" jdbcType="BIT" property="limitArrival" />
    <result column="arrival_location_id" jdbcType="INTEGER" property="arrivalLocationId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="limit_departure_type" jdbcType="INTEGER" property="limitDepartureType" />
    <result column="limit_arrival_type" jdbcType="INTEGER" property="limitArrivalType" />
    <result column="limit_path_type" jdbcType="INTEGER" property="limitPathType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, taxi_rule_id, limit_departure, departure_location_id, limit_arrival, 
    arrival_location_id, create_time, limit_departure_type, limit_arrival_type, limit_path_type
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiPathLocationExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from taxi_path_location
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${limit} offset ${offset}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from taxi_path_location
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from taxi_path_location
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiPathLocationExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from taxi_path_location
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiPathLocation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into taxi_path_location (company_id, taxi_rule_id, limit_departure, 
      departure_location_id, limit_arrival, arrival_location_id, 
      create_time, limit_departure_type, limit_arrival_type
      )
    values (#{companyId,jdbcType=VARCHAR}, #{taxiRuleId,jdbcType=INTEGER}, #{limitDeparture,jdbcType=BIT}, 
      #{departureLocationId,jdbcType=INTEGER}, #{limitArrival,jdbcType=BIT}, #{arrivalLocationId,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{limitDepartureType,jdbcType=INTEGER}, #{limitArrivalType,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiPathLocation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into taxi_path_location
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="taxiRuleId != null">
        taxi_rule_id,
      </if>
      <if test="limitDeparture != null">
        limit_departure,
      </if>
      <if test="departureLocationId != null">
        departure_location_id,
      </if>
      <if test="limitArrival != null">
        limit_arrival,
      </if>
      <if test="arrivalLocationId != null">
        arrival_location_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="limitDepartureType != null">
        limit_departure_type,
      </if>
      <if test="limitArrivalType != null">
        limit_arrival_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="taxiRuleId != null">
        #{taxiRuleId,jdbcType=INTEGER},
      </if>
      <if test="limitDeparture != null">
        #{limitDeparture,jdbcType=BIT},
      </if>
      <if test="departureLocationId != null">
        #{departureLocationId,jdbcType=INTEGER},
      </if>
      <if test="limitArrival != null">
        #{limitArrival,jdbcType=BIT},
      </if>
      <if test="arrivalLocationId != null">
        #{arrivalLocationId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="limitDepartureType != null">
        #{limitDepartureType,jdbcType=INTEGER},
      </if>
      <if test="limitArrivalType != null">
        #{limitArrivalType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiPathLocationExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from taxi_path_location
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update taxi_path_location
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.taxiRuleId != null">
        taxi_rule_id = #{record.taxiRuleId,jdbcType=INTEGER},
      </if>
      <if test="record.limitDeparture != null">
        limit_departure = #{record.limitDeparture,jdbcType=BIT},
      </if>
      <if test="record.departureLocationId != null">
        departure_location_id = #{record.departureLocationId,jdbcType=INTEGER},
      </if>
      <if test="record.limitArrival != null">
        limit_arrival = #{record.limitArrival,jdbcType=BIT},
      </if>
      <if test="record.arrivalLocationId != null">
        arrival_location_id = #{record.arrivalLocationId,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.limitDepartureType != null">
        limit_departure_type = #{record.limitDepartureType,jdbcType=INTEGER},
      </if>
      <if test="record.limitArrivalType != null">
        limit_arrival_type = #{record.limitArrivalType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update taxi_path_location
    set id = #{record.id,jdbcType=INTEGER},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      taxi_rule_id = #{record.taxiRuleId,jdbcType=INTEGER},
      limit_departure = #{record.limitDeparture,jdbcType=BIT},
      departure_location_id = #{record.departureLocationId,jdbcType=INTEGER},
      limit_arrival = #{record.limitArrival,jdbcType=BIT},
      arrival_location_id = #{record.arrivalLocationId,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      limit_departure_type = #{record.limitDepartureType,jdbcType=INTEGER},
      limit_arrival_type = #{record.limitArrivalType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiPathLocation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update taxi_path_location
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="taxiRuleId != null">
        taxi_rule_id = #{taxiRuleId,jdbcType=INTEGER},
      </if>
      <if test="limitDeparture != null">
        limit_departure = #{limitDeparture,jdbcType=BIT},
      </if>
      <if test="departureLocationId != null">
        departure_location_id = #{departureLocationId,jdbcType=INTEGER},
      </if>
      <if test="limitArrival != null">
        limit_arrival = #{limitArrival,jdbcType=BIT},
      </if>
      <if test="arrivalLocationId != null">
        arrival_location_id = #{arrivalLocationId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="limitDepartureType != null">
        limit_departure_type = #{limitDepartureType,jdbcType=INTEGER},
      </if>
      <if test="limitArrivalType != null">
        limit_arrival_type = #{limitArrivalType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiPathLocation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update taxi_path_location
    set company_id = #{companyId,jdbcType=VARCHAR},
      taxi_rule_id = #{taxiRuleId,jdbcType=INTEGER},
      limit_departure = #{limitDeparture,jdbcType=BIT},
      departure_location_id = #{departureLocationId,jdbcType=INTEGER},
      limit_arrival = #{limitArrival,jdbcType=BIT},
      arrival_location_id = #{arrivalLocationId,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      limit_departure_type = #{limitDepartureType,jdbcType=INTEGER},
      limit_arrival_type = #{limitArrivalType,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>