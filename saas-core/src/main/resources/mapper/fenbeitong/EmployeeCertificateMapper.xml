<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.EmployeeCertificateMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.EmployeeCertificate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat May 20 14:07:51 CST 2017.
    -->
    <constructor>
      <idArg column="employee_id" javaType="java.lang.String" jdbcType="VARCHAR" />
      <idArg column="cert_type" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="cert_no" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="create_time" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="update_time" javaType="java.util.Date" jdbcType="TIMESTAMP" />
    </constructor>
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat May 20 14:07:51 CST 2017.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat May 20 14:07:51 CST 2017.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat May 20 14:07:51 CST 2017.
    -->
    employee_id, cert_type, cert_no, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.EmployeeCertificateExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat May 20 14:07:51 CST 2017.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from employee_certificate
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.EmployeeCertificateExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat May 20 14:07:51 CST 2017.
    -->
    delete from employee_certificate
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.EmployeeCertificate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat May 20 14:07:51 CST 2017.
    -->
    insert into employee_certificate
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="employeeId != null">
        employee_id,
      </if>
      <if test="certType != null">
        cert_type,
      </if>
      <if test="certNo != null">
        cert_no,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="employeeId != null">
        #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="certType != null">
        #{certType,jdbcType=INTEGER},
      </if>
      <if test="certNo != null">
        #{certNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.EmployeeCertificateExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat May 20 14:07:51 CST 2017.
    -->
    select count(*) from employee_certificate
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat May 20 14:07:51 CST 2017.
    -->
    update employee_certificate
    <set>
      <if test="record.employeeId != null">
        employee_id = #{record.employeeId,jdbcType=VARCHAR},
      </if>
      <if test="record.certType != null">
        cert_type = #{record.certType,jdbcType=INTEGER},
      </if>
      <if test="record.certNo != null">
        cert_no = #{record.certNo,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat May 20 14:07:51 CST 2017.
    -->
    update employee_certificate
    set employee_id = #{record.employeeId,jdbcType=VARCHAR},
      cert_type = #{record.certType,jdbcType=INTEGER},
      cert_no = #{record.certNo,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.EmployeeCertificate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat May 20 14:07:51 CST 2017.
    -->
    update employee_certificate
    <set>
      <if test="certNo != null">
        cert_no = #{certNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where employee_id = #{employeeId,jdbcType=VARCHAR}
      and cert_type = #{certType,jdbcType=INTEGER}
  </update>

  <!--获取用户证件-->
  <select id="selectEmployeeCert" parameterType="java.lang.String" resultType="com.fenbeitong.saas.core.contract.user.innerbean.CertBean">

      SELECT
          c.cert_type AS certType,c.cert_no

      FROM
          employee_certificate c

      WHERE c.employee_id = #{employee_id,jdbcType=VARCHAR}


  </select>
</mapper>