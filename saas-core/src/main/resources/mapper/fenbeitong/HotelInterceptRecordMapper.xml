<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.HotelInterceptRecordMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.HotelInterceptRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="employee_id" jdbcType="CHAR" property="employeeId" />
    <result column="company_id" jdbcType="CHAR" property="companyId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="contact_name" jdbcType="VARCHAR" property="contactName" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="channel" jdbcType="INTEGER" property="channel" />
    <result column="total_price" jdbcType="NUMERIC" property="totalPrice" />
    <result column="hotel_rule" jdbcType="INTEGER" property="hotelRule" />
    <result column="hotel_rule_flag" jdbcType="BIT" property="hotelRuleFlag" />
    <result column="hotel_verify_flag" jdbcType="BIT" property="hotelVerifyFlag" />
    <result column="exceed_buy_flag" jdbcType="BIT" property="exceedBuyFlag" />
    <result column="first_tier_flag" jdbcType="BIT" property="firstTierFlag" />
    <result column="first_tier_price" jdbcType="NUMERIC" property="firstTierPrice" />
    <result column="second_tier_flag" jdbcType="BIT" property="secondTierFlag" />
    <result column="second_tier_price" jdbcType="NUMERIC" property="secondTierPrice" />
    <result column="other_tier_flag" jdbcType="BIT" property="otherTierFlag" />
    <result column="other_tier_price" jdbcType="NUMERIC" property="otherTierPrice" />
    <result column="hotel_address" jdbcType="VARCHAR" property="hotelAddress" />
    <result column="hotel_code" jdbcType="VARCHAR" property="hotelCode" />
    <result column="hotel_lng" jdbcType="VARCHAR" property="hotelLng" />
    <result column="hotel_lat" jdbcType="VARCHAR" property="hotelLat" />
    <result column="hotel_name" jdbcType="VARCHAR" property="hotelName" />
    <result column="hotel_phone" jdbcType="VARCHAR" property="hotelPhone" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="fb_city_name" jdbcType="VARCHAR" property="fbCityName" />
    <result column="fb_city_code" jdbcType="VARCHAR" property="fbCityCode" />
    <result column="bed_type" jdbcType="VARCHAR" property="bedType" />
    <result column="room_code" jdbcType="VARCHAR" property="roomCode" />
    <result column="room_name" jdbcType="VARCHAR" property="roomName" />
    <result column="plan_info" jdbcType="VARCHAR" property="planInfo" />
    <result column="customer_info" jdbcType="VARCHAR" property="customerInfo" />
    <result column="check_in_date" jdbcType="TIMESTAMP" property="checkInDate" />
    <result column="check_out_date" jdbcType="TIMESTAMP" property="checkOutDate" />
    <result column="err_msg" jdbcType="VARCHAR" property="errMsg" />
    <result column="err_code" jdbcType="INTEGER" property="errCode" />
    <result column="cost_center_id" jdbcType="VARCHAR" property="costCenterId" />
    <result column="cost_center_type" jdbcType="INTEGER" property="costCenterType" />
    <result column="exceed_buy_type" jdbcType="INTEGER" property="exceedBuyType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, employee_id, company_id, create_time, contact_name, contact_phone, channel, total_price, 
    hotel_rule, hotel_rule_flag, hotel_verify_flag, exceed_buy_flag, first_tier_flag, 
    first_tier_price, second_tier_flag, second_tier_price, other_tier_flag, other_tier_price, 
    hotel_address, hotel_code, hotel_lng, hotel_lat, hotel_name, hotel_phone, city_name, 
    city_code, fb_city_name, fb_city_code, bed_type, room_code, room_name, plan_info, 
    customer_info, check_in_date, check_out_date, err_msg, err_code, cost_center_id, 
    cost_center_type, exceed_buy_type
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.HotelInterceptRecordExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from hotel_intercept_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from hotel_intercept_record
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from hotel_intercept_record
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.HotelInterceptRecordExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from hotel_intercept_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.fenbeitong.HotelInterceptRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into hotel_intercept_record (id, employee_id, company_id, 
      create_time, contact_name, contact_phone, 
      channel, total_price, hotel_rule, 
      hotel_rule_flag, hotel_verify_flag, exceed_buy_flag, 
      first_tier_flag, first_tier_price, second_tier_flag, 
      second_tier_price, other_tier_flag, other_tier_price, 
      hotel_address, hotel_code, hotel_lng, 
      hotel_lat, hotel_name, hotel_phone, 
      city_name, city_code, fb_city_name, 
      fb_city_code, bed_type, room_code, 
      room_name, plan_info, customer_info, 
      check_in_date, check_out_date, err_msg, 
      err_code, cost_center_id, cost_center_type, 
      exceed_buy_type)
    values (#{id,jdbcType=CHAR}, #{employeeId,jdbcType=CHAR}, #{companyId,jdbcType=CHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{contactName,jdbcType=VARCHAR}, #{contactPhone,jdbcType=VARCHAR}, 
      #{channel,jdbcType=INTEGER}, #{totalPrice,jdbcType=NUMERIC}, #{hotelRule,jdbcType=INTEGER}, 
      #{hotelRuleFlag,jdbcType=BIT}, #{hotelVerifyFlag,jdbcType=BIT}, #{exceedBuyFlag,jdbcType=BIT}, 
      #{firstTierFlag,jdbcType=BIT}, #{firstTierPrice,jdbcType=NUMERIC}, #{secondTierFlag,jdbcType=BIT}, 
      #{secondTierPrice,jdbcType=NUMERIC}, #{otherTierFlag,jdbcType=BIT}, #{otherTierPrice,jdbcType=NUMERIC}, 
      #{hotelAddress,jdbcType=VARCHAR}, #{hotelCode,jdbcType=VARCHAR}, #{hotelLng,jdbcType=VARCHAR}, 
      #{hotelLat,jdbcType=VARCHAR}, #{hotelName,jdbcType=VARCHAR}, #{hotelPhone,jdbcType=VARCHAR}, 
      #{cityName,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, #{fbCityName,jdbcType=VARCHAR}, 
      #{fbCityCode,jdbcType=VARCHAR}, #{bedType,jdbcType=VARCHAR}, #{roomCode,jdbcType=VARCHAR}, 
      #{roomName,jdbcType=VARCHAR}, #{planInfo,jdbcType=VARCHAR}, #{customerInfo,jdbcType=VARCHAR}, 
      #{checkInDate,jdbcType=TIMESTAMP}, #{checkOutDate,jdbcType=TIMESTAMP}, #{errMsg,jdbcType=VARCHAR}, 
      #{errCode,jdbcType=INTEGER}, #{costCenterId,jdbcType=VARCHAR}, #{costCenterType,jdbcType=INTEGER}, 
      #{exceedBuyType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.HotelInterceptRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into hotel_intercept_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="employeeId != null">
        employee_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="contactName != null">
        contact_name,
      </if>
      <if test="contactPhone != null">
        contact_phone,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="totalPrice != null">
        total_price,
      </if>
      <if test="hotelRule != null">
        hotel_rule,
      </if>
      <if test="hotelRuleFlag != null">
        hotel_rule_flag,
      </if>
      <if test="hotelVerifyFlag != null">
        hotel_verify_flag,
      </if>
      <if test="exceedBuyFlag != null">
        exceed_buy_flag,
      </if>
      <if test="firstTierFlag != null">
        first_tier_flag,
      </if>
      <if test="firstTierPrice != null">
        first_tier_price,
      </if>
      <if test="secondTierFlag != null">
        second_tier_flag,
      </if>
      <if test="secondTierPrice != null">
        second_tier_price,
      </if>
      <if test="otherTierFlag != null">
        other_tier_flag,
      </if>
      <if test="otherTierPrice != null">
        other_tier_price,
      </if>
      <if test="hotelAddress != null">
        hotel_address,
      </if>
      <if test="hotelCode != null">
        hotel_code,
      </if>
      <if test="hotelLng != null">
        hotel_lng,
      </if>
      <if test="hotelLat != null">
        hotel_lat,
      </if>
      <if test="hotelName != null">
        hotel_name,
      </if>
      <if test="hotelPhone != null">
        hotel_phone,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="fbCityName != null">
        fb_city_name,
      </if>
      <if test="fbCityCode != null">
        fb_city_code,
      </if>
      <if test="bedType != null">
        bed_type,
      </if>
      <if test="roomCode != null">
        room_code,
      </if>
      <if test="roomName != null">
        room_name,
      </if>
      <if test="planInfo != null">
        plan_info,
      </if>
      <if test="customerInfo != null">
        customer_info,
      </if>
      <if test="checkInDate != null">
        check_in_date,
      </if>
      <if test="checkOutDate != null">
        check_out_date,
      </if>
      <if test="errMsg != null">
        err_msg,
      </if>
      <if test="errCode != null">
        err_code,
      </if>
      <if test="costCenterId != null">
        cost_center_id,
      </if>
      <if test="costCenterType != null">
        cost_center_type,
      </if>
      <if test="exceedBuyType != null">
        exceed_buy_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="employeeId != null">
        #{employeeId,jdbcType=CHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contactName != null">
        #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=INTEGER},
      </if>
      <if test="totalPrice != null">
        #{totalPrice,jdbcType=NUMERIC},
      </if>
      <if test="hotelRule != null">
        #{hotelRule,jdbcType=INTEGER},
      </if>
      <if test="hotelRuleFlag != null">
        #{hotelRuleFlag,jdbcType=BIT},
      </if>
      <if test="hotelVerifyFlag != null">
        #{hotelVerifyFlag,jdbcType=BIT},
      </if>
      <if test="exceedBuyFlag != null">
        #{exceedBuyFlag,jdbcType=BIT},
      </if>
      <if test="firstTierFlag != null">
        #{firstTierFlag,jdbcType=BIT},
      </if>
      <if test="firstTierPrice != null">
        #{firstTierPrice,jdbcType=NUMERIC},
      </if>
      <if test="secondTierFlag != null">
        #{secondTierFlag,jdbcType=BIT},
      </if>
      <if test="secondTierPrice != null">
        #{secondTierPrice,jdbcType=NUMERIC},
      </if>
      <if test="otherTierFlag != null">
        #{otherTierFlag,jdbcType=BIT},
      </if>
      <if test="otherTierPrice != null">
        #{otherTierPrice,jdbcType=NUMERIC},
      </if>
      <if test="hotelAddress != null">
        #{hotelAddress,jdbcType=VARCHAR},
      </if>
      <if test="hotelCode != null">
        #{hotelCode,jdbcType=VARCHAR},
      </if>
      <if test="hotelLng != null">
        #{hotelLng,jdbcType=VARCHAR},
      </if>
      <if test="hotelLat != null">
        #{hotelLat,jdbcType=VARCHAR},
      </if>
      <if test="hotelName != null">
        #{hotelName,jdbcType=VARCHAR},
      </if>
      <if test="hotelPhone != null">
        #{hotelPhone,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="fbCityName != null">
        #{fbCityName,jdbcType=VARCHAR},
      </if>
      <if test="fbCityCode != null">
        #{fbCityCode,jdbcType=VARCHAR},
      </if>
      <if test="bedType != null">
        #{bedType,jdbcType=VARCHAR},
      </if>
      <if test="roomCode != null">
        #{roomCode,jdbcType=VARCHAR},
      </if>
      <if test="roomName != null">
        #{roomName,jdbcType=VARCHAR},
      </if>
      <if test="planInfo != null">
        #{planInfo,jdbcType=VARCHAR},
      </if>
      <if test="customerInfo != null">
        #{customerInfo,jdbcType=VARCHAR},
      </if>
      <if test="checkInDate != null">
        #{checkInDate,jdbcType=TIMESTAMP},
      </if>
      <if test="checkOutDate != null">
        #{checkOutDate,jdbcType=TIMESTAMP},
      </if>
      <if test="errMsg != null">
        #{errMsg,jdbcType=VARCHAR},
      </if>
      <if test="errCode != null">
        #{errCode,jdbcType=INTEGER},
      </if>
      <if test="costCenterId != null">
        #{costCenterId,jdbcType=VARCHAR},
      </if>
      <if test="costCenterType != null">
        #{costCenterType,jdbcType=INTEGER},
      </if>
      <if test="exceedBuyType != null">
        #{exceedBuyType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.HotelInterceptRecordExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from hotel_intercept_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update hotel_intercept_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.employeeId != null">
        employee_id = #{record.employeeId,jdbcType=CHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=CHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.contactName != null">
        contact_name = #{record.contactName,jdbcType=VARCHAR},
      </if>
      <if test="record.contactPhone != null">
        contact_phone = #{record.contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.channel != null">
        channel = #{record.channel,jdbcType=INTEGER},
      </if>
      <if test="record.totalPrice != null">
        total_price = #{record.totalPrice,jdbcType=NUMERIC},
      </if>
      <if test="record.hotelRule != null">
        hotel_rule = #{record.hotelRule,jdbcType=INTEGER},
      </if>
      <if test="record.hotelRuleFlag != null">
        hotel_rule_flag = #{record.hotelRuleFlag,jdbcType=BIT},
      </if>
      <if test="record.hotelVerifyFlag != null">
        hotel_verify_flag = #{record.hotelVerifyFlag,jdbcType=BIT},
      </if>
      <if test="record.exceedBuyFlag != null">
        exceed_buy_flag = #{record.exceedBuyFlag,jdbcType=BIT},
      </if>
      <if test="record.firstTierFlag != null">
        first_tier_flag = #{record.firstTierFlag,jdbcType=BIT},
      </if>
      <if test="record.firstTierPrice != null">
        first_tier_price = #{record.firstTierPrice,jdbcType=NUMERIC},
      </if>
      <if test="record.secondTierFlag != null">
        second_tier_flag = #{record.secondTierFlag,jdbcType=BIT},
      </if>
      <if test="record.secondTierPrice != null">
        second_tier_price = #{record.secondTierPrice,jdbcType=NUMERIC},
      </if>
      <if test="record.otherTierFlag != null">
        other_tier_flag = #{record.otherTierFlag,jdbcType=BIT},
      </if>
      <if test="record.otherTierPrice != null">
        other_tier_price = #{record.otherTierPrice,jdbcType=NUMERIC},
      </if>
      <if test="record.hotelAddress != null">
        hotel_address = #{record.hotelAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.hotelCode != null">
        hotel_code = #{record.hotelCode,jdbcType=VARCHAR},
      </if>
      <if test="record.hotelLng != null">
        hotel_lng = #{record.hotelLng,jdbcType=VARCHAR},
      </if>
      <if test="record.hotelLat != null">
        hotel_lat = #{record.hotelLat,jdbcType=VARCHAR},
      </if>
      <if test="record.hotelName != null">
        hotel_name = #{record.hotelName,jdbcType=VARCHAR},
      </if>
      <if test="record.hotelPhone != null">
        hotel_phone = #{record.hotelPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.cityName != null">
        city_name = #{record.cityName,jdbcType=VARCHAR},
      </if>
      <if test="record.cityCode != null">
        city_code = #{record.cityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.fbCityName != null">
        fb_city_name = #{record.fbCityName,jdbcType=VARCHAR},
      </if>
      <if test="record.fbCityCode != null">
        fb_city_code = #{record.fbCityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.bedType != null">
        bed_type = #{record.bedType,jdbcType=VARCHAR},
      </if>
      <if test="record.roomCode != null">
        room_code = #{record.roomCode,jdbcType=VARCHAR},
      </if>
      <if test="record.roomName != null">
        room_name = #{record.roomName,jdbcType=VARCHAR},
      </if>
      <if test="record.planInfo != null">
        plan_info = #{record.planInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.customerInfo != null">
        customer_info = #{record.customerInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.checkInDate != null">
        check_in_date = #{record.checkInDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.checkOutDate != null">
        check_out_date = #{record.checkOutDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.errMsg != null">
        err_msg = #{record.errMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.errCode != null">
        err_code = #{record.errCode,jdbcType=INTEGER},
      </if>
      <if test="record.costCenterId != null">
        cost_center_id = #{record.costCenterId,jdbcType=VARCHAR},
      </if>
      <if test="record.costCenterType != null">
        cost_center_type = #{record.costCenterType,jdbcType=INTEGER},
      </if>
      <if test="record.exceedBuyType != null">
        exceed_buy_type = #{record.exceedBuyType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update hotel_intercept_record
    set id = #{record.id,jdbcType=CHAR},
      employee_id = #{record.employeeId,jdbcType=CHAR},
      company_id = #{record.companyId,jdbcType=CHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      contact_name = #{record.contactName,jdbcType=VARCHAR},
      contact_phone = #{record.contactPhone,jdbcType=VARCHAR},
      channel = #{record.channel,jdbcType=INTEGER},
      total_price = #{record.totalPrice,jdbcType=NUMERIC},
      hotel_rule = #{record.hotelRule,jdbcType=INTEGER},
      hotel_rule_flag = #{record.hotelRuleFlag,jdbcType=BIT},
      hotel_verify_flag = #{record.hotelVerifyFlag,jdbcType=BIT},
      exceed_buy_flag = #{record.exceedBuyFlag,jdbcType=BIT},
      first_tier_flag = #{record.firstTierFlag,jdbcType=BIT},
      first_tier_price = #{record.firstTierPrice,jdbcType=NUMERIC},
      second_tier_flag = #{record.secondTierFlag,jdbcType=BIT},
      second_tier_price = #{record.secondTierPrice,jdbcType=NUMERIC},
      other_tier_flag = #{record.otherTierFlag,jdbcType=BIT},
      other_tier_price = #{record.otherTierPrice,jdbcType=NUMERIC},
      hotel_address = #{record.hotelAddress,jdbcType=VARCHAR},
      hotel_code = #{record.hotelCode,jdbcType=VARCHAR},
      hotel_lng = #{record.hotelLng,jdbcType=VARCHAR},
      hotel_lat = #{record.hotelLat,jdbcType=VARCHAR},
      hotel_name = #{record.hotelName,jdbcType=VARCHAR},
      hotel_phone = #{record.hotelPhone,jdbcType=VARCHAR},
      city_name = #{record.cityName,jdbcType=VARCHAR},
      city_code = #{record.cityCode,jdbcType=VARCHAR},
      fb_city_name = #{record.fbCityName,jdbcType=VARCHAR},
      fb_city_code = #{record.fbCityCode,jdbcType=VARCHAR},
      bed_type = #{record.bedType,jdbcType=VARCHAR},
      room_code = #{record.roomCode,jdbcType=VARCHAR},
      room_name = #{record.roomName,jdbcType=VARCHAR},
      plan_info = #{record.planInfo,jdbcType=VARCHAR},
      customer_info = #{record.customerInfo,jdbcType=VARCHAR},
      check_in_date = #{record.checkInDate,jdbcType=TIMESTAMP},
      check_out_date = #{record.checkOutDate,jdbcType=TIMESTAMP},
      err_msg = #{record.errMsg,jdbcType=VARCHAR},
      err_code = #{record.errCode,jdbcType=INTEGER},
      cost_center_id = #{record.costCenterId,jdbcType=VARCHAR},
      cost_center_type = #{record.costCenterType,jdbcType=INTEGER},
      exceed_buy_type = #{record.exceedBuyType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.HotelInterceptRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update hotel_intercept_record
    <set>
      <if test="employeeId != null">
        employee_id = #{employeeId,jdbcType=CHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contactName != null">
        contact_name = #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        contact_phone = #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=INTEGER},
      </if>
      <if test="totalPrice != null">
        total_price = #{totalPrice,jdbcType=NUMERIC},
      </if>
      <if test="hotelRule != null">
        hotel_rule = #{hotelRule,jdbcType=INTEGER},
      </if>
      <if test="hotelRuleFlag != null">
        hotel_rule_flag = #{hotelRuleFlag,jdbcType=BIT},
      </if>
      <if test="hotelVerifyFlag != null">
        hotel_verify_flag = #{hotelVerifyFlag,jdbcType=BIT},
      </if>
      <if test="exceedBuyFlag != null">
        exceed_buy_flag = #{exceedBuyFlag,jdbcType=BIT},
      </if>
      <if test="firstTierFlag != null">
        first_tier_flag = #{firstTierFlag,jdbcType=BIT},
      </if>
      <if test="firstTierPrice != null">
        first_tier_price = #{firstTierPrice,jdbcType=NUMERIC},
      </if>
      <if test="secondTierFlag != null">
        second_tier_flag = #{secondTierFlag,jdbcType=BIT},
      </if>
      <if test="secondTierPrice != null">
        second_tier_price = #{secondTierPrice,jdbcType=NUMERIC},
      </if>
      <if test="otherTierFlag != null">
        other_tier_flag = #{otherTierFlag,jdbcType=BIT},
      </if>
      <if test="otherTierPrice != null">
        other_tier_price = #{otherTierPrice,jdbcType=NUMERIC},
      </if>
      <if test="hotelAddress != null">
        hotel_address = #{hotelAddress,jdbcType=VARCHAR},
      </if>
      <if test="hotelCode != null">
        hotel_code = #{hotelCode,jdbcType=VARCHAR},
      </if>
      <if test="hotelLng != null">
        hotel_lng = #{hotelLng,jdbcType=VARCHAR},
      </if>
      <if test="hotelLat != null">
        hotel_lat = #{hotelLat,jdbcType=VARCHAR},
      </if>
      <if test="hotelName != null">
        hotel_name = #{hotelName,jdbcType=VARCHAR},
      </if>
      <if test="hotelPhone != null">
        hotel_phone = #{hotelPhone,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        city_code = #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="fbCityName != null">
        fb_city_name = #{fbCityName,jdbcType=VARCHAR},
      </if>
      <if test="fbCityCode != null">
        fb_city_code = #{fbCityCode,jdbcType=VARCHAR},
      </if>
      <if test="bedType != null">
        bed_type = #{bedType,jdbcType=VARCHAR},
      </if>
      <if test="roomCode != null">
        room_code = #{roomCode,jdbcType=VARCHAR},
      </if>
      <if test="roomName != null">
        room_name = #{roomName,jdbcType=VARCHAR},
      </if>
      <if test="planInfo != null">
        plan_info = #{planInfo,jdbcType=VARCHAR},
      </if>
      <if test="customerInfo != null">
        customer_info = #{customerInfo,jdbcType=VARCHAR},
      </if>
      <if test="checkInDate != null">
        check_in_date = #{checkInDate,jdbcType=TIMESTAMP},
      </if>
      <if test="checkOutDate != null">
        check_out_date = #{checkOutDate,jdbcType=TIMESTAMP},
      </if>
      <if test="errMsg != null">
        err_msg = #{errMsg,jdbcType=VARCHAR},
      </if>
      <if test="errCode != null">
        err_code = #{errCode,jdbcType=INTEGER},
      </if>
      <if test="costCenterId != null">
        cost_center_id = #{costCenterId,jdbcType=VARCHAR},
      </if>
      <if test="costCenterType != null">
        cost_center_type = #{costCenterType,jdbcType=INTEGER},
      </if>
      <if test="exceedBuyType != null">
        exceed_buy_type = #{exceedBuyType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.fenbeitong.HotelInterceptRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update hotel_intercept_record
    set employee_id = #{employeeId,jdbcType=CHAR},
      company_id = #{companyId,jdbcType=CHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      contact_name = #{contactName,jdbcType=VARCHAR},
      contact_phone = #{contactPhone,jdbcType=VARCHAR},
      channel = #{channel,jdbcType=INTEGER},
      total_price = #{totalPrice,jdbcType=NUMERIC},
      hotel_rule = #{hotelRule,jdbcType=INTEGER},
      hotel_rule_flag = #{hotelRuleFlag,jdbcType=BIT},
      hotel_verify_flag = #{hotelVerifyFlag,jdbcType=BIT},
      exceed_buy_flag = #{exceedBuyFlag,jdbcType=BIT},
      first_tier_flag = #{firstTierFlag,jdbcType=BIT},
      first_tier_price = #{firstTierPrice,jdbcType=NUMERIC},
      second_tier_flag = #{secondTierFlag,jdbcType=BIT},
      second_tier_price = #{secondTierPrice,jdbcType=NUMERIC},
      other_tier_flag = #{otherTierFlag,jdbcType=BIT},
      other_tier_price = #{otherTierPrice,jdbcType=NUMERIC},
      hotel_address = #{hotelAddress,jdbcType=VARCHAR},
      hotel_code = #{hotelCode,jdbcType=VARCHAR},
      hotel_lng = #{hotelLng,jdbcType=VARCHAR},
      hotel_lat = #{hotelLat,jdbcType=VARCHAR},
      hotel_name = #{hotelName,jdbcType=VARCHAR},
      hotel_phone = #{hotelPhone,jdbcType=VARCHAR},
      city_name = #{cityName,jdbcType=VARCHAR},
      city_code = #{cityCode,jdbcType=VARCHAR},
      fb_city_name = #{fbCityName,jdbcType=VARCHAR},
      fb_city_code = #{fbCityCode,jdbcType=VARCHAR},
      bed_type = #{bedType,jdbcType=VARCHAR},
      room_code = #{roomCode,jdbcType=VARCHAR},
      room_name = #{roomName,jdbcType=VARCHAR},
      plan_info = #{planInfo,jdbcType=VARCHAR},
      customer_info = #{customerInfo,jdbcType=VARCHAR},
      check_in_date = #{checkInDate,jdbcType=TIMESTAMP},
      check_out_date = #{checkOutDate,jdbcType=TIMESTAMP},
      err_msg = #{errMsg,jdbcType=VARCHAR},
      err_code = #{errCode,jdbcType=INTEGER},
      cost_center_id = #{costCenterId,jdbcType=VARCHAR},
      cost_center_type = #{costCenterType,jdbcType=INTEGER},
      exceed_buy_type = #{exceedBuyType,jdbcType=INTEGER}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>