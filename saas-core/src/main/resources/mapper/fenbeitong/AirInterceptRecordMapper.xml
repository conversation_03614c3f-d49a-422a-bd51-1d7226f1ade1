<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.AirInterceptRecordMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.AirInterceptRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="employee_id" jdbcType="CHAR" property="employeeId" />
    <result column="company_id" jdbcType="CHAR" property="companyId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="contact_name" jdbcType="VARCHAR" property="contactName" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="channel" jdbcType="INTEGER" property="channel" />
    <result column="total_price" jdbcType="NUMERIC" property="totalPrice" />
    <result column="air_rule" jdbcType="INTEGER" property="airRule" />
    <result column="air_rule_flag" jdbcType="BIT" property="airRuleFlag" />
    <result column="air_verify_flag" jdbcType="BIT" property="airVerifyFlag" />
    <result column="exceed_buy_flag" jdbcType="BIT" property="exceedBuyFlag" />
    <result column="air_cabin_flag" jdbcType="BIT" property="airCabinFlag" />
    <result column="air_cabin_type" jdbcType="VARCHAR" property="airCabinType" />
    <result column="air_price_flag" jdbcType="BIT" property="airPriceFlag" />
    <result column="air_unit_price" jdbcType="NUMERIC" property="airUnitPrice" />
    <result column="flight_no" jdbcType="VARCHAR" property="flightNo" />
    <result column="plane_type" jdbcType="VARCHAR" property="planeType" />
    <result column="airline_name" jdbcType="VARCHAR" property="airlineName" />
    <result column="service_class" jdbcType="INTEGER" property="serviceClass" />
    <result column="seat_msg" jdbcType="VARCHAR" property="seatMsg" />
    <result column="cabin" jdbcType="VARCHAR" property="cabin" />
    <result column="starting_city" jdbcType="VARCHAR" property="startingCity" />
    <result column="starting_code" jdbcType="VARCHAR" property="startingCode" />
    <result column="destination_city" jdbcType="VARCHAR" property="destinationCity" />
    <result column="destination_code" jdbcType="VARCHAR" property="destinationCode" />
    <result column="starting_airport" jdbcType="VARCHAR" property="startingAirport" />
    <result column="destination_airport" jdbcType="VARCHAR" property="destinationAirport" />
    <result column="starting_terminal" jdbcType="VARCHAR" property="startingTerminal" />
    <result column="destination_terminal" jdbcType="VARCHAR" property="destinationTerminal" />
    <result column="is_middle_stop" jdbcType="BIT" property="isMiddleStop" />
    <result column="departure_timestamp" jdbcType="TIMESTAMP" property="departureTimestamp" />
    <result column="arrived_timestamp" jdbcType="TIMESTAMP" property="arrivedTimestamp" />
    <result column="par_price" jdbcType="NUMERIC" property="parPrice" />
    <result column="fuel_tax" jdbcType="NUMERIC" property="fuelTax" />
    <result column="airport_tax" jdbcType="NUMERIC" property="airportTax" />
    <result column="sale_price" jdbcType="NUMERIC" property="salePrice" />
    <result column="passenger_info_list" jdbcType="VARCHAR" property="passengerInfoList" />
    <result column="err_msg" jdbcType="VARCHAR" property="errMsg" />
    <result column="err_code" jdbcType="INTEGER" property="errCode" />
    <result column="cost_center_id" jdbcType="VARCHAR" property="costCenterId" />
    <result column="cost_center_type" jdbcType="INTEGER" property="costCenterType" />
    <result column="exceed_buy_type" jdbcType="INTEGER" property="exceedBuyType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, employee_id, company_id, create_time, contact_name, contact_phone, channel, total_price, 
    air_rule, air_rule_flag, air_verify_flag, exceed_buy_flag, air_cabin_flag, air_cabin_type, 
    air_price_flag, air_unit_price, flight_no, plane_type, airline_name, service_class, 
    seat_msg, cabin, starting_city, starting_code, destination_city, destination_code, 
    starting_airport, destination_airport, starting_terminal, destination_terminal, is_middle_stop, 
    departure_timestamp, arrived_timestamp, par_price, fuel_tax, airport_tax, sale_price, 
    passenger_info_list, err_msg, err_code, cost_center_id, cost_center_type, exceed_buy_type
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.AirInterceptRecordExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from air_intercept_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from air_intercept_record
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from air_intercept_record
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.AirInterceptRecordExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from air_intercept_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.fenbeitong.AirInterceptRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into air_intercept_record (id, employee_id, company_id, 
      create_time, contact_name, contact_phone, 
      channel, total_price, air_rule, 
      air_rule_flag, air_verify_flag, exceed_buy_flag, 
      air_cabin_flag, air_cabin_type, air_price_flag, 
      air_unit_price, flight_no, plane_type, 
      airline_name, service_class, seat_msg, 
      cabin, starting_city, starting_code, 
      destination_city, destination_code, starting_airport, 
      destination_airport, starting_terminal, destination_terminal, 
      is_middle_stop, departure_timestamp, arrived_timestamp, 
      par_price, fuel_tax, airport_tax, 
      sale_price, passenger_info_list, err_msg, 
      err_code, cost_center_id, cost_center_type, 
      exceed_buy_type)
    values (#{id,jdbcType=CHAR}, #{employeeId,jdbcType=CHAR}, #{companyId,jdbcType=CHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{contactName,jdbcType=VARCHAR}, #{contactPhone,jdbcType=VARCHAR}, 
      #{channel,jdbcType=INTEGER}, #{totalPrice,jdbcType=NUMERIC}, #{airRule,jdbcType=INTEGER}, 
      #{airRuleFlag,jdbcType=BIT}, #{airVerifyFlag,jdbcType=BIT}, #{exceedBuyFlag,jdbcType=BIT}, 
      #{airCabinFlag,jdbcType=BIT}, #{airCabinType,jdbcType=VARCHAR}, #{airPriceFlag,jdbcType=BIT}, 
      #{airUnitPrice,jdbcType=NUMERIC}, #{flightNo,jdbcType=VARCHAR}, #{planeType,jdbcType=VARCHAR}, 
      #{airlineName,jdbcType=VARCHAR}, #{serviceClass,jdbcType=INTEGER}, #{seatMsg,jdbcType=VARCHAR}, 
      #{cabin,jdbcType=VARCHAR}, #{startingCity,jdbcType=VARCHAR}, #{startingCode,jdbcType=VARCHAR}, 
      #{destinationCity,jdbcType=VARCHAR}, #{destinationCode,jdbcType=VARCHAR}, #{startingAirport,jdbcType=VARCHAR}, 
      #{destinationAirport,jdbcType=VARCHAR}, #{startingTerminal,jdbcType=VARCHAR}, #{destinationTerminal,jdbcType=VARCHAR}, 
      #{isMiddleStop,jdbcType=BIT}, #{departureTimestamp,jdbcType=TIMESTAMP}, #{arrivedTimestamp,jdbcType=TIMESTAMP}, 
      #{parPrice,jdbcType=NUMERIC}, #{fuelTax,jdbcType=NUMERIC}, #{airportTax,jdbcType=NUMERIC}, 
      #{salePrice,jdbcType=NUMERIC}, #{passengerInfoList,jdbcType=VARCHAR}, #{errMsg,jdbcType=VARCHAR}, 
      #{errCode,jdbcType=INTEGER}, #{costCenterId,jdbcType=VARCHAR}, #{costCenterType,jdbcType=INTEGER}, 
      #{exceedBuyType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.AirInterceptRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into air_intercept_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="employeeId != null">
        employee_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="contactName != null">
        contact_name,
      </if>
      <if test="contactPhone != null">
        contact_phone,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="totalPrice != null">
        total_price,
      </if>
      <if test="airRule != null">
        air_rule,
      </if>
      <if test="airRuleFlag != null">
        air_rule_flag,
      </if>
      <if test="airVerifyFlag != null">
        air_verify_flag,
      </if>
      <if test="exceedBuyFlag != null">
        exceed_buy_flag,
      </if>
      <if test="airCabinFlag != null">
        air_cabin_flag,
      </if>
      <if test="airCabinType != null">
        air_cabin_type,
      </if>
      <if test="airPriceFlag != null">
        air_price_flag,
      </if>
      <if test="airUnitPrice != null">
        air_unit_price,
      </if>
      <if test="flightNo != null">
        flight_no,
      </if>
      <if test="planeType != null">
        plane_type,
      </if>
      <if test="airlineName != null">
        airline_name,
      </if>
      <if test="serviceClass != null">
        service_class,
      </if>
      <if test="seatMsg != null">
        seat_msg,
      </if>
      <if test="cabin != null">
        cabin,
      </if>
      <if test="startingCity != null">
        starting_city,
      </if>
      <if test="startingCode != null">
        starting_code,
      </if>
      <if test="destinationCity != null">
        destination_city,
      </if>
      <if test="destinationCode != null">
        destination_code,
      </if>
      <if test="startingAirport != null">
        starting_airport,
      </if>
      <if test="destinationAirport != null">
        destination_airport,
      </if>
      <if test="startingTerminal != null">
        starting_terminal,
      </if>
      <if test="destinationTerminal != null">
        destination_terminal,
      </if>
      <if test="isMiddleStop != null">
        is_middle_stop,
      </if>
      <if test="departureTimestamp != null">
        departure_timestamp,
      </if>
      <if test="arrivedTimestamp != null">
        arrived_timestamp,
      </if>
      <if test="parPrice != null">
        par_price,
      </if>
      <if test="fuelTax != null">
        fuel_tax,
      </if>
      <if test="airportTax != null">
        airport_tax,
      </if>
      <if test="salePrice != null">
        sale_price,
      </if>
      <if test="passengerInfoList != null">
        passenger_info_list,
      </if>
      <if test="errMsg != null">
        err_msg,
      </if>
      <if test="errCode != null">
        err_code,
      </if>
      <if test="costCenterId != null">
        cost_center_id,
      </if>
      <if test="costCenterType != null">
        cost_center_type,
      </if>
      <if test="exceedBuyType != null">
        exceed_buy_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="employeeId != null">
        #{employeeId,jdbcType=CHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contactName != null">
        #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=INTEGER},
      </if>
      <if test="totalPrice != null">
        #{totalPrice,jdbcType=NUMERIC},
      </if>
      <if test="airRule != null">
        #{airRule,jdbcType=INTEGER},
      </if>
      <if test="airRuleFlag != null">
        #{airRuleFlag,jdbcType=BIT},
      </if>
      <if test="airVerifyFlag != null">
        #{airVerifyFlag,jdbcType=BIT},
      </if>
      <if test="exceedBuyFlag != null">
        #{exceedBuyFlag,jdbcType=BIT},
      </if>
      <if test="airCabinFlag != null">
        #{airCabinFlag,jdbcType=BIT},
      </if>
      <if test="airCabinType != null">
        #{airCabinType,jdbcType=VARCHAR},
      </if>
      <if test="airPriceFlag != null">
        #{airPriceFlag,jdbcType=BIT},
      </if>
      <if test="airUnitPrice != null">
        #{airUnitPrice,jdbcType=NUMERIC},
      </if>
      <if test="flightNo != null">
        #{flightNo,jdbcType=VARCHAR},
      </if>
      <if test="planeType != null">
        #{planeType,jdbcType=VARCHAR},
      </if>
      <if test="airlineName != null">
        #{airlineName,jdbcType=VARCHAR},
      </if>
      <if test="serviceClass != null">
        #{serviceClass,jdbcType=INTEGER},
      </if>
      <if test="seatMsg != null">
        #{seatMsg,jdbcType=VARCHAR},
      </if>
      <if test="cabin != null">
        #{cabin,jdbcType=VARCHAR},
      </if>
      <if test="startingCity != null">
        #{startingCity,jdbcType=VARCHAR},
      </if>
      <if test="startingCode != null">
        #{startingCode,jdbcType=VARCHAR},
      </if>
      <if test="destinationCity != null">
        #{destinationCity,jdbcType=VARCHAR},
      </if>
      <if test="destinationCode != null">
        #{destinationCode,jdbcType=VARCHAR},
      </if>
      <if test="startingAirport != null">
        #{startingAirport,jdbcType=VARCHAR},
      </if>
      <if test="destinationAirport != null">
        #{destinationAirport,jdbcType=VARCHAR},
      </if>
      <if test="startingTerminal != null">
        #{startingTerminal,jdbcType=VARCHAR},
      </if>
      <if test="destinationTerminal != null">
        #{destinationTerminal,jdbcType=VARCHAR},
      </if>
      <if test="isMiddleStop != null">
        #{isMiddleStop,jdbcType=BIT},
      </if>
      <if test="departureTimestamp != null">
        #{departureTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="arrivedTimestamp != null">
        #{arrivedTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="parPrice != null">
        #{parPrice,jdbcType=NUMERIC},
      </if>
      <if test="fuelTax != null">
        #{fuelTax,jdbcType=NUMERIC},
      </if>
      <if test="airportTax != null">
        #{airportTax,jdbcType=NUMERIC},
      </if>
      <if test="salePrice != null">
        #{salePrice,jdbcType=NUMERIC},
      </if>
      <if test="passengerInfoList != null">
        #{passengerInfoList,jdbcType=VARCHAR},
      </if>
      <if test="errMsg != null">
        #{errMsg,jdbcType=VARCHAR},
      </if>
      <if test="errCode != null">
        #{errCode,jdbcType=INTEGER},
      </if>
      <if test="costCenterId != null">
        #{costCenterId,jdbcType=VARCHAR},
      </if>
      <if test="costCenterType != null">
        #{costCenterType,jdbcType=INTEGER},
      </if>
      <if test="exceedBuyType != null">
        #{exceedBuyType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.AirInterceptRecordExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from air_intercept_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update air_intercept_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.employeeId != null">
        employee_id = #{record.employeeId,jdbcType=CHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=CHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.contactName != null">
        contact_name = #{record.contactName,jdbcType=VARCHAR},
      </if>
      <if test="record.contactPhone != null">
        contact_phone = #{record.contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.channel != null">
        channel = #{record.channel,jdbcType=INTEGER},
      </if>
      <if test="record.totalPrice != null">
        total_price = #{record.totalPrice,jdbcType=NUMERIC},
      </if>
      <if test="record.airRule != null">
        air_rule = #{record.airRule,jdbcType=INTEGER},
      </if>
      <if test="record.airRuleFlag != null">
        air_rule_flag = #{record.airRuleFlag,jdbcType=BIT},
      </if>
      <if test="record.airVerifyFlag != null">
        air_verify_flag = #{record.airVerifyFlag,jdbcType=BIT},
      </if>
      <if test="record.exceedBuyFlag != null">
        exceed_buy_flag = #{record.exceedBuyFlag,jdbcType=BIT},
      </if>
      <if test="record.airCabinFlag != null">
        air_cabin_flag = #{record.airCabinFlag,jdbcType=BIT},
      </if>
      <if test="record.airCabinType != null">
        air_cabin_type = #{record.airCabinType,jdbcType=VARCHAR},
      </if>
      <if test="record.airPriceFlag != null">
        air_price_flag = #{record.airPriceFlag,jdbcType=BIT},
      </if>
      <if test="record.airUnitPrice != null">
        air_unit_price = #{record.airUnitPrice,jdbcType=NUMERIC},
      </if>
      <if test="record.flightNo != null">
        flight_no = #{record.flightNo,jdbcType=VARCHAR},
      </if>
      <if test="record.planeType != null">
        plane_type = #{record.planeType,jdbcType=VARCHAR},
      </if>
      <if test="record.airlineName != null">
        airline_name = #{record.airlineName,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceClass != null">
        service_class = #{record.serviceClass,jdbcType=INTEGER},
      </if>
      <if test="record.seatMsg != null">
        seat_msg = #{record.seatMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.cabin != null">
        cabin = #{record.cabin,jdbcType=VARCHAR},
      </if>
      <if test="record.startingCity != null">
        starting_city = #{record.startingCity,jdbcType=VARCHAR},
      </if>
      <if test="record.startingCode != null">
        starting_code = #{record.startingCode,jdbcType=VARCHAR},
      </if>
      <if test="record.destinationCity != null">
        destination_city = #{record.destinationCity,jdbcType=VARCHAR},
      </if>
      <if test="record.destinationCode != null">
        destination_code = #{record.destinationCode,jdbcType=VARCHAR},
      </if>
      <if test="record.startingAirport != null">
        starting_airport = #{record.startingAirport,jdbcType=VARCHAR},
      </if>
      <if test="record.destinationAirport != null">
        destination_airport = #{record.destinationAirport,jdbcType=VARCHAR},
      </if>
      <if test="record.startingTerminal != null">
        starting_terminal = #{record.startingTerminal,jdbcType=VARCHAR},
      </if>
      <if test="record.destinationTerminal != null">
        destination_terminal = #{record.destinationTerminal,jdbcType=VARCHAR},
      </if>
      <if test="record.isMiddleStop != null">
        is_middle_stop = #{record.isMiddleStop,jdbcType=BIT},
      </if>
      <if test="record.departureTimestamp != null">
        departure_timestamp = #{record.departureTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="record.arrivedTimestamp != null">
        arrived_timestamp = #{record.arrivedTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="record.parPrice != null">
        par_price = #{record.parPrice,jdbcType=NUMERIC},
      </if>
      <if test="record.fuelTax != null">
        fuel_tax = #{record.fuelTax,jdbcType=NUMERIC},
      </if>
      <if test="record.airportTax != null">
        airport_tax = #{record.airportTax,jdbcType=NUMERIC},
      </if>
      <if test="record.salePrice != null">
        sale_price = #{record.salePrice,jdbcType=NUMERIC},
      </if>
      <if test="record.passengerInfoList != null">
        passenger_info_list = #{record.passengerInfoList,jdbcType=VARCHAR},
      </if>
      <if test="record.errMsg != null">
        err_msg = #{record.errMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.errCode != null">
        err_code = #{record.errCode,jdbcType=INTEGER},
      </if>
      <if test="record.costCenterId != null">
        cost_center_id = #{record.costCenterId,jdbcType=VARCHAR},
      </if>
      <if test="record.costCenterType != null">
        cost_center_type = #{record.costCenterType,jdbcType=INTEGER},
      </if>
      <if test="record.exceedBuyType != null">
        exceed_buy_type = #{record.exceedBuyType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update air_intercept_record
    set id = #{record.id,jdbcType=CHAR},
      employee_id = #{record.employeeId,jdbcType=CHAR},
      company_id = #{record.companyId,jdbcType=CHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      contact_name = #{record.contactName,jdbcType=VARCHAR},
      contact_phone = #{record.contactPhone,jdbcType=VARCHAR},
      channel = #{record.channel,jdbcType=INTEGER},
      total_price = #{record.totalPrice,jdbcType=NUMERIC},
      air_rule = #{record.airRule,jdbcType=INTEGER},
      air_rule_flag = #{record.airRuleFlag,jdbcType=BIT},
      air_verify_flag = #{record.airVerifyFlag,jdbcType=BIT},
      exceed_buy_flag = #{record.exceedBuyFlag,jdbcType=BIT},
      air_cabin_flag = #{record.airCabinFlag,jdbcType=BIT},
      air_cabin_type = #{record.airCabinType,jdbcType=VARCHAR},
      air_price_flag = #{record.airPriceFlag,jdbcType=BIT},
      air_unit_price = #{record.airUnitPrice,jdbcType=NUMERIC},
      flight_no = #{record.flightNo,jdbcType=VARCHAR},
      plane_type = #{record.planeType,jdbcType=VARCHAR},
      airline_name = #{record.airlineName,jdbcType=VARCHAR},
      service_class = #{record.serviceClass,jdbcType=INTEGER},
      seat_msg = #{record.seatMsg,jdbcType=VARCHAR},
      cabin = #{record.cabin,jdbcType=VARCHAR},
      starting_city = #{record.startingCity,jdbcType=VARCHAR},
      starting_code = #{record.startingCode,jdbcType=VARCHAR},
      destination_city = #{record.destinationCity,jdbcType=VARCHAR},
      destination_code = #{record.destinationCode,jdbcType=VARCHAR},
      starting_airport = #{record.startingAirport,jdbcType=VARCHAR},
      destination_airport = #{record.destinationAirport,jdbcType=VARCHAR},
      starting_terminal = #{record.startingTerminal,jdbcType=VARCHAR},
      destination_terminal = #{record.destinationTerminal,jdbcType=VARCHAR},
      is_middle_stop = #{record.isMiddleStop,jdbcType=BIT},
      departure_timestamp = #{record.departureTimestamp,jdbcType=TIMESTAMP},
      arrived_timestamp = #{record.arrivedTimestamp,jdbcType=TIMESTAMP},
      par_price = #{record.parPrice,jdbcType=NUMERIC},
      fuel_tax = #{record.fuelTax,jdbcType=NUMERIC},
      airport_tax = #{record.airportTax,jdbcType=NUMERIC},
      sale_price = #{record.salePrice,jdbcType=NUMERIC},
      passenger_info_list = #{record.passengerInfoList,jdbcType=VARCHAR},
      err_msg = #{record.errMsg,jdbcType=VARCHAR},
      err_code = #{record.errCode,jdbcType=INTEGER},
      cost_center_id = #{record.costCenterId,jdbcType=VARCHAR},
      cost_center_type = #{record.costCenterType,jdbcType=INTEGER},
      exceed_buy_type = #{record.exceedBuyType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.AirInterceptRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update air_intercept_record
    <set>
      <if test="employeeId != null">
        employee_id = #{employeeId,jdbcType=CHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contactName != null">
        contact_name = #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        contact_phone = #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=INTEGER},
      </if>
      <if test="totalPrice != null">
        total_price = #{totalPrice,jdbcType=NUMERIC},
      </if>
      <if test="airRule != null">
        air_rule = #{airRule,jdbcType=INTEGER},
      </if>
      <if test="airRuleFlag != null">
        air_rule_flag = #{airRuleFlag,jdbcType=BIT},
      </if>
      <if test="airVerifyFlag != null">
        air_verify_flag = #{airVerifyFlag,jdbcType=BIT},
      </if>
      <if test="exceedBuyFlag != null">
        exceed_buy_flag = #{exceedBuyFlag,jdbcType=BIT},
      </if>
      <if test="airCabinFlag != null">
        air_cabin_flag = #{airCabinFlag,jdbcType=BIT},
      </if>
      <if test="airCabinType != null">
        air_cabin_type = #{airCabinType,jdbcType=VARCHAR},
      </if>
      <if test="airPriceFlag != null">
        air_price_flag = #{airPriceFlag,jdbcType=BIT},
      </if>
      <if test="airUnitPrice != null">
        air_unit_price = #{airUnitPrice,jdbcType=NUMERIC},
      </if>
      <if test="flightNo != null">
        flight_no = #{flightNo,jdbcType=VARCHAR},
      </if>
      <if test="planeType != null">
        plane_type = #{planeType,jdbcType=VARCHAR},
      </if>
      <if test="airlineName != null">
        airline_name = #{airlineName,jdbcType=VARCHAR},
      </if>
      <if test="serviceClass != null">
        service_class = #{serviceClass,jdbcType=INTEGER},
      </if>
      <if test="seatMsg != null">
        seat_msg = #{seatMsg,jdbcType=VARCHAR},
      </if>
      <if test="cabin != null">
        cabin = #{cabin,jdbcType=VARCHAR},
      </if>
      <if test="startingCity != null">
        starting_city = #{startingCity,jdbcType=VARCHAR},
      </if>
      <if test="startingCode != null">
        starting_code = #{startingCode,jdbcType=VARCHAR},
      </if>
      <if test="destinationCity != null">
        destination_city = #{destinationCity,jdbcType=VARCHAR},
      </if>
      <if test="destinationCode != null">
        destination_code = #{destinationCode,jdbcType=VARCHAR},
      </if>
      <if test="startingAirport != null">
        starting_airport = #{startingAirport,jdbcType=VARCHAR},
      </if>
      <if test="destinationAirport != null">
        destination_airport = #{destinationAirport,jdbcType=VARCHAR},
      </if>
      <if test="startingTerminal != null">
        starting_terminal = #{startingTerminal,jdbcType=VARCHAR},
      </if>
      <if test="destinationTerminal != null">
        destination_terminal = #{destinationTerminal,jdbcType=VARCHAR},
      </if>
      <if test="isMiddleStop != null">
        is_middle_stop = #{isMiddleStop,jdbcType=BIT},
      </if>
      <if test="departureTimestamp != null">
        departure_timestamp = #{departureTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="arrivedTimestamp != null">
        arrived_timestamp = #{arrivedTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="parPrice != null">
        par_price = #{parPrice,jdbcType=NUMERIC},
      </if>
      <if test="fuelTax != null">
        fuel_tax = #{fuelTax,jdbcType=NUMERIC},
      </if>
      <if test="airportTax != null">
        airport_tax = #{airportTax,jdbcType=NUMERIC},
      </if>
      <if test="salePrice != null">
        sale_price = #{salePrice,jdbcType=NUMERIC},
      </if>
      <if test="passengerInfoList != null">
        passenger_info_list = #{passengerInfoList,jdbcType=VARCHAR},
      </if>
      <if test="errMsg != null">
        err_msg = #{errMsg,jdbcType=VARCHAR},
      </if>
      <if test="errCode != null">
        err_code = #{errCode,jdbcType=INTEGER},
      </if>
      <if test="costCenterId != null">
        cost_center_id = #{costCenterId,jdbcType=VARCHAR},
      </if>
      <if test="costCenterType != null">
        cost_center_type = #{costCenterType,jdbcType=INTEGER},
      </if>
      <if test="exceedBuyType != null">
        exceed_buy_type = #{exceedBuyType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.fenbeitong.AirInterceptRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update air_intercept_record
    set employee_id = #{employeeId,jdbcType=CHAR},
      company_id = #{companyId,jdbcType=CHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      contact_name = #{contactName,jdbcType=VARCHAR},
      contact_phone = #{contactPhone,jdbcType=VARCHAR},
      channel = #{channel,jdbcType=INTEGER},
      total_price = #{totalPrice,jdbcType=NUMERIC},
      air_rule = #{airRule,jdbcType=INTEGER},
      air_rule_flag = #{airRuleFlag,jdbcType=BIT},
      air_verify_flag = #{airVerifyFlag,jdbcType=BIT},
      exceed_buy_flag = #{exceedBuyFlag,jdbcType=BIT},
      air_cabin_flag = #{airCabinFlag,jdbcType=BIT},
      air_cabin_type = #{airCabinType,jdbcType=VARCHAR},
      air_price_flag = #{airPriceFlag,jdbcType=BIT},
      air_unit_price = #{airUnitPrice,jdbcType=NUMERIC},
      flight_no = #{flightNo,jdbcType=VARCHAR},
      plane_type = #{planeType,jdbcType=VARCHAR},
      airline_name = #{airlineName,jdbcType=VARCHAR},
      service_class = #{serviceClass,jdbcType=INTEGER},
      seat_msg = #{seatMsg,jdbcType=VARCHAR},
      cabin = #{cabin,jdbcType=VARCHAR},
      starting_city = #{startingCity,jdbcType=VARCHAR},
      starting_code = #{startingCode,jdbcType=VARCHAR},
      destination_city = #{destinationCity,jdbcType=VARCHAR},
      destination_code = #{destinationCode,jdbcType=VARCHAR},
      starting_airport = #{startingAirport,jdbcType=VARCHAR},
      destination_airport = #{destinationAirport,jdbcType=VARCHAR},
      starting_terminal = #{startingTerminal,jdbcType=VARCHAR},
      destination_terminal = #{destinationTerminal,jdbcType=VARCHAR},
      is_middle_stop = #{isMiddleStop,jdbcType=BIT},
      departure_timestamp = #{departureTimestamp,jdbcType=TIMESTAMP},
      arrived_timestamp = #{arrivedTimestamp,jdbcType=TIMESTAMP},
      par_price = #{parPrice,jdbcType=NUMERIC},
      fuel_tax = #{fuelTax,jdbcType=NUMERIC},
      airport_tax = #{airportTax,jdbcType=NUMERIC},
      sale_price = #{salePrice,jdbcType=NUMERIC},
      passenger_info_list = #{passengerInfoList,jdbcType=VARCHAR},
      err_msg = #{errMsg,jdbcType=VARCHAR},
      err_code = #{errCode,jdbcType=INTEGER},
      cost_center_id = #{costCenterId,jdbcType=VARCHAR},
      cost_center_type = #{costCenterType,jdbcType=INTEGER},
      exceed_buy_type = #{exceedBuyType,jdbcType=INTEGER}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>