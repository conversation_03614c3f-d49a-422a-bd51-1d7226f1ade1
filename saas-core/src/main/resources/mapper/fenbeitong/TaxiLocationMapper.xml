<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.TaxiLocationMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.TaxiLocation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="company_id" jdbcType="CHAR" property="companyId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="lat" jdbcType="DOUBLE" property="lat" />
    <result column="lng" jdbcType="DOUBLE" property="lng" />
    <result column="radius" jdbcType="DOUBLE" property="radius" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="comment_name" jdbcType="VARCHAR" property="commentName" />
    <result column="location_type" jdbcType="INTEGER" property="locationType" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, name, lat, lng, radius, modify_time, comment_name, location_type,
    city_name, city_code
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiLocationExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from taxi_location
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from taxi_location
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="listByPrimaryKey" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from taxi_location
    where id in (<foreach collection="ids" item="x" separator=",">#{x,jdbcType=INTEGER}</foreach>)
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from taxi_location
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiLocationExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from taxi_location
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiLocation" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into taxi_location (company_id, name, lat,
    lng, radius, modify_time,
    comment_name, location_type, city_name,
    city_code)
    values (#{companyId,jdbcType=CHAR}, #{name,jdbcType=VARCHAR}, #{lat,jdbcType=DOUBLE},
    #{lng,jdbcType=DOUBLE}, #{radius,jdbcType=DOUBLE}, #{modifyTime,jdbcType=TIMESTAMP},
    #{commentName,jdbcType=VARCHAR}, #{locationType,jdbcType=INTEGER}, #{cityName,jdbcType=VARCHAR},
    #{cityCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiLocation" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into taxi_location
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="lat != null">
        lat,
      </if>
      <if test="lng != null">
        lng,
      </if>
      <if test="radius != null">
        radius,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="commentName != null">
        comment_name,
      </if>
      <if test="locationType != null">
        location_type,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=CHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="lat != null">
        #{lat,jdbcType=DOUBLE},
      </if>
      <if test="lng != null">
        #{lng,jdbcType=DOUBLE},
      </if>
      <if test="radius != null">
        #{radius,jdbcType=DOUBLE},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="commentName != null">
        #{commentName,jdbcType=VARCHAR},
      </if>
      <if test="locationType != null">
        #{locationType,jdbcType=INTEGER},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiLocationExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from taxi_location
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update taxi_location
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=CHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.lat != null">
        lat = #{record.lat,jdbcType=DOUBLE},
      </if>
      <if test="record.lng != null">
        lng = #{record.lng,jdbcType=DOUBLE},
      </if>
      <if test="record.radius != null">
        radius = #{record.radius,jdbcType=DOUBLE},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.commentName != null">
        comment_name = #{record.commentName,jdbcType=VARCHAR},
      </if>
      <if test="record.locationType != null">
        location_type = #{record.locationType,jdbcType=INTEGER},
      </if>
      <if test="record.cityName != null">
        city_name = #{record.cityName,jdbcType=VARCHAR},
      </if>
      <if test="record.cityCode != null">
        city_code = #{record.cityCode,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update taxi_location
    set id = #{record.id,jdbcType=INTEGER},
    company_id = #{record.companyId,jdbcType=CHAR},
    name = #{record.name,jdbcType=VARCHAR},
    lat = #{record.lat,jdbcType=DOUBLE},
    lng = #{record.lng,jdbcType=DOUBLE},
    radius = #{record.radius,jdbcType=DOUBLE},
    modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
    comment_name = #{record.commentName,jdbcType=VARCHAR},
    location_type = #{record.locationType,jdbcType=INTEGER},
    city_name = #{record.cityName,jdbcType=VARCHAR},
    city_code = #{record.cityCode,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiLocation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update taxi_location
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=CHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="lat != null">
        lat = #{lat,jdbcType=DOUBLE},
      </if>
      <if test="lng != null">
        lng = #{lng,jdbcType=DOUBLE},
      </if>
      <if test="radius != null">
        radius = #{radius,jdbcType=DOUBLE},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="commentName != null">
        comment_name = #{commentName,jdbcType=VARCHAR},
      </if>
      <if test="locationType != null">
        location_type = #{locationType,jdbcType=INTEGER},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        city_code = #{cityCode,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiLocation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update taxi_location
    set company_id = #{companyId,jdbcType=CHAR},
    name = #{name,jdbcType=VARCHAR},
    lat = #{lat,jdbcType=DOUBLE},
    lng = #{lng,jdbcType=DOUBLE},
    radius = #{radius,jdbcType=DOUBLE},
    modify_time = #{modifyTime,jdbcType=TIMESTAMP},
    comment_name = #{commentName,jdbcType=VARCHAR},
    location_type = #{locationType,jdbcType=INTEGER},
    city_name = #{cityName,jdbcType=VARCHAR},
    city_code = #{cityCode,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>