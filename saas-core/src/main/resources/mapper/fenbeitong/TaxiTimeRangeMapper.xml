<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.TaxiTimeRangeMapper">
    <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.TaxiTimeRange">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed Sep 06 18:12:27 CST 2017.
        -->
        <constructor>
            <idArg column="id" javaType="java.lang.Integer" jdbcType="INTEGER"/>
            <arg column="rule_id" javaType="java.lang.Integer" jdbcType="INTEGER"/>
            <arg column="day_type" javaType="java.lang.Integer" jdbcType="INTEGER"/>
            <arg column="begin_time" javaType="java.util.Date" jdbcType="TIME"/>
            <arg column="end_time" javaType="java.util.Date" jdbcType="TIME"/>
            <arg column="is_overnight" javaType="java.lang.Boolean" jdbcType="BIT"/>
            <arg column="batch_id" jdbcType="BIGINT" javaType="java.lang.Long" />
            <arg column="dingtalk_flag" jdbcType="BIT" javaType="java.lang.Boolean" />
        </constructor>
    </resultMap>
    <sql id="Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed Sep 06 18:12:27 CST 2017.
        -->
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed Sep 06 18:12:27 CST 2017.
        -->
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed Sep 06 18:12:27 CST 2017.
        -->
        id, rule_id, day_type, begin_time, end_time, is_overnight, batch_id, dingtalk_flag
    </sql>
    <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiTimeRangeExample"
            resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed Sep 06 18:12:27 CST 2017.
        -->
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from taxi_time_range
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed Sep 06 18:12:27 CST 2017.
        -->
        select
        <include refid="Base_Column_List"/>
        from taxi_time_range
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed Sep 06 18:12:27 CST 2017.
        -->
        delete from taxi_time_range
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiTimeRangeExample">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed Sep 06 18:12:27 CST 2017.
        -->
        delete from taxi_time_range
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiTimeRange">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed Sep 06 18:12:27 CST 2017.
        -->
        insert into taxi_time_range ( rule_id, day_type,
        begin_time, end_time, is_overnight,batch_id, dingtalk_flag
        )
        values (#{ruleId,jdbcType=INTEGER}, #{dayType,jdbcType=INTEGER},
        #{beginTime,jdbcType=TIME}, #{endTime,jdbcType=TIME}, #{isOvernight,jdbcType=BIT},
        #{batchId,jdbcType=BIGINT}, #{dingtalkFlag,jdbcType=BIT}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiTimeRange">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed Sep 06 18:12:27 CST 2017.
        -->
        insert into taxi_time_range
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="ruleId != null">
                rule_id,
            </if>
            <if test="dayType != null">
                day_type,
            </if>
            <if test="beginTime != null">
                begin_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="isOvernight != null">
                is_overnight,
            </if>
            <if test="batchId != null">
                batch_id,
            </if>
            <if test="dingtalkFlag != null">
                dingtalk_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="ruleId != null">
                #{ruleId,jdbcType=INTEGER},
            </if>
            <if test="dayType != null">
                #{dayType,jdbcType=INTEGER},
            </if>
            <if test="beginTime != null">
                #{beginTime,jdbcType=TIME},
            </if>
            <if test="endTime != null">
                #{endTime,jdbcType=TIME},
            </if>
            <if test="isOvernight != null">
                #{isOvernight,jdbcType=BIT},
            </if>
            <if test="batchId != null">
                #{batchId,jdbcType=BIGINT},
            </if>
            <if test="dingtalkFlag != null">
                #{dingtalkFlag,jdbcType=BIT},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiTimeRangeExample"
            resultType="java.lang.Long">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed Sep 06 18:12:27 CST 2017.
        -->
        select count(*) from taxi_time_range
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed Sep 06 18:12:27 CST 2017.
        -->
        update taxi_time_range
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=INTEGER},
            </if>
            <if test="record.ruleId != null">
                rule_id = #{record.ruleId,jdbcType=INTEGER},
            </if>
            <if test="record.dayType != null">
                day_type = #{record.dayType,jdbcType=INTEGER},
            </if>
            <if test="record.beginTime != null">
                begin_time = #{record.beginTime,jdbcType=TIME},
            </if>
            <if test="record.endTime != null">
                end_time = #{record.endTime,jdbcType=TIME},
            </if>
            <if test="record.isOvernight != null">
                is_overnight = #{record.isOvernight,jdbcType=BIT},
            </if>
            <if test="record.batchId != null">
                batch_id = #{record.batchId,jdbcType=BIGINT},
            </if>
            <if test="record.dingtalkFlag != null">
                dingtalk_flag = #{record.dingtalkFlag,jdbcType=BIT},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed Sep 06 18:12:27 CST 2017.
        -->
        update taxi_time_range
        set id = #{record.id,jdbcType=INTEGER},
        rule_id = #{record.ruleId,jdbcType=INTEGER},
        day_type = #{record.dayType,jdbcType=INTEGER},
        begin_time = #{record.beginTime,jdbcType=TIME},
        end_time = #{record.endTime,jdbcType=TIME},
        is_overnight = #{record.isOvernight,jdbcType=BIT},
        batch_id = #{record.batchId,jdbcType=BIGINT},
        dingtalk_flag = #{record.dingtalkFlag,jdbcType=BIT}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiTimeRange">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed Sep 06 18:12:27 CST 2017.
        -->
        update taxi_time_range
        <set>
            <if test="ruleId != null">
                rule_id = #{ruleId,jdbcType=INTEGER},
            </if>
            <if test="dayType != null">
                day_type = #{dayType,jdbcType=INTEGER},
            </if>
            <if test="beginTime != null">
                begin_time = #{beginTime,jdbcType=TIME},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=TIME},
            </if>
            <if test="isOvernight != null">
                is_overnight = #{isOvernight,jdbcType=BIT},
            </if>
            <if test="batchId != null">
                batch_id = #{batchId,jdbcType=BIGINT},
            </if>
            <if test="dingtalkFlag != null">
                dingtalk_flag = #{dingtalkFlag,jdbcType=BIT},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TaxiTimeRange">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed Sep 06 18:12:27 CST 2017.
        -->
        update taxi_time_range
        set rule_id = #{ruleId,jdbcType=INTEGER},
        day_type = #{dayType,jdbcType=INTEGER},
        begin_time = #{beginTime,jdbcType=TIME},
        end_time = #{endTime,jdbcType=TIME},
        is_overnight = #{isOvernight,jdbcType=BIT},
        batch_id = #{batchId,jdbcType=BIGINT},
        dingtalk_flag = #{dingtalkFlag,jdbcType=BIT}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="queryRuleRangeTime" resultMap="BaseResultMap">
        select * from taxi_time_range
        where rule_id = #{ruleId,jdbcType=INTEGER}
        order by day_type asc
    </select>
</mapper>