<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.TrainInterceptRecordMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.TrainInterceptRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="employee_id" jdbcType="CHAR" property="employeeId" />
    <result column="company_id" jdbcType="CHAR" property="companyId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="contact_name" jdbcType="VARCHAR" property="contactName" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="channel" jdbcType="INTEGER" property="channel" />
    <result column="total_price" jdbcType="NUMERIC" property="totalPrice" />
    <result column="train_rule" jdbcType="INTEGER" property="trainRule" />
    <result column="train_rule_flag" jdbcType="BIT" property="trainRuleFlag" />
    <result column="train_verify_flag" jdbcType="BIT" property="trainVerifyFlag" />
    <result column="exceed_buy_flag" jdbcType="BIT" property="exceedBuyFlag" />
    <result column="train_seat_flag" jdbcType="BIT" property="trainSeatFlag" />
    <result column="common_train_seat_type" jdbcType="VARCHAR" property="commonTrainSeatType" />
    <result column="highspeed_train_seat_type" jdbcType="VARCHAR" property="highspeedTrainSeatType" />
    <result column="train_code" jdbcType="VARCHAR" property="trainCode" />
    <result column="train_no" jdbcType="VARCHAR" property="trainNo" />
    <result column="from_station_code" jdbcType="VARCHAR" property="fromStationCode" />
    <result column="from_station_name" jdbcType="VARCHAR" property="fromStationName" />
    <result column="to_station_code" jdbcType="VARCHAR" property="toStationCode" />
    <result column="to_station_name" jdbcType="VARCHAR" property="toStationName" />
    <result column="train_start_date" jdbcType="VARCHAR" property="trainStartDate" />
    <result column="train_end_date" jdbcType="VARCHAR" property="trainEndDate" />
    <result column="start_time" jdbcType="VARCHAR" property="startTime" />
    <result column="arrive_time" jdbcType="VARCHAR" property="arriveTime" />
    <result column="run_time" jdbcType="VARCHAR" property="runTime" />
    <result column="arrive_days" jdbcType="INTEGER" property="arriveDays" />
    <result column="seat_type" jdbcType="VARCHAR" property="seatType" />
    <result column="seat_no" jdbcType="VARCHAR" property="seatNo" />
    <result column="seat_price" jdbcType="NUMERIC" property="seatPrice" />
    <result column="service_fee" jdbcType="NUMERIC" property="serviceFee" />
    <result column="passenger_info_list" jdbcType="VARCHAR" property="passengerInfoList" />
    <result column="err_msg" jdbcType="VARCHAR" property="errMsg" />
    <result column="err_code" jdbcType="INTEGER" property="errCode" />
    <result column="cost_center_id" jdbcType="VARCHAR" property="costCenterId" />
    <result column="cost_center_type" jdbcType="INTEGER" property="costCenterType" />
    <result column="exceed_buy_type" jdbcType="INTEGER" property="exceedBuyType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, employee_id, company_id, create_time, contact_name, contact_phone, channel, total_price, 
    train_rule, train_rule_flag, train_verify_flag, exceed_buy_flag, train_seat_flag, 
    common_train_seat_type, highspeed_train_seat_type, train_code, train_no, from_station_code, 
    from_station_name, to_station_code, to_station_name, train_start_date, train_end_date, 
    start_time, arrive_time, run_time, arrive_days, seat_type, seat_no, seat_price, service_fee, 
    passenger_info_list, err_msg, err_code, cost_center_id, cost_center_type, exceed_buy_type
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TrainInterceptRecordExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from train_intercept_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from train_intercept_record
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from train_intercept_record
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TrainInterceptRecordExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from train_intercept_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TrainInterceptRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into train_intercept_record (id, employee_id, company_id, 
      create_time, contact_name, contact_phone, 
      channel, total_price, train_rule, 
      train_rule_flag, train_verify_flag, exceed_buy_flag, 
      train_seat_flag, common_train_seat_type, highspeed_train_seat_type, 
      train_code, train_no, from_station_code, 
      from_station_name, to_station_code, to_station_name, 
      train_start_date, train_end_date, start_time, 
      arrive_time, run_time, arrive_days, 
      seat_type, seat_no, seat_price, 
      service_fee, passenger_info_list, err_msg, 
      err_code, cost_center_id, cost_center_type, 
      exceed_buy_type)
    values (#{id,jdbcType=CHAR}, #{employeeId,jdbcType=CHAR}, #{companyId,jdbcType=CHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{contactName,jdbcType=VARCHAR}, #{contactPhone,jdbcType=VARCHAR}, 
      #{channel,jdbcType=INTEGER}, #{totalPrice,jdbcType=NUMERIC}, #{trainRule,jdbcType=INTEGER}, 
      #{trainRuleFlag,jdbcType=BIT}, #{trainVerifyFlag,jdbcType=BIT}, #{exceedBuyFlag,jdbcType=BIT}, 
      #{trainSeatFlag,jdbcType=BIT}, #{commonTrainSeatType,jdbcType=VARCHAR}, #{highspeedTrainSeatType,jdbcType=VARCHAR}, 
      #{trainCode,jdbcType=VARCHAR}, #{trainNo,jdbcType=VARCHAR}, #{fromStationCode,jdbcType=VARCHAR}, 
      #{fromStationName,jdbcType=VARCHAR}, #{toStationCode,jdbcType=VARCHAR}, #{toStationName,jdbcType=VARCHAR}, 
      #{trainStartDate,jdbcType=VARCHAR}, #{trainEndDate,jdbcType=VARCHAR}, #{startTime,jdbcType=VARCHAR}, 
      #{arriveTime,jdbcType=VARCHAR}, #{runTime,jdbcType=VARCHAR}, #{arriveDays,jdbcType=INTEGER}, 
      #{seatType,jdbcType=VARCHAR}, #{seatNo,jdbcType=VARCHAR}, #{seatPrice,jdbcType=NUMERIC}, 
      #{serviceFee,jdbcType=NUMERIC}, #{passengerInfoList,jdbcType=VARCHAR}, #{errMsg,jdbcType=VARCHAR}, 
      #{errCode,jdbcType=INTEGER}, #{costCenterId,jdbcType=VARCHAR}, #{costCenterType,jdbcType=INTEGER}, 
      #{exceedBuyType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TrainInterceptRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into train_intercept_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="employeeId != null">
        employee_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="contactName != null">
        contact_name,
      </if>
      <if test="contactPhone != null">
        contact_phone,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="totalPrice != null">
        total_price,
      </if>
      <if test="trainRule != null">
        train_rule,
      </if>
      <if test="trainRuleFlag != null">
        train_rule_flag,
      </if>
      <if test="trainVerifyFlag != null">
        train_verify_flag,
      </if>
      <if test="exceedBuyFlag != null">
        exceed_buy_flag,
      </if>
      <if test="trainSeatFlag != null">
        train_seat_flag,
      </if>
      <if test="commonTrainSeatType != null">
        common_train_seat_type,
      </if>
      <if test="highspeedTrainSeatType != null">
        highspeed_train_seat_type,
      </if>
      <if test="trainCode != null">
        train_code,
      </if>
      <if test="trainNo != null">
        train_no,
      </if>
      <if test="fromStationCode != null">
        from_station_code,
      </if>
      <if test="fromStationName != null">
        from_station_name,
      </if>
      <if test="toStationCode != null">
        to_station_code,
      </if>
      <if test="toStationName != null">
        to_station_name,
      </if>
      <if test="trainStartDate != null">
        train_start_date,
      </if>
      <if test="trainEndDate != null">
        train_end_date,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="arriveTime != null">
        arrive_time,
      </if>
      <if test="runTime != null">
        run_time,
      </if>
      <if test="arriveDays != null">
        arrive_days,
      </if>
      <if test="seatType != null">
        seat_type,
      </if>
      <if test="seatNo != null">
        seat_no,
      </if>
      <if test="seatPrice != null">
        seat_price,
      </if>
      <if test="serviceFee != null">
        service_fee,
      </if>
      <if test="passengerInfoList != null">
        passenger_info_list,
      </if>
      <if test="errMsg != null">
        err_msg,
      </if>
      <if test="errCode != null">
        err_code,
      </if>
      <if test="costCenterId != null">
        cost_center_id,
      </if>
      <if test="costCenterType != null">
        cost_center_type,
      </if>
      <if test="exceedBuyType != null">
        exceed_buy_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="employeeId != null">
        #{employeeId,jdbcType=CHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contactName != null">
        #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=INTEGER},
      </if>
      <if test="totalPrice != null">
        #{totalPrice,jdbcType=NUMERIC},
      </if>
      <if test="trainRule != null">
        #{trainRule,jdbcType=INTEGER},
      </if>
      <if test="trainRuleFlag != null">
        #{trainRuleFlag,jdbcType=BIT},
      </if>
      <if test="trainVerifyFlag != null">
        #{trainVerifyFlag,jdbcType=BIT},
      </if>
      <if test="exceedBuyFlag != null">
        #{exceedBuyFlag,jdbcType=BIT},
      </if>
      <if test="trainSeatFlag != null">
        #{trainSeatFlag,jdbcType=BIT},
      </if>
      <if test="commonTrainSeatType != null">
        #{commonTrainSeatType,jdbcType=VARCHAR},
      </if>
      <if test="highspeedTrainSeatType != null">
        #{highspeedTrainSeatType,jdbcType=VARCHAR},
      </if>
      <if test="trainCode != null">
        #{trainCode,jdbcType=VARCHAR},
      </if>
      <if test="trainNo != null">
        #{trainNo,jdbcType=VARCHAR},
      </if>
      <if test="fromStationCode != null">
        #{fromStationCode,jdbcType=VARCHAR},
      </if>
      <if test="fromStationName != null">
        #{fromStationName,jdbcType=VARCHAR},
      </if>
      <if test="toStationCode != null">
        #{toStationCode,jdbcType=VARCHAR},
      </if>
      <if test="toStationName != null">
        #{toStationName,jdbcType=VARCHAR},
      </if>
      <if test="trainStartDate != null">
        #{trainStartDate,jdbcType=VARCHAR},
      </if>
      <if test="trainEndDate != null">
        #{trainEndDate,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=VARCHAR},
      </if>
      <if test="arriveTime != null">
        #{arriveTime,jdbcType=VARCHAR},
      </if>
      <if test="runTime != null">
        #{runTime,jdbcType=VARCHAR},
      </if>
      <if test="arriveDays != null">
        #{arriveDays,jdbcType=INTEGER},
      </if>
      <if test="seatType != null">
        #{seatType,jdbcType=VARCHAR},
      </if>
      <if test="seatNo != null">
        #{seatNo,jdbcType=VARCHAR},
      </if>
      <if test="seatPrice != null">
        #{seatPrice,jdbcType=NUMERIC},
      </if>
      <if test="serviceFee != null">
        #{serviceFee,jdbcType=NUMERIC},
      </if>
      <if test="passengerInfoList != null">
        #{passengerInfoList,jdbcType=VARCHAR},
      </if>
      <if test="errMsg != null">
        #{errMsg,jdbcType=VARCHAR},
      </if>
      <if test="errCode != null">
        #{errCode,jdbcType=INTEGER},
      </if>
      <if test="costCenterId != null">
        #{costCenterId,jdbcType=VARCHAR},
      </if>
      <if test="costCenterType != null">
        #{costCenterType,jdbcType=INTEGER},
      </if>
      <if test="exceedBuyType != null">
        #{exceedBuyType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TrainInterceptRecordExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from train_intercept_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update train_intercept_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.employeeId != null">
        employee_id = #{record.employeeId,jdbcType=CHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=CHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.contactName != null">
        contact_name = #{record.contactName,jdbcType=VARCHAR},
      </if>
      <if test="record.contactPhone != null">
        contact_phone = #{record.contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.channel != null">
        channel = #{record.channel,jdbcType=INTEGER},
      </if>
      <if test="record.totalPrice != null">
        total_price = #{record.totalPrice,jdbcType=NUMERIC},
      </if>
      <if test="record.trainRule != null">
        train_rule = #{record.trainRule,jdbcType=INTEGER},
      </if>
      <if test="record.trainRuleFlag != null">
        train_rule_flag = #{record.trainRuleFlag,jdbcType=BIT},
      </if>
      <if test="record.trainVerifyFlag != null">
        train_verify_flag = #{record.trainVerifyFlag,jdbcType=BIT},
      </if>
      <if test="record.exceedBuyFlag != null">
        exceed_buy_flag = #{record.exceedBuyFlag,jdbcType=BIT},
      </if>
      <if test="record.trainSeatFlag != null">
        train_seat_flag = #{record.trainSeatFlag,jdbcType=BIT},
      </if>
      <if test="record.commonTrainSeatType != null">
        common_train_seat_type = #{record.commonTrainSeatType,jdbcType=VARCHAR},
      </if>
      <if test="record.highspeedTrainSeatType != null">
        highspeed_train_seat_type = #{record.highspeedTrainSeatType,jdbcType=VARCHAR},
      </if>
      <if test="record.trainCode != null">
        train_code = #{record.trainCode,jdbcType=VARCHAR},
      </if>
      <if test="record.trainNo != null">
        train_no = #{record.trainNo,jdbcType=VARCHAR},
      </if>
      <if test="record.fromStationCode != null">
        from_station_code = #{record.fromStationCode,jdbcType=VARCHAR},
      </if>
      <if test="record.fromStationName != null">
        from_station_name = #{record.fromStationName,jdbcType=VARCHAR},
      </if>
      <if test="record.toStationCode != null">
        to_station_code = #{record.toStationCode,jdbcType=VARCHAR},
      </if>
      <if test="record.toStationName != null">
        to_station_name = #{record.toStationName,jdbcType=VARCHAR},
      </if>
      <if test="record.trainStartDate != null">
        train_start_date = #{record.trainStartDate,jdbcType=VARCHAR},
      </if>
      <if test="record.trainEndDate != null">
        train_end_date = #{record.trainEndDate,jdbcType=VARCHAR},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=VARCHAR},
      </if>
      <if test="record.arriveTime != null">
        arrive_time = #{record.arriveTime,jdbcType=VARCHAR},
      </if>
      <if test="record.runTime != null">
        run_time = #{record.runTime,jdbcType=VARCHAR},
      </if>
      <if test="record.arriveDays != null">
        arrive_days = #{record.arriveDays,jdbcType=INTEGER},
      </if>
      <if test="record.seatType != null">
        seat_type = #{record.seatType,jdbcType=VARCHAR},
      </if>
      <if test="record.seatNo != null">
        seat_no = #{record.seatNo,jdbcType=VARCHAR},
      </if>
      <if test="record.seatPrice != null">
        seat_price = #{record.seatPrice,jdbcType=NUMERIC},
      </if>
      <if test="record.serviceFee != null">
        service_fee = #{record.serviceFee,jdbcType=NUMERIC},
      </if>
      <if test="record.passengerInfoList != null">
        passenger_info_list = #{record.passengerInfoList,jdbcType=VARCHAR},
      </if>
      <if test="record.errMsg != null">
        err_msg = #{record.errMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.errCode != null">
        err_code = #{record.errCode,jdbcType=INTEGER},
      </if>
      <if test="record.costCenterId != null">
        cost_center_id = #{record.costCenterId,jdbcType=VARCHAR},
      </if>
      <if test="record.costCenterType != null">
        cost_center_type = #{record.costCenterType,jdbcType=INTEGER},
      </if>
      <if test="record.exceedBuyType != null">
        exceed_buy_type = #{record.exceedBuyType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update train_intercept_record
    set id = #{record.id,jdbcType=CHAR},
      employee_id = #{record.employeeId,jdbcType=CHAR},
      company_id = #{record.companyId,jdbcType=CHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      contact_name = #{record.contactName,jdbcType=VARCHAR},
      contact_phone = #{record.contactPhone,jdbcType=VARCHAR},
      channel = #{record.channel,jdbcType=INTEGER},
      total_price = #{record.totalPrice,jdbcType=NUMERIC},
      train_rule = #{record.trainRule,jdbcType=INTEGER},
      train_rule_flag = #{record.trainRuleFlag,jdbcType=BIT},
      train_verify_flag = #{record.trainVerifyFlag,jdbcType=BIT},
      exceed_buy_flag = #{record.exceedBuyFlag,jdbcType=BIT},
      train_seat_flag = #{record.trainSeatFlag,jdbcType=BIT},
      common_train_seat_type = #{record.commonTrainSeatType,jdbcType=VARCHAR},
      highspeed_train_seat_type = #{record.highspeedTrainSeatType,jdbcType=VARCHAR},
      train_code = #{record.trainCode,jdbcType=VARCHAR},
      train_no = #{record.trainNo,jdbcType=VARCHAR},
      from_station_code = #{record.fromStationCode,jdbcType=VARCHAR},
      from_station_name = #{record.fromStationName,jdbcType=VARCHAR},
      to_station_code = #{record.toStationCode,jdbcType=VARCHAR},
      to_station_name = #{record.toStationName,jdbcType=VARCHAR},
      train_start_date = #{record.trainStartDate,jdbcType=VARCHAR},
      train_end_date = #{record.trainEndDate,jdbcType=VARCHAR},
      start_time = #{record.startTime,jdbcType=VARCHAR},
      arrive_time = #{record.arriveTime,jdbcType=VARCHAR},
      run_time = #{record.runTime,jdbcType=VARCHAR},
      arrive_days = #{record.arriveDays,jdbcType=INTEGER},
      seat_type = #{record.seatType,jdbcType=VARCHAR},
      seat_no = #{record.seatNo,jdbcType=VARCHAR},
      seat_price = #{record.seatPrice,jdbcType=NUMERIC},
      service_fee = #{record.serviceFee,jdbcType=NUMERIC},
      passenger_info_list = #{record.passengerInfoList,jdbcType=VARCHAR},
      err_msg = #{record.errMsg,jdbcType=VARCHAR},
      err_code = #{record.errCode,jdbcType=INTEGER},
      cost_center_id = #{record.costCenterId,jdbcType=VARCHAR},
      cost_center_type = #{record.costCenterType,jdbcType=INTEGER},
      exceed_buy_type = #{record.exceedBuyType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TrainInterceptRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update train_intercept_record
    <set>
      <if test="employeeId != null">
        employee_id = #{employeeId,jdbcType=CHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contactName != null">
        contact_name = #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        contact_phone = #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=INTEGER},
      </if>
      <if test="totalPrice != null">
        total_price = #{totalPrice,jdbcType=NUMERIC},
      </if>
      <if test="trainRule != null">
        train_rule = #{trainRule,jdbcType=INTEGER},
      </if>
      <if test="trainRuleFlag != null">
        train_rule_flag = #{trainRuleFlag,jdbcType=BIT},
      </if>
      <if test="trainVerifyFlag != null">
        train_verify_flag = #{trainVerifyFlag,jdbcType=BIT},
      </if>
      <if test="exceedBuyFlag != null">
        exceed_buy_flag = #{exceedBuyFlag,jdbcType=BIT},
      </if>
      <if test="trainSeatFlag != null">
        train_seat_flag = #{trainSeatFlag,jdbcType=BIT},
      </if>
      <if test="commonTrainSeatType != null">
        common_train_seat_type = #{commonTrainSeatType,jdbcType=VARCHAR},
      </if>
      <if test="highspeedTrainSeatType != null">
        highspeed_train_seat_type = #{highspeedTrainSeatType,jdbcType=VARCHAR},
      </if>
      <if test="trainCode != null">
        train_code = #{trainCode,jdbcType=VARCHAR},
      </if>
      <if test="trainNo != null">
        train_no = #{trainNo,jdbcType=VARCHAR},
      </if>
      <if test="fromStationCode != null">
        from_station_code = #{fromStationCode,jdbcType=VARCHAR},
      </if>
      <if test="fromStationName != null">
        from_station_name = #{fromStationName,jdbcType=VARCHAR},
      </if>
      <if test="toStationCode != null">
        to_station_code = #{toStationCode,jdbcType=VARCHAR},
      </if>
      <if test="toStationName != null">
        to_station_name = #{toStationName,jdbcType=VARCHAR},
      </if>
      <if test="trainStartDate != null">
        train_start_date = #{trainStartDate,jdbcType=VARCHAR},
      </if>
      <if test="trainEndDate != null">
        train_end_date = #{trainEndDate,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=VARCHAR},
      </if>
      <if test="arriveTime != null">
        arrive_time = #{arriveTime,jdbcType=VARCHAR},
      </if>
      <if test="runTime != null">
        run_time = #{runTime,jdbcType=VARCHAR},
      </if>
      <if test="arriveDays != null">
        arrive_days = #{arriveDays,jdbcType=INTEGER},
      </if>
      <if test="seatType != null">
        seat_type = #{seatType,jdbcType=VARCHAR},
      </if>
      <if test="seatNo != null">
        seat_no = #{seatNo,jdbcType=VARCHAR},
      </if>
      <if test="seatPrice != null">
        seat_price = #{seatPrice,jdbcType=NUMERIC},
      </if>
      <if test="serviceFee != null">
        service_fee = #{serviceFee,jdbcType=NUMERIC},
      </if>
      <if test="passengerInfoList != null">
        passenger_info_list = #{passengerInfoList,jdbcType=VARCHAR},
      </if>
      <if test="errMsg != null">
        err_msg = #{errMsg,jdbcType=VARCHAR},
      </if>
      <if test="errCode != null">
        err_code = #{errCode,jdbcType=INTEGER},
      </if>
      <if test="costCenterId != null">
        cost_center_id = #{costCenterId,jdbcType=VARCHAR},
      </if>
      <if test="costCenterType != null">
        cost_center_type = #{costCenterType,jdbcType=INTEGER},
      </if>
      <if test="exceedBuyType != null">
        exceed_buy_type = #{exceedBuyType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TrainInterceptRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update train_intercept_record
    set employee_id = #{employeeId,jdbcType=CHAR},
      company_id = #{companyId,jdbcType=CHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      contact_name = #{contactName,jdbcType=VARCHAR},
      contact_phone = #{contactPhone,jdbcType=VARCHAR},
      channel = #{channel,jdbcType=INTEGER},
      total_price = #{totalPrice,jdbcType=NUMERIC},
      train_rule = #{trainRule,jdbcType=INTEGER},
      train_rule_flag = #{trainRuleFlag,jdbcType=BIT},
      train_verify_flag = #{trainVerifyFlag,jdbcType=BIT},
      exceed_buy_flag = #{exceedBuyFlag,jdbcType=BIT},
      train_seat_flag = #{trainSeatFlag,jdbcType=BIT},
      common_train_seat_type = #{commonTrainSeatType,jdbcType=VARCHAR},
      highspeed_train_seat_type = #{highspeedTrainSeatType,jdbcType=VARCHAR},
      train_code = #{trainCode,jdbcType=VARCHAR},
      train_no = #{trainNo,jdbcType=VARCHAR},
      from_station_code = #{fromStationCode,jdbcType=VARCHAR},
      from_station_name = #{fromStationName,jdbcType=VARCHAR},
      to_station_code = #{toStationCode,jdbcType=VARCHAR},
      to_station_name = #{toStationName,jdbcType=VARCHAR},
      train_start_date = #{trainStartDate,jdbcType=VARCHAR},
      train_end_date = #{trainEndDate,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=VARCHAR},
      arrive_time = #{arriveTime,jdbcType=VARCHAR},
      run_time = #{runTime,jdbcType=VARCHAR},
      arrive_days = #{arriveDays,jdbcType=INTEGER},
      seat_type = #{seatType,jdbcType=VARCHAR},
      seat_no = #{seatNo,jdbcType=VARCHAR},
      seat_price = #{seatPrice,jdbcType=NUMERIC},
      service_fee = #{serviceFee,jdbcType=NUMERIC},
      passenger_info_list = #{passengerInfoList,jdbcType=VARCHAR},
      err_msg = #{errMsg,jdbcType=VARCHAR},
      err_code = #{errCode,jdbcType=INTEGER},
      cost_center_id = #{costCenterId,jdbcType=VARCHAR},
      cost_center_type = #{costCenterType,jdbcType=INTEGER},
      exceed_buy_type = #{exceedBuyType,jdbcType=INTEGER}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>