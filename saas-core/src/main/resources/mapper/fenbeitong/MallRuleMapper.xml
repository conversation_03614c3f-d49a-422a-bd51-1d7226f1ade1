<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.MallRuleMapper">
    <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.MallRule">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Tue May 23 22:18:08 CST 2017.
        -->
        <constructor>
            <idArg column="id" javaType="java.lang.String" jdbcType="CHAR"/>
            <arg column="company_id" javaType="java.lang.String" jdbcType="CHAR"/>
            <arg column="name" javaType="java.lang.String" jdbcType="VARCHAR"/>
            <arg column="limit_category_ids" javaType="java.lang.String" jdbcType="VARCHAR"/>
            <arg column="limit_price_flag" javaType="java.lang.Boolean" jdbcType="BIT"/>
            <arg column="limit_price_highest" javaType="java.math.BigDecimal" jdbcType="NUMERIC"/>
            <arg column="modify_time" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
            <arg column="limit_type" javaType="INTEGER" jdbcType="INTEGER"/>
        </constructor>
    </resultMap>
    <sql id="Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Tue May 23 22:18:08 CST 2017.
        -->
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Tue May 23 22:18:08 CST 2017.
        -->
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Tue May 23 22:18:08 CST 2017.
        -->
        id, company_id, name, limit_category_ids, limit_price_flag, limit_price_highest,
        modify_time, limit_type
    </sql>
    <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.MallRuleExample"
            resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Tue May 23 22:18:08 CST 2017.
        -->
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from mall_rule
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.MallRuleExample">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Tue May 23 22:18:08 CST 2017.
        -->
        delete from mall_rule
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.MallRule">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Tue May 23 22:18:08 CST 2017.
        -->
        insert into mall_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="companyId != null">
                company_id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="limitCategoryIds != null">
                limit_category_ids,
            </if>
            <if test="limitPriceFlag != null">
                limit_price_flag,
            </if>
            <if test="limitPriceHighest != null">
                limit_price_highest,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="limitType != null">
                limit_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=CHAR},
            </if>
            <if test="companyId != null">
                #{companyId,jdbcType=CHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="limitCategoryIds != null">
                #{limitCategoryIds,jdbcType=VARCHAR},
            </if>
            <if test="limitPriceFlag != null">
                #{limitPriceFlag,jdbcType=BIT},
            </if>
            <if test="limitPriceHighest != null">
                #{limitPriceHighest,jdbcType=NUMERIC},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="limitType != null">
                #{limitType,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.MallRuleExample"
            resultType="java.lang.Long">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Tue May 23 22:18:08 CST 2017.
        -->
        select count(*) from mall_rule
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Tue May 23 22:18:08 CST 2017.
        -->
        update mall_rule
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=CHAR},
            </if>
            <if test="record.companyId != null">
                company_id = #{record.companyId,jdbcType=CHAR},
            </if>
            <if test="record.name != null">
                name = #{record.name,jdbcType=VARCHAR},
            </if>
            <if test="record.limitCategoryIds != null">
                limit_category_ids = #{record.limitCategoryIds,jdbcType=VARCHAR},
            </if>
            <if test="record.limitPriceFlag != null">
                limit_price_flag = #{record.limitPriceFlag,jdbcType=BIT},
            </if>
            <if test="record.limitPriceHighest != null">
                limit_price_highest = #{record.limitPriceHighest,jdbcType=NUMERIC},
            </if>
            <if test="record.modifyTime != null">
                modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.limitType != null">
                limit_type = #{record.limitType,jdbcType=INTEGER},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Tue May 23 22:18:08 CST 2017.
        -->
        update mall_rule
        set id = #{record.id,jdbcType=CHAR},
        company_id = #{record.companyId,jdbcType=CHAR},
        name = #{record.name,jdbcType=VARCHAR},
        limit_category_ids = #{record.limitCategoryIds,jdbcType=VARCHAR},
        limit_price_flag = #{record.limitPriceFlag,jdbcType=BIT},
        limit_price_highest = #{record.limitPriceHighest,jdbcType=NUMERIC},
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
        limit_type = #{record.limitType,jdbcType=INTEGER}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.MallRule">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Tue May 23 22:18:08 CST 2017.
        -->
        update mall_rule
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=CHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="limitCategoryIds != null">
                limit_category_ids = #{limitCategoryIds,jdbcType=VARCHAR},
            </if>
            <if test="limitPriceFlag != null">
                limit_price_flag = #{limitPriceFlag,jdbcType=BIT},
            </if>
            <if test="limitPriceHighest != null">
                limit_price_highest = #{limitPriceHighest,jdbcType=NUMERIC},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="limitType != null">
                limit_type = #{limitType,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=CHAR}
    </update>

    <!-- 查找全部的类目id集合 -->
    <select id="catorgeryIdsList" resultType="java.lang.String">
        select
          limit_category_ids
        from mall_rule
    </select>

    <!-- 批量更新limit_category_ids -->
    <update id="updateCatorgeryByMap" parameterType="java.lang.String">
        update mall_rule

          SET limit_category_ids = #{value}

        WHERE limit_category_ids = #{key}

    </update>


    <!--<update id="updateCatorgeryByMap" parameterType="java.util.Map">
        UPDATE mall_rule
        SET limit_category_ids =
        <foreach collection="params.keys" item="key" index="index"
                 separator="," open="case limit_category_ids" close="end">
            when #{key}  then #{params[${key}]}
        </foreach>
        WHERE
        limit_category_ids in
        <foreach collection="params.keys" item="key" open="(" close=")" separator="," >
            ${key}
        </foreach>
    </update>-->
</mapper>