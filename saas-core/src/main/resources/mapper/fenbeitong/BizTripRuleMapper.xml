<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.BizTripRuleMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.BizTripRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 26 10:59:12 CST 2017.
    -->
    <constructor>
      <idArg column="id" javaType="java.lang.String" jdbcType="CHAR" />
      <arg column="company_id" javaType="java.lang.String" jdbcType="CHAR" />
      <arg column="name" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="air_rule_id" javaType="java.lang.String" jdbcType="CHAR" />
      <arg column="hotel_rule_id" javaType="java.lang.String" jdbcType="CHAR" />
      <arg column="train_rule_id" javaType="java.lang.String" jdbcType="CHAR" />
      <arg column="modify_time" javaType="java.util.Date" jdbcType="TIMESTAMP" />
    </constructor>
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 26 10:59:12 CST 2017.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 26 10:59:12 CST 2017.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 26 10:59:12 CST 2017.
    -->
    id, company_id, name, air_rule_id, hotel_rule_id, train_rule_id, modify_time
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.BizTripRuleExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 26 10:59:12 CST 2017.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from biz_trip_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 26 10:59:12 CST 2017.
    -->
    select 
    <include refid="Base_Column_List" />
    from biz_trip_rule
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 26 10:59:12 CST 2017.
    -->
    delete from biz_trip_rule
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.BizTripRuleExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 26 10:59:12 CST 2017.
    -->
    delete from biz_trip_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.fenbeitong.BizTripRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 26 10:59:12 CST 2017.
    -->
    insert into biz_trip_rule (id, company_id, name, 
      air_rule_id, hotel_rule_id, train_rule_id, 
      modify_time)
    values (#{id,jdbcType=CHAR}, #{companyId,jdbcType=CHAR}, #{name,jdbcType=VARCHAR}, 
      #{airRuleId,jdbcType=CHAR}, #{hotelRuleId,jdbcType=CHAR}, #{trainRuleId,jdbcType=CHAR}, 
      #{modifyTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.BizTripRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 26 10:59:12 CST 2017.
    -->
    insert into biz_trip_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="airRuleId != null">
        air_rule_id,
      </if>
      <if test="hotelRuleId != null">
        hotel_rule_id,
      </if>
      <if test="trainRuleId != null">
        train_rule_id,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=CHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="airRuleId != null">
        #{airRuleId,jdbcType=CHAR},
      </if>
      <if test="hotelRuleId != null">
        #{hotelRuleId,jdbcType=CHAR},
      </if>
      <if test="trainRuleId != null">
        #{trainRuleId,jdbcType=CHAR},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.BizTripRuleExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 26 10:59:12 CST 2017.
    -->
    select count(*) from biz_trip_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 26 10:59:12 CST 2017.
    -->
    update biz_trip_rule
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=CHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.airRuleId != null">
        air_rule_id = #{record.airRuleId,jdbcType=CHAR},
      </if>
      <if test="record.hotelRuleId != null">
        hotel_rule_id = #{record.hotelRuleId,jdbcType=CHAR},
      </if>
      <if test="record.trainRuleId != null">
        train_rule_id = #{record.trainRuleId,jdbcType=CHAR},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 26 10:59:12 CST 2017.
    -->
    update biz_trip_rule
    set id = #{record.id,jdbcType=CHAR},
      company_id = #{record.companyId,jdbcType=CHAR},
      name = #{record.name,jdbcType=VARCHAR},
      air_rule_id = #{record.airRuleId,jdbcType=CHAR},
      hotel_rule_id = #{record.hotelRuleId,jdbcType=CHAR},
      train_rule_id = #{record.trainRuleId,jdbcType=CHAR},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.BizTripRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 26 10:59:12 CST 2017.
    -->
    update biz_trip_rule
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=CHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="airRuleId != null">
        air_rule_id = #{airRuleId,jdbcType=CHAR},
      </if>
      <if test="hotelRuleId != null">
        hotel_rule_id = #{hotelRuleId,jdbcType=CHAR},
      </if>
      <if test="trainRuleId != null">
        train_rule_id = #{trainRuleId,jdbcType=CHAR},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.fenbeitong.BizTripRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 26 10:59:12 CST 2017.
    -->
    update biz_trip_rule
    set company_id = #{companyId,jdbcType=CHAR},
      name = #{name,jdbcType=VARCHAR},
      air_rule_id = #{airRuleId,jdbcType=CHAR},
      hotel_rule_id = #{hotelRuleId,jdbcType=CHAR},
      train_rule_id = #{trainRuleId,jdbcType=CHAR},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>