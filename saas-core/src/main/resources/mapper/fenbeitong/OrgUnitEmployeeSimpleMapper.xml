<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.OrgUnitEmployeeSimpleMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.OrgUnitEmployeeSimple">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 05 16:55:42 CST 2017.
    -->
    <constructor>
      <idArg column="id" javaType="java.lang.Long" jdbcType="BIGINT" />
      <arg column="company_id" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="employee_id" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="org_unit_id" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="is_manager" javaType="java.lang.Boolean" jdbcType="BIT" />
    </constructor>
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 05 16:55:42 CST 2017.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 05 16:55:42 CST 2017.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 05 16:55:42 CST 2017.
    -->
    id, company_id, employee_id, org_unit_id, is_manager
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.OrgUnitEmployeeSimpleExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 05 16:55:42 CST 2017.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from org_unit_employee_simple
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.OrgUnitEmployeeSimpleExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 05 16:55:42 CST 2017.
    -->
    delete from org_unit_employee_simple
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.OrgUnitEmployeeSimple">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 05 16:55:42 CST 2017.
    -->
    insert into org_unit_employee_simple
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="employeeId != null">
        employee_id,
      </if>
      <if test="orgUnitId != null">
        org_unit_id,
      </if>
      <if test="isManager != null">
        is_manager,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="orgUnitId != null">
        #{orgUnitId,jdbcType=VARCHAR},
      </if>
      <if test="isManager != null">
        #{isManager,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.OrgUnitEmployeeSimpleExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 05 16:55:42 CST 2017.
    -->
    select count(*) from org_unit_employee_simple
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 05 16:55:42 CST 2017.
    -->
    update org_unit_employee_simple
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeId != null">
        employee_id = #{record.employeeId,jdbcType=VARCHAR},
      </if>
      <if test="record.orgUnitId != null">
        org_unit_id = #{record.orgUnitId,jdbcType=VARCHAR},
      </if>
      <if test="record.isManager != null">
        is_manager = #{record.isManager,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 05 16:55:42 CST 2017.
    -->
    update org_unit_employee_simple
    set id = #{record.id,jdbcType=BIGINT},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      employee_id = #{record.employeeId,jdbcType=VARCHAR},
      org_unit_id = #{record.orgUnitId,jdbcType=VARCHAR},
      is_manager = #{record.isManager,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.OrgUnitEmployeeSimple">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 05 16:55:42 CST 2017.
    -->
    update org_unit_employee_simple
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        employee_id = #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="orgUnitId != null">
        org_unit_id = #{orgUnitId,jdbcType=VARCHAR},
      </if>
      <if test="isManager != null">
        is_manager = #{isManager,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>