<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.TimeRangeExtMapper">
    <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.TimeRange">
        <constructor>
            <arg column="day_type" javaType="java.lang.Integer" jdbcType="INTEGER"/>
            <arg column="begin_time" javaType="java.util.Date" jdbcType="TIME"/>
            <arg column="end_time" javaType="java.util.Date" jdbcType="TIME"/>
            <arg column="is_overnight" javaType="java.lang.Boolean" jdbcType="BIT"/>
        </constructor>
    </resultMap>
    <sql id="Base_Column_List">
        day_type, begin_time, end_time, is_overnight
    </sql>

    <select id="queryEmployeeRuleRangeTime" resultMap="BaseResultMap">

        select
        <include refid="Base_Column_List"/>
        from
        <if test="type == 1">
            taxi_time_range
        </if>
        <if test="type == 2">
            air_time_range
        </if>
        <if test="type == 3">
            dinner_time_range
        </if>
        <if test="type == 4">
            takeaway_time_range
        </if>
        where rule_id=#{ruleId,jdbcType=VARCHAR}
        group by day_type,begin_time,end_time,is_overnight
        order by day_type,begin_time
    </select>

    <select id="queryEmployeeTaxiRuleRangeTime" resultMap="BaseResultMap">

        select
        <include refid="Base_Column_List"/>
        from taxi_time_range
        where rule_id=#{ruleId,jdbcType=INTEGER}
        group by day_type,begin_time,end_time,is_overnight
        order by day_type,begin_time
    </select>

</mapper>