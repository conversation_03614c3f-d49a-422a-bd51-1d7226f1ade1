<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.DinnerRuleMapperExt">
  <resultMap id="BaseResultMapExt" type="com.fenbeitong.saas.core.model.fenbeitong.DinnerRule"
             extends="com.fenbeitong.saas.core.dao.fenbeitong.DinnerRuleMapper.BaseResultMap">
  </resultMap>

  <select id="getCountByCompanyId" resultType="java.lang.Integer">
    select count(*)
    from dinner_rule
    where company_id = #{companyId,jdbcType=VARCHAR}
  </select>

  <select id="getDinnerRuleListByCompanyId" resultMap="BaseResultMapExt">
    select *
    from dinner_rule
    where company_id = #{companyId,jdbcType=VARCHAR}
    order by update_time desc
  </select>

</mapper>