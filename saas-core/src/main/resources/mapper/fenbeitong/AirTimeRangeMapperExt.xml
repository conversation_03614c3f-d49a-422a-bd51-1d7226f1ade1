<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.AirTimeRangeExtMapper">
    <resultMap id="BaseResultMapEx" type="com.fenbeitong.saas.core.model.fenbeitong.AirTimeRange"
               extends="com.fenbeitong.saas.core.dao.fenbeitong.AirTimeRangeMapper.BaseResultMap">
    </resultMap>


    <select id="airRangeList" resultType="com.fenbeitong.saas.core.contract.rule.AirTimeRangeContract"
            parameterType="java.lang.String">

      SELECT
			r.day_type AS dayType,
			r.begin_time AS beginTime,
			r.end_time AS endTime,
			r.is_overnight AS isOvernight,
			r.batch_id AS batchId
		FROM
			air_time_range r
		WHERE
			rule_id = #{ruleId,jdbcType=VARCHAR}
		GROUP BY
			r.batch_id,
			r.is_overnight,
			r.day_type,
			r.begin_time,
			r.end_time

		ORDER BY batch_id DESC
 	</select>

    <select id="airRangeListByIds" resultType="com.fenbeitong.saas.core.contract.rule.AirTimeRangeContract"
            parameterType="java.util.List">

      SELECT
			r.rule_id AS ruleId,
			r.day_type AS dayType,
			r.begin_time AS beginTime,
			r.end_time AS endTime,
			r.is_overnight AS isOvernight,
			r.batch_id AS batchId
		FROM
			air_time_range r
		WHERE
			rule_id in
			<foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
				#{item}
			</foreach>
		GROUP BY
			r.rule_id,
			r.batch_id,
			r.is_overnight,
			r.day_type,
			r.begin_time,
			r.end_time

		ORDER BY rule_id, batch_id DESC
 	</select>


	<select id="airTimeRangeList" resultType="com.fenbeitong.saas.core.contract.rule.AirTimeRangeContract"
			parameterType="java.lang.String">

		SELECT
			atr.day_type AS dayType,
            atr.begin_time AS beginTime,
            atr.end_time AS endTime,
            atr.batch_id AS batchId
		FROM
			air_rule ar
		LEFT JOIN air_time_range atr ON atr.rule_id = ar."id"
		WHERE
			atr.rule_id = #{ruleId,jdbcType=VARCHAR}
	</select>
</mapper>