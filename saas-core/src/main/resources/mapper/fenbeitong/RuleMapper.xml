<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.RuleMapper">

  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.contract.rule.RuleResponseContract">
    <constructor>
      <idArg column="id" javaType="java.lang.String" jdbcType="CHAR" />
      <arg column="name" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="phone" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="orgUnitIds" javaType="java.lang.String" jdbcType="VARCHAR" />
    </constructor>
  </resultMap>

  <!-- 获取没有限制规则的用户数量 (1:差旅 2:用车 3:采购 8:国际机票)-->
  <select id="getCountByType" parameterType="map" resultType="java.lang.Integer">
    <choose>
      <when test=" type == 1 ">
        select count(DISTINCT employee_id) num from
        (select * from (
        SELECT ce.company_id, ce.employee_id, 1 as type from company_employee ce
        left join employee_air_rule ear on ce.employee_id=ear.employee_id
        where ce.company_id=#{companyId,jdbcType=VARCHAR}
        and ear.air_rule=4 and ear.air_rule_flag=FALSE)a
        UNION all
        (SELECT ce.company_id, ce.employee_id, 2 as type from company_employee ce
        left join employee_hotel_rule ehr on ce.employee_id=ehr.employee_id
        where ce.company_id=#{companyId,jdbcType=VARCHAR}
        and ehr.hotel_rule=2 and ehr.hotel_rule_flag=FALSE)
        UNION all
        (SELECT ce.company_id, ce.employee_id, 3 as type from company_employee ce
        left join employee_train_rule etr on ce.employee_id=etr.employee_id
        where ce.company_id=#{companyId,jdbcType=VARCHAR}
        and etr.train_rule=4 and etr.train_rule_flag=FALSE)) aht
      </when>
      <when test=" type == 2 ">
        select count(0) num from company_employee ce
        left join employee_taxi_rule etr on ce.employee_id=etr.employee_id
        where ce.company_id=#{companyId,jdbcType=VARCHAR} and etr.taxi_rule in (2,3)
        and etr.taxi_rule_flag=FALSE
      </when>
      <when test=" type == 3 ">
        SELECT count(0) num from company_employee ce
        left join employee_mall_rule emr on ce.employee_id=emr.employee_id
        where ce.company_id=#{companyId,jdbcType=VARCHAR} and emr.mall_rule=2
        and emr.mall_rule_flag=FALSE
      </when>
      <when test=" type == 4 ">
        SELECT count(0) num from company_employee ce
        left join employee_dinner_rule emr on ce.employee_id=emr.employee_id
        where ce.company_id=#{companyId,jdbcType=VARCHAR} and emr.dinner_rule=2
        and emr.dinner_rule_flag=FALSE
      </when>
      <when test=" type == 5 ">
        select count(DISTINCT employee_id) num from
        (select * from (
        SELECT ce.company_id, ce.employee_id, 1 as type from company_employee ce
        left join employee_air_rule ear on ce.employee_id=ear.employee_id
        where ce.company_id=#{companyId,jdbcType=VARCHAR}
        and ear.air_rule=4 and ear.air_rule_flag=FALSE)a) aht
      </when>
      <when test=" type == 6 ">
        select count(DISTINCT employee_id) num from
        (SELECT ce.company_id, ce.employee_id, 2 as type from company_employee ce
        left join employee_hotel_rule ehr on ce.employee_id=ehr.employee_id
        where ce.company_id=#{companyId,jdbcType=VARCHAR}
        and ehr.hotel_rule=2 and ehr.hotel_rule_flag=FALSE) aht
      </when>
      <when test=" type == 7 ">
        select count(DISTINCT employee_id) num from
        (SELECT ce.company_id, ce.employee_id, 3 as type from company_employee ce
        left join employee_train_rule etr on ce.employee_id=etr.employee_id
        where ce.company_id=#{companyId,jdbcType=VARCHAR}
        and etr.train_rule=4 and etr.train_rule_flag=FALSE) aht
      </when>
      <when test=" type == 8 ">
        select count(DISTINCT employee_id) num from
        (select * from (
        SELECT ce.company_id, ce.employee_id, 1 as type from company_employee ce
        left join employee_intl_air_rule ear on ce.employee_id=ear.employee_id
        where ce.company_id=#{companyId,jdbcType=VARCHAR}
        and ear.air_rule=4 and ear.air_rule_flag=FALSE)a) aht
      </when>
      <when test=" type == 9 ">
        select count(DISTINCT employee_id) num from
        (select * from (
        SELECT ce.company_id, ce.employee_id, 1 as type from company_employee ce
        left join employee_takeaway_rule etr on ce.employee_id=etr.employee_id
        where ce.company_id=#{companyId,jdbcType=VARCHAR}
        and etr.takeaway_rule=4 and etr.takeaway_rule_flag=FALSE)a) aht
      </when>
    </choose>
  </select>

  <!--获取分页数据-->
  <select id="searchList" parameterType="map" resultMap="BaseResultMap">
    <choose>
      <when test=" type == 1 ">
        select DISTINCT employee_id as  id,name ,orgUnitIds,phone_num phone  from
        (select * from (
        SELECT ce.employee_id,ce.name,ou."name" as orgUnitIds,e.phone_num from company_employee ce
        left join employee_air_rule ear on ce.employee_id=ear.employee_id
        left join employee e on ce.employee_id=e."id"
        left join org_unit ou on ou.id=ce.org_unit_id
        where ce.company_id=#{companyId,jdbcType=VARCHAR}
        and ear.air_rule=4 and ear.air_rule_flag=FALSE)a
        UNION all
        (SELECT ce.employee_id,ce.name,ou."name" as orgUnitIds,e.phone_num from company_employee ce
        left join employee_hotel_rule ehr on ce.employee_id=ehr.employee_id
        left join employee e on ce.employee_id=e."id"
        left join org_unit ou on ou.id=ce.org_unit_id
        where ce.company_id=#{companyId,jdbcType=VARCHAR}
        and ehr.hotel_rule=2 and ehr.hotel_rule_flag=FALSE)
        UNION all
        (SELECT ce.employee_id,ce.name,ou."name" as orgUnitIds,e.phone_num  from company_employee ce
        left join employee_train_rule etr on ce.employee_id=etr.employee_id
        left join employee e on ce.employee_id=e."id"
        left join org_unit ou on ou.id=ce.org_unit_id
        where ce.company_id=#{companyId,jdbcType=VARCHAR}
        and etr.train_rule=4 and etr.train_rule_flag=FALSE)) aht
        <if test=" pageTag == 1 ">
          LIMIT #{pageSize} OFFSET #{start}
        </if>

      </when>
      <when test=" type == 2 ">
        SELECT ce.employee_id as id,ce.name,ou."name" as orgUnitIds,e.phone_num phone from company_employee ce
        left join employee_taxi_rule etr on ce.employee_id=etr.employee_id
        left join employee e on ce.employee_id=e."id"
        left join org_unit ou on ou.id=ce.org_unit_id
        where ce.company_id=#{companyId,jdbcType=VARCHAR} and etr.taxi_rule in (2,3)
        and etr.taxi_rule_flag=FALSE
        <if test=" pageTag == 1 ">
          LIMIT #{pageSize} OFFSET #{start}
        </if>
      </when>
      <when test=" type == 3 ">
        SELECT ce.employee_id as id,ce.name,ou."name" as orgUnitIds,e.phone_num phone from company_employee ce
        left join employee_mall_rule emr on ce.employee_id=emr.employee_id
        left join employee e on ce.employee_id=e."id"
        left join org_unit ou on ou.id=ce.org_unit_id
        where ce.company_id=#{companyId,jdbcType=VARCHAR} and emr.mall_rule=2
        and emr.mall_rule_flag=FALSE
        <if test=" pageTag == 1 ">
          LIMIT #{pageSize} OFFSET #{start}
        </if>
      </when>
      <when test=" type == 4 ">
        SELECT ce.employee_id as id,ce.name,ou."name" as orgUnitIds,e.phone_num phone from company_employee ce
        left join employee_dinner_rule emr on ce.employee_id=emr.employee_id
        left join employee e on ce.employee_id=e."id"
        left join org_unit ou on ou.id=ce.org_unit_id
        where ce.company_id=#{companyId,jdbcType=VARCHAR} and emr.dinner_rule=2
        and emr.dinner_rule_flag=FALSE
        <if test=" pageTag == 1 ">
          LIMIT #{pageSize} OFFSET #{start}
        </if>
      </when>
      <when test=" type == 5 ">
        select DISTINCT employee_id as  id,name ,orgUnitIds,phone_num phone  from
        (select * from (
        SELECT ce.employee_id,ce.name,ou."name" as orgUnitIds,e.phone_num from company_employee ce
        left join employee_air_rule ear on ce.employee_id=ear.employee_id
        left join employee e on ce.employee_id=e."id"
        left join org_unit ou on ou.id=ce.org_unit_id
        where ce.company_id=#{companyId,jdbcType=VARCHAR}
        and ear.air_rule=4 and ear.air_rule_flag=FALSE)a) aht
        <if test=" pageTag == 1 ">
          LIMIT #{pageSize} OFFSET #{start}
        </if>

      </when>
      <when test=" type == 6 ">
        select DISTINCT employee_id as  id,name ,orgUnitIds,phone_num phone  from
        (SELECT ce.employee_id,ce.name,ou."name" as orgUnitIds,e.phone_num from company_employee ce
        left join employee_hotel_rule ehr on ce.employee_id=ehr.employee_id
        left join employee e on ce.employee_id=e."id"
        left join org_unit ou on ou.id=ce.org_unit_id
        where ce.company_id=#{companyId,jdbcType=VARCHAR}
        and ehr.hotel_rule=2 and ehr.hotel_rule_flag=FALSE) aht
        <if test=" pageTag == 1 ">
          LIMIT #{pageSize} OFFSET #{start}
        </if>

      </when>
      <when test=" type == 7 ">
        select DISTINCT employee_id as  id,name ,orgUnitIds,phone_num phone  from
        (SELECT ce.employee_id,ce.name,ou."name" as orgUnitIds,e.phone_num  from company_employee ce
        left join employee_train_rule etr on ce.employee_id=etr.employee_id
        left join employee e on ce.employee_id=e."id"
        left join org_unit ou on ou.id=ce.org_unit_id
        where ce.company_id=#{companyId,jdbcType=VARCHAR}
        and etr.train_rule=4 and etr.train_rule_flag=FALSE) aht
        <if test=" pageTag == 1 ">
          LIMIT #{pageSize} OFFSET #{start}
        </if>

      </when>
      <when test=" type == 8 ">
        select DISTINCT employee_id as  id,name ,orgUnitIds,phone_num phone  from
        (select * from (
        SELECT ce.employee_id,ce.name,ou."name" as orgUnitIds,e.phone_num from company_employee ce
        left join employee_intl_air_rule ear on ce.employee_id=ear.employee_id
        left join employee e on ce.employee_id=e."id"
        left join org_unit ou on ou.id=ce.org_unit_id
        where ce.company_id=#{companyId,jdbcType=VARCHAR}
        and ear.air_rule=4 and ear.air_rule_flag=FALSE)a) aht
        <if test=" pageTag == 1 ">
          LIMIT #{pageSize} OFFSET #{start}
        </if>

      </when>
      <when test=" type == 9 ">
        select DISTINCT employee_id as  id,name ,orgUnitIds,phone_num phone  from
        (select * from (
        SELECT ce.employee_id,ce.name,ou."name" as orgUnitIds,e.phone_num from company_employee ce
        left join employee_takeaway_rule etr on ce.employee_id=etr.employee_id
        left join employee e on ce.employee_id=e."id"
        left join org_unit ou on ou.id=ce.org_unit_id
        where ce.company_id=#{companyId,jdbcType=VARCHAR}
        and etr.takeaway_rule=4 and etr.takeaway_rule_flag=FALSE)a) aht
        <if test=" pageTag == 1 ">
          LIMIT #{pageSize} OFFSET #{start}
        </if>

      </when>
    </choose>

  </select>

</mapper>