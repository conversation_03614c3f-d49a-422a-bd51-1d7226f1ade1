<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.fenbeitong.TrainRuleMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.fenbeitong.TrainRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="company_id" jdbcType="CHAR" property="companyId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="train_seat_flag" jdbcType="BIT" property="trainSeatFlag" />
    <result column="common_train_seat_type" jdbcType="VARCHAR" property="commonTrainSeatType" />
    <result column="highspeed_train_seat_type" jdbcType="VARCHAR" property="highspeedTrainSeatType" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="grap_common_seat_type" jdbcType="VARCHAR" property="grapCommonSeatType" />
    <result column="grap_highspeed_seat_type" jdbcType="VARCHAR" property="grapHighspeedSeatType" />
    <result column="price_limit" jdbcType="NUMERIC" property="priceLimit" />
    <result column="priv_day_min" jdbcType="INTEGER" property="privDayMin" />
    <result column="priv_day_max" jdbcType="INTEGER" property="privDayMax" />
    <result column="day_limit" jdbcType="BIT" property="dayLimit" />
    <result column="is_same_seat" jdbcType="BIT" property="isSameSeat" />
    <result column="grap_limit" jdbcType="INTEGER" property="grapLimit" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, name, train_seat_flag, common_train_seat_type, highspeed_train_seat_type,
    modify_time, grap_common_seat_type, grap_highspeed_seat_type, price_limit, priv_day_min,
    priv_day_max, day_limit, is_same_seat, grap_limit
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TrainRuleExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from train_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${limit} offset ${offset}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from train_rule
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from train_rule
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TrainRuleExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from train_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TrainRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into train_rule (id, company_id, name,
    train_seat_flag, common_train_seat_type, highspeed_train_seat_type,
    modify_time, grap_common_seat_type, grap_highspeed_seat_type,
    price_limit, priv_day_min, priv_day_max,
    day_limit, is_same_seat, grap_limit
    )
    values (#{id,jdbcType=CHAR}, #{companyId,jdbcType=CHAR}, #{name,jdbcType=VARCHAR},
    #{trainSeatFlag,jdbcType=BIT}, #{commonTrainSeatType,jdbcType=VARCHAR}, #{highspeedTrainSeatType,jdbcType=VARCHAR},
    #{modifyTime,jdbcType=TIMESTAMP}, #{grapCommonSeatType,jdbcType=VARCHAR}, #{grapHighspeedSeatType,jdbcType=VARCHAR},
    #{priceLimit,jdbcType=NUMERIC}, #{privDayMin,jdbcType=INTEGER}, #{privDayMax,jdbcType=INTEGER},
    #{dayLimit,jdbcType=BIT}, #{isSameSeat,jdbcType=BIT}, #{grapLimit,jdbcType=INTEGER}
    )
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TrainRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into train_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="trainSeatFlag != null">
        train_seat_flag,
      </if>
      <if test="commonTrainSeatType != null">
        common_train_seat_type,
      </if>
      <if test="highspeedTrainSeatType != null">
        highspeed_train_seat_type,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="grapCommonSeatType != null">
        grap_common_seat_type,
      </if>
      <if test="grapHighspeedSeatType != null">
        grap_highspeed_seat_type,
      </if>
      <if test="priceLimit != null">
        price_limit,
      </if>
      <if test="privDayMin != null">
        priv_day_min,
      </if>
      <if test="privDayMax != null">
        priv_day_max,
      </if>
      <if test="dayLimit != null">
        day_limit,
      </if>
      <if test="isSameSeat != null">
        is_same_seat,
      </if>
      <if test="grapLimit != null">
        grap_limit,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=CHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="trainSeatFlag != null">
        #{trainSeatFlag,jdbcType=BIT},
      </if>
      <if test="commonTrainSeatType != null">
        #{commonTrainSeatType,jdbcType=VARCHAR},
      </if>
      <if test="highspeedTrainSeatType != null">
        #{highspeedTrainSeatType,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="grapCommonSeatType != null">
        #{grapCommonSeatType,jdbcType=VARCHAR},
      </if>
      <if test="grapHighspeedSeatType != null">
        #{grapHighspeedSeatType,jdbcType=VARCHAR},
      </if>
      <if test="priceLimit != null">
        #{priceLimit,jdbcType=NUMERIC},
      </if>
      <if test="privDayMin != null">
        #{privDayMin,jdbcType=INTEGER},
      </if>
      <if test="privDayMax != null">
        #{privDayMax,jdbcType=INTEGER},
      </if>
      <if test="dayLimit != null">
        #{dayLimit,jdbcType=BIT},
      </if>
      <if test="isSameSeat != null">
        #{isSameSeat,jdbcType=BIT},
      </if>
      <if test="grapLimit != null">
        #{grapLimit,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TrainRuleExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from train_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update train_rule
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=CHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.trainSeatFlag != null">
        train_seat_flag = #{record.trainSeatFlag,jdbcType=BIT},
      </if>
      <if test="record.commonTrainSeatType != null">
        common_train_seat_type = #{record.commonTrainSeatType,jdbcType=VARCHAR},
      </if>
      <if test="record.highspeedTrainSeatType != null">
        highspeed_train_seat_type = #{record.highspeedTrainSeatType,jdbcType=VARCHAR},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.grapCommonSeatType != null">
        grap_common_seat_type = #{record.grapCommonSeatType,jdbcType=VARCHAR},
      </if>
      <if test="record.grapHighspeedSeatType != null">
        grap_highspeed_seat_type = #{record.grapHighspeedSeatType,jdbcType=VARCHAR},
      </if>
      <if test="record.priceLimit != null">
        price_limit = #{record.priceLimit,jdbcType=NUMERIC},
      </if>
      <if test="record.privDayMin != null">
        priv_day_min = #{record.privDayMin,jdbcType=INTEGER},
      </if>
      <if test="record.privDayMax != null">
        priv_day_max = #{record.privDayMax,jdbcType=INTEGER},
      </if>
      <if test="record.dayLimit != null">
        day_limit = #{record.dayLimit,jdbcType=BIT},
      </if>
      <if test="record.isSameSeat != null">
        is_same_seat = #{record.isSameSeat,jdbcType=BIT},
      </if>
      <if test="record.grapLimit != null">
        grap_limit = #{record.grapLimit,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update train_rule
    set id = #{record.id,jdbcType=CHAR},
    company_id = #{record.companyId,jdbcType=CHAR},
    name = #{record.name,jdbcType=VARCHAR},
    train_seat_flag = #{record.trainSeatFlag,jdbcType=BIT},
    common_train_seat_type = #{record.commonTrainSeatType,jdbcType=VARCHAR},
    highspeed_train_seat_type = #{record.highspeedTrainSeatType,jdbcType=VARCHAR},
    modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
    grap_common_seat_type = #{record.grapCommonSeatType,jdbcType=VARCHAR},
    grap_highspeed_seat_type = #{record.grapHighspeedSeatType,jdbcType=VARCHAR},
    price_limit = #{record.priceLimit,jdbcType=NUMERIC},
    priv_day_min = #{record.privDayMin,jdbcType=INTEGER},
    priv_day_max = #{record.privDayMax,jdbcType=INTEGER},
    day_limit = #{record.dayLimit,jdbcType=BIT},
    is_same_seat = #{record.isSameSeat,jdbcType=BIT},
    grap_limit = #{record.grapLimit,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TrainRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update train_rule
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=CHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="trainSeatFlag != null">
        train_seat_flag = #{trainSeatFlag,jdbcType=BIT},
      </if>
      <if test="commonTrainSeatType != null">
        common_train_seat_type = #{commonTrainSeatType,jdbcType=VARCHAR},
      </if>
      <if test="highspeedTrainSeatType != null">
        highspeed_train_seat_type = #{highspeedTrainSeatType,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="grapCommonSeatType != null">
        grap_common_seat_type = #{grapCommonSeatType,jdbcType=VARCHAR},
      </if>
      <if test="grapHighspeedSeatType != null">
        grap_highspeed_seat_type = #{grapHighspeedSeatType,jdbcType=VARCHAR},
      </if>
      <if test="priceLimit != null">
        price_limit = #{priceLimit,jdbcType=NUMERIC},
      </if>
      <if test="privDayMin != null">
        priv_day_min = #{privDayMin,jdbcType=INTEGER},
      </if>
      <if test="privDayMax != null">
        priv_day_max = #{privDayMax,jdbcType=INTEGER},
      </if>
      <if test="dayLimit != null">
        day_limit = #{dayLimit,jdbcType=BIT},
      </if>
      <if test="isSameSeat != null">
        is_same_seat = #{isSameSeat,jdbcType=BIT},
      </if>
      <if test="grapLimit != null">
        grap_limit = #{grapLimit,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.fenbeitong.TrainRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update train_rule
    set company_id = #{companyId,jdbcType=CHAR},
    name = #{name,jdbcType=VARCHAR},
    train_seat_flag = #{trainSeatFlag,jdbcType=BIT},
    common_train_seat_type = #{commonTrainSeatType,jdbcType=VARCHAR},
    highspeed_train_seat_type = #{highspeedTrainSeatType,jdbcType=VARCHAR},
    modify_time = #{modifyTime,jdbcType=TIMESTAMP},
    grap_common_seat_type = #{grapCommonSeatType,jdbcType=VARCHAR},
    grap_highspeed_seat_type = #{grapHighspeedSeatType,jdbcType=VARCHAR},
    price_limit = #{priceLimit,jdbcType=NUMERIC},
    priv_day_min = #{privDayMin,jdbcType=INTEGER},
    priv_day_max = #{privDayMax,jdbcType=INTEGER},
    day_limit = #{dayLimit,jdbcType=BIT},
    is_same_seat = #{isSameSeat,jdbcType=BIT},
    grap_limit = #{grapLimit,jdbcType=INTEGER}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>