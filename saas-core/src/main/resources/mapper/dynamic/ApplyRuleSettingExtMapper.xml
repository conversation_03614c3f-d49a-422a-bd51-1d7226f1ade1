<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.dynamic.ApplyRuleSettingExtMapper">
  <resultMap id="BaseResultMap"
             type="com.fenbeitong.saas.core.model.saas.ApplyRuleSetting"
             extends="com.fenbeitong.saas.core.dao.dynamic.ApplyRuleSettingMapper.BaseResultMap">
  </resultMap>

  <select id="queryApplyRuleByApplyOrderId" resultMap="BaseResultMap">
    select *
    from apply_rule_setting
    where apply_order_id = #{applyId,jdbcType=CHAR} and trip_id is null limit 1
  </select>
</mapper>