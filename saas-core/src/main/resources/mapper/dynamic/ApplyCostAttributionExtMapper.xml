<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.dynamic.ApplyCostAttributionExtMapper">
  <resultMap id="BaseResultMapExt" type="com.fenbeitong.saas.core.model.saas.ApplyCostAttribution"
             extends="com.fenbeitong.saas.core.dao.dynamic.ApplyCostAttributionMapper.BaseResultMap">
  </resultMap>

  <insert id="batchInsertCostAttribution">
    insert into apply_cost_attribution (apply_id, cost_attribution_id,
    cost_attribution_name, cost_attribution_category,
    company_id,bring_in,record_id,category_name)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.applyId}, #{item.costAttributionId}, #{item.costAttributionName}, #{item.costAttributionCategory}
      , #{item.companyId}, #{item.bringIn}, #{item.recordId}, #{item.categoryName})
    </foreach>
  </insert>
</mapper>