<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.dynamic.ApplyTripInfoMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.ApplyTripInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="apply_order_id" jdbcType="CHAR" property="applyOrderId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="float_start_time" jdbcType="TIMESTAMP" property="floatStartTime" />
    <result column="float_end_time" jdbcType="TIMESTAMP" property="floatEndTime" />
    <result column="start_city_id" jdbcType="VARCHAR" property="startCityId" />
    <result column="arrival_city_id" jdbcType="VARCHAR" property="arrivalCityId" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="trip_applicate_current_id" jdbcType="CHAR" property="tripApplicateCurrentId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="estimated_amount" jdbcType="DECIMAL" property="estimatedAmount" />
    <result column="start_city_name" jdbcType="VARCHAR" property="startCityName" />
    <result column="arrival_city_name" jdbcType="VARCHAR" property="arrivalCityName" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_time" jdbcType="TIMESTAMP" property="orderTime" />
    <result column="person_count" jdbcType="INTEGER" property="personCount" />
    <result column="price_structure" jdbcType="VARCHAR" property="priceStructure" />
    <result column="order_reason" jdbcType="VARCHAR" property="orderReason" />
    <result column="address_info" jdbcType="VARCHAR" property="addressInfo" />
    <result column="cost_attribution_name" jdbcType="VARCHAR" property="costAttributionName" />
    <result column="mall_list" jdbcType="VARCHAR" property="mallList" />
    <result column="order_reason_desc" jdbcType="VARCHAR" property="orderReasonDesc" />
    <result column="back_start_time" jdbcType="TIMESTAMP" property="backStartTime" />
    <result column="back_end_time" jdbcType="TIMESTAMP" property="backEndTime" />
    <result column="float_back_start_time" jdbcType="TIMESTAMP" property="floatBackStartTime" />
    <result column="float_back_end_time" jdbcType="TIMESTAMP" property="floatBackEndTime" />
    <result column="trip_type" jdbcType="INTEGER" property="tripType" />
    <result column="trip_content" jdbcType="VARCHAR" property="tripContent" />
    <result column="order_reason_id" jdbcType="INTEGER" property="orderReasonId" />
    <result column="delete_status" jdbcType="INTEGER" property="deleteStatus" />
    <result column="taxi_apply_scene_type" jdbcType="INTEGER" property="taxiApplySceneType" />
    <result column="date_type" jdbcType="INTEGER" property="dateType" />
    <result column="apply_reason_id" jdbcType="INTEGER" property="applyReasonId" />
    <result column="apply_reason" jdbcType="VARCHAR" property="applyReason" />
    <result column="apply_reason_desc" jdbcType="VARCHAR" property="applyReasonDesc" />
    <result column="reason_bring_in" jdbcType="INTEGER" property="reasonBringIn" />
    <result column="sub_scene_id" jdbcType="INTEGER" property="subSceneId" />
    <result column="add_reason_bring_in" jdbcType="INTEGER" property="addReasonBringIn" />
    <result column="root_trip_id" jdbcType="VARCHAR" property="rootTripId" />
    <result column="parent_trip_id" jdbcType="VARCHAR" property="parentTripId" />
    <result column="check_rule" jdbcType="BIT" property="checkRule" />
    <result column="custform_module_id" jdbcType="VARCHAR" property="custformModuleId" />
    <result column="api_use_version" jdbcType="INTEGER" property="apiUseVersion" />
    <result column="config_code" jdbcType="VARCHAR" property="configCode" />
    <result column="is_ding" jdbcType="TINYINT" property="isDing" />
    <result column="trip_third_id" jdbcType="VARCHAR" property="tripThirdId" />
    <result column="disable_city_list" jdbcType="VARCHAR" property="disableCityList" />
    <result column="apply_reason_is_edit" jdbcType="INTEGER" property="applyReasonIsEdit" />
    <result column="trip_add_reason_is_edit" jdbcType="INTEGER" property="tripAddReasonIsEdit" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, apply_order_id, create_time, type, start_time, end_time, float_start_time, float_end_time,
    start_city_id, arrival_city_id, state, trip_applicate_current_id, update_time, estimated_amount,
    start_city_name, arrival_city_name, title, content, order_time, person_count, price_structure,
    order_reason, address_info, cost_attribution_name, mall_list, order_reason_desc,
    back_start_time, back_end_time, float_back_start_time, float_back_end_time, trip_type,
    trip_content, order_reason_id, delete_status, taxi_apply_scene_type, date_type, apply_reason_id,
    apply_reason, apply_reason_desc, reason_bring_in, sub_scene_id, add_reason_bring_in,
    root_trip_id, parent_trip_id, check_rule, custform_module_id, api_use_version, config_code,
    is_ding, trip_third_id, disable_city_list, apply_reason_is_edit, trip_add_reason_is_edit
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyTripInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from apply_trip_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from apply_trip_info
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from apply_trip_info
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyTripInfoExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from apply_trip_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.saas.ApplyTripInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into apply_trip_info (id, apply_order_id, create_time, 
      type, start_time, end_time, 
      float_start_time, float_end_time, start_city_id,
      arrival_city_id, state, trip_applicate_current_id,
      update_time, estimated_amount, start_city_name,
      arrival_city_name, title, content,
      order_time, person_count, price_structure,
      order_reason, address_info, cost_attribution_name,
      mall_list, order_reason_desc, back_start_time,
      back_end_time, float_back_start_time, float_back_end_time,
      trip_type, trip_content, order_reason_id,
      delete_status, taxi_apply_scene_type, date_type,
      apply_reason_id, apply_reason, apply_reason_desc,
      reason_bring_in, sub_scene_id, add_reason_bring_in,
      root_trip_id, parent_trip_id, check_rule,
      custform_module_id, api_use_version, config_code,
      is_ding, trip_third_id, disable_city_list,
      apply_reason_is_edit, trip_add_reason_is_edit)
    values (#{id,jdbcType=CHAR}, #{applyOrderId,jdbcType=CHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{type,jdbcType=INTEGER}, #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, 
      #{floatStartTime,jdbcType=TIMESTAMP}, #{floatEndTime,jdbcType=TIMESTAMP}, #{startCityId,jdbcType=VARCHAR},
      #{arrivalCityId,jdbcType=VARCHAR}, #{state,jdbcType=INTEGER}, #{tripApplicateCurrentId,jdbcType=CHAR},
      #{updateTime,jdbcType=TIMESTAMP}, #{estimatedAmount,jdbcType=DECIMAL}, #{startCityName,jdbcType=VARCHAR},
      #{arrivalCityName,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR},
      #{orderTime,jdbcType=TIMESTAMP}, #{personCount,jdbcType=INTEGER}, #{priceStructure,jdbcType=VARCHAR},
      #{orderReason,jdbcType=VARCHAR}, #{addressInfo,jdbcType=VARCHAR}, #{costAttributionName,jdbcType=VARCHAR},
      #{mallList,jdbcType=VARCHAR}, #{orderReasonDesc,jdbcType=VARCHAR}, #{backStartTime,jdbcType=TIMESTAMP},
      #{backEndTime,jdbcType=TIMESTAMP}, #{floatBackStartTime,jdbcType=TIMESTAMP}, #{floatBackEndTime,jdbcType=TIMESTAMP},
      #{tripType,jdbcType=INTEGER}, #{tripContent,jdbcType=VARCHAR}, #{orderReasonId,jdbcType=INTEGER},
      #{deleteStatus,jdbcType=INTEGER}, #{taxiApplySceneType,jdbcType=INTEGER}, #{dateType,jdbcType=INTEGER},
      #{applyReasonId,jdbcType=INTEGER}, #{applyReason,jdbcType=VARCHAR}, #{applyReasonDesc,jdbcType=VARCHAR},
      #{reasonBringIn,jdbcType=INTEGER}, #{subSceneId,jdbcType=INTEGER}, #{addReasonBringIn,jdbcType=INTEGER},
      #{rootTripId,jdbcType=VARCHAR}, #{parentTripId,jdbcType=VARCHAR}, #{checkRule,jdbcType=BIT},
      #{custformModuleId,jdbcType=VARCHAR}, #{apiUseVersion,jdbcType=INTEGER}, #{configCode,jdbcType=VARCHAR},
      #{isDing,jdbcType=TINYINT}, #{tripThirdId,jdbcType=VARCHAR}, #{disableCityList,jdbcType=VARCHAR},
      #{applyReasonIsEdit,jdbcType=INTEGER}, #{tripAddReasonIsEdit,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.saas.ApplyTripInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into apply_trip_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="applyOrderId != null">
        apply_order_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="floatStartTime != null">
        float_start_time,
      </if>
      <if test="floatEndTime != null">
        float_end_time,
      </if>
      <if test="startCityId != null">
        start_city_id,
      </if>
      <if test="arrivalCityId != null">
        arrival_city_id,
      </if>
      <if test="state != null">
        state,
      </if>
      <if test="tripApplicateCurrentId != null">
        trip_applicate_current_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="estimatedAmount != null">
        estimated_amount,
      </if>
      <if test="startCityName != null">
        start_city_name,
      </if>
      <if test="arrivalCityName != null">
        arrival_city_name,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="orderTime != null">
        order_time,
      </if>
      <if test="personCount != null">
        person_count,
      </if>
      <if test="priceStructure != null">
        price_structure,
      </if>
      <if test="orderReason != null">
        order_reason,
      </if>
      <if test="addressInfo != null">
        address_info,
      </if>
      <if test="costAttributionName != null">
        cost_attribution_name,
      </if>
      <if test="mallList != null">
        mall_list,
      </if>
      <if test="orderReasonDesc != null">
        order_reason_desc,
      </if>
      <if test="backStartTime != null">
        back_start_time,
      </if>
      <if test="backEndTime != null">
        back_end_time,
      </if>
      <if test="floatBackStartTime != null">
        float_back_start_time,
      </if>
      <if test="floatBackEndTime != null">
        float_back_end_time,
      </if>
      <if test="tripType != null">
        trip_type,
      </if>
      <if test="tripContent != null">
        trip_content,
      </if>
      <if test="orderReasonId != null">
        order_reason_id,
      </if>
      <if test="deleteStatus != null">
        delete_status,
      </if>
      <if test="taxiApplySceneType != null">
        taxi_apply_scene_type,
      </if>
      <if test="dateType != null">
        date_type,
      </if>
      <if test="applyReasonId != null">
        apply_reason_id,
      </if>
      <if test="applyReason != null">
        apply_reason,
      </if>
      <if test="applyReasonDesc != null">
        apply_reason_desc,
      </if>
      <if test="reasonBringIn != null">
        reason_bring_in,
      </if>
      <if test="subSceneId != null">
        sub_scene_id,
      </if>
      <if test="addReasonBringIn != null">
        add_reason_bring_in,
      </if>
      <if test="rootTripId != null">
        root_trip_id,
      </if>
      <if test="parentTripId != null">
        parent_trip_id,
      </if>
      <if test="checkRule != null">
        check_rule,
      </if>
      <if test="custformModuleId != null">
        custform_module_id,
      </if>
      <if test="apiUseVersion != null">
        api_use_version,
      </if>
      <if test="configCode != null">
        config_code,
      </if>
      <if test="isDing != null">
        is_ding,
      </if>
      <if test="tripThirdId != null">
        trip_third_id,
      </if>
      <if test="disableCityList != null">
        disable_city_list,
      </if>
      <if test="applyReasonIsEdit != null">
        apply_reason_is_edit,
      </if>
      <if test="tripAddReasonIsEdit != null">
        trip_add_reason_is_edit,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="applyOrderId != null">
        #{applyOrderId,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="floatStartTime != null">
        #{floatStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="floatEndTime != null">
        #{floatEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="startCityId != null">
        #{startCityId,jdbcType=VARCHAR},
      </if>
      <if test="arrivalCityId != null">
        #{arrivalCityId,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=INTEGER},
      </if>
      <if test="tripApplicateCurrentId != null">
        #{tripApplicateCurrentId,jdbcType=CHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="estimatedAmount != null">
        #{estimatedAmount,jdbcType=DECIMAL},
      </if>
      <if test="startCityName != null">
        #{startCityName,jdbcType=VARCHAR},
      </if>
      <if test="arrivalCityName != null">
        #{arrivalCityName,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="orderTime != null">
        #{orderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="personCount != null">
        #{personCount,jdbcType=INTEGER},
      </if>
      <if test="priceStructure != null">
        #{priceStructure,jdbcType=VARCHAR},
      </if>
      <if test="orderReason != null">
        #{orderReason,jdbcType=VARCHAR},
      </if>
      <if test="addressInfo != null">
        #{addressInfo,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionName != null">
        #{costAttributionName,jdbcType=VARCHAR},
      </if>
      <if test="mallList != null">
        #{mallList,jdbcType=VARCHAR},
      </if>
      <if test="orderReasonDesc != null">
        #{orderReasonDesc,jdbcType=VARCHAR},
      </if>
      <if test="backStartTime != null">
        #{backStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="backEndTime != null">
        #{backEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="floatBackStartTime != null">
        #{floatBackStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="floatBackEndTime != null">
        #{floatBackEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tripType != null">
        #{tripType,jdbcType=INTEGER},
      </if>
      <if test="tripContent != null">
        #{tripContent,jdbcType=VARCHAR},
      </if>
      <if test="orderReasonId != null">
        #{orderReasonId,jdbcType=INTEGER},
      </if>
      <if test="deleteStatus != null">
        #{deleteStatus,jdbcType=INTEGER},
      </if>
      <if test="taxiApplySceneType != null">
        #{taxiApplySceneType,jdbcType=INTEGER},
      </if>
      <if test="dateType != null">
        #{dateType,jdbcType=INTEGER},
      </if>
      <if test="applyReasonId != null">
        #{applyReasonId,jdbcType=INTEGER},
      </if>
      <if test="applyReason != null">
        #{applyReason,jdbcType=VARCHAR},
      </if>
      <if test="applyReasonDesc != null">
        #{applyReasonDesc,jdbcType=VARCHAR},
      </if>
      <if test="reasonBringIn != null">
        #{reasonBringIn,jdbcType=INTEGER},
      </if>
      <if test="subSceneId != null">
        #{subSceneId,jdbcType=INTEGER},
      </if>
      <if test="addReasonBringIn != null">
        #{addReasonBringIn,jdbcType=INTEGER},
      </if>
      <if test="rootTripId != null">
        #{rootTripId,jdbcType=VARCHAR},
      </if>
      <if test="parentTripId != null">
        #{parentTripId,jdbcType=VARCHAR},
      </if>
      <if test="checkRule != null">
        #{checkRule,jdbcType=BIT},
      </if>
      <if test="custformModuleId != null">
        #{custformModuleId,jdbcType=VARCHAR},
      </if>
      <if test="apiUseVersion != null">
        #{apiUseVersion,jdbcType=INTEGER},
      </if>
      <if test="configCode != null">
        #{configCode,jdbcType=VARCHAR},
      </if>
      <if test="isDing != null">
        #{isDing,jdbcType=TINYINT},
      </if>
      <if test="tripThirdId != null">
        #{tripThirdId,jdbcType=VARCHAR},
      </if>
      <if test="disableCityList != null">
        #{disableCityList,jdbcType=VARCHAR},
      </if>
      <if test="applyReasonIsEdit != null">
        #{applyReasonIsEdit,jdbcType=INTEGER},
      </if>
      <if test="tripAddReasonIsEdit != null">
        #{tripAddReasonIsEdit,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyTripInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from apply_trip_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_trip_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.applyOrderId != null">
        apply_order_id = #{record.applyOrderId,jdbcType=CHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.floatStartTime != null">
        float_start_time = #{record.floatStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.floatEndTime != null">
        float_end_time = #{record.floatEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.startCityId != null">
        start_city_id = #{record.startCityId,jdbcType=VARCHAR},
      </if>
      <if test="record.arrivalCityId != null">
        arrival_city_id = #{record.arrivalCityId,jdbcType=VARCHAR},
      </if>
      <if test="record.state != null">
        state = #{record.state,jdbcType=INTEGER},
      </if>
      <if test="record.tripApplicateCurrentId != null">
        trip_applicate_current_id = #{record.tripApplicateCurrentId,jdbcType=CHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.estimatedAmount != null">
        estimated_amount = #{record.estimatedAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.startCityName != null">
        start_city_name = #{record.startCityName,jdbcType=VARCHAR},
      </if>
      <if test="record.arrivalCityName != null">
        arrival_city_name = #{record.arrivalCityName,jdbcType=VARCHAR},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=VARCHAR},
      </if>
      <if test="record.orderTime != null">
        order_time = #{record.orderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.personCount != null">
        person_count = #{record.personCount,jdbcType=INTEGER},
      </if>
      <if test="record.priceStructure != null">
        price_structure = #{record.priceStructure,jdbcType=VARCHAR},
      </if>
      <if test="record.orderReason != null">
        order_reason = #{record.orderReason,jdbcType=VARCHAR},
      </if>
      <if test="record.addressInfo != null">
        address_info = #{record.addressInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.costAttributionName != null">
        cost_attribution_name = #{record.costAttributionName,jdbcType=VARCHAR},
      </if>
      <if test="record.mallList != null">
        mall_list = #{record.mallList,jdbcType=VARCHAR},
      </if>
      <if test="record.orderReasonDesc != null">
        order_reason_desc = #{record.orderReasonDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.backStartTime != null">
        back_start_time = #{record.backStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.backEndTime != null">
        back_end_time = #{record.backEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.floatBackStartTime != null">
        float_back_start_time = #{record.floatBackStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.floatBackEndTime != null">
        float_back_end_time = #{record.floatBackEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tripType != null">
        trip_type = #{record.tripType,jdbcType=INTEGER},
      </if>
      <if test="record.tripContent != null">
        trip_content = #{record.tripContent,jdbcType=VARCHAR},
      </if>
      <if test="record.orderReasonId != null">
        order_reason_id = #{record.orderReasonId,jdbcType=INTEGER},
      </if>
      <if test="record.deleteStatus != null">
        delete_status = #{record.deleteStatus,jdbcType=INTEGER},
      </if>
      <if test="record.taxiApplySceneType != null">
        taxi_apply_scene_type = #{record.taxiApplySceneType,jdbcType=INTEGER},
      </if>
      <if test="record.dateType != null">
        date_type = #{record.dateType,jdbcType=INTEGER},
      </if>
      <if test="record.applyReasonId != null">
        apply_reason_id = #{record.applyReasonId,jdbcType=INTEGER},
      </if>
      <if test="record.applyReason != null">
        apply_reason = #{record.applyReason,jdbcType=VARCHAR},
      </if>
      <if test="record.applyReasonDesc != null">
        apply_reason_desc = #{record.applyReasonDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.reasonBringIn != null">
        reason_bring_in = #{record.reasonBringIn,jdbcType=INTEGER},
      </if>
      <if test="record.subSceneId != null">
        sub_scene_id = #{record.subSceneId,jdbcType=INTEGER},
      </if>
      <if test="record.addReasonBringIn != null">
        add_reason_bring_in = #{record.addReasonBringIn,jdbcType=INTEGER},
      </if>
      <if test="record.rootTripId != null">
        root_trip_id = #{record.rootTripId,jdbcType=VARCHAR},
      </if>
      <if test="record.parentTripId != null">
        parent_trip_id = #{record.parentTripId,jdbcType=VARCHAR},
      </if>
      <if test="record.checkRule != null">
        check_rule = #{record.checkRule,jdbcType=BIT},
      </if>
      <if test="record.custformModuleId != null">
        custform_module_id = #{record.custformModuleId,jdbcType=VARCHAR},
      </if>
      <if test="record.apiUseVersion != null">
        api_use_version = #{record.apiUseVersion,jdbcType=INTEGER},
      </if>
      <if test="record.configCode != null">
        config_code = #{record.configCode,jdbcType=VARCHAR},
      </if>
      <if test="record.isDing != null">
        is_ding = #{record.isDing,jdbcType=TINYINT},
      </if>
      <if test="record.tripThirdId != null">
        trip_third_id = #{record.tripThirdId,jdbcType=VARCHAR},
      </if>
      <if test="record.disableCityList != null">
        disable_city_list = #{record.disableCityList,jdbcType=VARCHAR},
      </if>
      <if test="record.applyReasonIsEdit != null">
        apply_reason_is_edit = #{record.applyReasonIsEdit,jdbcType=INTEGER},
      </if>
      <if test="record.tripAddReasonIsEdit != null">
        trip_add_reason_is_edit = #{record.tripAddReasonIsEdit,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_trip_info
    set id = #{record.id,jdbcType=CHAR},
      apply_order_id = #{record.applyOrderId,jdbcType=CHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      type = #{record.type,jdbcType=INTEGER},
      start_time = #{record.startTime,jdbcType=TIMESTAMP},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      float_start_time = #{record.floatStartTime,jdbcType=TIMESTAMP},
      float_end_time = #{record.floatEndTime,jdbcType=TIMESTAMP},
      start_city_id = #{record.startCityId,jdbcType=VARCHAR},
      arrival_city_id = #{record.arrivalCityId,jdbcType=VARCHAR},
      state = #{record.state,jdbcType=INTEGER},
      trip_applicate_current_id = #{record.tripApplicateCurrentId,jdbcType=CHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      estimated_amount = #{record.estimatedAmount,jdbcType=DECIMAL},
      start_city_name = #{record.startCityName,jdbcType=VARCHAR},
      arrival_city_name = #{record.arrivalCityName,jdbcType=VARCHAR},
      title = #{record.title,jdbcType=VARCHAR},
      content = #{record.content,jdbcType=VARCHAR},
      order_time = #{record.orderTime,jdbcType=TIMESTAMP},
      person_count = #{record.personCount,jdbcType=INTEGER},
      price_structure = #{record.priceStructure,jdbcType=VARCHAR},
      order_reason = #{record.orderReason,jdbcType=VARCHAR},
      address_info = #{record.addressInfo,jdbcType=VARCHAR},
      cost_attribution_name = #{record.costAttributionName,jdbcType=VARCHAR},
      mall_list = #{record.mallList,jdbcType=VARCHAR},
      order_reason_desc = #{record.orderReasonDesc,jdbcType=VARCHAR},
      back_start_time = #{record.backStartTime,jdbcType=TIMESTAMP},
      back_end_time = #{record.backEndTime,jdbcType=TIMESTAMP},
      float_back_start_time = #{record.floatBackStartTime,jdbcType=TIMESTAMP},
      float_back_end_time = #{record.floatBackEndTime,jdbcType=TIMESTAMP},
      trip_type = #{record.tripType,jdbcType=INTEGER},
      trip_content = #{record.tripContent,jdbcType=VARCHAR},
      order_reason_id = #{record.orderReasonId,jdbcType=INTEGER},
      delete_status = #{record.deleteStatus,jdbcType=INTEGER},
      taxi_apply_scene_type = #{record.taxiApplySceneType,jdbcType=INTEGER},
      date_type = #{record.dateType,jdbcType=INTEGER},
      apply_reason_id = #{record.applyReasonId,jdbcType=INTEGER},
      apply_reason = #{record.applyReason,jdbcType=VARCHAR},
      apply_reason_desc = #{record.applyReasonDesc,jdbcType=VARCHAR},
      reason_bring_in = #{record.reasonBringIn,jdbcType=INTEGER},
      sub_scene_id = #{record.subSceneId,jdbcType=INTEGER},
      add_reason_bring_in = #{record.addReasonBringIn,jdbcType=INTEGER},
      root_trip_id = #{record.rootTripId,jdbcType=VARCHAR},
      parent_trip_id = #{record.parentTripId,jdbcType=VARCHAR},
      check_rule = #{record.checkRule,jdbcType=BIT},
      custform_module_id = #{record.custformModuleId,jdbcType=VARCHAR},
      api_use_version = #{record.apiUseVersion,jdbcType=INTEGER},
      config_code = #{record.configCode,jdbcType=VARCHAR},
      is_ding = #{record.isDing,jdbcType=TINYINT},
      trip_third_id = #{record.tripThirdId,jdbcType=VARCHAR},
      disable_city_list = #{record.disableCityList,jdbcType=VARCHAR},
      apply_reason_is_edit = #{record.applyReasonIsEdit,jdbcType=INTEGER},
      trip_add_reason_is_edit = #{record.tripAddReasonIsEdit,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saas.ApplyTripInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_trip_info
    <set>
      <if test="applyOrderId != null">
        apply_order_id = #{applyOrderId,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="floatStartTime != null">
        float_start_time = #{floatStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="floatEndTime != null">
        float_end_time = #{floatEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="startCityId != null">
        start_city_id = #{startCityId,jdbcType=VARCHAR},
      </if>
      <if test="arrivalCityId != null">
        arrival_city_id = #{arrivalCityId,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=INTEGER},
      </if>
      <if test="tripApplicateCurrentId != null">
        trip_applicate_current_id = #{tripApplicateCurrentId,jdbcType=CHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="estimatedAmount != null">
        estimated_amount = #{estimatedAmount,jdbcType=DECIMAL},
      </if>
      <if test="startCityName != null">
        start_city_name = #{startCityName,jdbcType=VARCHAR},
      </if>
      <if test="arrivalCityName != null">
        arrival_city_name = #{arrivalCityName,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="orderTime != null">
        order_time = #{orderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="personCount != null">
        person_count = #{personCount,jdbcType=INTEGER},
      </if>
      <if test="priceStructure != null">
        price_structure = #{priceStructure,jdbcType=VARCHAR},
      </if>
      <if test="orderReason != null">
        order_reason = #{orderReason,jdbcType=VARCHAR},
      </if>
      <if test="addressInfo != null">
        address_info = #{addressInfo,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionName != null">
        cost_attribution_name = #{costAttributionName,jdbcType=VARCHAR},
      </if>
      <if test="mallList != null">
        mall_list = #{mallList,jdbcType=VARCHAR},
      </if>
      <if test="orderReasonDesc != null">
        order_reason_desc = #{orderReasonDesc,jdbcType=VARCHAR},
      </if>
      <if test="backStartTime != null">
        back_start_time = #{backStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="backEndTime != null">
        back_end_time = #{backEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="floatBackStartTime != null">
        float_back_start_time = #{floatBackStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="floatBackEndTime != null">
        float_back_end_time = #{floatBackEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tripType != null">
        trip_type = #{tripType,jdbcType=INTEGER},
      </if>
      <if test="tripContent != null">
        trip_content = #{tripContent,jdbcType=VARCHAR},
      </if>
      <if test="orderReasonId != null">
        order_reason_id = #{orderReasonId,jdbcType=INTEGER},
      </if>
      <if test="deleteStatus != null">
        delete_status = #{deleteStatus,jdbcType=INTEGER},
      </if>
      <if test="taxiApplySceneType != null">
        taxi_apply_scene_type = #{taxiApplySceneType,jdbcType=INTEGER},
      </if>
      <if test="dateType != null">
        date_type = #{dateType,jdbcType=INTEGER},
      </if>
      <if test="applyReasonId != null">
        apply_reason_id = #{applyReasonId,jdbcType=INTEGER},
      </if>
      <if test="applyReason != null">
        apply_reason = #{applyReason,jdbcType=VARCHAR},
      </if>
      <if test="applyReasonDesc != null">
        apply_reason_desc = #{applyReasonDesc,jdbcType=VARCHAR},
      </if>
      <if test="reasonBringIn != null">
        reason_bring_in = #{reasonBringIn,jdbcType=INTEGER},
      </if>
      <if test="subSceneId != null">
        sub_scene_id = #{subSceneId,jdbcType=INTEGER},
      </if>
      <if test="addReasonBringIn != null">
        add_reason_bring_in = #{addReasonBringIn,jdbcType=INTEGER},
      </if>
      <if test="rootTripId != null">
        root_trip_id = #{rootTripId,jdbcType=VARCHAR},
      </if>
      <if test="parentTripId != null">
        parent_trip_id = #{parentTripId,jdbcType=VARCHAR},
      </if>
      <if test="checkRule != null">
        check_rule = #{checkRule,jdbcType=BIT},
      </if>
      <if test="custformModuleId != null">
        custform_module_id = #{custformModuleId,jdbcType=VARCHAR},
      </if>
      <if test="apiUseVersion != null">
        api_use_version = #{apiUseVersion,jdbcType=INTEGER},
      </if>
      <if test="configCode != null">
        config_code = #{configCode,jdbcType=VARCHAR},
      </if>
      <if test="isDing != null">
        is_ding = #{isDing,jdbcType=TINYINT},
      </if>
      <if test="tripThirdId != null">
        trip_third_id = #{tripThirdId,jdbcType=VARCHAR},
      </if>
      <if test="disableCityList != null">
        disable_city_list = #{disableCityList,jdbcType=VARCHAR},
      </if>
      <if test="applyReasonIsEdit != null">
        apply_reason_is_edit = #{applyReasonIsEdit,jdbcType=INTEGER},
      </if>
      <if test="tripAddReasonIsEdit != null">
        trip_add_reason_is_edit = #{tripAddReasonIsEdit,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.saas.ApplyTripInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_trip_info
    set apply_order_id = #{applyOrderId,jdbcType=CHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      type = #{type,jdbcType=INTEGER},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      float_start_time = #{floatStartTime,jdbcType=TIMESTAMP},
      float_end_time = #{floatEndTime,jdbcType=TIMESTAMP},
      start_city_id = #{startCityId,jdbcType=VARCHAR},
      arrival_city_id = #{arrivalCityId,jdbcType=VARCHAR},
      state = #{state,jdbcType=INTEGER},
      trip_applicate_current_id = #{tripApplicateCurrentId,jdbcType=CHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      estimated_amount = #{estimatedAmount,jdbcType=DECIMAL},
      start_city_name = #{startCityName,jdbcType=VARCHAR},
      arrival_city_name = #{arrivalCityName,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR},
      order_time = #{orderTime,jdbcType=TIMESTAMP},
      person_count = #{personCount,jdbcType=INTEGER},
      price_structure = #{priceStructure,jdbcType=VARCHAR},
      order_reason = #{orderReason,jdbcType=VARCHAR},
      address_info = #{addressInfo,jdbcType=VARCHAR},
      cost_attribution_name = #{costAttributionName,jdbcType=VARCHAR},
      mall_list = #{mallList,jdbcType=VARCHAR},
      order_reason_desc = #{orderReasonDesc,jdbcType=VARCHAR},
      back_start_time = #{backStartTime,jdbcType=TIMESTAMP},
      back_end_time = #{backEndTime,jdbcType=TIMESTAMP},
      float_back_start_time = #{floatBackStartTime,jdbcType=TIMESTAMP},
      float_back_end_time = #{floatBackEndTime,jdbcType=TIMESTAMP},
      trip_type = #{tripType,jdbcType=INTEGER},
      trip_content = #{tripContent,jdbcType=VARCHAR},
      order_reason_id = #{orderReasonId,jdbcType=INTEGER},
      delete_status = #{deleteStatus,jdbcType=INTEGER},
      taxi_apply_scene_type = #{taxiApplySceneType,jdbcType=INTEGER},
      date_type = #{dateType,jdbcType=INTEGER},
      apply_reason_id = #{applyReasonId,jdbcType=INTEGER},
      apply_reason = #{applyReason,jdbcType=VARCHAR},
      apply_reason_desc = #{applyReasonDesc,jdbcType=VARCHAR},
      reason_bring_in = #{reasonBringIn,jdbcType=INTEGER},
      sub_scene_id = #{subSceneId,jdbcType=INTEGER},
      add_reason_bring_in = #{addReasonBringIn,jdbcType=INTEGER},
      root_trip_id = #{rootTripId,jdbcType=VARCHAR},
      parent_trip_id = #{parentTripId,jdbcType=VARCHAR},
      check_rule = #{checkRule,jdbcType=BIT},
      custform_module_id = #{custformModuleId,jdbcType=VARCHAR},
      api_use_version = #{apiUseVersion,jdbcType=INTEGER},
      config_code = #{configCode,jdbcType=VARCHAR},
      is_ding = #{isDing,jdbcType=TINYINT},
      trip_third_id = #{tripThirdId,jdbcType=VARCHAR},
      disable_city_list = #{disableCityList,jdbcType=VARCHAR},
      apply_reason_is_edit = #{applyReasonIsEdit,jdbcType=INTEGER},
      trip_add_reason_is_edit = #{tripAddReasonIsEdit,jdbcType=INTEGER}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>