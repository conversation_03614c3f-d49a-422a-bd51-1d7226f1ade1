<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.dynamic.ApplyTripApplicateMapper">
    <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.ApplyTripApplicate">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="CHAR" property="id" />
        <result column="apply_trip_id" jdbcType="CHAR" property="applyTripId" />
        <result column="apply_id" jdbcType="CHAR" property="applyId" />
        <result column="order_id" jdbcType="VARCHAR" property="orderId" />
        <result column="order_product_type" jdbcType="INTEGER" property="orderProductType" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="action" jdbcType="INTEGER" property="action" />
        <result column="comment" jdbcType="VARCHAR" property="comment" />
        <result column="use_time" jdbcType="TIMESTAMP" property="useTime" />
        <result column="apply_trip_type" jdbcType="INTEGER" property="applyTripType"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        id, apply_trip_id, apply_id, order_id, order_product_type, create_time, action, comment,
        use_time, apply_trip_type
    </sql>
    <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyTripApplicateExample" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List" />
        from apply_trip_applicate
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <choose>
                <when test="offset == null">
                    limit ${limit}
                </when>
                <otherwise>
                    limit ${limit} offset ${offset}
                </otherwise>
            </choose>
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <include refid="Base_Column_List" />
        from apply_trip_applicate
        where id = #{id,jdbcType=CHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from apply_trip_applicate
        where id = #{id,jdbcType=CHAR}
    </delete>
    <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyTripApplicateExample">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from apply_trip_applicate
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.fenbeitong.saas.core.model.saas.ApplyTripApplicate">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into apply_trip_applicate (id, apply_trip_id, apply_id,
        order_id, order_product_type, create_time,
        action, comment, use_time,
        apply_trip_type)
        values (#{id,jdbcType=CHAR}, #{applyTripId,jdbcType=CHAR}, #{applyId,jdbcType=CHAR},
        #{orderId,jdbcType=VARCHAR}, #{orderProductType,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP},
        #{action,jdbcType=INTEGER}, #{comment,jdbcType=VARCHAR}, #{useTime,jdbcType=TIMESTAMP},
        #{applyTripType,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.saas.ApplyTripApplicate">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into apply_trip_applicate
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="applyTripId != null">
                apply_trip_id,
            </if>
            <if test="applyId != null">
                apply_id,
            </if>
            <if test="orderId != null">
                order_id,
            </if>
            <if test="orderProductType != null">
                order_product_type,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="action != null">
                action,
            </if>
            <if test="comment != null">
                comment,
            </if>
            <if test="useTime != null">
                use_time,
            </if>
            <if test="applyTripType != null">
                apply_trip_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=CHAR},
            </if>
            <if test="applyTripId != null">
                #{applyTripId,jdbcType=CHAR},
            </if>
            <if test="applyId != null">
                #{applyId,jdbcType=CHAR},
            </if>
            <if test="orderId != null">
                #{orderId,jdbcType=VARCHAR},
            </if>
            <if test="orderProductType != null">
                #{orderProductType,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="action != null">
                #{action,jdbcType=INTEGER},
            </if>
            <if test="comment != null">
                #{comment,jdbcType=VARCHAR},
            </if>
            <if test="useTime != null">
                #{useTime,jdbcType=TIMESTAMP},
            </if>
            <if test="applyTripType != null">
                #{applyTripType,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyTripApplicateExample" resultType="java.lang.Long">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select count(*) from apply_trip_applicate
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update apply_trip_applicate
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=CHAR},
            </if>
            <if test="record.applyTripId != null">
                apply_trip_id = #{record.applyTripId,jdbcType=CHAR},
            </if>
            <if test="record.applyId != null">
                apply_id = #{record.applyId,jdbcType=CHAR},
            </if>
            <if test="record.orderId != null">
                order_id = #{record.orderId,jdbcType=VARCHAR},
            </if>
            <if test="record.orderProductType != null">
                order_product_type = #{record.orderProductType,jdbcType=INTEGER},
            </if>
            <if test="record.createTime != null">
                create_time = #{record.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.action != null">
                action = #{record.action,jdbcType=INTEGER},
            </if>
            <if test="record.comment != null">
                comment = #{record.comment,jdbcType=VARCHAR},
            </if>
            <if test="record.useTime != null">
                use_time = #{record.useTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.applyTripType != null">
                apply_trip_type = #{record.applyTripType,jdbcType=INTEGER},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update apply_trip_applicate
        set id = #{record.id,jdbcType=CHAR},
        apply_trip_id = #{record.applyTripId,jdbcType=CHAR},
        apply_id = #{record.applyId,jdbcType=CHAR},
        order_id = #{record.orderId,jdbcType=VARCHAR},
        order_product_type = #{record.orderProductType,jdbcType=INTEGER},
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
        action = #{record.action,jdbcType=INTEGER},
        comment = #{record.comment,jdbcType=VARCHAR},
        use_time = #{record.useTime,jdbcType=TIMESTAMP},
        apply_trip_type = #{record.applyTripType,jdbcType=INTEGER}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause" />
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saas.ApplyTripApplicate">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update apply_trip_applicate
        <set>
            <if test="applyTripId != null">
                apply_trip_id = #{applyTripId,jdbcType=CHAR},
            </if>
            <if test="applyId != null">
                apply_id = #{applyId,jdbcType=CHAR},
            </if>
            <if test="orderId != null">
                order_id = #{orderId,jdbcType=VARCHAR},
            </if>
            <if test="orderProductType != null">
                order_product_type = #{orderProductType,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="action != null">
                action = #{action,jdbcType=INTEGER},
            </if>
            <if test="comment != null">
                comment = #{comment,jdbcType=VARCHAR},
            </if>
            <if test="useTime != null">
                use_time = #{useTime,jdbcType=TIMESTAMP},
            </if>
            <if test="applyTripType != null">
                apply_trip_type = #{applyTripType,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=CHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.saas.ApplyTripApplicate">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update apply_trip_applicate
        set apply_trip_id = #{applyTripId,jdbcType=CHAR},
        apply_id = #{applyId,jdbcType=CHAR},
        order_id = #{orderId,jdbcType=VARCHAR},
        order_product_type = #{orderProductType,jdbcType=INTEGER},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        action = #{action,jdbcType=INTEGER},
        comment = #{comment,jdbcType=VARCHAR},
        use_time = #{useTime,jdbcType=TIMESTAMP},
        apply_trip_type = #{applyTripType,jdbcType=INTEGER}
        where id = #{id,jdbcType=CHAR}
    </update>
    <select id="selectAll" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Thu Mar 09 10:17:40 CST 2017.
        -->
        select id, apply_trip_id, apply_id, order_id, order_product_type, create_time, action,
        comment, apply_trip_type
        from apply_trip_applicate
    </select>
</mapper>