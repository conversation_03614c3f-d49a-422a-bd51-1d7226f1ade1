<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.dynamic.ApplyRuleSettingMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.ApplyRuleSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="apply_order_id" jdbcType="CHAR" property="applyOrderId" />
    <result column="air_info" jdbcType="VARCHAR" property="airInfo" />
    <result column="intl_air_info" jdbcType="VARCHAR" property="intlAirInfo" />
    <result column="train_info" jdbcType="VARCHAR" property="trainInfo" />
    <result column="hotel_info" jdbcType="VARCHAR" property="hotelInfo" />
    <result column="taxi_info" jdbcType="VARCHAR" property="taxiInfo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, apply_order_id, air_info, intl_air_info, train_info, hotel_info, taxi_info, create_time, 
    update_time
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyRuleSettingExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from apply_rule_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${limit} offset ${offset}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from apply_rule_setting
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from apply_rule_setting
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyRuleSettingExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from apply_rule_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.saas.ApplyRuleSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into apply_rule_setting (id, apply_order_id, air_info, 
      intl_air_info, train_info, hotel_info, 
      taxi_info, create_time, update_time
      )
    values (#{id,jdbcType=INTEGER}, #{applyOrderId,jdbcType=CHAR}, #{airInfo,jdbcType=VARCHAR}, 
      #{intlAirInfo,jdbcType=VARCHAR}, #{trainInfo,jdbcType=VARCHAR}, #{hotelInfo,jdbcType=VARCHAR}, 
      #{taxiInfo,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.saas.ApplyRuleSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into apply_rule_setting
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="applyOrderId != null">
        apply_order_id,
      </if>
      <if test="airInfo != null">
        air_info,
      </if>
      <if test="intlAirInfo != null">
        intl_air_info,
      </if>
      <if test="trainInfo != null">
        train_info,
      </if>
      <if test="hotelInfo != null">
        hotel_info,
      </if>
      <if test="taxiInfo != null">
        taxi_info,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="applyOrderId != null">
        #{applyOrderId,jdbcType=CHAR},
      </if>
      <if test="airInfo != null">
        #{airInfo,jdbcType=VARCHAR},
      </if>
      <if test="intlAirInfo != null">
        #{intlAirInfo,jdbcType=VARCHAR},
      </if>
      <if test="trainInfo != null">
        #{trainInfo,jdbcType=VARCHAR},
      </if>
      <if test="hotelInfo != null">
        #{hotelInfo,jdbcType=VARCHAR},
      </if>
      <if test="taxiInfo != null">
        #{taxiInfo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyRuleSettingExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from apply_rule_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_rule_setting
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.applyOrderId != null">
        apply_order_id = #{record.applyOrderId,jdbcType=CHAR},
      </if>
      <if test="record.airInfo != null">
        air_info = #{record.airInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.intlAirInfo != null">
        intl_air_info = #{record.intlAirInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.trainInfo != null">
        train_info = #{record.trainInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.hotelInfo != null">
        hotel_info = #{record.hotelInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.taxiInfo != null">
        taxi_info = #{record.taxiInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_rule_setting
    set id = #{record.id,jdbcType=INTEGER},
      apply_order_id = #{record.applyOrderId,jdbcType=CHAR},
      air_info = #{record.airInfo,jdbcType=VARCHAR},
      intl_air_info = #{record.intlAirInfo,jdbcType=VARCHAR},
      train_info = #{record.trainInfo,jdbcType=VARCHAR},
      hotel_info = #{record.hotelInfo,jdbcType=VARCHAR},
      taxi_info = #{record.taxiInfo,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saas.ApplyRuleSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_rule_setting
    <set>
      <if test="applyOrderId != null">
        apply_order_id = #{applyOrderId,jdbcType=CHAR},
      </if>
      <if test="airInfo != null">
        air_info = #{airInfo,jdbcType=VARCHAR},
      </if>
      <if test="intlAirInfo != null">
        intl_air_info = #{intlAirInfo,jdbcType=VARCHAR},
      </if>
      <if test="trainInfo != null">
        train_info = #{trainInfo,jdbcType=VARCHAR},
      </if>
      <if test="hotelInfo != null">
        hotel_info = #{hotelInfo,jdbcType=VARCHAR},
      </if>
      <if test="taxiInfo != null">
        taxi_info = #{taxiInfo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.saas.ApplyRuleSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_rule_setting
    set apply_order_id = #{applyOrderId,jdbcType=CHAR},
      air_info = #{airInfo,jdbcType=VARCHAR},
      intl_air_info = #{intlAirInfo,jdbcType=VARCHAR},
      train_info = #{trainInfo,jdbcType=VARCHAR},
      hotel_info = #{hotelInfo,jdbcType=VARCHAR},
      taxi_info = #{taxiInfo,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>