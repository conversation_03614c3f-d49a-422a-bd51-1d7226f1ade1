<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.dynamic.ApplyTripGuestExtMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.ApplyTripGuest">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="apply_order_id" jdbcType="CHAR" property="applyOrderId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="contact_id" jdbcType="CHAR" property="contactId" />
    <result column="contact_info" jdbcType="VARCHAR" property="contactInfo" />
    <result column="delete_status" jdbcType="INTEGER" property="deleteStatus" />
  </resultMap>

  <select id="selectListByApplyOrderId" resultMap="BaseResultMap" parameterType="java.lang.String">
    select id, apply_order_id, create_time,  contact_id, contact_info
    from apply_trip_guest where apply_order_id = #{applyOrderId,jdbcType=CHAR} order by id asc
  </select>

  <select id="selectListByApplyOrderIds" resultMap="BaseResultMap" parameterType="java.util.List">
    select id, apply_order_id, create_time,  contact_id, contact_info
    from apply_trip_guest
    where apply_order_id in
    <foreach collection="list" item="applyOrderId" index="index"
             open="(" close=")" separator=",">
      #{applyOrderId}
    </foreach>
    order by apply_order_id desc, id asc
  </select>

  <delete id="deleteByApplyOrderId" parameterType="java.lang.String">
    delete from apply_trip_guest
    where apply_order_id = #{applyOrderId,jdbcType=CHAR}
  </delete>
</mapper>