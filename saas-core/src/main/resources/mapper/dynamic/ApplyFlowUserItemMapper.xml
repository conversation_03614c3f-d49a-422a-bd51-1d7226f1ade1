<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.dynamic.ApplyFlowUserItemMapper">
    <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.ApplyFlowUserItem">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Tue Apr 25 15:21:39 CST 2017.
        -->
        <id column="id" jdbcType="CHAR" property="id"/>
        <result column="apply_order_id" jdbcType="VARCHAR" property="applyOrderId"/>
        <result column="apply_flow_user_id" jdbcType="VARCHAR" property="applyFlowUserId"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="item_type" jdbcType="INTEGER" property="itemType"/>
        <result column="item_id" jdbcType="VARCHAR" property="itemId"/>
        <result column="item_name" jdbcType="VARCHAR" property="itemName"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
    </resultMap>
    <sql id="columnList">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Tue Apr 25 15:21:39 CST 2017.
        -->
        id, apply_order_id, apply_flow_user_id, sort, item_type, item_id, user_id, update_time,
        status, item_name
    </sql>
    <delete id="deleteByApplyFlowUserId">
    delete from apply_flow_user_item
    where apply_flow_user_id = #{id,jdbcType=CHAR}
  </delete>
    <insert id="insert" parameterType="com.fenbeitong.saas.core.model.saas.ApplyFlowUserItem">
        insert into apply_flow_user_item (
        <include refid="columnList"></include>
        )values (#{id,jdbcType=CHAR}, #{applyOrderId,jdbcType=VARCHAR},
        #{applyFlowUserId,jdbcType=VARCHAR}, #{sort,jdbcType=INTEGER},
        #{itemType,jdbcType=INTEGER}, #{itemId,jdbcType=VARCHAR},
        #{userId,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
        #{status,jdbcType=INTEGER}, #{itemName,jdbcType=VARCHAR})
    </insert>
    <select id="selectListByApplyFlowUserId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="columnList"></include>
        from apply_flow_user_item
        where apply_flow_user_id = #{applyFlowUserId}
        order by sort asc
    </select>
    <select id="selectListByApplyId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="columnList"></include>
        from apply_flow_user_item
        where apply_order_id = #{applyId}
        order by sort asc
    </select>
    <update id="setApplyFlowUserItemStatus">
      UPDATE apply_flow_user_item
      set status = #{status},
      update_time = #{time}
      where id = #{id}
    </update>
    <select id="queryHistoryApplyUserByApplyId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="columnList"></include>
        from apply_flow_user_item
        where apply_order_id = #{applyId}
        and status in (4,8,64)
        order by sort asc
    </select>

    <select id="selectFlowItemsByApplyId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="columnList"></include>
        from apply_flow_user_item
        where apply_order_id = #{applyId}
        order by sort desc
    </select>
</mapper>