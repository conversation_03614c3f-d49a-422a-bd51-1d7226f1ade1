<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.dynamic.ApplyOrderMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.ApplyOrder">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="meaning_no" jdbcType="VARCHAR" property="meaningNo" />
    <result column="bill_no" jdbcType="VARCHAR" property="billNo" />
    <result column="type" jdbcType="SMALLINT" property="type" />
    <result column="employee_id" jdbcType="CHAR" property="employeeId" />
    <result column="budget" jdbcType="INTEGER" property="budget" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="check_reason" jdbcType="VARCHAR" property="checkReason" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="reimburse_time" jdbcType="TIMESTAMP" property="reimburseTime" />
    <result column="reimburser_id" jdbcType="VARCHAR" property="reimburserId" />
    <result column="reimburser_name" jdbcType="VARCHAR" property="reimburserName" />
    <result column="reimburser_snap_content" jdbcType="VARCHAR" property="reimburserSnapContent" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="company_id" jdbcType="CHAR" property="companyId" />
    <result column="mail_order_id" jdbcType="CHAR" property="mailOrderId" />
    <result column="travel_price_detail" jdbcType="VARCHAR" property="travelPriceDetail" />
    <result column="apply_reason" jdbcType="VARCHAR" property="applyReason" />
    <result column="apply_reason_desc" jdbcType="VARCHAR" property="applyReasonDesc" />
    <result column="city_range" jdbcType="VARCHAR" property="cityRange" />
    <result column="time_range" jdbcType="VARCHAR" property="timeRange" />
    <result column="current_log_id" jdbcType="BIGINT" property="currentLogId" />
    <result column="approver_id" jdbcType="VARCHAR" property="approverId" />
    <result column="flow_type" jdbcType="INTEGER" property="flowType" />
    <result column="flow_cc_type" jdbcType="INTEGER" property="flowCcType" />
    <result column="past_status" jdbcType="BIT" property="pastStatus" />
    <result column="past_day" jdbcType="BIGINT" property="pastDay" />
    <result column="third_id" jdbcType="VARCHAR" property="thirdId" />
    <result column="third_remark" jdbcType="VARCHAR" property="thirdRemark" />
    <result column="exceed_buy_desc" jdbcType="VARCHAR" property="exceedBuyDesc" />
    <result column="repulse_desc" jdbcType="VARCHAR" property="repulseDesc" />
    <result column="apply_order_type" jdbcType="INTEGER" property="applyOrderType" />
    <result column="applicant_name" jdbcType="VARCHAR" property="applicantName" />
    <result column="exceed_buy_desc_content" jdbcType="VARCHAR" property="exceedBuyDescContent" />
    <result column="cost_attribution_id" jdbcType="VARCHAR" property="costAttributionId" />
    <result column="cost_attribution_name" jdbcType="VARCHAR" property="costAttributionName" />
    <result column="cost_attribution_category" jdbcType="INTEGER" property="costAttributionCategory" />
    <result column="real_price" jdbcType="DECIMAL" property="realPrice" />
    <result column="exceed_buy_type" jdbcType="INTEGER" property="exceedBuyType" />
    <result column="overtime" jdbcType="TIMESTAMP" property="overtime" />
    <result column="travel_day" jdbcType="DECIMAL" property="travelDay" />
    <result column="root_apply_order_id" jdbcType="CHAR" property="rootApplyOrderId" />
    <result column="parent_apply_order_id" jdbcType="CHAR" property="parentApplyOrderId" />
    <result column="is_change_apply" jdbcType="BIT" property="isChangeApply" />
    <result column="change_reason" jdbcType="VARCHAR" property="changeReason" />
    <result column="change_reason_desc" jdbcType="VARCHAR" property="changeReasonDesc" />
    <result column="cancel_reason" jdbcType="VARCHAR" property="cancelReason" />
    <result column="cancel_reason_desc" jdbcType="VARCHAR" property="cancelReasonDesc" />
    <result column="sub_type" jdbcType="INTEGER" property="subType" />
    <result column="custom_fields" jdbcType="VARCHAR" property="customFields" />
    <result column="apply_reason_id" jdbcType="INTEGER" property="applyReasonId" />
    <result column="change_reason_id" jdbcType="INTEGER" property="changeReasonId" />
    <result column="cancel_reason_id" jdbcType="INTEGER" property="cancelReasonId" />
    <result column="download_link" jdbcType="VARCHAR" property="downloadLink" />
    <result column="voucher_status" jdbcType="INTEGER" property="voucherStatus" />
    <result column="voucher_code" jdbcType="VARCHAR" property="voucherCode" />
    <result column="hasten_statu" jdbcType="INTEGER" property="hastenStatu" />
    <result column="hasten_create_time" jdbcType="TIMESTAMP" property="hastenCreateTime" />
    <result column="return_ticket" jdbcType="INTEGER" property="returnTicket" />
    <result column="return_download" jdbcType="INTEGER" property="returnDownload" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="payment_status" jdbcType="INTEGER" property="paymentStatus" />
    <result column="payment_time" jdbcType="TIMESTAMP" property="paymentTime" />
    <result column="snap_content" jdbcType="VARCHAR" property="snapContent" />
    <result column="reimburse_order_id" jdbcType="VARCHAR" property="reimburseOrderId" />
    <result column="pay_apply_status" jdbcType="INTEGER" property="payApplyStatus" />
    <result column="task_info" jdbcType="VARCHAR" property="taskInfo" />
    <result column="send_status" jdbcType="INTEGER" property="sendStatus" />
    <result column="delete_status" jdbcType="INTEGER" property="deleteStatus" />
    <result column="flow_process_type" jdbcType="INTEGER" property="flowProcessType" />
    <result column="flow_process_id" jdbcType="VARCHAR" property="flowProcessId" />
    <result column="client_type" jdbcType="VARCHAR" property="clientType" />
    <result column="client_version" jdbcType="VARCHAR" property="clientVersion" />
    <result column="cost_attribution_code" jdbcType="VARCHAR" property="costAttributionCode" />
    <result column="is_ding" jdbcType="TINYINT" property="isDing" />
    <result column="meta_info" jdbcType="VARCHAR" property="metaInfo" />
    <result column="form_id" jdbcType="VARCHAR" property="formId" />
    <result column="create_param_snapshot" jdbcType="VARCHAR" property="createParamSnapshot" />
    <result column="thrid_relation_no" jdbcType="VARCHAR" property="thridRelationNo" />
    <result column="create_version" jdbcType="INTEGER" property="createVersion" />
    <result column="is_associate" jdbcType="BIT" property="isAssociate" />
    <result column="return_date" jdbcType="TIMESTAMP" property="returnDate" />
    <result column="invoice_state" jdbcType="INTEGER" property="invoiceState" />
    <result column="invoice_reminder" jdbcType="BIT" property="invoiceReminder" />
    <result column="final_approve_time" jdbcType="TIMESTAMP" property="finalApproveTime" />
    <result column="relation_apply_id" jdbcType="VARCHAR" property="relationApplyId" />
    <result column="invoice_pdf_url" jdbcType="VARCHAR" property="invoicePdfUrl" />
    <result column="apply_pdf_url" jdbcType="VARCHAR" property="applyPdfUrl" />
    <result column="order_name" jdbcType="VARCHAR" property="orderName" />
  </resultMap>

  <resultMap id="BaseResultGroupMap" type="com.fenbeitong.saas.core.model.saas.ApplyOrderStateGroup">
    <id column="count" jdbcType="INTEGER" property="count" />
    <result column="state" jdbcType="INTEGER" property="state" />
  </resultMap>
  <sql id="Groyp_By_State">
    count(1) as count,state
  </sql>

  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, meaning_no, bill_no, type, employee_id, budget, state, check_reason, update_time, reimburse_time,
    reimburser_id, reimburser_name, reimburser_snap_content, create_time, company_id, 
    mail_order_id, travel_price_detail, apply_reason, apply_reason_desc, city_range, 
    time_range, current_log_id, approver_id, flow_type, flow_cc_type, past_status, past_day, 
    third_id, third_remark, exceed_buy_desc, repulse_desc, apply_order_type, applicant_name, 
    exceed_buy_desc_content, cost_attribution_id, cost_attribution_name, cost_attribution_category, 
    real_price, exceed_buy_type, overtime, travel_day, root_apply_order_id, parent_apply_order_id, 
    is_change_apply, change_reason, change_reason_desc, cancel_reason, cancel_reason_desc, 
    sub_type, custom_fields, apply_reason_id, change_reason_id, cancel_reason_id, download_link, 
    voucher_status, voucher_code, hasten_statu, hasten_create_time, return_ticket, return_download, 
    title, payment_status, payment_time, snap_content, reimburse_order_id, pay_apply_status, 
    task_info, send_status, delete_status, flow_process_type, flow_process_id, client_type, 
    client_version, cost_attribution_code, is_ding, meta_info, form_id, create_param_snapshot, 
    thrid_relation_no, create_version, is_associate, return_date, invoice_state, invoice_reminder, 
    final_approve_time, relation_apply_id, invoice_pdf_url, apply_pdf_url, order_name
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrderExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from apply_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectGroypByStateExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrderExample" resultMap="BaseResultGroupMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Groyp_By_State"/>
    from apply_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    group by state
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from apply_order
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from apply_order
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrderExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from apply_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrder">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into apply_order (id, bill_no, type, 
      employee_id, budget, state, 
      check_reason, update_time, reimburse_time, 
      reimburser_id, reimburser_name, reimburser_snap_content, 
      create_time, company_id, mail_order_id, 
      travel_price_detail, apply_reason, apply_reason_desc, 
      city_range, time_range, current_log_id, 
      approver_id, flow_type, flow_cc_type, 
      past_status, past_day, third_id, 
      third_remark, exceed_buy_desc, repulse_desc, 
      apply_order_type, applicant_name, exceed_buy_desc_content, 
      cost_attribution_id, cost_attribution_name, 
      cost_attribution_category, real_price, exceed_buy_type, 
      overtime, travel_day, root_apply_order_id, 
      parent_apply_order_id, is_change_apply, change_reason, 
      change_reason_desc, cancel_reason, cancel_reason_desc, 
      sub_type, custom_fields, apply_reason_id, 
      change_reason_id, cancel_reason_id, download_link, 
      voucher_status, voucher_code, hasten_statu, 
      hasten_create_time, return_ticket, return_download, 
      title, payment_status, payment_time, 
      snap_content, reimburse_order_id, pay_apply_status, 
      task_info, send_status, delete_status, 
      flow_process_type, flow_process_id, client_type, 
      client_version, cost_attribution_code, is_ding, 
      meta_info, form_id, create_param_snapshot, 
      thrid_relation_no, create_version, is_associate, 
      return_date, invoice_state, invoice_reminder, 
      final_approve_time, relation_apply_id, invoice_pdf_url, 
      apply_pdf_url)
    values (#{id,jdbcType=CHAR}, #{billNo,jdbcType=VARCHAR}, #{type,jdbcType=SMALLINT}, 
      #{employeeId,jdbcType=CHAR}, #{budget,jdbcType=INTEGER}, #{state,jdbcType=INTEGER}, 
      #{checkReason,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{reimburseTime,jdbcType=TIMESTAMP}, 
      #{reimburserId,jdbcType=VARCHAR}, #{reimburserName,jdbcType=VARCHAR}, #{reimburserSnapContent,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{companyId,jdbcType=CHAR}, #{mailOrderId,jdbcType=CHAR}, 
      #{travelPriceDetail,jdbcType=VARCHAR}, #{applyReason,jdbcType=VARCHAR}, #{applyReasonDesc,jdbcType=VARCHAR}, 
      #{cityRange,jdbcType=VARCHAR}, #{timeRange,jdbcType=VARCHAR}, #{currentLogId,jdbcType=BIGINT}, 
      #{approverId,jdbcType=VARCHAR}, #{flowType,jdbcType=INTEGER}, #{flowCcType,jdbcType=INTEGER}, 
      #{pastStatus,jdbcType=BIT}, #{pastDay,jdbcType=BIGINT}, #{thirdId,jdbcType=VARCHAR}, 
      #{thirdRemark,jdbcType=VARCHAR}, #{exceedBuyDesc,jdbcType=VARCHAR}, #{repulseDesc,jdbcType=VARCHAR}, 
      #{applyOrderType,jdbcType=INTEGER}, #{applicantName,jdbcType=VARCHAR}, #{exceedBuyDescContent,jdbcType=VARCHAR}, 
      #{costAttributionId,jdbcType=VARCHAR}, #{costAttributionName,jdbcType=VARCHAR}, 
      #{costAttributionCategory,jdbcType=INTEGER}, #{realPrice,jdbcType=DECIMAL}, #{exceedBuyType,jdbcType=INTEGER}, 
      #{overtime,jdbcType=TIMESTAMP}, #{travelDay,jdbcType=DECIMAL}, #{rootApplyOrderId,jdbcType=CHAR}, 
      #{parentApplyOrderId,jdbcType=CHAR}, #{isChangeApply,jdbcType=BIT}, #{changeReason,jdbcType=VARCHAR}, 
      #{changeReasonDesc,jdbcType=VARCHAR}, #{cancelReason,jdbcType=VARCHAR}, #{cancelReasonDesc,jdbcType=VARCHAR}, 
      #{subType,jdbcType=INTEGER}, #{customFields,jdbcType=VARCHAR}, #{applyReasonId,jdbcType=INTEGER}, 
      #{changeReasonId,jdbcType=INTEGER}, #{cancelReasonId,jdbcType=INTEGER}, #{downloadLink,jdbcType=VARCHAR}, 
      #{voucherStatus,jdbcType=INTEGER}, #{voucherCode,jdbcType=VARCHAR}, #{hastenStatu,jdbcType=INTEGER}, 
      #{hastenCreateTime,jdbcType=TIMESTAMP}, #{returnTicket,jdbcType=INTEGER}, #{returnDownload,jdbcType=INTEGER}, 
      #{title,jdbcType=VARCHAR}, #{paymentStatus,jdbcType=INTEGER}, #{paymentTime,jdbcType=TIMESTAMP}, 
      #{snapContent,jdbcType=VARCHAR}, #{reimburseOrderId,jdbcType=VARCHAR}, #{payApplyStatus,jdbcType=INTEGER}, 
      #{taskInfo,jdbcType=VARCHAR}, #{sendStatus,jdbcType=INTEGER}, #{deleteStatus,jdbcType=INTEGER}, 
      #{flowProcessType,jdbcType=INTEGER}, #{flowProcessId,jdbcType=VARCHAR}, #{clientType,jdbcType=VARCHAR}, 
      #{clientVersion,jdbcType=VARCHAR}, #{costAttributionCode,jdbcType=VARCHAR}, #{isDing,jdbcType=TINYINT}, 
      #{metaInfo,jdbcType=VARCHAR}, #{formId,jdbcType=VARCHAR}, #{createParamSnapshot,jdbcType=VARCHAR}, 
      #{thridRelationNo,jdbcType=VARCHAR}, #{createVersion,jdbcType=INTEGER}, #{isAssociate,jdbcType=BIT}, 
      #{returnDate,jdbcType=TIMESTAMP}, #{invoiceState,jdbcType=INTEGER}, #{invoiceReminder,jdbcType=BIT}, 
      #{finalApproveTime,jdbcType=TIMESTAMP}, #{relationApplyId,jdbcType=VARCHAR}, #{invoicePdfUrl,jdbcType=VARCHAR}, 
      #{applyPdfUrl,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrder">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into apply_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="meaningNo != null">
        meaning_no,
      </if>
      <if test="billNo != null">
        bill_no,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="employeeId != null">
        employee_id,
      </if>
      <if test="budget != null">
        budget,
      </if>
      <if test="state != null">
        state,
      </if>
      <if test="checkReason != null">
        check_reason,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="reimburseTime != null">
        reimburse_time,
      </if>
      <if test="reimburserId != null">
        reimburser_id,
      </if>
      <if test="reimburserName != null">
        reimburser_name,
      </if>
      <if test="reimburserSnapContent != null">
        reimburser_snap_content,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="mailOrderId != null">
        mail_order_id,
      </if>
      <if test="travelPriceDetail != null">
        travel_price_detail,
      </if>
      <if test="applyReason != null">
        apply_reason,
      </if>
      <if test="applyReasonDesc != null">
        apply_reason_desc,
      </if>
      <if test="cityRange != null">
        city_range,
      </if>
      <if test="timeRange != null">
        time_range,
      </if>
      <if test="currentLogId != null">
        current_log_id,
      </if>
      <if test="approverId != null">
        approver_id,
      </if>
      <if test="flowType != null">
        flow_type,
      </if>
      <if test="flowCcType != null">
        flow_cc_type,
      </if>
      <if test="pastStatus != null">
        past_status,
      </if>
      <if test="pastDay != null">
        past_day,
      </if>
      <if test="thirdId != null">
        third_id,
      </if>
      <if test="thirdRemark != null">
        third_remark,
      </if>
      <if test="exceedBuyDesc != null">
        exceed_buy_desc,
      </if>
      <if test="repulseDesc != null">
        repulse_desc,
      </if>
      <if test="applyOrderType != null">
        apply_order_type,
      </if>
      <if test="applicantName != null">
        applicant_name,
      </if>
      <if test="exceedBuyDescContent != null">
        exceed_buy_desc_content,
      </if>
      <if test="costAttributionId != null">
        cost_attribution_id,
      </if>
      <if test="costAttributionName != null">
        cost_attribution_name,
      </if>
      <if test="costAttributionCategory != null">
        cost_attribution_category,
      </if>
      <if test="realPrice != null">
        real_price,
      </if>
      <if test="exceedBuyType != null">
        exceed_buy_type,
      </if>
      <if test="overtime != null">
        overtime,
      </if>
      <if test="travelDay != null">
        travel_day,
      </if>
      <if test="rootApplyOrderId != null">
        root_apply_order_id,
      </if>
      <if test="parentApplyOrderId != null">
        parent_apply_order_id,
      </if>
      <if test="isChangeApply != null">
        is_change_apply,
      </if>
      <if test="changeReason != null">
        change_reason,
      </if>
      <if test="changeReasonDesc != null">
        change_reason_desc,
      </if>
      <if test="cancelReason != null">
        cancel_reason,
      </if>
      <if test="cancelReasonDesc != null">
        cancel_reason_desc,
      </if>
      <if test="subType != null">
        sub_type,
      </if>
      <if test="customFields != null">
        custom_fields,
      </if>
      <if test="applyReasonId != null">
        apply_reason_id,
      </if>
      <if test="changeReasonId != null">
        change_reason_id,
      </if>
      <if test="cancelReasonId != null">
        cancel_reason_id,
      </if>
      <if test="downloadLink != null">
        download_link,
      </if>
      <if test="voucherStatus != null">
        voucher_status,
      </if>
      <if test="voucherCode != null">
        voucher_code,
      </if>
      <if test="hastenStatu != null">
        hasten_statu,
      </if>
      <if test="hastenCreateTime != null">
        hasten_create_time,
      </if>
      <if test="returnTicket != null">
        return_ticket,
      </if>
      <if test="returnDownload != null">
        return_download,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="paymentStatus != null">
        payment_status,
      </if>
      <if test="paymentTime != null">
        payment_time,
      </if>
      <if test="snapContent != null">
        snap_content,
      </if>
      <if test="reimburseOrderId != null">
        reimburse_order_id,
      </if>
      <if test="payApplyStatus != null">
        pay_apply_status,
      </if>
      <if test="taskInfo != null">
        task_info,
      </if>
      <if test="sendStatus != null">
        send_status,
      </if>
      <if test="deleteStatus != null">
        delete_status,
      </if>
      <if test="flowProcessType != null">
        flow_process_type,
      </if>
      <if test="flowProcessId != null">
        flow_process_id,
      </if>
      <if test="clientType != null">
        client_type,
      </if>
      <if test="clientVersion != null">
        client_version,
      </if>
      <if test="costAttributionCode != null">
        cost_attribution_code,
      </if>
      <if test="isDing != null">
        is_ding,
      </if>
      <if test="metaInfo != null">
        meta_info,
      </if>
      <if test="formId != null">
        form_id,
      </if>
      <if test="createParamSnapshot != null">
        create_param_snapshot,
      </if>
      <if test="thridRelationNo != null">
        thrid_relation_no,
      </if>
      <if test="createVersion != null">
        create_version,
      </if>
      <if test="isAssociate != null">
        is_associate,
      </if>
      <if test="returnDate != null">
        return_date,
      </if>
      <if test="invoiceState != null">
        invoice_state,
      </if>
      <if test="invoiceReminder != null">
        invoice_reminder,
      </if>
      <if test="finalApproveTime != null">
        final_approve_time,
      </if>
      <if test="relationApplyId != null">
        relation_apply_id,
      </if>
      <if test="invoicePdfUrl != null">
        invoice_pdf_url,
      </if>
      <if test="applyPdfUrl != null">
        apply_pdf_url,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="meaningNo != null">
        #{meaningNo,jdbcType=VARCHAR},
      </if>
      <if test="billNo != null">
        #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=SMALLINT},
      </if>
      <if test="employeeId != null">
        #{employeeId,jdbcType=CHAR},
      </if>
      <if test="budget != null">
        #{budget,jdbcType=INTEGER},
      </if>
      <if test="state != null">
        #{state,jdbcType=INTEGER},
      </if>
      <if test="checkReason != null">
        #{checkReason,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reimburseTime != null">
        #{reimburseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reimburserId != null">
        #{reimburserId,jdbcType=VARCHAR},
      </if>
      <if test="reimburserName != null">
        #{reimburserName,jdbcType=VARCHAR},
      </if>
      <if test="reimburserSnapContent != null">
        #{reimburserSnapContent,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=CHAR},
      </if>
      <if test="mailOrderId != null">
        #{mailOrderId,jdbcType=CHAR},
      </if>
      <if test="travelPriceDetail != null">
        #{travelPriceDetail,jdbcType=VARCHAR},
      </if>
      <if test="applyReason != null">
        #{applyReason,jdbcType=VARCHAR},
      </if>
      <if test="applyReasonDesc != null">
        #{applyReasonDesc,jdbcType=VARCHAR},
      </if>
      <if test="cityRange != null">
        #{cityRange,jdbcType=VARCHAR},
      </if>
      <if test="timeRange != null">
        #{timeRange,jdbcType=VARCHAR},
      </if>
      <if test="currentLogId != null">
        #{currentLogId,jdbcType=BIGINT},
      </if>
      <if test="approverId != null">
        #{approverId,jdbcType=VARCHAR},
      </if>
      <if test="flowType != null">
        #{flowType,jdbcType=INTEGER},
      </if>
      <if test="flowCcType != null">
        #{flowCcType,jdbcType=INTEGER},
      </if>
      <if test="pastStatus != null">
        #{pastStatus,jdbcType=BIT},
      </if>
      <if test="pastDay != null">
        #{pastDay,jdbcType=BIGINT},
      </if>
      <if test="thirdId != null">
        #{thirdId,jdbcType=VARCHAR},
      </if>
      <if test="thirdRemark != null">
        #{thirdRemark,jdbcType=VARCHAR},
      </if>
      <if test="exceedBuyDesc != null">
        #{exceedBuyDesc,jdbcType=VARCHAR},
      </if>
      <if test="repulseDesc != null">
        #{repulseDesc,jdbcType=VARCHAR},
      </if>
      <if test="applyOrderType != null">
        #{applyOrderType,jdbcType=INTEGER},
      </if>
      <if test="applicantName != null">
        #{applicantName,jdbcType=VARCHAR},
      </if>
      <if test="exceedBuyDescContent != null">
        #{exceedBuyDescContent,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionId != null">
        #{costAttributionId,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionName != null">
        #{costAttributionName,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionCategory != null">
        #{costAttributionCategory,jdbcType=INTEGER},
      </if>
      <if test="realPrice != null">
        #{realPrice,jdbcType=DECIMAL},
      </if>
      <if test="exceedBuyType != null">
        #{exceedBuyType,jdbcType=INTEGER},
      </if>
      <if test="overtime != null">
        #{overtime,jdbcType=TIMESTAMP},
      </if>
      <if test="travelDay != null">
        #{travelDay,jdbcType=DECIMAL},
      </if>
      <if test="rootApplyOrderId != null">
        #{rootApplyOrderId,jdbcType=CHAR},
      </if>
      <if test="parentApplyOrderId != null">
        #{parentApplyOrderId,jdbcType=CHAR},
      </if>
      <if test="isChangeApply != null">
        #{isChangeApply,jdbcType=BIT},
      </if>
      <if test="changeReason != null">
        #{changeReason,jdbcType=VARCHAR},
      </if>
      <if test="changeReasonDesc != null">
        #{changeReasonDesc,jdbcType=VARCHAR},
      </if>
      <if test="cancelReason != null">
        #{cancelReason,jdbcType=VARCHAR},
      </if>
      <if test="cancelReasonDesc != null">
        #{cancelReasonDesc,jdbcType=VARCHAR},
      </if>
      <if test="subType != null">
        #{subType,jdbcType=INTEGER},
      </if>
      <if test="customFields != null">
        #{customFields,jdbcType=VARCHAR},
      </if>
      <if test="applyReasonId != null">
        #{applyReasonId,jdbcType=INTEGER},
      </if>
      <if test="changeReasonId != null">
        #{changeReasonId,jdbcType=INTEGER},
      </if>
      <if test="cancelReasonId != null">
        #{cancelReasonId,jdbcType=INTEGER},
      </if>
      <if test="downloadLink != null">
        #{downloadLink,jdbcType=VARCHAR},
      </if>
      <if test="voucherStatus != null">
        #{voucherStatus,jdbcType=INTEGER},
      </if>
      <if test="voucherCode != null">
        #{voucherCode,jdbcType=VARCHAR},
      </if>
      <if test="hastenStatu != null">
        #{hastenStatu,jdbcType=INTEGER},
      </if>
      <if test="hastenCreateTime != null">
        #{hastenCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="returnTicket != null">
        #{returnTicket,jdbcType=INTEGER},
      </if>
      <if test="returnDownload != null">
        #{returnDownload,jdbcType=INTEGER},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="paymentStatus != null">
        #{paymentStatus,jdbcType=INTEGER},
      </if>
      <if test="paymentTime != null">
        #{paymentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="snapContent != null">
        #{snapContent,jdbcType=VARCHAR},
      </if>
      <if test="reimburseOrderId != null">
        #{reimburseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="payApplyStatus != null">
        #{payApplyStatus,jdbcType=INTEGER},
      </if>
      <if test="taskInfo != null">
        #{taskInfo,jdbcType=VARCHAR},
      </if>
      <if test="sendStatus != null">
        #{sendStatus,jdbcType=INTEGER},
      </if>
      <if test="deleteStatus != null">
        #{deleteStatus,jdbcType=INTEGER},
      </if>
      <if test="flowProcessType != null">
        #{flowProcessType,jdbcType=INTEGER},
      </if>
      <if test="flowProcessId != null">
        #{flowProcessId,jdbcType=VARCHAR},
      </if>
      <if test="clientType != null">
        #{clientType,jdbcType=VARCHAR},
      </if>
      <if test="clientVersion != null">
        #{clientVersion,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionCode != null">
        #{costAttributionCode,jdbcType=VARCHAR},
      </if>
      <if test="isDing != null">
        #{isDing,jdbcType=TINYINT},
      </if>
      <if test="metaInfo != null">
        #{metaInfo,jdbcType=VARCHAR},
      </if>
      <if test="formId != null">
        #{formId,jdbcType=VARCHAR},
      </if>
      <if test="createParamSnapshot != null">
        #{createParamSnapshot,jdbcType=VARCHAR},
      </if>
      <if test="thridRelationNo != null">
        #{thridRelationNo,jdbcType=VARCHAR},
      </if>
      <if test="createVersion != null">
        #{createVersion,jdbcType=INTEGER},
      </if>
      <if test="isAssociate != null">
        #{isAssociate,jdbcType=BIT},
      </if>
      <if test="returnDate != null">
        #{returnDate,jdbcType=TIMESTAMP},
      </if>
      <if test="invoiceState != null">
        #{invoiceState,jdbcType=INTEGER},
      </if>
      <if test="invoiceReminder != null">
        #{invoiceReminder,jdbcType=BIT},
      </if>
      <if test="finalApproveTime != null">
        #{finalApproveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="relationApplyId != null">
        #{relationApplyId,jdbcType=VARCHAR},
      </if>
      <if test="invoicePdfUrl != null">
        #{invoicePdfUrl,jdbcType=VARCHAR},
      </if>
      <if test="applyPdfUrl != null">
        #{applyPdfUrl,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrderExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from apply_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.billNo != null">
        bill_no = #{record.billNo,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=SMALLINT},
      </if>
      <if test="record.employeeId != null">
        employee_id = #{record.employeeId,jdbcType=CHAR},
      </if>
      <if test="record.budget != null">
        budget = #{record.budget,jdbcType=INTEGER},
      </if>
      <if test="record.state != null">
        state = #{record.state,jdbcType=INTEGER},
      </if>
      <if test="record.checkReason != null">
        check_reason = #{record.checkReason,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reimburseTime != null">
        reimburse_time = #{record.reimburseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reimburserId != null">
        reimburser_id = #{record.reimburserId,jdbcType=VARCHAR},
      </if>
      <if test="record.reimburserName != null">
        reimburser_name = #{record.reimburserName,jdbcType=VARCHAR},
      </if>
      <if test="record.reimburserSnapContent != null">
        reimburser_snap_content = #{record.reimburserSnapContent,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=CHAR},
      </if>
      <if test="record.mailOrderId != null">
        mail_order_id = #{record.mailOrderId,jdbcType=CHAR},
      </if>
      <if test="record.travelPriceDetail != null">
        travel_price_detail = #{record.travelPriceDetail,jdbcType=VARCHAR},
      </if>
      <if test="record.applyReason != null">
        apply_reason = #{record.applyReason,jdbcType=VARCHAR},
      </if>
      <if test="record.applyReasonDesc != null">
        apply_reason_desc = #{record.applyReasonDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.cityRange != null">
        city_range = #{record.cityRange,jdbcType=VARCHAR},
      </if>
      <if test="record.timeRange != null">
        time_range = #{record.timeRange,jdbcType=VARCHAR},
      </if>
      <if test="record.currentLogId != null">
        current_log_id = #{record.currentLogId,jdbcType=BIGINT},
      </if>
      <if test="record.approverId != null">
        approver_id = #{record.approverId,jdbcType=VARCHAR},
      </if>
      <if test="record.flowType != null">
        flow_type = #{record.flowType,jdbcType=INTEGER},
      </if>
      <if test="record.flowCcType != null">
        flow_cc_type = #{record.flowCcType,jdbcType=INTEGER},
      </if>
      <if test="record.pastStatus != null">
        past_status = #{record.pastStatus,jdbcType=BIT},
      </if>
      <if test="record.pastDay != null">
        past_day = #{record.pastDay,jdbcType=BIGINT},
      </if>
      <if test="record.thirdId != null">
        third_id = #{record.thirdId,jdbcType=VARCHAR},
      </if>
      <if test="record.thirdRemark != null">
        third_remark = #{record.thirdRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.exceedBuyDesc != null">
        exceed_buy_desc = #{record.exceedBuyDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.repulseDesc != null">
        repulse_desc = #{record.repulseDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.applyOrderType != null">
        apply_order_type = #{record.applyOrderType,jdbcType=INTEGER},
      </if>
      <if test="record.applicantName != null">
        applicant_name = #{record.applicantName,jdbcType=VARCHAR},
      </if>
      <if test="record.exceedBuyDescContent != null">
        exceed_buy_desc_content = #{record.exceedBuyDescContent,jdbcType=VARCHAR},
      </if>
      <if test="record.costAttributionId != null">
        cost_attribution_id = #{record.costAttributionId,jdbcType=VARCHAR},
      </if>
      <if test="record.costAttributionName != null">
        cost_attribution_name = #{record.costAttributionName,jdbcType=VARCHAR},
      </if>
      <if test="record.costAttributionCategory != null">
        cost_attribution_category = #{record.costAttributionCategory,jdbcType=INTEGER},
      </if>
      <if test="record.realPrice != null">
        real_price = #{record.realPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.exceedBuyType != null">
        exceed_buy_type = #{record.exceedBuyType,jdbcType=INTEGER},
      </if>
      <if test="record.overtime != null">
        overtime = #{record.overtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.travelDay != null">
        travel_day = #{record.travelDay,jdbcType=DECIMAL},
      </if>
      <if test="record.rootApplyOrderId != null">
        root_apply_order_id = #{record.rootApplyOrderId,jdbcType=CHAR},
      </if>
      <if test="record.parentApplyOrderId != null">
        parent_apply_order_id = #{record.parentApplyOrderId,jdbcType=CHAR},
      </if>
      <if test="record.isChangeApply != null">
        is_change_apply = #{record.isChangeApply,jdbcType=BIT},
      </if>
      <if test="record.changeReason != null">
        change_reason = #{record.changeReason,jdbcType=VARCHAR},
      </if>
      <if test="record.changeReasonDesc != null">
        change_reason_desc = #{record.changeReasonDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.cancelReason != null">
        cancel_reason = #{record.cancelReason,jdbcType=VARCHAR},
      </if>
      <if test="record.cancelReasonDesc != null">
        cancel_reason_desc = #{record.cancelReasonDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.subType != null">
        sub_type = #{record.subType,jdbcType=INTEGER},
      </if>
      <if test="record.customFields != null">
        custom_fields = #{record.customFields,jdbcType=VARCHAR},
      </if>
      <if test="record.applyReasonId != null">
        apply_reason_id = #{record.applyReasonId,jdbcType=INTEGER},
      </if>
      <if test="record.changeReasonId != null">
        change_reason_id = #{record.changeReasonId,jdbcType=INTEGER},
      </if>
      <if test="record.cancelReasonId != null">
        cancel_reason_id = #{record.cancelReasonId,jdbcType=INTEGER},
      </if>
      <if test="record.downloadLink != null">
        download_link = #{record.downloadLink,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherStatus != null">
        voucher_status = #{record.voucherStatus,jdbcType=INTEGER},
      </if>
      <if test="record.voucherCode != null">
        voucher_code = #{record.voucherCode,jdbcType=VARCHAR},
      </if>
      <if test="record.hastenStatu != null">
        hasten_statu = #{record.hastenStatu,jdbcType=INTEGER},
      </if>
      <if test="record.hastenCreateTime != null">
        hasten_create_time = #{record.hastenCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.returnTicket != null">
        return_ticket = #{record.returnTicket,jdbcType=INTEGER},
      </if>
      <if test="record.returnDownload != null">
        return_download = #{record.returnDownload,jdbcType=INTEGER},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.paymentStatus != null">
        payment_status = #{record.paymentStatus,jdbcType=INTEGER},
      </if>
      <if test="record.paymentTime != null">
        payment_time = #{record.paymentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.snapContent != null">
        snap_content = #{record.snapContent,jdbcType=VARCHAR},
      </if>
      <if test="record.reimburseOrderId != null">
        reimburse_order_id = #{record.reimburseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.payApplyStatus != null">
        pay_apply_status = #{record.payApplyStatus,jdbcType=INTEGER},
      </if>
      <if test="record.taskInfo != null">
        task_info = #{record.taskInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.sendStatus != null">
        send_status = #{record.sendStatus,jdbcType=INTEGER},
      </if>
      <if test="record.deleteStatus != null">
        delete_status = #{record.deleteStatus,jdbcType=INTEGER},
      </if>
      <if test="record.flowProcessType != null">
        flow_process_type = #{record.flowProcessType,jdbcType=INTEGER},
      </if>
      <if test="record.flowProcessId != null">
        flow_process_id = #{record.flowProcessId,jdbcType=VARCHAR},
      </if>
      <if test="record.clientType != null">
        client_type = #{record.clientType,jdbcType=VARCHAR},
      </if>
      <if test="record.clientVersion != null">
        client_version = #{record.clientVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.costAttributionCode != null">
        cost_attribution_code = #{record.costAttributionCode,jdbcType=VARCHAR},
      </if>
      <if test="record.isDing != null">
        is_ding = #{record.isDing,jdbcType=TINYINT},
      </if>
      <if test="record.metaInfo != null">
        meta_info = #{record.metaInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.formId != null">
        form_id = #{record.formId,jdbcType=VARCHAR},
      </if>
      <if test="record.createParamSnapshot != null">
        create_param_snapshot = #{record.createParamSnapshot,jdbcType=VARCHAR},
      </if>
      <if test="record.thridRelationNo != null">
        thrid_relation_no = #{record.thridRelationNo,jdbcType=VARCHAR},
      </if>
      <if test="record.createVersion != null">
        create_version = #{record.createVersion,jdbcType=INTEGER},
      </if>
      <if test="record.isAssociate != null">
        is_associate = #{record.isAssociate,jdbcType=BIT},
      </if>
      <if test="record.returnDate != null">
        return_date = #{record.returnDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.invoiceState != null">
        invoice_state = #{record.invoiceState,jdbcType=INTEGER},
      </if>
      <if test="record.invoiceReminder != null">
        invoice_reminder = #{record.invoiceReminder,jdbcType=BIT},
      </if>
      <if test="record.finalApproveTime != null">
        final_approve_time = #{record.finalApproveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.relationApplyId != null">
        relation_apply_id = #{record.relationApplyId,jdbcType=VARCHAR},
      </if>
      <if test="record.invoicePdfUrl != null">
        invoice_pdf_url = #{record.invoicePdfUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.applyPdfUrl != null">
        apply_pdf_url = #{record.applyPdfUrl,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_order
    set id = #{record.id,jdbcType=CHAR},
      bill_no = #{record.billNo,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=SMALLINT},
      employee_id = #{record.employeeId,jdbcType=CHAR},
      budget = #{record.budget,jdbcType=INTEGER},
      state = #{record.state,jdbcType=INTEGER},
      check_reason = #{record.checkReason,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      reimburse_time = #{record.reimburseTime,jdbcType=TIMESTAMP},
      reimburser_id = #{record.reimburserId,jdbcType=VARCHAR},
      reimburser_name = #{record.reimburserName,jdbcType=VARCHAR},
      reimburser_snap_content = #{record.reimburserSnapContent,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      company_id = #{record.companyId,jdbcType=CHAR},
      mail_order_id = #{record.mailOrderId,jdbcType=CHAR},
      travel_price_detail = #{record.travelPriceDetail,jdbcType=VARCHAR},
      apply_reason = #{record.applyReason,jdbcType=VARCHAR},
      apply_reason_desc = #{record.applyReasonDesc,jdbcType=VARCHAR},
      city_range = #{record.cityRange,jdbcType=VARCHAR},
      time_range = #{record.timeRange,jdbcType=VARCHAR},
      current_log_id = #{record.currentLogId,jdbcType=BIGINT},
      approver_id = #{record.approverId,jdbcType=VARCHAR},
      flow_type = #{record.flowType,jdbcType=INTEGER},
      flow_cc_type = #{record.flowCcType,jdbcType=INTEGER},
      past_status = #{record.pastStatus,jdbcType=BIT},
      past_day = #{record.pastDay,jdbcType=BIGINT},
      third_id = #{record.thirdId,jdbcType=VARCHAR},
      third_remark = #{record.thirdRemark,jdbcType=VARCHAR},
      exceed_buy_desc = #{record.exceedBuyDesc,jdbcType=VARCHAR},
      repulse_desc = #{record.repulseDesc,jdbcType=VARCHAR},
      apply_order_type = #{record.applyOrderType,jdbcType=INTEGER},
      applicant_name = #{record.applicantName,jdbcType=VARCHAR},
      exceed_buy_desc_content = #{record.exceedBuyDescContent,jdbcType=VARCHAR},
      cost_attribution_id = #{record.costAttributionId,jdbcType=VARCHAR},
      cost_attribution_name = #{record.costAttributionName,jdbcType=VARCHAR},
      cost_attribution_category = #{record.costAttributionCategory,jdbcType=INTEGER},
      real_price = #{record.realPrice,jdbcType=DECIMAL},
      exceed_buy_type = #{record.exceedBuyType,jdbcType=INTEGER},
      overtime = #{record.overtime,jdbcType=TIMESTAMP},
      travel_day = #{record.travelDay,jdbcType=DECIMAL},
      root_apply_order_id = #{record.rootApplyOrderId,jdbcType=CHAR},
      parent_apply_order_id = #{record.parentApplyOrderId,jdbcType=CHAR},
      is_change_apply = #{record.isChangeApply,jdbcType=BIT},
      change_reason = #{record.changeReason,jdbcType=VARCHAR},
      change_reason_desc = #{record.changeReasonDesc,jdbcType=VARCHAR},
      cancel_reason = #{record.cancelReason,jdbcType=VARCHAR},
      cancel_reason_desc = #{record.cancelReasonDesc,jdbcType=VARCHAR},
      sub_type = #{record.subType,jdbcType=INTEGER},
      custom_fields = #{record.customFields,jdbcType=VARCHAR},
      apply_reason_id = #{record.applyReasonId,jdbcType=INTEGER},
      change_reason_id = #{record.changeReasonId,jdbcType=INTEGER},
      cancel_reason_id = #{record.cancelReasonId,jdbcType=INTEGER},
      download_link = #{record.downloadLink,jdbcType=VARCHAR},
      voucher_status = #{record.voucherStatus,jdbcType=INTEGER},
      voucher_code = #{record.voucherCode,jdbcType=VARCHAR},
      hasten_statu = #{record.hastenStatu,jdbcType=INTEGER},
      hasten_create_time = #{record.hastenCreateTime,jdbcType=TIMESTAMP},
      return_ticket = #{record.returnTicket,jdbcType=INTEGER},
      return_download = #{record.returnDownload,jdbcType=INTEGER},
      title = #{record.title,jdbcType=VARCHAR},
      payment_status = #{record.paymentStatus,jdbcType=INTEGER},
      payment_time = #{record.paymentTime,jdbcType=TIMESTAMP},
      snap_content = #{record.snapContent,jdbcType=VARCHAR},
      reimburse_order_id = #{record.reimburseOrderId,jdbcType=VARCHAR},
      pay_apply_status = #{record.payApplyStatus,jdbcType=INTEGER},
      task_info = #{record.taskInfo,jdbcType=VARCHAR},
      send_status = #{record.sendStatus,jdbcType=INTEGER},
      delete_status = #{record.deleteStatus,jdbcType=INTEGER},
      flow_process_type = #{record.flowProcessType,jdbcType=INTEGER},
      flow_process_id = #{record.flowProcessId,jdbcType=VARCHAR},
      client_type = #{record.clientType,jdbcType=VARCHAR},
      client_version = #{record.clientVersion,jdbcType=VARCHAR},
      cost_attribution_code = #{record.costAttributionCode,jdbcType=VARCHAR},
      is_ding = #{record.isDing,jdbcType=TINYINT},
      meta_info = #{record.metaInfo,jdbcType=VARCHAR},
      form_id = #{record.formId,jdbcType=VARCHAR},
      create_param_snapshot = #{record.createParamSnapshot,jdbcType=VARCHAR},
      thrid_relation_no = #{record.thridRelationNo,jdbcType=VARCHAR},
      create_version = #{record.createVersion,jdbcType=INTEGER},
      is_associate = #{record.isAssociate,jdbcType=BIT},
      return_date = #{record.returnDate,jdbcType=TIMESTAMP},
      invoice_state = #{record.invoiceState,jdbcType=INTEGER},
      invoice_reminder = #{record.invoiceReminder,jdbcType=BIT},
      final_approve_time = #{record.finalApproveTime,jdbcType=TIMESTAMP},
      relation_apply_id = #{record.relationApplyId,jdbcType=VARCHAR},
      invoice_pdf_url = #{record.invoicePdfUrl,jdbcType=VARCHAR},
      apply_pdf_url = #{record.applyPdfUrl,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrder">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_order
    <set>
      <if test="billNo != null">
        bill_no = #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=SMALLINT},
      </if>
      <if test="employeeId != null">
        employee_id = #{employeeId,jdbcType=CHAR},
      </if>
      <if test="budget != null">
        budget = #{budget,jdbcType=INTEGER},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=INTEGER},
      </if>
      <if test="checkReason != null">
        check_reason = #{checkReason,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reimburseTime != null">
        reimburse_time = #{reimburseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reimburserId != null">
        reimburser_id = #{reimburserId,jdbcType=VARCHAR},
      </if>
      <if test="reimburserName != null">
        reimburser_name = #{reimburserName,jdbcType=VARCHAR},
      </if>
      <if test="reimburserSnapContent != null">
        reimburser_snap_content = #{reimburserSnapContent,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=CHAR},
      </if>
      <if test="mailOrderId != null">
        mail_order_id = #{mailOrderId,jdbcType=CHAR},
      </if>
      <if test="travelPriceDetail != null">
        travel_price_detail = #{travelPriceDetail,jdbcType=VARCHAR},
      </if>
      <if test="applyReason != null">
        apply_reason = #{applyReason,jdbcType=VARCHAR},
      </if>
      <if test="applyReasonDesc != null">
        apply_reason_desc = #{applyReasonDesc,jdbcType=VARCHAR},
      </if>
      <if test="cityRange != null">
        city_range = #{cityRange,jdbcType=VARCHAR},
      </if>
      <if test="timeRange != null">
        time_range = #{timeRange,jdbcType=VARCHAR},
      </if>
      <if test="currentLogId != null">
        current_log_id = #{currentLogId,jdbcType=BIGINT},
      </if>
      <if test="approverId != null">
        approver_id = #{approverId,jdbcType=VARCHAR},
      </if>
      <if test="flowType != null">
        flow_type = #{flowType,jdbcType=INTEGER},
      </if>
      <if test="flowCcType != null">
        flow_cc_type = #{flowCcType,jdbcType=INTEGER},
      </if>
      <if test="pastStatus != null">
        past_status = #{pastStatus,jdbcType=BIT},
      </if>
      <if test="pastDay != null">
        past_day = #{pastDay,jdbcType=BIGINT},
      </if>
      <if test="thirdId != null">
        third_id = #{thirdId,jdbcType=VARCHAR},
      </if>
      <if test="thirdRemark != null">
        third_remark = #{thirdRemark,jdbcType=VARCHAR},
      </if>
      <if test="exceedBuyDesc != null">
        exceed_buy_desc = #{exceedBuyDesc,jdbcType=VARCHAR},
      </if>
      <if test="repulseDesc != null">
        repulse_desc = #{repulseDesc,jdbcType=VARCHAR},
      </if>
      <if test="applyOrderType != null">
        apply_order_type = #{applyOrderType,jdbcType=INTEGER},
      </if>
      <if test="applicantName != null">
        applicant_name = #{applicantName,jdbcType=VARCHAR},
      </if>
      <if test="exceedBuyDescContent != null">
        exceed_buy_desc_content = #{exceedBuyDescContent,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionId != null">
        cost_attribution_id = #{costAttributionId,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionName != null">
        cost_attribution_name = #{costAttributionName,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionCategory != null">
        cost_attribution_category = #{costAttributionCategory,jdbcType=INTEGER},
      </if>
      <if test="realPrice != null">
        real_price = #{realPrice,jdbcType=DECIMAL},
      </if>
      <if test="exceedBuyType != null">
        exceed_buy_type = #{exceedBuyType,jdbcType=INTEGER},
      </if>
      <if test="overtime != null">
        overtime = #{overtime,jdbcType=TIMESTAMP},
      </if>
      <if test="travelDay != null">
        travel_day = #{travelDay,jdbcType=DECIMAL},
      </if>
      <if test="rootApplyOrderId != null">
        root_apply_order_id = #{rootApplyOrderId,jdbcType=CHAR},
      </if>
      <if test="parentApplyOrderId != null">
        parent_apply_order_id = #{parentApplyOrderId,jdbcType=CHAR},
      </if>
      <if test="isChangeApply != null">
        is_change_apply = #{isChangeApply,jdbcType=BIT},
      </if>
      <if test="changeReason != null">
        change_reason = #{changeReason,jdbcType=VARCHAR},
      </if>
      <if test="changeReasonDesc != null">
        change_reason_desc = #{changeReasonDesc,jdbcType=VARCHAR},
      </if>
      <if test="cancelReason != null">
        cancel_reason = #{cancelReason,jdbcType=VARCHAR},
      </if>
      <if test="cancelReasonDesc != null">
        cancel_reason_desc = #{cancelReasonDesc,jdbcType=VARCHAR},
      </if>
      <if test="subType != null">
        sub_type = #{subType,jdbcType=INTEGER},
      </if>
      <if test="customFields != null">
        custom_fields = #{customFields,jdbcType=VARCHAR},
      </if>
      <if test="applyReasonId != null">
        apply_reason_id = #{applyReasonId,jdbcType=INTEGER},
      </if>
      <if test="changeReasonId != null">
        change_reason_id = #{changeReasonId,jdbcType=INTEGER},
      </if>
      <if test="cancelReasonId != null">
        cancel_reason_id = #{cancelReasonId,jdbcType=INTEGER},
      </if>
      <if test="downloadLink != null">
        download_link = #{downloadLink,jdbcType=VARCHAR},
      </if>
      <if test="voucherStatus != null">
        voucher_status = #{voucherStatus,jdbcType=INTEGER},
      </if>
      <if test="voucherCode != null">
        voucher_code = #{voucherCode,jdbcType=VARCHAR},
      </if>
      <if test="hastenStatu != null">
        hasten_statu = #{hastenStatu,jdbcType=INTEGER},
      </if>
      <if test="hastenCreateTime != null">
        hasten_create_time = #{hastenCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="returnTicket != null">
        return_ticket = #{returnTicket,jdbcType=INTEGER},
      </if>
      <if test="returnDownload != null">
        return_download = #{returnDownload,jdbcType=INTEGER},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="paymentStatus != null">
        payment_status = #{paymentStatus,jdbcType=INTEGER},
      </if>
      <if test="paymentTime != null">
        payment_time = #{paymentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="snapContent != null">
        snap_content = #{snapContent,jdbcType=VARCHAR},
      </if>
      <if test="reimburseOrderId != null">
        reimburse_order_id = #{reimburseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="payApplyStatus != null">
        pay_apply_status = #{payApplyStatus,jdbcType=INTEGER},
      </if>
      <if test="taskInfo != null">
        task_info = #{taskInfo,jdbcType=VARCHAR},
      </if>
      <if test="sendStatus != null">
        send_status = #{sendStatus,jdbcType=INTEGER},
      </if>
      <if test="deleteStatus != null">
        delete_status = #{deleteStatus,jdbcType=INTEGER},
      </if>
      <if test="flowProcessType != null">
        flow_process_type = #{flowProcessType,jdbcType=INTEGER},
      </if>
      <if test="flowProcessId != null">
        flow_process_id = #{flowProcessId,jdbcType=VARCHAR},
      </if>
      <if test="clientType != null">
        client_type = #{clientType,jdbcType=VARCHAR},
      </if>
      <if test="clientVersion != null">
        client_version = #{clientVersion,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionCode != null">
        cost_attribution_code = #{costAttributionCode,jdbcType=VARCHAR},
      </if>
      <if test="isDing != null">
        is_ding = #{isDing,jdbcType=TINYINT},
      </if>
      <if test="metaInfo != null">
        meta_info = #{metaInfo,jdbcType=VARCHAR},
      </if>
      <if test="formId != null">
        form_id = #{formId,jdbcType=VARCHAR},
      </if>
      <if test="createParamSnapshot != null">
        create_param_snapshot = #{createParamSnapshot,jdbcType=VARCHAR},
      </if>
      <if test="thridRelationNo != null">
        thrid_relation_no = #{thridRelationNo,jdbcType=VARCHAR},
      </if>
      <if test="createVersion != null">
        create_version = #{createVersion,jdbcType=INTEGER},
      </if>
      <if test="isAssociate != null">
        is_associate = #{isAssociate,jdbcType=BIT},
      </if>
      <if test="returnDate != null">
        return_date = #{returnDate,jdbcType=TIMESTAMP},
      </if>
      <if test="invoiceState != null">
        invoice_state = #{invoiceState,jdbcType=INTEGER},
      </if>
      <if test="invoiceReminder != null">
        invoice_reminder = #{invoiceReminder,jdbcType=BIT},
      </if>
      <if test="finalApproveTime != null">
        final_approve_time = #{finalApproveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="relationApplyId != null">
        relation_apply_id = #{relationApplyId,jdbcType=VARCHAR},
      </if>
      <if test="invoicePdfUrl != null">
        invoice_pdf_url = #{invoicePdfUrl,jdbcType=VARCHAR},
      </if>
      <if test="applyPdfUrl != null">
        apply_pdf_url = #{applyPdfUrl,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrder">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_order
    set bill_no = #{billNo,jdbcType=VARCHAR},
      type = #{type,jdbcType=SMALLINT},
      employee_id = #{employeeId,jdbcType=CHAR},
      budget = #{budget,jdbcType=INTEGER},
      state = #{state,jdbcType=INTEGER},
      check_reason = #{checkReason,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      reimburse_time = #{reimburseTime,jdbcType=TIMESTAMP},
      reimburser_id = #{reimburserId,jdbcType=VARCHAR},
      reimburser_name = #{reimburserName,jdbcType=VARCHAR},
      reimburser_snap_content = #{reimburserSnapContent,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=CHAR},
      mail_order_id = #{mailOrderId,jdbcType=CHAR},
      travel_price_detail = #{travelPriceDetail,jdbcType=VARCHAR},
      apply_reason = #{applyReason,jdbcType=VARCHAR},
      apply_reason_desc = #{applyReasonDesc,jdbcType=VARCHAR},
      city_range = #{cityRange,jdbcType=VARCHAR},
      time_range = #{timeRange,jdbcType=VARCHAR},
      current_log_id = #{currentLogId,jdbcType=BIGINT},
      approver_id = #{approverId,jdbcType=VARCHAR},
      flow_type = #{flowType,jdbcType=INTEGER},
      flow_cc_type = #{flowCcType,jdbcType=INTEGER},
      past_status = #{pastStatus,jdbcType=BIT},
      past_day = #{pastDay,jdbcType=BIGINT},
      third_id = #{thirdId,jdbcType=VARCHAR},
      third_remark = #{thirdRemark,jdbcType=VARCHAR},
      exceed_buy_desc = #{exceedBuyDesc,jdbcType=VARCHAR},
      repulse_desc = #{repulseDesc,jdbcType=VARCHAR},
      apply_order_type = #{applyOrderType,jdbcType=INTEGER},
      applicant_name = #{applicantName,jdbcType=VARCHAR},
      exceed_buy_desc_content = #{exceedBuyDescContent,jdbcType=VARCHAR},
      cost_attribution_id = #{costAttributionId,jdbcType=VARCHAR},
      cost_attribution_name = #{costAttributionName,jdbcType=VARCHAR},
      cost_attribution_category = #{costAttributionCategory,jdbcType=INTEGER},
      real_price = #{realPrice,jdbcType=DECIMAL},
      exceed_buy_type = #{exceedBuyType,jdbcType=INTEGER},
      overtime = #{overtime,jdbcType=TIMESTAMP},
      travel_day = #{travelDay,jdbcType=DECIMAL},
      root_apply_order_id = #{rootApplyOrderId,jdbcType=CHAR},
      parent_apply_order_id = #{parentApplyOrderId,jdbcType=CHAR},
      is_change_apply = #{isChangeApply,jdbcType=BIT},
      change_reason = #{changeReason,jdbcType=VARCHAR},
      change_reason_desc = #{changeReasonDesc,jdbcType=VARCHAR},
      cancel_reason = #{cancelReason,jdbcType=VARCHAR},
      cancel_reason_desc = #{cancelReasonDesc,jdbcType=VARCHAR},
      sub_type = #{subType,jdbcType=INTEGER},
      custom_fields = #{customFields,jdbcType=VARCHAR},
      apply_reason_id = #{applyReasonId,jdbcType=INTEGER},
      change_reason_id = #{changeReasonId,jdbcType=INTEGER},
      cancel_reason_id = #{cancelReasonId,jdbcType=INTEGER},
      download_link = #{downloadLink,jdbcType=VARCHAR},
      voucher_status = #{voucherStatus,jdbcType=INTEGER},
      voucher_code = #{voucherCode,jdbcType=VARCHAR},
      hasten_statu = #{hastenStatu,jdbcType=INTEGER},
      hasten_create_time = #{hastenCreateTime,jdbcType=TIMESTAMP},
      return_ticket = #{returnTicket,jdbcType=INTEGER},
      return_download = #{returnDownload,jdbcType=INTEGER},
      title = #{title,jdbcType=VARCHAR},
      payment_status = #{paymentStatus,jdbcType=INTEGER},
      payment_time = #{paymentTime,jdbcType=TIMESTAMP},
      snap_content = #{snapContent,jdbcType=VARCHAR},
      reimburse_order_id = #{reimburseOrderId,jdbcType=VARCHAR},
      pay_apply_status = #{payApplyStatus,jdbcType=INTEGER},
      task_info = #{taskInfo,jdbcType=VARCHAR},
      send_status = #{sendStatus,jdbcType=INTEGER},
      delete_status = #{deleteStatus,jdbcType=INTEGER},
      flow_process_type = #{flowProcessType,jdbcType=INTEGER},
      flow_process_id = #{flowProcessId,jdbcType=VARCHAR},
      client_type = #{clientType,jdbcType=VARCHAR},
      client_version = #{clientVersion,jdbcType=VARCHAR},
      cost_attribution_code = #{costAttributionCode,jdbcType=VARCHAR},
      is_ding = #{isDing,jdbcType=TINYINT},
      meta_info = #{metaInfo,jdbcType=VARCHAR},
      form_id = #{formId,jdbcType=VARCHAR},
      create_param_snapshot = #{createParamSnapshot,jdbcType=VARCHAR},
      thrid_relation_no = #{thridRelationNo,jdbcType=VARCHAR},
      create_version = #{createVersion,jdbcType=INTEGER},
      is_associate = #{isAssociate,jdbcType=BIT},
      return_date = #{returnDate,jdbcType=TIMESTAMP},
      invoice_state = #{invoiceState,jdbcType=INTEGER},
      invoice_reminder = #{invoiceReminder,jdbcType=BIT},
      final_approve_time = #{finalApproveTime,jdbcType=TIMESTAMP},
      relation_apply_id = #{relationApplyId,jdbcType=VARCHAR},
      invoice_pdf_url = #{invoicePdfUrl,jdbcType=VARCHAR},
      apply_pdf_url = #{applyPdfUrl,jdbcType=VARCHAR}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>