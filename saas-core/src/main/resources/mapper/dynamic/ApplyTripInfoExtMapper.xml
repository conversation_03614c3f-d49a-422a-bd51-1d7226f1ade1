<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.dynamic.ApplyTripInfoExtMapper">
    <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.ApplyTripInfo"
               extends="com.fenbeitong.saas.core.dao.dynamic.ApplyTripInfoMapper.BaseResultMap">
    </resultMap>

    <select id="selectListByApplyOrderId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select *
        from apply_trip_info
        where
        apply_order_id = #{applyOrderId,jdbcType=CHAR}
        order by id asc

  </select>

    <select id="selectListByApplyOrderIds" resultMap="BaseResultMap" parameterType="java.util.List">
        select *
        from apply_trip_info
        where
        apply_order_id in
        <foreach collection="list" item="applyOrderId" index="index"
                 open="(" close=")" separator=",">
            #{applyOrderId}
        </foreach>
        order by apply_order_id desc, id asc

    </select>

    <select id="selectListByApplyOrderIdAndTripType" resultMap="BaseResultMap">
        select *
        from apply_trip_info
        where
        apply_order_id = #{applyOrderId,jdbcType=CHAR}
        and type = #{tripType,jdbcType=INTEGER}
        order by id asc

    </select>
    <delete id="deleteByApplyOrderId" parameterType="java.lang.String">
        delete from apply_trip_info
        where apply_order_id = #{applyOrderId,jdbcType=CHAR}
    </delete>

    <update id="setStateAndApplicateLogId">
        update apply_trip_info
        set state = #{state,jdbcType=INTEGER},
        trip_applicate_current_id = #{applyTripApplicateId,jdbcType=VARCHAR},
        update_time = #{time,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=CHAR}
    </update>

    <select id="selectApplicatedTrip" resultMap="BaseResultMap">
        select t.*
        from apply_trip_info t,apply_trip_applicate app
        where
        t.apply_order_id = #{applyId,jdbcType=CHAR}
        and t.trip_applicate_current_id = app.id
        and app.order_id = #{orderId,jdbcType=VARCHAR}
        and app.order_product_type=#{orderProductType,jdbcType=INTEGER}
    </select>

    <select id="queryApplyTripListByApplyOrderId" resultMap="BaseResultMap">
        select *
        from apply_trip_info
        where
        apply_order_id = #{applyId,jdbcType=CHAR}
        and state = 1
    </select>

    <select id="queryApplyTripInfo" resultMap="BaseResultMap">
        select *
        from apply_trip_info a,apply_trip_applicate b
        where
        a.id = b.apply_trip_id
        and b.order_id = #{orderId,jdbcType=VARCHAR}
        and b.apply_id = #{applyId,jdbcType=CHAR}
        and b.action = 1
    </select>

    <select id="queryAllTripListByApplyOrderId" resultMap="BaseResultMap">
        select *
        from apply_trip_info
        where
        apply_order_id = #{applyId,jdbcType=CHAR}
        order by id asc
    </select>

    <select id="queryApplyTripInfoByCompanyId" resultMap="BaseResultMap">
        select * from apply_trip_info at
        left join apply_order ao
        on at.apply_order_id = ao.id
        where at.apply_order_id= #{applyId,jdbcType=CHAR}
        and at.id= #{id,jdbcType=CHAR}
        and ao.employee_id= #{userId,jdbcType=CHAR}
        and ao.company_id = #{companyId,jdbcType=CHAR}
        and at.state=1
        and ao.delete_status=0
    </select>

    <select id="queryApplyTripInfoByEmployeeId" resultMap="BaseResultMap">
        select at.* from apply_trip_info at
        left join apply_order ao
        on at.apply_order_id = ao.id
        where ao.employee_id= #{userId,jdbcType=CHAR}
          and ao.company_id = #{companyId,jdbcType=CHAR}
          and ao.past_status =0
          and at.state=1
          and ao.delete_status=0
    </select>

    <select id="getTaxiValidApplyTripList" resultMap="BaseResultMap">
        select t.*
        from apply_order a,apply_trip_info t
        where a.id = t.apply_order_id
        and a.state = 4
        and a.apply_order_type = 1
        and a.employee_id = #{userId,jdbcType=CHAR}
        and a.company_id = #{companyId,jdbcType=CHAR}
        and t.type = 3
        and t.start_city_id IN
        <foreach collection="startCityIds" item="startCityId" index="index"
                 open="(" close=")" separator=",">
            #{startCityId}
        </foreach>
        and t.end_time > #{timeMinusOneDay,jdbcType=TIMESTAMP}
        and t.start_time &lt;= #{time,jdbcType = TIMESTAMP}
        and t.state = 1
        and a.delete_status=0
    </select>

    <select id="getApplyTripList" resultMap="BaseResultMap">
        select * from apply_trip_info where apply_order_id in
        <foreach collection="list" item="applyOrderId" index="index"
                 open="(" close=")" separator=",">
            #{applyOrderId}
        </foreach>
        and type in
        <foreach collection="bizTypeList" item="bizType" index="index" open="(" close=")" separator=",">
          #{bizType}
        </foreach>
        order by create_time desc
    </select>

    <select id="getApplyTripListByApplyIdAndType" resultMap="BaseResultMap">
        select * from apply_trip_info where apply_order_id in
        <foreach collection="list" item="applyOrderId" index="index"
                 open="(" close=")" separator=",">
            #{applyOrderId}
        </foreach>
        and (type = #{bizType}
        or content like #{likeString})
        order by create_time
    </select>
</mapper>