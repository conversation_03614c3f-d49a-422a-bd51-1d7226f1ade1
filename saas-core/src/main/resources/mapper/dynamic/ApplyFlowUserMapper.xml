<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.dynamic.ApplyFlowUserMapper">
    <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.ApplyFlowUser">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Tue Apr 25 15:19:23 CST 2017.
        -->
        <id column="id" jdbcType="CHAR" property="id"/>
        <result column="apply_flow_id" jdbcType="CHAR" property="applyFlowId"/>
        <result column="company_id" jdbcType="VARCHAR" property="companyId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="apply_order_id" jdbcType="VARCHAR" property="applyOrderId"/>
    </resultMap>
    <sql id="columnList">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Tue Apr 25 15:19:23 CST 2017.
        -->
        id, apply_flow_id, company_id, create_time, update_time, user_id, apply_order_id
    </sql>
    <insert id="insert" parameterType="com.fenbeitong.saas.core.model.saas.ApplyFlowUser">
        insert into apply_flow_user (
        <include refid="columnList"></include>
        )values (#{id,jdbcType=CHAR}, #{applyFlowId,jdbcType=CHAR},
        #{companyId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP}, #{userId,jdbcType=VARCHAR},
        #{applyOrderId,jdbcType=VARCHAR})
    </insert>
    <delete id="deleteByPrimaryKey">
    delete from apply_flow_user
    where id = #{id,jdbcType=CHAR}
  </delete>
    <select id="selectByApplyOrderId" resultMap="BaseResultMap">
        select
        <include refid="columnList"></include>
        from apply_flow_user
        where apply_order_id = #{applyId}
    </select>

</mapper>