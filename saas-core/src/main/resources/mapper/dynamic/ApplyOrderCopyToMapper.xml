<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.dynamic.ApplyOrderCopyToMapper">
    <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.ApplyOrderCopyTo">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Jul 24 20:27:37 CST 2017.
        -->
        <id column="id" jdbcType="CHAR" property="id"/>
        <result column="apply_order_id" jdbcType="CHAR" property="applyOrderId"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="item_type" jdbcType="INTEGER" property="itemType"/>
        <result column="item_id" jdbcType="VARCHAR" property="itemId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Jul 24 20:27:37 CST 2017.
        -->
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Jul 24 20:27:37 CST 2017.
        -->
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Jul 24 20:27:37 CST 2017.
        -->
        id, apply_order_id, sort, item_type, item_id, create_time, is_delete, user_id
    </sql>
    <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrderCopyToExample"
            resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Jul 24 20:27:37 CST 2017.
        -->
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from apply_order_copy_to
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrderCopyToExample">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Jul 24 20:27:37 CST 2017.
        -->
        delete from apply_order_copy_to
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrderCopyTo">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Jul 24 20:27:37 CST 2017.
        -->
        insert into apply_order_copy_to
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="applyOrderId != null">
                apply_order_id,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="itemType != null">
                item_type,
            </if>
            <if test="itemId != null">
                item_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="userId != null">
                user_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=CHAR},
            </if>
            <if test="applyOrderId != null">
                #{applyOrderId,jdbcType=CHAR},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=INTEGER},
            </if>
            <if test="itemType != null">
                #{itemType,jdbcType=INTEGER},
            </if>
            <if test="itemId != null">
                #{itemId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrderCopyToExample"
            resultType="java.lang.Long">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Jul 24 20:27:37 CST 2017.
        -->
        select count(*) from apply_order_copy_to
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Jul 24 20:27:37 CST 2017.
        -->
        update apply_order_copy_to
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=CHAR},
            </if>
            <if test="record.applyOrderId != null">
                apply_order_id = #{record.applyOrderId,jdbcType=CHAR},
            </if>
            <if test="record.sort != null">
                sort = #{record.sort,jdbcType=INTEGER},
            </if>
            <if test="record.itemType != null">
                item_type = #{record.itemType,jdbcType=INTEGER},
            </if>
            <if test="record.itemId != null">
                item_id = #{record.itemId,jdbcType=VARCHAR},
            </if>
            <if test="record.createTime != null">
                create_time = #{record.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.isDelete != null">
                is_delete = #{record.isDelete,jdbcType=INTEGER},
            </if>
            <if test="record.userId != null">
                user_id = #{record.userId,jdbcType=VARCHAR},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Jul 24 20:27:37 CST 2017.
        -->
        update apply_order_copy_to
        set id = #{record.id,jdbcType=CHAR},
        apply_order_id = #{record.applyOrderId,jdbcType=CHAR},
        sort = #{record.sort,jdbcType=INTEGER},
        item_type = #{record.itemType,jdbcType=INTEGER},
        item_id = #{record.itemId,jdbcType=VARCHAR},
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
        is_delete = #{record.isDelete,jdbcType=INTEGER},
        user_id = #{record.userId,jdbcType=VARCHAR}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrderCopyTo">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Jul 24 20:27:37 CST 2017.
        -->
        update apply_order_copy_to
        <set>
            <if test="applyOrderId != null">
                apply_order_id = #{applyOrderId,jdbcType=CHAR},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=INTEGER},
            </if>
            <if test="itemType != null">
                item_type = #{itemType,jdbcType=INTEGER},
            </if>
            <if test="itemId != null">
                item_id = #{itemId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=CHAR}
    </update>

    <select id="queryApplyOrderCCByApplyOrderId" resultMap="BaseResultMap">
    select *
    from apply_order_copy_to
    where apply_order_id = #{applyOrderId,jdbcType=CHAR}
    order by sort asc
  </select>

    <delete id="deleteCCByApplyOrderId">
  delete from apply_order_copy_to
  where apply_order_id = #{applyOrderId,jdbcType=CHAR}
</delete>

    <select id="queryCcUnreadCountByUserId" resultType="java.lang.Integer">
        select count(*)
        from apply_order_copy_to t1
	    inner join apply_order t2
	    on t1.apply_order_id=t2.id
        where t1.user_id = #{userId,jdbcType=VARCHAR}
        and t1.read = false
        and t2.company_id = #{companyId,jdbcType=VARCHAR}
        and t2.type != 5
    </select>

    <update id="updateReadStatusByUserId" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrderCopyTo">
        update apply_order_copy_to
        <set>
            read = true
        </set>
        where user_id = #{userId,jdbcType=VARCHAR}
        and read = false
    </update>
</mapper>