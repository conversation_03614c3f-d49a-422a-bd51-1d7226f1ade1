<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saasplus.CustformReimbursementConfigMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saasplus.CustformReimbursementConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="form_id" jdbcType="VARCHAR" property="formId" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="reimbursement_id" jdbcType="VARCHAR" property="reimbursementId" />
    <result column="is_allowed_reimbursement" jdbcType="INTEGER" property="isAllowedReimbursement" />
    <result column="is_trip_end_reimbursement" jdbcType="INTEGER" property="isTripEndReimbursement" />
    <result column="is_trip_end_over_reimbursement" jdbcType="INTEGER" property="isTripEndOverReimbursement" />
    <result column="trip_end_over_reimbursement_day" jdbcType="INTEGER" property="tripEndOverReimbursementDay" />
    <result column="is_auto_reimbursement_draft" jdbcType="INTEGER" property="isAutoReimbursementDraft" />
    <result column="trip_end_auto_reimbursement_draft_day" jdbcType="INTEGER" property="tripEndAutoReimbursementDraftDay" />
    <result column="is_unreimbursed_create_apply" jdbcType="INTEGER" property="isUnreimbursedCreateApply" />
    <result column="trip_end_over_create_day" jdbcType="INTEGER" property="tripEndOverCreateDay" />
    <result column="control_intensity" jdbcType="INTEGER" property="controlIntensity" />
    <result column="control_msg" jdbcType="VARCHAR" property="controlMsg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="effective_date" jdbcType="TIMESTAMP" property="effectiveDate" />
    <result column="is_effective_date" jdbcType="INTEGER" property="isEffectiveDate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, form_id, company_id, reimbursement_id, is_allowed_reimbursement, is_trip_end_reimbursement, 
    is_trip_end_over_reimbursement, trip_end_over_reimbursement_day, is_auto_reimbursement_draft, 
    trip_end_auto_reimbursement_draft_day, is_unreimbursed_create_apply, trip_end_over_create_day, 
    control_intensity, control_msg, create_time, update_time, effective_date, is_effective_date
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.CustformReimbursementConfigExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from custform_reimbursement_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from custform_reimbursement_config
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from custform_reimbursement_config
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.CustformReimbursementConfigExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from custform_reimbursement_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.saasplus.CustformReimbursementConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into custform_reimbursement_config (id, form_id, company_id, 
      reimbursement_id, is_allowed_reimbursement, 
      is_trip_end_reimbursement, is_trip_end_over_reimbursement, 
      trip_end_over_reimbursement_day, is_auto_reimbursement_draft, 
      trip_end_auto_reimbursement_draft_day, is_unreimbursed_create_apply, 
      trip_end_over_create_day, control_intensity, 
      control_msg, create_time, update_time, 
      effective_date, is_effective_date)
    values (#{id,jdbcType=VARCHAR}, #{formId,jdbcType=VARCHAR}, #{companyId,jdbcType=VARCHAR}, 
      #{reimbursementId,jdbcType=VARCHAR}, #{isAllowedReimbursement,jdbcType=INTEGER}, 
      #{isTripEndReimbursement,jdbcType=INTEGER}, #{isTripEndOverReimbursement,jdbcType=INTEGER}, 
      #{tripEndOverReimbursementDay,jdbcType=INTEGER}, #{isAutoReimbursementDraft,jdbcType=INTEGER}, 
      #{tripEndAutoReimbursementDraftDay,jdbcType=INTEGER}, #{isUnreimbursedCreateApply,jdbcType=INTEGER}, 
      #{tripEndOverCreateDay,jdbcType=INTEGER}, #{controlIntensity,jdbcType=INTEGER}, 
      #{controlMsg,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{effectiveDate,jdbcType=TIMESTAMP}, #{isEffectiveDate,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.saasplus.CustformReimbursementConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into custform_reimbursement_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="formId != null">
        form_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="reimbursementId != null">
        reimbursement_id,
      </if>
      <if test="isAllowedReimbursement != null">
        is_allowed_reimbursement,
      </if>
      <if test="isTripEndReimbursement != null">
        is_trip_end_reimbursement,
      </if>
      <if test="isTripEndOverReimbursement != null">
        is_trip_end_over_reimbursement,
      </if>
      <if test="tripEndOverReimbursementDay != null">
        trip_end_over_reimbursement_day,
      </if>
      <if test="isAutoReimbursementDraft != null">
        is_auto_reimbursement_draft,
      </if>
      <if test="tripEndAutoReimbursementDraftDay != null">
        trip_end_auto_reimbursement_draft_day,
      </if>
      <if test="isUnreimbursedCreateApply != null">
        is_unreimbursed_create_apply,
      </if>
      <if test="tripEndOverCreateDay != null">
        trip_end_over_create_day,
      </if>
      <if test="controlIntensity != null">
        control_intensity,
      </if>
      <if test="controlMsg != null">
        control_msg,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="effectiveDate != null">
        effective_date,
      </if>
      <if test="isEffectiveDate != null">
        is_effective_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="formId != null">
        #{formId,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="reimbursementId != null">
        #{reimbursementId,jdbcType=VARCHAR},
      </if>
      <if test="isAllowedReimbursement != null">
        #{isAllowedReimbursement,jdbcType=INTEGER},
      </if>
      <if test="isTripEndReimbursement != null">
        #{isTripEndReimbursement,jdbcType=INTEGER},
      </if>
      <if test="isTripEndOverReimbursement != null">
        #{isTripEndOverReimbursement,jdbcType=INTEGER},
      </if>
      <if test="tripEndOverReimbursementDay != null">
        #{tripEndOverReimbursementDay,jdbcType=INTEGER},
      </if>
      <if test="isAutoReimbursementDraft != null">
        #{isAutoReimbursementDraft,jdbcType=INTEGER},
      </if>
      <if test="tripEndAutoReimbursementDraftDay != null">
        #{tripEndAutoReimbursementDraftDay,jdbcType=INTEGER},
      </if>
      <if test="isUnreimbursedCreateApply != null">
        #{isUnreimbursedCreateApply,jdbcType=INTEGER},
      </if>
      <if test="tripEndOverCreateDay != null">
        #{tripEndOverCreateDay,jdbcType=INTEGER},
      </if>
      <if test="controlIntensity != null">
        #{controlIntensity,jdbcType=INTEGER},
      </if>
      <if test="controlMsg != null">
        #{controlMsg,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="effectiveDate != null">
        #{effectiveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isEffectiveDate != null">
        #{isEffectiveDate,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.CustformReimbursementConfigExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from custform_reimbursement_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update custform_reimbursement_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.formId != null">
        form_id = #{record.formId,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.reimbursementId != null">
        reimbursement_id = #{record.reimbursementId,jdbcType=VARCHAR},
      </if>
      <if test="record.isAllowedReimbursement != null">
        is_allowed_reimbursement = #{record.isAllowedReimbursement,jdbcType=INTEGER},
      </if>
      <if test="record.isTripEndReimbursement != null">
        is_trip_end_reimbursement = #{record.isTripEndReimbursement,jdbcType=INTEGER},
      </if>
      <if test="record.isTripEndOverReimbursement != null">
        is_trip_end_over_reimbursement = #{record.isTripEndOverReimbursement,jdbcType=INTEGER},
      </if>
      <if test="record.tripEndOverReimbursementDay != null">
        trip_end_over_reimbursement_day = #{record.tripEndOverReimbursementDay,jdbcType=INTEGER},
      </if>
      <if test="record.isAutoReimbursementDraft != null">
        is_auto_reimbursement_draft = #{record.isAutoReimbursementDraft,jdbcType=INTEGER},
      </if>
      <if test="record.tripEndAutoReimbursementDraftDay != null">
        trip_end_auto_reimbursement_draft_day = #{record.tripEndAutoReimbursementDraftDay,jdbcType=INTEGER},
      </if>
      <if test="record.isUnreimbursedCreateApply != null">
        is_unreimbursed_create_apply = #{record.isUnreimbursedCreateApply,jdbcType=INTEGER},
      </if>
      <if test="record.tripEndOverCreateDay != null">
        trip_end_over_create_day = #{record.tripEndOverCreateDay,jdbcType=INTEGER},
      </if>
      <if test="record.controlIntensity != null">
        control_intensity = #{record.controlIntensity,jdbcType=INTEGER},
      </if>
      <if test="record.controlMsg != null">
        control_msg = #{record.controlMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.effectiveDate != null">
        effective_date = #{record.effectiveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isEffectiveDate != null">
        is_effective_date = #{record.isEffectiveDate,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update custform_reimbursement_config
    set id = #{record.id,jdbcType=VARCHAR},
      form_id = #{record.formId,jdbcType=VARCHAR},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      reimbursement_id = #{record.reimbursementId,jdbcType=VARCHAR},
      is_allowed_reimbursement = #{record.isAllowedReimbursement,jdbcType=INTEGER},
      is_trip_end_reimbursement = #{record.isTripEndReimbursement,jdbcType=INTEGER},
      is_trip_end_over_reimbursement = #{record.isTripEndOverReimbursement,jdbcType=INTEGER},
      trip_end_over_reimbursement_day = #{record.tripEndOverReimbursementDay,jdbcType=INTEGER},
      is_auto_reimbursement_draft = #{record.isAutoReimbursementDraft,jdbcType=INTEGER},
      trip_end_auto_reimbursement_draft_day = #{record.tripEndAutoReimbursementDraftDay,jdbcType=INTEGER},
      is_unreimbursed_create_apply = #{record.isUnreimbursedCreateApply,jdbcType=INTEGER},
      trip_end_over_create_day = #{record.tripEndOverCreateDay,jdbcType=INTEGER},
      control_intensity = #{record.controlIntensity,jdbcType=INTEGER},
      control_msg = #{record.controlMsg,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      effective_date = #{record.effectiveDate,jdbcType=TIMESTAMP},
      is_effective_date = #{record.isEffectiveDate,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saasplus.CustformReimbursementConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update custform_reimbursement_config
    <set>
      <if test="formId != null">
        form_id = #{formId,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="reimbursementId != null">
        reimbursement_id = #{reimbursementId,jdbcType=VARCHAR},
      </if>
      <if test="isAllowedReimbursement != null">
        is_allowed_reimbursement = #{isAllowedReimbursement,jdbcType=INTEGER},
      </if>
      <if test="isTripEndReimbursement != null">
        is_trip_end_reimbursement = #{isTripEndReimbursement,jdbcType=INTEGER},
      </if>
      <if test="isTripEndOverReimbursement != null">
        is_trip_end_over_reimbursement = #{isTripEndOverReimbursement,jdbcType=INTEGER},
      </if>
      <if test="tripEndOverReimbursementDay != null">
        trip_end_over_reimbursement_day = #{tripEndOverReimbursementDay,jdbcType=INTEGER},
      </if>
      <if test="isAutoReimbursementDraft != null">
        is_auto_reimbursement_draft = #{isAutoReimbursementDraft,jdbcType=INTEGER},
      </if>
      <if test="tripEndAutoReimbursementDraftDay != null">
        trip_end_auto_reimbursement_draft_day = #{tripEndAutoReimbursementDraftDay,jdbcType=INTEGER},
      </if>
      <if test="isUnreimbursedCreateApply != null">
        is_unreimbursed_create_apply = #{isUnreimbursedCreateApply,jdbcType=INTEGER},
      </if>
      <if test="tripEndOverCreateDay != null">
        trip_end_over_create_day = #{tripEndOverCreateDay,jdbcType=INTEGER},
      </if>
      <if test="controlIntensity != null">
        control_intensity = #{controlIntensity,jdbcType=INTEGER},
      </if>
      <if test="controlMsg != null">
        control_msg = #{controlMsg,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="effectiveDate != null">
        effective_date = #{effectiveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isEffectiveDate != null">
        is_effective_date = #{isEffectiveDate,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.saasplus.CustformReimbursementConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update custform_reimbursement_config
    set form_id = #{formId,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=VARCHAR},
      reimbursement_id = #{reimbursementId,jdbcType=VARCHAR},
      is_allowed_reimbursement = #{isAllowedReimbursement,jdbcType=INTEGER},
      is_trip_end_reimbursement = #{isTripEndReimbursement,jdbcType=INTEGER},
      is_trip_end_over_reimbursement = #{isTripEndOverReimbursement,jdbcType=INTEGER},
      trip_end_over_reimbursement_day = #{tripEndOverReimbursementDay,jdbcType=INTEGER},
      is_auto_reimbursement_draft = #{isAutoReimbursementDraft,jdbcType=INTEGER},
      trip_end_auto_reimbursement_draft_day = #{tripEndAutoReimbursementDraftDay,jdbcType=INTEGER},
      is_unreimbursed_create_apply = #{isUnreimbursedCreateApply,jdbcType=INTEGER},
      trip_end_over_create_day = #{tripEndOverCreateDay,jdbcType=INTEGER},
      control_intensity = #{controlIntensity,jdbcType=INTEGER},
      control_msg = #{controlMsg,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      effective_date = #{effectiveDate,jdbcType=TIMESTAMP},
      is_effective_date = #{isEffectiveDate,jdbcType=INTEGER}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>