<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saasplus.ApplyOrderExtendMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saasplus.ApplyOrderExtend">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="apply_order_id" jdbcType="CHAR" property="applyOrderId" />
    <result column="personal_reason_type_desc" jdbcType="VARCHAR" property="personalReasonTypeDesc" />
    <result column="personal_reason" jdbcType="VARCHAR" property="personalReason" />
    <result column="personal_reason_desc" jdbcType="VARCHAR" property="personalReasonDesc" />
    <result column="booking_type" jdbcType="INTEGER" property="bookingType" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="consume_company_id" jdbcType="CHAR" property="consumeCompanyId" />
    <result column="consume_company_name" jdbcType="VARCHAR" property="consumeCompanyName" />
    <result column="picture_attachment_info" jdbcType="VARCHAR" property="pictureAttachmentInfo" />
    <result column="total_estimated_amount" jdbcType="BIGINT" property="totalEstimatedAmount" />
    <result column="apply_reason_bring_in_order" jdbcType="INTEGER" property="applyReasonBringInOrder" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="taxi_apply_scene_type" jdbcType="INTEGER" property="taxiApplySceneType" />
    <result column="budget_occupy_voucher" jdbcType="VARCHAR" property="budgetOccupyVoucher" />
    <result column="budget_occupy_desc" jdbcType="VARCHAR" property="budgetOccupyDesc" />
    <result column="control_upgrade_flag" jdbcType="INTEGER" property="controlUpgradeFlag" />
    <result column="trip_end_date" jdbcType="TIMESTAMP" property="tripEndDate" />
    <result column="has_send_push" jdbcType="SMALLINT" property="hasSendPush" />
    <result column="new_on_change" jdbcType="INTEGER" property="newOnChange" />
    <result column="adjust_accept_id" jdbcType="VARCHAR" property="adjustAcceptId" />
    <result column="is_show" jdbcType="INTEGER" property="isShow" />
    <result column="api_use_version" jdbcType="INTEGER" property="apiUseVersion" />
    <result column="reimbursement_state" jdbcType="INTEGER" property="reimbursementState" />
    <result column="auto_create_reimbursement" jdbcType="INTEGER" property="autoCreateReimbursement" />
    <result column="auto_create_reimbursement_results" jdbcType="INTEGER" property="autoCreateReimbursementResults" />
    <result column="auto_create_reimbursement_id" jdbcType="VARCHAR" property="autoCreateReimbursementId" />
    <result column="company_legal_entity_id" jdbcType="VARCHAR" property="companyLegalEntityId" />
    <result column="company_legal_entity_name" jdbcType="VARCHAR" property="companyLegalEntityName" />
    <result column="multi_trip_use_count" jdbcType="VARCHAR" property="multiTripUseCount" />
    <result column="is_close" jdbcType="INTEGER" property="isClose" />
    <result column="apply_reason_is_edit" jdbcType="INTEGER" property="applyReasonIsEdit" />
    <result column="trip_add_reason_is_edit" jdbcType="INTEGER" property="tripAddReasonIsEdit" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, apply_order_id, personal_reason_type_desc, personal_reason, personal_reason_desc,
    booking_type, company_id, group_id, consume_company_id, consume_company_name, picture_attachment_info,
    total_estimated_amount, apply_reason_bring_in_order, create_time, update_time, taxi_apply_scene_type,
    budget_occupy_voucher, budget_occupy_desc, control_upgrade_flag, trip_end_date, has_send_push,
    new_on_change, adjust_accept_id, is_show, api_use_version, reimbursement_state, auto_create_reimbursement,
    auto_create_reimbursement_results, auto_create_reimbursement_id, company_legal_entity_id,
    company_legal_entity_name, multi_trip_use_count, is_close, apply_reason_is_edit,
    trip_add_reason_is_edit
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.ApplyOrderExtendExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from apply_order_extend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from apply_order_extend
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from apply_order_extend
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.ApplyOrderExtendExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from apply_order_extend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.saasplus.ApplyOrderExtend">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into apply_order_extend (id, apply_order_id, personal_reason_type_desc,
      personal_reason, personal_reason_desc, booking_type,
      company_id, group_id, consume_company_id,
      consume_company_name, picture_attachment_info,
      total_estimated_amount, apply_reason_bring_in_order,
      create_time, update_time, taxi_apply_scene_type,
      budget_occupy_voucher, budget_occupy_desc,
      control_upgrade_flag, trip_end_date, has_send_push,
      new_on_change, adjust_accept_id, is_show,
      api_use_version, reimbursement_state, auto_create_reimbursement,
      auto_create_reimbursement_results, auto_create_reimbursement_id,
      company_legal_entity_id, company_legal_entity_name,
      multi_trip_use_count, is_close, apply_reason_is_edit,
      trip_add_reason_is_edit)
    values (#{id,jdbcType=CHAR}, #{applyOrderId,jdbcType=CHAR}, #{personalReasonTypeDesc,jdbcType=VARCHAR},
      #{personalReason,jdbcType=VARCHAR}, #{personalReasonDesc,jdbcType=VARCHAR}, #{bookingType,jdbcType=INTEGER},
      #{companyId,jdbcType=VARCHAR}, #{groupId,jdbcType=VARCHAR}, #{consumeCompanyId,jdbcType=CHAR},
      #{consumeCompanyName,jdbcType=VARCHAR}, #{pictureAttachmentInfo,jdbcType=VARCHAR},
      #{totalEstimatedAmount,jdbcType=BIGINT}, #{applyReasonBringInOrder,jdbcType=INTEGER},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{taxiApplySceneType,jdbcType=INTEGER},
      #{budgetOccupyVoucher,jdbcType=VARCHAR}, #{budgetOccupyDesc,jdbcType=VARCHAR},
      #{controlUpgradeFlag,jdbcType=INTEGER}, #{tripEndDate,jdbcType=TIMESTAMP}, #{hasSendPush,jdbcType=SMALLINT},
      #{newOnChange,jdbcType=INTEGER}, #{adjustAcceptId,jdbcType=VARCHAR}, #{isShow,jdbcType=INTEGER},
      #{apiUseVersion,jdbcType=INTEGER}, #{reimbursementState,jdbcType=INTEGER}, #{autoCreateReimbursement,jdbcType=INTEGER},
      #{autoCreateReimbursementResults,jdbcType=INTEGER}, #{autoCreateReimbursementId,jdbcType=VARCHAR},
      #{companyLegalEntityId,jdbcType=VARCHAR}, #{companyLegalEntityName,jdbcType=VARCHAR},
      #{multiTripUseCount,jdbcType=VARCHAR}, #{isClose,jdbcType=INTEGER}, #{applyReasonIsEdit,jdbcType=INTEGER},
      #{tripAddReasonIsEdit,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.saasplus.ApplyOrderExtend">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into apply_order_extend
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="applyOrderId != null">
        apply_order_id,
      </if>
      <if test="personalReasonTypeDesc != null">
        personal_reason_type_desc,
      </if>
      <if test="personalReason != null">
        personal_reason,
      </if>
      <if test="personalReasonDesc != null">
        personal_reason_desc,
      </if>
      <if test="bookingType != null">
        booking_type,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="consumeCompanyId != null">
        consume_company_id,
      </if>
      <if test="consumeCompanyName != null">
        consume_company_name,
      </if>
      <if test="pictureAttachmentInfo != null">
        picture_attachment_info,
      </if>
      <if test="totalEstimatedAmount != null">
        total_estimated_amount,
      </if>
      <if test="applyReasonBringInOrder != null">
        apply_reason_bring_in_order,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="taxiApplySceneType != null">
        taxi_apply_scene_type,
      </if>
      <if test="budgetOccupyVoucher != null">
        budget_occupy_voucher,
      </if>
      <if test="budgetOccupyDesc != null">
        budget_occupy_desc,
      </if>
      <if test="controlUpgradeFlag != null">
        control_upgrade_flag,
      </if>
      <if test="tripEndDate != null">
        trip_end_date,
      </if>
      <if test="hasSendPush != null">
        has_send_push,
      </if>
      <if test="newOnChange != null">
        new_on_change,
      </if>
      <if test="adjustAcceptId != null">
        adjust_accept_id,
      </if>
      <if test="isShow != null">
        is_show,
      </if>
      <if test="apiUseVersion != null">
        api_use_version,
      </if>
      <if test="reimbursementState != null">
        reimbursement_state,
      </if>
      <if test="autoCreateReimbursement != null">
        auto_create_reimbursement,
      </if>
      <if test="autoCreateReimbursementResults != null">
        auto_create_reimbursement_results,
      </if>
      <if test="autoCreateReimbursementId != null">
        auto_create_reimbursement_id,
      </if>
      <if test="companyLegalEntityId != null">
        company_legal_entity_id,
      </if>
      <if test="companyLegalEntityName != null">
        company_legal_entity_name,
      </if>
      <if test="multiTripUseCount != null">
        multi_trip_use_count,
      </if>
      <if test="isClose != null">
        is_close,
      </if>
      <if test="applyReasonIsEdit != null">
        apply_reason_is_edit,
      </if>
      <if test="tripAddReasonIsEdit != null">
        trip_add_reason_is_edit,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="applyOrderId != null">
        #{applyOrderId,jdbcType=CHAR},
      </if>
      <if test="personalReasonTypeDesc != null">
        #{personalReasonTypeDesc,jdbcType=VARCHAR},
      </if>
      <if test="personalReason != null">
        #{personalReason,jdbcType=VARCHAR},
      </if>
      <if test="personalReasonDesc != null">
        #{personalReasonDesc,jdbcType=VARCHAR},
      </if>
      <if test="bookingType != null">
        #{bookingType,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="consumeCompanyId != null">
        #{consumeCompanyId,jdbcType=CHAR},
      </if>
      <if test="consumeCompanyName != null">
        #{consumeCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="pictureAttachmentInfo != null">
        #{pictureAttachmentInfo,jdbcType=VARCHAR},
      </if>
      <if test="totalEstimatedAmount != null">
        #{totalEstimatedAmount,jdbcType=BIGINT},
      </if>
      <if test="applyReasonBringInOrder != null">
        #{applyReasonBringInOrder,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taxiApplySceneType != null">
        #{taxiApplySceneType,jdbcType=INTEGER},
      </if>
      <if test="budgetOccupyVoucher != null">
        #{budgetOccupyVoucher,jdbcType=VARCHAR},
      </if>
      <if test="budgetOccupyDesc != null">
        #{budgetOccupyDesc,jdbcType=VARCHAR},
      </if>
      <if test="controlUpgradeFlag != null">
        #{controlUpgradeFlag,jdbcType=INTEGER},
      </if>
      <if test="tripEndDate != null">
        #{tripEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="hasSendPush != null">
        #{hasSendPush,jdbcType=SMALLINT},
      </if>
      <if test="newOnChange != null">
        #{newOnChange,jdbcType=INTEGER},
      </if>
      <if test="adjustAcceptId != null">
        #{adjustAcceptId,jdbcType=VARCHAR},
      </if>
      <if test="isShow != null">
        #{isShow,jdbcType=INTEGER},
      </if>
      <if test="apiUseVersion != null">
        #{apiUseVersion,jdbcType=INTEGER},
      </if>
      <if test="reimbursementState != null">
        #{reimbursementState,jdbcType=INTEGER},
      </if>
      <if test="autoCreateReimbursement != null">
        #{autoCreateReimbursement,jdbcType=INTEGER},
      </if>
      <if test="autoCreateReimbursementResults != null">
        #{autoCreateReimbursementResults,jdbcType=INTEGER},
      </if>
      <if test="autoCreateReimbursementId != null">
        #{autoCreateReimbursementId,jdbcType=VARCHAR},
      </if>
      <if test="companyLegalEntityId != null">
        #{companyLegalEntityId,jdbcType=VARCHAR},
      </if>
      <if test="companyLegalEntityName != null">
        #{companyLegalEntityName,jdbcType=VARCHAR},
      </if>
      <if test="multiTripUseCount != null">
        #{multiTripUseCount,jdbcType=VARCHAR},
      </if>
      <if test="isClose != null">
        #{isClose,jdbcType=INTEGER},
      </if>
      <if test="applyReasonIsEdit != null">
        #{applyReasonIsEdit,jdbcType=INTEGER},
      </if>
      <if test="tripAddReasonIsEdit != null">
        #{tripAddReasonIsEdit,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.ApplyOrderExtendExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from apply_order_extend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_order_extend
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.applyOrderId != null">
        apply_order_id = #{record.applyOrderId,jdbcType=CHAR},
      </if>
      <if test="record.personalReasonTypeDesc != null">
        personal_reason_type_desc = #{record.personalReasonTypeDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.personalReason != null">
        personal_reason = #{record.personalReason,jdbcType=VARCHAR},
      </if>
      <if test="record.personalReasonDesc != null">
        personal_reason_desc = #{record.personalReasonDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.bookingType != null">
        booking_type = #{record.bookingType,jdbcType=INTEGER},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.groupId != null">
        group_id = #{record.groupId,jdbcType=VARCHAR},
      </if>
      <if test="record.consumeCompanyId != null">
        consume_company_id = #{record.consumeCompanyId,jdbcType=CHAR},
      </if>
      <if test="record.consumeCompanyName != null">
        consume_company_name = #{record.consumeCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="record.pictureAttachmentInfo != null">
        picture_attachment_info = #{record.pictureAttachmentInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.totalEstimatedAmount != null">
        total_estimated_amount = #{record.totalEstimatedAmount,jdbcType=BIGINT},
      </if>
      <if test="record.applyReasonBringInOrder != null">
        apply_reason_bring_in_order = #{record.applyReasonBringInOrder,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.taxiApplySceneType != null">
        taxi_apply_scene_type = #{record.taxiApplySceneType,jdbcType=INTEGER},
      </if>
      <if test="record.budgetOccupyVoucher != null">
        budget_occupy_voucher = #{record.budgetOccupyVoucher,jdbcType=VARCHAR},
      </if>
      <if test="record.budgetOccupyDesc != null">
        budget_occupy_desc = #{record.budgetOccupyDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.controlUpgradeFlag != null">
        control_upgrade_flag = #{record.controlUpgradeFlag,jdbcType=INTEGER},
      </if>
      <if test="record.tripEndDate != null">
        trip_end_date = #{record.tripEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.hasSendPush != null">
        has_send_push = #{record.hasSendPush,jdbcType=SMALLINT},
      </if>
      <if test="record.newOnChange != null">
        new_on_change = #{record.newOnChange,jdbcType=INTEGER},
      </if>
      <if test="record.adjustAcceptId != null">
        adjust_accept_id = #{record.adjustAcceptId,jdbcType=VARCHAR},
      </if>
      <if test="record.isShow != null">
        is_show = #{record.isShow,jdbcType=INTEGER},
      </if>
      <if test="record.apiUseVersion != null">
        api_use_version = #{record.apiUseVersion,jdbcType=INTEGER},
      </if>
      <if test="record.reimbursementState != null">
        reimbursement_state = #{record.reimbursementState,jdbcType=INTEGER},
      </if>
      <if test="record.autoCreateReimbursement != null">
        auto_create_reimbursement = #{record.autoCreateReimbursement,jdbcType=INTEGER},
      </if>
      <if test="record.autoCreateReimbursementResults != null">
        auto_create_reimbursement_results = #{record.autoCreateReimbursementResults,jdbcType=INTEGER},
      </if>
      <if test="record.autoCreateReimbursementId != null">
        auto_create_reimbursement_id = #{record.autoCreateReimbursementId,jdbcType=VARCHAR},
      </if>
      <if test="record.companyLegalEntityId != null">
        company_legal_entity_id = #{record.companyLegalEntityId,jdbcType=VARCHAR},
      </if>
      <if test="record.companyLegalEntityName != null">
        company_legal_entity_name = #{record.companyLegalEntityName,jdbcType=VARCHAR},
      </if>
      <if test="record.multiTripUseCount != null">
        multi_trip_use_count = #{record.multiTripUseCount,jdbcType=VARCHAR},
      </if>
      <if test="record.isClose != null">
        is_close = #{record.isClose,jdbcType=INTEGER},
      </if>
      <if test="record.applyReasonIsEdit != null">
        apply_reason_is_edit = #{record.applyReasonIsEdit,jdbcType=INTEGER},
      </if>
      <if test="record.tripAddReasonIsEdit != null">
        trip_add_reason_is_edit = #{record.tripAddReasonIsEdit,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_order_extend
    set id = #{record.id,jdbcType=CHAR},
      apply_order_id = #{record.applyOrderId,jdbcType=CHAR},
      personal_reason_type_desc = #{record.personalReasonTypeDesc,jdbcType=VARCHAR},
      personal_reason = #{record.personalReason,jdbcType=VARCHAR},
      personal_reason_desc = #{record.personalReasonDesc,jdbcType=VARCHAR},
      booking_type = #{record.bookingType,jdbcType=INTEGER},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      group_id = #{record.groupId,jdbcType=VARCHAR},
      consume_company_id = #{record.consumeCompanyId,jdbcType=CHAR},
      consume_company_name = #{record.consumeCompanyName,jdbcType=VARCHAR},
      picture_attachment_info = #{record.pictureAttachmentInfo,jdbcType=VARCHAR},
      total_estimated_amount = #{record.totalEstimatedAmount,jdbcType=BIGINT},
      apply_reason_bring_in_order = #{record.applyReasonBringInOrder,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      taxi_apply_scene_type = #{record.taxiApplySceneType,jdbcType=INTEGER},
      budget_occupy_voucher = #{record.budgetOccupyVoucher,jdbcType=VARCHAR},
      budget_occupy_desc = #{record.budgetOccupyDesc,jdbcType=VARCHAR},
      control_upgrade_flag = #{record.controlUpgradeFlag,jdbcType=INTEGER},
      trip_end_date = #{record.tripEndDate,jdbcType=TIMESTAMP},
      has_send_push = #{record.hasSendPush,jdbcType=SMALLINT},
      new_on_change = #{record.newOnChange,jdbcType=INTEGER},
      adjust_accept_id = #{record.adjustAcceptId,jdbcType=VARCHAR},
      is_show = #{record.isShow,jdbcType=INTEGER},
      api_use_version = #{record.apiUseVersion,jdbcType=INTEGER},
      reimbursement_state = #{record.reimbursementState,jdbcType=INTEGER},
      auto_create_reimbursement = #{record.autoCreateReimbursement,jdbcType=INTEGER},
      auto_create_reimbursement_results = #{record.autoCreateReimbursementResults,jdbcType=INTEGER},
      auto_create_reimbursement_id = #{record.autoCreateReimbursementId,jdbcType=VARCHAR},
      company_legal_entity_id = #{record.companyLegalEntityId,jdbcType=VARCHAR},
      company_legal_entity_name = #{record.companyLegalEntityName,jdbcType=VARCHAR},
      multi_trip_use_count = #{record.multiTripUseCount,jdbcType=VARCHAR},
      is_close = #{record.isClose,jdbcType=INTEGER},
      apply_reason_is_edit = #{record.applyReasonIsEdit,jdbcType=INTEGER},
      trip_add_reason_is_edit = #{record.tripAddReasonIsEdit,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saasplus.ApplyOrderExtend">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_order_extend
    <set>
      <if test="applyOrderId != null">
        apply_order_id = #{applyOrderId,jdbcType=CHAR},
      </if>
      <if test="personalReasonTypeDesc != null">
        personal_reason_type_desc = #{personalReasonTypeDesc,jdbcType=VARCHAR},
      </if>
      <if test="personalReason != null">
        personal_reason = #{personalReason,jdbcType=VARCHAR},
      </if>
      <if test="personalReasonDesc != null">
        personal_reason_desc = #{personalReasonDesc,jdbcType=VARCHAR},
      </if>
      <if test="bookingType != null">
        booking_type = #{bookingType,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="consumeCompanyId != null">
        consume_company_id = #{consumeCompanyId,jdbcType=CHAR},
      </if>
      <if test="consumeCompanyName != null">
        consume_company_name = #{consumeCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="pictureAttachmentInfo != null">
        picture_attachment_info = #{pictureAttachmentInfo,jdbcType=VARCHAR},
      </if>
      <if test="totalEstimatedAmount != null">
        total_estimated_amount = #{totalEstimatedAmount,jdbcType=BIGINT},
      </if>
      <if test="applyReasonBringInOrder != null">
        apply_reason_bring_in_order = #{applyReasonBringInOrder,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taxiApplySceneType != null">
        taxi_apply_scene_type = #{taxiApplySceneType,jdbcType=INTEGER},
      </if>
      <if test="budgetOccupyVoucher != null">
        budget_occupy_voucher = #{budgetOccupyVoucher,jdbcType=VARCHAR},
      </if>
      <if test="budgetOccupyDesc != null">
        budget_occupy_desc = #{budgetOccupyDesc,jdbcType=VARCHAR},
      </if>
      <if test="controlUpgradeFlag != null">
        control_upgrade_flag = #{controlUpgradeFlag,jdbcType=INTEGER},
      </if>
      <if test="tripEndDate != null">
        trip_end_date = #{tripEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="hasSendPush != null">
        has_send_push = #{hasSendPush,jdbcType=SMALLINT},
      </if>
      <if test="newOnChange != null">
        new_on_change = #{newOnChange,jdbcType=INTEGER},
      </if>
      <if test="adjustAcceptId != null">
        adjust_accept_id = #{adjustAcceptId,jdbcType=VARCHAR},
      </if>
      <if test="isShow != null">
        is_show = #{isShow,jdbcType=INTEGER},
      </if>
      <if test="apiUseVersion != null">
        api_use_version = #{apiUseVersion,jdbcType=INTEGER},
      </if>
      <if test="reimbursementState != null">
        reimbursement_state = #{reimbursementState,jdbcType=INTEGER},
      </if>
      <if test="autoCreateReimbursement != null">
        auto_create_reimbursement = #{autoCreateReimbursement,jdbcType=INTEGER},
      </if>
      <if test="autoCreateReimbursementResults != null">
        auto_create_reimbursement_results = #{autoCreateReimbursementResults,jdbcType=INTEGER},
      </if>
      <if test="autoCreateReimbursementId != null">
        auto_create_reimbursement_id = #{autoCreateReimbursementId,jdbcType=VARCHAR},
      </if>
      <if test="companyLegalEntityId != null">
        company_legal_entity_id = #{companyLegalEntityId,jdbcType=VARCHAR},
      </if>
      <if test="companyLegalEntityName != null">
        company_legal_entity_name = #{companyLegalEntityName,jdbcType=VARCHAR},
      </if>
      <if test="multiTripUseCount != null">
        multi_trip_use_count = #{multiTripUseCount,jdbcType=VARCHAR},
      </if>
      <if test="isClose != null">
        is_close = #{isClose,jdbcType=INTEGER},
      </if>
      <if test="applyReasonIsEdit != null">
        apply_reason_is_edit = #{applyReasonIsEdit,jdbcType=INTEGER},
      </if>
      <if test="tripAddReasonIsEdit != null">
        trip_add_reason_is_edit = #{tripAddReasonIsEdit,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.saasplus.ApplyOrderExtend">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_order_extend
    set apply_order_id = #{applyOrderId,jdbcType=CHAR},
      personal_reason_type_desc = #{personalReasonTypeDesc,jdbcType=VARCHAR},
      personal_reason = #{personalReason,jdbcType=VARCHAR},
      personal_reason_desc = #{personalReasonDesc,jdbcType=VARCHAR},
      booking_type = #{bookingType,jdbcType=INTEGER},
      company_id = #{companyId,jdbcType=VARCHAR},
      group_id = #{groupId,jdbcType=VARCHAR},
      consume_company_id = #{consumeCompanyId,jdbcType=CHAR},
      consume_company_name = #{consumeCompanyName,jdbcType=VARCHAR},
      picture_attachment_info = #{pictureAttachmentInfo,jdbcType=VARCHAR},
      total_estimated_amount = #{totalEstimatedAmount,jdbcType=BIGINT},
      apply_reason_bring_in_order = #{applyReasonBringInOrder,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      taxi_apply_scene_type = #{taxiApplySceneType,jdbcType=INTEGER},
      budget_occupy_voucher = #{budgetOccupyVoucher,jdbcType=VARCHAR},
      budget_occupy_desc = #{budgetOccupyDesc,jdbcType=VARCHAR},
      control_upgrade_flag = #{controlUpgradeFlag,jdbcType=INTEGER},
      trip_end_date = #{tripEndDate,jdbcType=TIMESTAMP},
      has_send_push = #{hasSendPush,jdbcType=SMALLINT},
      new_on_change = #{newOnChange,jdbcType=INTEGER},
      adjust_accept_id = #{adjustAcceptId,jdbcType=VARCHAR},
      is_show = #{isShow,jdbcType=INTEGER},
      api_use_version = #{apiUseVersion,jdbcType=INTEGER},
      reimbursement_state = #{reimbursementState,jdbcType=INTEGER},
      auto_create_reimbursement = #{autoCreateReimbursement,jdbcType=INTEGER},
      auto_create_reimbursement_results = #{autoCreateReimbursementResults,jdbcType=INTEGER},
      auto_create_reimbursement_id = #{autoCreateReimbursementId,jdbcType=VARCHAR},
      company_legal_entity_id = #{companyLegalEntityId,jdbcType=VARCHAR},
      company_legal_entity_name = #{companyLegalEntityName,jdbcType=VARCHAR},
      multi_trip_use_count = #{multiTripUseCount,jdbcType=VARCHAR},
      is_close = #{isClose,jdbcType=INTEGER},
      apply_reason_is_edit = #{applyReasonIsEdit,jdbcType=INTEGER},
      trip_add_reason_is_edit = #{tripAddReasonIsEdit,jdbcType=INTEGER}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>