<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saasplus.ApplyOrderTripMapper">



    <select id="selectShareApply" resultType="java.lang.String">
        SELECT
        id
        FROM
        apply_order a
        WHERE
        a.id in
        <foreach collection="applyIds" item="applyId" index="index" open="(" close=")" separator=",">
            #{applyId}
        </foreach>
        AND state = 4
        AND delete_status = 0
        AND past_status = 0
        AND a.apply_order_type in (1, 15, 16)
        AND ( SELECT count(1) FROM apply_trip_info b WHERE b.apply_order_id = a.id
        AND b.type in
        <foreach collection="bizTypeList" item="bizType" index="index" open="(" close=")" separator=",">
            #{bizType}
        </foreach>
        ) > 0
    </select>


    <select id="selectBookerApply" resultType="java.lang.String">
        SELECT
            id
        FROM
            apply_order a
        WHERE
            employee_id = #{employeeId, jdbcType=VARCHAR}
            AND state = 4
            AND delete_status = 0
            AND past_status = 0
            AND a.apply_order_type in (1, 15, 16)
            AND ( SELECT count(1) FROM apply_trip_info b WHERE b.apply_order_id = a.id
                AND b.type in
                <foreach collection="bizTypeList" item="bizType" index="index" open="(" close=")" separator=",">
                  #{bizType}
                </foreach>
            ) > 0
    </select>

    <select id="selectShareRoomApply" resultType="java.lang.String">
        select DISTINCT(apply_order_id) as id from apply_trip_info where `apply_order_id`  in (
        SELECT
        id
        FROM
        apply_order a WHERE
        a.company_id = #{companyId, jdbcType=VARCHAR}
        <!-- 员工 ID 条件 -->
        <if test="employeeIds != null and employeeIds.size() > 0">
            AND a.employee_id IN
            <foreach item="employeeId" collection="employeeIds" open="(" separator="," close=")">
                #{employeeId}
            </foreach>
        </if>
        and type in (1,23,24)
        AND a.apply_order_type in (1, 15, 16)
        AND a.delete_status = 0
        AND a.state = 4
        AND a.past_status = 0
        ) and type in
        <foreach collection="bizTypeList" item="bizType" index="index" open="(" close=")" separator=",">
            #{bizType}
        </foreach>
    </select>

    <select id="selectTravelerOrBookerApply" resultType="java.lang.String">
        SELECT
            id
        FROM
            apply_order a
        WHERE
            employee_id = #{employeeId, jdbcType=VARCHAR}
            AND state = 4
            AND delete_status = 0
            AND past_status = 0
            AND a.apply_order_type in (1, 15, 16)
            AND ( SELECT count( 1 ) FROM apply_trip_info b WHERE b.apply_order_id = a.id
                AND b.type in
                <foreach collection="bizTypeList" item="bizType" index="index" open="(" close=")" separator=",">
                  #{bizType}
                </foreach>
            ) > 0
            AND ( SELECT count( 1 ) FROM apply_trip_guest c WHERE c.apply_order_id = a.id and c.contact_info not like '%is_self_auth%') = 0
        UNION
        select id from apply_order a where id in (
            select apply_order_id from apply_trip_guest a
            where a.contact_id = #{employeeId, jdbcType=VARCHAR}
        )
           AND state = 4
           AND delete_status = 0
           AND past_status = 0
           AND a.apply_order_type in (1, 15, 16)
           AND ( SELECT count( 1 ) FROM apply_trip_info b WHERE b.apply_order_id = a.id
              AND b.type in
              <foreach collection="bizTypeList" item="bizType" index="index" open="(" close=")" separator=",">
                #{bizType}
              </foreach>
           ) > 0

    </select>

    <select id="selectTravelerApply" resultType="java.lang.String">
        SELECT
            id
        FROM
            apply_order a
        WHERE
            employee_id = #{employeeId, jdbcType=VARCHAR}
          AND state = 4
          AND delete_status = 0
          AND past_status = 0
          AND a.apply_order_type in (1, 15, 16)
          AND ( SELECT count( 1 ) FROM apply_trip_info b WHERE b.apply_order_id = a.id
              AND b.type in
              <foreach collection="bizTypeList" item="bizType" index="index" open="(" close=")" separator=",">
                #{bizType}
              </foreach>
          ) > 0
          AND ( SELECT count( 1 ) FROM apply_trip_guest c WHERE c.apply_order_id = a.id and c.contact_info not like '%is_self_auth%') = 0
        UNION
        SELECT
            id
        FROM
            apply_order a
        WHERE
            employee_id = #{select_employee_id, jdbcType=VARCHAR}
          AND state = 4
          AND delete_status = 0
          AND past_status = 0
          AND a.apply_order_type in (1, 15, 16)
          AND ( SELECT count( 1 ) FROM apply_trip_info b WHERE b.apply_order_id = a.id
              AND b.type in
              <foreach collection="bizTypeList" item="bizType" index="index" open="(" close=")" separator=",">
                #{bizType}
              </foreach>
          ) > 0
          AND ( SELECT count( 1 ) FROM apply_trip_guest c WHERE c.apply_order_id = a.id and c.contact_info not like '%is_self_auth%') = 0
        UNION
        select id from apply_order a where id in (
            select apply_order_id from apply_trip_guest a
            where a.contact_id = #{select_employee_id, jdbcType=VARCHAR}
        )
        AND state = 4
        AND delete_status = 0
        AND past_status = 0
        AND a.apply_order_type in (1, 15, 16)
        AND ( SELECT count( 1 ) FROM apply_trip_info b WHERE b.apply_order_id = a.id
            AND b.type in
            <foreach collection="bizTypeList" item="bizType" index="index" open="(" close=")" separator=",">
              #{bizType}
            </foreach>
        ) > 0

    </select>

    <select id="selectConsumeCompanyApply" resultType="java.lang.String">
      SELECT
        a.id
      FROM
        apply_order a
        left join apply_order_extend e on a.id = e.apply_order_id
      WHERE
        a.id in
        <foreach collection="applyIds" item="applyId" index="index" open="(" close=")" separator=",">
          #{applyId}
        </foreach>
        AND ifnull(e.consume_company_id,a.company_id) = #{companyId, jdbcType=VARCHAR}
    </select>

    <select id="selectTravelerApplyIds" resultType="java.lang.String">
        select id from apply_order a where id in (
        select apply_order_id from apply_trip_guest a
        where a.contact_id in
        <foreach collection="TripperIds" item="tripperId" index="index" open="(" close=")" separator=",">
            #{tripperId}
        </foreach>
        )
        AND delete_status = 0
        AND a.apply_order_type in (1, 15, 16)
        <if test="employeeIds!=null and employeeIds.size!=0">
        union
        select id from apply_order a where employee_id  in
            <foreach collection="employeeIds" item="employeeId" index="index" open="(" close=")" separator=",">
                #{employeeId}
            </foreach>
        AND delete_status = 0
        AND a.apply_order_type in (1, 15, 16)
        </if>
    </select>


<!--    select a.id from apply_order a left join apply_order_extend b  on a.id = b.apply_order_id-->
<!--    where a.company_id = #{companyId}-->
<!--    and a.employee_id = #{employeeId}-->
<!--    and a.form_id =  #{formId}-->
<!--    and a.type = 24-->
<!--    and a.state  in-->
<!--    <foreach collection="states" item="state" index="index" open="(" close=")" separator=",">-->
<!--        #{state}-->
<!--    </foreach>-->
<!--    and a.create_time >= DATE_SUB(CURDATE(),INTERVAL 1 YEAR)-->
<!--    and b.trip_end_date &lt; DATE_SUB(CURDATE(),INTERVAL #{day} DAY)-->
<!--    order by a.create_time desc-->


    <select id="selectApplyByUnReimbursementByOnly" resultType="java.lang.String">
        select apply_order_id from (
        select apply_order_id,
        CASE
        WHEN end_time is NULL and back_end_time is NULL and start_time is null then NULL
        when end_time is NULL and back_end_time is NULL then start_time
        when back_end_time is NULL then end_time
        else back_end_time end AS max_time
        from (
        select apply_order_id,max(create_time) as create_time,
        max(ifnull(float_end_time,end_time)) end_time,
        max(ifnull(float_back_end_time,back_end_time)) back_end_time,
        max(ifnull(float_start_time,start_time)) start_time
        from apply_trip_info
        where apply_order_id in (
        select a.id from apply_order a
        where a.company_id = #{companyId}
        and a.employee_id = #{employeeId}
        and a.form_id =  #{formId}
        and a.type = 24
        and a.state  in
        <foreach collection="states" item="state" index="index" open="(" close=")" separator=",">
            #{state}
        </foreach>
        and a.create_time >= #{date}
        )
        group by apply_order_id
        ) a having max_time &lt; DATE_SUB(CURDATE(),INTERVAL #{day} DAY) order by create_time desc) a
    </select>

    <select id="selectApplyByUnReimbursement" resultType="java.lang.String">
        select apply_order_id from (
        select apply_order_id,
        CASE
        WHEN end_time is NULL and back_end_time is NULL and start_time is null then NULL
        when end_time is NULL and back_end_time is NULL then start_time
        when back_end_time is NULL then end_time
        else back_end_time end AS max_time
        from (
        select apply_order_id,max(create_time) as create_time,
        max(ifnull(float_end_time,end_time)) end_time,
        max(ifnull(float_back_end_time,back_end_time)) back_end_time,
        max(ifnull(float_start_time,start_time)) start_time
        from apply_trip_info
        where apply_order_id in (
        select a.id from apply_order a
        where a.company_id = #{companyId}
        and a.employee_id = #{employeeId}
        and a.form_id =  #{formId}
        and a.type = 24
        and a.state  in
        <foreach collection="states" item="state" index="index" open="(" close=")" separator=",">
            #{state}
        </foreach>
        and a.create_time >= DATE_SUB(CURDATE(),INTERVAL 1 YEAR)
        )
        group by apply_order_id
        ) a having max_time &lt; DATE_SUB(CURDATE(),INTERVAL #{day} DAY) order by create_time desc) a
    </select>



    <select id="selecTrelationApplyList" resultType="java.lang.String">
select id from (
        SELECT id,create_time FROM apply_order
        WHERE company_id = #{companyId}
        <!-- 员工 ID 条件 -->
        <if test="employeeIds != null and employeeIds.size() > 0">
            AND employee_id IN
            <foreach item="employeeId" collection="employeeIds" open="(" separator="," close=")">
                #{employeeId}
            </foreach>
        </if>
        <!-- ID 条件 -->
        <if test="applyIds != null and applyIds.size() > 0">
            AND id IN
            <foreach item="applyId" collection="applyIds" open="(" separator="," close=")">
                #{applyId}
            </foreach>
        </if>
        <!-- 类型条件 -->
        <if test="types != null and types.size() > 0">
            AND type IN
            <foreach item="type" collection="types" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        <!-- 状态条件 -->
        <if test="states != null and states.size() > 0">
            AND state IN
            <foreach item="state" collection="states" open="(" separator="," close=")">
                #{state}
            </foreach>
        </if>
        <!-- 创建时间条件 -->
        <if test="createTimeStart != null  and createTimeStart !=''">
            AND create_time &gt;= #{createTimeStart}
        </if>
        <if test="createTimeEnd != null  and createTimeEnd !=''">
            AND create_time &lt;= #{createTimeEnd}
        </if>

        <!-- 关联条件 -->
        <if test="associate != null">
            AND ifnull(is_associate,0)=#{associate}
        </if>
        AND delete_status = 0

        <!-- 表单 ID 条件 -->
        <if test="fromIdList != null and fromIdList.size() > 0">
            UNION
            SELECT id,create_time FROM apply_order
            WHERE company_id = #{companyId} and type = 24
                AND form_id IN
                <foreach item="fromId" collection="fromIdList" open="(" separator="," close=")">
                    #{fromId}
                </foreach>
            <!-- 员工 ID 条件 -->
            <if test="employeeIds != null and employeeIds.size() > 0">
                AND employee_id IN
                <foreach item="employeeId" collection="employeeIds" open="(" separator="," close=")">
                    #{employeeId}
                </foreach>
            </if>
            <!-- 状态条件 -->
            <if test="states != null and states.size() > 0">
                AND state IN
                <foreach item="state" collection="states" open="(" separator="," close=")">
                    #{state}
                </foreach>
            </if>
            <!-- 创建时间条件 -->
            <if test="createTimeStart != null  and createTimeStart !=''">
                AND create_time &gt;= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null  and createTimeEnd !=''">
                AND create_time &lt;= #{createTimeEnd}
            </if>

            <!-- 关联条件 -->
            <if test="associate != null">
                AND ifnull(is_associate,0)=#{associate}
            </if>
            AND delete_status = 0
        </if>
        <!-- triperid 条件 -->
        <!-- 表单 ID 条件 -->
        <if test="fromIdList != null and fromIdList.size() > 0 and tripIds != null and tripIds.size() > 0">
            UNION
            SELECT id,create_time FROM apply_order
            WHERE company_id = #{companyId}
            AND id IN
            <foreach item="tripId" collection="tripIds" open="(" separator="," close=")">
                #{tripId}
            </foreach>
            AND form_id IN
            <foreach item="fromId" collection="fromIdList" open="(" separator="," close=")">
                #{fromId}
            </foreach>
            <!-- 状态条件 -->
            <if test="states != null and states.size() > 0">
                AND state IN
                <foreach item="state" collection="states" open="(" separator="," close=")">
                    #{state}
                </foreach>
            </if>
            <!-- 创建时间条件 -->
            <if test="createTimeStart != null  and createTimeStart !=''">
                AND create_time &gt;= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null  and createTimeEnd !=''">
                AND create_time &lt;= #{createTimeEnd}
            </if>

            <!-- 关联条件 -->
            <if test="associate != null">
                AND ifnull(is_associate,0)=#{associate}
            </if>
            AND delete_status = 0
        </if>
        <if test="tripIds != null and tripIds.size() > 0">
            UNION
            SELECT id,create_time FROM apply_order
            WHERE company_id = #{companyId}
                AND id IN
                <foreach item="tripId" collection="tripIds" open="(" separator="," close=")">
                    #{tripId}
                </foreach>
            <!-- 类型条件 -->
            <if test="types != null and types.size() > 0">
                AND type IN
                <foreach item="type" collection="types" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            <!-- 状态条件 -->
            <if test="states != null and states.size() > 0">
                AND state IN
                <foreach item="state" collection="states" open="(" separator="," close=")">
                    #{state}
                </foreach>
            </if>
            <!-- 创建时间条件 -->
            <if test="createTimeStart != null  and createTimeStart !=''">
                AND create_time &gt;= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null  and createTimeEnd !=''">
                AND create_time &lt;= #{createTimeEnd}
            </if>

            <!-- 关联条件 -->
            <if test="associate != null">
                AND ifnull(is_associate,0)=#{associate}
            </if>
            AND delete_status = 0
        </if>

        <!-- 非必填的分页条件 -->
        <if test="offset != null and limit != null">
            ORDER BY create_time desc
            LIMIT #{offset}, #{limit}
        </if>
        ) a
    </select>






</mapper>