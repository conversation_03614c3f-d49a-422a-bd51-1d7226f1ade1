<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saasplus.CustformFormMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saasplus.CustformForm">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="form_name" jdbcType="VARCHAR" property="formName" />
    <result column="form_name_en" jdbcType="VARCHAR" property="formNameEn" />
    <result column="form_code" jdbcType="VARCHAR" property="formCode" />
    <result column="form_type" jdbcType="INTEGER" property="formType" />
    <result column="travel_type" jdbcType="INTEGER" property="travelType" />
    <result column="docking_type" jdbcType="INTEGER" property="dockingType" />
    <result column="limit_use_type" jdbcType="INTEGER" property="limitUseType" />
    <result column="group_type" jdbcType="INTEGER" property="groupType" />
    <result column="form_obj_id" jdbcType="VARCHAR" property="formObjId" />
    <result column="form_obj_code" jdbcType="VARCHAR" property="formObjCode" />
    <result column="form_apply_type" jdbcType="VARCHAR" property="formApplyType" />
    <result column="form_category_type" jdbcType="VARCHAR" property="formCategoryType" />
    <result column="form_group_id" jdbcType="INTEGER" property="formGroupId" />
    <result column="form_status" jdbcType="INTEGER" property="formStatus" />
    <result column="icon_url" jdbcType="VARCHAR" property="iconUrl" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="form_flow_type" jdbcType="INTEGER" property="formFlowType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, form_name, form_name_en, form_code, form_type, travel_type, docking_type,
    limit_use_type, group_type, form_obj_id, form_obj_code, form_apply_type, form_category_type,
    form_group_id, form_status, icon_url, state, create_time, update_time, form_flow_type
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.CustformFormExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from custform_form
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from custform_form
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from custform_form
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.CustformFormExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from custform_form
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.saasplus.CustformForm">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into custform_form (id, company_id, form_name,
    form_name_en, form_code, form_type,
    travel_type, docking_type, limit_use_type,
    group_type, form_obj_id, form_obj_code,
    form_apply_type, form_category_type, form_group_id,
    form_status, icon_url, state,
    create_time, update_time, form_flow_type
    )
    values (#{id,jdbcType=VARCHAR}, #{companyId,jdbcType=VARCHAR}, #{formName,jdbcType=VARCHAR},
    #{formNameEn,jdbcType=VARCHAR}, #{formCode,jdbcType=VARCHAR}, #{formType,jdbcType=INTEGER},
    #{travelType,jdbcType=INTEGER}, #{dockingType,jdbcType=INTEGER}, #{limitUseType,jdbcType=INTEGER},
    #{groupType,jdbcType=INTEGER}, #{formObjId,jdbcType=VARCHAR}, #{formObjCode,jdbcType=VARCHAR},
    #{formApplyType,jdbcType=VARCHAR}, #{formCategoryType,jdbcType=VARCHAR}, #{formGroupId,jdbcType=INTEGER},
    #{formStatus,jdbcType=INTEGER}, #{iconUrl,jdbcType=VARCHAR}, #{state,jdbcType=INTEGER},
    #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{formFlowType,jdbcType=INTEGER}
    )
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.saasplus.CustformForm">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into custform_form
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="formName != null">
        form_name,
      </if>
      <if test="formNameEn != null">
        form_name_en,
      </if>
      <if test="formCode != null">
        form_code,
      </if>
      <if test="formType != null">
        form_type,
      </if>
      <if test="travelType != null">
        travel_type,
      </if>
      <if test="dockingType != null">
        docking_type,
      </if>
      <if test="limitUseType != null">
        limit_use_type,
      </if>
      <if test="groupType != null">
        group_type,
      </if>
      <if test="formObjId != null">
        form_obj_id,
      </if>
      <if test="formObjCode != null">
        form_obj_code,
      </if>
      <if test="formApplyType != null">
        form_apply_type,
      </if>
      <if test="formCategoryType != null">
        form_category_type,
      </if>
      <if test="formGroupId != null">
        form_group_id,
      </if>
      <if test="formStatus != null">
        form_status,
      </if>
      <if test="iconUrl != null">
        icon_url,
      </if>
      <if test="state != null">
        state,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="formFlowType != null">
        form_flow_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="formName != null">
        #{formName,jdbcType=VARCHAR},
      </if>
      <if test="formNameEn != null">
        #{formNameEn,jdbcType=VARCHAR},
      </if>
      <if test="formCode != null">
        #{formCode,jdbcType=VARCHAR},
      </if>
      <if test="formType != null">
        #{formType,jdbcType=INTEGER},
      </if>
      <if test="travelType != null">
        #{travelType,jdbcType=INTEGER},
      </if>
      <if test="dockingType != null">
        #{dockingType,jdbcType=INTEGER},
      </if>
      <if test="limitUseType != null">
        #{limitUseType,jdbcType=INTEGER},
      </if>
      <if test="groupType != null">
        #{groupType,jdbcType=INTEGER},
      </if>
      <if test="formObjId != null">
        #{formObjId,jdbcType=VARCHAR},
      </if>
      <if test="formObjCode != null">
        #{formObjCode,jdbcType=VARCHAR},
      </if>
      <if test="formApplyType != null">
        #{formApplyType,jdbcType=VARCHAR},
      </if>
      <if test="formCategoryType != null">
        #{formCategoryType,jdbcType=VARCHAR},
      </if>
      <if test="formGroupId != null">
        #{formGroupId,jdbcType=INTEGER},
      </if>
      <if test="formStatus != null">
        #{formStatus,jdbcType=INTEGER},
      </if>
      <if test="iconUrl != null">
        #{iconUrl,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="formFlowType != null">
        #{formFlowType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.CustformFormExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from custform_form
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update custform_form
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.formName != null">
        form_name = #{record.formName,jdbcType=VARCHAR},
      </if>
      <if test="record.formNameEn != null">
        form_name_en = #{record.formNameEn,jdbcType=VARCHAR},
      </if>
      <if test="record.formCode != null">
        form_code = #{record.formCode,jdbcType=VARCHAR},
      </if>
      <if test="record.formType != null">
        form_type = #{record.formType,jdbcType=INTEGER},
      </if>
      <if test="record.travelType != null">
        travel_type = #{record.travelType,jdbcType=INTEGER},
      </if>
      <if test="record.dockingType != null">
        docking_type = #{record.dockingType,jdbcType=INTEGER},
      </if>
      <if test="record.limitUseType != null">
        limit_use_type = #{record.limitUseType,jdbcType=INTEGER},
      </if>
      <if test="record.groupType != null">
        group_type = #{record.groupType,jdbcType=INTEGER},
      </if>
      <if test="record.formObjId != null">
        form_obj_id = #{record.formObjId,jdbcType=VARCHAR},
      </if>
      <if test="record.formObjCode != null">
        form_obj_code = #{record.formObjCode,jdbcType=VARCHAR},
      </if>
      <if test="record.formApplyType != null">
        form_apply_type = #{record.formApplyType,jdbcType=VARCHAR},
      </if>
      <if test="record.formCategoryType != null">
        form_category_type = #{record.formCategoryType,jdbcType=VARCHAR},
      </if>
      <if test="record.formGroupId != null">
        form_group_id = #{record.formGroupId,jdbcType=INTEGER},
      </if>
      <if test="record.formStatus != null">
        form_status = #{record.formStatus,jdbcType=INTEGER},
      </if>
      <if test="record.iconUrl != null">
        icon_url = #{record.iconUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.state != null">
        state = #{record.state,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.formFlowType != null">
        form_flow_type = #{record.formFlowType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update custform_form
    set id = #{record.id,jdbcType=VARCHAR},
    company_id = #{record.companyId,jdbcType=VARCHAR},
    form_name = #{record.formName,jdbcType=VARCHAR},
    form_name_en = #{record.formNameEn,jdbcType=VARCHAR},
    form_code = #{record.formCode,jdbcType=VARCHAR},
    form_type = #{record.formType,jdbcType=INTEGER},
    travel_type = #{record.travelType,jdbcType=INTEGER},
    docking_type = #{record.dockingType,jdbcType=INTEGER},
    limit_use_type = #{record.limitUseType,jdbcType=INTEGER},
    group_type = #{record.groupType,jdbcType=INTEGER},
    form_obj_id = #{record.formObjId,jdbcType=VARCHAR},
    form_obj_code = #{record.formObjCode,jdbcType=VARCHAR},
    form_apply_type = #{record.formApplyType,jdbcType=VARCHAR},
    form_category_type = #{record.formCategoryType,jdbcType=VARCHAR},
    form_group_id = #{record.formGroupId,jdbcType=INTEGER},
    form_status = #{record.formStatus,jdbcType=INTEGER},
    icon_url = #{record.iconUrl,jdbcType=VARCHAR},
    state = #{record.state,jdbcType=INTEGER},
    create_time = #{record.createTime,jdbcType=TIMESTAMP},
    update_time = #{record.updateTime,jdbcType=TIMESTAMP},
    form_flow_type = #{record.formFlowType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saasplus.CustformForm">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update custform_form
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="formName != null">
        form_name = #{formName,jdbcType=VARCHAR},
      </if>
      <if test="formNameEn != null">
        form_name_en = #{formNameEn,jdbcType=VARCHAR},
      </if>
      <if test="formCode != null">
        form_code = #{formCode,jdbcType=VARCHAR},
      </if>
      <if test="formType != null">
        form_type = #{formType,jdbcType=INTEGER},
      </if>
      <if test="travelType != null">
        travel_type = #{travelType,jdbcType=INTEGER},
      </if>
      <if test="dockingType != null">
        docking_type = #{dockingType,jdbcType=INTEGER},
      </if>
      <if test="limitUseType != null">
        limit_use_type = #{limitUseType,jdbcType=INTEGER},
      </if>
      <if test="groupType != null">
        group_type = #{groupType,jdbcType=INTEGER},
      </if>
      <if test="formObjId != null">
        form_obj_id = #{formObjId,jdbcType=VARCHAR},
      </if>
      <if test="formObjCode != null">
        form_obj_code = #{formObjCode,jdbcType=VARCHAR},
      </if>
      <if test="formApplyType != null">
        form_apply_type = #{formApplyType,jdbcType=VARCHAR},
      </if>
      <if test="formCategoryType != null">
        form_category_type = #{formCategoryType,jdbcType=VARCHAR},
      </if>
      <if test="formGroupId != null">
        form_group_id = #{formGroupId,jdbcType=INTEGER},
      </if>
      <if test="formStatus != null">
        form_status = #{formStatus,jdbcType=INTEGER},
      </if>
      <if test="iconUrl != null">
        icon_url = #{iconUrl,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="formFlowType != null">
        form_flow_type = #{formFlowType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.saasplus.CustformForm">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update custform_form
    set company_id = #{companyId,jdbcType=VARCHAR},
    form_name = #{formName,jdbcType=VARCHAR},
    form_name_en = #{formNameEn,jdbcType=VARCHAR},
    form_code = #{formCode,jdbcType=VARCHAR},
    form_type = #{formType,jdbcType=INTEGER},
    travel_type = #{travelType,jdbcType=INTEGER},
    docking_type = #{dockingType,jdbcType=INTEGER},
    limit_use_type = #{limitUseType,jdbcType=INTEGER},
    group_type = #{groupType,jdbcType=INTEGER},
    form_obj_id = #{formObjId,jdbcType=VARCHAR},
    form_obj_code = #{formObjCode,jdbcType=VARCHAR},
    form_apply_type = #{formApplyType,jdbcType=VARCHAR},
    form_category_type = #{formCategoryType,jdbcType=VARCHAR},
    form_group_id = #{formGroupId,jdbcType=INTEGER},
    form_status = #{formStatus,jdbcType=INTEGER},
    icon_url = #{iconUrl,jdbcType=VARCHAR},
    state = #{state,jdbcType=INTEGER},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    update_time = #{updateTime,jdbcType=TIMESTAMP},
    form_flow_type = #{formFlowType,jdbcType=INTEGER}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>