<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saasplus.CustomFormMappingApplyOptionMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saasplus.CustomFormMappingApplyOption">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="apply_order_id" jdbcType="CHAR" property="applyOrderId" />
    <result column="meaning_no" jdbcType="VARCHAR" property="meaningNo" />
    <result column="form_id" jdbcType="CHAR" property="formId" />
    <result column="apply_option_snapshot" jdbcType="VARCHAR" property="applyOptionSnapshot" />
    <result column="total_estimated_option_snapshot" jdbcType="VARCHAR" property="totalEstimatedOptionSnapshot" />
    <result column="cost_attribution_option_snapshot" jdbcType="VARCHAR" property="costAttributionOptionSnapshot" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, apply_order_id, meaning_no, form_id, apply_option_snapshot, total_estimated_option_snapshot, 
    cost_attribution_option_snapshot, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.CustomFormMappingApplyOptionExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from custom_form_mapping_apply_option
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from custom_form_mapping_apply_option
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from custom_form_mapping_apply_option
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.CustomFormMappingApplyOptionExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from custom_form_mapping_apply_option
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.saasplus.CustomFormMappingApplyOption">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into custom_form_mapping_apply_option (id, apply_order_id, meaning_no, 
      form_id, apply_option_snapshot, total_estimated_option_snapshot, 
      cost_attribution_option_snapshot, create_time, 
      update_time)
    values (#{id,jdbcType=CHAR}, #{applyOrderId,jdbcType=CHAR}, #{meaningNo,jdbcType=VARCHAR}, 
      #{formId,jdbcType=CHAR}, #{applyOptionSnapshot,jdbcType=VARCHAR}, #{totalEstimatedOptionSnapshot,jdbcType=VARCHAR}, 
      #{costAttributionOptionSnapshot,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.saasplus.CustomFormMappingApplyOption">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into custom_form_mapping_apply_option
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="applyOrderId != null">
        apply_order_id,
      </if>
      <if test="meaningNo != null">
        meaning_no,
      </if>
      <if test="formId != null">
        form_id,
      </if>
      <if test="applyOptionSnapshot != null">
        apply_option_snapshot,
      </if>
      <if test="totalEstimatedOptionSnapshot != null">
        total_estimated_option_snapshot,
      </if>
      <if test="costAttributionOptionSnapshot != null">
        cost_attribution_option_snapshot,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="applyOrderId != null">
        #{applyOrderId,jdbcType=CHAR},
      </if>
      <if test="meaningNo != null">
        #{meaningNo,jdbcType=VARCHAR},
      </if>
      <if test="formId != null">
        #{formId,jdbcType=CHAR},
      </if>
      <if test="applyOptionSnapshot != null">
        #{applyOptionSnapshot,jdbcType=VARCHAR},
      </if>
      <if test="totalEstimatedOptionSnapshot != null">
        #{totalEstimatedOptionSnapshot,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionOptionSnapshot != null">
        #{costAttributionOptionSnapshot,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.CustomFormMappingApplyOptionExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from custom_form_mapping_apply_option
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update custom_form_mapping_apply_option
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.applyOrderId != null">
        apply_order_id = #{record.applyOrderId,jdbcType=CHAR},
      </if>
      <if test="record.meaningNo != null">
        meaning_no = #{record.meaningNo,jdbcType=VARCHAR},
      </if>
      <if test="record.formId != null">
        form_id = #{record.formId,jdbcType=CHAR},
      </if>
      <if test="record.applyOptionSnapshot != null">
        apply_option_snapshot = #{record.applyOptionSnapshot,jdbcType=VARCHAR},
      </if>
      <if test="record.totalEstimatedOptionSnapshot != null">
        total_estimated_option_snapshot = #{record.totalEstimatedOptionSnapshot,jdbcType=VARCHAR},
      </if>
      <if test="record.costAttributionOptionSnapshot != null">
        cost_attribution_option_snapshot = #{record.costAttributionOptionSnapshot,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update custom_form_mapping_apply_option
    set id = #{record.id,jdbcType=CHAR},
      apply_order_id = #{record.applyOrderId,jdbcType=CHAR},
      meaning_no = #{record.meaningNo,jdbcType=VARCHAR},
      form_id = #{record.formId,jdbcType=CHAR},
      apply_option_snapshot = #{record.applyOptionSnapshot,jdbcType=VARCHAR},
      total_estimated_option_snapshot = #{record.totalEstimatedOptionSnapshot,jdbcType=VARCHAR},
      cost_attribution_option_snapshot = #{record.costAttributionOptionSnapshot,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saasplus.CustomFormMappingApplyOption">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update custom_form_mapping_apply_option
    <set>
      <if test="applyOrderId != null">
        apply_order_id = #{applyOrderId,jdbcType=CHAR},
      </if>
      <if test="meaningNo != null">
        meaning_no = #{meaningNo,jdbcType=VARCHAR},
      </if>
      <if test="formId != null">
        form_id = #{formId,jdbcType=CHAR},
      </if>
      <if test="applyOptionSnapshot != null">
        apply_option_snapshot = #{applyOptionSnapshot,jdbcType=VARCHAR},
      </if>
      <if test="totalEstimatedOptionSnapshot != null">
        total_estimated_option_snapshot = #{totalEstimatedOptionSnapshot,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionOptionSnapshot != null">
        cost_attribution_option_snapshot = #{costAttributionOptionSnapshot,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.saasplus.CustomFormMappingApplyOption">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update custom_form_mapping_apply_option
    set apply_order_id = #{applyOrderId,jdbcType=CHAR},
      meaning_no = #{meaningNo,jdbcType=VARCHAR},
      form_id = #{formId,jdbcType=CHAR},
      apply_option_snapshot = #{applyOptionSnapshot,jdbcType=VARCHAR},
      total_estimated_option_snapshot = #{totalEstimatedOptionSnapshot,jdbcType=VARCHAR},
      cost_attribution_option_snapshot = #{costAttributionOptionSnapshot,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>