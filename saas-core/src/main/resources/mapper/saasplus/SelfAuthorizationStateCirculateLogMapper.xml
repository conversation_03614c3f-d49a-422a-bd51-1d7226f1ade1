<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saasplus.SelfAuthorizationStateCirculateLogMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saasplus.SelfAuthorizationStateCirculateLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="self_authorization_id" jdbcType="CHAR" property="selfAuthorizationId" />
    <result column="self_authorization_type" jdbcType="INTEGER" property="selfAuthorizationType" />
    <result column="auth_permisson_type" jdbcType="INTEGER" property="authPermissonType" />
    <result column="extend_time" jdbcType="INTEGER" property="extendTime" />
    <result column="operate_employee_id" jdbcType="CHAR" property="operateEmployeeId" />
    <result column="change_operate_time" jdbcType="TIMESTAMP" property="changeOperateTime" />
    <result column="action" jdbcType="INTEGER" property="action" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, self_authorization_id, self_authorization_type, auth_permisson_type, extend_time, 
    operate_employee_id, change_operate_time, action, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.SelfAuthorizationStateCirculateLogExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from self_authorization_state_circulate_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from self_authorization_state_circulate_log
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from self_authorization_state_circulate_log
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.SelfAuthorizationStateCirculateLogExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from self_authorization_state_circulate_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.saasplus.SelfAuthorizationStateCirculateLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into self_authorization_state_circulate_log (id, self_authorization_id, self_authorization_type, 
      auth_permisson_type, extend_time, operate_employee_id, 
      change_operate_time, action, create_time, 
      update_time)
    values (#{id,jdbcType=CHAR}, #{selfAuthorizationId,jdbcType=CHAR}, #{selfAuthorizationType,jdbcType=INTEGER}, 
      #{authPermissonType,jdbcType=INTEGER}, #{extendTime,jdbcType=INTEGER}, #{operateEmployeeId,jdbcType=CHAR}, 
      #{changeOperateTime,jdbcType=TIMESTAMP}, #{action,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.saasplus.SelfAuthorizationStateCirculateLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into self_authorization_state_circulate_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="selfAuthorizationId != null">
        self_authorization_id,
      </if>
      <if test="selfAuthorizationType != null">
        self_authorization_type,
      </if>
      <if test="authPermissonType != null">
        auth_permisson_type,
      </if>
      <if test="extendTime != null">
        extend_time,
      </if>
      <if test="operateEmployeeId != null">
        operate_employee_id,
      </if>
      <if test="changeOperateTime != null">
        change_operate_time,
      </if>
      <if test="action != null">
        action,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="selfAuthorizationId != null">
        #{selfAuthorizationId,jdbcType=CHAR},
      </if>
      <if test="selfAuthorizationType != null">
        #{selfAuthorizationType,jdbcType=INTEGER},
      </if>
      <if test="authPermissonType != null">
        #{authPermissonType,jdbcType=INTEGER},
      </if>
      <if test="extendTime != null">
        #{extendTime,jdbcType=INTEGER},
      </if>
      <if test="operateEmployeeId != null">
        #{operateEmployeeId,jdbcType=CHAR},
      </if>
      <if test="changeOperateTime != null">
        #{changeOperateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="action != null">
        #{action,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.SelfAuthorizationStateCirculateLogExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from self_authorization_state_circulate_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update self_authorization_state_circulate_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.selfAuthorizationId != null">
        self_authorization_id = #{record.selfAuthorizationId,jdbcType=CHAR},
      </if>
      <if test="record.selfAuthorizationType != null">
        self_authorization_type = #{record.selfAuthorizationType,jdbcType=INTEGER},
      </if>
      <if test="record.authPermissonType != null">
        auth_permisson_type = #{record.authPermissonType,jdbcType=INTEGER},
      </if>
      <if test="record.extendTime != null">
        extend_time = #{record.extendTime,jdbcType=INTEGER},
      </if>
      <if test="record.operateEmployeeId != null">
        operate_employee_id = #{record.operateEmployeeId,jdbcType=CHAR},
      </if>
      <if test="record.changeOperateTime != null">
        change_operate_time = #{record.changeOperateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.action != null">
        action = #{record.action,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update self_authorization_state_circulate_log
    set id = #{record.id,jdbcType=CHAR},
      self_authorization_id = #{record.selfAuthorizationId,jdbcType=CHAR},
      self_authorization_type = #{record.selfAuthorizationType,jdbcType=INTEGER},
      auth_permisson_type = #{record.authPermissonType,jdbcType=INTEGER},
      extend_time = #{record.extendTime,jdbcType=INTEGER},
      operate_employee_id = #{record.operateEmployeeId,jdbcType=CHAR},
      change_operate_time = #{record.changeOperateTime,jdbcType=TIMESTAMP},
      action = #{record.action,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saasplus.SelfAuthorizationStateCirculateLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update self_authorization_state_circulate_log
    <set>
      <if test="selfAuthorizationId != null">
        self_authorization_id = #{selfAuthorizationId,jdbcType=CHAR},
      </if>
      <if test="selfAuthorizationType != null">
        self_authorization_type = #{selfAuthorizationType,jdbcType=INTEGER},
      </if>
      <if test="authPermissonType != null">
        auth_permisson_type = #{authPermissonType,jdbcType=INTEGER},
      </if>
      <if test="extendTime != null">
        extend_time = #{extendTime,jdbcType=INTEGER},
      </if>
      <if test="operateEmployeeId != null">
        operate_employee_id = #{operateEmployeeId,jdbcType=CHAR},
      </if>
      <if test="changeOperateTime != null">
        change_operate_time = #{changeOperateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="action != null">
        action = #{action,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.saasplus.SelfAuthorizationStateCirculateLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update self_authorization_state_circulate_log
    set self_authorization_id = #{selfAuthorizationId,jdbcType=CHAR},
      self_authorization_type = #{selfAuthorizationType,jdbcType=INTEGER},
      auth_permisson_type = #{authPermissonType,jdbcType=INTEGER},
      extend_time = #{extendTime,jdbcType=INTEGER},
      operate_employee_id = #{operateEmployeeId,jdbcType=CHAR},
      change_operate_time = #{changeOperateTime,jdbcType=TIMESTAMP},
      action = #{action,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>