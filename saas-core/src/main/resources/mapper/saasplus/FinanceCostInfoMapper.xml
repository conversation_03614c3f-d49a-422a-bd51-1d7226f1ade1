<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saasplus.FinanceCostInfoMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saasplus.FinanceCostInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="cost_no" jdbcType="VARCHAR" property="costNo" />
    <result column="apply_order_id" jdbcType="VARCHAR" property="applyOrderId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="cost_cause_id" jdbcType="INTEGER" property="costCauseId" />
    <result column="cost_cause" jdbcType="VARCHAR" property="costCause" />
    <result column="cost_cause_ext" jdbcType="VARCHAR" property="costCauseExt" />
    <result column="cost_category_id" jdbcType="INTEGER" property="costCategoryId" />
    <result column="cost_category" jdbcType="VARCHAR" property="costCategory" />
    <result column="cost_category_ext" jdbcType="VARCHAR" property="costCategoryExt" />
    <result column="cost_desc" jdbcType="VARCHAR" property="costDesc" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="delete_tag" jdbcType="INTEGER" property="deleteTag" />
    <result column="have_invoice" jdbcType="INTEGER" property="haveInvoice" />
    <result column="return_ticket_price" jdbcType="DECIMAL" property="returnTicketPrice" />
    <result column="cost_attribution_id" jdbcType="INTEGER" property="costAttributionId" />
    <result column="cost_attribution_name" jdbcType="VARCHAR" property="costAttributionName" />
    <result column="cost_attribution_desc" jdbcType="VARCHAR" property="costAttributionDesc" />
    <result column="reserve_reason_id" jdbcType="INTEGER" property="reserveReasonId" />
    <result column="reserve_reason_name" jdbcType="VARCHAR" property="reserveReasonName" />
    <result column="reserve_reason_desc" jdbcType="VARCHAR" property="reserveReasonDesc" />
    <result column="supplement_reason_id" jdbcType="INTEGER" property="supplementReasonId" />
    <result column="supplement_reason_name" jdbcType="VARCHAR" property="supplementReasonName" />
    <result column="supplement_reason_desc" jdbcType="VARCHAR" property="supplementReasonDesc" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="bound_status" jdbcType="INTEGER" property="boundStatus" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="return_ticket_status" jdbcType="INTEGER" property="returnTicketStatus" />
    <result column="invoice_category" jdbcType="VARCHAR" property="invoiceCategory" />
    <result column="deductible_tax" jdbcType="DECIMAL" property="deductibleTax" />
    <result column="cost_info_type" jdbcType="INTEGER" property="costInfoType" />
    <result column="verify_status" jdbcType="INTEGER" property="verifyStatus" />
    <result column="is_ding" jdbcType="TINYINT" property="isDing" />
    <result column="reimburser_id" jdbcType="VARCHAR" property="reimburserId" />
    <result column="reimburser_name" jdbcType="VARCHAR" property="reimburserName" />
    <result column="reimburser_snap_content" jdbcType="VARCHAR" property="reimburserSnapContent" />
    <result column="subject_id" jdbcType="VARCHAR" property="subjectId" />
    <result column="subject_name" jdbcType="VARCHAR" property="subjectName" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="meta_info" jdbcType="VARCHAR" property="metaInfo" />
    <result column="form_id" jdbcType="VARCHAR" property="formId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, cost_no, apply_order_id, type, status, total_amount, cost_cause_id, cost_cause, 
    cost_cause_ext, cost_category_id, cost_category, cost_category_ext, cost_desc, reason, 
    create_time, update_time, create_user, update_user, delete_tag, have_invoice, return_ticket_price, 
    cost_attribution_id, cost_attribution_name, cost_attribution_desc, reserve_reason_id, 
    reserve_reason_name, reserve_reason_desc, supplement_reason_id, supplement_reason_name, 
    supplement_reason_desc, company_id, bound_status, state, return_ticket_status, invoice_category, 
    deductible_tax, cost_info_type, verify_status, is_ding, reimburser_id, reimburser_name, 
    reimburser_snap_content, subject_id, subject_name, icon, meta_info, form_id
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.FinanceCostInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from finance_cost_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from finance_cost_info
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from finance_cost_info
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.FinanceCostInfoExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from finance_cost_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.fenbeitong.saas.core.model.saasplus.FinanceCostInfo" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into finance_cost_info (cost_no, apply_order_id, type, 
      status, total_amount, cost_cause_id, 
      cost_cause, cost_cause_ext, cost_category_id, 
      cost_category, cost_category_ext, cost_desc, 
      reason, create_time, update_time, 
      create_user, update_user, delete_tag, 
      have_invoice, return_ticket_price, cost_attribution_id, 
      cost_attribution_name, cost_attribution_desc, 
      reserve_reason_id, reserve_reason_name, reserve_reason_desc, 
      supplement_reason_id, supplement_reason_name, 
      supplement_reason_desc, company_id, bound_status, 
      state, return_ticket_status, invoice_category, 
      deductible_tax, cost_info_type, verify_status, 
      is_ding, reimburser_id, reimburser_name, 
      reimburser_snap_content, subject_id, subject_name, 
      icon, meta_info, form_id
      )
    values (#{costNo,jdbcType=VARCHAR}, #{applyOrderId,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, 
      #{status,jdbcType=INTEGER}, #{totalAmount,jdbcType=DECIMAL}, #{costCauseId,jdbcType=INTEGER}, 
      #{costCause,jdbcType=VARCHAR}, #{costCauseExt,jdbcType=VARCHAR}, #{costCategoryId,jdbcType=INTEGER}, 
      #{costCategory,jdbcType=VARCHAR}, #{costCategoryExt,jdbcType=VARCHAR}, #{costDesc,jdbcType=VARCHAR}, 
      #{reason,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{createUser,jdbcType=VARCHAR}, #{updateUser,jdbcType=VARCHAR}, #{deleteTag,jdbcType=BIT}, 
      #{haveInvoice,jdbcType=INTEGER}, #{returnTicketPrice,jdbcType=DECIMAL}, #{costAttributionId,jdbcType=INTEGER}, 
      #{costAttributionName,jdbcType=VARCHAR}, #{costAttributionDesc,jdbcType=VARCHAR}, 
      #{reserveReasonId,jdbcType=INTEGER}, #{reserveReasonName,jdbcType=VARCHAR}, #{reserveReasonDesc,jdbcType=VARCHAR}, 
      #{supplementReasonId,jdbcType=INTEGER}, #{supplementReasonName,jdbcType=VARCHAR}, 
      #{supplementReasonDesc,jdbcType=VARCHAR}, #{companyId,jdbcType=VARCHAR}, #{boundStatus,jdbcType=INTEGER}, 
      #{state,jdbcType=INTEGER}, #{returnTicketStatus,jdbcType=INTEGER}, #{invoiceCategory,jdbcType=VARCHAR}, 
      #{deductibleTax,jdbcType=DECIMAL}, #{costInfoType,jdbcType=INTEGER}, #{verifyStatus,jdbcType=INTEGER}, 
      #{isDing,jdbcType=TINYINT}, #{reimburserId,jdbcType=VARCHAR}, #{reimburserName,jdbcType=VARCHAR}, 
      #{reimburserSnapContent,jdbcType=VARCHAR}, #{subjectId,jdbcType=VARCHAR}, #{subjectName,jdbcType=VARCHAR}, 
      #{icon,jdbcType=VARCHAR}, #{metaInfo,jdbcType=VARCHAR}, #{formId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.fenbeitong.saas.core.model.saasplus.FinanceCostInfo" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into finance_cost_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="costNo != null">
        cost_no,
      </if>
      <if test="applyOrderId != null">
        apply_order_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="costCauseId != null">
        cost_cause_id,
      </if>
      <if test="costCause != null">
        cost_cause,
      </if>
      <if test="costCauseExt != null">
        cost_cause_ext,
      </if>
      <if test="costCategoryId != null">
        cost_category_id,
      </if>
      <if test="costCategory != null">
        cost_category,
      </if>
      <if test="costCategoryExt != null">
        cost_category_ext,
      </if>
      <if test="costDesc != null">
        cost_desc,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="deleteTag != null">
        delete_tag,
      </if>
      <if test="haveInvoice != null">
        have_invoice,
      </if>
      <if test="returnTicketPrice != null">
        return_ticket_price,
      </if>
      <if test="costAttributionId != null">
        cost_attribution_id,
      </if>
      <if test="costAttributionName != null">
        cost_attribution_name,
      </if>
      <if test="costAttributionDesc != null">
        cost_attribution_desc,
      </if>
      <if test="reserveReasonId != null">
        reserve_reason_id,
      </if>
      <if test="reserveReasonName != null">
        reserve_reason_name,
      </if>
      <if test="reserveReasonDesc != null">
        reserve_reason_desc,
      </if>
      <if test="supplementReasonId != null">
        supplement_reason_id,
      </if>
      <if test="supplementReasonName != null">
        supplement_reason_name,
      </if>
      <if test="supplementReasonDesc != null">
        supplement_reason_desc,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="boundStatus != null">
        bound_status,
      </if>
      <if test="state != null">
        state,
      </if>
      <if test="returnTicketStatus != null">
        return_ticket_status,
      </if>
      <if test="invoiceCategory != null">
        invoice_category,
      </if>
      <if test="deductibleTax != null">
        deductible_tax,
      </if>
      <if test="costInfoType != null">
        cost_info_type,
      </if>
      <if test="verifyStatus != null">
        verify_status,
      </if>
      <if test="isDing != null">
        is_ding,
      </if>
      <if test="reimburserId != null">
        reimburser_id,
      </if>
      <if test="reimburserName != null">
        reimburser_name,
      </if>
      <if test="reimburserSnapContent != null">
        reimburser_snap_content,
      </if>
      <if test="subjectId != null">
        subject_id,
      </if>
      <if test="subjectName != null">
        subject_name,
      </if>
      <if test="icon != null">
        icon,
      </if>
      <if test="metaInfo != null">
        meta_info,
      </if>
      <if test="formId != null">
        form_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="costNo != null">
        #{costNo,jdbcType=VARCHAR},
      </if>
      <if test="applyOrderId != null">
        #{applyOrderId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="costCauseId != null">
        #{costCauseId,jdbcType=INTEGER},
      </if>
      <if test="costCause != null">
        #{costCause,jdbcType=VARCHAR},
      </if>
      <if test="costCauseExt != null">
        #{costCauseExt,jdbcType=VARCHAR},
      </if>
      <if test="costCategoryId != null">
        #{costCategoryId,jdbcType=INTEGER},
      </if>
      <if test="costCategory != null">
        #{costCategory,jdbcType=VARCHAR},
      </if>
      <if test="costCategoryExt != null">
        #{costCategoryExt,jdbcType=VARCHAR},
      </if>
      <if test="costDesc != null">
        #{costDesc,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="deleteTag != null">
        #{deleteTag,jdbcType=BIT},
      </if>
      <if test="haveInvoice != null">
        #{haveInvoice,jdbcType=INTEGER},
      </if>
      <if test="returnTicketPrice != null">
        #{returnTicketPrice,jdbcType=DECIMAL},
      </if>
      <if test="costAttributionId != null">
        #{costAttributionId,jdbcType=INTEGER},
      </if>
      <if test="costAttributionName != null">
        #{costAttributionName,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionDesc != null">
        #{costAttributionDesc,jdbcType=VARCHAR},
      </if>
      <if test="reserveReasonId != null">
        #{reserveReasonId,jdbcType=INTEGER},
      </if>
      <if test="reserveReasonName != null">
        #{reserveReasonName,jdbcType=VARCHAR},
      </if>
      <if test="reserveReasonDesc != null">
        #{reserveReasonDesc,jdbcType=VARCHAR},
      </if>
      <if test="supplementReasonId != null">
        #{supplementReasonId,jdbcType=INTEGER},
      </if>
      <if test="supplementReasonName != null">
        #{supplementReasonName,jdbcType=VARCHAR},
      </if>
      <if test="supplementReasonDesc != null">
        #{supplementReasonDesc,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="boundStatus != null">
        #{boundStatus,jdbcType=INTEGER},
      </if>
      <if test="state != null">
        #{state,jdbcType=INTEGER},
      </if>
      <if test="returnTicketStatus != null">
        #{returnTicketStatus,jdbcType=INTEGER},
      </if>
      <if test="invoiceCategory != null">
        #{invoiceCategory,jdbcType=VARCHAR},
      </if>
      <if test="deductibleTax != null">
        #{deductibleTax,jdbcType=DECIMAL},
      </if>
      <if test="costInfoType != null">
        #{costInfoType,jdbcType=INTEGER},
      </if>
      <if test="verifyStatus != null">
        #{verifyStatus,jdbcType=INTEGER},
      </if>
      <if test="isDing != null">
        #{isDing,jdbcType=TINYINT},
      </if>
      <if test="reimburserId != null">
        #{reimburserId,jdbcType=VARCHAR},
      </if>
      <if test="reimburserName != null">
        #{reimburserName,jdbcType=VARCHAR},
      </if>
      <if test="reimburserSnapContent != null">
        #{reimburserSnapContent,jdbcType=VARCHAR},
      </if>
      <if test="subjectId != null">
        #{subjectId,jdbcType=VARCHAR},
      </if>
      <if test="subjectName != null">
        #{subjectName,jdbcType=VARCHAR},
      </if>
      <if test="icon != null">
        #{icon,jdbcType=VARCHAR},
      </if>
      <if test="metaInfo != null">
        #{metaInfo,jdbcType=VARCHAR},
      </if>
      <if test="formId != null">
        #{formId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.FinanceCostInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from finance_cost_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update finance_cost_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.costNo != null">
        cost_no = #{record.costNo,jdbcType=VARCHAR},
      </if>
      <if test="record.applyOrderId != null">
        apply_order_id = #{record.applyOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.totalAmount != null">
        total_amount = #{record.totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.costCauseId != null">
        cost_cause_id = #{record.costCauseId,jdbcType=INTEGER},
      </if>
      <if test="record.costCause != null">
        cost_cause = #{record.costCause,jdbcType=VARCHAR},
      </if>
      <if test="record.costCauseExt != null">
        cost_cause_ext = #{record.costCauseExt,jdbcType=VARCHAR},
      </if>
      <if test="record.costCategoryId != null">
        cost_category_id = #{record.costCategoryId,jdbcType=INTEGER},
      </if>
      <if test="record.costCategory != null">
        cost_category = #{record.costCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.costCategoryExt != null">
        cost_category_ext = #{record.costCategoryExt,jdbcType=VARCHAR},
      </if>
      <if test="record.costDesc != null">
        cost_desc = #{record.costDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.reason != null">
        reason = #{record.reason,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.deleteTag != null">
        delete_tag = #{record.deleteTag,jdbcType=BIT},
      </if>
      <if test="record.haveInvoice != null">
        have_invoice = #{record.haveInvoice,jdbcType=INTEGER},
      </if>
      <if test="record.returnTicketPrice != null">
        return_ticket_price = #{record.returnTicketPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.costAttributionId != null">
        cost_attribution_id = #{record.costAttributionId,jdbcType=INTEGER},
      </if>
      <if test="record.costAttributionName != null">
        cost_attribution_name = #{record.costAttributionName,jdbcType=VARCHAR},
      </if>
      <if test="record.costAttributionDesc != null">
        cost_attribution_desc = #{record.costAttributionDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveReasonId != null">
        reserve_reason_id = #{record.reserveReasonId,jdbcType=INTEGER},
      </if>
      <if test="record.reserveReasonName != null">
        reserve_reason_name = #{record.reserveReasonName,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveReasonDesc != null">
        reserve_reason_desc = #{record.reserveReasonDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.supplementReasonId != null">
        supplement_reason_id = #{record.supplementReasonId,jdbcType=INTEGER},
      </if>
      <if test="record.supplementReasonName != null">
        supplement_reason_name = #{record.supplementReasonName,jdbcType=VARCHAR},
      </if>
      <if test="record.supplementReasonDesc != null">
        supplement_reason_desc = #{record.supplementReasonDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.boundStatus != null">
        bound_status = #{record.boundStatus,jdbcType=INTEGER},
      </if>
      <if test="record.state != null">
        state = #{record.state,jdbcType=INTEGER},
      </if>
      <if test="record.returnTicketStatus != null">
        return_ticket_status = #{record.returnTicketStatus,jdbcType=INTEGER},
      </if>
      <if test="record.invoiceCategory != null">
        invoice_category = #{record.invoiceCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.deductibleTax != null">
        deductible_tax = #{record.deductibleTax,jdbcType=DECIMAL},
      </if>
      <if test="record.costInfoType != null">
        cost_info_type = #{record.costInfoType,jdbcType=INTEGER},
      </if>
      <if test="record.verifyStatus != null">
        verify_status = #{record.verifyStatus,jdbcType=INTEGER},
      </if>
      <if test="record.isDing != null">
        is_ding = #{record.isDing,jdbcType=TINYINT},
      </if>
      <if test="record.reimburserId != null">
        reimburser_id = #{record.reimburserId,jdbcType=VARCHAR},
      </if>
      <if test="record.reimburserName != null">
        reimburser_name = #{record.reimburserName,jdbcType=VARCHAR},
      </if>
      <if test="record.reimburserSnapContent != null">
        reimburser_snap_content = #{record.reimburserSnapContent,jdbcType=VARCHAR},
      </if>
      <if test="record.subjectId != null">
        subject_id = #{record.subjectId,jdbcType=VARCHAR},
      </if>
      <if test="record.subjectName != null">
        subject_name = #{record.subjectName,jdbcType=VARCHAR},
      </if>
      <if test="record.icon != null">
        icon = #{record.icon,jdbcType=VARCHAR},
      </if>
      <if test="record.metaInfo != null">
        meta_info = #{record.metaInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.formId != null">
        form_id = #{record.formId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update finance_cost_info
    set id = #{record.id,jdbcType=INTEGER},
      cost_no = #{record.costNo,jdbcType=VARCHAR},
      apply_order_id = #{record.applyOrderId,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER},
      total_amount = #{record.totalAmount,jdbcType=DECIMAL},
      cost_cause_id = #{record.costCauseId,jdbcType=INTEGER},
      cost_cause = #{record.costCause,jdbcType=VARCHAR},
      cost_cause_ext = #{record.costCauseExt,jdbcType=VARCHAR},
      cost_category_id = #{record.costCategoryId,jdbcType=INTEGER},
      cost_category = #{record.costCategory,jdbcType=VARCHAR},
      cost_category_ext = #{record.costCategoryExt,jdbcType=VARCHAR},
      cost_desc = #{record.costDesc,jdbcType=VARCHAR},
      reason = #{record.reason,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      delete_tag = #{record.deleteTag,jdbcType=BIT},
      have_invoice = #{record.haveInvoice,jdbcType=INTEGER},
      return_ticket_price = #{record.returnTicketPrice,jdbcType=DECIMAL},
      cost_attribution_id = #{record.costAttributionId,jdbcType=INTEGER},
      cost_attribution_name = #{record.costAttributionName,jdbcType=VARCHAR},
      cost_attribution_desc = #{record.costAttributionDesc,jdbcType=VARCHAR},
      reserve_reason_id = #{record.reserveReasonId,jdbcType=INTEGER},
      reserve_reason_name = #{record.reserveReasonName,jdbcType=VARCHAR},
      reserve_reason_desc = #{record.reserveReasonDesc,jdbcType=VARCHAR},
      supplement_reason_id = #{record.supplementReasonId,jdbcType=INTEGER},
      supplement_reason_name = #{record.supplementReasonName,jdbcType=VARCHAR},
      supplement_reason_desc = #{record.supplementReasonDesc,jdbcType=VARCHAR},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      bound_status = #{record.boundStatus,jdbcType=INTEGER},
      state = #{record.state,jdbcType=INTEGER},
      return_ticket_status = #{record.returnTicketStatus,jdbcType=INTEGER},
      invoice_category = #{record.invoiceCategory,jdbcType=VARCHAR},
      deductible_tax = #{record.deductibleTax,jdbcType=DECIMAL},
      cost_info_type = #{record.costInfoType,jdbcType=INTEGER},
      verify_status = #{record.verifyStatus,jdbcType=INTEGER},
      is_ding = #{record.isDing,jdbcType=TINYINT},
      reimburser_id = #{record.reimburserId,jdbcType=VARCHAR},
      reimburser_name = #{record.reimburserName,jdbcType=VARCHAR},
      reimburser_snap_content = #{record.reimburserSnapContent,jdbcType=VARCHAR},
      subject_id = #{record.subjectId,jdbcType=VARCHAR},
      subject_name = #{record.subjectName,jdbcType=VARCHAR},
      icon = #{record.icon,jdbcType=VARCHAR},
      meta_info = #{record.metaInfo,jdbcType=VARCHAR},
      form_id = #{record.formId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saasplus.FinanceCostInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update finance_cost_info
    <set>
      <if test="costNo != null">
        cost_no = #{costNo,jdbcType=VARCHAR},
      </if>
      <if test="applyOrderId != null">
        apply_order_id = #{applyOrderId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="costCauseId != null">
        cost_cause_id = #{costCauseId,jdbcType=INTEGER},
      </if>
      <if test="costCause != null">
        cost_cause = #{costCause,jdbcType=VARCHAR},
      </if>
      <if test="costCauseExt != null">
        cost_cause_ext = #{costCauseExt,jdbcType=VARCHAR},
      </if>
      <if test="costCategoryId != null">
        cost_category_id = #{costCategoryId,jdbcType=INTEGER},
      </if>
      <if test="costCategory != null">
        cost_category = #{costCategory,jdbcType=VARCHAR},
      </if>
      <if test="costCategoryExt != null">
        cost_category_ext = #{costCategoryExt,jdbcType=VARCHAR},
      </if>
      <if test="costDesc != null">
        cost_desc = #{costDesc,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="deleteTag != null">
        delete_tag = #{deleteTag,jdbcType=BIT},
      </if>
      <if test="haveInvoice != null">
        have_invoice = #{haveInvoice,jdbcType=INTEGER},
      </if>
      <if test="returnTicketPrice != null">
        return_ticket_price = #{returnTicketPrice,jdbcType=DECIMAL},
      </if>
      <if test="costAttributionId != null">
        cost_attribution_id = #{costAttributionId,jdbcType=INTEGER},
      </if>
      <if test="costAttributionName != null">
        cost_attribution_name = #{costAttributionName,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionDesc != null">
        cost_attribution_desc = #{costAttributionDesc,jdbcType=VARCHAR},
      </if>
      <if test="reserveReasonId != null">
        reserve_reason_id = #{reserveReasonId,jdbcType=INTEGER},
      </if>
      <if test="reserveReasonName != null">
        reserve_reason_name = #{reserveReasonName,jdbcType=VARCHAR},
      </if>
      <if test="reserveReasonDesc != null">
        reserve_reason_desc = #{reserveReasonDesc,jdbcType=VARCHAR},
      </if>
      <if test="supplementReasonId != null">
        supplement_reason_id = #{supplementReasonId,jdbcType=INTEGER},
      </if>
      <if test="supplementReasonName != null">
        supplement_reason_name = #{supplementReasonName,jdbcType=VARCHAR},
      </if>
      <if test="supplementReasonDesc != null">
        supplement_reason_desc = #{supplementReasonDesc,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="boundStatus != null">
        bound_status = #{boundStatus,jdbcType=INTEGER},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=INTEGER},
      </if>
      <if test="returnTicketStatus != null">
        return_ticket_status = #{returnTicketStatus,jdbcType=INTEGER},
      </if>
      <if test="invoiceCategory != null">
        invoice_category = #{invoiceCategory,jdbcType=VARCHAR},
      </if>
      <if test="deductibleTax != null">
        deductible_tax = #{deductibleTax,jdbcType=DECIMAL},
      </if>
      <if test="costInfoType != null">
        cost_info_type = #{costInfoType,jdbcType=INTEGER},
      </if>
      <if test="verifyStatus != null">
        verify_status = #{verifyStatus,jdbcType=INTEGER},
      </if>
      <if test="isDing != null">
        is_ding = #{isDing,jdbcType=TINYINT},
      </if>
      <if test="reimburserId != null">
        reimburser_id = #{reimburserId,jdbcType=VARCHAR},
      </if>
      <if test="reimburserName != null">
        reimburser_name = #{reimburserName,jdbcType=VARCHAR},
      </if>
      <if test="reimburserSnapContent != null">
        reimburser_snap_content = #{reimburserSnapContent,jdbcType=VARCHAR},
      </if>
      <if test="subjectId != null">
        subject_id = #{subjectId,jdbcType=VARCHAR},
      </if>
      <if test="subjectName != null">
        subject_name = #{subjectName,jdbcType=VARCHAR},
      </if>
      <if test="icon != null">
        icon = #{icon,jdbcType=VARCHAR},
      </if>
      <if test="metaInfo != null">
        meta_info = #{metaInfo,jdbcType=VARCHAR},
      </if>
      <if test="formId != null">
        form_id = #{formId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.saasplus.FinanceCostInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update finance_cost_info
    set cost_no = #{costNo,jdbcType=VARCHAR},
      apply_order_id = #{applyOrderId,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      total_amount = #{totalAmount,jdbcType=DECIMAL},
      cost_cause_id = #{costCauseId,jdbcType=INTEGER},
      cost_cause = #{costCause,jdbcType=VARCHAR},
      cost_cause_ext = #{costCauseExt,jdbcType=VARCHAR},
      cost_category_id = #{costCategoryId,jdbcType=INTEGER},
      cost_category = #{costCategory,jdbcType=VARCHAR},
      cost_category_ext = #{costCategoryExt,jdbcType=VARCHAR},
      cost_desc = #{costDesc,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_user = #{createUser,jdbcType=VARCHAR},
      update_user = #{updateUser,jdbcType=VARCHAR},
      delete_tag = #{deleteTag,jdbcType=BIT},
      have_invoice = #{haveInvoice,jdbcType=INTEGER},
      return_ticket_price = #{returnTicketPrice,jdbcType=DECIMAL},
      cost_attribution_id = #{costAttributionId,jdbcType=INTEGER},
      cost_attribution_name = #{costAttributionName,jdbcType=VARCHAR},
      cost_attribution_desc = #{costAttributionDesc,jdbcType=VARCHAR},
      reserve_reason_id = #{reserveReasonId,jdbcType=INTEGER},
      reserve_reason_name = #{reserveReasonName,jdbcType=VARCHAR},
      reserve_reason_desc = #{reserveReasonDesc,jdbcType=VARCHAR},
      supplement_reason_id = #{supplementReasonId,jdbcType=INTEGER},
      supplement_reason_name = #{supplementReasonName,jdbcType=VARCHAR},
      supplement_reason_desc = #{supplementReasonDesc,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=VARCHAR},
      bound_status = #{boundStatus,jdbcType=INTEGER},
      state = #{state,jdbcType=INTEGER},
      return_ticket_status = #{returnTicketStatus,jdbcType=INTEGER},
      invoice_category = #{invoiceCategory,jdbcType=VARCHAR},
      deductible_tax = #{deductibleTax,jdbcType=DECIMAL},
      cost_info_type = #{costInfoType,jdbcType=INTEGER},
      verify_status = #{verifyStatus,jdbcType=INTEGER},
      is_ding = #{isDing,jdbcType=TINYINT},
      reimburser_id = #{reimburserId,jdbcType=VARCHAR},
      reimburser_name = #{reimburserName,jdbcType=VARCHAR},
      reimburser_snap_content = #{reimburserSnapContent,jdbcType=VARCHAR},
      subject_id = #{subjectId,jdbcType=VARCHAR},
      subject_name = #{subjectName,jdbcType=VARCHAR},
      icon = #{icon,jdbcType=VARCHAR},
      meta_info = #{metaInfo,jdbcType=VARCHAR},
      form_id = #{formId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>