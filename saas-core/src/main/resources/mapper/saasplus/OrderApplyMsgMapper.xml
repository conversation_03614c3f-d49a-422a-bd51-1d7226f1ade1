<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saasplus.OrderApplyMsgMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saasplus.OrderApplyMsg">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="category_type" jdbcType="INTEGER" property="categoryType" />
    <result column="passengers" jdbcType="VARCHAR" property="passengers" />
    <result column="start_city_id" jdbcType="VARCHAR" property="startCityId" />
    <result column="arrival_city_id" jdbcType="VARCHAR" property="arrivalCityId" />
    <result column="start_date" jdbcType="TIMESTAMP" property="startDate" />
    <result column="success" jdbcType="BIT" property="success" />
    <result column="during_apply_id" jdbcType="VARCHAR" property="duringApplyId" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="exceeded" jdbcType="BIT" property="exceeded" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, user_id, company_id, category_type, passengers, start_city_id, arrival_city_id, 
    start_date, success, during_apply_id, order_id, exceeded, price, update_time, create_time
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.OrderApplyMsgExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from order_apply_msg
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from order_apply_msg
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from order_apply_msg
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.OrderApplyMsgExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from order_apply_msg
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.saasplus.OrderApplyMsg">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into order_apply_msg (id, user_id, company_id, 
      category_type, passengers, start_city_id, 
      arrival_city_id, start_date, success, 
      during_apply_id, order_id, exceeded, 
      price, update_time, create_time
      )
    values (#{id,jdbcType=CHAR}, #{userId,jdbcType=VARCHAR}, #{companyId,jdbcType=VARCHAR}, 
      #{categoryType,jdbcType=INTEGER}, #{passengers,jdbcType=VARCHAR}, #{startCityId,jdbcType=VARCHAR}, 
      #{arrivalCityId,jdbcType=VARCHAR}, #{startDate,jdbcType=TIMESTAMP}, #{success,jdbcType=BIT}, 
      #{duringApplyId,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR}, #{exceeded,jdbcType=BIT}, 
      #{price,jdbcType=DECIMAL}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.saasplus.OrderApplyMsg">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into order_apply_msg
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="categoryType != null">
        category_type,
      </if>
      <if test="passengers != null">
        passengers,
      </if>
      <if test="startCityId != null">
        start_city_id,
      </if>
      <if test="arrivalCityId != null">
        arrival_city_id,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="success != null">
        success,
      </if>
      <if test="duringApplyId != null">
        during_apply_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="exceeded != null">
        exceeded,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="categoryType != null">
        #{categoryType,jdbcType=INTEGER},
      </if>
      <if test="passengers != null">
        #{passengers,jdbcType=VARCHAR},
      </if>
      <if test="startCityId != null">
        #{startCityId,jdbcType=VARCHAR},
      </if>
      <if test="arrivalCityId != null">
        #{arrivalCityId,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="success != null">
        #{success,jdbcType=BIT},
      </if>
      <if test="duringApplyId != null">
        #{duringApplyId,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="exceeded != null">
        #{exceeded,jdbcType=BIT},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.OrderApplyMsgExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from order_apply_msg
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update order_apply_msg
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryType != null">
        category_type = #{record.categoryType,jdbcType=INTEGER},
      </if>
      <if test="record.passengers != null">
        passengers = #{record.passengers,jdbcType=VARCHAR},
      </if>
      <if test="record.startCityId != null">
        start_city_id = #{record.startCityId,jdbcType=VARCHAR},
      </if>
      <if test="record.arrivalCityId != null">
        arrival_city_id = #{record.arrivalCityId,jdbcType=VARCHAR},
      </if>
      <if test="record.startDate != null">
        start_date = #{record.startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.success != null">
        success = #{record.success,jdbcType=BIT},
      </if>
      <if test="record.duringApplyId != null">
        during_apply_id = #{record.duringApplyId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.exceeded != null">
        exceeded = #{record.exceeded,jdbcType=BIT},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=DECIMAL},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update order_apply_msg
    set id = #{record.id,jdbcType=CHAR},
      user_id = #{record.userId,jdbcType=VARCHAR},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      category_type = #{record.categoryType,jdbcType=INTEGER},
      passengers = #{record.passengers,jdbcType=VARCHAR},
      start_city_id = #{record.startCityId,jdbcType=VARCHAR},
      arrival_city_id = #{record.arrivalCityId,jdbcType=VARCHAR},
      start_date = #{record.startDate,jdbcType=TIMESTAMP},
      success = #{record.success,jdbcType=BIT},
      during_apply_id = #{record.duringApplyId,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      exceeded = #{record.exceeded,jdbcType=BIT},
      price = #{record.price,jdbcType=DECIMAL},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saasplus.OrderApplyMsg">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update order_apply_msg
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="categoryType != null">
        category_type = #{categoryType,jdbcType=INTEGER},
      </if>
      <if test="passengers != null">
        passengers = #{passengers,jdbcType=VARCHAR},
      </if>
      <if test="startCityId != null">
        start_city_id = #{startCityId,jdbcType=VARCHAR},
      </if>
      <if test="arrivalCityId != null">
        arrival_city_id = #{arrivalCityId,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="success != null">
        success = #{success,jdbcType=BIT},
      </if>
      <if test="duringApplyId != null">
        during_apply_id = #{duringApplyId,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="exceeded != null">
        exceeded = #{exceeded,jdbcType=BIT},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.saasplus.OrderApplyMsg">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update order_apply_msg
    set user_id = #{userId,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=VARCHAR},
      category_type = #{categoryType,jdbcType=INTEGER},
      passengers = #{passengers,jdbcType=VARCHAR},
      start_city_id = #{startCityId,jdbcType=VARCHAR},
      arrival_city_id = #{arrivalCityId,jdbcType=VARCHAR},
      start_date = #{startDate,jdbcType=TIMESTAMP},
      success = #{success,jdbcType=BIT},
      during_apply_id = #{duringApplyId,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=VARCHAR},
      exceeded = #{exceeded,jdbcType=BIT},
      price = #{price,jdbcType=DECIMAL},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>