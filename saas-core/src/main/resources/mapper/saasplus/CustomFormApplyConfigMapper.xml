<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saasplus.CustomFormApplyConfigMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saasplus.CustomFormApplyConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="form_id" jdbcType="VARCHAR" property="formId" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="city_type" jdbcType="INTEGER" property="cityType" />
    <result column="start_date_type" jdbcType="INTEGER" property="startDateType" />
    <result column="return_trip_date_type" jdbcType="INTEGER" property="returnTripDateType" />
    <result column="food_delivery_date_type" jdbcType="INTEGER" property="foodDeliveryDateType" />
    <result column="estimated_cost_check_switch" jdbcType="INTEGER" property="estimatedCostCheckSwitch" />
    <result column="all_order_estimated_cost_check" jdbcType="INTEGER" property="allOrderEstimatedCostCheck" />
    <result column="travel_form_estimated_cost_check_type" jdbcType="INTEGER" property="travelFormEstimatedCostCheckType" />
    <result column="multi_trip_apply_estimated_cost_check" jdbcType="INTEGER" property="multiTripApplyEstimatedCostCheck" />
    <result column="air_change_estimated_cost_check_switch" jdbcType="INTEGER" property="airChangeEstimatedCostCheckSwitch" />
    <result column="train_change_estimated_cost_check_switch" jdbcType="INTEGER" property="trainChangeEstimatedCostCheckSwitch" />
    <result column="personal_pay_switch" jdbcType="INTEGER" property="personalPaySwitch" />
    <result column="traveler_control_flag" jdbcType="INTEGER" property="travelerControlFlag" />
    <result column="traveler_fill_type" jdbcType="INTEGER" property="travelerFillType" />
    <result column="order_check_traveler_flag" jdbcType="INTEGER" property="orderCheckTravelerFlag" />
    <result column="traveler_limit_flag" jdbcType="INTEGER" property="travelerLimitFlag" />
    <result column="total_estimated_control_flag" jdbcType="INTEGER" property="totalEstimatedControlFlag" />
    <result column="total_estimated_fill_type" jdbcType="INTEGER" property="totalEstimatedFillType" />
    <result column="check_total_estimated_flag" jdbcType="INTEGER" property="checkTotalEstimatedFlag" />
    <result column="total_estimated_limit_type" jdbcType="INTEGER" property="totalEstimatedLimitType" />
    <result column="total_estimated_limit_amount" jdbcType="DECIMAL" property="totalEstimatedLimitAmount" />
    <result column="total_estimated_check_scene" jdbcType="VARCHAR" property="totalEstimatedCheckScene" />
    <result column="max_average_daily_travel_cost_amount" jdbcType="BIGINT" property="maxAverageDailyTravelCostAmount" />
    <result column="only_check_estimated_amount_switch" jdbcType="INTEGER" property="onlyCheckEstimatedAmountSwitch" />
    <result column="total_estimated_personal_pay_switch" jdbcType="INTEGER" property="totalEstimatedPersonalPaySwitch" />
    <result column="total_estimated_personal_pay_scene_list" jdbcType="VARCHAR" property="totalEstimatedPersonalPaySceneList" />
    <result column="apply_reason_bring_in_order" jdbcType="INTEGER" property="applyReasonBringInOrder" />
    <result column="cost_attribution_control_flag" jdbcType="INTEGER" property="costAttributionControlFlag" />
    <result column="cost_attribution_fill_type" jdbcType="INTEGER" property="costAttributionFillType" />
    <result column="cost_attribution_bring_in_order_flag" jdbcType="INTEGER" property="costAttributionBringInOrderFlag" />
    <result column="takeaway_use_count_limit_type" jdbcType="INTEGER" property="takeawayUseCountLimitType" />
    <result column="meishi_use_count_limit_type" jdbcType="INTEGER" property="meishiUseCountLimitType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="travel_date_control_flag" jdbcType="INTEGER" property="travelDateControlFlag" />
    <result column="travel_date_fill_type" jdbcType="INTEGER" property="travelDateFillType" />
    <result column="travel_date_duplicate_verification" jdbcType="INTEGER" property="travelDateDuplicateVerification" />
    <result column="travel_date_auto_calculation" jdbcType="INTEGER" property="travelDateAutoCalculation" />
    <result column="travel_date_format" jdbcType="INTEGER" property="travelDateFormat" />
    <result column="travel_date_cross_form" jdbcType="INTEGER" property="travelDateCrossForm" />
    <result column="address_type" jdbcType="INTEGER" property="addressType" />
    <result column="apply_scene_default_value" jdbcType="INTEGER" property="applySceneDefaultValue" />
    <result column="travel_form_estimated_all_order_switch" jdbcType="INTEGER" property="travelFormEstimatedAllOrderSwitch" />
    <result column="travel_form_estimated_single_up_switch" jdbcType="INTEGER" property="travelFormEstimatedSingleUpSwitch" />
    <result column="control_upgrade_flag" jdbcType="INTEGER" property="controlUpgradeFlag" />
    <result column="use_count_limit_type" jdbcType="INTEGER" property="useCountLimitType" />
    <result column="city_limit_type" jdbcType="INTEGER" property="cityLimitType" />
    <result column="trip_apply_reason_bring_in_order" jdbcType="INTEGER" property="tripApplyReasonBringInOrder" />
    <result column="add_reason_bring_in_order" jdbcType="INTEGER" property="addReasonBringInOrder" />
    <result column="trip_add_reason_bring_in_order" jdbcType="INTEGER" property="tripAddReasonBringInOrder" />
    <result column="travel_date_item_limit" jdbcType="INTEGER" property="travelDateItemLimit" />
    <result column="delivery_period_limit" jdbcType="INTEGER" property="deliveryPeriodLimit" />
    <result column="start_city_type" jdbcType="INTEGER" property="startCityType" />
    <result column="arrival_city_type" jdbcType="INTEGER" property="arrivalCityType" />
    <result column="estimated_amount_control_required" jdbcType="INTEGER" property="estimatedAmountControlRequired" />
    <result column="traveler_range_type" jdbcType="INTEGER" property="travelerRangeType" />
    <result column="traveler_employee_range" jdbcType="INTEGER" property="travelerEmployeeRange" />
    <result column="traveler_other_employee_range" jdbcType="INTEGER" property="travelerOtherEmployeeRange" />
    <result column="traveler_allow_group_all" jdbcType="INTEGER" property="travelerAllowGroupAll" />
    <result column="traveler_relation_list" jdbcType="VARCHAR" property="travelerRelationList" />
    <result column="apply_reason_is_edit" jdbcType="INTEGER" property="applyReasonIsEdit" />
    <result column="trip_add_reason_is_edit" jdbcType="INTEGER" property="tripAddReasonIsEdit" />
    <result column="float_val" jdbcType="INTEGER" property="floatVal" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.fenbeitong.saas.core.model.saasplus.CustomFormApplyConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="trip_rule_config" jdbcType="LONGVARCHAR" property="tripRuleConfig" />
    <result column="apply_change_config" jdbcType="LONGVARCHAR" property="applyChangeConfig" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, form_id, company_id, city_type, start_date_type, return_trip_date_type, food_delivery_date_type, 
    estimated_cost_check_switch, all_order_estimated_cost_check, travel_form_estimated_cost_check_type, 
    multi_trip_apply_estimated_cost_check, air_change_estimated_cost_check_switch, train_change_estimated_cost_check_switch, 
    personal_pay_switch, traveler_control_flag, traveler_fill_type, order_check_traveler_flag, 
    traveler_limit_flag, total_estimated_control_flag, total_estimated_fill_type, check_total_estimated_flag, 
    total_estimated_limit_type, total_estimated_limit_amount, total_estimated_check_scene, 
    max_average_daily_travel_cost_amount, only_check_estimated_amount_switch, total_estimated_personal_pay_switch, 
    total_estimated_personal_pay_scene_list, apply_reason_bring_in_order, cost_attribution_control_flag, 
    cost_attribution_fill_type, cost_attribution_bring_in_order_flag, takeaway_use_count_limit_type, 
    meishi_use_count_limit_type, create_time, update_time, travel_date_control_flag, 
    travel_date_fill_type, travel_date_duplicate_verification, travel_date_auto_calculation, 
    travel_date_format, travel_date_cross_form, address_type, apply_scene_default_value, 
    travel_form_estimated_all_order_switch, travel_form_estimated_single_up_switch, control_upgrade_flag, 
    use_count_limit_type, city_limit_type, trip_apply_reason_bring_in_order, add_reason_bring_in_order, 
    trip_add_reason_bring_in_order, travel_date_item_limit, delivery_period_limit, start_city_type, 
    arrival_city_type, estimated_amount_control_required, traveler_range_type, traveler_employee_range, 
    traveler_other_employee_range, traveler_allow_group_all, traveler_relation_list, 
    apply_reason_is_edit, trip_add_reason_is_edit, float_val
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    trip_rule_config, apply_change_config
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.fenbeitong.saas.core.model.saasplus.CustomFormApplyConfigExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from custom_form_apply_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.CustomFormApplyConfigExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from custom_form_apply_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from custom_form_apply_config
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from custom_form_apply_config
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.CustomFormApplyConfigExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from custom_form_apply_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.saasplus.CustomFormApplyConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into custom_form_apply_config (id, form_id, company_id, 
      city_type, start_date_type, return_trip_date_type, 
      food_delivery_date_type, estimated_cost_check_switch, 
      all_order_estimated_cost_check, travel_form_estimated_cost_check_type, 
      multi_trip_apply_estimated_cost_check, air_change_estimated_cost_check_switch, 
      train_change_estimated_cost_check_switch, personal_pay_switch, 
      traveler_control_flag, traveler_fill_type, 
      order_check_traveler_flag, traveler_limit_flag, 
      total_estimated_control_flag, total_estimated_fill_type, 
      check_total_estimated_flag, total_estimated_limit_type, 
      total_estimated_limit_amount, total_estimated_check_scene, 
      max_average_daily_travel_cost_amount, only_check_estimated_amount_switch, 
      total_estimated_personal_pay_switch, total_estimated_personal_pay_scene_list, 
      apply_reason_bring_in_order, cost_attribution_control_flag, 
      cost_attribution_fill_type, cost_attribution_bring_in_order_flag, 
      takeaway_use_count_limit_type, meishi_use_count_limit_type, 
      create_time, update_time, travel_date_control_flag, 
      travel_date_fill_type, travel_date_duplicate_verification, 
      travel_date_auto_calculation, travel_date_format, 
      travel_date_cross_form, address_type, apply_scene_default_value, 
      travel_form_estimated_all_order_switch, travel_form_estimated_single_up_switch, 
      control_upgrade_flag, use_count_limit_type, 
      city_limit_type, trip_apply_reason_bring_in_order, 
      add_reason_bring_in_order, trip_add_reason_bring_in_order, 
      travel_date_item_limit, delivery_period_limit, 
      start_city_type, arrival_city_type, estimated_amount_control_required, 
      traveler_range_type, traveler_employee_range, 
      traveler_other_employee_range, traveler_allow_group_all, 
      traveler_relation_list, apply_reason_is_edit, 
      trip_add_reason_is_edit, float_val, trip_rule_config, 
      apply_change_config)
    values (#{id,jdbcType=CHAR}, #{formId,jdbcType=VARCHAR}, #{companyId,jdbcType=VARCHAR}, 
      #{cityType,jdbcType=INTEGER}, #{startDateType,jdbcType=INTEGER}, #{returnTripDateType,jdbcType=INTEGER}, 
      #{foodDeliveryDateType,jdbcType=INTEGER}, #{estimatedCostCheckSwitch,jdbcType=INTEGER}, 
      #{allOrderEstimatedCostCheck,jdbcType=INTEGER}, #{travelFormEstimatedCostCheckType,jdbcType=INTEGER}, 
      #{multiTripApplyEstimatedCostCheck,jdbcType=INTEGER}, #{airChangeEstimatedCostCheckSwitch,jdbcType=INTEGER}, 
      #{trainChangeEstimatedCostCheckSwitch,jdbcType=INTEGER}, #{personalPaySwitch,jdbcType=INTEGER}, 
      #{travelerControlFlag,jdbcType=INTEGER}, #{travelerFillType,jdbcType=INTEGER}, 
      #{orderCheckTravelerFlag,jdbcType=INTEGER}, #{travelerLimitFlag,jdbcType=INTEGER}, 
      #{totalEstimatedControlFlag,jdbcType=INTEGER}, #{totalEstimatedFillType,jdbcType=INTEGER}, 
      #{checkTotalEstimatedFlag,jdbcType=INTEGER}, #{totalEstimatedLimitType,jdbcType=INTEGER}, 
      #{totalEstimatedLimitAmount,jdbcType=DECIMAL}, #{totalEstimatedCheckScene,jdbcType=VARCHAR}, 
      #{maxAverageDailyTravelCostAmount,jdbcType=BIGINT}, #{onlyCheckEstimatedAmountSwitch,jdbcType=INTEGER}, 
      #{totalEstimatedPersonalPaySwitch,jdbcType=INTEGER}, #{totalEstimatedPersonalPaySceneList,jdbcType=VARCHAR}, 
      #{applyReasonBringInOrder,jdbcType=INTEGER}, #{costAttributionControlFlag,jdbcType=INTEGER}, 
      #{costAttributionFillType,jdbcType=INTEGER}, #{costAttributionBringInOrderFlag,jdbcType=INTEGER}, 
      #{takeawayUseCountLimitType,jdbcType=INTEGER}, #{meishiUseCountLimitType,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{travelDateControlFlag,jdbcType=INTEGER}, 
      #{travelDateFillType,jdbcType=INTEGER}, #{travelDateDuplicateVerification,jdbcType=INTEGER}, 
      #{travelDateAutoCalculation,jdbcType=INTEGER}, #{travelDateFormat,jdbcType=INTEGER}, 
      #{travelDateCrossForm,jdbcType=INTEGER}, #{addressType,jdbcType=INTEGER}, #{applySceneDefaultValue,jdbcType=INTEGER}, 
      #{travelFormEstimatedAllOrderSwitch,jdbcType=INTEGER}, #{travelFormEstimatedSingleUpSwitch,jdbcType=INTEGER}, 
      #{controlUpgradeFlag,jdbcType=INTEGER}, #{useCountLimitType,jdbcType=INTEGER}, 
      #{cityLimitType,jdbcType=INTEGER}, #{tripApplyReasonBringInOrder,jdbcType=INTEGER}, 
      #{addReasonBringInOrder,jdbcType=INTEGER}, #{tripAddReasonBringInOrder,jdbcType=INTEGER}, 
      #{travelDateItemLimit,jdbcType=INTEGER}, #{deliveryPeriodLimit,jdbcType=INTEGER}, 
      #{startCityType,jdbcType=INTEGER}, #{arrivalCityType,jdbcType=INTEGER}, #{estimatedAmountControlRequired,jdbcType=INTEGER}, 
      #{travelerRangeType,jdbcType=INTEGER}, #{travelerEmployeeRange,jdbcType=INTEGER}, 
      #{travelerOtherEmployeeRange,jdbcType=INTEGER}, #{travelerAllowGroupAll,jdbcType=INTEGER}, 
      #{travelerRelationList,jdbcType=VARCHAR}, #{applyReasonIsEdit,jdbcType=INTEGER}, 
      #{tripAddReasonIsEdit,jdbcType=INTEGER}, #{floatVal,jdbcType=INTEGER}, #{tripRuleConfig,jdbcType=LONGVARCHAR}, 
      #{applyChangeConfig,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.saasplus.CustomFormApplyConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into custom_form_apply_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="formId != null">
        form_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="cityType != null">
        city_type,
      </if>
      <if test="startDateType != null">
        start_date_type,
      </if>
      <if test="returnTripDateType != null">
        return_trip_date_type,
      </if>
      <if test="foodDeliveryDateType != null">
        food_delivery_date_type,
      </if>
      <if test="estimatedCostCheckSwitch != null">
        estimated_cost_check_switch,
      </if>
      <if test="allOrderEstimatedCostCheck != null">
        all_order_estimated_cost_check,
      </if>
      <if test="travelFormEstimatedCostCheckType != null">
        travel_form_estimated_cost_check_type,
      </if>
      <if test="multiTripApplyEstimatedCostCheck != null">
        multi_trip_apply_estimated_cost_check,
      </if>
      <if test="airChangeEstimatedCostCheckSwitch != null">
        air_change_estimated_cost_check_switch,
      </if>
      <if test="trainChangeEstimatedCostCheckSwitch != null">
        train_change_estimated_cost_check_switch,
      </if>
      <if test="personalPaySwitch != null">
        personal_pay_switch,
      </if>
      <if test="travelerControlFlag != null">
        traveler_control_flag,
      </if>
      <if test="travelerFillType != null">
        traveler_fill_type,
      </if>
      <if test="orderCheckTravelerFlag != null">
        order_check_traveler_flag,
      </if>
      <if test="travelerLimitFlag != null">
        traveler_limit_flag,
      </if>
      <if test="totalEstimatedControlFlag != null">
        total_estimated_control_flag,
      </if>
      <if test="totalEstimatedFillType != null">
        total_estimated_fill_type,
      </if>
      <if test="checkTotalEstimatedFlag != null">
        check_total_estimated_flag,
      </if>
      <if test="totalEstimatedLimitType != null">
        total_estimated_limit_type,
      </if>
      <if test="totalEstimatedLimitAmount != null">
        total_estimated_limit_amount,
      </if>
      <if test="totalEstimatedCheckScene != null">
        total_estimated_check_scene,
      </if>
      <if test="maxAverageDailyTravelCostAmount != null">
        max_average_daily_travel_cost_amount,
      </if>
      <if test="onlyCheckEstimatedAmountSwitch != null">
        only_check_estimated_amount_switch,
      </if>
      <if test="totalEstimatedPersonalPaySwitch != null">
        total_estimated_personal_pay_switch,
      </if>
      <if test="totalEstimatedPersonalPaySceneList != null">
        total_estimated_personal_pay_scene_list,
      </if>
      <if test="applyReasonBringInOrder != null">
        apply_reason_bring_in_order,
      </if>
      <if test="costAttributionControlFlag != null">
        cost_attribution_control_flag,
      </if>
      <if test="costAttributionFillType != null">
        cost_attribution_fill_type,
      </if>
      <if test="costAttributionBringInOrderFlag != null">
        cost_attribution_bring_in_order_flag,
      </if>
      <if test="takeawayUseCountLimitType != null">
        takeaway_use_count_limit_type,
      </if>
      <if test="meishiUseCountLimitType != null">
        meishi_use_count_limit_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="travelDateControlFlag != null">
        travel_date_control_flag,
      </if>
      <if test="travelDateFillType != null">
        travel_date_fill_type,
      </if>
      <if test="travelDateDuplicateVerification != null">
        travel_date_duplicate_verification,
      </if>
      <if test="travelDateAutoCalculation != null">
        travel_date_auto_calculation,
      </if>
      <if test="travelDateFormat != null">
        travel_date_format,
      </if>
      <if test="travelDateCrossForm != null">
        travel_date_cross_form,
      </if>
      <if test="addressType != null">
        address_type,
      </if>
      <if test="applySceneDefaultValue != null">
        apply_scene_default_value,
      </if>
      <if test="travelFormEstimatedAllOrderSwitch != null">
        travel_form_estimated_all_order_switch,
      </if>
      <if test="travelFormEstimatedSingleUpSwitch != null">
        travel_form_estimated_single_up_switch,
      </if>
      <if test="controlUpgradeFlag != null">
        control_upgrade_flag,
      </if>
      <if test="useCountLimitType != null">
        use_count_limit_type,
      </if>
      <if test="cityLimitType != null">
        city_limit_type,
      </if>
      <if test="tripApplyReasonBringInOrder != null">
        trip_apply_reason_bring_in_order,
      </if>
      <if test="addReasonBringInOrder != null">
        add_reason_bring_in_order,
      </if>
      <if test="tripAddReasonBringInOrder != null">
        trip_add_reason_bring_in_order,
      </if>
      <if test="travelDateItemLimit != null">
        travel_date_item_limit,
      </if>
      <if test="deliveryPeriodLimit != null">
        delivery_period_limit,
      </if>
      <if test="startCityType != null">
        start_city_type,
      </if>
      <if test="arrivalCityType != null">
        arrival_city_type,
      </if>
      <if test="estimatedAmountControlRequired != null">
        estimated_amount_control_required,
      </if>
      <if test="travelerRangeType != null">
        traveler_range_type,
      </if>
      <if test="travelerEmployeeRange != null">
        traveler_employee_range,
      </if>
      <if test="travelerOtherEmployeeRange != null">
        traveler_other_employee_range,
      </if>
      <if test="travelerAllowGroupAll != null">
        traveler_allow_group_all,
      </if>
      <if test="travelerRelationList != null">
        traveler_relation_list,
      </if>
      <if test="applyReasonIsEdit != null">
        apply_reason_is_edit,
      </if>
      <if test="tripAddReasonIsEdit != null">
        trip_add_reason_is_edit,
      </if>
      <if test="floatVal != null">
        float_val,
      </if>
      <if test="tripRuleConfig != null">
        trip_rule_config,
      </if>
      <if test="applyChangeConfig != null">
        apply_change_config,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="formId != null">
        #{formId,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="cityType != null">
        #{cityType,jdbcType=INTEGER},
      </if>
      <if test="startDateType != null">
        #{startDateType,jdbcType=INTEGER},
      </if>
      <if test="returnTripDateType != null">
        #{returnTripDateType,jdbcType=INTEGER},
      </if>
      <if test="foodDeliveryDateType != null">
        #{foodDeliveryDateType,jdbcType=INTEGER},
      </if>
      <if test="estimatedCostCheckSwitch != null">
        #{estimatedCostCheckSwitch,jdbcType=INTEGER},
      </if>
      <if test="allOrderEstimatedCostCheck != null">
        #{allOrderEstimatedCostCheck,jdbcType=INTEGER},
      </if>
      <if test="travelFormEstimatedCostCheckType != null">
        #{travelFormEstimatedCostCheckType,jdbcType=INTEGER},
      </if>
      <if test="multiTripApplyEstimatedCostCheck != null">
        #{multiTripApplyEstimatedCostCheck,jdbcType=INTEGER},
      </if>
      <if test="airChangeEstimatedCostCheckSwitch != null">
        #{airChangeEstimatedCostCheckSwitch,jdbcType=INTEGER},
      </if>
      <if test="trainChangeEstimatedCostCheckSwitch != null">
        #{trainChangeEstimatedCostCheckSwitch,jdbcType=INTEGER},
      </if>
      <if test="personalPaySwitch != null">
        #{personalPaySwitch,jdbcType=INTEGER},
      </if>
      <if test="travelerControlFlag != null">
        #{travelerControlFlag,jdbcType=INTEGER},
      </if>
      <if test="travelerFillType != null">
        #{travelerFillType,jdbcType=INTEGER},
      </if>
      <if test="orderCheckTravelerFlag != null">
        #{orderCheckTravelerFlag,jdbcType=INTEGER},
      </if>
      <if test="travelerLimitFlag != null">
        #{travelerLimitFlag,jdbcType=INTEGER},
      </if>
      <if test="totalEstimatedControlFlag != null">
        #{totalEstimatedControlFlag,jdbcType=INTEGER},
      </if>
      <if test="totalEstimatedFillType != null">
        #{totalEstimatedFillType,jdbcType=INTEGER},
      </if>
      <if test="checkTotalEstimatedFlag != null">
        #{checkTotalEstimatedFlag,jdbcType=INTEGER},
      </if>
      <if test="totalEstimatedLimitType != null">
        #{totalEstimatedLimitType,jdbcType=INTEGER},
      </if>
      <if test="totalEstimatedLimitAmount != null">
        #{totalEstimatedLimitAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalEstimatedCheckScene != null">
        #{totalEstimatedCheckScene,jdbcType=VARCHAR},
      </if>
      <if test="maxAverageDailyTravelCostAmount != null">
        #{maxAverageDailyTravelCostAmount,jdbcType=BIGINT},
      </if>
      <if test="onlyCheckEstimatedAmountSwitch != null">
        #{onlyCheckEstimatedAmountSwitch,jdbcType=INTEGER},
      </if>
      <if test="totalEstimatedPersonalPaySwitch != null">
        #{totalEstimatedPersonalPaySwitch,jdbcType=INTEGER},
      </if>
      <if test="totalEstimatedPersonalPaySceneList != null">
        #{totalEstimatedPersonalPaySceneList,jdbcType=VARCHAR},
      </if>
      <if test="applyReasonBringInOrder != null">
        #{applyReasonBringInOrder,jdbcType=INTEGER},
      </if>
      <if test="costAttributionControlFlag != null">
        #{costAttributionControlFlag,jdbcType=INTEGER},
      </if>
      <if test="costAttributionFillType != null">
        #{costAttributionFillType,jdbcType=INTEGER},
      </if>
      <if test="costAttributionBringInOrderFlag != null">
        #{costAttributionBringInOrderFlag,jdbcType=INTEGER},
      </if>
      <if test="takeawayUseCountLimitType != null">
        #{takeawayUseCountLimitType,jdbcType=INTEGER},
      </if>
      <if test="meishiUseCountLimitType != null">
        #{meishiUseCountLimitType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="travelDateControlFlag != null">
        #{travelDateControlFlag,jdbcType=INTEGER},
      </if>
      <if test="travelDateFillType != null">
        #{travelDateFillType,jdbcType=INTEGER},
      </if>
      <if test="travelDateDuplicateVerification != null">
        #{travelDateDuplicateVerification,jdbcType=INTEGER},
      </if>
      <if test="travelDateAutoCalculation != null">
        #{travelDateAutoCalculation,jdbcType=INTEGER},
      </if>
      <if test="travelDateFormat != null">
        #{travelDateFormat,jdbcType=INTEGER},
      </if>
      <if test="travelDateCrossForm != null">
        #{travelDateCrossForm,jdbcType=INTEGER},
      </if>
      <if test="addressType != null">
        #{addressType,jdbcType=INTEGER},
      </if>
      <if test="applySceneDefaultValue != null">
        #{applySceneDefaultValue,jdbcType=INTEGER},
      </if>
      <if test="travelFormEstimatedAllOrderSwitch != null">
        #{travelFormEstimatedAllOrderSwitch,jdbcType=INTEGER},
      </if>
      <if test="travelFormEstimatedSingleUpSwitch != null">
        #{travelFormEstimatedSingleUpSwitch,jdbcType=INTEGER},
      </if>
      <if test="controlUpgradeFlag != null">
        #{controlUpgradeFlag,jdbcType=INTEGER},
      </if>
      <if test="useCountLimitType != null">
        #{useCountLimitType,jdbcType=INTEGER},
      </if>
      <if test="cityLimitType != null">
        #{cityLimitType,jdbcType=INTEGER},
      </if>
      <if test="tripApplyReasonBringInOrder != null">
        #{tripApplyReasonBringInOrder,jdbcType=INTEGER},
      </if>
      <if test="addReasonBringInOrder != null">
        #{addReasonBringInOrder,jdbcType=INTEGER},
      </if>
      <if test="tripAddReasonBringInOrder != null">
        #{tripAddReasonBringInOrder,jdbcType=INTEGER},
      </if>
      <if test="travelDateItemLimit != null">
        #{travelDateItemLimit,jdbcType=INTEGER},
      </if>
      <if test="deliveryPeriodLimit != null">
        #{deliveryPeriodLimit,jdbcType=INTEGER},
      </if>
      <if test="startCityType != null">
        #{startCityType,jdbcType=INTEGER},
      </if>
      <if test="arrivalCityType != null">
        #{arrivalCityType,jdbcType=INTEGER},
      </if>
      <if test="estimatedAmountControlRequired != null">
        #{estimatedAmountControlRequired,jdbcType=INTEGER},
      </if>
      <if test="travelerRangeType != null">
        #{travelerRangeType,jdbcType=INTEGER},
      </if>
      <if test="travelerEmployeeRange != null">
        #{travelerEmployeeRange,jdbcType=INTEGER},
      </if>
      <if test="travelerOtherEmployeeRange != null">
        #{travelerOtherEmployeeRange,jdbcType=INTEGER},
      </if>
      <if test="travelerAllowGroupAll != null">
        #{travelerAllowGroupAll,jdbcType=INTEGER},
      </if>
      <if test="travelerRelationList != null">
        #{travelerRelationList,jdbcType=VARCHAR},
      </if>
      <if test="applyReasonIsEdit != null">
        #{applyReasonIsEdit,jdbcType=INTEGER},
      </if>
      <if test="tripAddReasonIsEdit != null">
        #{tripAddReasonIsEdit,jdbcType=INTEGER},
      </if>
      <if test="floatVal != null">
        #{floatVal,jdbcType=INTEGER},
      </if>
      <if test="tripRuleConfig != null">
        #{tripRuleConfig,jdbcType=LONGVARCHAR},
      </if>
      <if test="applyChangeConfig != null">
        #{applyChangeConfig,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.CustomFormApplyConfigExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from custom_form_apply_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update custom_form_apply_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.formId != null">
        form_id = #{record.formId,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.cityType != null">
        city_type = #{record.cityType,jdbcType=INTEGER},
      </if>
      <if test="record.startDateType != null">
        start_date_type = #{record.startDateType,jdbcType=INTEGER},
      </if>
      <if test="record.returnTripDateType != null">
        return_trip_date_type = #{record.returnTripDateType,jdbcType=INTEGER},
      </if>
      <if test="record.foodDeliveryDateType != null">
        food_delivery_date_type = #{record.foodDeliveryDateType,jdbcType=INTEGER},
      </if>
      <if test="record.estimatedCostCheckSwitch != null">
        estimated_cost_check_switch = #{record.estimatedCostCheckSwitch,jdbcType=INTEGER},
      </if>
      <if test="record.allOrderEstimatedCostCheck != null">
        all_order_estimated_cost_check = #{record.allOrderEstimatedCostCheck,jdbcType=INTEGER},
      </if>
      <if test="record.travelFormEstimatedCostCheckType != null">
        travel_form_estimated_cost_check_type = #{record.travelFormEstimatedCostCheckType,jdbcType=INTEGER},
      </if>
      <if test="record.multiTripApplyEstimatedCostCheck != null">
        multi_trip_apply_estimated_cost_check = #{record.multiTripApplyEstimatedCostCheck,jdbcType=INTEGER},
      </if>
      <if test="record.airChangeEstimatedCostCheckSwitch != null">
        air_change_estimated_cost_check_switch = #{record.airChangeEstimatedCostCheckSwitch,jdbcType=INTEGER},
      </if>
      <if test="record.trainChangeEstimatedCostCheckSwitch != null">
        train_change_estimated_cost_check_switch = #{record.trainChangeEstimatedCostCheckSwitch,jdbcType=INTEGER},
      </if>
      <if test="record.personalPaySwitch != null">
        personal_pay_switch = #{record.personalPaySwitch,jdbcType=INTEGER},
      </if>
      <if test="record.travelerControlFlag != null">
        traveler_control_flag = #{record.travelerControlFlag,jdbcType=INTEGER},
      </if>
      <if test="record.travelerFillType != null">
        traveler_fill_type = #{record.travelerFillType,jdbcType=INTEGER},
      </if>
      <if test="record.orderCheckTravelerFlag != null">
        order_check_traveler_flag = #{record.orderCheckTravelerFlag,jdbcType=INTEGER},
      </if>
      <if test="record.travelerLimitFlag != null">
        traveler_limit_flag = #{record.travelerLimitFlag,jdbcType=INTEGER},
      </if>
      <if test="record.totalEstimatedControlFlag != null">
        total_estimated_control_flag = #{record.totalEstimatedControlFlag,jdbcType=INTEGER},
      </if>
      <if test="record.totalEstimatedFillType != null">
        total_estimated_fill_type = #{record.totalEstimatedFillType,jdbcType=INTEGER},
      </if>
      <if test="record.checkTotalEstimatedFlag != null">
        check_total_estimated_flag = #{record.checkTotalEstimatedFlag,jdbcType=INTEGER},
      </if>
      <if test="record.totalEstimatedLimitType != null">
        total_estimated_limit_type = #{record.totalEstimatedLimitType,jdbcType=INTEGER},
      </if>
      <if test="record.totalEstimatedLimitAmount != null">
        total_estimated_limit_amount = #{record.totalEstimatedLimitAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.totalEstimatedCheckScene != null">
        total_estimated_check_scene = #{record.totalEstimatedCheckScene,jdbcType=VARCHAR},
      </if>
      <if test="record.maxAverageDailyTravelCostAmount != null">
        max_average_daily_travel_cost_amount = #{record.maxAverageDailyTravelCostAmount,jdbcType=BIGINT},
      </if>
      <if test="record.onlyCheckEstimatedAmountSwitch != null">
        only_check_estimated_amount_switch = #{record.onlyCheckEstimatedAmountSwitch,jdbcType=INTEGER},
      </if>
      <if test="record.totalEstimatedPersonalPaySwitch != null">
        total_estimated_personal_pay_switch = #{record.totalEstimatedPersonalPaySwitch,jdbcType=INTEGER},
      </if>
      <if test="record.totalEstimatedPersonalPaySceneList != null">
        total_estimated_personal_pay_scene_list = #{record.totalEstimatedPersonalPaySceneList,jdbcType=VARCHAR},
      </if>
      <if test="record.applyReasonBringInOrder != null">
        apply_reason_bring_in_order = #{record.applyReasonBringInOrder,jdbcType=INTEGER},
      </if>
      <if test="record.costAttributionControlFlag != null">
        cost_attribution_control_flag = #{record.costAttributionControlFlag,jdbcType=INTEGER},
      </if>
      <if test="record.costAttributionFillType != null">
        cost_attribution_fill_type = #{record.costAttributionFillType,jdbcType=INTEGER},
      </if>
      <if test="record.costAttributionBringInOrderFlag != null">
        cost_attribution_bring_in_order_flag = #{record.costAttributionBringInOrderFlag,jdbcType=INTEGER},
      </if>
      <if test="record.takeawayUseCountLimitType != null">
        takeaway_use_count_limit_type = #{record.takeawayUseCountLimitType,jdbcType=INTEGER},
      </if>
      <if test="record.meishiUseCountLimitType != null">
        meishi_use_count_limit_type = #{record.meishiUseCountLimitType,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.travelDateControlFlag != null">
        travel_date_control_flag = #{record.travelDateControlFlag,jdbcType=INTEGER},
      </if>
      <if test="record.travelDateFillType != null">
        travel_date_fill_type = #{record.travelDateFillType,jdbcType=INTEGER},
      </if>
      <if test="record.travelDateDuplicateVerification != null">
        travel_date_duplicate_verification = #{record.travelDateDuplicateVerification,jdbcType=INTEGER},
      </if>
      <if test="record.travelDateAutoCalculation != null">
        travel_date_auto_calculation = #{record.travelDateAutoCalculation,jdbcType=INTEGER},
      </if>
      <if test="record.travelDateFormat != null">
        travel_date_format = #{record.travelDateFormat,jdbcType=INTEGER},
      </if>
      <if test="record.travelDateCrossForm != null">
        travel_date_cross_form = #{record.travelDateCrossForm,jdbcType=INTEGER},
      </if>
      <if test="record.addressType != null">
        address_type = #{record.addressType,jdbcType=INTEGER},
      </if>
      <if test="record.applySceneDefaultValue != null">
        apply_scene_default_value = #{record.applySceneDefaultValue,jdbcType=INTEGER},
      </if>
      <if test="record.travelFormEstimatedAllOrderSwitch != null">
        travel_form_estimated_all_order_switch = #{record.travelFormEstimatedAllOrderSwitch,jdbcType=INTEGER},
      </if>
      <if test="record.travelFormEstimatedSingleUpSwitch != null">
        travel_form_estimated_single_up_switch = #{record.travelFormEstimatedSingleUpSwitch,jdbcType=INTEGER},
      </if>
      <if test="record.controlUpgradeFlag != null">
        control_upgrade_flag = #{record.controlUpgradeFlag,jdbcType=INTEGER},
      </if>
      <if test="record.useCountLimitType != null">
        use_count_limit_type = #{record.useCountLimitType,jdbcType=INTEGER},
      </if>
      <if test="record.cityLimitType != null">
        city_limit_type = #{record.cityLimitType,jdbcType=INTEGER},
      </if>
      <if test="record.tripApplyReasonBringInOrder != null">
        trip_apply_reason_bring_in_order = #{record.tripApplyReasonBringInOrder,jdbcType=INTEGER},
      </if>
      <if test="record.addReasonBringInOrder != null">
        add_reason_bring_in_order = #{record.addReasonBringInOrder,jdbcType=INTEGER},
      </if>
      <if test="record.tripAddReasonBringInOrder != null">
        trip_add_reason_bring_in_order = #{record.tripAddReasonBringInOrder,jdbcType=INTEGER},
      </if>
      <if test="record.travelDateItemLimit != null">
        travel_date_item_limit = #{record.travelDateItemLimit,jdbcType=INTEGER},
      </if>
      <if test="record.deliveryPeriodLimit != null">
        delivery_period_limit = #{record.deliveryPeriodLimit,jdbcType=INTEGER},
      </if>
      <if test="record.startCityType != null">
        start_city_type = #{record.startCityType,jdbcType=INTEGER},
      </if>
      <if test="record.arrivalCityType != null">
        arrival_city_type = #{record.arrivalCityType,jdbcType=INTEGER},
      </if>
      <if test="record.estimatedAmountControlRequired != null">
        estimated_amount_control_required = #{record.estimatedAmountControlRequired,jdbcType=INTEGER},
      </if>
      <if test="record.travelerRangeType != null">
        traveler_range_type = #{record.travelerRangeType,jdbcType=INTEGER},
      </if>
      <if test="record.travelerEmployeeRange != null">
        traveler_employee_range = #{record.travelerEmployeeRange,jdbcType=INTEGER},
      </if>
      <if test="record.travelerOtherEmployeeRange != null">
        traveler_other_employee_range = #{record.travelerOtherEmployeeRange,jdbcType=INTEGER},
      </if>
      <if test="record.travelerAllowGroupAll != null">
        traveler_allow_group_all = #{record.travelerAllowGroupAll,jdbcType=INTEGER},
      </if>
      <if test="record.travelerRelationList != null">
        traveler_relation_list = #{record.travelerRelationList,jdbcType=VARCHAR},
      </if>
      <if test="record.applyReasonIsEdit != null">
        apply_reason_is_edit = #{record.applyReasonIsEdit,jdbcType=INTEGER},
      </if>
      <if test="record.tripAddReasonIsEdit != null">
        trip_add_reason_is_edit = #{record.tripAddReasonIsEdit,jdbcType=INTEGER},
      </if>
      <if test="record.floatVal != null">
        float_val = #{record.floatVal,jdbcType=INTEGER},
      </if>
      <if test="record.tripRuleConfig != null">
        trip_rule_config = #{record.tripRuleConfig,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.applyChangeConfig != null">
        apply_change_config = #{record.applyChangeConfig,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update custom_form_apply_config
    set id = #{record.id,jdbcType=CHAR},
      form_id = #{record.formId,jdbcType=VARCHAR},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      city_type = #{record.cityType,jdbcType=INTEGER},
      start_date_type = #{record.startDateType,jdbcType=INTEGER},
      return_trip_date_type = #{record.returnTripDateType,jdbcType=INTEGER},
      food_delivery_date_type = #{record.foodDeliveryDateType,jdbcType=INTEGER},
      estimated_cost_check_switch = #{record.estimatedCostCheckSwitch,jdbcType=INTEGER},
      all_order_estimated_cost_check = #{record.allOrderEstimatedCostCheck,jdbcType=INTEGER},
      travel_form_estimated_cost_check_type = #{record.travelFormEstimatedCostCheckType,jdbcType=INTEGER},
      multi_trip_apply_estimated_cost_check = #{record.multiTripApplyEstimatedCostCheck,jdbcType=INTEGER},
      air_change_estimated_cost_check_switch = #{record.airChangeEstimatedCostCheckSwitch,jdbcType=INTEGER},
      train_change_estimated_cost_check_switch = #{record.trainChangeEstimatedCostCheckSwitch,jdbcType=INTEGER},
      personal_pay_switch = #{record.personalPaySwitch,jdbcType=INTEGER},
      traveler_control_flag = #{record.travelerControlFlag,jdbcType=INTEGER},
      traveler_fill_type = #{record.travelerFillType,jdbcType=INTEGER},
      order_check_traveler_flag = #{record.orderCheckTravelerFlag,jdbcType=INTEGER},
      traveler_limit_flag = #{record.travelerLimitFlag,jdbcType=INTEGER},
      total_estimated_control_flag = #{record.totalEstimatedControlFlag,jdbcType=INTEGER},
      total_estimated_fill_type = #{record.totalEstimatedFillType,jdbcType=INTEGER},
      check_total_estimated_flag = #{record.checkTotalEstimatedFlag,jdbcType=INTEGER},
      total_estimated_limit_type = #{record.totalEstimatedLimitType,jdbcType=INTEGER},
      total_estimated_limit_amount = #{record.totalEstimatedLimitAmount,jdbcType=DECIMAL},
      total_estimated_check_scene = #{record.totalEstimatedCheckScene,jdbcType=VARCHAR},
      max_average_daily_travel_cost_amount = #{record.maxAverageDailyTravelCostAmount,jdbcType=BIGINT},
      only_check_estimated_amount_switch = #{record.onlyCheckEstimatedAmountSwitch,jdbcType=INTEGER},
      total_estimated_personal_pay_switch = #{record.totalEstimatedPersonalPaySwitch,jdbcType=INTEGER},
      total_estimated_personal_pay_scene_list = #{record.totalEstimatedPersonalPaySceneList,jdbcType=VARCHAR},
      apply_reason_bring_in_order = #{record.applyReasonBringInOrder,jdbcType=INTEGER},
      cost_attribution_control_flag = #{record.costAttributionControlFlag,jdbcType=INTEGER},
      cost_attribution_fill_type = #{record.costAttributionFillType,jdbcType=INTEGER},
      cost_attribution_bring_in_order_flag = #{record.costAttributionBringInOrderFlag,jdbcType=INTEGER},
      takeaway_use_count_limit_type = #{record.takeawayUseCountLimitType,jdbcType=INTEGER},
      meishi_use_count_limit_type = #{record.meishiUseCountLimitType,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      travel_date_control_flag = #{record.travelDateControlFlag,jdbcType=INTEGER},
      travel_date_fill_type = #{record.travelDateFillType,jdbcType=INTEGER},
      travel_date_duplicate_verification = #{record.travelDateDuplicateVerification,jdbcType=INTEGER},
      travel_date_auto_calculation = #{record.travelDateAutoCalculation,jdbcType=INTEGER},
      travel_date_format = #{record.travelDateFormat,jdbcType=INTEGER},
      travel_date_cross_form = #{record.travelDateCrossForm,jdbcType=INTEGER},
      address_type = #{record.addressType,jdbcType=INTEGER},
      apply_scene_default_value = #{record.applySceneDefaultValue,jdbcType=INTEGER},
      travel_form_estimated_all_order_switch = #{record.travelFormEstimatedAllOrderSwitch,jdbcType=INTEGER},
      travel_form_estimated_single_up_switch = #{record.travelFormEstimatedSingleUpSwitch,jdbcType=INTEGER},
      control_upgrade_flag = #{record.controlUpgradeFlag,jdbcType=INTEGER},
      use_count_limit_type = #{record.useCountLimitType,jdbcType=INTEGER},
      city_limit_type = #{record.cityLimitType,jdbcType=INTEGER},
      trip_apply_reason_bring_in_order = #{record.tripApplyReasonBringInOrder,jdbcType=INTEGER},
      add_reason_bring_in_order = #{record.addReasonBringInOrder,jdbcType=INTEGER},
      trip_add_reason_bring_in_order = #{record.tripAddReasonBringInOrder,jdbcType=INTEGER},
      travel_date_item_limit = #{record.travelDateItemLimit,jdbcType=INTEGER},
      delivery_period_limit = #{record.deliveryPeriodLimit,jdbcType=INTEGER},
      start_city_type = #{record.startCityType,jdbcType=INTEGER},
      arrival_city_type = #{record.arrivalCityType,jdbcType=INTEGER},
      estimated_amount_control_required = #{record.estimatedAmountControlRequired,jdbcType=INTEGER},
      traveler_range_type = #{record.travelerRangeType,jdbcType=INTEGER},
      traveler_employee_range = #{record.travelerEmployeeRange,jdbcType=INTEGER},
      traveler_other_employee_range = #{record.travelerOtherEmployeeRange,jdbcType=INTEGER},
      traveler_allow_group_all = #{record.travelerAllowGroupAll,jdbcType=INTEGER},
      traveler_relation_list = #{record.travelerRelationList,jdbcType=VARCHAR},
      apply_reason_is_edit = #{record.applyReasonIsEdit,jdbcType=INTEGER},
      trip_add_reason_is_edit = #{record.tripAddReasonIsEdit,jdbcType=INTEGER},
      float_val = #{record.floatVal,jdbcType=INTEGER},
      trip_rule_config = #{record.tripRuleConfig,jdbcType=LONGVARCHAR},
      apply_change_config = #{record.applyChangeConfig,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update custom_form_apply_config
    set id = #{record.id,jdbcType=CHAR},
      form_id = #{record.formId,jdbcType=VARCHAR},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      city_type = #{record.cityType,jdbcType=INTEGER},
      start_date_type = #{record.startDateType,jdbcType=INTEGER},
      return_trip_date_type = #{record.returnTripDateType,jdbcType=INTEGER},
      food_delivery_date_type = #{record.foodDeliveryDateType,jdbcType=INTEGER},
      estimated_cost_check_switch = #{record.estimatedCostCheckSwitch,jdbcType=INTEGER},
      all_order_estimated_cost_check = #{record.allOrderEstimatedCostCheck,jdbcType=INTEGER},
      travel_form_estimated_cost_check_type = #{record.travelFormEstimatedCostCheckType,jdbcType=INTEGER},
      multi_trip_apply_estimated_cost_check = #{record.multiTripApplyEstimatedCostCheck,jdbcType=INTEGER},
      air_change_estimated_cost_check_switch = #{record.airChangeEstimatedCostCheckSwitch,jdbcType=INTEGER},
      train_change_estimated_cost_check_switch = #{record.trainChangeEstimatedCostCheckSwitch,jdbcType=INTEGER},
      personal_pay_switch = #{record.personalPaySwitch,jdbcType=INTEGER},
      traveler_control_flag = #{record.travelerControlFlag,jdbcType=INTEGER},
      traveler_fill_type = #{record.travelerFillType,jdbcType=INTEGER},
      order_check_traveler_flag = #{record.orderCheckTravelerFlag,jdbcType=INTEGER},
      traveler_limit_flag = #{record.travelerLimitFlag,jdbcType=INTEGER},
      total_estimated_control_flag = #{record.totalEstimatedControlFlag,jdbcType=INTEGER},
      total_estimated_fill_type = #{record.totalEstimatedFillType,jdbcType=INTEGER},
      check_total_estimated_flag = #{record.checkTotalEstimatedFlag,jdbcType=INTEGER},
      total_estimated_limit_type = #{record.totalEstimatedLimitType,jdbcType=INTEGER},
      total_estimated_limit_amount = #{record.totalEstimatedLimitAmount,jdbcType=DECIMAL},
      total_estimated_check_scene = #{record.totalEstimatedCheckScene,jdbcType=VARCHAR},
      max_average_daily_travel_cost_amount = #{record.maxAverageDailyTravelCostAmount,jdbcType=BIGINT},
      only_check_estimated_amount_switch = #{record.onlyCheckEstimatedAmountSwitch,jdbcType=INTEGER},
      total_estimated_personal_pay_switch = #{record.totalEstimatedPersonalPaySwitch,jdbcType=INTEGER},
      total_estimated_personal_pay_scene_list = #{record.totalEstimatedPersonalPaySceneList,jdbcType=VARCHAR},
      apply_reason_bring_in_order = #{record.applyReasonBringInOrder,jdbcType=INTEGER},
      cost_attribution_control_flag = #{record.costAttributionControlFlag,jdbcType=INTEGER},
      cost_attribution_fill_type = #{record.costAttributionFillType,jdbcType=INTEGER},
      cost_attribution_bring_in_order_flag = #{record.costAttributionBringInOrderFlag,jdbcType=INTEGER},
      takeaway_use_count_limit_type = #{record.takeawayUseCountLimitType,jdbcType=INTEGER},
      meishi_use_count_limit_type = #{record.meishiUseCountLimitType,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      travel_date_control_flag = #{record.travelDateControlFlag,jdbcType=INTEGER},
      travel_date_fill_type = #{record.travelDateFillType,jdbcType=INTEGER},
      travel_date_duplicate_verification = #{record.travelDateDuplicateVerification,jdbcType=INTEGER},
      travel_date_auto_calculation = #{record.travelDateAutoCalculation,jdbcType=INTEGER},
      travel_date_format = #{record.travelDateFormat,jdbcType=INTEGER},
      travel_date_cross_form = #{record.travelDateCrossForm,jdbcType=INTEGER},
      address_type = #{record.addressType,jdbcType=INTEGER},
      apply_scene_default_value = #{record.applySceneDefaultValue,jdbcType=INTEGER},
      travel_form_estimated_all_order_switch = #{record.travelFormEstimatedAllOrderSwitch,jdbcType=INTEGER},
      travel_form_estimated_single_up_switch = #{record.travelFormEstimatedSingleUpSwitch,jdbcType=INTEGER},
      control_upgrade_flag = #{record.controlUpgradeFlag,jdbcType=INTEGER},
      use_count_limit_type = #{record.useCountLimitType,jdbcType=INTEGER},
      city_limit_type = #{record.cityLimitType,jdbcType=INTEGER},
      trip_apply_reason_bring_in_order = #{record.tripApplyReasonBringInOrder,jdbcType=INTEGER},
      add_reason_bring_in_order = #{record.addReasonBringInOrder,jdbcType=INTEGER},
      trip_add_reason_bring_in_order = #{record.tripAddReasonBringInOrder,jdbcType=INTEGER},
      travel_date_item_limit = #{record.travelDateItemLimit,jdbcType=INTEGER},
      delivery_period_limit = #{record.deliveryPeriodLimit,jdbcType=INTEGER},
      start_city_type = #{record.startCityType,jdbcType=INTEGER},
      arrival_city_type = #{record.arrivalCityType,jdbcType=INTEGER},
      estimated_amount_control_required = #{record.estimatedAmountControlRequired,jdbcType=INTEGER},
      traveler_range_type = #{record.travelerRangeType,jdbcType=INTEGER},
      traveler_employee_range = #{record.travelerEmployeeRange,jdbcType=INTEGER},
      traveler_other_employee_range = #{record.travelerOtherEmployeeRange,jdbcType=INTEGER},
      traveler_allow_group_all = #{record.travelerAllowGroupAll,jdbcType=INTEGER},
      traveler_relation_list = #{record.travelerRelationList,jdbcType=VARCHAR},
      apply_reason_is_edit = #{record.applyReasonIsEdit,jdbcType=INTEGER},
      trip_add_reason_is_edit = #{record.tripAddReasonIsEdit,jdbcType=INTEGER},
      float_val = #{record.floatVal,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saasplus.CustomFormApplyConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update custom_form_apply_config
    <set>
      <if test="formId != null">
        form_id = #{formId,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="cityType != null">
        city_type = #{cityType,jdbcType=INTEGER},
      </if>
      <if test="startDateType != null">
        start_date_type = #{startDateType,jdbcType=INTEGER},
      </if>
      <if test="returnTripDateType != null">
        return_trip_date_type = #{returnTripDateType,jdbcType=INTEGER},
      </if>
      <if test="foodDeliveryDateType != null">
        food_delivery_date_type = #{foodDeliveryDateType,jdbcType=INTEGER},
      </if>
      <if test="estimatedCostCheckSwitch != null">
        estimated_cost_check_switch = #{estimatedCostCheckSwitch,jdbcType=INTEGER},
      </if>
      <if test="allOrderEstimatedCostCheck != null">
        all_order_estimated_cost_check = #{allOrderEstimatedCostCheck,jdbcType=INTEGER},
      </if>
      <if test="travelFormEstimatedCostCheckType != null">
        travel_form_estimated_cost_check_type = #{travelFormEstimatedCostCheckType,jdbcType=INTEGER},
      </if>
      <if test="multiTripApplyEstimatedCostCheck != null">
        multi_trip_apply_estimated_cost_check = #{multiTripApplyEstimatedCostCheck,jdbcType=INTEGER},
      </if>
      <if test="airChangeEstimatedCostCheckSwitch != null">
        air_change_estimated_cost_check_switch = #{airChangeEstimatedCostCheckSwitch,jdbcType=INTEGER},
      </if>
      <if test="trainChangeEstimatedCostCheckSwitch != null">
        train_change_estimated_cost_check_switch = #{trainChangeEstimatedCostCheckSwitch,jdbcType=INTEGER},
      </if>
      <if test="personalPaySwitch != null">
        personal_pay_switch = #{personalPaySwitch,jdbcType=INTEGER},
      </if>
      <if test="travelerControlFlag != null">
        traveler_control_flag = #{travelerControlFlag,jdbcType=INTEGER},
      </if>
      <if test="travelerFillType != null">
        traveler_fill_type = #{travelerFillType,jdbcType=INTEGER},
      </if>
      <if test="orderCheckTravelerFlag != null">
        order_check_traveler_flag = #{orderCheckTravelerFlag,jdbcType=INTEGER},
      </if>
      <if test="travelerLimitFlag != null">
        traveler_limit_flag = #{travelerLimitFlag,jdbcType=INTEGER},
      </if>
      <if test="totalEstimatedControlFlag != null">
        total_estimated_control_flag = #{totalEstimatedControlFlag,jdbcType=INTEGER},
      </if>
      <if test="totalEstimatedFillType != null">
        total_estimated_fill_type = #{totalEstimatedFillType,jdbcType=INTEGER},
      </if>
      <if test="checkTotalEstimatedFlag != null">
        check_total_estimated_flag = #{checkTotalEstimatedFlag,jdbcType=INTEGER},
      </if>
      <if test="totalEstimatedLimitType != null">
        total_estimated_limit_type = #{totalEstimatedLimitType,jdbcType=INTEGER},
      </if>
      <if test="totalEstimatedLimitAmount != null">
        total_estimated_limit_amount = #{totalEstimatedLimitAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalEstimatedCheckScene != null">
        total_estimated_check_scene = #{totalEstimatedCheckScene,jdbcType=VARCHAR},
      </if>
      <if test="maxAverageDailyTravelCostAmount != null">
        max_average_daily_travel_cost_amount = #{maxAverageDailyTravelCostAmount,jdbcType=BIGINT},
      </if>
      <if test="onlyCheckEstimatedAmountSwitch != null">
        only_check_estimated_amount_switch = #{onlyCheckEstimatedAmountSwitch,jdbcType=INTEGER},
      </if>
      <if test="totalEstimatedPersonalPaySwitch != null">
        total_estimated_personal_pay_switch = #{totalEstimatedPersonalPaySwitch,jdbcType=INTEGER},
      </if>
      <if test="totalEstimatedPersonalPaySceneList != null">
        total_estimated_personal_pay_scene_list = #{totalEstimatedPersonalPaySceneList,jdbcType=VARCHAR},
      </if>
      <if test="applyReasonBringInOrder != null">
        apply_reason_bring_in_order = #{applyReasonBringInOrder,jdbcType=INTEGER},
      </if>
      <if test="costAttributionControlFlag != null">
        cost_attribution_control_flag = #{costAttributionControlFlag,jdbcType=INTEGER},
      </if>
      <if test="costAttributionFillType != null">
        cost_attribution_fill_type = #{costAttributionFillType,jdbcType=INTEGER},
      </if>
      <if test="costAttributionBringInOrderFlag != null">
        cost_attribution_bring_in_order_flag = #{costAttributionBringInOrderFlag,jdbcType=INTEGER},
      </if>
      <if test="takeawayUseCountLimitType != null">
        takeaway_use_count_limit_type = #{takeawayUseCountLimitType,jdbcType=INTEGER},
      </if>
      <if test="meishiUseCountLimitType != null">
        meishi_use_count_limit_type = #{meishiUseCountLimitType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="travelDateControlFlag != null">
        travel_date_control_flag = #{travelDateControlFlag,jdbcType=INTEGER},
      </if>
      <if test="travelDateFillType != null">
        travel_date_fill_type = #{travelDateFillType,jdbcType=INTEGER},
      </if>
      <if test="travelDateDuplicateVerification != null">
        travel_date_duplicate_verification = #{travelDateDuplicateVerification,jdbcType=INTEGER},
      </if>
      <if test="travelDateAutoCalculation != null">
        travel_date_auto_calculation = #{travelDateAutoCalculation,jdbcType=INTEGER},
      </if>
      <if test="travelDateFormat != null">
        travel_date_format = #{travelDateFormat,jdbcType=INTEGER},
      </if>
      <if test="travelDateCrossForm != null">
        travel_date_cross_form = #{travelDateCrossForm,jdbcType=INTEGER},
      </if>
      <if test="addressType != null">
        address_type = #{addressType,jdbcType=INTEGER},
      </if>
      <if test="applySceneDefaultValue != null">
        apply_scene_default_value = #{applySceneDefaultValue,jdbcType=INTEGER},
      </if>
      <if test="travelFormEstimatedAllOrderSwitch != null">
        travel_form_estimated_all_order_switch = #{travelFormEstimatedAllOrderSwitch,jdbcType=INTEGER},
      </if>
      <if test="travelFormEstimatedSingleUpSwitch != null">
        travel_form_estimated_single_up_switch = #{travelFormEstimatedSingleUpSwitch,jdbcType=INTEGER},
      </if>
      <if test="controlUpgradeFlag != null">
        control_upgrade_flag = #{controlUpgradeFlag,jdbcType=INTEGER},
      </if>
      <if test="useCountLimitType != null">
        use_count_limit_type = #{useCountLimitType,jdbcType=INTEGER},
      </if>
      <if test="cityLimitType != null">
        city_limit_type = #{cityLimitType,jdbcType=INTEGER},
      </if>
      <if test="tripApplyReasonBringInOrder != null">
        trip_apply_reason_bring_in_order = #{tripApplyReasonBringInOrder,jdbcType=INTEGER},
      </if>
      <if test="addReasonBringInOrder != null">
        add_reason_bring_in_order = #{addReasonBringInOrder,jdbcType=INTEGER},
      </if>
      <if test="tripAddReasonBringInOrder != null">
        trip_add_reason_bring_in_order = #{tripAddReasonBringInOrder,jdbcType=INTEGER},
      </if>
      <if test="travelDateItemLimit != null">
        travel_date_item_limit = #{travelDateItemLimit,jdbcType=INTEGER},
      </if>
      <if test="deliveryPeriodLimit != null">
        delivery_period_limit = #{deliveryPeriodLimit,jdbcType=INTEGER},
      </if>
      <if test="startCityType != null">
        start_city_type = #{startCityType,jdbcType=INTEGER},
      </if>
      <if test="arrivalCityType != null">
        arrival_city_type = #{arrivalCityType,jdbcType=INTEGER},
      </if>
      <if test="estimatedAmountControlRequired != null">
        estimated_amount_control_required = #{estimatedAmountControlRequired,jdbcType=INTEGER},
      </if>
      <if test="travelerRangeType != null">
        traveler_range_type = #{travelerRangeType,jdbcType=INTEGER},
      </if>
      <if test="travelerEmployeeRange != null">
        traveler_employee_range = #{travelerEmployeeRange,jdbcType=INTEGER},
      </if>
      <if test="travelerOtherEmployeeRange != null">
        traveler_other_employee_range = #{travelerOtherEmployeeRange,jdbcType=INTEGER},
      </if>
      <if test="travelerAllowGroupAll != null">
        traveler_allow_group_all = #{travelerAllowGroupAll,jdbcType=INTEGER},
      </if>
      <if test="travelerRelationList != null">
        traveler_relation_list = #{travelerRelationList,jdbcType=VARCHAR},
      </if>
      <if test="applyReasonIsEdit != null">
        apply_reason_is_edit = #{applyReasonIsEdit,jdbcType=INTEGER},
      </if>
      <if test="tripAddReasonIsEdit != null">
        trip_add_reason_is_edit = #{tripAddReasonIsEdit,jdbcType=INTEGER},
      </if>
      <if test="floatVal != null">
        float_val = #{floatVal,jdbcType=INTEGER},
      </if>
      <if test="tripRuleConfig != null">
        trip_rule_config = #{tripRuleConfig,jdbcType=LONGVARCHAR},
      </if>
      <if test="applyChangeConfig != null">
        apply_change_config = #{applyChangeConfig,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.fenbeitong.saas.core.model.saasplus.CustomFormApplyConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update custom_form_apply_config
    set form_id = #{formId,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=VARCHAR},
      city_type = #{cityType,jdbcType=INTEGER},
      start_date_type = #{startDateType,jdbcType=INTEGER},
      return_trip_date_type = #{returnTripDateType,jdbcType=INTEGER},
      food_delivery_date_type = #{foodDeliveryDateType,jdbcType=INTEGER},
      estimated_cost_check_switch = #{estimatedCostCheckSwitch,jdbcType=INTEGER},
      all_order_estimated_cost_check = #{allOrderEstimatedCostCheck,jdbcType=INTEGER},
      travel_form_estimated_cost_check_type = #{travelFormEstimatedCostCheckType,jdbcType=INTEGER},
      multi_trip_apply_estimated_cost_check = #{multiTripApplyEstimatedCostCheck,jdbcType=INTEGER},
      air_change_estimated_cost_check_switch = #{airChangeEstimatedCostCheckSwitch,jdbcType=INTEGER},
      train_change_estimated_cost_check_switch = #{trainChangeEstimatedCostCheckSwitch,jdbcType=INTEGER},
      personal_pay_switch = #{personalPaySwitch,jdbcType=INTEGER},
      traveler_control_flag = #{travelerControlFlag,jdbcType=INTEGER},
      traveler_fill_type = #{travelerFillType,jdbcType=INTEGER},
      order_check_traveler_flag = #{orderCheckTravelerFlag,jdbcType=INTEGER},
      traveler_limit_flag = #{travelerLimitFlag,jdbcType=INTEGER},
      total_estimated_control_flag = #{totalEstimatedControlFlag,jdbcType=INTEGER},
      total_estimated_fill_type = #{totalEstimatedFillType,jdbcType=INTEGER},
      check_total_estimated_flag = #{checkTotalEstimatedFlag,jdbcType=INTEGER},
      total_estimated_limit_type = #{totalEstimatedLimitType,jdbcType=INTEGER},
      total_estimated_limit_amount = #{totalEstimatedLimitAmount,jdbcType=DECIMAL},
      total_estimated_check_scene = #{totalEstimatedCheckScene,jdbcType=VARCHAR},
      max_average_daily_travel_cost_amount = #{maxAverageDailyTravelCostAmount,jdbcType=BIGINT},
      only_check_estimated_amount_switch = #{onlyCheckEstimatedAmountSwitch,jdbcType=INTEGER},
      total_estimated_personal_pay_switch = #{totalEstimatedPersonalPaySwitch,jdbcType=INTEGER},
      total_estimated_personal_pay_scene_list = #{totalEstimatedPersonalPaySceneList,jdbcType=VARCHAR},
      apply_reason_bring_in_order = #{applyReasonBringInOrder,jdbcType=INTEGER},
      cost_attribution_control_flag = #{costAttributionControlFlag,jdbcType=INTEGER},
      cost_attribution_fill_type = #{costAttributionFillType,jdbcType=INTEGER},
      cost_attribution_bring_in_order_flag = #{costAttributionBringInOrderFlag,jdbcType=INTEGER},
      takeaway_use_count_limit_type = #{takeawayUseCountLimitType,jdbcType=INTEGER},
      meishi_use_count_limit_type = #{meishiUseCountLimitType,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      travel_date_control_flag = #{travelDateControlFlag,jdbcType=INTEGER},
      travel_date_fill_type = #{travelDateFillType,jdbcType=INTEGER},
      travel_date_duplicate_verification = #{travelDateDuplicateVerification,jdbcType=INTEGER},
      travel_date_auto_calculation = #{travelDateAutoCalculation,jdbcType=INTEGER},
      travel_date_format = #{travelDateFormat,jdbcType=INTEGER},
      travel_date_cross_form = #{travelDateCrossForm,jdbcType=INTEGER},
      address_type = #{addressType,jdbcType=INTEGER},
      apply_scene_default_value = #{applySceneDefaultValue,jdbcType=INTEGER},
      travel_form_estimated_all_order_switch = #{travelFormEstimatedAllOrderSwitch,jdbcType=INTEGER},
      travel_form_estimated_single_up_switch = #{travelFormEstimatedSingleUpSwitch,jdbcType=INTEGER},
      control_upgrade_flag = #{controlUpgradeFlag,jdbcType=INTEGER},
      use_count_limit_type = #{useCountLimitType,jdbcType=INTEGER},
      city_limit_type = #{cityLimitType,jdbcType=INTEGER},
      trip_apply_reason_bring_in_order = #{tripApplyReasonBringInOrder,jdbcType=INTEGER},
      add_reason_bring_in_order = #{addReasonBringInOrder,jdbcType=INTEGER},
      trip_add_reason_bring_in_order = #{tripAddReasonBringInOrder,jdbcType=INTEGER},
      travel_date_item_limit = #{travelDateItemLimit,jdbcType=INTEGER},
      delivery_period_limit = #{deliveryPeriodLimit,jdbcType=INTEGER},
      start_city_type = #{startCityType,jdbcType=INTEGER},
      arrival_city_type = #{arrivalCityType,jdbcType=INTEGER},
      estimated_amount_control_required = #{estimatedAmountControlRequired,jdbcType=INTEGER},
      traveler_range_type = #{travelerRangeType,jdbcType=INTEGER},
      traveler_employee_range = #{travelerEmployeeRange,jdbcType=INTEGER},
      traveler_other_employee_range = #{travelerOtherEmployeeRange,jdbcType=INTEGER},
      traveler_allow_group_all = #{travelerAllowGroupAll,jdbcType=INTEGER},
      traveler_relation_list = #{travelerRelationList,jdbcType=VARCHAR},
      apply_reason_is_edit = #{applyReasonIsEdit,jdbcType=INTEGER},
      trip_add_reason_is_edit = #{tripAddReasonIsEdit,jdbcType=INTEGER},
      float_val = #{floatVal,jdbcType=INTEGER},
      trip_rule_config = #{tripRuleConfig,jdbcType=LONGVARCHAR},
      apply_change_config = #{applyChangeConfig,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.saasplus.CustomFormApplyConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update custom_form_apply_config
    set form_id = #{formId,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=VARCHAR},
      city_type = #{cityType,jdbcType=INTEGER},
      start_date_type = #{startDateType,jdbcType=INTEGER},
      return_trip_date_type = #{returnTripDateType,jdbcType=INTEGER},
      food_delivery_date_type = #{foodDeliveryDateType,jdbcType=INTEGER},
      estimated_cost_check_switch = #{estimatedCostCheckSwitch,jdbcType=INTEGER},
      all_order_estimated_cost_check = #{allOrderEstimatedCostCheck,jdbcType=INTEGER},
      travel_form_estimated_cost_check_type = #{travelFormEstimatedCostCheckType,jdbcType=INTEGER},
      multi_trip_apply_estimated_cost_check = #{multiTripApplyEstimatedCostCheck,jdbcType=INTEGER},
      air_change_estimated_cost_check_switch = #{airChangeEstimatedCostCheckSwitch,jdbcType=INTEGER},
      train_change_estimated_cost_check_switch = #{trainChangeEstimatedCostCheckSwitch,jdbcType=INTEGER},
      personal_pay_switch = #{personalPaySwitch,jdbcType=INTEGER},
      traveler_control_flag = #{travelerControlFlag,jdbcType=INTEGER},
      traveler_fill_type = #{travelerFillType,jdbcType=INTEGER},
      order_check_traveler_flag = #{orderCheckTravelerFlag,jdbcType=INTEGER},
      traveler_limit_flag = #{travelerLimitFlag,jdbcType=INTEGER},
      total_estimated_control_flag = #{totalEstimatedControlFlag,jdbcType=INTEGER},
      total_estimated_fill_type = #{totalEstimatedFillType,jdbcType=INTEGER},
      check_total_estimated_flag = #{checkTotalEstimatedFlag,jdbcType=INTEGER},
      total_estimated_limit_type = #{totalEstimatedLimitType,jdbcType=INTEGER},
      total_estimated_limit_amount = #{totalEstimatedLimitAmount,jdbcType=DECIMAL},
      total_estimated_check_scene = #{totalEstimatedCheckScene,jdbcType=VARCHAR},
      max_average_daily_travel_cost_amount = #{maxAverageDailyTravelCostAmount,jdbcType=BIGINT},
      only_check_estimated_amount_switch = #{onlyCheckEstimatedAmountSwitch,jdbcType=INTEGER},
      total_estimated_personal_pay_switch = #{totalEstimatedPersonalPaySwitch,jdbcType=INTEGER},
      total_estimated_personal_pay_scene_list = #{totalEstimatedPersonalPaySceneList,jdbcType=VARCHAR},
      apply_reason_bring_in_order = #{applyReasonBringInOrder,jdbcType=INTEGER},
      cost_attribution_control_flag = #{costAttributionControlFlag,jdbcType=INTEGER},
      cost_attribution_fill_type = #{costAttributionFillType,jdbcType=INTEGER},
      cost_attribution_bring_in_order_flag = #{costAttributionBringInOrderFlag,jdbcType=INTEGER},
      takeaway_use_count_limit_type = #{takeawayUseCountLimitType,jdbcType=INTEGER},
      meishi_use_count_limit_type = #{meishiUseCountLimitType,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      travel_date_control_flag = #{travelDateControlFlag,jdbcType=INTEGER},
      travel_date_fill_type = #{travelDateFillType,jdbcType=INTEGER},
      travel_date_duplicate_verification = #{travelDateDuplicateVerification,jdbcType=INTEGER},
      travel_date_auto_calculation = #{travelDateAutoCalculation,jdbcType=INTEGER},
      travel_date_format = #{travelDateFormat,jdbcType=INTEGER},
      travel_date_cross_form = #{travelDateCrossForm,jdbcType=INTEGER},
      address_type = #{addressType,jdbcType=INTEGER},
      apply_scene_default_value = #{applySceneDefaultValue,jdbcType=INTEGER},
      travel_form_estimated_all_order_switch = #{travelFormEstimatedAllOrderSwitch,jdbcType=INTEGER},
      travel_form_estimated_single_up_switch = #{travelFormEstimatedSingleUpSwitch,jdbcType=INTEGER},
      control_upgrade_flag = #{controlUpgradeFlag,jdbcType=INTEGER},
      use_count_limit_type = #{useCountLimitType,jdbcType=INTEGER},
      city_limit_type = #{cityLimitType,jdbcType=INTEGER},
      trip_apply_reason_bring_in_order = #{tripApplyReasonBringInOrder,jdbcType=INTEGER},
      add_reason_bring_in_order = #{addReasonBringInOrder,jdbcType=INTEGER},
      trip_add_reason_bring_in_order = #{tripAddReasonBringInOrder,jdbcType=INTEGER},
      travel_date_item_limit = #{travelDateItemLimit,jdbcType=INTEGER},
      delivery_period_limit = #{deliveryPeriodLimit,jdbcType=INTEGER},
      start_city_type = #{startCityType,jdbcType=INTEGER},
      arrival_city_type = #{arrivalCityType,jdbcType=INTEGER},
      estimated_amount_control_required = #{estimatedAmountControlRequired,jdbcType=INTEGER},
      traveler_range_type = #{travelerRangeType,jdbcType=INTEGER},
      traveler_employee_range = #{travelerEmployeeRange,jdbcType=INTEGER},
      traveler_other_employee_range = #{travelerOtherEmployeeRange,jdbcType=INTEGER},
      traveler_allow_group_all = #{travelerAllowGroupAll,jdbcType=INTEGER},
      traveler_relation_list = #{travelerRelationList,jdbcType=VARCHAR},
      apply_reason_is_edit = #{applyReasonIsEdit,jdbcType=INTEGER},
      trip_add_reason_is_edit = #{tripAddReasonIsEdit,jdbcType=INTEGER},
      float_val = #{floatVal,jdbcType=INTEGER}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>