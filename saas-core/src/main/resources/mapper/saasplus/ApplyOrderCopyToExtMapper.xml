<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saasplus.ApplyOrderCopyToExtMapper">
    <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.ApplyOrderCopyTo"
            extends="com.fenbeitong.saas.core.dao.saasplus.ApplyOrderCopyToMapper.BaseResultMap">
    </resultMap>

    <select id="queryApplyOrderCCByApplyOrderId" resultMap="BaseResultMap">
    select *
    from apply_order_copy_to
    where apply_order_id = #{applyOrderId,jdbcType=CHAR}
    order by sort asc
  </select>

    <delete id="deleteCCByApplyOrderId">
  delete from apply_order_copy_to
  where apply_order_id = #{applyOrderId,jdbcType=CHAR}
</delete>

    <select id="queryCcUnreadCountByUserId" resultType="java.lang.Integer">
        select count(*)
        from apply_order_copy_to t1
	    inner join apply_order t2
	    on t1.apply_order_id=t2.id
        where t1.user_id = #{userId,jdbcType=VARCHAR}
        and t1.read = false
        and t2.company_id = #{companyId,jdbcType=VARCHAR}
        and t2.type != 5
    </select>

    <update id="updateReadStatusByUserId" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrderCopyTo">
        update apply_order_copy_to
        <set>
            `read` = true
        </set>
        where user_id = #{userId,jdbcType=VARCHAR}
        and `read` = false
    </update>
</mapper>