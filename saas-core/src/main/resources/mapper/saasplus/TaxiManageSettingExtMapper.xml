<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saasplus.TaxiManageSettingExtMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saasplus.TaxiManageSetting"
            extends="com.fenbeitong.saas.core.dao.saasplus.TaxiManageSettingMapper.BaseResultMap">
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, scene_name, icon, can_delete, category, type, state, create_time,
    update_time, operater, is_ding, view_name, didi_taxi_manage_setting_id
  </sql>
  <select id="queryFenbeiTaxiManageSettingBySceneCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from taxi_manage_setting
    where company_id = #{companyId,jdbcType=CHAR}
    and type = #{type,jdbcType=INTEGER}
    and didi_taxi_manage_setting_id is null
  </select>
  <select id="queryDiDiTaxiManageSettingBySceneCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from taxi_manage_setting
    where company_id = #{companyId,jdbcType=CHAR}
    and didi_taxi_manage_setting_id = #{didiTaxiManageSettingId,jdbcType=VARCHAR}
  </select>
</mapper>