<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saasplus.TakeawayManageSettingMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saasplus.TakeawayManageSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="scene_id" jdbcType="INTEGER" property="sceneId" />
    <result column="scene_name" jdbcType="VARCHAR" property="sceneName" />
    <result column="view_name" jdbcType="VARCHAR" property="viewName" />
    <result column="scene_type" jdbcType="INTEGER" property="sceneType" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="can_delete" jdbcType="INTEGER" property="canDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operater" jdbcType="VARCHAR" property="operater" />
    <result column="is_ding" jdbcType="TINYINT" property="isDing" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, scene_id, scene_name, view_name, scene_type, sort, state, can_delete, 
    create_time, update_time, operater, is_ding
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.TakeawayManageSettingExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from takeaway_manage_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from takeaway_manage_setting
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from takeaway_manage_setting
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.TakeawayManageSettingExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from takeaway_manage_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.saasplus.TakeawayManageSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into takeaway_manage_setting (id, company_id, scene_id, 
      scene_name, view_name, scene_type, 
      sort, state, can_delete, 
      create_time, update_time, operater, 
      is_ding)
    values (#{id,jdbcType=VARCHAR}, #{companyId,jdbcType=VARCHAR}, #{sceneId,jdbcType=INTEGER}, 
      #{sceneName,jdbcType=VARCHAR}, #{viewName,jdbcType=VARCHAR}, #{sceneType,jdbcType=INTEGER}, 
      #{sort,jdbcType=INTEGER}, #{state,jdbcType=INTEGER}, #{canDelete,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{operater,jdbcType=VARCHAR}, 
      #{isDing,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.saasplus.TakeawayManageSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into takeaway_manage_setting
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="sceneId != null">
        scene_id,
      </if>
      <if test="sceneName != null">
        scene_name,
      </if>
      <if test="viewName != null">
        view_name,
      </if>
      <if test="sceneType != null">
        scene_type,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="state != null">
        state,
      </if>
      <if test="canDelete != null">
        can_delete,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="operater != null">
        operater,
      </if>
      <if test="isDing != null">
        is_ding,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="sceneId != null">
        #{sceneId,jdbcType=INTEGER},
      </if>
      <if test="sceneName != null">
        #{sceneName,jdbcType=VARCHAR},
      </if>
      <if test="viewName != null">
        #{viewName,jdbcType=VARCHAR},
      </if>
      <if test="sceneType != null">
        #{sceneType,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="state != null">
        #{state,jdbcType=INTEGER},
      </if>
      <if test="canDelete != null">
        #{canDelete,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operater != null">
        #{operater,jdbcType=VARCHAR},
      </if>
      <if test="isDing != null">
        #{isDing,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.TakeawayManageSettingExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from takeaway_manage_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update takeaway_manage_setting
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.sceneId != null">
        scene_id = #{record.sceneId,jdbcType=INTEGER},
      </if>
      <if test="record.sceneName != null">
        scene_name = #{record.sceneName,jdbcType=VARCHAR},
      </if>
      <if test="record.viewName != null">
        view_name = #{record.viewName,jdbcType=VARCHAR},
      </if>
      <if test="record.sceneType != null">
        scene_type = #{record.sceneType,jdbcType=INTEGER},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.state != null">
        state = #{record.state,jdbcType=INTEGER},
      </if>
      <if test="record.canDelete != null">
        can_delete = #{record.canDelete,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.operater != null">
        operater = #{record.operater,jdbcType=VARCHAR},
      </if>
      <if test="record.isDing != null">
        is_ding = #{record.isDing,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update takeaway_manage_setting
    set id = #{record.id,jdbcType=VARCHAR},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      scene_id = #{record.sceneId,jdbcType=INTEGER},
      scene_name = #{record.sceneName,jdbcType=VARCHAR},
      view_name = #{record.viewName,jdbcType=VARCHAR},
      scene_type = #{record.sceneType,jdbcType=INTEGER},
      sort = #{record.sort,jdbcType=INTEGER},
      state = #{record.state,jdbcType=INTEGER},
      can_delete = #{record.canDelete,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      operater = #{record.operater,jdbcType=VARCHAR},
      is_ding = #{record.isDing,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saasplus.TakeawayManageSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update takeaway_manage_setting
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="sceneId != null">
        scene_id = #{sceneId,jdbcType=INTEGER},
      </if>
      <if test="sceneName != null">
        scene_name = #{sceneName,jdbcType=VARCHAR},
      </if>
      <if test="viewName != null">
        view_name = #{viewName,jdbcType=VARCHAR},
      </if>
      <if test="sceneType != null">
        scene_type = #{sceneType,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=INTEGER},
      </if>
      <if test="canDelete != null">
        can_delete = #{canDelete,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operater != null">
        operater = #{operater,jdbcType=VARCHAR},
      </if>
      <if test="isDing != null">
        is_ding = #{isDing,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.saasplus.TakeawayManageSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update takeaway_manage_setting
    set company_id = #{companyId,jdbcType=VARCHAR},
      scene_id = #{sceneId,jdbcType=INTEGER},
      scene_name = #{sceneName,jdbcType=VARCHAR},
      view_name = #{viewName,jdbcType=VARCHAR},
      scene_type = #{sceneType,jdbcType=INTEGER},
      sort = #{sort,jdbcType=INTEGER},
      state = #{state,jdbcType=INTEGER},
      can_delete = #{canDelete,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      operater = #{operater,jdbcType=VARCHAR},
      is_ding = #{isDing,jdbcType=TINYINT}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>