<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saasplus.ApplyOrderCopyToMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.ApplyOrderCopyTo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="apply_order_id" jdbcType="CHAR" property="applyOrderId" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="item_type" jdbcType="INTEGER" property="itemType" />
    <result column="item_id" jdbcType="VARCHAR" property="itemId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="read" jdbcType="BIT" property="read" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, apply_order_id, sort, item_type, item_id, create_time, is_delete, user_id, `read`
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrderCopyToExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from apply_order_copy_to
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from apply_order_copy_to
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from apply_order_copy_to
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrderCopyToExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from apply_order_copy_to
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrderCopyTo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into apply_order_copy_to (id, apply_order_id, sort,
      item_type, item_id, create_time,
      is_delete, user_id, `read`)
    values (#{id,jdbcType=CHAR}, #{applyOrderId,jdbcType=CHAR}, #{sort,jdbcType=INTEGER},
      #{itemType,jdbcType=INTEGER}, #{itemId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{isDelete,jdbcType=INTEGER}, #{userId,jdbcType=VARCHAR}, #{read,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrderCopyTo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into apply_order_copy_to
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="applyOrderId != null">
        apply_order_id,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="itemType != null">
        item_type,
      </if>
      <if test="itemId != null">
        item_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="read != null">
        `read`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="applyOrderId != null">
        #{applyOrderId,jdbcType=CHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="itemType != null">
        #{itemType,jdbcType=INTEGER},
      </if>
      <if test="itemId != null">
        #{itemId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="read != null">
        #{read,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrderCopyToExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from apply_order_copy_to
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_order_copy_to
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.applyOrderId != null">
        apply_order_id = #{record.applyOrderId,jdbcType=CHAR},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.itemType != null">
        item_type = #{record.itemType,jdbcType=INTEGER},
      </if>
      <if test="record.itemId != null">
        item_id = #{record.itemId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDelete != null">
        is_delete = #{record.isDelete,jdbcType=INTEGER},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.read != null">
        `read` = #{record.read,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_order_copy_to
    set id = #{record.id,jdbcType=CHAR},
      apply_order_id = #{record.applyOrderId,jdbcType=CHAR},
      sort = #{record.sort,jdbcType=INTEGER},
      item_type = #{record.itemType,jdbcType=INTEGER},
      item_id = #{record.itemId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      is_delete = #{record.isDelete,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=VARCHAR},
      `read` = #{record.read,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrderCopyTo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_order_copy_to
    <set>
      <if test="applyOrderId != null">
        apply_order_id = #{applyOrderId,jdbcType=CHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="itemType != null">
        item_type = #{itemType,jdbcType=INTEGER},
      </if>
      <if test="itemId != null">
        item_id = #{itemId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="read != null">
        `read` = #{read,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrderCopyTo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_order_copy_to
    set apply_order_id = #{applyOrderId,jdbcType=CHAR},
      sort = #{sort,jdbcType=INTEGER},
      item_type = #{itemType,jdbcType=INTEGER},
      item_id = #{itemId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      is_delete = #{isDelete,jdbcType=INTEGER},
      user_id = #{userId,jdbcType=VARCHAR},
      `read` = #{read,jdbcType=BIT}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>