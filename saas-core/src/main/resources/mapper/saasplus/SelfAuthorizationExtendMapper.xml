<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saasplus.SelfAuthorizationExtendMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saasplus.SelfAuthorizationExtend">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="self_authorization_id" jdbcType="CHAR" property="selfAuthorizationId" />
    <result column="self_authorization_state_circulate_log_id" jdbcType="CHAR" property="selfAuthorizationStateCirculateLogId" />
    <result column="extend_state" jdbcType="INTEGER" property="extendState" />
    <result column="extend_time" jdbcType="INTEGER" property="extendTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, self_authorization_id, self_authorization_state_circulate_log_id, extend_state, 
    extend_time, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.SelfAuthorizationExtendExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from self_authorization_extend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from self_authorization_extend
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from self_authorization_extend
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.SelfAuthorizationExtendExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from self_authorization_extend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.saasplus.SelfAuthorizationExtend">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into self_authorization_extend (id, self_authorization_id, self_authorization_state_circulate_log_id, 
      extend_state, extend_time, create_time, 
      update_time)
    values (#{id,jdbcType=CHAR}, #{selfAuthorizationId,jdbcType=CHAR}, #{selfAuthorizationStateCirculateLogId,jdbcType=CHAR}, 
      #{extendState,jdbcType=INTEGER}, #{extendTime,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.saasplus.SelfAuthorizationExtend">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into self_authorization_extend
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="selfAuthorizationId != null">
        self_authorization_id,
      </if>
      <if test="selfAuthorizationStateCirculateLogId != null">
        self_authorization_state_circulate_log_id,
      </if>
      <if test="extendState != null">
        extend_state,
      </if>
      <if test="extendTime != null">
        extend_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="selfAuthorizationId != null">
        #{selfAuthorizationId,jdbcType=CHAR},
      </if>
      <if test="selfAuthorizationStateCirculateLogId != null">
        #{selfAuthorizationStateCirculateLogId,jdbcType=CHAR},
      </if>
      <if test="extendState != null">
        #{extendState,jdbcType=INTEGER},
      </if>
      <if test="extendTime != null">
        #{extendTime,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.SelfAuthorizationExtendExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from self_authorization_extend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update self_authorization_extend
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.selfAuthorizationId != null">
        self_authorization_id = #{record.selfAuthorizationId,jdbcType=CHAR},
      </if>
      <if test="record.selfAuthorizationStateCirculateLogId != null">
        self_authorization_state_circulate_log_id = #{record.selfAuthorizationStateCirculateLogId,jdbcType=CHAR},
      </if>
      <if test="record.extendState != null">
        extend_state = #{record.extendState,jdbcType=INTEGER},
      </if>
      <if test="record.extendTime != null">
        extend_time = #{record.extendTime,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update self_authorization_extend
    set id = #{record.id,jdbcType=CHAR},
      self_authorization_id = #{record.selfAuthorizationId,jdbcType=CHAR},
      self_authorization_state_circulate_log_id = #{record.selfAuthorizationStateCirculateLogId,jdbcType=CHAR},
      extend_state = #{record.extendState,jdbcType=INTEGER},
      extend_time = #{record.extendTime,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saasplus.SelfAuthorizationExtend">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update self_authorization_extend
    <set>
      <if test="selfAuthorizationId != null">
        self_authorization_id = #{selfAuthorizationId,jdbcType=CHAR},
      </if>
      <if test="selfAuthorizationStateCirculateLogId != null">
        self_authorization_state_circulate_log_id = #{selfAuthorizationStateCirculateLogId,jdbcType=CHAR},
      </if>
      <if test="extendState != null">
        extend_state = #{extendState,jdbcType=INTEGER},
      </if>
      <if test="extendTime != null">
        extend_time = #{extendTime,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.saasplus.SelfAuthorizationExtend">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update self_authorization_extend
    set self_authorization_id = #{selfAuthorizationId,jdbcType=CHAR},
      self_authorization_state_circulate_log_id = #{selfAuthorizationStateCirculateLogId,jdbcType=CHAR},
      extend_state = #{extendState,jdbcType=INTEGER},
      extend_time = #{extendTime,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>