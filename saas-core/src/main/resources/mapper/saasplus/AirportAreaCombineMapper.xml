<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saasplus.AirportAreaCombineMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saasplus.AirportAreaCombine">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="area_code" jdbcType="VARCHAR" property="areaCode" />
    <result column="area_name" jdbcType="VARCHAR" property="areaName" />
    <result column="airport_code" jdbcType="VARCHAR" property="airportCode" />
    <result column="airport_name" jdbcType="VARCHAR" property="airportName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    area_code, area_name, airport_code, airport_name, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.AirportAreaCombineExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from airport_area_combine
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.AirportAreaCombineExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from airport_area_combine
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.saasplus.AirportAreaCombine">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into airport_area_combine (area_code, area_name, airport_code, 
      airport_name, create_time, update_time
      )
    values (#{areaCode,jdbcType=VARCHAR}, #{areaName,jdbcType=VARCHAR}, #{airportCode,jdbcType=VARCHAR}, 
      #{airportName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.saasplus.AirportAreaCombine">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into airport_area_combine
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="areaCode != null">
        area_code,
      </if>
      <if test="areaName != null">
        area_name,
      </if>
      <if test="airportCode != null">
        airport_code,
      </if>
      <if test="airportName != null">
        airport_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="areaCode != null">
        #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="areaName != null">
        #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="airportCode != null">
        #{airportCode,jdbcType=VARCHAR},
      </if>
      <if test="airportName != null">
        #{airportName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.AirportAreaCombineExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from airport_area_combine
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update airport_area_combine
    <set>
      <if test="record.areaCode != null">
        area_code = #{record.areaCode,jdbcType=VARCHAR},
      </if>
      <if test="record.areaName != null">
        area_name = #{record.areaName,jdbcType=VARCHAR},
      </if>
      <if test="record.airportCode != null">
        airport_code = #{record.airportCode,jdbcType=VARCHAR},
      </if>
      <if test="record.airportName != null">
        airport_name = #{record.airportName,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update airport_area_combine
    set area_code = #{record.areaCode,jdbcType=VARCHAR},
      area_name = #{record.areaName,jdbcType=VARCHAR},
      airport_code = #{record.airportCode,jdbcType=VARCHAR},
      airport_name = #{record.airportName,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>