<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saasplus.AssistantRuleMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saasplus.AssistantRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="crowd" jdbcType="INTEGER" property="crowd" />
    <result column="time_trigger" jdbcType="INTEGER" property="timeTrigger" />
    <result column="current_page" jdbcType="INTEGER" property="currentPage" />
    <result column="forward_type" jdbcType="INTEGER" property="forwardType" />
    <result column="forward_page" jdbcType="VARCHAR" property="forwardPage" />
    <result column="ding_talk_forward_page" jdbcType="VARCHAR" property="dingTalkForwardPage" />
    <result column="enterprise_forward_page" jdbcType="VARCHAR" property="enterpriseForwardPage" />
    <result column="notice_message" jdbcType="VARCHAR" property="noticeMessage" />
    <result column="operator_id" jdbcType="VARCHAR" property="operatorId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, crowd, time_trigger, current_page, forward_type, forward_page, ding_talk_forward_page, 
    enterprise_forward_page, notice_message, operator_id, operator_name, create_time, 
    update_time
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.AssistantRuleExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from assistant_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from assistant_rule
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from assistant_rule
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.AssistantRuleExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from assistant_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.saasplus.AssistantRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into assistant_rule (id, crowd, time_trigger, 
      current_page, forward_type, forward_page, 
      ding_talk_forward_page, enterprise_forward_page, 
      notice_message, operator_id, operator_name, 
      create_time, update_time)
    values (#{id,jdbcType=VARCHAR}, #{crowd,jdbcType=INTEGER}, #{timeTrigger,jdbcType=INTEGER}, 
      #{currentPage,jdbcType=INTEGER}, #{forwardType,jdbcType=INTEGER}, #{forwardPage,jdbcType=VARCHAR}, 
      #{dingTalkForwardPage,jdbcType=VARCHAR}, #{enterpriseForwardPage,jdbcType=VARCHAR}, 
      #{noticeMessage,jdbcType=VARCHAR}, #{operatorId,jdbcType=VARCHAR}, #{operatorName,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.saasplus.AssistantRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into assistant_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="crowd != null">
        crowd,
      </if>
      <if test="timeTrigger != null">
        time_trigger,
      </if>
      <if test="currentPage != null">
        current_page,
      </if>
      <if test="forwardType != null">
        forward_type,
      </if>
      <if test="forwardPage != null">
        forward_page,
      </if>
      <if test="dingTalkForwardPage != null">
        ding_talk_forward_page,
      </if>
      <if test="enterpriseForwardPage != null">
        enterprise_forward_page,
      </if>
      <if test="noticeMessage != null">
        notice_message,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="operatorName != null">
        operator_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="crowd != null">
        #{crowd,jdbcType=INTEGER},
      </if>
      <if test="timeTrigger != null">
        #{timeTrigger,jdbcType=INTEGER},
      </if>
      <if test="currentPage != null">
        #{currentPage,jdbcType=INTEGER},
      </if>
      <if test="forwardType != null">
        #{forwardType,jdbcType=INTEGER},
      </if>
      <if test="forwardPage != null">
        #{forwardPage,jdbcType=VARCHAR},
      </if>
      <if test="dingTalkForwardPage != null">
        #{dingTalkForwardPage,jdbcType=VARCHAR},
      </if>
      <if test="enterpriseForwardPage != null">
        #{enterpriseForwardPage,jdbcType=VARCHAR},
      </if>
      <if test="noticeMessage != null">
        #{noticeMessage,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="operatorName != null">
        #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.AssistantRuleExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from assistant_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update assistant_rule
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.crowd != null">
        crowd = #{record.crowd,jdbcType=INTEGER},
      </if>
      <if test="record.timeTrigger != null">
        time_trigger = #{record.timeTrigger,jdbcType=INTEGER},
      </if>
      <if test="record.currentPage != null">
        current_page = #{record.currentPage,jdbcType=INTEGER},
      </if>
      <if test="record.forwardType != null">
        forward_type = #{record.forwardType,jdbcType=INTEGER},
      </if>
      <if test="record.forwardPage != null">
        forward_page = #{record.forwardPage,jdbcType=VARCHAR},
      </if>
      <if test="record.dingTalkForwardPage != null">
        ding_talk_forward_page = #{record.dingTalkForwardPage,jdbcType=VARCHAR},
      </if>
      <if test="record.enterpriseForwardPage != null">
        enterprise_forward_page = #{record.enterpriseForwardPage,jdbcType=VARCHAR},
      </if>
      <if test="record.noticeMessage != null">
        notice_message = #{record.noticeMessage,jdbcType=VARCHAR},
      </if>
      <if test="record.operatorId != null">
        operator_id = #{record.operatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.operatorName != null">
        operator_name = #{record.operatorName,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update assistant_rule
    set id = #{record.id,jdbcType=VARCHAR},
      crowd = #{record.crowd,jdbcType=INTEGER},
      time_trigger = #{record.timeTrigger,jdbcType=INTEGER},
      current_page = #{record.currentPage,jdbcType=INTEGER},
      forward_type = #{record.forwardType,jdbcType=INTEGER},
      forward_page = #{record.forwardPage,jdbcType=VARCHAR},
      ding_talk_forward_page = #{record.dingTalkForwardPage,jdbcType=VARCHAR},
      enterprise_forward_page = #{record.enterpriseForwardPage,jdbcType=VARCHAR},
      notice_message = #{record.noticeMessage,jdbcType=VARCHAR},
      operator_id = #{record.operatorId,jdbcType=VARCHAR},
      operator_name = #{record.operatorName,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saasplus.AssistantRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update assistant_rule
    <set>
      <if test="crowd != null">
        crowd = #{crowd,jdbcType=INTEGER},
      </if>
      <if test="timeTrigger != null">
        time_trigger = #{timeTrigger,jdbcType=INTEGER},
      </if>
      <if test="currentPage != null">
        current_page = #{currentPage,jdbcType=INTEGER},
      </if>
      <if test="forwardType != null">
        forward_type = #{forwardType,jdbcType=INTEGER},
      </if>
      <if test="forwardPage != null">
        forward_page = #{forwardPage,jdbcType=VARCHAR},
      </if>
      <if test="dingTalkForwardPage != null">
        ding_talk_forward_page = #{dingTalkForwardPage,jdbcType=VARCHAR},
      </if>
      <if test="enterpriseForwardPage != null">
        enterprise_forward_page = #{enterpriseForwardPage,jdbcType=VARCHAR},
      </if>
      <if test="noticeMessage != null">
        notice_message = #{noticeMessage,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="operatorName != null">
        operator_name = #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.saasplus.AssistantRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update assistant_rule
    set crowd = #{crowd,jdbcType=INTEGER},
      time_trigger = #{timeTrigger,jdbcType=INTEGER},
      current_page = #{currentPage,jdbcType=INTEGER},
      forward_type = #{forwardType,jdbcType=INTEGER},
      forward_page = #{forwardPage,jdbcType=VARCHAR},
      ding_talk_forward_page = #{dingTalkForwardPage,jdbcType=VARCHAR},
      enterprise_forward_page = #{enterpriseForwardPage,jdbcType=VARCHAR},
      notice_message = #{noticeMessage,jdbcType=VARCHAR},
      operator_id = #{operatorId,jdbcType=VARCHAR},
      operator_name = #{operatorName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>