<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saasplus.TbBankOrderMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saasplus.TbBankOrder">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="shop_name" jdbcType="VARCHAR" property="shopName" />
    <result column="bank_account_no" jdbcType="VARCHAR" property="bankAccountNo" />
    <result column="total_price" jdbcType="DECIMAL" property="totalPrice" />
    <result column="cost_price" jdbcType="DECIMAL" property="costPrice" />
    <result column="company_total_pay" jdbcType="DECIMAL" property="companyTotalPay" />
    <result column="order_status" jdbcType="INTEGER" property="orderStatus" />
    <result column="supplier_id" jdbcType="INTEGER" property="supplierId" />
    <result column="user_mark" jdbcType="INTEGER" property="userMark" />
    <result column="fb_order_id" jdbcType="VARCHAR" property="fbOrderId" />
    <result column="transaction_type" jdbcType="INTEGER" property="transactionType" />
    <result column="order_channel" jdbcType="INTEGER" property="orderChannel" />
    <result column="category_type" jdbcType="INTEGER" property="categoryType" />
    <result column="account_type" jdbcType="INTEGER" property="accountType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="user_phone" jdbcType="VARCHAR" property="userPhone" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="bank_trans_time" jdbcType="TIMESTAMP" property="bankTransTime" />
    <result column="bank_trans_no" jdbcType="VARCHAR" property="bankTransNo" />
    <result column="fb_cashier_txn_id" jdbcType="VARCHAR" property="fbCashierTxnId" />
    <result column="fb_pay_notify_url" jdbcType="VARCHAR" property="fbPayNotifyUrl" />
    <result column="account_sub_type" jdbcType="INTEGER" property="accountSubType" />
    <result column="cost_image_url" jdbcType="VARCHAR" property="costImageUrl" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="bank_hupo_trans_type" jdbcType="INTEGER" property="bankHupoTransType" />
    <result column="bank_ori_trans_no" jdbcType="VARCHAR" property="bankOriTransNo" />
    <result column="month_type" jdbcType="VARCHAR" property="monthType" />
    <result column="sys_ext_1" jdbcType="VARCHAR" property="sysExt1" />
    <result column="sys_ext_2" jdbcType="VARCHAR" property="sysExt2" />
    <result column="sys_ext_3" jdbcType="VARCHAR" property="sysExt3" />
    <result column="sys_ext_4" jdbcType="VARCHAR" property="sysExt4" />
    <result column="show_type" jdbcType="INTEGER" property="showType" />
    <result column="check_status" jdbcType="INTEGER" property="checkStatus" />
    <result column="check_time" jdbcType="TIMESTAMP" property="checkTime" />
    <result column="replace_tag" jdbcType="INTEGER" property="replaceTag" />
    <result column="user_mark_time" jdbcType="TIMESTAMP" property="userMarkTime" />
    <result column="refund_status" jdbcType="INTEGER" property="refundStatus" />
    <result column="total_invoice_price" jdbcType="DECIMAL" property="totalInvoicePrice" />
    <result column="payment_id" jdbcType="VARCHAR" property="paymentId" />
    <result column="electronic_status" jdbcType="INTEGER" property="electronicStatus" />
    <result column="fail_desc" jdbcType="VARCHAR" property="failDesc" />
    <result column="receiver_name" jdbcType="VARCHAR" property="receiverName" />
    <result column="receiver_bank" jdbcType="VARCHAR" property="receiverBank" />
    <result column="receiver_account" jdbcType="VARCHAR" property="receiverAccount" />
    <result column="public_model" jdbcType="INTEGER" property="publicModel" />
    <result column="receiver_account_name" jdbcType="VARCHAR" property="receiverAccountName" />
    <result column="business_mode" jdbcType="INTEGER" property="businessMode" />
    <result column="remind_status" jdbcType="INTEGER" property="remindStatus" />
    <result column="remind_time" jdbcType="TIMESTAMP" property="remindTime" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="apply_bind" jdbcType="INTEGER" property="applyBind" />
    <result column="petty_id" jdbcType="VARCHAR" property="pettyId" />
    <result column="payment_purpose" jdbcType="VARCHAR" property="paymentPurpose" />
    <result column="bank_account_acct_name" jdbcType="VARCHAR" property="bankAccountAcctName" />
    <result column="company_account_id" jdbcType="VARCHAR" property="companyAccountId" />
    <result column="root_order_id" jdbcType="VARCHAR" property="rootOrderId" />
    <result column="pay_back_status" jdbcType="INTEGER" property="payBackStatus" />
    <result column="personal_total_pay" jdbcType="DECIMAL" property="personalTotalPay" />
    <result column="third_payment_price" jdbcType="DECIMAL" property="thirdPaymentPrice" />
    <result column="third_payment_channel" jdbcType="VARCHAR" property="thirdPaymentChannel" />
    <result column="expired_time" jdbcType="TIMESTAMP" property="expiredTime" />
    <result column="reserve_time" jdbcType="TIMESTAMP" property="reserveTime" />
    <result column="invoice_back_status" jdbcType="INTEGER" property="invoiceBackStatus" />
    <result column="invoice_back_time" jdbcType="TIMESTAMP" property="invoiceBackTime" />
    <result column="direct_acct_type" jdbcType="INTEGER" property="directAcctType" />
    <result column="bank_pay_type" jdbcType="VARCHAR" property="bankPayType" />
    <result column="bank_pay_price" jdbcType="DECIMAL" property="bankPayPrice" />
    <result column="supplier_order_no" jdbcType="VARCHAR" property="supplierOrderNo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, order_id, shop_name, bank_account_no, total_price, cost_price, company_total_pay, 
    order_status, supplier_id, user_mark, fb_order_id, transaction_type, order_channel, 
    category_type, account_type, create_time, update_time, pay_time, complete_time, user_id, 
    user_name, user_phone, company_id, bank_trans_time, bank_trans_no, fb_cashier_txn_id, 
    fb_pay_notify_url, account_sub_type, cost_image_url, bank_name, bank_hupo_trans_type, 
    bank_ori_trans_no, month_type, sys_ext_1, sys_ext_2, sys_ext_3, sys_ext_4, show_type, 
    check_status, check_time, replace_tag, user_mark_time, refund_status, total_invoice_price, 
    payment_id, electronic_status, fail_desc, receiver_name, receiver_bank, receiver_account, 
    public_model, receiver_account_name, business_mode, remind_status, remind_time, remarks, 
    apply_bind, petty_id, payment_purpose, bank_account_acct_name, company_account_id, 
    root_order_id, pay_back_status, personal_total_pay, third_payment_price, third_payment_channel, 
    expired_time, reserve_time, invoice_back_status, invoice_back_time, direct_acct_type, 
    bank_pay_type, bank_pay_price, supplier_order_no
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from tb_bank_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tb_bank_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.TbBankOrderExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tb_bank_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.fenbeitong.saas.core.model.saasplus.TbBankOrder" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into tb_bank_order (order_id, shop_name, bank_account_no, 
      total_price, cost_price, company_total_pay, 
      order_status, supplier_id, user_mark, 
      fb_order_id, transaction_type, order_channel, 
      category_type, account_type, create_time, 
      update_time, pay_time, complete_time, 
      user_id, user_name, user_phone, 
      company_id, bank_trans_time, bank_trans_no, 
      fb_cashier_txn_id, fb_pay_notify_url, account_sub_type, 
      cost_image_url, bank_name, bank_hupo_trans_type, 
      bank_ori_trans_no, month_type, sys_ext_1, 
      sys_ext_2, sys_ext_3, sys_ext_4, 
      show_type, check_status, check_time, 
      replace_tag, user_mark_time, refund_status, 
      total_invoice_price, payment_id, electronic_status, 
      fail_desc, receiver_name, receiver_bank, 
      receiver_account, public_model, receiver_account_name, 
      business_mode, remind_status, remind_time, 
      remarks, apply_bind, petty_id, 
      payment_purpose, bank_account_acct_name, company_account_id, 
      root_order_id, pay_back_status, personal_total_pay, 
      third_payment_price, third_payment_channel, 
      expired_time, reserve_time, invoice_back_status, 
      invoice_back_time, direct_acct_type, bank_pay_type, 
      bank_pay_price, supplier_order_no)
    values (#{orderId,jdbcType=VARCHAR}, #{shopName,jdbcType=VARCHAR}, #{bankAccountNo,jdbcType=VARCHAR}, 
      #{totalPrice,jdbcType=DECIMAL}, #{costPrice,jdbcType=DECIMAL}, #{companyTotalPay,jdbcType=DECIMAL}, 
      #{orderStatus,jdbcType=INTEGER}, #{supplierId,jdbcType=INTEGER}, #{userMark,jdbcType=INTEGER}, 
      #{fbOrderId,jdbcType=VARCHAR}, #{transactionType,jdbcType=INTEGER}, #{orderChannel,jdbcType=INTEGER}, 
      #{categoryType,jdbcType=INTEGER}, #{accountType,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{payTime,jdbcType=TIMESTAMP}, #{completeTime,jdbcType=TIMESTAMP}, 
      #{userId,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, #{userPhone,jdbcType=VARCHAR}, 
      #{companyId,jdbcType=VARCHAR}, #{bankTransTime,jdbcType=TIMESTAMP}, #{bankTransNo,jdbcType=VARCHAR}, 
      #{fbCashierTxnId,jdbcType=VARCHAR}, #{fbPayNotifyUrl,jdbcType=VARCHAR}, #{accountSubType,jdbcType=INTEGER}, 
      #{costImageUrl,jdbcType=VARCHAR}, #{bankName,jdbcType=VARCHAR}, #{bankHupoTransType,jdbcType=INTEGER}, 
      #{bankOriTransNo,jdbcType=VARCHAR}, #{monthType,jdbcType=VARCHAR}, #{sysExt1,jdbcType=VARCHAR}, 
      #{sysExt2,jdbcType=VARCHAR}, #{sysExt3,jdbcType=VARCHAR}, #{sysExt4,jdbcType=VARCHAR}, 
      #{showType,jdbcType=INTEGER}, #{checkStatus,jdbcType=INTEGER}, #{checkTime,jdbcType=TIMESTAMP}, 
      #{replaceTag,jdbcType=INTEGER}, #{userMarkTime,jdbcType=TIMESTAMP}, #{refundStatus,jdbcType=INTEGER}, 
      #{totalInvoicePrice,jdbcType=DECIMAL}, #{paymentId,jdbcType=VARCHAR}, #{electronicStatus,jdbcType=INTEGER}, 
      #{failDesc,jdbcType=VARCHAR}, #{receiverName,jdbcType=VARCHAR}, #{receiverBank,jdbcType=VARCHAR}, 
      #{receiverAccount,jdbcType=VARCHAR}, #{publicModel,jdbcType=INTEGER}, #{receiverAccountName,jdbcType=VARCHAR}, 
      #{businessMode,jdbcType=INTEGER}, #{remindStatus,jdbcType=INTEGER}, #{remindTime,jdbcType=TIMESTAMP}, 
      #{remarks,jdbcType=VARCHAR}, #{applyBind,jdbcType=INTEGER}, #{pettyId,jdbcType=VARCHAR}, 
      #{paymentPurpose,jdbcType=VARCHAR}, #{bankAccountAcctName,jdbcType=VARCHAR}, #{companyAccountId,jdbcType=VARCHAR}, 
      #{rootOrderId,jdbcType=VARCHAR}, #{payBackStatus,jdbcType=INTEGER}, #{personalTotalPay,jdbcType=DECIMAL}, 
      #{thirdPaymentPrice,jdbcType=DECIMAL}, #{thirdPaymentChannel,jdbcType=VARCHAR}, 
      #{expiredTime,jdbcType=TIMESTAMP}, #{reserveTime,jdbcType=TIMESTAMP}, #{invoiceBackStatus,jdbcType=INTEGER}, 
      #{invoiceBackTime,jdbcType=TIMESTAMP}, #{directAcctType,jdbcType=INTEGER}, #{bankPayType,jdbcType=VARCHAR}, 
      #{bankPayPrice,jdbcType=DECIMAL}, #{supplierOrderNo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.fenbeitong.saas.core.model.saasplus.TbBankOrder" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into tb_bank_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="shopName != null">
        shop_name,
      </if>
      <if test="bankAccountNo != null">
        bank_account_no,
      </if>
      <if test="totalPrice != null">
        total_price,
      </if>
      <if test="costPrice != null">
        cost_price,
      </if>
      <if test="companyTotalPay != null">
        company_total_pay,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="supplierId != null">
        supplier_id,
      </if>
      <if test="userMark != null">
        user_mark,
      </if>
      <if test="fbOrderId != null">
        fb_order_id,
      </if>
      <if test="transactionType != null">
        transaction_type,
      </if>
      <if test="orderChannel != null">
        order_channel,
      </if>
      <if test="categoryType != null">
        category_type,
      </if>
      <if test="accountType != null">
        account_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="payTime != null">
        pay_time,
      </if>
      <if test="completeTime != null">
        complete_time,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="userPhone != null">
        user_phone,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="bankTransTime != null">
        bank_trans_time,
      </if>
      <if test="bankTransNo != null">
        bank_trans_no,
      </if>
      <if test="fbCashierTxnId != null">
        fb_cashier_txn_id,
      </if>
      <if test="fbPayNotifyUrl != null">
        fb_pay_notify_url,
      </if>
      <if test="accountSubType != null">
        account_sub_type,
      </if>
      <if test="costImageUrl != null">
        cost_image_url,
      </if>
      <if test="bankName != null">
        bank_name,
      </if>
      <if test="bankHupoTransType != null">
        bank_hupo_trans_type,
      </if>
      <if test="bankOriTransNo != null">
        bank_ori_trans_no,
      </if>
      <if test="monthType != null">
        month_type,
      </if>
      <if test="sysExt1 != null">
        sys_ext_1,
      </if>
      <if test="sysExt2 != null">
        sys_ext_2,
      </if>
      <if test="sysExt3 != null">
        sys_ext_3,
      </if>
      <if test="sysExt4 != null">
        sys_ext_4,
      </if>
      <if test="showType != null">
        show_type,
      </if>
      <if test="checkStatus != null">
        check_status,
      </if>
      <if test="checkTime != null">
        check_time,
      </if>
      <if test="replaceTag != null">
        replace_tag,
      </if>
      <if test="userMarkTime != null">
        user_mark_time,
      </if>
      <if test="refundStatus != null">
        refund_status,
      </if>
      <if test="totalInvoicePrice != null">
        total_invoice_price,
      </if>
      <if test="paymentId != null">
        payment_id,
      </if>
      <if test="electronicStatus != null">
        electronic_status,
      </if>
      <if test="failDesc != null">
        fail_desc,
      </if>
      <if test="receiverName != null">
        receiver_name,
      </if>
      <if test="receiverBank != null">
        receiver_bank,
      </if>
      <if test="receiverAccount != null">
        receiver_account,
      </if>
      <if test="publicModel != null">
        public_model,
      </if>
      <if test="receiverAccountName != null">
        receiver_account_name,
      </if>
      <if test="businessMode != null">
        business_mode,
      </if>
      <if test="remindStatus != null">
        remind_status,
      </if>
      <if test="remindTime != null">
        remind_time,
      </if>
      <if test="remarks != null">
        remarks,
      </if>
      <if test="applyBind != null">
        apply_bind,
      </if>
      <if test="pettyId != null">
        petty_id,
      </if>
      <if test="paymentPurpose != null">
        payment_purpose,
      </if>
      <if test="bankAccountAcctName != null">
        bank_account_acct_name,
      </if>
      <if test="companyAccountId != null">
        company_account_id,
      </if>
      <if test="rootOrderId != null">
        root_order_id,
      </if>
      <if test="payBackStatus != null">
        pay_back_status,
      </if>
      <if test="personalTotalPay != null">
        personal_total_pay,
      </if>
      <if test="thirdPaymentPrice != null">
        third_payment_price,
      </if>
      <if test="thirdPaymentChannel != null">
        third_payment_channel,
      </if>
      <if test="expiredTime != null">
        expired_time,
      </if>
      <if test="reserveTime != null">
        reserve_time,
      </if>
      <if test="invoiceBackStatus != null">
        invoice_back_status,
      </if>
      <if test="invoiceBackTime != null">
        invoice_back_time,
      </if>
      <if test="directAcctType != null">
        direct_acct_type,
      </if>
      <if test="bankPayType != null">
        bank_pay_type,
      </if>
      <if test="bankPayPrice != null">
        bank_pay_price,
      </if>
      <if test="supplierOrderNo != null">
        supplier_order_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="shopName != null">
        #{shopName,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountNo != null">
        #{bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="totalPrice != null">
        #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="costPrice != null">
        #{costPrice,jdbcType=DECIMAL},
      </if>
      <if test="companyTotalPay != null">
        #{companyTotalPay,jdbcType=DECIMAL},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="supplierId != null">
        #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="userMark != null">
        #{userMark,jdbcType=INTEGER},
      </if>
      <if test="fbOrderId != null">
        #{fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="transactionType != null">
        #{transactionType,jdbcType=INTEGER},
      </if>
      <if test="orderChannel != null">
        #{orderChannel,jdbcType=INTEGER},
      </if>
      <if test="categoryType != null">
        #{categoryType,jdbcType=INTEGER},
      </if>
      <if test="accountType != null">
        #{accountType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payTime != null">
        #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userPhone != null">
        #{userPhone,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="bankTransTime != null">
        #{bankTransTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bankTransNo != null">
        #{bankTransNo,jdbcType=VARCHAR},
      </if>
      <if test="fbCashierTxnId != null">
        #{fbCashierTxnId,jdbcType=VARCHAR},
      </if>
      <if test="fbPayNotifyUrl != null">
        #{fbPayNotifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="accountSubType != null">
        #{accountSubType,jdbcType=INTEGER},
      </if>
      <if test="costImageUrl != null">
        #{costImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankHupoTransType != null">
        #{bankHupoTransType,jdbcType=INTEGER},
      </if>
      <if test="bankOriTransNo != null">
        #{bankOriTransNo,jdbcType=VARCHAR},
      </if>
      <if test="monthType != null">
        #{monthType,jdbcType=VARCHAR},
      </if>
      <if test="sysExt1 != null">
        #{sysExt1,jdbcType=VARCHAR},
      </if>
      <if test="sysExt2 != null">
        #{sysExt2,jdbcType=VARCHAR},
      </if>
      <if test="sysExt3 != null">
        #{sysExt3,jdbcType=VARCHAR},
      </if>
      <if test="sysExt4 != null">
        #{sysExt4,jdbcType=VARCHAR},
      </if>
      <if test="showType != null">
        #{showType,jdbcType=INTEGER},
      </if>
      <if test="checkStatus != null">
        #{checkStatus,jdbcType=INTEGER},
      </if>
      <if test="checkTime != null">
        #{checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="replaceTag != null">
        #{replaceTag,jdbcType=INTEGER},
      </if>
      <if test="userMarkTime != null">
        #{userMarkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundStatus != null">
        #{refundStatus,jdbcType=INTEGER},
      </if>
      <if test="totalInvoicePrice != null">
        #{totalInvoicePrice,jdbcType=DECIMAL},
      </if>
      <if test="paymentId != null">
        #{paymentId,jdbcType=VARCHAR},
      </if>
      <if test="electronicStatus != null">
        #{electronicStatus,jdbcType=INTEGER},
      </if>
      <if test="failDesc != null">
        #{failDesc,jdbcType=VARCHAR},
      </if>
      <if test="receiverName != null">
        #{receiverName,jdbcType=VARCHAR},
      </if>
      <if test="receiverBank != null">
        #{receiverBank,jdbcType=VARCHAR},
      </if>
      <if test="receiverAccount != null">
        #{receiverAccount,jdbcType=VARCHAR},
      </if>
      <if test="publicModel != null">
        #{publicModel,jdbcType=INTEGER},
      </if>
      <if test="receiverAccountName != null">
        #{receiverAccountName,jdbcType=VARCHAR},
      </if>
      <if test="businessMode != null">
        #{businessMode,jdbcType=INTEGER},
      </if>
      <if test="remindStatus != null">
        #{remindStatus,jdbcType=INTEGER},
      </if>
      <if test="remindTime != null">
        #{remindTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remarks != null">
        #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="applyBind != null">
        #{applyBind,jdbcType=INTEGER},
      </if>
      <if test="pettyId != null">
        #{pettyId,jdbcType=VARCHAR},
      </if>
      <if test="paymentPurpose != null">
        #{paymentPurpose,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountAcctName != null">
        #{bankAccountAcctName,jdbcType=VARCHAR},
      </if>
      <if test="companyAccountId != null">
        #{companyAccountId,jdbcType=VARCHAR},
      </if>
      <if test="rootOrderId != null">
        #{rootOrderId,jdbcType=VARCHAR},
      </if>
      <if test="payBackStatus != null">
        #{payBackStatus,jdbcType=INTEGER},
      </if>
      <if test="personalTotalPay != null">
        #{personalTotalPay,jdbcType=DECIMAL},
      </if>
      <if test="thirdPaymentPrice != null">
        #{thirdPaymentPrice,jdbcType=DECIMAL},
      </if>
      <if test="thirdPaymentChannel != null">
        #{thirdPaymentChannel,jdbcType=VARCHAR},
      </if>
      <if test="expiredTime != null">
        #{expiredTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reserveTime != null">
        #{reserveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="invoiceBackStatus != null">
        #{invoiceBackStatus,jdbcType=INTEGER},
      </if>
      <if test="invoiceBackTime != null">
        #{invoiceBackTime,jdbcType=TIMESTAMP},
      </if>
      <if test="directAcctType != null">
        #{directAcctType,jdbcType=INTEGER},
      </if>
      <if test="bankPayType != null">
        #{bankPayType,jdbcType=VARCHAR},
      </if>
      <if test="bankPayPrice != null">
        #{bankPayPrice,jdbcType=DECIMAL},
      </if>
      <if test="supplierOrderNo != null">
        #{supplierOrderNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.TbBankOrderExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from tb_bank_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tb_bank_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.shopName != null">
        shop_name = #{record.shopName,jdbcType=VARCHAR},
      </if>
      <if test="record.bankAccountNo != null">
        bank_account_no = #{record.bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="record.totalPrice != null">
        total_price = #{record.totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.costPrice != null">
        cost_price = #{record.costPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.companyTotalPay != null">
        company_total_pay = #{record.companyTotalPay,jdbcType=DECIMAL},
      </if>
      <if test="record.orderStatus != null">
        order_status = #{record.orderStatus,jdbcType=INTEGER},
      </if>
      <if test="record.supplierId != null">
        supplier_id = #{record.supplierId,jdbcType=INTEGER},
      </if>
      <if test="record.userMark != null">
        user_mark = #{record.userMark,jdbcType=INTEGER},
      </if>
      <if test="record.fbOrderId != null">
        fb_order_id = #{record.fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.transactionType != null">
        transaction_type = #{record.transactionType,jdbcType=INTEGER},
      </if>
      <if test="record.orderChannel != null">
        order_channel = #{record.orderChannel,jdbcType=INTEGER},
      </if>
      <if test="record.categoryType != null">
        category_type = #{record.categoryType,jdbcType=INTEGER},
      </if>
      <if test="record.accountType != null">
        account_type = #{record.accountType,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.payTime != null">
        pay_time = #{record.payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.completeTime != null">
        complete_time = #{record.completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.userName != null">
        user_name = #{record.userName,jdbcType=VARCHAR},
      </if>
      <if test="record.userPhone != null">
        user_phone = #{record.userPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.bankTransTime != null">
        bank_trans_time = #{record.bankTransTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.bankTransNo != null">
        bank_trans_no = #{record.bankTransNo,jdbcType=VARCHAR},
      </if>
      <if test="record.fbCashierTxnId != null">
        fb_cashier_txn_id = #{record.fbCashierTxnId,jdbcType=VARCHAR},
      </if>
      <if test="record.fbPayNotifyUrl != null">
        fb_pay_notify_url = #{record.fbPayNotifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.accountSubType != null">
        account_sub_type = #{record.accountSubType,jdbcType=INTEGER},
      </if>
      <if test="record.costImageUrl != null">
        cost_image_url = #{record.costImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.bankName != null">
        bank_name = #{record.bankName,jdbcType=VARCHAR},
      </if>
      <if test="record.bankHupoTransType != null">
        bank_hupo_trans_type = #{record.bankHupoTransType,jdbcType=INTEGER},
      </if>
      <if test="record.bankOriTransNo != null">
        bank_ori_trans_no = #{record.bankOriTransNo,jdbcType=VARCHAR},
      </if>
      <if test="record.monthType != null">
        month_type = #{record.monthType,jdbcType=VARCHAR},
      </if>
      <if test="record.sysExt1 != null">
        sys_ext_1 = #{record.sysExt1,jdbcType=VARCHAR},
      </if>
      <if test="record.sysExt2 != null">
        sys_ext_2 = #{record.sysExt2,jdbcType=VARCHAR},
      </if>
      <if test="record.sysExt3 != null">
        sys_ext_3 = #{record.sysExt3,jdbcType=VARCHAR},
      </if>
      <if test="record.sysExt4 != null">
        sys_ext_4 = #{record.sysExt4,jdbcType=VARCHAR},
      </if>
      <if test="record.showType != null">
        show_type = #{record.showType,jdbcType=INTEGER},
      </if>
      <if test="record.checkStatus != null">
        check_status = #{record.checkStatus,jdbcType=INTEGER},
      </if>
      <if test="record.checkTime != null">
        check_time = #{record.checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.replaceTag != null">
        replace_tag = #{record.replaceTag,jdbcType=INTEGER},
      </if>
      <if test="record.userMarkTime != null">
        user_mark_time = #{record.userMarkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.refundStatus != null">
        refund_status = #{record.refundStatus,jdbcType=INTEGER},
      </if>
      <if test="record.totalInvoicePrice != null">
        total_invoice_price = #{record.totalInvoicePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.paymentId != null">
        payment_id = #{record.paymentId,jdbcType=VARCHAR},
      </if>
      <if test="record.electronicStatus != null">
        electronic_status = #{record.electronicStatus,jdbcType=INTEGER},
      </if>
      <if test="record.failDesc != null">
        fail_desc = #{record.failDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.receiverName != null">
        receiver_name = #{record.receiverName,jdbcType=VARCHAR},
      </if>
      <if test="record.receiverBank != null">
        receiver_bank = #{record.receiverBank,jdbcType=VARCHAR},
      </if>
      <if test="record.receiverAccount != null">
        receiver_account = #{record.receiverAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.publicModel != null">
        public_model = #{record.publicModel,jdbcType=INTEGER},
      </if>
      <if test="record.receiverAccountName != null">
        receiver_account_name = #{record.receiverAccountName,jdbcType=VARCHAR},
      </if>
      <if test="record.businessMode != null">
        business_mode = #{record.businessMode,jdbcType=INTEGER},
      </if>
      <if test="record.remindStatus != null">
        remind_status = #{record.remindStatus,jdbcType=INTEGER},
      </if>
      <if test="record.remindTime != null">
        remind_time = #{record.remindTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.remarks != null">
        remarks = #{record.remarks,jdbcType=VARCHAR},
      </if>
      <if test="record.applyBind != null">
        apply_bind = #{record.applyBind,jdbcType=INTEGER},
      </if>
      <if test="record.pettyId != null">
        petty_id = #{record.pettyId,jdbcType=VARCHAR},
      </if>
      <if test="record.paymentPurpose != null">
        payment_purpose = #{record.paymentPurpose,jdbcType=VARCHAR},
      </if>
      <if test="record.bankAccountAcctName != null">
        bank_account_acct_name = #{record.bankAccountAcctName,jdbcType=VARCHAR},
      </if>
      <if test="record.companyAccountId != null">
        company_account_id = #{record.companyAccountId,jdbcType=VARCHAR},
      </if>
      <if test="record.rootOrderId != null">
        root_order_id = #{record.rootOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.payBackStatus != null">
        pay_back_status = #{record.payBackStatus,jdbcType=INTEGER},
      </if>
      <if test="record.personalTotalPay != null">
        personal_total_pay = #{record.personalTotalPay,jdbcType=DECIMAL},
      </if>
      <if test="record.thirdPaymentPrice != null">
        third_payment_price = #{record.thirdPaymentPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.thirdPaymentChannel != null">
        third_payment_channel = #{record.thirdPaymentChannel,jdbcType=VARCHAR},
      </if>
      <if test="record.expiredTime != null">
        expired_time = #{record.expiredTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reserveTime != null">
        reserve_time = #{record.reserveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.invoiceBackStatus != null">
        invoice_back_status = #{record.invoiceBackStatus,jdbcType=INTEGER},
      </if>
      <if test="record.invoiceBackTime != null">
        invoice_back_time = #{record.invoiceBackTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.directAcctType != null">
        direct_acct_type = #{record.directAcctType,jdbcType=INTEGER},
      </if>
      <if test="record.bankPayType != null">
        bank_pay_type = #{record.bankPayType,jdbcType=VARCHAR},
      </if>
      <if test="record.bankPayPrice != null">
        bank_pay_price = #{record.bankPayPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.supplierOrderNo != null">
        supplier_order_no = #{record.supplierOrderNo,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tb_bank_order
    set id = #{record.id,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      shop_name = #{record.shopName,jdbcType=VARCHAR},
      bank_account_no = #{record.bankAccountNo,jdbcType=VARCHAR},
      total_price = #{record.totalPrice,jdbcType=DECIMAL},
      cost_price = #{record.costPrice,jdbcType=DECIMAL},
      company_total_pay = #{record.companyTotalPay,jdbcType=DECIMAL},
      order_status = #{record.orderStatus,jdbcType=INTEGER},
      supplier_id = #{record.supplierId,jdbcType=INTEGER},
      user_mark = #{record.userMark,jdbcType=INTEGER},
      fb_order_id = #{record.fbOrderId,jdbcType=VARCHAR},
      transaction_type = #{record.transactionType,jdbcType=INTEGER},
      order_channel = #{record.orderChannel,jdbcType=INTEGER},
      category_type = #{record.categoryType,jdbcType=INTEGER},
      account_type = #{record.accountType,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      pay_time = #{record.payTime,jdbcType=TIMESTAMP},
      complete_time = #{record.completeTime,jdbcType=TIMESTAMP},
      user_id = #{record.userId,jdbcType=VARCHAR},
      user_name = #{record.userName,jdbcType=VARCHAR},
      user_phone = #{record.userPhone,jdbcType=VARCHAR},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      bank_trans_time = #{record.bankTransTime,jdbcType=TIMESTAMP},
      bank_trans_no = #{record.bankTransNo,jdbcType=VARCHAR},
      fb_cashier_txn_id = #{record.fbCashierTxnId,jdbcType=VARCHAR},
      fb_pay_notify_url = #{record.fbPayNotifyUrl,jdbcType=VARCHAR},
      account_sub_type = #{record.accountSubType,jdbcType=INTEGER},
      cost_image_url = #{record.costImageUrl,jdbcType=VARCHAR},
      bank_name = #{record.bankName,jdbcType=VARCHAR},
      bank_hupo_trans_type = #{record.bankHupoTransType,jdbcType=INTEGER},
      bank_ori_trans_no = #{record.bankOriTransNo,jdbcType=VARCHAR},
      month_type = #{record.monthType,jdbcType=VARCHAR},
      sys_ext_1 = #{record.sysExt1,jdbcType=VARCHAR},
      sys_ext_2 = #{record.sysExt2,jdbcType=VARCHAR},
      sys_ext_3 = #{record.sysExt3,jdbcType=VARCHAR},
      sys_ext_4 = #{record.sysExt4,jdbcType=VARCHAR},
      show_type = #{record.showType,jdbcType=INTEGER},
      check_status = #{record.checkStatus,jdbcType=INTEGER},
      check_time = #{record.checkTime,jdbcType=TIMESTAMP},
      replace_tag = #{record.replaceTag,jdbcType=INTEGER},
      user_mark_time = #{record.userMarkTime,jdbcType=TIMESTAMP},
      refund_status = #{record.refundStatus,jdbcType=INTEGER},
      total_invoice_price = #{record.totalInvoicePrice,jdbcType=DECIMAL},
      payment_id = #{record.paymentId,jdbcType=VARCHAR},
      electronic_status = #{record.electronicStatus,jdbcType=INTEGER},
      fail_desc = #{record.failDesc,jdbcType=VARCHAR},
      receiver_name = #{record.receiverName,jdbcType=VARCHAR},
      receiver_bank = #{record.receiverBank,jdbcType=VARCHAR},
      receiver_account = #{record.receiverAccount,jdbcType=VARCHAR},
      public_model = #{record.publicModel,jdbcType=INTEGER},
      receiver_account_name = #{record.receiverAccountName,jdbcType=VARCHAR},
      business_mode = #{record.businessMode,jdbcType=INTEGER},
      remind_status = #{record.remindStatus,jdbcType=INTEGER},
      remind_time = #{record.remindTime,jdbcType=TIMESTAMP},
      remarks = #{record.remarks,jdbcType=VARCHAR},
      apply_bind = #{record.applyBind,jdbcType=INTEGER},
      petty_id = #{record.pettyId,jdbcType=VARCHAR},
      payment_purpose = #{record.paymentPurpose,jdbcType=VARCHAR},
      bank_account_acct_name = #{record.bankAccountAcctName,jdbcType=VARCHAR},
      company_account_id = #{record.companyAccountId,jdbcType=VARCHAR},
      root_order_id = #{record.rootOrderId,jdbcType=VARCHAR},
      pay_back_status = #{record.payBackStatus,jdbcType=INTEGER},
      personal_total_pay = #{record.personalTotalPay,jdbcType=DECIMAL},
      third_payment_price = #{record.thirdPaymentPrice,jdbcType=DECIMAL},
      third_payment_channel = #{record.thirdPaymentChannel,jdbcType=VARCHAR},
      expired_time = #{record.expiredTime,jdbcType=TIMESTAMP},
      reserve_time = #{record.reserveTime,jdbcType=TIMESTAMP},
      invoice_back_status = #{record.invoiceBackStatus,jdbcType=INTEGER},
      invoice_back_time = #{record.invoiceBackTime,jdbcType=TIMESTAMP},
      direct_acct_type = #{record.directAcctType,jdbcType=INTEGER},
      bank_pay_type = #{record.bankPayType,jdbcType=VARCHAR},
      bank_pay_price = #{record.bankPayPrice,jdbcType=DECIMAL},
      supplier_order_no = #{record.supplierOrderNo,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saasplus.TbBankOrder">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tb_bank_order
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="shopName != null">
        shop_name = #{shopName,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountNo != null">
        bank_account_no = #{bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="totalPrice != null">
        total_price = #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="costPrice != null">
        cost_price = #{costPrice,jdbcType=DECIMAL},
      </if>
      <if test="companyTotalPay != null">
        company_total_pay = #{companyTotalPay,jdbcType=DECIMAL},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="supplierId != null">
        supplier_id = #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="userMark != null">
        user_mark = #{userMark,jdbcType=INTEGER},
      </if>
      <if test="fbOrderId != null">
        fb_order_id = #{fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="transactionType != null">
        transaction_type = #{transactionType,jdbcType=INTEGER},
      </if>
      <if test="orderChannel != null">
        order_channel = #{orderChannel,jdbcType=INTEGER},
      </if>
      <if test="categoryType != null">
        category_type = #{categoryType,jdbcType=INTEGER},
      </if>
      <if test="accountType != null">
        account_type = #{accountType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payTime != null">
        pay_time = #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        complete_time = #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userPhone != null">
        user_phone = #{userPhone,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="bankTransTime != null">
        bank_trans_time = #{bankTransTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bankTransNo != null">
        bank_trans_no = #{bankTransNo,jdbcType=VARCHAR},
      </if>
      <if test="fbCashierTxnId != null">
        fb_cashier_txn_id = #{fbCashierTxnId,jdbcType=VARCHAR},
      </if>
      <if test="fbPayNotifyUrl != null">
        fb_pay_notify_url = #{fbPayNotifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="accountSubType != null">
        account_sub_type = #{accountSubType,jdbcType=INTEGER},
      </if>
      <if test="costImageUrl != null">
        cost_image_url = #{costImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        bank_name = #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankHupoTransType != null">
        bank_hupo_trans_type = #{bankHupoTransType,jdbcType=INTEGER},
      </if>
      <if test="bankOriTransNo != null">
        bank_ori_trans_no = #{bankOriTransNo,jdbcType=VARCHAR},
      </if>
      <if test="monthType != null">
        month_type = #{monthType,jdbcType=VARCHAR},
      </if>
      <if test="sysExt1 != null">
        sys_ext_1 = #{sysExt1,jdbcType=VARCHAR},
      </if>
      <if test="sysExt2 != null">
        sys_ext_2 = #{sysExt2,jdbcType=VARCHAR},
      </if>
      <if test="sysExt3 != null">
        sys_ext_3 = #{sysExt3,jdbcType=VARCHAR},
      </if>
      <if test="sysExt4 != null">
        sys_ext_4 = #{sysExt4,jdbcType=VARCHAR},
      </if>
      <if test="showType != null">
        show_type = #{showType,jdbcType=INTEGER},
      </if>
      <if test="checkStatus != null">
        check_status = #{checkStatus,jdbcType=INTEGER},
      </if>
      <if test="checkTime != null">
        check_time = #{checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="replaceTag != null">
        replace_tag = #{replaceTag,jdbcType=INTEGER},
      </if>
      <if test="userMarkTime != null">
        user_mark_time = #{userMarkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundStatus != null">
        refund_status = #{refundStatus,jdbcType=INTEGER},
      </if>
      <if test="totalInvoicePrice != null">
        total_invoice_price = #{totalInvoicePrice,jdbcType=DECIMAL},
      </if>
      <if test="paymentId != null">
        payment_id = #{paymentId,jdbcType=VARCHAR},
      </if>
      <if test="electronicStatus != null">
        electronic_status = #{electronicStatus,jdbcType=INTEGER},
      </if>
      <if test="failDesc != null">
        fail_desc = #{failDesc,jdbcType=VARCHAR},
      </if>
      <if test="receiverName != null">
        receiver_name = #{receiverName,jdbcType=VARCHAR},
      </if>
      <if test="receiverBank != null">
        receiver_bank = #{receiverBank,jdbcType=VARCHAR},
      </if>
      <if test="receiverAccount != null">
        receiver_account = #{receiverAccount,jdbcType=VARCHAR},
      </if>
      <if test="publicModel != null">
        public_model = #{publicModel,jdbcType=INTEGER},
      </if>
      <if test="receiverAccountName != null">
        receiver_account_name = #{receiverAccountName,jdbcType=VARCHAR},
      </if>
      <if test="businessMode != null">
        business_mode = #{businessMode,jdbcType=INTEGER},
      </if>
      <if test="remindStatus != null">
        remind_status = #{remindStatus,jdbcType=INTEGER},
      </if>
      <if test="remindTime != null">
        remind_time = #{remindTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remarks != null">
        remarks = #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="applyBind != null">
        apply_bind = #{applyBind,jdbcType=INTEGER},
      </if>
      <if test="pettyId != null">
        petty_id = #{pettyId,jdbcType=VARCHAR},
      </if>
      <if test="paymentPurpose != null">
        payment_purpose = #{paymentPurpose,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountAcctName != null">
        bank_account_acct_name = #{bankAccountAcctName,jdbcType=VARCHAR},
      </if>
      <if test="companyAccountId != null">
        company_account_id = #{companyAccountId,jdbcType=VARCHAR},
      </if>
      <if test="rootOrderId != null">
        root_order_id = #{rootOrderId,jdbcType=VARCHAR},
      </if>
      <if test="payBackStatus != null">
        pay_back_status = #{payBackStatus,jdbcType=INTEGER},
      </if>
      <if test="personalTotalPay != null">
        personal_total_pay = #{personalTotalPay,jdbcType=DECIMAL},
      </if>
      <if test="thirdPaymentPrice != null">
        third_payment_price = #{thirdPaymentPrice,jdbcType=DECIMAL},
      </if>
      <if test="thirdPaymentChannel != null">
        third_payment_channel = #{thirdPaymentChannel,jdbcType=VARCHAR},
      </if>
      <if test="expiredTime != null">
        expired_time = #{expiredTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reserveTime != null">
        reserve_time = #{reserveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="invoiceBackStatus != null">
        invoice_back_status = #{invoiceBackStatus,jdbcType=INTEGER},
      </if>
      <if test="invoiceBackTime != null">
        invoice_back_time = #{invoiceBackTime,jdbcType=TIMESTAMP},
      </if>
      <if test="directAcctType != null">
        direct_acct_type = #{directAcctType,jdbcType=INTEGER},
      </if>
      <if test="bankPayType != null">
        bank_pay_type = #{bankPayType,jdbcType=VARCHAR},
      </if>
      <if test="bankPayPrice != null">
        bank_pay_price = #{bankPayPrice,jdbcType=DECIMAL},
      </if>
      <if test="supplierOrderNo != null">
        supplier_order_no = #{supplierOrderNo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.saasplus.TbBankOrder">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tb_bank_order
    set order_id = #{orderId,jdbcType=VARCHAR},
      shop_name = #{shopName,jdbcType=VARCHAR},
      bank_account_no = #{bankAccountNo,jdbcType=VARCHAR},
      total_price = #{totalPrice,jdbcType=DECIMAL},
      cost_price = #{costPrice,jdbcType=DECIMAL},
      company_total_pay = #{companyTotalPay,jdbcType=DECIMAL},
      order_status = #{orderStatus,jdbcType=INTEGER},
      supplier_id = #{supplierId,jdbcType=INTEGER},
      user_mark = #{userMark,jdbcType=INTEGER},
      fb_order_id = #{fbOrderId,jdbcType=VARCHAR},
      transaction_type = #{transactionType,jdbcType=INTEGER},
      order_channel = #{orderChannel,jdbcType=INTEGER},
      category_type = #{categoryType,jdbcType=INTEGER},
      account_type = #{accountType,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      pay_time = #{payTime,jdbcType=TIMESTAMP},
      complete_time = #{completeTime,jdbcType=TIMESTAMP},
      user_id = #{userId,jdbcType=VARCHAR},
      user_name = #{userName,jdbcType=VARCHAR},
      user_phone = #{userPhone,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=VARCHAR},
      bank_trans_time = #{bankTransTime,jdbcType=TIMESTAMP},
      bank_trans_no = #{bankTransNo,jdbcType=VARCHAR},
      fb_cashier_txn_id = #{fbCashierTxnId,jdbcType=VARCHAR},
      fb_pay_notify_url = #{fbPayNotifyUrl,jdbcType=VARCHAR},
      account_sub_type = #{accountSubType,jdbcType=INTEGER},
      cost_image_url = #{costImageUrl,jdbcType=VARCHAR},
      bank_name = #{bankName,jdbcType=VARCHAR},
      bank_hupo_trans_type = #{bankHupoTransType,jdbcType=INTEGER},
      bank_ori_trans_no = #{bankOriTransNo,jdbcType=VARCHAR},
      month_type = #{monthType,jdbcType=VARCHAR},
      sys_ext_1 = #{sysExt1,jdbcType=VARCHAR},
      sys_ext_2 = #{sysExt2,jdbcType=VARCHAR},
      sys_ext_3 = #{sysExt3,jdbcType=VARCHAR},
      sys_ext_4 = #{sysExt4,jdbcType=VARCHAR},
      show_type = #{showType,jdbcType=INTEGER},
      check_status = #{checkStatus,jdbcType=INTEGER},
      check_time = #{checkTime,jdbcType=TIMESTAMP},
      replace_tag = #{replaceTag,jdbcType=INTEGER},
      user_mark_time = #{userMarkTime,jdbcType=TIMESTAMP},
      refund_status = #{refundStatus,jdbcType=INTEGER},
      total_invoice_price = #{totalInvoicePrice,jdbcType=DECIMAL},
      payment_id = #{paymentId,jdbcType=VARCHAR},
      electronic_status = #{electronicStatus,jdbcType=INTEGER},
      fail_desc = #{failDesc,jdbcType=VARCHAR},
      receiver_name = #{receiverName,jdbcType=VARCHAR},
      receiver_bank = #{receiverBank,jdbcType=VARCHAR},
      receiver_account = #{receiverAccount,jdbcType=VARCHAR},
      public_model = #{publicModel,jdbcType=INTEGER},
      receiver_account_name = #{receiverAccountName,jdbcType=VARCHAR},
      business_mode = #{businessMode,jdbcType=INTEGER},
      remind_status = #{remindStatus,jdbcType=INTEGER},
      remind_time = #{remindTime,jdbcType=TIMESTAMP},
      remarks = #{remarks,jdbcType=VARCHAR},
      apply_bind = #{applyBind,jdbcType=INTEGER},
      petty_id = #{pettyId,jdbcType=VARCHAR},
      payment_purpose = #{paymentPurpose,jdbcType=VARCHAR},
      bank_account_acct_name = #{bankAccountAcctName,jdbcType=VARCHAR},
      company_account_id = #{companyAccountId,jdbcType=VARCHAR},
      root_order_id = #{rootOrderId,jdbcType=VARCHAR},
      pay_back_status = #{payBackStatus,jdbcType=INTEGER},
      personal_total_pay = #{personalTotalPay,jdbcType=DECIMAL},
      third_payment_price = #{thirdPaymentPrice,jdbcType=DECIMAL},
      third_payment_channel = #{thirdPaymentChannel,jdbcType=VARCHAR},
      expired_time = #{expiredTime,jdbcType=TIMESTAMP},
      reserve_time = #{reserveTime,jdbcType=TIMESTAMP},
      invoice_back_status = #{invoiceBackStatus,jdbcType=INTEGER},
      invoice_back_time = #{invoiceBackTime,jdbcType=TIMESTAMP},
      direct_acct_type = #{directAcctType,jdbcType=INTEGER},
      bank_pay_type = #{bankPayType,jdbcType=VARCHAR},
      bank_pay_price = #{bankPayPrice,jdbcType=DECIMAL},
      supplier_order_no = #{supplierOrderNo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>