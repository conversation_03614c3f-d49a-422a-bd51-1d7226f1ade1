<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saasplus.SelfAuthorizationMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saasplus.SelfAuthorization">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="auth_employee_id" jdbcType="CHAR" property="authEmployeeId" />
    <result column="be_auth_employee_id" jdbcType="CHAR" property="beAuthEmployeeId" />
    <result column="company_id" jdbcType="CHAR" property="companyId" />
    <result column="self_authorization_type" jdbcType="INTEGER" property="selfAuthorizationType" />
    <result column="auth_permission_type" jdbcType="INTEGER" property="authPermissionType" />
    <result column="auth _start_time" jdbcType="TIMESTAMP" property="authStartTime" />
    <result column="auth_end_time" jdbcType="TIMESTAMP" property="authEndTime" />
    <result column="is_forever" jdbcType="INTEGER" property="isForever" />
    <result column="auth_source" jdbcType="INTEGER" property="authSource" />
    <result column="auth_state" jdbcType="INTEGER" property="authState" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, auth_employee_id, be_auth_employee_id, company_id, self_authorization_type, auth_permission_type, 
    `auth _start_time`, auth_end_time, is_forever, auth_source, auth_state, create_time,
    update_time
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.SelfAuthorizationExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from self_authorization
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from self_authorization
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from self_authorization
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.SelfAuthorizationExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from self_authorization
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.saasplus.SelfAuthorization">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into self_authorization (id, auth_employee_id, be_auth_employee_id, 
      company_id, self_authorization_type, auth_permission_type, 
      `auth _start_time`, auth_end_time, is_forever, 
      auth_source, auth_state, create_time,
      update_time)
    values (#{id,jdbcType=CHAR}, #{authEmployeeId,jdbcType=CHAR}, #{beAuthEmployeeId,jdbcType=CHAR}, 
      #{companyId,jdbcType=CHAR}, #{selfAuthorizationType,jdbcType=INTEGER}, #{authPermissionType,jdbcType=INTEGER}, 
      #{authStartTime,jdbcType=TIMESTAMP}, #{authEndTime,jdbcType=TIMESTAMP}, #{isForever,jdbcType=INTEGER}, 
      #{authSource,jdbcType=INTEGER}, #{authState,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.saasplus.SelfAuthorization">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into self_authorization
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="authEmployeeId != null">
        auth_employee_id,
      </if>
      <if test="beAuthEmployeeId != null">
        be_auth_employee_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="selfAuthorizationType != null">
        self_authorization_type,
      </if>
      <if test="authPermissionType != null">
        auth_permission_type,
      </if>
      <if test="authStartTime != null">
        `auth _start_time`,
      </if>
      <if test="authEndTime != null">
        auth_end_time,
      </if>
      <if test="isForever != null">
        is_forever,
      </if>
      <if test="authSource != null">
        auth_source,
      </if>
      <if test="authState != null">
        auth_state,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="authEmployeeId != null">
        #{authEmployeeId,jdbcType=CHAR},
      </if>
      <if test="beAuthEmployeeId != null">
        #{beAuthEmployeeId,jdbcType=CHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=CHAR},
      </if>
      <if test="selfAuthorizationType != null">
        #{selfAuthorizationType,jdbcType=INTEGER},
      </if>
      <if test="authPermissionType != null">
        #{authPermissionType,jdbcType=INTEGER},
      </if>
      <if test="authStartTime != null">
        #{authStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="authEndTime != null">
        #{authEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isForever != null">
        #{isForever,jdbcType=INTEGER},
      </if>
      <if test="authSource != null">
        #{authSource,jdbcType=INTEGER},
      </if>
      <if test="authState != null">
        #{authState,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saasplus.SelfAuthorizationExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from self_authorization
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update self_authorization
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.authEmployeeId != null">
        auth_employee_id = #{record.authEmployeeId,jdbcType=CHAR},
      </if>
      <if test="record.beAuthEmployeeId != null">
        be_auth_employee_id = #{record.beAuthEmployeeId,jdbcType=CHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=CHAR},
      </if>
      <if test="record.selfAuthorizationType != null">
        self_authorization_type = #{record.selfAuthorizationType,jdbcType=INTEGER},
      </if>
      <if test="record.authPermissionType != null">
        auth_permission_type = #{record.authPermissionType,jdbcType=INTEGER},
      </if>
      <if test="record.authStartTime != null">
        `auth _start_time` = #{record.authStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.authEndTime != null">
        auth_end_time = #{record.authEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isForever != null">
        is_forever = #{record.isForever,jdbcType=INTEGER},
      </if>
      <if test="record.authSource != null">
        auth_source = #{record.authSource,jdbcType=INTEGER},
      </if>
      <if test="record.authState != null">
        auth_state = #{record.authState,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update self_authorization
    set id = #{record.id,jdbcType=CHAR},
      auth_employee_id = #{record.authEmployeeId,jdbcType=CHAR},
      be_auth_employee_id = #{record.beAuthEmployeeId,jdbcType=CHAR},
      company_id = #{record.companyId,jdbcType=CHAR},
      self_authorization_type = #{record.selfAuthorizationType,jdbcType=INTEGER},
      auth_permission_type = #{record.authPermissionType,jdbcType=INTEGER},
      `auth _start_time` = #{record.authStartTime,jdbcType=TIMESTAMP},
      auth_end_time = #{record.authEndTime,jdbcType=TIMESTAMP},
      is_forever = #{record.isForever,jdbcType=INTEGER},
      auth_source = #{record.authSource,jdbcType=INTEGER},
      auth_state = #{record.authState,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saasplus.SelfAuthorization">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update self_authorization
    <set>
      <if test="authEmployeeId != null">
        auth_employee_id = #{authEmployeeId,jdbcType=CHAR},
      </if>
      <if test="beAuthEmployeeId != null">
        be_auth_employee_id = #{beAuthEmployeeId,jdbcType=CHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=CHAR},
      </if>
      <if test="selfAuthorizationType != null">
        self_authorization_type = #{selfAuthorizationType,jdbcType=INTEGER},
      </if>
      <if test="authPermissionType != null">
        auth_permission_type = #{authPermissionType,jdbcType=INTEGER},
      </if>
      <if test="authStartTime != null">
        `auth _start_time` = #{authStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="authEndTime != null">
        auth_end_time = #{authEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isForever != null">
        is_forever = #{isForever,jdbcType=INTEGER},
      </if>
      <if test="authSource != null">
        auth_source = #{authSource,jdbcType=INTEGER},
      </if>
      <if test="authState != null">
        auth_state = #{authState,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.saasplus.SelfAuthorization">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update self_authorization
    set auth_employee_id = #{authEmployeeId,jdbcType=CHAR},
      be_auth_employee_id = #{beAuthEmployeeId,jdbcType=CHAR},
      company_id = #{companyId,jdbcType=CHAR},
      self_authorization_type = #{selfAuthorizationType,jdbcType=INTEGER},
      auth_permission_type = #{authPermissionType,jdbcType=INTEGER},
      `auth _start_time` = #{authStartTime,jdbcType=TIMESTAMP},
      auth_end_time = #{authEndTime,jdbcType=TIMESTAMP},
      is_forever = #{isForever,jdbcType=INTEGER},
      auth_source = #{authSource,jdbcType=INTEGER},
      auth_state = #{authState,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>