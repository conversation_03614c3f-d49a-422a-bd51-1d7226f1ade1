<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.rules.multi.ConsumeTemplateUsedRangeExtMapper">

    <insert id="batchInsert" parameterType="java.util.List" >
        insert into consume_template_used_range
        (
        id,
        rule_id,
        item_id,
        item_name,
        delete_status
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=VARCHAR},
            #{item.ruleId,jdbcType=VARCHAR},
            #{item.itemId,jdbcType=VARCHAR},
            #{item.itemName,jdbcType=VARCHAR},
            #{item.deleteStatus,jdbcType=INTEGER}
            )
        </foreach>
    </insert>

    <update id="batchUpdateSelective" parameterType="java.util.List" >
        <foreach collection="list" item="item" separator=";">
            update consume_template_used_range
            <set>
                <if test="item.ruleId != null">
                    rule_id = #{item.ruleId,jdbcType=VARCHAR},
                </if>
                <if test="item.itemId != null">
                    item_id = #{item.itemId,jdbcType=VARCHAR},
                </if>
                <if test="item.itemName != null">
                    item_name = #{item.itemName,jdbcType=VARCHAR},
                </if>
                <if test="item.deleteStatus != null">
                    delete_status = #{item.deleteStatus,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>



</mapper>