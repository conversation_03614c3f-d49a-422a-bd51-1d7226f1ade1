<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.rules.multi.ConsumeTemplateRulesExtMapper">

  <insert id="batchInsert" parameterType="java.util.List" >
    insert into consume_template_rules
    (
    id,
    name,
    type,
    priority_type,
    priority_no,
    consumer_rule,
    rule_id,
    rule_name,
    biz_type,
    group_status,
    template_id,
    company_id,
    is_able,
    delete_status,
    active_status,
    create_time,
    update_time
    ) values
    <foreach collection="list" item="item" separator=",">
      (
      #{item.id,jdbcType=VARCHAR},
      #{item.name,jdbcType=VARCHAR},
      #{item.type,jdbcType=INTEGER},
      #{item.priorityType,jdbcType=INTEGER},
      #{item.priorityNo,jdbcType=INTEGER},
      #{item.consumerRule,jdbcType=VARCHAR},
      #{item.ruleId,jdbcType=VARCHAR},
      #{item.ruleName,jdbcType=VARCHAR},
      #{item.bizType,jdbcType=INTEGER},
      #{item.groupStatus,jdbcType=INTEGER},
      #{item.templateId,jdbcType=VARCHAR},
      #{item.companyId,jdbcType=VARCHAR},
      #{item.isAble,jdbcType=INTEGER},
      #{item.deleteStatus,jdbcType=INTEGER},
      #{item.activeStatus,jdbcType=INTEGER},
      #{item.createTime,jdbcType=TIMESTAMP},
      #{item.updateTime,jdbcType=TIMESTAMP}
      )
    </foreach>
  </insert>

  <update id="batchUpdateSelective" parameterType="java.util.List" >
    <foreach collection="list" item="item" separator=";">
      update consume_template_rules
      <set>
        <if test="item.name != null">
          name = #{item.name,jdbcType=VARCHAR},
        </if>
        <if test="item.type != null">
          type = #{item.type,jdbcType=INTEGER},
        </if>
        <if test="item.priorityType != null">
          priority_type = #{item.priorityType,jdbcType=INTEGER},
        </if>
        <if test="item.priorityNo != null">
          priority_no = #{item.priorityNo,jdbcType=INTEGER},
        </if>
        <if test="item.consumerRule != null">
          consumer_rule = #{item.consumerRule,jdbcType=VARCHAR},
        </if>
        <if test="item.ruleId != null">
          rule_id = #{item.ruleId,jdbcType=VARCHAR},
        </if>
        <if test="item.ruleName != null">
          rule_name = #{item.ruleName,jdbcType=VARCHAR},
        </if>
        <if test="item.bizType != null">
          biz_type = #{item.bizType,jdbcType=INTEGER},
        </if>
        <if test="item.groupStatus != null">
          group_status = #{item.groupStatus,jdbcType=INTEGER},
        </if>
        <if test="item.templateId != null">
          template_id = #{item.templateId,jdbcType=VARCHAR},
        </if>
        <if test="item.companyId != null">
          company_id = #{item.companyId,jdbcType=VARCHAR},
        </if>
        <if test="item.isAble != null">
          is_able = #{item.isAble,jdbcType=INTEGER},
        </if>
        <if test="item.createTime != null">
          create_time = #{item.createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.updateTime != null">
          update_time = #{item.updateTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.deleteStatus != null">
          delete_status = #{item.deleteStatus,jdbcType=INTEGER},
        </if>
        <if test="item.activeStatus != null">
          active_status = #{item.activeStatus,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id,jdbcType=VARCHAR}
    </foreach>
  </update>

</mapper>