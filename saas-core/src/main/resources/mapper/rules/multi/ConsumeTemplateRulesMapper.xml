<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.rules.multi.ConsumeTemplateRulesMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.rules.multi.ConsumeTemplateRules">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="priority_type" jdbcType="INTEGER" property="priorityType" />
    <result column="priority_no" jdbcType="INTEGER" property="priorityNo" />
    <result column="consumer_rule" jdbcType="VARCHAR" property="consumerRule" />
    <result column="rule_id" jdbcType="VARCHAR" property="ruleId" />
    <result column="rule_name" jdbcType="VARCHAR" property="ruleName" />
    <result column="biz_type" jdbcType="INTEGER" property="bizType" />
    <result column="group_status" jdbcType="INTEGER" property="groupStatus" />
    <result column="template_id" jdbcType="VARCHAR" property="templateId" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="is_able" jdbcType="INTEGER" property="isAble" />
    <result column="delete_status" jdbcType="INTEGER" property="deleteStatus" />
    <result column="active_status" jdbcType="INTEGER" property="activeStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, name, type, priority_type, priority_no, consumer_rule, rule_id, rule_name, biz_type, 
    group_status, template_id, company_id, is_able, delete_status, active_status, create_time, 
    update_time
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.rules.multi.ConsumeTemplateRulesExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from consume_template_rules
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from consume_template_rules
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from consume_template_rules
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.rules.multi.ConsumeTemplateRulesExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from consume_template_rules
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.rules.multi.ConsumeTemplateRules">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into consume_template_rules (id, name, type, 
      priority_type, priority_no, consumer_rule, 
      rule_id, rule_name, biz_type, 
      group_status, template_id, company_id, 
      is_able, delete_status, active_status, 
      create_time, update_time)
    values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, 
      #{priorityType,jdbcType=INTEGER}, #{priorityNo,jdbcType=INTEGER}, #{consumerRule,jdbcType=VARCHAR}, 
      #{ruleId,jdbcType=VARCHAR}, #{ruleName,jdbcType=VARCHAR}, #{bizType,jdbcType=INTEGER}, 
      #{groupStatus,jdbcType=INTEGER}, #{templateId,jdbcType=VARCHAR}, #{companyId,jdbcType=VARCHAR}, 
      #{isAble,jdbcType=INTEGER}, #{deleteStatus,jdbcType=INTEGER}, #{activeStatus,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.rules.multi.ConsumeTemplateRules">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into consume_template_rules
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="priorityType != null">
        priority_type,
      </if>
      <if test="priorityNo != null">
        priority_no,
      </if>
      <if test="consumerRule != null">
        consumer_rule,
      </if>
      <if test="ruleId != null">
        rule_id,
      </if>
      <if test="ruleName != null">
        rule_name,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="groupStatus != null">
        group_status,
      </if>
      <if test="templateId != null">
        template_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="isAble != null">
        is_able,
      </if>
      <if test="deleteStatus != null">
        delete_status,
      </if>
      <if test="activeStatus != null">
        active_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="priorityType != null">
        #{priorityType,jdbcType=INTEGER},
      </if>
      <if test="priorityNo != null">
        #{priorityNo,jdbcType=INTEGER},
      </if>
      <if test="consumerRule != null">
        #{consumerRule,jdbcType=VARCHAR},
      </if>
      <if test="ruleId != null">
        #{ruleId,jdbcType=VARCHAR},
      </if>
      <if test="ruleName != null">
        #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=INTEGER},
      </if>
      <if test="groupStatus != null">
        #{groupStatus,jdbcType=INTEGER},
      </if>
      <if test="templateId != null">
        #{templateId,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="isAble != null">
        #{isAble,jdbcType=INTEGER},
      </if>
      <if test="deleteStatus != null">
        #{deleteStatus,jdbcType=INTEGER},
      </if>
      <if test="activeStatus != null">
        #{activeStatus,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.rules.multi.ConsumeTemplateRulesExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from consume_template_rules
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update consume_template_rules
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.priorityType != null">
        priority_type = #{record.priorityType,jdbcType=INTEGER},
      </if>
      <if test="record.priorityNo != null">
        priority_no = #{record.priorityNo,jdbcType=INTEGER},
      </if>
      <if test="record.consumerRule != null">
        consumer_rule = #{record.consumerRule,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleId != null">
        rule_id = #{record.ruleId,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleName != null">
        rule_name = #{record.ruleName,jdbcType=VARCHAR},
      </if>
      <if test="record.bizType != null">
        biz_type = #{record.bizType,jdbcType=INTEGER},
      </if>
      <if test="record.groupStatus != null">
        group_status = #{record.groupStatus,jdbcType=INTEGER},
      </if>
      <if test="record.templateId != null">
        template_id = #{record.templateId,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.isAble != null">
        is_able = #{record.isAble,jdbcType=INTEGER},
      </if>
      <if test="record.deleteStatus != null">
        delete_status = #{record.deleteStatus,jdbcType=INTEGER},
      </if>
      <if test="record.activeStatus != null">
        active_status = #{record.activeStatus,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update consume_template_rules
    set id = #{record.id,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=INTEGER},
      priority_type = #{record.priorityType,jdbcType=INTEGER},
      priority_no = #{record.priorityNo,jdbcType=INTEGER},
      consumer_rule = #{record.consumerRule,jdbcType=VARCHAR},
      rule_id = #{record.ruleId,jdbcType=VARCHAR},
      rule_name = #{record.ruleName,jdbcType=VARCHAR},
      biz_type = #{record.bizType,jdbcType=INTEGER},
      group_status = #{record.groupStatus,jdbcType=INTEGER},
      template_id = #{record.templateId,jdbcType=VARCHAR},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      is_able = #{record.isAble,jdbcType=INTEGER},
      delete_status = #{record.deleteStatus,jdbcType=INTEGER},
      active_status = #{record.activeStatus,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.rules.multi.ConsumeTemplateRules">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update consume_template_rules
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="priorityType != null">
        priority_type = #{priorityType,jdbcType=INTEGER},
      </if>
      <if test="priorityNo != null">
        priority_no = #{priorityNo,jdbcType=INTEGER},
      </if>
      <if test="consumerRule != null">
        consumer_rule = #{consumerRule,jdbcType=VARCHAR},
      </if>
      <if test="ruleId != null">
        rule_id = #{ruleId,jdbcType=VARCHAR},
      </if>
      <if test="ruleName != null">
        rule_name = #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=INTEGER},
      </if>
      <if test="groupStatus != null">
        group_status = #{groupStatus,jdbcType=INTEGER},
      </if>
      <if test="templateId != null">
        template_id = #{templateId,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="isAble != null">
        is_able = #{isAble,jdbcType=INTEGER},
      </if>
      <if test="deleteStatus != null">
        delete_status = #{deleteStatus,jdbcType=INTEGER},
      </if>
      <if test="activeStatus != null">
        active_status = #{activeStatus,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.rules.multi.ConsumeTemplateRules">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update consume_template_rules
    set name = #{name,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      priority_type = #{priorityType,jdbcType=INTEGER},
      priority_no = #{priorityNo,jdbcType=INTEGER},
      consumer_rule = #{consumerRule,jdbcType=VARCHAR},
      rule_id = #{ruleId,jdbcType=VARCHAR},
      rule_name = #{ruleName,jdbcType=VARCHAR},
      biz_type = #{bizType,jdbcType=INTEGER},
      group_status = #{groupStatus,jdbcType=INTEGER},
      template_id = #{templateId,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=VARCHAR},
      is_able = #{isAble,jdbcType=INTEGER},
      delete_status = #{deleteStatus,jdbcType=INTEGER},
      active_status = #{activeStatus,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>