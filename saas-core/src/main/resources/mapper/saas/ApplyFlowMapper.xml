<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.ApplyFlowMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.ApplyFlow">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="operator_id" jdbcType="VARCHAR" property="operatorId" />
    <result column="company_apply_setting_id" jdbcType="VARCHAR" property="companyApplySettingId" />
    <result column="company_apply_type" jdbcType="INTEGER" property="companyApplyType" />
    <result column="cc_notice_type" jdbcType="INTEGER" property="ccNoticeType" />
    <result column="is_exceed_buy_flow" jdbcType="INTEGER" property="isExceedBuyFlow" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, create_time, operator_id, company_apply_setting_id, company_apply_type, 
    cc_notice_type, is_exceed_buy_flow
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyFlowExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from apply_flow
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from apply_flow
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from apply_flow
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyFlowExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from apply_flow
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.saas.ApplyFlow">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into apply_flow (id, company_id, create_time, 
      operator_id, company_apply_setting_id, company_apply_type, 
      cc_notice_type, is_exceed_buy_flow)
    values (#{id,jdbcType=CHAR}, #{companyId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{operatorId,jdbcType=VARCHAR}, #{companyApplySettingId,jdbcType=VARCHAR}, #{companyApplyType,jdbcType=INTEGER}, 
      #{ccNoticeType,jdbcType=INTEGER}, #{isExceedBuyFlow,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.saas.ApplyFlow">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into apply_flow
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="companyApplySettingId != null">
        company_apply_setting_id,
      </if>
      <if test="companyApplyType != null">
        company_apply_type,
      </if>
      <if test="ccNoticeType != null">
        cc_notice_type,
      </if>
      <if test="isExceedBuyFlow != null">
        is_exceed_buy_flow,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="companyApplySettingId != null">
        #{companyApplySettingId,jdbcType=VARCHAR},
      </if>
      <if test="companyApplyType != null">
        #{companyApplyType,jdbcType=INTEGER},
      </if>
      <if test="ccNoticeType != null">
        #{ccNoticeType,jdbcType=INTEGER},
      </if>
      <if test="isExceedBuyFlow != null">
        #{isExceedBuyFlow,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyFlowExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from apply_flow
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_flow
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.operatorId != null">
        operator_id = #{record.operatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.companyApplySettingId != null">
        company_apply_setting_id = #{record.companyApplySettingId,jdbcType=VARCHAR},
      </if>
      <if test="record.companyApplyType != null">
        company_apply_type = #{record.companyApplyType,jdbcType=INTEGER},
      </if>
      <if test="record.ccNoticeType != null">
        cc_notice_type = #{record.ccNoticeType,jdbcType=INTEGER},
      </if>
      <if test="record.isExceedBuyFlow != null">
        is_exceed_buy_flow = #{record.isExceedBuyFlow,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_flow
    set id = #{record.id,jdbcType=CHAR},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      operator_id = #{record.operatorId,jdbcType=VARCHAR},
      company_apply_setting_id = #{record.companyApplySettingId,jdbcType=VARCHAR},
      company_apply_type = #{record.companyApplyType,jdbcType=INTEGER},
      cc_notice_type = #{record.ccNoticeType,jdbcType=INTEGER},
      is_exceed_buy_flow = #{record.isExceedBuyFlow,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saas.ApplyFlow">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_flow
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="companyApplySettingId != null">
        company_apply_setting_id = #{companyApplySettingId,jdbcType=VARCHAR},
      </if>
      <if test="companyApplyType != null">
        company_apply_type = #{companyApplyType,jdbcType=INTEGER},
      </if>
      <if test="ccNoticeType != null">
        cc_notice_type = #{ccNoticeType,jdbcType=INTEGER},
      </if>
      <if test="isExceedBuyFlow != null">
        is_exceed_buy_flow = #{isExceedBuyFlow,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.saas.ApplyFlow">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_flow
    set company_id = #{companyId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      operator_id = #{operatorId,jdbcType=VARCHAR},
      company_apply_setting_id = #{companyApplySettingId,jdbcType=VARCHAR},
      company_apply_type = #{companyApplyType,jdbcType=INTEGER},
      cc_notice_type = #{ccNoticeType,jdbcType=INTEGER},
      is_exceed_buy_flow = #{isExceedBuyFlow,jdbcType=INTEGER}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>