<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.MessageMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.Message">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="msg_type" jdbcType="INTEGER" property="msgType" />
    <result column="biz_type" jdbcType="INTEGER" property="bizType" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="biz_order" jdbcType="VARCHAR" property="bizOrder" />
    <result column="info" jdbcType="VARCHAR" property="info" />
    <result column="link" jdbcType="VARCHAR" property="link" />
    <result column="sender" jdbcType="CHAR" property="sender" />
    <result column="sender_type" jdbcType="INTEGER" property="senderType" />
    <result column="receiver" jdbcType="CHAR" property="receiver" />
    <result column="read" jdbcType="BIT" property="read" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="company_id" jdbcType="CHAR" property="companyId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, msg_type, biz_type, title, comment, biz_order, info, link, sender, sender_type,
    receiver, read, create_time, update_time, company_id
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saas.MessageExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from message
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="pageSize != null">
      <if test="offset == null">
        limit ${pageSize}
      </if>
      <if test="offset != null">
        limit ${pageSize} offset ${offset}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from message
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from message
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saas.MessageExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from message
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.saas.Message">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into message (id, msg_type, biz_type,
    title, comment, biz_order,
    info, link, sender, sender_type,
    receiver, read, create_time,
    update_time, company_id)
    values (#{id,jdbcType=CHAR}, #{msgType,jdbcType=INTEGER}, #{bizType,jdbcType=INTEGER},
    #{title,jdbcType=VARCHAR}, #{comment,jdbcType=VARCHAR}, #{bizOrder,jdbcType=VARCHAR},
    #{info,jdbcType=VARCHAR}, #{link,jdbcType=VARCHAR}, #{sender,jdbcType=CHAR}, #{senderType,jdbcType=INTEGER},
    #{receiver,jdbcType=CHAR}, #{read,jdbcType=BIT}, #{createTime,jdbcType=TIMESTAMP},
    #{updateTime,jdbcType=TIMESTAMP}, #{companyId,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.saas.Message">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into message
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="msgType != null">
        msg_type,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="comment != null">
        comment,
      </if>
      <if test="bizOrder != null">
        biz_order,
      </if>
      <if test="info != null">
        info,
      </if>
      <if test="link != null">
        link,
      </if>
      <if test="sender != null">
        sender,
      </if>
      <if test="senderType != null">
        sender_type,
      </if>
      <if test="receiver != null">
        receiver,
      </if>
      <if test="read != null">
        read,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="msgType != null">
        #{msgType,jdbcType=INTEGER},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=INTEGER},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="comment != null">
        #{comment,jdbcType=VARCHAR},
      </if>
      <if test="bizOrder != null">
        #{bizOrder,jdbcType=VARCHAR},
      </if>
      <if test="info != null">
        #{info,jdbcType=VARCHAR},
      </if>
      <if test="link != null">
        #{link,jdbcType=VARCHAR},
      </if>
      <if test="sender != null">
        #{sender,jdbcType=CHAR},
      </if>
      <if test="senderType != null">
        #{senderType,jdbcType=INTEGER},
      </if>
      <if test="receiver != null">
        #{receiver,jdbcType=CHAR},
      </if>
      <if test="read != null">
        #{read,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saas.MessageExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from message
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update message
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.msgType != null">
        msg_type = #{record.msgType,jdbcType=INTEGER},
      </if>
      <if test="record.bizType != null">
        biz_type = #{record.bizType,jdbcType=INTEGER},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.comment != null">
        comment = #{record.comment,jdbcType=VARCHAR},
      </if>
      <if test="record.bizOrder != null">
        biz_order = #{record.bizOrder,jdbcType=VARCHAR},
      </if>
      <if test="record.info != null">
        info = #{record.info,jdbcType=VARCHAR},
      </if>
      <if test="record.link != null">
        link = #{record.link,jdbcType=VARCHAR},
      </if>
      <if test="record.sender != null">
        sender = #{record.sender,jdbcType=CHAR},
      </if>
      <if test="record.senderType != null">
        sender_type = #{record.senderType,jdbcType=INTEGER},
      </if>
      <if test="record.receiver != null">
        receiver = #{record.receiver,jdbcType=CHAR},
      </if>
      <if test="record.read != null">
        read = #{record.read,jdbcType=BIT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update message
    set id = #{record.id,jdbcType=CHAR},
    msg_type = #{record.msgType,jdbcType=INTEGER},
    biz_type = #{record.bizType,jdbcType=INTEGER},
    title = #{record.title,jdbcType=VARCHAR},
    comment = #{record.comment,jdbcType=VARCHAR},
    biz_order = #{record.bizOrder,jdbcType=VARCHAR},
    info = #{record.info,jdbcType=VARCHAR},
    link = #{record.link,jdbcType=VARCHAR},
    sender = #{record.sender,jdbcType=CHAR},
    sender_type = #{record.senderType,jdbcType=INTEGER},
    receiver = #{record.receiver,jdbcType=CHAR},
    read = #{record.read,jdbcType=BIT},
    create_time = #{record.createTime,jdbcType=TIMESTAMP},
    update_time = #{record.updateTime,jdbcType=TIMESTAMP},
    company_id = #{record.companyId,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saas.Message">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update message
    <set>
      <if test="msgType != null">
        msg_type = #{msgType,jdbcType=INTEGER},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=INTEGER},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="comment != null">
        comment = #{comment,jdbcType=VARCHAR},
      </if>
      <if test="bizOrder != null">
        biz_order = #{bizOrder,jdbcType=VARCHAR},
      </if>
      <if test="info != null">
        info = #{info,jdbcType=VARCHAR},
      </if>
      <if test="link != null">
        link = #{link,jdbcType=VARCHAR},
      </if>
      <if test="sender != null">
        sender = #{sender,jdbcType=CHAR},
      </if>
      <if test="senderType != null">
        sender_type = #{senderType,jdbcType=INTEGER},
      </if>
      <if test="receiver != null">
        receiver = #{receiver,jdbcType=CHAR},
      </if>
      <if test="read != null">
        read = #{read,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.saas.Message">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update message
    set msg_type = #{msgType,jdbcType=INTEGER},
    biz_type = #{bizType,jdbcType=INTEGER},
    title = #{title,jdbcType=VARCHAR},
    comment = #{comment,jdbcType=VARCHAR},
    biz_order = #{bizOrder,jdbcType=VARCHAR},
    info = #{info,jdbcType=VARCHAR},
    link = #{link,jdbcType=VARCHAR},
    sender = #{sender,jdbcType=CHAR},
    sender_type = #{senderType,jdbcType=INTEGER},
    receiver = #{receiver,jdbcType=CHAR},
    read = #{read,jdbcType=BIT},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    update_time = #{updateTime,jdbcType=TIMESTAMP},
    company_id = #{companyId,jdbcType=CHAR}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>