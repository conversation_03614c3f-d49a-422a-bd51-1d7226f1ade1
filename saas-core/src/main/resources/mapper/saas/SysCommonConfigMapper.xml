<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.SysCommonConfigMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.SysCommonConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="method_code" jdbcType="VARCHAR" property="methodCode" />
    <result column="method_type" jdbcType="INTEGER" property="methodType" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="code" jdbcType="INTEGER" property="code" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="msg" jdbcType="VARCHAR" property="msg" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="switch_type" jdbcType="INTEGER" property="switchType" />
    <result column="ext_content" jdbcType="VARCHAR" property="extContent" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, method_code, method_type, version, code, type, msg, title, switch_type, ext_content, 
    create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saas.SysCommonConfigExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sys_common_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${limit} offset ${offset}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from sys_common_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from sys_common_config
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saas.SysCommonConfigExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from sys_common_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.saas.SysCommonConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into sys_common_config (id, method_code, method_type, 
      version, code, type, 
      msg, title, switch_type, 
      ext_content, create_time, update_time
      )
    values (#{id,jdbcType=INTEGER}, #{methodCode,jdbcType=VARCHAR}, #{methodType,jdbcType=INTEGER}, 
      #{version,jdbcType=VARCHAR}, #{code,jdbcType=INTEGER}, #{type,jdbcType=INTEGER}, 
      #{msg,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, #{switchType,jdbcType=INTEGER}, 
      #{extContent,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.saas.SysCommonConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into sys_common_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="methodCode != null">
        method_code,
      </if>
      <if test="methodType != null">
        method_type,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="msg != null">
        msg,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="switchType != null">
        switch_type,
      </if>
      <if test="extContent != null">
        ext_content,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="methodCode != null">
        #{methodCode,jdbcType=VARCHAR},
      </if>
      <if test="methodType != null">
        #{methodType,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="msg != null">
        #{msg,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="switchType != null">
        #{switchType,jdbcType=INTEGER},
      </if>
      <if test="extContent != null">
        #{extContent,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saas.SysCommonConfigExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from sys_common_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update sys_common_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.methodCode != null">
        method_code = #{record.methodCode,jdbcType=VARCHAR},
      </if>
      <if test="record.methodType != null">
        method_type = #{record.methodType,jdbcType=INTEGER},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=VARCHAR},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=INTEGER},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.msg != null">
        msg = #{record.msg,jdbcType=VARCHAR},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.switchType != null">
        switch_type = #{record.switchType,jdbcType=INTEGER},
      </if>
      <if test="record.extContent != null">
        ext_content = #{record.extContent,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update sys_common_config
    set id = #{record.id,jdbcType=INTEGER},
      method_code = #{record.methodCode,jdbcType=VARCHAR},
      method_type = #{record.methodType,jdbcType=INTEGER},
      version = #{record.version,jdbcType=VARCHAR},
      code = #{record.code,jdbcType=INTEGER},
      type = #{record.type,jdbcType=INTEGER},
      msg = #{record.msg,jdbcType=VARCHAR},
      title = #{record.title,jdbcType=VARCHAR},
      switch_type = #{record.switchType,jdbcType=INTEGER},
      ext_content = #{record.extContent,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saas.SysCommonConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update sys_common_config
    <set>
      <if test="methodCode != null">
        method_code = #{methodCode,jdbcType=VARCHAR},
      </if>
      <if test="methodType != null">
        method_type = #{methodType,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="msg != null">
        msg = #{msg,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="switchType != null">
        switch_type = #{switchType,jdbcType=INTEGER},
      </if>
      <if test="extContent != null">
        ext_content = #{extContent,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.saas.SysCommonConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update sys_common_config
    set method_code = #{methodCode,jdbcType=VARCHAR},
      method_type = #{methodType,jdbcType=INTEGER},
      version = #{version,jdbcType=VARCHAR},
      code = #{code,jdbcType=INTEGER},
      type = #{type,jdbcType=INTEGER},
      msg = #{msg,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
      switch_type = #{switchType,jdbcType=INTEGER},
      ext_content = #{extContent,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>