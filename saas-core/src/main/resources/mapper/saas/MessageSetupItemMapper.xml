<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.MessageSetupItemMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.MessageSetupItem">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 01 16:35:35 CST 2017.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="busi_code" jdbcType="VARCHAR" property="busiCode" />
    <result column="item_code" jdbcType="VARCHAR" property="itemCode" />
    <result column="default_checked" jdbcType="INTEGER" property="defaultChecked" />
    <result column="default_int_val1" jdbcType="INTEGER" property="defaultIntVal1" />
    <result column="default_int_val2" jdbcType="INTEGER" property="defaultIntVal2" />
    <result column="default_int_val3" jdbcType="INTEGER" property="defaultIntVal3" />
    <result column="default_str_val1" jdbcType="VARCHAR" property="defaultStrVal1" />
    <result column="default_str_val2" jdbcType="VARCHAR" property="defaultStrVal2" />
    <result column="default_str_val3" jdbcType="VARCHAR" property="defaultStrVal3" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 01 16:35:35 CST 2017.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 01 16:35:35 CST 2017.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 01 16:35:35 CST 2017.
    -->
    id, busi_code, item_code, default_checked, default_int_val1, default_int_val2, default_int_val3, 
    default_str_val1, default_str_val2, default_str_val3, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saas.MessageSetupItemExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 01 16:35:35 CST 2017.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from message_setup_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saas.MessageSetupItemExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 01 16:35:35 CST 2017.
    -->
    delete from message_setup_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.saas.MessageSetupItem">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 01 16:35:35 CST 2017.
    -->
    insert into message_setup_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="busiCode != null">
        busi_code,
      </if>
      <if test="itemCode != null">
        item_code,
      </if>
      <if test="defaultChecked != null">
        default_checked,
      </if>
      <if test="defaultIntVal1 != null">
        default_int_val1,
      </if>
      <if test="defaultIntVal2 != null">
        default_int_val2,
      </if>
      <if test="defaultIntVal3 != null">
        default_int_val3,
      </if>
      <if test="defaultStrVal1 != null">
        default_str_val1,
      </if>
      <if test="defaultStrVal2 != null">
        default_str_val2,
      </if>
      <if test="defaultStrVal3 != null">
        default_str_val3,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="busiCode != null">
        #{busiCode,jdbcType=VARCHAR},
      </if>
      <if test="itemCode != null">
        #{itemCode,jdbcType=VARCHAR},
      </if>
      <if test="defaultChecked != null">
        #{defaultChecked,jdbcType=INTEGER},
      </if>
      <if test="defaultIntVal1 != null">
        #{defaultIntVal1,jdbcType=INTEGER},
      </if>
      <if test="defaultIntVal2 != null">
        #{defaultIntVal2,jdbcType=INTEGER},
      </if>
      <if test="defaultIntVal3 != null">
        #{defaultIntVal3,jdbcType=INTEGER},
      </if>
      <if test="defaultStrVal1 != null">
        #{defaultStrVal1,jdbcType=VARCHAR},
      </if>
      <if test="defaultStrVal2 != null">
        #{defaultStrVal2,jdbcType=VARCHAR},
      </if>
      <if test="defaultStrVal3 != null">
        #{defaultStrVal3,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saas.MessageSetupItemExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 01 16:35:35 CST 2017.
    -->
    select count(*) from message_setup_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 01 16:35:35 CST 2017.
    -->
    update message_setup_item
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.busiCode != null">
        busi_code = #{record.busiCode,jdbcType=VARCHAR},
      </if>
      <if test="record.itemCode != null">
        item_code = #{record.itemCode,jdbcType=VARCHAR},
      </if>
      <if test="record.defaultChecked != null">
        default_checked = #{record.defaultChecked,jdbcType=INTEGER},
      </if>
      <if test="record.defaultIntVal1 != null">
        default_int_val1 = #{record.defaultIntVal1,jdbcType=INTEGER},
      </if>
      <if test="record.defaultIntVal2 != null">
        default_int_val2 = #{record.defaultIntVal2,jdbcType=INTEGER},
      </if>
      <if test="record.defaultIntVal3 != null">
        default_int_val3 = #{record.defaultIntVal3,jdbcType=INTEGER},
      </if>
      <if test="record.defaultStrVal1 != null">
        default_str_val1 = #{record.defaultStrVal1,jdbcType=VARCHAR},
      </if>
      <if test="record.defaultStrVal2 != null">
        default_str_val2 = #{record.defaultStrVal2,jdbcType=VARCHAR},
      </if>
      <if test="record.defaultStrVal3 != null">
        default_str_val3 = #{record.defaultStrVal3,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 01 16:35:35 CST 2017.
    -->
    update message_setup_item
    set id = #{record.id,jdbcType=INTEGER},
      busi_code = #{record.busiCode,jdbcType=VARCHAR},
      item_code = #{record.itemCode,jdbcType=VARCHAR},
      default_checked = #{record.defaultChecked,jdbcType=INTEGER},
      default_int_val1 = #{record.defaultIntVal1,jdbcType=INTEGER},
      default_int_val2 = #{record.defaultIntVal2,jdbcType=INTEGER},
      default_int_val3 = #{record.defaultIntVal3,jdbcType=INTEGER},
      default_str_val1 = #{record.defaultStrVal1,jdbcType=VARCHAR},
      default_str_val2 = #{record.defaultStrVal2,jdbcType=VARCHAR},
      default_str_val3 = #{record.defaultStrVal3,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saas.MessageSetupItem">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 01 16:35:35 CST 2017.
    -->
    update message_setup_item
    <set>
      <if test="busiCode != null">
        busi_code = #{busiCode,jdbcType=VARCHAR},
      </if>
      <if test="itemCode != null">
        item_code = #{itemCode,jdbcType=VARCHAR},
      </if>
      <if test="defaultChecked != null">
        default_checked = #{defaultChecked,jdbcType=INTEGER},
      </if>
      <if test="defaultIntVal1 != null">
        default_int_val1 = #{defaultIntVal1,jdbcType=INTEGER},
      </if>
      <if test="defaultIntVal2 != null">
        default_int_val2 = #{defaultIntVal2,jdbcType=INTEGER},
      </if>
      <if test="defaultIntVal3 != null">
        default_int_val3 = #{defaultIntVal3,jdbcType=INTEGER},
      </if>
      <if test="defaultStrVal1 != null">
        default_str_val1 = #{defaultStrVal1,jdbcType=VARCHAR},
      </if>
      <if test="defaultStrVal2 != null">
        default_str_val2 = #{defaultStrVal2,jdbcType=VARCHAR},
      </if>
      <if test="defaultStrVal3 != null">
        default_str_val3 = #{defaultStrVal3,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

</mapper>