<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.MessageSetupEmailRelationExtMapper">

  <insert id="batchInsert" parameterType="java.util.List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into message_setup_email_relation
        (id,company_id,busi_code,employee_id,email,state,create_time)
        values
      <foreach collection="list" item="item" separator=",">
        (#{item.id}, #{item.companyId}, #{item.busiCode}, #{item.employeeId}, #{item.email}, #{item.state}, #{item.createTime})
      </foreach>
  </insert>

</mapper>