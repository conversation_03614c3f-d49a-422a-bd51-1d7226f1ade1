<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.ApplyCostAttributionMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.ApplyCostAttribution">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="apply_id" jdbcType="CHAR" property="applyId" />
    <result column="cost_attribution_id" jdbcType="VARCHAR" property="costAttributionId" />
    <result column="cost_attribution_name" jdbcType="VARCHAR" property="costAttributionName" />
    <result column="cost_attribution_category" jdbcType="INTEGER" property="costAttributionCategory" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="bring_in" jdbcType="INTEGER" property="bringIn" />
    <result column="record_id" jdbcType="VARCHAR" property="recordId" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, apply_id, cost_attribution_id, cost_attribution_name, cost_attribution_category,
    company_id, create_time, bring_in, record_id, category_name
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyCostAttributionExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from apply_cost_attribution
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${limit} offset ${offset}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from apply_cost_attribution
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from apply_cost_attribution
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyCostAttributionExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from apply_cost_attribution
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.fenbeitong.saas.core.model.saas.ApplyCostAttribution" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into apply_cost_attribution (apply_id, cost_attribution_id, cost_attribution_name,
    cost_attribution_category, company_id, create_time,
    bring_in, record_id, category_name
    )
    values (#{applyId,jdbcType=CHAR}, #{costAttributionId,jdbcType=VARCHAR}, #{costAttributionName,jdbcType=VARCHAR},
    #{costAttributionCategory,jdbcType=INTEGER}, #{companyId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
    #{bringIn,jdbcType=INTEGER}, #{recordId,jdbcType=VARCHAR}, #{categoryName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.fenbeitong.saas.core.model.saas.ApplyCostAttribution" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into apply_cost_attribution
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="applyId != null">
        apply_id,
      </if>
      <if test="costAttributionId != null">
        cost_attribution_id,
      </if>
      <if test="costAttributionName != null">
        cost_attribution_name,
      </if>
      <if test="costAttributionCategory != null">
        cost_attribution_category,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="bringIn != null">
        bring_in,
      </if>
      <if test="recordId != null">
        record_id,
      </if>
      <if test="categoryName != null">
        category_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="applyId != null">
        #{applyId,jdbcType=CHAR},
      </if>
      <if test="costAttributionId != null">
        #{costAttributionId,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionName != null">
        #{costAttributionName,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionCategory != null">
        #{costAttributionCategory,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bringIn != null">
        #{bringIn,jdbcType=INTEGER},
      </if>
      <if test="recordId != null">
        #{recordId,jdbcType=VARCHAR},
      </if>
      <if test="categoryName != null">
        #{categoryName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyCostAttributionExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from apply_cost_attribution
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_cost_attribution
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.applyId != null">
        apply_id = #{record.applyId,jdbcType=CHAR},
      </if>
      <if test="record.costAttributionId != null">
        cost_attribution_id = #{record.costAttributionId,jdbcType=VARCHAR},
      </if>
      <if test="record.costAttributionName != null">
        cost_attribution_name = #{record.costAttributionName,jdbcType=VARCHAR},
      </if>
      <if test="record.costAttributionCategory != null">
        cost_attribution_category = #{record.costAttributionCategory,jdbcType=INTEGER},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.bringIn != null">
        bring_in = #{record.bringIn,jdbcType=INTEGER},
      </if>
      <if test="record.recordId != null">
        record_id = #{record.recordId,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryName != null">
        category_name = #{record.categoryName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_cost_attribution
    set id = #{record.id,jdbcType=INTEGER},
      apply_id = #{record.applyId,jdbcType=CHAR},
      cost_attribution_id = #{record.costAttributionId,jdbcType=VARCHAR},
      cost_attribution_name = #{record.costAttributionName,jdbcType=VARCHAR},
      cost_attribution_category = #{record.costAttributionCategory,jdbcType=INTEGER},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      bring_in = #{record.bringIn,jdbcType=INTEGER},
      record_id = #{record.recordId,jdbcType=VARCHAR},
      category_name = #{record.categoryName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saas.ApplyCostAttribution">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_cost_attribution
    <set>
      <if test="applyId != null">
        apply_id = #{applyId,jdbcType=CHAR},
      </if>
      <if test="costAttributionId != null">
        cost_attribution_id = #{costAttributionId,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionName != null">
        cost_attribution_name = #{costAttributionName,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionCategory != null">
        cost_attribution_category = #{costAttributionCategory,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bringIn != null">
        bring_in = #{bringIn,jdbcType=INTEGER},
      </if>
      <if test="recordId != null">
        record_id = #{recordId,jdbcType=VARCHAR},
      </if>
      <if test="categoryName != null">
        category_name = #{categoryName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.saas.ApplyCostAttribution">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_cost_attribution
    set apply_id = #{applyId,jdbcType=CHAR},
      cost_attribution_id = #{costAttributionId,jdbcType=VARCHAR},
      cost_attribution_name = #{costAttributionName,jdbcType=VARCHAR},
      cost_attribution_category = #{costAttributionCategory,jdbcType=INTEGER},
      company_id = #{companyId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      bring_in = #{bringIn,jdbcType=INTEGER},
      record_id = #{recordId,jdbcType=VARCHAR},
      category_name = #{categoryName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>