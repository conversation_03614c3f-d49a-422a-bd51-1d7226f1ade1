<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.ApplyOrderLogMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.ApplyOrderLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="apply_order_id" jdbcType="CHAR" property="applyOrderId" />
    <result column="sponsor_id" jdbcType="CHAR" property="sponsorId" />
    <result column="action" jdbcType="INTEGER" property="action" />
    <result column="receiver_id" jdbcType="CHAR" property="receiverId" />
    <result column="ip" jdbcType="VARCHAR" property="ip" />
    <result column="check_reason" jdbcType="VARCHAR" property="checkReason" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="price" jdbcType="NUMERIC" property="price" />
    <result column="root_apply_order_id" jdbcType="CHAR" property="rootApplyOrderId" />
    <result column="sponsor_name" jdbcType="VARCHAR" property="sponsorName" />
    <result column="receiver_name" jdbcType="VARCHAR" property="receiverName" />
    <result column="delete_status" jdbcType="INTEGER" property="deleteStatus" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, apply_order_id, sponsor_id, action, receiver_id, ip, check_reason, create_time, 
    sort, price, root_apply_order_id, sponsor_name, receiver_name, delete_status
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrderLogExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from apply_order_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${limit} offset ${offset}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from apply_order_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from apply_order_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrderLogExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from apply_order_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrderLog" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into apply_order_log (apply_order_id, sponsor_id, action, 
      receiver_id, ip, check_reason, 
      create_time, sort, price, 
      root_apply_order_id, sponsor_name, receiver_name, 
      delete_status)
    values (#{applyOrderId,jdbcType=CHAR}, #{sponsorId,jdbcType=CHAR}, #{action,jdbcType=INTEGER}, 
      #{receiverId,jdbcType=CHAR}, #{ip,jdbcType=VARCHAR}, #{checkReason,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{sort,jdbcType=INTEGER}, #{price,jdbcType=NUMERIC}, 
      #{rootApplyOrderId,jdbcType=CHAR}, #{sponsorName,jdbcType=VARCHAR}, #{receiverName,jdbcType=VARCHAR}, 
      #{deleteStatus,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrderLog" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into apply_order_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="applyOrderId != null">
        apply_order_id,
      </if>
      <if test="sponsorId != null">
        sponsor_id,
      </if>
      <if test="action != null">
        action,
      </if>
      <if test="receiverId != null">
        receiver_id,
      </if>
      <if test="ip != null">
        ip,
      </if>
      <if test="checkReason != null">
        check_reason,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="rootApplyOrderId != null">
        root_apply_order_id,
      </if>
      <if test="sponsorName != null">
        sponsor_name,
      </if>
      <if test="receiverName != null">
        receiver_name,
      </if>
      <if test="deleteStatus != null">
        delete_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="applyOrderId != null">
        #{applyOrderId,jdbcType=CHAR},
      </if>
      <if test="sponsorId != null">
        #{sponsorId,jdbcType=CHAR},
      </if>
      <if test="action != null">
        #{action,jdbcType=INTEGER},
      </if>
      <if test="receiverId != null">
        #{receiverId,jdbcType=CHAR},
      </if>
      <if test="ip != null">
        #{ip,jdbcType=VARCHAR},
      </if>
      <if test="checkReason != null">
        #{checkReason,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        #{price,jdbcType=NUMERIC},
      </if>
      <if test="rootApplyOrderId != null">
        #{rootApplyOrderId,jdbcType=CHAR},
      </if>
      <if test="sponsorName != null">
        #{sponsorName,jdbcType=VARCHAR},
      </if>
      <if test="receiverName != null">
        #{receiverName,jdbcType=VARCHAR},
      </if>
      <if test="deleteStatus != null">
        #{deleteStatus,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrderLogExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from apply_order_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_order_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.applyOrderId != null">
        apply_order_id = #{record.applyOrderId,jdbcType=CHAR},
      </if>
      <if test="record.sponsorId != null">
        sponsor_id = #{record.sponsorId,jdbcType=CHAR},
      </if>
      <if test="record.action != null">
        action = #{record.action,jdbcType=INTEGER},
      </if>
      <if test="record.receiverId != null">
        receiver_id = #{record.receiverId,jdbcType=CHAR},
      </if>
      <if test="record.ip != null">
        ip = #{record.ip,jdbcType=VARCHAR},
      </if>
      <if test="record.checkReason != null">
        check_reason = #{record.checkReason,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=NUMERIC},
      </if>
      <if test="record.rootApplyOrderId != null">
        root_apply_order_id = #{record.rootApplyOrderId,jdbcType=CHAR},
      </if>
      <if test="record.sponsorName != null">
        sponsor_name = #{record.sponsorName,jdbcType=VARCHAR},
      </if>
      <if test="record.receiverName != null">
        receiver_name = #{record.receiverName,jdbcType=VARCHAR},
      </if>
      <if test="record.deleteStatus != null">
        delete_status = #{record.deleteStatus,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_order_log
    set id = #{record.id,jdbcType=INTEGER},
      apply_order_id = #{record.applyOrderId,jdbcType=CHAR},
      sponsor_id = #{record.sponsorId,jdbcType=CHAR},
      action = #{record.action,jdbcType=INTEGER},
      receiver_id = #{record.receiverId,jdbcType=CHAR},
      ip = #{record.ip,jdbcType=VARCHAR},
      check_reason = #{record.checkReason,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      sort = #{record.sort,jdbcType=INTEGER},
      price = #{record.price,jdbcType=NUMERIC},
      root_apply_order_id = #{record.rootApplyOrderId,jdbcType=CHAR},
      sponsor_name = #{record.sponsorName,jdbcType=VARCHAR},
      receiver_name = #{record.receiverName,jdbcType=VARCHAR},
      delete_status = #{record.deleteStatus,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrderLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_order_log
    <set>
      <if test="applyOrderId != null">
        apply_order_id = #{applyOrderId,jdbcType=CHAR},
      </if>
      <if test="sponsorId != null">
        sponsor_id = #{sponsorId,jdbcType=CHAR},
      </if>
      <if test="action != null">
        action = #{action,jdbcType=INTEGER},
      </if>
      <if test="receiverId != null">
        receiver_id = #{receiverId,jdbcType=CHAR},
      </if>
      <if test="ip != null">
        ip = #{ip,jdbcType=VARCHAR},
      </if>
      <if test="checkReason != null">
        check_reason = #{checkReason,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=NUMERIC},
      </if>
      <if test="rootApplyOrderId != null">
        root_apply_order_id = #{rootApplyOrderId,jdbcType=CHAR},
      </if>
      <if test="sponsorName != null">
        sponsor_name = #{sponsorName,jdbcType=VARCHAR},
      </if>
      <if test="receiverName != null">
        receiver_name = #{receiverName,jdbcType=VARCHAR},
      </if>
      <if test="deleteStatus != null">
        delete_status = #{deleteStatus,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrderLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_order_log
    set apply_order_id = #{applyOrderId,jdbcType=CHAR},
      sponsor_id = #{sponsorId,jdbcType=CHAR},
      action = #{action,jdbcType=INTEGER},
      receiver_id = #{receiverId,jdbcType=CHAR},
      ip = #{ip,jdbcType=VARCHAR},
      check_reason = #{checkReason,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      sort = #{sort,jdbcType=INTEGER},
      price = #{price,jdbcType=NUMERIC},
      root_apply_order_id = #{rootApplyOrderId,jdbcType=CHAR},
      sponsor_name = #{sponsorName,jdbcType=VARCHAR},
      receiver_name = #{receiverName,jdbcType=VARCHAR},
      delete_status = #{deleteStatus,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>