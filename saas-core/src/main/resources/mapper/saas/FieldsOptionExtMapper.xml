<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.FieldsOptionExtMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.FieldsOption"
             extends="com.fenbeitong.saas.core.dao.saas.FieldsOptionMapper.BaseResultMap">
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, message_setup_id, content, content_en, create_time, taxi_message_setup_id
  </sql>

  <insert id="batchInsertFieldsOption">
    INSERT INTO fields_option(company_id, message_setup_id, content, create_time)
    VALUES
    <foreach collection="list" item="item" separator=",">
      (#{item.companyId}, #{item.messageSetupId}, #{item.content}, now())
    </foreach>
  </insert>

  <select id="queryFieldOptionNum" resultType="java.lang.Integer">
    select count(1)
    from fields_option
    where company_id = #{companyId,jdbcType=CHAR}
    and message_setup_id = #{messageSetupId,jdbcType=INTEGER}
    <if test="option != null">
      and content like #{option,jdbcType=CHAR}
    </if>
  </select>

  <select id="queryFieldOptionList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from fields_option
    where company_id = #{companyId,jdbcType=CHAR}
    and message_setup_id = #{messageSetupId,jdbcType=INTEGER}
    <if test="option != null">
      and content like #{option,jdbcType=CHAR}
    </if>
    order by id asc
    limit #{pageSize,jdbcType=INTEGER} offset #{pageStart,jdbcType=INTEGER}
  </select>
  <select id="queryTaxiFieldOptionNum" resultType="java.lang.Integer">
    select count(1)
    from fields_option
    where company_id = #{companyId,jdbcType=CHAR}
    and taxi_message_setup_id = #{taxiMessageSetupId,jdbcType=CHAR}
    <if test="option != null">
      and content like #{option,jdbcType=CHAR}
    </if>
  </select>
  <select id="queryTaxiFieldOptionList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from fields_option
    where company_id = #{companyId,jdbcType=CHAR}
    and taxi_message_setup_id = #{taxiMessageSetupId,jdbcType=CHAR}
    <if test="option != null">
      and content like #{option,jdbcType=CHAR}
    </if>
    order by id asc
    limit #{pageSize,jdbcType=INTEGER} offset #{pageStart,jdbcType=INTEGER}
  </select>
</mapper>