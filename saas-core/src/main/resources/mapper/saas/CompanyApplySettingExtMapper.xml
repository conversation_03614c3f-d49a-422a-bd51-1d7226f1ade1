<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.CompanyApplySettingExtMapper">
    <resultMap id="BaseResultMapExt" type="com.fenbeitong.saas.core.model.saas.CompanyApplySetting"
               extends="com.fenbeitong.saas.core.dao.saas.CompanyApplySettingMapper.BaseResultMap">
    </resultMap>

    <resultMap id="companyApplyExt" type="com.fenbeitong.saas.core.model.saas.CompanyApplyExt">
        <result column="id" jdbcType="CHAR" property="id" />
        <result column="apply_type" jdbcType="INTEGER" property="applyType" />
        <result column="company_setting_type" jdbcType="INTEGER" property="companySettingType" />
        <result column="item_id" jdbcType="VARCHAR" property="itemId"/>
    </resultMap>

    <!--自定义-->
    <delete id="deleteByPrimaryKey">
        DELETE FROM company_apply_setting
        WHERE id = #{companyApplySettingId,jdbcType=VARCHAR}
    </delete>

    <select id="selectByCompanyIdAndApplyType" resultMap="BaseResultMapExt">
        SELECT
        id,
        company_id,
        apply_type,
        create_time,
        update_time,
        company_apply_type,
        apply_flow_id,
        operator_id,
        flow_name
        FROM company_apply_setting
        WHERE company_id = #{companyId,jdbcType=CHAR}
        AND apply_type = #{applyType,jdbcType=INTEGER}
    </select>

    <select id="selectByCompanyId" resultMap="BaseResultMapExt">
        SELECT
        id,
        company_id,
        apply_type,
        create_time,
        update_time,
        company_apply_type,
        apply_flow_id,
        operator_id,
        flow_name
        FROM company_apply_setting
        WHERE company_id = #{companyId,jdbcType=CHAR}
    </select>

    <select id="getCompanyApplySettingListByApplyType" resultMap="BaseResultMapExt">
        select *
        from company_apply_setting where apply_type = #{applyType,jdbcType=INTEGER}
        and company_id = #{companyId,jdbcType=VARCHAR}
        and apply_status= #{applyStatus,jdbcType=INTEGER}
        and company_setting_type = #{companySettingType,jdbcType=INTEGER}
        order by can_delete desc,cost_attribution_category asc,update_time desc
        limit #{pageSize,jdbcType=INTEGER} offset #{pageStart,jdbcType=INTEGER}
    </select>

    <select id="getCountByApplyType" resultType="java.lang.Integer">
        select count(*)
        from company_apply_setting where apply_type = #{applyType,jdbcType=INTEGER}
        and company_id = #{companyId,jdbcType=VARCHAR}
        and apply_status = #{applyStatus,jdbcType=INTEGER}
        and company_setting_type = #{companySettingType,jdbcType=INTEGER}
    </select>

    <select id="queryCompanyApplySettingByItemId" resultMap="BaseResultMapExt">
        select a.*
        from company_apply_setting a,company_apply_setting_use b where a.id = b.company_apply_setting_id
        and b.item_id = #{itemId,jdbcType=VARCHAR} and a.apply_type = #{applyType,jdbcType=INTEGER}
        and a.apply_status = 1 and b.setting_type = #{settingType,jdbcType=INTEGER}
        and a.company_setting_type = #{companySettingType,jdbcType=INTEGER}
    </select>

    <select id="queryApplySettingListByApplyType" resultMap="BaseResultMapExt">
        select
        *
        from company_apply_setting where apply_type = #{applyType,jdbcType=INTEGER}
        and company_id = #{companyId,jdbcType=VARCHAR}
        and apply_status= #{applyStatus,jdbcType=INTEGER}
        order by update_time desc
    </select>

    <select id="queryCompanySettingByFlowId" resultMap="BaseResultMapExt">
        select a.*
        from company_apply_setting a,apply_flow b where a.apply_flow_id = b.id
        and b.id= #{flowId,jdbcType=VARCHAR}
    </select>

    <select id="queryDefaultFlow" resultMap="BaseResultMapExt">
        select *
        from company_apply_setting where company_id = #{companyId,jdbcType=VARCHAR}
        and apply_status = 1
        and can_delete = #{canDelete,jdbcType=INTEGER}
        and apply_type = #{applyType,jdbcType=INTEGER}
        and company_setting_type = #{companySettingType,jdbcType=INTEGER}
        and cost_attribution_category = #{costAttributionCategory,jdbcType=INTEGER}
        order by update_time desc
    </select>


    <select id="queryCompanySettingByUserId" resultMap="BaseResultMapExt">
        select distinct cas.*
        from company_apply_setting cas
        left join apply_flow af on cas.id = af.company_apply_setting_id
        left join apply_flow_item afi on af.id = afi.apply_flow_id
        where afi.item_type = 2
        and afi.item_id = #{userId,jdbcType=VARCHAR}
        and af.company_id = #{companyId,jdbcType=VARCHAR}
        and cas.apply_status = 1
    </select>

    <select id="queryApplyFlowItemByCompanyApplySettingId" resultType="java.lang.Integer">
        select count(*)
        from apply_flow_item afi
        left join apply_flow af
        on afi.apply_flow_id = af.id
        left join company_apply_setting cas
        on cas.id = af.company_apply_setting_id
        where af.company_id = #{companyId,jdbcType=VARCHAR}
        and cas.id = #{companyApplySettingId,jdbcType=VARCHAR}
    </select>

    <select id="queryAbnormalApproval" resultMap="companyApplyExt">
        SELECT
	    t.id,t.apply_type,t.company_setting_type,afi.item_id
        FROM
            company_apply_setting t,
            apply_flow af,
            apply_flow_item afi
        WHERE
            t .id = af .company_apply_setting_id
        AND af .id = afi.apply_flow_id
        and t.company_id = #{companyId,jdbcType=VARCHAR}
        and afi.item_type=2
    </select>
</mapper>