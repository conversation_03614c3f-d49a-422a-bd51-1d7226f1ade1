<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.ApplyFlowCopyToMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.ApplyFlowCopyTo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="company_apply_setting_id" jdbcType="VARCHAR" property="companyApplySettingId" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="item_type" jdbcType="INTEGER" property="itemType" />
    <result column="item_id" jdbcType="VARCHAR" property="itemId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="apply_flow_id" jdbcType="CHAR" property="applyFlowId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_apply_setting_id, sort, item_type, item_id, create_time, apply_flow_id
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyFlowCopyToExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from apply_flow_copy_to
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from apply_flow_copy_to
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from apply_flow_copy_to
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyFlowCopyToExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from apply_flow_copy_to
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.saas.ApplyFlowCopyTo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into apply_flow_copy_to (id, company_apply_setting_id, sort, 
      item_type, item_id, create_time, 
      apply_flow_id)
    values (#{id,jdbcType=CHAR}, #{companyApplySettingId,jdbcType=VARCHAR}, #{sort,jdbcType=INTEGER}, 
      #{itemType,jdbcType=INTEGER}, #{itemId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{applyFlowId,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.saas.ApplyFlowCopyTo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into apply_flow_copy_to
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="companyApplySettingId != null">
        company_apply_setting_id,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="itemType != null">
        item_type,
      </if>
      <if test="itemId != null">
        item_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="applyFlowId != null">
        apply_flow_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="companyApplySettingId != null">
        #{companyApplySettingId,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="itemType != null">
        #{itemType,jdbcType=INTEGER},
      </if>
      <if test="itemId != null">
        #{itemId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="applyFlowId != null">
        #{applyFlowId,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyFlowCopyToExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from apply_flow_copy_to
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_flow_copy_to
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.companyApplySettingId != null">
        company_apply_setting_id = #{record.companyApplySettingId,jdbcType=VARCHAR},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.itemType != null">
        item_type = #{record.itemType,jdbcType=INTEGER},
      </if>
      <if test="record.itemId != null">
        item_id = #{record.itemId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.applyFlowId != null">
        apply_flow_id = #{record.applyFlowId,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_flow_copy_to
    set id = #{record.id,jdbcType=CHAR},
      company_apply_setting_id = #{record.companyApplySettingId,jdbcType=VARCHAR},
      sort = #{record.sort,jdbcType=INTEGER},
      item_type = #{record.itemType,jdbcType=INTEGER},
      item_id = #{record.itemId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      apply_flow_id = #{record.applyFlowId,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saas.ApplyFlowCopyTo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_flow_copy_to
    <set>
      <if test="companyApplySettingId != null">
        company_apply_setting_id = #{companyApplySettingId,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="itemType != null">
        item_type = #{itemType,jdbcType=INTEGER},
      </if>
      <if test="itemId != null">
        item_id = #{itemId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="applyFlowId != null">
        apply_flow_id = #{applyFlowId,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.saas.ApplyFlowCopyTo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_flow_copy_to
    set company_apply_setting_id = #{companyApplySettingId,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      item_type = #{itemType,jdbcType=INTEGER},
      item_id = #{itemId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      apply_flow_id = #{applyFlowId,jdbcType=CHAR}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>