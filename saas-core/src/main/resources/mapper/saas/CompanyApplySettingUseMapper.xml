<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.CompanyApplySettingUseMapper">
    <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.CompanyApplySettingUse">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="setting_type" jdbcType="INTEGER" property="settingType" />
        <result column="item_id" jdbcType="VARCHAR" property="itemId" />
        <result column="company_id" jdbcType="VARCHAR" property="companyId" />
        <result column="company_apply_setting_id" jdbcType="VARCHAR" property="companyApplySettingId" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>
    <sql id="Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        id, setting_type, item_id, company_id, company_apply_setting_id, create_user, create_time,
        update_user, update_time
    </sql>
    <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saas.CompanyApplySettingUseExample" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List" />
        from company_apply_setting_use
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset == null">
                limit ${limit}
            </if>
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <include refid="Base_Column_List" />
        from company_apply_setting_use
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from company_apply_setting_use
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saas.CompanyApplySettingUseExample">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from company_apply_setting_use
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
    </delete>
    <insert id="insert" parameterType="com.fenbeitong.saas.core.model.saas.CompanyApplySettingUse">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into company_apply_setting_use (id, setting_type, item_id,
        company_id, company_apply_setting_id, create_user,
        create_time, update_user, update_time
        )
        values (#{id,jdbcType=VARCHAR}, #{settingType,jdbcType=INTEGER}, #{itemId,jdbcType=VARCHAR},
        #{companyId,jdbcType=VARCHAR}, #{companyApplySettingId,jdbcType=VARCHAR}, #{createUser,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.saas.CompanyApplySettingUse">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into company_apply_setting_use
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="settingType != null">
                setting_type,
            </if>
            <if test="itemId != null">
                item_id,
            </if>
            <if test="companyId != null">
                company_id,
            </if>
            <if test="companyApplySettingId != null">
                company_apply_setting_id,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="settingType != null">
                #{settingType,jdbcType=INTEGER},
            </if>
            <if test="itemId != null">
                #{itemId,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                #{companyId,jdbcType=VARCHAR},
            </if>
            <if test="companyApplySettingId != null">
                #{companyApplySettingId,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saas.CompanyApplySettingUseExample" resultType="java.lang.Long">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select count(*) from company_apply_setting_use
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update company_apply_setting_use
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=VARCHAR},
            </if>
            <if test="record.settingType != null">
                setting_type = #{record.settingType,jdbcType=INTEGER},
            </if>
            <if test="record.itemId != null">
                item_id = #{record.itemId,jdbcType=VARCHAR},
            </if>
            <if test="record.companyId != null">
                company_id = #{record.companyId,jdbcType=VARCHAR},
            </if>
            <if test="record.companyApplySettingId != null">
                company_apply_setting_id = #{record.companyApplySettingId,jdbcType=VARCHAR},
            </if>
            <if test="record.createUser != null">
                create_user = #{record.createUser,jdbcType=VARCHAR},
            </if>
            <if test="record.createTime != null">
                create_time = #{record.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updateUser != null">
                update_user = #{record.updateUser,jdbcType=VARCHAR},
            </if>
            <if test="record.updateTime != null">
                update_time = #{record.updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause" />
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update company_apply_setting_use
        set id = #{record.id,jdbcType=VARCHAR},
        setting_type = #{record.settingType,jdbcType=INTEGER},
        item_id = #{record.itemId,jdbcType=VARCHAR},
        company_id = #{record.companyId,jdbcType=VARCHAR},
        company_apply_setting_id = #{record.companyApplySettingId,jdbcType=VARCHAR},
        create_user = #{record.createUser,jdbcType=VARCHAR},
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
        update_user = #{record.updateUser,jdbcType=VARCHAR},
        update_time = #{record.updateTime,jdbcType=TIMESTAMP}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause" />
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saas.CompanyApplySettingUse">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update company_apply_setting_use
        <set>
            <if test="settingType != null">
                setting_type = #{settingType,jdbcType=INTEGER},
            </if>
            <if test="itemId != null">
                item_id = #{itemId,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=VARCHAR},
            </if>
            <if test="companyApplySettingId != null">
                company_apply_setting_id = #{companyApplySettingId,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.saas.CompanyApplySettingUse">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update company_apply_setting_use
        set setting_type = #{settingType,jdbcType=INTEGER},
        item_id = #{itemId,jdbcType=VARCHAR},
        company_id = #{companyId,jdbcType=VARCHAR},
        company_apply_setting_id = #{companyApplySettingId,jdbcType=VARCHAR},
        create_user = #{createUser,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_user = #{updateUser,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
</mapper>