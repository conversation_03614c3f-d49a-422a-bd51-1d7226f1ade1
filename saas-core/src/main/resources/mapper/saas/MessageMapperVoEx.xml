<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.MessageMapperVoEx">
    <resultMap id="BaseResultMapEx" type="com.fenbeitong.saas.core.contract.message.MessageListContract" extends="com.fenbeitong.saas.core.dao.saas.MessageMapper.BaseResultMap">
    </resultMap>
    <sql id="Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Thu Apr 13 10:08:31 CST 2017.
        -->
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Thu Apr 13 10:08:31 CST 2017.
        -->
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Thu Apr 13 10:08:31 CST 2017.
        -->
        id, msg_type, biz_type, title, comment, biz_order, info, link, sender, sender_type,
        receiver, read, create_time, update_time, company_id
    </sql>

    <select id="selectMsgList" parameterType="com.fenbeitong.saas.core.model.saas.MessageExample" resultMap="BaseResultMapEx">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List" />
        from message
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
        order by create_time desc
        limit #{pageSize} offset #{offset}
    </select>

    <select id="countMessage" parameterType="com.fenbeitong.saas.core.model.saas.MessageExample" resultType="java.lang.Long">
        select count(*) from message
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
    </select>

</mapper>