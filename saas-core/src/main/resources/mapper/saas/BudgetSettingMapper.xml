<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.BudgetSettingMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.BudgetSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="budget_type" jdbcType="INTEGER" property="budgetType" />
    <result column="budget_name" jdbcType="VARCHAR" property="budgetName" />
    <result column="travel_limit" jdbcType="NUMERIC" property="travelLimit" />
    <result column="car_limit" jdbcType="NUMERIC" property="carLimit" />
    <result column="mall_limit" jdbcType="NUMERIC" property="mallLimit" />
    <result column="warn_percent1" jdbcType="NUMERIC" property="warnPercent1" />
    <result column="warn_percent2" jdbcType="NUMERIC" property="warnPercent2" />
    <result column="over_limit_control" jdbcType="INTEGER" property="overLimitControl" />
    <result column="budget_status" jdbcType="INTEGER" property="budgetStatus" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="dinner_limit" jdbcType="NUMERIC" property="dinnerLimit" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, budget_type, budget_name, travel_limit, car_limit, mall_limit, warn_percent1,
    warn_percent2, over_limit_control, budget_status, create_user, create_time, update_user,
    update_time, dinner_limit
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saas.BudgetSettingExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from budget_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from budget_setting
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from budget_setting
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saas.BudgetSettingExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from budget_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.fenbeitong.saas.core.model.saas.BudgetSetting" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into budget_setting (company_id, budget_type, budget_name,
    travel_limit, car_limit, mall_limit,
    warn_percent1, warn_percent2, over_limit_control,
    budget_status, create_user, create_time,
    update_user, update_time, dinner_limit
    )
    values (#{companyId,jdbcType=VARCHAR}, #{budgetType,jdbcType=INTEGER}, #{budgetName,jdbcType=VARCHAR},
    #{travelLimit,jdbcType=NUMERIC}, #{carLimit,jdbcType=NUMERIC}, #{mallLimit,jdbcType=NUMERIC},
    #{warnPercent1,jdbcType=NUMERIC}, #{warnPercent2,jdbcType=NUMERIC}, #{overLimitControl,jdbcType=INTEGER},
    #{budgetStatus,jdbcType=INTEGER}, #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
    #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{dinnerLimit,jdbcType=NUMERIC}
    )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.fenbeitong.saas.core.model.saas.BudgetSetting" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into budget_setting
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="budgetType != null">
        budget_type,
      </if>
      <if test="budgetName != null">
        budget_name,
      </if>
      <if test="travelLimit != null">
        travel_limit,
      </if>
      <if test="carLimit != null">
        car_limit,
      </if>
      <if test="mallLimit != null">
        mall_limit,
      </if>
      <if test="warnPercent1 != null">
        warn_percent1,
      </if>
      <if test="warnPercent2 != null">
        warn_percent2,
      </if>
      <if test="overLimitControl != null">
        over_limit_control,
      </if>
      <if test="budgetStatus != null">
        budget_status,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="dinnerLimit != null">
        dinner_limit,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="budgetType != null">
        #{budgetType,jdbcType=INTEGER},
      </if>
      <if test="budgetName != null">
        #{budgetName,jdbcType=VARCHAR},
      </if>
      <if test="travelLimit != null">
        #{travelLimit,jdbcType=NUMERIC},
      </if>
      <if test="carLimit != null">
        #{carLimit,jdbcType=NUMERIC},
      </if>
      <if test="mallLimit != null">
        #{mallLimit,jdbcType=NUMERIC},
      </if>
      <if test="warnPercent1 != null">
        #{warnPercent1,jdbcType=NUMERIC},
      </if>
      <if test="warnPercent2 != null">
        #{warnPercent2,jdbcType=NUMERIC},
      </if>
      <if test="overLimitControl != null">
        #{overLimitControl,jdbcType=INTEGER},
      </if>
      <if test="budgetStatus != null">
        #{budgetStatus,jdbcType=INTEGER},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dinnerLimit != null">
        #{dinnerLimit,jdbcType=NUMERIC},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saas.BudgetSettingExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from budget_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update budget_setting
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.budgetType != null">
        budget_type = #{record.budgetType,jdbcType=INTEGER},
      </if>
      <if test="record.budgetName != null">
        budget_name = #{record.budgetName,jdbcType=VARCHAR},
      </if>
      <if test="record.travelLimit != null">
        travel_limit = #{record.travelLimit,jdbcType=NUMERIC},
      </if>
      <if test="record.carLimit != null">
        car_limit = #{record.carLimit,jdbcType=NUMERIC},
      </if>
      <if test="record.mallLimit != null">
        mall_limit = #{record.mallLimit,jdbcType=NUMERIC},
      </if>
      <if test="record.warnPercent1 != null">
        warn_percent1 = #{record.warnPercent1,jdbcType=NUMERIC},
      </if>
      <if test="record.warnPercent2 != null">
        warn_percent2 = #{record.warnPercent2,jdbcType=NUMERIC},
      </if>
      <if test="record.overLimitControl != null">
        over_limit_control = #{record.overLimitControl,jdbcType=INTEGER},
      </if>
      <if test="record.budgetStatus != null">
        budget_status = #{record.budgetStatus,jdbcType=INTEGER},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dinnerLimit != null">
        dinner_limit = #{record.dinnerLimit,jdbcType=NUMERIC},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update budget_setting
    set id = #{record.id,jdbcType=INTEGER},
    company_id = #{record.companyId,jdbcType=VARCHAR},
    budget_type = #{record.budgetType,jdbcType=INTEGER},
    budget_name = #{record.budgetName,jdbcType=VARCHAR},
    travel_limit = #{record.travelLimit,jdbcType=NUMERIC},
    car_limit = #{record.carLimit,jdbcType=NUMERIC},
    mall_limit = #{record.mallLimit,jdbcType=NUMERIC},
    warn_percent1 = #{record.warnPercent1,jdbcType=NUMERIC},
    warn_percent2 = #{record.warnPercent2,jdbcType=NUMERIC},
    over_limit_control = #{record.overLimitControl,jdbcType=INTEGER},
    budget_status = #{record.budgetStatus,jdbcType=INTEGER},
    create_user = #{record.createUser,jdbcType=VARCHAR},
    create_time = #{record.createTime,jdbcType=TIMESTAMP},
    update_user = #{record.updateUser,jdbcType=VARCHAR},
    update_time = #{record.updateTime,jdbcType=TIMESTAMP},
    dinner_limit = #{record.dinnerLimit,jdbcType=NUMERIC}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saas.BudgetSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update budget_setting
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="budgetType != null">
        budget_type = #{budgetType,jdbcType=INTEGER},
      </if>
      <if test="budgetName != null">
        budget_name = #{budgetName,jdbcType=VARCHAR},
      </if>
      <if test="travelLimit != null">
        travel_limit = #{travelLimit,jdbcType=NUMERIC},
      </if>
      <if test="carLimit != null">
        car_limit = #{carLimit,jdbcType=NUMERIC},
      </if>
      <if test="mallLimit != null">
        mall_limit = #{mallLimit,jdbcType=NUMERIC},
      </if>
      <if test="warnPercent1 != null">
        warn_percent1 = #{warnPercent1,jdbcType=NUMERIC},
      </if>
      <if test="warnPercent2 != null">
        warn_percent2 = #{warnPercent2,jdbcType=NUMERIC},
      </if>
      <if test="overLimitControl != null">
        over_limit_control = #{overLimitControl,jdbcType=INTEGER},
      </if>
      <if test="budgetStatus != null">
        budget_status = #{budgetStatus,jdbcType=INTEGER},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dinnerLimit != null">
        dinner_limit = #{dinnerLimit,jdbcType=NUMERIC},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.saas.BudgetSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update budget_setting
    set company_id = #{companyId,jdbcType=VARCHAR},
    budget_type = #{budgetType,jdbcType=INTEGER},
    budget_name = #{budgetName,jdbcType=VARCHAR},
    travel_limit = #{travelLimit,jdbcType=NUMERIC},
    car_limit = #{carLimit,jdbcType=NUMERIC},
    mall_limit = #{mallLimit,jdbcType=NUMERIC},
    warn_percent1 = #{warnPercent1,jdbcType=NUMERIC},
    warn_percent2 = #{warnPercent2,jdbcType=NUMERIC},
    over_limit_control = #{overLimitControl,jdbcType=INTEGER},
    budget_status = #{budgetStatus,jdbcType=INTEGER},
    create_user = #{createUser,jdbcType=VARCHAR},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    update_user = #{updateUser,jdbcType=VARCHAR},
    update_time = #{updateTime,jdbcType=TIMESTAMP},
    dinner_limit = #{dinnerLimit,jdbcType=NUMERIC}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="getBudgetSettingListByBudgetType" resultMap="BaseResultMap">
    select
    *
    from budget_setting where budget_type = #{budgetType,jdbcType=INTEGER}
    and company_id = #{companyId,jdbcType=VARCHAR}
    and budget_status= #{budgetStatus,jdbcType=INTEGER}
    order by update_time desc
    limit #{pageSize,jdbcType=INTEGER} offset #{pageStart,jdbcType=INTEGER}
  </select>

  <select id="getCountByBudgetType" resultType="java.lang.Integer">
    select count(*)
    from budget_setting where budget_type = #{budgetType,jdbcType=INTEGER}
    and company_id = #{companyId,jdbcType=VARCHAR}
    and budget_status = #{budgetStatus,jdbcType=INTEGER}
  </select>

  <select id="getBudgetSettingDetail" resultMap="BaseResultMap">
    select b.*
    from budget_setting b,budget_setting_use u
    where b.id=u.budget_id
    and u.item_id = #{itemId,jdbcType=VARCHAR}
    and b.budget_status=#{budgetStatus,jdbcType=INTEGER}
    and b.budget_type = #{budgetType,jdbcType=INTEGER}
    and u.budget_type = #{budgetType,jdbcType=INTEGER}
    and b.company_id = #{companyId,jdbcType=VARCHAR}
  </select>

  <update id="updateBudgetSettingById" parameterType="com.fenbeitong.saas.core.model.saas.BudgetSetting">
    update budget_setting
    <set>
      budget_status = #{budgetStatus,jdbcType=INTEGER},
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="queryBudgetStatistics" resultMap="BaseResultMap">
    select b.*
    from budget_setting b,budget_setting_use u
    where b.id = u.budget_id
    and u.item_id = #{itemId,jdbcType=VARCHAR}
    and b.company_id = #{company_id,jdbcType=VARCHAR}
  </select>

</mapper>