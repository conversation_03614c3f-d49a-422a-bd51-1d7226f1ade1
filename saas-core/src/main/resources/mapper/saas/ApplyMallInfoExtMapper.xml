<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.ApplyMallInfoExtMapper">
    <resultMap id="BaseResultMapExt" type="com.fenbeitong.saas.core.model.saas.ApplyMallInfo">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="apply_order_id" jdbcType="VARCHAR" property="applyOrderId"/>
        <result column="sku_id" jdbcType="VARCHAR" property="skuId"/>
        <result column="sku_name" jdbcType="VARCHAR" property="skuName"/>
        <result column="sku_pic_url" jdbcType="VARCHAR" property="skuPicUrl"/>
        <result column="price" jdbcType="NUMERIC" property="price"/>
        <result column="amount" jdbcType="INTEGER" property="amount"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="queryMallListByApplyId" resultMap="BaseResultMapExt">
       select *
       from apply_mall_info
       where apply_order_id = #{applyOrderId,jdbcType=VARCHAR}
    </select>
</mapper>