<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.CompanyApplySettingUseExtMapper">
    <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.CompanyApplySettingUse" extends="com.fenbeitong.saas.core.dao.saas.CompanyApplySettingUseMapper.BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Tue Jul 18 18:07:06 CST 2017.
        -->
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="setting_type" jdbcType="INTEGER" property="settingType"/>
        <result column="item_id" jdbcType="VARCHAR" property="itemId"/>
        <result column="company_id" jdbcType="VARCHAR" property="companyId"/>
        <result column="company_apply_setting_id" jdbcType="VARCHAR" property="companyApplySettingId"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="getCountByCompanyApplySettingId" resultType="java.lang.Integer">
    select count(*)
    from company_apply_setting_use
    where company_apply_setting_id = #{companyApplySettingId,jdbcType=CHAR}
    and setting_type = #{settingType,jdbcType=INTEGER}
  </select>

    <select id="querySettingUseByCompanyApplySettingId" resultMap="BaseResultMap">
    select *
    from company_apply_setting_use
    where company_apply_setting_id = #{companyApplySettingId,jdbcType=CHAR}
    and setting_type = #{settingType,jdbcType=INTEGER}
  </select>

    <select id="querySettingUseBySettingType" resultMap="BaseResultMap">
    select *
    from company_apply_setting_use a,company_apply_setting b
    where a.company_apply_setting_id = b.id
    and a.setting_type = #{settingType,jdbcType=INTEGER}
    and a.item_id = #{itemId,jdbcType=VARCHAR}
    and b.apply_type = #{applyType,jdbcType=INTEGER}
    and b.company_setting_type = #{companySettingType,jdbcType=INTEGER}
    and b.company_id = #{companyId,jdbcType=VARCHAR}
  </select>

    <delete id="deleteByCompanyApplySettingIdAndSettingType">
    delete from company_apply_setting_use
    where company_apply_setting_id = #{companyApplySettingId,jdbcType=VARCHAR} and setting_type = #{settingType,jdbcType=INTEGER}
</delete>

    <delete id="deleteByCompanyApplySettingId">
        delete from company_apply_setting_use
        where company_apply_setting_id = #{companyApplySettingId,jdbcType=VARCHAR}
    </delete>

    <select id="getCountByCompanyApplySettingIds" resultType="com.fenbeitong.saas.core.contract.applyflow.CountStatContract">
        select company_apply_setting_id id, count(*) count
        from company_apply_setting_use
        where company_apply_setting_id in
        <foreach collection="companyApplySettingIds" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and setting_type = #{settingType,jdbcType=INTEGER}
        group by company_apply_setting_id
    </select>

    <select id="querySettingUseByCompanyApplySettingIds" resultMap="BaseResultMap">
        select *
        from company_apply_setting_use
        where company_apply_setting_id in
        <foreach collection="companyApplySettingIds" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and setting_type = #{settingType,jdbcType=INTEGER}
    </select>
    <insert id="batchInsert" parameterType="java.util.List">
        insert into company_apply_setting_use (id, setting_type, item_id,
        company_id, company_apply_setting_id, create_user,
        create_time, update_user, update_time
        )
        values
        <foreach collection="companyApplySettingUseList" item="item" index="index" separator=",">
            (#{item.id,jdbcType=VARCHAR}, #{item.settingType,jdbcType=INTEGER}, #{item.itemId,jdbcType=VARCHAR},
            #{item.companyId,jdbcType=VARCHAR}, #{item.companyApplySettingId,jdbcType=VARCHAR}, #{item.createUser,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateUser,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>
</mapper>