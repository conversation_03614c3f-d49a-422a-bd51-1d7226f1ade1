<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.ApplyFlowExtMapper">
    <!-- 补充非弹性审批流字段值 wuchao 20180608-->
    <update id="updateNonElasticFlowFields">
        UPDATE apply_flow a
        SET company_apply_setting_id = t.id,
            company_apply_type = t.company_apply_type,
            cc_notice_type = t.cc_notice_type,
            is_exceed_buy_flow = (case t.company_setting_type when 2 then 1 else 0 end)
        FROM (SELECT *
                FROM company_apply_setting
                WHERE company_apply_type != 1) t
        WHERE a.id = t.apply_flow_id
        AND a.company_apply_setting_id is null
    </update>

    <!-- 查询原弹性审批流数据 wuchao 20180608-->
    <select id="queryElasticFlowData" resultMap="com.fenbeitong.saas.core.dao.saas.ApplyFlowMapper.BaseResultMap">
        SELECT company_id, operator_id, id company_apply_setting_id, company_apply_type, cc_notice_type, 0 is_exceed_buy_flow
        FROM company_apply_setting a
        WHERE company_apply_type = 1
        AND NOT exists(
            SELECT 1
            FROM apply_flow b
            WHERE a.id = b.company_apply_setting_id
        )
    </select>

    <!-- 批量插入弹性审批流数据 wuchao 20180608-->
    <insert id="batchInsert" parameterType="list">
        insert into apply_flow (id, company_id, create_time,
            operator_id, company_apply_setting_id, company_apply_type,
            cc_notice_type, is_exceed_buy_flow)
        values
            <foreach collection="list" item="item" open="" close="" separator=",">
                (#{item.id,jdbcType=CHAR}, #{item.companyId,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
                #{item.operatorId,jdbcType=VARCHAR}, #{item.companyApplySettingId,jdbcType=VARCHAR}, #{item.companyApplyType,jdbcType=INTEGER},
                #{item.ccNoticeType,jdbcType=INTEGER}, #{item.isExceedBuyFlow,jdbcType=INTEGER})
            </foreach>
    </insert>

    <!-- 查询差旅订单审批流数据 wuchao 20180608-->
    <select id="queryChailvOrderFlowData" resultMap="com.fenbeitong.saas.core.dao.saas.ApplyFlowMapper.BaseResultMap">
        SELECT company_id, operator_id, id company_apply_setting_id, 1 company_apply_type, 1 cc_notice_type, 0 is_exceed_buy_flow
        FROM company_apply_setting a
        WHERE company_setting_type = 2
        AND apply_type = 1
        AND NOT exists(
            SELECT 1
            FROM apply_flow b
            WHERE a.id = b.company_apply_setting_id
            AND is_exceed_buy_flow = 0
        )
    </select>

    <!-- 删除审批流表 wuchao 20180608-->
    <delete id="deleteByCompanyApplySettingId" parameterType="java.lang.String">
        delete from apply_flow
        where company_apply_setting_id = #{companyApplySettingId,jdbcType=CHAR}
    </delete>

    <!-- 根据公司审批流配置id查询 wuchao 20180608-->
    <select id="queryByCompanyApplySettingId" resultMap="com.fenbeitong.saas.core.dao.saas.ApplyFlowMapper.BaseResultMap">
        SELECT *
        FROM apply_flow
        WHERE company_apply_setting_id = #{companyApplySettingId,jdbcType=CHAR}
    </select>

    <!-- 根据公司审批流配置id批量查询 wuchao 20181224-->
    <select id="queryByCompanyApplySettingIds" resultMap="com.fenbeitong.saas.core.dao.saas.ApplyFlowMapper.BaseResultMap">
        SELECT *
        FROM apply_flow
        WHERE company_apply_setting_id IN
        <foreach collection="companyApplySettingIds" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <!-- 根据公司审批流配置id和审批流类型查询 wangguoqiang 20180619-->
    <select id="queryApplyFlow" resultMap="com.fenbeitong.saas.core.dao.saas.ApplyFlowMapper.BaseResultMap">
        SELECT *
        FROM apply_flow
        WHERE company_apply_setting_id = #{companyApplySettingId,jdbcType=CHAR}
        <if test="isExceedBuyFlow == 1">
            and is_exceed_buy_flow = 1
        </if>
        <if test="isExceedBuyFlow == 2">
            and is_exceed_buy_flow = 0
        </if>
        <if test="isExceedBuyFlow == 3">
            and is_exceed_buy_flow = 2
        </if>
        <if test="isExceedBuyFlow == 4">
            and is_exceed_buy_flow = 3
        </if>
    </select>
</mapper>