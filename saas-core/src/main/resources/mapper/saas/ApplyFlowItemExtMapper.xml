<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.ApplyFlowItemExtMapper">
    <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.ApplyFlowItem" extends="com.fenbeitong.saas.core.dao.saas.ApplyFlowItemMapper.BaseResultMap">
    </resultMap>

    <sql id="columnList">
      id, apply_flow_id, sort, item_type, item_id, create_time
    </sql>

    <delete id="deleteByFlowId">
        delete from apply_flow_item
        where apply_flow_id = #{applyFlowId,jdbcType=CHAR}
    </delete>


    <select id="selectFlowListById" resultMap="BaseResultMap">
        select
        <include refid="columnList"></include>
        from apply_flow_item
        where apply_flow_id = #{applyFlowId,jdbcType=CHAR}
        order by sort asc
    </select>

    <select id="selectListByFlowId" resultMap="BaseResultMap">
        select
        <include refid="columnList"></include>
        from apply_flow_item
        where apply_flow_id = #{applyFlowId,jdbcType=CHAR}
        order by sort asc
    </select>

    <select id="queryConditionalListByFlowId" resultMap="BaseResultMap">
        select *
        from apply_flow_item
        where apply_flow_id = #{applyFlowId,jdbcType=CHAR}
        order by sort asc,condition_min asc
    </select>

    <select id="queryConditionalListByFlowIds" resultMap="BaseResultMap">
        select *
        from apply_flow_item
        where apply_flow_id in
        <foreach collection="applyFlowIds" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        order by apply_flow_id asc, sort asc, condition_min asc
    </select>

    <select id="selectConditionalListByFlowId" resultMap="BaseResultMap">
        select *
        from apply_flow_item
        where apply_flow_id = #{applyFlowId,jdbcType=CHAR}
        and condition_min = #{conditionMin,jdbcType=NUMERIC}
        order by sort asc
    </select>

    <delete id="deleteByCompanyApplySettingId">
        delete from apply_flow_item
        where apply_flow_id in
            (select id from apply_flow
              where company_apply_setting_id = #{companyApplySettingId,jdbcType=CHAR})
    </delete>
</mapper>