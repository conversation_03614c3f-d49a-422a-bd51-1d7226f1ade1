<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.ApplyTripInfoMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.ApplyTripInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="apply_order_id" jdbcType="CHAR" property="applyOrderId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="start_city_id" jdbcType="VARCHAR" property="startCityId" />
    <result column="arrival_city_id" jdbcType="VARCHAR" property="arrivalCityId" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="trip_applicate_current_id" jdbcType="CHAR" property="tripApplicateCurrentId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="estimated_amount" jdbcType="NUMERIC" property="estimatedAmount" />
    <result column="start_city_name" jdbcType="VARCHAR" property="startCityName" />
    <result column="arrival_city_name" jdbcType="VARCHAR" property="arrivalCityName" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_time" jdbcType="TIMESTAMP" property="orderTime" />
    <result column="person_count" jdbcType="INTEGER" property="personCount" />
    <result column="price_structure" jdbcType="VARCHAR" property="priceStructure" />
    <result column="order_reason" jdbcType="VARCHAR" property="orderReason" />
    <result column="address_info" jdbcType="VARCHAR" property="addressInfo" />
    <result column="cost_attribution_name" jdbcType="VARCHAR" property="costAttributionName" />
    <result column="mall_list" jdbcType="VARCHAR" property="mallList" />
    <result column="order_reason_desc" jdbcType="VARCHAR" property="orderReasonDesc" />
    <result column="back_start_time" jdbcType="TIMESTAMP" property="backStartTime" />
    <result column="back_end_time" jdbcType="TIMESTAMP" property="backEndTime" />
    <result column="trip_type" jdbcType="INTEGER" property="tripType" />
    <result column="trip_content" jdbcType="VARCHAR" property="tripContent" />
    <result column="order_reason_id" jdbcType="INTEGER" property="orderReasonId" />
    <result column="delete_status" jdbcType="INTEGER" property="deleteStatus" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, apply_order_id, create_time, type, start_time, end_time, start_city_id, arrival_city_id, 
    state, trip_applicate_current_id, update_time, estimated_amount, start_city_name, 
    arrival_city_name, title, content, order_time, person_count, price_structure, order_reason, 
    address_info, cost_attribution_name, mall_list, order_reason_desc, back_start_time, 
    back_end_time, trip_type, trip_content, order_reason_id, delete_status
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyTripInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from apply_trip_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${limit} offset ${offset}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from apply_trip_info
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from apply_trip_info
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyTripInfoExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from apply_trip_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.saas.ApplyTripInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into apply_trip_info (id, apply_order_id, create_time, 
      type, start_time, end_time, 
      start_city_id, arrival_city_id, state, 
      trip_applicate_current_id, update_time, estimated_amount, 
      start_city_name, arrival_city_name, title, 
      content, order_time, person_count, 
      price_structure, order_reason, address_info, 
      cost_attribution_name, mall_list, order_reason_desc, 
      back_start_time, back_end_time, trip_type, 
      trip_content, order_reason_id, delete_status
      )
    values (#{id,jdbcType=CHAR}, #{applyOrderId,jdbcType=CHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{type,jdbcType=INTEGER}, #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, 
      #{startCityId,jdbcType=VARCHAR}, #{arrivalCityId,jdbcType=VARCHAR}, #{state,jdbcType=INTEGER}, 
      #{tripApplicateCurrentId,jdbcType=CHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{estimatedAmount,jdbcType=NUMERIC}, 
      #{startCityName,jdbcType=VARCHAR}, #{arrivalCityName,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, 
      #{content,jdbcType=VARCHAR}, #{orderTime,jdbcType=TIMESTAMP}, #{personCount,jdbcType=INTEGER}, 
      #{priceStructure,jdbcType=VARCHAR}, #{orderReason,jdbcType=VARCHAR}, #{addressInfo,jdbcType=VARCHAR}, 
      #{costAttributionName,jdbcType=VARCHAR}, #{mallList,jdbcType=VARCHAR}, #{orderReasonDesc,jdbcType=VARCHAR}, 
      #{backStartTime,jdbcType=TIMESTAMP}, #{backEndTime,jdbcType=TIMESTAMP}, #{tripType,jdbcType=INTEGER}, 
      #{tripContent,jdbcType=VARCHAR}, #{orderReasonId,jdbcType=INTEGER}, #{deleteStatus,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.saas.ApplyTripInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into apply_trip_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="applyOrderId != null">
        apply_order_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="startCityId != null">
        start_city_id,
      </if>
      <if test="arrivalCityId != null">
        arrival_city_id,
      </if>
      <if test="state != null">
        state,
      </if>
      <if test="tripApplicateCurrentId != null">
        trip_applicate_current_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="estimatedAmount != null">
        estimated_amount,
      </if>
      <if test="startCityName != null">
        start_city_name,
      </if>
      <if test="arrivalCityName != null">
        arrival_city_name,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="orderTime != null">
        order_time,
      </if>
      <if test="personCount != null">
        person_count,
      </if>
      <if test="priceStructure != null">
        price_structure,
      </if>
      <if test="orderReason != null">
        order_reason,
      </if>
      <if test="addressInfo != null">
        address_info,
      </if>
      <if test="costAttributionName != null">
        cost_attribution_name,
      </if>
      <if test="mallList != null">
        mall_list,
      </if>
      <if test="orderReasonDesc != null">
        order_reason_desc,
      </if>
      <if test="backStartTime != null">
        back_start_time,
      </if>
      <if test="backEndTime != null">
        back_end_time,
      </if>
      <if test="tripType != null">
        trip_type,
      </if>
      <if test="tripContent != null">
        trip_content,
      </if>
      <if test="orderReasonId != null">
        order_reason_id,
      </if>
      <if test="deleteStatus != null">
        delete_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="applyOrderId != null">
        #{applyOrderId,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="startCityId != null">
        #{startCityId,jdbcType=VARCHAR},
      </if>
      <if test="arrivalCityId != null">
        #{arrivalCityId,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=INTEGER},
      </if>
      <if test="tripApplicateCurrentId != null">
        #{tripApplicateCurrentId,jdbcType=CHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="estimatedAmount != null">
        #{estimatedAmount,jdbcType=NUMERIC},
      </if>
      <if test="startCityName != null">
        #{startCityName,jdbcType=VARCHAR},
      </if>
      <if test="arrivalCityName != null">
        #{arrivalCityName,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="orderTime != null">
        #{orderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="personCount != null">
        #{personCount,jdbcType=INTEGER},
      </if>
      <if test="priceStructure != null">
        #{priceStructure,jdbcType=VARCHAR},
      </if>
      <if test="orderReason != null">
        #{orderReason,jdbcType=VARCHAR},
      </if>
      <if test="addressInfo != null">
        #{addressInfo,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionName != null">
        #{costAttributionName,jdbcType=VARCHAR},
      </if>
      <if test="mallList != null">
        #{mallList,jdbcType=VARCHAR},
      </if>
      <if test="orderReasonDesc != null">
        #{orderReasonDesc,jdbcType=VARCHAR},
      </if>
      <if test="backStartTime != null">
        #{backStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="backEndTime != null">
        #{backEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tripType != null">
        #{tripType,jdbcType=INTEGER},
      </if>
      <if test="tripContent != null">
        #{tripContent,jdbcType=VARCHAR},
      </if>
      <if test="orderReasonId != null">
        #{orderReasonId,jdbcType=INTEGER},
      </if>
      <if test="deleteStatus != null">
        #{deleteStatus,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyTripInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from apply_trip_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_trip_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.applyOrderId != null">
        apply_order_id = #{record.applyOrderId,jdbcType=CHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.startCityId != null">
        start_city_id = #{record.startCityId,jdbcType=VARCHAR},
      </if>
      <if test="record.arrivalCityId != null">
        arrival_city_id = #{record.arrivalCityId,jdbcType=VARCHAR},
      </if>
      <if test="record.state != null">
        state = #{record.state,jdbcType=INTEGER},
      </if>
      <if test="record.tripApplicateCurrentId != null">
        trip_applicate_current_id = #{record.tripApplicateCurrentId,jdbcType=CHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.estimatedAmount != null">
        estimated_amount = #{record.estimatedAmount,jdbcType=NUMERIC},
      </if>
      <if test="record.startCityName != null">
        start_city_name = #{record.startCityName,jdbcType=VARCHAR},
      </if>
      <if test="record.arrivalCityName != null">
        arrival_city_name = #{record.arrivalCityName,jdbcType=VARCHAR},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=VARCHAR},
      </if>
      <if test="record.orderTime != null">
        order_time = #{record.orderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.personCount != null">
        person_count = #{record.personCount,jdbcType=INTEGER},
      </if>
      <if test="record.priceStructure != null">
        price_structure = #{record.priceStructure,jdbcType=VARCHAR},
      </if>
      <if test="record.orderReason != null">
        order_reason = #{record.orderReason,jdbcType=VARCHAR},
      </if>
      <if test="record.addressInfo != null">
        address_info = #{record.addressInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.costAttributionName != null">
        cost_attribution_name = #{record.costAttributionName,jdbcType=VARCHAR},
      </if>
      <if test="record.mallList != null">
        mall_list = #{record.mallList,jdbcType=VARCHAR},
      </if>
      <if test="record.orderReasonDesc != null">
        order_reason_desc = #{record.orderReasonDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.backStartTime != null">
        back_start_time = #{record.backStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.backEndTime != null">
        back_end_time = #{record.backEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tripType != null">
        trip_type = #{record.tripType,jdbcType=INTEGER},
      </if>
      <if test="record.tripContent != null">
        trip_content = #{record.tripContent,jdbcType=VARCHAR},
      </if>
      <if test="record.orderReasonId != null">
        order_reason_id = #{record.orderReasonId,jdbcType=INTEGER},
      </if>
      <if test="record.deleteStatus != null">
        delete_status = #{record.deleteStatus,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_trip_info
    set id = #{record.id,jdbcType=CHAR},
      apply_order_id = #{record.applyOrderId,jdbcType=CHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      type = #{record.type,jdbcType=INTEGER},
      start_time = #{record.startTime,jdbcType=TIMESTAMP},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      start_city_id = #{record.startCityId,jdbcType=VARCHAR},
      arrival_city_id = #{record.arrivalCityId,jdbcType=VARCHAR},
      state = #{record.state,jdbcType=INTEGER},
      trip_applicate_current_id = #{record.tripApplicateCurrentId,jdbcType=CHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      estimated_amount = #{record.estimatedAmount,jdbcType=NUMERIC},
      start_city_name = #{record.startCityName,jdbcType=VARCHAR},
      arrival_city_name = #{record.arrivalCityName,jdbcType=VARCHAR},
      title = #{record.title,jdbcType=VARCHAR},
      content = #{record.content,jdbcType=VARCHAR},
      order_time = #{record.orderTime,jdbcType=TIMESTAMP},
      person_count = #{record.personCount,jdbcType=INTEGER},
      price_structure = #{record.priceStructure,jdbcType=VARCHAR},
      order_reason = #{record.orderReason,jdbcType=VARCHAR},
      address_info = #{record.addressInfo,jdbcType=VARCHAR},
      cost_attribution_name = #{record.costAttributionName,jdbcType=VARCHAR},
      mall_list = #{record.mallList,jdbcType=VARCHAR},
      order_reason_desc = #{record.orderReasonDesc,jdbcType=VARCHAR},
      back_start_time = #{record.backStartTime,jdbcType=TIMESTAMP},
      back_end_time = #{record.backEndTime,jdbcType=TIMESTAMP},
      trip_type = #{record.tripType,jdbcType=INTEGER},
      trip_content = #{record.tripContent,jdbcType=VARCHAR},
      order_reason_id = #{record.orderReasonId,jdbcType=INTEGER},
      delete_status = #{record.deleteStatus,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saas.ApplyTripInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_trip_info
    <set>
      <if test="applyOrderId != null">
        apply_order_id = #{applyOrderId,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="startCityId != null">
        start_city_id = #{startCityId,jdbcType=VARCHAR},
      </if>
      <if test="arrivalCityId != null">
        arrival_city_id = #{arrivalCityId,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=INTEGER},
      </if>
      <if test="tripApplicateCurrentId != null">
        trip_applicate_current_id = #{tripApplicateCurrentId,jdbcType=CHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="estimatedAmount != null">
        estimated_amount = #{estimatedAmount,jdbcType=NUMERIC},
      </if>
      <if test="startCityName != null">
        start_city_name = #{startCityName,jdbcType=VARCHAR},
      </if>
      <if test="arrivalCityName != null">
        arrival_city_name = #{arrivalCityName,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="orderTime != null">
        order_time = #{orderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="personCount != null">
        person_count = #{personCount,jdbcType=INTEGER},
      </if>
      <if test="priceStructure != null">
        price_structure = #{priceStructure,jdbcType=VARCHAR},
      </if>
      <if test="orderReason != null">
        order_reason = #{orderReason,jdbcType=VARCHAR},
      </if>
      <if test="addressInfo != null">
        address_info = #{addressInfo,jdbcType=VARCHAR},
      </if>
      <if test="costAttributionName != null">
        cost_attribution_name = #{costAttributionName,jdbcType=VARCHAR},
      </if>
      <if test="mallList != null">
        mall_list = #{mallList,jdbcType=VARCHAR},
      </if>
      <if test="orderReasonDesc != null">
        order_reason_desc = #{orderReasonDesc,jdbcType=VARCHAR},
      </if>
      <if test="backStartTime != null">
        back_start_time = #{backStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="backEndTime != null">
        back_end_time = #{backEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tripType != null">
        trip_type = #{tripType,jdbcType=INTEGER},
      </if>
      <if test="tripContent != null">
        trip_content = #{tripContent,jdbcType=VARCHAR},
      </if>
      <if test="orderReasonId != null">
        order_reason_id = #{orderReasonId,jdbcType=INTEGER},
      </if>
      <if test="deleteStatus != null">
        delete_status = #{deleteStatus,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.saas.ApplyTripInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update apply_trip_info
    set apply_order_id = #{applyOrderId,jdbcType=CHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      type = #{type,jdbcType=INTEGER},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      start_city_id = #{startCityId,jdbcType=VARCHAR},
      arrival_city_id = #{arrivalCityId,jdbcType=VARCHAR},
      state = #{state,jdbcType=INTEGER},
      trip_applicate_current_id = #{tripApplicateCurrentId,jdbcType=CHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      estimated_amount = #{estimatedAmount,jdbcType=NUMERIC},
      start_city_name = #{startCityName,jdbcType=VARCHAR},
      arrival_city_name = #{arrivalCityName,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR},
      order_time = #{orderTime,jdbcType=TIMESTAMP},
      person_count = #{personCount,jdbcType=INTEGER},
      price_structure = #{priceStructure,jdbcType=VARCHAR},
      order_reason = #{orderReason,jdbcType=VARCHAR},
      address_info = #{addressInfo,jdbcType=VARCHAR},
      cost_attribution_name = #{costAttributionName,jdbcType=VARCHAR},
      mall_list = #{mallList,jdbcType=VARCHAR},
      order_reason_desc = #{orderReasonDesc,jdbcType=VARCHAR},
      back_start_time = #{backStartTime,jdbcType=TIMESTAMP},
      back_end_time = #{backEndTime,jdbcType=TIMESTAMP},
      trip_type = #{tripType,jdbcType=INTEGER},
      trip_content = #{tripContent,jdbcType=VARCHAR},
      order_reason_id = #{orderReasonId,jdbcType=INTEGER},
      delete_status = #{deleteStatus,jdbcType=INTEGER}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>