<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.ApplyFlowCopyToExtMapper">

  <select id="selectListById" resultMap="com.fenbeitong.saas.core.dao.saas.ApplyFlowCopyToMapper.BaseResultMap">
    select *
    from apply_flow_copy_to
    where apply_flow_id = #{applyFlowId,jdbcType=CHAR}
    order by sort asc
  </select>

  <select id="selectListByIds" resultMap="com.fenbeitong.saas.core.dao.saas.ApplyFlowCopyToMapper.BaseResultMap">
    select *
    from apply_flow_copy_to
    where apply_flow_id in
    <foreach collection="applyFlowIds" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
    order by sort asc
  </select>

  <delete id="deleteByCompanyApplySettingId">
    delete from apply_flow_copy_to
    where apply_flow_id in
      (select id from apply_flow
        where company_apply_setting_id = #{companyApplySettingId,jdbcType=CHAR})
  </delete>

  <!-- 审批流配置抄送数据清洗 wuchao 20180608-->
  <update id="updateApplyFlowCopyTo">
    UPDATE apply_flow_copy_to a
    SET apply_flow_id =
        (SELECT b.id
          FROM apply_flow b
          WHERE b.company_apply_setting_id = a.company_apply_setting_id)
    WHERE a.apply_flow_id is null
  </update>
</mapper>