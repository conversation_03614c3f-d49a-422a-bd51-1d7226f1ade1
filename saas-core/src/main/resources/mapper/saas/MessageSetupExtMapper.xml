<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.MessageSetupExtMapper">
  <resultMap id="BaseResultMap"
             type="com.fenbeitong.saas.core.model.saas.MessageSetup"
             extends="com.fenbeitong.saas.core.dao.saas.MessageSetupMapper.BaseResultMap">
  </resultMap>

  <insert id="batchInsert" parameterType="list">
    insert into message_setup (company_id, item_code,
    is_checked, int_val1, int_val2,
    int_val3, str_val1, str_val2,
    str_val3, create_time, update_time
    )
    values
    <foreach collection="list" item="item" open="" close="" separator=",">
      (#{item.companyId,jdbcType=VARCHAR}, #{item.itemCode,jdbcType=VARCHAR},
      #{item.isChecked,jdbcType=INTEGER}, #{item.intVal1,jdbcType=INTEGER}, #{item.intVal2,jdbcType=INTEGER},
      #{item.intVal3,jdbcType=INTEGER}, #{item.strVal1,jdbcType=VARCHAR}, #{item.strVal2,jdbcType=VARCHAR},
      #{item.strVal3,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
      )
    </foreach>
  </insert>

  <select id="getMessageSetupListWithDefault" parameterType="map" resultMap="BaseResultMap">
    select t1.busi_code, t1.item_code,
    coalesce(t2.is_checked, t1.default_checked) as "is_checked",
    coalesce(t2.int_val1, t1.default_int_val1) as "int_val1",
    coalesce(t2.int_val2, t1.default_int_val2) as "int_val2",
    coalesce(t2.int_val3, t1.default_int_val3) as "int_val3",
    coalesce(t2.str_val1, t1.default_str_val1) as "str_val1",
    coalesce(t2.str_val2, t1.default_str_val2) as "str_val2",
    coalesce(t2.str_val3, t1.default_str_val3) as "str_val3",
    t2.str_val1_en, t2.str_val2_en, t2.str_val3_en,
    t2.distinguish_categorytype as distinguishCategoryType ,
           t2.categorytype_amount as categoryTypeAmount,t2.direct_leader as directLeader,t2.depart_leader as departLeader,t2.company_id as companyId,
    t2.create_time, t2.update_time, t2.id
    from message_setup_item t1
    left join message_setup t2 on t2.item_code=t1.item_code and t2.company_id=#{companyId,jdbcType=VARCHAR}
    <where>
      <if test="itemCode != null">
        t1.item_code=#{itemCode,jdbcType=VARCHAR}
      </if>
      <if test="busiCodeList != null">
        t1.busi_code in
        <foreach collection="busiCodeList" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="itemCodeList != null">
        t1.item_code in
        <foreach collection="itemCodeList" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
  </select>

  <select id="queryMessageSetupListWithDefault" parameterType="map" resultMap="BaseResultMap">
    select t1.busi_code, t1.item_code,
    coalesce(t2.is_checked, t1.default_checked) as "is_checked",
    coalesce(t2.int_val1, t1.default_int_val1) as "int_val1",
    coalesce(t2.int_val2, t1.default_int_val2) as "int_val2",
    coalesce(t2.int_val3, t1.default_int_val3) as "int_val3",
    coalesce(t2.str_val1, t1.default_str_val1) as "str_val1",
    coalesce(t2.str_val2, t1.default_str_val2) as "str_val2",
    coalesce(t2.str_val3, t1.default_str_val3) as "str_val3",
    t2.create_time, t2.update_time, t2.company_id
    from message_setup_item t1
    left join message_setup t2 on t2.item_code=t1.item_code
    <where>
      <if test="itemCode != null">
        t1.item_code=#{itemCode,jdbcType=VARCHAR}
      </if>
      <if test="companyIdList != null">
        t2.company_id in
        <foreach collection="companyIdList" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
  </select>

  <select id="getUserMessageSetupListWithDefault" parameterType="map" resultMap="BaseResultMap">
    select t1.busi_code, t1.item_code,
    coalesce(t2.is_checked, t1.default_checked) as "is_checked",
    coalesce(t2.int_val1, t1.default_int_val1) as "int_val1",
    coalesce(t2.int_val2, t1.default_int_val2) as "int_val2",
    coalesce(t2.int_val3, t1.default_int_val3) as "int_val3",
    coalesce(t2.str_val1, t1.default_str_val1) as "str_val1",
    coalesce(t2.str_val2, t1.default_str_val2) as "str_val2",
    coalesce(t2.str_val3, t1.default_str_val3) as "str_val3",
    t2.create_time, t2.update_time, t2.user_id
    from message_setup_item t1
    left join message_setup t2 on t2.item_code=t1.item_code and t2.company_id=#{companyId,jdbcType=VARCHAR}
    <if test="userIdList != null">
      and t2.user_id in
      <foreach collection="userIdList" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <where>
      <if test="itemCode != null">
        t1.item_code=#{itemCode,jdbcType=VARCHAR}
      </if>
      <if test="busiCodeList != null">
        t1.busi_code in
        <foreach collection="busiCodeList" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="itemCodeList != null">
        t1.item_code in
        <foreach collection="itemCodeList" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
  </select>

  <select id="listCompanyMessageSetupByCompanyIdList" parameterType="map" resultMap="BaseResultMap">
    select t2.company_id, t1.busi_code, t1.item_code,
    coalesce(t2.is_checked, t1.default_checked) as "is_checked",
    coalesce(t2.int_val1, t1.default_int_val1) as "int_val1",
    coalesce(t2.int_val2, t1.default_int_val2) as "int_val2",
    coalesce(t2.int_val3, t1.default_int_val3) as "int_val3",
    coalesce(t2.str_val1, t1.default_str_val1) as "str_val1",
    coalesce(t2.str_val2, t1.default_str_val2) as "str_val2",
    coalesce(t2.str_val3, t1.default_str_val3) as "str_val3",
    t2.create_time, t2.update_time, t2.id
    from message_setup_item t1
    left join message_setup t2 on t2.item_code=t1.item_code
    <where>
      <if test="itemCodeList != null">
        t1.item_code in
        <foreach collection="itemCodeList" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="companyIdList != null">
        and t2.company_id in
        <foreach collection="companyIdList" item="id" index="index" open="(" separator="," close=")">
          #{id}
        </foreach>
      </if>
    </where>
  </select>

  <select id="getCompanyMessageSetupListWithDefault" parameterType="map" resultMap="BaseResultMap">
    select t1.busi_code, t1.item_code,
    coalesce(t2.is_checked, t1.default_checked) as "is_checked",
    coalesce(t2.int_val1, t1.default_int_val1) as "int_val1",
    coalesce(t2.int_val2, t1.default_int_val2) as "int_val2",
    coalesce(t2.int_val3, t1.default_int_val3) as "int_val3",
    coalesce(t2.str_val1, t1.default_str_val1) as "str_val1",
    coalesce(t2.str_val2, t1.default_str_val2) as "str_val2",
    coalesce(t2.str_val3, t1.default_str_val3) as "str_val3",
    t2.create_time, t2.update_time, t2.id
    from message_setup_item t1
    left join message_setup t2 on t2.item_code=t1.item_code and t2.company_id=#{companyId,jdbcType=VARCHAR}
        where t1.item_code=#{itemCode,jdbcType=VARCHAR}
        and t1.busi_code in
        <foreach collection="busiCodeList" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
  </select>

  <select id="getDistinctCompanyIds4VirtualCardWriteOffRemind" resultType="java.lang.String">
    select
        company_id
    FROM
        (
        select
            company_id,
            count(id) as num
        FROM
            message_setup
        where
            company_id in (
                select distinct
                    (company_id) as companyid
                from
                    message_setup
                where
                  item_code in (
                  'virtual_card_write_off_remind',
                  'virtual_card_write_off_remind_exceed_days'
                  )
                  )
                  and item_code in (
                  'virtual_card_write_off_remind',
                  'virtual_card_write_off_remind_exceed_days'
                  )
        GROUP BY
        company_id
        ) as b
    where
    b.num = 1;
  </select>

  <select id="queryMessageSetupInfoWithDefault" parameterType="map" resultMap="BaseResultMap">
    select t1.busi_code, t1.item_code,
           coalesce(t2.is_checked, t1.default_checked) as "is_checked",
           coalesce(t2.int_val1, t1.default_int_val1) as "int_val1",
           coalesce(t2.int_val2, t1.default_int_val2) as "int_val2",
           coalesce(t2.int_val3, t1.default_int_val3) as "int_val3",
           coalesce(t2.str_val1, t1.default_str_val1) as "str_val1",
           coalesce(t2.str_val2, t1.default_str_val2) as "str_val2",
           coalesce(t2.str_val3, t1.default_str_val3) as "str_val3",
           t2.create_time, t2.update_time
    from message_setup_item t1
           left join message_setup t2 on t2.item_code=t1.item_code and t2.company_id=#{companyId,jdbcType=VARCHAR}
    where
      t1.busi_code = #{busiCode,jdbcType=VARCHAR}
      and t1.item_code = #{itemCode,jdbcType=VARCHAR}
  </select>


</mapper>