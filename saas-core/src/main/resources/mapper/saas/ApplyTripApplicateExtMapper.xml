<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.ApplyTripApplicateExtMapper">
    <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.ApplyTripApplicateExt">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Thu Mar 09 10:17:40 CST 2017.
        -->
        <id column="id" jdbcType="CHAR" property="id"/>
        <result column="apply_trip_id" jdbcType="CHAR" property="applyTripId"/>
        <result column="apply_id" jdbcType="CHAR" property="applyId"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="order_product_type" jdbcType="INTEGER" property="orderProductType"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="action" jdbcType="INTEGER" property="action"/>
        <result column="comment" jdbcType="VARCHAR" property="comment"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
    </resultMap>

    <select id="queryOrderCount" resultType="java.lang.Integer">
        select count(1)
        from apply_trip_applicate ta left join apply_trip_info ti on ta.apply_trip_id=ti."id"
        where ta."action"!=2 and ta.apply_id= #{applyId,jdbcType=CHAR}
    </select>

    <select id="queryOrderList" resultMap="BaseResultMap">
        select ta.*,ti."type"
        from apply_trip_applicate ta left join apply_trip_info ti on ta.apply_trip_id=ti."id"
        where ta."action"!=2 and ta.apply_id= #{applyId,jdbcType=CHAR}
        order by ta.create_time desc
        limit #{pageSize,jdbcType=INTEGER} offset #{pageStart,jdbcType=INTEGER}
    </select>

    <select id="queryApplyTripApplicateList" resultMap="BaseResultMap">
        select ta.*,ti."type"
        from apply_trip_applicate ta left join apply_trip_info ti on ta.apply_trip_id=ti."id"
        where ta.apply_id = #{applyId,jdbcType=CHAR}
    </select>

    <select id="queryUseNumber" resultType="java.lang.String">
        select order_id
        from apply_trip_applicate
        where action = #{action,jdbcType=INTEGER}
        and apply_trip_id = #{applyTripId,jdbcType=CHAR}
        <![CDATA[and use_time <= #{endDayTime,jdbcType=TIMESTAMP}]]>
        <![CDATA[and use_time >= #{startDayTime,jdbcType = TIMESTAMP}]]>
    </select>

    <select id="queryApplyTripInfoApplicateList" resultMap="BaseResultMap">
       select *
       from apply_trip_applicate
       where apply_id = #{applyId,jdbcType=CHAR}
       and order_id = #{orderId,jdbcType=VARCHAR}
    </select>

    <select id="queryCancelUseNumber" resultType="java.lang.Integer">
        select count(*)
        from apply_trip_applicate
        where action = 2
        and apply_trip_id = #{applyTripId,jdbcType=CHAR}
        and order_id IN
        <foreach collection="orderIdList" item="orderId" index="index"
                 open="(" close=")" separator=",">
            #{orderId}
        </foreach>
    </select>

    <select id="queryApplyTravelOrderList" resultMap="BaseResultMap">
        select ta.*,ti."type"
        from apply_trip_applicate ta
        inner join apply_trip_info ti
        on ta.apply_trip_id=ti."id"
        inner join apply_order ao
        on ao.id = ti.apply_order_id
        where ta."action"!=2
        and ti."type" in(7,15,11)
        and ao.travel_day>0
        and ao.employee_id = #{userId,jdbcType=VARCHAR}
        and ao.company_id = #{companyId,jdbcType=VARCHAR}
        <![CDATA[and ti.start_time <= #{endTime,jdbcType=TIMESTAMP}]]>
        <![CDATA[and ti.start_time >= #{startTime,jdbcType = TIMESTAMP}]]>
        order by ta.use_time desc
    </select>

    <select id="selectTravelApplyOrder" resultMap="BaseResultMap">
        select apply_id,COUNT(*) "action" from apply_trip_applicate where apply_id in
        (select distinct apply_order_id
        from apply_travel_time
        where user_id = #{userId,jdbcType=VARCHAR}
        and company_id = #{companyId,jdbcType=CHAR}
        <![CDATA[and travel_time >= #{startDate,jdbcType=BIGINT}]]>
        <![CDATA[and travel_time <= #{endDate,jdbcType=BIGINT}]]>)
        group by apply_id
    </select>

    <select id="queryOrderListByType" resultMap="BaseResultMap">
        select ta.*,ti."type"
        from apply_trip_applicate ta left join apply_trip_info ti on ta.apply_trip_id=ti."id"
        where ta."action"!=2 and ta.apply_id= #{applyId,jdbcType=CHAR}
        and ti."type" = #{type,jdbcType=INTEGER}
        order by ta.create_time desc
    </select>

    <select id="queryOrderIdByTripId" resultMap="BaseResultMap">
        select *
        from apply_trip_applicate
	    where apply_trip_id = #{applyTripId,jdbcType=CHAR}
	    and apply_id = #{applyId,jdbcType=CHAR}
        order by create_time asc
    </select>

    <select id="queryApplyIdByOrderId" resultMap="BaseResultMap">
        select *
        from apply_trip_applicate
        where order_id = #{orderId,jdbcType=CHAR}
        order by create_time desc
    </select>
</mapper>