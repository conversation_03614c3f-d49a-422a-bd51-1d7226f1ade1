<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.MessageSetupEmailExtMapper">
  <insert id="batchInsert" parameterType="java.util.List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into message_setup_email
    (company_id,busi_code,email,create_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.companyId}, #{item.busiCode}, #{item.email}, #{item.createTime})
    </foreach>
  </insert>


  <select id="queryMessageEmailCompanyIds" resultType="java.lang.String">
        select distinct company_id from message_setup_email where company_id is not null
  </select>


</mapper>