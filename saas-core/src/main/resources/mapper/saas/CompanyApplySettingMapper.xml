<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.CompanyApplySettingMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.CompanyApplySetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="apply_type" jdbcType="INTEGER" property="applyType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="company_apply_type" jdbcType="INTEGER" property="companyApplyType" />
    <result column="apply_flow_id" jdbcType="CHAR" property="applyFlowId" />
    <result column="operator_id" jdbcType="VARCHAR" property="operatorId" />
    <result column="flow_name" jdbcType="VARCHAR" property="flowName" />
    <result column="cc_notice_type" jdbcType="INTEGER" property="ccNoticeType" />
    <result column="apply_status" jdbcType="INTEGER" property="applyStatus" />
    <result column="can_delete" jdbcType="INTEGER" property="canDelete" />
    <result column="company_setting_type" jdbcType="INTEGER" property="companySettingType" />
    <result column="cost_attribution_category" jdbcType="INTEGER" property="costAttributionCategory" />
    <result column="cost_center_status" jdbcType="INTEGER" property="costCenterStatus" />
    <result column="role_approve_type" jdbcType="INTEGER" property="roleApproveType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, apply_type, create_time, update_time, company_apply_type, apply_flow_id,
    operator_id, flow_name, cc_notice_type, apply_status, can_delete, company_setting_type,
    cost_attribution_category, cost_center_status, role_approve_type
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saas.CompanyApplySettingExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from company_apply_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from company_apply_setting
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from company_apply_setting
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saas.CompanyApplySettingExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from company_apply_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.saas.CompanyApplySetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into company_apply_setting (id, company_id, apply_type,
    create_time, update_time, company_apply_type,
    apply_flow_id, operator_id, flow_name,
    cc_notice_type, apply_status, can_delete,
    company_setting_type, cost_attribution_category,
    cost_center_status, role_approve_type)
    values (#{id,jdbcType=CHAR}, #{companyId,jdbcType=VARCHAR}, #{applyType,jdbcType=INTEGER},
    #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{companyApplyType,jdbcType=INTEGER},
    #{applyFlowId,jdbcType=CHAR}, #{operatorId,jdbcType=VARCHAR}, #{flowName,jdbcType=VARCHAR},
    #{ccNoticeType,jdbcType=INTEGER}, #{applyStatus,jdbcType=INTEGER}, #{canDelete,jdbcType=INTEGER},
    #{companySettingType,jdbcType=INTEGER}, #{costAttributionCategory,jdbcType=INTEGER},
    #{costCenterStatus,jdbcType=INTEGER}, #{roleApproveType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.saas.CompanyApplySetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into company_apply_setting
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="applyType != null">
        apply_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyApplyType != null">
        company_apply_type,
      </if>
      <if test="applyFlowId != null">
        apply_flow_id,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="flowName != null">
        flow_name,
      </if>
      <if test="ccNoticeType != null">
        cc_notice_type,
      </if>
      <if test="applyStatus != null">
        apply_status,
      </if>
      <if test="canDelete != null">
        can_delete,
      </if>
      <if test="companySettingType != null">
        company_setting_type,
      </if>
      <if test="costAttributionCategory != null">
        cost_attribution_category,
      </if>
      <if test="costCenterStatus != null">
        cost_center_status,
      </if>
      <if test="roleApproveType != null">
        role_approve_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="applyType != null">
        #{applyType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyApplyType != null">
        #{companyApplyType,jdbcType=INTEGER},
      </if>
      <if test="applyFlowId != null">
        #{applyFlowId,jdbcType=CHAR},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="flowName != null">
        #{flowName,jdbcType=VARCHAR},
      </if>
      <if test="ccNoticeType != null">
        #{ccNoticeType,jdbcType=INTEGER},
      </if>
      <if test="applyStatus != null">
        #{applyStatus,jdbcType=INTEGER},
      </if>
      <if test="canDelete != null">
        #{canDelete,jdbcType=INTEGER},
      </if>
      <if test="companySettingType != null">
        #{companySettingType,jdbcType=INTEGER},
      </if>
      <if test="costAttributionCategory != null">
        #{costAttributionCategory,jdbcType=INTEGER},
      </if>
      <if test="costCenterStatus != null">
        #{costCenterStatus,jdbcType=INTEGER},
      </if>
      <if test="roleApproveType != null">
        #{roleApproveType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saas.CompanyApplySettingExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from company_apply_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update company_apply_setting
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.applyType != null">
        apply_type = #{record.applyType,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.companyApplyType != null">
        company_apply_type = #{record.companyApplyType,jdbcType=INTEGER},
      </if>
      <if test="record.applyFlowId != null">
        apply_flow_id = #{record.applyFlowId,jdbcType=CHAR},
      </if>
      <if test="record.operatorId != null">
        operator_id = #{record.operatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.flowName != null">
        flow_name = #{record.flowName,jdbcType=VARCHAR},
      </if>
      <if test="record.ccNoticeType != null">
        cc_notice_type = #{record.ccNoticeType,jdbcType=INTEGER},
      </if>
      <if test="record.applyStatus != null">
        apply_status = #{record.applyStatus,jdbcType=INTEGER},
      </if>
      <if test="record.canDelete != null">
        can_delete = #{record.canDelete,jdbcType=INTEGER},
      </if>
      <if test="record.companySettingType != null">
        company_setting_type = #{record.companySettingType,jdbcType=INTEGER},
      </if>
      <if test="record.costAttributionCategory != null">
        cost_attribution_category = #{record.costAttributionCategory,jdbcType=INTEGER},
      </if>
      <if test="record.costCenterStatus != null">
        cost_center_status = #{record.costCenterStatus,jdbcType=INTEGER},
      </if>
      <if test="record.roleApproveType != null">
        role_approve_type = #{record.roleApproveType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update company_apply_setting
    set id = #{record.id,jdbcType=CHAR},
    company_id = #{record.companyId,jdbcType=VARCHAR},
    apply_type = #{record.applyType,jdbcType=INTEGER},
    create_time = #{record.createTime,jdbcType=TIMESTAMP},
    update_time = #{record.updateTime,jdbcType=TIMESTAMP},
    company_apply_type = #{record.companyApplyType,jdbcType=INTEGER},
    apply_flow_id = #{record.applyFlowId,jdbcType=CHAR},
    operator_id = #{record.operatorId,jdbcType=VARCHAR},
    flow_name = #{record.flowName,jdbcType=VARCHAR},
    cc_notice_type = #{record.ccNoticeType,jdbcType=INTEGER},
    apply_status = #{record.applyStatus,jdbcType=INTEGER},
    can_delete = #{record.canDelete,jdbcType=INTEGER},
    company_setting_type = #{record.companySettingType,jdbcType=INTEGER},
    cost_attribution_category = #{record.costAttributionCategory,jdbcType=INTEGER},
    cost_center_status = #{record.costCenterStatus,jdbcType=INTEGER},
    role_approve_type = #{record.roleApproveType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saas.CompanyApplySetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update company_apply_setting
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="applyType != null">
        apply_type = #{applyType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyApplyType != null">
        company_apply_type = #{companyApplyType,jdbcType=INTEGER},
      </if>
      <if test="applyFlowId != null">
        apply_flow_id = #{applyFlowId,jdbcType=CHAR},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="flowName != null">
        flow_name = #{flowName,jdbcType=VARCHAR},
      </if>
      <if test="ccNoticeType != null">
        cc_notice_type = #{ccNoticeType,jdbcType=INTEGER},
      </if>
      <if test="applyStatus != null">
        apply_status = #{applyStatus,jdbcType=INTEGER},
      </if>
      <if test="canDelete != null">
        can_delete = #{canDelete,jdbcType=INTEGER},
      </if>
      <if test="companySettingType != null">
        company_setting_type = #{companySettingType,jdbcType=INTEGER},
      </if>
      <if test="costAttributionCategory != null">
        cost_attribution_category = #{costAttributionCategory,jdbcType=INTEGER},
      </if>
      <if test="costCenterStatus != null">
        cost_center_status = #{costCenterStatus,jdbcType=INTEGER},
      </if>
      <if test="roleApproveType != null">
        role_approve_type = #{roleApproveType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.saas.CompanyApplySetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update company_apply_setting
    set company_id = #{companyId,jdbcType=VARCHAR},
    apply_type = #{applyType,jdbcType=INTEGER},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    update_time = #{updateTime,jdbcType=TIMESTAMP},
    company_apply_type = #{companyApplyType,jdbcType=INTEGER},
    apply_flow_id = #{applyFlowId,jdbcType=CHAR},
    operator_id = #{operatorId,jdbcType=VARCHAR},
    flow_name = #{flowName,jdbcType=VARCHAR},
    cc_notice_type = #{ccNoticeType,jdbcType=INTEGER},
    apply_status = #{applyStatus,jdbcType=INTEGER},
    can_delete = #{canDelete,jdbcType=INTEGER},
    company_setting_type = #{companySettingType,jdbcType=INTEGER},
    cost_attribution_category = #{costAttributionCategory,jdbcType=INTEGER},
    cost_center_status = #{costCenterStatus,jdbcType=INTEGER},
    role_approve_type = #{roleApproveType,jdbcType=INTEGER}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>