<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.ApplyFlowItemMapper">
    <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.ApplyFlowItem">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Jul 17 17:16:25 CST 2017.
        -->
        <id column="id" jdbcType="CHAR" property="id"/>
        <result column="apply_flow_id" jdbcType="CHAR" property="applyFlowId"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="item_type" jdbcType="INTEGER" property="itemType"/>
        <result column="item_id" jdbcType="VARCHAR" property="itemId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="condition_min" jdbcType="NUMERIC" property="conditionMin"/>
        <result column="condition_max" jdbcType="NUMERIC" property="conditionMax"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Jul 17 17:16:25 CST 2017.
        -->
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Jul 17 17:16:25 CST 2017.
        -->
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Jul 17 17:16:25 CST 2017.
        -->
        id, apply_flow_id, sort, item_type, item_id, create_time, condition_min, condition_max
    </sql>
    <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyFlowItemExample"
            resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Jul 17 17:16:25 CST 2017.
        -->
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from apply_flow_item
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyFlowItemExample">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Jul 17 17:16:25 CST 2017.
        -->
        delete from apply_flow_item
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.saas.ApplyFlowItem">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Jul 17 17:16:25 CST 2017.
        -->
        insert into apply_flow_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="applyFlowId != null">
                apply_flow_id,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="itemType != null">
                item_type,
            </if>
            <if test="itemId != null">
                item_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="conditionMin != null">
                condition_min,
            </if>
            <if test="conditionMax != null">
                condition_max,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=CHAR},
            </if>
            <if test="applyFlowId != null">
                #{applyFlowId,jdbcType=CHAR},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=INTEGER},
            </if>
            <if test="itemType != null">
                #{itemType,jdbcType=INTEGER},
            </if>
            <if test="itemId != null">
                #{itemId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="conditionMin != null">
                #{conditionMin,jdbcType=NUMERIC},
            </if>
            <if test="conditionMax != null">
                #{conditionMax,jdbcType=NUMERIC},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saas.ApplyFlowItemExample"
            resultType="java.lang.Long">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Jul 17 17:16:25 CST 2017.
        -->
        select count(*) from apply_flow_item
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Jul 17 17:16:25 CST 2017.
        -->
        update apply_flow_item
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=CHAR},
            </if>
            <if test="record.applyFlowId != null">
                apply_flow_id = #{record.applyFlowId,jdbcType=CHAR},
            </if>
            <if test="record.sort != null">
                sort = #{record.sort,jdbcType=INTEGER},
            </if>
            <if test="record.itemType != null">
                item_type = #{record.itemType,jdbcType=INTEGER},
            </if>
            <if test="record.itemId != null">
                item_id = #{record.itemId,jdbcType=VARCHAR},
            </if>
            <if test="record.createTime != null">
                create_time = #{record.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.conditionMin != null">
                condition_min = #{record.conditionMin,jdbcType=NUMERIC},
            </if>
            <if test="record.conditionMax != null">
                condition_max = #{record.conditionMax,jdbcType=NUMERIC},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Jul 17 17:16:25 CST 2017.
        -->
        update apply_flow_item
        set id = #{record.id,jdbcType=CHAR},
        apply_flow_id = #{record.applyFlowId,jdbcType=CHAR},
        sort = #{record.sort,jdbcType=INTEGER},
        item_type = #{record.itemType,jdbcType=INTEGER},
        item_id = #{record.itemId,jdbcType=VARCHAR},
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
        condition_min = #{record.conditionMin,jdbcType=NUMERIC},
        condition_max = #{record.conditionMax,jdbcType=NUMERIC}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saas.ApplyFlowItem">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Jul 17 17:16:25 CST 2017.
        -->
        update apply_flow_item
        <set>
            <if test="applyFlowId != null">
                apply_flow_id = #{applyFlowId,jdbcType=CHAR},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=INTEGER},
            </if>
            <if test="itemType != null">
                item_type = #{itemType,jdbcType=INTEGER},
            </if>
            <if test="itemId != null">
                item_id = #{itemId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="conditionMin != null">
                condition_min = #{conditionMin,jdbcType=NUMERIC},
            </if>
            <if test="conditionMax != null">
                condition_max = #{conditionMax,jdbcType=NUMERIC},
            </if>
        </set>
        where id = #{id,jdbcType=CHAR}
    </update>
</mapper>