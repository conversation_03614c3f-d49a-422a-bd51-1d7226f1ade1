<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.MessageWebMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.MessageWeb">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="company_id" jdbcType="CHAR" property="companyId" />
    <result column="msg_type" jdbcType="INTEGER" property="msgType" />
    <result column="msg_sub_type" jdbcType="INTEGER" property="msgSubType" />
    <result column="biz_type" jdbcType="INTEGER" property="bizType" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="biz_order" jdbcType="VARCHAR" property="bizOrder" />
    <result column="info" jdbcType="VARCHAR" property="info" />
    <result column="link" jdbcType="VARCHAR" property="link" />
    <result column="sender" jdbcType="CHAR" property="sender" />
    <result column="sender_type" jdbcType="INTEGER" property="senderType" />
    <result column="receiver" jdbcType="CHAR" property="receiver" />
    <result column="read" jdbcType="BIT" property="read" />
    <result column="biz_status" jdbcType="INTEGER" property="bizStatus" />
    <result column="biz_status_show" jdbcType="VARCHAR" property="bizStatusShow" />
    <result column="biz_ext_id" jdbcType="VARCHAR" property="bizExtId" />
    <result column="create_at" jdbcType="BIGINT" property="createAt" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="update_at" jdbcType="BIGINT" property="updateAt" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="is_del" jdbcType="INTEGER" property="isDel" />
    <result column="is_test" jdbcType="INTEGER" property="isTest" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, msg_type, msg_sub_type, biz_type, title, comment, biz_order, info, 
    link, sender, sender_type, receiver, read, biz_status, biz_status_show, biz_ext_id, 
    create_at, create_by, create_name, update_at, update_by, update_name, is_del, is_test
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saas.MessageWebExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from message_web
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${limit} offset ${offset}
        </otherwise>
      </choose>
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saas.MessageWebExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from message_web
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.saas.MessageWeb">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into message_web (id, company_id, msg_type, 
      msg_sub_type, biz_type, title, 
      comment, biz_order, info, 
      link, sender, sender_type, 
      receiver, read, biz_status, 
      biz_status_show, biz_ext_id, create_at, 
      create_by, create_name, update_at, 
      update_by, update_name, is_del, 
      is_test)
    values (#{id,jdbcType=VARCHAR}, #{companyId,jdbcType=CHAR}, #{msgType,jdbcType=INTEGER}, 
      #{msgSubType,jdbcType=INTEGER}, #{bizType,jdbcType=INTEGER}, #{title,jdbcType=VARCHAR}, 
      #{comment,jdbcType=VARCHAR}, #{bizOrder,jdbcType=VARCHAR}, #{info,jdbcType=VARCHAR}, 
      #{link,jdbcType=VARCHAR}, #{sender,jdbcType=CHAR}, #{senderType,jdbcType=INTEGER}, 
      #{receiver,jdbcType=CHAR}, #{read,jdbcType=BIT}, #{bizStatus,jdbcType=INTEGER}, 
      #{bizStatusShow,jdbcType=VARCHAR}, #{bizExtId,jdbcType=VARCHAR}, #{createAt,jdbcType=BIGINT}, 
      #{createBy,jdbcType=VARCHAR}, #{createName,jdbcType=VARCHAR}, #{updateAt,jdbcType=BIGINT}, 
      #{updateBy,jdbcType=VARCHAR}, #{updateName,jdbcType=VARCHAR}, #{isDel,jdbcType=INTEGER}, 
      #{isTest,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.saas.MessageWeb">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into message_web
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="msgType != null">
        msg_type,
      </if>
      <if test="msgSubType != null">
        msg_sub_type,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="comment != null">
        comment,
      </if>
      <if test="bizOrder != null">
        biz_order,
      </if>
      <if test="info != null">
        info,
      </if>
      <if test="link != null">
        link,
      </if>
      <if test="sender != null">
        sender,
      </if>
      <if test="senderType != null">
        sender_type,
      </if>
      <if test="receiver != null">
        receiver,
      </if>
      <if test="read != null">
        read,
      </if>
      <if test="bizStatus != null">
        biz_status,
      </if>
      <if test="bizStatusShow != null">
        biz_status_show,
      </if>
      <if test="bizExtId != null">
        biz_ext_id,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="isDel != null">
        is_del,
      </if>
      <if test="isTest != null">
        is_test,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=CHAR},
      </if>
      <if test="msgType != null">
        #{msgType,jdbcType=INTEGER},
      </if>
      <if test="msgSubType != null">
        #{msgSubType,jdbcType=INTEGER},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=INTEGER},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="comment != null">
        #{comment,jdbcType=VARCHAR},
      </if>
      <if test="bizOrder != null">
        #{bizOrder,jdbcType=VARCHAR},
      </if>
      <if test="info != null">
        #{info,jdbcType=VARCHAR},
      </if>
      <if test="link != null">
        #{link,jdbcType=VARCHAR},
      </if>
      <if test="sender != null">
        #{sender,jdbcType=CHAR},
      </if>
      <if test="senderType != null">
        #{senderType,jdbcType=INTEGER},
      </if>
      <if test="receiver != null">
        #{receiver,jdbcType=CHAR},
      </if>
      <if test="read != null">
        #{read,jdbcType=BIT},
      </if>
      <if test="bizStatus != null">
        #{bizStatus,jdbcType=INTEGER},
      </if>
      <if test="bizStatusShow != null">
        #{bizStatusShow,jdbcType=VARCHAR},
      </if>
      <if test="bizExtId != null">
        #{bizExtId,jdbcType=VARCHAR},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=BIGINT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=INTEGER},
      </if>
      <if test="isTest != null">
        #{isTest,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saas.MessageWebExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from message_web
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update message_web
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=CHAR},
      </if>
      <if test="record.msgType != null">
        msg_type = #{record.msgType,jdbcType=INTEGER},
      </if>
      <if test="record.msgSubType != null">
        msg_sub_type = #{record.msgSubType,jdbcType=INTEGER},
      </if>
      <if test="record.bizType != null">
        biz_type = #{record.bizType,jdbcType=INTEGER},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.comment != null">
        comment = #{record.comment,jdbcType=VARCHAR},
      </if>
      <if test="record.bizOrder != null">
        biz_order = #{record.bizOrder,jdbcType=VARCHAR},
      </if>
      <if test="record.info != null">
        info = #{record.info,jdbcType=VARCHAR},
      </if>
      <if test="record.link != null">
        link = #{record.link,jdbcType=VARCHAR},
      </if>
      <if test="record.sender != null">
        sender = #{record.sender,jdbcType=CHAR},
      </if>
      <if test="record.senderType != null">
        sender_type = #{record.senderType,jdbcType=INTEGER},
      </if>
      <if test="record.receiver != null">
        receiver = #{record.receiver,jdbcType=CHAR},
      </if>
      <if test="record.read != null">
        read = #{record.read,jdbcType=BIT},
      </if>
      <if test="record.bizStatus != null">
        biz_status = #{record.bizStatus,jdbcType=INTEGER},
      </if>
      <if test="record.bizStatusShow != null">
        biz_status_show = #{record.bizStatusShow,jdbcType=VARCHAR},
      </if>
      <if test="record.bizExtId != null">
        biz_ext_id = #{record.bizExtId,jdbcType=VARCHAR},
      </if>
      <if test="record.createAt != null">
        create_at = #{record.createAt,jdbcType=BIGINT},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createName != null">
        create_name = #{record.createName,jdbcType=VARCHAR},
      </if>
      <if test="record.updateAt != null">
        update_at = #{record.updateAt,jdbcType=BIGINT},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateName != null">
        update_name = #{record.updateName,jdbcType=VARCHAR},
      </if>
      <if test="record.isDel != null">
        is_del = #{record.isDel,jdbcType=INTEGER},
      </if>
      <if test="record.isTest != null">
        is_test = #{record.isTest,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update message_web
    set id = #{record.id,jdbcType=VARCHAR},
      company_id = #{record.companyId,jdbcType=CHAR},
      msg_type = #{record.msgType,jdbcType=INTEGER},
      msg_sub_type = #{record.msgSubType,jdbcType=INTEGER},
      biz_type = #{record.bizType,jdbcType=INTEGER},
      title = #{record.title,jdbcType=VARCHAR},
      comment = #{record.comment,jdbcType=VARCHAR},
      biz_order = #{record.bizOrder,jdbcType=VARCHAR},
      info = #{record.info,jdbcType=VARCHAR},
      link = #{record.link,jdbcType=VARCHAR},
      sender = #{record.sender,jdbcType=CHAR},
      sender_type = #{record.senderType,jdbcType=INTEGER},
      receiver = #{record.receiver,jdbcType=CHAR},
      read = #{record.read,jdbcType=BIT},
      biz_status = #{record.bizStatus,jdbcType=INTEGER},
      biz_status_show = #{record.bizStatusShow,jdbcType=VARCHAR},
      biz_ext_id = #{record.bizExtId,jdbcType=VARCHAR},
      create_at = #{record.createAt,jdbcType=BIGINT},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      create_name = #{record.createName,jdbcType=VARCHAR},
      update_at = #{record.updateAt,jdbcType=BIGINT},
      update_by = #{record.updateBy,jdbcType=VARCHAR},
      update_name = #{record.updateName,jdbcType=VARCHAR},
      is_del = #{record.isDel,jdbcType=INTEGER},
      is_test = #{record.isTest,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>