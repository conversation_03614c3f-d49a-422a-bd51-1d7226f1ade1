<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.CompanyReasonSettingUseMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.CompanyReasonSettingUse">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="reason_category" jdbcType="INTEGER" property="reasonCategory" />
    <result column="reason_type" jdbcType="INTEGER" property="reasonType" />
    <result column="reason_id" jdbcType="INTEGER" property="reasonId" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="operator_id" jdbcType="VARCHAR" property="operatorId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, reason_category, reason_type, reason_id, sort, operator_id, create_time
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saas.CompanyReasonSettingUseExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from company_reason_setting_use
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${limit} offset ${offset}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from company_reason_setting_use
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from company_reason_setting_use
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saas.CompanyReasonSettingUseExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from company_reason_setting_use
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.saas.CompanyReasonSettingUse">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into company_reason_setting_use (id, company_id, reason_category, 
      reason_type, reason_id, sort, 
      operator_id, create_time)
    values (#{id,jdbcType=INTEGER}, #{companyId,jdbcType=VARCHAR}, #{reasonCategory,jdbcType=INTEGER}, 
      #{reasonType,jdbcType=INTEGER}, #{reasonId,jdbcType=INTEGER}, #{sort,jdbcType=INTEGER}, 
      #{operatorId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.saas.CompanyReasonSettingUse">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into company_reason_setting_use
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="reasonCategory != null">
        reason_category,
      </if>
      <if test="reasonType != null">
        reason_type,
      </if>
      <if test="reasonId != null">
        reason_id,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="reasonCategory != null">
        #{reasonCategory,jdbcType=INTEGER},
      </if>
      <if test="reasonType != null">
        #{reasonType,jdbcType=INTEGER},
      </if>
      <if test="reasonId != null">
        #{reasonId,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saas.CompanyReasonSettingUseExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from company_reason_setting_use
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update company_reason_setting_use
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.reasonCategory != null">
        reason_category = #{record.reasonCategory,jdbcType=INTEGER},
      </if>
      <if test="record.reasonType != null">
        reason_type = #{record.reasonType,jdbcType=INTEGER},
      </if>
      <if test="record.reasonId != null">
        reason_id = #{record.reasonId,jdbcType=INTEGER},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.operatorId != null">
        operator_id = #{record.operatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update company_reason_setting_use
    set id = #{record.id,jdbcType=INTEGER},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      reason_category = #{record.reasonCategory,jdbcType=INTEGER},
      reason_type = #{record.reasonType,jdbcType=INTEGER},
      reason_id = #{record.reasonId,jdbcType=INTEGER},
      sort = #{record.sort,jdbcType=INTEGER},
      operator_id = #{record.operatorId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saas.CompanyReasonSettingUse">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update company_reason_setting_use
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="reasonCategory != null">
        reason_category = #{reasonCategory,jdbcType=INTEGER},
      </if>
      <if test="reasonType != null">
        reason_type = #{reasonType,jdbcType=INTEGER},
      </if>
      <if test="reasonId != null">
        reason_id = #{reasonId,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.saas.CompanyReasonSettingUse">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update company_reason_setting_use
    set company_id = #{companyId,jdbcType=VARCHAR},
      reason_category = #{reasonCategory,jdbcType=INTEGER},
      reason_type = #{reasonType,jdbcType=INTEGER},
      reason_id = #{reasonId,jdbcType=INTEGER},
      sort = #{sort,jdbcType=INTEGER},
      operator_id = #{operatorId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>