<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.ApplyOrderLogExtMapper">
    <resultMap id="BaseResultMap"
               type="com.fenbeitong.saas.core.model.saas.ApplyOrderLog"
               extends="com.fenbeitong.saas.core.dao.saas.ApplyOrderLogMapper.BaseResultMap">

        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="apply_order_id" jdbcType="CHAR" property="applyOrderId" />
        <result column="sponsor_id" jdbcType="CHAR" property="sponsorId" />
        <result column="action" jdbcType="INTEGER" property="action" />
        <result column="receiver_id" jdbcType="CHAR" property="receiverId" />
        <result column="ip" jdbcType="VARCHAR" property="ip" />
        <result column="check_reason" jdbcType="VARCHAR" property="checkReason" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="sort" jdbcType="INTEGER" property="sort" />
        <result column="price" jdbcType="NUMERIC" property="price" />
        <result column="root_apply_order_id" jdbcType="CHAR" property="rootApplyOrderId" />
        <result column="sponsor_name" jdbcType="VARCHAR" property="sponsorName" />
        <result column="receiver_name" jdbcType="VARCHAR" property="receiverName" />
    </resultMap>

    <select id="selectListByCreateTimeDesc" resultMap="BaseResultMap" parameterType="java.lang.String">
        select id, apply_order_id, sponsor_id, action, receiver_id, ip, check_reason, create_time, sort
        from apply_order_log
        where apply_order_id = #{applyId,jdbcType=CHAR}
        order by sort desc,create_time desc
    </select>

    <select id="selectListByCreateTimeAsc" resultMap="BaseResultMap" parameterType="java.lang.String">
        select id, apply_order_id, sponsor_id, action, receiver_id, ip, check_reason, create_time, sort
        from apply_order_log
        where apply_order_id = #{applyId,jdbcType=CHAR}
        order by sort asc,create_time asc
    </select>

    <select id="selectListByCreateTimeDescAndAction" resultMap="BaseResultMap" >
        select id, apply_order_id, sponsor_id, action, receiver_id, ip, check_reason, create_time, sort
        from apply_order_log
        where apply_order_id = #{applyId,jdbcType=CHAR} and action = #{action,jdbcType=INTEGER}
        order by sort desc,create_time desc
    </select>

    <select id="selectListToCurrentByCreateTimeDesc" resultMap="BaseResultMap" parameterType="java.lang.String">
        select id, apply_order_id, sponsor_id, action, receiver_id, ip, check_reason, create_time, sort
        from apply_order_log
        where apply_order_id = #{applyId,jdbcType=CHAR}
        and action != 0
        order by sort desc,create_time desc
    </select>

    <select id="batchSelectListToCurrentByCreateTimeDesc" resultMap="BaseResultMap" parameterType="java.util.List">
        select id, apply_order_id, sponsor_id, action, receiver_id, ip, check_reason, create_time, sort
        from apply_order_log
        where apply_order_id in
        <foreach collection="list" item="applyId" index="index"
                 open="(" close=")" separator=",">
            #{applyId}
        </foreach>
        and action != 0
        order by apply_order_id desc,sort desc,create_time desc
    </select>

    <select id="selectApproveUser" resultMap="BaseResultMap">
        select id, apply_order_id, sponsor_id, action, receiver_id, ip, check_reason, create_time, sort
        from apply_order_log
        where apply_order_id = #{applyId,jdbcType=CHAR}
        and action = #{action,jdbcType=INTEGER}
        order by sort asc,create_time asc
    </select>

    <update id="updateApplyOrderActionById" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrderLog">
        update apply_order_log
        set
            action = #{action,jdbcType=INTEGER},
            <if test="checkReason != null">
                check_reason = #{checkReason,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=NUMERIC},
            </if>
            receiver_id = #{receiverId,jdbcType=CHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectNextApproveUser" resultMap="BaseResultMap">
        select *
        from apply_order_log
        where apply_order_id = #{applyId,jdbcType=CHAR}
        and action = #{action,jdbcType=INTEGER}
        order by sort asc,create_time asc
    </select>

    <select id="selectByForConditon" parameterType="com.fenbeitong.saas.core.contract.apply.ApplyOrderLogVoContract" resultMap="BaseResultMap">
        SELECT
        l."id",
        l.apply_order_id,
        l.sponsor_id,
        l."action",
        l.receiver_id,
        l.ip,
        l.check_reason,
        l.create_time
        FROM apply_order_log l
        WHERE 1=1
        <if test="applyOrderId!=null">
            AND l.apply_order_id = #{applyOrderId}
        </if>
        AND l.sponsor_id = #{sponsorId}
        and l.delete_status=0
        AND <![CDATA[ l.create_time > #{endTime,jdbcType=TIMESTAMP}]]>
        ORDER BY l.sort,l.create_time ASC
    </select>

    <select id="selectListByIds" resultMap="BaseResultMap" parameterType="java.util.List">
        select *
        from apply_order_log
        where id in
        <foreach collection="list" item="logId" index="index"
                 open="(" close=")" separator=",">
            #{logId}
        </foreach>
    </select>
    <select id="selectListByApplyId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select *
        from apply_order_log
        where apply_order_id = #{applyId,jdbcType=CHAR}
        order by sort asc, create_time asc
    </select>
    <select id="selectListByRootApplyId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select *
        from apply_order_log
        where root_apply_order_id = #{rootApplyId,jdbcType=CHAR}
        order by sort asc, create_time asc
    </select>
    <delete id="deleteByApplyOrderId" parameterType="java.lang.String">
        delete from apply_order_log
        where apply_order_id = #{applyOrderId,jdbcType=CHAR}
    </delete>

    <update id="updateApplyLogById" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrderLog">
        update apply_order_log
        set
        action = #{action,jdbcType=INTEGER},
        <if test="checkReason != null">
            check_reason = #{checkReason,jdbcType=VARCHAR},
        </if>
        <if test="createTime != null">
            create_time = #{createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="price != null">
            price = #{price,jdbcType=NUMERIC},
        </if>
        <if test="receiverName != null">
            receiver_name = #{receiverName,jdbcType=VARCHAR},
        </if>
        receiver_id = #{receiverId,jdbcType=CHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="queryApprovalInfo" resultMap="BaseResultMap" parameterType="java.lang.String">
        select * from apply_order_log
        where apply_order_id = #{applyId,jdbcType=INTEGER}
        order by sort desc
    </select>

    <select id="queryLogListByApplyIds" resultMap="BaseResultMap" parameterType="java.lang.String">
        select id, apply_order_id, sponsor_id, action, receiver_id, ip, check_reason, create_time, sort
        from apply_order_log
        where apply_order_id in
        <foreach collection="applyIds" item="applyId" index="index"
                 open="(" close=")" separator=",">
            #{applyId}
        </foreach>
        order by sort asc,create_time asc
    </select>
</mapper>