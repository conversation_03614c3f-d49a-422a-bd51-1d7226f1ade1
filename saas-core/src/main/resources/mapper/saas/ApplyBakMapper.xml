<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.ApplyBakMapper">
    <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.ApplyBak">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Thu Mar 23 15:22:10 CST 2017.
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="company_id" jdbcType="VARCHAR" property="companyId"/>
        <result column="apply_data" jdbcType="VARCHAR" property="applyData"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="ip" jdbcType="VARCHAR" property="ip"/>
    </resultMap>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Thu Mar 23 15:22:10 CST 2017.
        -->
        delete from apply_bak
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.fenbeitong.saas.core.model.saas.ApplyBak" useGeneratedKeys="true" keyProperty="id"
            keyColumn="id">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Thu Mar 23 15:22:10 CST 2017.
        -->
        insert into apply_bak ( user_id, company_id,
        apply_data, create_time, ip
        )
        values (#{userId,jdbcType=VARCHAR}, #{companyId,jdbcType=VARCHAR},
        #{applyData,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{ip,jdbcType=VARCHAR}
        )
    </insert>
    <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.saas.ApplyBak">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Thu Mar 23 15:22:10 CST 2017.
        -->
        update apply_bak
        set user_id = #{userId,jdbcType=VARCHAR},
        company_id = #{companyId,jdbcType=VARCHAR},
        apply_data = #{applyData,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        ip = #{ip,jdbcType=VARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Thu Mar 23 15:22:10 CST 2017.
        -->
        select id, user_id, company_id, apply_data, create_time, ip
        from apply_bak
        where id = #{id,jdbcType=INTEGER}
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Thu Mar 23 15:22:10 CST 2017.
        -->
        select id, user_id, company_id, apply_data, create_time, ip
        from apply_bak
    </select>
</mapper>