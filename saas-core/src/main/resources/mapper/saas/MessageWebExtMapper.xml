<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.MessageWebExtMapper">

  <insert id="batchSave">
    INSERT INTO message_web(id, company_id, msg_type, msg_sub_type, biz_type, biz_order, title, comment, sender, sender_type, receiver, read, create_at)
    VALUES
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.companyId}, #{item.msgType}, #{item.msgSubType}, #{item.bizType}, #{item.bizOrder}, #{item.title},
       #{item.comment}, #{item.sender}, #{item.senderType}, #{item.receiver},#{item.read}, #{item.createAt})
    </foreach>
  </insert>

</mapper>