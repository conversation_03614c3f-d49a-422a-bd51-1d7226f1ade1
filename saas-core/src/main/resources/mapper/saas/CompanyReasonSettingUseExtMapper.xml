<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.CompanyReasonSettingUseExtMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.CompanyReasonSettingUse"
             extends="com.fenbeitong.saas.core.dao.saas.CompanyReasonSettingUseMapper.BaseResultMap">
  </resultMap>

  <select id="queryReasonItems" resultType="com.fenbeitong.saas.api.model.dto.reason.ReasonItem">
    SELECT crsu.reason_id id, ccr.name
    FROM company_reason_setting_use crsu
    JOIN company_custom_reason ccr ON crsu.reason_id = ccr.id AND ccr.status = 1
    WHERE crsu.company_id = #{companyId,jdbcType=VARCHAR}
    AND crsu.reason_type = #{reasonType,jdbcType=INTEGER}
    ORDER BY crsu.sort
  </select>

  <select id="queryReasonItemsByKeyword" resultType="com.fenbeitong.saas.api.model.dto.reason.ReasonItem">
    SELECT crsu.reason_id id, ccr.name
    FROM company_reason_setting_use crsu
    JOIN company_custom_reason ccr ON crsu.reason_id = ccr.id AND ccr.status = 1
    WHERE crsu.company_id = #{companyId,jdbcType=VARCHAR}
    AND crsu.reason_type = #{reasonType,jdbcType=INTEGER}
    <if test="keyword != null">
    AND ccr.name like #{keyword,jdbcType=VARCHAR}
    </if>
    ORDER BY crsu.sort
  </select>

  <insert id="batchInsertReasonSettingUse" parameterType="java.util.List">
    insert into company_reason_setting_use
      (company_id, reason_category,
      reason_type, reason_id, sort,
      operator_id, create_time)
    values
    <foreach collection="companyReasonSettingUseList" item="item" index="index" separator=",">
      (#{item.companyId,jdbcType=VARCHAR}, #{item.reasonCategory,jdbcType=INTEGER},
      #{item.reasonType,jdbcType=INTEGER}, #{item.reasonId,jdbcType=INTEGER}, #{item.sort,jdbcType=INTEGER},
      #{item.operatorId,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

    <select id="listReasonItems" parameterType="java.util.List"
            resultType="com.fenbeitong.saas.api.model.dto.reason.CompanyReasonItemDTO">
        SELECT crsu.company_id companyId, crsu.reason_id id, ccr.name
        FROM company_reason_setting_use crsu
        JOIN company_custom_reason ccr ON crsu.reason_id = ccr.id AND ccr.status = 1
        <where>
            crsu.company_id in
            <foreach collection="companyIdList" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
            AND crsu.reason_type = #{reasonType,jdbcType=INTEGER}
        </where>
        ORDER BY crsu.sort
    </select>
</mapper>