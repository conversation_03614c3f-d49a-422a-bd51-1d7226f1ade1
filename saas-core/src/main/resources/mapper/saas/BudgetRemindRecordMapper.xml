<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.BudgetRemindRecordMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.BudgetRemindRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 20 10:10:13 CST 2017.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="budget_type" jdbcType="INTEGER" property="budgetType" />
    <result column="item_id" jdbcType="VARCHAR" property="itemId" />
    <result column="remind_month" jdbcType="VARCHAR" property="remindMonth" />
    <result column="remind_type" jdbcType="VARCHAR" property="remindType" />
    <result column="remind_content" jdbcType="VARCHAR" property="remindContent" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 20 10:10:13 CST 2017.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 20 10:10:13 CST 2017.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 20 10:10:13 CST 2017.
    -->
    id, budget_type, item_id, remind_month, remind_type, remind_content, create_time, 
    update_time
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saas.BudgetRemindRecordExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 20 10:10:13 CST 2017.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from budget_remind_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saas.BudgetRemindRecordExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 20 10:10:13 CST 2017.
    -->
    delete from budget_remind_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.saas.BudgetRemindRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 20 10:10:13 CST 2017.
    -->
    insert into budget_remind_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="budgetType != null">
        budget_type,
      </if>
      <if test="itemId != null">
        item_id,
      </if>
      <if test="remindMonth != null">
        remind_month,
      </if>
      <if test="remindType != null">
        remind_type,
      </if>
      <if test="remindContent != null">
        remind_content,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="budgetType != null">
        #{budgetType,jdbcType=INTEGER},
      </if>
      <if test="itemId != null">
        #{itemId,jdbcType=VARCHAR},
      </if>
      <if test="remindMonth != null">
        #{remindMonth,jdbcType=VARCHAR},
      </if>
      <if test="remindType != null">
        #{remindType,jdbcType=VARCHAR},
      </if>
      <if test="remindContent != null">
        #{remindContent,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saas.BudgetRemindRecordExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 20 10:10:13 CST 2017.
    -->
    select count(*) from budget_remind_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 20 10:10:13 CST 2017.
    -->
    update budget_remind_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.budgetType != null">
        budget_type = #{record.budgetType,jdbcType=INTEGER},
      </if>
      <if test="record.itemId != null">
        item_id = #{record.itemId,jdbcType=VARCHAR},
      </if>
      <if test="record.remindMonth != null">
        remind_month = #{record.remindMonth,jdbcType=VARCHAR},
      </if>
      <if test="record.remindType != null">
        remind_type = #{record.remindType,jdbcType=VARCHAR},
      </if>
      <if test="record.remindContent != null">
        remind_content = #{record.remindContent,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 20 10:10:13 CST 2017.
    -->
    update budget_remind_record
    set id = #{record.id,jdbcType=BIGINT},
      budget_type = #{record.budgetType,jdbcType=INTEGER},
      item_id = #{record.itemId,jdbcType=VARCHAR},
      remind_month = #{record.remindMonth,jdbcType=VARCHAR},
      remind_type = #{record.remindType,jdbcType=VARCHAR},
      remind_content = #{record.remindContent,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saas.BudgetRemindRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jun 20 10:10:13 CST 2017.
    -->
    update budget_remind_record
    <set>
      <if test="budgetType != null">
        budget_type = #{budgetType,jdbcType=INTEGER},
      </if>
      <if test="itemId != null">
        item_id = #{itemId,jdbcType=VARCHAR},
      </if>
      <if test="remindMonth != null">
        remind_month = #{remindMonth,jdbcType=VARCHAR},
      </if>
      <if test="remindType != null">
        remind_type = #{remindType,jdbcType=VARCHAR},
      </if>
      <if test="remindContent != null">
        remind_content = #{remindContent,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>