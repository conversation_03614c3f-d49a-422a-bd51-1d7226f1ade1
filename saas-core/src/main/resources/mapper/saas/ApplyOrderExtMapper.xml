<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.ApplyOrderExtMapper">

    <resultMap id="BaseResultMap"
               type="com.fenbeitong.saas.core.model.saas.ApplyOrder"
               extends="com.fenbeitong.saas.core.dao.saas.ApplyOrderMapper.BaseResultMap">
    </resultMap>

    <resultMap id="ExportResultMap"
               type="com.fenbeitong.saas.core.contract.apply.ApplyOrderExportContract"
               extends="BaseResultMap">
        <collection property="applyTripInfoList" resultMap="com.fenbeitong.saas.core.dao.saas.ApplyTripInfoExtMapper.BaseResultMap" columnPrefix="trip_" />
        <collection property="applyTripGuestList" resultMap="com.fenbeitong.saas.core.dao.saas.ApplyTripGuestMapper.BaseResultMap" columnPrefix="guest_" />
        <collection property="applyOrderLogList" resultMap="com.fenbeitong.saas.core.dao.saas.ApplyOrderLogExtMapper.BaseResultMap" columnPrefix="log_" />
    </resultMap>

    <select id="queryStartApplyNumList" resultType="java.lang.Integer">
        select count(1)
        from apply_order
        where employee_id = #{userId,jdbcType=CHAR}
        and company_id = #{companyId,jdbcType=CHAR}
        and type != 5
        and type != 20
        and delete_status=0
        <if test="applyTypeList!=null">
            and type not in
            <foreach collection="applyTypeList" item="item" index="index"
                     open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <!-- status: 0.审批完成 1.审批中-->
        <if test="status == 1">
            and state = 2
        </if>
        <if test="status == 0">
            and state != 2 and state != 1
        </if>
        <if test="itemList != null">
            <foreach collection="itemList" item="item" open="and (" close=")" separator="or">
                <!-- state: 1.草稿 2.待审核 4.已审核 8.已作废 16.已驳回 64.撤销 128.已过期 256.超时 512.已完成 1024.变更中 2048.已变更 -->
                <!-- item: 2.有效行程申请 3.失效行程申请 4.差旅订单申请 5.用餐申请 6.采购申请-->
                <if test="item == 2">
                    (state = 4
                    and past_status = false
                    and (apply_order_type = 1
                    <if test="historyVersion == true">
                        or apply_order_type = 3
                    </if>)
                    )
                </if>
                <if test="item == 3">
                    ((state in (8, 16, 64, 128, 256, 512, 1024, 2048) or (state = 4 and past_status = true))
                    and (apply_order_type = 1
                    <if test="historyVersion == true">
                        or apply_order_type = 3
                    </if>)
                    )
                </if>
                <if test="item == 4">
                    (apply_order_type = 2
                    and type in (6, 7, 8, 9))
                </if>
                <if test="item == 5">
                    (apply_order_type = 5
                    and type = 11)
                </if>
                <if test="item == 6">
                    (apply_order_type = 3
                    and type = 4)
                </if>
                <if test="item == 7">
                    (apply_order_type = 4
                    and type = 10)
                </if>
                <if test="item == 8">
                    apply_order_type = 6
                </if>
                <if test="item == 9">
                    (apply_order_type = 7
                    and type = 14)
                </if>
                <if test="item == 10">
                    apply_order_type = 8
                </if>
                <if test="item == 11">
                    apply_order_type = 9
                </if>
                <if test="item == 12">
                    apply_order_type = 10
                </if>
                <if test="item == 13">
                    apply_order_type = 11
                </if>
                <if test="item == 14">
                    apply_order_type = 12
                </if>
                <if test="item == 15">
                    apply_order_type = 13
                </if>
            </foreach>
        </if>
    </select>

    <select id="queryStartApplyOrderList" resultMap="BaseResultMap">
        select *
        from apply_order
        where employee_id = #{userId,jdbcType=CHAR}
        and company_id = #{companyId,jdbcType=CHAR}
        and type != 5
        and type != 20
        and delete_status=0
        <if test="applyTypeList!=null">
            and type not in
            <foreach collection="applyTypeList" item="item" index="index"
                     open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <!-- status: 0.审批完成 1.审批中-->
        <if test="status == 1">
            and state = 2
        </if>
        <if test="status == 0">
            and state != 2 and state != 1
        </if>
        <if test="itemList != null">
            <foreach collection="itemList" item="item" open="and (" close=")" separator="or">
                <!-- state: 1.草稿 2.待审核 4.已审核 8.已作废 16.已驳回 64.撤销 128.已过期 256.超时 512.已完成 1024.变更中 2048.已变更 -->
                <!-- item: 2.有效行程申请 3.失效行程申请 4.差旅订单申请 5.用餐申请 6.采购申请-->
                <if test="item == 2">
                    (state = 4
                    and past_status = false
                    and (apply_order_type = 1
                    <if test="historyVersion == true">
                        or apply_order_type = 3
                    </if>)
                    )
                </if>
                <if test="item == 3">
                    ((state in (8, 16, 64, 128, 256, 512, 1024, 2048) or (state = 4 and past_status = true))
                    and (apply_order_type = 1
                    <if test="historyVersion == true">
                        or apply_order_type = 3
                    </if>)
                    )
                </if>
                <if test="item == 4">
                    (apply_order_type = 2
                    and type in (6, 7, 8, 9))
                </if>
                <if test="item == 5">
                    (apply_order_type = 5
                    and type = 11)
                </if>
                <if test="item == 6">
                    (apply_order_type = 3
                    and type = 4)
                </if>
                <if test="item == 7">
                    (apply_order_type = 4
                    and type = 10)
                </if>
                <if test="item == 8">
                    apply_order_type = 6
                </if>
                <if test="item == 9">
                    (apply_order_type = 7
                    and type = 14)
                </if>
                <if test="item == 10">
                    apply_order_type = 8
                </if>
                <if test="item == 11">
                    apply_order_type = 9
                </if>
                <if test="item == 12">
                    apply_order_type = 10
                </if>
                <if test="item == 13">
                    apply_order_type = 11
                </if>
                <if test="item == 14">
                    apply_order_type = 12
                </if>
                <if test="item == 15">
                    apply_order_type = 13
                </if>
            </foreach>
        </if>
        order by create_time desc
        limit #{pageSize,jdbcType=INTEGER} offset #{pageStart,jdbcType=INTEGER}
    </select>

    <select id="queryUnPastApplyNum" resultType="java.lang.Long">
        select count(1)
        from apply_order
        where past_status = false
        and state = 4
        and apply_order_type in (1, 5, 7)
        and delete_status=0
    </select>

    <select id="queryUnPastApplyList" resultMap="BaseResultMap">
        select *
        from apply_order
        where past_status = false
        and state = 4
        and delete_status=0
        and apply_order_type in (1, 5, 7)
        order by update_time desc
        limit #{pageSize,jdbcType=INTEGER} offset #{pageStart,jdbcType=INTEGER}
    </select>

    <update id="updatePastStatusById" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrder">
        update apply_order
        <set>
            past_status = true
        </set>
        where id = #{id,jdbcType=CHAR}
        and state = 4
        and apply_order_type in (1, 5, 7)
        and delete_status=0
    </update>

    <update id="setStatus">
        update apply_order
        set state = #{status,jdbcType=INTEGER},
        update_time = #{time,jdbcType=TIMESTAMP},
        current_log_id = #{currentLogId,jdbcType=INTEGER}
        where id = #{id,jdbcType=CHAR}
    </update>
    <select id="selectAll" resultMap="BaseResultMap">
        select *
        from apply_order
    </select>
    <update id="setApproverIdAndStatus">
        update apply_order
        set state = #{status,jdbcType=INTEGER},
        approver_id = #{approverId,jdbcType=CHAR},
        update_time = #{time,jdbcType=TIMESTAMP},
        current_log_id = #{currentLogId,jdbcType=INTEGER}
        <if test="repulseDesc != null">
            , repulse_desc = #{repulseDesc,jdbcType=CHAR}
        </if>
        where id = #{id,jdbcType=CHAR}
    </update>

    <select id="getPageListByApplyUserId" resultMap="BaseResultMap">
        select *
        from apply_order where employee_id = #{applyUserId,jdbcType=CHAR}
        and company_id = #{companyId,jdbcType=CHAR}
        and apply_order_type = 1
        and delete_status=0
        <if test="status == 2">
            <!--读取待处理申请单(草稿、待审核)-->
            and (state = 1 or state = 2)
        </if>
        <if test="status == 4">
            <!--读取已处理申请单(已退回、已通过)-->
            and (state = 16 or state = 4)
        </if>
        order by update_time desc
        limit #{pageSize,jdbcType=INTEGER} offset #{pageStart,jdbcType=INTEGER}
    </select>

    <select id="getCountByApplyUserId" resultType="java.lang.Integer">
        select count(1)
        from apply_order where employee_id = #{applyUserId,jdbcType=CHAR}
        and company_id = #{companyId,jdbcType=CHAR}
        and apply_order_type = 1
        and delete_status=0
        <if test="status == 2">
            <!--读取待处理申请单(草稿、被退回)-->
            and (state = 1 or state = 2)
        </if>
        <if test="status == 4">
            <!--读取已处理申请单(提交审核、已通过)-->
            and (state = 16 or state = 4)
        </if>
    </select>


    <select id="getPageListByApproverId" resultMap="BaseResultMap">
        select a.*
        from apply_order a,apply_approver_map m
        where a.id = m.apply_id
        and m.approver_id = #{approverId,jdbcType=CHAR}
        and a.company_id = #{companyId,jdbcType=CHAR}
        and a.delete_status=0
        <if test="status == 2">
            <!--读取待处理申请单(待处理)-->
            and (a.state = 2 and a.approver_id = #{approverId})
        </if>
        <if test="status == 4">
            <!--读取已处理申请单(已处理)-->
            and ((a.state = 4 or a.state = 16) or (a.state = 2 and a.approver_id != #{approverId}))
        </if>
        <if test=" status != 2 &amp;&amp; status != 4">
            and (a.state != 1)
        </if>
        order by a.update_time desc
        limit #{pageSize,jdbcType=INTEGER} offset #{pageStart,jdbcType=INTEGER}
    </select>

    <select id="getCountByApproverId" resultType="java.lang.Integer">
        select count(1)
        from apply_order a,apply_approver_map m
        where a.id = m.apply_id
        and m.approver_id = #{approverId,jdbcType=CHAR}
        and a.company_id = #{companyId,jdbcType=CHAR}
        and a.apply_order_type = 1
        and a.delete_status=0
        <if test="status == 2">
            <!--读取待处理申请单(待处理)-->
            and (a.state = 2 and a.approver_id = #{approverId})
        </if>
        <if test="status == 4">
            <!--读取已处理申请单(已处理)-->
            and ((a.state = 4 or a.state = 16) or (a.state = 2 and a.approver_id != #{approverId}))
        </if>
        <if test=" status != 2 &amp;&amp; status != 4">
            and (a.state != 1)
        </if>
    </select>

    <select id="getAirOrTranValidApplyList" resultMap="BaseResultMap">
        (select *
        from apply_order where id in (
        select a.id
        from apply_order a,apply_trip_info t
        where a.id = t.apply_order_id
        and a.employee_id = #{userId,jdbcType=CHAR}
        and a.company_id = #{companyId,jdbcType=CHAR}
        and a.state = 4
        and a.delete_status=0
        and t.type = #{tripType,jdbcType=INTEGER}
        and a.apply_order_type = 1
        <if test="cityFlag == 0">
            and t.start_city_id IN
            <foreach collection="startCityIds" item="startCityId" index="index"
                     open="(" close=")" separator=",">
                #{startCityId}
            </foreach>
            and t.arrival_city_id IN
            <foreach collection="arrivalCityIds" item="arrivalCityId" index="index"
                     open="(" close=")" separator=",">
                #{arrivalCityId}
            </foreach>
        </if>
        and t.end_time is null
        <![CDATA[and t.start_time <= #{maxTime,jdbcType=TIMESTAMP}]]>
        <![CDATA[and t.start_time >= #{minTime,jdbcType = TIMESTAMP}]]>
        and t.state = 1
        ))
        union all
        (select *
        from apply_order where id in (
        select a.id
        from apply_order a,apply_trip_info t
        where a.id = t.apply_order_id
        and a.employee_id = #{userId,jdbcType=CHAR}
        and a.company_id = #{companyId,jdbcType=CHAR}
        and a.state = 4
        and a.delete_status=0
        and t.type = #{tripType,jdbcType=INTEGER}
        and a.apply_order_type = 1
        <if test="cityFlag == 0">
            and t.start_city_id IN
            <foreach collection="startCityIds" item="startCityId" index="index"
                     open="(" close=")" separator=",">
                #{startCityId}
            </foreach>
            and t.arrival_city_id IN
            <foreach collection="arrivalCityIds" item="arrivalCityId" index="index"
                     open="(" close=")" separator=",">
                #{arrivalCityId}
            </foreach>
        </if>
        and t.end_time is not null
        <![CDATA[and t.end_time >= #{minTime,jdbcType=TIMESTAMP}]]>
        <![CDATA[and t.start_time <= #{minTime,jdbcType = TIMESTAMP}]]>
        and t.state = 1
        ))
    </select>
    <select id="getHotelValidApplyList" resultMap="BaseResultMap">
        select *
        from apply_order where id in (
        select a.id
        from apply_order a,apply_trip_info t
        where a.id = t.apply_order_id
        and a.state = 4
        and a.apply_order_type = 1
        and a.employee_id = #{userId,jdbcType=CHAR}
        and a.company_id = #{companyId,jdbcType=CHAR}
        and t.type = 11
        and a.delete_status=0
        <if test="cityFlag == 0">
            and t.start_city_id IN
            <foreach collection="startCityIds" item="startCityId" index="index"
                     open="(" close=")" separator=",">
                #{startCityId}
            </foreach>
        </if>
        <![CDATA[and t.end_time >= #{maxTime,jdbcType=TIMESTAMP}]]>
        <![CDATA[and t.start_time <= #{minTime,jdbcType = TIMESTAMP}]]>
        and t.state = 1
        )
    </select>
    <select id="getTaxiValidApplyList" resultMap="BaseResultMap">
        select *
        from apply_order where id in (
        select a.id
        from apply_order a,apply_trip_info t
        where a.id = t.apply_order_id
        and a.state = 4
        and a.apply_order_type = 1
        and a.type = 2
        and a.employee_id = #{userId,jdbcType=CHAR}
        and a.company_id = #{companyId,jdbcType=CHAR}
        and t.type = 3
        and a.delete_status=0
        and t.start_city_id IN
        <foreach collection="startCityIds" item="startCityId" index="index"
                 open="(" close=")" separator=",">
            #{startCityId}
        </foreach>
        and t.end_time > #{timeMinusOneDay,jdbcType=TIMESTAMP}
        and t.start_time &lt;= #{time,jdbcType = TIMESTAMP}
        and t.state = 1
        )
    </select>
    <select id="getDinnerValidApplyList" resultMap="BaseResultMap">
        select *
        from apply_order where id in (
        select a.id
        from apply_order a,apply_trip_info t
        where a.id = t.apply_order_id
        and a.state = 4
        and a.apply_order_type = 5
        and a.employee_id = #{userId,jdbcType=CHAR}
        and a.company_id = #{companyId,jdbcType=CHAR}
        and t.type = 60
        and t.end_time > #{orderTime,jdbcType=TIMESTAMP}
        and t.start_time &lt;= #{orderTime,jdbcType = TIMESTAMP}
        and t.state = 1
        and a.delete_status=0
        )
        order by create_time
    </select>
    <select id="getTakeawayValidApplyList" resultMap="BaseResultMap">
        select *
        from apply_order where id in (
        select a.id
        from apply_order a,apply_trip_info t
        where a.id = t.apply_order_id
        and a.state = 4
        and a.apply_order_type = 7
        and a.past_status = false
        and a.employee_id = #{userId,jdbcType=CHAR}
        and a.company_id = #{companyId,jdbcType=CHAR}
        and t.type = 50
        and a.delete_status=0
        <if test="orderTime != null">
            and t.end_time > #{orderTime,jdbcType=TIMESTAMP}
            and t.start_time &lt;= #{orderTime,jdbcType = TIMESTAMP}
        </if>
        )
        order by create_time
    </select>
    <select id="getByIdAndApproverIdAndCompanyId" resultMap="BaseResultMap">
        select a.*
        from apply_order a,apply_approver_map m
        where a.id = m.apply_id
        and m.approver_id = #{approverId,jdbcType=CHAR}
        and a.id = #{applyId,jdbcType=CHAR}
        and a.company_id = #{companyId,jdbcType=CHAR}
        and a.state != 1
    </select>

    <select id="getCCApplyOrderCountByApplyId" resultMap="BaseResultMap">
        select distinct a.id
        from apply_order a,apply_order_copy_to b
        where a.id = b.apply_order_id
        and b.user_id = #{userId,jdbcType=VARCHAR}
        and a.company_id = #{companyId,jdbcType=CHAR}
        and a.delete_status=0
        <if test="status == 2">
            <!--读取待处理申请单(待处理)-->
            and a.state = 2
        </if>
        <if test="status == 4">
            <!--读取已处理申请单(已处理)-->
            and a.state in (4,16)
        </if>
        <if test=" status != 2 &amp;&amp; status != 4">
            and a.state != 1
        </if>
    </select>

    <select id="getCCPageListByApplyId" resultMap="BaseResultMap">
        select distinct a.id,a,update_time
        from apply_order a,apply_order_copy_to b
        where a.id = b.apply_order_id
        and b.user_id = #{userId,jdbcType=VARCHAR}
        and a.company_id = #{companyId,jdbcType=CHAR}
        and a.apply_order_type = 1
        and a.delete_status=0
        <if test="status == 2">
            <!--读取待处理申请单(待处理)-->
            and a.state = 2
        </if>
        <if test="status == 4">
            <!--读取已处理申请单(已处理)-->
            and a.state in (4,16)
        </if>
        <if test=" status != 2 &amp;&amp; status != 4">
            and a.state != 1
        </if>
        order by a.update_time desc
        limit #{pageSize,jdbcType=INTEGER} offset #{pageStart,jdbcType=INTEGER}
    </select>

    <select id="getApplyOrderByUserIdAir" resultMap="BaseResultMap">
        (select distinct ao.*
        from apply_order ao, apply_trip_info ati
        where ao.id = ati.apply_order_id
        and ao.employee_id = #{userId,jdbcType=VARCHAR}
        and ao.state = #{status,jdbcType=INTEGER}
        and ao.past_status = false
        and ati.start_city_id = #{startCityId,jdbcType=VARCHAR}
        and ati.arrival_city_id = #{arrivalCityId,jdbcType=VARCHAR}
        and ati.end_time is null
        and ao.delete_status=0
        <![CDATA[and ati.start_time = #{startTime,jdbcType=TIMESTAMP}]]>
        )
        union all
        (select distinct ao.*
        from apply_order ao, apply_trip_info ati
        where ao.id = ati.apply_order_id
        and ao.employee_id = #{userId,jdbcType=VARCHAR}
        and ao.state = #{status,jdbcType=INTEGER}
        and ao.past_status = false
        and ati.start_city_id = #{startCityId,jdbcType=VARCHAR}
        and ati.arrival_city_id = #{arrivalCityId,jdbcType=VARCHAR}
        and ati.end_time is not null
        and ao.delete_status=0
        <![CDATA[and ati.start_time <= #{startTime,jdbcType=TIMESTAMP}]]>
        <![CDATA[and ati.end_time >= #{startTime,jdbcType=TIMESTAMP}]]>
        order by ao.update_time desc)
    </select>

    <select id="getApplyOrderByUserIdHotel" resultMap="BaseResultMap">
        select distinct ao.*
        from apply_order ao, apply_trip_info ati
        where ao.id = ati.apply_order_id
        and ao.employee_id = #{userId,jdbcType=VARCHAR}
        and ao.state = #{status,jdbcType=INTEGER}
        and ao.past_status = false
        and ati.start_city_id = #{startCityId,jdbcType=VARCHAR}
        and ao.delete_status=0
        <![CDATA[and ati.end_time >= #{endTime,jdbcType=TIMESTAMP}]]>
        <![CDATA[and ati.start_time <= #{startTime,jdbcType = TIMESTAMP}]]>
        order by ao.update_time desc
    </select>

    <select id="getApplyOrderByUserId" resultMap="BaseResultMap">
        select distinct ao.*
        from apply_order ao, apply_trip_info ati
        where ao.id = ati.apply_order_id
        and ao.employee_id = #{userId,jdbcType=VARCHAR}
        and ao.state = #{status,jdbcType=INTEGER}
        and ao.past_status = false
        and ao.delete_status=0
        order by ao.update_time desc
    </select>

    <select id="getApplyOrderTotalByCompanyId" resultType="java.lang.Integer">
        select count(*)
        from apply_order
        where company_id = #{companyId,jdbcType=CHAR}
        and type not in (5,16,17,20)
        and delete_status=0
        <if test="applyId != null">
            and id LIKE #{applyId,jdbcType=CHAR}
        </if>
        <if test="type != -1 &amp;&amp; type != null">
            <if test="type != 2 &amp;&amp; type != 12 &amp;&amp; type != 13">
                and type = #{type,jdbcType=INTEGER}
            </if>
            <if test="type == 2">
                and type in (2,12)
            </if>
            <if test="type == 1 or type == 2">
                and apply_order_type = 1
            </if>
            <if test="type == 5 or type == 6 or type == 7 or type == 8 or type == 9">
                and apply_order_type = 2
            </if>
            <if test="type == 4">
                and apply_order_type = 3
            </if>
            <if test="type == 10">
                and apply_order_type = 4
            </if>
            <if test="type == 11">
                and apply_order_type = 5
            </if>
            <if test="type == 12">
                and apply_order_type = 6
                and sub_type = 1
            </if>
            <if test="type == 13">
                and apply_order_type = 6
                and sub_type = 2
            </if>
            <if test="type == 14">
                and apply_order_type = 7
            </if>
            <if test="type == 15">
                and apply_order_type = 8
            </if>
<!--            <if test="type == 16">-->
<!--                and apply_order_type = 9-->
<!--            </if>-->
<!--            <if test="type == 17">-->
<!--                and apply_order_type = 10-->
<!--            </if>-->
            <if test="type == 18">
                and apply_order_type = 11
            </if>
            <if test="type == 19">
                and apply_order_type = 12
            </if>
            <if test="type == 21">
                and apply_order_type = 13
            </if>
        </if>
        <if test="state != -1 &amp;&amp; state != null">
            <if test="state == 128">
                and past_status = TRUE
            </if>
            <if test="state != 128">
                and past_status = FALSE
                and state = #{state,jdbcType=INTEGER}
            </if>
        </if>
        <if test="state != 2 &amp;&amp; state != 4 &amp;&amp; state != 16 &amp;&amp; state != 8 &amp;&amp; state != 1024 &amp;&amp; state != 2048 &amp;&amp; state != 512">
            and state in (2,4,8,16,512,1024,2048)
        </if>
        <if test="employeeList != null">
            and employee_id in
            <foreach collection="employeeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="startTime != null">
            and create_time >= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="available != null">
            <if test="available == 1">
                and past_status = FALSE
                and state = 4
            </if>
            <if test="available == 2">
                and past_status = TRUE
            </if>
        </if>
        <if test="userId != null">
            and employee_id = #{userId,jdbcType=CHAR}
        </if>
    </select>

    <select id="queryStartThirdApplyNumList" resultType="java.lang.Integer">
        select count(1)
        from apply_order
        where employee_id = #{userId,jdbcType=CHAR}
        and company_id = #{companyId,jdbcType=CHAR}
        and apply_order_type = 1
        and delete_status=0
        <if test="status == 1">
            and state = 4
            and past_status = false
        </if>
        <if test="status == 2">
            and state = 4 and past_status = true
        </if>
        <if test="status == 0">
            and state = 4
        </if>
        <if test="startTime != null">
            and create_time >= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
    </select>

    <select id="queryApplyApproveList" resultMap="BaseResultMap">
        select *
        from apply_order
        where company_id = #{companyId,jdbcType=CHAR}
        and type not in (5,16,17,20)
        and delete_status=0
        <if test="applyId != null">
            and id LIKE #{applyId,jdbcType=CHAR}
        </if>
        <if test="type != -1">
            <if test="type != 2 &amp;&amp; type != 12 &amp;&amp; type != 13">
                and type = #{type,jdbcType=INTEGER}
            </if>
            <if test="type == 2">
                and type in (2,12)
            </if>
            <if test="type == 1 or type == 2">
                and apply_order_type = 1
            </if>
            <if test="type == 5 or type == 6 or type == 7 or type == 8 or type == 9">
                and apply_order_type = 2
            </if>
            <if test="type == 4">
                and apply_order_type = 3
            </if>
            <if test="type == 10">
                and apply_order_type = 4
            </if>
            <if test="type == 11">
                and apply_order_type = 5
            </if>
            <if test="type == 12">
                and apply_order_type = 6
                and sub_type = 1
            </if>
            <if test="type == 13">
                and apply_order_type = 6
                and sub_type = 2
            </if>
            <if test="type == 14">
                and apply_order_type = 7
            </if>
            <if test="type == 15">
                and apply_order_type = 8
            </if>
<!--            <if test="type == 16">-->
<!--                and apply_order_type = 9-->
<!--            </if>-->
<!--            <if test="type == 17">-->
<!--                and apply_order_type = 10-->
<!--            </if>-->
            <if test="type == 18">
                and apply_order_type = 11
            </if>
            <if test="type == 19">
                and apply_order_type = 12
            </if>
            <if test="type == 21">
                and apply_order_type = 13
            </if>
        </if>
        <if test="state != -1 &amp;&amp; state != null">
            <if test="state == 128">
                and past_status = TRUE
            </if>
            <if test="state != 128">
                and past_status = FALSE
                and state = #{state,jdbcType=INTEGER}
            </if>
        </if>
        <if test="state != 2 &amp;&amp; state != 4 &amp;&amp; state != 16 &amp;&amp; state != 8 &amp;&amp; state != 1024 &amp;&amp; state != 2048 &amp;&amp; state != 512">
            and state in (2,4,8,16,512,1024,2048)
        </if>
        <if test="employeeList != null">
            and employee_id in
            <foreach collection="employeeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="startTime != null">
            and create_time >= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="available != null">
            <if test="available == 1">
                and past_status = FALSE
                and state = 4
            </if>
            <if test="available == 2">
                and past_status = TRUE
            </if>
        </if>
        <if test="userId != null">
            and employee_id = #{userId,jdbcType=CHAR}
        </if>
        order by create_time desc
        limit #{pageSize,jdbcType=INTEGER} offset #{pageStart,jdbcType=INTEGER}
    </select>

    <select id="queryApplyApproveExportList" resultMap="ExportResultMap">
        select ao.*,
        ati.id trip_id,
        ati.apply_order_id trip_apply_order_id,
        ati.create_time trip_create_time,
        ati.type trip_type,
        ati.start_time trip_start_time,
        ati.end_time trip_end_time,
        ati.start_city_id trip_start_city_id,
        ati.arrival_city_id trip_arrival_city_id,
        ati.state trip_state,
        ati.trip_applicate_current_id trip_trip_applicate_current_id,
        ati.update_time trip_update_time,
        ati.estimated_amount trip_estimated_amount,
        ati.start_city_name trip_start_city_name,
        ati.arrival_city_name trip_arrival_city_name,
        ati.title trip_title,
        ati.content trip_content,
        ati.order_time trip_order_time,
        ati.person_count trip_person_count,
        ati.price_structure trip_price_structure,
        ati.order_reason trip_order_reason,
        ati.address_info trip_address_info,
        ati.cost_attribution_name trip_cost_attribution_name,
        ati.mall_list trip_mall_list,
        ati.order_reason_desc trip_order_reason_desc,
        ati.back_start_time trip_back_start_time,
        ati.back_end_time trip_back_end_time,
        ati.trip_type trip_trip_type,
        ati.trip_content trip_trip_content,
        atg.contact_info guest_contact_info,
        aol.sponsor_id log_sponsor_id,
        aol.receiver_id log_receiver_id,
        aol.check_reason log_check_reason,
        aol.create_time log_create_time,
        aol.action log_action
        from apply_order ao
        left join apply_trip_info ati on ao.id = ati.apply_order_id
        left join apply_trip_guest atg on ao.id = atg.apply_order_id
        left join apply_order_log aol on ao.id = aol.apply_order_id and aol.action != 0
        where ao.company_id = #{companyId,jdbcType=CHAR}
        and ao.type not in (5,16,17,20)
        and ao.delete_status=0
        <if test="applyId != null">
            and ao.id LIKE #{applyId,jdbcType=CHAR}
        </if>
        <if test="thirdId != null">
            and ao.third_id = #{thirdId,jdbcType=CHAR}
        </if>
        <if test="type != -1">
            <if test="type != 2 &amp;&amp; type != 12 &amp;&amp; type != 13 &amp;&amp; type != 1">
                and ao.type = #{type,jdbcType=INTEGER}
            </if>
            <if test="type == 1">
                and ao.type in (1,23)
                and ao.apply_order_type in (1,15)
            </if>
            <if test="type == 2">
                and ao.type in (2,12)
                and ao.apply_order_type = 1
            </if>
            <if test="type == 5 or type == 6 or type == 7 or type == 8 or type == 9">
                and ao.apply_order_type = 2
            </if>
            <if test="type == 4">
                and ao.apply_order_type = 3
            </if>
            <if test="type == 10">
                and ao.apply_order_type = 4
            </if>
            <if test="type == 11">
                and ao.apply_order_type = 5
            </if>
            <if test="type == 12">
                and ao.apply_order_type = 6
                and ao.sub_type = 1
            </if>
            <if test="type == 13">
                and ao.apply_order_type = 6
                and ao.sub_type = 2
            </if>
            <if test="type == 14">
                and ao.apply_order_type = 7
            </if>
            <if test="type == 15">
                and ao.apply_order_type = 8
            </if>
<!--            <if test="type == 16">-->
<!--                and ao.apply_order_type = 9-->
<!--            </if>-->
<!--            <if test="type == 17">-->
<!--                and ao.apply_order_type = 10-->
<!--            </if>-->
            <if test="type == 18">
                and ao.apply_order_type = 11
            </if>
            <if test="type == 19">
                and ao.apply_order_type = 12
            </if>
            <if test="type == 21">
                and ao.apply_order_type = 13
            </if>
            <if test="type == 24">
                and ao.apply_order_type = 16
            </if>
        </if>
        <if test="state != -1 &amp;&amp; state != null">
            <if test="state == 128">
                and ao.past_status = TRUE
            </if>
            <if test="state != 128">
                and ao.past_status = FALSE
                and ao.state = #{state,jdbcType=INTEGER}
            </if>
        </if>
        <if test="state != 2 &amp;&amp; state != 4 &amp;&amp; state != 16 &amp;&amp; state != 8 &amp;&amp; state != 1024 &amp;&amp; state != 2048 &amp;&amp; state != 512">
            and ao.state in (2,4,8,16,512,1024,2048)
        </if>
        <if test="employeeList != null">
            and ao.employee_id in
            <foreach collection="employeeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="startTime != null">
            and ao.create_time >= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and ao.create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
        order by ao.create_time desc, ati.id asc, atg.id asc, aol.sort desc, aol.create_time desc
    </select>

    <select id="queryStartThirdApplyOrderList" resultMap="BaseResultMap">
        select *
        from apply_order
        where employee_id = #{userId,jdbcType=CHAR}
        and company_id = #{companyId,jdbcType=CHAR}
        and apply_order_type = 1
        and delete_status=0
        <if test="status == 1">
            and state = 4
            and past_status = false
        </if>
        <if test="status == 2">
            and state = 4 and past_status = true
        </if>
        <if test="status == 0">
            and state = 4
        </if>
        <if test="startTime != null">
            and create_time >= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
        order by update_time desc
        limit #{pageSize,jdbcType=INTEGER} offset #{pageStart,jdbcType=INTEGER}
    </select>

    <select id="getApplyTripExportTotal" resultType="java.lang.Integer">
        select num from
        (select apply_order_id,count(*) as num from apply_trip_info a,apply_order b
        where a.apply_order_id = b.id
        and b.company_id=#{companyId,jdbcType=CHAR}
        and b.apply_order_type = 1
        and b.delete_status=0
        <if test="applyId != null">
            and b.id LIKE #{applyId,jdbcType=CHAR}
        </if>
        <if test="type != -1">
            <if test="type != 2 &amp;&amp; type != 12 &amp;&amp; type != 13">
                and b.type = #{type,jdbcType=INTEGER}
            </if>
            <if test="type == 2">
                and b.type in (2,12)
            </if>
        </if>
        <if test="state != -1 &amp;&amp; state != null">
            and b.state = #{state,jdbcType=INTEGER}
        </if>
        <if test="state != 2 &amp;&amp; state != 4 &amp;&amp; state != 16 &amp;&amp; state != 8 &amp;&amp; state != 1024 &amp;&amp; state != 2048 ">
            and b.state in (2,4,8,16,1024,2048)
        </if>
        <if test="employeeList != null">
            and b.employee_id in
            <foreach collection="employeeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="startTime != null">
            and b.create_time >= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and b.create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
        group by apply_order_id)
        as t order by num desc limit 1
    </select>

    <select id="queryApproveList" resultMap="BaseResultMap">
        select a.*
        from apply_order a
        left join apply_approver_map m on a.id = m.apply_id
        where  a.company_id = #{companyId,jdbcType=CHAR}
        and m.approver_id = #{approverId,jdbcType=CHAR}
        and a.type != 5
        and a.delete_status=0
        <if test="applyTypeList!=null">
            and a.type not in
            <foreach collection="applyTypeList" item="item" index="index"
                     open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="status == 1">
            <!--待处理-->
            and (a.state = 2 and a.approver_id = #{approverId})
        </if>
        <if test="status == 2">
            <!--已处理-->
            and (
              a.state in (4,8,16,256,512,1024,2048)
              or (a.state = 64 and a.approver_id != #{approverId})
              or (a.state = 2 and a.approver_id != #{approverId})
            )
        </if>
        <if test="status == 3">
            <!--全部-->
            and (
                a.state in (2,4,8,16,256,512,1024,2048)
                or (a.state = 64 and a.approver_id != #{approverId})
            )
            <if test="name != null">
                and a.applicant_name like #{name,jdbcType=CHAR}
            </if>
        </if>
        <if test="applyType != null and applyType > 0 ">
            <if test="applyType == 12">
                and a.apply_order_type = 6
            </if>
            <if test="applyType == 6">
                and (a.type = 6
                or a.type = 7)
            </if>
            <if test="applyType != 6 &amp;&amp; applyType != 2 &amp;&amp; applyType != 12">
                and a.type = #{applyType,jdbcType=INTEGER}
            </if>
            <if test="applyType == 2">
                and a.type in (2,12)
            </if>
        </if>
        order by a.create_time desc
        limit #{pageSize,jdbcType=INTEGER} offset #{pageStart,jdbcType=INTEGER}
    </select>

    <select id="getCountByApproveList" resultType="java.lang.Integer">
        select count(1)
        from apply_order a
        left join apply_approver_map m on a.id = m.apply_id
        where a.company_id = #{companyId,jdbcType=CHAR}
        and m.approver_id = #{approverId,jdbcType=CHAR}
        and a.type != 5
        and a.delete_status=0
        <if test="applyTypeList!=null">
            and a.type not in
            <foreach collection="applyTypeList" item="item" index="index"
                     open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="status == 1">
            <!--待处理-->
            and (a.state = 2 and a.approver_id = #{approverId})
        </if>
        <if test="status == 2">
            <!--已处理-->
            and (
                a.state in (4,8,16,256,512,1024,2048)
                or (a.state = 64 and a.approver_id != #{approverId})
                or (a.state = 2 and a.approver_id != #{approverId})
            )
        </if>
        <if test="status == 3">
            <!--全部-->
            and (
                a.state in (2,4,8,16,256,512,1024,2048)
                or (a.state = 64 and a.approver_id != #{approverId})
            )
            <if test="name != null">
                and a.applicant_name like #{name,jdbcType=CHAR}
            </if>
        </if>
        <if test="applyType != null and applyType > 0 ">
            <if test="applyType == 12">
                and a.apply_order_type = 6
            </if>
            <if test="applyType == 6">
                and (a.type = 6
                or a.type = 7)
            </if>
            <if test="applyType != 6 &amp;&amp; applyType != 2 &amp;&amp; applyType != 12">
                and a.type = #{applyType,jdbcType=INTEGER}
            </if>
            <if test="applyType == 2">
                and a.type in (2,12)
            </if>
        </if>
    </select>

    <select id="getDraftNumByUserId" resultType="java.lang.Integer">
        select count(1)
        from apply_order
        where employee_id = #{userId,jdbcType=CHAR}
        and state = #{status,jdbcType=INTEGER}
        and company_id = #{companyId,jdbcType=CHAR}
        and apply_order_type in (1, 5, 7)
        and delete_status=0
    </select>

    <select id="queryDraftListByUserId" resultMap="BaseResultMap">
        select *
        from apply_order
        where employee_id = #{userId,jdbcType=CHAR}
        and state = #{status,jdbcType=INTEGER}
        and company_id = #{companyId,jdbcType=CHAR}
        and apply_order_type in (1, 5, 7)
        and delete_status=0
        order by update_time desc
        limit #{pageSize,jdbcType=INTEGER} offset #{pageStart,jdbcType=INTEGER}
    </select>

    <select id="getCCNumByApplyId" resultMap="BaseResultMap">
        select distinct a.id,a.create_time
        from apply_order a,apply_order_copy_to b
        where a.id = b.apply_order_id
        and b.user_id = #{userId,jdbcType=VARCHAR}
        and a.company_id = #{companyId,jdbcType=CHAR}
        and a.state != 1
        and a.type != 5
        and a.delete_status=0
        <if test="applyTypeList!=null">
            and a.type not in
            <foreach collection="applyTypeList" item="item" index="index"
                     open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="name != null">
            and a.applicant_name LIKE #{name,jdbcType=CHAR}
        </if>
        <if test="applyType != null and applyType > 0 ">
            <if test="applyType == 6">
                and (a.type = 6
                or a.type = 7)
            </if>
            <if test="applyType != 6">
                and a.type = #{applyType,jdbcType=INTEGER}
            </if>
        </if>
        order by create_time desc
    </select>

    <select id="getCCListByApplyId" resultMap="BaseResultMap">
        select distinct a.id,a.create_time
        from apply_order a,apply_order_copy_to b
        where a.id = b.apply_order_id
        and b.user_id = #{userId,jdbcType=VARCHAR}
        and a.company_id = #{companyId,jdbcType=CHAR}
        and a.state != 1
        and a.type != 5
        and a.delete_status=0
        <if test="applyTypeList!=null">
            and a.type not in
            <foreach collection="applyTypeList" item="item" index="index"
                     open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="name != null">
            and a.applicant_name LIKE #{name,jdbcType=CHAR}
        </if>
        <if test="applyType != null and applyType > 0 ">
            <if test="applyType == 6">
                and (a.type = 6
                or a.type = 7)
            </if>
            <if test="applyType != 6">
                and a.type = #{applyType,jdbcType=INTEGER}
            </if>
        </if>
        order by a.create_time desc
        limit #{pageSize,jdbcType=INTEGER} offset #{pageStart,jdbcType=INTEGER}
    </select>

    <update id="updateApproverIdAndStatus">
        update apply_order
        set state = #{status,jdbcType=INTEGER},
        approver_id = #{approverId,jdbcType=CHAR},
        update_time = #{time,jdbcType=TIMESTAMP},
        <if test="realPrice != null">
            real_price = #{realPrice,jdbcType=NUMERIC},
        </if>
        current_log_id = #{currentLogId,jdbcType=INTEGER},
        repulse_desc = #{repulseDesc,jdbcType=VARCHAR}
        where id = #{id,jdbcType=CHAR}
    </update>

    <select id="queryApplyOrderById" resultMap="BaseResultMap">
        select *
        from apply_order
        where id = #{id,jdbcType=CHAR}
    </select>

    <select id="queryApplyOrderDetail" resultMap="BaseResultMap">
        select ao.* from apply_order ao
        left join apply_trip_applicate at
        on ao."id" = at.apply_id
        where at.order_id = #{orderId,jdbcType=CHAR}
        and at.apply_id = #{applyId,jdbcType=CHAR}
        and at."action" = 1 and ao.state = 4
        and ao.apply_order_type = 1
    </select>

    <select id="queryUnOvertimeApplyNum" resultType="java.lang.Long">
        select count(1)
        from apply_order
        where state = 2
        and type in (1,5,6,7,8,9)
        and apply_order_type = 2
        and delete_status=0
        <![CDATA[and overtime <= #{overTime,jdbcType=TIMESTAMP}]]>
    </select>

    <select id="queryUnOvertimeApplyList" resultMap="BaseResultMap">
        select *
        from apply_order
        where state = 2
        and type in (1,5,6,7,8,9)
        and apply_order_type = 2
        and delete_status=0
        <![CDATA[and overtime <= #{overTime,jdbcType=TIMESTAMP}]]>
        order by update_time desc
        limit #{pageSize,jdbcType=INTEGER} offset #{pageStart,jdbcType=INTEGER}
    </select>

    <select id="queryCenterApply" resultMap="BaseResultMap">
        select ao.*
        from apply_order ao
        left join apply_trip_applicate ata
        on ao."id" = ata.apply_id
        where ao."id" = #{applyId,jdbcType=CHAR}
        and ata.order_id = #{orderId,jdbcType=CHAR}
	    and ata.apply_id = #{applyId,jdbcType=CHAR}
	    and ao.apply_order_type = 2
    </select>

    <update id="setCenterStatus">
        update apply_order
        set state = #{status,jdbcType=INTEGER},
        update_time = #{time,jdbcType=TIMESTAMP},
        current_log_id = #{currentLogId,jdbcType=INTEGER}
        where id = #{id,jdbcType=CHAR}
        and apply_order_type = #{applyOrderType,jdbcType=INTEGER}
    </update>

    <select id="getTeduAirOrTranValidApplyList" resultMap="BaseResultMap">
        (select *
        from apply_order where id in (
        select a.id
        from apply_order a,apply_trip_info t
        where a.id = t.apply_order_id
        and a.employee_id = #{userId,jdbcType=CHAR}
        and a.company_id = #{companyId,jdbcType=CHAR}
        and a.state = 4
        and t.type = #{tripType,jdbcType=INTEGER}
        and a.apply_order_type = 1
        and t.end_time is null
        and a.delete_status=0
        <![CDATA[and t.start_time <= #{maxTime,jdbcType=TIMESTAMP}]]>
        <![CDATA[and t.start_time >= #{minTime,jdbcType = TIMESTAMP}]]>
        and t.state = 1
        ))
        union all
        (select *
        from apply_order where id in (
        select a.id
        from apply_order a,apply_trip_info t
        where a.id = t.apply_order_id
        and a.employee_id = #{userId,jdbcType=CHAR}
        and a.company_id = #{companyId,jdbcType=CHAR}
        and a.state = 4
        and t.type = #{tripType,jdbcType=INTEGER}
        and a.apply_order_type = 1
        and t.end_time is not null
        and a.delete_status=0
        <![CDATA[and t.end_time >= #{minTime,jdbcType=TIMESTAMP}]]>
        <![CDATA[and t.start_time <= #{minTime,jdbcType = TIMESTAMP}]]>
        and t.state = 1
        ))
    </select>

    <select id="getTeduHotelValidApplyList" resultMap="BaseResultMap">
        select *
        from apply_order where id in (
        select a.id
        from apply_order a,apply_trip_info t
        where a.id = t.apply_order_id
        and a.state = 4
        and a.apply_order_type = 1
        and a.employee_id = #{userId,jdbcType=CHAR}
        and a.company_id = #{companyId,jdbcType=CHAR}
        and t.type = 11
        and a.delete_status=0
        <![CDATA[and t.end_time >= #{maxTime,jdbcType=TIMESTAMP}]]>
        <![CDATA[and t.start_time <= #{minTime,jdbcType = TIMESTAMP}]]>
        and t.state = 1
        )
    </select>

    <select id="getTeduTaxiValidApplyList" resultMap="BaseResultMap">
        select *
        from apply_order where id in (
        select a.id
        from apply_order a,apply_trip_info t
        where a.id = t.apply_order_id
        and a.state = 4
        and a.apply_order_type = 1
        and a.type = 2
        and a.employee_id = #{userId,jdbcType=CHAR}
        and a.company_id = #{companyId,jdbcType=CHAR}
        and t.type = 3
        and t.end_time > #{timeMinusOneDay,jdbcType=TIMESTAMP}
        and t.start_time &lt;= #{time,jdbcType = TIMESTAMP}
        and t.state = 1
        and a.delete_status=0
        )
    </select>

    <update id="updateApplicantNameById">
        update apply_order
        set applicant_name = #{applicantName,jdbcType=VARCHAR}
        where id = #{id,jdbcType=CHAR}
    </update>

    <select id="queryUnOvertimeDinnerApplyNum" resultType="java.lang.Long">
        select count(1)
        from apply_order ao
        left join apply_trip_info ati
        on ao.id = ati.apply_order_id
        where ao.state = 2
        and ao.type = 5
        and ao.apply_order_type = 2
        and ao.delete_status=0
        <![CDATA[and ati.end_time <= #{overTime,jdbcType=TIMESTAMP}]]>
    </select>

    <select id="queryUnOvertimeDinnerApplyList" resultMap="BaseResultMap">
        select ao.*
        from apply_order ao
        left join apply_trip_info ati
        on ao.id = ati.apply_order_id
        where ao.state = 2
        and ao.type = 5
        and ao.apply_order_type = 2
        and ao.delete_status=0
        <![CDATA[and ati.end_time <= #{overTime,jdbcType=TIMESTAMP}]]>
        order by ao.update_time desc
        limit #{pageSize,jdbcType=INTEGER} offset #{pageStart,jdbcType=INTEGER}
    </select>

    <select id="queryUnOvertimeMallApplyNum" resultType="java.lang.Long">
        select count(1)
        from apply_order
        where state = 4
        and type = 4
        and apply_order_type = 3
        and delete_status=0
        <![CDATA[and update_time <= #{overTime,jdbcType=TIMESTAMP}]]>
    </select>

    <select id="queryUnOvertimeMallApplyList" resultMap="BaseResultMap">
        select *
        from apply_order
        where state = 4
        and type = 4
        and apply_order_type = 3
        and delete_status=0
        <![CDATA[and update_time <= #{overTime,jdbcType=TIMESTAMP}]]>
        order by update_time desc
        limit #{pageSize,jdbcType=INTEGER} offset #{pageStart,jdbcType=INTEGER}
    </select>
    <select id="getIntlAirValidApplyList" resultMap="BaseResultMap">
        (select *
        from apply_order where id in (
        select a.id
        from apply_order a,apply_trip_info t
        where a.id = t.apply_order_id
        and a.employee_id = #{userId,jdbcType=CHAR}
        and a.company_id = #{companyId,jdbcType=CHAR}
        and a.state = 4
        and t.type = 40
        and a.apply_order_type = 1
        and a.delete_status=0
        <if test="cityFlag == 0">
            and ((t.start_city_id IN
            <foreach collection="startCityIds" item="startCityId" index="index"
                     open="(" close=")" separator=",">
                #{startCityId}
            </foreach>
            and t.arrival_city_id IN
            <foreach collection="arrivalCityIds" item="arrivalCityId" index="index"
                     open="(" close=")" separator=",">
                #{arrivalCityId}
            </foreach>)
            or (t.start_city_id IN
            <foreach collection="arrivalCityIds" item="arrivalCityId" index="index"
                     open="(" close=")" separator=",">
                #{arrivalCityId}
            </foreach>
            and t.arrival_city_id IN
            <foreach collection="startCityIds" item="startCityId" index="index"
                     open="(" close=")" separator=",">
                #{startCityId}
            </foreach>))
        </if>
        and t.end_time is null
        <![CDATA[and t.start_time <= #{maxTime,jdbcType=TIMESTAMP}]]>
        <![CDATA[and t.start_time >= #{minTime,jdbcType=TIMESTAMP}]]>
        and t.state = 1
        <if test="tripType !=null and tripType == 2">
            and t.back_end_time is null
            <![CDATA[and t.back_start_time <= #{backMaxTime,jdbcType=TIMESTAMP}]]>
            <![CDATA[and t.back_start_time >= #{backMinTime,jdbcType=TIMESTAMP}]]>
        </if>
        ))
        union all
        (select *
        from apply_order where id in (
        select a.id
        from apply_order a,apply_trip_info t
        where a.id = t.apply_order_id
        and a.employee_id = #{userId,jdbcType=CHAR}
        and a.company_id = #{companyId,jdbcType=CHAR}
        and a.state = 4
        and t.type = 40
        and a.apply_order_type = 1
        and a.delete_status=0
        <if test="cityFlag == 0">
            and ((t.start_city_id IN
            <foreach collection="startCityIds" item="startCityId" index="index"
                     open="(" close=")" separator=",">
                #{startCityId}
            </foreach>
            and t.arrival_city_id IN
            <foreach collection="arrivalCityIds" item="arrivalCityId" index="index"
                     open="(" close=")" separator=",">
                #{arrivalCityId}
            </foreach>)
            or (t.start_city_id IN
            <foreach collection="arrivalCityIds" item="arrivalCityId" index="index"
                     open="(" close=")" separator=",">
                #{arrivalCityId}
            </foreach>
            and t.arrival_city_id IN
            <foreach collection="startCityIds" item="startCityId" index="index"
                     open="(" close=")" separator=",">
                #{startCityId}
            </foreach>))
        </if>
        and t.end_time is not null
        <![CDATA[and t.end_time >= #{minTime,jdbcType=TIMESTAMP}]]>
        <![CDATA[and t.start_time <= #{minTime,jdbcType=TIMESTAMP}]]>
        and t.state = 1
        <if test="tripType !=null and tripType == 2">
            and t.back_end_time is not null
            <![CDATA[and t.back_end_time >= #{backMinTime,jdbcType=TIMESTAMP}]]>
            <![CDATA[and t.back_start_time <= #{backMinTime,jdbcType=TIMESTAMP}]]>
        </if>
        ))
    </select>

    <update id="setThirdStatus">
        update apply_order
        set state = #{status,jdbcType=INTEGER},
        update_time = #{time,jdbcType=TIMESTAMP}
        where third_id = #{id,jdbcType=CHAR}
        and company_id = #{companyId,jdbcType=CHAR}
    </update>

    <select id="queryHistoryTravelNum" resultMap="BaseResultMap">
        select distinct t1.* from apply_order t1
        left join apply_approver_map t2 on t1.id = t2.apply_id
        where t1.employee_id = #{userId,jdbcType=CHAR}
        <if test="type == null or type == 1">
            and t1.apply_order_type = 1
        </if>
        <if test="type == 2">
            and t1.apply_order_type = 4
            and t1.type = 10
        </if>
        <if test="type == 3">
            and t1.apply_order_type = 5
            and t1.type = 11
        </if>
        <if test="type == 4">
            and t1.apply_order_type = 1
            and t1.type = 12
        </if>
        <if test="type == 5">
            and t1.apply_order_type = 7
            and t1.type = 14
        </if>
        and t1.state in(2,4)
        and t1.company_id = #{companyId,jdbcType=CHAR}
        and t2.approver_id = #{approver,jdbcType=CHAR}
        and t1.delete_status=0
        <![CDATA[and t1.create_time > #{startTime,jdbcType=TIMESTAMP}]]>
        order by t1.create_time desc
    </select>

    <select id="queryHistoryTravelList" resultMap="BaseResultMap">
        select distinct t1.* from apply_order t1
        left join apply_approver_map t2 on t1.id = t2.apply_id
        where t1.employee_id = #{userId,jdbcType=CHAR}
        <if test="type == null or type == 1">
            and t1.apply_order_type = 1
        </if>
        <if test="type == 2">
            and t1.apply_order_type = 4
            and t1.type = 10
        </if>
        <if test="type == 3">
            and t1.apply_order_type = 5
            and t1.type = 11
        </if>
        <if test="type == 4">
            and t1.apply_order_type = 1
            and t1.type = 12
        </if>
        <if test="type == 5">
            and t1.apply_order_type = 7
            and t1.type = 14
        </if>
        and t1.state in(2,4)
        and t1.company_id = #{companyId,jdbcType=CHAR}
        and t2.approver_id = #{approver,jdbcType=CHAR}
        and t1.delete_status=0
        <![CDATA[and t1.create_time > #{startTime,jdbcType=TIMESTAMP}]]>
        order by t1.create_time desc
        limit #{pageSize,jdbcType=INTEGER} offset #{pageStart,jdbcType=INTEGER}
    </select>

    <select id="queryApplyOrderByUserId" resultMap="BaseResultMap">
        select * from apply_order
        where employee_id = #{userId,jdbcType=CHAR}
        and company_id = #{companyId,jdbcType=CHAR}
        and id = #{applyId,jdbcType=CHAR}
        and delete_status=0
    </select>

    <select id="queryUnOvertimeFenbeiCouponApplyNum" resultType="java.lang.Long">
        select count(*) from apply_order t1
        left join apply_trip_info t2
        on t1.id = t2.apply_order_id
        where
        t1.apply_order_type = 4
        and t1."type" = 10
        and t1.state = 2
        and t1.delete_status=0
        <![CDATA[and t2.end_time <= #{overTime,jdbcType=TIMESTAMP}]]>
    </select>

    <select id="queryUnOvertimeFenbeiCouponApplyList" resultMap="BaseResultMap">
        select t1.* from apply_order t1
        left join apply_trip_info t2
        on t1.id = t2.apply_order_id
        where
        t1.apply_order_type = 4
        and t1."type" = 10
        and t1.state = 2
        and t1.delete_status=0
        <![CDATA[and t2.end_time <= #{overTime,jdbcType=TIMESTAMP}]]>
        order by t1.update_time desc
        limit #{pageSize,jdbcType=INTEGER} offset #{pageStart,jdbcType=INTEGER}
    </select>

    <update id="setApplyStatus">
        update apply_order
        set state = #{status,jdbcType=INTEGER},
        update_time = #{time,jdbcType=TIMESTAMP},
        current_log_id = #{currentLogId,jdbcType=INTEGER}
        where id = #{id,jdbcType=CHAR}
        and apply_order_type = #{applyOrderType,jdbcType=INTEGER}
    </update>

    <select id="getValidTaxiCountByUserId" resultType="java.lang.Integer">
        select count(1)
        from apply_order
        where type = 12
        and state = 4
        and delete_status=0
        and past_status = FALSE
        and employee_id = #{userId,jdbcType=CHAR}
    </select>

    <select id="queryValidTaxiByUserId" resultMap="BaseResultMap">
        select t1.*
        from apply_order t1
        left join apply_trip_info t2
        on t1.id = t2.apply_order_id
        where t1.type = 12
        and t1.state = 4
        and t1.delete_status=0
        and t1.past_status = FALSE
        and employee_id = #{userId,jdbcType=CHAR}
        order by t2.start_time desc,t1.update_time desc
        limit #{pageSize,jdbcType=INTEGER} offset #{pageStart,jdbcType=INTEGER}
    </select>

    <select id="queryUnPastTaxiApplyNum" resultType="java.lang.Long">
        select count(1)
        from apply_order
        where past_status = false
        and state = 4
        and apply_order_type = 1
        and type = 12
        and delete_status=0
    </select>

    <select id="queryUnPastTaxiApplyList" resultMap="BaseResultMap">
        select *
        from apply_order
        where past_status = false
        and state = 4
        and apply_order_type = 1
        and type = 12
        and delete_status=0
        order by update_time desc
        limit #{pageSize,jdbcType=INTEGER} offset #{pageStart,jdbcType=INTEGER}
    </select>

    <select id="queryUnOvertimeRefundChangeApplyNum" resultType="java.lang.Long">
        select count(1)
        from apply_order
        where state = 2
        and apply_order_type = 6
        and delete_status=0
        <![CDATA[and overtime <= #{overTime,jdbcType=TIMESTAMP}]]>
    </select>

    <select id="queryUnOvertimeRefundChangeApplyList" resultMap="BaseResultMap">
        select *
        from apply_order
        where state = 2
        and apply_order_type = 6
        and delete_status=0
        <![CDATA[and overtime <= #{overTime,jdbcType=TIMESTAMP}]]>
        order by update_time desc
        limit #{pageSize,jdbcType=INTEGER} offset #{pageStart,jdbcType=INTEGER}
    </select>

    <select id="getCountByEmployeeId" resultType="java.lang.Integer">
        select count(1) from apply_order t1
        left join apply_trip_info t2
        on t1."id" = t2.apply_order_id
        where t1."state" = 4
        and t1.apply_order_type = 1
        and t1.past_status = FALSE
        and t1.employee_id = #{userId,jdbcType=CHAR}
        and t1.company_id = #{companyId,jdbcType=CHAR}
        and t2."type" = #{type,jdbcType=INTEGER}
        and t1.delete_status=0
        <if test="type == 7 or type == 15">
            and t2.start_time = #{startTime,jdbcType=TIMESTAMP}
            <if test="cityFlag == 0">
                and t2.start_city_id = #{startCityId,jdbcType=CHAR}
                and t2.arrival_city_id = #{arrivalCityId,jdbcType=CHAR}
            </if>
        </if>
        <if test="type == 11">
                <![CDATA[and t2.start_time <= #{startTime,jdbcType=TIMESTAMP}]]>
                <![CDATA[and t2.end_time >= #{endTime,jdbcType=TIMESTAMP}]]>
            <if test="cityFlag == 0">
                and t2.start_city_id = #{startCityId,jdbcType=CHAR}
            </if>
        </if>
    </select>

    <select id="getTypeApplyByEmployeeId" resultMap="BaseResultMap">
        select distinct t1.* from apply_order t1
        left join apply_trip_info t2
        on t1."id" = t2.apply_order_id
        where t1."state" = 4
        and t1.apply_order_type = 1
        and t1.past_status = FALSE
        and t1.employee_id = #{userId,jdbcType=CHAR}
        and t1.company_id = #{companyId,jdbcType=CHAR}
        and t2."type" = #{type,jdbcType=INTEGER}
        and t1.delete_status=0
        <if test="type == 7 or type == 15">
            and t2.start_time = #{startTime,jdbcType=TIMESTAMP}
            <if test="cityFlag == 0">
                and t2.start_city_id = #{startCityId,jdbcType=CHAR}
                and t2.arrival_city_id = #{arrivalCityId,jdbcType=CHAR}
            </if>
        </if>
        <if test="type == 11">
            <![CDATA[and t2.start_time <= #{startTime,jdbcType=TIMESTAMP}]]>
            <![CDATA[and t2.end_time >= #{endTime,jdbcType=TIMESTAMP}]]>
            <if test="cityFlag == 0">
                and t2.start_city_id = #{startCityId,jdbcType=CHAR}
            </if>
        </if>
    </select>

    <select id="getTeduIntlAirValidApplyList" resultMap="BaseResultMap">
        (select *
        from apply_order where id in (
        select a.id
        from apply_order a,apply_trip_info t
        where a.id = t.apply_order_id
        and a.employee_id = #{userId,jdbcType=CHAR}
        and a.company_id = #{companyId,jdbcType=CHAR}
        and a.state = 4
        and t.type = 40
        and a.apply_order_type = 1
        and t.end_time is null
        and a.delete_status=0
        <![CDATA[and t.start_time <= #{maxTime,jdbcType=TIMESTAMP}]]>
        <![CDATA[and t.start_time >= #{minTime,jdbcType=TIMESTAMP}]]>
        and t.state = 1
        <if test="tripType !=null and tripType == 2">
            and t.back_end_time is null
            <![CDATA[and t.back_start_time <= #{backMaxTime,jdbcType=TIMESTAMP}]]>
            <![CDATA[and t.back_start_time >= #{backMinTime,jdbcType=TIMESTAMP}]]>
        </if>
        ))
        union all
        (select *
        from apply_order where id in (
        select a.id
        from apply_order a,apply_trip_info t
        where a.id = t.apply_order_id
        and a.employee_id = #{userId,jdbcType=CHAR}
        and a.company_id = #{companyId,jdbcType=CHAR}
        and a.state = 4
        and t.type = 40
        and a.apply_order_type = 1
        and t.end_time is not null
        and a.delete_status=0
        <![CDATA[and t.end_time >= #{minTime,jdbcType=TIMESTAMP}]]>
        <![CDATA[and t.start_time <= #{minTime,jdbcType=TIMESTAMP}]]>
        and t.state = 1
        <if test="tripType !=null and tripType == 2">
            and t.back_end_time is not null
            <![CDATA[and t.back_end_time >= #{backMinTime,jdbcType=TIMESTAMP}]]>
            <![CDATA[and t.back_start_time <= #{backMinTime,jdbcType=TIMESTAMP}]]>
        </if>
        ))
    </select>

    <select id="queryApplyNumListByState" resultType="java.lang.Integer">
        select count(1)
        from apply_order
        where employee_id = #{userId,jdbcType=CHAR}
        and company_id = #{companyId,jdbcType=CHAR}
        and type = #{type,jdbcType=INTEGER}
        and delete_status=0
        <if test="status == 0">
            and state in (2,4,16,64)
        </if>
        <if test="status != 0">
            and state = #{status,jdbcType=INTEGER}
        </if>
    </select>

    <select id="queryApplyNumListByStateList" resultMap="BaseResultMap">
        select *
        from apply_order
        where employee_id = #{userId,jdbcType=CHAR}
        and company_id = #{companyId,jdbcType=CHAR}
        and type = #{type,jdbcType=INTEGER}
        and delete_status=0
        <if test="status == 0">
            and state in (2,4,16,64)
        </if>
        <if test="status != 0">
            and state = #{status,jdbcType=INTEGER}
        </if>
        order by create_time desc
        limit #{pageSize,jdbcType=INTEGER} offset #{pageStart,jdbcType=INTEGER}
    </select>

    <select id="getWriteoffApplyOrderTotal" resultType="java.lang.Integer">
        select count(*)
        from apply_order
        where company_id = #{companyId,jdbcType=CHAR}
        and type = 16
        and apply_order_type = 9
        and delete_status=0
        <if test="applyId != null">
            and id LIKE #{applyId,jdbcType=CHAR}
        </if>
        <if test="state != -1 &amp;&amp; state != null">
            and state = #{state,jdbcType=INTEGER}
        </if>
        <if test="state != 2 &amp;&amp; state != 4 &amp;&amp; state != 16 &amp;&amp; state != 64">
            and state in (2,4,16,64)
        </if>
        <if test="employeeList != null">
            and employee_id in
            <foreach collection="employeeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="startTime != null">
            and create_time >= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="voucherStatus != -1 &amp;&amp; voucherStatus != null">
            and voucher_status = #{voucherStatus,jdbcType=INTEGER}
        </if>
        <if test="returnTicket != -1 &amp;&amp; returnTicket != null">
            and return_ticket = #{returnTicket,jdbcType=INTEGER}
        </if>
        <if test="applicantName != null">
            and applicant_name like concat('%',#{applicantName,jdbcType=CHAR},'%')
        </if>
    </select>

    <select id="queryWriteoffApplyList" resultMap="BaseResultMap">
        select *
        from apply_order
        where company_id = #{companyId,jdbcType=CHAR}
        and type = 16
        and apply_order_type = 9
        and delete_status=0
        <if test="applyId != null">
            and id LIKE #{applyId,jdbcType=CHAR}
        </if>
        <if test="state != -1 &amp;&amp; state != null">
            and state = #{state,jdbcType=INTEGER}
        </if>
        <if test="state != 2 &amp;&amp; state != 4 &amp;&amp; state != 16 &amp;&amp; state != 64">
            and state in (2,4,16,64)
        </if>
        <if test="employeeList != null">
            and employee_id in
            <foreach collection="employeeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="startTime != null">
            and create_time >= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="voucherStatus != -1 &amp;&amp; voucherStatus != null">
            and voucher_status = #{voucherStatus,jdbcType=INTEGER}
        </if>
        <if test="returnTicket != -1 &amp;&amp; returnTicket != null">
            and return_ticket = #{returnTicket,jdbcType=INTEGER}
        </if>
        <if test="applicantName != null">
            and applicant_name like concat('%',#{applicantName,jdbcType=CHAR},'%')
        </if>
        order by create_time desc
        limit #{pageSize,jdbcType=INTEGER} offset #{pageStart,jdbcType=INTEGER}
    </select>

    <select id="queryWriteoffApply" resultMap="BaseResultMap">
        select *
        from apply_order
        where company_id = #{companyId,jdbcType=CHAR}
        and type = 16
        and apply_order_type = 9
        and id = #{applyId,jdbcType=CHAR}
    </select>

    <select id="queryTravelUserTotal" resultType="java.lang.Integer">
        select
        count(*)
        FROM
        (
        select
        distinct on(p.employee_id) p.employee_id
        FROM apply_order p,apply_travel_time t
        WHERE
        p.id = t.apply_order_id
        and p.type = 1
        and p."state" in(4,1024)
        AND p.apply_order_type = 1
        and p.delete_status=0
        <if test="statisticsScope == 1 and companyId !=null and companyId != ''">
            and  p.company_id= #{companyId}
        </if>

        <if test="statisticsScope == 2 and statisticsId != null and statisticsId !=''">
            and p.cost_attribution_category= 1
            and p.cost_attribution_id =#{statisticsId}
        </if>

        <if test="statisticsScope == 3 and statisticsId != null and statisticsId !=''">
            and p.cost_attribution_category= 2
            and p.cost_attribution_id =#{statisticsId}
        </if>
        <![CDATA[and t.travel_time >= #{startDate,jdbcType=BIGINT}]]>
        <![CDATA[and t.travel_time <= #{endDate,jdbcType=BIGINT}]]>
        )as b
    </select>

    <select id="queryTravelUser" resultType="java.lang.String">
        select
        b.employee_id
        FROM
        (
        select
        distinct on(p.employee_id) p.employee_id,
        t.id
        FROM apply_order p,apply_travel_time t
        WHERE
        p.id = t.apply_order_id
        and p.type = 1
        and p."state" in(4,1024)
        AND p.apply_order_type = 1
        and p.delete_status=0
        <if test="statisticsScope == 1 and companyId !=null and companyId != ''">
            and  p.company_id= #{companyId}
        </if>

        <if test="statisticsScope == 2 and statisticsId != null and statisticsId !=''">
            and p.cost_attribution_category= 1
            and p.cost_attribution_id =#{statisticsId}
        </if>

        <if test="statisticsScope == 3 and statisticsId != null and statisticsId !=''">
            and p.cost_attribution_category= 2
            and p.cost_attribution_id =#{statisticsId}
        </if>
        <![CDATA[and t.travel_time >= #{startDate,jdbcType=BIGINT}]]>
        <![CDATA[and t.travel_time <= #{endDate,jdbcType=BIGINT}]]>
        )as b
        order by b.id desc
        LIMIT ${pageSize}
        offset
        ${pageIndex}
    </select>
    <select id="queryTravelCompanyByAttributionIsNull" resultType="java.lang.String">
        SELECT
        P.company_id
        FROM
	    apply_order P
        WHERE
	    P.TYPE = 1
	    AND P.cost_attribution_category = 1
	    AND P.cost_attribution_id IS NULL
	    AND P.apply_order_type = 1
	    AND P.STATE IN ( 2, 4, 1024 )
	    and P.delete_status=0
        GROUP BY
	    company_id
    </select>

    <select id="queryTravelUserByAttributionIsNullTotal" resultType="java.lang.Integer">
            SELECT
            count(*)
            FROM
            apply_order p
            WHERE
            p.company_id =#{companyId} and
            p.type=1 and p.cost_attribution_category=1
            and p.cost_attribution_id is null and p.apply_order_type=1
            and p.state in (2,4,1024)
            and p.delete_status=0
    </select>

    <select id="queryTravelUserByAttributionIsNull" resultMap="BaseResultMap">
        select
              *
              from
            (SELECT
              p.id,
              p.employee_id,
              p.company_id,
              p.create_time
            FROM
            apply_order p
            WHERE
            p.company_id =#{companyId} and
            p.type=1 and p.cost_attribution_category=1
            and p.cost_attribution_id is null and p.apply_order_type=1
            and p.state in (2,4,1024)
            and p.delete_status=0
            )as b
            order by b.create_time desc
           LIMIT ${pageSize}
            offset
           ${pageIndex}
    </select>

    <update id="updateById" parameterType="com.fenbeitong.saas.core.model.saas.ApplyOrder">
        update
        apply_order
        set
        cost_attribution_id = #{costAttributionId,jdbcType=VARCHAR}
        where
         id = #{id,jdbcType=CHAR}
    </update>

    <select id="queryWriteoffApplyOrderTotalByCompanyId" resultType="java.lang.Integer">
        select count(*)
        from apply_order
        where company_id = #{companyId,jdbcType=CHAR}
        and type = 16
        and apply_order_type = 9
        and state = 4
        and delete_status=0
    </select>

    <select id="queryWriteoffApplyOrderByCompanyId" resultMap="BaseResultMap">
        select *
        from apply_order
        where company_id = #{companyId,jdbcType=CHAR}
        and type = 16
        and apply_order_type = 9
        and state = 4
        and delete_status=0
        order by create_time desc
        limit #{pageSize,jdbcType=INTEGER} offset #{pageStart,jdbcType=INTEGER}
    </select>

    <select id="getBusinessWriteoffApplyOrderTotal" resultType="java.lang.Integer">
        select count(*)
        from apply_order
        where company_id = #{companyId,jdbcType=CHAR}
        and type = 17
        and apply_order_type = 10
        and delete_status=0
        <if test="applyId != null">
            and id LIKE #{applyId,jdbcType=CHAR}
        </if>
        <if test="state != -1 &amp;&amp; state != null">
            and state = #{state,jdbcType=INTEGER}
        </if>
        <if test="state != 2 &amp;&amp; state != 4 &amp;&amp; state != 16 &amp;&amp; state != 64">
            and state in (2,4,16,64)
        </if>
        <if test="returnTicket!=null">
            and return_ticket=#{returnTicket,jdbcType=INTEGER}
        </if>
        <if test="paymentStatus!=null">
            and payment_status=#{paymentStatus,jdbcType=INTEGER}
        </if>
        <if test="employeeList != null">
            and employee_id in
            <foreach collection="employeeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="startTime != null">
            and create_time >= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
    </select>

    <select id="queryBusinessWriteoffApplyList" resultMap="BaseResultMap">
        select *
        from apply_order
        where company_id = #{companyId,jdbcType=CHAR}
        and type = 17
        and apply_order_type = 10
        and delete_status=0
        <if test="applyId != null">
            and id LIKE #{applyId,jdbcType=CHAR}
        </if>
        <if test="state != -1 &amp;&amp; state != null">
            and state = #{state,jdbcType=INTEGER}
        </if>
        <if test="state != 2 &amp;&amp; state != 4 &amp;&amp; state != 16 &amp;&amp; state != 64">
            and state in (2,4,16,64)
        </if>
        <if test="returnTicket!=null">
            and return_ticket=#{returnTicket,jdbcType=INTEGER}
        </if>
        <if test="paymentStatus!=null">
            and payment_status=#{paymentStatus,jdbcType=INTEGER}
        </if>
        <if test="employeeList != null">
            and employee_id in
            <foreach collection="employeeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="startTime != null">
            and create_time >= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
        order by create_time desc
        limit #{pageSize,jdbcType=INTEGER} offset #{pageStart,jdbcType=INTEGER}
    </select>

    <select id="queryBusinessWriteoffApplyListTotal" resultMap="BaseResultMap">
        select *
        from apply_order
        where company_id = #{companyId,jdbcType=CHAR}
        and type = 17
        and apply_order_type = 10
        and delete_status=0
        <if test="applyId != null">
            and id LIKE #{applyId,jdbcType=CHAR}
        </if>
        <if test="state != -1 &amp;&amp; state != null">
            and state = #{state,jdbcType=INTEGER}
        </if>
        <if test="state != 2 &amp;&amp; state != 4 &amp;&amp; state != 16 &amp;&amp; state != 64">
            and state in (2,4,16,64)
        </if>
        <if test="returnTicket!=null">
            and return_ticket=#{returnTicket,jdbcType=INTEGER}
        </if>
        <if test="paymentStatus!=null">
            and payment_status=#{paymentStatus,jdbcType=INTEGER}
        </if>
        <if test="employeeList != null">
            and employee_id in
            <foreach collection="employeeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="startTime != null">
            and create_time >= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
    </select>

    <select id="queryBusinessWriteoffApply" resultMap="BaseResultMap">
        select *
        from apply_order
        where company_id = #{companyId,jdbcType=CHAR}
        and type = 17
        and apply_order_type = 10
        and id = #{applyId,jdbcType=CHAR}
    </select>

    <insert id="batchInsert" parameterType="java.util.List" >
        insert into apply_order (
        id,
        type,
        employee_id,
        budget,
        state,
        create_time,
        update_time,
        company_id,
        apply_order_type,
        root_apply_order_id
        )
        values
        <foreach collection="list" item="item" index="index" open="" close="" separator=",">
            (
            #{item.id,jdbcType=VARCHAR},
            #{item.type,jdbcType=INTEGER},
            #{item.employeeId,jdbcType=VARCHAR},
            #{item.budget,jdbcType=INTEGER},
            #{item.state,jdbcType=INTEGER},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.companyId,jdbcType=VARCHAR},
            #{item.applyOrderType,jdbcType=INTEGER},
            #{item.rootApplyOrderId,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <select id="getReturnTicketCount" resultType="java.lang.Integer">
       select count(*)
       from
       apply_order r
       <where>
          r.type=#{type,jdbcType=INTEGER}
          and r.state= 4 and r.return_ticket in(0,2)
          and r.company_id =#{company_id}
          and delete_status=0
       </where>
    </select>

    <select id="getReturnTicketTotal" resultType="java.lang.Integer">
        select count(*)
        from apply_order r
        where company_id = #{companyId,jdbcType=CHAR}
        and type=#{type,jdbcType=INTEGER}
        and state= 4 and return_ticket in(0,2)
        and delete_status=0
        <if test="applyId != null">
            and id LIKE #{applyId,jdbcType=CHAR}
        </if>
        <if test="hasten_statu != null &amp;&amp;hasten_statu != -1">
            and hasten_statu = #{hasten_statu,jdbcType=INTEGER}
        </if>
        <if test="employeeList != null">
            and employee_id in
            <foreach collection="employeeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="startTime != null">
            and create_time >= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="hastenStartTime != null">
            and hasten_create_time >= #{hastenStartTime,jdbcType=TIMESTAMP}
        </if>
        <if test="hastenEndTime != null">
            and hasten_create_time &lt;= #{hastenEndTime,jdbcType=TIMESTAMP}
        </if>
    </select>

    <select id="getReturnTicketList" resultMap="BaseResultMap">
        select *
        from apply_order r
        where company_id = #{companyId,jdbcType=CHAR}
        and type=#{type,jdbcType=INTEGER}
        and state= 4 and return_ticket in(0,2)
        and delete_status=0
        <if test="applyId != null">
            and id LIKE #{applyId,jdbcType=CHAR}
        </if>
        <if test="hasten_statu != null &amp;&amp;hasten_statu != -1 ">
            and hasten_statu = #{hasten_statu,jdbcType=INTEGER}
        </if>
        <if test="employeeList != null">
            and employee_id in
            <foreach collection="employeeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="startTime != null">
            and create_time >= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="hastenStartTime != null">
            and hasten_create_time >= #{hastenStartTime,jdbcType=TIMESTAMP}
        </if>
        <if test="hastenEndTime != null">
            and hasten_create_time &lt;= #{hastenEndTime,jdbcType=TIMESTAMP}
        </if>
        order by create_time desc
        limit #{pageSize,jdbcType=INTEGER} offset #{pageStart,jdbcType=INTEGER}
    </select>

    <select id="queryWriteoffListTotal" resultType="java.lang.Integer">
        select
        count(*)
        from
        apply_order r
        where
        r.company_id = #{company_id}
        and r.employee_id = #{employee_id}
        and r.delete_status=0
        <if test="type == 0">
            and r.type in(16,17) and  r.apply_order_type in(9,10)
        </if>
        <if test="type == 16">
            and r.type =16 and  r.apply_order_type =9
        </if>
        <if test="type == 17">
            and r.type =17 and  r.apply_order_type =10
        </if>
        <if test="state == 0">
        <!-- 0全部, 1未提交草稿箱,2.审批中,4.审批通过 16.驳回,审批未通过 64.撤销已撤回、 128 已过期  512已完成-->
            and state in (1,2,4,16,64,128,512)
        </if>
        <if test="state == 1">
            <!--1未提交草稿箱-->
            and state =1
        </if>
        <if test="state == 2">
            <!--2.审批中-->
            and state =2
        </if>
        <if test="state == 4">
            <!--4.审批通过-->
            and state =4
        </if>
        <if test="state == 16">
            <!--16.驳回,审批未通过-->
            and state =16
        </if>
        <if test="state == 64">
            <!--64.撤销已撤回-->
            and state =64
        </if>
        <if test="state == 128">
            <!--128 已过期-->
            and state =128
        </if>
        <if test="state == 512">
            <!--512已完成 (state=4 && 发票/申请单全部补缴齐全)-->
            and state =4 and r.return_ticket=1
        </if>
        <!--待付款-->
        <if test="state == 1024">
            and pay_apply_status = 4 and payment_status=0 and return_ticket =1
        </if>
        <!--付款中-->
        <if test="state == 2048">
            and payment_status=2 and return_ticket =1 and pay_apply_status = 4
        </if>
        <!-- 已付款-->
        <if test="state == 4096">
            and payment_status= 1 and return_ticket =1 and pay_apply_status =4
        </if>
    </select>

    <select id="queryWriteoffList" resultMap="BaseResultMap">
        select *
        from apply_order r
        where
        r.company_id = #{company_id}
        and r.employee_id = #{employee_id}
        and r.delete_status=0
        <if test="type == 0">
            and r.type in(16,17) and  r.apply_order_type in(9,10)
        </if>
        <if test="type == 16">
            and r.type =16 and  r.apply_order_type =9
        </if>
        <if test="type == 17">
            and r.type =17 and  r.apply_order_type =10
        </if>
        <if test="state == 0">
            <!-- 0全部, 1未提交草稿箱,2.审批中,4.审批通过 16.驳回,审批未通过 64.撤销已撤回、 128 已过期  512已完成-->
            and state in (1,2,4,16,64,128,512)
        </if>
        <if test="state == 1">
            <!--1未提交草稿箱-->
            and state =1
        </if>
        <if test="state == 2">
            <!--2.审批中-->
            and state =2
        </if>
        <if test="state == 4">
            <!--4.审批通过-->
            and state =4
        </if>
        <if test="state == 16">
            <!--16.驳回,审批未通过-->
            and state =16
        </if>
        <if test="state == 64">
            <!--64.撤销已撤回-->
            and state =64
        </if>
        <if test="state == 128">
            <!--128 已过期-->
            and state =128
        </if>
        <if test="state == 512">
            <!--512已完成 (state=4 && 发票/申请单全部补缴齐全)-->
            and r.state =4 and r.return_ticket=1
        </if>
        <!--待付款-->
        <if test="state == 1024">
            and pay_apply_status = 4 and payment_status=0 and return_ticket =1
        </if>
        <!--付款中-->
        <if test="state == 2048">
            and payment_status=2 and return_ticket =1 and pay_apply_status = 4
        </if>
        <!-- 已付款-->
        <if test="state == 4096">
            and payment_status= 1 and return_ticket =1 and pay_apply_status =4
        </if>
        order by r.create_time desc
        limit #{pageSize,jdbcType=INTEGER} offset #{pageStart,jdbcType=INTEGER}
    </select>

    <select id="queryHastenRemindListTotal" resultType="java.lang.Integer">
        select
        count(*)
        from apply_order r
        where
        r.company_id = #{company_id}
        and r.employee_id = #{employee_id}
        and r.hasten_statu=2
        and r.return_ticket in(0,2)
        and r.type =16 and  r.apply_order_type =9
        and r.state = 4
        and r.delete_status=0
    </select>

    <select id="queryHastenRemindList" resultMap="BaseResultMap">
        select *
        from apply_order r
        where
        r.company_id = #{company_id}
        and r.employee_id = #{employee_id}
        and r.hasten_statu=2
        and r.return_ticket in(0,2)
        and r.type =16 and  r.apply_order_type =9
        and r.state = 4
        and r.delete_status=0
        order by r.hasten_create_time desc
        limit #{pageSize,jdbcType=INTEGER} offset #{pageStart,jdbcType=INTEGER}
    </select>

    <select id="getApplyMileageOrderTotalByUserId" resultType="java.lang.Integer">
        select count(1)
        from apply_order
        where company_id = #{companyId,jdbcType=CHAR}
        and state = 4
        and apply_order_type = 13
        and type = 21
        and delete_status=0
        <if test="applyId != null">
            and id LIKE #{applyId,jdbcType=CHAR}
        </if>
        <if test="proposer != null">
            and applicant_name LIKE #{proposer,jdbcType=CHAR}
        </if>
        <if test="startTime != null">
            and create_time >= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="sendStatus != null &amp;&amp;sendStatus != -1 ">
            and send_status = #{sendStatus,jdbcType=INTEGER}
        </if>
    </select>

    <select id="getApplyMileageOrderListByUserId" resultMap="BaseResultMap">
        select *
        from apply_order
        where company_id = #{companyId,jdbcType=CHAR}
        and state = 4
        and apply_order_type = 13
        and type = 21
        and delete_status=0
        <if test="applyId != null">
            and id LIKE #{applyId,jdbcType=CHAR}
        </if>
        <if test="proposer != null">
            and applicant_name LIKE #{proposer,jdbcType=CHAR}
        </if>
        <if test="startTime != null">
            and create_time >= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="sendStatus != null &amp;&amp;sendStatus != -1 ">
            and send_status = #{sendStatus,jdbcType=INTEGER}
        </if>
        order by create_time desc
        limit #{pageSize,jdbcType=INTEGER} offset #{pageStart,jdbcType=INTEGER}
    </select>

    <select id="getApplyMileageOrderExportListByUserId" resultMap="BaseResultMap">
        select *
        from apply_order
        where company_id = #{companyId,jdbcType=CHAR}
        and state = 4
        and apply_order_type = 13
        and type = 21
        and delete_status=0
        <if test="applyId != null">
            and id LIKE #{applyId,jdbcType=CHAR}
        </if>
        <if test="proposer != null">
            and applicant_name LIKE #{proposer,jdbcType=CHAR}
        </if>
        <if test="startTime != null">
            and create_time >= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="sendStatus != null &amp;&amp;sendStatus != -1 ">
            and send_status = #{sendStatus,jdbcType=INTEGER}
        </if>
        order by create_time desc
    </select>

    <update id="revokeMileage">
        UPDATE apply_order
        SET
        send_status = #{sendStatus},
        repulse_desc = null
        WHERE
        id IN
        <foreach collection="applyIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND company_id = #{companyId}
        AND apply_order_type = 13
        AND type = 21
        AND delete_status = 0
        AND send_status = 5
    </update>
</mapper>