<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.ApplyReasonMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.ApplyReason">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Feb 24 09:56:55 CST 2017.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="type" jdbcType="INTEGER" property="type" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Feb 24 09:56:55 CST 2017.
    -->
    delete from apply_reason
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.saas.ApplyReason">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Feb 24 09:56:55 CST 2017.
    -->
    insert into apply_reason (id, name, type
      )
    values (#{id,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}
      )
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.saas.ApplyReason">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Feb 24 09:56:55 CST 2017.
    -->
    update apply_reason
    set name = #{name,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Feb 24 09:56:55 CST 2017.
    -->
    select id, name, type
    from apply_reason
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Feb 24 09:56:55 CST 2017.
    -->
    select id, name, type
    from apply_reason
  </select>
  <select id="selectListByApplyType" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Feb 24 09:56:55 CST 2017.
    -->
    select id, name, type
    from apply_reason where type=#{applyType,jdbcType=INTEGER}
  </select>

</mapper>