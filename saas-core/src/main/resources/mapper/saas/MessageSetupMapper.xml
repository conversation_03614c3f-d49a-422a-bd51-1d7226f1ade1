<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.MessageSetupMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.MessageSetup">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="item_code" jdbcType="VARCHAR" property="itemCode" />
    <result column="is_checked" jdbcType="INTEGER" property="isChecked" />
    <result column="int_val1" jdbcType="INTEGER" property="intVal1" />
    <result column="int_val2" jdbcType="INTEGER" property="intVal2" />
    <result column="int_val3" jdbcType="INTEGER" property="intVal3" />
    <result column="str_val1" jdbcType="VARCHAR" property="strVal1" />
    <result column="str_val2" jdbcType="VARCHAR" property="strVal2" />
    <result column="str_val3" jdbcType="VARCHAR" property="strVal3" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="direct_leader" jdbcType="INTEGER" property="directLeader" />
    <result column="depart_leader" jdbcType="INTEGER" property="departLeader" />
    <result column="str_val1_en" jdbcType="VARCHAR" property="strVal1En" />
    <result column="str_val2_en" jdbcType="VARCHAR" property="strVal2En" />
    <result column="str_val3_en" jdbcType="VARCHAR" property="strVal3En" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, item_code, is_checked, int_val1, int_val2, int_val3, str_val1, str_val2, 
    str_val3, create_time, update_time, user_id,direct_leader,depart_leader, str_val1_en, str_val2_en, str_val3_en
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.saas.core.model.saas.MessageSetupExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from message_setup
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${limit} offset ${offset}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from message_setup
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from message_setup
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.saas.core.model.saas.MessageSetupExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from message_setup
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.saas.core.model.saas.MessageSetup" useGeneratedKeys="true"
          keyProperty="id" keyColumn="id">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into message_setup (id, company_id, item_code, 
      is_checked, int_val1, int_val2, 
      int_val3, str_val1, str_val2, 
      str_val3, create_time, update_time, 
      user_id, str_val1_en, str_val2_en, str_val3_en)
    values (#{id,jdbcType=INTEGER}, #{companyId,jdbcType=VARCHAR}, #{itemCode,jdbcType=VARCHAR}, 
      #{isChecked,jdbcType=INTEGER}, #{intVal1,jdbcType=INTEGER}, #{intVal2,jdbcType=INTEGER}, 
      #{intVal3,jdbcType=INTEGER}, #{strVal1,jdbcType=VARCHAR}, #{strVal2,jdbcType=VARCHAR}, 
      #{strVal3,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{userId,jdbcType=VARCHAR}), #{strVal1En,jdbcType=VARCHAR}, #{strVal2En,jdbcType=VARCHAR}, #{strVal3En,jdbcType=VARCHAR}
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.saas.core.model.saas.MessageSetup" useGeneratedKeys="true"
          keyProperty="id" keyColumn="id">
    insert into message_setup
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="distinguishCategoryType != null">
        distinguish_categoryType,
      </if>
      <if test="categoryTypeAmount != null">
        categoryType_amount,
      </if>
      <if test="directLeader != null">
        direct_leader,
      </if>
      <if test="departLeader != null">
        depart_leader,
      </if>
      <if test="itemCode != null">
        item_code,
      </if>
      <if test="isChecked != null">
        is_checked,
      </if>
      <if test="intVal1 != null">
        int_val1,
      </if>
      <if test="intVal2 != null">
        int_val2,
      </if>
      <if test="intVal3 != null">
        int_val3,
      </if>
      <if test="strVal1 != null">
        str_val1,
      </if>
      <if test="strVal2 != null">
        str_val2,
      </if>
      <if test="strVal3 != null">
        str_val3,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="strVal1En != null">
        str_val1_en,
      </if>
      <if test="strVal2En != null">
        str_val2_en,
      </if>
      <if test="strVal3En != null">
        str_val3_en,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="distinguishCategoryType != null">
        #{distinguishCategoryType,jdbcType=INTEGER},
      </if>
      <if test="categoryTypeAmount != null">
        #{categoryTypeAmount,jdbcType=VARCHAR},
      </if>
      <if test="directLeader != null">
        #{directLeader,jdbcType=VARCHAR},
      </if>
      <if test="departLeader != null">
        #{departLeader,jdbcType=VARCHAR},
      </if>
      <if test="itemCode != null">
        #{itemCode,jdbcType=VARCHAR},
      </if>
      <if test="isChecked != null">
        #{isChecked,jdbcType=INTEGER},
      </if>
      <if test="intVal1 != null">
        #{intVal1,jdbcType=INTEGER},
      </if>
      <if test="intVal2 != null">
        #{intVal2,jdbcType=INTEGER},
      </if>
      <if test="intVal3 != null">
        #{intVal3,jdbcType=INTEGER},
      </if>
      <if test="strVal1 != null">
        #{strVal1,jdbcType=VARCHAR},
      </if>
      <if test="strVal2 != null">
        #{strVal2,jdbcType=VARCHAR},
      </if>
      <if test="strVal3 != null">
        #{strVal3,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="strVal1En != null">
        #{strVal1En,jdbcType=VARCHAR},
      </if>
      <if test="strVal2En != null">
        #{strVal2En,jdbcType=VARCHAR},
      </if>
      <if test="strVal3En != null">
        #{strVal3En,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.saas.core.model.saas.MessageSetupExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from message_setup
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update message_setup
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.directLeader != null">
        direct_leader = #{record.directLeader,jdbcType=INTEGER},
      </if>
      <if test="record.departLeader != null">
        depart_leader = #{record.departLeader,jdbcType=INTEGER},
      </if>
      <if test="record.itemCode != null">
        item_code = #{record.itemCode,jdbcType=VARCHAR},
      </if>
      <if test="record.isChecked != null">
        is_checked = #{record.isChecked,jdbcType=INTEGER},
      </if>
      <if test="record.distinguishCategoryType != null">
        distinguish_categoryType = #{record.distinguishCategoryType,jdbcType=INTEGER},
      </if>
      <if test="record.categoryTypeAmount != null">
        categoryType_amount = #{record.categoryTypeAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.intVal1 != null">
        int_val1 = #{record.intVal1,jdbcType=INTEGER},
      </if>
      <if test="record.intVal2 != null">
        int_val2 = #{record.intVal2,jdbcType=INTEGER},
      </if>
      <if test="record.intVal3 != null">
        int_val3 = #{record.intVal3,jdbcType=INTEGER},
      </if>
      <if test="record.strVal1 != null">
        str_val1 = #{record.strVal1,jdbcType=VARCHAR},
      </if>
      <if test="record.strVal2 != null">
        str_val2 = #{record.strVal2,jdbcType=VARCHAR},
      </if>
      <if test="record.strVal3 != null">
        str_val3 = #{record.strVal3,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.strVal1En != null">
        str_val1_en = #{record.strVal1En,jdbcType=VARCHAR},
      </if>
      <if test="record.strVal2En != null">
        str_val2_en = #{record.strVal2En,jdbcType=VARCHAR},
      </if>
      <if test="record.strVal3En != null">
        str_val3_en = #{record.strVal3En,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update message_setup
    set id = #{record.id,jdbcType=INTEGER},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      item_code = #{record.itemCode,jdbcType=VARCHAR},
      is_checked = #{record.isChecked,jdbcType=INTEGER},
      int_val1 = #{record.intVal1,jdbcType=INTEGER},
      int_val2 = #{record.intVal2,jdbcType=INTEGER},
      int_val3 = #{record.intVal3,jdbcType=INTEGER},
      str_val1 = #{record.strVal1,jdbcType=VARCHAR},
      str_val2 = #{record.strVal2,jdbcType=VARCHAR},
      str_val3 = #{record.strVal3,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      user_id = #{record.userId,jdbcType=VARCHAR},
      str_val1_en = #{record.strVal1En,jdbcType=VARCHAR},
      str_val2_en = #{record.strVal2En,jdbcType=VARCHAR},
      str_val3_en = #{record.strVal3En,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.saas.core.model.saas.MessageSetup">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update message_setup
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="itemCode != null">
        item_code = #{itemCode,jdbcType=VARCHAR},
      </if>
      <if test="isChecked != null">
        is_checked = #{isChecked,jdbcType=INTEGER},
      </if>
      <if test="intVal1 != null">
        int_val1 = #{intVal1,jdbcType=INTEGER},
      </if>
      <if test="intVal2 != null">
        int_val2 = #{intVal2,jdbcType=INTEGER},
      </if>
      <if test="intVal3 != null">
        int_val3 = #{intVal3,jdbcType=INTEGER},
      </if>
      <if test="strVal1 != null">
        str_val1 = #{strVal1,jdbcType=VARCHAR},
      </if>
      <if test="strVal2 != null">
        str_val2 = #{strVal2,jdbcType=VARCHAR},
      </if>
      <if test="strVal3 != null">
        str_val3 = #{strVal3,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="strVal1En != null">
        str_val1_en = #{strVal1En,jdbcType=VARCHAR},
      </if>
      <if test="strVal2En != null">
        str_val2_en = #{strVal2En,jdbcType=VARCHAR},
      </if>
      <if test="strVal3En != null">
        str_val3_en = #{strVal3En,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.saas.core.model.saas.MessageSetup">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update message_setup
    set company_id = #{companyId,jdbcType=VARCHAR},
      item_code = #{itemCode,jdbcType=VARCHAR},
      is_checked = #{isChecked,jdbcType=INTEGER},
      int_val1 = #{intVal1,jdbcType=INTEGER},
      int_val2 = #{intVal2,jdbcType=INTEGER},
      int_val3 = #{intVal3,jdbcType=INTEGER},
      str_val1 = #{strVal1,jdbcType=VARCHAR},
      str_val2 = #{strVal2,jdbcType=VARCHAR},
      str_val3 = #{strVal3,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      user_id = #{userId,jdbcType=VARCHAR},
      str_val1_en = #{strVal1En,jdbcType=VARCHAR},
      str_val2_en = #{strVal2En,jdbcType=VARCHAR},
      str_val3_en = #{strVal3En,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>