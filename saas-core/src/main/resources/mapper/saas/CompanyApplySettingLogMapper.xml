<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.CompanyApplySettingLogMapper">
    <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.CompanyApplySettingLog">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Apr 21 10:05:05 CST 2017.
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="company_apply_setting_id" jdbcType="VARCHAR" property="companyApplySettingId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="action" jdbcType="INTEGER" property="action"/>
        <result column="ip" jdbcType="VARCHAR" property="ip"/>
        <result column="operator_id" jdbcType="VARCHAR" property="operatorId"/>
        <result column="operate_content" jdbcType="VARCHAR" property="operateContent" />
    </resultMap>
    <insert id="insert" parameterType="com.fenbeitong.saas.core.model.saas.CompanyApplySettingLog" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
    insert into company_apply_setting_log ( company_apply_setting_id,
        create_time, action,
        ip, operator_id, operate_content)
        values ( #{companyApplySettingId,jdbcType=VARCHAR},
        #{createTime,jdbcType=INTEGER}, #{action,jdbcType=TIMESTAMP},
        #{ip,jdbcType=TIMESTAMP}, #{operatorId,jdbcType=INTEGER}, #{operateContent,jdbcType=VARCHAR})
  </insert>
</mapper>