<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.MessageMapperEx">
    <resultMap id="BaseResultMapEx" type="com.fenbeitong.saas.core.model.saas.Message" extends="com.fenbeitong.saas.core.dao.saas.MessageMapper.BaseResultMap">
    </resultMap>
    <sql id="Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Thu Apr 13 10:08:31 CST 2017.
        -->
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Thu Apr 13 10:08:31 CST 2017.
        -->
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Thu Apr 13 10:08:31 CST 2017.
        -->
        id, msg_type, biz_type, title, comment, biz_order, info, link, sender, sender_type,
        receiver, read, create_time, update_time
    </sql>
    <select id="selectFirstByExample" parameterType="com.fenbeitong.saas.core.model.saas.MessageExample" resultMap="BaseResultMapEx">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Thu Apr 13 10:08:31 CST 2017.
        -->
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from message
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        limit 1
    </select>

    <select id="selectByExample" parameterType="map" resultMap="BaseResultMapEx">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Thu Apr 13 10:08:31 CST 2017.
        -->
        select
        <include refid="Base_Column_List"/>
        from message
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
        <if test="lastId != null">
            AND
            id &lt; #{lastId, jdbcType=CHAR}
        </if>
        <if test="example.orderByClause != null">
            order by ${example.orderByClause}
        </if>
        limit #{count,jdbcType=INTEGER}
    </select>

    <insert id="multiInsertReadInfo" parameterType="java.util.List">
      insert into message_read_info VALUES 
      <foreach collection="list" item="messageReadInfo" index="index" separator=",">
          (#{messageReadInfo.messageId},#{messageReadInfo.readTime},#{messageReadInfo.deviceInfo})
      </foreach>
    </insert>

    <select id="queryMessageByCompanyId" resultType="java.lang.Long">
       select count(1)
       from message
       where company_id is null
    </select>

    <insert id="batchInsert">
        insert into message (
            id,
            msg_type,
            biz_type,
            title,
            comment,
            biz_order,
            info,
            link,
            sender,
            sender_type,
            receiver,
            read,
            create_time,
            update_time,
            company_id
        )
        values
        <foreach collection="list" item="item" open="" close="" separator=",">
            (
                #{item.id,jdbcType=CHAR},
                #{item.msgType,jdbcType=INTEGER},
                #{item.bizType,jdbcType=INTEGER},
                #{item.title,jdbcType=VARCHAR},
                #{item.comment,jdbcType=VARCHAR},
                #{item.bizOrder,jdbcType=VARCHAR},
                #{item.info,jdbcType=VARCHAR},
                #{item.link,jdbcType=VARCHAR},
                #{item.sender,jdbcType=CHAR},
                #{item.senderType,jdbcType=INTEGER},
                #{item.receiver,jdbcType=CHAR},
                #{item.read,jdbcType=BIT},
                #{item.createTime,jdbcType=TIMESTAMP},
                #{item.updateTime,jdbcType=TIMESTAMP},
                #{item.companyId,jdbcType=CHAR}
            )
        </foreach>
    </insert>
</mapper>