<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.CompanyCustomReasonExtMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.saas.core.model.saas.CompanyCustomReason"
          extends="com.fenbeitong.saas.core.dao.saas.CompanyCustomReasonMapper.BaseResultMap">
  </resultMap>

  <insert id="initCompanyReason">
    INSERT INTO company_custom_reason (NAME, company_id)
		SELECT
			NAME, #{companyId}
		FROM
			company_custom_reason
		WHERE
			company_id = 'default'
  </insert>

  <insert id="batchInsertCompanyCustomReason" parameterType="java.util.List">
    insert into company_custom_reason
      (name, company_id, operator_id)
    values
    <foreach collection="companyCustomReasonList" item="item" index="index" separator=",">
      (#{item.name,jdbcType=VARCHAR}, #{item.companyId,jdbcType=VARCHAR}, #{item.operatorId,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="queryInitedCompanys" resultType="java.lang.String">
    SELECT DISTINCT company_id
    FROM company_custom_reason
    WHERE company_id != 'default'
  </select>
</mapper>