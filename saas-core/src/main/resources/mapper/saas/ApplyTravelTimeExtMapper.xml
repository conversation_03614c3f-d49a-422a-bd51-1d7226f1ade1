<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.saas.core.dao.saas.ApplyTravelTimeExtMapper">
  <resultMap id="BaseResultMap"
             type="com.fenbeitong.saas.core.model.saas.ApplyTravelTime"
             extends="com.fenbeitong.saas.core.dao.saas.ApplyTravelTimeMapper.BaseResultMap">
  </resultMap>

  <delete id="deleteByApplyOrderId" parameterType="java.lang.String">
    delete from apply_travel_time
    where apply_order_id = #{applyOrderId,jdbcType=CHAR}
  </delete>

  <select id="selectHistoryTravelList" resultMap="BaseResultMap">
    select att.travel_time, min(att.travel_type) as travel_type
    from apply_travel_time att
    left join apply_order ao
    on att.apply_order_id = ao.id
    where att.user_id = #{userId,jdbcType=CHAR}
    and att.company_id = #{companyId,jdbcType=CHAR}
    <if test="applyId != null">
      and att.apply_order_id != #{applyId,jdbcType=CHAR}
    </if>
    <![CDATA[and att.travel_time >= #{startDate,jdbcType=BIGINT}]]>
    and ao."state" in (2,4,1024)
    group by att.travel_time
    order by att.travel_time asc
  </select>

  <select id="selectApplyTravelTimeList" resultMap="BaseResultMap">
    select *
    from apply_travel_time
    where company_id = #{companyId,jdbcType=CHAR}
    and apply_order_id = #{applyId,jdbcType=CHAR}
    order by travel_time asc
  </select>

  <select id="selectTravelTimeByUserId" resultMap="BaseResultMap">
    select att.user_id,att.travel_time,min(att.travel_type) travel_type
    from apply_travel_time att
    inner join apply_order ao
    on att.apply_order_id = ao.id
    where att.user_id in
    <foreach collection="userIdList" item="userId" index="index"
             open="(" close=")" separator=",">
      #{userId}
    </foreach>
    and ao.state in(4,1024)
    and att.company_id = #{companyId,jdbcType=CHAR}
    <if test="costAttributionId !=null and costAttributionId !=''">
      and ao.cost_attribution_id =#{costAttributionId}
    </if>
    <![CDATA[and att.travel_time >= #{startDate,jdbcType=BIGINT}]]>
    <![CDATA[and att.travel_time <= #{endDate,jdbcType=BIGINT}]]>
    group by att.user_id,att.travel_time
    order by att.user_id,att.travel_time asc
  </select>
</mapper>