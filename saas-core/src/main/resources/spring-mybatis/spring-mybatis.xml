<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-4.2.xsd
        http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-4.2.xsd
        http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-4.2.xsd">

    <bean class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
        <property name="locations">
            <value>classpath:jdbc/jdbc.properties</value>
        </property>
        <property name="ignoreUnresolvablePlaceholders" value="true"/>
    </bean>
    <!-- ========================================针对spring-myBatis的配置项============================== -->
    <!-- 配置数据源，使用的是alibaba的Druid(德鲁伊)数据源 -->
    <bean name="dataSourceSaas" class="com.alibaba.druid.pool.DruidDataSource" init-method="init"
          destroy-method="close">
        <property name="driverClassName" value="${jdbc_driver_saas}"/>
        <property name="url" value="${jdbc_url_saas}"/>
        <property name="username" value="${jdbc_username_saas}"/>
        <property name="password" value="${jdbc_password_saas}"/>
        <!-- 初始化连接大小 -->
        <property name="initialSize" value="0"/>
        <!-- 连接池最大使用连接数量 -->
        <property name="maxActive" value="80"/>
        <!-- 连接池最大空闲 -->
        <!--<property name="maxIdle" value="20"/>-->
        <!-- 连接池最小空闲 -->
        <property name="minIdle" value="0"/>
        <!-- 获取连接最大等待时间 -->
        <property name="maxWait" value="60000"/>
        <!--
        <property name="poolPreparedStatements" value="true" />
        <property name="maxPoolPreparedStatementPerConnectionSize" value="33" />
        -->
        <property name="testOnBorrow" value="false"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="true"/>
        <!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
        <property name="timeBetweenEvictionRunsMillis" value="60000"/>
        <!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
        <property name="minEvictableIdleTimeMillis" value="25200000"/>
        <!-- 打开removeAbandoned功能 -->
        <property name="removeAbandoned" value="true"/>
        <!-- 1800秒，也就是30分钟 -->
        <property name="removeAbandonedTimeout" value="1800"/>
        <!-- 关闭abanded连接时输出错误日志 -->
        <property name="logAbandoned" value="true"/>
        <!-- 监控数据库 -->
        <!-- <property name="filters" value="stat" /> -->
        <property name="filters" value="mergeStat"/>
    </bean>
    <!-- ========================================针对myBatis的配置项============================== -->
    <!-- ==============================配置sqlSessionFactory============================== -->
    <bean id="sqlSessionFactorySaas" class="org.mybatis.spring.SqlSessionFactoryBean">
        <!-- 实例化sqlSessionFactory时需要使用上述配置好的数据源以及SQL映射文件 -->
        <property name="plugins">
            <array>
                <bean class="com.github.pagehelper.PageInterceptor">
                    <property name="properties">
                        <!--使用下面的方式配置参数，一行配置一个 -->
                        <value>
                            helperDialect=postgresql
                            rowBoundsWithCount=true
                            supportMethodsArguments=true
                            offsetAsPageNum=true
                            pageSizeZero=true
                            reasonable=false
                            returnPageInfo=check
                        </value>
                    </property>
                </bean>
            </array>
        </property>
        <property name="dataSource" ref="dataSourceSaas"/>
        <property name="mapperLocations" value="classpath:mapper/saas/*.xml"/>
    </bean>
    <!-- ==============================配置扫描器============================== -->
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactorySaas"/>
        <property name="basePackage" value="com.fenbeitong.saas.core.dao.saas"/>
    </bean>
    <!-- ==============================配置Spring的事务管理器============================== -->
    <bean id="transactionManagerSaas" class="org.springframework.jdbc.datasource.DataSourceTransactionManager" primary="true">
        <property name="dataSource" ref="dataSourceSaas"/>
        <qualifier value="saas"/>
    </bean>
    <tx:annotation-driven transaction-manager="transactionManagerSaas"/>
    <!-- ========================================针对spring-myBatis的配置项============================== -->
    <!-- 配置数据源，使用的是alibaba的Druid(德鲁伊)数据源 -->
    <bean name="dataSource_fenbeitong" class="com.alibaba.druid.pool.DruidDataSource" init-method="init"
          destroy-method="close">
        <property name="driverClassName" value="${jdbc_driver_fenbeiotng}"/>
        <property name="url" value="${jdbc_url_fenbeiotng}"/>
        <property name="username" value="${jdbc_username_fenbeiotng}"/>
        <property name="password" value="${jdbc_password_fenbeiotng}"/>
        <!-- 初始化连接大小 -->
        <property name="initialSize" value="0"/>
        <!-- 连接池最大使用连接数量 -->
        <property name="maxActive" value="20"/>
        <!-- 连接池最大空闲 -->
        <!--<property name="maxIdle" value="20"/>-->
        <!-- 连接池最小空闲 -->
        <property name="minIdle" value="0"/>
        <!-- 获取连接最大等待时间 -->
        <property name="maxWait" value="60000"/>
        <!--
        <property name="poolPreparedStatements" value="true" />
        <property name="maxPoolPreparedStatementPerConnectionSize" value="33" />
        -->
        <property name="testOnBorrow" value="false"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="true"/>
        <!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
        <property name="timeBetweenEvictionRunsMillis" value="60000"/>
        <!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
        <property name="minEvictableIdleTimeMillis" value="25200000"/>
        <!-- 打开removeAbandoned功能 -->
        <property name="removeAbandoned" value="true"/>
        <!-- 1800秒，也就是30分钟 -->
        <property name="removeAbandonedTimeout" value="1800"/>
        <!-- 关闭abanded连接时输出错误日志 -->
        <property name="logAbandoned" value="true"/>
        <!-- 监控数据库 -->
        <!-- <property name="filters" value="stat" /> -->
        <property name="filters" value="mergeStat"/>
    </bean>
    <!-- ========================================针对myBatis的配置项============================== -->
    <!-- ==============================配置sqlSessionFactory============================== -->
    <bean id="sqlSessionFactoryFenbeitong" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="plugins">
            <array>
                <bean class="com.github.pagehelper.PageInterceptor">
                    <property name="properties">
                        <!--使用下面的方式配置参数，一行配置一个 -->
                        <value>
                            helperDialect=postgresql
                            rowBoundsWithCount=true
                            supportMethodsArguments=true
                            offsetAsPageNum=true
                            pageSizeZero=true
                            reasonable=false
                            returnPageInfo=check
                        </value>
                    </property>
                </bean>
            </array>
        </property>
        <!-- 实例化sqlSessionFactory时需要使用上述配置好的数据源以及SQL映射文件 -->
        <property name="dataSource" ref="dataSource_fenbeitong"/>
        <property name="mapperLocations" value="classpath:mapper/fenbeitong/**/*.xml"/>
    </bean>
    <!-- ==============================配置扫描器============================== -->
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactoryFenbeitong"/>
        <property name="basePackage" value="com.fenbeitong.saas.core.dao.fenbeitong"/>
    </bean>
    <!-- ==============================配置Spring的事务管理器============================== -->
    <bean id="transactionManagerFenbeitong" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dataSource_fenbeitong"/>
        <qualifier value="fenbeitong"/>
    </bean>
    <tx:annotation-driven transaction-manager="transactionManagerFenbeitong"/>
    <!-- ========================================针对spring-myBatis的配置项============================== -->
    <!-- 配置数据源，使用的是alibaba的Druid(德鲁伊)数据源 -->
    <bean name="dataSourceSaasPlus" class="com.alibaba.druid.pool.DruidDataSource" init-method="init"
          destroy-method="close">
        <property name="driverClassName" value="${jdbc_driver_saasplus}"/>
        <property name="url" value="${jdbc_url_saasplus}"/>
        <property name="username" value="${jdbc_username_saasplus}"/>
        <property name="password" value="${jdbc_password_saasplus}"/>
        <!-- 初始化连接大小 -->
        <property name="initialSize" value="0"/>
        <!-- 连接池最大使用连接数量 -->
        <property name="maxActive" value="20"/>
        <!-- 连接池最大空闲 -->
        <!--<property name="maxIdle" value="20"/>-->
        <!-- 连接池最小空闲 -->
        <property name="minIdle" value="0"/>
        <property name="socketTimeout" value="1800000"/>
        <property name="connectTimeout" value="30000"/>
        <property name="validationQuery" value="select 1"/>

        <!-- 获取连接最大等待时间 -->
        <property name="maxWait" value="60000"/>
        <!--
        <property name="poolPreparedStatements" value="true" />
        <property name="maxPoolPreparedStatementPerConnectionSize" value="33" />
        -->
        <property name="testOnBorrow" value="false"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="true"/>
        <!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
        <property name="timeBetweenEvictionRunsMillis" value="60000"/>
        <!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
        <property name="minEvictableIdleTimeMillis" value="3600000"/>
        <!-- 打开removeAbandoned功能 -->
        <property name="removeAbandoned" value="true"/>
        <!-- 1800秒，也就是30分钟 -->
        <property name="removeAbandonedTimeout" value="1800"/>
        <!-- 关闭abanded连接时输出错误日志 -->
        <property name="logAbandoned" value="true"/>
        <!-- 监控数据库 -->
        <!-- <property name="filters" value="stat" /> -->
        <property name="filters" value="mergeStat"/>
    </bean>
    <!-- ========================================针对myBatis的配置项============================== -->
    <!-- ==============================配置sqlSessionFactory============================== -->
    <bean id="sqlSessionFactorySaasPlus" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="plugins">
            <array>
                <bean class="com.github.pagehelper.PageInterceptor">
                    <property name="properties">
                        <!--使用下面的方式配置参数，一行配置一个 -->
                        <value>
                            helperDialect=mysql
                            rowBoundsWithCount=true
                            supportMethodsArguments=true
                            offsetAsPageNum=true
                            pageSizeZero=true
                            reasonable=false
                            returnPageInfo=check
                        </value>
                    </property>
                </bean>
            </array>
        </property>
        <property name="dataSource" ref="dataSourceSaasPlus"/>
        <property name="mapperLocations" value="classpath:mapper/saasplus/*.xml"/>
    </bean>
    <!-- ==============================配置扫描器============================== -->
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactorySaasPlus"/>
        <property name="basePackage" value="com.fenbeitong.saas.core.dao.saasplus"/>
        <property name="nameGenerator" ref="saasplusMapperBeanNameGenerator"/>
    </bean>
    <bean id="saasplusMapperBeanNameGenerator" class="com.fenbeitong.saas.core.common.MapperBeanNameGenerator">
        <property name="prefix" value="saasplus"/>
    </bean>
    <!-- ==============================配置Spring的事务管理器============================== -->
    <bean id="transactionManagerSaasPlus" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dataSourceSaasPlus"/>
        <qualifier value="saas_plus"/>
    </bean>
    <tx:annotation-driven transaction-manager="transactionManagerSaasPlus"/>

    <!-- ========================================针对spring-myBatis的配置项============================== -->
    <!-- 配置数据源，使用的是alibaba的Druid(德鲁伊)数据源 -->
    <bean name="dataSourceFenBeiRules" class="com.alibaba.druid.pool.DruidDataSource" init-method="init"
          destroy-method="close">
        <property name="driverClassName" value="${jdbc_driver_fenbeirules}"/>
        <property name="url" value="${jdbc_url_fenbeirules}"/>
        <property name="username" value="${jdbc_username_fenbeirules}"/>
        <property name="password" value="${jdbc_password_fenbeirules}"/>
        <!-- 初始化连接大小 -->
        <property name="initialSize" value="0"/>
        <!-- 连接池最大使用连接数量 -->
        <property name="maxActive" value="80"/>
        <!-- 连接池最大空闲 -->
        <!--<property name="maxIdle" value="20"/>-->
        <!-- 连接池最小空闲 -->
        <property name="minIdle" value="0"/>
        <!-- 获取连接最大等待时间 -->
        <property name="maxWait" value="60000"/>
        <!--
        <property name="poolPreparedStatements" value="true" />
        <property name="maxPoolPreparedStatementPerConnectionSize" value="33" />
        -->
        <property name="testOnBorrow" value="false"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="true"/>
        <!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
        <property name="timeBetweenEvictionRunsMillis" value="60000"/>
        <!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
        <property name="minEvictableIdleTimeMillis" value="25200000"/>
        <!-- 打开removeAbandoned功能 -->
        <property name="removeAbandoned" value="true"/>
        <!-- 1800秒，也就是30分钟 -->
        <property name="removeAbandonedTimeout" value="1800"/>
        <!-- 关闭abanded连接时输出错误日志 -->
        <property name="logAbandoned" value="true"/>
        <!-- 监控数据库 -->
        <!-- <property name="filters" value="stat" /> -->
        <property name="filters" value="mergeStat"/>
    </bean>
    <!-- ========================================针对myBatis的配置项============================== -->
    <!-- ==============================配置sqlSessionFactory============================== -->
    <bean id="sqlSessionFactoryFenBeiRules" class="org.mybatis.spring.SqlSessionFactoryBean">
        <!-- 实例化sqlSessionFactory时需要使用上述配置好的数据源以及SQL映射文件 -->
        <property name="plugins">
            <array>
                <bean class="com.github.pagehelper.PageInterceptor">
                    <property name="properties">
                        <!--使用下面的方式配置参数，一行配置一个 -->
                        <value>
                            helperDialect=mysql
                            rowBoundsWithCount=true
                            supportMethodsArguments=true
                            offsetAsPageNum=true
                            pageSizeZero=true
                            reasonable=false
                            returnPageInfo=check
                        </value>
                    </property>
                </bean>
            </array>
        </property>
        <property name="dataSource" ref="dataSourceFenBeiRules"/>
        <property name="mapperLocations" value="classpath:mapper/rules/**/*.xml"/>
    </bean>
    <!-- ==============================配置扫描器============================== -->
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactoryFenBeiRules"/>
        <property name="basePackage" value="com.fenbeitong.saas.core.dao.rules"/>
    </bean>
    <!-- ==============================配置Spring的事务管理器============================== -->
    <bean id="transactionManagerFenBeiRules" class="org.springframework.jdbc.datasource.DataSourceTransactionManager" primary="true">
        <property name="dataSource" ref="dataSourceFenBeiRules"/>
        <qualifier value="fenbei-rules"/>
    </bean>
    <tx:annotation-driven transaction-manager="transactionManagerFenBeiRules"/>

    <!-- dynamic 数据源 -->
    <bean id="dynamicDataSource" class="com.fenbeitong.saas.core.common.DynamicDataSource">
        <constructor-arg name="defaultDataSource" ref="dataSourceSaasPlus"/>
        <constructor-arg name="dataSourceMap">
            <map>
                <entry key="saas">
                    <ref bean="dataSourceSaas"/>
                </entry>
                <entry key="saas_plus">
                    <ref bean="dataSourceSaasPlus"/>
                </entry>
            </map>
        </constructor-arg>
    </bean>
    <!-- ========================================针对myBatis的配置项============================== -->
    <!-- ==============================配置sqlSessionFactory============================== -->
    <bean id="sqlSessionFactoryDynamic" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="dynamicDataSource"/>
        <property name="mapperLocations" value="classpath:mapper/dynamic/*.xml"/>
    </bean>
    <!-- ==============================配置扫描器============================== -->
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactoryDynamic"/>
        <property name="basePackage" value="com.fenbeitong.saas.core.dao.dynamic"/>
        <property name="nameGenerator" ref="dynamicMapperBeanNameGenerator"/>
    </bean>
    <bean id="dynamicMapperBeanNameGenerator" class="com.fenbeitong.saas.core.common.MapperBeanNameGenerator">
        <property name="prefix" value="dynamic"/>
    </bean>

    <bean id="transactionManagerDynamic" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dynamicDataSource"/>
        <qualifier value="dynamic"/>
    </bean>
    <tx:annotation-driven transaction-manager="transactionManagerDynamic"/>

    <!-- 使用cglib实现aop动态代理 -->
    <aop:aspectj-autoproxy proxy-target-class="true"/>
</beans>
