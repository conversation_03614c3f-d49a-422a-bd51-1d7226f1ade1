insert into sms_template ( "id", "name", "vendor_id", "remote_id", "body", "update_time", "create_time") values ( '593515063f9ef30f56132820', '302_23613', '303', '23613', '{1}在分贝通中提交了一笔{2}的{3}订单，请注意。如有疑问，请联系分贝通客服：{4}', '2017-06-17 13:55:43', '2017-06-17 13:55:45');
insert into sms_template ( "update_time", "id", "remote_id", "vendor_id", "create_time", "body", "name") values ( '2017-06-20 15:57:43', '5948c63c0a42b2f0aba637af', '24983', '303', '2017-06-20 15:57:43', '尊敬的客户，根据贵司设置，您当月在分贝通{1}消费的月限额为{2}，您已使用{3}。如有疑问，请联系管理员。', '302_24983');