ALTER TABLE "public"."mall_rule"
	ALTER COLUMN "limit_type" SET DEFAULT 3;
COMMENT ON COLUMN "public"."mall_rule"."limit_type" IS '限制类型：1:品类限制 2:sku限制 3:不限制';

ALTER TABLE "public"."taxi_rule"
	ADD COLUMN "day_price_limit" numeric(20,2) DEFAULT -1;
COMMENT ON COLUMN "public"."taxi_rule"."day_price_limit" IS '单日价格限制';

ALTER TABLE "public"."taxi_location"
	ADD COLUMN "location_type" int4 DEFAULT 1;
COMMENT ON COLUMN "public"."taxi_location"."location_type" IS '1：位置 2：城市';

ALTER TABLE "public"."taxi_location"
	ADD COLUMN "city_code" varchar(50);
COMMENT ON COLUMN "public"."taxi_location"."city_code" IS '城市编码';

ALTER TABLE "public"."taxi_location"
	ADD COLUMN "city_name" varchar(100);


update apply_trip_info set person_count=-1 where type=3;

ALTER TABLE "public"."apply_trip_applicate"
	ADD COLUMN "use_time" timestamp(6) NOT NULL DEFAULT now();