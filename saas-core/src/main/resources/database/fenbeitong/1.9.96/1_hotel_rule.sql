CREATE TABLE "public"."hotel_rule_province_limit" (
	"id" varchar(24) NOT NULL COLLATE "default",
	"rule_id" varchar(24) NOT NULL COLLATE "default",
	"province_id" varchar(10) NOT NULL COLLATE "default",
	"province_name" varchar(255) COLLATE "default",
	"price" numeric(10,2),
	"rank" int8,
	"create_time" timestamp(6) NULL
)
WITH (OIDS=FALSE);
ALTER TABLE "public"."hotel_rule_province_limit" OWNER TO "postgres";
ALTER TABLE "public"."hotel_rule_province_limit" ADD PRIMARY KEY ("rule_id", "province_id") NOT DEFERRABLE INITIALLY IMMEDIATE;

CREATE TABLE "public"."hotel_rule_price_limit" (
	"id" varchar(24) NOT NULL COLLATE "default",
	"province_limit_id" varchar(24) COLLATE "default",
	"price" numeric(10,2) DEFAULT 0,
	"type" int4 DEFAULT 1,
	"create_time" timestamp(6) NULL
)
WITH (OIDS=FALSE);
ALTER TABLE "public"."hotel_rule_price_limit" OWNER TO "postgres";

COMMENT ON COLUMN "public"."hotel_rule_price_limit"."price" IS '价格';
COMMENT ON COLUMN "public"."hotel_rule_price_limit"."type" IS '淡旺季：1-淡季，2-旺季';
ALTER TABLE "public"."hotel_rule_price_limit" ADD PRIMARY KEY ("id") NOT DEFERRABLE INITIALLY IMMEDIATE;

CREATE TABLE "public"."hotel_rule_city_limit" (
	"id" varchar(24) NOT NULL COLLATE "default",
	"price_limit_id" varchar(24) COLLATE "default",
	"area_id" varchar(10) COLLATE "default",
	"area_name" varchar(255) COLLATE "default",
	"rank" int8 DEFAULT 1,
	"create_time" timestamp(6) NULL
)
WITH (OIDS=FALSE);
ALTER TABLE "public"."hotel_rule_city_limit" OWNER TO "postgres";
ALTER TABLE "public"."hotel_rule_city_limit" ADD PRIMARY KEY ("id") NOT DEFERRABLE INITIALLY IMMEDIATE;

CREATE TABLE "public"."hotel_rule_date_limit" (
	"id" varchar(24) NOT NULL COLLATE "default",
	"price_limit_id" varchar(24) COLLATE "default",
	"start_date" varchar(10) COLLATE "default",
	"end_date" varchar(10) COLLATE "default",
	"rank" int8,
	"create_time" timestamp(6) NULL
)
WITH (OIDS=FALSE);
ALTER TABLE "public"."hotel_rule_date_limit" OWNER TO "postgres";
ALTER TABLE "public"."hotel_rule_date_limit" ADD PRIMARY KEY ("id") NOT DEFERRABLE INITIALLY IMMEDIATE;

ALTER TABLE "public"."hotel_rule"
  ADD COLUMN "type" int4 DEFAULT 1;
	ADD COLUMN "global_price" decimal DEFAULT -1;
	ADD COLUMN "rule_limit" bool DEFAULT false;
COMMENT ON COLUMN "public"."hotel_rule"."rule_limit" IS '是否限制价格';
COMMENT ON COLUMN "public"."hotel_rule"."global_price" IS '规则高级配置全局默认价格(-1=不限制)';
COMMENT ON COLUMN "public"."hotel_rule"."type" IS '1:标准模式,2:高级模式';

ALTER TABLE "public"."company_rule"
	ADD COLUMN "hotel_rule_price_type" int4 DEFAULT 1;
COMMENT ON COLUMN "public"."company_rule"."hotel_rule_price_type" IS '1:均价限制 2:最高价限制';
--历史数据默认按照最高价限制
update company_rule set hotel_rule_price_type=2;