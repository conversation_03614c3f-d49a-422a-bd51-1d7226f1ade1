CREATE TABLE dinner_rule(
  id char(24) NOT NULL,
  company_id VARCHAR(24) NOT NULL,
  name VARCHAR(60) NOT NULL ,
  dinner_frequency_limit_price NUMERIC(20,2)  NULL,
  dinner_everyday_limit_price NUMERIC(20,2)  NULL,
  create_time TIMESTAMP NULL,
  update_time TIMESTAMP NULL,
  PRIMARY KEY (id)
);
comment on table name is
'用餐规则';
comment on column dinner_rule.name is
'规则名称';
comment on column dinner_rule.dinner_frequency_limit_price is
'用餐每次限制金额';
comment on column dinner_rule.dinner_everyday_limit_price is
'用餐每日限制金额';


CREATE TABLE employee_dinner_rule(
  employee_id char(24) NOT NULL,
  dinner_rule int4,
  dinner_rule_flag  bool NOT NULL,
  dinner_month_flag bool NOT NULL DEFAULT false,
	dinner_month_cost_limit numeric(21,4),
  default_dinner_rule_id VARCHAR(24) NULL,
  manual_dinner_rule_id VARCHAR(24) NULL,
  exceed_buy_flag  int4 NOT NULL DEFAULT 1,
  dinner_verify_flag bool NOT NULL DEFAULT false,
  PRIMARY KEY (employee_id)
);
comment on table name is
'员工用餐规则';
comment on column employee_dinner_rule.dinner_rule is
'是否需要权限';
comment on column employee_dinner_rule.dinner_rule_flag is
'是否应用规则';
comment on column employee_dinner_rule.dinner_verify_flag is
'是否需要审批';
comment on column employee_dinner_rule.default_dinner_rule_id is
'默认规则id';
comment on column employee_dinner_rule.manual_dinner_rule_id is
'应用规则id';
comment on column employee_dinner_rule.exceed_buy_flag is
'1:超出餐标由个人支付 2:超出餐标由企业支付';


ALTER TABLE "public"."apply_order"
	ADD COLUMN "third_id" varchar(100),
	ADD COLUMN "third_remark" varchar(2000);
COMMENT ON COLUMN "public"."apply_order"."third_id" IS '第三方id';
COMMENT ON COLUMN "public"."apply_order"."third_remark" IS '第三方备注';


ALTER TABLE "public"."taxi_rule"
	ALTER COLUMN "departure_location_id" TYPE varchar(1000),
	ALTER COLUMN "arrival_location_id" TYPE varchar(1000);

ALTER TABLE "public"."taxi_location"
	ADD COLUMN "comment_name" varchar(500);


ALTER TABLE "public"."company_rule"
	ADD COLUMN "dinner_rule" int4 DEFAULT 0,
	ADD COLUMN "dinner_money_flag" bool DEFAULT false;


INSERT INTO employee_dinner_rule(
 employee_id,dinner_rule,dinner_rule_flag,dinner_month_flag
) SELECT ID,1,false,false FROM employee where status!=4;


insert into fb_order_comment_item(title,TYPE,create_time) values ('市场拓展',5,now());

insert into fb_order_comment_item(title,TYPE,create_time) values ('商务洽谈',5,now());

insert into fb_order_comment_item(title,TYPE,create_time) values ('公关',5,now());

insert into fb_order_comment_item(title,TYPE,create_time) values ('其他',5,now());



insert into fb_order_exceed_reason_item(title,TYPE,create_time) values ('重要客户',4,now());

insert into fb_order_exceed_reason_item(title,TYPE,create_time) values ('突发事件',4,now());

insert into fb_order_exceed_reason_item(title,TYPE,create_time) values ('其他',4,now());
