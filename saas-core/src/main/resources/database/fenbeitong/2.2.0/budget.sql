CREATE TABLE "public"."org_consume_statistics" (
	"id" SERIAL NOT NULL,
	"company_id" varchar NOT NULL,
	"consumer_id" varchar NOT NULL,
	"consumer_type" int2 NOT NULL,
	"consume_category" int2 NOT NULL,
	"total_amount" numeric(20,2) NOT NULL,
	"total_count" numeric(20,2) NOT NULL,
	"statistics_time" int8 NOT NULL,
	"create_time" timestamp(6) NULL DEFAULT now(),
	"update_time" timestamp(6) NULL DEFAULT now(),
	PRIMARY KEY ("id") NOT DEFERRABLE INITIALLY IMMEDIATE
)
WITH (OIDS=FALSE);
COMMENT ON TABLE "public"."org_consume_statistics" IS '预算统计表';
COMMENT ON COLUMN "public"."org_consume_statistics"."id" IS '主键';
COMMENT ON COLUMN "public"."org_consume_statistics"."company_id" IS '公司id';
COMMENT ON COLUMN "public"."org_consume_statistics"."consumer_id" IS '个人/部门/项目id';
COMMENT ON COLUMN "public"."org_consume_statistics"."consumer_type" IS '1、个人  2、部门  3、项目';
COMMENT ON COLUMN "public"."org_consume_statistics"."consume_category" IS '业务类型（3、用车 7、国内机票 11、酒店 15、火车票 20、采购 30、用餐 40、国际机票）';
COMMENT ON COLUMN "public"."org_consume_statistics"."total_amount" IS '当天总金额';
COMMENT ON COLUMN "public"."org_consume_statistics"."total_count" IS '当天总数';
COMMENT ON COLUMN "public"."org_consume_statistics"."statistics_time" IS '计算时间 格式YYYYMMDD';

CREATE INDEX org_consume_statistics_company_consumer_idx
ON org_consume_statistics (company_id ASC, consumer_id ASC);
CREATE INDEX consume_statistics_time_idx
ON org_consume_statistics (statistics_time DESC);


