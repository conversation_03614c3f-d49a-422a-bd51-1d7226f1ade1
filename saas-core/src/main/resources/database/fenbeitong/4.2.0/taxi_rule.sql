-- 用车规则路线表
CREATE TABLE taxi_path_location
(
    id SERIAL PRIMARY KEY NOT NULL,
    company_id VARCHAR(32) NOT NULL,
    taxi_rule_id INTEGER NOT NULL,
    limit_departure BOOLEAN DEFAULT false NOT NULL,
    departure_location_id INTEGER DEFAULT -1,
    limit_arrival BOOLEAN DEFAULT false NOT NULL,
    arrival_location_id INTEGER DEFAULT -1,
    create_time TIMESTAMP DEFAULT now()
);
CREATE UNIQUE INDEX taxi_path_location_id_uindex ON taxi_path_location (id);
COMMENT ON TABLE taxi_path_location IS '用车规则路线';
COMMENT ON COLUMN taxi_path_location.id IS '主键id';
COMMENT ON COLUMN taxi_path_location.company_id IS '公司id';
COMMENT ON COLUMN taxi_path_location.taxi_rule_id IS '用车规则id';
COMMENT ON COLUMN taxi_path_location.limit_departure IS '是否限制起始位置';
COMMENT ON COLUMN taxi_path_location.departure_location_id IS '限制起始位置id';
COMMENT ON COLUMN taxi_path_location.limit_arrival IS '是否限制到达位置';
COMMENT ON COLUMN taxi_path_location.arrival_location_id IS '限制到达位置id';
COMMENT ON COLUMN taxi_path_location.create_time IS '创建时间';

-- 用车规则表添加限制路线字段
ALTER TABLE taxi_rule ADD limit_path BOOLEAN DEFAULT false NULL;
COMMENT ON COLUMN taxi_rule.limit_path IS '是否限制路线';