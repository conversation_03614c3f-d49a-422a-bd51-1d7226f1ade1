---------------fenbeitong---------------
ALTER TABLE "public"."frequent_contact"
 ADD COLUMN "family_name" varchar(50),
 ADD COLUMN "given_name" varchar(50),
 ADD COLUMN "cert_valid_date" timestamp(6) NULL,
 ADD COLUMN "nationality" varchar(50);
COMMENT ON COLUMN "public"."frequent_contact"."family_name" IS '姓氏（英文）';
COMMENT ON COLUMN "public"."frequent_contact"."given_name" IS '名（英文）';
COMMENT ON COLUMN "public"."frequent_contact"."cert_valid_date" IS '证件有效期';
COMMENT ON COLUMN "public"."frequent_contact"."nationality" IS '国籍';

ALTER TABLE "public"."frequent_contact"
	ADD COLUMN "nationality_name" varchar(50);
COMMENT ON COLUMN "public"."frequent_contact"."nationality_name" IS '国籍名称';

CREATE TABLE "public"."employee_intl_air_rule" (
	"employee_id" char(24) NOT NULL COLLATE "default",
	"air_rule" int4 DEFAULT 1,
	"air_month_flag" bool NOT NULL DEFAULT false,
	"air_month_cost_limit" numeric(21,4),
	"air_rule_flag" bool NOT NULL DEFAULT false,
	"default_air_rule_id" char(24) COLLATE "default",
	"manual_air_rule_id" char(24) COLLATE "default",
	"air_verify_flag" bool NOT NULL DEFAULT false,
	"exceed_buy_flag" bool NOT NULL DEFAULT false,
	"unemployee_air" bool DEFAULT false,
	"exceed_buy_type" int4 DEFAULT 1,
	PRIMARY KEY (employee_id)
);

INSERT INTO employee_intl_air_rule(
 employee_id,air_rule
) SELECT ID,1 FROM employee where status!=4;

---------------saas---------------
ALTER TABLE "public"."apply_order"
	ADD COLUMN "cost_attribution_id" varchar(50),
	ADD COLUMN "cost_attribution_name" varchar(50),
	ADD COLUMN "cost_attribution_category" int4 DEFAULT 1;
COMMENT ON COLUMN "public"."apply_order"."cost_attribution_id" IS '费用归属id';
COMMENT ON COLUMN "public"."apply_order"."cost_attribution_name" IS '费用归属名称';
COMMENT ON COLUMN "public"."apply_order"."cost_attribution_category" IS '费用归属类型';


ALTER TABLE "public"."company_apply_setting"
	ADD COLUMN "cost_attribution_category" int4 DEFAULT 1;
COMMENT ON COLUMN "public"."company_apply_setting"."cost_attribution_category" IS '1.部门 2.项目 3.个人';

update company_apply_setting set flow_name='部门默认审批流' where company_setting_type = 2 and can_delete = 2 and apply_status = 1;

insert into "public"."message_setup" ( "str_val3", "int_val1", "item_code", "company_id", "int_val2", "int_val3", "str_val1", "str_val2", "create_time", "update_time", "is_checked") values ( null, null, 'whether_use_apply', '595c4c225f281a59eb295bb5', null, null, '请使用志远oa提交审批流程', null, '2018-04-04 15:48:56.351521', '2018-04-04 15:48:56.351521', '0');

insert into "public"."message_setup_item" ( "default_int_val3", "default_int_val1", "item_code", "default_str_val2", "busi_code", "default_int_val2", "default_str_val3", "default_checked", "default_str_val1", "create_time", "update_time") values ( null, null, 'whether_use_apply', null, 'apply_setting_up', null, null, '1', null, '2018-04-04 15:48:56.335709', '2018-04-04 15:48:56.335709');