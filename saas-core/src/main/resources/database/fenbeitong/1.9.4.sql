----saas
ALTER TABLE "public"."apply_order"
	ADD COLUMN "applicant_name" varchar(50);
COMMENT ON COLUMN "public"."apply_order"."applicant_name" IS '申请人名称';

ALTER TABLE "public"."apply_trip_info"
	ADD COLUMN "order_time" timestamp NULL,
	ADD COLUMN "person_count" int4;

----fenbeitong
ALTER TABLE "public"."hotel_rule"
	ADD COLUMN "priv_day_min" int4,
	ADD COLUMN "priv_day_max" int4;
COMMENT ON COLUMN "public"."hotel_rule"."priv_day_min" IS '最少提前天数';
COMMENT ON COLUMN "public"."hotel_rule"."priv_day_max" IS '最多提前天数';


CREATE TABLE "public"."dinner_time_range" (
	"id" SERIAL NOT NULL ,
	"rule_id" varchar(50) NOT NULL,
	"day_type" int4 NOT NULL,
	"begin_time" time(6) NOT NULL,
	"end_time" time(6) NOT NULL,
	"is_overnight" bool NOT NULL DEFAULT false,
	"batch_id" int8 DEFAULT 0,
	PRIMARY KEY (id)
);


