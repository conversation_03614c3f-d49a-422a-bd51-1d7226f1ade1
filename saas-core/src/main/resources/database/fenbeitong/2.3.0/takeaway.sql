CREATE TABLE takeaway_intercept_record
(
    id CHAR(24) PRIMARY KEY NOT NULL,
    employee_id CHAR(24) NOT NULL,
    company_id CHAR(24) NOT NULL,
    create_time TIMESTAMP NOT NULL,
    contact_name VARCHAR(24),
    contact_phone VARCHAR(24),
    channel INTEGER DEFAULT 0 NOT NULL,
    total_price NUMERIC(21,4) NOT NULL,
    takeaway_rule INTEGER NOT NULL,
    takeaway_rule_flag BOOLEAN NOT NULL,
    limit_time BOOLEAN DEFAULT false NOT NULL,
    takeaway_time_range_info VARCHAR(4000),
    limit_location BOOLEAN DEFAULT false NOT NULL,
    takeaway_location_info VARCHAR(1024),
    err_msg VARCHAR(360) DEFAULT ''::character varying NOT NULL,
    err_code INTEGER DEFAULT 0 NOT NULL,
    cost_center_id VARCHAR,
    cost_center_type INTEGER,
    exceed_buy_type INTEGER DEFAULT 1,
    personal_pay BOOLEAN NOT NULL
);
COMMENT ON COLUMN takeaway_intercept_record.id IS '主键';
COMMENT ON COLUMN takeaway_intercept_record.employee_id IS '员工id';
COMMENT ON COLUMN takeaway_intercept_record.company_id IS '公司id';
COMMENT ON COLUMN takeaway_intercept_record.create_time IS '创建时间';
COMMENT ON COLUMN takeaway_intercept_record.contact_name IS '联系人';
COMMENT ON COLUMN takeaway_intercept_record.contact_phone IS '联系人电话';
COMMENT ON COLUMN takeaway_intercept_record.channel IS '下单渠道';
COMMENT ON COLUMN takeaway_intercept_record.total_price IS '订单金额';
COMMENT ON COLUMN takeaway_intercept_record.takeaway_rule IS '外卖权限 1.不允许 4.允许';
COMMENT ON COLUMN takeaway_intercept_record.takeaway_rule_flag IS '限制规则 true.限制规则 false.不限制规则';
COMMENT ON COLUMN takeaway_intercept_record.limit_time IS '时段限制 true.限制 false.不限制';
COMMENT ON COLUMN takeaway_intercept_record.takeaway_time_range_info IS '时段规则信息';
COMMENT ON COLUMN takeaway_intercept_record.limit_location IS '地址限制 true.限制 false.不限制';
COMMENT ON COLUMN takeaway_intercept_record.takeaway_location_info IS '地址规则信息';
COMMENT ON COLUMN takeaway_intercept_record.err_msg IS '错误信息(超规信息)';
COMMENT ON COLUMN takeaway_intercept_record.err_code IS '异常错误码';
COMMENT ON COLUMN takeaway_intercept_record.cost_center_id IS '费用归属id';
COMMENT ON COLUMN takeaway_intercept_record.cost_center_type IS '费用归属类型 1.部门 2.项目';
COMMENT ON COLUMN takeaway_intercept_record.exceed_buy_type IS '超规控制 1.禁止下单 2.填写理由';
COMMENT ON COLUMN takeaway_intercept_record.personal_pay IS '个人支付 true.开启 false.不开启';