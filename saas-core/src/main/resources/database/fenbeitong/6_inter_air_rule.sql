--国际机票规则表
CREATE TABLE "public"."intl_air_rule" (
	"id" char(24) NOT NULL COLLATE "default",
	"company_id" char(24) NOT NULL COLLATE "default",
	"name" varchar(60) NOT NULL COLLATE "default",
	"air_cabin_flag" bool NOT NULL,
	"air_cabin_type" varchar(50) COLLATE "default",
	"air_price_flag" bool NOT NULL,
	"air_unit_price" numeric(21,2),
	"modify_time" timestamp(6) NULL,
	"air_discount_flag" bool,
	"air_discount" numeric(5,3),
	"priv_day_min" int4,
	"priv_day_max" int4,
	CONSTRAINT "inter_air_rule_pkey" PRIMARY KEY ("id") NOT DEFERRABLE INITIALLY IMMEDIATE
)
WITH (OIDS=FALSE);
ALTER TABLE "public"."intl_air_rule" OWNER TO "postgres";
CREATE INDEX  "inter_air_rule_pkey" ON "public"."intl_air_rule" USING btree("id" COLLATE "default" "pg_catalog"."bpchar_ops" ASC NULLS LAST);


