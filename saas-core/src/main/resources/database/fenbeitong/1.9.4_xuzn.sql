ALTER TABLE "public"."company_employee" 
	ADD COLUMN "exceed_buy_type" int4 NOT NULL DEFAULT 1,
	ADD COLUMN "allow_shuttle" bool NOT NULL DEFAULT true;


ALTER TABLE "public"."train_rule" 
	ADD COLUMN "grap_common_seat_type" varchar,
	ADD COLUMN "grap_highspeed_seat_type" varchar,
	ADD COLUMN "price_limit" numeric,
	ADD COLUMN "priv_day_min" int4,
	ADD COLUMN "priv_day_max" int4,
	ADD COLUMN "day_limit" bool DEFAULT false,
	ADD COLUMN "is_same_seat" bool DEFAULT false;


CREATE TABLE "public"."air_time_range"(
	"id" SERIAL PRIMARY KEY,
	"rule_id" varchar NOT NULL,
	"day_type" int4 NOT NULL,
	"begin_time" TIME(6) NOT NULL,
	"end_time" TIME(6) NOT NULL,
	"is_overnight" bool NOT NULL DEFAULT FALSE,
	"batch_id" int8 DEFAULT 0
) WITH(OIDS = FALSE);

ALTER TABLE "public"."air_time_range" OWNER TO "postgres";


ALTER TABLE "public"."air_rule"
 ADD COLUMN "priv_day_min" int4,
 ADD COLUMN "priv_day_max" int4;


ALTER TABLE "public"."employee_air_rule" 
	ADD COLUMN "priv_order" bool DEFAULT false,
	ADD COLUMN "min_priv_day" int4,
	ADD COLUMN "max_priv_day" int4;


ALTER TABLE "public"."employee_train_rule" 
	ADD COLUMN "priv_order" bool DEFAULT false,
	ADD COLUMN "recommended_train_flag" bool DEFAULT false,
	ADD COLUMN "day_limit" bool DEFAULT false;


UPDATE "public"."company_employee" SET exceed_buy_type = CASE exceed_buy_flag WHEN TRUE THEN 2 WHEN FALSE THEN 1 END;