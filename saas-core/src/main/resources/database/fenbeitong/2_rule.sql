ALTER TABLE "public"."air_rule"
	ADD COLUMN "air_discount_flag" bool default false,
	ADD COLUMN "air_discount" numeric(5,3);


ALTER TABLE "public"."hotel_rule"
	ADD COLUMN "level" varchar(100) DEFAULT '0,1,2,3,4';

ALTER TABLE "public"."taxi_rule"
	ADD COLUMN "price_limit_flag" bool default false,
	ADD COLUMN "price_limit" numeric(20,4);

--初始化fenbeitong数据库采购规则相关数据表
CREATE TABLE mall_rule (
  id                  CHAR(24)    NOT NULL PRIMARY KEY, -- 主键，唯一标识该rule
  company_id          CHAR(24)    NOT NULL, --所属公司id
  name                VARCHAR(60) NOT NULL, -- 规则名称

  limit_category_ids  VARCHAR, -- 允许购买的品类,是一个字符数组

  limit_price_flag    BOOLEAN     NOT NULL, --是否设定单笔订单购买价格限制,false:不限制,true:限制
  limit_price_highest NUMERIC(21, 4), --单日订单价格,最高限额

  modify_time         TIMESTAMP --最后修改时间
);