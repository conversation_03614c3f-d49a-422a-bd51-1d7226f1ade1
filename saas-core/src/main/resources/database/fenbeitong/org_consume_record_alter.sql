ALTER TABLE public.org_consume_record ADD pre_order_id VARCHAR NULL;
COMMENT ON COLUMN public.org_consume_record.pre_order_id IS '父订单id';
ALTER TABLE public.org_consume_record ADD origin_order_id VARCHAR NULL;
COMMENT ON COLUMN public.org_consume_record.origin_order_id IS '原始订单id';
ALTER TABLE public.org_consume_record ADD is_latest BOOLEAN DEFAULT TRUE NOT NULL;
COMMENT ON COLUMN public.org_consume_record.is_latest IS '是否最新记录';
ALTER TABLE public.org_consume_record ADD request_time TIMESTAMP DEFAULT now() NOT NULL;
COMMENT ON COLUMN public.org_consume_record.request_time IS '请求时间';
ALTER TABLE public.org_consume_record ADD param_json VARCHAR NULL;
COMMENT ON COLUMN public.org_consume_record.param_json IS '请求参数';

update org_consume_record
SET request_time = create_time;
