CREATE TABLE "public"."org_consume_record_log" (
	"id" int8 NOT NULL DEFAULT nextval('org_consume_record_id_seq'::regclass),
	"consumer_id" varchar NOT NULL COLLATE "default",
	"consumer_name" varchar NOT NULL COLLATE "default",
	"company_id" varchar NOT NULL COLLATE "default",
	"order_id" varchar NOT NULL COLLATE "default",
	"amount" numeric(20,2) NOT NULL,
	"consume_category" int2 NOT NULL,
	"consumer_type" int2 NOT NULL,
	"create_time" timestamp(6) NOT NULL DEFAULT now(),
	"pre_order_id" varchar COLLATE "default",
	"origin_order_id" varchar COLLATE "default",
	"is_latest" bool NOT NULL DEFAULT true,
	"request_time" timestamp(6) NOT NULL DEFAULT now(),
	"param_json" varchar COLLATE "default",
	CONSTRAINT "org_consume_record_log_pkey" PRIMARY KEY ("id") NOT DEFERRABLE INITIALLY IMMEDIATE
)
WITH (OIDS=FALSE);
COMMENT ON COLUMN "public"."org_consume_record_log"."pre_order_id" IS '父订单id';
COMMENT ON COLUMN "public"."org_consume_record_log"."origin_order_id" IS '原始订单id';
COMMENT ON COLUMN "public"."org_consume_record_log"."is_latest" IS '是否最新记录';
COMMENT ON COLUMN "public"."org_consume_record_log"."request_time" IS '请求时间';
COMMENT ON COLUMN "public"."org_consume_record_log"."param_json" IS '请求参数';
CREATE INDEX  "ix_org_consume_record_log_company_id" ON "public"."org_consume_record_log" USING btree(company_id COLLATE "default" "pg_catalog"."text_ops" ASC NULLS LAST, consumer_id COLLATE "default" "pg_catalog"."text_ops" ASC NULLS LAST);
CREATE INDEX  "ix_org_consume_record_log_consumer_id" ON "public"."org_consume_record_log" USING btree(consumer_id COLLATE "default" "pg_catalog"."text_ops" ASC NULLS LAST);
CREATE INDEX  "org_consume_record_log_create_time_index" ON "public"."org_consume_record_log" USING btree(create_time "pg_catalog"."timestamp_ops" ASC NULLS LAST);