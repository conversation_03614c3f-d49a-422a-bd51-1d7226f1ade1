CREATE TABLE apply_cost_attribution (
  id SERIAL NOT NULL,
  apply_id char(24) NOT NULL,
  cost_attribution_id varchar(500) NOT NULL,
  cost_attribution_name varchar(500) NOT NULL,
  cost_attribution_category int4 NOT NULL,
  company_id varchar(50) NOT NULL,
  create_time timestamp(6) NOT NULL DEFAULT now(),
  bring_in int4 NOT NULL DEFAULT 0,
  PRIMARY KEY ("id") NOT DEFERRABLE INITIALLY IMMEDIATE
);
CREATE  INDEX  "aca_query_index" ON "public"."apply_cost_attribution" USING btree(company_id,apply_id);
COMMENT ON TABLE apply_cost_attribution IS '审配单费用归属表';
COMMENT ON COLUMN apply_cost_attribution.id IS '主键';
COMMENT ON COLUMN apply_cost_attribution.company_id IS '公司id';
COMMENT ON COLUMN apply_cost_attribution.apply_id IS '申请单id';
COMMENT ON COLUMN apply_cost_attribution.cost_attribution_id IS '费用归属id';
COMMENT ON COLUMN apply_cost_attribution.cost_attribution_name IS '费用归属name';
COMMENT ON COLUMN apply_cost_attribution.cost_attribution_category IS '费用归属类型';
COMMENT ON COLUMN apply_cost_attribution.bring_in IS '0.不带入 1.带入';