-- 自定义事由表
CREATE TABLE company_custom_reason
(
    id SERIAL NOT NULL,
    name VARCHAR(50) NOT NULL,
    company_id VARCHAR(50) NOT NULL,
    status INTEGER NOT NULL DEFAULT 1,
    operator_id VARCHAR(50),
    create_time TIMESTAMP(6) DEFAULT now(),
    update_time TIMESTAMP(6) DEFAULT now(),
    PRIMARY KEY ("id") NOT DEFERRABLE INITIALLY IMMEDIATE
);
COMMENT ON TABLE company_custom_reason IS '公司自定义事由表';
COMMENT ON COLUMN company_custom_reason.id IS '主键';
COMMENT ON COLUMN company_custom_reason.name IS '事由名称';
COMMENT ON COLUMN company_custom_reason.company_id IS '公司id';
COMMENT ON COLUMN company_custom_reason.status IS '状态 1-正常 0-删除';
COMMENT ON COLUMN company_custom_reason.operator_id IS '操作人id';
COMMENT ON COLUMN company_custom_reason.create_time IS '创建时间';
COMMENT ON COLUMN company_custom_reason.update_time IS '更新时间';
CREATE INDEX company_custom_reason_company_id_index ON company_custom_reason (company_id);

-- 事由使用关系表
CREATE TABLE company_reason_setting_use
(
    id SERIAL NOT NULL,
    company_id VARCHAR(50) NOT NULL,
    reason_category INTEGER NOT NULL,
    reason_type INTEGER NOT NULL,
    reason_id INTEGER NOT NULL,
    sort INTEGER NOT NULL,
    operator_id VARCHAR(50),
    create_time TIMESTAMP DEFAULT now(),
    PRIMARY KEY ("id") NOT DEFERRABLE INITIALLY IMMEDIATE
);
COMMENT ON TABLE company_reason_setting_use IS '公司事由使用配置';
COMMENT ON COLUMN company_reason_setting_use.id IS '主键id';
COMMENT ON COLUMN company_reason_setting_use.company_id IS '公司id';
COMMENT ON COLUMN company_reason_setting_use.reason_category IS '事由大类型 1-订单事由 2-申请单事由 3-超规订单事由';
COMMENT ON COLUMN company_reason_setting_use.reason_type IS '事由类型';
COMMENT ON COLUMN company_reason_setting_use.reason_id IS '事由id';
COMMENT ON COLUMN company_reason_setting_use.sort IS '排序';
COMMENT ON COLUMN company_reason_setting_use.operator_id IS '操作人id';
COMMENT ON COLUMN company_reason_setting_use.create_time IS '创建时间';
CREATE UNIQUE INDEX company_reason_setting_use_reason_type_reason_id_uindex ON company_reason_setting_use (reason_type, reason_id);
CREATE INDEX company_reason_setting_use_company_id_reason_type_index ON company_reason_setting_use (company_id, reason_type);

-- 初始化默认事由项数据
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('办公用品', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('不想去了', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('不想住了', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('不想走了', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('出差', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('地点有变动', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('公关', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('购买数量增加', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('行程变更', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('行程取消', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('航班延误', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('加班', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('紧急寄件', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('开发市场', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('临时出行', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('没合适车型', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('没票/没房了', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('陪客户出行', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('陪领导出差', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('陪领导出行', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('其他', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('商品价格上涨', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('商务', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('商务洽谈', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('时间不合适', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('特殊寄件', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('填错信息', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('突发事件', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('为客户购买', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('选错车次', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('选错航班', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('选错日期', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('学习培训', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('有退改可能', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('员工福利', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('重要寄件', 'default', 1);
INSERT INTO public.company_custom_reason (name, company_id, status) VALUES ('重要客户', 'default', 1);

-- 订单事由必填设置历史数据问题修复
UPDATE message_setup
    SET int_val1 = (
        CASE item_code
          WHEN 'order_reason_air' THEN 7
          WHEN 'order_reason_inter_air' THEN 40
          WHEN 'order_reason_hotel' THEN 11
          WHEN 'order_reason_dinner' THEN 30
          WHEN 'order_reason_train' THEN 15
          WHEN 'order_reason_mall' THEN 20
          WHEN 'order_reason_car' THEN 3
          WHEN 'order_reason_takeaway' THEN 50
          WHEN 'order_reason_express' THEN 130
          WHEN 'order_reason_sf_express' THEN 131
          WHEN 'order_reason_virtual_card' THEN 126
          ELSE NULL
        END
    )
WHERE item_code LIKE 'order_reason_%'
AND item_code != 'order_reason_additions'
AND int_val1 IS NULL;


-- 审批单表添加事由id字段
ALTER TABLE public.apply_order ADD apply_reason_id INTEGER NULL;
COMMENT ON COLUMN public.apply_order.apply_reason_id IS '审批事由id';
ALTER TABLE public.apply_order ADD change_reason_id INTEGER NULL;
COMMENT ON COLUMN public.apply_order.change_reason_id IS '变更事由id';
ALTER TABLE public.apply_order ADD cancel_reason_id INTEGER NULL;
COMMENT ON COLUMN public.apply_order.cancel_reason_id IS '作废事由id';
ALTER TABLE public.apply_trip_info ADD order_reason_id INTEGER NULL;
COMMENT ON COLUMN public.apply_trip_info.order_reason_id IS '订单事由id';

-- 操作日志表
CREATE TABLE custom_reason_operate_log
(
    id SERIAL PRIMARY KEY NOT NULL,
    company_id VARCHAR(50) NOT NULL,
    operator_id VARCHAR(50) NOT NULL,
    operate_type INTEGER NOT NULL,
    sub_type INTEGER,
    param_json VARCHAR NOT NULL,
    create_time TIMESTAMP(6) DEFAULT now()
);
COMMENT ON TABLE custom_reason_operate_log IS '自定义字段配置操作日志表';
COMMENT ON COLUMN custom_reason_operate_log.id IS '主键';
COMMENT ON COLUMN custom_reason_operate_log.company_id IS '公司id';
COMMENT ON COLUMN custom_reason_operate_log.operator_id IS '操作人id';
COMMENT ON COLUMN custom_reason_operate_log.operate_type IS '操作类型';
COMMENT ON COLUMN custom_reason_operate_log.sub_type IS '操作子类型';
COMMENT ON COLUMN custom_reason_operate_log.param_json IS '参数json';
COMMENT ON COLUMN custom_reason_operate_log.create_time IS '创建时间';
CREATE INDEX custom_reason_operate_log_company_id_operate_type_index ON custom_reason_operate_log (company_id, operate_type, sub_type);

-- 初始化默认事由配置
-- 订单事由必填
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('order_reason', 'order_reason_virtual_card', 0);
-- 订单事由补充必填
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked, default_int_val1) VALUES ('order_reason', 'order_reason_air_desc', 0, 7);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked, default_int_val1) VALUES ('order_reason', 'order_reason_inter_air_desc', 0, 40);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked, default_int_val1) VALUES ('order_reason', 'order_reason_hotel_desc', 0, 11);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked, default_int_val1) VALUES ('order_reason', 'order_reason_dinner_desc', 0, 30);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked, default_int_val1) VALUES ('order_reason', 'order_reason_train_desc', 0, 15);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked, default_int_val1) VALUES ('order_reason', 'order_reason_mall_desc', 0, 20);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked, default_int_val1) VALUES ('order_reason', 'order_reason_car_desc', 0, 3);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked, default_int_val1) VALUES ('order_reason', 'order_reason_takeaway_desc', 0, 50);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked, default_int_val1) VALUES ('order_reason', 'order_reason_express_desc', 0, 130);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked, default_int_val1) VALUES ('order_reason', 'order_reason_sf_express_desc', 0, 131);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked, default_int_val1) VALUES ('order_reason', 'order_reason_virtual_card_desc', 0, 126);

-- 审批单事由必填
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('apply_setting_up', 'apply_reason_exceed_order_air', 1);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('apply_setting_up', 'apply_reason_exceed_order_intl_air', 1);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('apply_setting_up', 'apply_reason_exceed_order_hotel', 1);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('apply_setting_up', 'apply_reason_exceed_order_train', 1);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('apply_setting_up', 'apply_reason_refund_air', 1);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('apply_setting_up', 'apply_reason_change_air', 1);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('apply_setting_up', 'apply_reason_refund_hotel', 1);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('apply_setting_up', 'apply_reason_refund_train', 1);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('apply_setting_up', 'apply_reason_change_train', 1);
-- 审批单事由补充必填
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('apply_setting_up', 'apply_reason_chailv_desc', 0);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('apply_setting_up', 'apply_reason_chailv_change_desc', 0);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('apply_setting_up', 'apply_reason_taxi_desc', 0);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('apply_setting_up', 'apply_reason_coupon_desc', 0);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('apply_setting_up', 'apply_reason_dinner_desc', 0);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('apply_setting_up', 'apply_reason_takeaway_desc', 0);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('apply_setting_up', 'apply_reason_bank_individual_desc', 0);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('apply_setting_up', 'apply_reason_exceed_order_air_desc', 0);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('apply_setting_up', 'apply_reason_exceed_order_intl_air_desc', 0);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('apply_setting_up', 'apply_reason_exceed_order_hotel_desc', 0);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('apply_setting_up', 'apply_reason_exceed_order_train_desc', 0);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('apply_setting_up', 'apply_reason_refund_air_desc', 0);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('apply_setting_up', 'apply_reason_change_air_desc', 0);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('apply_setting_up', 'apply_reason_refund_hotel_desc', 0);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('apply_setting_up', 'apply_reason_refund_train_desc', 0);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('apply_setting_up', 'apply_reason_change_train_desc', 0);

-- 订单超规填写理由事由必填
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('exceed_order_reason', 'exceed_order_reason_air', 1);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('exceed_order_reason', 'exceed_order_reason_inter_air', 1);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('exceed_order_reason', 'exceed_order_reason_hotel', 1);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('exceed_order_reason', 'exceed_order_reason_dinner', 1);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('exceed_order_reason', 'exceed_order_reason_train', 1);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('exceed_order_reason', 'exceed_order_reason_mall', 1);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('exceed_order_reason', 'exceed_order_reason_car', 1);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('exceed_order_reason', 'exceed_order_reason_takeaway', 1);
-- 订单超规填写理由事由补充必填
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('exceed_order_reason', 'exceed_order_reason_air_desc', 0);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('exceed_order_reason', 'exceed_order_reason_inter_air_desc', 0);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('exceed_order_reason', 'exceed_order_reason_hotel_desc', 0);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('exceed_order_reason', 'exceed_order_reason_dinner_desc', 0);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('exceed_order_reason', 'exceed_order_reason_train_desc', 0);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('exceed_order_reason', 'exceed_order_reason_mall_desc', 0);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('exceed_order_reason', 'exceed_order_reason_car_desc', 0);
INSERT INTO public.message_setup_item (busi_code, item_code, default_checked) VALUES ('exceed_order_reason', 'exceed_order_reason_takeaway_desc', 0);

-- 订单事由补充设置洗数据
INSERT INTO message_setup (company_id, item_code, is_checked, int_val1)
SELECT a.company_id, b.item_code, a.is_checked, b.default_int_val1
FROM
  (SELECT *
  FROM message_setup
  WHERE item_code = 'order_reason_additions'
  AND is_checked = 1) a,
  (SELECT *
  FROM message_setup_item
  WHERE busi_code = 'order_reason'
  AND item_code LIKE '%_desc'
  AND item_code != 'order_reason_exceed_desc') b,
  (SELECT *
  FROM message_setup
  WHERE item_code LIKE 'order_reason_%'
  AND item_code != 'order_reason_additions'
  AND is_checked = 1) c
WHERE a.company_id = c.company_id
AND b.item_code = concat(c.item_code, '_desc')
AND c.is_checked = 1
ORDER BY company_id, item_code;

-- 超规审批及超规填写理由补充设置洗数据
INSERT INTO message_setup (company_id, item_code, is_checked)
SELECT a.company_id, b.item_code, a.is_checked
FROM
  (SELECT *
  FROM message_setup
  WHERE item_code = 'order_reason_exceed_desc'
  AND is_checked = 1) a,
  (SELECT *
  FROM message_setup_item
  WHERE busi_code = 'exceed_order_reason'
  AND item_code LIKE '%_desc'
    UNION
  SELECT *
  FROM message_setup_item
  WHERE busi_code = 'apply_setting_up'
  AND item_code LIKE '%_desc'
  AND item_code LIKE 'apply_reason_exceed_order_%'
  ORDER BY busi_code) b;

-- 审批事由补充设置洗数据
INSERT INTO message_setup (company_id, item_code, is_checked)
SELECT a.company_id, b.item_code, a.is_checked
FROM
  (SELECT *
  FROM message_setup
  WHERE item_code = 'apply_reason_desc'
  AND is_checked = 1) a,
  (SELECT *
  FROM message_setup_item
  WHERE busi_code = 'apply_setting_up'
  AND item_code LIKE '%_desc'
  AND item_code != 'apply_reason_desc'
  AND item_code NOT LIKE 'apply_reason_exceed_order_%'
  AND item_code NOT LIKE 'apply_reason_refund_%'
  AND item_code NOT LIKE 'apply_reason_change_%') b,
  (SELECT *
  FROM message_setup
  WHERE item_code LIKE 'apply_reason_%'
  AND item_code NOT LIKE '%_desc'
  AND is_checked = 1) c
WHERE a.company_id = c.company_id
AND b.item_code = concat(c.item_code, '_desc')
AND c.is_checked = 1;

-- 老订单和采购审批事由洗数据
UPDATE apply_order
    SET apply_reason = ''
WHERE apply_order_type IN (2, 3)
AND apply_reason = apply_reason_desc
AND apply_reason != '';