---添加配置表字段
ALTER TABLE "public"."message_setup"
	ADD COLUMN "user_id" varchar(50) DEFAULT ''::character varying;
COMMENT ON COLUMN "public"."message_setup"."user_id" IS '用户id';

DROP INDEX "public"."message_setup_company_id_item_code";
CREATE UNIQUE INDEX  "message_setup_company_id_item_code" ON "public"."message_setup" USING btree(company_id COLLATE "default" "pg_catalog"."text_ops" ASC NULLS LAST, item_code COLLATE "default" "pg_catalog"."text_ops" ASC NULLS LAST, user_id COLLATE "default" "pg_catalog"."text_ops" ASC NULLS LAST);

---审批通知配置
INSERT INTO message_setup_item (busi_code, item_code, default_checked) VALUES ('apply_setting_up', 'apply_message_setting', 0);
