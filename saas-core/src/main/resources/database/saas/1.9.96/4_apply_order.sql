ALTER TABLE "public"."apply_order_log"
	ADD COLUMN "price" numeric(20,2);
COMMENT ON COLUMN "public"."apply_order_log"."price" IS '审批时价格';

ALTER TABLE "public"."apply_order"
	ADD COLUMN "real_price" numeric(20,2),
	ADD COLUMN "exceed_buy_type" int4 DEFAULT 2,
	ADD COLUMN "overtime" timestamp(6) NULL;
COMMENT ON COLUMN "public"."apply_order"."real_price" IS '最新价格';
COMMENT ON COLUMN "public"."apply_order"."exceed_buy_type" IS '是否超规 1,超规 2,不超规';
COMMENT ON COLUMN "public"."apply_order"."overtime" IS '订单审批的超时时间';

ALTER TABLE "public"."apply_trip_info"
	ADD COLUMN "back_start_time" timestamp(6) NULL,
	ADD COLUMN "back_end_time" timestamp(6) NULL,
	ADD COLUMN "trip_type" int4 DEFAULT 1,
	ADD COLUMN "trip_content" varchar(2000);
COMMENT ON COLUMN "public"."apply_trip_info"."back_start_time" IS '返程开始时间';
COMMENT ON COLUMN "public"."apply_trip_info"."back_end_time" IS '返程结束时间';
COMMENT ON COLUMN "public"."apply_trip_info"."trip_type" IS '行程类型 1.单程 2.往返';
COMMENT ON COLUMN "public"."apply_trip_info"."trip_content" IS '行程扩展字段';


ALTER TABLE "public"."apply_trip_info"
	ALTER COLUMN "content" TYPE varchar(2000);