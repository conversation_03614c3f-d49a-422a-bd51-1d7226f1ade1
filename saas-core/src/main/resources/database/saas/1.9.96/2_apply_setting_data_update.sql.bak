-- 改为程序处理, 此文件无需执行!

-- 1.补充非弹性审批流字段值
UPDATE apply_flow a
SET company_apply_setting_id = t.id,
    company_apply_type = t.company_apply_type,
    cc_notice_type = t.cc_notice_type,
    is_exceed_buy_flow = (case t.company_setting_type when 2 then 1 else 0 end)
FROM (SELECT *
      FROM company_apply_setting
      WHERE company_apply_type != 1) t
WHERE a.id = t.apply_flow_id
AND a.company_apply_setting_id is null;

-- 2.补全弹性审批流数据(超规审批流只支持固定审批流,所以弹性审批流均为非超规审批流)
INSERT INTO apply_flow (id, company_id, create_time, operator_id, company_apply_setting_id, company_apply_type, cc_notice_type, is_exceed_buy_flow)
  SELECT id, company_id, now(), operator_id, id, company_apply_type, cc_notice_type, 0
    FROM company_apply_setting a
    WHERE company_apply_type = 1
    AND NOT exists(
        SELECT 1
        FROM apply_flow b
        WHERE a.id = b.company_apply_setting_id
    );

-- 3.抄送
UPDATE apply_flow_copy_to a
SET apply_flow_id =
    (SELECT b.id
      FROM apply_flow b
      WHERE b.company_apply_setting_id = a.company_apply_setting_id)
WHERE a.apply_flow_id is null;

-- 4.差旅订单审批默认正常审批流
INSERT INTO apply_flow (id, company_id, create_time, operator_id, company_apply_setting_id, company_apply_type, cc_notice_type, is_exceed_buy_flow)
  SELECT id, company_id, now(), operator_id, id, 1, 1, 0
    FROM company_apply_setting a
    WHERE company_setting_type = 2
    AND apply_type = 1
    AND NOT exists(
        SELECT 1
        FROM apply_flow b
        WHERE a.id = b.company_apply_setting_id
        AND is_exceed_buy_flow = 0
    );