-- apply_flow表添加company_apply_setting_id、company_apply_type、cc_notice_type、is_exceed_buy_flow字段
ALTER TABLE public.apply_flow ADD company_apply_setting_id VARCHAR(24) NULL;
COMMENT ON COLUMN public.apply_flow.company_apply_setting_id IS '公司的审批流设置id';
ALTER TABLE public.apply_flow ADD company_apply_type INTEGER NULL;
COMMENT ON COLUMN public.apply_flow.company_apply_type IS '公司设置的审批类型 1.弹性审批流，2.固定审批流，3.分条件审批流';
ALTER TABLE public.apply_flow ADD cc_notice_type INTEGER NULL;
COMMENT ON COLUMN public.apply_flow.cc_notice_type IS '抄送通知 1.仅全部同意后通知 2.仅发起时通知 3.发起时和全部同意时都通知';
ALTER TABLE public.apply_flow ADD is_exceed_buy_flow INTEGER NULL;
COMMENT ON COLUMN public.apply_flow.is_exceed_buy_flow IS '是否超规流程 0.正常流程 1.超规流程';

-- 审批抄送表添加审批流id字段
ALTER TABLE public.apply_flow_copy_to ADD apply_flow_id CHAR(24) NULL;
COMMENT ON COLUMN public.apply_flow_copy_to.apply_flow_id IS '审批流id';

-- 设置company_apply_setting表审批流类型字段不为非空
ALTER TABLE public.company_apply_setting ALTER COLUMN company_apply_type DROP NOT NULL;
ALTER TABLE public.apply_flow_copy_to ALTER COLUMN company_apply_setting_id DROP NOT NULL;