CREATE TABLE order_check_log (
  id SERIAL NOT NULL,
  company_id varchar(50) NOT NULL,
  employee_id varchar(50) NOT NULL,
  biz_category int4 NOT NULL,
  client_version varchar(10),
  request_id varchar(50) NOT NULL,
  request TEXT NOT NULL,
  response TEXT NOT NULL,
  result varchar(50) NOT NULL,
  snapshot_info TEXT,
  ext_info TEXT,
  create_time timestamp(6) NOT NULL DEFAULT now(),
  PRIMARY KEY ("id") NOT DEFERRABLE INITIALLY IMMEDIATE
);
CREATE INDEX order_check_log_index ON order_check_log USING btree(company_id, employee_id);
COMMENT ON TABLE order_check_log IS '下单校验日志表';
COMMENT ON COLUMN order_check_log.id IS '主键';
COMMENT ON COLUMN order_check_log.company_id IS '公司id';
COMMENT ON COLUMN order_check_log.employee_id IS '员工id';
COMMENT ON COLUMN order_check_log.biz_category IS '场景类型';
COMMENT ON COLUMN order_check_log.client_version IS '版本号';
COMMENT ON COLUMN order_check_log.request_id IS 'request_id';
COMMENT ON COLUMN order_check_log.request IS '请求参数';
COMMENT ON COLUMN order_check_log.result IS '返回结果';
COMMENT ON COLUMN order_check_log.response IS '返回信息';
COMMENT ON COLUMN order_check_log.snapshot_info IS '快照信息';
COMMENT ON COLUMN order_check_log.ext_info IS '扩展信息';
COMMENT ON COLUMN order_check_log.create_time IS '创建时间';
