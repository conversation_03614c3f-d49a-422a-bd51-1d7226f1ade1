--删除唯一索引
DROP INDEX "public"."index_company_apply_setting_company_applytype";
CREATE INDEX  "index_company_apply_setting_company_applytype" ON "public"."company_apply_setting" USING btree(company_id COLLATE "default" "pg_catalog"."text_ops" ASC NULLS LAST, apply_type "pg_catalog"."int4_ops" ASC NULLS LAST);

DROP INDEX "public"."index_apply_flow_item_applyflowid";
CREATE INDEX  "index_apply_flow_item_applyflowid" ON "public"."apply_flow_item" USING btree(apply_flow_id COLLATE "default" "pg_catalog"."bpchar_ops" ASC NULLS LAST, sort "pg_catalog"."int4_ops" ASC NULLS LAST);

--公司的审批流设置 添加审批流抄送通知
alter table company_apply_setting add column cc_notice_type INT4 null default 2;
comment on column company_apply_setting.cc_notice_type is
'抄送通知 1.仅全部同意后通知 2.仅发起时通知 3.发起时和全部同意时都通知';
alter table company_apply_setting add COLUMN apply_status INT4 null default 1;
comment on column company_apply_setting.apply_status is
'状态 1.正常 2.删除';

--公司设置的审批流抄送人列表
CREATE TABLE apply_flow_copy_to(
  id CHAR(24) NOT NULL ,
  company_apply_setting_id VARCHAR(24) NOT NULL, --公司的审批流设置id
  sort INT NOT NULL ,--序列
  item_type INT  NOT NULL ,--类型 1.自定义角色 2.指定员工 4.系统管理员 8.企业管理员 16.主管
  item_id VARCHAR (24)  NULL ,-- 对应item_type 自定义角色->角色ID；指定员工->员工ID；系统管理员 ->null；企业管理员 ->null；主管->主管级别(Int)；
  create_time TIMESTAMP NOT NULL,
  PRIMARY KEY (id)
);
comment on table apply_flow_copy_to is
'抄送人设置';
comment on column apply_flow_copy_to.company_apply_setting_id is
'公司的审批流设置id';
comment on column apply_flow_copy_to.sort is
'排序';
comment on column apply_flow_copy_to.item_type is
'类型 1.自定义角色 2.指定员工 4.系统管理员 8.企业管理员 16.主管';
comment on column apply_flow_copy_to.item_id is
'对应item_type 自定义角色->角色ID；指定员工->员工ID；系统管理员 ->null；企业管理员 ->null；主管->主管级别(Int)';

--审批流节点表添加多条件审批流条件
alter table apply_flow_item add column condition_min NUMERIC(20,2)  null;
alter table apply_flow_item add column condition_max NUMERIC(20,2)  null;
comment on column apply_flow_item.condition_min is
'条件下限';
comment on column apply_flow_item.condition_max is
'条件上限';

create table company_apply_setting_use (
   id                   VARCHAR(24)     NOT NULL ,
   setting_type          INT4                 null default 1,
   item_id              VARCHAR(50)          null,
   company_id           VARCHAR(50)          null,
   company_apply_setting_id VARCHAR(24) NOT NULL,
   create_user          VARCHAR(50)          null,
   create_time          TIMESTAMP            null default now(),
   update_user          VARCHAR(50)          null,
   update_time          TIMESTAMP            null default now()
);

comment on table company_apply_setting_use is
'审批流应用分类';

comment on column company_apply_setting_use.setting_type is
'应用类型 1.部门 2.个人';

comment on column company_apply_setting_use.item_id is
'员工id/部门id';

comment on column company_apply_setting_use.company_id is
'公司id';

comment on column company_apply_setting_use.company_apply_setting_id is
'公司的审批流设置id';

comment on column company_apply_setting_use.create_user is
'创建人';

comment on column company_apply_setting_use.create_time is
'创建时间';

comment on column company_apply_setting_use.update_user is
'修改人';

comment on column company_apply_setting_use.update_time is
'修改时间';

/*==============================================================*/
/* Index: budget_setting_use_item_id                            */
/*==============================================================*/

CREATE INDEX company_apply_setting_use_item_id ON "public"."company_apply_setting_use" (setting_type,item_id,company_id,company_apply_setting_id);

--行程表添加预估金额字段
alter table apply_trip_info add column estimated_amount NUMERIC(20,2)  null;

--审批单抄送人表
CREATE TABLE apply_order_copy_to(
  id CHAR(24) NOT NULL ,
  apply_order_id  CHAR(24)    NOT NULL, --申请单ID
  sort INT NOT NULL ,--序列
  item_type INT  NOT NULL ,--类型 1.自定义角色 2.指定员工 4.系统管理员 8.企业管理员 16.主管
  item_id VARCHAR (24)  NULL ,-- 对应item_type 自定义角色->角色ID；指定员工->员工ID；系统管理员 ->null；企业管理员 ->null；主管->主管级别(Int)；
  create_time TIMESTAMP NOT NULL,
  is_delete INT4 null, --1:不能删除 2:可以删除
  user_id VARCHAR (24) NOT NULL , -- 员工ID
  PRIMARY KEY (id)
);


alter table apply_order add COLUMN  flow_cc_type INT4 null;
comment on column apply_order.flow_cc_type is
'抄送通知 1.仅全部同意后通知 2.仅发起时通知 3.发起时和全部同意时都通知';


INSERT INTO company_apply_setting_use(
	ID, item_id,
	company_id,
	company_apply_setting_id
) SELECT ID, company_id,company_id ,ID FROM company_apply_setting;
