--更新合同默认配置
UPDATE message_setup_item SET default_checked = 1, default_int_val1 = 0 WHERE busi_code = 'apply_setting_up' AND item_code = 'apply_payment_contract';
--更新凭证默认配置
UPDATE message_setup_item SET default_checked = 2, default_int_val1 = 0 WHERE busi_code = 'apply_setting_up' AND item_code = 'apply_payment_proof';
--新增已开发票默认配置
INSERT INTO message_setup_item (busi_code, item_code, default_checked, default_int_val1) VALUES ('apply_setting_up', 'apply_payment_has_invoice', 1, 0);
--新增待开发票默认配置
INSERT INTO message_setup_item (busi_code, item_code, default_checked) VALUES ('apply_setting_up', 'apply_payment_need_invoice', 1);
--新增无发票默认配置
INSERT INTO message_setup_item (busi_code, item_code, default_checked) VALUES ('apply_setting_up', 'apply_payment_no_invoice', 1);


--新增合同默认配置
INSERT INTO message_setup_item (busi_code, item_code, default_checked, default_int_val1) VALUES ('apply_setting_up', 'public_payment_contract', 1, 0);
--更新凭证默认配置
INSERT INTO message_setup_item (busi_code, item_code, default_checked, default_int_val1) VALUES ('apply_setting_up', 'public_payment_proof', 2, 0);
--新增已开发票默认配置
INSERT INTO message_setup_item (busi_code, item_code, default_checked, default_int_val1) VALUES ('apply_setting_up', 'public_payment_has_invoice', 1, 0);
--新增待开发票默认配置
INSERT INTO message_setup_item (busi_code, item_code, default_checked) VALUES ('apply_setting_up', 'public_payment_need_invoice', 1);
--新增无发票默认配置
INSERT INTO message_setup_item (busi_code, item_code, default_checked) VALUES ('apply_setting_up', 'public_payment_no_invoice', 1);