CREATE TABLE fields_option (
  id SERIAL NOT NULL,
  company_id varchar(50) NOT NULL,
  message_setup_id int4 NOT NULL,
  content varchar(500) NOT NULL,
  create_time TIMESTAMP(6),
  PRIMARY KEY ("id") NOT DEFERRABLE INITIALLY IMMEDIATE
);
CREATE  INDEX  "fo_query_index" ON "public"."fields_option" USING btree(company_id,message_setup_id);
COMMENT ON TABLE fields_option IS '字段选项表';
COMMENT ON COLUMN fields_option.id IS '主键';
COMMENT ON COLUMN fields_option.company_id IS '公司id';
COMMENT ON COLUMN fields_option.message_setup_id IS '自定义字段配置表id';
COMMENT ON COLUMN fields_option.content IS '选项内容';