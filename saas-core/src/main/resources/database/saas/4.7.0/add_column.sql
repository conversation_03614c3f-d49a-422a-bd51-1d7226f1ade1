ALTER TABLE "public"."apply_order"
	ADD COLUMN "delete_status" int4 DEFAULT 0;
COMMENT ON COLUMN "public"."apply_order"."delete_status" IS '删除状态 0:未删除 1:已删除';

ALTER TABLE "public"."apply_order_log"
	ADD COLUMN "delete_status" int4 DEFAULT 0;
COMMENT ON COLUMN "public"."apply_order_log"."delete_status" IS '删除状态 0:未删除 1:已删除';

ALTER TABLE "public"."apply_trip_guest"
	ADD COLUMN "delete_status" int4 DEFAULT 0;
COMMENT ON COLUMN "public"."apply_trip_guest"."delete_status" IS '删除状态 0:未删除 1:已删除';

ALTER TABLE "public"."apply_trip_info"
	ADD COLUMN "delete_status" int4 DEFAULT 0;
COMMENT ON COLUMN "public"."apply_trip_info"."delete_status" IS '删除状态 0:未删除 1:已删除';

ALTER TABLE "public"."apply_approver_map"
	ADD COLUMN "delete_status" int4 DEFAULT 0;
COMMENT ON COLUMN "public"."apply_approver_map"."delete_status" IS '删除状态 0:未删除 1:已删除';