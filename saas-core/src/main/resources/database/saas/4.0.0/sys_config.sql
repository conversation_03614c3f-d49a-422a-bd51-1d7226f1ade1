CREATE TABLE sys_common_config (
  id SERIAL NOT NULL,
  method_code varchar(24) ,
  method_type INTEGER ,
  version varchar(255) ,
  code INTEGER ,
  type INTEGER  ,
  msg varchar(255) ,
  title varchar(255)  ,
  switch_type INTEGER DEFAULT '0' ,
  ext_content varchar(255)  ,
  create_time TIMESTAMP(6) ,
  update_time TIMESTAMP(6) ,
  PRIMARY KEY ("id") NOT DEFERRABLE INITIALLY IMMEDIATE
);
CREATE UNIQUE INDEX  "scc_un_index" ON "public"."sys_common_config" USING btree(method_code);
CREATE  INDEX  "scc_query_index" ON "public"."sys_common_config" USING btree(method_code,method_type,switch_type);
COMMENT ON TABLE sys_common_config IS '系统接口升级配置信息表';
COMMENT ON COLUMN sys_common_config.id IS '主键';
COMMENT ON COLUMN sys_common_config.method_code IS '方法 code';
COMMENT ON COLUMN sys_common_config.method_type IS '类型：1:http接口， 2:内部event消息执行';
COMMENT ON COLUMN sys_common_config.version IS '版本号，低于该版本号时，提示升级信息';
COMMENT ON COLUMN sys_common_config.code IS '返回给客户端code码';
COMMENT ON COLUMN sys_common_config.type IS '提示类型: 6、toast  ；7、弹窗提示';
COMMENT ON COLUMN sys_common_config.msg IS '提示信息';
COMMENT ON COLUMN sys_common_config.title IS '提示标题';
COMMENT ON COLUMN sys_common_config.switch_type IS '提示开关，0:关闭 1:开启';
COMMENT ON COLUMN sys_common_config.ext_content IS '扩展参数';


insert into "public"."sys_common_config" ( "method_code", "method_type", "version", "code", "type", "msg", "title", "switch_type", "ext_content", "create_time", "update_time") values ( 'get_writeoff_detail', '1', '4.0.0', '600', '7', '请更新到最新版本后查看', '版本提示升级', '1', null, now(), now());
insert into "public"."sys_common_config" ( "method_code", "method_type", "version", "code", "type", "msg", "title", "switch_type", "ext_content", "create_time", "update_time") values ( 'writeoff_create', '1', '4.0.0', '600', '7', '请更新到最新版本后提交', '版本提示升级', '1', null, now(), now());