new message:

公共:
message_type: 2 消费通知 4 审批通知 8 订单通知 16 订票提醒 32 系统通知
title:标题
content: 内容
sender_type: 发送人类型: 1 人, 2 HL, 3 SAAS
sender: 发送人id
receiver: 接收人id
biz_order:业务订单号

消费:
consumption_info:{
    consumption_type: 3 打车, 7 机票, 11 酒店, 15 火车, 16 采购
    air_info:{
        creator_msg: 下单人 (名字)
        price_msg: 消费金额
        consume_time:消费时间
        travel_msg: 行程
        passenger_msg: 乘机人 (名字)
    },
    taxi_info:{
        creator_msg: 下单人 (名字)
        price_msg: 消费金额
        consume_time:消费时间
        travel_msg: 行程
        passenger_msg: 乘车人 (名字)
    },
    hotel_info:{
        creator_msg: 下单人 (名字)
        price_msg: 消费金额
        consume_time:消费时间
        travel_msg: 酒店详情
        guest_msg: 乘车人 (名字)
    },
    train_info:{
        creator_msg: 下单人 (名字)
        price_msg: 消费金额
        consume_time:消费时间
        travel_msg: 行程
        passenger_msg: 乘车人 (名字)
    },
    mall_info:{
        creator_msg: 下单人 (名字)
        price_msg: 消费金额
        consume_time:消费时间
        products_msg: 采购物品
    }
}

订单通知:
order_info:{
    order_type: 7 机票, 11 酒店, 15 火车
    create_time: 订单创建时间
    order_status_msg: 订单状态文本
    order_msg: 订单文本
    customer_msg: 顾客信息 (名字)
    link_info:{
        redirect_order_id: 跳转订单号
    }
}

审批:
apply_info:{
    apply_type: 1 差旅申请 2 用车申请 3 采购申请
    apply_time: 申请时间
    apply_msg: 申请审批文本
    applier_msg : 申请人信息 (名字)
    applier_department: 申请人部门 (名字)
    travel_msg : 行程信息
}

订票提醒:
order_alert_info:{
    link_info:{
        ???
    }
}

系统通知:
system_info:{
    link_info:{
        ???
    }
}