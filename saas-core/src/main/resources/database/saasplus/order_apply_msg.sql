CREATE TABLE `order_apply_msg` (
  `id` char(30) NOT NULL,
  `user_id` varchar(30) NOT NULL COMMENT '员工id',
  `company_id` varchar(30) NOT NULL COMMENT '公司id',
  `category_type` int(11) NOT NULL COMMENT '场景类型',
  `passengers` longtext NOT NULL COMMENT '出行人ID',
  `start_city_id` varchar(30) NOT NULL COMMENT '出发城市',
  `arrival_city_id` varchar(30) DEFAULT NULL COMMENT '目的城市',
  `start_date` datetime NOT NULL COMMENT '出发日期',
  `success` tinyint(1) DEFAULT '0' COMMENT '订单状态 true/false',
  `during_apply_id` varchar(30) NOT NULL COMMENT '审批单号',
  `order_id` varchar(30) NOT NULL COMMENT '订单号',
  `exceeded` tinyint(1) DEFAULT '0' COMMENT '是否超规',
  `price` decimal(20,2) DEFAULT NULL COMMENT '订单金额',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4

ALTER TABLE `order_apply_msg` ADD INDEX `idx_companyId_userId` (`user_id`, `company_id`);

