<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<!-- 配置生成器 -->
<generatorConfiguration>
    <classPathEntry location="src/main/resources/mybatis-gen/postgresql-42.0.0.jre7.jar"/>
    <context id="postgres" defaultModelType="conditional" targetRuntime="MyBatis3">
        <property name="autoDelimitKeywords" value="false"/>
        <property name="javaFileEncoding" value="UTF-8"/>
        <property name="javaFormatter" value="org.mybatis.generator.api.dom.DefaultJavaFormatter"/>
        <property name="xmlFormatter" value="org.mybatis.generator.api.dom.DefaultXmlFormatter"/>
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>

        <jdbcConnection driverClass="org.postgresql.Driver"
                        connectionURL="********************************"
                        userId="postgres"
                        password=""/>

        <javaTypeResolver type="org.mybatis.generator.internal.types.JavaTypeResolverDefaultImpl">
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <javaModelGenerator targetPackage="com.fenbeitong.saas.core.model.saas" targetProject="src/main/resources/mybatis-gen">
            <property name="constructorBased" value="false"/>
            <property name="enableSubPackages" value="true"/>
            <property name="immutable" value="false"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <sqlMapGenerator targetPackage="com.fenbeitong.saas.core.mapper.saas" targetProject="src/main/resources/mybatis-gen">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <javaClientGenerator targetPackage="com.fenbeitong.saas.core.dao.saas" type="MIXEDMAPPER"
                             targetProject="src/main/resources/mybatis-gen">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

        <table tableName="budget_remind_record">
            <property name="constructorBased" value="false"/>
            <property name="ignoreQualifiersAtRuntime" value="false"/>
            <property name="immutable" value="false"/>
            <property name="modelOnly" value="false"/>
            <property name="useActualColumnNames" value="false"/>
            <!--<property name="selectAllOrderByClause" value="age desc,username asc"/>-->
            <!-- <columnOverride column="username"> -->
            <!-- <property name="property" value="userName"/> -->
            <!-- </columnOverride> -->
        </table>
    </context>
</generatorConfiguration>
