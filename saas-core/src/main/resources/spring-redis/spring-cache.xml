<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:cache="http://www.springframework.org/schema/cache"
       xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/cache
       http://www.springframework.org/schema/cache/spring-cache.xsd">

    <bean class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
        <property name="locations">
            <value>classpath:conf/redis.properties</value>
        </property>
        <property name="ignoreUnresolvablePlaceholders" value="true"/>
    </bean>
    <cache:annotation-driven cache-manager="cacheManager"/>

    <!-- redis 相关配置 -->
    <bean id="poolConfig" class="redis.clients.jedis.JedisPoolConfig">
        <!--<property name="maxIdle" value="300" />-->
        <!--<property name="maxWaitMillis" value="3000" />-->
        <!--<property name="testOnBorrow" value="true" />-->
        <property name="maxIdle" value="${redis.maxIdle}"/>
        <property name="maxWaitMillis" value="${redis.maxWait}"/>
        <property name="testOnBorrow" value="${redis.testOnBorrow}"/>
    </bean>

    <bean id="JedisConnectionFactory" class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory"
          p:host-name="${redis.host}"
          p:port="${redis.port}"
          p:password="${redis.pass}"
          p:pool-config-ref="poolConfig"
          p:database="${redis.database}"/>

    <bean id="redisTemplate" class="org.springframework.data.redis.core.RedisTemplate">
        <property name="connectionFactory" ref="JedisConnectionFactory"/>
    </bean>

    <!-- spring自己的缓存管理器，这里定义了缓存位置名称 ，即注解中的value -->
    <bean id="cacheManager" class="org.springframework.cache.support.SimpleCacheManager">
        <property name="caches">
            <set>
                <!-- 这里可以配置多个redis -->
                <!-- <bean class="com.cn.util.RedisCache">
                     <property name="redisTemplate" ref="redisTemplate" />
                     <property name="name" value="default"/>
                </bean> -->
                <bean class="com.fenbeitong.saas.core.utils.cache.RedisCache">
                    <property name="redisTemplate" ref="redisTemplate"/>
                    <property name="name" value="common"/>
                    <!-- common名称要在类或方法的注解中使用 -->
                </bean>
            </set>
        </property>
    </bean>
</beans>