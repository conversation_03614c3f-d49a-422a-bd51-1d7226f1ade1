<?xml version="1.0" encoding="UTF-8" ?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://code.alibabatech.com/schema/dubbo
       http://code.alibabatech.com/schema/dubbo/dubbo.xsd">
    <!-- 生成远程服务代理，可以和本地bean一样使用demoService -->
    <dubbo:reference id="testService" interface="com.fenbeitong.usercenter.api.service.test.TestService" check="false"/>

    <!-- uc -->
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeAirRuleService"
                     id="iBaseEmployeeAirRuleService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeIntlAirRuleService"
                     id="iBaseEmployeeIntlAirRuleService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeHotelRuleService"
                     id="iBaseEmployeeHotelRuleService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeTrainRuleService"
                     id="iBaseEmployeeTrainRuleService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeDinnerRuleService"
                     id="iBaseEmployeeDinnerRuleService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeMallRuleService"
                     id="iBaseEmployeeMallRuleService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeTaxiRuleService"
                     id="iBaseEmployeeTaxiRuleService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.employee.IBaseEmployeeExtService"
                     id="iBaseEmployeeExtService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.employee.IREmployeeService"
                     id="irEmployeeService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeDinnerRuleExtService"
                     id="iBaseEmployeeDinnerRuleExtService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeAirRuleExtService"
                     id="iBaseEmployeeAirRuleExtService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeIntlAirRuleExtService"
                     id="iBaseEmployeeIntlAirRuleExtService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeHotelRuleExtService"
                     id="iBaseEmployeeHotelRuleExtService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeTrainRuleExtService"
                     id="iBaseEmployeeTrainRuleExtService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeMallRuleExtService"
                     id="iBaseEmployeeMallRuleExtService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeTaxiRuleExtService"
                     id="iBaseEmployeeTaxiRuleExtService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.frequent.IFrequentService"
                     id="iFrequentService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.privilege.IPrivilegeService"
                     id="iPrivilegeService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.costcenter.ICostCenterService"
                     id="iCostCenterService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.costcenter.ICostCenterGroupService"
                     id="iCostCenterGroupService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.orgunit.IOrgUnitService"
                     id="iOrgUnitService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeTakeawayRuleService"
                     id="iBaseEmployeeTakeawayRuleService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.company.ICompanyService"
                     id="iCompanyService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.calendar.RpcCompanyCalendarService"
                     id="rpcCompanyCalendarService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeMeishiRuleExtService"
                     id="iBaseEmployeeMeishiRuleExtService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.rule.ICommonEmployeeRuleService"
                     id="iCommonEmployeeRuleService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.common.ICommonService"
                     id="iCommonService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.meta.IMetaService"
                     id="iMetaService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.company.ICompanyRuleService"
                     id="iCompanyRuleService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.payment.ICompanyPaymentService"
                     id="iCompanyPaymentService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.company.ICompanyNewInfoService"
                     id="iCompanyNewInfoService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeMileageRuleExtService"
                     id="iBaseEmployeeMileageRuleExtService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeBusRuleService"
                     id="iBaseEmployeeBusRuleService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.invoice.IInvoiceConfigService"
                     id="iInvoiceConfigService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.company.IRCompanyService"
                     id="iRCompanyService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.rule.IBaseEmployeeRuleService"
                     id="iBaseEmployeeRuleService"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.entrance.IEmployeeEntranceAuthService"
                     id="iEmployeeEntranceAuthService"/>
    <!-- saas-plus -->
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.budget.IBudgetService"
                     id="iBudgetService"/>
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.rule.IBaseTakeawayRuleExtService"
                     id="iBaseTakeawayRuleExtService"/>
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.rule.IBaseTakeawayRuleService"
                     id="iBaseTakeawayRuleService"/>
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.coupon.ICouponApplySettingService"
                     id="iCouponApplySettingService"/>
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.rule.ITaxiApproveRuleService"
                     id="iTaxiApproveRuleService"/>
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.setting.HotelSettingService"
                     id="hotelSettingService"/>
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.finance.IFinanceCostService"
                     id="iFinanceCostService"/>
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.messageSetup.IMessageSetupService"
                     id="iMessageSetupRpcService"/>
    <dubbo:reference interface="com.fenbeitong.noc.api.service.transport.service.ITransportOrderSlaveSearchService"
                     id="iTransportOrderSlaveSearchService"/>
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.reason.ICustomReasonAttrService"
                     id="customReasonAttrService"/>
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.task.ITaskService"
                     id="iTaskService"/>
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.rule.IMileageRuleService"
                     id="iMileageRuleService"/>
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.apply.IApplyOrderService"
                     id="iApplyOrderService"/>
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.finance.IOrderCostService"
                                                               id="iOrderCostService"/>
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.setting.ICostAttributionSettingService"
                     id="iCostAttributionSettingService"/>
    <dubbo:reference interface="com.fenbeitong.expense.management.api.costAttribution.ICostAttributionService"
                     id="iCostAttributionService"/>
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.custform.ICustomFormService"
                     id="iCustomFormService"/>
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.setting.IROrderApprovalSettingService"
                     id="iROrderApprovalSettingService"/>
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.bill.IExpenseOrderRpcService"
                     id="iExpenseOrderRpcService"/>
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.rule.IHotelGroupRuleService"
                     id="iHotelGroupRuleService"/>
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.hotel.IHotelGroupAreaService"
                     id="iHotelGroupAreaService"/>
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.rule.IRuleService"
                     id="iRuleService"/>
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.rule.ITakeawayDinnerManageService"
                     id="iTakeawayDinnerManageService"/>
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.rule.IChangeRuleService"
                     id="iChangeRuleService"/>
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.taxi.ICompanyTaxiService"
                     id="iCompanyTaxiService"/>
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.taxi.ITaxiRuleCityService"
                     id="iTaxiRuleCityService"/>
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.setting.IOrderMsgUseService"
                     id="iOrderMsgUseService"/>
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.setting.ICommonSettingService"
                     id="iCommonSettingService"/>
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.custom.ICustomReasonRpcService"
                     id="iCustomReasonRpcService"/>

    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.virtualCard.IVirtualCardAlertService"
                     id="iVirtualCardAlertService"/>
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.push.INewPushService"
                     id="iNewPushService"/>
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.finance.IFinanceCostInfoRelationApplyService"
                     id="iFinanceCostInfoRelationApplyService" />

    <!--    超规个人付：机票规则rpc: com.fenbeitong.saasplus.api.service.rule.IRuleV2Service-->
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.rule.IRuleV2Service"
                     id="iRuleV2Service"/>

    <!-- fenbei-noc -->
    <dubbo:reference interface="com.fenbeitong.noc.api.service.takeaway.service.ISaaSStatisticsService"
                     id="iSaaSStatisticsService"/>
    <dubbo:reference interface="com.fenbeitong.noc.api.service.INocOrderService"
                     id="iNocOrderService"/>
    <dubbo:reference interface="com.fenbeitong.noc.api.service.takeaway.service.ITakeawayOrderService"
                     id="iTakeawayOrderService" group="element"/>
    <dubbo:reference interface="com.fenbeitong.noc.api.service.bank.service.IBankOrderService"
                     id="iBankOrderService"/>
    <dubbo:reference interface="com.fenbeitong.noc.api.service.bank.service.IBankOrderSearchService"
                     id="iBankOrderSearchService"/>
    <dubbo:reference interface="com.fenbeitong.noc.api.service.takeaway.service.ITakeawayOrderSearchService"
                     id="iTakeawayOrderSearchService"/>
    <dubbo:reference interface="com.fenbeitong.noc.api.service.mileage.service.IAllowanceMileageOrderSearchService"
                     id="iAllowanceMileageOrderSearchService"/>
    <dubbo:reference interface="com.fenbeitong.noc.api.service.bus.service.IBusOrderSearchService"
                     id="iBusOrderSearchService" check="false"/>
    <dubbo:reference interface="com.fenbeitong.noc.api.service.express.service.IExpressOrderSearchService"
                     id="iExpressOrderSearchService"/>
    <!-- account-->
    <dubbo:reference interface="com.fenbeitong.fenbeipay.api.service.na.IAccountSubService" id="iAccountSubService"/>
    <dubbo:reference interface="com.fenbeitong.fenbeipay.api.service.voucher.IVouchersTaskService"
                     id="iVouchersTaskService"/>
    <dubbo:reference interface="com.fenbeitong.fenbeipay.api.service.bank.IBankCardSearchService"
                     id="iBankCardSearchService"/>
    <dubbo:reference interface="com.fenbeitong.fenbeipay.api.service.acctpublic.IAcctPublicSearchService"
                     id="iAcctPublicSearchService"/>
    <dubbo:reference interface="com.fenbeitong.fenbeipay.api.service.bank.IBankPettySearchService"
                     id="iBankPettySearchService"/>
    <dubbo:reference interface="com.fenbeitong.fenbeipay.api.service.voucher.IVouchersTempletService"
                     id="iVouchersTempletService"/>
    <dubbo:reference interface="com.fenbeitong.fenbeipay.api.service.acctpublic.IAcctPublicSearchDechService"
                     id="iAcctPublicSearchDechService"/>

    <!-- meishi-->
    <dubbo:reference interface="com.fenbeitong.noc.api.service.meishi.service.IMeishiOrderSearchService" id="iMeishiOrderSearchService"/>

    <!-- fenbei-bank-->
    <dubbo:reference interface="com.fenbeitong.bank.api.service.IBankHuPoFBTService"
                     id="iBankHuPoFBTService"/>
    <dubbo:reference interface="com.fenbeitong.bank.api.service.IBankPaymentService"
                     id="iBankPaymentService"/>

    <!-- fenbei-noc-api-car-->
    <dubbo:reference interface="com.fenbeitong.noc.api.car.service.ICarSassService"
                     id="iCarSassService"/>
    <dubbo:reference interface="com.fenbeitong.noc.api.car.service.ICarSaasService"
                     id="iCarSaasService"/>

    <!-- fenbei-invoice-api -->
    <dubbo:reference interface="com.fenbeitong.invoice.api.service.IFbtInvoiceService"
                     id="iFbtInvoiceService"/>
    <dubbo:reference interface="com.fenbeitong.invoice.api.service.IInvoiceTradeService"
                     id="iInvoiceTradeService"/>
    <dubbo:reference interface="com.fenbeitong.invoice.api.service.IFbtInvoicePublicService"
                     id="iFbtInvoicePublicService"/>
    <dubbo:reference interface="com.fenbeitong.car.didi.service.DidiRiverApprovalService"
                     id="didiRiverApprovalService"/>
    <dubbo:reference interface="com.fenbeitong.invoice.api.service.IFbtCompanyPackageService"
                     id="iFbtCompanyPackageService"/>

    <!-- harmony-api -->
    <dubbo:reference interface="com.fenbeitong.harmony.city.contrail.service.airport.IAirportContrailSearchService"
                     id="iAirportContrailSearchService"/>
    <!-- harmony-api -->
    <dubbo:reference interface="com.fenbeitong.harmony.city.contrail.service.trainstation.ITrainStationContrailService"
                     id="iTrainStationContrailService"/>
    <dubbo:reference interface="com.fenbeitong.harmony.city.contrail.service.airport.IAirportService"
                     id="iAirportService"/>

    <!-- budget -->
    <dubbo:reference interface="com.fenbeitong.sass.budget.service.api.dubbo.PersonalBudgetPlanQueryDubboService"
                     id="personalBudgetPlanQueryDubboService"/>

    <!-- order-center-api -->
    <dubbo:reference interface="com.fenbeitong.oc.api.service.AirTicketService"
                     id="airTicketService"/>
    <dubbo:reference interface="com.fenbeitong.oc.api.service.ITrainService"
                     id="iTrainService"/>

    <!-- config -->
    <dubbo:reference interface="com.fenbeitong.config.api.config.SysConfigItemCompanyService"
                     id="sysConfigItemCompanyService"/>

    <dubbo:reference interface="com.fenbeitong.noc.api.service.biz.service.IBizOrderService"
                     id="iBizOrderService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.oc.api.service.IOrderSaturnSearchService"
                     id="iOrderSaturnSearchService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.finhub.saturn.service.SaturnSearchOrderServiceTaxi"
                     id="saturnSearchOrderServiceTaxi" check="false"/>

    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.archive.IArchiveService"
                     id="iArchiveService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.saas.card.api.order.api.channel.saas.IOrderSearchRpcCallerSassApi"
                     id="iOrderSearchRpcCallerSassApi" check="false"  />

    <dubbo:reference interface="com.fenbeitong.saas.card.api.order.api.channel.saas.IOrderUpdateRpcCallerSaasApi"
                     id="iOrderUpdateRpcCallerSaasApi" check="false"   />

    <!-- oc -->
    <dubbo:reference interface="com.fenbeitong.oc.api.service.ITrainSaasService"
                     id="iTrainSaasService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.oc.api.service.air.IAirSaasService"
                     id="iAirSaasService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.oc.api.service.IHotelSaasService"
                     id="iHotelSaasService" check="false"/>

    <!--biz-card-->
    <dubbo:reference interface="com.fenbeitong.saas.card.api.petty.api.search.IBankPettySearchRpcService"
                     id="iBankPettySearchRpcService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.saas.card.api.card.api.IBankCardRpcService"
                     id="iBankCardRpcService"/>
    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.commonRule.ICommonRuleRpcService"
                     id="iCommonRuleRpcService" check="false"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.employee.IEmployeeI18nConfigService"
                     id="iEmployeeI18nConfigService" check="false"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.group.IGroupInfoService"
                     id="iGroupInfoService" check="false"/>
    <dubbo:reference interface="com.fenbeitong.usercenter.api.service.group.IGroupEmployeeService"
                     id="iGroupEmployeeService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.rule.ITaxiDingtalkService"
                     id="iTaxiDingtalkService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.fenbeimeta.api.service.data.IMongoDataService"
                     id="iMongoDataService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.rule.grant.IGrantRuleService"
                     id="iGrantRuleService" check="false" />

    <dubbo:reference interface="com.fenbeitong.harmony.city.contrail.service.city.IAreaService"
                     id="iAreaService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.apply.IApplyOccupyBudgetService"
                     id="iApplyOccupyBudgetService" check="false"/>

    <dubbo:reference interface="com.fenbeitong.search.order.api.order.service.IOrderSearchService"
                     id="iOrderSearchService" check="false"/>

    <dubbo:reference interface="com.fenbei.cost.management.api.budget.BudgetSystemConfigService"
                     id="budgetSystemConfigService" check="false" />

    <dubbo:reference interface="com.fenbeitong.search.worker.api.city.service.ICitySearchWorkerService"
                     id="iCitySearchWorkerService" check="false" />

    <dubbo:reference interface="com.fenbeitong.search.worker.api.trip.ITripOrderBizSearchService"
                     id="iTripOrderBizSearchService" check="false" />

    <dubbo:reference interface="com.fenbeitong.search.worker.api.trip.ITripConsumeOrderSearchService"
                     id="iTripConsumeOrderSearchService" check="false" />

    <dubbo:reference interface="com.fenbeitong.travel.rule.api.service.apply.ICustomApplyService"
                     id="iCustomApplyService" check="false" />

    <dubbo:reference interface="com.fenbeitong.hotel.api.service.HotelInternalService"
                     id="hotelInternalService" check="false" />

    <dubbo:reference interface="com.fenbeitong.travel.rule.api.service.custform.IFormModuleService"
                     id="iFormModuleService" check="false" />

    <dubbo:reference interface="com.fenbeitong.travel.rule.api.service.custform.ICustformFormService"
                     id="iCustformFormService" check="false" />

    <!--mileage-->
    <dubbo:reference interface="com.fenbeitong.mileage.api.service.IMileageRelationApplyRpcService"
                     id="iMileageRelationApplyRpcService" check="false" />

    <dubbo:reference interface="com.fenbeitong.expense.management.api.expenseOrder.IExpenseOrderService"
                     id="iExpenseOrderService" check="false" />

    <dubbo:reference interface="com.fenbeitong.travel.rule.api.service.rule.IOrderSensitiveWordService"
                     id="iOrderSensitiveWordService" check="false" />

    <dubbo:reference interface="com.fenbeitong.saasplus.api.service.rule.ICarRentalRuleExtService"
                     id="iCarRentalRuleExtService"/>

</beans>
