# Redis settings
# server IP
redis.host=127.0.0.1
# server port
redis.port=6379
#å¯ç 
redis.pass=
# use dbIndex
redis.database=0
# æ§å¶ä¸ä¸ªpoolæå¤æå¤å°ä¸ªç¶æä¸ºidle(ç©ºé²ç)çjediså®ä¾
redis.maxIdle=300
# è¡¨ç¤ºå½borrow(å¼å¥)ä¸ä¸ªjediså®ä¾æ¶ï¼æå¤§çç­å¾æ¶é´ï¼å¦æè¶è¿ç­å¾æ¶é´(æ¯«ç§)ï¼åç´æ¥æåºJedisConnectionExceptionï¼
redis.maxWait=3000
# å¨borrowä¸ä¸ªjediså®ä¾æ¶ï¼æ¯å¦æåè¿è¡validateæä½ï¼å¦æä¸ºtrueï¼åå¾å°çjediså®ä¾åæ¯å¯ç¨ç
redis.testOnBorrow=true